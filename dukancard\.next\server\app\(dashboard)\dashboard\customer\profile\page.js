(()=>{var e={};e.id=2408,e.ids=[2408],e.modules={3018:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>l,TN:()=>d,XL:()=>n});var a=t(60687);t(43210);var s=t(24224),i=t(96241);let o=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,...t}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(o({variant:r}),e),...t})}function n({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,i.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}},3109:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("LayoutList",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}],["path",{d:"M14 4h7",key:"3xa0d5"}],["path",{d:"M14 9h7",key:"1icrd9"}],["path",{d:"M14 15h7",key:"1mj8o2"}],["path",{d:"M14 20h7",key:"11slyb"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8126:(e,r,t)=>{"use strict";t.d(r,{Lt:()=>U,Rx:()=>B,Zr:()=>$,EO:()=>M,$v:()=>I,ck:()=>L,wd:()=>F,r7:()=>_});var a=t(60687),s=t(43210),i=t(11273),o=t(98599),l=t(10991),n=t(70569),d=Symbol("radix.slottable"),c="AlertDialog",[u,m]=(0,i.A)(c,[l.Hs]),p=(0,l.Hs)(),f=e=>{let{__scopeAlertDialog:r,...t}=e,s=p(r);return(0,a.jsx)(l.bL,{...s,...t,modal:!0})};f.displayName=c,s.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...s}=e,i=p(t);return(0,a.jsx)(l.l9,{...i,...s,ref:r})}).displayName="AlertDialogTrigger";var h=e=>{let{__scopeAlertDialog:r,...t}=e,s=p(r);return(0,a.jsx)(l.ZL,{...s,...t})};h.displayName="AlertDialogPortal";var g=s.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...s}=e,i=p(t);return(0,a.jsx)(l.hJ,{...i,...s,ref:r})});g.displayName="AlertDialogOverlay";var x="AlertDialogContent",[b,y]=u(x),v=function(e){let r=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=d,r}("AlertDialogContent"),j=s.forwardRef((e,r)=>{let{__scopeAlertDialog:t,children:i,...d}=e,c=p(t),u=s.useRef(null),m=(0,o.s)(r,u),f=s.useRef(null);return(0,a.jsx)(l.G$,{contentName:x,titleName:w,docsSlug:"alert-dialog",children:(0,a.jsx)(b,{scope:t,cancelRef:f,children:(0,a.jsxs)(l.UC,{role:"alertdialog",...c,...d,ref:m,onOpenAutoFocus:(0,n.m)(d.onOpenAutoFocus,e=>{e.preventDefault(),f.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,a.jsx)(v,{children:i}),(0,a.jsx)(S,{contentRef:u})]})})})});j.displayName=x;var w="AlertDialogTitle",N=s.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...s}=e,i=p(t);return(0,a.jsx)(l.hE,{...i,...s,ref:r})});N.displayName=w;var A="AlertDialogDescription",C=s.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...s}=e,i=p(t);return(0,a.jsx)(l.VY,{...i,...s,ref:r})});C.displayName=A;var k=s.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...s}=e,i=p(t);return(0,a.jsx)(l.bm,{...i,...s,ref:r})});k.displayName="AlertDialogAction";var P="AlertDialogCancel",R=s.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...s}=e,{cancelRef:i}=y(P,t),n=p(t),d=(0,o.s)(r,i);return(0,a.jsx)(l.bm,{...n,...s,ref:d})});R.displayName=P;var S=({contentRef:e})=>{let r=`\`${x}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${x}\` by passing a \`${A}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${x}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return s.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(r)},[r,e]),null},z=t(96241),D=t(24934);function U({...e}){return(0,a.jsx)(f,{"data-slot":"alert-dialog",...e})}function E({...e}){return(0,a.jsx)(h,{"data-slot":"alert-dialog-portal",...e})}function q({className:e,...r}){return(0,a.jsx)(g,{"data-slot":"alert-dialog-overlay",className:(0,z.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...r})}function M({className:e,...r}){return(0,a.jsxs)(E,{children:[(0,a.jsx)(q,{}),(0,a.jsx)(j,{"data-slot":"alert-dialog-content",className:(0,z.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r})]})}function F({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,z.cn)("flex flex-col gap-2 text-center sm:text-left",e),...r})}function L({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,z.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...r})}function _({className:e,...r}){return(0,a.jsx)(N,{"data-slot":"alert-dialog-title",className:(0,z.cn)("text-lg font-semibold",e),...r})}function I({className:e,...r}){return(0,a.jsx)(C,{"data-slot":"alert-dialog-description",className:(0,z.cn)("text-muted-foreground text-sm",e),...r})}function B({className:e,...r}){return(0,a.jsx)(k,{className:(0,z.cn)((0,D.r)(),e),...r})}function $({className:e,...r}){return(0,a.jsx)(R,{className:(0,z.cn)((0,D.r)({variant:"outline"}),e),...r})}},8555:(e,r,t)=>{"use strict";t.d(r,{t:()=>s});var a=t(6475);let s=(0,a.createServerReference)("40d32ea8edc6596cf0013772648e4fa734c9679198",a.callServer,void 0,a.findSourceMapURL,"getPincodeDetails")},8819:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},11997:e=>{"use strict";e.exports=require("punycode")},17090:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},17313:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},17971:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23676:(e,r,t)=>{"use strict";t.d(r,{LE:()=>s,oX:()=>i});var a=t(68567);let s=a.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:a.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),a.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character");let i=a.z.object({mobile:s,password:a.z.string().trim().min(1,{message:"Password is required"})})},24107:(e,r,t)=>{"use strict";function a(e){let{pincode:r,state:t,city:a,locality:s}=e;return!!(r&&""!==r.trim()&&t&&""!==t.trim()&&a&&""!==a.trim()&&s&&""!==s.trim())}function s(e){let r=[];return e.pincode&&""!==e.pincode.trim()||r.push("pincode"),e.state&&""!==e.state.trim()||r.push("state"),e.city&&""!==e.city.trim()||r.push("city"),e.locality&&""!==e.locality.trim()||r.push("locality"),r}function i(e){if(0===e.length)return"";let r=e.map(e=>{switch(e){case"pincode":return"Pincode";case"state":return"State";case"city":return"City";case"locality":return"Locality";default:return e}});if(1===r.length)return`Please update your ${r[0]} in your profile.`;{if(2===r.length)return`Please update your ${r.join(" and ")} in your profile.`;let e=r.pop();return`Please update your ${r.join(", ")}, and ${e} in your profile.`}}t.d(r,{Gs:()=>a,SJ:()=>s,zp:()=>i})},27910:e=>{"use strict";e.exports=require("stream")},28369:(e,r,t)=>{Promise.resolve().then(t.bind(t,77864))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37360:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},38097:(e,r,t)=>{Promise.resolve().then(t.bind(t,80520))},41427:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(65239),s=t(48088),i=t(88170),o=t.n(i),l=t(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["customer",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78155)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,78050)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/customer/profile/page",pathname:"/dashboard/customer/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},48340:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},49625:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},51214:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},55192:(e,r,t)=>{"use strict";t.d(r,{BT:()=>n,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>c});var a=t(60687);t(43210);var s=t(96241);function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function o({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function l({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...r})}function n({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56528:(e,r,t)=>{"use strict";t.d(r,{JM:()=>l,eb:()=>o,tz:()=>i});var a=t(91199);t(42087);var s=t(76881);async function i(e){if(!e||!/^\d{6}$/.test(e))return{error:"Invalid Pincode format."};let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.from("pincodes").select("OfficeName, DivisionName, StateName").eq("Pincode",e).order("OfficeName");if(a)return console.error("Pincode Fetch Error:",a),{error:"Database error fetching pincode details."};if(!t||0===t.length)return{error:"Pincode not found."};let s=t[0].StateName,i=t[0].DivisionName,o=[...new Set(t.map(e=>e.OfficeName))];return{data:{city:i,state:s,localities:o},city:i,state:s,localities:o}}catch(e){return console.error("Pincode Lookup Exception:",e),{error:"An unexpected error occurred during pincode lookup."}}}async function o(e){if(!e||e.length<2)return{error:"City name must be at least 2 characters."};let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.from("pincodes").select("Pincode, OfficeName, StateName, DivisionName").ilike("DivisionName",`%${e}%`).order("Pincode");if(a)return console.error("City Fetch Error:",a),{error:"Database error fetching city details."};if(!t||0===t.length)return{error:"City not found."};let s=t[0].StateName,i=[...new Set(t.map(e=>e.Pincode))],o=[...new Set(t.map(e=>e.OfficeName))];return{data:{pincodes:i,state:s,localities:o},pincodes:i,state:s,localities:o}}catch(e){return console.error("City Lookup Exception:",e),{error:"An unexpected error occurred during city lookup."}}}async function l(e){if(!e||e.length<2)return{error:"Query must be at least 2 characters."};let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.rpc("get_distinct_cities",{search_query:`%${e}%`,result_limit:5});if(a){console.error("City Suggestions Error:",a);try{let{data:t,error:a}=await r.from("pincodes").select("DivisionName").ilike("DivisionName",`%${e}%`).order("DivisionName").limit(100);if(a)throw a;if(!t||0===t.length)return{data:{cities:[]},cities:[]};let s=[...new Set(t.map(e=>e.DivisionName.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase())))].slice(0,5);return{data:{cities:s},cities:s}}catch(e){return console.error("Fallback City Query Error:",e),{error:"Database error fetching city suggestions."}}}if(!t||0===t.length)return{data:{cities:[]},cities:[]};let s=t.map(e=>e.city.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase()));return{data:{cities:s},cities:s}}catch(e){return console.error("City Suggestions Exception:",e),{error:"An unexpected error occurred while fetching city suggestions."}}}(0,t(33331).D)([i,o,l]),(0,a.A)(i,"40d32ea8edc6596cf0013772648e4fa734c9679198",null),(0,a.A)(o,"40142db2f2e9e23e42f1d64a8d98f054ab16849fcf",null),(0,a.A)(l,"406809393363051c82bcecb759b1153ca34eced5e4",null)},58559:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},62369:(e,r,t)=>{"use strict";t.d(r,{b:()=>d});var a=t(43210),s=t(3416),i=t(60687),o="horizontal",l=["horizontal","vertical"],n=a.forwardRef((e,r)=>{var t;let{decorative:a,orientation:n=o,...d}=e,c=(t=n,l.includes(t))?n:o;return(0,i.jsx)(s.sG.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:r})});n.displayName="Separator";var d=n},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65471:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var a=t(60687),s=t(43210),i=t(47473),o=t(24934),l=t(37826),n=t(41862),d=t(58709);let c=e=>new Promise((r,t)=>{let a=new Image;a.addEventListener("load",()=>r(a)),a.addEventListener("error",e=>t(e)),a.setAttribute("crossOrigin","anonymous"),a.src=e});async function u(e,r){let t=await c(e),a=document.createElement("canvas"),s=a.getContext("2d");if(!s)return null;let i=t.naturalWidth/t.width,o=t.naturalHeight/t.height,l=window.devicePixelRatio||1;return a.width=r.width*l*i,a.height=r.height*l*o,s.setTransform(l,0,0,l,0,0),s.imageSmoothingQuality="high",s.drawImage(t,r.x*i,r.y*o,r.width*i,r.height*o,0,0,r.width*i,r.height*o),new Promise(e=>{a.toBlob(e,"image/png")})}function m({imgSrc:e,onCropComplete:r,onClose:t,isOpen:c}){let[m,p]=(0,s.useState)({x:0,y:0}),[f,h]=(0,s.useState)(1),[g,x]=(0,s.useState)(null),[b,y]=(0,s.useState)(!1),v=(0,s.useCallback)((e,r)=>{x(r)},[]),j=async()=>{if(!e||!g){console.warn("Image source or crop area not available."),r(null);return}y(!0);try{let t=await u(e,g);r(t)}catch(e){console.error("Error cropping image:",e),r(null)}finally{y(!1)}};return(0,a.jsx)(l.lG,{open:c,onOpenChange:e=>!e&&t(),children:(0,a.jsxs)(l.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsx)(l.c7,{children:(0,a.jsx)(l.L3,{children:"Crop Your Logo"})}),(0,a.jsx)("div",{className:"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800",children:e?(0,a.jsx)(i.Ay,{image:e,crop:m,zoom:f,aspect:1,cropShape:"round",showGrid:!1,onCropChange:p,onZoomChange:h,onCropComplete:v}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)("p",{children:"Loading image..."})})}),(0,a.jsx)("div",{className:"px-4 pb-4",children:(0,a.jsx)(d.A,{min:1,max:3,step:.1,value:[f],onValueChange:e=>h(e[0]),className:"w-full","aria-label":"Zoom slider"})}),(0,a.jsxs)(l.Es,{children:[(0,a.jsx)(o.$,{variant:"outline",onClick:t,disabled:b,children:"Cancel"}),(0,a.jsxs)(o.$,{onClick:j,disabled:b,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[b?(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Crop Image"]})]})]})})}},67080:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>a.B,"40142db2f2e9e23e42f1d64a8d98f054ab16849fcf":()=>w.eb,"401ab8dc2d3f647115d61a12212dbd9df058de4b39":()=>k,"4067b0c981cd7953a4ec579dd3c31e777ec76be460":()=>C,"406809393363051c82bcecb759b1153ca34eced5e4":()=>w.JM,"40d32ea8edc6596cf0013772648e4fa734c9679198":()=>w.tz,"40def710293f250d0dac140f014b0c26aab495bbd7":()=>A,"6033e9dd595c9ff2b73428ae4a670f8a040814b1d9":()=>b,"60536d5d116a56d61492068181924f65f50300cc5a":()=>j,"60af724a6b6f142d83cff7c0eff2902b6494266bb2":()=>y,"60c58026dfab279127ad6b1e1e5ce60cc0117e6ee3":()=>x,"60e580379af773ff2a64323c535c68faa2379cad73":()=>g,"60f2708dfd7a6287c4ed7704d0ffa0d07e044962d1":()=>v});var a=t(64275),s=t(91199);t(42087);var i=t(76881),o=t(68567),l=t(7944),n=t(23676),d=t(33331);let c=o.z.object({name:o.z.string().min(1,"Name cannot be empty").max(100,"Name is too long")}),u=o.z.object({phone:n.LE}),m=o.z.object({address:o.z.string().max(100,{message:"Address cannot exceed 100 characters."}).optional().or(o.z.literal("")),pincode:o.z.string().min(1,{message:"Pincode is required"}).regex(/^\d{6}$/,{message:"Must be a valid 6-digit pincode"}),city:o.z.string().min(1,{message:"City is required"}).refine(e=>e.trim().length>0,{message:"City cannot be empty"}),state:o.z.string().min(1,{message:"State is required"}).refine(e=>e.trim().length>0,{message:"State cannot be empty"}),locality:o.z.string().min(1,{message:"Locality is required"}).refine(e=>e.trim().length>0,{message:"Locality cannot be empty"})}),p=o.z.object({email:o.z.string().email({message:"Please enter a valid email address"})}),f=o.z.object({mobile:n.LE}),h=o.z.object({name:o.z.string().min(1,"Name cannot be empty").max(100,"Name is too long"),address:o.z.string().max(100,{message:"Address cannot exceed 100 characters."}).optional().or(o.z.literal("")),pincode:o.z.string().min(1,{message:"Pincode is required"}).regex(/^\d{6}$/,{message:"Must be a valid 6-digit pincode"}).optional().or(o.z.literal("")),city:o.z.string().optional().or(o.z.literal("")),state:o.z.string().optional().or(o.z.literal("")),locality:o.z.string().optional().or(o.z.literal(""))});async function g(e,r){let t=await (0,i.createClient)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{message:"Not authenticated",success:!1};let o=c.safeParse({name:r.get("name")});if(!o.success)return{message:"Invalid data provided.",errors:o.error.flatten().fieldErrors,success:!1};let{name:n}=o.data;try{let{error:e}=await t.auth.updateUser({data:{full_name:n}});if(e)return console.error("Error updating auth user metadata:",e),{message:`Auth Error: ${e.message}`,success:!1};return(0,l.revalidatePath)("/dashboard/customer/profile"),(0,l.revalidatePath)("/dashboard/customer/layout"),{message:"Profile updated successfully!",success:!0}}catch(e){return console.error("Unexpected error updating profile:",e),{message:"An unexpected error occurred.",success:!1}}}async function x(e,r){let t=await (0,i.createClient)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{message:"Not authenticated",success:!1};let o=m.safeParse({address:r.get("address"),pincode:r.get("pincode"),city:r.get("city"),state:r.get("state"),locality:r.get("locality")});if(!o.success)return{message:"Invalid data provided.",errors:o.error.flatten().fieldErrors,success:!1};let{address:n,pincode:d,city:c,state:u,locality:p}=o.data;try{let{error:e}=await t.from("customer_profiles").update({address:n||null,pincode:d,city:c,state:u,locality:p}).eq("id",a.id);if(e)return console.error("Error updating customer address:",e),{message:`Database Error: ${e.message}`,success:!1};return(0,l.revalidatePath)("/dashboard/customer/profile"),(0,l.revalidatePath)("/dashboard/customer"),{message:"Address updated successfully!",success:!0}}catch(e){return console.error("Unexpected error updating address:",e),{message:"An unexpected error occurred.",success:!1}}}async function b(e,r){let t=await (0,i.createClient)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{message:"Not authenticated",success:!1};let o=u.safeParse({phone:r.get("phone")});if(!o.success)return{message:"Invalid data provided.",errors:o.error.flatten().fieldErrors,success:!1};let{phone:n}=o.data;try{let{error:e}=await t.from("customer_profiles").update({phone:n}).eq("id",a.id);if(e)return console.error("Error updating customer phone:",e),{message:`Database Error: ${e.message}`,success:!1};let{error:r}=await t.auth.updateUser({phone:`+91${n}`});return r&&console.warn("Failed to update auth phone field:",r.message),(0,l.revalidatePath)("/dashboard/customer/profile"),(0,l.revalidatePath)("/dashboard/customer"),{message:"Phone number updated successfully!",success:!0}}catch(e){return console.error("Unexpected error updating phone:",e),{message:"An unexpected error occurred.",success:!1}}}async function y(e,r){let t=await (0,i.createClient)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{message:"Not authenticated",success:!1};let o=p.safeParse({email:r.get("email")});if(!o.success)return{message:"Invalid data provided.",errors:o.error.flatten().fieldErrors,success:!1};let{email:n}=o.data;try{let e=a.app_metadata?.provider==="google",r=!1;if(e&&a.email)try{let e=await (0,i.createClient)(),{data:t}=await e.auth.admin.getUserById(a.id);t?.user?.identities&&(r=t.user.identities.some(e=>"email"===e.provider))}catch(e){console.error("Error checking user auth methods:",e),r=!1}if(e&&!r)return{message:"Email cannot be changed for Google accounts. Your email is linked to your Google account.",success:!1};if(a.email===n)return{message:"Email address is the same as current.",success:!1};let{error:s}=await t.auth.updateUser({email:n});if(s){console.error("Error updating auth email:",s);let e="Failed to update email address.";return s.message.includes("duplicate key value violates unique constraint")?e="This email address is already in use by another account.":s.message.includes("check constraint")?e="Invalid email format provided.":s.message.includes("rate limit")&&(e="Too many requests. Please try again later."),{message:e,success:!1}}return(0,l.revalidatePath)("/dashboard/customer/profile"),(0,l.revalidatePath)("/dashboard/customer"),{message:"Email address updated successfully! You may need to verify the new email address.",success:!0}}catch(e){return console.error("Unexpected error updating email:",e),{message:"An unexpected error occurred.",success:!1}}}async function v(e,r){let t=await (0,i.createClient)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{message:"Not authenticated",success:!1};let o=f.safeParse({mobile:r.get("mobile")});if(!o.success)return{message:"Invalid data provided.",errors:o.error.flatten().fieldErrors,success:!1};let{mobile:n}=o.data;try{let e=a.phone?a.phone.replace(/^\+91/,""):"";if(n===e)return{message:"Mobile number is the same as current.",success:!1};let{error:r}=await t.auth.updateUser({phone:`+91${n}`});if(r){console.error("Error updating auth mobile:",r);let e="Failed to update mobile number.";return r.message.includes("duplicate key value violates unique constraint")?e="This mobile number is already in use by another account.":r.message.includes("check constraint")?e="Invalid mobile number format provided.":r.message.includes("rate limit")&&(e="Too many requests. Please try again later."),{message:e,success:!1}}return(0,l.revalidatePath)("/dashboard/customer/profile"),(0,l.revalidatePath)("/dashboard/customer"),{message:"Mobile number updated successfully!",success:!0}}catch(e){return console.error("Unexpected error updating mobile:",e),{message:"An unexpected error occurred.",success:!1}}}async function j(e,r){let t=await (0,i.createClient)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{message:"Not authenticated",success:!1};let o=h.safeParse({name:r.get("name"),address:r.get("address"),pincode:r.get("pincode"),city:r.get("city"),state:r.get("state"),locality:r.get("locality")});if(!o.success)return{message:"Invalid data provided.",errors:o.error.flatten().fieldErrors,success:!1};let{name:n,address:d,pincode:c,city:u,state:m,locality:p}=o.data,f=c||u||m||p;if(f&&(!c||!u||!m||!p))return{message:"If providing address information, pincode, city, state, and locality are required.",success:!1};try{let{error:e}=await t.auth.updateUser({data:{full_name:n}});if(e)return console.error("Error updating auth user metadata:",e),{message:`Auth Error: ${e.message}`,success:!1};if(f){let{error:e}=await t.from("customer_profiles").update({address:d||null,pincode:c,city:u,state:m,locality:p}).eq("id",a.id);if(e)return console.error("Error updating customer address:",e),{message:`Database Error: ${e.message}`,success:!1}}return(0,l.revalidatePath)("/dashboard/customer/profile"),(0,l.revalidatePath)("/dashboard/customer/layout"),(0,l.revalidatePath)("/dashboard/customer"),{message:f?"Profile and address updated successfully!":"Profile updated successfully!",success:!0}}catch(e){return console.error("Unexpected error updating profile and address:",e),{message:"An unexpected error occurred.",success:!1}}}(0,d.D)([g,x,b,y,v,j]),(0,s.A)(g,"60e580379af773ff2a64323c535c68faa2379cad73",null),(0,s.A)(x,"60c58026dfab279127ad6b1e1e5ce60cc0117e6ee3",null),(0,s.A)(b,"6033e9dd595c9ff2b73428ae4a670f8a040814b1d9",null),(0,s.A)(y,"60af724a6b6f142d83cff7c0eff2902b6494266bb2",null),(0,s.A)(v,"60f2708dfd7a6287c4ed7704d0ffa0d07e044962d1",null),(0,s.A)(j,"60536d5d116a56d61492068181924f65f50300cc5a",null);var w=t(56528),N=t(50937);async function A(e){let r=await (0,i.createClient)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return{success:!1,error:"User not authenticated."};let s=t.id,o=e.get("avatarFile");if(!o)return{success:!1,error:"No avatar file provided."};if(!["image/png","image/jpeg","image/gif","image/webp"].includes(o.type))return{success:!1,error:"Invalid file type."};if(o.size>0xf00000)return{success:!1,error:"File size must be less than 15MB."};try{let e=Buffer.from(await o.arrayBuffer()),t="customers",a=new Date().getTime()+Math.floor(1e3*Math.random()),i=(0,N.tS)(s,a),{error:l}=await r.storage.from(t).upload(i,e,{contentType:o.type,upsert:!0});if(l)return console.error("Avatar Upload Error:",l),{success:!1,error:`Failed to upload avatar: ${l.message}`};let{data:n}=r.storage.from(t).getPublicUrl(i);if(!n?.publicUrl)return console.error("Get Public URL Error: URL data is null or missing publicUrl property for path:",i),{success:!1,error:"Could not retrieve public URL after upload."};return{success:!0,url:n.publicUrl}}catch(e){return console.error("Image Processing/Upload Error:",e),{success:!1,error:"Failed to process or upload image."}}}async function C(e){let r=await (0,i.createClient)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return{success:!1,error:"User not authenticated."};let{error:s}=await r.from("customer_profiles").update({avatar_url:e,updated_at:new Date().toISOString()}).eq("id",t.id);return s?(console.error("Avatar URL Update Error:",s),{success:!1,error:`Failed to update avatar URL: ${s.message}`}):((0,l.revalidatePath)("/dashboard/customer"),(0,l.revalidatePath)("/dashboard/customer/profile"),{success:!0})}async function k(e){let r=await (0,i.createClient)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return{success:!1,error:"User not authenticated."};try{let a=function(e){try{let r=new URL(e).pathname.split("/"),t=r.findIndex(e=>"business"===e||"customers"===e);if(-1===t||t===r.length-1)return null;return r.slice(t+1).join("/")}catch(e){return console.error("Error extracting file path from URL:",e),null}}(e);if(!a)return{success:!1,error:"Invalid avatar URL provided."};let{error:s}=await r.storage.from("customers").remove([a]);if(s)return console.error("Error deleting avatar from storage:",s),{success:!1,error:`Failed to delete avatar from storage: ${s.message}`};let{error:i}=await r.from("customer_profiles").update({avatar_url:null,updated_at:new Date().toISOString()}).eq("id",t.id);if(i)return console.error("Error updating customer profile after avatar deletion:",i),{success:!1,error:`Failed to update profile after avatar deletion: ${i.message}`};return(0,l.revalidatePath)("/dashboard/customer"),(0,l.revalidatePath)("/dashboard/customer/profile"),{success:!0}}catch(e){return console.error("Error in deleteCustomerAvatar:",e),{success:!1,error:e instanceof Error?e.message:"Failed to delete avatar"}}}(0,d.D)([A,C,k]),(0,s.A)(A,"40def710293f250d0dac140f014b0c26aab495bbd7",null),(0,s.A)(C,"4067b0c981cd7953a4ec579dd3c31e777ec76be460",null),(0,s.A)(k,"401ab8dc2d3f647115d61a12212dbd9df058de4b39",null)},74075:e=>{"use strict";e.exports=require("zlib")},77864:(e,r,t)=>{"use strict";t.d(r,{default:()=>Q});var a=t(60687),s=t(43210),i=t(58869),o=t(97992),l=t(41862),n=t(8819),d=t(77882),c=t(27605),u=t(63442),m=t(45880),p=t(6475);let f=(0,p.createServerReference)("60e580379af773ff2a64323c535c68faa2379cad73",p.callServer,void 0,p.findSourceMapURL,"updateCustomerProfile");var h=t(24934),g=t(68988),x=t(39390),b=t(52581),y=t(96241);let v=m.z.object({name:m.z.string().min(1,"Name cannot be empty").max(100,"Name is too long")}),j=(0,s.forwardRef)(function({initialName:e,hideSubmitButton:r=!1},t){let[o,d]=(0,s.useTransition)(),[m,p]=(0,s.useState)({message:null,errors:{},success:!1}),j=(0,c.mN)({resolver:(0,u.u)(v),defaultValues:{name:e||""},mode:"onChange"});(0,s.useImperativeHandle)(t,()=>({getFormData:()=>j.getValues(),validateForm:()=>{j.trigger();let e=Object.keys(j.formState.errors).length>0,r=j.getValues();return!e&&!!(r.name&&r.name.trim().length>0)},getFormErrors:()=>j.formState.errors})),(0,s.useEffect)(()=>{console.log("Form state changed:",m),(null!==m.message||Object.keys(m.errors||{}).length>0)&&(console.log("Response received from server"),m.success?b.oR.success(m.message||"Profile updated successfully!"):m.success||m.errors&&0!==Object.keys(m.errors).length||b.oR.error(m.message))},[m]),(0,s.useEffect)(()=>{e&&j.reset({name:e})},[e,j]);let w=async e=>{console.log("Form submission started");let r=new FormData;r.append("name",e.name),d(async()=>{try{console.log("Dispatching form data to server action");let e=await f({message:null,errors:{},success:!1},r);console.log("Server action completed:",e),p(e)}catch(e){console.error("Error submitting form:",e),p({message:"An unexpected error occurred. Please try again.",success:!1,errors:{}}),b.oR.error("An unexpected error occurred. Please try again.")}})};return(0,a.jsxs)("form",{onSubmit:j.handleSubmit(w),className:"space-y-4","data-testid":"profile-form",children:[m.message&&!m.success&&0===Object.keys(m.errors||{}).length&&(0,a.jsx)("div",{className:"p-3 rounded-md bg-red-50 dark:bg-red-950/30 text-red-600 dark:text-red-400 text-sm",children:m.message}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"name",className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Full Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500"}),(0,a.jsx)(g.p,{id:"name",...j.register("name"),className:(0,y.cn)("pl-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800","focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]","transition-all duration-200",o&&"opacity-70"),placeholder:"Your full name","aria-invalid":!!j.formState.errors.name||!!m.errors?.name,"aria-describedby":"name-error",disabled:o})]}),j.formState.errors.name&&(0,a.jsx)("p",{id:"name-error",className:"text-sm font-medium text-red-500 dark:text-red-400 mt-1",children:j.formState.errors.name.message}),m.errors?.name&&(0,a.jsx)("p",{id:"name-error-server",className:"text-sm font-medium text-red-500 dark:text-red-400 mt-1",children:m.errors.name.join(", ")})]}),!r&&(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)(h.$,{type:"submit",disabled:o||!j.formState.isValid,className:"bg-primary hover:bg-primary/90 text-primary-foreground",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})})]})});var w=t(16189);let N=(0,p.createServerReference)("60c58026dfab279127ad6b1e1e5ce60cc0117e6ee3",p.callServer,void 0,p.findSourceMapURL,"updateCustomerAddress");var A=t(96882),C=t(11437),k=t(17313),P=t(3018),R=t(63974),S=t(58164),z=t(8555);let D=m.z.object({address:m.z.string().max(100,{message:"Address cannot exceed 100 characters."}).optional().or(m.z.literal("")),pincode:m.z.string().min(1,{message:"Pincode is required"}).regex(/^\d{6}$/,{message:"Must be a valid 6-digit pincode"}),city:m.z.string().min(1,{message:"City is required"}).refine(e=>e.trim().length>0,{message:"City cannot be empty"}),state:m.z.string().min(1,{message:"State is required"}).refine(e=>e.trim().length>0,{message:"State cannot be empty"}),locality:m.z.string().min(1,{message:"Locality is required"}).refine(e=>e.trim().length>0,{message:"Locality cannot be empty"})}),U=(0,s.forwardRef)(({initialData:e,hideSubmitButton:r=!1},t)=>{let[i,d]=(0,s.useTransition)(),[m,p]=(0,s.useState)({message:null,success:!1,errors:{}}),f=(0,w.useSearchParams)().get("message"),x=(0,c.mN)({resolver:(0,u.u)(D),defaultValues:{address:e?.address||"",pincode:e?.pincode||"",city:e?.city||"",state:e?.state||"",locality:e?.locality||""}});(0,s.useImperativeHandle)(t,()=>({getFormData:()=>x.getValues(),validateForm:()=>(x.trigger(),0===Object.keys(x.formState.errors).length),getFormErrors:()=>x.formState.errors}));let{isPincodeLoading:y,availableLocalities:v,handlePincodeChange:j}=function({form:e,initialPincode:r,initialLocality:t}){let[a,i]=(0,s.useState)(!1),[o,l]=(0,s.useState)([]);return{isPincodeLoading:a,availableLocalities:o,handlePincodeChange:(0,s.useCallback)(async r=>{if(6!==r.length)return;i(!0),l([]),e.setValue("locality",""),e.setValue("city",""),e.setValue("state","");let t=await (0,z.t)(r);i(!1),t.error?b.oR.error(t.error):t.city&&t.state&&t.localities&&(e.setValue("city",t.city,{shouldValidate:!0}),e.setValue("state",t.state,{shouldValidate:!0}),l(t.localities),1===t.localities.length&&e.setValue("locality",t.localities[0],{shouldValidate:!0,shouldDirty:!0}),b.oR.success("City and State auto-filled. Please select your locality."))},[e])}}({form:x,initialPincode:e?.pincode,initialLocality:e?.locality});return(0,s.useEffect)(()=>{f&&b.oR.info(f)},[f]),(0,s.useEffect)(()=>{m.message&&(m.success?(b.oR.success(m.message),p({message:null,success:!1,errors:{}})):b.oR.error(m.message))},[m]),(0,a.jsxs)("div",{className:"space-y-6",children:[f&&(0,a.jsxs)(P.Fc,{className:"border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-amber-600 dark:text-amber-400"}),(0,a.jsx)(P.TN,{className:"text-amber-800 dark:text-amber-200",children:f})]}),(0,a.jsx)(S.lV,{...x,children:(0,a.jsxs)("form",{onSubmit:x.handleSubmit(e=>{let r=new FormData;r.append("address",e.address||""),r.append("pincode",e.pincode),r.append("city",e.city),r.append("state",e.state),r.append("locality",e.locality),d(async()=>{try{let e=await N({message:null,errors:{},success:!1},r);p(e)}catch(e){console.error("Error submitting address form:",e),p({message:"An unexpected error occurred. Please try again.",success:!1,errors:{}})}})}),className:"space-y-4",children:[(0,a.jsx)(S.zB,{control:x.control,name:"address",render:({field:e})=>(0,a.jsxs)(S.eI,{children:[(0,a.jsx)(S.lR,{className:"text-sm font-medium",children:"Address (Optional)"}),(0,a.jsx)(S.MJ,{children:(0,a.jsx)(g.p,{placeholder:"e.g., House/Flat No., Street Name",...e,value:e.value??"",className:"w-full"})}),(0,a.jsx)(S.Rr,{className:"text-xs text-muted-foreground",children:"Your street address or building details"}),(0,a.jsx)(S.C5,{})]})}),(0,a.jsx)(S.zB,{control:x.control,name:"pincode",render:({field:e})=>(0,a.jsxs)(S.eI,{children:[(0,a.jsxs)(S.lR,{className:"text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 text-primary"}),"Pincode *"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.MJ,{className:"flex-1",children:(0,a.jsx)(g.p,{placeholder:"e.g., 751001",...e,value:e.value??"",maxLength:6,type:"number",onChange:r=>{e.onChange(r),6===r.target.value.length&&j(r.target.value)},onInput:e=>{let r=e.target;r.value=r.value.replace(/[^0-9]/g,"")}})}),y&&(0,a.jsx)(l.A,{className:"h-4 w-4 animate-spin text-primary"})]}),(0,a.jsx)(S.Rr,{className:"text-xs text-muted-foreground",children:"6-digit pincode to auto-fill city and state"}),(0,a.jsx)(S.C5,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,a.jsx)(S.zB,{control:x.control,name:"city",render:({field:e})=>(0,a.jsxs)(S.eI,{children:[(0,a.jsxs)(S.lR,{className:"text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-primary/50"}),"City *"]}),(0,a.jsx)(S.MJ,{children:(0,a.jsx)(g.p,{placeholder:"Auto-filled from Pincode",...e,value:e.value??"",className:"bg-muted cursor-not-allowed",readOnly:!0})}),(0,a.jsx)(S.C5,{})]})}),(0,a.jsx)(S.zB,{control:x.control,name:"state",render:({field:e})=>(0,a.jsxs)(S.eI,{children:[(0,a.jsxs)(S.lR,{className:"text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-primary/50"}),"State *"]}),(0,a.jsx)(S.MJ,{children:(0,a.jsx)(g.p,{placeholder:"Auto-filled from Pincode",...e,value:e.value??"",className:"bg-muted cursor-not-allowed",readOnly:!0})}),(0,a.jsx)(S.C5,{})]})})]}),(0,a.jsx)(S.zB,{control:x.control,name:"locality",render:({field:e})=>(0,a.jsxs)(S.eI,{children:[(0,a.jsxs)(S.lR,{className:"text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 text-primary"}),"Locality / Area *"]}),(0,a.jsxs)(R.l6,{onValueChange:e.onChange,value:e.value??"",disabled:0===v.length,children:[(0,a.jsx)(S.MJ,{children:(0,a.jsx)(R.bq,{disabled:0===v.length,className:"w-full",children:(0,a.jsx)(R.yv,{placeholder:0===v.length?"Enter Pincode first":"Select your locality"})})}),(0,a.jsx)(R.gC,{className:"w-full",children:v.map(e=>(0,a.jsx)(R.eb,{value:e,children:e},e))})]}),(0,a.jsx)(S.Rr,{className:"text-xs text-muted-foreground",children:"Select the specific area within the pincode"}),(0,a.jsx)(S.C5,{})]})}),!r&&(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)(h.$,{type:"submit",disabled:i,className:"bg-primary hover:bg-primary/90 text-primary-foreground",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Update Address"]})})})]})})]})});U.displayName="AddressForm";var E=t(70373),q=t(51361),M=t(11860);let F=(0,p.createServerReference)("40def710293f250d0dac140f014b0c26aab495bbd7",p.callServer,void 0,p.findSourceMapURL,"uploadAvatarAndGetUrl"),L=(0,p.createServerReference)("4067b0c981cd7953a4ec579dd3c31e777ec76be460",p.callServer,void 0,p.findSourceMapURL,"updateAvatarUrl"),_=(0,p.createServerReference)("401ab8dc2d3f647115d61a12212dbd9df058de4b39",p.callServer,void 0,p.findSourceMapURL,"deleteCustomerAvatar");var I=t(28640),B=t(65471),$=t(8126);function O({isOpen:e,onClose:r,onConfirm:t,isDeleting:s=!1}){return(0,a.jsx)($.Lt,{open:e,onOpenChange:r,children:(0,a.jsxs)($.EO,{children:[(0,a.jsxs)($.wd,{children:[(0,a.jsx)($.r7,{children:"Remove Profile Picture"}),(0,a.jsx)($.$v,{children:"Are you sure you want to remove your profile picture? This action cannot be undone."})]}),(0,a.jsxs)($.ck,{children:[(0,a.jsx)($.Zr,{disabled:s,children:"Cancel"}),(0,a.jsx)($.Rx,{onClick:t,disabled:s,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:s?"Removing...":"Remove"})]})]})})}function T({initialAvatarUrl:e,userName:r,onUpdateAvatar:t}){let[i,o]=(0,s.useState)(!1),{localPreviewUrl:n,isAvatarUploading:c,imageToCrop:u,onFileSelect:m,handleCropComplete:p,handleCropDialogClose:f,handleAvatarDelete:h,avatarErrorDisplay:x}=function({onUpdateAvatar:e}){let[r,t]=(0,s.useState)("idle"),[a,i]=(0,s.useState)(null),[o,l]=(0,s.useState)(null),[n,d]=(0,s.useTransition)(),[c,u]=(0,s.useState)(null),[m,p]=(0,s.useState)(null),f=async r=>{t("uploading"),i(null),d(async()=>{let a=new FormData;a.append("avatarFile",r);let s=await F(a);if(s.success&&s.url){let r=s.url;t("success"),l(null),o&&URL.revokeObjectURL(o),b.oR.success("Avatar uploaded successfully!");try{let t=await L(r);t.success||b.oR.error(`Avatar uploaded, but failed to save URL: ${t.error}`),t.success&&e(r)}catch(e){console.error("Error saving avatar URL:",e),b.oR.error("Error saving avatar URL after upload.")}}else{t("error");let e=s.error||"Failed to upload avatar.";i(e),l(null),o&&URL.revokeObjectURL(o),e.includes("File size must be less than 15MB")?b.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}):e.includes("Invalid file type")?b.oR.error("Invalid file type",{description:"Please select a JPG, PNG, WebP, or GIF image"}):b.oR.error("Upload failed",{description:e})}})},h=async e=>{if(u(null),e&&m)try{let r=new File([e],m.name,{type:"image/png"}),t=await (0,I.q)(r,{maxDimension:400,targetSizeKB:45}),a=new File([t.blob],m.name,{type:t.blob.type}),s=URL.createObjectURL(a);l(s),f(a)}catch(r){console.error("Image compression failed:",r),b.oR.error("Failed to process image. Please try a different image."),p(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")}else{p(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")}};return{avatarUploadStatus:r,avatarUploadError:a,localPreviewUrl:o,isAvatarUploading:n,imageToCrop:c,onFileSelect:e=>{if(o&&(URL.revokeObjectURL(o),l(null)),e){if(e.size>0xf00000){b.oR.error("File size must be less than 15MB."),t("idle"),i("File size must be less than 15MB."),l(null);return}p(e);let r=new FileReader;r.onloadend=()=>{u(r.result)},r.readAsDataURL(e)}else t("idle"),i(null),l(null)},handleAvatarUpload:f,handleCropComplete:h,handleCropDialogClose:()=>{u(null),p(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")},handleAvatarDelete:async r=>{d(async()=>{try{t("uploading"),i(null);let a=await _(r);a.success?(t("success"),l(null),e(""),b.oR.success("Avatar deleted successfully!")):(t("error"),i(a.error||"Failed to delete avatar"),b.oR.error(a.error||"Failed to delete avatar"))}catch(r){t("error");let e=r instanceof Error?r.message:"Failed to delete avatar";i(e),b.oR.error(e)}})},avatarErrorDisplay:"error"===r&&a?a:null}}({initialAvatarUrl:e,onUpdateAvatar:t}),v=(e=>{if(!e)return"U";let r=e.split(/\s+/);return 1===r.length?e.substring(0,2).toUpperCase():(r[0].charAt(0)+r[r.length-1].charAt(0)).toUpperCase()})(r),j=!!(n||e);return(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsxs)(d.P.div,{className:"relative",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300,damping:15},children:[(0,a.jsxs)(E.eu,{className:(0,y.cn)("h-32 w-32","border-4 border-primary/20","shadow-2xl","ring-4 ring-primary/10","transition-all duration-300","hover:shadow-3xl hover:ring-primary/20"),children:[n||e?(0,a.jsx)(E.BK,{src:n||e,alt:r||"User"}):null,(0,a.jsx)(E.q5,{className:(0,y.cn)("bg-gradient-to-br from-primary/10 to-primary/20 dark:from-primary/20 dark:to-primary/30","text-primary dark:text-primary text-2xl font-semibold","border border-primary/20"),children:v})]}),(0,a.jsxs)(d.P.label,{htmlFor:"avatar-upload",className:(0,y.cn)("absolute bottom-0 right-0 p-2 rounded-full","bg-primary text-primary-foreground cursor-pointer","hover:bg-primary/90 transition-colors","shadow-lg hover:shadow-xl","border-2 border-background"),whileHover:{scale:1.1},whileTap:{scale:.95},children:[(0,a.jsx)(q.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"sr-only",children:"Upload avatar"})]}),j&&(0,a.jsxs)(d.P.button,{onClick:()=>{o(!0)},className:(0,y.cn)("absolute top-0 right-0 p-2 rounded-full","bg-destructive text-destructive-foreground cursor-pointer","hover:bg-destructive/90 transition-colors","shadow-lg hover:shadow-xl","border-2 border-background"),whileHover:{scale:1.1},whileTap:{scale:.95},disabled:c,children:[(0,a.jsx)(M.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Remove avatar"})]}),(0,a.jsx)(g.p,{id:"avatar-upload",type:"file",accept:"image/png, image/jpeg, image/gif, image/webp",className:"hidden",onChange:e=>m(e.target.files?.[0]||null),disabled:c})]}),c&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-neutral-500 dark:text-neutral-400",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Uploading..."]}),x&&(0,a.jsx)("div",{className:"text-sm text-red-500",children:x}),(0,a.jsx)(B.A,{isOpen:!!u,imgSrc:u,onCropComplete:p,onClose:f}),(0,a.jsx)(O,{isOpen:i,onClose:()=>{o(!1)},onConfirm:()=>{let r=n||e;r&&h(r),o(!1)},isDeleting:c})]})}var V=t(37826),G=t(41550),H=t(48340),Z=t(93613),J=t(5336);function Y({hasCompleteAddress:e=!1}){let[r,t]=(0,s.useState)(!1),[i,l]=(0,s.useState)([]);(0,w.useSearchParams)(),(0,w.useRouter)();let n=e=>{switch(e){case"email":return{icon:(0,a.jsx)(G.A,{className:"w-5 h-5"}),label:"Email Address",description:"Required for account notifications and password reset",color:"text-[#C29D5B]",bgColor:"bg-[#C29D5B]/10",borderColor:"border-[#C29D5B]/20"};case"phone":return{icon:(0,a.jsx)(H.A,{className:"w-5 h-5"}),label:"Mobile Number",description:"Required for account access and verification",color:"text-[#C29D5B]",bgColor:"bg-[#C29D5B]/10",borderColor:"border-[#C29D5B]/20"};case"address":return{icon:(0,a.jsx)(o.A,{className:"w-5 h-5"}),label:"Address Information",description:"Required for location-based services",color:"text-[#C29D5B]",bgColor:"bg-[#C29D5B]/10",borderColor:"border-[#C29D5B]/20"};default:return{icon:(0,a.jsx)(Z.A,{className:"w-5 h-5"}),label:e,description:"Required information",color:"text-[#C29D5B]",bgColor:"bg-[#C29D5B]/10",borderColor:"border-[#C29D5B]/20"}}};return 0===i.length?null:(0,a.jsx)(V.lG,{open:r,onOpenChange:t,children:(0,a.jsxs)(V.Cf,{className:"sm:max-w-lg max-w-[calc(100vw-2rem)] mx-auto p-0 gap-0 overflow-hidden border-0 shadow-2xl",children:[(0,a.jsxs)("div",{className:"relative bg-gradient-to-br from-[#C29D5B] to-[#B08A4A] px-6 py-8 text-white",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/5"}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-3 rounded-xl bg-white/20 backdrop-blur-sm",children:(0,a.jsx)(Z.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(V.L3,{className:"text-xl font-bold text-white mb-1",children:"Complete Your Profile"}),(0,a.jsx)(V.rr,{className:"text-white/80 text-sm",children:"Just a few more details to get started"})]})]})})})]}),(0,a.jsxs)("div",{className:"px-6 py-6",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed",children:"Please add the following required information to unlock all dashboard features and ensure the best experience."})}),(0,a.jsx)("div",{className:"space-y-4 mb-8",children:i.map((e,r)=>{let t=n(e);return(0,a.jsxs)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*r},className:`group relative flex items-start gap-4 p-4 rounded-xl border-2 ${t.borderColor} ${t.bgColor} hover:shadow-md transition-all duration-200`,children:[(0,a.jsx)("div",{className:`flex-shrink-0 p-3 rounded-lg ${t.color} bg-white shadow-sm`,children:t.icon}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-foreground mb-1",children:t.label}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed",children:t.description})]}),(0,a.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full border-2 border-[#C29D5B]/30 bg-white group-hover:border-[#C29D5B] transition-colors duration-200"})]},e)})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(h.$,{onClick:()=>{t(!1)},className:"w-full h-12 bg-gradient-to-r from-[#C29D5B] to-[#B08A4A] hover:from-[#B08A4A] hover:to-[#9A7A3A] text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 border-0",children:[(0,a.jsx)(J.A,{className:"w-5 h-5 mr-2"}),"Got it, let me complete my profile"]}),(0,a.jsx)("p",{className:"text-xs text-center text-muted-foreground px-4 leading-relaxed",children:"You can update these details using the forms below. All information is securely stored and protected."})]})]})]})})}var W=t(55192);let K=(0,p.createServerReference)("60536d5d116a56d61492068181924f65f50300cc5a",p.callServer,void 0,p.findSourceMapURL,"updateCustomerProfileAndAddress");function Q({initialName:e,initialAvatarUrl:r,initialAddressData:t,hasCompleteAddress:c=!1}){let[u,m]=(0,s.useState)(r||void 0),p=(0,s.useRef)(null),f=(0,s.useRef)(null),[g,x]=(0,s.useTransition)();console.log("ProfilePageClient - current isPending state:",g);let[y,v]=(0,s.useState)({message:null,errors:{},success:!1}),w=async()=>{let e=p.current?.getFormData(),r=f.current?.getFormData();if(!p.current?.validateForm())return void b.oR.error("Please check your profile information. Name is required.");let t=f.current?.validateForm()??!0;if(!e?.name?.trim())return void b.oR.error("Name is required");if(r&&(r.pincode||r.city||r.state||r.locality)&&!t)return void b.oR.error("Please complete all required address fields or leave them empty");let a=new FormData;a.append("name",e.name),r&&(a.append("address",r.address||""),a.append("pincode",r.pincode||""),a.append("city",r.city||""),a.append("state",r.state||""),a.append("locality",r.locality||"")),x(async()=>{try{let e=await K({message:null,errors:{},success:!1},a);v(e),console.log("ProfilePageClient - formState after server action:",e),e.success?b.oR.success(e.message||"Profile updated successfully!"):b.oR.error(e.message||"Failed to update profile")}catch(e){console.error("Error submitting unified form:",e),v({message:"An unexpected error occurred. Please try again.",success:!1,errors:{}}),b.oR.error("An unexpected error occurred. Please try again.")}})},N={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y,{hasCompleteAddress:c}),(0,a.jsxs)(d.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,staggerChildren:.1}}},className:"space-y-8",children:[(0,a.jsx)(d.P.div,{variants:N,className:"space-y-6",children:(0,a.jsx)("div",{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,a.jsx)(i.A,{className:"w-5 h-5 text-primary"})}),(0,a.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Customer Profile"})]}),(0,a.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 dark:from-neutral-100 dark:to-neutral-400 bg-clip-text text-transparent",children:"Profile Management"}),(0,a.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 text-lg",children:"Manage your personal information and preferences"})]})})}),(0,a.jsxs)("div",{className:"grid gap-8 grid-cols-1 lg:grid-cols-2",children:[(0,a.jsxs)(d.P.div,{variants:N,className:"space-y-8",children:[(0,a.jsx)(W.Zp,{className:"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsx)(W.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"text-center space-y-6",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(T,{initialAvatarUrl:u,userName:e,onUpdateAvatar:e=>m(e)})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-foreground",children:e||"Customer"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Dukancard Customer"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-neutral-200/60 dark:border-neutral-800/60",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:c?"✓":"○"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Address"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:u?"✓":"○"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Avatar"})]})]})]})})}),(0,a.jsxs)(W.Zp,{className:"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)(W.aR,{className:"border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-500/5 border border-blue-500/20",children:(0,a.jsx)(i.A,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:"Personal Information"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Update your name and personal details"})]})]})}),(0,a.jsx)(W.Wu,{className:"p-8",children:(0,a.jsx)(j,{ref:p,initialName:e,hideSubmitButton:!0})})]})]}),(0,a.jsx)(d.P.div,{variants:N,className:"space-y-8",children:(0,a.jsxs)(W.Zp,{className:"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300 h-fit",children:[(0,a.jsx)(W.aR,{className:"border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/10 to-emerald-500/5 border border-emerald-500/20",children:(0,a.jsx)(o.A,{className:"w-5 h-5 text-emerald-600 dark:text-emerald-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:"Address Information"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Update your address details (optional)"})]})]})}),(0,a.jsx)(W.Wu,{className:"p-8",children:(0,a.jsx)(U,{ref:f,initialData:t||void 0,hideSubmitButton:!0})})]})})]}),(0,a.jsxs)(d.P.div,{variants:N,className:"space-y-6",children:[(0,a.jsx)(W.Zp,{className:"bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 shadow-lg",children:(0,a.jsx)(W.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row items-center justify-between gap-6",children:[(0,a.jsxs)("div",{className:"text-center lg:text-left",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"Ready to save your changes?"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Make sure all information is correct before saving"})]}),(0,a.jsx)(h.$,{onClick:w,disabled:g,size:"lg",className:"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200 min-w-[160px]",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"w-5 h-5 mr-2"}),"Save Profile"]})})]})})}),y.message&&!y.success&&(0,a.jsx)(d.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-4 bg-destructive/10 border border-destructive/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-destructive",children:y.message})})]})]})]})}},78155:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>n});var a=t(37413),s=t(32032),i=t(39916),o=t(80520),l=t(24107);let n={title:"My Profile - Dukancard",description:"Manage your Dukancard customer profile.",robots:"noindex, nofollow"};async function d(){let e=await (0,s.createClient)(),{data:{user:r},error:t}=await e.auth.getUser();(t||!r)&&(0,i.redirect)("/login");let{data:n,error:d}=await e.from("customer_profiles").select("avatar_url, address, pincode, city, state, locality").eq("id",r.id).maybeSingle(),c=null;r.user_metadata?.full_name?c=r.user_metadata.full_name:r.user_metadata?.name&&(c=r.user_metadata.name);let u=null,m=null,p=!1;return d?console.error("Error fetching customer profile:",d):(u=n?.avatar_url||null,m={address:n?.address,pincode:n?.pincode,city:n?.city,state:n?.state,locality:n?.locality},p=(0,l.Gs)(m)),(0,a.jsx)(o.default,{initialName:c,initialAvatarUrl:u,initialAddressData:m,hasCompleteAddress:p})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80520:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\customer\\\\profile\\\\components\\\\ProfilePageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},82621:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("WalletCards",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2",key:"4125el"}],["path",{d:"M3 11h3c.8 0 1.6.3 2.1.9l1.1.9c1.6 1.6 4.1 1.6 5.7 0l1.1-.9c.5-.5 1.3-.9 2.1-.9H21",key:"1dpki6"}]])},84027:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},91645:e=>{"use strict";e.exports=require("net")},93613:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},95682:(e,r,t)=>{"use strict";t.d(r,{Ke:()=>w,R6:()=>v,UC:()=>P,bL:()=>C,l9:()=>k,z3:()=>h});var a=t(43210),s=t(70569),i=t(11273),o=t(65551),l=t(66156),n=t(98599),d=t(3416),c=t(46059),u=t(19344),m=t(60687),p="Collapsible",[f,h]=(0,i.A)(p),[g,x]=f(p),b=a.forwardRef((e,r)=>{let{__scopeCollapsible:t,open:s,defaultOpen:i,disabled:l,onOpenChange:n,...c}=e,[f,h]=(0,o.i)({prop:s,defaultProp:i??!1,onChange:n,caller:p});return(0,m.jsx)(g,{scope:t,disabled:l,contentId:(0,u.B)(),open:f,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),children:(0,m.jsx)(d.sG.div,{"data-state":A(f),"data-disabled":l?"":void 0,...c,ref:r})})});b.displayName=p;var y="CollapsibleTrigger",v=a.forwardRef((e,r)=>{let{__scopeCollapsible:t,...a}=e,i=x(y,t);return(0,m.jsx)(d.sG.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":A(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...a,ref:r,onClick:(0,s.m)(e.onClick,i.onOpenToggle)})});v.displayName=y;var j="CollapsibleContent",w=a.forwardRef((e,r)=>{let{forceMount:t,...a}=e,s=x(j,e.__scopeCollapsible);return(0,m.jsx)(c.C,{present:t||s.open,children:({present:e})=>(0,m.jsx)(N,{...a,ref:r,present:e})})});w.displayName=j;var N=a.forwardRef((e,r)=>{let{__scopeCollapsible:t,present:s,children:i,...o}=e,c=x(j,t),[u,p]=a.useState(s),f=a.useRef(null),h=(0,n.s)(r,f),g=a.useRef(0),b=g.current,y=a.useRef(0),v=y.current,w=c.open||u,N=a.useRef(w),C=a.useRef(void 0);return a.useEffect(()=>{let e=requestAnimationFrame(()=>N.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=f.current;if(e){C.current=C.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let r=e.getBoundingClientRect();g.current=r.height,y.current=r.width,N.current||(e.style.transitionDuration=C.current.transitionDuration,e.style.animationName=C.current.animationName),p(s)}},[c.open,s]),(0,m.jsx)(d.sG.div,{"data-state":A(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!w,...o,ref:h,style:{"--radix-collapsible-content-height":b?`${b}px`:void 0,"--radix-collapsible-content-width":v?`${v}px`:void 0,...e.style},children:w&&i})});function A(e){return e?"open":"closed"}var C=b,k=v,P=w},96882:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97992:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,5880,8567,4851,3442,3064,3037,3739,9538,5918,4812],()=>t(41427));module.exports=a})();