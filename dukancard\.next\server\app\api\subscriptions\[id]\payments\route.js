"use strict";(()=>{var e={};e.id=2820,e.ids=[2820],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},80811:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c});var i=t(96559),o=t(48088),n=t(37719),a=t(32190),p=t(32032),u=t(8033);async function c(e,{params:r}){try{let{id:e}=await r,t=await (0,p.createClient)(),{data:{user:s},error:i}=await t.auth.getUser();if(i||!s)return a.NextResponse.json({error:"Authentication required"},{status:401});let{data:o,error:n}=await t.from("payment_subscriptions").select("id, business_profile_id, razorpay_subscription_id").eq("razorpay_subscription_id",e).maybeSingle();if(n)return console.error("Error fetching subscription:",n),a.NextResponse.json({error:"Could not fetch subscription details"},{status:500});if(!o){console.log(`[SUBSCRIPTION_PAYMENTS] No active subscription found for ID: ${e}, checking user history`);let{data:r}=await t.from("business_profiles").select("id").eq("id",s.id).single();if(!r)return a.NextResponse.json({error:"Unauthorized to access this subscription"},{status:403})}if(o){let{data:e,error:r}=await t.from("business_profiles").select("id").eq("id",o.business_profile_id).single();if(r||!e||e.id!==s.id)return a.NextResponse.json({error:"Unauthorized to access this subscription"},{status:403})}let c=await (0,u.$)(e);if(!c.success)return console.error("Error fetching subscription payments:",c.error),a.NextResponse.json({error:"Failed to fetch subscription payments"},{status:500});return a.NextResponse.json(c)}catch(e){return console.error("Error in subscription payments API:",e),a.NextResponse.json({error:"An unexpected error occurred"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/payments/route",pathname:"/api/subscriptions/[id]/payments",filename:"route",bundlePath:"app/api/subscriptions/[id]/payments/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:f}=d;function b(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,9398,4386,580,6298],()=>t(80811));module.exports=s})();