(()=>{var e={};e.id=2017,e.ids=[2017],e.modules={2005:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(onboarding)\\\\onboarding\\\\OnboardingClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6053:e=>{var r=/\s/;e.exports=function(e){for(var t=e.length;t--&&r.test(e.charAt(t)););return t}},8555:(e,r,t)=>{"use strict";t.d(r,{t:()=>s});var a=t(6475);let s=(0,a.createServerReference)("40d32ea8edc6596cf0013772648e4fa734c9679198",a.callServer,void 0,a.findSourceMapURL,"getPincodeDetails")},8588:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var a=t(60687),s=t(43210),n=t(17971),i=t(13964),l=t(96241),o=t(24934),d=t(4331),c=t(33135),u=t(25499);function m({value:e,onChange:r,placeholder:t="Select a category...",className:m,disabled:g=!1}){let[b,x]=s.useState(!1);return(0,a.jsxs)(c.AM,{open:b,onOpenChange:x,children:[(0,a.jsx)(c.Wv,{asChild:!0,children:(0,a.jsxs)(o.$,{variant:"outline",role:"combobox","aria-expanded":b,className:(0,l.cn)("w-full justify-between h-12 text-sm",m),disabled:g,children:[e?u.qW.find(r=>r.name===e)?.name:t,(0,a.jsx)(n.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,a.jsx)(c.hl,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:(0,a.jsxs)(d.uB,{children:[(0,a.jsx)(d.G7,{placeholder:"Search category...",className:"h-9 border-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"}),(0,a.jsxs)(d.oI,{className:"max-h-[300px]",children:[(0,a.jsx)(d.xL,{children:"No category found."}),(0,a.jsx)(d.L$,{children:u.qW.map(t=>(0,a.jsxs)(d.h_,{value:t.name,onSelect:t=>{r(t===e?"":t),x(!1)},children:[(0,a.jsx)(t.icon,{className:"mr-2 h-4 w-4"}),t.name,(0,a.jsx)(i.A,{className:(0,l.cn)("ml-auto h-4 w-4",e===t.name?"opacity-100":"opacity-0")})]},t.slug))})]})]})})]})}},10663:e=>{e.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11539:(e,r,t)=>{var a=t(37643),s=t(55048),n=t(49227),i=0/0,l=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,d=/^0o[0-7]+$/i,c=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(n(e))return i;if(s(e)){var r="function"==typeof e.valueOf?e.valueOf():e;e=s(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=a(e);var t=o.test(e);return t||d.test(e)?c(e.slice(2),t?2:8):l.test(e)?i:+e}},11637:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\BottomNav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx","default")},11705:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(65239),s=t(48088),n=t(88170),i=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["(onboarding)",{children:["onboarding",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,64870)),"C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,15937)),"C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,19567)),"C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(onboarding)/onboarding/page",pathname:"/onboarding",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},11997:e=>{"use strict";e.exports=require("punycode")},15937:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,generateMetadata:()=>s});var a=t(37413);async function s(){return{title:"Onboarding",description:"Complete your Dukancard setup.",robots:"noindex, nofollow"}}function n({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}t(61120)},17971:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19164:(e,r,t)=>{"use strict";t.d(r,{Nh:()=>s,kn:()=>n});var a=t(73511);let s=e=>a.NB.map(r=>(function(e,r){let t="enterprise"===e.id,a=e.pricing.monthly,s=e.pricing.yearly;return{id:e.id,name:`${e.name} Plan`,razorpayPlanIds:{monthly:e.razorpayPlanIds.monthly||null,yearly:e.razorpayPlanIds.yearly||null},price:t?"Contact Sales":"monthly"===r?`₹${e.pricing.monthly.toLocaleString("en-IN")}`:`₹${e.pricing.yearly.toLocaleString("en-IN")}`,yearlyPrice:t?"Contact Sales":`₹${e.pricing.yearly.toLocaleString("en-IN")}`,period:t?"":"monthly"===r?"/month":"/year",savings:t?void 0:"yearly"===r?`Save ₹${(t?0:12*a-s).toLocaleString("en-IN")}`:void 0,description:e.description,features:e.features.map(e=>{if("Product Listings"===e.name&&e.included){let r="unlimited"===e.limit?"Unlimited":e.limit;return`Product/Service Listings (up to ${r})`}return e.included?e.name:`❌ ${e.name}`}),button:t?"Contact Sales":"Subscribe Now",available:!0,featured:"free"===e.id||"basic"===e.id||"growth"===e.id||"pro"===e.id,recommended:e.recommended||!1,mostPopular:e.recommended||!1}})(r,e)),n=s("monthly")},19567:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(37413);t(61120);var s=t(22255),n=t(23392),i=t(82662),l=t(11637),o=t(32032);async function d({children:e}){let r=await (0,o.createClient)(),{data:{user:t}}=await r.auth.getUser(),d=t?.user_metadata?.full_name??t?.user_metadata?.name??null;return(0,a.jsx)(n.ThemeProvider,{attribute:"class",children:(0,a.jsxs)("div",{className:"min-h-screen flex flex-col bg-background",children:[(0,a.jsxs)(s.default,{userName:d,children:[null," ",null," ",(0,a.jsx)(i.ThemeToggle,{variant:"dashboard"})]}),(0,a.jsx)("main",{className:"flex-grow",children:e}),(0,a.jsx)(l.default,{})]})})}},20540:(e,r,t)=>{var a=t(55048),s=t(70151),n=t(11539),i=Math.max,l=Math.min;e.exports=function(e,r,t){var o,d,c,u,m,g,b=0,x=!1,p=!1,f=!0;if("function"!=typeof e)throw TypeError("Expected a function");function h(r){var t=o,a=d;return o=d=void 0,b=r,u=e.apply(a,t)}function v(e){var t=e-g,a=e-b;return void 0===g||t>=r||t<0||p&&a>=c}function y(){var e,t,a,n=s();if(v(n))return j(n);m=setTimeout(y,(e=n-g,t=n-b,a=r-e,p?l(a,c-t):a))}function j(e){return(m=void 0,f&&o)?h(e):(o=d=void 0,u)}function k(){var e,t=s(),a=v(t);if(o=arguments,d=this,g=t,a){if(void 0===m)return b=e=g,m=setTimeout(y,r),x?h(e):u;if(p)return clearTimeout(m),m=setTimeout(y,r),h(g)}return void 0===m&&(m=setTimeout(y,r)),u}return r=n(r)||0,a(t)&&(x=!!t.leading,c=(p="maxWait"in t)?i(n(t.maxWait)||0,r):c,f="trailing"in t?!!t.trailing:f),k.cancel=function(){void 0!==m&&clearTimeout(m),b=0,o=g=d=m=void 0},k.flush=function(){return void 0===m?u:j(s())},k}},22255:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\MinimalHeader.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx","default")},23676:(e,r,t)=>{"use strict";t.d(r,{LE:()=>s,oX:()=>n});var a=t(68567);let s=a.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:a.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),a.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character");let n=a.z.object({mobile:s,password:a.z.string().trim().min(1,{message:"Password is required"})})},24146:(e,r,t)=>{Promise.resolve().then(t.bind(t,11637)),Promise.resolve().then(t.bind(t,22255)),Promise.resolve().then(t.bind(t,82662)),Promise.resolve().then(t.bind(t,23392))},27467:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},27625:(e,r,t)=>{"use strict";t.d(r,{default:()=>x});var a=t(60687),s=t(43210),n=t.n(s),i=t(16189),l=t(70373),o=t(55629),d=t(24934),c=t(40083),u=t(42154),m=t(4952);let g=(e,r)=>{let t=e||r;if(!t)return"?";let a=t.trim().split(/\s+/);return 1===a.length?a[0].charAt(0).toUpperCase():a[0].charAt(0).toUpperCase()+a[a.length-1].charAt(0).toUpperCase()},b=e=>{let r=e.split("/").filter(Boolean);if(r.includes("customer"))switch(r[r.length-1]){case"customer":return"Feed";case"overview":return"Overview";case"likes":return"My Likes";case"subscriptions":return"Subscriptions";case"reviews":return"My Reviews";case"profile":return"Profile";case"settings":return"Settings";default:return"Dashboard"}if(r.includes("business"))switch(r[r.length-1]){case"business":return"Feed";case"overview":return"Overview";case"analytics":return"Analytics";case"card":return"Manage Card";case"products":return"Products & Services";case"gallery":return"Gallery";case"subscriptions":return"Subscriptions";case"likes":return"Likes";case"reviews":return"Reviews";case"activities":return"Activities";case"settings":return"Settings";case"plan":return"Plan Management";default:return"Business Dashboard"}return"Dashboard"},x=({children:e,businessName:r,logoUrl:t,userName:s})=>{let x=(0,i.usePathname)(),p=b(x),{scrollDirection:f,isScrolled:h}=(0,m.Y)({threshold:50}),v=h&&"down"===f&&(x.startsWith("/dashboard/")||"/discover"===x||x.startsWith("/post/")),y=x.includes("/choose-role")||x.includes("/onboarding"),j=g(s,r),k="User";s&&r?k=`${r} (${s})`:s?k=s:r&&(k=r);let w=n().Children.toArray(e),N=w[0],S=w[1],C=w[2];return(0,a.jsx)("header",{className:`sticky top-0 z-40 w-full border-b border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80 transition-transform duration-300 ease-in-out ${v?"-translate-y-full":"translate-y-0"}`,children:(0,a.jsxs)("div",{className:"container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[N,(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:p})}),S]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[y?(0,a.jsx)("form",{action:u.B,children:(0,a.jsxs)(d.$,{type:"submit",variant:"ghost",className:"flex items-center gap-2 h-10 px-3 rounded-lg",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Log out"})]})}):(0,a.jsxs)(o.rI,{children:[(0,a.jsx)(o.ty,{asChild:!0,children:(0,a.jsxs)(d.$,{variant:"ghost",className:"cursor-pointer flex items-center gap-3 h-10 px-3 rounded-lg focus-visible:ring-0 focus-visible:ring-offset-0",children:[(0,a.jsxs)(l.eu,{className:"h-8 w-8 border-2 border-border",children:[t?(0,a.jsx)(l.BK,{src:t,alt:s||r||"User"}):null,(0,a.jsx)(l.q5,{className:"bg-gradient-to-br from-primary/10 to-primary/5 text-primary font-semibold text-sm",children:j})]}),(0,a.jsx)("span",{className:"hidden sm:block text-sm font-medium text-foreground max-w-32 truncate",children:r||s||"User"})]})}),(0,a.jsxs)(o.SQ,{className:"w-64",align:"end",forceMount:!0,children:[" ",(0,a.jsx)(o.lp,{className:"font-normal",children:(0,a.jsxs)("p",{className:"text-sm font-medium leading-none truncate py-2",children:[k," "]})}),(0,a.jsx)(o.mB,{}),(0,a.jsx)("form",{action:u.B,className:"w-full px-2 py-1.5",children:(0,a.jsxs)(d.$,{type:"submit",variant:"ghost",className:"w-full justify-start font-normal text-sm h-auto py-1 cursor-pointer",children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Log out"})]})})]})]}),C," "]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28559:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(e,r,t)=>{var a=t(79474),s=t(70222),n=t(84713),i=a?a.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?s(e):n(e)}},33135:(e,r,t)=>{"use strict";t.d(r,{AM:()=>i,Wv:()=>l,hl:()=>o});var a=t(60687);t(43210);var s=t(52676),n=t(96241);function i({...e}){return(0,a.jsx)(s.bL,{"data-slot":"popover",...e})}function l({...e}){return(0,a.jsx)(s.l9,{"data-slot":"popover-trigger",...e})}function o({className:e,align:r="center",sideOffset:t=4,...i}){return(0,a.jsx)(s.ZL,{children:(0,a.jsx)(s.UC,{"data-slot":"popover-content",align:r,sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...i})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37643:(e,r,t)=>{var a=t(6053),s=/^\s+/;e.exports=function(e){return e?e.slice(0,a(e)+1).replace(s,""):e}},38213:(e,r,t)=>{Promise.resolve().then(t.bind(t,38606)),Promise.resolve().then(t.bind(t,27625)),Promise.resolve().then(t.bind(t,41956)),Promise.resolve().then(t.bind(t,10218))},41312:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},42154:(e,r,t)=>{"use strict";t.d(r,{B:()=>s});var a=t(6475);let s=(0,a.createServerReference)("00a3593a777ba7988175db3502399fe90e4a6ac663",a.callServer,void 0,a.findSourceMapURL,"signOutUser")},47342:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},48340:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},49227:(e,r,t)=>{var a=t(29395),s=t(27467);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==a(e)}},51487:(e,r,t)=>{Promise.resolve().then(t.bind(t,63779))},55048:e=>{e.exports=function(e){var r=typeof e;return null!=e&&("object"==r||"function"==r)}},55192:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var a=t(60687);t(43210);var s=t(96241);function n({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function l({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...r})}function o({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58164:(e,r,t)=>{"use strict";t.d(r,{C5:()=>h,MJ:()=>p,Rr:()=>f,eI:()=>b,lR:()=>x,lV:()=>d,zB:()=>u});var a=t(60687),s=t(43210),n=t(8730),i=t(27605),l=t(96241),o=t(39390);let d=i.Op,c=s.createContext({}),u=({...e})=>(0,a.jsx)(c.Provider,{value:{name:e.name},children:(0,a.jsx)(i.xI,{...e})}),m=()=>{let e=s.useContext(c),r=s.useContext(g),{getFieldState:t}=(0,i.xW)(),a=(0,i.lN)({name:e.name}),n=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},g=s.createContext({});function b({className:e,...r}){let t=s.useId();return(0,a.jsx)(g.Provider,{value:{id:t},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",e),...r})})}function x({className:e,...r}){let{error:t,formItemId:s}=m();return(0,a.jsx)(o.J,{"data-slot":"form-label","data-error":!!t,className:(0,l.cn)("data-[error=true]:text-destructive",e),htmlFor:s,...r})}function p({...e}){let{error:r,formItemId:t,formDescriptionId:s,formMessageId:i}=m();return(0,a.jsx)(n.DX,{"data-slot":"form-control",id:t,"aria-describedby":r?`${s} ${i}`:`${s}`,"aria-invalid":!!r,...e})}function f({className:e,...r}){let{formDescriptionId:t}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:t,className:(0,l.cn)("text-muted-foreground text-sm",e),...r})}function h({className:e,...r}){let{error:t,formMessageId:s}=m(),n=t?String(t?.message??""):r.children;return n?(0,a.jsx)("p",{"data-slot":"form-message",id:s,className:(0,l.cn)("text-destructive text-sm",e),...r,children:n}):null}},62369:(e,r,t)=>{"use strict";t.d(r,{b:()=>d});var a=t(43210),s=t(3416),n=t(60687),i="horizontal",l=["horizontal","vertical"],o=a.forwardRef((e,r)=>{var t;let{decorative:a,orientation:o=i,...d}=e,c=(t=o,l.includes(t))?o:i;return(0,n.jsx)(s.sG.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:r})});o.displayName="Separator";var d=o},62599:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>a.B,"00c19fcd271ff4dd7deb52f7fbab320718d86640ae":()=>p,"40058db62dc30578fcbfd58f8274f8dc59046ae95e":()=>v,"40142db2f2e9e23e42f1d64a8d98f054ab16849fcf":()=>y.eb,"406809393363051c82bcecb759b1153ca34eced5e4":()=>y.JM,"40d32ea8edc6596cf0013772648e4fa734c9679198":()=>y.tz,"40f3fc57f11db1570845526aba403b36381f06977d":()=>h});var a=t(64275),s=t(91199);t(42087);var n=t(76881),i=t(90141),l=t(7944),o=t(68567),d=t(60043),c=t(33331);async function u(e,r){if((0,l.unstable_noStore)(),!e||e.length<3)return{available:!1,error:"Slug must be at least 3 characters."};if(!o.Yj().regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/).safeParse(e).success)return{available:!1,error:"Invalid format (lowercase, numbers, hyphens only)."};let t=await (0,n.createClient)(),a=r;if(!a){let{data:{user:e}}=await t.auth.getUser();a=e?.id}try{let{data:r,error:s}=await t.from("business_profiles").select("id, business_slug").ilike("business_slug",e).neq("id",a??"").maybeSingle();if(s)return{available:!1,error:"Database error checking slug."};return{available:!r}}catch(e){return{available:!1,error:"An unexpected error occurred."}}}(0,c.D)([u]),(0,s.A)(u,"60d3238d9658733e678eb0e501056bcd3469a6f842",null);var m=t(23676);function g(e){if(!e)return null;let r=e.trim();return(r.startsWith("+91")?r=r.substring(3):12===r.length&&r.startsWith("91")&&(r=r.substring(2)),/^\d{10}$/.test(r))?r:null}var b=t(11798),x=t(63032);async function p(){try{let e=await (0,n.createClient)(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return{error:"User not authenticated."};let{data:a,error:s}=await e.from("business_profiles").select(`
        business_name,
        contact_email,
        member_name,
        title,
        phone,
        business_category,
        business_slug,
        address_line,
        pincode,
        city,
        state,
        locality,
        status
      `).eq("id",r.id).maybeSingle();if(s)return console.error("Error fetching existing business profile:",s),{error:"Failed to fetch existing profile data."};if(!a){let e=null,t=null,a=null;r.phone&&(e=g(r.phone)),r.email&&(t=r.email),r.user_metadata?.full_name?a=r.user_metadata.full_name:r.user_metadata?.name?a=r.user_metadata.name:r.user_metadata?.display_name&&(a=r.user_metadata.display_name);let s={};return e&&(s.phone=e),t&&(s.email=t),a&&(s.memberName=a),{data:s}}let{data:i,error:l}=await e.from("payment_subscriptions").select("plan_id").eq("business_profile_id",r.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();l&&console.error("Error fetching subscription data:",l);let o=g(a.phone)||void 0;!o&&r.phone&&(o=g(r.phone)||void 0);let d=a.member_name||void 0;return!d&&r.user_metadata?.display_name&&(d=r.user_metadata.display_name),{data:{businessName:a.business_name||void 0,email:a.contact_email||void 0,memberName:d,title:a.title||void 0,phone:o,businessCategory:a.business_category||void 0,businessSlug:a.business_slug||void 0,addressLine:a.address_line||void 0,pincode:a.pincode||void 0,city:a.city||void 0,state:a.state||void 0,locality:a.locality||void 0,businessStatus:"online"===a.status?"online":"offline",planId:i?.plan_id||void 0,hasExistingSubscription:!!i&&!!i.plan_id}}}catch(e){return console.error("Unexpected error fetching existing business profile:",e),{error:"An unexpected error occurred."}}}let f=o.z.object({businessName:o.z.string().min(2,{message:"Business name must be at least 2 characters."}),email:o.z.string().email({message:"Please enter a valid email."}),memberName:o.z.string().min(2,{message:"Your name is required."}),title:o.z.string().min(2,{message:"Your title/designation is required."}),phone:m.LE,businessCategory:o.z.string().min(1,{message:"Business category is required."}),businessSlug:o.z.string().min(3,{message:"URL slug must be at least 3 characters."}).regex(/^[a-z0-9-]+$/,{message:"URL slug can only contain lowercase letters, numbers, and hyphens."}),addressLine:o.z.string().min(1,{message:"Address line is required."}),pincode:o.z.string().min(6,{message:"Pincode must be 6 digits."}).max(6,{message:"Pincode must be 6 digits."}).regex(/^\d+$/,{message:"Pincode must contain only digits."}),city:o.z.string().min(1,{message:"City is required."}),state:o.z.string().min(1,{message:"State is required."}),locality:o.z.string().min(1,{message:"Locality/area is required."}),businessStatus:o.z.enum(["online","offline"]).default("online")});async function h(e){let r=await (0,n.createClient)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return{error:"User not authenticated."};let{formData:s,planId:o,redirectSlug:c,message:m}=e;if(!o)return{error:"Plan selection is missing."};if(!d.kn.filter(e=>e.available).map(e=>e.id).includes(o))return{error:"Selected plan is not available. Please choose an active plan."};let g=f.safeParse({businessName:s.get("businessName"),email:s.get("email"),memberName:s.get("memberName"),title:s.get("title"),phone:s.get("phone"),businessCategory:s.get("businessCategory"),businessSlug:s.get("businessSlug"),addressLine:s.get("addressLine"),pincode:s.get("pincode"),city:s.get("city"),state:s.get("state"),locality:s.get("locality"),businessStatus:s.get("businessStatus")});if(!g.success)return console.error("Server-side validation failed:",g.error.flatten().fieldErrors),{error:"Invalid form data provided.",fieldErrors:g.error.flatten().fieldErrors};let{businessName:p,email:h,memberName:v,title:y,phone:j,businessCategory:k,businessSlug:w,addressLine:N,pincode:S,city:C,state:A,locality:P,businessStatus:z}=g.data,_=null;"free"!==o&&(_=new Date(Date.now()+2592e6));let{data:L,error:R}=await r.from("business_profiles").select("id").eq("id",t.id).maybeSingle();if(R)return console.error("Error checking existing business profile:",R),{error:"Database error checking profile."};L&&(c?(0,i.redirect)(`/${c}`):(0,i.redirect)("/dashboard/business"));let{available:E,error:M}=await u(w);if(M)return console.error("Error checking existing slug:",M),{error:"Database error checking URL slug."};if(!E)return{error:"This URL slug is already taken. Please choose another.",fieldErrors:{businessSlug:["This URL slug is already taken."]}};let $={id:t.id,business_name:p,contact_email:h,has_active_subscription:b.ew.shouldHaveActiveSubscription("free"===o?x.d.ACTIVE:x.d.TRIAL,o),member_name:v,title:y,phone:j,business_slug:w,business_category:k,address_line:N,pincode:S,city:C,state:A,locality:P,status:z,latitude:null,longitude:null};_&&($.trial_end_date=_.toISOString());let I={plan_id:o,plan_cycle:"monthly",subscription_status:"free"===o?x.d.ACTIVE:x.d.TRIAL,..."free"===o&&{subscription_start_date:new Date().toISOString()}},{data:q,error:T}=await r.rpc("create_business_profile_atomic",{p_business_data:$,p_subscription_data:I});return T?(console.error("RPC error creating business profile:",T),{error:"Failed to create business profile."}):q?.success?void((0,l.revalidatePath)("/(onboarding)/onboarding"),(0,l.revalidatePath)("/dashboard/business"),c?((0,l.revalidatePath)(`/${c}`),(0,i.redirect)(`/${c}`)):(0,i.redirect)("/dashboard/business")):(console.error("Business profile creation failed:",q?.error),q?.error_code==="SLUG_EXISTS")?{error:"This URL slug is already taken. Please choose another.",fieldErrors:{businessSlug:["This URL slug is already taken."]}}:{error:q?.error||"Failed to create business profile."}}async function v(e){return{available:(await u(e)).available}}(0,c.D)([p,h,v]),(0,s.A)(p,"00c19fcd271ff4dd7deb52f7fbab320718d86640ae",null),(0,s.A)(h,"40f3fc57f11db1570845526aba403b36381f06977d",null),(0,s.A)(v,"40058db62dc30578fcbfd58f8274f8dc59046ae95e",null);var y=t(56528)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63779:(e,r,t)=>{"use strict";t.d(r,{default:()=>er});var a=t(60687),s=t(43210),n=t.n(s),i=t(55192),l=t(78377),o=t(58164),d=t(16189),c=t(38398),u=t(52581),m=t(19164),g=t(6475);g.callServer,g.findSourceMapURL;var b=t(27605),x=t(63442);let p=(0,g.createServerReference)("40f3fc57f11db1570845526aba403b36381f06977d",g.callServer,void 0,g.findSourceMapURL,"createBusinessProfile");var f=t(45880),h=t(87033);let v=f.z.object({businessName:f.z.string().min(2,{message:"Business name must be at least 2 characters."}),email:f.z.string().email({message:"Please enter a valid email."}),memberName:f.z.string().min(2,{message:"Your name is required."}),title:f.z.string().min(2,{message:"Your title/designation is required."}),phone:h.LE,businessCategory:f.z.string().min(1,{message:"Business category is required."}),businessSlug:f.z.string().min(3,{message:"URL slug must be at least 3 characters."}).regex(/^[a-z0-9-]+$/,{message:"URL slug can only contain lowercase letters, numbers, and hyphens."}),addressLine:f.z.string().min(1,{message:"Address line is required."}),pincode:f.z.string().min(6,{message:"Pincode must be 6 digits."}).max(6,{message:"Pincode must be 6 digits."}).regex(/^\d+$/,{message:"Pincode must contain only digits."}),city:f.z.string().min(1,{message:"City is required."}),state:f.z.string().min(1,{message:"State is required."}),locality:f.z.string().min(1,{message:"Locality/area is required."}),businessStatus:f.z.enum(["online","offline"]).default("online"),planId:f.z.string().min(1,{message:"Please select a plan."})}),y=[["businessName","email"],["memberName","title","phone","businessCategory","businessSlug"],["addressLine","pincode","city","state","locality","businessStatus"],["planId"]],j=["Business Details","Card Information","Address & Status","Choose Your Plan"],k=["Let's set up your business profile","Let's create your unique digital business card","Complete your business address and status","Select the perfect plan for your business"],w=(e,r)=>3===e&&r?"Review your current plan and complete setup":k[e]||"";t(96241);var N=t(20540),S=t.n(N);let C=(0,g.createServerReference)("40058db62dc30578fcbfd58f8274f8dc59046ae95e",g.callServer,void 0,g.findSourceMapURL,"checkSlugAvailability");var A=t(8555),P=t(13964);function z({currentStep:e,existingData:r,_isLoadingExistingData:t}){return(0,a.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,a.jsx)("div",{className:"flex justify-center items-center mb-4",children:(0,a.jsx)("div",{className:"flex items-center space-x-2",children:[1,2,3,4].map(r=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center transition-colors ${r<e?"bg-primary dark:bg-[var(--brand-gold)] text-white dark:text-black":r===e?"bg-primary/20 dark:bg-[var(--brand-gold)]/20 text-primary dark:text-[var(--brand-gold)] border border-primary dark:border-[var(--brand-gold)]":"bg-muted dark:bg-neutral-800 text-muted-foreground dark:text-neutral-500"}`,children:r<e?(0,a.jsx)(P.A,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-label":"Check"}):(0,a.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:r})}),r<4&&(0,a.jsx)("div",{"data-testid":"progress-line",className:`w-8 sm:w-12 h-0.5 mx-1 transition-colors ${r<e?"bg-primary dark:bg-[var(--brand-gold)]":"bg-muted dark:bg-neutral-700"}`})]},r))})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-xs sm:text-sm text-muted-foreground dark:text-neutral-400 mb-1",children:["Step ",e," of 4"]}),(0,a.jsx)("h3",{className:"text-sm sm:text-base font-medium text-primary dark:text-[var(--brand-gold)]",children:j[e-1]})]}),(0,a.jsx)("div",{className:"text-center mt-6 sm:mt-8",children:(0,a.jsx)("p",{className:"text-sm sm:text-base text-muted-foreground dark:text-neutral-400",children:w(e-1,r?.hasExistingSubscription)},`desc-${e}`)})]})}var _=t(24934),L=t(28559),R=t(70334),E=t(41862);function M({currentStep:e,isSubmitting:r,isCheckingSlug:t,slugAvailable:s,selectedPlan:n,existingData:i,onNextStep:l,onPreviousStep:o,onSubmitIntended:c}){let u=(0,d.useRouter)();return 1===e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(_.$,{type:"button",variant:"outline",onClick:()=>u.push("/choose-role"),className:"cursor-pointer border-border text-foreground hover:bg-muted dark:border-neutral-700 dark:hover:bg-neutral-800 dark:text-neutral-200 group transition-all text-xs sm:text-sm h-9 sm:h-10",children:[(0,a.jsx)(L.A,{className:"mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:-translate-x-1 transition-transform"}),"Back"]}),(0,a.jsxs)(_.$,{type:"button",onClick:l,disabled:r,className:"cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black group transition-all text-xs sm:text-sm h-9 sm:h-10",children:["Next",(0,a.jsx)(R.A,{className:"ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:translate-x-1 transition-transform"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(_.$,{type:"button",variant:"outline",onClick:o,disabled:r,className:"cursor-pointer border-border text-foreground hover:bg-muted dark:border-neutral-700 dark:hover:bg-neutral-800 dark:text-neutral-200 group transition-all text-xs sm:text-sm h-9 sm:h-10",children:[(0,a.jsx)(L.A,{className:"mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:-translate-x-1 transition-transform"}),"Back"]}),e<4?(0,a.jsxs)(_.$,{type:"button",onClick:l,disabled:r||2===e&&(t||!0!==s),className:"cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black group transition-all text-xs sm:text-sm h-9 sm:h-10",children:["Next",(0,a.jsx)(R.A,{className:"ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:translate-x-1 transition-transform"})]}):(0,a.jsxs)(_.$,{type:"submit",className:"cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black relative overflow-hidden group transition-all text-xs sm:text-sm h-9 sm:h-10",disabled:r||!n,onClick:c,children:[(0,a.jsx)("span",{className:"relative z-10 flex items-center",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 animate-spin"}),i?.hasExistingSubscription?"Updating Profile...":n?.id==="free"?"Creating Account...":"Starting Trial..."]}):(0,a.jsxs)(a.Fragment,{children:[i?.hasExistingSubscription?"Complete Profile Setup":n?.id==="free"?"Get Started for FREE":"Finish Setup & Start Trial"," ",(0,a.jsx)(R.A,{className:"w-4 h-4 sm:w-5 sm:h-5 ml-1 sm:ml-2 group-hover:translate-x-1 transition-transform"})]})}),(0,a.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-primary/0 via-white/10 to-primary/0 dark:from-[var(--brand-gold)]/0 dark:via-white/20 dark:to-[var(--brand-gold)]/0 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-in-out"})]})]})}function $({isLoading:e,message:r="Loading your profile data..."}){return e?(0,a.jsx)("div",{className:"absolute inset-0 bg-background/80 dark:bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,a.jsx)(E.A,{className:"w-8 h-8 animate-spin text-primary dark:text-[var(--brand-gold)]",role:"status","aria-label":"Loading"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground dark:text-neutral-400",children:r})]})}):null}var I=t(68988),q=t(17313),T=t(41550);function B({form:e,isSubmitting:r,user:t}){let s=t?.app_metadata?.provider==="google",n=t?.app_metadata?.provider==="email",i=t?.app_metadata?.provider==="phone";return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.zB,{control:e.control,name:"businessName",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Business Name"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.p,{placeholder:"My Awesome Business",...e,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:r}),(0,a.jsx)(q.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsx)(o.zB,{control:e.control,name:"email",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(T.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Contact Email",s&&(0,a.jsx)("span",{className:"text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full",children:"Google Account"}),n&&(0,a.jsx)("span",{className:"text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full",children:"Email Account"}),i&&(0,a.jsx)("span",{className:"text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full",children:"Mobile Account"})]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.p,{placeholder:"<EMAIL>",type:"email",...e,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:r}),(0,a.jsx)(T.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(o.C5,{})]})})]})}var U=t(8588),D=t(58869),O=t(57800),F=t(48340),J=t(47342),V=t(14719),W=t(93613);function G({form:e,isSubmitting:r,slugAvailable:t,isCheckingSlug:s,setSlugToCheck:n,setSlugAvailable:i,user:l}){let d=!!(l?.phone&&l?.phone.trim()!=="");return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.zB,{control:e.control,name:"memberName",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Your Name"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.p,{placeholder:"e.g., John Doe",...e,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:r}),(0,a.jsx)(D.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsx)(o.zB,{control:e.control,name:"title",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(O.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Your Title/Designation"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.p,{placeholder:"e.g., Founder, Manager",...e,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:r}),(0,a.jsx)(O.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsx)(o.zB,{control:e.control,name:"phone",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(F.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Primary Phone Number",d&&(0,a.jsx)("span",{className:"text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full",children:"Mobile Account"})]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.p,{placeholder:"e.g., **********",type:"tel",pattern:"[0-9]*",inputMode:"numeric",...e,onChange:r=>{let t=r.target.value.replace(/^\+91/,"");(t=t.replace(/\D/g,"")).length>10&&(t=t.slice(0,10)),e.onChange(t)},onKeyDown:e=>{let r=/^[0-9]$/.test(e.key),t=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);r||t||e.preventDefault()},className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:r}),(0,a.jsx)(F.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsx)(o.zB,{control:e.control,name:"businessCategory",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(O.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Business Category"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(U.A,{value:e.value,onChange:e.onChange,placeholder:"Select a business category",disabled:r,className:"w-full bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 text-foreground transition-all rounded-lg"})}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsx)(o.zB,{control:e.control,name:"businessSlug",render:({field:l})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(J.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Desired Card URL"]}),(0,a.jsx)("div",{className:"flex flex-col space-y-2",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center rounded-lg overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-muted dark:bg-neutral-800 px-3 py-3 flex items-center border-y border-l border-border dark:border-neutral-700 rounded-l-lg",children:[(0,a.jsx)(J.A,{className:"w-4 h-4 text-muted-foreground dark:text-neutral-400 mr-1"}),(0,a.jsx)("span",{className:"text-muted-foreground dark:text-neutral-400 text-sm whitespace-nowrap",children:"dukancard.in/"})]}),(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(o.MJ,{children:(0,a.jsx)(I.p,{placeholder:"your-business-name",...l,onChange:e=>{let r=e.target.value.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,"");l.onChange(r),r.length>=3&&/^[a-z0-9-]+$/.test(r)&&n&&i?(console.log("Client: Setting slug to check:",r),n(r),i(null)):i&&i(null)},className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 h-12 text-foreground transition-all rounded-none rounded-r-lg border-l-0 pl-3 pr-10",disabled:r})}),(0,a.jsxs)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center w-5 h-5",children:[s&&(0,a.jsx)(E.A,{className:"w-5 h-5 text-muted-foreground animate-spin"}),!s&&!0===t&&(0,a.jsx)(V.A,{className:"w-5 h-5 text-green-500"}),!s&&!1===t&&(0,a.jsx)(W.A,{className:"w-5 h-5 text-red-500"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center h-5 px-1",children:[s&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Checking availability..."}),!s&&!0===t&&!e.formState.errors.businessSlug&&(0,a.jsxs)("p",{className:"text-xs text-green-500 flex items-center gap-1",children:[(0,a.jsx)(P.A,{className:"w-3 h-3"})," URL is available!"]})]})]})}),(0,a.jsx)(o.C5,{})]})})]})}var Y=t(63974),Z=t(32192),H=t(97992);function X({form:e,isSubmitting:r,availableLocalities:t=[],isPincodeLoading:s=!1,handlePincodeChange:n}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.zB,{control:e.control,name:"addressLine",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(Z.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Address Line"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.p,{placeholder:"e.g., 123 Main Street, Building A",...e,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:r}),(0,a.jsx)(Z.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsx)(o.zB,{control:e.control,name:"pincode",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Pincode"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.p,{placeholder:"e.g., 110001",type:"tel",pattern:"[0-9]*",inputMode:"numeric",...e,onChange:r=>{let t=r.target.value.replace(/\D/g,"");e.onChange(t),6===t.length&&n&&n(t)},onKeyDown:e=>{let r=/^[0-9]$/.test(e.key),t=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);r||t||e.preventDefault()},className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 pr-10 h-12 text-foreground transition-all rounded-lg",disabled:r}),(0,a.jsx)(H.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"}),s&&(0,a.jsx)(E.A,{className:"absolute right-3 top-3.5 w-5 h-5 text-muted-foreground animate-spin"})]})}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(o.zB,{control:e.control,name:"city",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"City"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(I.p,{placeholder:"Auto-filled from pincode",...e,className:"bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 h-12 transition-all rounded-lg cursor-not-allowed",disabled:!0,readOnly:!0})}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsx)(o.zB,{control:e.control,name:"state",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"State"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(I.p,{placeholder:"Auto-filled from pincode",...e,className:"bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 h-12 transition-all rounded-lg cursor-not-allowed",disabled:!0,readOnly:!0})}),(0,a.jsx)(o.C5,{})]})})]}),(0,a.jsx)(o.zB,{control:e.control,name:"locality",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Locality/Area"]}),(0,a.jsxs)(Y.l6,{onValueChange:e.onChange,value:e.value??"",disabled:0===t.length||r,children:[(0,a.jsx)(o.MJ,{children:(0,a.jsx)(Y.bq,{className:"w-full bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 h-12 text-foreground transition-all rounded-lg",children:(0,a.jsx)(Y.yv,{placeholder:0===t.length?"Enter Pincode first":"Select your locality"})})}),(0,a.jsx)(Y.gC,{className:"w-full border border-border dark:border-neutral-700 rounded-lg shadow-lg",children:t.map(e=>(0,a.jsx)(Y.eb,{value:e,className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20",children:e},e))})]}),(0,a.jsx)(o.C5,{})]})}),(0,a.jsx)(o.zB,{control:e.control,name:"businessStatus",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Business Status"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>e.onChange("online"),className:`flex-1 p-3 rounded-lg border transition-all ${"online"===e.value?"bg-primary/10 border-primary text-primary dark:bg-[var(--brand-gold)]/10 dark:border-[var(--brand-gold)] dark:text-[var(--brand-gold)]":"bg-background border-border text-muted-foreground hover:border-primary/50 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)]/50"}`,disabled:r,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Online"}),(0,a.jsx)("div",{className:"text-xs opacity-75",children:"Ready to serve customers"})]})}),(0,a.jsx)("button",{type:"button",onClick:()=>e.onChange("offline"),className:`flex-1 p-3 rounded-lg border transition-all ${"offline"===e.value?"bg-primary/10 border-primary text-primary dark:bg-[var(--brand-gold)]/10 dark:border-[var(--brand-gold)] dark:text-[var(--brand-gold)]":"bg-background border-border text-muted-foreground hover:border-primary/50 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)]/50"}`,disabled:r,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Offline"}),(0,a.jsx)("div",{className:"text-xs opacity-75",children:"Setting up business"})]})})]})}),(0,a.jsx)(o.C5,{})]})})]})}var K=t(85778),Q=t(78272);function ee({form:e,isSubmitting:r,existingData:t,selectedPlan:s,setSelectedPlan:i,showPlans:d,setShowPlans:c}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.zB,{control:e.control,name:"planId",render:({field:e})=>(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(K.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Select Plan"]}),(0,a.jsx)(o.MJ,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{r||t?.hasExistingSubscription||!c||c(!d)},className:`flex items-center justify-between w-full border rounded-lg px-4 py-3 text-left h-12 transition-all ${t?.hasExistingSubscription?"bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 cursor-not-allowed opacity-75":"cursor-pointer bg-background border-border hover:border-primary hover:bg-background/80 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)] dark:hover:bg-neutral-800/80 text-foreground dark:text-white"} ${r?"opacity-50":""}`,disabled:r||t?.hasExistingSubscription,children:[(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(K.A,{className:"w-5 h-5 text-muted-foreground dark:text-neutral-400"}),s?s.name:"Select a plan",t?.hasExistingSubscription&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground dark:text-neutral-400",children:"(Current Plan)"})]}),!t?.hasExistingSubscription&&(0,a.jsx)(Q.A,{className:`w-5 h-5 text-muted-foreground dark:text-neutral-400 transition-transform ${d?"rotate-180":""}`})]}),(0,a.jsx)("input",{type:"hidden",...e,value:s?.id||""})]})}),(0,a.jsx)(o.C5,{})]})}),d&&!t?.hasExistingSubscription&&(0,a.jsx)("div",{className:"bg-popover border border-border dark:bg-neutral-900 dark:border-[var(--brand-gold)]/30 rounded-lg shadow-lg overflow-hidden",children:m.kn.map((r,t,o)=>{let d=!r.available;return(0,a.jsxs)(n().Fragment,{children:[(0,a.jsx)("div",{"data-testid":`plan-item-${r.id}`,onClick:()=>{!d&&i&&c&&(i(r),e.setValue("planId",r.id,{shouldValidate:!0}),c(!1))},"aria-disabled":d,tabIndex:d?-1:0,className:`p-4 transition-colors flex flex-col gap-1
                    ${d?"opacity-60 cursor-not-allowed bg-muted dark:bg-neutral-800":"cursor-pointer hover:bg-muted dark:hover:bg-neutral-800"}
                    ${r.recommended?"border-l-4 border-primary dark:border-[var(--brand-gold)]":""}
                    ${s?.id===r.id?"bg-muted dark:bg-neutral-800":""}
                  `,title:d?"This plan is coming soon and cannot be selected.":"",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-medium text-popover-foreground dark:text-white",children:[r.name,d&&(0,a.jsx)("span",{className:"ml-2 text-xs text-muted-foreground dark:text-neutral-400 font-semibold",children:"(Coming Soon)"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground dark:text-neutral-400",children:r.price})]}),s?.id===r.id&&!d&&(0,a.jsx)("div",{className:"w-6 h-6 rounded-full bg-primary dark:bg-[var(--brand-gold)] flex items-center justify-center",children:(0,a.jsx)(P.A,{className:"w-4 h-4 text-white dark:text-black"})}),r.recommended&&(0,a.jsx)("span",{className:"text-xs bg-primary/10 text-primary dark:bg-[var(--brand-gold)]/20 dark:text-[var(--brand-gold)] px-3 py-1 rounded-full font-medium",children:"Recommended"})]})}),t<o.length-1&&(0,a.jsx)(l.w,{className:"bg-border dark:bg-[var(--brand-gold)]/20"})]},r.id)})}),t?.hasExistingSubscription&&s&&(0,a.jsx)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center",children:(0,a.jsx)(V.A,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:["You're already subscribed to ",s.name]}),(0,a.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-300",children:"Your current plan will continue as usual. You can manage your subscription from the dashboard."})]})]})}),(0,a.jsx)("div",{className:"p-5 bg-muted/50 border border-border dark:bg-neutral-900/50 dark:border-[var(--brand-gold)]/20 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 dark:bg-[var(--brand-gold)]/10 flex items-center justify-center",children:(0,a.jsx)(P.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-foreground dark:text-white text-lg",children:t?.hasExistingSubscription?"Continue with Current Plan":s?.id==="free"?"Free Forever Plan":"1 Month Free Trial"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground dark:text-neutral-400",children:t?.hasExistingSubscription?"Complete your profile setup to access all features of your current plan.":s?.id==="free"?"Get your business online instantly with our free plan. Upgrade anytime.":"Start your 30-day free trial of premium features by completing the setup."})]})]})})]})}function er({redirectSlug:e,message:r}={}){let[t,n]=(0,s.useState)(!1),[m,g]=(0,s.useState)(null),{user:f}=function(){(0,d.useRouter)(),(0,c.U)();let[e,r]=(0,s.useState)(null);return{user:e}}(),{isLoadingExistingData:h,existingData:j}=function({user:e,form:r,setSelectedPlan:t}){let[a,n]=(0,s.useState)(!0),[i,l]=(0,s.useState)(null);return{isLoadingExistingData:a,existingData:i}}({user:f,form:null,setSelectedPlan:g}),[k,w]=(0,s.useState)(null),[N,P]=(0,s.useState)(""),{form:_,isSubmitting:L,currentStep:R,handleNextStep:E,handlePreviousStep:I,onSubmitHandler:q,setIsSubmitIntended:T}=function({redirectSlug:e,message:r,user:t,existingData:a,slugAvailable:n,selectedPlan:i}){(0,d.useSearchParams)();let[l,o]=(0,s.useTransition)(),[c,m]=(0,s.useState)(1),[g,f]=(0,s.useState)(e||null),[h,j]=(0,s.useState)(r||null),[k,w]=(0,s.useState)(!1),N=(0,b.mN)({resolver:(0,x.u)(v),mode:"onChange",defaultValues:{businessName:"",email:"",memberName:"",title:"",phone:"",businessCategory:"",businessSlug:"",addressLine:"",pincode:"",city:"",state:"",locality:"",businessStatus:"online",planId:""}}),S=(0,s.useCallback)(async()=>{let e=y[c-1],r=await N.trigger(e);if(2===c){if(!r)return;if(!0!==n)return void N.setError("businessSlug",{type:"manual",message:!1===n?"This URL slug is already taken.":"Please enter a valid slug and wait for check."})}else if(!r)return;w(!1),c<4&&m(c+1)},[c,N,n]),C=(0,s.useCallback)(()=>{w(!1),c>1&&m(c-1)},[c]),A=(0,s.useCallback)(e=>{if(!k)return;if(!i)return void u.oR.error("Please select a plan.");let r=new FormData;Object.entries(e).forEach(([e,t])=>{null!=t&&r.append(e,t)}),r.set("planId",i.id),o(async()=>{let e=await p({formData:r,planId:i.id,redirectSlug:g,message:h});e?.error?(u.oR.error(`Onboarding failed: ${e.error}`),Object.entries(e.fieldErrors||{}).forEach(([e,r])=>{r&&r.length>0&&N.setError(e,{message:r[0]})}),e.fieldErrors?.businessSlug&&m(2)):u.oR.success("Onboarding complete! Welcome aboard.")})},[k,i,g,h,N]);return{form:N,isSubmitting:l,currentStep:c,handleNextStep:S,handlePreviousStep:C,onSubmitHandler:A,setIsSubmitIntended:w}}({redirectSlug:e,message:r,user:f,existingData:j,slugAvailable:k,selectedPlan:m}),{isCheckingSlug:U}=function({form:e,slugToCheck:r,setSlugAvailable:t}){let[a,n]=(0,s.useTransition)(),i=(0,s.useCallback)(async r=>{if(console.log("Client: Performing slug check for:",r),!r||r.length<3||!/^[a-z0-9-]+$/.test(r)){console.log("Client: Slug failed basic validation:",r),t(null);return}n(async()=>{try{console.log("Client: Calling checkSlugAvailability for:",r);let{available:a}=await C(r);console.log("Client: Slug availability result:",{slug:r,available:a}),t(a),a?(console.log("Client: Clearing errors for available slug:",r),e.clearErrors("businessSlug")):(console.log("Client: Setting error for unavailable slug:",r),e.setError("businessSlug",{type:"manual",message:"This URL slug is already taken."}))}catch(r){console.error("Client: Error checking slug availability:",r),t(!1),e.setError("businessSlug",{type:"manual",message:"Error checking slug availability. Please try again."})}})},[e,t,n]);return(0,s.useMemo)(()=>S()(e=>i(e),500),[i]),{isCheckingSlug:a}}({form:_,slugToCheck:N,setSlugAvailable:w}),{isPincodeLoading:D,availableLocalities:O,handlePincodeChange:F}=function({form:e,initialPincode:r,initialLocality:t}){let[a,n]=(0,s.useState)(!1),[i,l]=(0,s.useState)([]);return{isPincodeLoading:a,availableLocalities:i,handlePincodeChange:(0,s.useCallback)(async r=>{if(6!==r.length)return;n(!0),l([]),e.setValue("locality",""),e.setValue("city",""),e.setValue("state","");let t=await (0,A.t)(r);n(!1),t.error?u.oR.error(t.error):t.city&&t.state&&t.localities&&t.localities.length>0&&(e.setValue("city",t.city,{shouldValidate:!0}),e.setValue("state",t.state,{shouldValidate:!0}),l(t.localities),1===t.localities.length&&e.setValue("locality",t.localities[0],{shouldValidate:!0,shouldDirty:!0}),u.oR.success("City and State auto-filled. Please select your locality."))},[e])}}({form:_,initialPincode:j?.pincode,initialLocality:j?.locality});return(0,a.jsxs)("div",{className:"w-full flex items-center justify-center relative",children:[(0,a.jsx)("div",{className:"fixed -top-24 -left-24 w-64 h-64 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl pointer-events-none"}),(0,a.jsx)("div",{className:"fixed -bottom-32 -right-32 w-96 h-96 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl pointer-events-none"}),(0,a.jsx)("div",{className:"w-full max-w-[90%] sm:max-w-md md:max-w-lg",children:(0,a.jsxs)(i.Zp,{className:"w-full border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl shadow-lg relative overflow-hidden backdrop-blur-sm",children:[(0,a.jsx)($,{isLoading:h}),(0,a.jsx)(z,{currentStep:R,existingData:j,_isLoadingExistingData:h}),(0,a.jsx)(i.Wu,{className:"p-0",children:(0,a.jsx)(o.lV,{..._,children:(0,a.jsxs)("form",{onSubmit:_.handleSubmit(q),className:"space-y-6",children:[(0,a.jsx)("div",{className:"space-y-6",children:(()=>{switch(R){case 1:return(0,a.jsx)(B,{form:_,isSubmitting:L,user:f,existingData:j});case 2:return(0,a.jsx)(G,{form:_,isSubmitting:L,user:f,existingData:j,slugAvailable:k,isCheckingSlug:U,setSlugToCheck:P,setSlugAvailable:w});case 3:return(0,a.jsx)(X,{form:_,isSubmitting:L,user:f,existingData:j,availableLocalities:O,isPincodeLoading:D,handlePincodeChange:F});case 4:return(0,a.jsx)(ee,{form:_,isSubmitting:L,user:f,existingData:j,selectedPlan:m,setSelectedPlan:g,showPlans:t,setShowPlans:n});default:return null}})()},R),(0,a.jsx)(l.w,{className:"bg-border/50 dark:bg-neutral-700/50 my-6 sm:my-8"}),(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsx)(M,{currentStep:R,isSubmitting:L,isCheckingSlug:U,slugAvailable:k,selectedPlan:m,existingData:j,onNextStep:E,onPreviousStep:I,onSubmitIntended:()=>T(!0)})})]})})})]})})]})}},63974:(e,r,t)=>{"use strict";t.d(r,{TR:()=>b,bq:()=>m,eb:()=>x,gC:()=>g,l6:()=>d,s3:()=>c,yv:()=>u});var a=t(60687);t(43210);var s=t(28695),n=t(78272),i=t(13964),l=t(3589),o=t(96241);function d({...e}){return(0,a.jsx)(s.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(s.YJ,{"data-slot":"select-group",...e})}function u({...e}){return(0,a.jsx)(s.WT,{"data-slot":"select-value",...e})}function m({className:e,size:r="default",children:t,...i}){return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[t,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function g({className:e,children:r,position:t="popper",...n}){return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...n,children:[(0,a.jsx)(p,{}),(0,a.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,a.jsx)(f,{})]})})}function b({className:e,...r}){return(0,a.jsx)(s.JU,{"data-slot":"select-label",className:(0,o.cn)("text-muted-foreground px-2 py-1.5 text-xs",e),...r})}function x({className:e,children:r,...t}){return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...t,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:r})]})}function p({className:e,...r}){return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,a.jsx)(l.A,{className:"size-4"})})}function f({className:e,...r}){return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,a.jsx)(n.A,{className:"size-4"})})}},64275:(e,r,t)=>{"use strict";t.d(r,{B:()=>i});var a=t(91199);t(42087);var s=t(76881),n=t(90141);async function i(){let e=await (0,s.createClient)();try{let{error:r}=await e.auth.signOut(),a=await Promise.all([t.e(4208),t.e(4659)]).then(t.bind(t,74208)).then(e=>e.cookies());for(let e of["sb-access-token","sb-refresh-token"])try{a.set(e,"",{expires:new Date(0),maxAge:-1})}catch{}}catch{}return(0,n.redirect)("/login?logged_out=true")}(0,t(33331).D)([i]),(0,a.A)(i,"00a3593a777ba7988175db3502399fe90e4a6ac663",null)},64870:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var a=t(37413);t(61120);var s=t(2005);async function n({searchParams:e}){let{redirect:r,message:t}=await e;return(0,a.jsx)("div",{className:"w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center pt-6 pb-20 md:pb-6",children:(0,a.jsx)(s.default,{redirectSlug:r||null,message:t||null})})}},68988:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(60687);t(43210);var s=t(96241);function n({className:e,type:r,...t}){return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},69024:(e,r,t)=>{"use strict";t.d(r,{Qg:()=>i,bL:()=>o});var a=t(43210),s=t(3416),n=t(60687),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=a.forwardRef((e,r)=>(0,n.jsx)(s.sG.span,{...e,ref:r,style:{...i,...e.style}}));l.displayName="VisuallyHidden";var o=l},69639:(e,r,t)=>{Promise.resolve().then(t.bind(t,2005))},70151:(e,r,t)=>{var a=t(85718);e.exports=function(){return a.Date.now()}},70222:(e,r,t)=>{var a=t(79474),s=Object.prototype,n=s.hasOwnProperty,i=s.toString,l=a?a.toStringTag:void 0;e.exports=function(e){var r=n.call(e,l),t=e[l];try{e[l]=void 0;var a=!0}catch(e){}var s=i.call(e);return a&&(r?e[l]=t:delete e[l]),s}},70334:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78377:(e,r,t)=>{"use strict";t.d(r,{w:()=>i});var a=t(60687);t(43210);var s=t(62369),n=t(96241);function i({className:e,orientation:r="horizontal",decorative:t=!0,...i}){return(0,a.jsx)(s.b,{"data-slot":"separator-root",decorative:t,orientation:r,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...i})}},79428:e=>{"use strict";e.exports=require("buffer")},79474:(e,r,t)=>{e.exports=t(85718).Symbol},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82662:(e,r,t)=>{"use strict";t.d(r,{ThemeToggle:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx","ThemeToggle")},84713:e=>{var r=Object.prototype.toString;e.exports=function(e){return r.call(e)}},85718:(e,r,t)=>{var a=t(10663),s="object"==typeof self&&self&&self.Object===Object&&self;e.exports=a||s||Function("return this")()},85778:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},87033:(e,r,t)=>{"use strict";t.d(r,{LE:()=>s,oX:()=>n});var a=t(45880);let s=a.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:a.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),a.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character");let n=a.z.object({mobile:s,password:a.z.string().trim().min(1,{message:"Password is required"})})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,9398,4386,6724,2997,1107,3206,9190,7793,5880,8567,4851,3442,2836,7342,3037,3739,3511,4,427],()=>t(11705));module.exports=a})();