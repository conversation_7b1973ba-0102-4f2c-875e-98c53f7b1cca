(()=>{var e={};e.id=2770,e.ids=[2770],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29383:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>c,POST:()=>l});var a=r(32190),n=r(32032),i=r(33243),o=r(42651),u=e([i]);async function l(e){try{let t=await e.json(),r=e.headers.get("x-api-key")||"",s=process.env.WEBHOOK_RETRY_API_KEY;if(!s||r!==s)return console.error("[RAZORPAY_WEBHOOK_RETRY] Invalid API key"),a.NextResponse.json({success:!1,message:"Invalid API key"},{status:401});let n=t.max_retry_count||3,u=await (0,o.gz)(n);if(0===u.length)return a.NextResponse.json({success:!0,message:"No pending webhook errors to retry"},{status:200});console.log(`[RAZORPAY_WEBHOOK_RETRY] Found ${u.length} pending webhook errors to retry`);let l=[];for(let e of u)try{await (0,o.z5)(e.id,"retrying",e.retry_count+1);let t=await (0,i.handleRazorpayWebhook)(e.payload,"",e.id,void 0);l.push({error_id:e.id,event_type:e.event_type,subscription_id:e.subscription_id,success:t.success,message:t.message})}catch(t){console.error(`[RAZORPAY_WEBHOOK_RETRY] Error retrying webhook ${e.id}:`,t),await (0,o.z5)(e.id,"failed",e.retry_count+1,t instanceof Error?t.message:String(t)),l.push({error_id:e.id,event_type:e.event_type,subscription_id:e.subscription_id,success:!1,message:t instanceof Error?t.message:String(t)})}return a.NextResponse.json({success:!0,message:`Processed ${u.length} pending webhook errors`,results:l},{status:200})}catch(e){return console.error("[RAZORPAY_WEBHOOK_RETRY] Error processing retry request:",e),a.NextResponse.json({success:!1,message:"Error processing retry request"},{status:500})}}async function c(e){try{let t=e.headers.get("x-api-key")||"",r=process.env.WEBHOOK_RETRY_API_KEY;if(!r||t!==r)return console.error("[RAZORPAY_WEBHOOK_RETRY] Invalid API key"),a.NextResponse.json({success:!1,message:"Invalid API key"},{status:401});let s=await (0,n.createClient)(),{data:i,error:o}=await s.rpc("get_webhook_error_stats");if(o)return console.error("[RAZORPAY_WEBHOOK_RETRY] Error getting webhook error stats:",o),a.NextResponse.json({success:!1,message:"Error getting webhook error stats"},{status:500});return a.NextResponse.json({success:!0,message:"Webhook error stats retrieved",stats:i},{status:200})}catch(e){return console.error("[RAZORPAY_WEBHOOK_RETRY] Error getting webhook error stats:",e),a.NextResponse.json({success:!1,message:"Error getting webhook error stats"},{status:500})}}i=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>a});var s=r(34386);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let a=null,n=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));a=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,a=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,a){let n=()=>{var n,i,o,u,l;return n=e,i=t,o=r,u=s,l=a,"customer_profiles"===n?{data:o&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:o&&"business"===i?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===i?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,r)=>i(e),neq:(t,r)=>i(e),gt:(t,r)=>i(e),gte:(t,r)=>i(e),lt:(t,r)=>i(e),lte:(t,r)=>i(e),like:(t,r)=>i(e),ilike:(t,r)=>i(e),is:(t,r)=>i(e),in:(t,r)=>i(e),contains:(t,r)=>i(e),containedBy:(t,r)=>i(e),rangeGt:(t,r)=>i(e),rangeGte:(t,r)=>i(e),rangeLt:(t,r)=>i(e),rangeLte:(t,r)=>i(e),rangeAdjacent:(t,r)=>i(e),overlaps:(t,r)=>i(e),textSearch:(t,r)=>i(e),match:t=>i(e),not:(t,r,s)=>i(e),or:t=>i(e),filter:(t,r,s)=>i(e),order:(t,r)=>i(e),limit:(t,r)=>i(e),range:(t,r,s)=>i(e),abortSignal:t=>i(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,r,s,a,n)}}(a):n?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await n.set(t,r,s)}catch{}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89093:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var a=r(96559),n=r(48088),i=r(37719),o=r(29383),u=e([o]);o=(u.then?(await u)():u)[0];let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/webhooks/razorpay/retry/route",pathname:"/api/webhooks/razorpay/retry",filename:"route",bundlePath:"app/api/webhooks/razorpay/retry/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\retry\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:g}=c;function l(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,9398,4386,580,5193,8485,9423,5569,3243],()=>r(89093));module.exports=s})();