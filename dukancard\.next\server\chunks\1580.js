exports.id=1580,exports.ids=[1580],exports.modules={713:(e,t,r)=>{var n=r(27071);function o(e){this.mode=n.MODE_8BIT_BYTE,this.data=e}o.prototype={getLength:function(e){return this.data.length},write:function(e){for(var t=0;t<this.data.length;t++)e.put(this.data.charCodeAt(t),8)}},e.exports=o},11437:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},23026:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},25404:e=>{e.exports={L:1,M:0,Q:3,H:2}},27071:e=>{e.exports={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8}},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},32396:(e,t,r)=>{var n=r(27071),o=r(53850),a=r(39106),i={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},l={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;l.getBCHDigit(t)-l.getBCHDigit(l.G15)>=0;)t^=l.G15<<l.getBCHDigit(t)-l.getBCHDigit(l.G15);return(e<<10|t)^l.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;l.getBCHDigit(t)-l.getBCHDigit(l.G18)>=0;)t^=l.G18<<l.getBCHDigit(t)-l.getBCHDigit(l.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;0!=e;)t++,e>>>=1;return t},getPatternPosition:function(e){return l.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,r){switch(e){case i.PATTERN000:return(t+r)%2==0;case i.PATTERN001:return t%2==0;case i.PATTERN010:return r%3==0;case i.PATTERN011:return(t+r)%3==0;case i.PATTERN100:return(Math.floor(t/2)+Math.floor(r/3))%2==0;case i.PATTERN101:return t*r%2+t*r%3==0;case i.PATTERN110:return(t*r%2+t*r%3)%2==0;case i.PATTERN111:return(t*r%3+(t+r)%2)%2==0;default:throw Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new o([1],0),r=0;r<e;r++)t=t.multiply(new o([1,a.gexp(r)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case n.MODE_NUMBER:return 10;case n.MODE_ALPHA_NUM:return 9;case n.MODE_8BIT_BYTE:case n.MODE_KANJI:return 8;default:throw Error("mode:"+e)}if(t<27)switch(e){case n.MODE_NUMBER:return 12;case n.MODE_ALPHA_NUM:return 11;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 10;default:throw Error("mode:"+e)}if(t<41)switch(e){case n.MODE_NUMBER:return 14;case n.MODE_ALPHA_NUM:return 13;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 12;default:throw Error("mode:"+e)}throw Error("type:"+t)},getLostPoint:function(e){for(var t=e.getModuleCount(),r=0,n=0;n<t;n++)for(var o=0;o<t;o++){for(var a=0,i=e.isDark(n,o),l=-1;l<=1;l++)if(!(n+l<0)&&!(t<=n+l))for(var s=-1;s<=1;s++)!(o+s<0)&&!(t<=o+s)&&(0!=l||0!=s)&&i==e.isDark(n+l,o+s)&&a++;a>5&&(r+=3+a-5)}for(var n=0;n<t-1;n++)for(var o=0;o<t-1;o++){var u=0;e.isDark(n,o)&&u++,e.isDark(n+1,o)&&u++,e.isDark(n,o+1)&&u++,e.isDark(n+1,o+1)&&u++,(0==u||4==u)&&(r+=3)}for(var n=0;n<t;n++)for(var o=0;o<t-6;o++)e.isDark(n,o)&&!e.isDark(n,o+1)&&e.isDark(n,o+2)&&e.isDark(n,o+3)&&e.isDark(n,o+4)&&!e.isDark(n,o+5)&&e.isDark(n,o+6)&&(r+=40);for(var o=0;o<t;o++)for(var n=0;n<t-6;n++)e.isDark(n,o)&&!e.isDark(n+1,o)&&e.isDark(n+2,o)&&e.isDark(n+3,o)&&e.isDark(n+4,o)&&!e.isDark(n+5,o)&&e.isDark(n+6,o)&&(r+=40);for(var c=0,o=0;o<t;o++)for(var n=0;n<t;n++)e.isDark(n,o)&&c++;return r+10*(Math.abs(100*c/t/t-50)/5)}};e.exports=l},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},38311:(e,t,r)=>{"use strict";var n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o=c(r(87955)),a=c(r(25404)),i=c(r(57534)),l=r(43210),s=c(l),u=c(r(75352));function c(e){return e&&e.__esModule?e:{default:e}}var d={bgColor:o.default.oneOfType([o.default.object,o.default.string]),fgColor:o.default.oneOfType([o.default.object,o.default.string]),level:o.default.string,size:o.default.number,value:o.default.string.isRequired},f=(0,l.forwardRef)(function(e,t){var r=e.bgColor,o=e.fgColor,l=e.level,c=e.size,d=e.value,f=function(e,t){var r={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r}(e,["bgColor","fgColor","level","size","value"]),h=new i.default(-1,a.default[void 0===l?"L":l]);h.addData(d),h.make();var g=h.modules;return s.default.createElement(u.default,n({},f,{bgColor:void 0===r?"#FFFFFF":r,bgD:g.map(function(e,t){return e.map(function(e,r){return e?"":"M "+r+" "+t+" l 1 0 0 1 -1 0 Z"}).join(" ")}).join(" "),fgColor:void 0===o?"#000000":o,fgD:g.map(function(e,t){return e.map(function(e,r){return e?"M "+r+" "+t+" l 1 0 0 1 -1 0 Z":""}).join(" ")}).join(" "),ref:t,size:void 0===c?256:c,viewBoxSize:g.length}))});f.displayName="QRCode",f.propTypes=d,t.Ay=f},39106:e=>{for(var t={glog:function(e){if(e<1)throw Error("glog("+e+")");return t.LOG_TABLE[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return t.EXP_TABLE[e]},EXP_TABLE:Array(256),LOG_TABLE:Array(256)},r=0;r<8;r++)t.EXP_TABLE[r]=1<<r;for(var r=8;r<256;r++)t.EXP_TABLE[r]=t.EXP_TABLE[r-4]^t.EXP_TABLE[r-5]^t.EXP_TABLE[r-6]^t.EXP_TABLE[r-8];for(var r=0;r<255;r++)t.LOG_TABLE[t.EXP_TABLE[r]]=r;e.exports=t},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41550:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},48340:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},52462:(e,t,r)=>{"use strict";let n;r.d(t,{Tf:()=>ex});let o="[modern-screenshot]",a="undefined"!=typeof window,i=a&&"Worker"in window,l=a&&"atob"in window,s=a&&"btoa"in window,u=a?window.navigator?.userAgent:"",c=u.includes("Chrome"),d=u.includes("AppleWebKit")&&!c,f=u.includes("Firefox"),h=e=>e&&"__CONTEXT__"in e,g=e=>"CSSFontFaceRule"===e.constructor.name,m=e=>"CSSImportRule"===e.constructor.name,p=e=>1===e.nodeType,y=e=>"object"==typeof e.className,w=e=>"image"===e.tagName,v=e=>"use"===e.tagName,b=e=>p(e)&&void 0!==e.style&&!y(e),E=e=>8===e.nodeType,A=e=>3===e.nodeType,C=e=>"IMG"===e.tagName,T=e=>"VIDEO"===e.tagName,x=e=>"CANVAS"===e.tagName,k=e=>"TEXTAREA"===e.tagName,S=e=>"INPUT"===e.tagName,D=e=>"STYLE"===e.tagName,P=e=>"SCRIPT"===e.tagName,N=e=>"SELECT"===e.tagName,L=e=>"SLOT"===e.tagName,B=e=>"IFRAME"===e.tagName,_=(...e)=>console.warn(o,...e),M=e=>e.startsWith("data:");function R(e,t){if(e.match(/^[a-z]+:\/\//i))return e;if(a&&e.match(/^\/\//))return window.location.protocol+e;if(e.match(/^[a-z]+:/i)||!a)return e;let r=O().implementation.createHTMLDocument(),n=r.createElement("base"),o=r.createElement("a");return r.head.appendChild(n),r.body.appendChild(o),t&&(n.href=t),o.href=e,o.href}function O(e){return(e&&p(e)?e?.ownerDocument:e)??window.document}let I="http://www.w3.org/2000/svg";function F(e,t,r){let n=O(r).createElementNS(I,"svg");return n.setAttributeNS(null,"width",e.toString()),n.setAttributeNS(null,"height",t.toString()),n.setAttributeNS(null,"viewBox",`0 0 ${e} ${t}`),n}function $(e,t){let r=new XMLSerializer().serializeToString(e);return t&&(r=r.replace(/[\u0000-\u0008\v\f\u000E-\u001F\uD800-\uDFFF\uFFFE\uFFFF]/gu,"")),`data:image/svg+xml;charset=utf-8,${encodeURIComponent(r)}`}function j(e,t){return new Promise((r,n)=>{let o=new FileReader;o.onload=()=>r(o.result),o.onerror=()=>n(o.error),o.onabort=()=>n(Error(`Failed read blob to ${t}`)),"dataUrl"===t?o.readAsDataURL(e):"arrayBuffer"===t&&o.readAsArrayBuffer(e)})}let U=e=>j(e,"dataUrl");function H(e,t){let r=O(t).createElement("img");return r.decoding="sync",r.loading="eager",r.src=e,r}function z(e,t){return new Promise(r=>{let{timeout:n,ownerDocument:o,onError:a,onWarn:i}=t??{},l="string"==typeof e?H(e,O(o)):e,s=null,u=null;function c(){r(l),s&&clearTimeout(s),u?.()}if(n&&(s=setTimeout(c,n)),T(l)){let e=l.currentSrc||l.src;if(!e)return l.poster?z(l.poster,t).then(r):c();if(l.readyState>=2)return c();let n=t=>{i?.("Failed video load",e,t),a?.(t),c()};u=()=>{l.removeEventListener("loadeddata",c),l.removeEventListener("error",n)},l.addEventListener("loadeddata",c,{once:!0}),l.addEventListener("error",n,{once:!0})}else{let e=w(l)?l.href.baseVal:l.currentSrc||l.src;if(!e)return c();let t=async()=>{if(C(l)&&"decode"in l)try{await l.decode()}catch(t){i?.("Failed to decode image, trying to render anyway",l.dataset.originalSrc||e,t)}c()},r=t=>{i?.("Failed image load",l.dataset.originalSrc||e,t),c()};if(C(l)&&l.complete)return t();u=()=>{l.removeEventListener("load",t),l.removeEventListener("error",r)},l.addEventListener("load",t,{once:!0}),l.addEventListener("error",r,{once:!0})}})}async function q(e,t){b(e)&&(C(e)||T(e)?await z(e,t):await Promise.all(["img","video"].flatMap(r=>Array.from(e.querySelectorAll(r)).map(e=>z(e,t)))))}let W=function(){let e=0,t=()=>`0000${(1679616*Math.random()|0).toString(36)}`.slice(-4);return()=>(e+=1,`u${t()}${e}`)}();function G(e){return e?.split(",").map(e=>e.trim().replace(/"|'/g,"").toLowerCase()).filter(Boolean)}let X=0;async function V(e,t){return h(e)?e:K(e,{...t,autoDestruct:!0})}async function K(e,t){let{scale:r=1,workerUrl:n,workerNumber:l=1}=t||{},s=!!t?.debug,u=t?.features??!0,c=e.ownerDocument??(a?window.document:void 0),d=e.ownerDocument?.defaultView??(a?window:void 0),f=new Map,h={width:0,height:0,quality:1,type:"image/png",scale:r,backgroundColor:null,style:null,filter:null,maximumCanvasSize:0,timeout:3e4,progress:null,debug:s,fetch:{requestInit:{cache:t?.fetch?.bypassingCache?"no-cache":"force-cache"},placeholderImage:"data:image/png;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",bypassingCache:!1,...t?.fetch},fetchFn:null,font:{},drawImageInterval:100,workerUrl:null,workerNumber:l,onCloneNode:null,onEmbedNode:null,onCreateForeignObjectSvg:null,includeStyleProperties:null,autoDestruct:!1,...t,__CONTEXT__:!0,log:function(e){let t=`${o}[#${X}]`;return X++,{time:r=>e&&console.time(`${t} ${r}`),timeEnd:r=>e&&console.timeEnd(`${t} ${r}`),warn:(...t)=>e&&_(...t)}}(s),node:e,ownerDocument:c,ownerWindow:d,dpi:1===r?null:96*r,svgStyleElement:Y(c),svgDefsElement:c?.createElementNS(I,"defs"),svgStyles:new Map,defaultComputedStyles:new Map,workers:[...Array.from({length:i&&n&&l?l:0})].map(()=>{try{let e=new Worker(n);return e.onmessage=async e=>{let{url:t,result:r}=e.data;r?f.get(t)?.resolve?.(r):f.get(t)?.reject?.(Error(`Error receiving message from worker: ${t}`))},e.onmessageerror=e=>{let{url:t}=e.data;f.get(t)?.reject?.(Error(`Error receiving message from worker: ${t}`))},e}catch(e){return h.log.warn("Failed to new Worker",e),null}}).filter(Boolean),fontFamilies:new Map,fontCssTexts:new Map,acceptOfImage:`${[function(e){let t=e?.createElement?.("canvas");return t&&(t.height=t.width=1),!!t&&"toDataURL"in t&&!!t.toDataURL("image/webp").includes("image/webp")}(c)&&"image/webp","image/svg+xml","image/*","*/*"].filter(Boolean).join(",")};q=0.8`,requests:f,drawImageCount:0,tasks:[],features:u,isEnable:e=>"restoreScrollPosition"===e?"boolean"!=typeof u&&(u[e]??!1):"boolean"==typeof u?u:u[e]??!0};h.log.time("wait until load"),await q(e,{timeout:h.timeout,onWarn:h.log.warn}),h.log.timeEnd("wait until load");let{width:g,height:m}=function(e,t){let{width:r,height:n}=t;if(p(e)&&(!r||!n)){let t=e.getBoundingClientRect();r=r||t.width||Number(e.getAttribute("width"))||0,n=n||t.height||Number(e.getAttribute("height"))||0}return{width:r,height:n}}(e,h);return h.width=g,h.height=m,h}function Y(e){if(!e)return;let t=e.createElement("style"),r=t.ownerDocument.createTextNode(`
.______background-clip--text {
  background-clip: text;
  -webkit-background-clip: text;
}
`);return t.appendChild(r),t}async function J(e,t){let{log:r,timeout:n,drawImageCount:o,drawImageInterval:a}=t;r.time("image to canvas");let i=await z(e,{timeout:n,onWarn:t.log.warn}),{canvas:l,context2d:s}=function(e,t){let{width:r,height:n,scale:o,backgroundColor:a,maximumCanvasSize:i}=t,l=e.createElement("canvas");l.width=Math.floor(r*o),l.height=Math.floor(n*o),l.style.width=`${r}px`,l.style.height=`${n}px`,i&&(l.width>i||l.height>i)&&(l.width>i&&l.height>i?l.width>l.height?(l.height*=i/l.width,l.width=i):(l.width*=i/l.height,l.height=i):l.width>i?(l.height*=i/l.width,l.width=i):(l.width*=i/l.height,l.height=i));let s=l.getContext("2d");return s&&a&&(s.fillStyle=a,s.fillRect(0,0,l.width,l.height)),{canvas:l,context2d:s}}(e.ownerDocument,t),u=()=>{try{s?.drawImage(i,0,0,l.width,l.height)}catch(e){t.log.warn("Failed to drawImage",e)}};if(u(),t.isEnable("fixSvgXmlDecode"))for(let e=0;e<o;e++)await new Promise(t=>{setTimeout(()=>{u(),t()},e+a)});return t.drawImageCount=0,r.timeEnd("image to canvas"),l}function Q(e,t){if(e.ownerDocument)try{let t=e.toDataURL();if("data:,"!==t)return H(t,e.ownerDocument)}catch(e){t.log.warn("Failed to clone canvas",e)}let r=e.cloneNode(!1),n=e.getContext("2d"),o=r.getContext("2d");try{n&&o&&o.putImageData(n.getImageData(0,0,e.width,e.height),0,0)}catch(e){t.log.warn("Failed to clone canvas",e)}return r}async function Z(e,t){if(e.ownerDocument&&!e.currentSrc&&e.poster)return H(e.poster,e.ownerDocument);let r=e.cloneNode(!1);r.crossOrigin="anonymous",e.currentSrc&&e.currentSrc!==e.src&&(r.src=e.currentSrc);let n=r.ownerDocument;if(n){let o=!0;if(await z(r,{onError:()=>o=!1,onWarn:t.log.warn}),!o)return e.poster?H(e.poster,e.ownerDocument):r;r.currentTime=e.currentTime,await new Promise(e=>{r.addEventListener("seeked",e,{once:!0})});let a=n.createElement("canvas");a.width=e.offsetWidth,a.height=e.offsetHeight;try{let e=a.getContext("2d");e&&e.drawImage(r,0,0,a.width,a.height)}catch(n){if(t.log.warn("Failed to clone video",n),e.poster)return H(e.poster,e.ownerDocument);return r}return Q(a,t)}return r}let ee=["width","height","-webkit-text-fill-color"],et=["stroke","fill"];function er(e,t,r){let n,o,{defaultComputedStyles:a}=r,i=e.nodeName.toLowerCase(),l=y(e)&&"svg"!==i,s=l?et.map(t=>[t,e.getAttribute(t)]).filter(([,e])=>null!==e):[],u=[l&&"svg",i,s.map((e,t)=>`${e}=${t}`).join(","),t].filter(Boolean).join(":");if(a.has(u))return a.get(u);let c=function(e){let t=e.sandbox;if(!t){let{ownerDocument:r}=e;try{r&&((t=r.createElement("iframe")).id=`__SANDBOX__-${W()}`,t.width="0",t.height="0",t.style.visibility="hidden",t.style.position="fixed",r.body.appendChild(t),t.contentWindow?.document.write('<!DOCTYPE html><meta charset="UTF-8"><title></title><body>'),e.sandbox=t)}catch(t){e.log.warn("Failed to getSandBox",t)}}return t}(r),d=c?.contentWindow;if(!d)return new Map;let f=d?.document;l?(o=(n=f.createElementNS(I,"svg")).ownerDocument.createElementNS(n.namespaceURI,i),s.forEach(([e,t])=>{o.setAttributeNS(null,e,t)}),n.appendChild(o)):n=o=f.createElement(i),o.textContent=" ",f.body.appendChild(n);let h=d.getComputedStyle(o,t),g=new Map;for(let e=h.length,t=0;t<e;t++){let e=h.item(t);ee.includes(e)||g.set(e,h.getPropertyValue(e))}return f.body.removeChild(n),a.set(u,g),g}function en(e,t,r){let n=new Map,o=[],a=new Map;if(r)for(let e of r)i(e);else for(let t=e.length,r=0;r<t;r++)i(e.item(r));for(let e=o.length,t=0;t<e;t++)a.get(o[t])?.forEach((e,t)=>n.set(t,e));function i(r){let i=e.getPropertyValue(r),l=e.getPropertyPriority(r),s=r.lastIndexOf("-"),u=s>-1?r.substring(0,s):void 0;if(u){let e=a.get(u);e||(e=new Map,a.set(u,e)),e.set(r,[i,l])}(t.get(r)!==i||l)&&(u?o.push(u):n.set(r,[i,l]))}return n}let eo=[":before",":after"],ea=[":-webkit-scrollbar",":-webkit-scrollbar-button",":-webkit-scrollbar-thumb",":-webkit-scrollbar-track",":-webkit-scrollbar-track-piece",":-webkit-scrollbar-corner",":-webkit-resizer"],ei=new Set(["symbol"]);async function el(e,t,r,n,o){if(p(r)&&(D(r)||P(r))||n.filter&&!n.filter(r))return;ei.has(t.nodeName)||ei.has(r.nodeName)?n.currentParentNodeStyle=void 0:n.currentParentNodeStyle=n.currentNodeStyle;let a=await ec(r,n,!1,o);n.isEnable("restoreScrollPosition")&&function(e,t){if(!b(e)||!b(t))return;let{scrollTop:r,scrollLeft:n}=e;if(!r&&!n)return;let{transform:o}=t.style,a=new DOMMatrix(o),{a:i,b:l,c:s,d:u}=a;a.a=1,a.b=0,a.c=0,a.d=1,a.translateSelf(-n,-r),a.a=i,a.b=l,a.c=s,a.d=u,t.style.transform=a.toString()}(e,a),t.appendChild(a)}async function es(e,t,r,n){let o=(p(e)?e.shadowRoot?.firstChild:void 0)??e.firstChild;for(let a=o;a;a=a.nextSibling)if(!E(a))if(p(a)&&L(a)&&"function"==typeof a.assignedNodes){let o=a.assignedNodes();for(let a=0;a<o.length;a++)await el(e,t,o[a],r,n)}else await el(e,t,a,r,n)}let eu=/^[\w-:]+$/;async function ec(e,t,r=!1,n){let{ownerDocument:o,ownerWindow:a,fontFamilies:i}=t;if(o&&A(e))return n&&/\S/.test(e.data)&&n(e.data),o.createTextNode(e.data);if(o&&a&&p(e)&&(b(e)||y(e))){let n=await function(e,t){if(x(e))return Q(e,t);if(B(e)){try{if(e?.contentDocument?.body)return ec(e.contentDocument.body,t)}catch(e){t.log.warn("Failed to clone iframe",e)}return e.cloneNode(!1)}if(C(e)){let t=e.cloneNode(!1);return e.currentSrc&&e.currentSrc!==e.src&&(t.src=e.currentSrc,t.srcset=""),"lazy"===t.loading&&(t.loading="eager"),t}return T(e)?Z(e,t):e.cloneNode(!1)}(e,t);if(t.isEnable("removeAbnormalAttributes")){let e=n.getAttributeNames();for(let t=e.length,r=0;r<t;r++){let t=e[r];eu.test(t)||n.removeAttribute(t)}}let o=t.currentNodeStyle=function(e,t,r,n){let{ownerWindow:o,includeStyleProperties:a,currentParentNodeStyle:i}=n,l=t.style,s=o.getComputedStyle(e),u=er(e,null,n);i?.forEach((e,t)=>{u.delete(t)});let d=en(s,u,a);d.delete("transition-property"),d.delete("all"),d.delete("d"),d.delete("content"),r&&(d.delete("margin-top"),d.delete("margin-right"),d.delete("margin-bottom"),d.delete("margin-left"),d.delete("margin-block-start"),d.delete("margin-block-end"),d.delete("margin-inline-start"),d.delete("margin-inline-end"),d.set("box-sizing",["border-box",""])),d.get("background-clip")?.[0]==="text"&&t.classList.add("______background-clip--text"),c&&(d.has("font-kerning")||d.set("font-kerning",["normal",""]),(d.get("overflow-x")?.[0]==="hidden"||d.get("overflow-y")?.[0]==="hidden")&&d.get("text-overflow")?.[0]==="ellipsis"&&e.scrollWidth===e.clientWidth&&d.set("text-overflow",["clip",""]));for(let e=l.length,t=0;t<e;t++)l.removeProperty(l.item(t));return d.forEach(([e,t],r)=>{l.setProperty(r,e,t)}),d}(e,n,r,t);r&&function(e,t){let{backgroundColor:r,width:n,height:o,style:a}=t,i=e.style;if(r&&i.setProperty("background-color",r,"important"),n&&i.setProperty("width",`${n}px`,"important"),o&&i.setProperty("height",`${o}px`,"important"),a)for(let e in a)i[e]=a[e]}(n,t);let a=!1;if(t.isEnable("copyScrollbar")){let t=[o.get("overflow-x")?.[0],o.get("overflow-y")?.[0]];a=t.includes("scroll")||(t.includes("auto")||t.includes("overlay"))&&(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth)}let u=o.get("text-transform")?.[0],d=G(o.get("font-family")?.[0]),f=d?e=>{"uppercase"===u?e=e.toUpperCase():"lowercase"===u?e=e.toLowerCase():"capitalize"===u&&(e=e[0].toUpperCase()+e.substring(1)),d.forEach(t=>{let r=i.get(t);r||i.set(t,r=new Set),e.split("").forEach(e=>r.add(e))})}:void 0;var l=a;let{ownerWindow:h,svgStyleElement:g,svgStyles:m,currentNodeStyle:p}=t;function s(r){let o=h.getComputedStyle(e,r),a=o.getPropertyValue("content");if(!a||"none"===a)return;f?.(a),a=a.replace(/(')|(")|(counter\(.+\))/g,"");let i=[W()],l=er(e,r,t);p?.forEach((e,t)=>{l.delete(t)});let s=en(o,l,t.includeStyleProperties);s.delete("content"),s.delete("-webkit-locale"),s.get("background-clip")?.[0]==="text"&&n.classList.add("______background-clip--text");let u=[`content: '${a}';`];if(s.forEach(([e,t],r)=>{u.push(`${r}: ${e}${t?" !important":""};`)}),1===u.length)return;try{n.className=[n.className,...i].join(" ")}catch(e){t.log.warn("Failed to copyPseudoClass",e);return}let c=u.join("\n  "),d=m.get(c);d||(d=[],m.set(c,d)),d.push(`.${i[0]}:${r}`)}return g&&h&&(eo.forEach(s),l&&ea.forEach(s)),(k(e)||S(e)||N(e))&&n.setAttribute("value",e.value),T(e)||await es(e,n,t,f),n}let u=e.cloneNode(!1);return await es(e,u,t),u}function ed(e,t){let{url:r,requestType:n="text",responseType:o="text",imageDom:a}=t,i=r,{timeout:l,acceptOfImage:s,requests:u,fetchFn:c,fetch:{requestInit:h,bypassingCache:g,placeholderImage:m},font:p,workers:y,fontFamilies:w}=e;"image"===n&&(d||f)&&e.drawImageCount++;let v=u.get(r);if(!v){g&&g instanceof RegExp&&g.test(i)&&(i+=(/\?/.test(i)?"&":"?")+new Date().getTime());let t=n.startsWith("font")&&p&&p.minify,f=new Set;t&&n.split(";")[1].split(",").forEach(e=>{w.has(e)&&w.get(e).forEach(e=>f.add(e))});let b=t&&f.size,E={url:i,timeout:l,responseType:b?"arrayBuffer":o,headers:"image"===n?{accept:s}:void 0,...h};(v={type:n,resolve:void 0,reject:void 0,response:null}).response=(async()=>{if(c&&"image"===n){let e=await c(r);if(e)return e}if(!d&&r.startsWith("http")&&y.length)return new Promise((e,t)=>{y[u.size&y.length-1].postMessage({rawUrl:r,...E}),v.resolve=e,v.reject=t});let{url:e,timeout:t,responseType:o,...a}=E,i=new AbortController,l=t?setTimeout(()=>i.abort(),t):void 0;return fetch(e,{signal:i.signal,...a}).then(e=>{if(!e.ok)throw Error("Failed fetch, not 2xx response",{cause:e});switch(o){case"arrayBuffer":return e.arrayBuffer();case"dataUrl":return e.blob().then(U);default:return e.text()}}).finally(()=>clearTimeout(l))})().catch(t=>{if(u.delete(r),"image"===n&&m)return e.log.warn("Failed to fetch image base64, trying to use placeholder image",i),"string"==typeof m?m:m(a);throw t}),u.set(r,v)}return v.response}async function ef(e,t,r,n){if(!eh(e))return e;for(let[o,a]of function(e,t){let r=[];return e.replace(eg,(e,n,o)=>(r.push([o,R(o,t)]),e)),r.filter(([e])=>!M(e))}(e,t))try{let t=await ed(r,{url:a,requestType:n?"image":"text",responseType:"dataUrl"});e=e.replace(function(e){let t=e.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1");return RegExp(`(url\\(['"]?)(${t})(['"]?\\))`,"g")}(o),`$1${t}$3`)}catch(e){r.log.warn("Failed to fetch css data url",o,e)}return e}function eh(e){return/url\((['"]?)([^'"]+?)\1\)/.test(e)}let eg=/url\((['"]?)([^'"]+?)\1\)/g,em=["background-image","border-image-source","-webkit-border-image","-webkit-mask-image","list-style-image"];async function ep(e,t){let{ownerDocument:r,svgStyleElement:n,fontFamilies:o,fontCssTexts:a,tasks:i,font:l}=t;if(r&&n&&o.size)if(l&&l.cssText){let e=eE(l.cssText,t);n.appendChild(r.createTextNode(`${e}
`))}else{let e=Array.from(r.styleSheets).filter(e=>{try{return"cssRules"in e&&!!e.cssRules.length}catch(r){return t.log.warn(`Error while reading CSS rules from ${e.href}`,r),!1}});await Promise.all(e.flatMap(e=>Array.from(e.cssRules).map(async(r,n)=>{if(m(r)){let o=n+1,a=r.href,i="";try{i=await ed(t,{url:a,requestType:"text",responseType:"text"})}catch(e){t.log.warn(`Error fetch remote css import from ${a}`,e)}for(let r of function(e){if(null==e)return[];let t=[],r=e.replace(ey,"");for(;;){let e=ew.exec(r);if(!e)break;t.push(e[0])}r=r.replace(ew,"");let n=/@import[\s\S]*?url\([^)]*\)[\s\S]*?;/gi,o=RegExp("((\\s*?(?:\\/\\*[\\s\\S]*?\\*\\/)?\\s*?@media[\\s\\S]*?){([\\s\\S]*?)}\\s*?})|(([\\s\\S]*?){([\\s\\S]*?)})","gi");for(;;){let e=n.exec(r);if(e)o.lastIndex=n.lastIndex;else if(e=o.exec(r))n.lastIndex=o.lastIndex;else break;t.push(e[0])}return t}(i.replace(eg,(e,t,r)=>e.replace(r,R(r,a)))))try{e.insertRule(r,r.startsWith("@import")?o+=1:e.cssRules.length)}catch(e){t.log.warn("Error inserting rule from remote css import",{rule:r,error:e})}}}))),e.flatMap(e=>Array.from(e.cssRules)).filter(e=>g(e)&&eh(e.style.getPropertyValue("src"))&&G(e.style.getPropertyValue("font-family"))?.some(e=>o.has(e))).forEach(e=>{let o=a.get(e.cssText);o?n.appendChild(r.createTextNode(`${o}
`)):i.push(ef(e.cssText,e.parentStyleSheet?e.parentStyleSheet.href:null,t).then(o=>{o=eE(o,t),a.set(e.cssText,o),n.appendChild(r.createTextNode(`${o}
`))}))})}}let ey=/(\/\*[\s\S]*?\*\/)/g,ew=/((@.*?keyframes [\s\S]*?){([\s\S]*?}\s*?)})/gi,ev=/url\([^)]+\)\s*format\((["']?)([^"']+)\1\)/g,eb=/src:\s*(?:url\([^)]+\)\s*format\([^)]+\)[,;]\s*)+/g;function eE(e,t){let{font:r}=t,n=r?r?.preferredFormat:void 0;return n?e.replace(eb,e=>{for(;;){let[t,,r]=ev.exec(e)||[];if(!r)return"";if(r===n)return`src: ${t};`}}):e}async function eA(e,t){let r=await V(e,t);if(p(r.node)&&y(r.node))return r.node;let{ownerDocument:n,log:o,tasks:a,svgStyleElement:i,svgDefsElement:l,svgStyles:s,font:u,progress:c,autoDestruct:h,onCloneNode:g,onEmbedNode:m,onCreateForeignObjectSvg:E}=r;o.time("clone node");let A=await ec(r.node,r,!0);if(i&&n){let e="";s.forEach((t,r)=>{e+=`${t.join(",\n")} {
  ${r}
}
`}),i.appendChild(n.createTextNode(e))}o.timeEnd("clone node"),await g?.(A),!1!==u&&p(A)&&(o.time("embed web font"),await ep(A,r),o.timeEnd("embed web font")),o.time("embed node"),function e(t,r){var n;let{tasks:o}=r;p(t)&&((C(t)||w(t))&&o.push(...function(e,t){if(C(e)){let r=e.currentSrc||e.src;if(!M(r))return[ed(t,{url:r,imageDom:e,requestType:"image",responseType:"dataUrl"}).then(t=>{t&&(e.srcset="",e.dataset.originalSrc=r,e.src=t||"")})];(d||f)&&t.drawImageCount++}else if(y(e)&&!M(e.href.baseVal)){let r=e.href.baseVal;return[ed(t,{url:r,imageDom:e,requestType:"image",responseType:"dataUrl"}).then(t=>{t&&(e.dataset.originalSrc=r,e.href.baseVal=t||"")})]}return[]}(t,r)),v(t)&&o.push(...function(e,t){let{ownerDocument:r,svgDefsElement:n}=t,o=e.getAttribute("href")??e.getAttribute("xlink:href");if(!o)return[];let[a,i]=o.split("#");if(i){let o=`#${i}`,l=r?.querySelector(`svg ${o}`);if(a&&e.setAttribute("href",o),n?.querySelector(o))return[];if(l)n?.appendChild(l.cloneNode(!0));else if(a)return[ed(t,{url:a,responseType:"text"}).then(e=>{n?.insertAdjacentHTML("beforeend",e)})]}return[]}(t,r))),b(t)&&o.push(...(n=t.style,em.map(e=>{let t=n.getPropertyValue(e);return t&&"none"!==t?((d||f)&&r.drawImageCount++,ef(t,null,r,!0).then(r=>{r&&t!==r&&n.setProperty(e,r,n.getPropertyPriority(e))})):null}).filter(Boolean))),t.childNodes.forEach(t=>{e(t,r)})}(A,r);let T=a.length,x=0,k=async()=>{for(;;){let e=a.pop();if(!e)break;try{await e}catch(e){r.log.warn("Failed to run task",e)}c?.(++x,T)}};c?.(x,T),await Promise.all([...Array.from({length:4})].map(k)),o.timeEnd("embed node"),await m?.(A);let S=function(e,t){let{width:r,height:n}=t,o=F(r,n,e.ownerDocument),a=o.ownerDocument.createElementNS(o.namespaceURI,"foreignObject");return a.setAttributeNS(null,"x","0%"),a.setAttributeNS(null,"y","0%"),a.setAttributeNS(null,"width","100%"),a.setAttributeNS(null,"height","100%"),a.append(e),o.appendChild(a),o}(A,r);return l&&S.insertBefore(l,S.children[0]),i&&S.insertBefore(i,S.children[0]),h&&function(e){if(e.ownerDocument=void 0,e.ownerWindow=void 0,e.svgStyleElement=void 0,e.svgDefsElement=void 0,e.svgStyles.clear(),e.defaultComputedStyles.clear(),e.sandbox){try{e.sandbox.remove()}catch(t){e.log.warn("Failed to destroyContext",t)}e.sandbox=void 0}e.workers=[],e.fontFamilies.clear(),e.fontCssTexts.clear(),e.requests.clear(),e.tasks=[]}(r),await E?.(S),S}async function eC(e,t){let r=await V(e,t),n=await eA(r),o=$(n,r.isEnable("removeControlCharacter"));r.autoDestruct||(r.svgStyleElement=Y(r.ownerDocument),r.svgDefsElement=r.ownerDocument?.createElementNS(I,"defs"),r.svgStyles.clear());let a=H(o,n.ownerDocument);return await J(a,r)}async function eT(e,t){let r=await V(e,t),{log:o,quality:a,type:i,dpi:u}=r,c=await eC(r);o.time("canvas to data url");let d=c.toDataURL(i,a);if(["image/png","image/jpeg"].includes(i)&&u&&l&&s){var f,h;let[e,t]=d.split(","),r=0,o=!1;if("image/png"===i){let e,n=(-1===(e=t.indexOf("AAlwSFlz"))&&(e=t.indexOf("AAAJcEhZ")),-1===e&&(e=t.indexOf("AAAACXBI")),e);n>=0?(r=4*Math.ceil((n+28)/3),o=!0):r=44}else"image/jpeg"===i&&(r=24);let a=t.substring(0,r),l=t.substring(r),s=window.atob(a),c=new Uint8Array(s.length);for(let e=0;e<c.length;e++)c[e]=s.charCodeAt(e);let g="image/png"===i?function(e,t,r=!1){let o=new Uint8Array(13);t*=39.3701,o[0]=112,o[1]=72,o[2]=89,o[3]=115,o[4]=t>>>24,o[5]=t>>>16,o[6]=t>>>8,o[7]=255&t,o[8]=o[4],o[9]=o[5],o[10]=o[6],o[11]=o[7],o[12]=1;let a=function(e){let t=-1;n||(n=function(){let e=new Int32Array(256);for(let t=0;t<256;t++){let r=t;for(let e=0;e<8;e++)r=1&r?0xedb88320^r>>>1:r>>>1;e[t]=r}return e}());for(let r=0;r<e.length;r++)t=n[(t^e[r])&255]^t>>>8;return -1^t}(o),i=new Uint8Array(4);if(i[0]=a>>>24,i[1]=a>>>16,i[2]=a>>>8,i[3]=255&a,r){let t=function(e){let t=e.length-1;for(let r=t;r>=4;r--)if(9===e[r-4]&&112===e[r-3]&&72===e[r-2]&&89===e[r-1]&&115===e[r])return r-3;return 0}(e);return e.set(o,t),e.set(i,t+13),e}{let t=new Uint8Array(4);t[0]=0,t[1]=0,t[2]=0,t[3]=9;let r=new Uint8Array(54);return r.set(e,0),r.set(t,33),r.set(o,37),r.set(i,50),r}}(c,u,o):(f=c,h=u,f[13]=1,f[14]=h>>8,f[15]=255&h,f[16]=h>>8,f[17]=255&h,f);d=[e,",",window.btoa(String.fromCharCode(...g)),l].join("")}return o.timeEnd("canvas to data url"),d}async function ex(e,t){return eT(await V(e,{...t,type:"image/png"}))}},52635:e=>{function t(){this.buffer=[],this.length=0}t.prototype={get:function(e){var t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)==1},put:function(e,t){for(var r=0;r<t;r++)this.putBit((e>>>t-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},e.exports=t},53850:(e,t,r)=>{var n=r(39106);function o(e,t){if(void 0==e.length)throw Error(e.length+"/"+t);for(var r=0;r<e.length&&0==e[r];)r++;this.num=Array(e.length-r+t);for(var n=0;n<e.length-r;n++)this.num[n]=e[n+r]}o.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=Array(this.getLength()+e.getLength()-1),r=0;r<this.getLength();r++)for(var a=0;a<e.getLength();a++)t[r+a]^=n.gexp(n.glog(this.get(r))+n.glog(e.get(a)));return new o(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=n.glog(this.get(0))-n.glog(e.get(0)),r=Array(this.getLength()),a=0;a<this.getLength();a++)r[a]=this.get(a);for(var a=0;a<e.getLength();a++)r[a]^=n.gexp(n.glog(e.get(a))+t);return new o(r,0).mod(e)}},e.exports=o},57534:(e,t,r)=>{var n=r(713),o=r(80790),a=r(52635),i=r(32396),l=r(53850);function s(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}var u=s.prototype;u.addData=function(e){var t=new n(e);this.dataList.push(t),this.dataCache=null},u.isDark=function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw Error(e+","+t);return this.modules[e][t]},u.getModuleCount=function(){return this.moduleCount},u.make=function(){if(this.typeNumber<1){var e=1;for(e=1;e<40;e++){for(var t=o.getRSBlocks(e,this.errorCorrectLevel),r=new a,n=0,l=0;l<t.length;l++)n+=t[l].dataCount;for(var l=0;l<this.dataList.length;l++){var s=this.dataList[l];r.put(s.mode,4),r.put(s.getLength(),i.getLengthInBits(s.mode,e)),s.write(r)}if(r.getLengthInBits()<=8*n)break}this.typeNumber=e}this.makeImpl(!1,this.getBestMaskPattern())},u.makeImpl=function(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++){this.modules[r]=Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++)this.modules[r][n]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=s.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)},u.setupPositionProbePattern=function(e,t){for(var r=-1;r<=7;r++)if(!(e+r<=-1)&&!(this.moduleCount<=e+r))for(var n=-1;n<=7;n++)t+n<=-1||this.moduleCount<=t+n||(0<=r&&r<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=n&&n<=4?this.modules[e+r][t+n]=!0:this.modules[e+r][t+n]=!1)},u.getBestMaskPattern=function(){for(var e=0,t=0,r=0;r<8;r++){this.makeImpl(!0,r);var n=i.getLostPoint(this);(0==r||e>n)&&(e=n,t=r)}return t},u.createMovieClip=function(e,t,r){var n=e.createEmptyMovieClip(t,r);this.make();for(var o=0;o<this.modules.length;o++)for(var a=+o,i=0;i<this.modules[o].length;i++){var l=+i;this.modules[o][i]&&(n.beginFill(0,100),n.moveTo(l,a),n.lineTo(l+1,a),n.lineTo(l+1,a+1),n.lineTo(l,a+1),n.endFill())}return n},u.setupTimingPattern=function(){for(var e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)},u.setupPositionAdjustPattern=function(){for(var e=i.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var r=0;r<e.length;r++){var n=e[t],o=e[r];if(null==this.modules[n][o])for(var a=-2;a<=2;a++)for(var l=-2;l<=2;l++)-2==a||2==a||-2==l||2==l||0==a&&0==l?this.modules[n+a][o+l]=!0:this.modules[n+a][o+l]=!1}},u.setupTypeNumber=function(e){for(var t=i.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var n=!e&&(t>>r&1)==1;this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=n}for(var r=0;r<18;r++){var n=!e&&(t>>r&1)==1;this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=n}},u.setupTypeInfo=function(e,t){for(var r=this.errorCorrectLevel<<3|t,n=i.getBCHTypeInfo(r),o=0;o<15;o++){var a=!e&&(n>>o&1)==1;o<6?this.modules[o][8]=a:o<8?this.modules[o+1][8]=a:this.modules[this.moduleCount-15+o][8]=a}for(var o=0;o<15;o++){var a=!e&&(n>>o&1)==1;o<8?this.modules[8][this.moduleCount-o-1]=a:o<9?this.modules[8][15-o-1+1]=a:this.modules[8][15-o-1]=a}this.modules[this.moduleCount-8][8]=!e},u.mapData=function(e,t){for(var r=-1,n=this.moduleCount-1,o=7,a=0,l=this.moduleCount-1;l>0;l-=2)for(6==l&&l--;;){for(var s=0;s<2;s++)if(null==this.modules[n][l-s]){var u=!1;a<e.length&&(u=(e[a]>>>o&1)==1),i.getMask(t,n,l-s)&&(u=!u),this.modules[n][l-s]=u,-1==--o&&(a++,o=7)}if((n+=r)<0||this.moduleCount<=n){n-=r,r=-r;break}}},s.PAD0=236,s.PAD1=17,s.createData=function(e,t,r){for(var n=o.getRSBlocks(e,t),l=new a,u=0;u<r.length;u++){var c=r[u];l.put(c.mode,4),l.put(c.getLength(),i.getLengthInBits(c.mode,e)),c.write(l)}for(var d=0,u=0;u<n.length;u++)d+=n[u].dataCount;if(l.getLengthInBits()>8*d)throw Error("code length overflow. ("+l.getLengthInBits()+">"+8*d+")");for(l.getLengthInBits()+4<=8*d&&l.put(0,4);l.getLengthInBits()%8!=0;)l.putBit(!1);for(;!(l.getLengthInBits()>=8*d)&&(l.put(s.PAD0,8),!(l.getLengthInBits()>=8*d));){;l.put(s.PAD1,8)}return s.createBytes(l,n)},s.createBytes=function(e,t){for(var r=0,n=0,o=0,a=Array(t.length),s=Array(t.length),u=0;u<t.length;u++){var c=t[u].dataCount,d=t[u].totalCount-c;n=Math.max(n,c),o=Math.max(o,d),a[u]=Array(c);for(var f=0;f<a[u].length;f++)a[u][f]=255&e.buffer[f+r];r+=c;var h=i.getErrorCorrectPolynomial(d),g=new l(a[u],h.getLength()-1).mod(h);s[u]=Array(h.getLength()-1);for(var f=0;f<s[u].length;f++){var m=f+g.getLength()-s[u].length;s[u][f]=m>=0?g.get(m):0}}for(var p=0,f=0;f<t.length;f++)p+=t[f].totalCount;for(var y=Array(p),w=0,f=0;f<n;f++)for(var u=0;u<t.length;u++)f<a[u].length&&(y[w++]=a[u][f]);for(var f=0;f<o;f++)for(var u=0;u<t.length;u++)f<s[u].length&&(y[w++]=s[u][f]);return y},e.exports=s},75352:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o=l(r(87955)),a=r(43210),i=l(a);function l(e){return e&&e.__esModule?e:{default:e}}var s={bgColor:o.default.oneOfType([o.default.object,o.default.string]).isRequired,bgD:o.default.string.isRequired,fgColor:o.default.oneOfType([o.default.object,o.default.string]).isRequired,fgD:o.default.string.isRequired,size:o.default.number.isRequired,title:o.default.string,viewBoxSize:o.default.number.isRequired,xmlns:o.default.string},u=(0,a.forwardRef)(function(e,t){var r=e.bgColor,o=e.bgD,a=e.fgD,l=e.fgColor,s=e.size,u=e.title,c=e.viewBoxSize,d=e.xmlns,f=function(e,t){var r={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r}(e,["bgColor","bgD","fgD","fgColor","size","title","viewBoxSize","xmlns"]);return i.default.createElement("svg",n({},f,{height:s,ref:t,viewBox:"0 0 "+c+" "+c,width:s,xmlns:void 0===d?"http://www.w3.org/2000/svg":d}),u?i.default.createElement("title",null,u):null,i.default.createElement("path",{d:o,fill:r}),i.default.createElement("path",{d:a,fill:l}))});u.displayName="QRCodeSvg",u.propTypes=s,t.default=u},80790:(e,t,r)=>{var n=r(25404);function o(e,t){this.totalCount=e,this.dataCount=t}o.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],o.getRSBlocks=function(e,t){var r=o.getRsBlockTable(e,t);if(void 0==r)throw Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var n=r.length/3,a=[],i=0;i<n;i++)for(var l=r[3*i+0],s=r[3*i+1],u=r[3*i+2],c=0;c<l;c++)a.push(new o(s,u));return a},o.getRsBlockTable=function(e,t){switch(t){case n.L:return o.RS_BLOCK_TABLE[(e-1)*4+0];case n.M:return o.RS_BLOCK_TABLE[(e-1)*4+1];case n.Q:return o.RS_BLOCK_TABLE[(e-1)*4+2];case n.H:return o.RS_BLOCK_TABLE[(e-1)*4+3];default:return}},e.exports=o},84031:(e,t,r)=>{"use strict";var n=r(34452);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,a,i){if(i!==n){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return r.PropTypes=r,r}},87955:(e,t,r)=>{e.exports=r(84031)()},89757:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("FileDown",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]])},96882:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};