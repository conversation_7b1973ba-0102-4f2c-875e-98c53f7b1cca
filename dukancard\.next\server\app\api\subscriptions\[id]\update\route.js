(()=>{var e={};e.id=4290,e.ids=[33,4290,5453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10033:(e,t,s)=>{"use strict";s.d(t,{SubscriptionStateManager:()=>i,TQ:()=>a});var r=s(80223);class i{static shouldHaveActiveSubscription(e,t=r.v.FREE){return t!==r.v.FREE&&e!==r.d.TRIAL&&[r.d.ACTIVE].includes(e)}static isTerminalStatus(e){return[r.d.CANCELLED,r.d.EXPIRED,r.d.COMPLETED].includes(e)}static isTrialStatus(e){return e===r.d.TRIAL}static isFreeStatus(e,t){return t===r.v.FREE||"free"===e}static getAccessLevel(e,t=r.v.FREE){return t===r.v.FREE?"free":e===r.d.TRIAL?"trial":this.shouldHaveActiveSubscription(e,t)?"paid":"free"}static isActivePaidSubscription(e,t=r.v.FREE){return this.shouldHaveActiveSubscription(e,t)}static isValidStatusTransition(e,t){return!this.isTerminalStatus(e)||!!this.isTerminalStatus(t)}}function a(e){return i.isTerminalStatus(e)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{createClient:()=>i});var r=s(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let i=null,a=null;try{let{headers:e,cookies:t}=await s.e(4999).then(s.bind(s,44999));i=await e(),a=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,i=e.get("x-test-business-slug"),a=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,i){let a=()=>{var a,n,u,o,c;return a=e,n=t,u=s,o=r,c=i,"customer_profiles"===a?{data:u&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===a?{data:u&&"business"===n?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===a?{data:"business"===n?{id:"test-subscription-id",plan_id:c,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===a?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,s)=>n(e),neq:(t,s)=>n(e),gt:(t,s)=>n(e),gte:(t,s)=>n(e),lt:(t,s)=>n(e),lte:(t,s)=>n(e),like:(t,s)=>n(e),ilike:(t,s)=>n(e),is:(t,s)=>n(e),in:(t,s)=>n(e),contains:(t,s)=>n(e),containedBy:(t,s)=>n(e),rangeGt:(t,s)=>n(e),rangeGte:(t,s)=>n(e),rangeLt:(t,s)=>n(e),rangeLte:(t,s)=>n(e),rangeAdjacent:(t,s)=>n(e),overlaps:(t,s)=>n(e),textSearch:(t,s)=>n(e),match:t=>n(e),not:(t,s,r)=>n(e),or:t=>n(e),filter:(t,s,r)=>n(e),order:(t,s)=>n(e),limit:(t,s)=>n(e),range:(t,s,r)=>n(e),abortSignal:t=>n(e),single:async()=>a(),maybeSingle:async()=>a(),then:async e=>{let t=a();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,s,r,i,a)}}(i):a?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await a.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await a.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},52316:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>y,routeModule:()=>_,serverHooks:()=>m,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{PATCH:()=>p});var i=s(96559),a=s(48088),n=s(37719),u=s(32190),o=s(32032),c=s(31546),l=s(79209),d=s(10033);async function p(e,{params:t}){try{let{id:r}=await t,{planId:i,planCycle:a,quantity:n,scheduleChangeAt:p="now",customerNotify:_=!0,remainingCount:b,startAt:g,offerId:m,notes:y={}}=await e.json(),h=await (0,o.createClient)(),{data:{user:A},error:E}=await h.auth.getUser();if(E||!A)return u.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let{data:f,error:v}=await h.from("payment_subscriptions").select("*").eq("razorpay_subscription_id",r).single();if(v){if("PGRST116"===v.code)return u.NextResponse.json({success:!1,error:"Subscription not found"},{status:404});return console.error("[RAZORPAY_ERROR] Error fetching subscription:",v),u.NextResponse.json({success:!1,error:"Error fetching subscription"},{status:500})}if(f.business_profile_id!==A.id)return u.NextResponse.json({success:!1,error:"Unauthorized to update this subscription"},{status:403});if(!d.SubscriptionStateManager.isActivePaidSubscription(f.subscription_status,f.plan_id))return u.NextResponse.json({success:!1,error:"Subscription cannot be updated in its current state"},{status:400});let R={schedule_change_at:p,customer_notify:_};if(i&&a){let e=(0,l.b0)(i,a);if(!e)return u.NextResponse.json({success:!1,error:"Invalid plan or plan cycle"},{status:400});R.plan_id=e;let t=f.plan_cycle;t&&t!==a&&(console.log(`[SUBSCRIPTION_UPDATE] Billing cycle change detected: ${t} -> ${a}`),"monthly"===a?R.remaining_count=120:"yearly"===a&&(R.remaining_count=10),console.log(`[SUBSCRIPTION_UPDATE] Set remaining_count to ${R.remaining_count} for ${a} cycle`))}void 0!==n&&(R.quantity=n),void 0===b||R.remaining_count||(R.remaining_count=b),void 0!==g&&(R.start_at=g),void 0!==m&&(R.offer_id=m),Object.keys(y).length>0&&(R.notes=y);let x=await (0,c.updateSubscription)(r,R);if(!x.success)return u.NextResponse.json({success:!1,error:x.error},{status:400});console.log("[SUBSCRIPTION_UPDATE] Razorpay update successful, updating database...");let S=new Date().toISOString(),T={updated_at:S};i&&(T.plan_id=i),a&&(T.plan_cycle=a);let{EdgeSubscriptionStateManager:I}=await s.e(2753).then(s.bind(s,22753)),w=I.shouldHaveActiveSubscription(f.subscription_status,i||f.plan_id),{data:O,error:C}=await h.rpc("update_subscription_atomic",{p_additional_data:T,p_business_profile_id:f.business_profile_id,p_has_active_subscription:w,p_new_status:f.subscription_status,p_subscription_id:r,p_webhook_timestamp:void 0});return C||!O?.success?console.error("[RAZORPAY_ERROR] Error updating subscription atomically:",C||O?.error):console.log("[SUBSCRIPTION_UPDATE] Database update successful"),u.NextResponse.json({success:!0,data:{...x.data,db_subscription:{id:f.id,plan_id:i||f.plan_id,plan_cycle:a||f.plan_cycle,updated_at:S}}},{status:200})}catch(e){return console.error("[RAZORPAY_ERROR] Error updating subscription:",e),u.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/update/route",pathname:"/api/subscriptions/[id]/update",filename:"route",bundlePath:"app/api/subscriptions/[id]/update/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\update\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:b,workUnitAsyncStorage:g,serverHooks:m}=_;function y(){return(0,n.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:g})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80223:(e,t,s)=>{"use strict";s.d(t,{d:()=>r,v:()=>i});let r={ACTIVE:"active",AUTHENTICATED:"authenticated",TRIAL:"trial",PENDING:"pending",HALTED:"halted",CANCELLED:"cancelled",EXPIRED:"expired",COMPLETED:"completed",PAYMENT_FAILED:"payment_failed",CANCELLATION_SCHEDULED:"cancellation_scheduled"},i={FREE:"free",BASIC:"basic",GROWTH:"growth",PRO:"pro",ENTERPRISE:"enterprise"}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95453:(e,t,s)=>{"use strict";s.d(t,{ST:()=>a,bG:()=>u,t6:()=>o});var r=s(55511),i=s.n(r);let a="https://api.razorpay.com/v2",n=()=>{let e,t;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",t=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!t)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!t,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:t}},u=()=>{let{keyId:e,keySecret:t}=n(),s=Buffer.from(`${e}:${t}`).toString("base64");return{Authorization:`Basic ${s}`,"Content-Type":"application/json"}},o=(e,t,s)=>{try{let r=i().createHmac("sha256",s).update(e).digest("hex");return i().timingSafeEqual(Buffer.from(t),Buffer.from(r))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,9398,4386,580,1546,9209],()=>s(52316));module.exports=r})();