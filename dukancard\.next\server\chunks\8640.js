"use strict";exports.id=8640,exports.ids=[8640],exports.modules={28640:(e,t,i)=>{async function r(e,t={}){let{format:i="webp",targetSizeKB:a=100,maxDimension:n=800,quality:o=.8}=t;return new Promise((t,r)=>{let s=new Image;s.onload=()=>{try{let l=document.createElement("canvas"),c=l.getContext("2d");if(!c)return void r(Error("Could not get canvas context"));let{width:m,height:d}=s;(m>n||d>n)&&(m>d?(d=d*n/m,m=n):(m=m*n/d,d=n)),l.width=m,l.height=d,c.drawImage(s,0,0,m,d);let u=o,g=0,x=()=>{l.toBlob(i=>{if(!i)return void r(Error("Failed to create blob"));let n=i.size/1024;if(n<=a||g>=5||u<=.1){let r=e.size/i.size;t({blob:i,finalSizeKB:Math.round(100*n)/100,compressionRatio:Math.round(100*r)/100,dimensions:{width:m,height:d}})}else g++,u=Math.max(.1,u-.15),x()},`image/${i}`,u)};x()}catch(e){r(e)}},s.onerror=()=>r(Error("Failed to load image")),s.src=URL.createObjectURL(e)})}async function a(e,t={}){let i=e.size/1048576,n=100,o=800,s=.7;return i<=2?(s=.7,o=800,n=90):i<=5?(s=.55,o=700,n=80):i<=10?(s=.45,o=600,n=70):(s=.35,o=550,n=60),r(e,{...t,targetSizeKB:t.targetSizeKB||n,maxDimension:t.maxDimension||o,quality:t.quality||s})}async function n(e,t={}){return r(e,{targetSizeKB:50,maxDimension:400,quality:.7,...t})}i.d(t,{compressImageUltraAggressiveClient:()=>a,q:()=>n})}};