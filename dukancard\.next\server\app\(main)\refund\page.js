(()=>{var e={};e.id=5258,e.ids=[5258],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16002:(e,t,s)=>{Promise.resolve().then(s.bind(s,18116))},18116:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});let i=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\refund\\\\ModernRefundPolicyClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34154:(e,t,s)=>{Promise.resolve().then(s.bind(s,69698))},34631:e=>{"use strict";e.exports=require("tls")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60209:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>l});var i=s(37413),r=s(61120),n=s(63420),a=s(18116);let l={title:"Cancellation and Refund Policy",description:"Learn about Dukancard's cancellation and refund policy for subscription issues and payment problems."};function o(){return(0,i.jsx)("div",{className:"min-h-screen bg-white dark:bg-black",children:(0,i.jsx)(r.Suspense,{fallback:(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsx)(n.A,{className:"h-8 w-8 animate-spin text-[var(--brand-gold)]"})}),children:(0,i.jsx)(a.default,{})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69698:(e,t,s)=>{"use strict";s.d(t,{default:()=>A});var i=s(60687),r=s(43210),n=s(77882),a=s(10022),l=s(85778),o=s(5336),c=s(78122),d=s(48730),u=s(84027),p=s(43649),h=s(40228),m=s(41550),y=s(55192),x=s(56310),f=s(48856),b=s(8587),g=s(19310),j=s(27908),v=s(32688);let w=[{id:"introduction",title:"Introduction"},{id:"subscription",title:"Subscription Cancellation"},{id:"refund-eligibility",title:"Refund Eligibility"},{id:"refund-process",title:"Refund Process"},{id:"processing-time",title:"Processing Time"},{id:"cancellation-effects",title:"Cancellation Effects"},{id:"non-refundable",title:"Non-Refundable Items"},{id:"policy-changes",title:"Policy Changes"},{id:"contact",title:"Contact Us"}],k=[{title:"Terms of Service",href:"/terms"},{title:"Privacy Policy",href:"/privacy"},{title:"Cookie Policy",href:"/cookies"}];function A(){let e=(0,r.useRef)(null);return(0,i.jsx)("div",{ref:e,className:"min-h-screen bg-white dark:bg-black",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(x.A,{title:"Cancellation and Refund Policy",lastUpdated:"May 19, 2025",variant:"purple"}),(0,i.jsx)("div",{className:"container mx-auto px-4 max-w-4xl pb-16",children:(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,i.jsx)("div",{className:"w-full lg:w-1/4 order-1",children:(0,i.jsx)("div",{className:"lg:sticky lg:top-24 self-start",children:(0,i.jsx)(b.A,{items:w})})}),(0,i.jsxs)("div",{className:"w-full lg:w-3/4 order-2",children:[(0,i.jsx)(y.Zp,{className:"p-6 md:p-8 border border-border shadow-sm mb-8",children:(0,i.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"prose prose-neutral dark:prose-invert max-w-none",children:(0,i.jsx)("p",{className:"text-lg",children:"At Dukancard, we strive to ensure complete satisfaction with our digital business card services. This Cancellation and Refund Policy outlines the terms and conditions regarding subscription cancellations and refunds for our services."})})}),(0,i.jsx)(f.A,{id:"introduction",title:"1. Introduction",icon:(0,i.jsx)(a.A,{className:"h-6 w-6"}),delay:0,children:(0,i.jsx)("p",{children:"At Dukancard, we strive to ensure complete satisfaction with our digital business card services. This Cancellation and Refund Policy outlines the terms and conditions regarding subscription cancellations and refunds for our services."})}),(0,i.jsxs)(f.A,{id:"subscription",title:"2. Subscription Cancellation",icon:(0,i.jsx)(l.A,{className:"h-6 w-6"}),delay:1,children:[(0,i.jsx)("p",{children:"You may cancel your subscription at any time through your Dukancard dashboard. Here's what you need to know about cancellation:"}),(0,i.jsxs)("ul",{className:"list-disc pl-6 mb-6",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Cancellation Process:"}),' Log in to your Dukancard dashboard, navigate to the Business Plan page, and click on the "Cancel Subscription" button. Your subscription will be cancelled through our Razorpay payment gateway.']}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"End-of-Term Cancellation:"})," When you cancel your subscription, it will remain active until the end of your current billing cycle, and you will not be charged for the next billing cycle."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"No Partial Refunds:"})," We do not provide partial refunds for unused portions of your subscription."]})]})]}),(0,i.jsxs)(f.A,{id:"refund-eligibility",title:"3. Refund Eligibility",icon:(0,i.jsx)(o.A,{className:"h-6 w-6"}),delay:2,children:[(0,i.jsx)("p",{children:"Dukancard currently offers refunds only in specific circumstances:"}),(0,i.jsxs)("ul",{className:"list-disc pl-6 mb-6",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Failed Payments:"})," If you were charged for a subscription but the payment failed to process correctly."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Subscription Issues:"})," If there were technical issues with your subscription that prevented you from accessing the service."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Double Charges:"})," If you were charged twice for the same subscription period."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Contact Required:"})," All refund requests must be submitted by contacting our support team directly. We review each request on a case-by-case basis."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Plan Downgrade:"})," Upon refund approval, your account will be downgraded to the Free plan. You will still be able to maintain your online presence with the features available in the Free plan."]})]})]}),(0,i.jsx)(j.A,{variant:"purple",className:"my-12"}),(0,i.jsxs)(f.A,{id:"refund-process",title:"4. Refund Process",icon:(0,i.jsx)(c.A,{className:"h-6 w-6"}),delay:3,children:[(0,i.jsx)("p",{children:"To request a refund for eligible circumstances, follow these steps:"}),(0,i.jsxs)("ol",{className:"list-decimal pl-6 mb-6",children:[(0,i.jsxs)("li",{children:["Contact our support team via email at ",v.C.contact.email,"."]}),(0,i.jsxs)("li",{children:["Include the following information in your request:",(0,i.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,i.jsx)("li",{children:"Your account email address"}),(0,i.jsx)("li",{children:"Business name"}),(0,i.jsx)("li",{children:"Date of payment"}),(0,i.jsx)("li",{children:"Reason for refund request"}),(0,i.jsx)("li",{children:"Any relevant screenshots or documentation"})]})]}),(0,i.jsx)("li",{children:"Our team will review your request and respond within 2-3 business days."}),(0,i.jsx)("li",{children:"If approved, the refund will be processed to your original payment method."})]})]}),(0,i.jsxs)(f.A,{id:"processing-time",title:"5. Processing Time",icon:(0,i.jsx)(d.A,{className:"h-6 w-6"}),delay:4,children:[(0,i.jsx)("p",{children:"All payments are processed through Razorpay, our trusted payment gateway partner. Refund processing times vary depending on your payment method:"}),(0,i.jsxs)("ul",{className:"list-disc pl-6 mb-6",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Credit/Debit Cards:"})," 5-7 business days, depending on your card issuer."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"UPI:"})," 1-3 business days."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Net Banking:"})," 3-5 business days."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Wallet:"})," 1-2 business days."]})]}),(0,i.jsx)("p",{children:"Please note that while Dukancard processes refunds promptly through Razorpay, the actual time for the refund to appear in your account depends on your payment provider's processing times."})]}),(0,i.jsxs)(f.A,{id:"cancellation-effects",title:"6. Cancellation Effects",icon:(0,i.jsx)(u.A,{className:"h-6 w-6"}),delay:5,children:[(0,i.jsx)("p",{children:"When you cancel your subscription or receive a refund, the following changes will take effect:"}),(0,i.jsxs)("ul",{className:"list-disc pl-6 mb-6",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Approved Refund:"})," If your refund request is approved, your account will be downgraded to the Free plan immediately. You will still be able to maintain your online presence with the features available in the Free plan."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"End-of-Term Cancellation:"})," You will retain access to your current plan features until the end of your billing cycle, at which point your account will be downgraded to the Free plan."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Data Retention:"})," Your business profile and data will be retained according to our Privacy Policy, but certain premium features will no longer be accessible."]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Reactivation:"})," You can reactivate your subscription at any time by purchasing a new plan."]})]})]}),(0,i.jsxs)(f.A,{id:"non-refundable",title:"7. Non-Refundable Items",icon:(0,i.jsx)(p.A,{className:"h-6 w-6"}),delay:6,children:[(0,i.jsx)("p",{children:"The following are generally not eligible for refunds:"}),(0,i.jsxs)("ul",{className:"list-disc pl-6 mb-6",children:[(0,i.jsx)("li",{children:"Regular subscription cancellations (cancellations without technical issues)."}),(0,i.jsx)("li",{children:"Subscriptions cancelled due to lack of usage or change of mind."}),(0,i.jsx)("li",{children:"Accounts that have violated our Terms of Service."}),(0,i.jsx)("li",{children:"Requests made without proper documentation or evidence of payment issues."})]})]}),(0,i.jsxs)(f.A,{id:"policy-changes",title:"8. Changes to This Policy",icon:(0,i.jsx)(h.A,{className:"h-6 w-6"}),delay:7,children:[(0,i.jsx)("p",{children:'We may update our Cancellation and Refund Policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last Updated" date at the top of this policy.'}),(0,i.jsx)("p",{children:"You are advised to review this policy periodically for any changes. Changes to this policy are effective when they are posted on this page."})]}),(0,i.jsxs)(f.A,{id:"contact",title:"9. Contact Us",icon:(0,i.jsx)(m.A,{className:"h-6 w-6"}),delay:8,children:[(0,i.jsx)("p",{children:"If you have any questions about our Cancellation and Refund Policy, please contact us:"}),(0,i.jsxs)("ul",{className:"list-disc pl-6 mb-6",children:[(0,i.jsxs)("li",{children:["By email: ",v.C.contact.email]}),(0,i.jsxs)("li",{children:["By phone: ",v.C.contact.phone]}),(0,i.jsxs)("li",{children:["By mail: ",v.C.contact.address.full]})]})]})]})]})}),(0,i.jsx)(j.A,{variant:"gold",className:"my-8"}),(0,i.jsx)(g.A,{relatedLinks:k})]})})}},71015:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var i=s(65239),r=s(48088),n=s(88170),a=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["(main)",{children:["refund",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60209)),"C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19559)),"C:\\web-app\\dukancard\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(main)/refund/page",pathname:"/refund",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},85778:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(62688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[4447,6724,2997,1107,7065,1222,3037,6177,321],()=>s(71015));module.exports=i})();