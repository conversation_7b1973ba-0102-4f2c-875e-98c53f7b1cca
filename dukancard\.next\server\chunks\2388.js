"use strict";exports.id=2388,exports.ids=[2388],exports.modules={19101:(e,t,r)=>{r.d(t,{U:()=>a});var s=r(79384);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let a=null,n=null;try{let{headers:e,cookies:t}=await r.e(9277).then(r.bind(r,9277));a=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,a=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,a){let n=()=>{var n,i,l,o,u;return n=e,i=t,l=r,o=s,u=a,"customer_profiles"===n?{data:l&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:l&&"business"===i?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===i?{id:"test-subscription-id",plan_id:u,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,r)=>i(e),neq:(t,r)=>i(e),gt:(t,r)=>i(e),gte:(t,r)=>i(e),lt:(t,r)=>i(e),lte:(t,r)=>i(e),like:(t,r)=>i(e),ilike:(t,r)=>i(e),is:(t,r)=>i(e),in:(t,r)=>i(e),contains:(t,r)=>i(e),containedBy:(t,r)=>i(e),rangeGt:(t,r)=>i(e),rangeGte:(t,r)=>i(e),rangeLt:(t,r)=>i(e),rangeLte:(t,r)=>i(e),rangeAdjacent:(t,r)=>i(e),overlaps:(t,r)=>i(e),textSearch:(t,r)=>i(e),match:t=>i(e),not:(t,r,s)=>i(e),or:t=>i(e),filter:(t,r,s)=>i(e),order:(t,r)=>i(e),limit:(t,r)=>i(e),range:(t,r,s)=>i(e),abortSignal:t=>i(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,r,s,a,n)}}(a):n?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await n.set(t,r,s)}catch{}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},22388:(e,t,r)=>{r.d(t,{deleteCustomerPostMedia:()=>i});var s=r(19101),a=r(65646),n=r(73170);async function i(e,t,r){let i=await (0,s.U)();try{let s=n.SC.CUSTOMERS,l=(0,a.EK)(e,t,r),{data:o,error:u}=await i.storage.from(s).list(l,{limit:1e3,sortBy:{column:"name",order:"asc"}});if(u)return console.error("Error listing customer post folder contents:",u),{success:!1,error:`Failed to list customer post folder: ${u.message}`};if(!o||0===o.length)return{success:!0};let c=o.map(e=>`${l}/${e.name}`),{error:d}=await i.storage.from(s).remove(c);if(d)return console.error("Error deleting customer post folder contents:",d),{success:!1,error:`Failed to delete customer post folder: ${d.message}`};return{success:!0}}catch(e){return console.error("Error in deleteCustomerPostMedia:",e),{success:!1,error:"An unexpected error occurred while deleting customer post folder."}}}},65646:(e,t,r)=>{function s(e){if(!e||"string"!=typeof e)throw Error(`Invalid userId: expected string, got ${typeof e}. Value: ${e}`);if(e.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${e}`);let t=e.substring(0,2).toLowerCase(),r=e.substring(2,4).toLowerCase();return`users/${t}/${r}/${e}`}function a(e,t,r,a,n){let i=s(e),l=n?new Date(n):new Date,o=l.getFullYear(),u=String(l.getMonth()+1).padStart(2,"0");return`${i}/posts/${o}/${u}/${t}/image_${r}_${a}.webp`}function n(e,t,r){let a=s(e),n=new Date(r),i=n.getFullYear(),l=String(n.getMonth()+1).padStart(2,"0");return`${a}/posts/${i}/${l}/${t}`}r.d(t,{EK:()=>n,RE:()=>a})}};