"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7711],{5937:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]])},14738:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},29869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},35695:(e,t,n)=>{var r=n(18999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},51362:(e,t,n)=>{n.d(t,{D:()=>u,ThemeProvider:()=>l});var r=n(12115),o=(e,t,n,r,o,i,a,s)=>{let c=document.documentElement,u=["light","dark"];function l(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&i?o.map(e=>i[e]||e):o;n?(c.classList.remove(...r),c.classList.add(i&&i[t]?i[t]:t)):c.setAttribute(e,t)}),n=t,s&&u.includes(n)&&(c.style.colorScheme=n)}if(r)l(r);else try{let e=localStorage.getItem(t)||n,r=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;l(r)}catch(e){}},i=["light","dark"],a="(prefers-color-scheme: dark)",s=r.createContext(void 0),c={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=r.useContext(s))?e:c},l=e=>r.useContext(s)?r.createElement(r.Fragment,null,e.children):r.createElement(d,{...e}),h=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:o=!0,enableColorScheme:c=!0,storageKey:u="theme",themes:l=h,defaultTheme:d=o?"system":"light",attribute:m="data-theme",value:S,children:T,nonce:M,scriptProps:C}=e,[E,A]=r.useState(()=>f(u,d)),[I,v]=r.useState(()=>"system"===E?y():E),D=S?Object.values(S):l,N=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=y());let r=S?S[t]:t,a=n?g(M):null,s=document.documentElement,u=e=>{"class"===e?(s.classList.remove(...D),r&&s.classList.add(r)):e.startsWith("data-")&&(r?s.setAttribute(e,r):s.removeAttribute(e))};if(Array.isArray(m)?m.forEach(u):u(m),c){let e=i.includes(d)?d:null,n=i.includes(t)?t:e;s.style.colorScheme=n}null==a||a()},[M]),b=r.useCallback(e=>{let t="function"==typeof e?e(E):e;A(t);try{localStorage.setItem(u,t)}catch(e){}},[E]),w=r.useCallback(e=>{v(y(e)),"system"===E&&o&&!t&&N("system")},[E,t]);r.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(w),w(e),()=>e.removeListener(w)},[w]),r.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?A(e.newValue):b(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[b]),r.useEffect(()=>{N(null!=t?t:E)},[t,E]);let L=r.useMemo(()=>({theme:E,setTheme:b,forcedTheme:t,resolvedTheme:"system"===E?I:E,themes:o?[...l,"system"]:l,systemTheme:o?I:void 0}),[E,b,t,I,o,l]);return r.createElement(s.Provider,{value:L},r.createElement(p,{forcedTheme:t,storageKey:u,attribute:m,enableSystem:o,enableColorScheme:c,defaultTheme:d,value:S,themes:l,nonce:M,scriptProps:C}),T)},p=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:i,enableSystem:a,enableColorScheme:s,defaultTheme:c,value:u,themes:l,nonce:h,scriptProps:d}=e,p=JSON.stringify([i,n,c,t,l,u,a,s]).slice(1,-1);return r.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),f=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},g=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},57340:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},62098:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},71007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},93509:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},97939:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},98015:(e,t,n)=>{n.d(t,{j9:()=>W,a8:()=>i,TF:()=>eg}),function(e){e[e.QR_CODE=0]="QR_CODE",e[e.AZTEC=1]="AZTEC",e[e.CODABAR=2]="CODABAR",e[e.CODE_39=3]="CODE_39",e[e.CODE_93=4]="CODE_93",e[e.CODE_128=5]="CODE_128",e[e.DATA_MATRIX=6]="DATA_MATRIX",e[e.MAXICODE=7]="MAXICODE",e[e.ITF=8]="ITF",e[e.EAN_13=9]="EAN_13",e[e.EAN_8=10]="EAN_8",e[e.PDF_417=11]="PDF_417",e[e.RSS_14=12]="RSS_14",e[e.RSS_EXPANDED=13]="RSS_EXPANDED",e[e.UPC_A=14]="UPC_A",e[e.UPC_E=15]="UPC_E",e[e.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"}(r||(r={}));var r,o,i,a,s,c,u=new Map([[r.QR_CODE,"QR_CODE"],[r.AZTEC,"AZTEC"],[r.CODABAR,"CODABAR"],[r.CODE_39,"CODE_39"],[r.CODE_93,"CODE_93"],[r.CODE_128,"CODE_128"],[r.DATA_MATRIX,"DATA_MATRIX"],[r.MAXICODE,"MAXICODE"],[r.ITF,"ITF"],[r.EAN_13,"EAN_13"],[r.EAN_8,"EAN_8"],[r.PDF_417,"PDF_417"],[r.RSS_14,"RSS_14"],[r.RSS_EXPANDED,"RSS_EXPANDED"],[r.UPC_A,"UPC_A"],[r.UPC_E,"UPC_E"],[r.UPC_EAN_EXTENSION,"UPC_EAN_EXTENSION"]]);!function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.URL=1]="URL"}(o||(o={})),!function(e){e[e.SCAN_TYPE_CAMERA=0]="SCAN_TYPE_CAMERA",e[e.SCAN_TYPE_FILE=1]="SCAN_TYPE_FILE"}(i||(i={}));var l=function(){function e(){}return e.GITHUB_PROJECT_URL="https://github.com/mebjas/html5-qrcode",e.SCAN_DEFAULT_FPS=2,e.DEFAULT_DISABLE_FLIP=!1,e.DEFAULT_REMEMBER_LAST_CAMERA_USED=!0,e.DEFAULT_SUPPORTED_SCAN_TYPE=[i.SCAN_TYPE_CAMERA,i.SCAN_TYPE_FILE],e}(),h=function(){function e(e,t){this.format=e,this.formatName=t}return e.prototype.toString=function(){return this.formatName},e.create=function(t){if(!u.has(t))throw"".concat(t," not in html5QrcodeSupportedFormatsTextMap");return new e(t,u.get(t))},e}(),d=function(){function e(){}return e.createFromText=function(e){return{decodedText:e,result:{text:e}}},e.createFromQrcodeResult=function(e){return{decodedText:e.text,result:e}},e}();!function(e){e[e.UNKWOWN_ERROR=0]="UNKWOWN_ERROR",e[e.IMPLEMENTATION_ERROR=1]="IMPLEMENTATION_ERROR",e[e.NO_CODE_FOUND_ERROR=2]="NO_CODE_FOUND_ERROR"}(a||(a={}));var p=function(){function e(){}return e.createFrom=function(e){return{errorMessage:e,type:a.UNKWOWN_ERROR}},e}(),f=function(){function e(e){this.verbose=e}return e.prototype.log=function(e){this.verbose&&console.log(e)},e.prototype.warn=function(e){this.verbose&&console.warn(e)},e.prototype.logError=function(e,t){(this.verbose||!0===t)&&console.error(e)},e.prototype.logErrors=function(e){if(0===e.length)throw"Logger#logError called without arguments";this.verbose&&console.error(e)},e}();function g(e){return null==e}var y=function(){function e(){}return e.codeParseError=function(e){return"QR code parse error, error = ".concat(e)},e.errorGettingUserMedia=function(e){return"Error getting userMedia, error = ".concat(e)},e.onlyDeviceSupportedError=function(){return"The device doesn't support navigator.mediaDevices , only supported cameraIdOrConfig in this case is deviceId parameter (string)."},e.cameraStreamingNotSupported=function(){return"Camera streaming not supported by the browser."},e.unableToQuerySupportedDevices=function(){return"Unable to query supported devices, unknown error."},e.insecureContextCameraQueryError=function(){return"Camera access is only supported in secure context like https or localhost."},e.scannerPaused=function(){return"Scanner paused"},e}(),m=function(){function e(){}return e.scanningStatus=function(){return"Scanning"},e.idleStatus=function(){return"Idle"},e.errorStatus=function(){return"Error"},e.permissionStatus=function(){return"Permission"},e.noCameraFoundErrorStatus=function(){return"No Cameras"},e.lastMatch=function(e){return"Last Match: ".concat(e)},e.codeScannerTitle=function(){return"Code Scanner"},e.cameraPermissionTitle=function(){return"Request Camera Permissions"},e.cameraPermissionRequesting=function(){return"Requesting camera permissions..."},e.noCameraFound=function(){return"No camera found"},e.scanButtonStopScanningText=function(){return"Stop Scanning"},e.scanButtonStartScanningText=function(){return"Start Scanning"},e.torchOnButton=function(){return"Switch On Torch"},e.torchOffButton=function(){return"Switch Off Torch"},e.torchOnFailedMessage=function(){return"Failed to turn on torch"},e.torchOffFailedMessage=function(){return"Failed to turn off torch"},e.scanButtonScanningStarting=function(){return"Launching Camera..."},e.textIfCameraScanSelected=function(){return"Scan an Image File"},e.textIfFileScanSelected=function(){return"Scan using camera directly"},e.selectCamera=function(){return"Select Camera"},e.fileSelectionChooseImage=function(){return"Choose Image"},e.fileSelectionChooseAnother=function(){return"Choose Another"},e.fileSelectionNoImageSelected=function(){return"No image choosen"},e.anonymousCameraPrefix=function(){return"Anonymous Camera"},e.dragAndDropMessage=function(){return"Or drop an image to scan"},e.dragAndDropMessageOnlyImages=function(){return"Or drop an image to scan (other files not supported)"},e.zoom=function(){return"zoom"},e.loadingImage=function(){return"Loading image..."},e.cameraScanAltText=function(){return"Camera based scan"},e.fileScanAltText=function(){return"Fule based scan"},e}(),S=function(){function e(){}return e.poweredBy=function(){return"Powered by "},e.reportIssues=function(){return"Report issues"},e}(),T=function(){function e(){}return e.isMediaStreamConstraintsValid=function(e,t){if("object"!=typeof e){var n=typeof e;return t.logError("videoConstraints should be of type object, the "+"object passed is of type ".concat(n,"."),!0),!1}for(var r=new Set(["autoGainControl","channelCount","echoCancellation","latency","noiseSuppression","sampleRate","sampleSize","volume"]),o=Object.keys(e),i=0;i<o.length;i++){var a=o[i];if(r.has(a))return t.logError("".concat(a," is not supported videoConstaints."),!0),!1}return!0},e}(),M=n(8542),C=function(){function e(e,t,n){if(this.formatMap=new Map([[r.QR_CODE,M.BarcodeFormat.QR_CODE],[r.AZTEC,M.BarcodeFormat.AZTEC],[r.CODABAR,M.BarcodeFormat.CODABAR],[r.CODE_39,M.BarcodeFormat.CODE_39],[r.CODE_93,M.BarcodeFormat.CODE_93],[r.CODE_128,M.BarcodeFormat.CODE_128],[r.DATA_MATRIX,M.BarcodeFormat.DATA_MATRIX],[r.MAXICODE,M.BarcodeFormat.MAXICODE],[r.ITF,M.BarcodeFormat.ITF],[r.EAN_13,M.BarcodeFormat.EAN_13],[r.EAN_8,M.BarcodeFormat.EAN_8],[r.PDF_417,M.BarcodeFormat.PDF_417],[r.RSS_14,M.BarcodeFormat.RSS_14],[r.RSS_EXPANDED,M.BarcodeFormat.RSS_EXPANDED],[r.UPC_A,M.BarcodeFormat.UPC_A],[r.UPC_E,M.BarcodeFormat.UPC_E],[r.UPC_EAN_EXTENSION,M.BarcodeFormat.UPC_EAN_EXTENSION]]),this.reverseFormatMap=this.createReverseFormatMap(),!M)throw"Use html5qrcode.min.js without edit, ZXing not found.";this.verbose=t,this.logger=n;var o=this.createZXingFormats(e),i=new Map;i.set(M.DecodeHintType.POSSIBLE_FORMATS,o),i.set(M.DecodeHintType.TRY_HARDER,!1),this.hints=i}return e.prototype.decodeAsync=function(e){var t=this;return new Promise(function(n,r){try{n(t.decode(e))}catch(e){r(e)}})},e.prototype.decode=function(e){var t=new M.MultiFormatReader(this.verbose,this.hints),n=new M.HTMLCanvasElementLuminanceSource(e),r=new M.BinaryBitmap(new M.HybridBinarizer(n)),o=t.decode(r);return{text:o.text,format:h.create(this.toHtml5QrcodeSupportedFormats(o.format)),debugData:this.createDebugData()}},e.prototype.createReverseFormatMap=function(){var e=new Map;return this.formatMap.forEach(function(t,n,r){e.set(t,n)}),e},e.prototype.toHtml5QrcodeSupportedFormats=function(e){if(!this.reverseFormatMap.has(e))throw"reverseFormatMap doesn't have ".concat(e);return this.reverseFormatMap.get(e)},e.prototype.createZXingFormats=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];this.formatMap.has(r)?t.push(this.formatMap.get(r)):this.logger.logError("".concat(r," is not supported by")+"ZXingHtml5QrcodeShim")}return t},e.prototype.createDebugData=function(){return{decoderName:"zxing-js"}},e}(),E=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){var u=[s,c];if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},A=function(){function e(t,n,o){if(this.formatMap=new Map([[r.QR_CODE,"qr_code"],[r.AZTEC,"aztec"],[r.CODABAR,"codabar"],[r.CODE_39,"code_39"],[r.CODE_93,"code_93"],[r.CODE_128,"code_128"],[r.DATA_MATRIX,"data_matrix"],[r.ITF,"itf"],[r.EAN_13,"ean_13"],[r.EAN_8,"ean_8"],[r.PDF_417,"pdf417"],[r.UPC_A,"upc_a"],[r.UPC_E,"upc_e"]]),this.reverseFormatMap=this.createReverseFormatMap(),!e.isSupported())throw"Use html5qrcode.min.js without edit, Use BarcodeDetectorDelegate only if it isSupported();";this.verbose=n,this.logger=o;var i=this.createBarcodeDetectorFormats(t);if(this.detector=new BarcodeDetector(i),!this.detector)throw"BarcodeDetector detector not supported"}return e.isSupported=function(){return"BarcodeDetector"in window&&(new BarcodeDetector({formats:["qr_code"]}),!0)},e.prototype.decodeAsync=function(e){var t,n,r,o;return t=this,n=void 0,r=void 0,o=function(){var t,n;return E(this,function(r){switch(r.label){case 0:return[4,this.detector.detect(e)];case 1:if(!(t=r.sent())||0===t.length)throw"No barcode or QR code detected.";return[2,{text:(n=this.selectLargestBarcode(t)).rawValue,format:h.create(this.toHtml5QrcodeSupportedFormats(n.format)),debugData:this.createDebugData()}]}})},new(r||(r=Promise))(function(e,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(t){var n;t.done?e(t.value):((n=t.value)instanceof r?n:new r(function(e){e(n)})).then(a,s)}c((o=o.apply(t,n||[])).next())})},e.prototype.selectLargestBarcode=function(e){for(var t=null,n=0,r=0;r<e.length;r++){var o=e[r],i=o.boundingBox.width*o.boundingBox.height;i>n&&(n=i,t=o)}if(!t)throw"No largest barcode found";return t},e.prototype.createBarcodeDetectorFormats=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];this.formatMap.has(r)?t.push(this.formatMap.get(r)):this.logger.warn("".concat(r," is not supported by")+"BarcodeDetectorDelegate")}return{formats:t}},e.prototype.toHtml5QrcodeSupportedFormats=function(e){if(!this.reverseFormatMap.has(e))throw"reverseFormatMap doesn't have ".concat(e);return this.reverseFormatMap.get(e)},e.prototype.createReverseFormatMap=function(){var e=new Map;return this.formatMap.forEach(function(t,n,r){e.set(t,n)}),e},e.prototype.createDebugData=function(){return{decoderName:"BarcodeDetector"}},e}(),I=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((r=r.apply(e,t||[])).next())})},v=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){var u=[s,c];if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},D=function(){function e(e,t,n,r){this.EXECUTIONS_TO_REPORT_PERFORMANCE=100,this.executions=0,this.executionResults=[],this.wasPrimaryDecoderUsedInLastDecode=!1,this.verbose=n,t&&A.isSupported()?(this.primaryDecoder=new A(e,n,r),this.secondaryDecoder=new C(e,n,r)):this.primaryDecoder=new C(e,n,r)}return e.prototype.decodeAsync=function(e){return I(this,void 0,void 0,function(){var t;return v(this,function(n){switch(n.label){case 0:t=performance.now(),n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.getDecoder().decodeAsync(e)];case 2:return[2,n.sent()];case 3:return this.possiblyLogPerformance(t),[7];case 4:return[2]}})})},e.prototype.decodeRobustlyAsync=function(e){return I(this,void 0,void 0,function(){var t,n;return v(this,function(r){switch(r.label){case 0:t=performance.now(),r.label=1;case 1:return r.trys.push([1,3,4,5]),[4,this.primaryDecoder.decodeAsync(e)];case 2:return[2,r.sent()];case 3:if(n=r.sent(),this.secondaryDecoder)return[2,this.secondaryDecoder.decodeAsync(e)];throw n;case 4:return this.possiblyLogPerformance(t),[7];case 5:return[2]}})})},e.prototype.getDecoder=function(){return this.secondaryDecoder?!1===this.wasPrimaryDecoderUsedInLastDecode?(this.wasPrimaryDecoderUsedInLastDecode=!0,this.primaryDecoder):(this.wasPrimaryDecoderUsedInLastDecode=!1,this.secondaryDecoder):this.primaryDecoder},e.prototype.possiblyLogPerformance=function(e){if(this.verbose){var t=performance.now()-e;this.executionResults.push(t),this.executions++,this.possiblyFlushPerformanceReport()}},e.prototype.possiblyFlushPerformanceReport=function(){if(!(this.executions<this.EXECUTIONS_TO_REPORT_PERFORMANCE)){for(var e=0,t=0,n=this.executionResults;t<n.length;t++)e+=n[t];var r=e/this.executionResults.length;console.log("".concat(r," ms for ").concat(this.executionResults.length," last runs.")),this.executions=0,this.executionResults=[]}},e}(),N=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),b=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((r=r.apply(e,t||[])).next())})},w=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){var u=[s,c];if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},L=function(){function e(e,t){this.name=e,this.track=t}return e.prototype.isSupported=function(){return!!this.track.getCapabilities&&this.name in this.track.getCapabilities()},e.prototype.apply=function(e){var t={};return t[this.name]=e,this.track.applyConstraints({advanced:[t]})},e.prototype.value=function(){var e=this.track.getSettings();return this.name in e?e[this.name]:null},e}(),x=function(e){function t(t){return e.call(this,"zoom",t)||this}return N(t,e),t}(function(e){function t(t,n){return e.call(this,t,n)||this}return N(t,e),t.prototype.min=function(){return this.getCapabilities().min},t.prototype.max=function(){return this.getCapabilities().max},t.prototype.step=function(){return this.getCapabilities().step},t.prototype.apply=function(e){var t={};return t[this.name]=e,this.track.applyConstraints({advanced:[t]})},t.prototype.getCapabilities=function(){this.failIfNotSupported();var e=this.track.getCapabilities()[this.name];return{min:e.min,max:e.max,step:e.step}},t.prototype.failIfNotSupported=function(){if(!this.isSupported())throw Error("".concat(this.name," capability not supported"))},t}(L)),O=function(e){function t(t){return e.call(this,"torch",t)||this}return N(t,e),t}(L),_=function(){function e(e){this.track=e}return e.prototype.zoomFeature=function(){return new x(this.track)},e.prototype.torchFeature=function(){return new O(this.track)},e}(),R=function(){function e(e,t,n){this.isClosed=!1,this.parentElement=e,this.mediaStream=t,this.callbacks=n,this.surface=this.createVideoElement(this.parentElement.clientWidth),e.append(this.surface)}return e.prototype.createVideoElement=function(e){var t=document.createElement("video");return t.style.width="".concat(e,"px"),t.style.display="block",t.muted=!0,t.setAttribute("muted","true"),t.playsInline=!0,t},e.prototype.setupSurface=function(){var e=this;this.surface.onabort=function(){throw"RenderedCameraImpl video surface onabort() called"},this.surface.onerror=function(){throw"RenderedCameraImpl video surface onerror() called"};var t=function(){var n=e.surface.clientWidth,r=e.surface.clientHeight;e.callbacks.onRenderSurfaceReady(n,r),e.surface.removeEventListener("playing",t)};this.surface.addEventListener("playing",t),this.surface.srcObject=this.mediaStream,this.surface.play()},e.create=function(t,n,r,o){return b(this,void 0,void 0,function(){var i,a;return w(this,function(s){switch(s.label){case 0:if(i=new e(t,n,o),!r.aspectRatio)return[3,2];return a={aspectRatio:r.aspectRatio},[4,i.getFirstTrackOrFail().applyConstraints(a)];case 1:s.sent(),s.label=2;case 2:return i.setupSurface(),[2,i]}})})},e.prototype.failIfClosed=function(){if(this.isClosed)throw"The RenderedCamera has already been closed."},e.prototype.getFirstTrackOrFail=function(){if(this.failIfClosed(),0===this.mediaStream.getVideoTracks().length)throw"No video tracks found";return this.mediaStream.getVideoTracks()[0]},e.prototype.pause=function(){this.failIfClosed(),this.surface.pause()},e.prototype.resume=function(e){this.failIfClosed();var t=this,n=function(){setTimeout(e,200),t.surface.removeEventListener("playing",n)};this.surface.addEventListener("playing",n),this.surface.play()},e.prototype.isPaused=function(){return this.failIfClosed(),this.surface.paused},e.prototype.getSurface=function(){return this.failIfClosed(),this.surface},e.prototype.getRunningTrackCapabilities=function(){return this.getFirstTrackOrFail().getCapabilities()},e.prototype.getRunningTrackSettings=function(){return this.getFirstTrackOrFail().getSettings()},e.prototype.applyVideoConstraints=function(e){return b(this,void 0,void 0,function(){return w(this,function(t){if("aspectRatio"in e)throw"Changing 'aspectRatio' in run-time is not yet supported.";return[2,this.getFirstTrackOrFail().applyConstraints(e)]})})},e.prototype.close=function(){if(this.isClosed)return Promise.resolve();var e=this;return new Promise(function(t,n){var r=e.mediaStream.getVideoTracks().length,o=0;e.mediaStream.getVideoTracks().forEach(function(n){e.mediaStream.removeTrack(n),n.stop(),++o>=r&&(e.isClosed=!0,e.parentElement.removeChild(e.surface),t())})})},e.prototype.getCapabilities=function(){return new _(this.getFirstTrackOrFail())},e}(),U=function(){function e(e){this.mediaStream=e}return e.prototype.render=function(e,t,n){return b(this,void 0,void 0,function(){return w(this,function(r){return[2,R.create(e,this.mediaStream,t,n)]})})},e.create=function(t){return b(this,void 0,void 0,function(){var n;return w(this,function(r){switch(r.label){case 0:if(!navigator.mediaDevices)throw"navigator.mediaDevices not supported";return n={audio:!1,video:t},[4,navigator.mediaDevices.getUserMedia(n)];case 1:return[2,new e(r.sent())]}})})},e}(),j=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((r=r.apply(e,t||[])).next())})},k=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){var u=[s,c];if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},B=function(){function e(){}return e.failIfNotSupported=function(){return j(this,void 0,void 0,function(){return k(this,function(t){if(!navigator.mediaDevices)throw"navigator.mediaDevices not supported";return[2,new e]})})},e.prototype.create=function(e){return j(this,void 0,void 0,function(){return k(this,function(t){return[2,U.create(e)]})})},e}(),F=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){var u=[s,c];if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},z=function(){function e(){}return e.retrieve=function(){if(navigator.mediaDevices)return e.getCamerasFromMediaDevices();var t=MediaStreamTrack;return MediaStreamTrack&&t.getSources?e.getCamerasFromMediaStreamTrack():e.rejectWithError()},e.rejectWithError=function(){var t=y.unableToQuerySupportedDevices();return e.isHttpsOrLocalhost()||(t=y.insecureContextCameraQueryError()),Promise.reject(t)},e.isHttpsOrLocalhost=function(){if("https:"===location.protocol)return!0;var e=location.host.split(":")[0];return"127.0.0.1"===e||"localhost"===e},e.getCamerasFromMediaDevices=function(){var e,t,n,r;return e=this,t=void 0,n=void 0,r=function(){var e,t,n,r,o,i,a;return F(this,function(s){switch(s.label){case 0:return e=function(e){for(var t=e.getVideoTracks(),n=0;n<t.length;n++){var r=t[n];r.enabled=!1,r.stop(),e.removeTrack(r)}},[4,navigator.mediaDevices.getUserMedia({audio:!1,video:!0})];case 1:return t=s.sent(),[4,navigator.mediaDevices.enumerateDevices()];case 2:for(o=0,n=s.sent(),r=[],i=n;o<i.length;o++)"videoinput"===(a=i[o]).kind&&r.push({id:a.deviceId,label:a.label});return e(t),[2,r]}})},new(n||(n=Promise))(function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((r=r.apply(e,t||[])).next())})},e.getCamerasFromMediaStreamTrack=function(){return new Promise(function(e,t){MediaStreamTrack.getSources(function(t){for(var n=[],r=0;r<t.length;r++){var o=t[r];"video"===o.kind&&n.push({id:o.id,label:o.label})}e(n)})})},e}();!function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.NOT_STARTED=1]="NOT_STARTED",e[e.SCANNING=2]="SCANNING",e[e.PAUSED=3]="PAUSED"}(s||(s={}));var P=function(){function e(){this.state=s.NOT_STARTED,this.onGoingTransactionNewState=s.UNKNOWN}return e.prototype.directTransition=function(e){this.failIfTransitionOngoing(),this.validateTransition(e),this.state=e},e.prototype.startTransition=function(e){return this.failIfTransitionOngoing(),this.validateTransition(e),this.onGoingTransactionNewState=e,this},e.prototype.execute=function(){if(this.onGoingTransactionNewState===s.UNKNOWN)throw"Transaction is already cancelled, cannot execute().";var e=this.onGoingTransactionNewState;this.onGoingTransactionNewState=s.UNKNOWN,this.directTransition(e)},e.prototype.cancel=function(){if(this.onGoingTransactionNewState===s.UNKNOWN)throw"Transaction is already cancelled, cannot cancel().";this.onGoingTransactionNewState=s.UNKNOWN},e.prototype.getState=function(){return this.state},e.prototype.failIfTransitionOngoing=function(){if(this.onGoingTransactionNewState!==s.UNKNOWN)throw"Cannot transition to a new state, already under transition"},e.prototype.validateTransition=function(e){switch(this.state){case s.UNKNOWN:throw"Transition from unknown is not allowed";case s.NOT_STARTED:this.failIfNewStateIs(e,[s.PAUSED]);case s.SCANNING:case s.PAUSED:}},e.prototype.failIfNewStateIs=function(e,t){for(var n=0;n<t.length;n++)if(e===t[n])throw"Cannot transition from ".concat(this.state," to ").concat(e)},e}(),H=function(){function e(e){this.stateManager=e}return e.prototype.startTransition=function(e){return this.stateManager.startTransition(e)},e.prototype.directTransition=function(e){this.stateManager.directTransition(e)},e.prototype.getState=function(){return this.stateManager.getState()},e.prototype.canScanFile=function(){return this.stateManager.getState()===s.NOT_STARTED},e.prototype.isScanning=function(){return this.stateManager.getState()!==s.NOT_STARTED},e.prototype.isStrictlyScanning=function(){return this.stateManager.getState()===s.SCANNING},e.prototype.isPaused=function(){return this.stateManager.getState()===s.PAUSED},e}(),Q=function(){function e(){}return e.create=function(){return new H(new P)},e}(),Y=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),V=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),t.DEFAULT_WIDTH=300,t.DEFAULT_WIDTH_OFFSET=2,t.FILE_SCAN_MIN_HEIGHT=300,t.FILE_SCAN_HIDDEN_CANVAS_PADDING=100,t.MIN_QR_BOX_SIZE=50,t.SHADED_LEFT=1,t.SHADED_RIGHT=2,t.SHADED_TOP=3,t.SHADED_BOTTOM=4,t.SHADED_REGION_ELEMENT_ID="qr-shaded-region",t.VERBOSE=!1,t.BORDER_SHADER_DEFAULT_COLOR="#ffffff",t.BORDER_SHADER_MATCH_COLOR="rgb(90, 193, 56)",t}(l),q=function(){function e(e,t){this.logger=t,this.fps=V.SCAN_DEFAULT_FPS,e?(e.fps&&(this.fps=e.fps),this.disableFlip=!0===e.disableFlip,this.qrbox=e.qrbox,this.aspectRatio=e.aspectRatio,this.videoConstraints=e.videoConstraints):this.disableFlip=V.DEFAULT_DISABLE_FLIP}return e.prototype.isMediaStreamConstraintsValid=function(){return this.videoConstraints?T.isMediaStreamConstraintsValid(this.videoConstraints,this.logger):(this.logger.logError("Empty videoConstraints",!0),!1)},e.prototype.isShadedBoxEnabled=function(){return!g(this.qrbox)},e.create=function(t,n){return new e(t,n)},e}(),W=function(){function e(e,t){var n;if(this.element=null,this.canvasElement=null,this.scannerPausedUiElement=null,this.hasBorderShaders=null,this.borderShaders=null,this.qrMatch=null,this.renderedCamera=null,this.qrRegion=null,this.context=null,this.lastScanImageFile=null,this.isScanning=!1,!document.getElementById(e))throw"HTML Element with id=".concat(e," not found");this.elementId=e,this.verbose=!1,"boolean"==typeof t?this.verbose=!0===t:t&&(n=t,this.verbose=!0===n.verbose,n.experimentalFeatures),this.logger=new f(this.verbose),this.qrcode=new D(this.getSupportedFormats(t),this.getUseBarCodeDetectorIfSupported(n),this.verbose,this.logger),this.foreverScanTimeout,this.shouldScan=!0,this.stateManagerProxy=Q.create()}return e.prototype.start=function(e,t,n,r){var o,i=this;if(!e)throw"cameraIdOrConfig is required";if(!n||"function"!=typeof n)throw"qrCodeSuccessCallback is required and should be a function.";o=r||(this.verbose?this.logger.log:function(){});var a=q.create(t,this.logger);this.clearElement();var c=!1;a.videoConstraints&&(a.isMediaStreamConstraintsValid()?c=!0:this.logger.logError("'videoConstraints' is not valid 'MediaStreamConstraints, it will be ignored.'",!0));var u=c,l=document.getElementById(this.elementId);l.clientWidth?l.clientWidth:V.DEFAULT_WIDTH,l.style.position="relative",this.shouldScan=!0,this.element=l;var h=this,d=this.stateManagerProxy.startTransition(s.SCANNING);return new Promise(function(t,r){var s=u?a.videoConstraints:h.createVideoConstraints(e);if(!s){d.cancel(),r("videoConstraints should be defined");return}var c={};(!u||a.aspectRatio)&&(c.aspectRatio=a.aspectRatio);var l={onRenderSurfaceReady:function(e,t){h.setupUi(e,t,a),h.isScanning=!0,h.foreverScan(a,n,o)}};B.failIfNotSupported().then(function(e){e.create(s).then(function(e){return e.render(i.element,c,l).then(function(e){h.renderedCamera=e,d.execute(),t(null)}).catch(function(e){d.cancel(),r(e)})}).catch(function(e){d.cancel(),r(y.errorGettingUserMedia(e))})}).catch(function(e){d.cancel(),r(y.cameraStreamingNotSupported())})})},e.prototype.pause=function(e){if(!this.stateManagerProxy.isStrictlyScanning())throw"Cannot pause, scanner is not scanning.";this.stateManagerProxy.directTransition(s.PAUSED),this.showPausedState(),(g(e)||!0!==e)&&(e=!1),e&&this.renderedCamera&&this.renderedCamera.pause()},e.prototype.resume=function(){if(!this.stateManagerProxy.isPaused())throw"Cannot result, scanner is not paused.";if(!this.renderedCamera)throw"renderedCamera doesn't exist while trying resume()";var e=this,t=function(){e.stateManagerProxy.directTransition(s.SCANNING),e.hidePausedState()};if(!this.renderedCamera.isPaused())return void t();this.renderedCamera.resume(function(){t()})},e.prototype.getState=function(){return this.stateManagerProxy.getState()},e.prototype.stop=function(){var e=this;if(!this.stateManagerProxy.isScanning())throw"Cannot stop, scanner is not running or paused.";var t=this.stateManagerProxy.startTransition(s.NOT_STARTED);this.shouldScan=!1,this.foreverScanTimeout&&clearTimeout(this.foreverScanTimeout);var n=function(){if(e.element){var t=document.getElementById(V.SHADED_REGION_ELEMENT_ID);t&&e.element.removeChild(t)}},r=this;return this.renderedCamera.close().then(function(){return r.renderedCamera=null,r.element&&(r.element.removeChild(r.canvasElement),r.canvasElement=null),n(),r.qrRegion&&(r.qrRegion=null),r.context&&(r.context=null),t.execute(),r.hidePausedState(),r.isScanning=!1,Promise.resolve()})},e.prototype.scanFile=function(e,t){return this.scanFileV2(e,t).then(function(e){return e.decodedText})},e.prototype.scanFileV2=function(e,t){var n=this;if(!e||!(e instanceof File))throw"imageFile argument is mandatory and should be instance of File. Use 'event.target.files[0]'.";if(g(t)&&(t=!0),!this.stateManagerProxy.canScanFile())throw"Cannot start file scan - ongoing camera scan";return new Promise(function(r,o){n.possiblyCloseLastScanImageFile(),n.clearElement(),n.lastScanImageFile=URL.createObjectURL(e);var i=new Image;i.onload=function(){var e=i.width,a=i.height,s=document.getElementById(n.elementId),c=s.clientWidth?s.clientWidth:V.DEFAULT_WIDTH,u=Math.max(s.clientHeight?s.clientHeight:a,V.FILE_SCAN_MIN_HEIGHT),l=n.computeCanvasDrawConfig(e,a,c,u);if(t){var h=n.createCanvasElement(c,u,"qr-canvas-visible");h.style.display="inline-block",s.appendChild(h);var p=h.getContext("2d");if(!p)throw"Unable to get 2d context from canvas";p.canvas.width=c,p.canvas.height=u,p.drawImage(i,0,0,e,a,l.x,l.y,l.width,l.height)}var f=V.FILE_SCAN_HIDDEN_CANVAS_PADDING,g=Math.max(i.width,l.width),y=Math.max(i.height,l.height),m=g+2*f,S=y+2*f,T=n.createCanvasElement(m,S);s.appendChild(T);var M=T.getContext("2d");if(!M)throw"Unable to get 2d context from canvas";M.canvas.width=m,M.canvas.height=S,M.drawImage(i,0,0,e,a,f,f,g,y);try{n.qrcode.decodeRobustlyAsync(T).then(function(e){r(d.createFromQrcodeResult(e))}).catch(o)}catch(e){o("QR code parse error, error = ".concat(e))}},i.onerror=o,i.onabort=o,i.onstalled=o,i.onsuspend=o,i.src=URL.createObjectURL(e)})},e.prototype.clear=function(){this.clearElement()},e.getCameras=function(){return z.retrieve()},e.prototype.getRunningTrackCapabilities=function(){return this.getRenderedCameraOrFail().getRunningTrackCapabilities()},e.prototype.getRunningTrackSettings=function(){return this.getRenderedCameraOrFail().getRunningTrackSettings()},e.prototype.getRunningTrackCameraCapabilities=function(){return this.getRenderedCameraOrFail().getCapabilities()},e.prototype.applyVideoConstraints=function(e){if(e){if(!T.isMediaStreamConstraintsValid(e,this.logger))throw"invalid videoConstaints passed, check logs for more details"}else throw"videoConstaints is required argument.";return this.getRenderedCameraOrFail().applyVideoConstraints(e)},e.prototype.getRenderedCameraOrFail=function(){if(null==this.renderedCamera)throw"Scanning is not in running state, call this API only when QR code scanning using camera is in running state.";return this.renderedCamera},e.prototype.getSupportedFormats=function(e){var t=[r.QR_CODE,r.AZTEC,r.CODABAR,r.CODE_39,r.CODE_93,r.CODE_128,r.DATA_MATRIX,r.MAXICODE,r.ITF,r.EAN_13,r.EAN_8,r.PDF_417,r.RSS_14,r.RSS_EXPANDED,r.UPC_A,r.UPC_E,r.UPC_EAN_EXTENSION];if(!e||"boolean"==typeof e||!e.formatsToSupport)return t;if(!Array.isArray(e.formatsToSupport))throw"configOrVerbosityFlag.formatsToSupport should be undefined or an array.";if(0===e.formatsToSupport.length)throw"Atleast 1 formatsToSupport is needed.";for(var n=[],o=0,i=e.formatsToSupport;o<i.length;o++){var a=i[o];Object.values(r).includes(a)?n.push(a):this.logger.warn("Invalid format: ".concat(a," passed in config, ignoring."))}if(0===n.length)throw"None of formatsToSupport match supported values.";return n},e.prototype.getUseBarCodeDetectorIfSupported=function(e){if(g(e))return!0;if(!g(e.useBarCodeDetectorIfSupported))return!1!==e.useBarCodeDetectorIfSupported;if(g(e.experimentalFeatures))return!0;var t=e.experimentalFeatures;return!!g(t.useBarCodeDetectorIfSupported)||!1!==t.useBarCodeDetectorIfSupported},e.prototype.validateQrboxSize=function(e,t,n){var r,o=n.qrbox;this.validateQrboxConfig(o);var i=this.toQrdimensions(e,t,o),a=function(e){if(e<V.MIN_QR_BOX_SIZE)throw"minimum size of 'config.qrbox' dimension value is"+" ".concat(V.MIN_QR_BOX_SIZE,"px.")};a(i.width),a(i.height),(r=i.width)>e&&(this.logger.warn("`qrbox.width` or `qrbox` is larger than the width of the root element. The width will be truncated to the width of root element."),r=e),i.width=r},e.prototype.validateQrboxConfig=function(e){if("number"!=typeof e&&"function"!=typeof e&&(void 0===e.width||void 0===e.height))throw"Invalid instance of QrDimensions passed for 'config.qrbox'. Both 'width' and 'height' should be set."},e.prototype.toQrdimensions=function(e,t,n){if("number"==typeof n)return{width:n,height:n};if("function"==typeof n)try{return n(e,t)}catch(e){throw Error("qrbox config was passed as a function but it failed with unknown error"+e)}return n},e.prototype.setupUi=function(e,t,n){n.isShadedBoxEnabled()&&this.validateQrboxSize(e,t,n);var r=g(n.qrbox)?{width:e,height:t}:n.qrbox;this.validateQrboxConfig(r);var o=this.toQrdimensions(e,t,r);o.height>t&&this.logger.warn("[Html5Qrcode] config.qrbox has height that isgreater than the height of the video stream. Shading will be ignored");var i=n.isShadedBoxEnabled()&&o.height<=t,a=i?this.getShadedRegionBounds(e,t,o):{x:0,y:0,width:e,height:t},s=this.createCanvasElement(a.width,a.height),c=s.getContext("2d",{willReadFrequently:!0});c.canvas.width=a.width,c.canvas.height=a.height,this.element.append(s),i&&this.possiblyInsertShadingElement(this.element,e,t,o),this.createScannerPausedUiElement(this.element),this.qrRegion=a,this.context=c,this.canvasElement=s},e.prototype.createScannerPausedUiElement=function(e){var t=document.createElement("div");t.innerText=y.scannerPaused(),t.style.display="none",t.style.position="absolute",t.style.top="0px",t.style.zIndex="1",t.style.background="rgba(9, 9, 9, 0.46)",t.style.color="#FFECEC",t.style.textAlign="center",t.style.width="100%",e.appendChild(t),this.scannerPausedUiElement=t},e.prototype.scanContext=function(e,t){var n=this;return this.stateManagerProxy.isPaused()?Promise.resolve(!1):this.qrcode.decodeAsync(this.canvasElement).then(function(t){return e(t.text,d.createFromQrcodeResult(t)),n.possiblyUpdateShaders(!0),!0}).catch(function(e){n.possiblyUpdateShaders(!1);var r=y.codeParseError(e);return t(r,p.createFrom(r)),!1})},e.prototype.foreverScan=function(e,t,n){var r=this;if(this.shouldScan&&this.renderedCamera){var o=this.renderedCamera.getSurface(),i=o.videoWidth/o.clientWidth,a=o.videoHeight/o.clientHeight;if(!this.qrRegion)throw"qrRegion undefined when localMediaStream is ready.";var s=this.qrRegion.width*i,c=this.qrRegion.height*a,u=this.qrRegion.x*i,l=this.qrRegion.y*a;this.context.drawImage(o,u,l,s,c,0,0,this.qrRegion.width,this.qrRegion.height);var h=function(){r.foreverScanTimeout=setTimeout(function(){r.foreverScan(e,t,n)},r.getTimeoutFps(e.fps))};this.scanContext(t,n).then(function(o){o||!0===e.disableFlip?h():(r.context.translate(r.context.canvas.width,0),r.context.scale(-1,1),r.scanContext(t,n).finally(function(){h()}))}).catch(function(e){r.logger.logError("Error happend while scanning context",e),h()})}},e.prototype.createVideoConstraints=function(e){if("string"==typeof e)return{deviceId:{exact:e}};if("object"==typeof e){var t="facingMode",n="deviceId",r={user:!0,environment:!0},o="exact",i=function(e){if(e in r)return!0;throw"config has invalid 'facingMode' value = "+"'".concat(e,"'")},a=Object.keys(e);if(1!==a.length)throw"'cameraIdOrConfig' object should have exactly 1 key,"+" if passed as an object, found ".concat(a.length," keys");var s=Object.keys(e)[0];if(s!==t&&s!==n)throw"Only '".concat(t,"' and '").concat(n,"' ")+" are supported for 'cameraIdOrConfig'";if(s===t){var c=e.facingMode;if("string"==typeof c){if(i(c))return{facingMode:c}}else if("object"==typeof c)if(o in c){if(i(c["".concat(o)]))return{facingMode:{exact:c["".concat(o)]}}}else throw"'facingMode' should be string or object with"+" ".concat(o," as key.");else{var u=typeof c;throw"Invalid type of 'facingMode' = ".concat(u)}}else{var l=e.deviceId;if("string"==typeof l)return{deviceId:l};if("object"==typeof l)if(o in l)return{deviceId:{exact:l["".concat(o)]}};else throw"'deviceId' should be string or object with"+" ".concat(o," as key.");var h=typeof l;throw"Invalid type of 'deviceId' = ".concat(h)}}var d=typeof e;throw"Invalid type of 'cameraIdOrConfig' = ".concat(d)},e.prototype.computeCanvasDrawConfig=function(e,t,n,r){if(e<=n&&t<=r)return{x:(n-e)/2,y:(r-t)/2,width:e,height:t};var o=e,i=t;return e>n&&(t=n/e*t,e=n),t>r&&(e=r/t*e,t=r),this.logger.log("Image downsampled from "+"".concat(o,"X").concat(i)+" to ".concat(e,"X").concat(t,".")),this.computeCanvasDrawConfig(e,t,n,r)},e.prototype.clearElement=function(){if(this.stateManagerProxy.isScanning())throw"Cannot clear while scan is ongoing, close it first.";var e=document.getElementById(this.elementId);e&&(e.innerHTML="")},e.prototype.possiblyUpdateShaders=function(e){this.qrMatch!==e&&(this.hasBorderShaders&&this.borderShaders&&this.borderShaders.length&&this.borderShaders.forEach(function(t){t.style.backgroundColor=e?V.BORDER_SHADER_MATCH_COLOR:V.BORDER_SHADER_DEFAULT_COLOR}),this.qrMatch=e)},e.prototype.possiblyCloseLastScanImageFile=function(){this.lastScanImageFile&&(URL.revokeObjectURL(this.lastScanImageFile),this.lastScanImageFile=null)},e.prototype.createCanvasElement=function(e,t,n){var r=document.createElement("canvas");return r.style.width="".concat(e,"px"),r.style.height="".concat(t,"px"),r.style.display="none",r.id=g(n)?"qr-canvas":n,r},e.prototype.getShadedRegionBounds=function(e,t,n){if(n.width>e||n.height>t)throw"'config.qrbox' dimensions should not be greater than the dimensions of the root HTML element.";return{x:(e-n.width)/2,y:(t-n.height)/2,width:n.width,height:n.height}},e.prototype.possiblyInsertShadingElement=function(e,t,n,r){if(!(t-r.width<1)&&!(n-r.height<1)){var o=document.createElement("div");o.style.position="absolute";var i=(t-r.width)/2,a=(n-r.height)/2;o.style.borderLeft="".concat(i,"px solid rgba(0, 0, 0, 0.48)"),o.style.borderRight="".concat(i,"px solid rgba(0, 0, 0, 0.48)"),o.style.borderTop="".concat(a,"px solid rgba(0, 0, 0, 0.48)"),o.style.borderBottom="".concat(a,"px solid rgba(0, 0, 0, 0.48)"),o.style.boxSizing="border-box",o.style.top="0px",o.style.bottom="0px",o.style.left="0px",o.style.right="0px",o.id="".concat(V.SHADED_REGION_ELEMENT_ID),t-r.width<11||n-r.height<11?this.hasBorderShaders=!1:(this.insertShaderBorders(o,40,5,-5,null,0,!0),this.insertShaderBorders(o,40,5,-5,null,0,!1),this.insertShaderBorders(o,40,5,null,-5,0,!0),this.insertShaderBorders(o,40,5,null,-5,0,!1),this.insertShaderBorders(o,5,45,-5,null,-5,!0),this.insertShaderBorders(o,5,45,null,-5,-5,!0),this.insertShaderBorders(o,5,45,-5,null,-5,!1),this.insertShaderBorders(o,5,45,null,-5,-5,!1),this.hasBorderShaders=!0),e.append(o)}},e.prototype.insertShaderBorders=function(e,t,n,r,o,i,a){var s=document.createElement("div");s.style.position="absolute",s.style.backgroundColor=V.BORDER_SHADER_DEFAULT_COLOR,s.style.width="".concat(t,"px"),s.style.height="".concat(n,"px"),null!==r&&(s.style.top="".concat(r,"px")),null!==o&&(s.style.bottom="".concat(o,"px")),a?s.style.left="".concat(i,"px"):s.style.right="".concat(i,"px"),this.borderShaders||(this.borderShaders=[]),this.borderShaders.push(s),e.appendChild(s)},e.prototype.showPausedState=function(){if(!this.scannerPausedUiElement)throw"[internal error] scanner paused UI element not found";this.scannerPausedUiElement.style.display="block"},e.prototype.hidePausedState=function(){if(!this.scannerPausedUiElement)throw"[internal error] scanner paused UI element not found";this.scannerPausedUiElement.style.display="none"},e.prototype.getTimeoutFps=function(e){return 1e3/e},e}(),G="data:image/svg+xml;base64,",X=G+"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",Z=G+"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1OS4wMTggNTkuMDE4IiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1OS4wMTggNTkuMDE4IiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBkPSJtNTguNzQxIDU0LjgwOS01Ljk2OS02LjI0NGExMC43NCAxMC43NCAwIDAgMCAyLjgyLTcuMjVjMC01Ljk1My00Ljg0My0xMC43OTYtMTAuNzk2LTEwLjc5NlMzNCAzNS4zNjEgMzQgNDEuMzE0IDM4Ljg0MyA1Mi4xMSA0NC43OTYgNTIuMTFjMi40NDEgMCA0LjY4OC0uODI0IDYuNDk5LTIuMTk2bDYuMDAxIDYuMjc3YS45OTguOTk4IDAgMCAwIDEuNDE0LjAzMiAxIDEgMCAwIDAgLjAzMS0xLjQxNHpNMzYgNDEuMzE0YzAtNC44NSAzLjk0Ni04Ljc5NiA4Ljc5Ni04Ljc5NnM4Ljc5NiAzLjk0NiA4Ljc5NiA4Ljc5Ni0zLjk0NiA4Ljc5Ni04Ljc5NiA4Ljc5NlMzNiA0Ni4xNjQgMzYgNDEuMzE0ek0xMC40MzEgMTYuMDg4YzAgMy4wNyAyLjQ5OCA1LjU2OCA1LjU2OSA1LjU2OHM1LjU2OS0yLjQ5OCA1LjU2OS01LjU2OGMwLTMuMDcxLTIuNDk4LTUuNTY5LTUuNTY5LTUuNTY5cy01LjU2OSAyLjQ5OC01LjU2OSA1LjU2OXptOS4xMzggMGMwIDEuOTY4LTEuNjAyIDMuNTY4LTMuNTY5IDMuNTY4cy0zLjU2OS0xLjYwMS0zLjU2OS0zLjU2OCAxLjYwMi0zLjU2OSAzLjU2OS0zLjU2OSAzLjU2OSAxLjYwMSAzLjU2OSAzLjU2OXoiLz48cGF0aCBkPSJtMzAuODgyIDI4Ljk4NyA5LjE4LTEwLjA1NCAxMS4yNjIgMTAuMzIzYTEgMSAwIDAgMCAxLjM1MS0xLjQ3NWwtMTItMTFhMSAxIDAgMCAwLTEuNDE0LjA2M2wtOS43OTQgMTAuNzI3LTQuNzQzLTQuNzQzYTEuMDAzIDEuMDAzIDAgMCAwLTEuMzY4LS4wNDRMNi4zMzkgMzcuNzY4YTEgMSAwIDEgMCAxLjMyMiAxLjUwMWwxNi4zMTMtMTQuMzYyIDcuMzE5IDcuMzE4YS45OTkuOTk5IDAgMSAwIDEuNDE0LTEuNDE0bC0xLjgyNS0xLjgyNHoiLz48cGF0aCBkPSJNMzAgNDYuNTE4SDJ2LTQyaDU0djI4YTEgMSAwIDEgMCAyIDB2LTI5YTEgMSAwIDAgMC0xLTFIMWExIDEgMCAwIDAtMSAxdjQ0YTEgMSAwIDAgMCAxIDFoMjlhMSAxIDAgMSAwIDAtMnoiLz48L3N2Zz4=",K=G+"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NjAgNDYwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA0NjAgNDYwIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBkPSJNMjMwIDBDMTAyLjk3NSAwIDAgMTAyLjk3NSAwIDIzMHMxMDIuOTc1IDIzMCAyMzAgMjMwIDIzMC0xMDIuOTc0IDIzMC0yMzBTMzU3LjAyNSAwIDIzMCAwem0zOC4zMzMgMzc3LjM2YzAgOC42NzYtNy4wMzQgMTUuNzEtMTUuNzEgMTUuNzFoLTQzLjEwMWMtOC42NzYgMC0xNS43MS03LjAzNC0xNS43MS0xNS43MVYyMDIuNDc3YzAtOC42NzYgNy4wMzMtMTUuNzEgMTUuNzEtMTUuNzFoNDMuMTAxYzguNjc2IDAgMTUuNzEgNy4wMzMgMTUuNzEgMTUuNzFWMzc3LjM2ek0yMzAgMTU3Yy0yMS41MzkgMC0zOS0xNy40NjEtMzktMzlzMTcuNDYxLTM5IDM5LTM5IDM5IDE3LjQ2MSAzOSAzOS0xNy40NjEgMzktMzkgMzl6Ii8+PC9zdmc+",J=function(){function e(){}return e.createDefault=function(){return{hasPermission:!1,lastUsedCameraId:null}},e}(),$=function(){function e(){this.data=J.createDefault();var t=localStorage.getItem(e.LOCAL_STORAGE_KEY);t?this.data=JSON.parse(t):this.reset()}return e.prototype.hasCameraPermissions=function(){return this.data.hasPermission},e.prototype.getLastUsedCameraId=function(){return this.data.lastUsedCameraId},e.prototype.setHasPermission=function(e){this.data.hasPermission=e,this.flush()},e.prototype.setLastUsedCameraId=function(e){this.data.lastUsedCameraId=e,this.flush()},e.prototype.resetLastUsedCameraId=function(){this.data.lastUsedCameraId=null,this.flush()},e.prototype.reset=function(){this.data=J.createDefault(),this.flush()},e.prototype.flush=function(){localStorage.setItem(e.LOCAL_STORAGE_KEY,JSON.stringify(this.data))},e.LOCAL_STORAGE_KEY="HTML5_QRCODE_DATA",e}(),ee=function(){function e(){this.infoDiv=document.createElement("div")}return e.prototype.renderInto=function(e){this.infoDiv.style.position="absolute",this.infoDiv.style.top="10px",this.infoDiv.style.right="10px",this.infoDiv.style.zIndex="2",this.infoDiv.style.display="none",this.infoDiv.style.padding="5pt",this.infoDiv.style.border="1px solid #171717",this.infoDiv.style.fontSize="10pt",this.infoDiv.style.background="rgb(0 0 0 / 69%)",this.infoDiv.style.borderRadius="5px",this.infoDiv.style.textAlign="center",this.infoDiv.style.fontWeight="400",this.infoDiv.style.color="white",this.infoDiv.innerText=S.poweredBy();var t=document.createElement("a");t.innerText="ScanApp",t.href="https://scanapp.org",t.target="new",t.style.color="white",this.infoDiv.appendChild(t);var n=document.createElement("br"),r=document.createElement("br");this.infoDiv.appendChild(n),this.infoDiv.appendChild(r);var o=document.createElement("a");o.innerText=S.reportIssues(),o.href="https://github.com/mebjas/html5-qrcode/issues",o.target="new",o.style.color="white",this.infoDiv.appendChild(o),e.appendChild(this.infoDiv)},e.prototype.show=function(){this.infoDiv.style.display="block"},e.prototype.hide=function(){this.infoDiv.style.display="none"},e}(),et=function(){function e(e,t){this.isShowingInfoIcon=!0,this.onTapIn=e,this.onTapOut=t,this.infoIcon=document.createElement("img")}return e.prototype.renderInto=function(e){var t=this;this.infoIcon.alt="Info icon",this.infoIcon.src=K,this.infoIcon.style.position="absolute",this.infoIcon.style.top="4px",this.infoIcon.style.right="4px",this.infoIcon.style.opacity="0.6",this.infoIcon.style.cursor="pointer",this.infoIcon.style.zIndex="2",this.infoIcon.style.width="16px",this.infoIcon.style.height="16px",this.infoIcon.onmouseover=function(e){return t.onHoverIn()},this.infoIcon.onmouseout=function(e){return t.onHoverOut()},this.infoIcon.onclick=function(e){return t.onClick()},e.appendChild(this.infoIcon)},e.prototype.onHoverIn=function(){this.isShowingInfoIcon&&(this.infoIcon.style.opacity="1")},e.prototype.onHoverOut=function(){this.isShowingInfoIcon&&(this.infoIcon.style.opacity="0.6")},e.prototype.onClick=function(){this.isShowingInfoIcon?(this.isShowingInfoIcon=!1,this.onTapIn(),this.infoIcon.src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAQgAAAEIBarqQRAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAE1SURBVDiNfdI7S0NBEAXgLya1otFgpbYSbISAgpXYi6CmiH9KCAiChaVga6OiWPgfRDQ+0itaGVNosXtluWwcuMzePfM4M3sq8lbHBubwg1dc4m1E/J/N4ghDPOIsfk/4xiEao5KX0McFljN4C9d4QTPXuY99jP3DsIoDPGM6BY5i5yI5R7O4q+ImFkJY2DCh3cAH2klyB+9J1xUMMAG7eCh1a+Mr+k48b5diXrFVwwLuS+BJ9MfR7+G0FHOHhTHhnXNWS87VDF4pcnfQK4Ep7XScNLmPTZgURNKKYENYWDpzW1BhscS1WHS8CDgURFJQrWcoF3c13KKbgg1BYQfy8xZWEzTTw1QZbAoKu8FqJnktdu5hcVSHmchiILzzuaDQvjBzV2m8yohCE1jHfPx/xhU+y4G/D75ELlRJsSYAAAAASUVORK5CYII=",this.infoIcon.style.opacity="1"):(this.isShowingInfoIcon=!0,this.onTapOut(),this.infoIcon.src=K,this.infoIcon.style.opacity="0.6")},e}(),en=function(){function e(){var e=this;this.infoDiv=new ee,this.infoIcon=new et(function(){e.infoDiv.show()},function(){e.infoDiv.hide()})}return e.prototype.renderInto=function(e){this.infoDiv.renderInto(e),this.infoIcon.renderInto(e)},e}(),er=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){var u=[s,c];if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},eo=function(){function e(){}return e.hasPermissions=function(){var e,t,n,r;return e=this,t=void 0,n=void 0,r=function(){var e,t,n;return er(this,function(r){switch(r.label){case 0:return[4,navigator.mediaDevices.enumerateDevices()];case 1:for(e=0,t=r.sent();e<t.length;e++)if("videoinput"===(n=t[e]).kind&&n.label)return[2,!0];return[2,!1]}})},new(n||(n=Promise))(function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((r=r.apply(e,t||[])).next())})},e}(),ei=function(){function e(e){this.supportedScanTypes=this.validateAndReturnScanTypes(e)}return e.prototype.getDefaultScanType=function(){return this.supportedScanTypes[0]},e.prototype.hasMoreThanOneScanType=function(){return this.supportedScanTypes.length>1},e.prototype.isCameraScanRequired=function(){for(var t=0,n=this.supportedScanTypes;t<n.length;t++){var r=n[t];if(e.isCameraScanType(r))return!0}return!1},e.isCameraScanType=function(e){return e===i.SCAN_TYPE_CAMERA},e.isFileScanType=function(e){return e===i.SCAN_TYPE_FILE},e.prototype.validateAndReturnScanTypes=function(e){if(!e||0===e.length)return l.DEFAULT_SUPPORTED_SCAN_TYPE;var t=l.DEFAULT_SUPPORTED_SCAN_TYPE.length;if(e.length>t)throw"Max ".concat(t," values expected for ")+"supportedScanTypes";for(var n=0;n<e.length;n++){var r=e[n];if(!l.DEFAULT_SUPPORTED_SCAN_TYPE.includes(r))throw"Unsupported scan type ".concat(r)}return e},e}(),ea=function(){function e(){}return e.ALL_ELEMENT_CLASS="html5-qrcode-element",e.CAMERA_PERMISSION_BUTTON_ID="html5-qrcode-button-camera-permission",e.CAMERA_START_BUTTON_ID="html5-qrcode-button-camera-start",e.CAMERA_STOP_BUTTON_ID="html5-qrcode-button-camera-stop",e.TORCH_BUTTON_ID="html5-qrcode-button-torch",e.CAMERA_SELECTION_SELECT_ID="html5-qrcode-select-camera",e.FILE_SELECTION_BUTTON_ID="html5-qrcode-button-file-selection",e.ZOOM_SLIDER_ID="html5-qrcode-input-range-zoom",e.SCAN_TYPE_CHANGE_ANCHOR_ID="html5-qrcode-anchor-scan-type-change",e.TORCH_BUTTON_CLASS_TORCH_ON="html5-qrcode-button-torch-on",e.TORCH_BUTTON_CLASS_TORCH_OFF="html5-qrcode-button-torch-off",e}(),es=function(){function e(){}return e.createElement=function(e,t){var n=document.createElement(e);return n.id=t,n.classList.add(ea.ALL_ELEMENT_CLASS),"button"===e&&n.setAttribute("type","button"),n},e}(),ec=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((r=r.apply(e,t||[])).next())})},eu=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){var u=[s,c];if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},el=function(){function e(e,t,n){this.isTorchOn=!1,this.torchCapability=e,this.buttonController=t,this.onTorchActionFailureCallback=n}return e.prototype.isTorchEnabled=function(){return this.isTorchOn},e.prototype.flipState=function(){return ec(this,void 0,void 0,function(){var e,t;return eu(this,function(n){switch(n.label){case 0:this.buttonController.disable(),e=!this.isTorchOn,n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.torchCapability.apply(e)];case 2:return n.sent(),this.updateUiBasedOnLatestSettings(this.torchCapability.value(),e),[3,4];case 3:return t=n.sent(),this.propagateFailure(e,t),this.buttonController.enable(),[3,4];case 4:return[2]}})})},e.prototype.updateUiBasedOnLatestSettings=function(e,t){e===t?(this.buttonController.setText(t?m.torchOffButton():m.torchOnButton()),this.isTorchOn=t):this.propagateFailure(t),this.buttonController.enable()},e.prototype.propagateFailure=function(e,t){var n=e?m.torchOnFailedMessage():m.torchOffFailedMessage();t&&(n+="; Error = "+t),this.onTorchActionFailureCallback(n)},e.prototype.reset=function(){this.isTorchOn=!1},e}(),eh=function(){function e(e,t){this.onTorchActionFailureCallback=t,this.torchButton=es.createElement("button",ea.TORCH_BUTTON_ID),this.torchController=new el(e,this,t)}return e.prototype.render=function(e,t){var n=this;this.torchButton.innerText=m.torchOnButton(),this.torchButton.style.display=t.display,this.torchButton.style.marginLeft=t.marginLeft;var r=this;this.torchButton.addEventListener("click",function(e){return ec(n,void 0,void 0,function(){return eu(this,function(e){switch(e.label){case 0:return[4,r.torchController.flipState()];case 1:return e.sent(),r.torchController.isTorchEnabled()?(r.torchButton.classList.remove(ea.TORCH_BUTTON_CLASS_TORCH_OFF),r.torchButton.classList.add(ea.TORCH_BUTTON_CLASS_TORCH_ON)):(r.torchButton.classList.remove(ea.TORCH_BUTTON_CLASS_TORCH_ON),r.torchButton.classList.add(ea.TORCH_BUTTON_CLASS_TORCH_OFF)),[2]}})})}),e.appendChild(this.torchButton)},e.prototype.updateTorchCapability=function(e){this.torchController=new el(e,this,this.onTorchActionFailureCallback)},e.prototype.getTorchButton=function(){return this.torchButton},e.prototype.hide=function(){this.torchButton.style.display="none"},e.prototype.show=function(){this.torchButton.style.display="inline-block"},e.prototype.disable=function(){this.torchButton.disabled=!0},e.prototype.enable=function(){this.torchButton.disabled=!1},e.prototype.setText=function(e){this.torchButton.innerText=e},e.prototype.reset=function(){this.torchButton.innerText=m.torchOnButton(),this.torchController.reset()},e.create=function(t,n,r,o){var i=new e(n,o);return i.render(t,r),i},e}(),ed=function(){function e(e,t,n){this.fileBasedScanRegion=this.createFileBasedScanRegion(),this.fileBasedScanRegion.style.display=t?"block":"none",e.appendChild(this.fileBasedScanRegion);var r=document.createElement("label");r.setAttribute("for",this.getFileScanInputId()),r.style.display="inline-block",this.fileBasedScanRegion.appendChild(r),this.fileSelectionButton=es.createElement("button",ea.FILE_SELECTION_BUTTON_ID),this.setInitialValueToButton(),this.fileSelectionButton.addEventListener("click",function(e){r.click()}),r.append(this.fileSelectionButton),this.fileScanInput=es.createElement("input",this.getFileScanInputId()),this.fileScanInput.type="file",this.fileScanInput.accept="image/*",this.fileScanInput.style.display="none",r.appendChild(this.fileScanInput);var o=this;this.fileScanInput.addEventListener("change",function(e){if(null!=e&&null!=e.target){var t=e.target;if(!t.files||0!==t.files.length){var r=t.files[0],i=r.name;o.setImageNameToButton(i),n(r)}}});var i=this.createDragAndDropMessage();this.fileBasedScanRegion.appendChild(i),this.fileBasedScanRegion.addEventListener("dragenter",function(e){o.fileBasedScanRegion.style.border=o.fileBasedScanRegionActiveBorder(),e.stopPropagation(),e.preventDefault()}),this.fileBasedScanRegion.addEventListener("dragleave",function(e){o.fileBasedScanRegion.style.border=o.fileBasedScanRegionDefaultBorder(),e.stopPropagation(),e.preventDefault()}),this.fileBasedScanRegion.addEventListener("dragover",function(e){o.fileBasedScanRegion.style.border=o.fileBasedScanRegionActiveBorder(),e.stopPropagation(),e.preventDefault()}),this.fileBasedScanRegion.addEventListener("drop",function(e){e.stopPropagation(),e.preventDefault(),o.fileBasedScanRegion.style.border=o.fileBasedScanRegionDefaultBorder();var t=e.dataTransfer;if(t){var r=t.files;if(!r||0===r.length)return;for(var a=!1,s=0;s<r.length;++s){var c=r.item(s);if(c){var u=/image.*/;if(c.type.match(u)){a=!0;var l=c.name;o.setImageNameToButton(l),n(c),i.innerText=m.dragAndDropMessage();break}}}a||(i.innerText=m.dragAndDropMessageOnlyImages())}})}return e.prototype.hide=function(){this.fileBasedScanRegion.style.display="none",this.fileScanInput.disabled=!0},e.prototype.show=function(){this.fileBasedScanRegion.style.display="block",this.fileScanInput.disabled=!1},e.prototype.isShowing=function(){return"block"===this.fileBasedScanRegion.style.display},e.prototype.resetValue=function(){this.fileScanInput.value="",this.setInitialValueToButton()},e.prototype.createFileBasedScanRegion=function(){var e=document.createElement("div");return e.style.textAlign="center",e.style.margin="auto",e.style.width="80%",e.style.maxWidth="600px",e.style.border=this.fileBasedScanRegionDefaultBorder(),e.style.padding="10px",e.style.marginBottom="10px",e},e.prototype.fileBasedScanRegionDefaultBorder=function(){return"6px dashed #ebebeb"},e.prototype.fileBasedScanRegionActiveBorder=function(){return"6px dashed rgb(153 151 151)"},e.prototype.createDragAndDropMessage=function(){var e=document.createElement("div");return e.innerText=m.dragAndDropMessage(),e.style.fontWeight="400",e},e.prototype.setImageNameToButton=function(e){if(e.length>20){var t=e.substring(0,8),n=e.length,r=e.substring(n-8,n);e="".concat(t,"....").concat(r)}var o=m.fileSelectionChooseAnother()+" - "+e;this.fileSelectionButton.innerText=o},e.prototype.setInitialValueToButton=function(){var e=m.fileSelectionChooseImage()+" - "+m.fileSelectionNoImageSelected();this.fileSelectionButton.innerText=e},e.prototype.getFileScanInputId=function(){return"html5-qrcode-private-filescan-input"},e.create=function(t,n,r){return new e(t,n,r)},e}(),ep=function(){function e(e){this.selectElement=es.createElement("select",ea.CAMERA_SELECTION_SELECT_ID),this.cameras=e,this.options=[]}return e.prototype.render=function(e){var t=document.createElement("span");t.style.marginRight="10px";var n=this.cameras.length;if(0===n)throw Error("No cameras found");if(1===n)t.style.display="none";else{var r=m.selectCamera();t.innerText="".concat(r," (").concat(this.cameras.length,")  ")}for(var o=1,i=0,a=this.cameras;i<a.length;i++){var s=a[i],c=s.id,u=null==s.label?c:s.label;u&&""!==u||(u=[m.anonymousCameraPrefix(),o++].join(" "));var l=document.createElement("option");l.value=c,l.innerText=u,this.options.push(l),this.selectElement.appendChild(l)}t.appendChild(this.selectElement),e.appendChild(t)},e.prototype.disable=function(){this.selectElement.disabled=!0},e.prototype.isDisabled=function(){return!0===this.selectElement.disabled},e.prototype.enable=function(){this.selectElement.disabled=!1},e.prototype.getValue=function(){return this.selectElement.value},e.prototype.hasValue=function(e){for(var t=0,n=this.options;t<n.length;t++)if(n[t].value===e)return!0;return!1},e.prototype.setValue=function(e){if(!this.hasValue(e))throw Error("".concat(e," is not present in the camera list."));this.selectElement.value=e},e.prototype.hasSingleItem=function(){return 1===this.cameras.length},e.prototype.numCameras=function(){return this.cameras.length},e.create=function(t,n){var r=new e(n);return r.render(t),r},e}(),ef=function(){function e(){this.onChangeCallback=null,this.zoomElementContainer=document.createElement("div"),this.rangeInput=es.createElement("input",ea.ZOOM_SLIDER_ID),this.rangeInput.type="range",this.rangeText=document.createElement("span"),this.rangeInput.min="1",this.rangeInput.max="5",this.rangeInput.value="1",this.rangeInput.step="0.1"}return e.prototype.render=function(e,t){this.zoomElementContainer.style.display=t?"block":"none",this.zoomElementContainer.style.padding="5px 10px",this.zoomElementContainer.style.textAlign="center",e.appendChild(this.zoomElementContainer),this.rangeInput.style.display="inline-block",this.rangeInput.style.width="50%",this.rangeInput.style.height="5px",this.rangeInput.style.background="#d3d3d3",this.rangeInput.style.outline="none",this.rangeInput.style.opacity="0.7";var n=m.zoom();this.rangeText.innerText="".concat(this.rangeInput.value,"x ").concat(n),this.rangeText.style.marginRight="10px";var r=this;this.rangeInput.addEventListener("input",function(){return r.onValueChange()}),this.rangeInput.addEventListener("change",function(){return r.onValueChange()}),this.zoomElementContainer.appendChild(this.rangeInput),this.zoomElementContainer.appendChild(this.rangeText)},e.prototype.onValueChange=function(){var e=m.zoom();this.rangeText.innerText="".concat(this.rangeInput.value,"x ").concat(e),this.onChangeCallback&&this.onChangeCallback(parseFloat(this.rangeInput.value))},e.prototype.setValues=function(e,t,n,r){this.rangeInput.min=e.toString(),this.rangeInput.max=t.toString(),this.rangeInput.step=r.toString(),this.rangeInput.value=n.toString(),this.onValueChange()},e.prototype.show=function(){this.zoomElementContainer.style.display="block"},e.prototype.hide=function(){this.zoomElementContainer.style.display="none"},e.prototype.setOnCameraZoomValueChangeCallback=function(e){this.onChangeCallback=e},e.prototype.removeOnCameraZoomValueChangeCallback=function(){this.onChangeCallback=null},e.create=function(t,n){var r=new e;return r.render(t,n),r},e}();!function(e){e[e.STATUS_DEFAULT=0]="STATUS_DEFAULT",e[e.STATUS_SUCCESS=1]="STATUS_SUCCESS",e[e.STATUS_WARNING=2]="STATUS_WARNING",e[e.STATUS_REQUESTING_PERMISSION=3]="STATUS_REQUESTING_PERMISSION"}(c||(c={}));var eg=function(){function e(e,t,n){if(this.lastMatchFound=null,this.cameraScanImage=null,this.fileScanImage=null,this.fileSelectionUi=null,this.elementId=e,this.config=this.createConfig(t),this.verbose=!0===n,!document.getElementById(e))throw"HTML Element with id=".concat(e," not found");this.scanTypeSelector=new ei(this.config.supportedScanTypes),this.currentScanType=this.scanTypeSelector.getDefaultScanType(),this.sectionSwapAllowed=!0,this.logger=new f(this.verbose),this.persistedDataManager=new $,!0!==t.rememberLastUsedCamera&&this.persistedDataManager.reset()}return e.prototype.render=function(e,t){var n,r,o=this;this.lastMatchFound=null,this.qrCodeSuccessCallback=function(t,n){if(e)e(t,n);else{if(o.lastMatchFound===t)return;o.lastMatchFound=t,o.setHeaderMessage(m.lastMatch(t),c.STATUS_SUCCESS)}},this.qrCodeErrorCallback=function(e,n){t&&t(e,n)};var i=document.getElementById(this.elementId);if(!i)throw"HTML Element with id=".concat(this.elementId," not found");i.innerHTML="",this.createBasicLayout(i),this.html5Qrcode=new W(this.getScanRegionId(),(n=this.config,r=this.verbose,{formatsToSupport:n.formatsToSupport,useBarCodeDetectorIfSupported:n.useBarCodeDetectorIfSupported,experimentalFeatures:n.experimentalFeatures,verbose:r}))},e.prototype.pause=function(e){(g(e)||!0!==e)&&(e=!1),this.getHtml5QrcodeOrFail().pause(e)},e.prototype.resume=function(){this.getHtml5QrcodeOrFail().resume()},e.prototype.getState=function(){return this.getHtml5QrcodeOrFail().getState()},e.prototype.clear=function(){var e=this,t=function(){var t=document.getElementById(e.elementId);t&&(t.innerHTML="",e.resetBasicLayout(t))};return this.html5Qrcode?new Promise(function(n,r){if(!e.html5Qrcode)return void n();e.html5Qrcode.isScanning?e.html5Qrcode.stop().then(function(r){if(!e.html5Qrcode)return void n();e.html5Qrcode.clear(),t(),n()}).catch(function(t){e.verbose&&e.logger.logError("Unable to stop qrcode scanner",t),r(t)}):(e.html5Qrcode.clear(),t(),n())}):Promise.resolve()},e.prototype.getRunningTrackCapabilities=function(){return this.getHtml5QrcodeOrFail().getRunningTrackCapabilities()},e.prototype.getRunningTrackSettings=function(){return this.getHtml5QrcodeOrFail().getRunningTrackSettings()},e.prototype.applyVideoConstraints=function(e){return this.getHtml5QrcodeOrFail().applyVideoConstraints(e)},e.prototype.getHtml5QrcodeOrFail=function(){if(!this.html5Qrcode)throw"Code scanner not initialized.";return this.html5Qrcode},e.prototype.createConfig=function(e){return e?(e.fps||(e.fps=l.SCAN_DEFAULT_FPS),!l.DEFAULT_REMEMBER_LAST_CAMERA_USED!==e.rememberLastUsedCamera&&(e.rememberLastUsedCamera=l.DEFAULT_REMEMBER_LAST_CAMERA_USED),e.supportedScanTypes||(e.supportedScanTypes=l.DEFAULT_SUPPORTED_SCAN_TYPE),e):{fps:l.SCAN_DEFAULT_FPS,rememberLastUsedCamera:l.DEFAULT_REMEMBER_LAST_CAMERA_USED,supportedScanTypes:l.DEFAULT_SUPPORTED_SCAN_TYPE}},e.prototype.createBasicLayout=function(e){e.style.position="relative",e.style.padding="0px",e.style.border="1px solid silver",this.createHeader(e);var t=document.createElement("div");t.id=this.getScanRegionId(),t.style.width="100%",t.style.minHeight="100px",t.style.textAlign="center",e.appendChild(t),ei.isCameraScanType(this.currentScanType)?this.insertCameraScanImageToScanRegion():this.insertFileScanImageToScanRegion();var n=document.createElement("div");n.id=this.getDashboardId(),n.style.width="100%",e.appendChild(n),this.setupInitialDashboard(n)},e.prototype.resetBasicLayout=function(e){e.style.border="none"},e.prototype.setupInitialDashboard=function(e){this.createSection(e),this.createSectionControlPanel(),this.scanTypeSelector.hasMoreThanOneScanType()&&this.createSectionSwap()},e.prototype.createHeader=function(e){var t=document.createElement("div");t.style.textAlign="left",t.style.margin="0px",e.appendChild(t),new en().renderInto(t);var n=document.createElement("div");n.id=this.getHeaderMessageContainerId(),n.style.display="none",n.style.textAlign="center",n.style.fontSize="14px",n.style.padding="2px 10px",n.style.margin="4px",n.style.borderTop="1px solid #f6f6f6",t.appendChild(n)},e.prototype.createSection=function(e){var t=document.createElement("div");t.id=this.getDashboardSectionId(),t.style.width="100%",t.style.padding="10px 0px 10px 0px",t.style.textAlign="left",e.appendChild(t)},e.prototype.createCameraListUi=function(e,t,n){var r=this;r.showHideScanTypeSwapLink(!1),r.setHeaderMessage(m.cameraPermissionRequesting());var o=function(){n||r.createPermissionButton(e,t)};W.getCameras().then(function(n){r.persistedDataManager.setHasPermission(!0),r.showHideScanTypeSwapLink(!0),r.resetHeaderMessage(),n&&n.length>0?(e.removeChild(t),r.renderCameraSelection(n)):(r.setHeaderMessage(m.noCameraFound(),c.STATUS_WARNING),o())}).catch(function(e){r.persistedDataManager.setHasPermission(!1),n?n.disabled=!1:o(),r.setHeaderMessage(e,c.STATUS_WARNING),r.showHideScanTypeSwapLink(!0)})},e.prototype.createPermissionButton=function(e,t){var n=this,r=es.createElement("button",this.getCameraPermissionButtonId());r.innerText=m.cameraPermissionTitle(),r.addEventListener("click",function(){r.disabled=!0,n.createCameraListUi(e,t,r)}),t.appendChild(r)},e.prototype.createPermissionsUi=function(e,t){var n=this;if(ei.isCameraScanType(this.currentScanType)&&this.persistedDataManager.hasCameraPermissions())return void eo.hasPermissions().then(function(r){r?n.createCameraListUi(e,t):(n.persistedDataManager.setHasPermission(!1),n.createPermissionButton(e,t))}).catch(function(r){n.persistedDataManager.setHasPermission(!1),n.createPermissionButton(e,t)});this.createPermissionButton(e,t)},e.prototype.createSectionControlPanel=function(){var e=document.getElementById(this.getDashboardSectionId()),t=document.createElement("div");e.appendChild(t);var n=document.createElement("div");n.id=this.getDashboardSectionCameraScanRegionId(),n.style.display=ei.isCameraScanType(this.currentScanType)?"block":"none",t.appendChild(n);var r=document.createElement("div");r.style.textAlign="center",n.appendChild(r),this.scanTypeSelector.isCameraScanRequired()&&this.createPermissionsUi(n,r),this.renderFileScanUi(t)},e.prototype.renderFileScanUi=function(e){var t=ei.isFileScanType(this.currentScanType),n=this;this.fileSelectionUi=ed.create(e,t,function(e){if(!n.html5Qrcode)throw"html5Qrcode not defined";ei.isFileScanType(n.currentScanType)&&(n.setHeaderMessage(m.loadingImage()),n.html5Qrcode.scanFileV2(e,!0).then(function(e){n.resetHeaderMessage(),n.qrCodeSuccessCallback(e.decodedText,e)}).catch(function(e){n.setHeaderMessage(e,c.STATUS_WARNING),n.qrCodeErrorCallback(e,p.createFrom(e))}))})},e.prototype.renderCameraSelection=function(e){var t,n=this,r=this,o=document.getElementById(this.getDashboardSectionCameraScanRegionId());o.style.textAlign="center";var i=ef.create(o,!1),a=function(e){var t=e.zoomFeature();if(t.isSupported()){i.setOnCameraZoomValueChangeCallback(function(e){t.apply(e)});var r,o,a,s=1;n.config.defaultZoomValueIfSupported&&(s=n.config.defaultZoomValueIfSupported),r=s,o=t.min(),s=r>(a=t.max())?a:r<o?o:r,i.setValues(t.min(),t.max(),s,t.step()),i.show()}},s=ep.create(o,e),u=document.createElement("span"),l=es.createElement("button",ea.CAMERA_START_BUTTON_ID);l.innerText=m.scanButtonStartScanningText(),u.appendChild(l);var h=es.createElement("button",ea.CAMERA_STOP_BUTTON_ID);h.innerText=m.scanButtonStopScanningText(),h.style.display="none",h.disabled=!0,u.appendChild(h);var d=function(e){if(!e.torchFeature().isSupported()){t&&t.hide();return}t?t.updateTorchCapability(e.torchFeature()):t=eh.create(u,e.torchFeature(),{display:"none",marginLeft:"5px"},function(e){r.setHeaderMessage(e,c.STATUS_WARNING)}),t.show()};o.appendChild(u);var p=function(e){e||(l.style.display="none"),l.innerText=m.scanButtonStartScanningText(),l.style.opacity="1",l.disabled=!1,e&&(l.style.display="inline-block")};if(l.addEventListener("click",function(e){l.innerText=m.scanButtonScanningStarting(),s.disable(),l.disabled=!0,l.style.opacity="0.5",n.scanTypeSelector.hasMoreThanOneScanType()&&r.showHideScanTypeSwapLink(!1),r.resetHeaderMessage();var t,o=s.getValue();r.persistedDataManager.setLastUsedCameraId(o),r.html5Qrcode.start(o,{fps:(t=r.config).fps,qrbox:t.qrbox,aspectRatio:t.aspectRatio,disableFlip:t.disableFlip,videoConstraints:t.videoConstraints},r.qrCodeSuccessCallback,r.qrCodeErrorCallback).then(function(e){h.disabled=!1,h.style.display="inline-block",p(!1);var t=r.html5Qrcode.getRunningTrackCameraCapabilities();!0===n.config.showTorchButtonIfSupported&&d(t),!0===n.config.showZoomSliderIfSupported&&a(t)}).catch(function(e){r.showHideScanTypeSwapLink(!0),s.enable(),p(!0),r.setHeaderMessage(e,c.STATUS_WARNING)})}),s.hasSingleItem()&&l.click(),h.addEventListener("click",function(e){if(!r.html5Qrcode)throw"html5Qrcode not defined";h.disabled=!0,r.html5Qrcode.stop().then(function(e){n.scanTypeSelector.hasMoreThanOneScanType()&&r.showHideScanTypeSwapLink(!0),s.enable(),l.disabled=!1,h.style.display="none",l.style.display="inline-block",t&&(t.reset(),t.hide()),i.removeOnCameraZoomValueChangeCallback(),i.hide(),r.insertCameraScanImageToScanRegion()}).catch(function(e){h.disabled=!1,r.setHeaderMessage(e,c.STATUS_WARNING)})}),r.persistedDataManager.getLastUsedCameraId()){var f=r.persistedDataManager.getLastUsedCameraId();s.hasValue(f)?(s.setValue(f),l.click()):r.persistedDataManager.resetLastUsedCameraId()}},e.prototype.createSectionSwap=function(){var e=this,t=m.textIfCameraScanSelected(),n=m.textIfFileScanSelected(),r=document.getElementById(this.getDashboardSectionId()),o=document.createElement("div");o.style.textAlign="center";var a=es.createElement("span",this.getDashboardSectionSwapLinkId());a.style.textDecoration="underline",a.style.cursor="pointer",a.innerText=ei.isCameraScanType(this.currentScanType)?t:n,a.addEventListener("click",function(){if(!e.sectionSwapAllowed){e.verbose&&e.logger.logError("Section swap called when not allowed");return}e.resetHeaderMessage(),e.fileSelectionUi.resetValue(),e.sectionSwapAllowed=!1,ei.isCameraScanType(e.currentScanType)?(e.clearScanRegion(),e.getCameraScanRegion().style.display="none",e.fileSelectionUi.show(),a.innerText=n,e.currentScanType=i.SCAN_TYPE_FILE,e.insertFileScanImageToScanRegion()):(e.clearScanRegion(),e.getCameraScanRegion().style.display="block",e.fileSelectionUi.hide(),a.innerText=t,e.currentScanType=i.SCAN_TYPE_CAMERA,e.insertCameraScanImageToScanRegion(),e.startCameraScanIfPermissionExistsOnSwap()),e.sectionSwapAllowed=!0}),o.appendChild(a),r.appendChild(o)},e.prototype.startCameraScanIfPermissionExistsOnSwap=function(){var e=this,t=this;if(this.persistedDataManager.hasCameraPermissions())return void eo.hasPermissions().then(function(n){if(n){var r=document.getElementById(t.getCameraPermissionButtonId());if(!r)throw e.logger.logError("Permission button not found, fail;"),"Permission button not found";r.click()}else t.persistedDataManager.setHasPermission(!1)}).catch(function(e){t.persistedDataManager.setHasPermission(!1)})},e.prototype.resetHeaderMessage=function(){document.getElementById(this.getHeaderMessageContainerId()).style.display="none"},e.prototype.setHeaderMessage=function(e,t){t||(t=c.STATUS_DEFAULT);var n=this.getHeaderMessageDiv();switch(n.innerText=e,n.style.display="block",t){case c.STATUS_SUCCESS:n.style.background="rgba(106, 175, 80, 0.26)",n.style.color="#477735";break;case c.STATUS_WARNING:n.style.background="rgba(203, 36, 49, 0.14)",n.style.color="#cb2431";break;case c.STATUS_DEFAULT:default:n.style.background="rgba(0, 0, 0, 0)",n.style.color="rgb(17, 17, 17)"}},e.prototype.showHideScanTypeSwapLink=function(e){this.scanTypeSelector.hasMoreThanOneScanType()&&(!0!==e&&(e=!1),this.sectionSwapAllowed=e,this.getDashboardSectionSwapLink().style.display=e?"inline-block":"none")},e.prototype.insertCameraScanImageToScanRegion=function(){var e=this,t=document.getElementById(this.getScanRegionId());if(this.cameraScanImage){t.innerHTML="<br>",t.appendChild(this.cameraScanImage);return}this.cameraScanImage=new Image,this.cameraScanImage.onload=function(n){t.innerHTML="<br>",t.appendChild(e.cameraScanImage)},this.cameraScanImage.width=64,this.cameraScanImage.style.opacity="0.8",this.cameraScanImage.src=X,this.cameraScanImage.alt=m.cameraScanAltText()},e.prototype.insertFileScanImageToScanRegion=function(){var e=this,t=document.getElementById(this.getScanRegionId());if(this.fileScanImage){t.innerHTML="<br>",t.appendChild(this.fileScanImage);return}this.fileScanImage=new Image,this.fileScanImage.onload=function(n){t.innerHTML="<br>",t.appendChild(e.fileScanImage)},this.fileScanImage.width=64,this.fileScanImage.style.opacity="0.8",this.fileScanImage.src=Z,this.fileScanImage.alt=m.fileScanAltText()},e.prototype.clearScanRegion=function(){document.getElementById(this.getScanRegionId()).innerHTML=""},e.prototype.getDashboardSectionId=function(){return"".concat(this.elementId,"__dashboard_section")},e.prototype.getDashboardSectionCameraScanRegionId=function(){return"".concat(this.elementId,"__dashboard_section_csr")},e.prototype.getDashboardSectionSwapLinkId=function(){return ea.SCAN_TYPE_CHANGE_ANCHOR_ID},e.prototype.getScanRegionId=function(){return"".concat(this.elementId,"__scan_region")},e.prototype.getDashboardId=function(){return"".concat(this.elementId,"__dashboard")},e.prototype.getHeaderMessageContainerId=function(){return"".concat(this.elementId,"__header_message")},e.prototype.getCameraPermissionButtonId=function(){return ea.CAMERA_PERMISSION_BUTTON_ID},e.prototype.getCameraScanRegion=function(){return document.getElementById(this.getDashboardSectionCameraScanRegionId())},e.prototype.getDashboardSectionSwapLink=function(){return document.getElementById(this.getDashboardSectionSwapLinkId())},e.prototype.getHeaderMessageDiv=function(){return document.getElementById(this.getHeaderMessageContainerId())},e}()}}]);