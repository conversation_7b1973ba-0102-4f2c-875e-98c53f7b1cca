(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9097],{5003:(e,r,t)=>{"use strict";t.d(r,{default:()=>h});var s=t(95155),a=t(12115),n=t(35695),i=t(69663),l=t(67133),o=t(97168),c=t(34835),d=t(6187),u=t(39048);let m=(e,r)=>{let t=e||r;if(!t)return"?";let s=t.trim().split(/\s+/);return 1===s.length?s[0].charAt(0).toUpperCase():s[0].charAt(0).toUpperCase()+s[s.length-1].charAt(0).toUpperCase()},f=e=>{let r=e.split("/").filter(Boolean);if(r.includes("customer"))switch(r[r.length-1]){case"customer":return"Feed";case"overview":return"Overview";case"likes":return"My Likes";case"subscriptions":return"Subscriptions";case"reviews":return"My Reviews";case"profile":return"Profile";case"settings":return"Settings";default:return"Dashboard"}if(r.includes("business"))switch(r[r.length-1]){case"business":return"Feed";case"overview":return"Overview";case"analytics":return"Analytics";case"card":return"Manage Card";case"products":return"Products & Services";case"gallery":return"Gallery";case"subscriptions":return"Subscriptions";case"likes":return"Likes";case"reviews":return"Reviews";case"activities":return"Activities";case"settings":return"Settings";case"plan":return"Plan Management";default:return"Business Dashboard"}return"Dashboard"},h=e=>{let{children:r,businessName:t,logoUrl:h,userName:v}=e,p=(0,n.usePathname)(),x=f(p),{scrollDirection:g,isScrolled:b}=(0,u.Y)({threshold:50}),y=b&&"down"===g&&(p.startsWith("/dashboard/")||"/discover"===p||p.startsWith("/post/")),j=p.includes("/choose-role")||p.includes("/onboarding"),w=m(v,t),N="User";v&&t?N="".concat(t," (").concat(v,")"):v?N=v:t&&(N=t);let S=a.Children.toArray(r),k=S[0],A=S[1],P=S[2];return(0,s.jsx)("header",{className:"sticky top-0 z-40 w-full border-b border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80 transition-transform duration-300 ease-in-out ".concat(y?"-translate-y-full":"translate-y-0"),children:(0,s.jsxs)("div",{className:"container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[k,(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:x})}),A]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[j?(0,s.jsx)("form",{action:d.B,children:(0,s.jsxs)(o.$,{type:"submit",variant:"ghost",className:"flex items-center gap-2 h-10 px-3 rounded-lg",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})}):(0,s.jsxs)(l.rI,{children:[(0,s.jsx)(l.ty,{asChild:!0,children:(0,s.jsxs)(o.$,{variant:"ghost",className:"cursor-pointer flex items-center gap-3 h-10 px-3 rounded-lg focus-visible:ring-0 focus-visible:ring-offset-0",children:[(0,s.jsxs)(i.eu,{className:"h-8 w-8 border-2 border-border",children:[h?(0,s.jsx)(i.BK,{src:h,alt:v||t||"User"}):null,(0,s.jsx)(i.q5,{className:"bg-gradient-to-br from-primary/10 to-primary/5 text-primary font-semibold text-sm",children:w})]}),(0,s.jsx)("span",{className:"hidden sm:block text-sm font-medium text-foreground max-w-32 truncate",children:t||v||"User"})]})}),(0,s.jsxs)(l.SQ,{className:"w-64",align:"end",forceMount:!0,children:[" ",(0,s.jsx)(l.lp,{className:"font-normal",children:(0,s.jsxs)("p",{className:"text-sm font-medium leading-none truncate py-2",children:[N," "]})}),(0,s.jsx)(l.mB,{}),(0,s.jsx)("form",{action:d.B,className:"w-full px-2 py-1.5",children:(0,s.jsxs)(o.$,{type:"submit",variant:"ghost",className:"w-full justify-start font-normal text-sm h-auto py-1 cursor-pointer",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})})]})]}),P," "]})]})})}},6187:(e,r,t)=>{"use strict";t.d(r,{B:()=>a});var s=t(34477);let a=(0,s.createServerReference)("00a3593a777ba7988175db3502399fe90e4a6ac663",s.callServer,void 0,s.findSourceMapURL,"signOutUser")},13062:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},22436:(e,r,t)=>{"use strict";var s=t(12115),a="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},n=s.useState,i=s.useEffect,l=s.useLayoutEffect,o=s.useDebugValue;function c(e){var r=e.getSnapshot;e=e.value;try{var t=r();return!a(e,t)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var t=r(),s=n({inst:{value:t,getSnapshot:r}}),a=s[0].inst,d=s[1];return l(function(){a.value=t,a.getSnapshot=r,c(a)&&d({inst:a})},[e,t,r]),i(function(){return c(a)&&d({inst:a}),e(function(){c(a)&&d({inst:a})})},[e]),o(t),t};r.useSyncExternalStore=void 0!==s.useSyncExternalStore?s.useSyncExternalStore:d},27938:(e,r,t)=>{"use strict";t.d(r,{C:()=>s});let s={name:"Dukancard",description:"Create and share digital business cards, showcase products, and connect with customers.",url:"https://dukancard.in",ogImage:"https://dukancard.in/opengraph-image.png",contact:{email:"<EMAIL>",phone:"+91 8458060663",address:{street:"Bisra Road",city:"Rourkela",state:"Odisha",postalCode:"769001",country:"India",full:"Bisra Road, Rourkela, Odisha - 769001"},hours:"Monday - Friday: 9:00 AM - 6:00 PM"},legal:{privacyPolicy:"/privacy",termsOfService:"/terms",refundPolicy:"/refund"},support:{email:"<EMAIL>",phone:"+91 8458060663",helpCenter:"/support"},advertising:{email:"<EMAIL>",phone:"+************",page:"/advertise"}}},34477:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{callServer:function(){return s.callServer},createServerReference:function(){return n},findSourceMapURL:function(){return a.findSourceMapURL}});let s=t(53806),a=t(31818),n=t(34979).createServerReference},34835:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},35717:(e,r,t)=>{Promise.resolve().then(t.bind(t,85578)),Promise.resolve().then(t.bind(t,95875)),Promise.resolve().then(t.bind(t,5003)),Promise.resolve().then(t.bind(t,66674)),Promise.resolve().then(t.bind(t,51362))},47924:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},49033:(e,r,t)=>{"use strict";e.exports=t(22436)},54011:(e,r,t)=>{"use strict";t.d(r,{H4:()=>S,_V:()=>N,bL:()=>w});var s=t(12115),a=t(46081),n=t(39033),i=t(52712),l=t(63540),o=t(49033);function c(){return()=>{}}var d=t(95155),u="Avatar",[m,f]=(0,a.A)(u),[h,v]=m(u),p=s.forwardRef((e,r)=>{let{__scopeAvatar:t,...a}=e,[n,i]=s.useState("idle");return(0,d.jsx)(h,{scope:t,imageLoadingStatus:n,onImageLoadingStatusChange:i,children:(0,d.jsx)(l.sG.span,{...a,ref:r})})});p.displayName=u;var x="AvatarImage",g=s.forwardRef((e,r)=>{let{__scopeAvatar:t,src:a,onLoadingStatusChange:u=()=>{},...m}=e,f=v(x,t),h=function(e,r){let{referrerPolicy:t,crossOrigin:a}=r,n=(0,o.useSyncExternalStore)(c,()=>!0,()=>!1),l=s.useRef(null),d=n?(l.current||(l.current=new window.Image),l.current):null,[u,m]=s.useState(()=>j(d,e));return(0,i.N)(()=>{m(j(d,e))},[d,e]),(0,i.N)(()=>{let e=e=>()=>{m(e)};if(!d)return;let r=e("loaded"),s=e("error");return d.addEventListener("load",r),d.addEventListener("error",s),t&&(d.referrerPolicy=t),"string"==typeof a&&(d.crossOrigin=a),()=>{d.removeEventListener("load",r),d.removeEventListener("error",s)}},[d,a,t]),u}(a,m),p=(0,n.c)(e=>{u(e),f.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==h&&p(h)},[h,p]),"loaded"===h?(0,d.jsx)(l.sG.img,{...m,ref:r,src:a}):null});g.displayName=x;var b="AvatarFallback",y=s.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:a,...n}=e,i=v(b,t),[o,c]=s.useState(void 0===a);return s.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>c(!0),a);return()=>window.clearTimeout(e)}},[a]),o&&"loaded"!==i.imageLoadingStatus?(0,d.jsx)(l.sG.span,{...n,ref:r}):null});function j(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}y.displayName=b;var w=p,N=g,S=y},69663:(e,r,t)=>{"use strict";t.d(r,{BK:()=>l,eu:()=>i,q5:()=>o});var s=t(95155);t(12115);var a=t(54011),n=t(53999);function i(e){let{className:r,...t}=e;return(0,s.jsx)(a.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",r),...t})}function l(e){let{className:r,...t}=e;return(0,s.jsx)(a._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",r),...t})}},85578:(e,r,t)=>{"use strict";t.d(r,{default:()=>m});var s=t(95155),a=t(12115),n=t(6874),i=t.n(n),l=t(13062),o=t(28695),c=t(27938),d=t(35695);let u={writingMode:"vertical-rl",textOrientation:"mixed",transform:"rotate(180deg)",letterSpacing:"0.05em",fontSize:"0.8rem",fontWeight:600};function m(){let[e,r]=(0,a.useState)(!1),[t,n]=(0,a.useState)(!1),m=(0,d.usePathname)();(0,a.useEffect)(()=>{n(!0)},[]);let f=null==m?void 0:m.includes("/dashboard"),h="/advertise"===m;return!t||f||h?null:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"fixed right-0 top-1/2 -translate-y-1/2 z-40 hidden sm:block",children:(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(o.P.div,{className:"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-l-lg blur-md",animate:{opacity:e?.8:.5},transition:{duration:.3}}),(0,s.jsx)(i(),{href:c.C.advertising.page,children:(0,s.jsx)(o.P.div,{className:"relative",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1),whileHover:{scale:1.03},whileTap:{scale:.98},children:(0,s.jsxs)("div",{className:"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 px-2 py-6 rounded-l-lg font-medium text-xs relative overflow-hidden shadow-lg flex flex-col items-center justify-center w-10 h-32",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center gap-2",children:[(0,s.jsx)(l.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{style:u,children:"Advertise"})]}),(0,s.jsx)(o.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear",repeatDelay:1}})]})})})]})}),(0,s.jsx)("div",{className:"fixed left-4 bottom-20 z-40 sm:hidden",children:(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(o.P.div,{className:"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full blur-md",animate:{opacity:.6},transition:{duration:.3}}),(0,s.jsx)(i(),{href:c.C.advertising.page,children:(0,s.jsx)(o.P.div,{className:"relative",whileTap:{scale:.95},children:(0,s.jsxs)("div",{className:"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 p-2 rounded-full font-medium text-xs relative overflow-hidden shadow-md flex items-center justify-center w-10 h-10",children:[(0,s.jsx)(l.A,{className:"w-4 h-4"}),(0,s.jsx)(o.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear",repeatDelay:1}})]})})})]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[5756,5604,4277,8695,6874,2290,6671,375,5152,7665,1884,67,6215,7711,365,6743,8441,1684,7358],()=>r(35717)),_N_E=e.O()}]);