(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4396],{381:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>s,t:()=>l});var n=r(12115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function s(...e){return n.useCallback(l(...e),e)}},17580:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...m}=e;return(0,n.createElement)("svg",{ref:t,...s,width:i,height:i,stroke:r,strokeWidth:o?24*Number(a)/Number(i):a,className:l("lucide",d),...m},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:o,...d}=r;return(0,n.createElement)(a,{ref:s,iconNode:t,className:l("lucide-".concat(i(e)),o),...d})});return r.displayName="".concat(e),r}},38564:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},51976:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{M0:()=>c,Yq:()=>u,cn:()=>l,gV:()=>s,gY:()=>d,kY:()=>a,vA:()=>o,vv:()=>m});var n=r(52596),i=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}function s(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function a(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),r=e.substring(e.length-2),n="*".repeat(e.length-4);return"".concat(t).concat(n).concat(r)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),r=t[0],n=t[1];if(r.length<=2||n.length<=2||!n.includes("."))return"Email Hidden";let i=r.substring(0,2)+"*".repeat(r.length-2),l=n.split("."),s=l[0],a=l.slice(1).join("."),o=s.substring(0,2)+"*".repeat(s.length-2);return"".concat(i,"@").concat(o,".").concat(a)}function d(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),r=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let n=r.length-1;n>=0;n--)if(t>=r[n].value)return(e/r[n].value).toFixed(1).replace(/\.0$/,"")+r[n].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let r={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(r.hour="2-digit",r.minute="2-digit",r.hour12=!0),e.toLocaleString("en-IN",r)}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(r){return"".concat(t," ").concat(e.toFixed(2))}}},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var n=r(52596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,s=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:a}=t,o=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let l=i(t)||i(n);return s[e][l]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...d}[t]):({...a,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},76241:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var n=r(95155),i=r(6874),l=r.n(i),s=r(97168),a=r(71007),o=r(381),d=r(28695),c=r(81497),u=r(51976),m=r(17580),v=r(38564);function f(e){let{title:t,value:r,icon:i,description:a,color:o,href:d}=e,c=(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"flex flex-col items-center text-center space-y-4",children:[(0,n.jsx)("div",{className:"p-3 rounded-xl bg-muted",children:(0,n.jsx)(i,{className:"w-6 h-6 ".concat({blue:"text-blue-600 dark:text-blue-400",indigo:"text-indigo-600 dark:text-indigo-400",purple:"text-purple-600 dark:text-purple-400",rose:"text-rose-600 dark:text-rose-400",red:"text-red-600 dark:text-red-400",yellow:"text-yellow-600 dark:text-yellow-400",brand:"text-amber-600 dark:text-amber-400"}[o])})}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-foreground",children:r}),(0,n.jsx)("div",{className:"text-sm font-medium text-muted-foreground",children:t})]}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:a}),d&&(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsx)(s.$,{asChild:!0,variant:"outline",size:"sm",className:"w-full text-xs font-medium",children:(0,n.jsxs)(l(),{href:d,children:["View ",t]})})})]})});return(0,n.jsx)("div",{className:"rounded-xl p-6 bg-card border border-border",children:c})}function h(e){let{initialReviewCount:t,initialSubscriptionCount:r,initialLikesCount:i,userId:l}=e;return(0,n.jsxs)(d.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4",children:(0,n.jsx)(f,{title:"Activity Score",value:t+2*r+i,icon:c.A,description:"Your engagement level",color:"brand"})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,n.jsx)(f,{title:"Likes",value:i,icon:u.A,description:"Businesses you've liked",color:"red",href:"/dashboard/customer/likes"}),(0,n.jsx)(f,{title:"Followers",value:r,icon:m.A,description:"Businesses you're following",color:"blue",href:"/dashboard/customer/subscriptions"}),(0,n.jsx)(f,{title:"Rating",value:t,icon:v.A,description:"Reviews you've left for businesses",color:"yellow",href:"/dashboard/customer/reviews"})]})]})}function g(e){let{customerName:t,userId:r,initialReviewCount:i,initialSubscriptionCount:d,initialLikesCount:c}=e;return(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6",children:[(0,n.jsx)("div",{className:"p-3 rounded-xl bg-muted hidden sm:block",children:(0,n.jsx)(a.A,{className:"w-6 h-6 text-foreground"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("h1",{className:"text-2xl font-bold text-foreground",children:["Welcome, ",t]}),(0,n.jsx)("p",{className:"text-muted-foreground mt-1",children:"Manage your subscriptions and interactions"})]}),(0,n.jsx)(s.$,{asChild:!0,variant:"outline",size:"sm",children:(0,n.jsxs)(l(),{href:"/dashboard/customer/profile",className:"flex items-center",children:[(0,n.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Edit Profile"]})})]}),(0,n.jsx)(h,{initialReviewCount:i,initialSubscriptionCount:d,initialLikesCount:c,userId:r})]})}},80170:(e,t,r)=>{Promise.resolve().then(r.bind(r,76241))},81497:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>a});var n=r(95155);r(12115);var i=r(99708),l=r(74466),s=r(53999);let a=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:l,asChild:o=!1,...d}=e,c=o?i.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,s.cn)(a({variant:r,size:l,className:t})),...d})}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>s});var n=r(12115),i=r(6101),l=r(95155),s=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var s;let e,a,o=(s=r,(a=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(a=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),d=function(e,t){let r={...t};for(let n in t){let i=e[n],l=t[n];/^on[A-Z]/.test(n)?i&&l?r[n]=(...e)=>{let t=l(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...l}:"className"===n&&(r[n]=[i,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,i.t)(t,o):o),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...s}=e,a=n.Children.toArray(i),d=a.find(o);if(d){let e=d.props.children,i=a.map(t=>t!==d?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...s,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,l.jsx)(t,{...s,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}("Slot"),a=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,8441,1684,7358],()=>t(80170)),_N_E=e.O()}]);