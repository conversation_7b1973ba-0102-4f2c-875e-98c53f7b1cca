exports.id=3739,exports.ids=[3739],exports.modules={32032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{createClient:()=>a});var r=s(34386);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let a=null,n=null;try{let{headers:e,cookies:t}=await s.e(4999).then(s.bind(s,44999));a=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,a=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,a){let n=()=>{var n,i,l,u,o;return n=e,i=t,l=s,u=r,o=a,"customer_profiles"===n?{data:l&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:l&&"business"===i?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===i?{id:"test-subscription-id",plan_id:o,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,s)=>i(e),neq:(t,s)=>i(e),gt:(t,s)=>i(e),gte:(t,s)=>i(e),lt:(t,s)=>i(e),lte:(t,s)=>i(e),like:(t,s)=>i(e),ilike:(t,s)=>i(e),is:(t,s)=>i(e),in:(t,s)=>i(e),contains:(t,s)=>i(e),containedBy:(t,s)=>i(e),rangeGt:(t,s)=>i(e),rangeGte:(t,s)=>i(e),rangeLt:(t,s)=>i(e),rangeLte:(t,s)=>i(e),rangeAdjacent:(t,s)=>i(e),overlaps:(t,s)=>i(e),textSearch:(t,s)=>i(e),match:t=>i(e),not:(t,s,r)=>i(e),or:t=>i(e),filter:(t,s,r)=>i(e),order:(t,s)=>i(e),limit:(t,s)=>i(e),range:(t,s,r)=>i(e),abortSignal:t=>i(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,s,r,a,n)}}(a):n?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await n.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},56399:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=56399,e.exports=t},70373:(e,t,s)=>{"use strict";s.d(t,{BK:()=>l,eu:()=>i,q5:()=>u});var r=s(60687);s(43210);var a=s(11096),n=s(96241);function i({className:e,...t}){return(0,r.jsx)(a.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function l({className:e,...t}){return(0,r.jsx)(a._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",e),...t})}function u({className:e,...t}){return(0,r.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},76881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{createClient:()=>a});var r=s(63014);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let a=null,n=null;try{let{headers:e,cookies:t}=await Promise.all([s.e(4208),s.e(4659)]).then(s.bind(s,74208));a=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,a=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,a){let n=()=>{var n,i,l,u,o;return n=e,i=t,l=s,u=r,o=a,"customer_profiles"===n?{data:l&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:l&&"business"===i?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===i?{id:"test-subscription-id",plan_id:o,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,s)=>i(e),neq:(t,s)=>i(e),gt:(t,s)=>i(e),gte:(t,s)=>i(e),lt:(t,s)=>i(e),lte:(t,s)=>i(e),like:(t,s)=>i(e),ilike:(t,s)=>i(e),is:(t,s)=>i(e),in:(t,s)=>i(e),contains:(t,s)=>i(e),containedBy:(t,s)=>i(e),rangeGt:(t,s)=>i(e),rangeGte:(t,s)=>i(e),rangeLt:(t,s)=>i(e),rangeLte:(t,s)=>i(e),rangeAdjacent:(t,s)=>i(e),overlaps:(t,s)=>i(e),textSearch:(t,s)=>i(e),match:t=>i(e),not:(t,s,r)=>i(e),or:t=>i(e),filter:(t,s,r)=>i(e),order:(t,s)=>i(e),limit:(t,s)=>i(e),range:(t,s,r)=>i(e),abortSignal:t=>i(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,s,r,a,n)}}(a):n?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await n.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}}};