(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196,6701],{4229:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},6654:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=t(12115);function i(e,a){let t=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=t.current;e&&(t.current=null,e());let a=i.current;a&&(i.current=null,a())}else e&&(t.current=n(e,r)),a&&(i.current=n(a,r))},[e,a])}function n(e,a){if("function"!=typeof e)return e.current=a,()=>{e.current=null};{let t=e(a);return"function"==typeof t?t:()=>e(null)}}("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},16559:(e,a,t)=>{"use strict";t.d(a,{$v:()=>p,EO:()=>c,Lt:()=>s,Rx:()=>h,Zr:()=>x,ck:()=>g,r7:()=>m,wd:()=>u});var r=t(95155);t(12115);var i=t(62278),n=t(53999),l=t(97168);function s(e){let{...a}=e;return(0,r.jsx)(i.bL,{"data-slot":"alert-dialog",...a})}function o(e){let{...a}=e;return(0,r.jsx)(i.ZL,{"data-slot":"alert-dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,r.jsx)(i.hJ,{"data-slot":"alert-dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function c(e){let{className:a,...t}=e;return(0,r.jsxs)(o,{children:[(0,r.jsx)(d,{}),(0,r.jsx)(i.UC,{"data-slot":"alert-dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...t})]})}function u(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function g(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function m(e){let{className:a,...t}=e;return(0,r.jsx)(i.hE,{"data-slot":"alert-dialog-title",className:(0,n.cn)("text-lg font-semibold",a),...t})}function p(e){let{className:a,...t}=e;return(0,r.jsx)(i.VY,{"data-slot":"alert-dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",a),...t})}function h(e){let{className:a,...t}=e;return(0,r.jsx)(i.rc,{className:(0,n.cn)((0,l.r)(),a),...t})}function x(e){let{className:a,...t}=e;return(0,r.jsx)(i.ZD,{className:(0,n.cn)((0,l.r)({variant:"outline"}),a),...t})}},29869:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},40133:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},53999:(e,a,t)=>{"use strict";t.d(a,{M0:()=>c,Yq:()=>u,cn:()=>n,gV:()=>l,gY:()=>d,kY:()=>s,vA:()=>o,vv:()=>g});var r=t(52596),i=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,i.QP)((0,r.$)(a))}function l(e){if(!e)return null;let a=e.trim();return(a.startsWith("+91")?a=a.substring(3):12===a.length&&a.startsWith("91")&&(a=a.substring(2)),/^\d{10}$/.test(a))?a:null}function s(e){if(!e||e.length<4)return"Invalid Phone";let a=e.substring(0,2),t=e.substring(e.length-2),r="*".repeat(e.length-4);return"".concat(a).concat(r).concat(t)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let a=e.split("@"),t=a[0],r=a[1];if(t.length<=2||r.length<=2||!r.includes("."))return"Email Hidden";let i=t.substring(0,2)+"*".repeat(t.length-2),n=r.split("."),l=n[0],s=n.slice(1).join("."),o=l.substring(0,2)+"*".repeat(l.length-2);return"".concat(i,"@").concat(o,".").concat(s)}function d(e){if(null==e||isNaN(e))return"0";let a=Math.abs(e),t=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(a<1e5)return a>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let r=t.length-1;r>=0;r--)if(a>=t[r].value)return(e/t[r].value).toFixed(1).replace(/\.0$/,"")+t[r].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let t={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return a&&(t.hour="2-digit",t.minute="2-digit",t.hour12=!0),e.toLocaleString("en-IN",t)}function g(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:a,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(t){return"".concat(a," ").concat(e.toFixed(2))}}},57918:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]])},62278:(e,a,t)=>{"use strict";t.d(a,{rc:()=>O,ZD:()=>F,UC:()=>U,VY:()=>L,hJ:()=>P,ZL:()=>z,bL:()=>R,hE:()=>M});var r=t(12115),i=t(46081),n=t(6101),l=t(45821),s=t(85185),o=t(95155),d=Symbol("radix.slottable"),c="AlertDialog",[u,g]=(0,i.A)(c,[l.Hs]),m=(0,l.Hs)(),p=e=>{let{__scopeAlertDialog:a,...t}=e,r=m(a);return(0,o.jsx)(l.bL,{...r,...t,modal:!0})};p.displayName=c,r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,o.jsx)(l.l9,{...i,...r,ref:a})}).displayName="AlertDialogTrigger";var h=e=>{let{__scopeAlertDialog:a,...t}=e,r=m(a);return(0,o.jsx)(l.ZL,{...r,...t})};h.displayName="AlertDialogPortal";var x=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,o.jsx)(l.hJ,{...i,...r,ref:a})});x.displayName="AlertDialogOverlay";var f="AlertDialogContent",[v,b]=u(f),y=function(e){let a=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return a.displayName=`${e}.Slottable`,a.__radixId=d,a}("AlertDialogContent"),j=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,children:i,...d}=e,c=m(t),u=r.useRef(null),g=(0,n.s)(a,u),p=r.useRef(null);return(0,o.jsx)(l.G$,{contentName:f,titleName:w,docsSlug:"alert-dialog",children:(0,o.jsx)(v,{scope:t,cancelRef:p,children:(0,o.jsxs)(l.UC,{role:"alertdialog",...c,...d,ref:g,onOpenAutoFocus:(0,s.m)(d.onOpenAutoFocus,e=>{var a;e.preventDefault(),null==(a=p.current)||a.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,o.jsx)(y,{children:i}),(0,o.jsx)(S,{contentRef:u})]})})})});j.displayName=f;var w="AlertDialogTitle",N=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,o.jsx)(l.hE,{...i,...r,ref:a})});N.displayName=w;var k="AlertDialogDescription",D=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,o.jsx)(l.VY,{...i,...r,ref:a})});D.displayName=k;var I=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,o.jsx)(l.bm,{...i,...r,ref:a})});I.displayName="AlertDialogAction";var A="AlertDialogCancel",C=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,{cancelRef:i}=b(A,t),s=m(t),d=(0,n.s)(a,i);return(0,o.jsx)(l.bm,{...s,...r,ref:d})});C.displayName=A;var S=e=>{let{contentRef:a}=e,t="`".concat(f,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(f,"` by passing a `").concat(k,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(f,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=a.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(t)},[t,a]),null},R=p,z=h,P=x,U=j,O=I,F=C,M=N,L=D},74613:(e,a,t)=>{Promise.resolve().then(t.bind(t,83311))},81284:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},83311:(e,a,t)=>{"use strict";t.d(a,{default:()=>K});var r=t(95155),i=t(28695),n=t(56671),l=t(34477);let s=(0,l.createServerReference)("40b4abcdf4b56fee20ded485f2c566ea38014b1780",l.callServer,void 0,l.findSourceMapURL,"uploadGalleryImage"),o=(0,l.createServerReference)("40bed46f79802386d699491b1d6d0c5e77a52d30e9",l.callServer,void 0,l.findSourceMapURL,"deleteGalleryImage"),d=e=>e.size>0xf00000?(n.oR.error("File too large",{description:"Please select an image under 15MB"}),!1):!!["image/jpeg","image/png","image/webp","image/gif"].includes(e.type)||(n.oR.error("Invalid file type",{description:"Please select a JPG, PNG, WebP, or GIF image"}),!1),c=e=>URL.createObjectURL(e),u=e=>{switch(e){case"free":default:return 1;case"basic":return 3;case"growth":return 10;case"pro":return 50;case"enterprise":return 100}};var g=t(12115);let m=e=>{let[a,t]=(0,g.useState)(e),[r,i]=(0,g.useState)(0),[n,l]=(0,g.useState)(!1),[s,o]=(0,g.useState)({isUploading:!1,selectedFile:null,previewUrl:null,uploadDialogOpen:!1}),[d,c]=(0,g.useState)({isDeleting:!1,selectedImage:null,deleteDialogOpen:!1}),[u,m]=(0,g.useState)({lightboxImage:null});return(0,g.useEffect)(()=>{l(!0)},[]),(0,g.useEffect)(()=>{s.uploadDialogOpen||o(e=>({...e,selectedFile:null,previewUrl:null}))},[s.uploadDialogOpen]),{images:a,refreshKey:r,isClient:n,uploadState:s,deleteState:d,lightboxState:u,updateImages:e=>{t(e),i(e=>e+1)},updateUploadState:e=>{o(a=>({...a,...e}))},updateDeleteState:e=>{c(a=>({...a,...e}))},updateLightboxState:e=>{m(a=>({...a,...e}))}}},p=e=>{let{canAddMore:a,userPlan:t,galleryLimit:r,updateUploadState:i}=e,[l,s]=(0,g.useState)(!1);return{isDragging:l,handleDragEnter:e=>{e.preventDefault(),e.stopPropagation(),s(!0)},handleDragLeave:e=>{e.preventDefault(),e.stopPropagation(),e.currentTarget.contains(e.relatedTarget)||s(!1)},handleDragOver:e=>{e.preventDefault(),e.stopPropagation(),l||s(!0)},handleDrop:e=>{if(e.preventDefault(),e.stopPropagation(),s(!1),!a)return void n.oR.error("Gallery limit reached",{description:"You've reached your ".concat(t," plan limit of ").concat(r," photos. Please upgrade to add more.")});let l=e.dataTransfer.files;if(l.length>0){let e=l[0];d(e)&&i({selectedFile:e,previewUrl:c(e),uploadDialogOpen:!0})}},handleFileSelect:e=>{d(e)&&i({selectedFile:e,previewUrl:c(e)})}}};var h=t(50402);let x=(0,l.createServerReference)("404a320a1c65860fcbb5305f902486a31a65356ee8",l.callServer,void 0,l.findSourceMapURL,"updateGalleryOrder"),f=e=>{let{images:a,updateImages:t}=e,[r,i]=(0,g.useState)({orderedImages:a,hasUnsavedChanges:!1,isSavingOrder:!1,isReordering:!1}),[l,s]=(0,g.useState)(null);(0,g.useEffect)(()=>{i(e=>({...e,orderedImages:a,hasUnsavedChanges:!1}))},[a]);let o=async()=>{i(e=>({...e,isSavingOrder:!0}));try{let{success:e,error:a}=await x(r.orderedImages);e?(t(r.orderedImages),i(e=>({...e,hasUnsavedChanges:!1})),n.oR.success("Gallery order updated",{description:"Your gallery images have been reordered successfully"})):n.oR.error("Failed to save order",{description:a||"Failed to update gallery order"})}catch(e){console.error("Error saving gallery order:",e),n.oR.error("Failed to save order",{description:"An unexpected error occurred"})}finally{i(e=>({...e,isSavingOrder:!1}))}};return{reorderState:r,activeId:l,handleDragStart:e=>{s(e.active.id),i(e=>({...e,isReordering:!0}))},handleDragEnd:e=>{let{active:a,over:t}=e;if(s(null),i(e=>({...e,isReordering:!1})),a.id!==(null==t?void 0:t.id)&&t){let e=r.orderedImages.findIndex(e=>e.id===a.id),n=r.orderedImages.findIndex(e=>e.id===t.id);if(-1!==e&&-1!==n){let a=(0,h.be)(r.orderedImages,e,n);i(e=>({...e,orderedImages:a,hasUnsavedChanges:!0}))}}},handleSaveOrder:o,handleResetOrder:()=>{i(e=>({...e,orderedImages:a,hasUnsavedChanges:!1}))}}};var v=t(90196),b=t(84355),y=t(97168);function j(e){let{canAddMore:a,isUploading:t,onUploadClick:n}=e;return(0,r.jsxs)(i.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,r.jsx)(b.A,{className:"w-5 h-5 text-primary"})}),(0,r.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,r.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Media Management"})]}),(0,r.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Gallery Management"}),(0,r.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Showcase your business with beautiful photos and manage your visual content with drag-and-drop organization."})]}),(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsxs)(y.$,{onClick:n,disabled:!a||t,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"}),t?"Uploading...":"Add Photo"]})})]})}var w=t(81284),N=t(75143),k=t(66766),D=t(53999),I=t(78266),A=t(48021),C=t(57918),S=t(62525);function R(e){let{image:a,onViewImage:t,onDeleteImage:i}=e,{attributes:n,listeners:l,setNodeRef:s,transform:o,transition:d,isDragging:c}=(0,h.gl)({id:a.id,transition:{duration:150,easing:"cubic-bezier(0.25, 1, 0.5, 1)"}}),u={transform:I.Ks.Transform.toString(o),transition:d,opacity:c?.3:1,zIndex:c?1e3:"auto"};return(0,r.jsx)("div",{ref:s,style:u,className:"relative group",children:(0,r.jsxs)("div",{className:"aspect-square relative overflow-hidden rounded-xl transition-all duration-300",children:[(0,r.jsx)(k.default,{src:a.url,alt:"Gallery image",fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,r.jsx)("div",{...n,...l,className:"absolute top-2 left-2 p-2 bg-black/70 rounded-md cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-30 hover:bg-black/80",style:{touchAction:"none"},children:(0,r.jsx)(A.A,{className:"h-4 w-4 text-white"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 pointer-events-none",children:(0,r.jsxs)("div",{className:"absolute bottom-3 right-3 flex space-x-2 pointer-events-auto",children:[(0,r.jsx)(y.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/20 backdrop-blur-sm text-white shadow-md hover:bg-white/30",onClick:e=>{e.stopPropagation(),t(a.url)},children:(0,r.jsx)(C.A,{className:"h-4 w-4"})}),(0,r.jsx)(y.$,{variant:"destructive",size:"icon",className:"h-8 w-8 bg-red-500/80 hover:bg-red-600/90 text-white shadow-md",onClick:e=>{e.stopPropagation(),i(a)},children:(0,r.jsx)(S.A,{className:"h-4 w-4"})})]})})]})})}function z(e){let{image:a,onViewImage:t,onDeleteImage:i}=e;return(0,r.jsx)("div",{className:"relative group",children:(0,r.jsxs)("div",{className:"aspect-square relative overflow-hidden rounded-xl transition-all duration-300",children:[(0,r.jsx)(k.default,{src:a.url,alt:"Gallery image",fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 pointer-events-none",children:(0,r.jsxs)("div",{className:"absolute bottom-3 right-3 flex space-x-2 pointer-events-auto",children:[(0,r.jsx)(y.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/20 backdrop-blur-sm text-white shadow-md hover:bg-white/30",onClick:e=>{e.stopPropagation(),t(a.url)},children:(0,r.jsx)(C.A,{className:"h-4 w-4"})}),(0,r.jsx)(y.$,{variant:"destructive",size:"icon",className:"h-8 w-8 bg-red-500/80 hover:bg-red-600/90 text-white shadow-md",onClick:e=>{e.stopPropagation(),i(a)},children:(0,r.jsx)(S.A,{className:"h-4 w-4"})})]})})]})})}var P=t(40133),U=t(51154),O=t(4229);function F(e){let{isReordering:a,hasUnsavedChanges:t,isSavingOrder:n,onSaveOrder:l,onResetOrder:s}=e;return(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:(0,D.cn)("flex items-center gap-2 text-sm rounded-lg p-3 transition-all duration-200",a?"bg-amber-500/20 text-amber-700 dark:text-amber-300 border border-amber-500/30":"bg-muted/30 text-muted-foreground"),children:[(0,r.jsx)(A.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:a?"Reordering images... Drop to place in new position.":'Drag the grip handle (⋮⋮) on images to reorder them. Changes will be saved when you click "Save Order".'})]}),t&&(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"flex items-center justify-center gap-3 p-3 bg-amber-500/10 border border-amber-500/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-amber-700 dark:text-amber-300 font-medium",children:[(0,r.jsx)(w.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"You have unsaved changes"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(y.$,{variant:"outline",size:"sm",onClick:s,disabled:n,className:"text-xs border-amber-500/30 hover:bg-amber-500/10",children:[(0,r.jsx)(P.A,{className:"mr-1 h-3 w-3"}),"Reset"]}),(0,r.jsx)(y.$,{onClick:l,disabled:n,className:"bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-xs",size:"sm",children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(U.A,{className:"mr-1 h-3 w-3 animate-spin"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(O.A,{className:"mr-1 h-3 w-3"}),"Save Order"]})})]})]})]})}var M=t(27213),L=t(29869);function E(e){let{canAddMore:a,isDragging:t,userPlan:n,galleryLimit:l,onUploadClick:s}=e;return(0,r.jsxs)(i.P.div,{className:(0,D.cn)("text-center py-20 border-2 border-dashed rounded-2xl bg-neutral-50/50 dark:bg-neutral-900/50 transition-all duration-300",a&&"hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50 cursor-pointer border-neutral-300 dark:border-neutral-700",!a&&"border-neutral-200 dark:border-neutral-800",t&&a&&"border-primary bg-primary/10 dark:bg-primary/5"),initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.3},onClick:a?s:void 0,children:[(0,r.jsx)("div",{className:(0,D.cn)("w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 flex items-center justify-center mb-6 transition-all duration-300",t&&a&&"bg-gradient-to-br from-primary/20 to-primary/10 scale-110 border-primary/40"),children:(0,r.jsx)(M.A,{className:(0,D.cn)("h-8 w-8 text-primary transition-all duration-300",t&&a&&"scale-110")})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:a?"Start Your Gallery":"Gallery Limit Reached"}),(0,r.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 max-w-md mx-auto leading-relaxed",children:a?(0,r.jsxs)(r.Fragment,{children:["Showcase your business with beautiful photos. Upload images to create an engaging visual experience for your customers.",(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-primary font-medium mt-2 block",children:t?"Drop your images here":"Click to upload or drag and drop"})]}):(0,r.jsxs)(r.Fragment,{children:["You've reached your plan's gallery limit of ",l," photos.",(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-primary font-medium mt-2 block",children:"Upgrade your plan to add more photos."})]})}),a&&(0,r.jsxs)(y.$,{onClick:e=>{e.stopPropagation(),s()},disabled:!a,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:[(0,r.jsx)(L.A,{className:"mr-2 h-4 w-4"}),"Upload Your First Photo"]})]})]})}function G(e){let{images:a,imagesCount:t,galleryLimit:n,canAddMore:l,isDragging:s,userPlan:o,isClient:d,reorderState:c,activeId:u,onDragStart:g,onDragEnd:m,onSaveOrder:p,onResetOrder:x,onViewImage:f,onDeleteImage:v,onUploadClick:b}=e,y=(0,N.FR)((0,N.MS)(N.AN,{activationConstraint:{distance:8}}),(0,N.MS)(N.uN,{coordinateGetter:h.JR}));return(0,r.jsxs)(i.P.div,{className:"space-y-8",initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Gallery Collection"}),(0,r.jsx)("div",{className:"h-px flex-1 bg-gradient-to-r from-neutral-200 to-transparent dark:from-neutral-700"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-500 dark:text-neutral-400",children:[(0,r.jsx)("span",{className:"font-medium text-neutral-900 dark:text-neutral-100",children:t}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{children:n===1/0?"∞":n}),(0,r.jsx)(w.A,{className:"h-4 w-4 opacity-60"})]})]}),(0,r.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed",children:0===t?"Start building your gallery by uploading your first image.":"Manage your ".concat(t," image").concat(1===t?"":"s"," with drag-and-drop reordering.")})]}),(0,r.jsx)("div",{className:"relative",children:0===c.orderedImages.length?(0,r.jsx)(E,{canAddMore:l,isDragging:s,userPlan:o,galleryLimit:n,onUploadClick:b}):(0,r.jsxs)("div",{className:"space-y-4",children:[c.orderedImages.length>1&&d&&(0,r.jsx)(F,{isReordering:c.isReordering,hasUnsavedChanges:c.hasUnsavedChanges,isSavingOrder:c.isSavingOrder,onSaveOrder:p,onResetOrder:x}),d?(0,r.jsxs)(N.Mp,{sensors:y,collisionDetection:N.fp,onDragStart:g,onDragEnd:m,children:[(0,r.jsx)(h.gB,{items:c.orderedImages.map(e=>e.id),strategy:h.kL,children:(0,r.jsx)("div",{className:(0,D.cn)("grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 transition-all duration-200",c.isReordering&&"bg-primary/5 rounded-xl p-4"),children:c.orderedImages.map(e=>(0,r.jsx)(R,{image:e,onViewImage:f,onDeleteImage:v},e.id))})}),(0,r.jsx)(N.Hd,{children:u?(0,r.jsx)("div",{className:"aspect-square relative overflow-hidden rounded-lg border shadow-lg opacity-90 transform rotate-3 scale-105",children:(()=>{let e=c.orderedImages.find(e=>e.id===u);return e?(0,r.jsx)(k.default,{src:e.url,alt:"Dragging image",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}):null})()}):null})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6",children:c.orderedImages.map(e=>(0,r.jsx)(z,{image:e,onViewImage:f,onDeleteImage:v},e.id))})]})})]})}var _=t(99840);function $(e){let{uploadState:a,isDragging:t,imagesCount:i,galleryLimit:n,canAddMore:l,onOpenChange:s,onFileChange:o,onDragEnter:d,onDragOver:c,onDragLeave:u,onDrop:g,onUpload:m,onClearPreview:p}=e;return(0,r.jsx)(_.lG,{open:a.uploadDialogOpen,onOpenChange:s,children:(0,r.jsxs)(_.Cf,{className:"sm:max-w-md",children:[(0,r.jsxs)(_.c7,{children:[(0,r.jsx)(_.L3,{children:"Upload Gallery Image"}),(0,r.jsx)(_.rr,{children:"Add a new photo to showcase your business"})]}),(0,r.jsx)("div",{className:"space-y-4 py-4",children:a.previewUrl?(0,r.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-lg border shadow-md",children:[(0,r.jsx)(k.default,{src:a.previewUrl,alt:"Preview",fill:!0,className:"object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300",children:(0,r.jsx)(y.$,{variant:"outline",size:"sm",className:"absolute top-2 right-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/20",onClick:p,children:"Change"})})]}):(0,r.jsxs)("div",{className:(0,D.cn)("flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-12 transition-all duration-200",l?t?"border-primary bg-primary/10":"border-neutral-200 dark:border-neutral-700 bg-muted/30 hover:bg-muted/50":"border-neutral-200 dark:border-neutral-700 bg-neutral-100/50 dark:bg-neutral-800/50 opacity-60"),onDragEnter:d,onDragOver:c,onDragLeave:u,onDrop:g,children:[(0,r.jsx)("div",{className:(0,D.cn)("w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-200",l?t?"bg-primary/20 scale-110":"bg-primary/10":"bg-neutral-200/50 dark:bg-neutral-700/50"),children:(0,r.jsx)(L.A,{className:(0,D.cn)("h-8 w-8 transition-all duration-200",l?t?"text-primary opacity-100":"text-primary opacity-80":"text-neutral-400 dark:text-neutral-600")})}),(0,r.jsxs)("label",{htmlFor:"gallery-image",className:(0,D.cn)("text-center",l?"cursor-pointer":"cursor-not-allowed"),children:[(0,r.jsx)("span",{className:(0,D.cn)("text-sm font-medium transition-all duration-200",l?"text-primary":"text-neutral-500 dark:text-neutral-400"),children:l?t?"Drop image here":"Click to upload":"Gallery limit reached"}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground block mt-1",children:l?!t&&"or drag and drop":"Remove images to upload new ones"}),l&&(0,r.jsx)("span",{className:"text-xs text-muted-foreground block mt-1",children:"JPG, PNG, WebP, or GIF (max. 15MB)"}),(0,r.jsx)("input",{id:"gallery-image",type:"file",accept:"image/jpeg,image/png,image/webp,image/gif",className:"hidden",onChange:o,disabled:!l})]})]})}),(0,r.jsxs)(_.Es,{children:[(0,r.jsx)(y.$,{variant:"outline",onClick:()=>s(!1),disabled:a.isUploading,className:"border-neutral-200 hover:bg-neutral-100 dark:border-neutral-800 dark:hover:bg-neutral-800",children:"Cancel"}),(0,r.jsx)(y.$,{onClick:m,disabled:!a.selectedFile||a.isUploading||!l,className:"bg-primary hover:bg-primary/90 text-primary-foreground font-medium",children:a.isUploading?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(U.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uploading..."]}):l?"Upload":"Gallery Limit Reached"})]})]})})}var Y=t(16559);function q(e){let{deleteState:a,onOpenChange:t,onDelete:i}=e;return(0,r.jsx)(Y.Lt,{open:a.deleteDialogOpen,onOpenChange:t,children:(0,r.jsxs)(Y.EO,{className:"border-neutral-200 dark:border-neutral-800 shadow-lg",children:[(0,r.jsxs)(Y.wd,{children:[(0,r.jsx)(Y.r7,{children:"Delete Gallery Image"}),(0,r.jsx)(Y.$v,{children:"Are you sure you want to delete this image? This action cannot be undone."})]}),(0,r.jsxs)(Y.ck,{children:[(0,r.jsx)(Y.Zr,{disabled:a.isDeleting,className:"border-neutral-200 hover:bg-neutral-100 dark:border-neutral-800 dark:hover:bg-neutral-800",children:"Cancel"}),(0,r.jsx)(Y.Rx,{onClick:e=>{e.preventDefault(),i()},disabled:a.isDeleting,className:"bg-red-500 hover:bg-red-600 text-white",children:a.isDeleting?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(U.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete"})]})]})})}var B=t(60760),V=t(54416);function H(e){let{lightboxState:a,onClose:t}=e;return(0,r.jsx)(B.N,{children:a.lightboxImage&&(0,r.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4",onClick:t,children:[(0,r.jsx)(i.P.button,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.2},className:"absolute top-4 right-4 text-white bg-black/50 rounded-full p-2 hover:bg-black/70 transition-colors duration-200 z-50",onClick:e=>{e.stopPropagation(),t()},children:(0,r.jsx)(V.A,{className:"h-6 w-6"})}),(0,r.jsx)(i.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.3},className:"relative max-w-4xl max-h-[80vh] w-full h-full",onClick:e=>e.stopPropagation(),children:(0,r.jsx)(k.default,{src:a.lightboxImage,alt:"Gallery image",fill:!0,className:"object-contain",sizes:"100vw"})})]})})}function K(e){let{initialImages:a,userPlan:t,businessName:l}=e,d=u(t),{images:c,isClient:g,uploadState:h,deleteState:x,lightboxState:b,updateImages:y,updateUploadState:w,updateDeleteState:N,updateLightboxState:k}=m(a),D=c.length<d,{isDragging:I,handleDragEnter:A,handleDragLeave:C,handleDragOver:S,handleDrop:R,handleFileSelect:z}=p({canAddMore:D,userPlan:t,galleryLimit:d,updateUploadState:w}),{reorderState:P,activeId:U,handleDragStart:O,handleDragEnd:F,handleSaveOrder:M,handleResetOrder:L}=f({images:c,updateImages:y}),E=async()=>{if(h.selectedFile){w({isUploading:!0});try{let e=await (0,v.compressImageUltraAggressiveClient)(h.selectedFile,{maxDimension:1200,targetSizeKB:100}),a=new File([e.blob],h.selectedFile.name,{type:e.blob.type}),t=new FormData;t.append("image",a);let{success:r,image:i,error:l}=await s(t);if(r&&i){let e=[...c,i];y(e),n.oR.success("Image uploaded",{description:"Your gallery image has been uploaded successfully"}),w({uploadDialogOpen:!1})}else(null==l?void 0:l.includes("File size must be less than 15MB"))?n.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}):(null==l?void 0:l.includes("Invalid file type"))?n.oR.error("Invalid file type",{description:"Please select a JPG, PNG, WebP, or GIF image"}):(null==l?void 0:l.includes("reached the limit"))?n.oR.error("Gallery limit reached",{description:l}):n.oR.error("Upload failed",{description:l||"Failed to upload image"})}catch(e){console.error("Error uploading image:",e),n.oR.error("Upload failed",{description:"An unexpected error occurred"})}finally{w({isUploading:!1})}}},_=async()=>{if(x.selectedImage){N({isDeleting:!0});try{let e=await o(x.selectedImage.id),{success:a,error:t}=e;if(a){let a=c.filter(e=>e.id!==x.selectedImage.id);y(a),e.warning?n.oR.success("Image deleted",{description:e.warning}):n.oR.success("Image deleted",{description:"The gallery image has been deleted successfully"}),N({deleteDialogOpen:!1,selectedImage:null})}else n.oR.error("Deletion failed",{description:t||"Failed to delete image"})}catch(e){console.error("Error deleting image:",e),n.oR.error("Deletion failed",{description:"An unexpected error occurred"})}finally{N({isDeleting:!1})}}},Y=()=>{w({uploadDialogOpen:!0})};return(0,r.jsxs)(i.P.div,{className:"space-y-10",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[(0,r.jsx)(j,{canAddMore:D,isUploading:h.isUploading,onUploadClick:Y}),(0,r.jsx)(G,{images:c,imagesCount:c.length,galleryLimit:d,canAddMore:D,isDragging:I,userPlan:t,isClient:g,reorderState:P,activeId:U,onDragStart:O,onDragEnd:F,onSaveOrder:M,onResetOrder:L,onViewImage:e=>{k({lightboxImage:e})},onDeleteImage:e=>{N({selectedImage:e,deleteDialogOpen:!0})},onUploadClick:Y}),(0,r.jsx)($,{uploadState:h,isDragging:I,imagesCount:c.length,galleryLimit:d,canAddMore:D,onOpenChange:e=>{w({uploadDialogOpen:e})},onFileChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];t&&z(t)},onDragEnter:A,onDragOver:S,onDragLeave:C,onDrop:e=>{R(e)},onUpload:E,onClearPreview:()=>{w({selectedFile:null,previewUrl:null})}}),(0,r.jsx)(q,{deleteState:x,onOpenChange:e=>{N({deleteDialogOpen:e})},onDelete:_}),(0,r.jsx)(H,{lightboxState:b,onClose:()=>{k({lightboxImage:null})}})]})}},84355:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},90196:(e,a,t)=>{"use strict";async function r(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{format:t="webp",targetSizeKB:r=100,maxDimension:i=800,quality:n=.8}=a;return new Promise((a,l)=>{let s=new Image;s.onload=()=>{try{let o=document.createElement("canvas"),d=o.getContext("2d");if(!d)return void l(Error("Could not get canvas context"));let{width:c,height:u}=s;(c>i||u>i)&&(c>u?(u=u*i/c,c=i):(c=c*i/u,u=i)),o.width=c,o.height=u,d.drawImage(s,0,0,c,u);let g=n,m=0,p=()=>{o.toBlob(t=>{if(!t)return void l(Error("Failed to create blob"));let i=t.size/1024;if(i<=r||m>=5||g<=.1){let r=e.size/t.size;a({blob:t,finalSizeKB:Math.round(100*i)/100,compressionRatio:Math.round(100*r)/100,dimensions:{width:c,height:u}})}else m++,g=Math.max(.1,g-.15),p()},"image/".concat(t),g)};p()}catch(e){l(e)}},s.onerror=()=>l(Error("Failed to load image")),s.src=URL.createObjectURL(e)})}async function i(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.size/1048576,i=100,n=800,l=.7;return t<=2?(l=.7,n=800,i=90):t<=5?(l=.55,n=700,i=80):t<=10?(l=.45,n=600,i=70):(l=.35,n=550,i=60),r(e,{...a,targetSizeKB:a.targetSizeKB||i,maxDimension:a.maxDimension||n,quality:a.quality||l})}async function n(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return r(e,{targetSizeKB:50,maxDimension:400,quality:.7,...a})}t.d(a,{compressImageUltraAggressiveClient:()=>i,q:()=>n})},97168:(e,a,t)=>{"use strict";t.d(a,{$:()=>o,r:()=>s});var r=t(95155);t(12115);var i=t(99708),n=t(74466),l=t(53999);let s=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:t,size:n,asChild:o=!1,...d}=e,c=o?i.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,l.cn)(s({variant:t,size:n,className:a})),...d})}},99840:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>g,Es:()=>p,HM:()=>c,L3:()=>h,c7:()=>m,lG:()=>s,rr:()=>x,zM:()=>o});var r=t(95155);t(12115);var i=t(45821),n=t(54416),l=t(53999);function s(e){let{...a}=e;return(0,r.jsx)(i.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,r.jsx)(i.l9,{"data-slot":"dialog-trigger",...a})}function d(e){let{...a}=e;return(0,r.jsx)(i.ZL,{"data-slot":"dialog-portal",...a})}function c(e){let{...a}=e;return(0,r.jsx)(i.bm,{"data-slot":"dialog-close",...a})}function u(e){let{className:a,...t}=e;return(0,r.jsx)(i.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function g(e){let{className:a,children:t,hideClose:s=!1,...o}=e;return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(i.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[t,!s&&(0,r.jsxs)(i.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function p(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function h(e){let{className:a,...t}=e;return(0,r.jsx)(i.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",a),...t})}function x(e){let{className:a,...t}=e;return(0,r.jsx)(i.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...t})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8695,2290,6671,375,1884,6766,62,8441,1684,7358],()=>a(74613)),_N_E=e.O()}]);