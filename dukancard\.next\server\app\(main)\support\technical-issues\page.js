(()=>{var e={};e.id=1434,e.ids=[1434],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9729:(e,t,r)=>{Promise.resolve().then(r.bind(r,16066))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},11997:e=>{"use strict";e.exports=require("punycode")},16066:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\support\\\\technical-issues\\\\TechnicalIssuesClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx","default")},19080:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22881:(e,t,r)=>{Promise.resolve().then(r.bind(r,88883))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},74075:e=>{"use strict";e.exports=require("zlib")},77711:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),i=r(48088),o=r(88170),a=r.n(o),n=r(30893),u={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>n[e]);r.d(t,u);let c={children:["",{children:["(main)",{children:["support",{children:["technical-issues",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96401)),"C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19559)),"C:\\web-app\\dukancard\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(main)/support/technical-issues/page",pathname:"/support/technical-issues",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},78122:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88883:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(60687);r(43210);var i=r(64021),o=r(11437),a=r(78122),n=r(85778),u=r(19080),c=r(41312),d=r(93613),l=r(63539);let p=[{id:1,question:"I can't log in to my account. What should I do?",answer:"First, ensure you're using the correct email address and password. Check that you're connected to the internet and try clearing your browser cache. If problems persist, contact our support team."},{id:2,question:"My images aren't uploading properly. How can I fix this?",answer:"Ensure your images meet our requirements: JPG, PNG, or WebP format, and under 15MB in size. For logos, use square images (1:1 ratio) for best results. If you're still having issues, try using a different browser or device, or compress the image before uploading."},{id:3,question:"My digital card isn't displaying correctly on mobile devices. What's wrong?",answer:"Dukancard is designed to be fully responsive across all devices. Try refreshing the page or clearing your browser cache. If the issue persists, check if you're using an outdated browser version. For specific display issues, take a screenshot and contact our support team."},{id:4,question:"I'm seeing error messages when trying to save my card information. What should I do?",answer:"Error messages typically indicate specific issues that need to be addressed. Read the error message carefully for guidance. Common issues include required fields left empty or invalid format for phone numbers or URLs. If you're still stuck, contact our support team with details of the error message."},{id:5,question:"The QR code for my card isn't working when scanned. How can I fix this?",answer:"Ensure you've downloaded the QR code at a high resolution and it's not distorted when printed. When scanning, make sure there's adequate lighting and the camera is focused on the QR code. If the issue persists, try regenerating the QR code from Business Management > Manage Card in your dashboard."},{id:6,question:"My changes to my digital card aren't showing up on the public view. Why?",answer:"Changes should appear immediately, but sometimes there might be a slight delay due to caching. Try refreshing the page using Ctrl+F5 (Windows) or Cmd+Shift+R (Mac). If changes still don't appear after a few minutes, log out and back in, then check again."},{id:7,question:"I'm having trouble with the verification email. What should I do?",answer:"Check your spam or junk folder for the verification email. If you don't see it, you can request a new verification email from the login page. Ensure you're checking the correct email account and that your email address was entered correctly during registration."},{id:8,question:"The website is loading slowly for me. How can I improve this?",answer:"Slow loading can be caused by various factors. Try clearing your browser cache, closing unnecessary tabs or applications, and ensuring you have a stable internet connection. If the issue persists across different devices and networks, please report it to our support team."},{id:9,question:"I accidentally deleted a product. Can I recover it?",answer:"Currently, there's no self-service option to recover deleted products. We recommend creating a new product entry with the same information in the Products & Services section. In the future, always double-check before confirming deletion. For urgent recovery needs, contact our support team as soon as possible."},{id:10,question:"My analytics data isn't updating. What's happening?",answer:"Analytics data typically updates within a few minutes but may take longer during high traffic periods. If you notice no updates after 24 hours, try clearing your browser cache and refreshing the page. If the issue persists, contact our support team with details of the problem."}],h=[{id:"account-login",title:"Account & Login Issues",icon:(0,s.jsx)(i.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Login Problems",steps:["Double-check that you're using the correct email address you registered with.","Ensure caps lock is off and you're entering the correct password.","Try clearing your browser cache and cookies, then attempt to log in again.","If your account is locked after multiple failed attempts, wait 30 minutes before trying again."],image:"/support/login-issues.jpg",imageAlt:"Login troubleshooting",tip:"Check your spam folder if you don't see the password reset email within a few minutes."},{title:"Registration & Verification Issues",steps:["Check your spam or junk folder for the verification email.","If you don't see it after 15 minutes, return to the login page and click 'Resend Verification Email'.","Ensure you entered your email address correctly during registration.","If verification links don't work, try copying and pasting the link directly into your browser address bar.","For social login issues, ensure you're allowing pop-ups from our site and that you're logged into your social account."],image:"/support/registration-issues.jpg",imageAlt:"Registration troubleshooting",tip:"Verification links expire after 24 hours. Request a new verification email if your link has expired."}]},{id:"content-display",title:"Content & Display Issues",icon:(0,s.jsx)(o.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Image Upload Problems",steps:["Ensure your images are in JPG, PNG, or WebP format and under 15MB in size.","Check your internet connection stability before uploading.","For logos, use square images (1:1 ratio) for best results.","For product images, ensure they have good resolution (at least 800x800 pixels recommended).","If upload seems stuck, refresh the page and try again with a smaller or compressed version."],image:"/support/image-upload-issues.jpg",imageAlt:"Image upload troubleshooting",tip:"If images are consistently disappearing, contact support with details of the issue."},{title:"Card Display Issues",steps:["For mobile display issues, try viewing in portrait mode and refreshing the page.","Clear your browser cache using Ctrl+F5 (Windows) or Cmd+Shift+R (Mac).","Ensure your mobile browser is up to date.","Avoid using special characters or excessive formatting in text fields.","For QR code issues, ensure you've downloaded it at high resolution and it's not distorted when printed."],image:"/support/display-issues.jpg",imageAlt:"Display troubleshooting",tip:"Changes to your card should appear immediately, but may take a few minutes due to caching. Log out and back in if changes don't appear."}]},{id:"performance-errors",title:"Performance & Error Messages",icon:(0,s.jsx)(a.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Website Performance Issues",steps:["Clear your browser cache and cookies to improve loading times.","Close unnecessary tabs or applications that might be using system resources.","Ensure you have a stable internet connection.","Try using a different browser or device to see if the issue persists.","Check your browser settings to ensure JavaScript is enabled."],image:"/support/performance-issues.jpg",imageAlt:"Performance troubleshooting",tip:"Disable any ad blockers or browser extensions that might interfere with the site's functionality."},{title:"Error Messages & Codes",steps:["For 404 errors, check the URL for typos and report broken links to our support team.","For 500 errors, try refreshing the page or coming back later.","Read form validation error messages carefully as they indicate what needs to be fixed.","For payment errors, check that your card details are correct and not expired.","Contact our support team if errors persist for more than an hour."],image:"/support/error-messages.jpg",imageAlt:"Error message troubleshooting",tip:"Common form issues include required fields left empty, invalid email formats, or password requirements not met."}]}];function m(){let e=[{title:"Business Card Setup",description:"Learn how to create and customize your digital business card",icon:(0,s.jsx)(n.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/business-card-setup"},{title:"Product Management",description:"Learn how to add and manage products in your digital storefront",icon:(0,s.jsx)(u.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/product-management"},{title:"Account & Billing",description:"Manage your account, subscription, and billing information",icon:(0,s.jsx)(c.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/account-billing"}];return(0,s.jsx)(l.A,{title:"Technical Issues & Troubleshooting",description:"Find solutions to common technical problems and learn how to troubleshoot issues with your digital business card.",icon:(0,s.jsx)(d.A,{className:"w-12 h-12 text-[var(--brand-gold)]"}),quickHelp:[{title:"Common Login Issues:",items:[{text:"Clear browser cache and cookies"},{text:"Check spam folder for verification emails"}]},{title:"Display & Image Issues:",items:[{text:"Use Ctrl+F5 (Windows) or Cmd+Shift+R (Mac) to refresh"},{text:"Ensure images are under 15MB in JPG, PNG, or WebP format"},{text:"Try using a different browser if issues persist"}]}],guideSections:h,faqs:p,relatedResources:e,navigationButtons:[{label:"Account & Login",href:"#account-login"},{label:"Content & Display",href:"#content-display"},{label:"Performance & Errors",href:"#performance-errors"},{label:"FAQs",href:"#faqs"}]})}},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96401:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,generateMetadata:()=>a});var s=r(37413),i=r(16066),o=r(54670);async function a(){let e="Technical Issues",t="Get help with technical problems and troubleshooting for your Dukancard digital business card. Find solutions to common issues with login, card display, image uploads, and more.",r="http://localhost:3000",s=`${r}/support/technical-issues`,i=`${r}/opengraph-image.png`;return{title:e,description:t,keywords:["Dukancard troubleshooting","technical support","login issues","image upload problems","card not displaying","error messages"],alternates:{canonical:"/support/technical-issues"},openGraph:{title:e,description:t,url:s,siteName:o.C.name,type:"website",locale:"en_IN",images:[{url:i,width:1200,height:630,alt:`${o.C.name} Technical Support`}]},twitter:{card:"summary_large_image",title:e,description:t,images:[i]},other:{"application-ld+json":JSON.stringify({"@context":"https://schema.org","@type":"WebPage",name:e,description:t,url:s,isPartOf:{"@type":"WebSite",name:o.C.name,url:r}})}}}function n(){return(0,s.jsx)(i.default,{})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,6724,2997,1107,7065,9389,3037,6177,1374],()=>r(77711));module.exports=s})();