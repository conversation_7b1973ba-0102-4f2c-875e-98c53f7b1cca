(()=>{var e={};e.id=7068,e.ids=[7068],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8849:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\support\\\\analytics\\\\AnalyticsClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\AnalyticsClient.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19080:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(62688).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(62688).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},27910:e=>{"use strict";e.exports=require("stream")},28039:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o,generateMetadata:()=>s});var a=i(37413),r=i(8849),n=i(54670);async function s(){let e="Analytics & Insights",t="Learn how to track and analyze visitor engagement with your Dukancard digital business card. Understand your analytics dashboard, visitor metrics, and how to use data to improve your digital presence.",i="http://localhost:3000",a=`${i}/support/analytics`,r=`${i}/opengraph-image.png`;return{title:e,description:t,keywords:["Dukancard analytics","business card metrics","visitor tracking","engagement analytics","digital card statistics","customer insights"],alternates:{canonical:"/support/analytics"},openGraph:{title:e,description:t,url:a,siteName:n.C.name,type:"website",locale:"en_IN",images:[{url:r,width:1200,height:630,alt:`${n.C.name} Analytics & Insights Guide`}]},twitter:{card:"summary_large_image",title:e,description:t,images:[r]},other:{"application-ld+json":JSON.stringify({"@context":"https://schema.org","@type":"WebPage",name:e,description:t,url:a,isPartOf:{"@type":"WebSite",name:n.C.name,url:i}})}}}function o(){return(0,a.jsx)(r.default,{})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34835:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var a=i(65239),r=i(48088),n=i(88170),s=i.n(n),o=i(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);i.d(t,c);let d={children:["",{children:["(main)",{children:["support",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,28039)),"C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,19559)),"C:\\web-app\\dukancard\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(i.bind(i,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(i.bind(i,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\page.tsx"],u={require:i,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(main)/support/analytics/page",pathname:"/support/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},42856:(e,t,i)=>{Promise.resolve().then(i.bind(i,67356))},53411:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(62688).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67356:(e,t,i)=>{"use strict";i.d(t,{default:()=>p});var a=i(60687);i(43210);var r=i(53411);let n=(0,i(62688).A)("MousePointer",[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]]);var s=i(25541),o=i(85778),c=i(19080),d=i(63539);let l=[{id:1,question:"What analytics are available in Dukancard?",answer:"Dukancard offers analytics including total card views, contact button clicks (WhatsApp/Call), and product view counts. The Free plan includes basic analytics, while the Basic, Growth, Pro, and Enterprise plans offer increasingly advanced analytics features."},{id:2,question:"Where can I find my analytics dashboard?",answer:"You can access your analytics dashboard by logging into your Dukancard account and clicking on 'Analytics & Insights' in the sidebar menu. The dashboard provides an overview of your card's performance."},{id:3,question:"How often are analytics updated?",answer:"Analytics are updated in near real-time. When someone views your card or clicks a contact button, the data is typically reflected in your dashboard within a few minutes."},{id:4,question:"Can I see who viewed my digital card?",answer:"For privacy reasons, Dukancard doesn't show the specific identities of visitors. However, you can see the total number of views and interactions with your card and products."},{id:5,question:"How do I track product performance?",answer:"In the Products & Services section of your dashboard, you can see view counts for each product. This helps you understand which products are generating the most interest from visitors."},{id:6,question:"What is the difference between card views and product views?",answer:"Card views count the number of times your digital card has been loaded. Product views count how many times visitors have viewed the details of specific products in your catalog."},{id:7,question:"Can I export my analytics data?",answer:"Data export functionality is available in the Growth, Pro, and Enterprise plans. This feature allows you to download your analytics data for further analysis or reporting. The Free and Basic plans do not include data export capabilities."},{id:8,question:"How can I use analytics to improve my digital card?",answer:"Monitor which products receive the most views and feature them prominently. If you notice high card views but low contact button clicks, consider improving your call-to-action or offering special promotions to encourage engagement."},{id:9,question:"Will I get notified about significant changes in my analytics?",answer:"Currently, automatic notifications for analytics changes are not available. We recommend checking your dashboard regularly to monitor performance. Email notifications for significant metrics may be added in future updates."},{id:10,question:"What advanced analytics are available in premium plans?",answer:"Premium plans (Basic, Growth, Pro, and Enterprise) include features like visitor demographics, geographic location data, time-based analytics (hourly/daily/weekly trends), referral source tracking, and detailed engagement metrics for each section of your card. Higher-tier plans offer more comprehensive analytics capabilities."}],u=[{id:"understanding-dashboard",title:"Understanding Your Analytics Dashboard",icon:(0,a.jsx)(r.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Accessing Your Analytics",steps:["Log in to your Dukancard account and navigate to your dashboard.","Click on 'Analytics & Insights' in the sidebar menu to access your analytics dashboard.","The main dashboard provides an overview of key metrics at a glance.","You can see total card views, contact button clicks, and product engagement.","The dashboard shows both current metrics and trends over time."],image:"/support/analytics-dashboard.jpg",imageAlt:"Analytics dashboard overview",tip:"Check your analytics regularly, ideally once a week, to track performance trends and make timely adjustments to your digital card strategy."},{title:"Key Metrics Explained",steps:["Card Views: The total number of times your digital card has been loaded by visitors.","Contact Clicks: How many times visitors have clicked your WhatsApp or Call buttons.","Product Views: The number of times visitors have viewed your products.","Engagement Rate: The percentage of visitors who interact with your card (clicking contact buttons or viewing products).","Conversion Rate: The percentage of visitors who click on contact buttons to reach out to you."],image:"/support/key-metrics.jpg",imageAlt:"Key analytics metrics",tip:"Focus on conversion rates rather than just total views. A lower number of views with a higher conversion rate is often more valuable than many views with few conversions."},{title:"Reading Analytics Graphs",steps:["The timeline graph shows your card's performance over time (daily/weekly).","Blue bars or lines typically represent card views.","Green elements usually indicate positive interactions like contact clicks.","Hover over data points to see specific numbers for that time period.","Look for patterns in the data, such as days or times with higher engagement."],image:"/support/analytics-graphs.jpg",imageAlt:"Reading analytics graphs",tip:"Pay attention to spikes or drops in your analytics. These often correlate with your marketing activities or external events that might affect your business."}]},{id:"tracking-engagement",title:"Tracking Visitor Engagement",icon:(0,a.jsx)(n,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Monitoring Contact Button Clicks",steps:["In your analytics dashboard, find the 'Contact Engagement' section.","View the breakdown of WhatsApp button clicks versus Call button clicks.","Compare the ratio of views to contact clicks to assess your call-to-action effectiveness.","A healthy conversion rate is typically 5-10% of total views resulting in contact clicks.","If your conversion rate is low, consider making your contact buttons more prominent or offering incentives."],image:"/support/contact-clicks.jpg",imageAlt:"Contact button click analytics",tip:"If one contact method (WhatsApp or Call) significantly outperforms the other, consider featuring the preferred method more prominently on your card."},{title:"Analyzing Product Performance",steps:["Navigate to the 'Products & Services' section of your dashboard.","View the list of your products with their respective view counts.","Identify your most popular products based on visitor interest.","Compare product views to overall card views to understand visitor browsing patterns.","Use this data to decide which products to feature more prominently or which might need better descriptions or images."],image:"/support/product-analytics.jpg",imageAlt:"Product performance analytics",tip:"If certain products receive significantly more views than others, consider creating variations or related offerings to capitalize on visitor interest in that category."},{title:"Understanding Visitor Behavior",steps:["Review the 'Visitor Flow' section to see how users navigate through your card.","Note the average time spent on your card, which indicates visitor engagement level.","Check if visitors are scrolling through your entire product list or only viewing the top few items.","Identify any drop-off points where visitors tend to leave your card.","Use these insights to optimize the structure and content of your digital card."],image:"/support/visitor-behavior.jpg",imageAlt:"Visitor behavior analytics",tip:"If visitors aren't scrolling through your entire product list, consider reorganizing to put your most important items at the top or adding compelling content that encourages scrolling."}]},{id:"using-insights",title:"Using Analytics for Business Growth",icon:(0,a.jsx)(s.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Making Data-Driven Decisions",steps:["Regularly review your analytics to identify trends and patterns in visitor behavior.","Compare performance before and after making changes to your card or products.","Use product view data to inform inventory decisions and marketing focus.","Identify peak times or days when your card receives the most views.","Align your availability or promotional activities with these high-traffic periods."],image:"/support/data-decisions.jpg",imageAlt:"Making data-driven decisions",tip:"Create a simple monthly review process where you analyze your analytics and make at least one improvement to your digital card based on the data."},{title:"Optimizing Your Digital Card",steps:["Use analytics to identify which elements of your card are performing well and which need improvement.","If product views are high but contact clicks are low, improve your call-to-action or offer special deals.","If certain products receive few views, improve their descriptions, images, or position in your list.","Test different approaches to your bio, product descriptions, or pricing display.","Monitor analytics after each change to measure its impact on visitor engagement."],image:"/support/card-optimization.jpg",imageAlt:"Optimizing your digital card",tip:"Make one change at a time and monitor its impact before making additional changes. This helps you clearly identify which modifications are effective."},{title:"Planning Marketing Activities",steps:["Use analytics to measure the effectiveness of your card-sharing efforts.","Track spikes in views to correlate with your marketing activities or events.","Identify which channels drive the most traffic to your digital card.","Plan targeted promotions based on your most viewed or clicked products.","Set specific goals for increasing key metrics and develop strategies to achieve them."],image:"/support/marketing-planning.jpg",imageAlt:"Planning marketing activities",tip:"When running promotions or sharing your card through different channels, use different messaging or timing to help identify which source is driving traffic when you see spikes in your analytics."}]}];function p(){let e=[{title:"Business Card Setup",description:"Learn how to create and customize your digital business card",icon:(0,a.jsx)(o.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/business-card-setup"},{title:"Product Management",description:"Learn how to add and manage products in your digital storefront",icon:(0,a.jsx)(c.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/product-management"}];return(0,a.jsx)(d.A,{title:"Analytics & Insights",description:"Learn how to track and analyze visitor engagement with your digital business card to improve your online presence.",icon:(0,a.jsx)(r.A,{className:"w-12 h-12 text-[var(--brand-gold)]"}),quickHelp:[{title:"Analytics Basics:",items:[{text:"Access analytics in Analytics & Insights section"},{text:"Check card views and contact button clicks"},{text:"View product performance in Products & Services"}]},{title:"Using Analytics Effectively:",items:[{text:"Check analytics weekly to track performance"},{text:"Focus on conversion rates, not just total views"},{text:"Use data to optimize your product listings"}]}],guideSections:u,faqs:l,relatedResources:e,navigationButtons:[{label:"Dashboard Overview",href:"#understanding-dashboard"},{label:"Tracking Engagement",href:"#tracking-engagement"},{label:"Using Insights",href:"#using-insights"},{label:"FAQs",href:"#faqs"}]})}},74075:e=>{"use strict";e.exports=require("zlib")},79304:(e,t,i)=>{Promise.resolve().then(i.bind(i,8849))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[4447,6724,2997,1107,7065,9389,3037,6177,1374],()=>i(34835));module.exports=a})();