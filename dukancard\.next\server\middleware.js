(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{3:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>n,fetch:()=>i});var r=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==s.g)return s.g;throw Error("unable to locate global object")}();let i=r.fetch,n=r.fetch.bind(r),a=r.Headers,o=r.Request,l=r.Response},35:(e,t)=>{"use strict";var s=Array.isArray,r=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),n=(Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy")),a=Symbol.iterator;Object.prototype.hasOwnProperty,Object.assign;var o=/\/+/g;function l(e,t){var s,r;return"object"==typeof e&&null!==e&&null!=e.key?(s=""+e.key,r={"=":"=0",":":"=2"},"$"+s.replace(/[=:]/g,function(e){return r[e]})):t.toString(36)}function c(){}},128:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let r=s(203);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${r.version}`}},144:(e,t,s)=>{"use strict";var r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,o=(e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})},l={};o(l,{Analytics:()=>h,IpDenyList:()=>v,MultiRegionRatelimit:()=>N,Ratelimit:()=>L}),e.exports=((e,t,s,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of n(t))a.call(e,l)||l===s||r(e,l,{get:()=>t[l],enumerable:!(o=i(t,l))||o.enumerable});return e})(r({},"__esModule",{value:!0}),l);var c=s(975),h=class{analytics;table="events";constructor(e){this.analytics=new c.Analytics({redis:e.redis,window:"1h",prefix:e.prefix??"@upstash/ratelimit",retention:"90d"})}extractGeo(e){return void 0!==e.geo?e.geo:void 0!==e.cf?e.cf:{}}async record(e){await this.analytics.ingest(this.table,e)}async series(e,t){let s=Math.min((this.analytics.getBucket(Date.now())-this.analytics.getBucket(t))/36e5,256);return this.analytics.aggregateBucketsWithPipeline(this.table,e,s)}async getUsage(e=0){let t=Math.min((this.analytics.getBucket(Date.now())-this.analytics.getBucket(e))/36e5,256);return await this.analytics.getAllowedBlocked(this.table,t)}async getUsageOverTime(e,t){return await this.analytics.aggregateBucketsWithPipeline(this.table,t,e)}async getMostAllowedBlocked(e,t,s){return t=t??5,this.analytics.getMostAllowedBlocked(this.table,e,t,void 0,s)}},u=class{cache;constructor(e){this.cache=e}isBlocked(e){if(!this.cache.has(e))return{blocked:!1,reset:0};let t=this.cache.get(e);return t<Date.now()?(this.cache.delete(e),{blocked:!1,reset:0}):{blocked:!0,reset:t}}blockUntil(e,t){this.cache.set(e,t)}set(e,t){this.cache.set(e,t)}get(e){return this.cache.get(e)||null}incr(e){let t=this.cache.get(e)??0;return t+=1,this.cache.set(e,t),t}pop(e){this.cache.delete(e)}empty(){this.cache.clear()}size(){return this.cache.size}};function d(e){let t=e.match(/^(\d+)\s?(ms|s|m|h|d)$/);if(!t)throw Error(`Unable to parse window size: ${e}`);let s=Number.parseInt(t[1]);switch(t[2]){case"ms":return s;case"s":return 1e3*s;case"m":return 1e3*s*60;case"h":return 1e3*s*3600;case"d":return 1e3*s*86400;default:throw Error(`Unable to parse window size: ${e}`)}}var p=async(e,t,s,r)=>{try{return await e.redis.evalsha(t.hash,s,r)}catch(i){if(`${i}`.includes("NOSCRIPT")){let i=await e.redis.scriptLoad(t.script);return i!==t.hash&&console.warn("Upstash Ratelimit: Expected hash and the hash received from Redis are different. Ratelimit will work as usual but performance will be reduced."),await e.redis.evalsha(i,s,r)}throw i}},f={singleRegion:{fixedWindow:{limit:{script:`
  local key           = KEYS[1]
  local window        = ARGV[1]
  local incrementBy   = ARGV[2] -- increment rate per request at a given value, default is 1

  local r = redis.call("INCRBY", key, incrementBy)
  if r == tonumber(incrementBy) then
  -- The first time this key is set, the value will be equal to incrementBy.
  -- So we only need the expire command once
  redis.call("PEXPIRE", key, window)
  end

  return r
`,hash:"b13943e359636db027ad280f1def143f02158c13"},getRemaining:{script:`
      local key = KEYS[1]
      local tokens = 0

      local value = redis.call('GET', key)
      if value then
          tokens = value
      end
      return tokens
    `,hash:"8c4c341934502aee132643ffbe58ead3450e5208"}},slidingWindow:{limit:{script:`
  local currentKey  = KEYS[1]           -- identifier including prefixes
  local previousKey = KEYS[2]           -- key of the previous bucket
  local tokens      = tonumber(ARGV[1]) -- tokens per window
  local now         = ARGV[2]           -- current timestamp in milliseconds
  local window      = ARGV[3]           -- interval in milliseconds
  local incrementBy = ARGV[4]           -- increment rate per request at a given value, default is 1

  local requestsInCurrentWindow = redis.call("GET", currentKey)
  if requestsInCurrentWindow == false then
    requestsInCurrentWindow = 0
  end

  local requestsInPreviousWindow = redis.call("GET", previousKey)
  if requestsInPreviousWindow == false then
    requestsInPreviousWindow = 0
  end
  local percentageInCurrent = ( now % window ) / window
  -- weighted requests to consider from the previous window
  requestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)
  if requestsInPreviousWindow + requestsInCurrentWindow >= tokens then
    return -1
  end

  local newValue = redis.call("INCRBY", currentKey, incrementBy)
  if newValue == tonumber(incrementBy) then
    -- The first time this key is set, the value will be equal to incrementBy.
    -- So we only need the expire command once
    redis.call("PEXPIRE", currentKey, window * 2 + 1000) -- Enough time to overlap with a new window + 1 second
  end
  return tokens - ( newValue + requestsInPreviousWindow )
`,hash:"e1391e429b699c780eb0480350cd5b7280fd9213"},getRemaining:{script:`
  local currentKey  = KEYS[1]           -- identifier including prefixes
  local previousKey = KEYS[2]           -- key of the previous bucket
  local now         = ARGV[1]           -- current timestamp in milliseconds
  local window      = ARGV[2]           -- interval in milliseconds

  local requestsInCurrentWindow = redis.call("GET", currentKey)
  if requestsInCurrentWindow == false then
    requestsInCurrentWindow = 0
  end

  local requestsInPreviousWindow = redis.call("GET", previousKey)
  if requestsInPreviousWindow == false then
    requestsInPreviousWindow = 0
  end

  local percentageInCurrent = ( now % window ) / window
  -- weighted requests to consider from the previous window
  requestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)

  return requestsInPreviousWindow + requestsInCurrentWindow
`,hash:"65a73ac5a05bf9712903bc304b77268980c1c417"}},tokenBucket:{limit:{script:`
  local key         = KEYS[1]           -- identifier including prefixes
  local maxTokens   = tonumber(ARGV[1]) -- maximum number of tokens
  local interval    = tonumber(ARGV[2]) -- size of the window in milliseconds
  local refillRate  = tonumber(ARGV[3]) -- how many tokens are refilled after each interval
  local now         = tonumber(ARGV[4]) -- current timestamp in milliseconds
  local incrementBy = tonumber(ARGV[5]) -- how many tokens to consume, default is 1
        
  local bucket = redis.call("HMGET", key, "refilledAt", "tokens")
        
  local refilledAt
  local tokens

  if bucket[1] == false then
    refilledAt = now
    tokens = maxTokens
  else
    refilledAt = tonumber(bucket[1])
    tokens = tonumber(bucket[2])
  end
        
  if now >= refilledAt + interval then
    local numRefills = math.floor((now - refilledAt) / interval)
    tokens = math.min(maxTokens, tokens + numRefills * refillRate)

    refilledAt = refilledAt + numRefills * interval
  end

  if tokens == 0 then
    return {-1, refilledAt + interval}
  end

  local remaining = tokens - incrementBy
  local expireAt = math.ceil(((maxTokens - remaining) / refillRate)) * interval
        
  redis.call("HSET", key, "refilledAt", refilledAt, "tokens", remaining)
  redis.call("PEXPIRE", key, expireAt)
  return {remaining, refilledAt + interval}
`,hash:"5bece90aeef8189a8cfd28995b479529e270b3c6"},getRemaining:{script:`
  local key         = KEYS[1]
  local maxTokens   = tonumber(ARGV[1])
        
  local bucket = redis.call("HMGET", key, "refilledAt", "tokens")

  if bucket[1] == false then
    return {maxTokens, -1}
  end
        
  return {tonumber(bucket[2]), tonumber(bucket[1])}
`,hash:"a15be2bb1db2a15f7c82db06146f9d08983900d0"}},cachedFixedWindow:{limit:{script:`
  local key     = KEYS[1]
  local window  = ARGV[1]
  local incrementBy   = ARGV[2] -- increment rate per request at a given value, default is 1

  local r = redis.call("INCRBY", key, incrementBy)
  if r == incrementBy then
  -- The first time this key is set, the value will be equal to incrementBy.
  -- So we only need the expire command once
  redis.call("PEXPIRE", key, window)
  end
      
  return r
`,hash:"c26b12703dd137939b9a69a3a9b18e906a2d940f"},getRemaining:{script:`
  local key = KEYS[1]
  local tokens = 0

  local value = redis.call('GET', key)
  if value then
      tokens = value
  end
  return tokens
`,hash:"8e8f222ccae68b595ee6e3f3bf2199629a62b91a"}}},multiRegion:{fixedWindow:{limit:{script:`
	local key           = KEYS[1]
	local id            = ARGV[1]
	local window        = ARGV[2]
	local incrementBy   = tonumber(ARGV[3])

	redis.call("HSET", key, id, incrementBy)
	local fields = redis.call("HGETALL", key)
	if #fields == 2 and tonumber(fields[2])==incrementBy then
	-- The first time this key is set, and the value will be equal to incrementBy.
	-- So we only need the expire command once
	  redis.call("PEXPIRE", key, window)
	end

	return fields
`,hash:"a8c14f3835aa87bd70e5e2116081b81664abcf5c"},getRemaining:{script:`
      local key = KEYS[1]
      local tokens = 0

      local fields = redis.call("HGETALL", key)

      return fields
    `,hash:"8ab8322d0ed5fe5ac8eb08f0c2e4557f1b4816fd"}},slidingWindow:{limit:{script:`
	local currentKey    = KEYS[1]           -- identifier including prefixes
	local previousKey   = KEYS[2]           -- key of the previous bucket
	local tokens        = tonumber(ARGV[1]) -- tokens per window
	local now           = ARGV[2]           -- current timestamp in milliseconds
	local window        = ARGV[3]           -- interval in milliseconds
	local requestId     = ARGV[4]           -- uuid for this request
	local incrementBy   = tonumber(ARGV[5]) -- custom rate, default is  1

	local currentFields = redis.call("HGETALL", currentKey)
	local requestsInCurrentWindow = 0
	for i = 2, #currentFields, 2 do
	requestsInCurrentWindow = requestsInCurrentWindow + tonumber(currentFields[i])
	end

	local previousFields = redis.call("HGETALL", previousKey)
	local requestsInPreviousWindow = 0
	for i = 2, #previousFields, 2 do
	requestsInPreviousWindow = requestsInPreviousWindow + tonumber(previousFields[i])
	end

	local percentageInCurrent = ( now % window) / window
	if requestsInPreviousWindow * (1 - percentageInCurrent ) + requestsInCurrentWindow >= tokens then
	  return {currentFields, previousFields, false}
	end

	redis.call("HSET", currentKey, requestId, incrementBy)

	if requestsInCurrentWindow == 0 then 
	  -- The first time this key is set, the value will be equal to incrementBy.
	  -- So we only need the expire command once
	  redis.call("PEXPIRE", currentKey, window * 2 + 1000) -- Enough time to overlap with a new window + 1 second
	end
	return {currentFields, previousFields, true}
`,hash:"cb4fdc2575056df7c6d422764df0de3a08d6753b"},getRemaining:{script:`
	local currentKey    = KEYS[1]           -- identifier including prefixes
	local previousKey   = KEYS[2]           -- key of the previous bucket
	local now         	= ARGV[1]           -- current timestamp in milliseconds
  	local window      	= ARGV[2]           -- interval in milliseconds

	local currentFields = redis.call("HGETALL", currentKey)
	local requestsInCurrentWindow = 0
	for i = 2, #currentFields, 2 do
	requestsInCurrentWindow = requestsInCurrentWindow + tonumber(currentFields[i])
	end

	local previousFields = redis.call("HGETALL", previousKey)
	local requestsInPreviousWindow = 0
	for i = 2, #previousFields, 2 do
	requestsInPreviousWindow = requestsInPreviousWindow + tonumber(previousFields[i])
	end

	local percentageInCurrent = ( now % window) / window
  	requestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)
	
	return requestsInCurrentWindow + requestsInPreviousWindow
`,hash:"558c9306b7ec54abb50747fe0b17e5d44bd24868"}}}},m={script:`
      local pattern = KEYS[1]

      -- Initialize cursor to start from 0
      local cursor = "0"

      repeat
          -- Scan for keys matching the pattern
          local scan_result = redis.call('SCAN', cursor, 'MATCH', pattern)

          -- Extract cursor for the next iteration
          cursor = scan_result[1]

          -- Extract keys from the scan result
          local keys = scan_result[2]

          for i=1, #keys do
          redis.call('DEL', keys[i])
          end

      -- Continue scanning until cursor is 0 (end of keyspace)
      until cursor == "0"
    `,hash:"54bd274ddc59fb3be0f42deee2f64322a10e2b50"},g="denyList",w="ipDenyList",b="ipDenyListStatus",y=`
  -- Checks if values provideed in ARGV are present in the deny lists.
  -- This is done using the allDenyListsKey below.

  -- Additionally, checks the status of the ip deny list using the
  -- ipDenyListStatusKey below. Here are the possible states of the
  -- ipDenyListStatusKey key:
  -- * status == -1: set to "disabled" with no TTL
  -- * status == -2: not set, meaning that is was set before but expired
  -- * status  >  0: set to "valid", with a TTL
  --
  -- In the case of status == -2, we set the status to "pending" with
  -- 30 second ttl. During this time, the process which got status == -2
  -- will update the ip deny list.

  local allDenyListsKey     = KEYS[1]
  local ipDenyListStatusKey = KEYS[2]

  local results = redis.call('SMISMEMBER', allDenyListsKey, unpack(ARGV))
  local status  = redis.call('TTL', ipDenyListStatusKey)
  if status == -2 then
    redis.call('SETEX', ipDenyListStatusKey, 30, "pending")
  end

  return { results, status }
`,v={};o(v,{ThresholdError:()=>x,disableIpDenyList:()=>E,updateIpDenyList:()=>O});var _=e=>864e5-((e||Date.now())-72e5)%864e5,x=class extends Error{constructor(e){super(`Allowed threshold values are from 1 to 8, 1 and 8 included. Received: ${e}`),this.name="ThresholdError"}},S=async e=>{if("number"!=typeof e||e<1||e>8)throw new x(e);try{let t=await fetch(`https://raw.githubusercontent.com/stamparm/ipsum/master/levels/${e}.txt`);if(!t.ok)throw Error(`Error fetching data: ${t.statusText}`);return(await t.text()).split("\n").filter(e=>e.length>0)}catch(e){throw Error(`Failed to fetch ip deny list: ${e}`)}},O=async(e,t,s,r)=>{let i=await S(s),n=[t,g,"all"].join(":"),a=[t,g,w].join(":"),o=[t,b].join(":"),l=e.multi();return l.sdiffstore(n,n,a),l.del(a),l.sadd(a,i.at(0),...i.slice(1)),l.sdiffstore(a,a,n),l.sunionstore(n,n,a),l.set(o,"valid",{px:r??_()}),await l.exec()},E=async(e,t)=>{let s=[t,g,"all"].join(":"),r=[t,g,w].join(":"),i=[t,b].join(":"),n=e.multi();return n.sdiffstore(s,s,r),n.del(r),n.set(i,"disabled"),await n.exec()},k=new u(new Map),T=e=>e.find(e=>k.isBlocked(e).blocked),R=e=>{k.size()>1e3&&k.empty(),k.blockUntil(e,Date.now()+6e4)},P=async(e,t,s)=>{let r,[i,n]=await e.eval(y,[[t,g,"all"].join(":"),[t,b].join(":")],s);return i.map((e,t)=>{e&&(R(s[t]),r=s[t])}),{deniedValue:r,invalidIpDenyList:-2===n}},C=(e,t,[s,r],i)=>{if(r.deniedValue&&(s.success=!1,s.remaining=0,s.reason="denyList",s.deniedValue=r.deniedValue),r.invalidIpDenyList){let r=O(e,t,i);s.pending=Promise.all([s.pending,r])}return s},A=e=>({success:!1,limit:0,remaining:0,reset:0,pending:Promise.resolve(),reason:"denyList",deniedValue:e}),I=class{limiter;ctx;prefix;timeout;primaryRedis;analytics;enableProtection;denyListThreshold;constructor(e){this.ctx=e.ctx,this.limiter=e.limiter,this.timeout=e.timeout??5e3,this.prefix=e.prefix??"@upstash/ratelimit",this.enableProtection=e.enableProtection??!1,this.denyListThreshold=e.denyListThreshold??6,this.primaryRedis="redis"in this.ctx?this.ctx.redis:this.ctx.regionContexts[0].redis,this.analytics=e.analytics?new h({redis:this.primaryRedis,prefix:this.prefix}):void 0,e.ephemeralCache instanceof Map?this.ctx.cache=new u(e.ephemeralCache):void 0===e.ephemeralCache&&(this.ctx.cache=new u(new Map))}limit=async(e,t)=>{let s=null;try{let r=this.getRatelimitResponse(e,t),{responseArray:i,newTimeoutId:n}=this.applyTimeout(r);s=n;let a=await Promise.race(i);return this.submitAnalytics(a,e,t)}finally{s&&clearTimeout(s)}};blockUntilReady=async(e,t)=>{let s;if(t<=0)throw Error("timeout must be positive");let r=Date.now()+t;for(;!(s=await this.limit(e)).success;){if(0===s.reset)throw Error("This should not happen");let e=Math.min(s.reset,r)-Date.now();if(await new Promise(t=>setTimeout(t,e)),Date.now()>r)break}return s};resetUsedTokens=async e=>{let t=[this.prefix,e].join(":");await this.limiter().resetTokens(this.ctx,t)};getRemaining=async e=>{let t=[this.prefix,e].join(":");return await this.limiter().getRemaining(this.ctx,t)};getRatelimitResponse=async(e,t)=>{let s=this.getKey(e),r=this.getDefinedMembers(e,t),i=T(r),n=i?[A(i),{deniedValue:i,invalidIpDenyList:!1}]:await Promise.all([this.limiter().limit(this.ctx,s,t?.rate),this.enableProtection?P(this.primaryRedis,this.prefix,r):{deniedValue:void 0,invalidIpDenyList:!1}]);return C(this.primaryRedis,this.prefix,n,this.denyListThreshold)};applyTimeout=e=>{let t=null,s=[e];if(this.timeout>0){let e=new Promise(e=>{t=setTimeout(()=>{e({success:!0,limit:0,remaining:0,reset:0,pending:Promise.resolve(),reason:"timeout"})},this.timeout)});s.push(e)}return{responseArray:s,newTimeoutId:t}};submitAnalytics=(e,t,s)=>{if(this.analytics)try{let r=s?this.analytics.extractGeo(s):void 0,i=this.analytics.record({identifier:"denyList"===e.reason?e.deniedValue:t,time:Date.now(),success:"denyList"===e.reason?"denied":e.success,...r}).catch(e=>{let t="Failed to record analytics";`${e}`.includes("WRONGTYPE")&&(t=`
    Failed to record analytics. See the information below:

    This can occur when you uprade to Ratelimit version 1.1.2
    or later from an earlier version.

    This occurs simply because the way we store analytics data
    has changed. To avoid getting this error, disable analytics
    for *an hour*, then simply enable it back.

    `),console.warn(t,e)});e.pending=Promise.all([e.pending,i])}catch(e){console.warn("Failed to record analytics",e)}return e};getKey=e=>[this.prefix,e].join(":");getDefinedMembers=(e,t)=>[e,t?.ip,t?.userAgent,t?.country].filter(Boolean)};function j(){let e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",s=t.length;for(let r=0;r<16;r++)e+=t.charAt(Math.floor(Math.random()*s));return e}var N=class extends I{constructor(e){super({prefix:e.prefix,limiter:e.limiter,timeout:e.timeout,analytics:e.analytics,ctx:{regionContexts:e.redis.map(e=>({redis:e})),cache:e.ephemeralCache?new u(e.ephemeralCache):void 0}})}static fixedWindow(e,t){let s=d(t);return()=>({async limit(t,r,i){if(t.cache){let{blocked:s,reset:i}=t.cache.isBlocked(r);if(s)return{success:!1,limit:e,remaining:0,reset:i,pending:Promise.resolve(),reason:"cacheBlock"}}let n=j(),a=Math.floor(Date.now()/s),o=[r,a].join(":"),l=i?Math.max(1,i):1,c=t.regionContexts.map(e=>({redis:e.redis,request:p(e,f.multiRegion.fixedWindow.limit,[o],[n,s,l])})),h=e-(await Promise.any(c.map(e=>e.request))).reduce((e,t,s)=>{let r=0;return s%2&&(r=Number.parseInt(t)),e+r},0);async function u(){let t=[...new Set((await Promise.all(c.map(e=>e.request))).flat().reduce((e,t,s)=>(s%2==0&&e.push(t),e),[])).values()];for(let s of c){let r=(await s.request).reduce((e,t,s)=>{let r=0;return s%2&&(r=Number.parseInt(t)),e+r},0),i=(await s.request).reduce((e,t,s)=>(s%2==0&&e.push(t),e),[]);if(r>=e)continue;let n=t.filter(e=>!i.includes(e));if(0!==n.length)for(let e of n)await s.redis.hset(o,{[e]:l})}}let d=h>0,m=(a+1)*s;return t.cache&&!d&&t.cache.blockUntil(r,m),{success:d,limit:e,remaining:h,reset:m,pending:u()}},async getRemaining(t,r){let i=Math.floor(Date.now()/s),n=[r,i].join(":"),a=t.regionContexts.map(e=>({redis:e.redis,request:p(e,f.multiRegion.fixedWindow.getRemaining,[n],[null])}));return{remaining:Math.max(0,e-(await Promise.any(a.map(e=>e.request))).reduce((e,t,s)=>{let r=0;return s%2&&(r=Number.parseInt(t)),e+r},0)),reset:(i+1)*s}},async resetTokens(e,t){let s=[t,"*"].join(":");e.cache&&e.cache.pop(t),await Promise.all(e.regionContexts.map(e=>{p(e,m,[s],[null])}))}})}static slidingWindow(e,t){let s=d(t),r=d(t);return()=>({async limit(t,i,n){if(t.cache){let{blocked:s,reset:r}=t.cache.isBlocked(i);if(s)return{success:!1,limit:e,remaining:0,reset:r,pending:Promise.resolve(),reason:"cacheBlock"}}let a=j(),o=Date.now(),l=Math.floor(o/s),c=[i,l].join(":"),h=[i,l-1].join(":"),u=n?Math.max(1,n):1,d=t.regionContexts.map(t=>({redis:t.redis,request:p(t,f.multiRegion.slidingWindow.limit,[c,h],[e,o,r,a,u])})),m=o%r/r,[g,w,b]=await Promise.any(d.map(e=>e.request));b&&g.push(a,u.toString());let y=w.reduce((e,t,s)=>{let r=0;return s%2&&(r=Number.parseInt(t)),e+r},0),v=g.reduce((e,t,s)=>{let r=0;return s%2&&(r=Number.parseInt(t)),e+r},0),_=e-(Math.ceil(y*(1-m))+v);async function x(){let t=[...new Set((await Promise.all(d.map(e=>e.request))).flatMap(([e])=>e).reduce((e,t,s)=>(s%2==0&&e.push(t),e),[])).values()];for(let s of d){let[r,i,n]=await s.request,a=r.reduce((e,t,s)=>(s%2==0&&e.push(t),e),[]);if(r.reduce((e,t,s)=>{let r=0;return s%2&&(r=Number.parseInt(t)),e+r},0)>=e)continue;let o=t.filter(e=>!a.includes(e));if(0!==o.length)for(let e of o)await s.redis.hset(c,{[e]:u})}}let S=(l+1)*r;return t.cache&&!b&&t.cache.blockUntil(i,S),{success:!!b,limit:e,remaining:Math.max(0,_),reset:S,pending:x()}},async getRemaining(t,r){let i=Date.now(),n=Math.floor(i/s),a=[r,n].join(":"),o=[r,n-1].join(":"),l=t.regionContexts.map(e=>({redis:e.redis,request:p(e,f.multiRegion.slidingWindow.getRemaining,[a,o],[i,s])}));return{remaining:Math.max(0,e-await Promise.any(l.map(e=>e.request))),reset:(n+1)*s}},async resetTokens(e,t){let s=[t,"*"].join(":");e.cache&&e.cache.pop(t),await Promise.all(e.regionContexts.map(e=>{p(e,m,[s],[null])}))}})}},L=class extends I{constructor(e){super({prefix:e.prefix,limiter:e.limiter,timeout:e.timeout,analytics:e.analytics,ctx:{redis:e.redis},ephemeralCache:e.ephemeralCache,enableProtection:e.enableProtection,denyListThreshold:e.denyListThreshold})}static fixedWindow(e,t){let s=d(t);return()=>({async limit(t,r,i){let n=Math.floor(Date.now()/s),a=[r,n].join(":");if(t.cache){let{blocked:s,reset:i}=t.cache.isBlocked(r);if(s)return{success:!1,limit:e,remaining:0,reset:i,pending:Promise.resolve(),reason:"cacheBlock"}}let o=i?Math.max(1,i):1,l=await p(t,f.singleRegion.fixedWindow.limit,[a],[s,o]),c=l<=e,h=Math.max(0,e-l),u=(n+1)*s;return t.cache&&!c&&t.cache.blockUntil(r,u),{success:c,limit:e,remaining:h,reset:u,pending:Promise.resolve()}},async getRemaining(t,r){let i=Math.floor(Date.now()/s),n=[r,i].join(":");return{remaining:Math.max(0,e-await p(t,f.singleRegion.fixedWindow.getRemaining,[n],[null])),reset:(i+1)*s}},async resetTokens(e,t){let s=[t,"*"].join(":");e.cache&&e.cache.pop(t),await p(e,m,[s],[null])}})}static slidingWindow(e,t){let s=d(t);return()=>({async limit(t,r,i){let n=Date.now(),a=Math.floor(n/s),o=[r,a].join(":"),l=[r,a-1].join(":");if(t.cache){let{blocked:s,reset:i}=t.cache.isBlocked(r);if(s)return{success:!1,limit:e,remaining:0,reset:i,pending:Promise.resolve(),reason:"cacheBlock"}}let c=i?Math.max(1,i):1,h=await p(t,f.singleRegion.slidingWindow.limit,[o,l],[e,n,s,c]),u=h>=0,d=(a+1)*s;return t.cache&&!u&&t.cache.blockUntil(r,d),{success:u,limit:e,remaining:Math.max(0,h),reset:d,pending:Promise.resolve()}},async getRemaining(t,r){let i=Date.now(),n=Math.floor(i/s),a=[r,n].join(":"),o=[r,n-1].join(":");return{remaining:Math.max(0,e-await p(t,f.singleRegion.slidingWindow.getRemaining,[a,o],[i,s])),reset:(n+1)*s}},async resetTokens(e,t){let s=[t,"*"].join(":");e.cache&&e.cache.pop(t),await p(e,m,[s],[null])}})}static tokenBucket(e,t,s){let r=d(t);return()=>({async limit(t,i,n){if(t.cache){let{blocked:e,reset:r}=t.cache.isBlocked(i);if(e)return{success:!1,limit:s,remaining:0,reset:r,pending:Promise.resolve(),reason:"cacheBlock"}}let a=Date.now(),o=n?Math.max(1,n):1,[l,c]=await p(t,f.singleRegion.tokenBucket.limit,[i],[s,r,e,a,o]),h=l>=0;return t.cache&&!h&&t.cache.blockUntil(i,c),{success:h,limit:s,remaining:l,reset:c,pending:Promise.resolve()}},async getRemaining(e,t){let[i,n]=await p(e,f.singleRegion.tokenBucket.getRemaining,[t],[s]),a=Date.now()+r,o=n+r;return{remaining:i,reset:-1===n?a:o}},async resetTokens(e,t){e.cache&&e.cache.pop(t),await p(e,m,[t],[null])}})}static cachedFixedWindow(e,t){let s=d(t);return()=>({async limit(t,r,i){if(!t.cache)throw Error("This algorithm requires a cache");let n=Math.floor(Date.now()/s),a=[r,n].join(":"),o=(n+1)*s,l=i?Math.max(1,i):1;if("number"==typeof t.cache.get(a)){let r=t.cache.incr(a),i=r<e,n=i?p(t,f.singleRegion.cachedFixedWindow.limit,[a],[s,l]):Promise.resolve();return{success:i,limit:e,remaining:e-r,reset:o,pending:n}}let c=await p(t,f.singleRegion.cachedFixedWindow.limit,[a],[s,l]);t.cache.set(a,c);let h=e-c;return{success:h>=0,limit:e,remaining:h,reset:o,pending:Promise.resolve()}},async getRemaining(t,r){if(!t.cache)throw Error("This algorithm requires a cache");let i=Math.floor(Date.now()/s),n=[r,i].join(":");return"number"==typeof t.cache.get(n)?{remaining:Math.max(0,e-(t.cache.get(n)??0)),reset:(i+1)*s}:{remaining:Math.max(0,e-await p(t,f.singleRegion.cachedFixedWindow.getRemaining,[n],[null])),reset:(i+1)*s}},async resetTokens(e,t){if(!e.cache)throw Error("This algorithm requires a cache");let r=[t,Math.floor(Date.now()/s)].join(":");e.cache.pop(r);let i=[t,"*"].join(":");await p(e,m,[i],[null])}})}}},201:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return n}});let r=new(s(521)).AsyncLocalStorage;function i(e,t){let s=t.header(e,"next-test-proxy-port");if(!s)return;let r=t.url(e);return{url:r,proxyPort:Number(s),testData:t.header(e,"next-test-data")||""}}function n(e,t,s){let n=i(e,t);return n?r.run(n,s):s()}function a(e,t){let s=r.getStore();return s||(e&&t?i(e,t):void 0)}},203:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},279:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(3)),n=r(s(784));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=i.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,s,r;let i=null,a=null,o=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let r=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),n=null==(s=e.headers.get("content-range"))?void 0:s.split("/");r&&n&&n.length>1&&(o=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(i={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,c="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{i=JSON.parse(t),Array.isArray(i)&&404===e.status&&(a=[],i=null,l=200,c="OK")}catch(s){404===e.status&&""===t?(l=204,c="No Content"):i={message:t}}if(i&&this.isMaybeSingle&&(null==(r=null==i?void 0:i.details)?void 0:r.includes("0 rows"))&&(i=null,l=200,c="OK"),i&&this.shouldThrowOnError)throw new n.default(i)}return{error:i,data:a,count:o,status:l,statusText:c}});return this.shouldThrowOnError||(s=s.catch(e=>{var t,s,r;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(s=null==e?void 0:e.stack)?s:""}`,hint:"",code:`${null!=(r=null==e?void 0:e.code)?r:""}`},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},280:(e,t,s)=>{var r;(()=>{var i={226:function(i,n){!function(a,o){"use strict";var l="function",c="undefined",h="object",u="string",d="major",p="model",f="name",m="type",g="vendor",w="version",b="architecture",y="console",v="mobile",_="tablet",x="smarttv",S="wearable",O="embedded",E="Amazon",k="Apple",T="ASUS",R="BlackBerry",P="Browser",C="Chrome",A="Firefox",I="Google",j="Huawei",N="Microsoft",L="Motorola",M="Opera",D="Samsung",U="Sharp",$="Sony",q="Xiaomi",B="Zebra",z="Facebook",W="Chromium OS",G="Mac OS",V=function(e,t){var s={};for(var r in e)t[r]&&t[r].length%2==0?s[r]=t[r].concat(e[r]):s[r]=e[r];return s},F=function(e){for(var t={},s=0;s<e.length;s++)t[e[s].toUpperCase()]=e[s];return t},K=function(e,t){return typeof e===u&&-1!==H(t).indexOf(H(e))},H=function(e){return e.toLowerCase()},J=function(e,t){if(typeof e===u)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var s,r,i,n,a,c,u=0;u<t.length&&!a;){var d=t[u],p=t[u+1];for(s=r=0;s<d.length&&!a&&d[s];)if(a=d[s++].exec(e))for(i=0;i<p.length;i++)c=a[++r],typeof(n=p[i])===h&&n.length>0?2===n.length?typeof n[1]==l?this[n[0]]=n[1].call(this,c):this[n[0]]=n[1]:3===n.length?typeof n[1]!==l||n[1].exec&&n[1].test?this[n[0]]=c?c.replace(n[1],n[2]):void 0:this[n[0]]=c?n[1].call(this,c,n[2]):void 0:4===n.length&&(this[n[0]]=c?n[3].call(this,c.replace(n[1],n[2])):o):this[n]=c||o;u+=2}},Y=function(e,t){for(var s in t)if(typeof t[s]===h&&t[s].length>0){for(var r=0;r<t[s].length;r++)if(K(t[s][r],e))return"?"===s?o:s}else if(K(t[s],e))return"?"===s?o:s;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[w,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[w,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,w],[/opios[\/ ]+([\w\.]+)/i],[w,[f,M+" Mini"]],[/\bopr\/([\w\.]+)/i],[w,[f,M]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,w],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[w,[f,"UC"+P]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[w,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[w,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[w,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[w,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[w,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+P],w],[/\bfocus\/([\w\.]+)/i],[w,[f,A+" Focus"]],[/\bopt\/([\w\.]+)/i],[w,[f,M+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[w,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[w,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[w,[f,M+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[w,[f,"MIUI "+P]],[/fxios\/([-\w\.]+)/i],[w,[f,A]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+P]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+P],w],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],w],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,w],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,z],w],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,w],[/\bgsa\/([\w\.]+) .*safari\//i],[w,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[w,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[w,[f,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,C+" WebView"],w],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[w,[f,"Android "+P]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,w],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[w,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[w,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[w,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,w],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],w],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[w,[f,A+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,w],[/(cobalt)\/([\w\.]+)/i],[f,[w,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,H]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",H]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,H]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[g,D],[m,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[g,D],[m,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[g,k],[m,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[g,k],[m,_]],[/(macintosh);/i],[p,[g,k]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[g,U],[m,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[g,j],[m,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[g,j],[m,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[g,q],[m,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[g,q],[m,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[g,"OPPO"],[m,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[g,"Vivo"],[m,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[g,"Realme"],[m,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[g,L],[m,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[g,L],[m,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[g,"LG"],[m,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[g,"LG"],[m,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[g,"Lenovo"],[m,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[g,"Nokia"],[m,v]],[/(pixel c)\b/i],[p,[g,I],[m,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[g,I],[m,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[g,$],[m,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[g,$],[m,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[g,"OnePlus"],[m,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[g,E],[m,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[g,E],[m,v]],[/(playbook);[-\w\),; ]+(rim)/i],[p,g,[m,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[g,R],[m,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[g,T],[m,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[g,T],[m,v]],[/(nexus 9)/i],[p,[g,"HTC"],[m,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[g,[p,/_/g," "],[m,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[g,"Acer"],[m,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[g,"Meizu"],[m,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[g,p,[m,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[g,p,[m,_]],[/(surface duo)/i],[p,[g,N],[m,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[g,"Fairphone"],[m,v]],[/(u304aa)/i],[p,[g,"AT&T"],[m,v]],[/\bsie-(\w*)/i],[p,[g,"Siemens"],[m,v]],[/\b(rct\w+) b/i],[p,[g,"RCA"],[m,_]],[/\b(venue[\d ]{2,7}) b/i],[p,[g,"Dell"],[m,_]],[/\b(q(?:mv|ta)\w+) b/i],[p,[g,"Verizon"],[m,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[g,"Barnes & Noble"],[m,_]],[/\b(tm\d{3}\w+) b/i],[p,[g,"NuVision"],[m,_]],[/\b(k88) b/i],[p,[g,"ZTE"],[m,_]],[/\b(nx\d{3}j) b/i],[p,[g,"ZTE"],[m,v]],[/\b(gen\d{3}) b.+49h/i],[p,[g,"Swiss"],[m,v]],[/\b(zur\d{3}) b/i],[p,[g,"Swiss"],[m,_]],[/\b((zeki)?tb.*\b) b/i],[p,[g,"Zeki"],[m,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[g,"Dragon Touch"],p,[m,_]],[/\b(ns-?\w{0,9}) b/i],[p,[g,"Insignia"],[m,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[g,"NextBook"],[m,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[g,"Voice"],p,[m,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[g,"LvTel"],p,[m,v]],[/\b(ph-1) /i],[p,[g,"Essential"],[m,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[g,"Envizen"],[m,_]],[/\b(trio[-\w\. ]+) b/i],[p,[g,"MachSpeed"],[m,_]],[/\btu_(1491) b/i],[p,[g,"Rotor"],[m,_]],[/(shield[\w ]+) b/i],[p,[g,"Nvidia"],[m,_]],[/(sprint) (\w+)/i],[g,p,[m,v]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[g,N],[m,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[g,B],[m,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[g,B],[m,v]],[/smart-tv.+(samsung)/i],[g,[m,x]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[g,D],[m,x]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[g,"LG"],[m,x]],[/(apple) ?tv/i],[g,[p,k+" TV"],[m,x]],[/crkey/i],[[p,C+"cast"],[g,I],[m,x]],[/droid.+aft(\w)( bui|\))/i],[p,[g,E],[m,x]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[g,U],[m,x]],[/(bravia[\w ]+)( bui|\))/i],[p,[g,$],[m,x]],[/(mitv-\w{5}) bui/i],[p,[g,q],[m,x]],[/Hbbtv.*(technisat) (.*);/i],[g,p,[m,x]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[g,J],[p,J],[m,x]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,x]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[g,p,[m,y]],[/droid.+; (shield) bui/i],[p,[g,"Nvidia"],[m,y]],[/(playstation [345portablevi]+)/i],[p,[g,$],[m,y]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[g,N],[m,y]],[/((pebble))app/i],[g,p,[m,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[g,k],[m,S]],[/droid.+; (glass) \d/i],[p,[g,I],[m,S]],[/droid.+; (wt63?0{2,3})\)/i],[p,[g,B],[m,S]],[/(quest( 2| pro)?)/i],[p,[g,z],[m,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[g,[m,O]],[/(aeobc)\b/i],[p,[g,E],[m,O]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[m,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[m,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,v]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[g,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[w,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[w,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,w],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[w,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,w],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[w,Y,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[w,Y,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[w,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,G],[w,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[w,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,w],[/\(bb(10);/i],[w,[f,R]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[w,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[w,[f,A+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[w,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[w,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[w,[f,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,W],w],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,w],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],w],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,w]]},ee=function(e,t){if(typeof e===h&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var s=typeof a!==c&&a.navigator?a.navigator:o,r=e||(s&&s.userAgent?s.userAgent:""),i=s&&s.userAgentData?s.userAgentData:o,n=t?V(Z,t):Z,y=s&&s.userAgent==r;return this.getBrowser=function(){var e,t={};return t[f]=o,t[w]=o,X.call(t,r,n.browser),t[d]=typeof(e=t[w])===u?e.replace(/[^\d\.]/g,"").split(".")[0]:o,y&&s&&s.brave&&typeof s.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[b]=o,X.call(e,r,n.cpu),e},this.getDevice=function(){var e={};return e[g]=o,e[p]=o,e[m]=o,X.call(e,r,n.device),y&&!e[m]&&i&&i.mobile&&(e[m]=v),y&&"Macintosh"==e[p]&&s&&typeof s.standalone!==c&&s.maxTouchPoints&&s.maxTouchPoints>2&&(e[p]="iPad",e[m]=_),e},this.getEngine=function(){var e={};return e[f]=o,e[w]=o,X.call(e,r,n.engine),e},this.getOS=function(){var e={};return e[f]=o,e[w]=o,X.call(e,r,n.os),y&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,W).replace(/macos/i,G)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===u&&e.length>350?J(e,350):e,this},this.setUA(r),this};ee.VERSION="1.0.35",ee.BROWSER=F([f,w,d]),ee.CPU=F([b]),ee.DEVICE=F([p,g,m,y,v,x,_,S,O]),ee.ENGINE=ee.OS=F([f,w]),typeof n!==c?(i.exports&&(n=i.exports=ee),n.UAParser=ee):s.amdO?void 0===(r=(function(){return ee}).call(t,s,t,e))||(e.exports=r):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var es=new ee;et.ua=es.getResult(),et.ua.get=function(){return es.getUA()},et.ua.set=function(e){es.setUA(e);var t=es.getResult();for(var s in t)et.ua[s]=t[s]}}}("object"==typeof window?window:this)}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var s=n[e]={exports:{}},r=!0;try{i[e].call(s.exports,s,s.exports,a),r=!1}finally{r&&delete n[e]}return s.exports}a.ab="//",e.exports=a(226)})()},314:(e,t,s)=>{"use strict";let r,i;s.r(t),s.d(t,{default:()=>ou});var n,a,o,l,c,h,u,d,p,f,m,g={};async function w(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}s.r(g),s.d(g,{config:()=>oo,middleware:()=>on});let b=null;async function y(){if("phase-production-build"===process.env.NEXT_PHASE)return;b||(b=w());let e=await b;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function v(...e){let t=await w();try{var s;await (null==t||null==(s=t.onRequestError)?void 0:s.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let _=null;function x(){return _||(_=y()),_}function S(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==s.g.process&&(process.env=s.g.process.env,s.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,s){if("then"===s)return{};throw Object.defineProperty(Error(S(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(S(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(s,r,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(S(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),x();class O extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class E extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class k extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let T="_N_T_",R={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function P(e){var t,s,r,i,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(s=e.charAt(o))){for(r=o,o+=1,l(),i=o;o<e.length&&"="!==(s=e.charAt(o))&&";"!==s&&","!==s;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=i,a.push(e.substring(t,r)),t=o):o=r+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function C(e){let t={},s=[];if(e)for(let[r,i]of e.entries())"set-cookie"===r.toLowerCase()?(s.push(...P(i)),t[r]=1===s.length?s[0]:s):t[r]=i;return t}function A(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...R,GROUP:{builtinReact:[R.reactServerComponents,R.actionBrowser],serverOnly:[R.reactServerComponents,R.actionBrowser,R.instrument,R.middleware],neutralTarget:[R.apiNode,R.apiEdge],clientOnly:[R.serverSideRendering,R.appPagesBrowser],bundled:[R.reactServerComponents,R.actionBrowser,R.serverSideRendering,R.appPagesBrowser,R.shared,R.instrument,R.middleware],appPages:[R.reactServerComponents,R.serverSideRendering,R.appPagesBrowser,R.actionBrowser]}});let I=Symbol("response"),j=Symbol("passThrough"),N=Symbol("waitUntil");class L{constructor(e,t){this[j]=!1,this[N]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[I]||(this[I]=Promise.resolve(e))}passThroughOnException(){this[j]=!0}waitUntil(e){if("external"===this[N].kind)return(0,this[N].function)(e);this[N].promises.push(e)}}class M extends L{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new O({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new O({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function D(e){return e.replace(/\/$/,"")||"/"}function U(e){let t=e.indexOf("#"),s=e.indexOf("?"),r=s>-1&&(t<0||s<t);return r||t>-1?{pathname:e.substring(0,r?s:t),query:r?e.substring(s,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function $(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:s,query:r,hash:i}=U(e);return""+t+s+r+i}function q(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:s,query:r,hash:i}=U(e);return""+s+t+r+i}function B(e,t){if("string"!=typeof e)return!1;let{pathname:s}=U(e);return s===t||s.startsWith(t+"/")}let z=new WeakMap;function W(e,t){let s;if(!t)return{pathname:e};let r=z.get(t);r||(r=t.map(e=>e.toLowerCase()),z.set(t,r));let i=e.split("/",2);if(!i[1])return{pathname:e};let n=i[1].toLowerCase(),a=r.indexOf(n);return a<0?{pathname:e}:(s=t[a],{pathname:e=e.slice(s.length+1)||"/",detectedLocale:s})}let G=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function V(e,t){return new URL(String(e).replace(G,"localhost"),t&&String(t).replace(G,"localhost"))}let F=Symbol("NextURLInternal");class K{constructor(e,t,s){let r,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(r=t,i=s||{}):i=s||t||{},this[F]={url:V(e,r??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,s,r,i;let n=function(e,t){var s,r;let{basePath:i,i18n:n,trailingSlash:a}=null!=(s=t.nextConfig)?s:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};i&&B(o.pathname,i)&&(o.pathname=function(e,t){if(!B(e,t))return e;let s=e.slice(t.length);return s.startsWith("/")?s:"/"+s}(o.pathname,i),o.basePath=i);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");o.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(n){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):W(o.pathname,n.locales);o.locale=e.detectedLocale,o.pathname=null!=(r=e.pathname)?r:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):W(l,n.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[F].url.pathname,{nextConfig:this[F].options.nextConfig,parseData:!0,i18nProvider:this[F].options.i18nProvider}),a=function(e,t){let s;if((null==t?void 0:t.host)&&!Array.isArray(t.host))s=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;s=e.hostname}return s.toLowerCase()}(this[F].url,this[F].options.headers);this[F].domainLocale=this[F].options.i18nProvider?this[F].options.i18nProvider.detectDomainLocale(a):function(e,t,s){if(e)for(let n of(s&&(s=s.toLowerCase()),e)){var r,i;if(t===(null==(r=n.domain)?void 0:r.split(":",1)[0].toLowerCase())||s===n.defaultLocale.toLowerCase()||(null==(i=n.locales)?void 0:i.some(e=>e.toLowerCase()===s)))return n}}(null==(t=this[F].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(s=this[F].domainLocale)?void 0:s.defaultLocale)||(null==(i=this[F].options.nextConfig)||null==(r=i.i18n)?void 0:r.defaultLocale);this[F].url.pathname=n.pathname,this[F].defaultLocale=o,this[F].basePath=n.basePath??"",this[F].buildId=n.buildId,this[F].locale=n.locale??o,this[F].trailingSlash=n.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,s,r){if(!t||t===s)return e;let i=e.toLowerCase();return!r&&(B(i,"/api")||B(i,"/"+t.toLowerCase()))?e:$(e,"/"+t)}((e={basePath:this[F].basePath,buildId:this[F].buildId,defaultLocale:this[F].options.forceLocale?void 0:this[F].defaultLocale,locale:this[F].locale,pathname:this[F].url.pathname,trailingSlash:this[F].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=D(t)),e.buildId&&(t=q($(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=$(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:q(t,"/"):D(t)}formatSearch(){return this[F].url.search}get buildId(){return this[F].buildId}set buildId(e){this[F].buildId=e}get locale(){return this[F].locale??""}set locale(e){var t,s;if(!this[F].locale||!(null==(s=this[F].options.nextConfig)||null==(t=s.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[F].locale=e}get defaultLocale(){return this[F].defaultLocale}get domainLocale(){return this[F].domainLocale}get searchParams(){return this[F].url.searchParams}get host(){return this[F].url.host}set host(e){this[F].url.host=e}get hostname(){return this[F].url.hostname}set hostname(e){this[F].url.hostname=e}get port(){return this[F].url.port}set port(e){this[F].url.port=e}get protocol(){return this[F].url.protocol}set protocol(e){this[F].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[F].url=V(e),this.analyze()}get origin(){return this[F].url.origin}get pathname(){return this[F].url.pathname}set pathname(e){this[F].url.pathname=e}get hash(){return this[F].url.hash}set hash(e){this[F].url.hash=e}get search(){return this[F].url.search}set search(e){this[F].url.search=e}get password(){return this[F].url.password}set password(e){this[F].url.password=e}get username(){return this[F].url.username}set username(e){this[F].url.username=e}get basePath(){return this[F].basePath}set basePath(e){this[F].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new K(String(this),this[F].options)}}var H=s(724);let J=Symbol("internal request");class X extends Request{constructor(e,t={}){let s="string"!=typeof e&&"url"in e?e.url:String(e);A(s),e instanceof Request?super(e,t):super(s,t);let r=new K(s,{headers:C(this.headers),nextConfig:t.nextConfig});this[J]={cookies:new H.RequestCookies(this.headers),nextUrl:r,url:r.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[J].cookies}get nextUrl(){return this[J].nextUrl}get page(){throw new E}get ua(){throw new k}get url(){return this[J].url}}class Y{static get(e,t,s){let r=Reflect.get(e,t,s);return"function"==typeof r?r.bind(e):r}static set(e,t,s,r){return Reflect.set(e,t,s,r)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let Q=Symbol("internal response"),Z=new Set([301,302,303,307,308]);function ee(e,t){var s;if(null==e||null==(s=e.request)?void 0:s.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let s=[];for(let[r,i]of e.request.headers)t.set("x-middleware-request-"+r,i),s.push(r);t.set("x-middleware-override-headers",s.join(","))}}class et extends Response{constructor(e,t={}){super(e,t);let s=this.headers,r=new Proxy(new H.ResponseCookies(s),{get(e,r,i){switch(r){case"delete":case"set":return(...i)=>{let n=Reflect.apply(e[r],e,i),a=new Headers(s);return n instanceof H.ResponseCookies&&s.set("x-middleware-set-cookie",n.getAll().map(e=>(0,H.stringifyCookie)(e)).join(",")),ee(t,a),n};default:return Y.get(e,r,i)}}});this[Q]={cookies:r,url:t.url?new K(t.url,{headers:C(s),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[Q].cookies}static json(e,t){let s=Response.json(e,t);return new et(s.body,s)}static redirect(e,t){let s="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!Z.has(s))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let r="object"==typeof t?t:{},i=new Headers(null==r?void 0:r.headers);return i.set("Location",A(e)),new et(null,{...r,headers:i,status:s})}static rewrite(e,t){let s=new Headers(null==t?void 0:t.headers);return s.set("x-middleware-rewrite",A(e)),ee(t,s),new et(null,{...t,headers:s})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),ee(e,t),new et(null,{...e,headers:t})}}function es(e,t){let s="string"==typeof t?new URL(t):t,r=new URL(e,t),i=r.origin===s.origin;return{url:i?r.toString().slice(s.origin.length):r.toString(),isRelative:i}}let er="Next-Router-Prefetch",ei=["RSC","Next-Router-State-Tree",er,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],en="_rsc";class ea extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ea}}class eo extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,s,r){if("symbol"==typeof s)return Y.get(t,s,r);let i=s.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==n)return Y.get(t,n,r)},set(t,s,r,i){if("symbol"==typeof s)return Y.set(t,s,r,i);let n=s.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return Y.set(t,a??s,r,i)},has(t,s){if("symbol"==typeof s)return Y.has(t,s);let r=s.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===r);return void 0!==i&&Y.has(t,i)},deleteProperty(t,s){if("symbol"==typeof s)return Y.deleteProperty(t,s);let r=s.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===r);return void 0===i||Y.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,s){switch(t){case"append":case"delete":case"set":return ea.callable;default:return Y.get(e,t,s)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new eo(e)}append(e,t){let s=this.headers[e];"string"==typeof s?this.headers[e]=[s,t]:Array.isArray(s)?s.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[s,r]of this.entries())e.call(t,r,s,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),s=this.get(t);yield[t,s]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let el=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class ec{disable(){throw el}getStore(){}run(){throw el}exit(){throw el}enterWith(){throw el}static bind(e){return e}}let eh="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function eu(){return eh?new eh:new ec}let ed=eu(),ep=eu();class ef extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new ef}}class em{static seal(e){return new Proxy(e,{get(e,t,s){switch(t){case"clear":case"delete":case"set":return ef.callable;default:return Y.get(e,t,s)}}})}}let eg=Symbol.for("next.mutated.cookies");class ew{static wrap(e,t){let s=new H.ResponseCookies(new Headers);for(let t of e.getAll())s.set(t);let r=[],i=new Set,n=()=>{let e=ed.getStore();if(e&&(e.pathWasRevalidated=!0),r=s.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of r){let s=new H.ResponseCookies(new Headers);s.set(t),e.push(s.toString())}t(e)}},a=new Proxy(s,{get(e,t,s){switch(t){case eg:return r;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),a}finally{n()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),a}finally{n()}};default:return Y.get(e,t,s)}}});return a}}function eb(e){if("action"!==function(e){let t=ep.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}(e).phase)throw new ef}var ey=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(ey||{}),ev=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(ev||{}),e_=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(e_||{}),ex=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ex||{}),eS=function(e){return e.startServer="startServer.startServer",e}(eS||{}),eO=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(eO||{}),eE=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(eE||{}),ek=function(e){return e.executeRoute="Router.executeRoute",e}(ek||{}),eT=function(e){return e.runHandler="Node.runHandler",e}(eT||{}),eR=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(eR||{}),eP=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(eP||{}),eC=function(e){return e.execute="Middleware.execute",e}(eC||{});let eA=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],eI=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function ej(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:eN,propagation:eL,trace:eM,SpanStatusCode:eD,SpanKind:eU,ROOT_CONTEXT:e$}=r=s(956);class eq extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eB=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eq})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eD.ERROR,message:null==t?void 0:t.message})),e.end()},ez=new Map,eW=r.createContextKey("next.rootSpanId"),eG=0,eV=()=>eG++,eF={set(e,t,s){e.push({key:t,value:s})}};class eK{getTracerInstance(){return eM.getTracer("next.js","0.0.1")}getContext(){return eN}getTracePropagationData(){let e=eN.active(),t=[];return eL.inject(e,t,eF),t}getActiveScopeSpan(){return eM.getSpan(null==eN?void 0:eN.active())}withPropagatedContext(e,t,s){let r=eN.active();if(eM.getSpanContext(r))return t();let i=eL.extract(r,e,s);return eN.with(i,t)}trace(...e){var t;let[s,r,i]=e,{fn:n,options:a}="function"==typeof r?{fn:r,options:{}}:{fn:i,options:{...r}},o=a.spanName??s;if(!eA.includes(s)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return n();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=eM.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==eN?void 0:eN.active())??e$,c=!0);let h=eV();return a.attributes={"next.span_name":o,"next.span_type":s,...a.attributes},eN.with(l.setValue(eW,h),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,r=()=>{ez.delete(h),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&eI.includes(s||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(s.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&ez.set(h,new Map(Object.entries(a.attributes??{})));try{if(n.length>1)return n(e,t=>eB(e,t));let t=n(e);if(ej(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eB(e,t),t}).finally(r);return e.end(),r(),t}catch(t){throw eB(e,t),r(),t}}))}wrap(...e){let t=this,[s,r,i]=3===e.length?e:[e[0],{},e[1]];return eA.includes(s)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=r;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let n=arguments.length-1,a=arguments[n];if("function"!=typeof a)return t.trace(s,e,()=>i.apply(this,arguments));{let r=t.getContext().bind(eN.active(),a);return t.trace(s,e,(e,t)=>(arguments[n]=function(e){return null==t||t(e),r.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,s]=e,r=this.getSpanContext((null==s?void 0:s.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,s,r)}getSpanContext(e){return e?eM.setSpan(eN.active(),e):void 0}getRootSpanAttributes(){let e=eN.active().getValue(eW);return ez.get(e)}setRootSpanAttribute(e,t){let s=eN.active().getValue(eW),r=ez.get(s);r&&r.set(e,t)}}let eH=(()=>{let e=new eK;return()=>e})(),eJ="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eJ);class eX{constructor(e,t,s,r){var i;let n=e&&function(e,t){let s=eo.from(e.headers);return{isOnDemandRevalidate:s.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:s.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(i=s.get(eJ))?void 0:i.value;this._isEnabled=!!(!n&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=r}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eJ,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:eJ,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eY(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let s=e.headers["x-middleware-set-cookie"],r=new Headers;for(let e of P(s))r.append("set-cookie",e);for(let e of new H.ResponseCookies(r).getAll())t.set(e)}}var eQ=s(802),eZ=s.n(eQ);class e0 extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}class e1{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let s=this.calculateSize(t);if(s>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,s),this.totalSize+=s,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}s(356).Buffer,new e1(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let e2=Symbol.for("@next/cache-handlers-map"),e3=Symbol.for("@next/cache-handlers-set"),e6=globalThis;function e5(){if(e6[e2])return e6[e2].entries()}async function e4(e,t){if(!e)return t();let s=e9(e);try{return await t()}finally{let t=function(e,t){let s=new Set(e.pendingRevalidatedTags),r=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!s.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!r.has(e))}}(s,e9(e));await e7(e,t)}}function e9(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function e8(e,t){if(0===e.length)return;let s=[];t&&s.push(t.revalidateTag(e));let r=function(){if(e6[e3])return e6[e3].values()}();if(r)for(let t of r)s.push(t.expireTags(...e));await Promise.all(s)}async function e7(e,t){let s=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],r=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([e8(s,e.incrementalCache),...Object.values(r),...i])}let te=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class tt{disable(){throw te}getStore(){}run(){throw te}exit(){throw te}enterWith(){throw te}static bind(e){return e}}let ts="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,tr=ts?new ts:new tt;class ti{constructor({waitUntil:e,onClose:t,onTaskError:s}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=s,this.callbackQueue=new(eZ()),this.callbackQueue.pause()}after(e){if(ej(e))this.waitUntil||tn(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||tn();let s=ep.getStore();s&&this.workUnitStores.add(s);let r=tr.getStore(),i=r?r.rootTaskSpawnPhase:null==s?void 0:s.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let n=(t=async()=>{try{await tr.run({rootTaskSpawnPhase:i},()=>e())}catch(e){this.reportTaskError("function",e)}},ts?ts.bind(t):tt.bind(t));this.callbackQueue.add(n)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=ed.getStore();if(!e)throw Object.defineProperty(new e0("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return e4(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new e0("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function tn(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function ta(e){let t,s={then:(r,i)=>(t||(t=e()),t.then(e=>{s.value=e}).catch(()=>{}),t.then(r,i))};return s}class to{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function tl(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let tc=Symbol.for("@next/request-context"),th=e=>{let t=["/layout"];if(e.startsWith("/")){let s=e.split("/");for(let e=1;e<s.length+1;e++){let r=s.slice(0,e).join("/");r&&(r.endsWith("/page")||r.endsWith("/route")||(r=`${r}${!r.endsWith("/")?"/":""}layout`),t.push(r))}}return t};async function tu(e,t,s){let r=[],i=s&&s.size>0;for(let t of th(e))t=`${T}${t}`,r.push(t);if(t.pathname&&!i){let e=`${T}${t.pathname}`;r.push(e)}return{tags:r,expirationsByCacheKind:function(e){let t=new Map,s=e5();if(s)for(let[r,i]of s)"getExpiration"in i&&t.set(r,ta(async()=>i.getExpiration(...e)));return t}(r)}}class td extends X{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new O({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new O({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new O({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let tp={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},tf=(e,t)=>eH().withPropagatedContext(e.headers,t,tp),tm=!1;async function tg(e){var t;let r,i;if(!tm&&(tm=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=s(905);e(),tf=t(tf)}await x();let n=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new K(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),s=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(s){for(let e of(a.searchParams.delete(s),t))a.searchParams.append(s,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[s,r]of Object.entries(e))for(let e of Array.isArray(r)?r:[r])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(s,e));return t}(e.request.headers),c=l.has("x-nextjs-data"),h="1"===l.get("RSC");c&&"/index"===a.pathname&&(a.pathname="/");let u=new Map;if(!n)for(let e of ei){let t=e.toLowerCase(),s=l.get(t);null!==s&&(u.set(t,s),l.delete(t))}let d=new td({page:e.page,input:(function(e){let t="string"==typeof e,s=t?new URL(e):e;return s.searchParams.delete(en),t?s.toString():s})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});c&&Object.defineProperty(d,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:tl()})}));let p=e.request.waitUntil??(null==(t=function(){let e=globalThis[tc];return null==e?void 0:e.get()}())?void 0:t.waitUntil),f=new M({request:d,page:e.page,context:p?{waitUntil:p}:void 0});if((r=await tf(d,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=f.waitUntil.bind(f),s=new to;return eH().trace(eC.execute,{spanName:`middleware ${d.method} ${d.nextUrl.pathname}`,attributes:{"http.target":d.nextUrl.pathname,"http.method":d.method}},async()=>{try{var r,n,a,l,c,h;let u=tl(),p=await tu("/",d.nextUrl,null),m=(c=d.nextUrl,h=e=>{i=e},function(e,t,s,r,i,n,a,o,l,c,h){function u(e){s&&s.setHeader("Set-Cookie",e)}let d={};return{type:"request",phase:e,implicitTags:n,url:{pathname:r.pathname,search:r.search??""},rootParams:i,get headers(){return d.headers||(d.headers=function(e){let t=eo.from(e);for(let e of ei)t.delete(e.toLowerCase());return eo.seal(t)}(t.headers)),d.headers},get cookies(){if(!d.cookies){let e=new H.RequestCookies(eo.from(t.headers));eY(t,e),d.cookies=em.seal(e)}return d.cookies},set cookies(value){d.cookies=value},get mutableCookies(){if(!d.mutableCookies){let e=function(e,t){let s=new H.RequestCookies(eo.from(e));return ew.wrap(s,t)}(t.headers,a||(s?u:void 0));eY(t,e),d.mutableCookies=e}return d.mutableCookies},get userspaceMutableCookies(){return d.userspaceMutableCookies||(d.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,s,r){switch(s){case"delete":return function(...s){return eb("cookies().delete"),e.delete(...s),t};case"set":return function(...s){return eb("cookies().set"),e.set(...s),t};default:return Y.get(e,s,r)}}});return t}(this.mutableCookies)),d.userspaceMutableCookies},get draftMode(){return d.draftMode||(d.draftMode=new eX(l,t,this.cookies,this.mutableCookies)),d.draftMode},renderResumeDataCache:o??null,isHmrRefresh:c,serverComponentsHmrCache:h||globalThis.__serverComponentsHmrCache}}("action",d,void 0,c,{},p,h,void 0,u,!1,void 0)),g=function({page:e,fallbackRouteParams:t,renderOpts:s,requestEndedState:r,isPrefetchRequest:i,buildId:n,previouslyRevalidatedTags:a}){var o;let l={isStaticGeneration:!s.shouldWaitOnAllReady&&!s.supportsDynamicResponse&&!s.isDraftMode&&!s.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(o=e.split("/").reduce((e,t,s,r)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&s===r.length-1?e:e+"/"+t:e,"")).startsWith("/")?o:"/"+o,incrementalCache:s.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:s.cacheLifeProfiles,isRevalidate:s.isRevalidate,isPrerendering:s.nextExport,fetchCache:s.fetchCache,isOnDemandRevalidate:s.isOnDemandRevalidate,isDraftMode:s.isDraftMode,requestEndedState:r,isPrefetchRequest:i,buildId:n,reactLoadableManifest:(null==s?void 0:s.reactLoadableManifest)||{},assetPrefix:(null==s?void 0:s.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:s,onAfterTaskError:r}=e;return new ti({waitUntil:t,onClose:s,onTaskError:r})}(s),dynamicIOEnabled:s.experimental.dynamicIO,dev:s.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=e5();if(t)for(let[s,r]of t)"refreshTags"in r&&e.set(s,ta(async()=>r.refreshTags()));return e}()};return s.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(n=e.request.nextConfig)||null==(r=n.experimental)?void 0:r.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:s.onClose.bind(s),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:d.headers.has(er),buildId:o??"",previouslyRevalidatedTags:[]});return await ed.run(g,()=>ep.run(m,e.handler,d,f))}finally{setTimeout(()=>{s.dispatchClose()},0)}})}return e.handler(d,f)}))&&!(r instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});r&&i&&r.headers.set("set-cookie",i);let m=null==r?void 0:r.headers.get("x-middleware-rewrite");if(r&&m&&(h||!n)){let t=new K(m,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});n||t.host!==d.nextUrl.host||(t.buildId=o||t.buildId,r.headers.set("x-middleware-rewrite",String(t)));let{url:s,isRelative:i}=es(t.toString(),a.toString());!n&&c&&r.headers.set("x-nextjs-rewrite",s),h&&i&&(a.pathname!==t.pathname&&r.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&r.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let g=null==r?void 0:r.headers.get("Location");if(r&&g&&!n){let t=new K(g,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});r=new Response(r.body,r),t.host===a.host&&(t.buildId=o||t.buildId,r.headers.set("Location",t.toString())),c&&(r.headers.delete("Location"),r.headers.set("x-nextjs-redirect",es(t.toString(),a.toString()).url))}let w=r||et.next(),b=w.headers.get("x-middleware-override-headers"),y=[];if(b){for(let[e,t]of u)w.headers.set(`x-middleware-request-${e}`,t),y.push(e);y.length>0&&w.headers.set("x-middleware-override-headers",b+","+y.join(","))}return{response:w,waitUntil:("internal"===f[N].kind?Promise.all(f[N].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:d.fetchMetrics}}s(280),"undefined"==typeof URLPattern||URLPattern;var tw=s(815);new WeakMap;let tb="function"==typeof tw.unstable_postpone;function ty(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(ty("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`),new WeakMap;var tv=s(554);function t_(){return"undefined"!=typeof window&&void 0!==window.document}let tx={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},tS=/^(.*)[.](0|[1-9][0-9]*)$/;function tO(e,t){if(e===t)return!0;let s=e.match(tS);return!!s&&s[1]===t}function tE(e,t,s){let r=s??3180,i=encodeURIComponent(t);if(i.length<=r)return[{name:e,value:t}];let n=[];for(;i.length>0;){let e=i.slice(0,r),t=e.lastIndexOf("%");t>r-3&&(e=e.slice(0,t));let s="";for(;e.length>0;)try{s=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}n.push(s),i=i.slice(e.length)}return n.map((t,s)=>({name:`${e}.${s}`,value:t}))}async function tk(e,t){let s=await t(e);if(s)return s;let r=[];for(let s=0;;s++){let i=`${e}.${s}`,n=await t(i);if(!n)break;r.push(n)}return r.length>0?r.join(""):null}let tT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),tR=" 	\n\r=".split(""),tP=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<tR.length;t+=1)e[tR[t].charCodeAt(0)]=-2;for(let t=0;t<tT.length;t+=1)e[tT[t].charCodeAt(0)]=t;return e})();function tC(e){let t=[],s=0,r=0;if(function(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){let t=(r-55296)*1024&65535;r=(e.charCodeAt(s+1)-56320&65535|t)+65536,s+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(r,t)}}(e,e=>{for(s=s<<8|e,r+=8;r>=6;){let e=s>>r-6&63;t.push(tT[e]),r-=6}}),r>0)for(s<<=6-r,r=6;r>=6;){let e=s>>r-6&63;t.push(tT[e]),r-=6}return t.join("")}function tA(e){let t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},i=0,n=0;for(let t=0;t<e.length;t+=1){let a=tP[e.charCodeAt(t)];if(a>-1)for(i=i<<6|a,n+=6;n>=8;)(function(e,t,s){if(0===t.utf8seq){if(e<=127)return s(e);for(let s=1;s<6;s+=1)if((e>>7-s&1)==0){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}})(i>>n-8&255,r,s),n-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}let tI="base64-";async function tj({getAll:e,setAll:t,setItems:s,removedItems:r},i){let n=i.cookieEncoding,a=i.cookieOptions??null,o=await e([...s?Object.keys(s):[],...r?Object.keys(r):[]]),l=o?.map(({name:e})=>e)||[],c=Object.keys(r).flatMap(e=>l.filter(t=>tO(t,e))),h=Object.keys(s).flatMap(e=>{let t=new Set(l.filter(t=>tO(t,e))),r=s[e];"base64url"===n&&(r=tI+tC(r));let i=tE(e,r);return i.forEach(e=>{t.delete(e.name)}),c.push(...t),i}),u={...tx,...a,maxAge:0},d={...tx,...a,maxAge:tx.maxAge};delete u.name,delete d.name,await t([...c.map(e=>({name:e,value:"",options:u})),...h.map(({name:e,value:t})=>({name:e,value:t,options:d}))])}let tN=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,3)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};class tL extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class tM extends tL{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class tD extends tL{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class tU extends tL{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(n||(n={}));class t${constructor(e,{headers:t={},customFetch:s,region:r=n.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=tN(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s,r,i,n,a;return r=this,i=void 0,n=void 0,a=function*(){try{let r,i,{headers:n,method:a,body:o}=t,l={},{region:c}=t;c||(c=this.region),c&&"any"!==c&&(l["x-region"]=c),o&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",r=o):"string"==typeof o?(l["Content-Type"]="text/plain",r=o):"undefined"!=typeof FormData&&o instanceof FormData?r=o:(l["Content-Type"]="application/json",r=JSON.stringify(o)));let h=yield this.fetch(`${this.url}/${e}`,{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),n),body:r}).catch(e=>{throw new tM(e)}),u=h.headers.get("x-relay-error");if(u&&"true"===u)throw new tD(h);if(!h.ok)throw new tU(h);let d=(null!=(s=h.headers.get("Content-Type"))?s:"text/plain").split(";")[0].trim();return{data:"application/json"===d?yield h.json():"application/octet-stream"===d?yield h.blob():"text/event-stream"===d?h:"multipart/form-data"===d?yield h.formData():yield h.text(),error:null}}catch(e){return{data:null,error:e}}},new(n||(n=Promise))(function(e,t){function s(e){try{l(a.next(e))}catch(e){t(e)}}function o(e){try{l(a.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof n?r:new n(function(e){e(r)})).then(s,o)}l((a=a.apply(r,i||[])).next())})}}let{PostgrestClient:tq,PostgrestQueryBuilder:tB,PostgrestFilterBuilder:tz,PostgrestTransformBuilder:tW,PostgrestBuilder:tG,PostgrestError:tV}=s(355),tF="undefined"==typeof window?s(999):window.WebSocket,tK={"X-Client-Info":"realtime-js/2.11.10"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(a||(a={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(o||(o={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(l||(l={})),(c||(c={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(h||(h={}));class tH{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){let r=t.getUint8(1),i=t.getUint8(2),n=this.HEADER_LENGTH+2,a=s.decode(e.slice(n,n+r));n+=r;let o=s.decode(e.slice(n,n+i));return n+=i,{ref:null,topic:a,event:o,payload:JSON.parse(s.decode(e.slice(n,e.byteLength)))}}}class tJ{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(u||(u={}));let tX=(e,t,s={})=>{var r;let i=null!=(r=s.skipTypes)?r:[];return Object.keys(t).reduce((s,r)=>(s[r]=tY(r,e,t,i),s),{})},tY=(e,t,s,r)=>{let i=t.find(t=>t.name===e),n=null==i?void 0:i.type,a=s[e];return n&&!r.includes(n)?tQ(n,a):tZ(a)},tQ=(e,t)=>{if("_"===e.charAt(0))return t3(t,e.slice(1,e.length));switch(e){case u.bool:return t0(t);case u.float4:case u.float8:case u.int2:case u.int4:case u.int8:case u.numeric:case u.oid:return t1(t);case u.json:case u.jsonb:return t2(t);case u.timestamp:return t6(t);case u.abstime:case u.date:case u.daterange:case u.int4range:case u.int8range:case u.money:case u.reltime:case u.text:case u.time:case u.timestamptz:case u.timetz:case u.tsrange:case u.tstzrange:default:return tZ(t)}},tZ=e=>e,t0=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},t1=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},t2=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},t3=(e,t)=>{if("string"!=typeof e)return e;let s=e.length-1,r=e[s];if("{"===e[0]&&"}"===r){let r,i=e.slice(1,s);try{r=JSON.parse("["+i+"]")}catch(e){r=i?i.split(","):[]}return r.map(e=>tQ(t,e))}return e},t6=e=>"string"==typeof e?e.replace(" ","T"):e,t5=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class t4{constructor(e,t,s={},r=1e4){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null==(s=this.receivedResp)?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(d||(d={}));class t9{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let s=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},e=>{let{onJoin:t,onLeave:s,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=t9.syncState(this.state,e,t,s),this.pendingDiffs.forEach(e=>{this.state=t9.syncDiff(this.state,e,t,s)}),this.pendingDiffs=[],r()}),this.channel._on(s.diff,{},e=>{let{onJoin:t,onLeave:s,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=t9.syncDiff(this.state,e,t,s),r())}),this.onJoin((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})}),this.onLeave((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){let i=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(i,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let s=i[e];if(s){let r=t.map(e=>e.presence_ref),i=s.map(e=>e.presence_ref),n=t.filter(e=>0>i.indexOf(e.presence_ref)),l=s.filter(e=>0>r.indexOf(e.presence_ref));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(i,{joins:a,leaves:o},s,r)}static syncDiff(e,t,s,r){let{joins:i,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(i,(t,r)=>{var i;let n=null!=(i=e[t])?i:[];if(e[t]=this.cloneDeep(r),n.length>0){let s=e[t].map(e=>e.presence_ref),r=n.filter(e=>0>s.indexOf(e.presence_ref));e[t].unshift(...r)}s(t,n,r)}),this.map(n,(t,s)=>{let i=e[t];if(!i)return;let n=s.map(e=>e.presence_ref);i=i.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=i,r(t,i,s),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,s)=>{let r=e[s];return"metas"in r?t[s]=r.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[s]=r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(p||(p={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(f||(f={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(m||(m={}));class t8{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=o.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new t4(this,l.join,this.params,this.timeout),this.rejoinTimer=new tJ(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=o.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=o.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=o.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=o.errored,this.rejoinTimer.scheduleTimeout())}),this._on(l.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new t9(this),this.broadcastEndpointURL=t5(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:i,presence:n,private:a}}=this.params;this._onError(t=>null==e?void 0:e(m.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(m.CLOSED));let l={},c={broadcast:i,presence:n,postgres_changes:null!=(r=null==(s=this.bindings.postgres_changes)?void 0:s.map(e=>e.filter))?r:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var s;if(this.socket.setAuth(),void 0===t){null==e||e(m.SUBSCRIBED);return}{let r=this.bindings.postgres_changes,i=null!=(s=null==r?void 0:r.length)?s:0,n=[];for(let s=0;s<i;s++){let i=r[s],{filter:{event:a,schema:l,table:c,filter:h}}=i,u=t&&t[s];if(u&&u.event===a&&u.schema===l&&u.table===c&&u.filter===h)n.push(Object.assign(Object.assign({},i),{id:u.id}));else{this.unsubscribe(),this.state=o.errored,null==e||e(m.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(m.SUBSCRIBED);return}}).receive("error",t=>{this.state=o.errored,null==e||e(m.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(m.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,r;if(this._canPush()||"broadcast"!==e.type)return new Promise(s=>{var r,i,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(n=null==(i=null==(r=this.params)?void 0:r.config)?void 0:i.broadcast)?void 0:n.ack)||s("ok"),a.receive("ok",()=>s("ok")),a.receive("error",()=>s("error")),a.receive("timeout",()=>s("timed out"))});{let{event:i,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!=(s=t.timeout)?s:this.timeout);return await (null==(r=e.body)?void 0:r.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=o.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(l.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(s=>{let r=new t4(this,l.leave,{},e);r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,s){let r=new AbortController,i=setTimeout(()=>r.abort(),s),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(i),n}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new t4(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,i;let n=e.toLocaleLowerCase(),{close:a,error:o,leave:c,join:h}=l;if(s&&[a,o,c,h].indexOf(n)>=0&&s!==this._joinRef())return;let u=this._onMessage(n,t,s);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null==(r=this.bindings.postgres_changes)||r.filter(e=>{var t,s,r;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(r=null==(s=e.filter)?void 0:s.event)?void 0:r.toLocaleLowerCase())===n}).map(e=>e.callback(u,s)):null==(i=this.bindings[n])||i.filter(e=>{var s,r,i,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,a=null==(s=e.filter)?void 0:s.event;return n&&(null==(r=t.ids)?void 0:r.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null==(i=t.data)?void 0:i.type.toLocaleLowerCase()))}{let s=null==(o=null==(a=null==e?void 0:e.filter)?void 0:a.event)?void 0:o.toLocaleLowerCase();return"*"===s||s===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof u&&"ids"in u){let e=u.data,{schema:t,table:s,commit_timestamp:r,type:i,errors:n}=e;u=Object.assign(Object.assign({},{schema:t,table:s,commit_timestamp:r,eventType:i,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(u,s)})}_isClosed(){return this.state===o.closed}_isJoined(){return this.state===o.joined}_isJoining(){return this.state===o.joining}_isLeaving(){return this.state===o.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){let r=e.toLocaleLowerCase(),i={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(i):this.bindings[r]=[i],this}_off(e,t){let s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(e=>{var r;return!((null==(r=e.type)?void 0:r.toLocaleLowerCase())===s&&t8.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(l.close,{},e)}_onError(e){this._on(l.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=o.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=tX(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=tX(e.columns,e.old_record)),t}}let t7=()=>{},se=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class st{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=tK,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=t7,this.ref=0,this.logger=t7,this.conn=null,this.sendBuffer=[],this.serializer=new tH,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,3)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${c.websocket}`,this.httpEndpoint=t5(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let i=null==(r=null==t?void 0:t.params)?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new tJ(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=tF),this.transport){"undefined"!=typeof window&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new ss(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return this.channels=this.channels.filter(t=>t._joinRef!==e._joinRef),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case a.connecting:return h.Connecting;case a.open:return h.Open;case a.closing:return h.Closing;default:return h.Closed}}isConnected(){return this.connectionState()===h.Open}channel(e,t={config:{}}){let s=`realtime:${e}`,r=this.getChannels().find(e=>e.topic===s);if(r)return r;{let s=new t8(`realtime:${e}`,t,this);return this.channels.push(s),s}}push(e){let{topic:t,event:s,payload:r,ref:i}=e,n=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${s} (${i})`,r),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(l.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:s,payload:r,ref:i}=e;"phoenix"===t&&"phx_reply"===s&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${s} ${i&&"("+i+")"||""}`,r),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(s,r,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(l.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return`${e}${s}${r}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([se],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class ss{constructor(e,t,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=a.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=s.close}}class sr extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function si(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class sn extends sr{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class sa extends sr{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let so=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,3)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},sl=()=>(function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(s.bind(s,3))).Response:Response}),sc=e=>{if(Array.isArray(e))return e.map(e=>sc(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,s])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=sc(s)}),t};var sh=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};let su=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),sd=(e,t,s)=>sh(void 0,void 0,void 0,function*(){e instanceof(yield sl())&&!(null==s?void 0:s.noResolveJson)?e.json().then(s=>{t(new sn(su(s),e.status||500))}).catch(e=>{t(new sa(su(e),e))}):t(new sa(su(e),e))}),sp=(e,t,s,r)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),s))};function sf(e,t,s,r,i,n){return sh(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(s,sp(t,r,i,n)).then(e=>{if(!e.ok)throw e;return(null==r?void 0:r.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>sd(e,o,r))})})}function sm(e,t,s,r){return sh(this,void 0,void 0,function*(){return sf(e,"GET",t,s,r)})}function sg(e,t,s,r,i){return sh(this,void 0,void 0,function*(){return sf(e,"POST",t,r,i,s)})}function sw(e,t,s,r,i){return sh(this,void 0,void 0,function*(){return sf(e,"DELETE",t,r,i,s)})}var sb=s(356).Buffer,sy=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};let sv={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},s_={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class sx{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=so(r)}uploadOrUpdate(e,t,s,r){return sy(this,void 0,void 0,function*(){try{let i,n=Object.assign(Object.assign({},s_),r),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),o=n.metadata;"undefined"!=typeof Blob&&s instanceof Blob?((i=new FormData).append("cacheControl",n.cacheControl),o&&i.append("metadata",this.encodeMetadata(o)),i.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?((i=s).append("cacheControl",n.cacheControl),o&&i.append("metadata",this.encodeMetadata(o))):(i=s,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==r?void 0:r.headers)&&(a=Object.assign(Object.assign({},a),r.headers));let l=this._removeEmptyFolders(t),c=this._getFinalPath(l),h=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:i,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),u=yield h.json();if(h.ok)return{data:{path:l,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(e){if(si(e))return{data:null,error:e};throw e}})}upload(e,t,s){return sy(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return sy(this,void 0,void 0,function*(){let i=this._removeEmptyFolders(e),n=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e,t=Object.assign({upsert:s_.upsert},r),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&s instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(e=s).append("cacheControl",t.cacheControl):(e=s,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),l=yield o.json();if(o.ok)return{data:{path:i,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(si(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return sy(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");let i=yield sg(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),n=new URL(this.url+i.url),a=n.searchParams.get("token");if(!a)throw new sr("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}update(e,t,s){return sy(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return sy(this,void 0,void 0,function*(){try{return{data:yield sg(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}copy(e,t,s){return sy(this,void 0,void 0,function*(){try{return{data:{path:(yield sg(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,s){return sy(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=yield sg(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers}),n=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:i={signedUrl:encodeURI(`${this.url}${i.signedURL}${n}`)},error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,s){return sy(this,void 0,void 0,function*(){try{let r=yield sg(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:r.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}download(e,t){return sy(this,void 0,void 0,function*(){let s=void 0!==(null==t?void 0:t.transform),r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=r?`?${r}`:"";try{let t=this._getFinalPath(e),r=yield sm(this.fetch,`${this.url}/${s?"render/image/authenticated":"object"}/${t}${i}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}info(e){return sy(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield sm(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:sc(e),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}exists(e){return sy(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,s,r){return sh(this,void 0,void 0,function*(){return sf(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(si(e)&&e instanceof sa){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let s=this._getFinalPath(e),r=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&r.push(i);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&r.push(a);let o=r.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${s}${o}`)}}}remove(e){return sy(this,void 0,void 0,function*(){try{return{data:yield sw(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}list(e,t,s){return sy(this,void 0,void 0,function*(){try{let r=Object.assign(Object.assign(Object.assign({},sv),t),{prefix:e||""});return{data:yield sg(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==sb?sb.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let sS={"X-Client-Info":"storage-js/2.7.1"};var sO=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};class sE{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},sS),t),this.fetch=so(s)}listBuckets(){return sO(this,void 0,void 0,function*(){try{return{data:yield sm(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}getBucket(e){return sO(this,void 0,void 0,function*(){try{return{data:yield sm(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return sO(this,void 0,void 0,function*(){try{return{data:yield sg(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return sO(this,void 0,void 0,function*(){try{return{data:yield function(e,t,s,r,i){return sh(this,void 0,void 0,function*(){return sf(e,"PUT",t,r,void 0,s)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}emptyBucket(e){return sO(this,void 0,void 0,function*(){try{return{data:yield sg(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}deleteBucket(e){return sO(this,void 0,void 0,function*(){try{return{data:yield sw(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(si(e))return{data:null,error:e};throw e}})}}class sk extends sE{constructor(e,t={},s){super(e,t,s)}from(e){return new sx(this.url,this.headers,e,this.fetch)}}let sT="";sT="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let sR={headers:{"X-Client-Info":`supabase-js-${sT}/2.50.0`}},sP={schema:"public"},sC={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},sA={};var sI=s(3);let sj=e=>{let t;return t=e||("undefined"==typeof fetch?sI.default:fetch),(...e)=>t(...e)},sN=()=>"undefined"==typeof Headers?sI.Headers:Headers,sL=(e,t,s)=>{let r=sj(s),i=sN();return(s,n)=>(function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!=(a=yield t())?a:e,l=new i(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),r(s,Object.assign(Object.assign({},n),{headers:l}))})},sM="2.70.0",sD={"X-Client-Info":`gotrue-js/${sM}`},sU="X-Supabase-Api-Version",s$={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},sq=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class sB extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function sz(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class sW extends sB{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}class sG extends sB{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class sV extends sB{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class sF extends sV{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class sK extends sV{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class sH extends sV{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class sJ extends sV{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class sX extends sV{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class sY extends sV{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function sQ(e){return sz(e)&&"AuthRetryableFetchError"===e.name}class sZ extends sV{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class s0 extends sV{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let s1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),s2=" 	\n\r=".split(""),s3=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<s2.length;t+=1)e[s2[t].charCodeAt(0)]=-2;for(let t=0;t<s1.length;t+=1)e[s1[t].charCodeAt(0)]=t;return e})();function s6(e,t,s){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)s(s1[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)s(s1[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function s5(e,t,s){let r=s3[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===r)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function s4(e){let t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},n=e=>{!function(e,t,s){if(0===t.utf8seq){if(e<=127)return s(e);for(let s=1;s<6;s+=1)if((e>>7-s&1)==0){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}(e,r,s)};for(let t=0;t<e.length;t+=1)s5(e.charCodeAt(t),i,n);return t.join("")}let s9=()=>"undefined"!=typeof window&&"undefined"!=typeof document,s8={tested:!1,writable:!1},s7=()=>{if(!s9())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(s8.tested)return s8.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),s8.tested=!0,s8.writable=!0}catch(e){s8.tested=!0,s8.writable=!1}return s8.writable},re=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,3)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},rt=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,rs=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},rr=async(e,t)=>{let s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch(e){return s}},ri=async(e,t)=>{await e.removeItem(t)};class rn{constructor(){this.promise=new rn.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function ra(e){let t=e.split(".");if(3!==t.length)throw new s0("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!sq.test(t[e]))throw new s0("JWT not in base64url format");return{header:JSON.parse(s4(t[0])),payload:JSON.parse(s4(t[1])),signature:function(e){let t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)s5(e.charCodeAt(t),s,r);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function ro(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function rl(e){return("0"+e.toString(16)).substr(-2)}async function rc(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function rh(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await rc(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function ru(e,t,s=!1){let r=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,s="";for(let r=0;r<56;r++)s+=e.charAt(Math.floor(Math.random()*t));return s}return crypto.getRandomValues(e),Array.from(e,rl).join("")}(),i=r;s&&(i+="/PASSWORD_RECOVERY"),await rs(e,`${t}-code-verifier`,i);let n=await rh(r),a=r===n?"plain":"s256";return[n,a]}rn.promiseConstructor=Promise;let rd=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,rp=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function rf(e){if(!rp.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var rm=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]]);return s};let rg=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),rw=[502,503,504];async function rb(e){var t;let s,r;if(!rt(e))throw new sY(rg(e),0);if(rw.includes(e.status))throw new sY(rg(e),e.status);try{s=await e.json()}catch(e){throw new sG(rg(e),e)}let i=function(e){let t=e.headers.get(sU);if(!t||!t.match(rd))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(i&&i.getTime()>=s$["2024-01-01"].timestamp&&"object"==typeof s&&s&&"string"==typeof s.code?r=s.code:"object"==typeof s&&s&&"string"==typeof s.error_code&&(r=s.error_code),r){if("weak_password"===r)throw new sZ(rg(s),e.status,(null==(t=s.weak_password)?void 0:t.reasons)||[]);else if("session_not_found"===r)throw new sF}else if("object"==typeof s&&s&&"object"==typeof s.weak_password&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new sZ(rg(s),e.status,s.weak_password.reasons);throw new sW(rg(s),e.status||500,r)}let ry=(e,t,s,r)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),s))};async function rv(e,t,s,r){var i;let n=Object.assign({},null==r?void 0:r.headers);n[sU]||(n[sU]=s$["2024-01-01"].name),(null==r?void 0:r.jwt)&&(n.Authorization=`Bearer ${r.jwt}`);let a=null!=(i=null==r?void 0:r.query)?i:{};(null==r?void 0:r.redirectTo)&&(a.redirect_to=r.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await r_(e,t,s+o,{headers:n,noResolveJson:null==r?void 0:r.noResolveJson},{},null==r?void 0:r.body);return(null==r?void 0:r.xform)?null==r?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function r_(e,t,s,r,i,n){let a,o=ry(t,r,i,n);try{a=await e(s,Object.assign({},o))}catch(e){throw console.error(e),new sY(rg(e),0)}if(a.ok||await rb(a),null==r?void 0:r.noResolveJson)return a;try{return await a.json()}catch(e){await rb(e)}}function rx(e){var t,s,r;let i=null;(r=e).access_token&&r.refresh_token&&r.expires_in&&(i=Object.assign({},e),e.expires_at||(i.expires_at=(s=e.expires_in,Math.round(Date.now()/1e3)+s)));return{data:{session:i,user:null!=(t=e.user)?t:e},error:null}}function rS(e){let t=rx(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function rO(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function rE(e){return{data:e,error:null}}function rk(e){let{action_link:t,email_otp:s,hashed_token:r,redirect_to:i,verification_type:n}=e;return{data:{properties:{action_link:t,email_otp:s,hashed_token:r,redirect_to:i,verification_type:n},user:Object.assign({},rm(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function rT(e){return e}let rR=["global","local","others"];var rP=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]]);return s};class rC{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=re(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=rR[0]){if(0>rR.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${rR.join(", ")}`);try{return await rv(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(sz(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await rv(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:rO})}catch(e){if(sz(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,s=rP(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=null==s?void 0:s.newEmail,delete r.newEmail),await rv(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:rk,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(sz(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await rv(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:rO})}catch(e){if(sz(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,s,r,i,n,a,o;try{let l={nextPage:null,lastPage:0,total:0},c=await rv(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(s=null==(t=null==e?void 0:e.page)?void 0:t.toString())?s:"",per_page:null!=(i=null==(r=null==e?void 0:e.perPage)?void 0:r.toString())?i:""},xform:rT});if(c.error)throw c.error;let h=await c.json(),u=null!=(n=c.headers.get("x-total-count"))?n:0,d=null!=(o=null==(a=c.headers.get("link"))?void 0:a.split(","))?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(e.split(";")[1].split("=")[1]);l[`${s}Page`]=t}),l.total=parseInt(u)),{data:Object.assign(Object.assign({},h),l),error:null}}catch(e){if(sz(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){rf(e);try{return await rv(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:rO})}catch(e){if(sz(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){rf(e);try{return await rv(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:rO})}catch(e){if(sz(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){rf(e);try{return await rv(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:rO})}catch(e){if(sz(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){rf(e.userId);try{let{data:t,error:s}=await rv(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:s}}catch(e){if(sz(e))return{data:null,error:e};throw e}}async _deleteFactor(e){rf(e.userId),rf(e.id);try{return{data:await rv(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(sz(e))return{data:null,error:e};throw e}}}let rA={getItem:e=>s7()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{s7()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{s7()&&globalThis.localStorage.removeItem(e)}};function rI(e={}){return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}let rj={debug:!!(globalThis&&s7()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class rN extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class rL extends rN{}async function rM(e,t,s){rj.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),rj.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async r=>{if(r){rj.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,r.name);try{return await s()}finally{rj.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,r.name)}}if(0===t)throw rj.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new rL(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(rj.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await s()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}let rD={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:sD,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function rU(e,t,s){return await s()}class r${constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=r$.nextInstanceID,r$.nextInstanceID+=1,this.instanceID>0&&s9()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let r=Object.assign(Object.assign({},rD),e);if(this.logDebugMessages=!!r.debug,"function"==typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new rC({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=re(r.fetch),this.lock=r.lock||rU,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:s9()&&(null==(t=null==globalThis?void 0:globalThis.navigator)?void 0:t.locks)?this.lock=rM:this.lock=rU,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:s7()?this.storage=rA:(this.memoryStorage={},this.storage=rI(this.memoryStorage)):(this.memoryStorage={},this.storage=rI(this.memoryStorage)),s9()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(s=this.broadcastChannel)||s.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${sM}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},s=new URL(e);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach((e,s)=>{t[s]=e})}catch(e){}return s.searchParams.forEach((e,s)=>{t[s]=e}),t}(window.location.href),s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),s9()&&this.detectSessionInUrl&&"none"!==s){let{data:r,error:i}=await this._getSessionFromURL(t,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),sz(i)&&"AuthImplicitGrantRedirectError"===i.name){let t=null==(e=i.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}let{session:n,redirectType:a}=r;return this._debug("#_initialize()","detected session in URL",n,"redirect type",a),await this._saveSession(n),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(sz(e))return{error:e};return{error:new sG("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,r;try{let{data:i,error:n}=await rv(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(s=null==(t=null==e?void 0:e.options)?void 0:t.data)?s:{},gotrue_meta_security:{captcha_token:null==(r=null==e?void 0:e.options)?void 0:r.captchaToken}},xform:rx});if(n||!i)return{data:{user:null,session:null},error:n};let a=i.session,o=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,s,r;try{let i;if("email"in e){let{email:s,password:r,options:n}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await ru(this.storage,this.storageKey)),i=await rv(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:s,password:r,data:null!=(t=null==n?void 0:n.data)?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:a,code_challenge_method:o},xform:rx})}else if("phone"in e){let{phone:t,password:n,options:a}=e;i=await rv(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!=(s=null==a?void 0:a.data)?s:{},channel:null!=(r=null==a?void 0:a.channel)?r:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:rx})}else throw new sH("You must provide either an email or phone number and a password");let{data:n,error:a}=i;if(a||!n)return{data:{user:null,session:null},error:a};let o=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:s,password:r,options:i}=e;t=await rv(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:rS})}else if("phone"in e){let{phone:s,password:r,options:i}=e;t=await rv(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:rS})}else throw new sH("You must provide either an email or phone number and a password");let{data:s,error:r}=t;if(r)return{data:{user:null,session:null},error:r};if(!s||!s.session||!s.user)return{data:{user:null,session:null},error:new sK};return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r}}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,s,r,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(s=e.options)?void 0:s.scopes,queryParams:null==(r=e.options)?void 0:r.queryParams,skipBrowserRedirect:null==(i=e.options)?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,s,r,i,n,a,o,l,c,h,u,d;let p,f;if("message"in e)p=e.message,f=e.signature;else{let u,{chain:d,wallet:m,statement:g,options:w}=e;if(s9())if("object"==typeof m)u=m;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))u=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if("object"!=typeof m||!(null==w?void 0:w.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");u=m}let b=new URL(null!=(t=null==w?void 0:w.url)?t:window.location.href);if("signIn"in u&&u.signIn){let e,t=await u.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==w?void 0:w.signInWithSolana),{version:"1",domain:b.host,uri:b.href}),g?{statement:g}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)p="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),f=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in u)||"function"!=typeof u.signMessage||!("publicKey"in u)||"object"!=typeof u||!u.publicKey||!("toBase58"in u.publicKey)||"function"!=typeof u.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");p=[`${b.host} wants you to sign in with your Solana account:`,u.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1",`URI: ${b.href}`,`Issued At: ${null!=(r=null==(s=null==w?void 0:w.signInWithSolana)?void 0:s.issuedAt)?r:new Date().toISOString()}`,...(null==(i=null==w?void 0:w.signInWithSolana)?void 0:i.notBefore)?[`Not Before: ${w.signInWithSolana.notBefore}`]:[],...(null==(n=null==w?void 0:w.signInWithSolana)?void 0:n.expirationTime)?[`Expiration Time: ${w.signInWithSolana.expirationTime}`]:[],...(null==(a=null==w?void 0:w.signInWithSolana)?void 0:a.chainId)?[`Chain ID: ${w.signInWithSolana.chainId}`]:[],...(null==(o=null==w?void 0:w.signInWithSolana)?void 0:o.nonce)?[`Nonce: ${w.signInWithSolana.nonce}`]:[],...(null==(l=null==w?void 0:w.signInWithSolana)?void 0:l.requestId)?[`Request ID: ${w.signInWithSolana.requestId}`]:[],...(null==(h=null==(c=null==w?void 0:w.signInWithSolana)?void 0:c.resources)?void 0:h.length)?["Resources",...w.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await u.signMessage(new TextEncoder().encode(p),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");f=e}}try{let{data:t,error:s}=await rv(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:p,signature:function(e){let t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};return e.forEach(e=>s6(e,s,r)),s6(null,s,r),t.join("")}(f)},(null==(u=e.options)?void 0:u.captchaToken)?{gotrue_meta_security:{captcha_token:null==(d=e.options)?void 0:d.captchaToken}}:null),xform:rx});if(s)throw s;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new sK};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:s}}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await rr(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(null!=t?t:"").split("/");try{let{data:t,error:i}=await rv(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:rx});if(await ri(this.storage,`${this.storageKey}-code-verifier`),i)throw i;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new sK};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=r?r:null}),error:i}}catch(e){if(sz(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:s,token:r,access_token:i,nonce:n}=e,{data:a,error:o}=await rv(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:i,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:rx});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new sK};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,s,r,i,n;try{if("email"in e){let{email:r,options:i}=e,n=null,a=null;"pkce"===this.flowType&&([n,a]=await ru(this.storage,this.storageKey));let{error:o}=await rv(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!=(t=null==i?void 0:i.data)?t:{},create_user:null==(s=null==i?void 0:i.shouldCreateUser)||s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:n,code_challenge_method:a},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:s}=e,{data:a,error:o}=await rv(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(r=null==s?void 0:s.data)?r:{},create_user:null==(i=null==s?void 0:s.shouldCreateUser)||i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},channel:null!=(n=null==s?void 0:s.channel)?n:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new sH("You must provide either an email or phone number.")}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,s;try{let r,i;"options"in e&&(r=null==(t=e.options)?void 0:t.redirectTo,i=null==(s=e.options)?void 0:s.captchaToken);let{data:n,error:a}=await rv(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:r,xform:rx});if(a)throw a;if(!n)throw Error("An error occurred on token verification.");let o=n.session,l=n.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,s,r;try{let i=null,n=null;return"pkce"===this.flowType&&([i,n]=await ru(this.storage,this.storageKey)),await rv(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(s=null==(t=e.options)?void 0:t.redirectTo)?s:void 0}),(null==(r=null==e?void 0:e.options)?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:n}),headers:this.headers,xform:rE})}catch(e){if(sz(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new sF;let{error:r}=await rv(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}})}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:s,type:r,options:i}=e,{error:n}=await rv(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:s,type:r,options:i}=e,{data:n,error:a}=await rv(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:a}}throw new sH("You must provide either an email or phone number and a type")}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch(e){}})()),s}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await rr(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let s=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,s,r)=>(t||"user"!==s||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,s,r))})}return{data:{session:e},error:null}}let{session:r,error:i}=await this._callRefreshToken(e.refresh_token);if(i)return{data:{session:null},error:i};return{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await rv(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:rO});return await this._useSession(async e=>{var t,s,r;let{data:i,error:n}=e;if(n)throw n;return(null==(t=i.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await rv(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(r=null==(s=i.session)?void 0:s.access_token)?r:void 0,xform:rO}):{data:{user:null},error:new sF}})}catch(e){if(sz(e))return sz(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await ri(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async s=>{let{data:r,error:i}=s;if(i)throw i;if(!r.session)throw new sF;let n=r.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await ru(this.storage,this.storageKey));let{data:l,error:c}=await rv(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:n.access_token,xform:rO});if(c)throw c;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(e){if(sz(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new sF;let t=Date.now()/1e3,s=t,r=!0,i=null,{payload:n}=ra(e.access_token);if(n.exp&&(r=(s=n.exp)<=t),r){let{session:t,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!t)return{data:{user:null,session:null},error:null};i=t}else{let{data:r,error:n}=await this._getUser(e.access_token);if(n)throw n;i={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if(sz(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var s;if(!e){let{data:r,error:i}=t;if(i)throw i;e=null!=(s=r.session)?s:void 0}if(!(null==e?void 0:e.refresh_token))throw new sF;let{session:r,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(sz(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!s9())throw new sJ("No browser detected.");if(e.error||e.error_description||e.error_code)throw new sJ(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new sX("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new sJ("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new sX("No code detected.");let{data:t,error:s}=await this._exchangeCodeForSession(e.code);if(s)throw s;let r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:s,provider_refresh_token:r,access_token:i,refresh_token:n,expires_in:a,expires_at:o,token_type:l}=e;if(!i||!a||!n||!l)throw new sJ("No session defined in URL");let c=Math.round(Date.now()/1e3),h=parseInt(a),u=c+h;o&&(u=parseInt(o));let d=u-c;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${h}s`);let p=u-h;c-p>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",p,u,c):c-p<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",p,u,c);let{data:f,error:m}=await this._getUser(i);if(m)throw m;let g={provider_token:s,provider_refresh_token:r,access_token:i,expires_in:h,expires_at:u,refresh_token:n,token_type:l,user:f.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:g,redirectType:e.type},error:null}}catch(e){if(sz(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await rr(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var s;let{data:r,error:i}=t;if(i)return{error:i};let n=null==(s=r.session)?void 0:s.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!(sz(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await ri(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession(async t=>{var s,r;try{let{data:{session:r},error:i}=t;if(i)throw i;await (null==(s=this.stateChangeEmitters.get(e))?void 0:s.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(t){await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let s=null,r=null;"pkce"===this.flowType&&([s,r]=await ru(this.storage,this.storageKey,!0));try{return await rv(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(sz(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if(sz(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:s,error:r}=await this._useSession(async t=>{var s,r,i,n,a;let{data:o,error:l}=t;if(l)throw l;let c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(s=e.options)?void 0:s.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:!0});return await rv(this.fetch,"GET",c,{headers:this.headers,jwt:null!=(a=null==(n=o.session)?void 0:n.access_token)?a:void 0})});if(r)throw r;return!s9()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==s?void 0:s.url),{data:{provider:e.provider,url:null==s?void 0:s.url},error:null}}catch(t){if(sz(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var s,r;let{data:i,error:n}=t;if(n)throw n;return await rv(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(r=null==(s=i.session)?void 0:s.access_token)?r:void 0})})}catch(e){if(sz(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var s,r;let i=Date.now();return await (s=async s=>(s>0&&await ro(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await rv(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:rx})),r=(e,t)=>{let s=200*Math.pow(2,e);return t&&sQ(t)&&Date.now()+s-i<3e4},new Promise((e,t)=>{(async()=>{for(let i=0;i<1/0;i++)try{let t=await s(i);if(!r(i,null,t))return void e(t)}catch(e){if(!r(i,e))return void t(e)}})()}))}catch(e){if(this._debug(t,"error",e),sz(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let s=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),s9()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let s=await rr(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s)){this._debug(t,"session is not valid"),null!==s&&await this._removeSession();return}let r=(null!=(e=s.expires_at)?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${r?"":" not"} expired with margin of 90000s`),r){if(this.autoRefreshToken&&s.refresh_token){let{error:e}=await this._callRefreshToken(s.refresh_token);e&&(console.error(e),sQ(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new sF;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new rn;let{data:t,error:s}=await this._refreshAccessToken(e);if(s)throw s;if(!t.session)throw new sF;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(e){if(this._debug(r,"error",e),sz(e)){let s={session:null,error:e};return sQ(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(s),s}throw null==(s=this.refreshingDeferred)||s.reject(e),e}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,s=!0){let r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});let r=[],i=Array.from(this.stateChangeEmitters.values()).map(async s=>{try{await s.callback(e,t)}catch(e){r.push(e)}});if(await Promise.all(i),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await rs(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await ri(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&s9()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let r=Math.floor((1e3*s.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),r<=3&&await this._callRefreshToken(s.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof rN)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!s9()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){let r=[`provider=${encodeURIComponent(t)}`];if((null==s?void 0:s.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){let[e,t]=await ru(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(s.toString())}if(null==s?void 0:s.queryParams){let e=new URLSearchParams(s.queryParams);r.push(e.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;return i?{data:null,error:i}:await rv(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token})})}catch(e){if(sz(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var s,r;let{data:i,error:n}=t;if(n)return{data:null,error:n};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await rv(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null==(s=null==i?void 0:i.session)?void 0:s.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null==(r=null==o?void 0:o.totp)?void 0:r.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(sz(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;if(i)return{data:null,error:i};let{data:n,error:a}=await rv(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:a})})}catch(e){if(sz(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;return i?{data:null,error:i}:await rv(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token})})}catch(e){if(sz(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let s=(null==e?void 0:e.factors)||[],r=s.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=s.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:s,totp:r,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,s;let{data:{session:r},error:i}=e;if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=ra(r.access_token),a=null;n.aal&&(a=n.aal);let o=a;return(null!=(s=null==(t=r.user.factors)?void 0:t.filter(e=>"verified"===e.status))?s:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let s=t.keys.find(t=>t.kid===e);if(s||(s=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return s;let{data:r,error:i}=await rv(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!r.keys||0===r.keys.length)throw new s0("JWKS is empty");if(this.jwks=r,this.jwks_cached_at=Date.now(),!(s=r.keys.find(t=>t.kid===e)))throw new s0("No matching signing key found in JWKS");return s}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:i,payload:n,signature:a,raw:{header:o,payload:l}}=ra(r);var s=n.exp;if(!s)throw Error("Missing exp claim");if(s<=Math.floor(Date.now()/1e3))throw Error("JWT has expired");if(!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:n,header:i,signature:a},error:null}}let c=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),h=await this.fetchJwk(i.kid,t),u=await crypto.subtle.importKey("jwk",h,c,!0,["verify"]);if(!await crypto.subtle.verify(c,u,a,function(e){let t=[];return function(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){let t=(r-55296)*1024&65535;r=(e.charCodeAt(s+1)-56320&65535|t)+65536,s+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(r,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${o}.${l}`)))throw new s0("Invalid JWT signature");return{data:{claims:n,header:i,signature:a},error:null}}catch(e){if(sz(e))return{data:null,error:e};throw e}}}r$.nextInstanceID=0;let rq=r$;class rB extends rq{constructor(e){super(e)}}class rz{constructor(e,t,s){var r,i,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,l=function(e,t){var s,r;let{db:i,auth:n,realtime:a,global:o}=e,{db:l,auth:c,realtime:h,global:u}=t,d={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},c),n),realtime:Object.assign(Object.assign({},h),a),global:Object.assign(Object.assign(Object.assign({},u),o),{headers:Object.assign(Object.assign({},null!=(s=null==u?void 0:u.headers)?s:{}),null!=(r=null==o?void 0:o.headers)?r:{})}),accessToken:()=>{var e,t,s,r;return e=this,t=void 0,r=function*(){return""},new(s=void 0,s=Promise)(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=s?s:{},{db:sP,realtime:sA,auth:Object.assign(Object.assign({},sC),{storageKey:o}),global:sR});this.storageKey=null!=(r=l.auth.storageKey)?r:"",this.headers=null!=(i=l.global.headers)?i:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(n=l.auth)?n:{},this.headers,l.global.fetch),this.fetch=sL(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new tq(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new t$(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new sk(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,s,r,i,n;return s=this,r=void 0,i=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:s}=yield this.auth.getSession();return null!=(t=null==(e=s.session)?void 0:e.access_token)?t:null},new(i||(i=Promise))(function(e,t){function a(e){try{l(n.next(e))}catch(e){t(e)}}function o(e){try{l(n.throw(e))}catch(e){t(e)}}function l(t){var s;t.done?e(t.value):((s=t.value)instanceof i?s:new i(function(e){e(s)})).then(a,o)}l((n=n.apply(s,r||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:i,flowType:n,lock:a,debug:o},l,c){let h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new rB({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),l),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:n,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new st(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,s){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==s?this.changedAccessToken=s:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let rW=(e,t,s)=>new rz(e,t,s);async function rG(e){let t=et.next({request:e}),s=function(e,t,s){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:r,getAll:i,setAll:n,setItems:a,removedItems:o}=function(e,t){let s,r,i=e.cookies??null,n=e.cookieEncoding,a={},o={};if(i)if("get"in i){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,s)=>`${e}.${s}`)]),s=[];for(let e=0;e<t.length;e+=1){let r=await i.get(t[e]);(r||"string"==typeof r)&&s.push({name:t[e],value:r})}return s};if(s=async t=>await e(t),"set"in i&&"remove"in i)r=async e=>{for(let t=0;t<e.length;t+=1){let{name:s,value:r,options:n}=e[t];r?await i.set(s,r,n):await i.remove(s,n)}};else if(t)r=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in i)if(s=async()=>await i.getAll(),"setAll"in i)r=i.setAll;else if(t)r=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${t_()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!t&&t_()){let e=()=>{let e=(0,tv.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};s=()=>e(),r=e=>{e.forEach(({name:e,value:t,options:s})=>{document.cookie=(0,tv.lK)(e,t,s)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else s=()=>[],r=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:s,setAll:r,setItems:a,removedItems:o,storage:{isServer:!0,getItem:async e=>{if("string"==typeof a[e])return a[e];if(o[e])return null;let t=await s([e]),r=await tk(e,async e=>{let s=t?.find(({name:t})=>t===e)||null;return s?s.value:null});if(!r)return null;let i=r;return"string"==typeof r&&r.startsWith(tI)&&(i=tA(r.substring(tI.length))),i},setItem:async(t,i)=>{t.endsWith("-code-verifier")&&await tj({getAll:s,setAll:r,setItems:{[t]:i},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:n}),a[t]=i,delete o[t]},removeItem:async e=>{delete a[e],o[e]=!0}}}:{getAll:s,setAll:r,setItems:a,removedItems:o,storage:{isServer:!1,getItem:async e=>{let t=await s([e]),r=await tk(e,async e=>{let s=t?.find(({name:t})=>t===e)||null;return s?s.value:null});if(!r)return null;let i=r;return r.startsWith(tI)&&(i=tA(r.substring(tI.length))),i},setItem:async(t,i)=>{let a=await s([t]),o=new Set((a?.map(({name:e})=>e)||[]).filter(e=>tO(e,t))),l=i;"base64url"===n&&(l=tI+tC(i));let c=tE(t,l);c.forEach(({name:e})=>{o.delete(e)});let h={...tx,...e?.cookieOptions,maxAge:0},u={...tx,...e?.cookieOptions,maxAge:tx.maxAge};delete h.name,delete u.name;let d=[...[...o].map(e=>({name:e,value:"",options:h})),...c.map(({name:e,value:t})=>({name:e,value:t,options:u}))];d.length>0&&await r(d)},removeItem:async t=>{let i=await s([t]),n=(i?.map(({name:e})=>e)||[]).filter(e=>tO(e,t)),a={...tx,...e?.cookieOptions,maxAge:0};delete a.name,n.length>0&&await r(n.map(e=>({name:e,value:"",options:a})))}}}}({...s,cookieEncoding:s?.cookieEncoding??"base64url"},!0),l=rW(e,t,{...s,global:{...s?.global,headers:{...s?.global?.headers,"X-Client-Info":"supabase-ssr/0.6.1 createServerClient"}},auth:{...s?.cookieOptions?.name?{storageKey:s.cookieOptions.name}:null,...s?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:r}});return l.auth.onAuthStateChange(async e=>{(Object.keys(a).length>0||Object.keys(o).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await tj({getAll:i,setAll:n,setItems:a,removedItems:o},{cookieOptions:s?.cookieOptions??null,cookieEncoding:s?.cookieEncoding??"base64url"})}),l}("https://rnjolcoecogzgglnblqn.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o",{cookies:{getAll:()=>e.cookies.getAll(),setAll(s){s.forEach(({name:t,value:s})=>e.cookies.set(t,s)),t=et.next({request:e}),s.forEach(({name:e,value:s,options:r})=>t.cookies.set(e,s,r))}}}),{data:{user:r}}=await s.auth.getUser(),{pathname:i}=e.nextUrl,n=["/dashboard","/onboarding","/choose-role"].some(e=>i.startsWith(e));if(!r&&n){let t=e.nextUrl.clone();return t.pathname="/login",t.searchParams.set("next",i),et.redirect(t)}if(r){let[t,n]=await Promise.all([s.from("customer_profiles").select("id").eq("id",r.id).maybeSingle(),s.from("business_profiles").select("id, business_slug, trial_end_date, has_active_subscription").eq("id",r.id).maybeSingle()]),a=t.data,o=t.error,l=n.data,c=n.error,h="true"===e.nextUrl.searchParams.get("logged_out");if(o||c);else if(a||l){let t=a?"customer":"business";if("business"===t&&!l?.business_slug&&"/onboarding"!==i&&!h){let t=e.nextUrl.clone();return t.pathname="/onboarding",et.redirect(t)}"business"===t&&l?.business_slug;let{data:n}=await s.from("payment_subscriptions").select("plan_id").eq("business_profile_id",r.id).order("created_at",{ascending:!1}).limit(1).maybeSingle(),o=n?.plan_id||"free";if("business"===t&&"free"===o&&i.startsWith("/dashboard/business/analytics")){let t=e.nextUrl.clone();return t.pathname="/dashboard/business/plan",t.searchParams.set("upgrade","analytics"),et.redirect(t)}if(("/login"===i||"/choose-role"===i||"/choose-role"===i)&&!("/login"===i&&h)){let s=e.nextUrl.clone();return s.pathname="business"===t?"/dashboard/business":"/dashboard/customer",et.redirect(s)}if("/onboarding"===i&&"customer"===t&&!h){let t=e.nextUrl.clone();return t.pathname="/dashboard/customer",et.redirect(t)}if(i.startsWith("/dashboard/customer")&&"customer"!==t&&!h){let t=e.nextUrl.clone();return t.pathname="/dashboard/business",et.redirect(t)}if(i.startsWith("/dashboard/business")&&"business"!==t&&!h){let t=e.nextUrl.clone();return t.pathname="/dashboard/customer",et.redirect(t)}}else if("/choose-role"!==i&&"/onboarding"!==i&&!h){let t=e.nextUrl.clone();return t.pathname="/choose-role",et.redirect(t)}}return t}var rV=s(144);let rF=globalThis.crypto.subtle;var rK=Object.defineProperty;((e,t)=>{for(var s in t)rK(e,s,{get:t[s],enumerable:!0})})({},{UpstashError:()=>rH,UrlError:()=>rJ});var rH=class extends Error{constructor(e){super(e),this.name="UpstashError"}},rJ=class extends Error{constructor(e){super(`Upstash Redis client was passed an invalid URL. You should pass a URL starting with https. Received: "${e}". `),this.name="UrlError"}};function rX(e){try{return function e(t){let s=Array.isArray(t)?t.map(t=>{try{return e(t)}catch{return t}}):JSON.parse(t);return"number"==typeof s&&s.toString()!==t?t:s}(e)}catch{return e}}function rY(e){return[e[0],...rX(e.slice(1))]}function rQ(e){let[t,s]=e,r=[];for(let e=0;e<s.length;e+=2)r.push({key:s[e],type:s[e+1]});return[t,r]}var rZ=class{baseUrl;headers;options;readYourWrites;upstashSyncToken="";hasCredentials;retry;constructor(e){if(this.options={backend:e.options?.backend,agent:e.agent,responseEncoding:e.responseEncoding??"base64",cache:e.cache,signal:e.signal,keepAlive:e.keepAlive??!0},this.upstashSyncToken="",this.readYourWrites=e.readYourWrites??!0,this.baseUrl=(e.baseUrl||"").replace(/\/$/,""),this.baseUrl&&!/^https?:\/\/[^\s#$./?].\S*$/.test(this.baseUrl))throw new rJ(this.baseUrl);this.headers={"Content-Type":"application/json",...e.headers},this.hasCredentials=!!(this.baseUrl&&this.headers.authorization.split(" ")[1]),"base64"===this.options.responseEncoding&&(this.headers["Upstash-Encoding"]="base64"),this.retry="boolean"!=typeof e.retry||e.retry?{attempts:e.retry?.retries??5,backoff:e.retry?.backoff??(e=>50*Math.exp(e))}:{attempts:1,backoff:()=>0}}mergeTelemetry(e){this.headers=r2(this.headers,"Upstash-Telemetry-Runtime",e.runtime),this.headers=r2(this.headers,"Upstash-Telemetry-Platform",e.platform),this.headers=r2(this.headers,"Upstash-Telemetry-Sdk",e.sdk)}async request(e){let t=function(...e){let t={};for(let s of e)if(s)for(let[e,r]of Object.entries(s))null!=r&&(t[e]=r);return t}(this.headers,e.headers??{}),s=[this.baseUrl,...e.path??[]].join("/"),r="text/event-stream"===t.Accept,i={cache:this.options.cache,method:"POST",headers:t,body:JSON.stringify(e.body),keepalive:this.options.keepAlive,agent:this.options.agent,signal:e.signal??this.options.signal,backend:this.options.backend};if(this.hasCredentials||console.warn("[Upstash Redis] Redis client was initialized without url or token. Failed to execute command."),this.readYourWrites){let e=this.upstashSyncToken;this.headers["upstash-sync-token"]=e}let n=null,a=null;for(let e=0;e<=this.retry.attempts;e++)try{n=await fetch(s,i);break}catch(t){if(this.options.signal?.aborted){n=new Response(new Blob([JSON.stringify({result:this.options.signal.reason??"Aborted"})]),{status:200,statusText:this.options.signal.reason??"Aborted"});break}a=t,e<this.retry.attempts&&await new Promise(t=>setTimeout(t,this.retry.backoff(e)))}if(!n)throw a??Error("Exhausted all retries");if(!n.ok){let t=await n.json();throw new rH(`${t.error}, command was: ${JSON.stringify(e.body)}`)}if(this.readYourWrites){let e=n.headers;this.upstashSyncToken=e.get("upstash-sync-token")??""}if(r&&e&&e.onMessage&&n.body){let t=n.body.getReader(),s=new TextDecoder;return(async()=>{try{for(;;){let{value:r,done:i}=await t.read();if(i)break;for(let t of s.decode(r).split("\n"))if(t.startsWith("data: ")){let s=t.slice(6);e.onMessage?.(s)}}}catch(e){e instanceof Error&&"AbortError"===e.name||console.error("Stream reading error:",e)}finally{try{await t.cancel()}catch{}}})(),{result:1}}let o=await n.json();if(this.readYourWrites){let e=n.headers;this.upstashSyncToken=e.get("upstash-sync-token")??""}return"base64"===this.options.responseEncoding?Array.isArray(o)?o.map(({result:e,error:t})=>({result:r1(e),error:t})):{result:r1(o.result),error:o.error}:o}};function r0(e){let t="";try{let s=atob(e),r=s.length,i=new Uint8Array(r);for(let e=0;e<r;e++)i[e]=s.charCodeAt(e);t=new TextDecoder().decode(i)}catch{t=e}return t}function r1(e){let t;switch(typeof e){case"undefined":return e;case"number":t=e;break;case"object":t=Array.isArray(e)?e.map(e=>"string"==typeof e?r0(e):Array.isArray(e)?e.map(e=>r1(e)):e):null;break;case"string":t="OK"===e?"OK":r0(e)}return t}function r2(e,t,s){return s&&(e[t]=e[t]?[e[t],s].join(","):s),e}var r3=e=>{switch(typeof e){case"string":case"number":case"boolean":return e;default:return JSON.stringify(e)}},r6=class{command;serialize;deserialize;headers;path;onMessage;isStreaming;signal;constructor(e,t){if(this.serialize=r3,this.deserialize=t?.automaticDeserialization===void 0||t.automaticDeserialization?t?.deserialize??rX:e=>e,this.command=e.map(e=>this.serialize(e)),this.headers=t?.headers,this.path=t?.path,this.onMessage=t?.streamOptions?.onMessage,this.isStreaming=t?.streamOptions?.isStreaming??!1,this.signal=t?.streamOptions?.signal,t?.latencyLogging){let e=this.exec.bind(this);this.exec=async t=>{let s=performance.now(),r=await e(t),i=(performance.now()-s).toFixed(2);return console.log(`Latency for \x1b[38;2;19;185;39m${this.command[0].toString().toUpperCase()}\x1b[0m: \x1b[38;2;0;255;255m${i} ms\x1b[0m`),r}}}async exec(e){let{result:t,error:s}=await e.request({body:this.command,path:this.path,upstashSyncToken:e.upstashSyncToken,headers:this.headers,onMessage:this.onMessage,isStreaming:this.isStreaming,signal:this.signal});if(s)throw new rH(s);if(void 0===t)throw TypeError("Request did not return a result");return this.deserialize(t)}},r5=class extends r6{constructor(e,t){let s=["hrandfield",e[0]];"number"==typeof e[1]&&s.push(e[1]),e[2]&&s.push("WITHVALUES"),super(s,{deserialize:e[2]?e=>(function(e){if(0===e.length)return null;let t={};for(let s=0;s<e.length;s+=2){let r=e[s],i=e[s+1];try{t[r]=JSON.parse(i)}catch{t[r]=i}}return t})(e):t?.deserialize,...t})}},r4=class extends r6{constructor(e,t){super(["append",...e],t)}},r9=class extends r6{constructor([e,t,s],r){let i=["bitcount",e];"number"==typeof t&&i.push(t),"number"==typeof s&&i.push(s),super(i,r)}},r8=class{constructor(e,t,s,r=e=>e.exec(this.client)){this.client=t,this.opts=s,this.execOperation=r,this.command=["bitfield",...e]}command;chain(...e){return this.command.push(...e),this}get(...e){return this.chain("get",...e)}set(...e){return this.chain("set",...e)}incrby(...e){return this.chain("incrby",...e)}overflow(e){return this.chain("overflow",e)}exec(){let e=new r6(this.command,this.opts);return this.execOperation(e)}},r7=class extends r6{constructor(e,t){super(["bitop",...e],t)}},ie=class extends r6{constructor(e,t){super(["bitpos",...e],t)}},it=class extends r6{constructor([e,t,s],r){super(["COPY",e,t,...s?.replace?["REPLACE"]:[]],{...r,deserialize:e=>e>0?"COPIED":"NOT_COPIED"})}},is=class extends r6{constructor(e){super(["dbsize"],e)}},ir=class extends r6{constructor(e,t){super(["decr",...e],t)}},ii=class extends r6{constructor(e,t){super(["decrby",...e],t)}},ia=class extends r6{constructor(e,t){super(["del",...e],t)}},io=class extends r6{constructor(e,t){super(["echo",...e],t)}},il=class extends r6{constructor([e,t,s],r){super(["eval_ro",e,t.length,...t,...s??[]],r)}},ic=class extends r6{constructor([e,t,s],r){super(["eval",e,t.length,...t,...s??[]],r)}},ih=class extends r6{constructor([e,t,s],r){super(["evalsha_ro",e,t.length,...t,...s??[]],r)}},iu=class extends r6{constructor([e,t,s],r){super(["evalsha",e,t.length,...t,...s??[]],r)}},id=class extends r6{constructor(e,t){super(e.map(e=>"string"==typeof e?e:String(e)),t)}},ip=class extends r6{constructor(e,t){super(["exists",...e],t)}},im=class extends r6{constructor(e,t){super(["expire",...e.filter(Boolean)],t)}},ig=class extends r6{constructor(e,t){super(["expireat",...e],t)}},iw=class extends r6{constructor(e,t){let s=["flushall"];e&&e.length>0&&e[0].async&&s.push("async"),super(s,t)}},ib=class extends r6{constructor([e],t){let s=["flushdb"];e?.async&&s.push("async"),super(s,t)}},iy=class extends r6{constructor([e,t,...s],r){let i=["geoadd",e];"nx"in t&&t.nx?i.push("nx"):"xx"in t&&t.xx&&i.push("xx"),"ch"in t&&t.ch&&i.push("ch"),"latitude"in t&&t.latitude&&i.push(t.longitude,t.latitude,t.member),i.push(...s.flatMap(({latitude:e,longitude:t,member:s})=>[t,e,s])),super(i,r)}},iv=class extends r6{constructor([e,t,s,r="M"],i){super(["GEODIST",e,t,s,r],i)}},i_=class extends r6{constructor(e,t){let[s]=e;super(["GEOHASH",s,...Array.isArray(e[1])?e[1]:e.slice(1)],t)}},ix=class extends r6{constructor(e,t){let[s]=e;super(["GEOPOS",s,...Array.isArray(e[1])?e[1]:e.slice(1)],{deserialize:e=>(function(e){let t=[];for(let s of e)s?.[0]&&s?.[1]&&t.push({lng:Number.parseFloat(s[0]),lat:Number.parseFloat(s[1])});return t})(e),...t})}},iS=class extends r6{constructor([e,t,s,r,i],n){let a=["GEOSEARCH",e];("FROMMEMBER"===t.type||"frommember"===t.type)&&a.push(t.type,t.member),("FROMLONLAT"===t.type||"fromlonlat"===t.type)&&a.push(t.type,t.coordinate.lon,t.coordinate.lat),("BYRADIUS"===s.type||"byradius"===s.type)&&a.push(s.type,s.radius,s.radiusType),("BYBOX"===s.type||"bybox"===s.type)&&a.push(s.type,s.rect.width,s.rect.height,s.rectType),a.push(r),i?.count&&a.push("COUNT",i.count.limit,...i.count.any?["ANY"]:[]),super([...a,...i?.withCoord?["WITHCOORD"]:[],...i?.withDist?["WITHDIST"]:[],...i?.withHash?["WITHHASH"]:[]],{deserialize:e=>i?.withCoord||i?.withDist||i?.withHash?e.map(e=>{let t=1,s={};try{s.member=JSON.parse(e[0])}catch{s.member=e[0]}return i.withDist&&(s.dist=Number.parseFloat(e[t++])),i.withHash&&(s.hash=e[t++].toString()),i.withCoord&&(s.coord={long:Number.parseFloat(e[t][0]),lat:Number.parseFloat(e[t][1])}),s}):e.map(e=>{try{return{member:JSON.parse(e)}}catch{return{member:e}}}),...n})}},iO=class extends r6{constructor([e,t,s,r,i,n],a){let o=["GEOSEARCHSTORE",e,t];("FROMMEMBER"===s.type||"frommember"===s.type)&&o.push(s.type,s.member),("FROMLONLAT"===s.type||"fromlonlat"===s.type)&&o.push(s.type,s.coordinate.lon,s.coordinate.lat),("BYRADIUS"===r.type||"byradius"===r.type)&&o.push(r.type,r.radius,r.radiusType),("BYBOX"===r.type||"bybox"===r.type)&&o.push(r.type,r.rect.width,r.rect.height,r.rectType),o.push(i),n?.count&&o.push("COUNT",n.count.limit,...n.count.any?["ANY"]:[]),super([...o,...n?.storeDist?["STOREDIST"]:[]],a)}},iE=class extends r6{constructor(e,t){super(["get",...e],t)}},ik=class extends r6{constructor(e,t){super(["getbit",...e],t)}},iT=class extends r6{constructor(e,t){super(["getdel",...e],t)}},iR=class extends r6{constructor([e,t],s){let r=["getex",e];t&&("ex"in t&&"number"==typeof t.ex?r.push("ex",t.ex):"px"in t&&"number"==typeof t.px?r.push("px",t.px):"exat"in t&&"number"==typeof t.exat?r.push("exat",t.exat):"pxat"in t&&"number"==typeof t.pxat?r.push("pxat",t.pxat):"persist"in t&&t.persist&&r.push("persist")),super(r,s)}},iP=class extends r6{constructor(e,t){super(["getrange",...e],t)}},iC=class extends r6{constructor(e,t){super(["getset",...e],t)}},iA=class extends r6{constructor(e,t){super(["hdel",...e],t)}},iI=class extends r6{constructor(e,t){super(["hexists",...e],t)}},ij=class extends r6{constructor(e,t){let[s,r,i,n]=e,a=Array.isArray(r)?r:[r];super(["hexpire",s,i,...n?[n]:[],"FIELDS",a.length,...a],t)}},iN=class extends r6{constructor(e,t){let[s,r,i,n]=e,a=Array.isArray(r)?r:[r];super(["hexpireat",s,i,...n?[n]:[],"FIELDS",a.length,...a],t)}},iL=class extends r6{constructor(e,t){let[s,r]=e,i=Array.isArray(r)?r:[r];super(["hexpiretime",s,"FIELDS",i.length,...i],t)}},iM=class extends r6{constructor(e,t){let[s,r]=e,i=Array.isArray(r)?r:[r];super(["hpersist",s,"FIELDS",i.length,...i],t)}},iD=class extends r6{constructor(e,t){let[s,r,i,n]=e,a=Array.isArray(r)?r:[r];super(["hpexpire",s,i,...n?[n]:[],"FIELDS",a.length,...a],t)}},iU=class extends r6{constructor(e,t){let[s,r,i,n]=e,a=Array.isArray(r)?r:[r];super(["hpexpireat",s,i,...n?[n]:[],"FIELDS",a.length,...a],t)}},i$=class extends r6{constructor(e,t){let[s,r]=e,i=Array.isArray(r)?r:[r];super(["hpexpiretime",s,"FIELDS",i.length,...i],t)}},iq=class extends r6{constructor(e,t){let[s,r]=e,i=Array.isArray(r)?r:[r];super(["hpttl",s,"FIELDS",i.length,...i],t)}},iB=class extends r6{constructor(e,t){super(["hget",...e],t)}},iz=class extends r6{constructor(e,t){super(["hgetall",...e],{deserialize:e=>(function(e){if(0===e.length)return null;let t={};for(let s=0;s<e.length;s+=2){let r=e[s],i=e[s+1];try{let e=!Number.isNaN(Number(i))&&!Number.isSafeInteger(Number(i));t[r]=e?i:JSON.parse(i)}catch{t[r]=i}}return t})(e),...t})}},iW=class extends r6{constructor(e,t){super(["hincrby",...e],t)}},iG=class extends r6{constructor(e,t){super(["hincrbyfloat",...e],t)}},iV=class extends r6{constructor([e],t){super(["hkeys",e],t)}},iF=class extends r6{constructor(e,t){super(["hlen",...e],t)}},iK=class extends r6{constructor([e,...t],s){super(["hmget",e,...t],{deserialize:e=>(function(e,t){if(t.every(e=>null===e))return null;let s={};for(let[r,i]of e.entries())try{s[i]=JSON.parse(t[r])}catch{s[i]=t[r]}return s})(t,e),...s})}},iH=class extends r6{constructor([e,t],s){super(["hmset",e,...Object.entries(t).flatMap(([e,t])=>[e,t])],s)}},iJ=class extends r6{constructor([e,t,s],r){let i=["hscan",e,t];s?.match&&i.push("match",s.match),"number"==typeof s?.count&&i.push("count",s.count),super(i,{deserialize:rY,...r})}},iX=class extends r6{constructor([e,t],s){super(["hset",e,...Object.entries(t).flatMap(([e,t])=>[e,t])],s)}},iY=class extends r6{constructor(e,t){super(["hsetnx",...e],t)}},iQ=class extends r6{constructor(e,t){super(["hstrlen",...e],t)}},iZ=class extends r6{constructor(e,t){let[s,r]=e,i=Array.isArray(r)?r:[r];super(["httl",s,"FIELDS",i.length,...i],t)}},i0=class extends r6{constructor(e,t){super(["hvals",...e],t)}},i1=class extends r6{constructor(e,t){super(["incr",...e],t)}},i2=class extends r6{constructor(e,t){super(["incrby",...e],t)}},i3=class extends r6{constructor(e,t){super(["incrbyfloat",...e],t)}},i6=class extends r6{constructor(e,t){super(["JSON.ARRAPPEND",...e],t)}},i5=class extends r6{constructor(e,t){super(["JSON.ARRINDEX",...e],t)}},i4=class extends r6{constructor(e,t){super(["JSON.ARRINSERT",...e],t)}},i9=class extends r6{constructor(e,t){super(["JSON.ARRLEN",e[0],e[1]??"$"],t)}},i8=class extends r6{constructor(e,t){super(["JSON.ARRPOP",...e],t)}},i7=class extends r6{constructor(e,t){let s=e[1]??"$";super(["JSON.ARRTRIM",e[0],s,e[2]??0,e[3]??0],t)}},ne=class extends r6{constructor(e,t){super(["JSON.CLEAR",...e],t)}},nt=class extends r6{constructor(e,t){super(["JSON.DEL",...e],t)}},ns=class extends r6{constructor(e,t){super(["JSON.FORGET",...e],t)}},nr=class extends r6{constructor(e,t){let s=["JSON.GET"];"string"==typeof e[1]?s.push(...e):(s.push(e[0]),e[1]&&(e[1].indent&&s.push("INDENT",e[1].indent),e[1].newline&&s.push("NEWLINE",e[1].newline),e[1].space&&s.push("SPACE",e[1].space)),s.push(...e.slice(2))),super(s,t)}},ni=class extends r6{constructor(e,t){super(["JSON.MERGE",...e],t)}},nn=class extends r6{constructor(e,t){super(["JSON.MGET",...e[0],e[1]],t)}},na=class extends r6{constructor(e,t){let s=["JSON.MSET"];for(let t of e)s.push(t.key,t.path,t.value);super(s,t)}},no=class extends r6{constructor(e,t){super(["JSON.NUMINCRBY",...e],t)}},nl=class extends r6{constructor(e,t){super(["JSON.NUMMULTBY",...e],t)}},nc=class extends r6{constructor(e,t){super(["JSON.OBJKEYS",...e],t)}},nh=class extends r6{constructor(e,t){super(["JSON.OBJLEN",...e],t)}},nu=class extends r6{constructor(e,t){super(["JSON.RESP",...e],t)}},nd=class extends r6{constructor(e,t){let s=["JSON.SET",e[0],e[1],e[2]];e[3]&&(e[3].nx?s.push("NX"):e[3].xx&&s.push("XX")),super(s,t)}},np=class extends r6{constructor(e,t){super(["JSON.STRAPPEND",...e],t)}},nf=class extends r6{constructor(e,t){super(["JSON.STRLEN",...e],t)}},nm=class extends r6{constructor(e,t){super(["JSON.TOGGLE",...e],t)}},ng=class extends r6{constructor(e,t){super(["JSON.TYPE",...e],t)}},nw=class extends r6{constructor(e,t){super(["keys",...e],t)}},nb=class extends r6{constructor(e,t){super(["lindex",...e],t)}},ny=class extends r6{constructor(e,t){super(["linsert",...e],t)}},nv=class extends r6{constructor(e,t){super(["llen",...e],t)}},n_=class extends r6{constructor(e,t){super(["lmove",...e],t)}},nx=class extends r6{constructor(e,t){let[s,r,i,n]=e;super(["LMPOP",s,...r,i,...n?["COUNT",n]:[]],t)}},nS=class extends r6{constructor(e,t){super(["lpop",...e],t)}},nO=class extends r6{constructor(e,t){let s=["lpos",e[0],e[1]];"number"==typeof e[2]?.rank&&s.push("rank",e[2].rank),"number"==typeof e[2]?.count&&s.push("count",e[2].count),"number"==typeof e[2]?.maxLen&&s.push("maxLen",e[2].maxLen),super(s,t)}},nE=class extends r6{constructor(e,t){super(["lpush",...e],t)}},nk=class extends r6{constructor(e,t){super(["lpushx",...e],t)}},nT=class extends r6{constructor(e,t){super(["lrange",...e],t)}},nR=class extends r6{constructor(e,t){super(["lrem",...e],t)}},nP=class extends r6{constructor(e,t){super(["lset",...e],t)}},nC=class extends r6{constructor(e,t){super(["ltrim",...e],t)}},nA=class extends r6{constructor(e,t){super(["mget",...Array.isArray(e[0])?e[0]:e],t)}},nI=class extends r6{constructor([e],t){super(["mset",...Object.entries(e).flatMap(([e,t])=>[e,t])],t)}},nj=class extends r6{constructor([e],t){super(["msetnx",...Object.entries(e).flat()],t)}},nN=class extends r6{constructor(e,t){super(["persist",...e],t)}},nL=class extends r6{constructor(e,t){super(["pexpire",...e],t)}},nM=class extends r6{constructor(e,t){super(["pexpireat",...e],t)}},nD=class extends r6{constructor(e,t){super(["pfadd",...e],t)}},nU=class extends r6{constructor(e,t){super(["pfcount",...e],t)}},n$=class extends r6{constructor(e,t){super(["pfmerge",...e],t)}},nq=class extends r6{constructor(e,t){let s=["ping"];e?.[0]!==void 0&&s.push(e[0]),super(s,t)}},nB=class extends r6{constructor(e,t){super(["psetex",...e],t)}},nz=class extends r6{constructor(e,t){super(["pttl",...e],t)}},nW=class extends r6{constructor(e,t){super(["publish",...e],t)}},nG=class extends r6{constructor(e){super(["randomkey"],e)}},nV=class extends r6{constructor(e,t){super(["rename",...e],t)}},nF=class extends r6{constructor(e,t){super(["renamenx",...e],t)}},nK=class extends r6{constructor(e,t){super(["rpop",...e],t)}},nH=class extends r6{constructor(e,t){super(["rpush",...e],t)}},nJ=class extends r6{constructor(e,t){super(["rpushx",...e],t)}},nX=class extends r6{constructor(e,t){super(["sadd",...e],t)}},nY=class extends r6{constructor([e,t],s){let r=["scan",e];t?.match&&r.push("match",t.match),"number"==typeof t?.count&&r.push("count",t.count),t&&"withType"in t&&!0===t.withType?r.push("withtype"):t&&"type"in t&&t.type&&t.type.length>0&&r.push("type",t.type),super(r,{deserialize:t?.withType?rQ:rY,...s})}},nQ=class extends r6{constructor(e,t){super(["scard",...e],t)}},nZ=class extends r6{constructor(e,t){super(["script","exists",...e],{deserialize:e=>e,...t})}},n0=class extends r6{constructor([e],t){let s=["script","flush"];e?.sync?s.push("sync"):e?.async&&s.push("async"),super(s,t)}},n1=class extends r6{constructor(e,t){super(["script","load",...e],t)}},n2=class extends r6{constructor(e,t){super(["sdiff",...e],t)}},n3=class extends r6{constructor(e,t){super(["sdiffstore",...e],t)}},n6=class extends r6{constructor([e,t,s],r){let i=["set",e,t];s&&("nx"in s&&s.nx?i.push("nx"):"xx"in s&&s.xx&&i.push("xx"),"get"in s&&s.get&&i.push("get"),"ex"in s&&"number"==typeof s.ex?i.push("ex",s.ex):"px"in s&&"number"==typeof s.px?i.push("px",s.px):"exat"in s&&"number"==typeof s.exat?i.push("exat",s.exat):"pxat"in s&&"number"==typeof s.pxat?i.push("pxat",s.pxat):"keepTtl"in s&&s.keepTtl&&i.push("keepTtl")),super(i,r)}},n5=class extends r6{constructor(e,t){super(["setbit",...e],t)}},n4=class extends r6{constructor(e,t){super(["setex",...e],t)}},n9=class extends r6{constructor(e,t){super(["setnx",...e],t)}},n8=class extends r6{constructor(e,t){super(["setrange",...e],t)}},n7=class extends r6{constructor(e,t){super(["sinter",...e],t)}},ae=class extends r6{constructor(e,t){super(["sinterstore",...e],t)}},at=class extends r6{constructor(e,t){super(["sismember",...e],t)}},as=class extends r6{constructor(e,t){super(["smembers",...e],t)}},ar=class extends r6{constructor(e,t){super(["smismember",e[0],...e[1]],t)}},ai=class extends r6{constructor(e,t){super(["smove",...e],t)}},an=class extends r6{constructor([e,t],s){let r=["spop",e];"number"==typeof t&&r.push(t),super(r,s)}},aa=class extends r6{constructor([e,t],s){let r=["srandmember",e];"number"==typeof t&&r.push(t),super(r,s)}},ao=class extends r6{constructor(e,t){super(["srem",...e],t)}},al=class extends r6{constructor([e,t,s],r){let i=["sscan",e,t];s?.match&&i.push("match",s.match),"number"==typeof s?.count&&i.push("count",s.count),super(i,{deserialize:rY,...r})}},ac=class extends r6{constructor(e,t){super(["strlen",...e],t)}},ah=class extends r6{constructor(e,t){super(["sunion",...e],t)}},au=class extends r6{constructor(e,t){super(["sunionstore",...e],t)}},ad=class extends r6{constructor(e){super(["time"],e)}},ap=class extends r6{constructor(e,t){super(["touch",...e],t)}},af=class extends r6{constructor(e,t){super(["ttl",...e],t)}},am=class extends r6{constructor(e,t){super(["type",...e],t)}},ag=class extends r6{constructor(e,t){super(["unlink",...e],t)}},aw=class extends r6{constructor([e,t,s],r){super(["XACK",e,t,...Array.isArray(s)?[...s]:[s]],r)}},ab=class extends r6{constructor([e,t,s,r],i){let n=["XADD",e];for(let[e,i]of(r&&(r.nomkStream&&n.push("NOMKSTREAM"),r.trim&&(n.push(r.trim.type,r.trim.comparison,r.trim.threshold),void 0!==r.trim.limit&&n.push("LIMIT",r.trim.limit))),n.push(t),Object.entries(s)))n.push(e,i);super(n,i)}},ay=class extends r6{constructor([e,t,s,r,i,n],a){let o=[];n?.count&&o.push("COUNT",n.count),n?.justId&&o.push("JUSTID"),super(["XAUTOCLAIM",e,t,s,r,i,...o],a)}},av=class extends r6{constructor([e,t,s,r,i,n],a){let o=Array.isArray(i)?[...i]:[i],l=[];n?.idleMS&&l.push("IDLE",n.idleMS),n?.idleMS&&l.push("TIME",n.timeMS),n?.retryCount&&l.push("RETRYCOUNT",n.retryCount),n?.force&&l.push("FORCE"),n?.justId&&l.push("JUSTID"),n?.lastId&&l.push("LASTID",n.lastId),super(["XCLAIM",e,t,s,r,...o,...l],a)}},a_=class extends r6{constructor([e,t],s){super(["XDEL",e,...Array.isArray(t)?[...t]:[t]],s)}},ax=class extends r6{constructor([e,t],s){let r=["XGROUP"];switch(t.type){case"CREATE":r.push("CREATE",e,t.group,t.id),t.options&&(t.options.MKSTREAM&&r.push("MKSTREAM"),void 0!==t.options.ENTRIESREAD&&r.push("ENTRIESREAD",t.options.ENTRIESREAD.toString()));break;case"CREATECONSUMER":r.push("CREATECONSUMER",e,t.group,t.consumer);break;case"DELCONSUMER":r.push("DELCONSUMER",e,t.group,t.consumer);break;case"DESTROY":r.push("DESTROY",e,t.group);break;case"SETID":r.push("SETID",e,t.group,t.id),t.options?.ENTRIESREAD!==void 0&&r.push("ENTRIESREAD",t.options.ENTRIESREAD.toString());break;default:throw Error("Invalid XGROUP")}super(r,s)}},aS=class extends r6{constructor([e,t],s){let r=[];"CONSUMERS"===t.type?r.push("CONSUMERS",e,t.group):r.push("GROUPS",e),super(["XINFO",...r],s)}},aO=class extends r6{constructor(e,t){super(["XLEN",...e],t)}},aE=class extends r6{constructor([e,t,s,r,i,n],a){super(["XPENDING",e,t,...n?.idleTime?["IDLE",n.idleTime]:[],s,r,i,...n?.consumer===void 0?[]:Array.isArray(n.consumer)?[...n.consumer]:[n.consumer]],a)}},ak=class extends r6{constructor([e,t,s,r],i){let n=["XRANGE",e,t,s];"number"==typeof r&&n.push("COUNT",r),super(n,{deserialize:e=>(function(e){let t={};for(let s of e)for(let e=0;e<s.length;e+=2){let r=s[e],i=s[e+1];r in t||(t[r]={});for(let e=0;e<i.length;e+=2){let s=i[e],n=i[e+1];try{t[r][s]=JSON.parse(n)}catch{t[r][s]=n}}}return t})(e),...i})}},aT=class extends r6{constructor([e,t,s],r){if(Array.isArray(e)&&Array.isArray(t)&&e.length!==t.length)throw Error("ERR Unbalanced XREAD list of streams: for each stream key an ID or '$' must be specified");let i=[];"number"==typeof s?.count&&i.push("COUNT",s.count),"number"==typeof s?.blockMS&&i.push("BLOCK",s.blockMS),i.push("STREAMS",...Array.isArray(e)?[...e]:[e],...Array.isArray(t)?[...t]:[t]),super(["XREAD",...i],r)}},aR=class extends r6{constructor([e,t,s,r,i],n){if(Array.isArray(s)&&Array.isArray(r)&&s.length!==r.length)throw Error("ERR Unbalanced XREADGROUP list of streams: for each stream key an ID or '$' must be specified");let a=[];"number"==typeof i?.count&&a.push("COUNT",i.count),"number"==typeof i?.blockMS&&a.push("BLOCK",i.blockMS),"boolean"==typeof i?.NOACK&&i.NOACK&&a.push("NOACK"),a.push("STREAMS",...Array.isArray(s)?[...s]:[s],...Array.isArray(r)?[...r]:[r]),super(["XREADGROUP","GROUP",e,t,...a],n)}},aP=class extends r6{constructor([e,t,s,r],i){let n=["XREVRANGE",e,t,s];"number"==typeof r&&n.push("COUNT",r),super(n,{deserialize:e=>(function(e){let t={};for(let s of e)for(let e=0;e<s.length;e+=2){let r=s[e],i=s[e+1];r in t||(t[r]={});for(let e=0;e<i.length;e+=2){let s=i[e],n=i[e+1];try{t[r][s]=JSON.parse(n)}catch{t[r][s]=n}}}return t})(e),...i})}},aC=class extends r6{constructor([e,t],s){let{limit:r,strategy:i,threshold:n,exactness:a="~"}=t;super(["XTRIM",e,i,a,n,...r?["LIMIT",r]:[]],s)}},aA=class extends r6{constructor([e,t,...s],r){let i=["zadd",e];"nx"in t&&t.nx?i.push("nx"):"xx"in t&&t.xx&&i.push("xx"),"ch"in t&&t.ch&&i.push("ch"),"incr"in t&&t.incr&&i.push("incr"),"lt"in t&&t.lt?i.push("lt"):"gt"in t&&t.gt&&i.push("gt"),"score"in t&&"member"in t&&i.push(t.score,t.member),i.push(...s.flatMap(({score:e,member:t})=>[e,t])),super(i,r)}},aI=class extends r6{constructor(e,t){super(["zcard",...e],t)}},aj=class extends r6{constructor(e,t){super(["zcount",...e],t)}},aN=class extends r6{constructor(e,t){super(["zincrby",...e],t)}},aL=class extends r6{constructor([e,t,s,r],i){let n=["zinterstore",e,t];Array.isArray(s)?n.push(...s):n.push(s),r&&("weights"in r&&r.weights?n.push("weights",...r.weights):"weight"in r&&"number"==typeof r.weight&&n.push("weights",r.weight),"aggregate"in r&&n.push("aggregate",r.aggregate)),super(n,i)}},aM=class extends r6{constructor(e,t){super(["zlexcount",...e],t)}},aD=class extends r6{constructor([e,t],s){let r=["zpopmax",e];"number"==typeof t&&r.push(t),super(r,s)}},aU=class extends r6{constructor([e,t],s){let r=["zpopmin",e];"number"==typeof t&&r.push(t),super(r,s)}},a$=class extends r6{constructor([e,t,s,r],i){let n=["zrange",e,t,s];r?.byScore&&n.push("byscore"),r?.byLex&&n.push("bylex"),r?.rev&&n.push("rev"),r?.count!==void 0&&void 0!==r.offset&&n.push("limit",r.offset,r.count),r?.withScores&&n.push("withscores"),super(n,i)}},aq=class extends r6{constructor(e,t){super(["zrank",...e],t)}},aB=class extends r6{constructor(e,t){super(["zrem",...e],t)}},az=class extends r6{constructor(e,t){super(["zremrangebylex",...e],t)}},aW=class extends r6{constructor(e,t){super(["zremrangebyrank",...e],t)}},aG=class extends r6{constructor(e,t){super(["zremrangebyscore",...e],t)}},aV=class extends r6{constructor(e,t){super(["zrevrank",...e],t)}},aF=class extends r6{constructor([e,t,s],r){let i=["zscan",e,t];s?.match&&i.push("match",s.match),"number"==typeof s?.count&&i.push("count",s.count),super(i,{deserialize:rY,...r})}},aK=class extends r6{constructor(e,t){super(["zscore",...e],t)}},aH=class extends r6{constructor([e,t,s],r){let i=["zunion",e];Array.isArray(t)?i.push(...t):i.push(t),s&&("weights"in s&&s.weights?i.push("weights",...s.weights):"weight"in s&&"number"==typeof s.weight&&i.push("weights",s.weight),"aggregate"in s&&i.push("aggregate",s.aggregate),s.withScores&&i.push("withscores")),super(i,r)}},aJ=class extends r6{constructor([e,t,s,r],i){let n=["zunionstore",e,t];Array.isArray(s)?n.push(...s):n.push(s),r&&("weights"in r&&r.weights?n.push("weights",...r.weights):"weight"in r&&"number"==typeof r.weight&&n.push("weights",r.weight),"aggregate"in r&&n.push("aggregate",r.aggregate)),super(n,i)}},aX=class extends r6{constructor(e,t){super(["zdiffstore",...e],t)}},aY=class extends r6{constructor(e,t){let[s,r]=e;super(["zmscore",s,...r],t)}},aQ=class{client;commands;commandOptions;multiExec;constructor(e){if(this.client=e.client,this.commands=[],this.commandOptions=e.commandOptions,this.multiExec=e.multiExec??!1,this.commandOptions?.latencyLogging){let e=this.exec.bind(this);this.exec=async t=>{let s=performance.now(),r=await (t?e(t):e()),i=(performance.now()-s).toFixed(2);return console.log(`Latency for \x1b[38;2;19;185;39m${this.multiExec?["MULTI-EXEC"]:["PIPELINE"].toString().toUpperCase()}\x1b[0m: \x1b[38;2;0;255;255m${i} ms\x1b[0m`),r}}}exec=async e=>{if(0===this.commands.length)throw Error("Pipeline is empty");let t=this.multiExec?["multi-exec"]:["pipeline"],s=await this.client.request({path:t,body:Object.values(this.commands).map(e=>e.command)});return e?.keepErrors?s.map(({error:e,result:t},s)=>({error:e,result:this.commands[s].deserialize(t)})):s.map(({error:e,result:t},s)=>{if(e)throw new rH(`Command ${s+1} [ ${this.commands[s].command[0]} ] failed: ${e}`);return this.commands[s].deserialize(t)})};length(){return this.commands.length}chain(e){return this.commands.push(e),this}append=(...e)=>this.chain(new r4(e,this.commandOptions));bitcount=(...e)=>this.chain(new r9(e,this.commandOptions));bitfield=(...e)=>new r8(e,this.client,this.commandOptions,this.chain.bind(this));bitop=(e,t,s,...r)=>this.chain(new r7([e,t,s,...r],this.commandOptions));bitpos=(...e)=>this.chain(new ie(e,this.commandOptions));copy=(...e)=>this.chain(new it(e,this.commandOptions));zdiffstore=(...e)=>this.chain(new aX(e,this.commandOptions));dbsize=()=>this.chain(new is(this.commandOptions));decr=(...e)=>this.chain(new ir(e,this.commandOptions));decrby=(...e)=>this.chain(new ii(e,this.commandOptions));del=(...e)=>this.chain(new ia(e,this.commandOptions));echo=(...e)=>this.chain(new io(e,this.commandOptions));evalRo=(...e)=>this.chain(new il(e,this.commandOptions));eval=(...e)=>this.chain(new ic(e,this.commandOptions));evalshaRo=(...e)=>this.chain(new ih(e,this.commandOptions));evalsha=(...e)=>this.chain(new iu(e,this.commandOptions));exists=(...e)=>this.chain(new ip(e,this.commandOptions));expire=(...e)=>this.chain(new im(e,this.commandOptions));expireat=(...e)=>this.chain(new ig(e,this.commandOptions));flushall=e=>this.chain(new iw(e,this.commandOptions));flushdb=(...e)=>this.chain(new ib(e,this.commandOptions));geoadd=(...e)=>this.chain(new iy(e,this.commandOptions));geodist=(...e)=>this.chain(new iv(e,this.commandOptions));geopos=(...e)=>this.chain(new ix(e,this.commandOptions));geohash=(...e)=>this.chain(new i_(e,this.commandOptions));geosearch=(...e)=>this.chain(new iS(e,this.commandOptions));geosearchstore=(...e)=>this.chain(new iO(e,this.commandOptions));get=(...e)=>this.chain(new iE(e,this.commandOptions));getbit=(...e)=>this.chain(new ik(e,this.commandOptions));getdel=(...e)=>this.chain(new iT(e,this.commandOptions));getex=(...e)=>this.chain(new iR(e,this.commandOptions));getrange=(...e)=>this.chain(new iP(e,this.commandOptions));getset=(e,t)=>this.chain(new iC([e,t],this.commandOptions));hdel=(...e)=>this.chain(new iA(e,this.commandOptions));hexists=(...e)=>this.chain(new iI(e,this.commandOptions));hexpire=(...e)=>this.chain(new ij(e,this.commandOptions));hexpireat=(...e)=>this.chain(new iN(e,this.commandOptions));hexpiretime=(...e)=>this.chain(new iL(e,this.commandOptions));httl=(...e)=>this.chain(new iZ(e,this.commandOptions));hpexpire=(...e)=>this.chain(new iD(e,this.commandOptions));hpexpireat=(...e)=>this.chain(new iU(e,this.commandOptions));hpexpiretime=(...e)=>this.chain(new i$(e,this.commandOptions));hpttl=(...e)=>this.chain(new iq(e,this.commandOptions));hpersist=(...e)=>this.chain(new iM(e,this.commandOptions));hget=(...e)=>this.chain(new iB(e,this.commandOptions));hgetall=(...e)=>this.chain(new iz(e,this.commandOptions));hincrby=(...e)=>this.chain(new iW(e,this.commandOptions));hincrbyfloat=(...e)=>this.chain(new iG(e,this.commandOptions));hkeys=(...e)=>this.chain(new iV(e,this.commandOptions));hlen=(...e)=>this.chain(new iF(e,this.commandOptions));hmget=(...e)=>this.chain(new iK(e,this.commandOptions));hmset=(e,t)=>this.chain(new iH([e,t],this.commandOptions));hrandfield=(e,t,s)=>this.chain(new r5([e,t,s],this.commandOptions));hscan=(...e)=>this.chain(new iJ(e,this.commandOptions));hset=(e,t)=>this.chain(new iX([e,t],this.commandOptions));hsetnx=(e,t,s)=>this.chain(new iY([e,t,s],this.commandOptions));hstrlen=(...e)=>this.chain(new iQ(e,this.commandOptions));hvals=(...e)=>this.chain(new i0(e,this.commandOptions));incr=(...e)=>this.chain(new i1(e,this.commandOptions));incrby=(...e)=>this.chain(new i2(e,this.commandOptions));incrbyfloat=(...e)=>this.chain(new i3(e,this.commandOptions));keys=(...e)=>this.chain(new nw(e,this.commandOptions));lindex=(...e)=>this.chain(new nb(e,this.commandOptions));linsert=(e,t,s,r)=>this.chain(new ny([e,t,s,r],this.commandOptions));llen=(...e)=>this.chain(new nv(e,this.commandOptions));lmove=(...e)=>this.chain(new n_(e,this.commandOptions));lpop=(...e)=>this.chain(new nS(e,this.commandOptions));lmpop=(...e)=>this.chain(new nx(e,this.commandOptions));lpos=(...e)=>this.chain(new nO(e,this.commandOptions));lpush=(e,...t)=>this.chain(new nE([e,...t],this.commandOptions));lpushx=(e,...t)=>this.chain(new nk([e,...t],this.commandOptions));lrange=(...e)=>this.chain(new nT(e,this.commandOptions));lrem=(e,t,s)=>this.chain(new nR([e,t,s],this.commandOptions));lset=(e,t,s)=>this.chain(new nP([e,t,s],this.commandOptions));ltrim=(...e)=>this.chain(new nC(e,this.commandOptions));mget=(...e)=>this.chain(new nA(e,this.commandOptions));mset=e=>this.chain(new nI([e],this.commandOptions));msetnx=e=>this.chain(new nj([e],this.commandOptions));persist=(...e)=>this.chain(new nN(e,this.commandOptions));pexpire=(...e)=>this.chain(new nL(e,this.commandOptions));pexpireat=(...e)=>this.chain(new nM(e,this.commandOptions));pfadd=(...e)=>this.chain(new nD(e,this.commandOptions));pfcount=(...e)=>this.chain(new nU(e,this.commandOptions));pfmerge=(...e)=>this.chain(new n$(e,this.commandOptions));ping=e=>this.chain(new nq(e,this.commandOptions));psetex=(e,t,s)=>this.chain(new nB([e,t,s],this.commandOptions));pttl=(...e)=>this.chain(new nz(e,this.commandOptions));publish=(...e)=>this.chain(new nW(e,this.commandOptions));randomkey=()=>this.chain(new nG(this.commandOptions));rename=(...e)=>this.chain(new nV(e,this.commandOptions));renamenx=(...e)=>this.chain(new nF(e,this.commandOptions));rpop=(...e)=>this.chain(new nK(e,this.commandOptions));rpush=(e,...t)=>this.chain(new nH([e,...t],this.commandOptions));rpushx=(e,...t)=>this.chain(new nJ([e,...t],this.commandOptions));sadd=(e,t,...s)=>this.chain(new nX([e,t,...s],this.commandOptions));scan=(...e)=>this.chain(new nY(e,this.commandOptions));scard=(...e)=>this.chain(new nQ(e,this.commandOptions));scriptExists=(...e)=>this.chain(new nZ(e,this.commandOptions));scriptFlush=(...e)=>this.chain(new n0(e,this.commandOptions));scriptLoad=(...e)=>this.chain(new n1(e,this.commandOptions));sdiff=(...e)=>this.chain(new n2(e,this.commandOptions));sdiffstore=(...e)=>this.chain(new n3(e,this.commandOptions));set=(e,t,s)=>this.chain(new n6([e,t,s],this.commandOptions));setbit=(...e)=>this.chain(new n5(e,this.commandOptions));setex=(e,t,s)=>this.chain(new n4([e,t,s],this.commandOptions));setnx=(e,t)=>this.chain(new n9([e,t],this.commandOptions));setrange=(...e)=>this.chain(new n8(e,this.commandOptions));sinter=(...e)=>this.chain(new n7(e,this.commandOptions));sinterstore=(...e)=>this.chain(new ae(e,this.commandOptions));sismember=(e,t)=>this.chain(new at([e,t],this.commandOptions));smembers=(...e)=>this.chain(new as(e,this.commandOptions));smismember=(e,t)=>this.chain(new ar([e,t],this.commandOptions));smove=(e,t,s)=>this.chain(new ai([e,t,s],this.commandOptions));spop=(...e)=>this.chain(new an(e,this.commandOptions));srandmember=(...e)=>this.chain(new aa(e,this.commandOptions));srem=(e,...t)=>this.chain(new ao([e,...t],this.commandOptions));sscan=(...e)=>this.chain(new al(e,this.commandOptions));strlen=(...e)=>this.chain(new ac(e,this.commandOptions));sunion=(...e)=>this.chain(new ah(e,this.commandOptions));sunionstore=(...e)=>this.chain(new au(e,this.commandOptions));time=()=>this.chain(new ad(this.commandOptions));touch=(...e)=>this.chain(new ap(e,this.commandOptions));ttl=(...e)=>this.chain(new af(e,this.commandOptions));type=(...e)=>this.chain(new am(e,this.commandOptions));unlink=(...e)=>this.chain(new ag(e,this.commandOptions));zadd=(...e)=>("score"in e[1],this.chain(new aA([e[0],e[1],...e.slice(2)],this.commandOptions)));xadd=(...e)=>this.chain(new ab(e,this.commandOptions));xack=(...e)=>this.chain(new aw(e,this.commandOptions));xdel=(...e)=>this.chain(new a_(e,this.commandOptions));xgroup=(...e)=>this.chain(new ax(e,this.commandOptions));xread=(...e)=>this.chain(new aT(e,this.commandOptions));xreadgroup=(...e)=>this.chain(new aR(e,this.commandOptions));xinfo=(...e)=>this.chain(new aS(e,this.commandOptions));xlen=(...e)=>this.chain(new aO(e,this.commandOptions));xpending=(...e)=>this.chain(new aE(e,this.commandOptions));xclaim=(...e)=>this.chain(new av(e,this.commandOptions));xautoclaim=(...e)=>this.chain(new ay(e,this.commandOptions));xtrim=(...e)=>this.chain(new aC(e,this.commandOptions));xrange=(...e)=>this.chain(new ak(e,this.commandOptions));xrevrange=(...e)=>this.chain(new aP(e,this.commandOptions));zcard=(...e)=>this.chain(new aI(e,this.commandOptions));zcount=(...e)=>this.chain(new aj(e,this.commandOptions));zincrby=(e,t,s)=>this.chain(new aN([e,t,s],this.commandOptions));zinterstore=(...e)=>this.chain(new aL(e,this.commandOptions));zlexcount=(...e)=>this.chain(new aM(e,this.commandOptions));zmscore=(...e)=>this.chain(new aY(e,this.commandOptions));zpopmax=(...e)=>this.chain(new aD(e,this.commandOptions));zpopmin=(...e)=>this.chain(new aU(e,this.commandOptions));zrange=(...e)=>this.chain(new a$(e,this.commandOptions));zrank=(e,t)=>this.chain(new aq([e,t],this.commandOptions));zrem=(e,...t)=>this.chain(new aB([e,...t],this.commandOptions));zremrangebylex=(...e)=>this.chain(new az(e,this.commandOptions));zremrangebyrank=(...e)=>this.chain(new aW(e,this.commandOptions));zremrangebyscore=(...e)=>this.chain(new aG(e,this.commandOptions));zrevrank=(e,t)=>this.chain(new aV([e,t],this.commandOptions));zscan=(...e)=>this.chain(new aF(e,this.commandOptions));zscore=(e,t)=>this.chain(new aK([e,t],this.commandOptions));zunionstore=(...e)=>this.chain(new aJ(e,this.commandOptions));zunion=(...e)=>this.chain(new aH(e,this.commandOptions));get json(){return{arrappend:(...e)=>this.chain(new i6(e,this.commandOptions)),arrindex:(...e)=>this.chain(new i5(e,this.commandOptions)),arrinsert:(...e)=>this.chain(new i4(e,this.commandOptions)),arrlen:(...e)=>this.chain(new i9(e,this.commandOptions)),arrpop:(...e)=>this.chain(new i8(e,this.commandOptions)),arrtrim:(...e)=>this.chain(new i7(e,this.commandOptions)),clear:(...e)=>this.chain(new ne(e,this.commandOptions)),del:(...e)=>this.chain(new nt(e,this.commandOptions)),forget:(...e)=>this.chain(new ns(e,this.commandOptions)),get:(...e)=>this.chain(new nr(e,this.commandOptions)),merge:(...e)=>this.chain(new ni(e,this.commandOptions)),mget:(...e)=>this.chain(new nn(e,this.commandOptions)),mset:(...e)=>this.chain(new na(e,this.commandOptions)),numincrby:(...e)=>this.chain(new no(e,this.commandOptions)),nummultby:(...e)=>this.chain(new nl(e,this.commandOptions)),objkeys:(...e)=>this.chain(new nc(e,this.commandOptions)),objlen:(...e)=>this.chain(new nh(e,this.commandOptions)),resp:(...e)=>this.chain(new nu(e,this.commandOptions)),set:(...e)=>this.chain(new nd(e,this.commandOptions)),strappend:(...e)=>this.chain(new np(e,this.commandOptions)),strlen:(...e)=>this.chain(new nf(e,this.commandOptions)),toggle:(...e)=>this.chain(new nm(e,this.commandOptions)),type:(...e)=>this.chain(new ng(e,this.commandOptions))}}},aZ=new Set(["scan","keys","flushdb","flushall","dbsize","hscan","hgetall","hkeys","lrange","sscan","smembers","xrange","xrevrange","zscan","zrange"]),a0=class{pipelinePromises=new WeakMap;activePipeline=null;indexInCurrentPipeline=0;redis;pipeline;pipelineCounter=0;constructor(e){this.redis=e,this.pipeline=e.pipeline()}async withAutoPipeline(e){let t=this.activePipeline??this.redis.pipeline();this.activePipeline||(this.activePipeline=t,this.indexInCurrentPipeline=0);let s=this.indexInCurrentPipeline++;e(t);let r=this.deferExecution().then(()=>{if(!this.pipelinePromises.has(t)){let e=t.exec({keepErrors:!0});this.pipelineCounter+=1,this.pipelinePromises.set(t,e),this.activePipeline=null}return this.pipelinePromises.get(t)}),i=(await r)[s];if(i.error)throw new rH(`Command failed: ${i.error}`);return i.result}async deferExecution(){await Promise.resolve(),await Promise.resolve()}},a1=class extends r6{constructor(e,t){super([],{...t,headers:{Accept:"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"},path:["psubscribe",...e],streamOptions:{isStreaming:!0,onMessage:t?.streamOptions?.onMessage,signal:t?.streamOptions?.signal}})}},a2=class extends EventTarget{subscriptions;client;listeners;constructor(e,t,s=!1){for(let r of(super(),this.client=e,this.subscriptions=new Map,this.listeners=new Map,t))s?this.subscribeToPattern(r):this.subscribeToChannel(r)}subscribeToChannel(e){let t=new AbortController,s=new a3([e],{streamOptions:{signal:t.signal,onMessage:e=>this.handleMessage(e,!1)}});s.exec(this.client).catch(e=>{"AbortError"!==e.name&&this.dispatchToListeners("error",e)}),this.subscriptions.set(e,{command:s,controller:t,isPattern:!1})}subscribeToPattern(e){let t=new AbortController,s=new a1([e],{streamOptions:{signal:t.signal,onMessage:e=>this.handleMessage(e,!0)}});s.exec(this.client).catch(e=>{"AbortError"!==e.name&&this.dispatchToListeners("error",e)}),this.subscriptions.set(e,{command:s,controller:t,isPattern:!0})}handleMessage(e,t){let s=e.replace(/^data:\s*/,""),r=s.indexOf(","),i=s.indexOf(",",r+1),n=t?s.indexOf(",",i+1):-1;if(-1!==r&&-1!==i){let e=s.slice(0,r);if(t&&"pmessage"===e&&-1!==n){let e=s.slice(r+1,i),t=s.slice(i+1,n),a=s.slice(n+1);try{let s=JSON.parse(a);this.dispatchToListeners("pmessage",{pattern:e,channel:t,message:s}),this.dispatchToListeners(`pmessage:${e}`,{pattern:e,channel:t,message:s})}catch(e){this.dispatchToListeners("error",Error(`Failed to parse message: ${e}`))}}else{let t=s.slice(r+1,i),n=s.slice(i+1);try{if("subscribe"===e||"psubscribe"===e||"unsubscribe"===e||"punsubscribe"===e){let t=Number.parseInt(n);this.dispatchToListeners(e,t)}else{let s=JSON.parse(n);this.dispatchToListeners(e,{channel:t,message:s}),this.dispatchToListeners(`${e}:${t}`,{channel:t,message:s})}}catch(e){this.dispatchToListeners("error",Error(`Failed to parse message: ${e}`))}}}}dispatchToListeners(e,t){let s=this.listeners.get(e);if(s)for(let e of s)e(t)}on(e,t){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e)?.add(t)}removeAllListeners(){this.listeners.clear()}async unsubscribe(e){if(e)for(let t of e){let e=this.subscriptions.get(t);if(e){try{e.controller.abort()}catch{}this.subscriptions.delete(t)}}else{for(let e of this.subscriptions.values())try{e.controller.abort()}catch{}this.subscriptions.clear(),this.removeAllListeners()}}getSubscribedChannels(){return[...this.subscriptions.keys()]}},a3=class extends r6{constructor(e,t){super([],{...t,headers:{Accept:"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"},path:["subscribe",...e],streamOptions:{isStreaming:!0,onMessage:t?.streamOptions?.onMessage,signal:t?.streamOptions?.signal}})}},a6=class{script;sha1;redis;constructor(e,t){this.redis=e,this.script=t,this.sha1="",this.init(t)}async init(e){this.sha1||(this.sha1=await this.digest(e))}async eval(e,t){return await this.init(this.script),await this.redis.eval(this.script,e,t)}async evalsha(e,t){return await this.init(this.script),await this.redis.evalsha(this.sha1,e,t)}async exec(e,t){return await this.init(this.script),await this.redis.evalsha(this.sha1,e,t).catch(async s=>{if(s instanceof Error&&s.message.toLowerCase().includes("noscript"))return await this.redis.eval(this.script,e,t);throw s})}async digest(e){let t=new TextEncoder().encode(e);return[...new Uint8Array(await rF.digest("SHA-1",t))].map(e=>e.toString(16).padStart(2,"0")).join("")}},a5=class{script;sha1;redis;constructor(e,t){this.redis=e,this.sha1="",this.script=t,this.init(t)}async init(e){this.sha1||(this.sha1=await this.digest(e))}async evalRo(e,t){return await this.init(this.script),await this.redis.evalRo(this.script,e,t)}async evalshaRo(e,t){return await this.init(this.script),await this.redis.evalshaRo(this.sha1,e,t)}async exec(e,t){return await this.init(this.script),await this.redis.evalshaRo(this.sha1,e,t).catch(async s=>{if(s instanceof Error&&s.message.toLowerCase().includes("noscript"))return await this.redis.evalRo(this.script,e,t);throw s})}async digest(e){let t=new TextEncoder().encode(e);return[...new Uint8Array(await rF.digest("SHA-1",t))].map(e=>e.toString(16).padStart(2,"0")).join("")}},a4=class{client;opts;enableTelemetry;enableAutoPipelining;constructor(e,t){this.client=e,this.opts=t,this.enableTelemetry=t?.enableTelemetry??!0,t?.readYourWrites===!1&&(this.client.readYourWrites=!1),this.enableAutoPipelining=t?.enableAutoPipelining??!0}get readYourWritesSyncToken(){return this.client.upstashSyncToken}set readYourWritesSyncToken(e){this.client.upstashSyncToken=e}get json(){return{arrappend:(...e)=>new i6(e,this.opts).exec(this.client),arrindex:(...e)=>new i5(e,this.opts).exec(this.client),arrinsert:(...e)=>new i4(e,this.opts).exec(this.client),arrlen:(...e)=>new i9(e,this.opts).exec(this.client),arrpop:(...e)=>new i8(e,this.opts).exec(this.client),arrtrim:(...e)=>new i7(e,this.opts).exec(this.client),clear:(...e)=>new ne(e,this.opts).exec(this.client),del:(...e)=>new nt(e,this.opts).exec(this.client),forget:(...e)=>new ns(e,this.opts).exec(this.client),get:(...e)=>new nr(e,this.opts).exec(this.client),merge:(...e)=>new ni(e,this.opts).exec(this.client),mget:(...e)=>new nn(e,this.opts).exec(this.client),mset:(...e)=>new na(e,this.opts).exec(this.client),numincrby:(...e)=>new no(e,this.opts).exec(this.client),nummultby:(...e)=>new nl(e,this.opts).exec(this.client),objkeys:(...e)=>new nc(e,this.opts).exec(this.client),objlen:(...e)=>new nh(e,this.opts).exec(this.client),resp:(...e)=>new nu(e,this.opts).exec(this.client),set:(...e)=>new nd(e,this.opts).exec(this.client),strappend:(...e)=>new np(e,this.opts).exec(this.client),strlen:(...e)=>new nf(e,this.opts).exec(this.client),toggle:(...e)=>new nm(e,this.opts).exec(this.client),type:(...e)=>new ng(e,this.opts).exec(this.client)}}use=e=>{let t=this.client.request.bind(this.client);this.client.request=s=>e(s,t)};addTelemetry=e=>{if(this.enableTelemetry)try{this.client.mergeTelemetry(e)}catch{}};createScript(e,t){return t?.readonly?new a5(this,e):new a6(this,e)}pipeline=()=>new aQ({client:this.client,commandOptions:this.opts,multiExec:!1});autoPipeline=()=>(function e(t,s){return t.autoPipelineExecutor||(t.autoPipelineExecutor=new a0(t)),new Proxy(t,{get:(t,r)=>{if("pipelineCounter"===r)return t.autoPipelineExecutor.pipelineCounter;if("json"===r)return e(t,!0);let i=r in t&&!(r in t.autoPipelineExecutor.pipeline),n=aZ.has(r);return i||n?t[r]:(s?"function"==typeof t.autoPipelineExecutor.pipeline.json[r]:"function"==typeof t.autoPipelineExecutor.pipeline[r])?(...e)=>t.autoPipelineExecutor.withAutoPipeline(t=>{s?t.json[r](...e):t[r](...e)}):t.autoPipelineExecutor.pipeline[r]}})})(this);multi=()=>new aQ({client:this.client,commandOptions:this.opts,multiExec:!0});bitfield=(...e)=>new r8(e,this.client,this.opts);append=(...e)=>new r4(e,this.opts).exec(this.client);bitcount=(...e)=>new r9(e,this.opts).exec(this.client);bitop=(e,t,s,...r)=>new r7([e,t,s,...r],this.opts).exec(this.client);bitpos=(...e)=>new ie(e,this.opts).exec(this.client);copy=(...e)=>new it(e,this.opts).exec(this.client);dbsize=()=>new is(this.opts).exec(this.client);decr=(...e)=>new ir(e,this.opts).exec(this.client);decrby=(...e)=>new ii(e,this.opts).exec(this.client);del=(...e)=>new ia(e,this.opts).exec(this.client);echo=(...e)=>new io(e,this.opts).exec(this.client);evalRo=(...e)=>new il(e,this.opts).exec(this.client);eval=(...e)=>new ic(e,this.opts).exec(this.client);evalshaRo=(...e)=>new ih(e,this.opts).exec(this.client);evalsha=(...e)=>new iu(e,this.opts).exec(this.client);exec=e=>new id(e,this.opts).exec(this.client);exists=(...e)=>new ip(e,this.opts).exec(this.client);expire=(...e)=>new im(e,this.opts).exec(this.client);expireat=(...e)=>new ig(e,this.opts).exec(this.client);flushall=e=>new iw(e,this.opts).exec(this.client);flushdb=(...e)=>new ib(e,this.opts).exec(this.client);geoadd=(...e)=>new iy(e,this.opts).exec(this.client);geopos=(...e)=>new ix(e,this.opts).exec(this.client);geodist=(...e)=>new iv(e,this.opts).exec(this.client);geohash=(...e)=>new i_(e,this.opts).exec(this.client);geosearch=(...e)=>new iS(e,this.opts).exec(this.client);geosearchstore=(...e)=>new iO(e,this.opts).exec(this.client);get=(...e)=>new iE(e,this.opts).exec(this.client);getbit=(...e)=>new ik(e,this.opts).exec(this.client);getdel=(...e)=>new iT(e,this.opts).exec(this.client);getex=(...e)=>new iR(e,this.opts).exec(this.client);getrange=(...e)=>new iP(e,this.opts).exec(this.client);getset=(e,t)=>new iC([e,t],this.opts).exec(this.client);hdel=(...e)=>new iA(e,this.opts).exec(this.client);hexists=(...e)=>new iI(e,this.opts).exec(this.client);hexpire=(...e)=>new ij(e,this.opts).exec(this.client);hexpireat=(...e)=>new iN(e,this.opts).exec(this.client);hexpiretime=(...e)=>new iL(e,this.opts).exec(this.client);httl=(...e)=>new iZ(e,this.opts).exec(this.client);hpexpire=(...e)=>new iD(e,this.opts).exec(this.client);hpexpireat=(...e)=>new iU(e,this.opts).exec(this.client);hpexpiretime=(...e)=>new i$(e,this.opts).exec(this.client);hpttl=(...e)=>new iq(e,this.opts).exec(this.client);hpersist=(...e)=>new iM(e,this.opts).exec(this.client);hget=(...e)=>new iB(e,this.opts).exec(this.client);hgetall=(...e)=>new iz(e,this.opts).exec(this.client);hincrby=(...e)=>new iW(e,this.opts).exec(this.client);hincrbyfloat=(...e)=>new iG(e,this.opts).exec(this.client);hkeys=(...e)=>new iV(e,this.opts).exec(this.client);hlen=(...e)=>new iF(e,this.opts).exec(this.client);hmget=(...e)=>new iK(e,this.opts).exec(this.client);hmset=(e,t)=>new iH([e,t],this.opts).exec(this.client);hrandfield=(e,t,s)=>new r5([e,t,s],this.opts).exec(this.client);hscan=(...e)=>new iJ(e,this.opts).exec(this.client);hset=(e,t)=>new iX([e,t],this.opts).exec(this.client);hsetnx=(e,t,s)=>new iY([e,t,s],this.opts).exec(this.client);hstrlen=(...e)=>new iQ(e,this.opts).exec(this.client);hvals=(...e)=>new i0(e,this.opts).exec(this.client);incr=(...e)=>new i1(e,this.opts).exec(this.client);incrby=(...e)=>new i2(e,this.opts).exec(this.client);incrbyfloat=(...e)=>new i3(e,this.opts).exec(this.client);keys=(...e)=>new nw(e,this.opts).exec(this.client);lindex=(...e)=>new nb(e,this.opts).exec(this.client);linsert=(e,t,s,r)=>new ny([e,t,s,r],this.opts).exec(this.client);llen=(...e)=>new nv(e,this.opts).exec(this.client);lmove=(...e)=>new n_(e,this.opts).exec(this.client);lpop=(...e)=>new nS(e,this.opts).exec(this.client);lmpop=(...e)=>new nx(e,this.opts).exec(this.client);lpos=(...e)=>new nO(e,this.opts).exec(this.client);lpush=(e,...t)=>new nE([e,...t],this.opts).exec(this.client);lpushx=(e,...t)=>new nk([e,...t],this.opts).exec(this.client);lrange=(...e)=>new nT(e,this.opts).exec(this.client);lrem=(e,t,s)=>new nR([e,t,s],this.opts).exec(this.client);lset=(e,t,s)=>new nP([e,t,s],this.opts).exec(this.client);ltrim=(...e)=>new nC(e,this.opts).exec(this.client);mget=(...e)=>new nA(e,this.opts).exec(this.client);mset=e=>new nI([e],this.opts).exec(this.client);msetnx=e=>new nj([e],this.opts).exec(this.client);persist=(...e)=>new nN(e,this.opts).exec(this.client);pexpire=(...e)=>new nL(e,this.opts).exec(this.client);pexpireat=(...e)=>new nM(e,this.opts).exec(this.client);pfadd=(...e)=>new nD(e,this.opts).exec(this.client);pfcount=(...e)=>new nU(e,this.opts).exec(this.client);pfmerge=(...e)=>new n$(e,this.opts).exec(this.client);ping=e=>new nq(e,this.opts).exec(this.client);psetex=(e,t,s)=>new nB([e,t,s],this.opts).exec(this.client);psubscribe=e=>{let t=Array.isArray(e)?e:[e];return new a2(this.client,t,!0)};pttl=(...e)=>new nz(e,this.opts).exec(this.client);publish=(...e)=>new nW(e,this.opts).exec(this.client);randomkey=()=>new nG().exec(this.client);rename=(...e)=>new nV(e,this.opts).exec(this.client);renamenx=(...e)=>new nF(e,this.opts).exec(this.client);rpop=(...e)=>new nK(e,this.opts).exec(this.client);rpush=(e,...t)=>new nH([e,...t],this.opts).exec(this.client);rpushx=(e,...t)=>new nJ([e,...t],this.opts).exec(this.client);sadd=(e,t,...s)=>new nX([e,t,...s],this.opts).exec(this.client);scan(e,t){return new nY([e,t],this.opts).exec(this.client)}scard=(...e)=>new nQ(e,this.opts).exec(this.client);scriptExists=(...e)=>new nZ(e,this.opts).exec(this.client);scriptFlush=(...e)=>new n0(e,this.opts).exec(this.client);scriptLoad=(...e)=>new n1(e,this.opts).exec(this.client);sdiff=(...e)=>new n2(e,this.opts).exec(this.client);sdiffstore=(...e)=>new n3(e,this.opts).exec(this.client);set=(e,t,s)=>new n6([e,t,s],this.opts).exec(this.client);setbit=(...e)=>new n5(e,this.opts).exec(this.client);setex=(e,t,s)=>new n4([e,t,s],this.opts).exec(this.client);setnx=(e,t)=>new n9([e,t],this.opts).exec(this.client);setrange=(...e)=>new n8(e,this.opts).exec(this.client);sinter=(...e)=>new n7(e,this.opts).exec(this.client);sinterstore=(...e)=>new ae(e,this.opts).exec(this.client);sismember=(e,t)=>new at([e,t],this.opts).exec(this.client);smismember=(e,t)=>new ar([e,t],this.opts).exec(this.client);smembers=(...e)=>new as(e,this.opts).exec(this.client);smove=(e,t,s)=>new ai([e,t,s],this.opts).exec(this.client);spop=(...e)=>new an(e,this.opts).exec(this.client);srandmember=(...e)=>new aa(e,this.opts).exec(this.client);srem=(e,...t)=>new ao([e,...t],this.opts).exec(this.client);sscan=(...e)=>new al(e,this.opts).exec(this.client);strlen=(...e)=>new ac(e,this.opts).exec(this.client);subscribe=e=>{let t=Array.isArray(e)?e:[e];return new a2(this.client,t)};sunion=(...e)=>new ah(e,this.opts).exec(this.client);sunionstore=(...e)=>new au(e,this.opts).exec(this.client);time=()=>new ad().exec(this.client);touch=(...e)=>new ap(e,this.opts).exec(this.client);ttl=(...e)=>new af(e,this.opts).exec(this.client);type=(...e)=>new am(e,this.opts).exec(this.client);unlink=(...e)=>new ag(e,this.opts).exec(this.client);xadd=(...e)=>new ab(e,this.opts).exec(this.client);xack=(...e)=>new aw(e,this.opts).exec(this.client);xdel=(...e)=>new a_(e,this.opts).exec(this.client);xgroup=(...e)=>new ax(e,this.opts).exec(this.client);xread=(...e)=>new aT(e,this.opts).exec(this.client);xreadgroup=(...e)=>new aR(e,this.opts).exec(this.client);xinfo=(...e)=>new aS(e,this.opts).exec(this.client);xlen=(...e)=>new aO(e,this.opts).exec(this.client);xpending=(...e)=>new aE(e,this.opts).exec(this.client);xclaim=(...e)=>new av(e,this.opts).exec(this.client);xautoclaim=(...e)=>new ay(e,this.opts).exec(this.client);xtrim=(...e)=>new aC(e,this.opts).exec(this.client);xrange=(...e)=>new ak(e,this.opts).exec(this.client);xrevrange=(...e)=>new aP(e,this.opts).exec(this.client);zadd=(...e)=>("score"in e[1],new aA([e[0],e[1],...e.slice(2)],this.opts).exec(this.client));zcard=(...e)=>new aI(e,this.opts).exec(this.client);zcount=(...e)=>new aj(e,this.opts).exec(this.client);zdiffstore=(...e)=>new aX(e,this.opts).exec(this.client);zincrby=(e,t,s)=>new aN([e,t,s],this.opts).exec(this.client);zinterstore=(...e)=>new aL(e,this.opts).exec(this.client);zlexcount=(...e)=>new aM(e,this.opts).exec(this.client);zmscore=(...e)=>new aY(e,this.opts).exec(this.client);zpopmax=(...e)=>new aD(e,this.opts).exec(this.client);zpopmin=(...e)=>new aU(e,this.opts).exec(this.client);zrange=(...e)=>new a$(e,this.opts).exec(this.client);zrank=(e,t)=>new aq([e,t],this.opts).exec(this.client);zrem=(e,...t)=>new aB([e,...t],this.opts).exec(this.client);zremrangebylex=(...e)=>new az(e,this.opts).exec(this.client);zremrangebyrank=(...e)=>new aW(e,this.opts).exec(this.client);zremrangebyscore=(...e)=>new aG(e,this.opts).exec(this.client);zrevrank=(e,t)=>new aV([e,t],this.opts).exec(this.client);zscan=(...e)=>new aF(e,this.opts).exec(this.client);zscore=(e,t)=>new aK([e,t],this.opts).exec(this.client);zunion=(...e)=>new aH(e,this.opts).exec(this.client);zunionstore=(...e)=>new aJ(e,this.opts).exec(this.client)},a9=s(356).Buffer;"undefined"==typeof atob&&(global.atob=e=>a9.from(e,"base64").toString("utf8"));var a8=class e extends a4{constructor(e){if("request"in e)return void super(e);if(e.url?(e.url.startsWith(" ")||e.url.endsWith(" ")||/\r|\n/.test(e.url))&&console.warn("[Upstash Redis] The redis url contains whitespace or newline, which can cause errors!"):console.warn("[Upstash Redis] The 'url' property is missing or undefined in your Redis config."),e.token?(e.token.startsWith(" ")||e.token.endsWith(" ")||/\r|\n/.test(e.token))&&console.warn("[Upstash Redis] The redis token contains whitespace or newline, which can cause errors!"):console.warn("[Upstash Redis] The 'token' property is missing or undefined in your Redis config."),super(new rZ({baseUrl:e.url,retry:e.retry,headers:{authorization:`Bearer ${e.token}`},agent:e.agent,responseEncoding:e.responseEncoding,cache:e.cache??"no-store",signal:e.signal,keepAlive:e.keepAlive,readYourWrites:e.readYourWrites}),{automaticDeserialization:e.automaticDeserialization,enableTelemetry:!process.env.UPSTASH_DISABLE_TELEMETRY,latencyLogging:e.latencyLogging,enableAutoPipelining:e.enableAutoPipelining}),this.addTelemetry({runtime:"edge-light",platform:process.env.VERCEL?"vercel":process.env.AWS_REGION?"aws":"unknown",sdk:"@upstash/redis@v1.35.0"}),this.enableAutoPipelining)return this.autoPipeline()}static fromEnv(t){if(void 0===process.env)throw TypeError('[Upstash Redis] Unable to get environment variables, `process.env` is undefined. If you are deploying to cloudflare, please import from "@upstash/redis/cloudflare" instead');let s=process.env.UPSTASH_REDIS_REST_URL||process.env.KV_REST_API_URL;s||console.warn("[Upstash Redis] Unable to find environment variable: `UPSTASH_REDIS_REST_URL`");let r=process.env.UPSTASH_REDIS_REST_TOKEN||process.env.KV_REST_API_TOKEN;return r||console.warn("[Upstash Redis] Unable to find environment variable: `UPSTASH_REDIS_REST_TOKEN`"),new e({...t,url:s,token:r})}};let a7=process.env.UPSTASH_REDIS_REST_URL,oe=process.env.UPSTASH_REDIS_REST_TOKEN;a7&&oe||console.error("Upstash Redis URL or Token is not defined in environment variables.");let ot=a7&&oe?new a8({url:a7,token:oe}):null,os=parseInt(process.env.RATE_LIMIT_MAX_REQUESTS||"10"),or=parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS||"10"),oi=ot?new rV.Ratelimit({redis:ot,limiter:rV.Ratelimit.slidingWindow(os,`${or} s`),analytics:!0,prefix:"@upstash/ratelimit/dukancard"}):null;async function on(e){if("true"===process.env.PLAYWRIGHT_TESTING||"true"===e.headers.get("x-playwright-testing"))return await oa(e);let t=e.nextUrl.clone(),s=t.hostname,r=t.protocol;if(!(s.includes("localhost")||s.includes("ngrok.io")||s.includes("ngrok-free.app")||s.includes("127.0.0.1"))){let e=!1;if(s.startsWith("www.")&&(t.hostname=s.replace("www.",""),e=!0),"http:"===r&&(t.protocol="https:",e=!0),e)return et.redirect(t.toString(),301)}if(e.nextUrl.pathname.startsWith("/api/")&&!e.nextUrl.pathname.startsWith("/api/webhooks/"))if(oi){let t=e.headers.get("x-forwarded-for"),s=t?t.split(",")[0].trim():"127.0.0.1";try{let{success:e,limit:t,remaining:r,reset:i}=await oi.limit(s);if(!e)return new et("Too Many Requests",{status:429,headers:{"X-RateLimit-Limit":t.toString(),"X-RateLimit-Remaining":r.toString(),"X-RateLimit-Reset":new Date(1e3*i).toISOString()}})}catch(e){console.error("Rate limiting error:",e)}}else console.warn("Rate limiting skipped: Redis not configured");return await rG(e)}async function oa(e){let{pathname:t}=e.nextUrl,s=e.headers.get("x-test-auth-state"),r=e.headers.get("x-test-user-type"),i="true"===e.headers.get("x-test-has-profile"),n=e.headers.get("x-test-business-slug"),a=e.headers.get("x-test-plan-id")||"free",o=["/dashboard","/onboarding","/choose-role"].some(e=>t.startsWith(e)),l="true"===e.nextUrl.searchParams.get("logged_out");if("unauthenticated"===s||"authenticated"!==s){if(o){let s=e.nextUrl.clone();return s.pathname="/login",s.searchParams.set("next",t),et.redirect(s)}return et.next()}if("authenticated"===s){if(!i){if("/choose-role"!==t&&"/onboarding"!==t&&!l){let t=e.nextUrl.clone();return t.pathname="/choose-role",et.redirect(t)}return et.next()}if(i&&r){if("business"===r&&!n&&"/onboarding"!==t&&!l){let t=e.nextUrl.clone();return t.pathname="/onboarding",et.redirect(t)}if("business"===r&&"free"===a&&t.startsWith("/dashboard/business/analytics")){let t=e.nextUrl.clone();return t.pathname="/dashboard/business/plan",t.searchParams.set("upgrade","analytics"),et.redirect(t)}if(("/login"===t||"/choose-role"===t)&&!("/login"===t&&l)){let t=e.nextUrl.clone();return t.pathname="business"===r?"/dashboard/business":"/dashboard/customer",et.redirect(t)}if("/onboarding"===t&&"customer"===r&&!l){let t=e.nextUrl.clone();return t.pathname="/dashboard/customer",et.redirect(t)}if(t.startsWith("/dashboard/customer")&&"customer"!==r&&!l){let t=e.nextUrl.clone();return t.pathname="/dashboard/business",et.redirect(t)}if(t.startsWith("/dashboard/business")&&"business"!==r&&!l){let t=e.nextUrl.clone();return t.pathname="/dashboard/customer",et.redirect(t)}}}return et.next()}let oo={matcher:["/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"]},ol=(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401}),{...g}),oc=ol.middleware||ol.default,oh="/middleware";if("function"!=typeof oc)throw Object.defineProperty(Error(`The Middleware "${oh}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function ou(e){return tg({...e,page:oh,handler:async(...e)=>{try{return await oc(...e)}catch(i){let t=e[0],s=new URL(t.url),r=s.pathname+s.search;throw await v(i,{path:r,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},355:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let i=r(s(729));t.PostgrestClient=i.default;let n=r(s(665));t.PostgrestQueryBuilder=n.default;let a=r(s(373));t.PostgrestFilterBuilder=a.default;let o=r(s(861));t.PostgrestTransformBuilder=o.default;let l=r(s(279));t.PostgrestBuilder=l.default;let c=r(s(784));t.PostgrestError=c.default,t.default={PostgrestClient:i.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:c.default}},356:e=>{"use strict";e.exports=require("node:buffer")},373:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(861));class n extends i.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let s=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:r}={}){let i="";"plain"===r?i="pl":"phrase"===r?i="ph":"websearch"===r&&(i="w");let n=void 0===s?"":`(${s})`;return this.url.searchParams.append(e,`${i}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){let r=s?`${s}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}}t.default=n},521:e=>{"use strict";e.exports=require("node:async_hooks")},552:(e,t,s)=>{"use strict";var r=s(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return n}});let i=s(201),n={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:s,method:i,headers:n,body:a,cache:o,credentials:l,integrity:c,mode:h,redirect:u,referrer:d,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:s,method:i,headers:[...Array.from(n),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?r.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:h,redirect:u,referrer:d,referrerPolicy:p}}}async function o(e,t){let s=(0,i.getTestReqInfo)(t,n);if(!s)return e(t);let{testData:o,proxyPort:l}=s,c=await a(o,t),h=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!h.ok)throw Object.defineProperty(Error(`Proxy request failed: ${h.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let u=await h.json(),{api:d}=u;switch(d){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:p,headers:f,body:m}=u.response;return new Response(m?r.from(m,"base64"):null,{status:p,headers:new Headers(f)})}function l(e){return s.g.fetch=function(t,s){var r;return(null==s||null==(r=s.next)?void 0:r.internal)?e(t,s):o(e,new Request(t,s))},()=>{s.g.fetch=e}}},554:(e,t)=>{"use strict";t.qg=function(e,t){let s=new o,r=e.length;if(r<2)return s;let i=t?.decode||h,n=0;do{let t=e.indexOf("=",n);if(-1===t)break;let a=e.indexOf(";",n),o=-1===a?r:a;if(t>o){n=e.lastIndexOf(";",t-1)+1;continue}let h=l(e,n,t),u=c(e,t,h),d=e.slice(h,u);if(void 0===s[d]){let r=l(e,t+1,o),n=c(e,o,r),a=i(e.slice(r,n));s[d]=a}n=o+1}while(n<r);return s},t.lK=function(e,t,o){let l=o?.encode||encodeURIComponent;if(!s.test(e))throw TypeError(`argument name is invalid: ${e}`);let c=l(t);if(!r.test(c))throw TypeError(`argument val is invalid: ${t}`);let h=e+"="+c;if(!o)return h;if(void 0!==o.maxAge){if(!Number.isInteger(o.maxAge))throw TypeError(`option maxAge is invalid: ${o.maxAge}`);h+="; Max-Age="+o.maxAge}if(o.domain){if(!i.test(o.domain))throw TypeError(`option domain is invalid: ${o.domain}`);h+="; Domain="+o.domain}if(o.path){if(!n.test(o.path))throw TypeError(`option path is invalid: ${o.path}`);h+="; Path="+o.path}if(o.expires){var u;if(u=o.expires,"[object Date]"!==a.call(u)||!Number.isFinite(o.expires.valueOf()))throw TypeError(`option expires is invalid: ${o.expires}`);h+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(h+="; HttpOnly"),o.secure&&(h+="; Secure"),o.partitioned&&(h+="; Partitioned"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():void 0){case"low":h+="; Priority=Low";break;case"medium":h+="; Priority=Medium";break;case"high":h+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${o.priority}`)}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":h+="; SameSite=Strict";break;case"lax":h+="; SameSite=Lax";break;case"none":h+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${o.sameSite}`)}return h};let s=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,o=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,s){do{let s=e.charCodeAt(t);if(32!==s&&9!==s)return t}while(++t<s);return s}function c(e,t,s){for(;t>s;){let s=e.charCodeAt(--t);if(32!==s&&9!==s)return t+1}return s}function h(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},665:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(373));class n{constructor(e,{headers:t={},schema:s,fetch:r}){this.url=e,this.headers=t,this.schema=s,this.fetch=r}select(e,{head:t=!1,count:s}={}){let r=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!r?"":('"'===e&&(r=!r),e)).join("");return this.url.searchParams.set("select",n),s&&(this.headers.Prefer=`count=${s}`),new i.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:s=!0}={}){let r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),s||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:s=!1,count:r,defaultToNull:n=!0}={}){let a=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),r&&a.push(`count=${r}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),this.headers.Prefer=s.join(","),new i.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new i.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},724:e=>{"use strict";var t=Object.defineProperty,s=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,n={};function a(e){var t;let s=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),r=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===s.length?r:`${r}; ${s.join("; ")}`}function o(e){let t=new Map;for(let s of e.split(/; */)){if(!s)continue;let e=s.indexOf("=");if(-1===e){t.set(s,"true");continue}let[r,i]=[s.slice(0,e),s.slice(e+1)];try{t.set(r,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,s],...r]=o(e),{domain:i,expires:n,httponly:a,maxage:l,path:u,samesite:d,secure:p,partitioned:f,priority:m}=Object.fromEntries(r.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var g,w,b={name:t,value:decodeURIComponent(s),domain:i,...n&&{expires:new Date(n)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:u,...d&&{sameSite:c.includes(g=(g=d).toLowerCase())?g:void 0},...p&&{secure:!0},...m&&{priority:h.includes(w=(w=m).toLowerCase())?w:void 0},...f&&{partitioned:!0}};let e={};for(let t in b)b[t]&&(e[t]=b[t]);return e}}((e,s)=>{for(var r in s)t(e,r,{get:s[r],enumerable:!0})})(n,{RequestCookies:()=>u,ResponseCookies:()=>d,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let l of r(n))i.call(e,l)||l===a||t(e,l,{get:()=>n[l],enumerable:!(o=s(n,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var c=["strict","lax","none"],h=["low","medium","high"],u=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,s]of o(t))this._parsed.set(e,{name:e,value:s})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let s=Array.from(this._parsed);if(!e.length)return s.map(([e,t])=>t);let r="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return s.filter(([e])=>e===r).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,s]=1===e.length?[e[0].name,e[0].value]:e,r=this._parsed;return r.set(t,{name:t,value:s}),this._headers.set("cookie",Array.from(r).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,s=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),s}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,s,r;this._parsed=new Map,this._headers=e;let i=null!=(r=null!=(s=null==(t=e.getSetCookie)?void 0:t.call(e))?s:e.get("set-cookie"))?r:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,s,r,i,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(s=e.charAt(o))){for(r=o,o+=1,l(),i=o;o<e.length&&"="!==(s=e.charAt(o))&&";"!==s&&","!==s;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=i,a.push(e.substring(t,r)),t=o):o=r+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let s=Array.from(this._parsed.values());if(!e.length)return s;let r="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return s.filter(e=>e.name===r)}has(e){return this._parsed.has(e)}set(...e){let[t,s,r]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:s,...r})),function(e,t){for(let[,s]of(t.delete("set-cookie"),e)){let e=a(s);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,s]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...s,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},729:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(665)),n=r(s(373)),a=s(128);class o{constructor(e,{headers:t={},schema:s,fetch:r}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=r}from(e){let t=new URL(`${this.url}/${e}`);return new i.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:r=!1,count:i}={}){let a,o,l=new URL(`${this.url}/rpc/${e}`);s||r?(a=s?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let c=Object.assign({},this.headers);return i&&(c.Prefer=`count=${i}`),new n.default({method:a,url:l,headers:c,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},784:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class s extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=s},802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,s="~";function r(){}function i(e,t,s){this.fn=e,this.context=t,this.once=s||!1}function n(e,t,r,n,a){if("function"!=typeof r)throw TypeError("The listener must be a function");var o=new i(r,n||e,a),l=s?s+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function o(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(s=!1)),o.prototype.eventNames=function(){var e,r,i=[];if(0===this._eventsCount)return i;for(r in e=this._events)t.call(e,r)&&i.push(s?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=s?s+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,n=r.length,a=Array(n);i<n;i++)a[i]=r[i].fn;return a},o.prototype.listenerCount=function(e){var t=s?s+e:e,r=this._events[t];return r?r.fn?1:r.length:0},o.prototype.emit=function(e,t,r,i,n,a){var o=s?s+e:e;if(!this._events[o])return!1;var l,c,h=this._events[o],u=arguments.length;if(h.fn){switch(h.once&&this.removeListener(e,h.fn,void 0,!0),u){case 1:return h.fn.call(h.context),!0;case 2:return h.fn.call(h.context,t),!0;case 3:return h.fn.call(h.context,t,r),!0;case 4:return h.fn.call(h.context,t,r,i),!0;case 5:return h.fn.call(h.context,t,r,i,n),!0;case 6:return h.fn.call(h.context,t,r,i,n,a),!0}for(c=1,l=Array(u-1);c<u;c++)l[c-1]=arguments[c];h.fn.apply(h.context,l)}else{var d,p=h.length;for(c=0;c<p;c++)switch(h[c].once&&this.removeListener(e,h[c].fn,void 0,!0),u){case 1:h[c].fn.call(h[c].context);break;case 2:h[c].fn.call(h[c].context,t);break;case 3:h[c].fn.call(h[c].context,t,r);break;case 4:h[c].fn.call(h[c].context,t,r,i);break;default:if(!l)for(d=1,l=Array(u-1);d<u;d++)l[d-1]=arguments[d];h[c].fn.apply(h[c].context,l)}}return!0},o.prototype.on=function(e,t,s){return n(this,e,t,s,!1)},o.prototype.once=function(e,t,s){return n(this,e,t,s,!0)},o.prototype.removeListener=function(e,t,r,i){var n=s?s+e:e;if(!this._events[n])return this;if(!t)return a(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||i&&!o.once||r&&o.context!==r||a(this,n);else{for(var l=0,c=[],h=o.length;l<h;l++)(o[l].fn!==t||i&&!o[l].once||r&&o[l].context!==r)&&c.push(o[l]);c.length?this._events[n]=1===c.length?c[0]:c:a(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=s?s+e:e,this._events[t]&&a(this,t)):(this._events=new r,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=s,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,s){let r=0,i=e.length;for(;i>0;){let n=i/2|0,a=r+n;0>=s(e[a],t)?(r=++a,i-=n+1):i=n}return r}},821:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0});let r=s(574);class i{constructor(){this._queue=[]}enqueue(e,t){let s={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(s);let i=r.default(this._queue,s,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,s)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,s)=>{let r=s(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let n=(e,t,s)=>new Promise((n,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void n(e);let o=setTimeout(()=>{if("function"==typeof s){try{n(s())}catch(e){a(e)}return}let r="string"==typeof s?s:`Promise timed out after ${t} milliseconds`,o=s instanceof Error?s:new i(r);"function"==typeof e.cancel&&e.cancel(),a(o)},t);r(e.then(n,a),()=>{clearTimeout(o)})});e.exports=n,e.exports.default=n,e.exports.TimeoutError=i}},s={};function r(e){var i=s[e];if(void 0!==i)return i.exports;var n=s[e]={exports:{}},a=!0;try{t[e](n,n.exports,r),a=!1}finally{a&&delete s[e]}return n.exports}r.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=r(993),t=r(816),s=r(821),n=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,r,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=n,this._resolveIdle=n,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:s.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(r=null==(t=e.intervalCap)?void 0:t.toString())?r:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(i=e.interval)?void 0:i.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=n,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=n,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,s={}){return new Promise((r,i)=>{let n=async()=>{this._pendingCount++,this._intervalCount++;try{let n=void 0===this._timeout&&void 0===s.timeout?e():t.default(Promise.resolve(e()),void 0===s.timeout?this._timeout:s.timeout,()=>{(void 0===s.throwOnTimeout?this._throwOnTimeout:s.throwOnTimeout)&&i(a)});r(await n)}catch(e){i(e)}this._next()};this._queue.enqueue(n,s),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=o})(),e.exports=i})()},815:(e,t,s)=>{"use strict";e.exports=s(35)},861:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(279));class n extends i.default{select(e){let t=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:i=r}={}){let n=i?`${i}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){let r=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:r=s}={}){let i=void 0===r?"offset":`${r}.offset`,n=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:i=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!=(a=this.headers.Accept)?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,s){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},n=t.split(r),a=(s||{}).decode||e,o=0;o<n.length;o++){var l=n[o],c=l.indexOf("=");if(!(c<0)){var h=l.substr(0,c).trim(),u=l.substr(++c,l.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==i[h]&&(i[h]=function(e,t){try{return t(e)}catch(t){return e}}(u,a))}}return i},t.serialize=function(e,t,r){var n=r||{},a=n.encode||s;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=n.maxAge){var c=n.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(n.domain){if(!i.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!i.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,s=encodeURIComponent,r=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},905:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{interceptTestApis:function(){return n},wrapRequestHandler:function(){return a}});let r=s(201),i=s(552);function n(){return(0,i.interceptFetch)(s.g.fetch)}function a(e){return(t,s)=>(0,r.withRequest)(t,i.reader,()=>e(t,s))}},956:(e,t,s)=>{(()=>{"use strict";var t={491:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let r=s(223),i=s(172),n=s(930),a="context",o=new r.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(a,e,n.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,s,...r){return this._getContextManager().with(e,t,s,...r)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(a,n.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let r=s(56),i=s(912),n=s(957),a=s(172);class o{constructor(){function e(e){return function(...t){let s=(0,a.getGlobal)("diag");if(s)return s[e](...t)}}let t=this;t.setLogger=(e,s={logLevel:n.DiagLogLevel.INFO})=>{var r,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(r=e.stack)?r:e.message),!1}"number"==typeof s&&(s={logLevel:s});let c=(0,a.getGlobal)("diag"),h=(0,i.createLogLevelDiagLogger)(null!=(o=s.logLevel)?o:n.DiagLogLevel.INFO,e);if(c&&!s.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),h.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",h,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new r.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let r=s(660),i=s(172),n=s(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(a,e,n.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(a)||r.NOOP_METER_PROVIDER}getMeter(e,t,s){return this.getMeterProvider().getMeter(e,t,s)}disable(){(0,i.unregisterGlobal)(a,n.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let r=s(172),i=s(874),n=s(194),a=s(277),o=s(369),l=s(930),c="propagation",h=new i.NoopTextMapPropagator;class u{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalPropagator(e){return(0,r.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,s=n.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,s)}extract(e,t,s=n.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,s)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,r.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,r.getGlobal)(c)||h}}t.PropagationAPI=u},997:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let r=s(172),i=s(846),n=s(139),a=s(607),o=s(930),l="trace";class c{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=n.wrapSpanContext,this.isSpanContextValid=n.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,r.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,r.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,r.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let r=s(491),i=(0,s(780).createContextKey)("OpenTelemetry Baggage Key");function n(e){return e.getValue(i)||void 0}t.getBaggage=n,t.getActiveBaggage=function(){return n(r.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class s{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let r=new s(this._entries);return r._entries.set(e,t),r}removeEntry(e){let t=new s(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new s(this._entries);for(let s of e)t._entries.delete(s);return t}clear(){return new s}}t.BaggageImpl=s},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let r=s(930),i=s(993),n=s(830),a=r.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:n.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=s(491).ContextAPI.getInstance()},223:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let r=s(780);class i{active(){return r.ROOT_CONTEXT}with(e,t,s,...r){return t.call(s,...r)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class s{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,r)=>{let i=new s(t._currentContext);return i._currentContext.set(e,r),i},t.deleteValue=e=>{let r=new s(t._currentContext);return r._currentContext.delete(e),r}}}t.ROOT_CONTEXT=new s},506:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=s(930).DiagAPI.instance()},56:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let r=s(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return n("debug",this._namespace,e)}error(...e){return n("error",this._namespace,e)}info(...e){return n("info",this._namespace,e)}warn(...e){return n("warn",this._namespace,e)}verbose(...e){return n("verbose",this._namespace,e)}}function n(e,t,s){let i=(0,r.getGlobal)("diag");if(i)return s.unshift(t),i[e](...s)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let s=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class r{constructor(){for(let e=0;e<s.length;e++)this[s[e].n]=function(e){return function(...t){if(console){let s=console[e];if("function"!=typeof s&&(s=console.log),"function"==typeof s)return s.apply(console,t)}}}(s[e].c)}}t.DiagConsoleLogger=r},912:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let r=s(957);t.createLogLevelDiagLogger=function(e,t){function s(s,r){let i=t[s];return"function"==typeof i&&e>=r?i.bind(t):function(){}}return e<r.DiagLogLevel.NONE?e=r.DiagLogLevel.NONE:e>r.DiagLogLevel.ALL&&(e=r.DiagLogLevel.ALL),t=t||{},{error:s("error",r.DiagLogLevel.ERROR),warn:s("warn",r.DiagLogLevel.WARN),info:s("info",r.DiagLogLevel.INFO),debug:s("debug",r.DiagLogLevel.DEBUG),verbose:s("verbose",r.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let r=s(200),i=s(521),n=s(130),a=i.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=r._globalThis;t.registerGlobal=function(e,t,s,r=!1){var n;let a=l[o]=null!=(n=l[o])?n:{version:i.VERSION};if(!r&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return s.error(t.stack||t.message),!1}if(a.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${i.VERSION}`);return s.error(t.stack||t.message),!1}return a[e]=t,s.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,s;let r=null==(t=l[o])?void 0:t.version;if(r&&(0,n.isCompatible)(r))return null==(s=l[o])?void 0:s[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let s=l[o];s&&delete s[e]}},130:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let r=s(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function n(e){let t=new Set([e]),s=new Set,r=e.match(i);if(!r)return()=>!1;let n={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(null!=n.prerelease)return function(t){return t===e};function a(e){return s.add(e),!1}return function(e){if(t.has(e))return!0;if(s.has(e))return!1;let r=e.match(i);if(!r)return a(e);let o={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(null!=o.prerelease||n.major!==o.major)return a(e);if(0===n.major)return n.minor===o.minor&&n.patch<=o.patch?(t.add(e),!0):a(e);return n.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=n,t.isCompatible=n(r.VERSION)},886:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=s(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class s{constructor(){}createHistogram(e,s){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,s){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,s){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,s){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,s){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,s){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=s;class r{}t.NoopMetric=r;class i extends r{add(e,t){}}t.NoopCounterMetric=i;class n extends r{add(e,t){}}t.NoopUpDownCounterMetric=n;class a extends r{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class c extends o{}t.NoopObservableGaugeMetric=c;class h extends o{}t.NoopObservableUpDownCounterMetric=h,t.NOOP_METER=new s,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new n,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new h,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let r=s(102);class i{getMeter(e,t,s){return r.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,s){var r=this&&this.__createBinding||(Object.create?function(e,t,s,r){void 0===r&&(r=s),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[s]}})}:function(e,t,s,r){void 0===r&&(r=s),e[r]=t[s]}),i=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||r(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),i(s(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:s.g},46:function(e,t,s){var r=this&&this.__createBinding||(Object.create?function(e,t,s,r){void 0===r&&(r=s),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[s]}})}:function(e,t,s,r){void 0===r&&(r=s),e[r]=t[s]}),i=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||r(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),i(s(651),t)},939:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=s(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class s{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=s},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,s){null!=e&&(e[t]=s)}}},845:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=s(997).TraceAPI.getInstance()},403:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let r=s(476);class i{constructor(e=r.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let r=s(491),i=s(607),n=s(403),a=s(139),o=r.ContextAPI.getInstance();class l{startSpan(e,t,s=o.active()){var r;if(null==t?void 0:t.root)return new n.NonRecordingSpan;let l=s&&(0,i.getSpanContext)(s);return"object"==typeof(r=l)&&"string"==typeof r.spanId&&"string"==typeof r.traceId&&"number"==typeof r.traceFlags&&(0,a.isSpanContextValid)(l)?new n.NonRecordingSpan(l):new n.NonRecordingSpan}startActiveSpan(e,t,s,r){let n,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(n=t,l=s):(n=t,a=s,l=r);let c=null!=a?a:o.active(),h=this.startSpan(e,n,c),u=(0,i.setSpan)(c,h);return o.with(u,l,void 0,h)}}t.NoopTracer=l},124:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let r=s(614);class i{getTracer(e,t,s){return new r.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let r=new(s(614)).NoopTracer;class i{constructor(e,t,s,r){this._provider=e,this.name=t,this.version=s,this.options=r}startSpan(e,t,s){return this._getTracer().startSpan(e,t,s)}startActiveSpan(e,t,s,r){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):r}}t.ProxyTracer=i},846:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let r=s(125),i=new(s(124)).NoopTracerProvider;class n{getTracer(e,t,s){var i;return null!=(i=this.getDelegateTracer(e,t,s))?i:new r.ProxyTracer(this,e,t,s)}getDelegate(){var e;return null!=(e=this._delegate)?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,s){var r;return null==(r=this._delegate)?void 0:r.getTracer(e,t,s)}}t.ProxyTracerProvider=n},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let r=s(780),i=s(403),n=s(491),a=(0,r.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(n.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=o(e))?void 0:t.spanContext()}},325:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let r=s(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let s=this._clone();return s._internalState.has(e)&&s._internalState.delete(e),s._internalState.set(e,t),s}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let s=t.trim(),i=s.indexOf("=");if(-1!==i){let n=s.slice(0,i),a=s.slice(i+1,t.length);(0,r.validateKey)(n)&&(0,r.validateValue)(a)&&e.set(n,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let s="[_0-9a-z-*/]",r=`[a-z]${s}{0,255}`,i=`[a-z0-9]${s}{0,240}@[a-z]${s}{0,13}`,n=RegExp(`^(?:${r}|${i})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return n.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let r=s(325);t.createTraceState=function(e){return new r.TraceStateImpl(e)}},476:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let r=s(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:r.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let r=s(476),i=s(403),n=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return n.test(e)&&e!==r.INVALID_TRACEID}function l(e){return a.test(e)&&e!==r.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},r={};function i(e){var s=r[e];if(void 0!==s)return s.exports;var n=r[e]={exports:{}},a=!0;try{t[e].call(n.exports,n,n.exports,i),a=!1}finally{a&&delete r[e]}return n.exports}i.ab="//";var n={};(()=>{Object.defineProperty(n,"__esModule",{value:!0}),n.trace=n.propagation=n.metrics=n.diag=n.context=n.INVALID_SPAN_CONTEXT=n.INVALID_TRACEID=n.INVALID_SPANID=n.isValidSpanId=n.isValidTraceId=n.isSpanContextValid=n.createTraceState=n.TraceFlags=n.SpanStatusCode=n.SpanKind=n.SamplingDecision=n.ProxyTracerProvider=n.ProxyTracer=n.defaultTextMapSetter=n.defaultTextMapGetter=n.ValueType=n.createNoopMeter=n.DiagLogLevel=n.DiagConsoleLogger=n.ROOT_CONTEXT=n.createContextKey=n.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(n,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(n,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(n,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var s=i(972);Object.defineProperty(n,"DiagConsoleLogger",{enumerable:!0,get:function(){return s.DiagConsoleLogger}});var r=i(957);Object.defineProperty(n,"DiagLogLevel",{enumerable:!0,get:function(){return r.DiagLogLevel}});var a=i(102);Object.defineProperty(n,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=i(901);Object.defineProperty(n,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=i(194);Object.defineProperty(n,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(n,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=i(125);Object.defineProperty(n,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var h=i(846);Object.defineProperty(n,"ProxyTracerProvider",{enumerable:!0,get:function(){return h.ProxyTracerProvider}});var u=i(996);Object.defineProperty(n,"SamplingDecision",{enumerable:!0,get:function(){return u.SamplingDecision}});var d=i(357);Object.defineProperty(n,"SpanKind",{enumerable:!0,get:function(){return d.SpanKind}});var p=i(847);Object.defineProperty(n,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=i(475);Object.defineProperty(n,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var m=i(98);Object.defineProperty(n,"createTraceState",{enumerable:!0,get:function(){return m.createTraceState}});var g=i(139);Object.defineProperty(n,"isSpanContextValid",{enumerable:!0,get:function(){return g.isSpanContextValid}}),Object.defineProperty(n,"isValidTraceId",{enumerable:!0,get:function(){return g.isValidTraceId}}),Object.defineProperty(n,"isValidSpanId",{enumerable:!0,get:function(){return g.isValidSpanId}});var w=i(476);Object.defineProperty(n,"INVALID_SPANID",{enumerable:!0,get:function(){return w.INVALID_SPANID}}),Object.defineProperty(n,"INVALID_TRACEID",{enumerable:!0,get:function(){return w.INVALID_TRACEID}}),Object.defineProperty(n,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return w.INVALID_SPAN_CONTEXT}});let b=i(67);Object.defineProperty(n,"context",{enumerable:!0,get:function(){return b.context}});let y=i(506);Object.defineProperty(n,"diag",{enumerable:!0,get:function(){return y.diag}});let v=i(886);Object.defineProperty(n,"metrics",{enumerable:!0,get:function(){return v.metrics}});let _=i(939);Object.defineProperty(n,"propagation",{enumerable:!0,get:function(){return _.propagation}});let x=i(845);Object.defineProperty(n,"trace",{enumerable:!0,get:function(){return x.trace}}),n.default={context:b.context,diag:y.diag,metrics:v.metrics,propagation:_.propagation,trace:x.trace}})(),e.exports=n})()},975:e=>{"use strict";var t=Object.defineProperty,s=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,n={};((e,s)=>{for(var r in s)t(e,r,{get:s[r],enumerable:!0})})(n,{Analytics:()=>c}),e.exports=((e,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let l of r(n))i.call(e,l)||l===a||t(e,l,{get:()=>n[l],enumerable:!(o=s(n,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var a=`
local key = KEYS[1]
local field = ARGV[1]

local data = redis.call("ZRANGE", key, 0, -1, "WITHSCORES")
local count = {}

for i = 1, #data, 2 do
  local json_str = data[i]
  local score = tonumber(data[i + 1])
  local obj = cjson.decode(json_str)

  local fieldValue = obj[field]

  if count[fieldValue] == nil then
    count[fieldValue] = score
  else
    count[fieldValue] = count[fieldValue] + score
  end
end

local result = {}
for k, v in pairs(count) do
  table.insert(result, {k, v})
end

return result
`,o=`
local prefix = KEYS[1]
local first_timestamp = tonumber(ARGV[1]) -- First timestamp to check
local increment = tonumber(ARGV[2])       -- Increment between each timestamp
local num_timestamps = tonumber(ARGV[3])  -- Number of timestampts to check (24 for a day and 24 * 7 for a week)
local num_elements = tonumber(ARGV[4])    -- Number of elements to fetch in each category
local check_at_most = tonumber(ARGV[5])   -- Number of elements to check at most.

local keys = {}
for i = 1, num_timestamps do
  local timestamp = first_timestamp - (i - 1) * increment
  table.insert(keys, prefix .. ":" .. timestamp)
end

-- get the union of the groups
local zunion_params = {"ZUNION", num_timestamps, unpack(keys)}
table.insert(zunion_params, "WITHSCORES")
local result = redis.call(unpack(zunion_params))

-- select num_elements many items
local true_group = {}
local false_group = {}
local denied_group = {}
local true_count = 0
local false_count = 0
local denied_count = 0
local i = #result - 1

-- index to stop at after going through "checkAtMost" many items:
local cutoff_index = #result - 2 * check_at_most

-- iterate over the results
while (true_count + false_count + denied_count) < (num_elements * 3) and 1 <= i and i >= cutoff_index do
  local score = tonumber(result[i + 1])
  if score > 0 then
    local element = result[i]
    if string.find(element, "success\\":true") and true_count < num_elements then
      table.insert(true_group, {score, element})
      true_count = true_count + 1
    elseif string.find(element, "success\\":false") and false_count < num_elements then
      table.insert(false_group, {score, element})
      false_count = false_count + 1
    elseif string.find(element, "success\\":\\"denied") and denied_count < num_elements then
      table.insert(denied_group, {score, element})
      denied_count = denied_count + 1
    end
  end
  i = i - 2
end

return {true_group, false_group, denied_group}
`,l=`
local prefix = KEYS[1]
local first_timestamp = tonumber(ARGV[1])
local increment = tonumber(ARGV[2])
local num_timestamps = tonumber(ARGV[3])

local keys = {}
for i = 1, num_timestamps do
  local timestamp = first_timestamp - (i - 1) * increment
  table.insert(keys, prefix .. ":" .. timestamp)
end

-- get the union of the groups
local zunion_params = {"ZUNION", num_timestamps, unpack(keys)}
table.insert(zunion_params, "WITHSCORES")
local result = redis.call(unpack(zunion_params))

return result
`,c=class{redis;prefix;bucketSize;constructor(e){this.redis=e.redis,this.prefix=e.prefix??"@upstash/analytics",this.bucketSize=this.parseWindow(e.window)}validateTableName(e){if(!/^[a-zA-Z0-9_-]+$/.test(e))throw Error(`Invalid table name: ${e}. Table names can only contain letters, numbers, dashes and underscores.`)}parseWindow(e){if("number"==typeof e){if(e<=0)throw Error(`Invalid window: ${e}`);return e}let t=/^(\d+)([smhd])$/;if(!t.test(e))throw Error(`Invalid window: ${e}`);let[,s,r]=e.match(t),i=parseInt(s);switch(r){case"s":return 1e3*i;case"m":return 1e3*i*60;case"h":return 1e3*i*3600;case"d":return 1e3*i*86400;default:throw Error(`Invalid window unit: ${r}`)}}getBucket(e){return Math.floor((e??Date.now())/this.bucketSize)*this.bucketSize}async ingest(e,...t){this.validateTableName(e),await Promise.all(t.map(async t=>{let s=this.getBucket(t.time),r=[this.prefix,e,s].join(":");await this.redis.zincrby(r,1,JSON.stringify({...t,time:void 0}))}))}formatBucketAggregate(e,t,s){let r={};return e.forEach(([e,s])=>{"success"==t&&(e=1===e?"true":null===e?"false":e),r[t]=r[t]||{},r[t][(e??"null").toString()]=s}),{time:s,...r}}async aggregateBucket(e,t,s){this.validateTableName(e);let r=this.getBucket(s),i=[this.prefix,e,r].join(":"),n=await this.redis.eval(a,[i],[t]);return this.formatBucketAggregate(n,t,r)}async aggregateBuckets(e,t,s,r){this.validateTableName(e);let i=this.getBucket(r),n=[];for(let r=0;r<s;r+=1)n.push(this.aggregateBucket(e,t,i)),i-=this.bucketSize;return Promise.all(n)}async aggregateBucketsWithPipeline(e,t,s,r,i){this.validateTableName(e),i=i??48;let n=this.getBucket(r),o=[],l=this.redis.pipeline(),c=[];for(let r=1;r<=s;r+=1){let h=[this.prefix,e,n].join(":");l.eval(a,[h],[t]),o.push(n),n-=this.bucketSize,(r%i==0||r==s)&&(c.push(l.exec()),l=this.redis.pipeline())}return(await Promise.all(c)).flat().map((e,s)=>this.formatBucketAggregate(e,t,o[s]))}async getAllowedBlocked(e,t,s){this.validateTableName(e);let r=[this.prefix,e].join(":"),i=this.getBucket(s),n=await this.redis.eval(l,[r],[i,this.bucketSize,t]),a={};for(let e=0;e<n.length;e+=2){let t=n[e],s=t.identifier,r=+n[e+1];a[s]||(a[s]={success:0,blocked:0}),a[s][t.success?"success":"blocked"]=r}return a}async getMostAllowedBlocked(e,t,s,r,i){this.validateTableName(e);let n=[this.prefix,e].join(":"),a=this.getBucket(r),[l,c,h]=await this.redis.eval(o,[n],[a,this.bucketSize,t,s,i??5*s]);return{allowed:this.toDicts(l),ratelimited:this.toDicts(c),denied:this.toDicts(h)}}toDicts(e){let t=[];for(let s=0;s<e.length;s+=1){let r=+e[s][0],i=e[s][1];t.push({identifier:i.identifier,count:r})}return t}}},999:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}}},e=>{var t=e(e.s=314);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map