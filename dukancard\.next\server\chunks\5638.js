"use strict";exports.id=5638,exports.ids=[5638],exports.modules={45638:(e,t,i)=>{i.d(t,{_:()=>c,g:()=>r,switchAuthenticatedSubscription:()=>o});var s=i(67218);i(79130);var n=i(27659),a=i(79209);async function o(e,t,s){let o,{user:r,profile:c,error:l}=await (0,n.GN)("has_active_subscription, trial_end_date");if(l)return(0,n.WX)(l);if(!r)return(0,n.WX)("User not found");let u=await Promise.resolve().then(i.bind(i,32032)).then(e=>e.createClient()),{data:d,error:_}=await u.from("payment_subscriptions").select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id
    `).eq("business_profile_id",r.id).eq("razorpay_subscription_id",e).maybeSingle();if(_)return console.error("Error fetching subscription:",_),(0,n.WX)("Error fetching subscription details");if(!d?.razorpay_subscription_id)return(0,n.WX)("Subscription does not belong to user");if("authenticated"!==d.subscription_status)return(0,n.WX)("Subscription is not in authenticated state");if(d.plan_id===t&&d.plan_cycle===s)return(0,n.WX)("You are already subscribed to this plan. Please choose a different plan or cycle.");console.log("[SUBSCRIPTION_SWITCH] Creating new subscription without cancelling the old one yet"),console.log("[SUBSCRIPTION_SWITCH] Old subscription ID will be stored in notes for webhook handler to process"),console.log("[SUBSCRIPTION_SWITCH] Creating new subscription with plan:",t,s);let p=c?.trial_end_date?new Date(c.trial_end_date):null,b=p&&p>new Date;try{o=(0,a.qD)(t,s)}catch(e){return(0,n.WX)(e instanceof Error?e.message:"Invalid plan selected")}let{getSubscription:m}=await Promise.all([i.e(1546),i.e(5453)]).then(i.bind(i,31546)),f=await m(e),h=null;if(f.success&&f.data?.customer_id)h=f.data.customer_id,console.log(`[SUBSCRIPTION_SWITCH] Found customer ID from existing subscription: ${h}`);else{console.log("[SUBSCRIPTION_SWITCH] No customer ID found in existing subscription, checking profile");let{findCustomerByEmail:e,createCustomer:t}=await i.e(616).then(i.bind(i,8235)),{data:s}=await u.from("business_profiles").select("business_name, contact_email, phone").eq("id",r.id).single(),n=c?.contact_email||r.email||"";if(n){console.log(`[SUBSCRIPTION_SWITCH] Checking if customer with email ${n} already exists`);let i=await e(n);if(i.success&&i.data)h=i.data.id,console.log(`[SUBSCRIPTION_SWITCH] Found existing customer with ID: ${h}`);else{console.log("[SUBSCRIPTION_SWITCH] No existing customer found, creating new customer");let e=await t({name:c?.business_name||"Customer",email:n,contact:c?.phone||"",notes:{user_id:r.id,business_name:c?.business_name||""}});e.success&&e.data?(h=e.data.id,console.log(`[SUBSCRIPTION_SWITCH] Created new customer with ID: ${h}`)):console.error("[SUBSCRIPTION_SWITCH] Failed to create customer:",e.error)}}}let{data:S}=await u.from("business_profiles").select("business_name, contact_email, phone").eq("id",r.id).single(),{createSubscription:I}=await Promise.all([i.e(1546),i.e(5453)]).then(i.bind(i,31546)),g={plan_id:o,total_count:"monthly"===s?120:10,quantity:1,customer_notify:!0,notes:{business_profile_id:r.id,old_subscription_id:e,plan_type:t,plan_cycle:s,is_plan_switch:"true",user_id:r.id,business_name:S?.business_name||"",email:S?.contact_email||r.email||"",phone:S?.phone||"",prorated_refund_amount:"",original_plan_id:"",original_plan_cycle:"",original_payment_id:""},start_at:void 0,...h&&{customer_id:h}};if(b&&p){let e=Math.floor(p.getTime()/1e3);g.start_at=e,console.log(`[SUBSCRIPTION_SWITCH] User is on trial until ${p.toISOString()}, setting start_at to ${e}`)}else console.log("[SUBSCRIPTION_SWITCH] No trial or trial has ended, immediate payment required");let y=await I(g);return y.success&&y.data?.id?((0,n.Ve)(),(0,n.$y)({message:"New subscription created. Please complete the payment authorization.",id:y.data.id,short_url:y.data.short_url,requires_authorization:!0})):(console.error("[SUBSCRIPTION_SWITCH] Error creating new subscription:",y.error),(0,n.WX)("Could not create new subscription"))}async function r(e,t,s){let{user:a,error:o}=await (0,n.GN)("has_active_subscription, trial_end_date");if(o)return(0,n.WX)(o);if(!a)return(0,n.WX)("User not found");let r=await Promise.resolve().then(i.bind(i,32032)).then(e=>e.createClient()),{data:l,error:u}=await r.from("payment_subscriptions").select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id,
      last_payment_method
    `).eq("business_profile_id",a.id).eq("razorpay_subscription_id",e).maybeSingle();if(u)return console.error("Error fetching subscription:",u),(0,n.WX)("Error fetching subscription details");if(!l?.razorpay_subscription_id)return(0,n.WX)("Subscription does not belong to user");if("active"!==l.subscription_status)return(0,n.WX)("Subscription is not in active state");if(l.plan_id===t&&l.plan_cycle===s)return(0,n.WX)("You are already subscribed to this plan. Please choose a different plan or cycle.");let d=l.last_payment_method?.toLowerCase()||"";return console.log(`[SUBSCRIPTION_SWITCH] Using simplified create/cancel flow for all payment methods. Payment method: ${d}`),c(e,t,s,d)}async function c(e,t,s,o){let r,{user:c,error:l}=await (0,n.GN)("has_active_subscription, trial_end_date");if(l)return(0,n.WX)(l);if(!c)return(0,n.WX)("User not found");let u=await Promise.resolve().then(i.bind(i,32032)).then(e=>e.createClient()),{data:d,error:_}=await u.from("payment_subscriptions").select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id,
      last_payment_method
    `).eq("business_profile_id",c.id).eq("razorpay_subscription_id",e).maybeSingle();if(_)return console.error("Error fetching subscription:",_),(0,n.WX)("Error fetching subscription details");if(!d?.razorpay_subscription_id)return(0,n.WX)("Subscription does not belong to user");if("active"!==d.subscription_status)return(0,n.WX)("Subscription is not in active state");if(d.plan_id===t&&d.plan_cycle===s)return(0,n.WX)("You are already subscribed to this plan. Please choose a different plan or cycle.");try{r=(0,a.qD)(t,s)}catch(e){return(0,n.WX)(e instanceof Error?e.message:"Invalid plan selected")}let{getSubscription:p}=await Promise.all([i.e(1546),i.e(5453)]).then(i.bind(i,31546)),b=await p(e),m=null;if(b.success&&b.data?.customer_id)m=b.data.customer_id,console.log(`[SUBSCRIPTION_SWITCH] Found customer ID from existing subscription: ${m}`);else{console.log("[SUBSCRIPTION_SWITCH] No customer ID found in existing subscription, checking profile");let{findCustomerByEmail:e,createCustomer:t}=await i.e(616).then(i.bind(i,8235)),{data:s}=await u.from("business_profiles").select("business_name, contact_email, phone").eq("id",c.id).single(),n=s?.contact_email||c.email||"";if(n){console.log(`[SUBSCRIPTION_SWITCH] Checking if customer with email ${n} already exists`);let i=await e(n);if(i.success&&i.data)m=i.data.id,console.log(`[SUBSCRIPTION_SWITCH] Found existing customer with ID: ${m}`);else{console.log("[SUBSCRIPTION_SWITCH] No existing customer found, creating new customer");let e=await t({name:s?.business_name||"Customer",email:n,contact:s?.phone||"",notes:{user_id:c.id,business_name:s?.business_name||""}});e.success&&e.data?(m=e.data.id,console.log(`[SUBSCRIPTION_SWITCH] Created new customer with ID: ${m}`)):console.error("[SUBSCRIPTION_SWITCH] Failed to create customer:",e.error)}}}let{data:f}=await u.from("business_profiles").select("business_name, contact_email, phone").eq("id",c.id).single();console.log(`[SUBSCRIPTION_SWITCH] Creating new subscription for plan switch. Old subscription ${e} will be cancelled after new subscription becomes active`);let{createSubscription:h}=await Promise.all([i.e(1546),i.e(5453)]).then(i.bind(i,31546)),S={plan_id:r,total_count:"monthly"===s?120:10,quantity:1,customer_notify:!0,notes:{business_profile_id:c.id,old_subscription_id:e,plan_type:t,plan_cycle:s,is_plan_switch:"true",user_id:c.id,business_name:f?.business_name||"",email:f?.contact_email||c.email||"",phone:f?.phone||"",payment_method:o,original_plan_id:d.plan_id,original_plan_cycle:d.plan_cycle},...m&&{customer_id:m}},I=await h(S);return I.success&&I.data?.id?((0,n.Ve)(),(0,n.$y)({message:"A new subscription has been created for your plan switch. Please complete the payment authorization to activate your new plan. Your current subscription will remain active until the new one is confirmed.",id:I.data.id,short_url:I.data.short_url,requires_authorization:!0})):(console.error("[SUBSCRIPTION_SWITCH] Error creating new subscription:",I.error),(0,n.WX)("Could not create new subscription"))}(0,i(17478).D)([o,r,c]),(0,s.A)(o,"7079684d5710a60a6f3b6c187d7182292bb2ca9359",null),(0,s.A)(r,"70d5ac2f53a3a59b11b18e95dcb79d8273fb1231ca",null),(0,s.A)(c,"7821496fbfb4e8bbdb4fd0cc9aeebfc2e46e676840",null)}};