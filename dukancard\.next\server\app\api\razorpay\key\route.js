(()=>{var e={};e.id=4124,e.ids=[4124],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18243:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>l});var a=r(96559),n=r(48088),i=r(37719),u=r(32190),o=r(32032);async function l(e){try{let e=await (0,o.createClient)(),{data:{user:t},error:r}=await e.auth.getUser();if(r||!t)return u.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let s=process.env.RAZORPAY_LIVE_KEY_ID||"rzp_live_WAmmYKACQauwBQ";return u.NextResponse.json({success:!0,key_id:s})}catch(e){return console.error("Error fetching Razorpay key ID:",e),u.NextResponse.json({success:!1,error:"An unexpected error occurred"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/razorpay/key/route",pathname:"/api/razorpay/key",filename:"route",bundlePath:"app/api/razorpay/key/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\razorpay\\key\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:y}=c;function m(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>a});var s=r(34386);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let a=null,n=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));a=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,a=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,a){let n=()=>{var n,i,u,o,l;return n=e,i=t,u=r,o=s,l=a,"customer_profiles"===n?{data:u&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:u&&"business"===i?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===i?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,r)=>i(e),neq:(t,r)=>i(e),gt:(t,r)=>i(e),gte:(t,r)=>i(e),lt:(t,r)=>i(e),lte:(t,r)=>i(e),like:(t,r)=>i(e),ilike:(t,r)=>i(e),is:(t,r)=>i(e),in:(t,r)=>i(e),contains:(t,r)=>i(e),containedBy:(t,r)=>i(e),rangeGt:(t,r)=>i(e),rangeGte:(t,r)=>i(e),rangeLt:(t,r)=>i(e),rangeLte:(t,r)=>i(e),rangeAdjacent:(t,r)=>i(e),overlaps:(t,r)=>i(e),textSearch:(t,r)=>i(e),match:t=>i(e),not:(t,r,s)=>i(e),or:t=>i(e),filter:(t,r,s)=>i(e),order:(t,r)=>i(e),limit:(t,r)=>i(e),range:(t,r,s)=>i(e),abortSignal:t=>i(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,r,s,a,n)}}(a):n?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await n.set(t,r,s)}catch{}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,9398,4386,580],()=>r(18243));module.exports=s})();