(()=>{var e={};e.id=418,e.ids=[418,937],e.modules={3109:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("LayoutList",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}],["path",{d:"M14 4h7",key:"3xa0d5"}],["path",{d:"M14 9h7",key:"1icrd9"}],["path",{d:"M14 15h7",key:"1mj8o2"}],["path",{d:"M14 20h7",key:"11slyb"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17090:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},17581:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},17971:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32950:(e,t,a)=>{"use strict";a.d(t,{default:()=>G});var r=a(60687),s=a(43210),i=a(77882),n=a(84027),l=a(52581),d=a(6475);d.callServer,d.findSourceMapURL;let c=(0,d.createServerReference)("00587c2eafaa8506ef7d1a09b6ceb2abfc5ab559fa",d.callServer,void 0,d.findSourceMapURL,"sendDeleteAccountOTP"),o=(0,d.createServerReference)("60890d22f4c7992f648c81b47a901bc0863dd3c988",d.callServer,void 0,d.findSourceMapURL,"verifyDeleteAccountOTP"),u=(0,d.createServerReference)("40393e24ab017bf6535b49b096bdca35f76e7cba5c",d.callServer,void 0,d.findSourceMapURL,"verifyDeleteAccountPassword"),m=(0,d.createServerReference)("00890d9a7f09ef9a0793405d70dcac6ff5a3417c20",d.callServer,void 0,d.findSourceMapURL,"deleteCustomerAccount");var x=a(24934),h=a(37826),p=a(68988),f=a(99008),g=a(16189),b=a(88920),y=a(88233),v=a(43649),j=a(41862),w=a(41550),N=a(17581),k=a(35071);function A(){let e=(0,g.useRouter)(),[t,a]=(0,s.useTransition)(),[n,d]=(0,s.useState)(""),[A,P]=(0,s.useState)(!1),[C,_]=(0,s.useState)(!1),[E,S]=(0,s.useState)("initial"),[R,$]=(0,s.useState)(!1),[F,T]=(0,s.useState)(!1),[M,q]=(0,s.useState)(""),[z,V]=(0,s.useState)(null),[U,L]=(0,s.useState)(""),[D,I]=(0,s.useState)(""),[O,G]=(0,s.useState)(!1),[W,Y]=(0,s.useState)(!1),[B,H]=(0,s.useState)(!1),[J,K]=(0,s.useState)(!1),X=e=>{V(e),"email"===e?S("email-otp"):S("password")},Q=async()=>{Y(!0);try{let e=await c();if(e.success)l.oR.success(e.message),e.email&&q(e.email);else if(l.oR.error(e.message),e.isConfigurationError)return}catch(e){l.oR.error("Failed to send verification code")}finally{Y(!1)}},Z=async()=>{if(6!==U.length)return void l.oR.error("Please enter a valid 6-digit code");G(!0);try{let e=await o(M,U);e.success?(l.oR.success(e.message),H(!0),S("final-confirm")):l.oR.error(e.message)}catch(e){l.oR.error("Failed to verify code")}finally{G(!1)}},ee=async()=>{if(!D.trim())return void l.oR.error("Please enter your password");G(!0);try{let e=await u(D);e.success?(l.oR.success(e.message),H(!0),S("final-confirm")):l.oR.error(e.message)}catch(e){l.oR.error("Failed to verify password")}finally{G(!1)}},et=()=>{d(""),_(!1),S("initial"),V(null),L(""),I(""),H(!1),q(""),K(!1)};return(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},className:"space-y-6",children:[(0,r.jsxs)("div",{className:"pb-6 border-b border-red-200/60 dark:border-red-700/60",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-red-500/10 to-red-500/5 border border-red-500/20",children:(0,r.jsx)(y.A,{className:"w-4 h-4 text-red-500"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-red-600 dark:text-red-400",children:"Delete Account"})]}),(0,r.jsx)("p",{className:"text-sm text-red-600/80 dark:text-red-400/80 leading-relaxed",children:"Permanently delete your account and all associated data."})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30 mb-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)(v.A,{className:"w-5 h-5 text-red-500 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-red-700 dark:text-red-400",children:"Warning: This action cannot be undone"}),(0,r.jsx)("p",{className:"text-xs text-red-600/80 dark:text-red-400/80 mt-1",children:"Deleting your account will permanently remove all your data, including your saved cards, likes, reviews, and subscriptions. You will not be able to recover this information."})]})]})}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(x.$,{variant:"destructive",disabled:A,onClick:()=>_(!0),className:"px-4 py-2 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",type:"button",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]})}),(0,r.jsx)(h.lG,{open:C,onOpenChange:e=>{A||O||W||et()},children:(0,r.jsxs)(h.Cf,{className:"sm:max-w-md",children:[(0,r.jsxs)(h.c7,{className:"text-center pb-2",children:[(0,r.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20",children:(0,r.jsx)(i.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},children:(0,r.jsx)(y.A,{className:"h-8 w-8 text-red-500"})})}),(0,r.jsx)(h.L3,{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"choose-method"===E?"Verify Your Identity":"email-otp"===E?"Email Verification":"password"===E?"Password Verification":"Delete your account?"}),(0,r.jsx)(h.rr,{className:"text-gray-500 dark:text-gray-400 mt-2",children:"choose-method"===E?"Choose how you want to verify your identity before deleting your account.":"email-otp"===E?"We've sent a verification code to your email address.":"password"===E?"Please enter your current password to verify your identity.":"This action cannot be undone. All your data will be permanently removed."})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[J&&(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 space-y-4",children:[(0,r.jsx)(j.A,{className:"h-8 w-8 animate-spin text-neutral-500"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Checking verification options..."})]}),!J&&"choose-method"===E&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mb-4",children:"For security, please verify your identity before proceeding:"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(x.$,{onClick:()=>X("email"),variant:"outline",className:"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",children:[(0,r.jsx)(w.A,{className:"h-5 w-5 text-blue-500"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Email Verification"}),(0,r.jsx)("div",{className:"text-xs text-neutral-500",children:"Send OTP to your email"})]})]}),(0,r.jsxs)(x.$,{onClick:()=>X("password"),variant:"outline",className:"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",children:[(0,r.jsx)(N.A,{className:"h-5 w-5 text-green-500"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:"Password Verification"}),(0,r.jsx)("div",{className:"text-xs text-neutral-500",children:"Enter your current password"})]})]})]})]}),!J&&"email-otp"===E&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(x.$,{onClick:Q,disabled:W,variant:"outline",className:"mb-4",children:W?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Sending..."]}):"Send Verification Code"})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Enter 6-digit verification code:"}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(f.UV,{maxLength:6,value:U,onChange:L,className:"gap-2",children:(0,r.jsxs)(f.NV,{children:[(0,r.jsx)(f.sF,{index:0,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,r.jsx)(f.sF,{index:1,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,r.jsx)(f.sF,{index:2,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,r.jsx)(f.sF,{index:3,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,r.jsx)(f.sF,{index:4,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,r.jsx)(f.sF,{index:5,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"})]})})})]}),(0,r.jsx)(x.$,{onClick:Z,disabled:6!==U.length||O,className:"w-full",children:O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Verifying..."]}):"Verify Code"})]}),!J&&"password"===E&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Enter your current password:"}),(0,r.jsx)(p.p,{type:"password",value:D,onChange:e=>I(e.target.value),placeholder:"Current password",className:"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"})]}),(0,r.jsx)(x.$,{onClick:ee,disabled:!D.trim()||O,className:"w-full",children:O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Verifying..."]}):"Verify Password"})]}),!J&&"final-confirm"===E&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-950/20 rounded-lg p-4 border border-red-100 dark:border-red-800/30",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(v.A,{className:"h-5 w-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-red-700 dark:text-red-400",children:"This will permanently delete:"}),(0,r.jsxs)("ul",{className:"text-xs text-red-600/80 dark:text-red-400/80 mt-1 space-y-0.5",children:[(0,r.jsx)("li",{children:"• Your saved business cards"}),(0,r.jsx)("li",{children:"• Your likes and subscriptions"}),(0,r.jsx)("li",{children:"• Your reviews and ratings"}),(0,r.jsx)("li",{children:"• Your account information"})]})]})]})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2",children:["Type ",(0,r.jsx)("span",{className:"font-bold text-red-500 dark:text-red-400",children:"DELETE"})," to confirm:"]}),(0,r.jsx)(p.p,{value:n,onChange:e=>d(e.target.value),placeholder:"DELETE",className:"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-red-500/30",disabled:A})]})]})]}),(0,r.jsxs)(h.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 pt-4",children:[(0,r.jsxs)(x.$,{type:"button",variant:"outline",onClick:()=>{et()},disabled:A||O||W||J,className:"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200",children:[(0,r.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),!J&&"final-confirm"===E&&(0,r.jsx)(i.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1",children:(0,r.jsx)(x.$,{type:"button",onClick:t=>(t.preventDefault(),"DELETE"!==n)?void l.oR.error('Please type "DELETE" to confirm'):(R||F)&&!B?void l.oR.error("Please complete verification first"):void(P(!0),a(async()=>{try{l.oR.success("Processing account deletion...");try{await m(),et(),e.push("/")}catch(t){t instanceof Error&&t.message.includes("Cannot read properties of undefined")?(et(),e.push("/")):(console.error("Error in account deletion:",t),l.oR.error("Failed to delete account"),P(!1))}}catch(e){console.error("Unexpected error during account deletion:",e),l.oR.error("An unexpected error occurred"),P(!1)}})),disabled:"DELETE"!==n||A,className:`
                    w-full relative overflow-hidden
                    bg-gradient-to-r from-red-500 to-red-600
                    hover:from-red-600 hover:to-red-700
                    text-white font-medium
                    shadow-lg hover:shadow-xl
                    transition-all duration-300
                    before:absolute before:inset-0
                    before:bg-gradient-to-r before:from-red-400 before:to-red-500
                    before:opacity-0 hover:before:opacity-20
                    before:transition-opacity before:duration-300
                    ${"DELETE"!==n||A?"cursor-not-allowed opacity-80":""}
                  `,style:{boxShadow:"DELETE"!==n||A?"0 4px 20px rgba(239, 68, 68, 0.3)":"0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)"},children:(0,r.jsx)(b.N,{mode:"wait",children:A?(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Deleting..."]},"deleting"):(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]},"delete")})})})]})]})})]})]})}var P=a(75034),C=a(96882),_=a(27605),E=a(63442),S=a(45880),R=a(58164),$=a(99891),F=a(5336);let T=(0,d.createServerReference)("00ac5331100c6eb661a835872f4aca4f1c0c57af60",d.callServer,void 0,d.findSourceMapURL,"sendEmailChangeOTP"),M=(0,d.createServerReference)("608844a615b87b85d4667dfe96d0b36222157db949",d.callServer,void 0,d.findSourceMapURL,"updateCustomerEmail"),q=(0,d.createServerReference)("60fe30a842b0e4f65cd95fe2d407dda8da860d82ff",d.callServer,void 0,d.findSourceMapURL,"confirmEmailChangeOTP"),z=(0,d.createServerReference)("6021663cdb43cf5374d99502948b3be7537d9a7970",d.callServer,void 0,d.findSourceMapURL,"updateCustomerEmailWithOTP"),V=S.z.object({email:S.z.string().email("Invalid email address.")}),U=S.z.object({otp:S.z.string().min(6,"OTP must be 6 digits.").max(6,"OTP must be 6 digits.")});function L({isOpen:e,onClose:t,currentEmail:a,onEmailUpdated:n}){let[d,c]=(0,s.useTransition)(),[o,u]=(0,s.useState)("email_input"),[m,g]=(0,s.useState)(""),[b,y]=(0,s.useState)(""),[v,N]=(0,s.useState)(""),k=(0,_.mN)({resolver:(0,E.u)(V),defaultValues:{email:""}}),A=(0,_.mN)({resolver:(0,E.u)(U),defaultValues:{otp:""}}),P=()=>{u("email_input"),g(""),y(""),N(""),k.reset(),A.reset(),t()};return(0,r.jsx)(h.lG,{open:e,onOpenChange:e=>!e&&P(),children:(0,r.jsxs)(h.Cf,{className:"sm:max-w-md",hideClose:!1,children:[(0,r.jsxs)(h.c7,{children:[(0,r.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,r.jsx)(w.A,{className:"w-5 h-5 text-primary"}),"Update Email Address"]}),(0,r.jsx)(h.rr,{children:a?"Verify your current email first, then set a new email address.":"Link an email address to your account for better security."})]}),(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"space-y-6",children:["email_input"===o&&(0,r.jsx)("div",{className:"space-y-4",children:a?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)($.A,{className:"w-4 h-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:"Security Verification Required"})]}),(0,r.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["To change your email address, we need to verify your current email: ",(0,r.jsx)("strong",{children:a})]})]}),(0,r.jsx)(x.$,{onClick:()=>{c(async()=>{try{let e=await T();e.success?(g(e.email||a||""),u("otp_verification"),N(`We&apos;ve sent a 6-digit verification code to ${e.email||a}.`),l.oR.success("Verification code sent to your current email!")):(l.oR.error(e.message||"Failed to send verification code."),N(e.message||"Failed to send verification code."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})},disabled:d,className:"w-full",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending Code..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Send Verification Code"]})})]}):(0,r.jsx)(R.lV,{...k,children:(0,r.jsxs)("form",{onSubmit:k.handleSubmit(e=>{c(async()=>{try{let t=new FormData;t.append("newEmail",e.email);let a=await M({message:null,success:!1},t);a.success?"otp_sent"===a.message?(g(e.email),u("otp_verification"),N(`We&apos;ve sent a 6-digit verification code to ${e.email}.`),l.oR.success("Verification code sent!")):(l.oR.success("Email linked successfully!"),P(),n?.()):(l.oR.error(a.message||"Failed to link email."),N(a.message||"Failed to link email."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,r.jsx)(R.zB,{control:k.control,name:"email",render:({field:e})=>(0,r.jsxs)(R.eI,{children:[(0,r.jsx)(R.lR,{children:"Email Address"}),(0,r.jsx)(R.MJ,{children:(0,r.jsx)(p.p,{type:"email",placeholder:"Enter your email address",...e,disabled:d})}),(0,r.jsx)(R.C5,{})]})}),(0,r.jsx)(x.$,{type:"submit",disabled:d,className:"w-full",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Linking Email..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Link Email Address"]})})]})})}),"otp_verification"===o&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full",children:(0,r.jsx)(w.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Check Your Email"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["We've sent a 6-digit code to ",(0,r.jsx)("strong",{children:m})]})]}),(0,r.jsx)(R.lV,{...A,children:(0,r.jsxs)("form",{onSubmit:A.handleSubmit(e=>{c(async()=>{try{let t=new FormData;t.append("email",m),t.append("otp",e.otp);let r=await q({email:m,otp:e.otp},t);r.success?a?(u("new_email_input"),N("OTP verified! Now enter your new email address."),l.oR.success("OTP verified! Enter your new email.")):(l.oR.success("Email verified and linked successfully!"),P(),n?.()):(l.oR.error(r.message||"Invalid OTP. Please try again."),N(r.message||"Invalid OTP. Please try again."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,r.jsx)(R.zB,{control:A.control,name:"otp",render:({field:e})=>(0,r.jsxs)(R.eI,{children:[(0,r.jsx)(R.lR,{children:"Verification Code"}),(0,r.jsx)(R.MJ,{children:(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(f.UV,{maxLength:6,...e,disabled:d,children:(0,r.jsxs)(f.NV,{children:[(0,r.jsx)(f.sF,{index:0}),(0,r.jsx)(f.sF,{index:1}),(0,r.jsx)(f.sF,{index:2}),(0,r.jsx)(f.sF,{index:3}),(0,r.jsx)(f.sF,{index:4}),(0,r.jsx)(f.sF,{index:5})]})})})}),(0,r.jsx)(R.C5,{})]})}),(0,r.jsx)(x.$,{type:"submit",disabled:d,className:"w-full",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Verifying..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(F.A,{className:"w-4 h-4 mr-2"}),"Verify Code"]})})]})})]}),"new_email_input"===o&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(F.A,{className:"w-4 h-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-900 dark:text-green-100",children:"Current Email Verified"})]}),(0,r.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:["Current email: ",(0,r.jsx)("strong",{children:a})]})]}),(0,r.jsx)(R.lV,{...k,children:(0,r.jsxs)("form",{onSubmit:k.handleSubmit(e=>{c(async()=>{try{let t=new FormData;t.append("email",e.email);let a=await z({message:null,success:!1},t);a.success?(l.oR.success("Email updated successfully!"),P(),n?.()):(l.oR.error(a.message||"Failed to update email."),N(a.message||"Failed to update email."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,r.jsx)(R.zB,{control:k.control,name:"email",render:({field:e})=>(0,r.jsxs)(R.eI,{children:[(0,r.jsx)(R.lR,{children:"New Email Address"}),(0,r.jsx)(R.MJ,{children:(0,r.jsx)(p.p,{type:"email",placeholder:"Enter your new email address",...e,disabled:d})}),(0,r.jsx)(R.C5,{})]})}),(0,r.jsx)(x.$,{type:"submit",disabled:d,className:"w-full",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Updating Email..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Update Email Address"]})})]})})]}),v&&(0,r.jsx)("div",{className:"p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,r.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:v})})]},o)]})})}function D({currentEmail:e,registrationType:t}){let[a,n]=(0,s.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20",children:(0,r.jsx)(w.A,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-50",children:"Email Address"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e?"Manage your account email address":"Link an email address for better security"})]})]})}),(0,r.jsx)("div",{className:"bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 p-6 space-y-4",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 block",children:"Current Email Address"}),(0,r.jsx)(p.p,{type:"email",value:e||"",placeholder:e||"No email address linked",readOnly:!0,className:"bg-neutral-50 dark:bg-neutral-800 cursor-not-allowed"}),(0,r.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1",children:e?"This is your current email address":"No email address is currently linked to your account"})]}),(0,r.jsxs)(x.$,{onClick:()=>n(!0),className:"w-full sm:w-auto",variant:"default",children:[(0,r.jsx)(P.A,{className:"w-4 h-4 mr-2"}),e?"Update Email Address":"Link Email Address"]})]})}),"google"===t&&(0,r.jsx)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(C.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1",children:"Google Account Integration"}),(0,r.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"You signed up with Google. You can still link or update your email address for additional security options."})]})]})})]}),(0,r.jsx)(L,{isOpen:a,onClose:()=>n(!1),currentEmail:e,onEmailUpdated:()=>{window.location.reload()}})]})}var I=a(48340);function O({currentPhone:e}){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-green-500/10 to-green-500/5 border border-green-500/20",children:(0,r.jsx)(I.A,{className:"w-4 h-4 text-green-500"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-100",children:"Phone Number"})]}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed",children:e?"Your current phone number linked to this account.":"No phone number is currently linked to your account."})]}),(0,r.jsx)("div",{className:"space-y-6",children:e?(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Current Phone Number"}),(0,r.jsx)("div",{className:"mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md",children:(0,r.jsx)("span",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e})}),(0,r.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1",children:"Phone number changes are not currently supported. Contact support if you need to update your number."})]})}):(0,r.jsxs)("div",{className:"text-center p-6 rounded-lg bg-neutral-50 dark:bg-neutral-900/50 border border-neutral-200 dark:border-neutral-700",children:[(0,r.jsx)("div",{className:"p-3 rounded-full bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 mx-auto mb-3 w-fit",children:(0,r.jsx)(I.A,{className:"w-6 h-6"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-2",children:"No Phone Number"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 max-w-sm mx-auto",children:"No phone number is currently linked to your account. Phone number linking is not available at this time."})]})})]})}function G({currentEmail:e,currentPhone:t,registrationType:a}){return(0,r.jsxs)(i.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-8",children:[(0,r.jsx)(i.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,r.jsx)(n.A,{className:"w-5 h-5 text-primary"})}),(0,r.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,r.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Account Management"})]}),(0,r.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Account Settings"}),(0,r.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Manage your account security, contact information, and preferences. Keep your profile up to date."})]})}),(0,r.jsxs)("div",{className:"space-y-12",children:[(0,r.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},children:(0,r.jsx)(D,{currentEmail:e,currentPhone:t,registrationType:a})}),(0,r.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3},children:(0,r.jsx)(O,{currentEmail:e,currentPhone:t,registrationType:a})}),(0,r.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.5},children:(0,r.jsx)(A,{})})]})]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37360:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},39390:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var r=a(60687);a(43210);var s=a(78148),i=a(96241);function n({className:e,...t}){return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},40817:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c,metadata:()=>d});var r=a(37413),s=a(32032),i=a(39916),n=a(58054),l=a(66819);let d={title:"Account Settings - Dukancard",description:"Manage your Dukancard customer account settings.",robots:"noindex, nofollow"};async function c(){let e=await (0,s.createClient)(),{data:{user:t},error:a}=await e.auth.getUser();(a||!t)&&(0,i.redirect)("/login?message=Authentication required");let d=(0,l.gV)(t?.phone),c=t.app_metadata?.provider==="google",o=!!t.email,u=!!d,m="email";return c?m="google":u&&!o?m="phone":o&&(m="email"),(0,r.jsx)(n.default,{currentEmail:t?.email,currentPhone:d,registrationType:m})}},41550:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},43649:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44095:(e,t,a)=>{Promise.resolve().then(a.bind(a,32950))},46903:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),l=a(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(t,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["customer",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,40817)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,78050)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,o=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/customer/settings/page",pathname:"/dashboard/customer/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48340:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},49625:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},50937:(e,t,a)=>{"use strict";function r(e){if(!e||"string"!=typeof e)throw Error(`Invalid userId: expected string, got ${typeof e}. Value: ${e}`);if(e.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${e}`);let t=e.substring(0,2).toLowerCase(),a=e.substring(2,4).toLowerCase();return`users/${t}/${a}/${e}`}function s(e,t,a,s){let i=r(e);return`${i}/products/${t}/base/image_${a}_${s}.webp`}function i(e,t,a,s,i){let n=r(e);return`${n}/products/${t}/${a}/image_${s}_${i}.webp`}function n(e,t,a,s,i){let n=r(e),l=i?new Date(i):new Date,d=l.getFullYear(),c=String(l.getMonth()+1).padStart(2,"0");return`${n}/posts/${d}/${c}/${t}/image_${a}_${s}.webp`}function l(e,t,a){let s=r(e),i=new Date(a),n=i.getFullYear(),l=String(i.getMonth()+1).padStart(2,"0");return`${s}/posts/${n}/${l}/${t}`}function d(e,t){let a=r(e);return`${a}/avatar/avatar_${t}.webp`}function c(e,t,a,s,i){let n=r(e),l=i?new Date(i):new Date,d=l.getFullYear(),c=String(l.getMonth()+1).padStart(2,"0");return`${n}/posts/${d}/${c}/${t}/image_${a}_${s}.webp`}function o(e,t){let a=r(e);return`${a}/ads/custom_ad_${t}.webp`}a.d(t,{EK:()=>l,JU:()=>o,RE:()=>n,Vl:()=>i,getScalableUserPath:()=>r,jA:()=>s,jt:()=>c,tS:()=>d})},51214:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58054:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\customer\\\\settings\\\\components\\\\SettingsPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx","default")},58164:(e,t,a)=>{"use strict";a.d(t,{C5:()=>b,MJ:()=>f,Rr:()=>g,eI:()=>h,lR:()=>p,lV:()=>c,zB:()=>u});var r=a(60687),s=a(43210),i=a(8730),n=a(27605),l=a(96241),d=a(39390);let c=n.Op,o=s.createContext({}),u=({...e})=>(0,r.jsx)(o.Provider,{value:{name:e.name},children:(0,r.jsx)(n.xI,{...e})}),m=()=>{let e=s.useContext(o),t=s.useContext(x),{getFieldState:a}=(0,n.xW)(),r=(0,n.lN)({name:e.name}),i=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},x=s.createContext({});function h({className:e,...t}){let a=s.useId();return(0,r.jsx)(x.Provider,{value:{id:a},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",e),...t})})}function p({className:e,...t}){let{error:a,formItemId:s}=m();return(0,r.jsx)(d.J,{"data-slot":"form-label","data-error":!!a,className:(0,l.cn)("data-[error=true]:text-destructive",e),htmlFor:s,...t})}function f({...e}){let{error:t,formItemId:a,formDescriptionId:s,formMessageId:n}=m();return(0,r.jsx)(i.DX,{"data-slot":"form-control",id:a,"aria-describedby":t?`${s} ${n}`:`${s}`,"aria-invalid":!!t,...e})}function g({className:e,...t}){let{formDescriptionId:a}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:a,className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}function b({className:e,...t}){let{error:a,formMessageId:s}=m(),i=a?String(a?.message??""):t.children;return i?(0,r.jsx)("p",{"data-slot":"form-message",id:s,className:(0,l.cn)("text-destructive text-sm",e),...t,children:i}):null}},58559:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},62369:(e,t,a)=>{"use strict";a.d(t,{b:()=>c});var r=a(43210),s=a(3416),i=a(60687),n="horizontal",l=["horizontal","vertical"],d=r.forwardRef((e,t)=>{var a;let{decorative:r,orientation:d=n,...c}=e,o=(a=d,l.includes(a))?d:n;return(0,i.jsx)(s.sG.div,{"data-orientation":o,...r?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:t})});d.displayName="Separator";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i,gV:()=>n});var r=a(75986),s=a(8974);function i(...e){return(0,s.QP)((0,r.$)(e))}function n(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}},74075:e=>{"use strict";e.exports=require("zlib")},75034:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},78599:(e,t,a)=>{Promise.resolve().then(a.bind(a,58054))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82621:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("WalletCards",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2",key:"4125el"}],["path",{d:"M3 11h3c.8 0 1.6.3 2.1.9l1.1.9c1.6 1.6 4.1 1.6 5.7 0l1.1-.9c.5-.5 1.3-.9 2.1-.9H21",key:"1dpki6"}]])},84027:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},85651:(e,t,a)=>{"use strict";a.r(t),a.d(t,{"00587c2eafaa8506ef7d1a09b6ceb2abfc5ab559fa":()=>b,"00890d9a7f09ef9a0793405d70dcac6ff5a3417c20":()=>j,"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>r.B,"00ac5331100c6eb661a835872f4aca4f1c0c57af60":()=>p,"00d2052c821136e9e2e8d8101d8866f4f3a3206603":()=>g,"40393e24ab017bf6535b49b096bdca35f76e7cba5c":()=>v,"6021663cdb43cf5374d99502948b3be7537d9a7970":()=>x,"608844a615b87b85d4667dfe96d0b36222157db949":()=>m,"60890d22f4c7992f648c81b47a901bc0863dd3c988":()=>y,"60fe30a842b0e4f65cd95fe2d407dda8da860d82ff":()=>f});var r=a(64275),s=a(91199);a(42087);var i=a(76881),n=a(50937),l=a(68567);l.z.object({name:l.z.string().min(2,{message:"Name must be at least 2 characters."})}),l.z.object({email:l.z.string().email({message:"Please enter a valid email."})});let d=l.z.object({email:l.z.string().email("Invalid email address."),otp:l.z.string().min(6,"OTP must be 6 digits.").max(6,"OTP must be 6 digits.")});l.z.object({address:l.z.string().max(100,{message:"Address cannot exceed 100 characters."}).optional().or(l.z.literal("")),pincode:l.z.string().min(1,{message:"Pincode is required"}).regex(/^\d{6}$/,{message:"Must be a valid 6-digit pincode"}),city:l.z.string().min(1,{message:"City is required"}).refine(e=>e.trim().length>0,{message:"City cannot be empty"}),state:l.z.string().min(1,{message:"State is required"}).refine(e=>e.trim().length>0,{message:"State cannot be empty"}),locality:l.z.string().min(1,{message:"Locality is required"}).refine(e=>e.trim().length>0,{message:"Locality cannot be empty"})});var c=a(7944),o=a(74208),u=a(33331);async function m(e,t){let a=await (0,i.createClient)(),{data:{user:r},error:s}=await a.auth.getUser();if(s||!r)return{message:"Not authenticated",success:!1};let n=h.safeParse({email:t.get("email")});if(!n.success)return{message:"Invalid data provided.",errors:n.error.flatten().fieldErrors,success:!1};let{email:l}=n.data;try{if(r.email)return{message:"otp_sent",success:!0};{let{error:e}=await a.auth.updateUser({email:l},{emailRedirectTo:`${(await (0,o.headers)()).get("origin")}/auth/callback?email_change_status=success&redirect_to=settings`});if(e){let t="Failed to link email address.";switch(e.code){case"email_exists":t="This email address is already registered with another account.";break;case"invalid_email":t="Please enter a valid email address.";break;case"email_change_confirm_limit":t="Too many email requests. Please wait before trying again.";break;case"over_email_send_rate_limit":t="Email rate limit exceeded. Please wait before trying again.";break;default:t="Unable to link email address. Please try again later."}return{message:t,success:!1}}return{message:"Email address linked successfully!",success:!0}}}catch(e){return{message:"An unexpected error occurred while linking email.",success:!1}}}async function x(e,t){let a=await (0,i.createClient)(),{data:{user:r},error:s}=await a.auth.getUser();if(s||!r)return{message:"Not authenticated",success:!1};let n=h.safeParse({email:t.get("email")});if(!n.success)return{message:"Invalid data provided.",errors:n.error.flatten().fieldErrors,success:!1};let{email:l}=n.data;try{let{error:e}=await a.auth.updateUser({email:l},{emailRedirectTo:`${(await (0,o.headers)()).get("origin")}/auth/callback?email_change_status=success&redirect_to=settings`});if(e){let t="Failed to update email address.";switch(e.code){case"email_exists":t="This email address is already registered with another account.";break;case"invalid_email":t="Please enter a valid email address.";break;case"email_change_confirm_limit":t="Too many email change requests. Please wait before trying again.";break;case"over_email_send_rate_limit":t="Email rate limit exceeded. Please wait before requesting another verification email.";break;case"email_not_confirmed":t="Please confirm your current email address before changing it.";break;case"same_email":t="The new email address is the same as your current email.";break;default:t="Unable to update email address. Please try again later."}return{message:t,success:!1}}return{message:"Verification email sent to both old and new addresses. Please check your inbox to complete the change.",success:!0}}catch(e){return{message:"An unexpected error occurred while updating email.",success:!1}}}let h=l.z.object({email:l.z.string().email("Invalid email address.")});async function p(){let e=await (0,i.createClient)(),{data:{user:t},error:a}=await e.auth.getUser();if(a||!t||!t.email)return{success:!1,message:"Authentication required or no email found."};try{let{error:a}=await e.auth.signInWithOtp({email:t.email,options:{shouldCreateUser:!1}});if(a){let e="Failed to send verification code.";return(a.message?.includes("email_send_rate_limit")||a.message?.includes("over_email_send_rate_limit"))&&(e="Email rate limit exceeded. Please try again later."),{success:!1,message:e}}return{success:!0,message:"Verification code sent to your current email address.",email:t.email}}catch(e){return console.error("Error sending email change OTP:",e),{success:!1,message:"An unexpected error occurred while sending verification code."}}}async function f(e,t){let a=await (0,i.createClient)(),{data:{user:r},error:s}=await a.auth.getUser();if(s||!r)return{success:!1,message:"Authentication required."};let n=d.safeParse({email:t.get("email"),otp:t.get("otp")});if(!n.success)return{success:!1,message:"Invalid data provided."};let{email:l,otp:c}=n.data;try{let{error:e}=await a.auth.verifyOtp({email:l,token:c,type:"email"});if(e){let t="Failed to verify code.";switch(e.code){case"invalid_otp":case"expired_otp":t="Invalid or expired verification code. Please try again.";break;case"too_many_requests":t="Too many verification attempts. Please wait before trying again.";break;default:t="Unable to verify code. Please try again."}return{success:!1,message:t}}return{success:!0,message:"OTP verified successfully."}}catch(e){return console.error("Error confirming email change OTP:",e),{success:!1,message:"An unexpected error occurred during OTP verification."}}}async function g(){let e=await (0,i.createClient)(),{data:{user:t},error:a}=await e.auth.getUser();return a||!t?{success:!1,hasEmail:!1,hasPhone:!1,message:"Authentication required."}:{success:!0,hasEmail:!!(t.email&&""!==t.email.trim()),hasPhone:!!(t.phone&&""!==t.phone.trim())}}async function b(){let e=await (0,i.createClient)(),{data:{user:t},error:a}=await e.auth.getUser();if(a||!t||!t.email)return{success:!1,message:"Authentication required or no email found."};try{let{error:a}=await e.auth.signInWithOtp({email:t.email,options:{shouldCreateUser:!1}});if(a){if(a.message?.includes("email_send_rate_limit")||a.message?.includes("over_email_send_rate_limit"))return{success:!1,message:"Email rate limit exceeded. Please try again later.",isConfigurationError:!0};return{success:!1,message:a.message||"Failed to send verification code."}}return{success:!0,message:"Verification code sent to your email address.",email:t.email}}catch(e){return{success:!1,message:"An unexpected error occurred while sending verification code."}}}async function y(e,t){let a=await (0,i.createClient)();try{let{error:r}=await a.auth.verifyOtp({email:e,token:t,type:"email"});if(r){let e="Failed to verify code.";switch(r.code){case"invalid_otp":case"expired_otp":e="Invalid or expired verification code. Please try again.";break;case"too_many_requests":e="Too many verification attempts. Please wait before trying again.";break;default:e="Unable to verify code. Please try again."}return{success:!1,message:e}}return{success:!0,message:"Verification successful."}}catch(e){return{success:!1,message:"An unexpected error occurred during verification."}}}async function v(e){let t=await (0,i.createClient)(),{data:{user:a},error:r}=await t.auth.getUser();if(r||!a||!a.phone)return{success:!1,message:"Authentication required or no phone found."};try{let{error:r}=await t.auth.signInWithPassword({phone:a.phone,password:e});if(r)return{success:!1,message:"Invalid password. Please try again."};return{success:!0,message:"Password verified successfully."}}catch(e){return{success:!1,message:"An unexpected error occurred during password verification."}}}async function j(){let e=await (0,i.createClient)(),{data:{user:t},error:a}=await e.auth.getUser();if(a||!t)return{message:"Not authenticated",success:!1};try{let a="customers",r=(0,n.getScalableUserPath)(t.id),s=async t=>{let{data:r,error:i}=await e.storage.from(a).list(t);if(i)return void console.error(`Error listing files in ${t}:`,i);if(!r||0===r.length)return;let n=[],l=[];for(let e of r){let a=t?`${t}/${e.name}`:e.name;null===e.metadata?l.push(a):n.push(a)}if(n.length>0){let{error:r}=await e.storage.from(a).remove(n);r&&"The resource was not found"!==r.message?console.error(`Error deleting files in ${t}:`,r):console.log(`Successfully deleted ${n.length} files in ${t}`)}for(let e of l)await s(e)};await s(r),console.log("Successfully cleaned up customer storage data")}catch(e){console.error("Error checking/cleaning customer storage:",e)}try{let e=await (0,i.createClient)();console.log("Deleting customer profile...");let{error:a}=await e.from("customer_profiles").delete().eq("id",t.id);if(a)return console.error("Error deleting customer profile:",a),{message:`Failed to delete customer profile: ${a.message}`,success:!1};console.log("Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed."),await e.auth.signOut();let{error:r}=await e.auth.admin.deleteUser(t.id,!1);if(r)return console.error("Error deleting user account:",r),{message:`Failed to delete account: ${r.message}`,success:!1};return(0,c.revalidatePath)("/","layout"),{message:"Account deleted successfully.",success:!0}}catch(e){return console.error("Unexpected error during account deletion:",e),{message:"An unexpected error occurred during account deletion.",success:!1}}}(0,u.D)([m,x,p,f,g,b,y,v,j]),(0,s.A)(m,"608844a615b87b85d4667dfe96d0b36222157db949",null),(0,s.A)(x,"6021663cdb43cf5374d99502948b3be7537d9a7970",null),(0,s.A)(p,"00ac5331100c6eb661a835872f4aca4f1c0c57af60",null),(0,s.A)(f,"60fe30a842b0e4f65cd95fe2d407dda8da860d82ff",null),(0,s.A)(g,"00d2052c821136e9e2e8d8101d8866f4f3a3206603",null),(0,s.A)(b,"00587c2eafaa8506ef7d1a09b6ceb2abfc5ab559fa",null),(0,s.A)(y,"60890d22f4c7992f648c81b47a901bc0863dd3c988",null),(0,s.A)(v,"40393e24ab017bf6535b49b096bdca35f76e7cba5c",null),(0,s.A)(j,"00890d9a7f09ef9a0793405d70dcac6ff5a3417c20",null)},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95682:(e,t,a)=>{"use strict";a.d(t,{Ke:()=>w,R6:()=>v,UC:()=>C,bL:()=>A,l9:()=>P,z3:()=>p});var r=a(43210),s=a(70569),i=a(11273),n=a(65551),l=a(66156),d=a(98599),c=a(3416),o=a(46059),u=a(19344),m=a(60687),x="Collapsible",[h,p]=(0,i.A)(x),[f,g]=h(x),b=r.forwardRef((e,t)=>{let{__scopeCollapsible:a,open:s,defaultOpen:i,disabled:l,onOpenChange:d,...o}=e,[h,p]=(0,n.i)({prop:s,defaultProp:i??!1,onChange:d,caller:x});return(0,m.jsx)(f,{scope:a,disabled:l,contentId:(0,u.B)(),open:h,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),children:(0,m.jsx)(c.sG.div,{"data-state":k(h),"data-disabled":l?"":void 0,...o,ref:t})})});b.displayName=x;var y="CollapsibleTrigger",v=r.forwardRef((e,t)=>{let{__scopeCollapsible:a,...r}=e,i=g(y,a);return(0,m.jsx)(c.sG.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":k(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...r,ref:t,onClick:(0,s.m)(e.onClick,i.onOpenToggle)})});v.displayName=y;var j="CollapsibleContent",w=r.forwardRef((e,t)=>{let{forceMount:a,...r}=e,s=g(j,e.__scopeCollapsible);return(0,m.jsx)(o.C,{present:a||s.open,children:({present:e})=>(0,m.jsx)(N,{...r,ref:t,present:e})})});w.displayName=j;var N=r.forwardRef((e,t)=>{let{__scopeCollapsible:a,present:s,children:i,...n}=e,o=g(j,a),[u,x]=r.useState(s),h=r.useRef(null),p=(0,d.s)(t,h),f=r.useRef(0),b=f.current,y=r.useRef(0),v=y.current,w=o.open||u,N=r.useRef(w),A=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>N.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=h.current;if(e){A.current=A.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();f.current=t.height,y.current=t.width,N.current||(e.style.transitionDuration=A.current.transitionDuration,e.style.animationName=A.current.animationName),x(s)}},[o.open,s]),(0,m.jsx)(c.sG.div,{"data-state":k(o.open),"data-disabled":o.disabled?"":void 0,id:o.contentId,hidden:!w,...n,ref:p,style:{"--radix-collapsible-content-height":b?`${b}px`:void 0,"--radix-collapsible-content-width":v?`${v}px`:void 0,...e.style},children:w&&i})});function k(e){return e?"open":"closed"}var A=b,P=v,C=w},96882:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},99008:(e,t,a)=>{"use strict";a.d(t,{NV:()=>d,UV:()=>l,sF:()=>c});var r=a(60687),s=a(43210),i=a(78119),n=a(96241);function l({className:e,containerClassName:t,...a}){return(0,r.jsx)(i.wE,{"data-slot":"input-otp",containerClassName:(0,n.cn)("flex items-center gap-2 has-disabled:opacity-50",t),className:(0,n.cn)("disabled:cursor-not-allowed",e),...a})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"input-otp-group",className:(0,n.cn)("flex items-center",e),...t})}function c({index:e,className:t,...a}){let l=s.useContext(i.dK),{char:d,hasFakeCaret:c,isActive:o}=l?.slots[e]??{};return(0,r.jsxs)("div",{"data-slot":"input-otp-slot","data-active":o,className:(0,n.cn)("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]",t),...a,children:[d,c&&(0,r.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-caret-blink bg-foreground h-4 w-px duration-1000"})})]})}},99891:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,5880,8567,3442,4017,4208,634,3037,3739,9538,5918],()=>a(46903));module.exports=r})();