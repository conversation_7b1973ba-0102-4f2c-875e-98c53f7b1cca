"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[781],{2564:(e,t,r)=>{r.d(t,{Qg:()=>i,bL:()=>s});var n=r(12115),o=r(63540),l=r(95155),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,l.jsx)(o.sG.span,{...e,ref:t,style:{...i,...e.style}}));a.displayName="VisuallyHidden";var s=a},17580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},78082:(e,t,r)=>{r.d(t,{i3:()=>K,UC:()=>Z,ZL:()=>Y,Kq:()=>G,bL:()=>W,l9:()=>X});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(19178),s=r(61285),u=r(35152),c=r(34378),d=r(28905),p=r(63540),f=r(95155),h=Symbol("radix.slottable"),x=r(5845),y=r(2564),[v,g]=(0,i.A)("Tooltip",[u.Bk]),b=(0,u.Bk)(),m="TooltipProvider",w="tooltip.open",[C,T]=v(m),k=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,f.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};k.displayName=m;var E="Tooltip",[j,L]=v(E),R=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:c}=e,d=T(E,e.__scopeTooltip),p=b(t),[h,y]=n.useState(null),v=(0,s.B)(),g=n.useRef(0),m=null!=a?a:d.disableHoverableContent,C=null!=c?c:d.delayDuration,k=n.useRef(!1),[L,R]=(0,x.i)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),null==i||i(e)},caller:E}),_=n.useMemo(()=>L?k.current?"delayed-open":"instant-open":"closed",[L]),M=n.useCallback(()=>{window.clearTimeout(g.current),g.current=0,k.current=!1,R(!0)},[R]),P=n.useCallback(()=>{window.clearTimeout(g.current),g.current=0,R(!1)},[R]),N=n.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{k.current=!0,R(!0),g.current=0},C)},[C,R]);return n.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,f.jsx)(u.bL,{...p,children:(0,f.jsx)(j,{scope:t,contentId:v,open:L,stateAttribute:_,trigger:h,onTriggerChange:y,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?N():M()},[d.isOpenDelayedRef,N,M]),onTriggerLeave:n.useCallback(()=>{m?P():(window.clearTimeout(g.current),g.current=0)},[P,m]),onOpen:M,onClose:P,disableHoverableContent:m,children:r})})};R.displayName=E;var _="TooltipTrigger",M=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,a=L(_,r),s=T(_,r),c=b(r),d=n.useRef(null),h=(0,l.s)(t,d,a.onTriggerChange),x=n.useRef(!1),y=n.useRef(!1),v=n.useCallback(()=>x.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,f.jsx)(u.Mz,{asChild:!0,...c,children:(0,f.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:h,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),x.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{x.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});M.displayName=_;var P="TooltipPortal",[N,O]=v(P,{forceMount:void 0}),D=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,l=L(P,t);return(0,f.jsx)(N,{scope:t,forceMount:r,children:(0,f.jsx)(d.C,{present:r||l.open,children:(0,f.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};D.displayName=P;var I="TooltipContent",B=n.forwardRef((e,t)=>{let r=O(I,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...l}=e,i=L(I,e.__scopeTooltip);return(0,f.jsx)(d.C,{present:n||i.open,children:i.disableHoverableContent?(0,f.jsx)(q,{side:o,...l,ref:t}):(0,f.jsx)(A,{side:o,...l,ref:t})})}),A=n.forwardRef((e,t)=>{let r=L(I,e.__scopeTooltip),o=T(I,e.__scopeTooltip),i=n.useRef(null),a=(0,l.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=i.current,{onPointerInTransitChange:h}=o,x=n.useCallback(()=>{u(null),h(!1)},[h]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(r,n,o,l)){case l:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),h(!0)},[h]);return n.useEffect(()=>()=>x(),[x]),n.useEffect(()=>{if(c&&p){let e=e=>y(e,p),t=e=>y(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,y,x]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?x():o&&(x(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,x]),(0,f.jsx)(q,{...e,ref:a})}),[H,S]=v(E,{isInside:!1}),F=function(e){let t=({children:e})=>(0,f.jsx)(f.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=h,t}("TooltipContent"),q=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,d=L(I,r),p=b(r),{onClose:h}=d;return n.useEffect(()=>(document.addEventListener(w,h),()=>document.removeEventListener(w,h)),[h]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,h]),(0,f.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,f.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,f.jsx)(F,{children:o}),(0,f.jsx)(H,{scope:r,isInside:!0,children:(0,f.jsx)(y.bL,{id:d.contentId,role:"tooltip",children:l||o})})]})})});B.displayName=I;var U="TooltipArrow",z=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=b(r);return S(U,r).isInside?null:(0,f.jsx)(u.i3,{...o,...n,ref:t})});z.displayName=U;var G=k,W=R,X=M,Y=D,Z=B,K=z}}]);