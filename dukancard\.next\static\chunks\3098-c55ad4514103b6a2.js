"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3098],{49026:(e,t,a)=>{a.d(t,{Fc:()=>i,TN:()=>o,XL:()=>d});var s=a(95155);a(12115);var r=a(74466),l=a(53999);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(n({variant:a}),t),...r})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,l.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},53687:(e,t,a)=>{a.d(t,{default:()=>n});var s=a(95155),r=a(27737),l=a(60944);function n(){return(0,s.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black",children:[(0,s.jsx)("div",{className:"w-full py-6 mt-6",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-3 items-start",children:[(0,s.jsx)(r.E,{className:"h-12 w-full md:w-[140px]"}),(0,s.jsxs)("div",{className:"flex-1 w-full flex flex-col md:flex-row gap-3",children:[(0,s.jsx)(r.E,{className:"h-12 flex-1"}),(0,s.jsx)(r.E,{className:"h-12 w-full md:w-[200px]"}),(0,s.jsx)(r.E,{className:"h-12 w-full md:w-[120px]"})]})]})})})}),(0,s.jsxs)("div",{className:"mb-6 container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)(r.E,{className:"h-5 w-36"}),(0,s.jsx)(r.E,{className:"h-4 w-24"})]}),(0,s.jsx)("div",{className:"flex gap-2 overflow-hidden",children:Array.from({length:5}).map((e,t)=>(0,s.jsx)(r.E,{className:"h-20 w-20 flex-shrink-0 rounded-xl"},t))})]}),(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)(r.E,{className:"h-10 w-64 rounded-xl"})}),(0,s.jsx)("div",{className:"container mx-auto px-4 mb-4",children:(0,s.jsx)(r.E,{className:"h-12 w-full rounded-lg"})}),(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsx)(l.A,{})})]})}},58880:(e,t,a)=>{a.d(t,{J:()=>m,u:()=>h});var s=a(95155),r=a(12115),l=a(35695),n=a(34477);let i=(0,n.createServerReference)("40edb48823df0fd876d80ab1b1ef92ac50df45d091",n.callServer,void 0,n.findSourceMapURL,"searchDiscoverCombined");var d=a(77560),o=a(94234);let c=(0,n.createServerReference)("4027ebed4ed3330c943c58b278d97ba413e3a31d66",n.callServer,void 0,n.findSourceMapURL,"fetchMoreBusinessCardsCombined"),u=(0,n.createServerReference)("4018b43028809df977ce7bb88f96c0d45dd8e2c788",n.callServer,void 0,n.findSourceMapURL,"fetchMoreProductsCombined"),p=(0,r.createContext)(void 0);function m(e){let{children:t}=e,a=(0,l.useSearchParams)(),n=a.get(o.ho)||null,m=a.get(o.lX)||null,h=a.get(o.KC)||null,g=a.get(o.Ie)||null,f=a.get(o.dv)||null,w=a.get(o.b2)||"products",x=a.get(o.OL)||"created_desc",v=a.get(o.v0)||"newest",y=a.get("sortBy");if(y){"cards"===w?x=y:"products"===w&&(v=y);{let e=new URL(window.location.href);e.searchParams.delete("sortBy"),e.searchParams.set(o.OL,x),e.searchParams.set(o.v0,v),window.history.replaceState({},"",e.toString())}}let b=a.get(o.ry)||"all",[S,N]=(0,r.useState)(w),[j,P]=(0,r.useState)(x),[_,C]=(0,r.useState)(f),[T,A]=(0,r.useState)(!1),[M,L]=(0,r.useState)(!1),[I,R]=(0,r.useState)(null),[z,E]=(0,r.useState)(b),[U,X]=(0,r.useState)(v),[F,k]=(0,r.useState)(null),[V,B]=(0,r.useState)([]),[K,O]=(0,r.useState)([]),[D,J]=(0,r.useState)(1),[q,W]=(0,r.useState)(!1),[Y,Z]=(0,r.useState)(0),[G,H]=(0,r.useState)(!1),{handleBusinessSortChange:Q,handleBusinessSearch:$,loadMoreBusinesses:ee}=function(e,t,a,s,n,d,u,p,m,h,g,f){let w=(0,l.useSearchParams)(),[,x]=(0,r.useTransition)();return{handleBusinessSortChange:r=>{if(r!==f){m(r),t(!0);{let e=new URL(window.location.href);e.searchParams.set(o.OL,r),e.searchParams.delete("sortBy"),window.history.replaceState({},"",e.toString())}if("cards"===e){let l=w.get(o.ho)||null,c=w.get(o.lX)||null,m=w.get(o.Ie)||null;"_any"===m&&(m=""),x(async()=>{let o=await i({businessName:l,pincode:c,locality:m,viewType:e,page:1,limit:20,businessSort:r});o.data?(a(o.data),s(o.data.isAuthenticated),o.data.businesses&&n(o.data.businesses),d(o.data.hasMore),u(o.data.totalCount),p(1)):h(o.error||"Failed to fetch results."),t(!1)})}}},handleBusinessSearch:r=>{if("cards"!==e)return;t(!0);{let e=new URL(window.location.href);r?e.searchParams.set(o.ho,r):e.searchParams.delete(o.ho),window.history.replaceState({},"",e.toString())}if(!r){let r=w.get(o.lX)||null,l=w.get(o.Ie)||null;"_any"===l&&(l=""),x(async()=>{await new Promise(e=>setTimeout(e,100));let o=await i({businessName:null,pincode:r,locality:l,viewType:e,page:1,limit:20,businessSort:f});o.data?(a(o.data),s(o.data.isAuthenticated),o.data.businesses&&n(o.data.businesses),d(o.data.hasMore),u(o.data.totalCount),p(1)):h(o.error||"Failed to fetch results."),t(!1)});return}let l=w.get(o.lX)||null,c=w.get(o.Ie)||null;"_any"===c&&(c=""),h(null),n([]),p(1),d(!1),u(0),x(async()=>{await new Promise(e=>setTimeout(e,100));let o=await i({businessName:r,pincode:l,locality:c,viewType:e,page:1,limit:20,businessSort:f});o.data?(a(o.data),s(o.data.isAuthenticated),o.data.businesses&&n(o.data.businesses),d(o.data.hasMore),u(o.data.totalCount),p(1)):h(o.error||"Failed to fetch results."),t(!1)})},loadMoreBusinesses:async(e,t,a)=>{if(t)return;let s=w.get(o.ho)||null,r=w.get(o.lX)||null,l=w.get(o.Ie)||null;"_any"===l&&(l=""),a(!0);try{await new Promise(e=>setTimeout(e,300));let t=await c({businessName:s,pincode:r,locality:l,page:e,limit:20,sortBy:f});if(!t.data||!t.data.businesses)return d(!1),!1;if(!(t.data.businesses.length>0))return d(!1),!1;{let a=t.data.businesses,s=new Set(g.map(e=>e.id)),r=a.filter(e=>!s.has(e.id));if(!(r.length>0))return console.warn("All new businesses were duplicates, stopping infinite scroll"),d(!1),!1;{let a=[...g,...r];return n(a),d(t.data.hasMore),p(e),!0}}}catch(e){return console.error("Error loading more businesses:",e),d(!1),!1}finally{a(!1)}}}}(S,A,k,H,B,W,Z,J,P,R,V,j),{handleProductSortChange:et,handleProductSearch:ea,handleProductFilterChange:es,loadMoreProducts:er}=function(e,t,a,s,n,c,p,m,h,g,f,w,x,v,y){let b=(0,l.useSearchParams)(),[,S]=(0,r.useTransition)();return{handleProductSortChange:r=>{if(r!==v){h(r),t(!0);{let e=new URL(window.location.href);e.searchParams.set(o.v0,r),e.searchParams.delete("sortBy"),window.history.replaceState({},"",e.toString())}let l=(0,d.tV)(r);if("products"===e){let r=b.get(o.u0)||null,d=b.get(o.lX)||null,u=b.get(o.Ie)||null;"_any"===u&&(u=""),S(async()=>{let o=await i({businessName:null,productName:r,pincode:d,locality:u,viewType:e,page:1,limit:20,productSort:l,productType:"all"===y?null:y});o.data?(a(o.data),s(o.data.isAuthenticated),"products"===e&&o.data.products&&n(o.data.products),c(o.data.hasMore),p(o.data.totalCount),m(1)):f(o.error||"Failed to fetch results."),t(!1)})}}},handleProductSearch:r=>{if("products"!==e)return;t(!0);{let e=new URL(window.location.href);r?e.searchParams.set(o.u0,r):e.searchParams.delete(o.u0),window.history.replaceState({},"",e.toString())}if(!r){let r=b.get(o.lX)||null,l=b.get(o.Ie)||null;"_any"===l&&(l=""),S(async()=>{await new Promise(e=>setTimeout(e,100));let o=(0,d.tV)(v),u=await i({businessName:null,productName:null,pincode:r,locality:l,viewType:e,page:1,limit:20,productSort:o,productType:"all"===y?null:y});u.data?(a(u.data),s(u.data.isAuthenticated),"products"===e&&u.data.products&&n(u.data.products),c(u.data.hasMore),p(u.data.totalCount),m(1)):f(u.error||"Failed to fetch results."),t(!1)});return}let l=b.get(o.lX)||null,u=b.get(o.Ie)||null;"_any"===u&&(u=""),f(null),n([]),m(1),c(!1),p(0),S(async()=>{await new Promise(e=>setTimeout(e,100));let o=(0,d.tV)(v),h=await i({businessName:null,productName:r,pincode:l,locality:u,viewType:e,page:1,limit:20,productSort:o,productType:"all"===y?null:y});h.data?(a(h.data),s(h.data.isAuthenticated),"products"===e&&h.data.products&&n(h.data.products),c(h.data.hasMore),p(h.data.totalCount),m(1)):f(h.error||"Failed to fetch results."),t(!1)})},handleProductFilterChange:r=>{if(r!==y){g(r),t(!0);{let e=new URL(window.location.href);e.searchParams.set(o.ry,r),window.history.replaceState({},"",e.toString())}if("products"===e){let l=b.get(o.u0)||null,u=b.get(o.lX)||null,h=b.get(o.Ie)||null;"_any"===h&&(h=""),S(async()=>{let o=(0,d.tV)(v),g=await i({businessName:null,productName:l,pincode:u,locality:h,viewType:e,page:1,limit:20,productSort:o,productType:"all"===r?null:r});g.data?(a(g.data),s(g.data.isAuthenticated),"products"===e&&g.data.products&&n(g.data.products),c(g.data.hasMore),p(g.data.totalCount),m(1)):f(g.error||"Failed to fetch results."),t(!1)})}}},loadMoreProducts:async(e,t,a)=>{if(t)return;let s=b.get(o.u0)||null,r=b.get(o.lX)||null,l=b.get(o.Ie)||null;"_any"===l&&(l=""),a(!0);try{await new Promise(e=>setTimeout(e,300));let t=(0,d.tV)(v),a=await u({businessName:null,productName:s,pincode:r,locality:l,page:e,limit:20,productSort:t,productType:"all"===y?null:y});if(!a.data||!a.data.products)return c(!1),!1;if(!(a.data.products.length>0))return c(!1),!1;{let t=a.data.products,s=new Set(w.map(e=>e.id)),r=t.filter(e=>!s.has(e.id));if(!(r.length>0))return console.warn("All new products were duplicates, stopping infinite scroll"),c(!1),!1;{let t=[...w,...r];return n(t),c(a.data.hasMore),m(e),!0}}}catch(e){return console.error("Error loading more products:",e),c(!1),!1}finally{a(!1)}}}}(S,A,k,H,O,W,Z,J,X,E,R,K,0,U,z),{isPending:el,handleViewChange:en,performSearch:ei,loadMore:ed}=function(e,t,a,s,n,d,c,u,p,m,h,g,f,w,x){let v=(0,l.useSearchParams)(),[y,b]=(0,r.useTransition)(),S=async(t,a,s,r,l)=>{if(r||y)return;let n=s+1;"cards"===e?await t(n,r,l):await a(n,r,l)};return{isPending:y,handleViewChange:r=>{if(r!==e){t(r),a(!0),"products"===r?c([]):d([]);{let e=new URL(window.location.href);e.searchParams.set(o.b2,r),window.history.replaceState({},"",e.toString())}let e=v.get(o.ho)||null,l=v.get(o.u0)||null,y=v.get(o.lX)||null,S=v.get(o.KC)||null,N=v.get(o.Ie)||null;"_any"===N&&(N=""),b(async()=>{try{await new Promise(e=>setTimeout(e,100)),"products"===r?d([]):c([]);let t=await i({businessName:"cards"===r?e:null,productName:"products"===r?l:null,pincode:y,city:S,locality:N,viewType:r,page:1,limit:20,businessSort:g,productSort:f,productType:"products"===r&&"all"!==w?w:null,category:x});t.data?(s(t.data),n(t.data.isAuthenticated),"cards"===r&&t.data.businesses?d(t.data.businesses):"products"===r&&t.data.products&&c(t.data.products),u(t.data.hasMore),p(t.data.totalCount),m(1)):h(t.error||"Failed to fetch results.")}catch(e){console.error("Error changing view:",e),h("An unexpected error occurred.")}finally{a(!1)}})}},performSearch:t=>{a(!0),h(null),"cards"===e?d([]):c([]),m(1),u(!1),p(0);let{businessName:r,pincode:l,city:v,locality:y,category:S}=t;{let t=new URL(window.location.href);t.searchParams.delete(o.ho),t.searchParams.delete(o.u0),t.searchParams.delete(o.lX),t.searchParams.delete(o.KC),t.searchParams.delete(o.Ie),t.searchParams.delete(o.dv),r&&("cards"===e?t.searchParams.set(o.ho,r):t.searchParams.set(o.u0,r)),l?(t.searchParams.set(o.lX,l),y&&"_any"!==y&&t.searchParams.set(o.Ie,y)):v&&t.searchParams.set(o.KC,v),S&&t.searchParams.set(o.dv,S),window.history.replaceState({},"",t.toString())}b(async()=>{try{await new Promise(e=>setTimeout(e,100));let t=await i({businessName:"cards"===e?r:null,productName:"products"===e?r:null,pincode:l,city:v,locality:"_any"===y?"":y,category:void 0!==S?S:x,viewType:e,page:1,limit:20,businessSort:g,productSort:f,productType:"products"===e&&"all"!==w?w:null});t.data?(s(t.data),n(t.data.isAuthenticated),"cards"===e&&t.data.businesses?d(t.data.businesses):"products"===e&&t.data.products&&c(t.data.products),u(t.data.hasMore),p(t.data.totalCount)):h(t.error||"Failed to fetch results.")}catch(e){console.error("Unexpected error in performSearch:",e),h("An unexpected error occurred. Please try again.")}finally{a(!1)}})},loadMore:S}}(S,N,A,k,H,B,O,W,Z,J,R,j,U,z,_),eo=async()=>{await ed(ee,er,D,M,L)};return(0,r.useEffect)(()=>{A(!0),n||m||h||f?ei({businessName:n,pincode:m,city:h,locality:"_any"===g?"":g,category:f}):(async()=>{try{console.log("Initial load: Fetching products");let e=await i({viewType:"products",page:1,limit:20,productSort:(0,d.tV)(U),productType:"all"===z?null:z});e.data?(k(e.data),H(e.data.isAuthenticated),e.data.products?(O(e.data.products),W(e.data.hasMore)):W(!1),Z(e.data.totalCount)):R(e.error||"Failed to fetch results.")}catch(e){console.error("Unexpected error loading initial data:",e),R("An unexpected error occurred. Please try again.")}finally{A(!1)}})()},[]),(0,s.jsx)(p.Provider,{value:{viewType:S,sortBy:j,selectedCategory:_,isSearching:T,isPending:el,isLoadingMore:M,searchError:I,productFilterBy:z,productSortBy:U,searchResult:F,businesses:V,products:K,currentPage:D,hasMore:q,totalCount:Y,isAuthenticated:G,performSearch:ei,handleViewChange:en,handleBusinessSortChange:Q,handleBusinessSearch:$,handleProductSearch:ea,handleProductSortChange:et,handleProductFilterChange:es,handleCategoryChange:e=>{C(e);{let t=new URL(window.location.href);e?t.searchParams.set(o.dv,e):t.searchParams.delete(o.dv),window.history.replaceState({},"",t.toString())}J(1),W(!1);let t=a.get(o.ho),s=a.get(o.lX);ei({businessName:t,pincode:s,city:a.get(o.KC),locality:a.get(o.Ie),category:e})},loadMore:eo},children:t})}function h(){let e=(0,r.useContext)(p);if(void 0===e)throw Error("useDiscoverContext must be used within a DiscoverProvider");return e}},77560:(e,t,a)=>{a.d(t,{tV:()=>s});function s(e){switch(e){case"newest":default:return"created_desc";case"name_asc":return"name_asc";case"name_desc":return"name_desc";case"price_low":return"price_asc";case"price_high":return"price_desc"}}},90497:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(95155),r=a(12115),l=a(28695),n=a(59779);function i(e){let{businesses:t}=e,a=(0,r.useRef)(null);return(0,s.jsx)(l.P.div,{ref:a,className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:t.map((e,t)=>(0,s.jsx)(n.A,{business:e,index:t},e.id))})}},94234:(e,t,a)=>{a.d(t,{Ie:()=>c,KC:()=>o,OL:()=>r,b2:()=>u,dv:()=>p,ho:()=>s,lX:()=>d,ry:()=>i,u0:()=>l,v0:()=>n});let s="businessName",r="businessSort",l="productName",n="productSort",i="productType",d="pincode",o="city",c="locality",u="view",p="category"},95784:(e,t,a)=>{a.d(t,{TR:()=>h,bq:()=>p,eb:()=>g,gC:()=>m,l6:()=>o,s3:()=>c,yv:()=>u});var s=a(95155);a(12115);var r=a(43433),l=a(66474),n=a(5196),i=a(47863),d=a(53999);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.YJ,{"data-slot":"select-group",...t})}function u(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function p(e){let{className:t,size:a="default",children:n,...i}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:l="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...n,children:[(0,s.jsx)(f,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(w,{})]})})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.JU,{"data-slot":"select-label",className:(0,d.cn)("text-muted-foreground px-2 py-1.5 text-xs",t),...a})}function g(e){let{className:t,children:a,...l}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}function w(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}}}]);