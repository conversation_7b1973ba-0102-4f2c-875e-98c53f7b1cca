(()=>{var e={};e.id=6644,e.ids=[6644],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12463:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});let s={FREE:"free",BASIC:"basic",GROWTH:"growth",PRO:"pro",ENTERPRISE:"enterprise"};class i{static determineSubscriptionFlow(e){let{context:t}=e,{trialEndDate:r,razorpaySubscriptionId:s}=t,i=this.isPostTrial(r),n=!!s;return i&&!n?{flowType:"UPFRONT_PAYMENT",requiresUpfrontPayment:!0,shouldCancelExisting:!1,shouldUpdateExisting:!1,paymentTiming:"IMMEDIATE",reason:"Post-trial user with no active subscription - requires upfront payment"}:n?{flowType:"CREATE_AND_CANCEL",requiresUpfrontPayment:!0,shouldCancelExisting:!0,shouldUpdateExisting:!1,paymentTiming:"IMMEDIATE",reason:"Active subscription - create new and cancel old (simplified flow for all payment methods)"}:{flowType:"FRESH_SUBSCRIPTION",requiresUpfrontPayment:!this.isOnTrial(r),shouldCancelExisting:!1,shouldUpdateExisting:!1,paymentTiming:this.isOnTrial(r)?"TRIAL_END":"IMMEDIATE",reason:"Fresh subscription - new user or trial user"}}static isPostTrial(e){return!!e&&new Date(e)<=new Date}static isOnTrial(e){return!!e&&new Date(e)>new Date}static validateRequest(e){let{planId:t,planCycle:r,context:i}=e;return Object.values(s).includes(t)?["monthly","yearly"].includes(r)?i.userId?{valid:!0}:{valid:!1,error:"User ID is required"}:{valid:!1,error:"Invalid plan cycle"}:{valid:!1,error:"Invalid plan ID"}}static getFlowDescription(e){switch(e.flowType){case"UPFRONT_PAYMENT":return"Payment will be processed immediately and subscription activated upon successful payment.";case"CREATE_AND_CANCEL":return"A new subscription will be created and your current subscription will be cancelled after activation.";case"FRESH_SUBSCRIPTION":return"TRIAL_END"===e.paymentTiming?"Subscription will be created and payment will be processed when your trial ends.":"New subscription will be created and activated immediately.";default:return"Subscription will be processed according to your current plan status."}}}},27413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c});var i=r(96559),n=r(48088),a=r(37719),u=r(32190),l=r(32032),o=r(12463);async function c(e){try{return u.NextResponse.json({success:!1,error:"Test endpoints only available in development"},{status:403})}catch(e){return console.error("[TEST_API] Error running subscription flow tests:",e),u.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test/subscription-flow/route",pathname:"/api/test/subscription-flow",filename:"route",bundlePath:"app/api/test/subscription-flow/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\test\\subscription-flow\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:y}=p;function b(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>i});var s=r(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let i=null,n=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));i=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,i=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,i){let n=()=>{var n,a,u,l,o;return n=e,a=t,u=r,l=s,o=i,"customer_profiles"===n?{data:u&&"customer"===a?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:u&&"business"===a?{id:"test-user-id",business_slug:l||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===a?{id:"test-subscription-id",plan_id:o,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===a?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},a=e=>({select:t=>a(e),eq:(t,r)=>a(e),neq:(t,r)=>a(e),gt:(t,r)=>a(e),gte:(t,r)=>a(e),lt:(t,r)=>a(e),lte:(t,r)=>a(e),like:(t,r)=>a(e),ilike:(t,r)=>a(e),is:(t,r)=>a(e),in:(t,r)=>a(e),contains:(t,r)=>a(e),containedBy:(t,r)=>a(e),rangeGt:(t,r)=>a(e),rangeGte:(t,r)=>a(e),rangeLt:(t,r)=>a(e),rangeLte:(t,r)=>a(e),rangeAdjacent:(t,r)=>a(e),overlaps:(t,r)=>a(e),textSearch:(t,r)=>a(e),match:t=>a(e),not:(t,r,s)=>a(e),or:t=>a(e),filter:(t,r,s)=>a(e),order:(t,r)=>a(e),limit:(t,r)=>a(e),range:(t,r,s)=>a(e),abortSignal:t=>a(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>a(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>a(e),upsert:e=>a(e),delete:()=>a(),rpc:(e,t)=>a()}})(e,r,s,i,n)}}(i):n?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await n.set(t,r,s)}catch{}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,9398,4386,580],()=>r(27413));module.exports=s})();