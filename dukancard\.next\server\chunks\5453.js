"use strict";exports.id=5453,exports.ids=[5453],exports.modules={95453:(r,e,t)=>{t.d(e,{ST:()=>s,bG:()=>a,t6:()=>c});var o=t(55511),n=t.n(o);let s="https://api.razorpay.com/v2",i=()=>{let r,e;if(r=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",e=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!r||!e)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!r,keySecret:!!e,env:"production"}),Error("Razorpay credentials not configured");return{keyId:r,keySecret:e}},a=()=>{let{keyId:r,keySecret:e}=i(),t=Buffer.from(`${r}:${e}`).toString("base64");return{Authorization:`Basic ${t}`,"Content-Type":"application/json"}},c=(r,e,t)=>{try{let o=n().createHmac("sha256",t).update(r).digest("hex");return n().timingSafeEqual(Buffer.from(e),Buffer.from(o))}catch(r){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",r),!1}}}};