(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5448],{11897:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(95155),s=t(12115),l=t(6874),n=t.n(l),i=t(85339),c=t(53904),o=t(57340);function d(e){let{error:r,reset:t}=e;return(0,s.useEffect)(()=>{console.error("Single post page error:",r)},[r]),(0,a.jsx)("div",{className:"min-h-screen bg-white dark:bg-black flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)(i.A,{className:"w-16 h-16 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"We encountered an error while loading this post. This might be a temporary issue."}),!1]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center justify-center w-full px-4 py-2 bg-[var(--brand-gold)] text-white font-medium rounded-lg transition-colors",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Try Again"]}),(0,a.jsxs)(n(),{href:"/",className:"inline-flex items-center justify-center w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Go to Homepage"]})]})]})})}},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var a=t(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:h,...m}=e;return(0,a.createElement)("svg",{ref:r,...n,width:s,height:s,stroke:t,strokeWidth:c?24*Number(i)/Number(s):i,className:l("lucide",o),...m},[...h.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(d)?d:[d]])}),c=(e,r)=>{let t=(0,a.forwardRef)((t,n)=>{let{className:c,...o}=t;return(0,a.createElement)(i,{ref:n,iconNode:r,className:l("lucide-".concat(s(e)),c),...o})});return t.displayName="".concat(e),t}},29790:(e,r,t)=>{Promise.resolve().then(t.bind(t,11897))},53904:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},57340:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},85339:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[6874,8441,1684,7358],()=>r(29790)),_N_E=e.O()}]);