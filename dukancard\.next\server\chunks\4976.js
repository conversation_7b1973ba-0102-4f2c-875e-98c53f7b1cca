"use strict";exports.id=4976,exports.ids=[4976,5453],exports.modules={94976:(e,t,r)=>{r.d(t,{getPayment:()=>n,getPaymentDetails:()=>a});var o=r(95453);async function a(e){try{console.log(`[RAZORPAY_PAYMENT] Fetching payment details for ID: ${e}`);let t=(0,o.bG)(),r=await fetch(`${o.ST.replace("/v2","/v1")}/payments/${e}`,{method:"GET",headers:t}),a=await r.json();if(!r.ok)return console.error("[RAZORPAY_PAYMENT] Error fetching payment:",a),{success:!1,error:a.error?.description||"Failed to fetch payment details"};return console.log("[RAZORPAY_PAYMENT] Payment details fetched successfully:",{id:a.id,method:a.method,status:a.status,amount:a.amount,currency:a.currency}),{success:!0,data:{id:a.id,amount:a.amount,currency:a.currency,status:a.status,method:a.method,created_at:a.created_at,captured:a.captured,description:a.description,email:a.email,contact:a.contact,notes:a.notes}}}catch(e){return console.error("[RAZORPAY_PAYMENT] Error fetching payment details:",e),{success:!1,error:e instanceof Error?e.message:"Failed to fetch payment details"}}}async function n(e){return a(e)}},95453:(e,t,r)=>{r.d(t,{ST:()=>n,bG:()=>c,t6:()=>i});var o=r(55511),a=r.n(o);let n="https://api.razorpay.com/v2",s=()=>{let e,t;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",t=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!t)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!t,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:t}},c=()=>{let{keyId:e,keySecret:t}=s(),r=Buffer.from(`${e}:${t}`).toString("base64");return{Authorization:`Basic ${r}`,"Content-Type":"application/json"}},i=(e,t,r)=>{try{let o=a().createHmac("sha256",r).update(e).digest("hex");return a().timingSafeEqual(Buffer.from(t),Buffer.from(o))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}}};