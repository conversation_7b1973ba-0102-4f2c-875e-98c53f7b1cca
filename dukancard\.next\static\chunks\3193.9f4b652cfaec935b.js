"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3193],{93193:(r,e,o)=>{o.d(e,{getPostLoginRedirectPath:()=>s});async function s(r,e){try{let[l,u]=await Promise.all([r.from("customer_profiles").select("id").eq("id",e),r.from("business_profiles").select("id, business_slug").eq("id",e)]);if(l.error||u.error){var o,s,a,t,i,n;if(console.error("[redirectAfterLogin] Supabase query error:",l.error,u.error),(null==(o=l.error)?void 0:o.code)==="PGRST116"||(null==(s=u.error)?void 0:s.code)==="PGRST116"||(null==(t=l.error)||null==(a=t.message)?void 0:a.toLowerCase().includes("no rows"))||(null==(n=u.error)||null==(i=n.message)?void 0:i.toLowerCase().includes("no rows")))return"/choose-role";return"/?view=home"}if(l.data&&Array.isArray(l.data)&&l.data.length>0)return"/dashboard/customer";if(u.data&&Array.isArray(u.data)&&u.data.length>0){if(u.data[0].business_slug)return"/dashboard/business";return"/onboarding"}return"/choose-role"}catch(r){return console.error("[redirectAfterLogin] Unexpected error:",r),"/?view=home"}}}}]);