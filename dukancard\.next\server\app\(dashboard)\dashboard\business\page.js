(()=>{var e={};e.id=6828,e.ids=[6828],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3956:(e,s,t)=>{Promise.resolve().then(t.bind(t,64522))},8256:(e,s,t)=>{Promise.resolve().then(t.bind(t,82921))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17313:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},17886:(e,s,t)=>{"use strict";t.r(s),t.d(s,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>a.B,"4009d2933c186901a1403c78560cd329314f35e005":()=>i.ys,"401bed28fefc4fa316c9657ce0fb9ce951b2f6cefe":()=>n.E,"4034b523293d3ffd37df154f2b315894750110b2c1":()=>n.H,"404b1d9bf7310a5b14e59bf18e153b291565613a40":()=>r.w,"4078b27f89338bdea95f74b22fd4d22c4710876eab":()=>i.pD,"60183dfe46fbe995d7961a9cf8fe9545820e57eb60":()=>i.Rl,"60e85a41c7f76e7af6443414547e080e8dfaf27c80":()=>i.yf,"60fef8195d22c743fbc958b9c6e7229ba7a09ebfb2":()=>i.gg});var a=t(64275),r=t(16111),i=t(33635),n=t(26471)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},45488:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var a=t(37413);t(61120);var r=t(32032),i=t(64522);let n=["member_name","title","business_name","business_category","contact_email","phone","address_line","pincode","city","state","locality"];var l=t(39916);async function o({children:e}){let s=await (0,r.createClient)(),t=null,o=null,d=null,c=null,{data:{user:u}}=await s.auth.getUser();if(u){let{data:e,error:a}=await s.from("business_profiles").select(`
        business_name,
        logo_url,
        member_name,
        title,
        business_category,
        contact_email,
        phone,
        address_line,
        pincode,
        city,
        state,
        locality
      `).eq("id",u.id).single(),{data:r}=await s.from("payment_subscriptions").select("plan_id").eq("business_profile_id",u.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(a)console.error("Error fetching business profile in layout:",a.message);else if(e){t=e.business_name,o=e.logo_url,d=e.member_name,c=r?.plan_id||"free";let s=function(e){if(!e)return{isComplete:!1,missingFields:[...n],missingFieldLabels:["Your name","Your title","Business name","Business category","Contact email","Primary phone","Address line","Pincode","City","State","Locality/area"]};let s=[],t=[],a={member_name:"Your name",title:"Your title",business_name:"Business name",business_category:"Business category",contact_email:"Contact email",phone:"Primary phone",address_line:"Address line",pincode:"Pincode",city:"City",state:"State",locality:"Locality/area"};return n.forEach(r=>{let i=e[r];i&&""!==String(i).trim()||(s.push(r),t.push(a[r]))}),{isComplete:0===s.length,missingFields:s,missingFieldLabels:t}}(e);if(!s.isComplete){let e=function(e){if(0===e.length)return"";if(1===e.length)return`Please complete your ${e[0].toLowerCase()} to access the dashboard.`;if(2===e.length)return`Please complete your ${e[0].toLowerCase()} and ${e[1].toLowerCase()} to access the dashboard.`;let s=e[e.length-1],t=e.slice(0,-1);return`Please complete your ${t.map(e=>e.toLowerCase()).join(", ")}, and ${s.toLowerCase()} to access the dashboard.`}(s.missingFieldLabels);(0,l.redirect)(`/onboarding?message=${encodeURIComponent(e)}`)}}}else console.warn("No user found in business dashboard layout.");return(0,a.jsx)(i.default,{businessName:t,logoUrl:o,memberName:d,userPlan:c,children:e})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61192:(e,s,t)=>{"use strict";t.d(s,{default:()=>u});var a=t(60687);t(43210);var r=t(27625),i=t(41956),n=t(38606),l=t(24861),o=t(21121),d=t(96241),c=t(52529);function u({children:e,businessName:s,logoUrl:t,memberName:u,userPlan:m}){return(0,a.jsx)(c.Q,{children:(0,a.jsxs)(l.GB,{children:[(0,a.jsx)(o.s,{businessName:s,logoUrl:t,memberName:u,userPlan:m}),(0,a.jsxs)(l.sF,{children:[(0,a.jsxs)(r.default,{businessName:s,logoUrl:t,userName:u,children:[(0,a.jsx)(l.x2,{className:"ml-auto md:ml-0"})," ",(0,a.jsx)(i.ThemeToggle,{variant:"dashboard"})]}),(0,a.jsx)("main",{className:(0,d.cn)("flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto","pb-20 sm:pb-18 md:pb-6","bg-white dark:bg-black"),children:e}),(0,a.jsx)(n.default,{})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64522:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\components\\\\BusinessDashboardClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx","default")},65063:(e,s,t)=>{"use strict";t.d(s,{default:()=>O});var a=t(60687),r=t(43210),i=t(37472),n=t(77882),l=t(88920),o=t(93613),d=t(41862),c=t(27124),u=t(3018),m=t(24934),h=t(51536),p=t(72945),x=t(73066),g=t(9912),f=t(81381),b=t(19080),y=t(11860),j=t(17313),v=t(99270),N=t(17971),w=t(13964),_=t(9005),k=t(97992),P=t(27900),C=t(70373),A=t(4331),S=t(33135),F=t(52581),R=t(38398),q=t(23135),M=t(16189),L=t(30474),B=t(17071),D=t(96241),E=t(868),$=t(23809),z=t(57601),U=t(51358),I=t(62478);function T({product:e,onRemove:s,formatPrice:t}){let{attributes:r,listeners:i,setNodeRef:n,transform:l,transition:o,isDragging:d}=(0,U.gl)({id:e.id}),c={transform:I.Ks.Transform.toString(l),transition:o,opacity:d?.5:1};return(0,a.jsxs)("div",{ref:n,style:c,className:"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/50 rounded-lg border min-h-[60px] sm:min-h-[68px]",children:[(0,a.jsx)("div",{...r,...i,className:"cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground shrink-0",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})}),(0,a.jsx)("div",{className:"relative h-8 w-8 sm:h-10 sm:w-10 shrink-0 rounded-md overflow-hidden bg-background",children:e.image_url?(0,a.jsx)(L.default,{src:e.image_url,alt:e.name,fill:!0,className:"object-cover",sizes:"(max-width: 640px) 32px, 40px"}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,a.jsx)(b.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0 pr-1 sm:pr-2",children:[(0,a.jsx)("div",{className:"font-medium text-xs sm:text-sm leading-tight mb-1",children:(0,a.jsx)("span",{className:"line-clamp-1 break-words",children:e.name})}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.discounted_price?(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-wrap",children:[(0,a.jsx)("span",{className:"text-primary font-medium",children:t(e.discounted_price)}),(0,a.jsx)("span",{className:"line-through text-xs",children:t(e.base_price)})]}):(0,a.jsx)("span",{className:"font-medium",children:t(e.base_price)})})]}),(0,a.jsx)(m.$,{variant:"ghost",size:"icon",className:"h-6 w-6 sm:h-8 sm:w-8 shrink-0 hover:bg-destructive/10 hover:text-destructive",onClick:()=>s(e.id),children:(0,a.jsx)(y.A,{className:"h-3 w-3 sm:h-4 sm:w-4"})})]})}function G({businessName:e,onPostCreated:s}){let[i,o]=(0,r.useState)(!1),[c,u]=(0,r.useState)(""),[h,p]=(0,r.useState)(null),[x,g]=(0,r.useState)(!1),[f,I]=(0,r.useState)(null),[G,O]=(0,r.useState)(e||""),[H,K]=(0,r.useState)(null),[W,Y]=(0,r.useState)(null),[V,X]=(0,r.useState)(null),[J,Q]=(0,r.useState)([]),[Z,ee]=(0,r.useState)(!1),[es,et]=(0,r.useState)(""),[ea,er]=(0,r.useState)(!1),[ei,en]=(0,r.useState)([]),[el,eo]=(0,r.useState)([]),[ed,ec]=(0,r.useState)(!1),eu=(0,r.useRef)(null),em=(0,r.useRef)(null),eh=(0,M.useRouter)(),ep=(0,z.FR)((0,z.MS)(z.AN),(0,z.MS)(z.uN,{coordinateGetter:U.JR})),ex=c.length,eg=ex>2e3,ef=()=>{K(null),W&&URL.revokeObjectURL(W),Y(null),p(null),X(null)},eb=e=>{let s=e.match(/(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^\s]*)?)/i);return s?s[1]:null},ey=e=>{u(e);let s=eb(e);s&&s!==V?(X(s),H||p(s)):!s&&V&&(X(null),H||p(null))};(0,r.useCallback)(async()=>{if(0===J.length)return void eo([]);er(!0);try{let e=await (0,E.E)(J);if(e.success&&e.data){let s=J.map(s=>e.data?.find(e=>e.id===s)).filter(Boolean);eo(s)}else console.error("Error loading selected products:",e.error),eo([])}catch(e){console.error("Error loading selected products:",e),eo([])}finally{er(!1)}},[J]);let ej=async e=>{er(!0),ec(!1);try{let s=await (0,$.H)(e);s.success&&s.data?en(s.data):(console.error("Error searching products:",s.error),en([]))}catch(e){console.error("Error searching products:",e),en([])}finally{er(!1),ec(!0)}},ev=e=>{let s;if(J.includes(e.id))s=J.filter(s=>s!==e.id),eo(s=>s.filter(s=>s.id!==e.id));else{if(J.length>=5)return;s=[...J,e.id],eo(s=>[...s,e])}Q(s)},eN=e=>{let s=J.filter(s=>s!==e);eo(s=>s.filter(s=>s.id!==e)),Q(s)},ew=e=>null===e?"N/A":`₹${e.toLocaleString("en-IN")}`,e_=async()=>{let e=(0,R.U)(),{data:{user:s}}=await e.auth.getUser();if(!s)return F.oR.error("Please log in to continue"),!1;let{data:t,error:a}=await e.from("business_profiles").select("business_name, pincode, city, state, locality").eq("id",s.id).single();return a?(F.oR.error("Failed to check business profile"),!1):!!t?.business_name&&""!==t.business_name.trim()||(F.oR.error("Please complete your business name in your profile"),eh.push("/dashboard/business/profile"),!1)},ek=async()=>{if(!c.trim()&&!W&&!V)return void F.oR.error("Please add some content or an image");if(eg)return void F.oR.error("Post content is too long");g(!0);try{let e;if(!await e_())return void g(!1);if(H&&!h){let{compressImageUltraAggressiveClient:s}=await t.e(8640).then(t.bind(t,28640)),a=await s(H,{maxDimension:1200,targetSizeKB:100}),r=new File([a.blob],H.name,{type:a.blob.type}),i=await (0,q.pD)({content:c.trim(),image_url:null,product_ids:J,mentioned_business_ids:[]});if(i.success&&i.data){let s=i.data;try{let{uploadBusinessPostImage:a}=await t.e(2376).then(t.bind(t,22376)),i=new FormData;i.append("imageFile",r);let n=await a(i,s.id,s.created_at);if(n.success&&n.url)e=await (0,q.gg)(s.id,{content:c.trim(),image_url:n.url,product_ids:J,mentioned_business_ids:[]});else{console.error("Image upload failed:",n.error);try{let{deletePost:e}=await Promise.resolve().then(t.bind(t,23135));await e(s.id),console.log("Rolled back post creation due to image upload failure")}catch(e){console.error("Failed to rollback post creation:",e)}F.oR.error("Failed to upload image. Please try again."),g(!1);return}}catch(e){console.error("Image upload error:",e);try{let{deletePost:e}=await Promise.resolve().then(t.bind(t,23135));await e(s.id),console.log("Rolled back post creation due to image upload error")}catch(e){console.error("Failed to rollback post creation:",e)}F.oR.error("Failed to upload image. Please try again."),g(!1);return}}else e=i}else e=await (0,q.pD)({content:c.trim(),image_url:h,product_ids:J,mentioned_business_ids:[]});e.success?(F.oR.success("Post created successfully!"),u(""),p(null),ef(),o(!1),Q([]),ee(!1),s?.()):F.oR.error(e.error||"Failed to create post")}catch(e){console.error("Error creating post:",e),F.oR.error("Failed to create post")}finally{g(!1)}};return(0,a.jsxs)(n.P.div,{layout:!0,className:"bg-white dark:bg-black overflow-hidden md:border md:border-gray-200 md:dark:border-gray-700 md:rounded-xl md:shadow-sm",children:[(0,a.jsx)(l.N,{mode:"wait",children:!i&&(0,a.jsx)(n.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer",onClick:()=>{o(!0)},children:[(0,a.jsxs)(C.eu,{className:"w-10 h-10 ring-2 ring-[#D4AF37]/30",children:[(0,a.jsx)(C.BK,{src:f||void 0,alt:G}),(0,a.jsx)(C.q5,{className:"bg-[#D4AF37] text-white text-sm font-medium",children:(0,a.jsx)(j.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"flex-1 bg-gray-50 dark:bg-gray-800 rounded-full px-4 py-3 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:["What's happening at ",G,"?"]})]})},"collapsed")}),(0,a.jsx)(l.N,{mode:"wait",children:i&&(0,a.jsxs)(n.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(C.eu,{className:"w-10 h-10 ring-2 ring-[#D4AF37]/30",children:[(0,a.jsx)(C.BK,{src:f||void 0,alt:G}),(0,a.jsx)(C.q5,{className:"bg-[#D4AF37] text-white text-sm font-medium",children:(0,a.jsx)(j.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100 text-sm",children:G}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Business post"})]})]}),(0,a.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>{c.trim()||h||(o(!1),Q([]),ee(!1))},className:"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsx)(B.A,{})}),(0,a.jsx)("textarea",{ref:eu,value:c,onChange:e=>ey(e.target.value),placeholder:`What's happening at ${G}?`,className:"w-full resize-none border-none outline-none text-lg placeholder-gray-500 dark:placeholder-gray-400 bg-transparent text-gray-900 dark:text-gray-100 min-h-[80px]",style:{maxHeight:"300px"}}),ex>0&&(0,a.jsxs)("div",{className:`text-xs mt-2 text-right ${eg?"text-red-500":"text-gray-400"}`,children:[ex,"/",2e3]})]}),(0,a.jsx)(l.N,{children:(W||V)&&(0,a.jsx)(n.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"px-4 pb-4",children:(0,a.jsxs)("div",{className:"relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700",children:[(0,a.jsx)(L.default,{src:W||V||"",alt:"Post image preview",width:500,height:300,className:"w-full h-auto max-h-96 object-cover",onError:ef}),(0,a.jsx)("button",{onClick:ef,className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1.5 hover:bg-opacity-70 transition-all",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),V&&!W&&(0,a.jsx)("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded",children:"Auto-detected image"})]})})}),(0,a.jsx)(l.N,{children:Z&&(0,a.jsx)(n.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"px-4 pb-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(S.AM,{open:es.length>=2,onOpenChange:e=>{e||(et(""),en([]),ec(!1))},children:[(0,a.jsx)(S.Wv,{asChild:!0,children:(0,a.jsxs)(m.$,{variant:"outline",role:"combobox",className:"w-full justify-between h-auto min-h-[40px] px-3 py-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-left text-muted-foreground",children:"Search and add products..."})]}),(0,a.jsx)(N.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,a.jsx)(S.hl,{className:"p-0",align:"start",sideOffset:4,style:{width:"var(--radix-popover-trigger-width)"},children:(0,a.jsxs)(A.uB,{children:[(0,a.jsx)(A.G7,{placeholder:"Search your products...",value:es,onValueChange:e=>{if(et(e),em.current&&clearTimeout(em.current),e.length<2){en([]),ec(!1);return}em.current=setTimeout(()=>{ej(e)},300)},className:"h-9 border-0 focus:ring-0 focus:ring-offset-0"}),(0,a.jsxs)(A.oI,{className:"max-h-[300px]",children:[ea&&(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-6 w-6 animate-spin text-[#D4AF37]"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Searching products..."})]})}),!ea&&0===ei.length&&(0,a.jsx)(A.xL,{children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,a.jsx)(b.A,{className:"h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:es.length<2?"Type at least 2 characters to search":ed?"No products found":""}),es.length>=2&&ed&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground mt-1",children:"Try a different search term"})]})}),!ea&&ei.length>0&&(0,a.jsx)(A.L$,{children:ei.map(e=>(0,a.jsxs)(A.h_,{value:e.slug||e.id,onSelect:()=>{(J.length<5||J.includes(e.id))&&(ev(e),et(""),en([]),ec(!1))},disabled:J.length>=5&&!J.includes(e.id),className:(0,D.cn)("flex items-center gap-3 p-3 cursor-pointer",J.length>=5&&!J.includes(e.id)?"opacity-50 cursor-not-allowed":""),children:[(0,a.jsx)("div",{className:"relative h-10 w-10 shrink-0 rounded-md overflow-hidden bg-muted",children:e.image_url?(0,a.jsx)(L.default,{src:e.image_url,alt:e.name,fill:!0,className:"object-cover",sizes:"40px"}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,a.jsx)(b.A,{className:"h-5 w-5 text-muted-foreground"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0 pr-2",children:[(0,a.jsx)("div",{className:"font-medium text-sm truncate mb-1",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.discounted_price?(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-wrap",children:[(0,a.jsx)("span",{className:"text-[#D4AF37] font-medium",children:ew(e.discounted_price)}),(0,a.jsx)("span",{className:"line-through text-xs",children:ew(e.base_price)})]}):(0,a.jsx)("span",{className:"font-medium",children:ew(e.base_price)})})]}),(0,a.jsx)(w.A,{className:(0,D.cn)("ml-auto h-4 w-4",J.includes(e.id)?"opacity-100 text-[#D4AF37]":"opacity-0")})]},e.id))})]})]})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[ea&&J.length>0&&0===el.length&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Loading Selected Products..."})]}),(0,a.jsx)("div",{className:"flex justify-center py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-6 w-6 animate-spin text-[#D4AF37]"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Fetching product details..."})]})})]}),el.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Selected Products"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground font-normal",children:"(Drag to reorder)"})]}),(0,a.jsx)(z.Mp,{sensors:ep,collisionDetection:z.fp,onDragEnd:e=>{let{active:s,over:t}=e;if(s.id!==t?.id){let e=el.findIndex(e=>e.id===s.id),a=el.findIndex(e=>e.id===t?.id);if(-1!==e&&-1!==a){let s=(0,U.be)(el,e,a);eo(s),Q(s.map(e=>e.id))}}},children:(0,a.jsx)(U.gB,{items:el.map(e=>e.id),strategy:U._G,children:(0,a.jsx)("div",{className:"grid gap-2",children:el.map(e=>(0,a.jsx)(T,{product:e,onRemove:eN,formatPrice:ew},e.id))})})})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:0===J.length?"No products selected":`${J.length} product${1!==J.length?"s":""} selected`}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsxs)("span",{className:(0,D.cn)("font-medium",J.length>=5?"text-destructive":"text-muted-foreground"),children:[J.length,"/5"]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"max"})]})]}),J.length>=5&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded-md",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 shrink-0"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Maximum limit of 5 products reached"})]})]})]})})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];return s?s.type.startsWith("image/")?s.size>5242880?void F.oR.error("Image size must be less than 5MB"):void(K(s),Y(URL.createObjectURL(s)),p(null)):void F.oR.error("Please select an image file"):void 0},className:"hidden",id:"business-image-upload"}),(0,a.jsx)("label",{htmlFor:"business-image-upload",children:(0,a.jsx)(m.$,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-[#D4AF37] dark:text-gray-400 dark:hover:text-[#D4AF37] h-9 px-3",asChild:!0,children:(0,a.jsxs)("span",{children:[(0,a.jsx)(_.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Photo"})]})})}),(0,a.jsxs)(m.$,{variant:"ghost",size:"sm",onClick:()=>{ee(!Z)},className:"text-gray-500 hover:text-purple-500 dark:text-gray-400 dark:hover:text-purple-400 h-9 px-3",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Products"})]}),(0,a.jsxs)(m.$,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 h-9 px-3",disabled:!0,children:[(0,a.jsx)(k.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Location"})]})]}),(0,a.jsx)(m.$,{onClick:ek,disabled:x||!c.trim()&&!W&&!V||eg,className:"bg-[#D4AF37] hover:bg-[#B8941F] text-white px-6 py-2 rounded-full font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Posting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Post"]})})]})]},"expanded")})]})}function O({initialPosts:e,initialTotalCount:s,initialHasMore:t,initialFilter:f="smart",citySlug:b,stateSlug:y,localitySlug:j,pincode:v,businessName:N="Business Owner"}){let[w,_]=(0,r.useState)(e),[k,P]=(0,r.useState)(s),[C,A]=(0,r.useState)(t),[S,F]=(0,r.useState)(1),[R,q]=(0,r.useState)(!1),[M,L]=(0,r.useState)(f),{ref:B,inView:D}=(0,i.Wx)(),E=(0,r.useCallback)(async()=>{if(C&&!R){q(!0);try{let e=S+1,s=await (0,c._)({filter:M,page:e,city_slug:b,state_slug:y,locality_slug:j,pincode:v});s.success&&s.data?.items&&(_(e=>{let t=new Set(e.map(e=>e.id)),a=s.data.items.filter(e=>!t.has(e.id));return[...e,...a]}),A(s.data.hasMore||!1),P(s.data.totalCount||0),F(e))}catch(e){console.error("Error loading more posts:",e)}finally{q(!1)}}},[C,R,S,M,b,y,j,v]),$=async e=>{if(e!==M){q(!0),L(e),_([]);try{let s=await (0,c._)({filter:e,page:1,city_slug:b,state_slug:y,locality_slug:j,pincode:v});s.success&&s.data&&(_(s.data.items),A(s.data.hasMore),P(s.data.totalCount),F(1))}catch(e){console.error("Error changing filter:",e)}finally{q(!1)}}},z=async()=>{try{let e=await (0,c._)({filter:M,page:1,city_slug:b,state_slug:y,locality_slug:j,pincode:v});e.success&&e.data?.items&&(_(e.data.items),A(e.data.hasMore||!1),P(e.data.totalCount||0),F(1))}catch(e){console.error("Error refreshing feed:",e)}},U=(e,s)=>{_(t=>t.map(t=>t.id===e?{...t,content:s}:t))},I=e=>{_(s=>s.filter(s=>s.id!==e)),P(e=>Math.max(0,e-1))},T=(e,s)=>{_(t=>t.map(t=>t.id===e?{...t,product_ids:s}:t))};return(0,a.jsxs)(g.A,{children:[(0,a.jsx)(x.A,{activeFilter:M,onFilterChange:$,isLoading:R}),(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:(0,a.jsx)(G,{businessName:N,onPostCreated:z})}),(0,a.jsx)(l.N,{mode:"wait",children:0!==w.length||R?(0,a.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"space-y-0",children:[R&&0===w.length&&(0,a.jsx)(a.Fragment,{children:Array.from({length:10}).map((e,s)=>(0,a.jsx)(p.default,{index:s,showImage:Math.random()>.3,showProducts:Math.random()>.7},`skeleton-${s}`))}),w.map((e,s)=>(0,a.jsx)(h.A,{post:e,index:s,onPostUpdate:U,onPostDelete:I,onProductsUpdate:T},e.id)),C&&(0,a.jsx)("div",{ref:B,className:"flex justify-center items-center py-8",children:R&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-neutral-500",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 animate-spin"}),(0,a.jsx)("span",{className:"text-sm",children:"Loading more posts..."})]})}),C&&!R&&(0,a.jsx)("div",{className:"flex justify-center mt-8 mb-4",children:(0,a.jsx)(m.$,{variant:"outline",onClick:E,disabled:R,className:"bg-white dark:bg-black border-neutral-200 dark:border-neutral-700",children:"Load More Posts"})})]},"posts-list"):(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:(0,a.jsxs)(u.Fc,{className:"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)(u.XL,{children:"No posts found"}),(0,a.jsx)(u.TN,{children:(()=>{switch(M){case"smart":return"No posts available in your smart feed. Try subscribing to businesses or check other filters.";case"subscribed":return"Subscribe to businesses to see their posts here.";case"locality":return"No posts from your locality yet.";case"pincode":return"No posts from your pincode yet.";case"city":return"No posts from your city yet.";case"state":return"No posts from your state yet.";default:return"No posts available at the moment."}})()})]})},"empty-state")})]})]})}},71808:(e,s,t)=>{Promise.resolve().then(t.bind(t,65063))},73375:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>o});var a=t(37413),r=t(32032),i=t(39916),n=t(82921),l=t(14109);let o={title:"Feed",description:"View your business feed and stay updated"};async function d(){let e=await (0,r.createClient)(),{data:{user:s},error:t}=await e.auth.getUser();(t||!s)&&(0,i.redirect)("/login?message=Please log in to view your dashboard");let{data:o,error:d}=await e.from("business_profiles").select("city_slug, state_slug, locality_slug, pincode, business_name").eq("id",s.id).single(),c=await (0,l._)({filter:"smart",page:1,limit:10,city_slug:o?.city_slug||void 0,state_slug:o?.state_slug||void 0,locality_slug:o?.locality_slug||void 0,pincode:o?.pincode||void 0}),u=c.success&&c.data?.items||[],m=!!c.success&&(c.data?.hasMore||!1);return c.success||console.error("Error fetching initial posts:",c.error),(0,a.jsx)(n.default,{initialPosts:u,initialTotalCount:0,initialHasMore:m,initialFilter:"smart",citySlug:o?.city_slug||void 0,stateSlug:o?.state_slug||void 0,localitySlug:o?.locality_slug||void 0,pincode:o?.pincode||void 0,businessName:o?.business_name||"Business Owner"})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79988:(e,s,t)=>{Promise.resolve().then(t.bind(t,61192))},81630:e=>{"use strict";e.exports=require("http")},82921:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\components\\\\feed\\\\ModernBusinessFeedList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},93595:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73375)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/page",pathname:"/dashboard/business",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,1753,6380,399,2836,4212,1606,3701,3037,3739,9538,5265,4308,3496],()=>t(93595));module.exports=a})();