(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1434],{17580:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},26528:(e,t,s)=>{"use strict";s.d(t,{default:()=>g});var r=s(95155);s(12115);var o=s(32919),i=s(34869),a=s(53904),n=s(81586),c=s(37108),u=s(17580),d=s(85339),l=s(71199);let h=[{id:1,question:"I can't log in to my account. What should I do?",answer:"First, ensure you're using the correct email address and password. Check that you're connected to the internet and try clearing your browser cache. If problems persist, contact our support team."},{id:2,question:"My images aren't uploading properly. How can I fix this?",answer:"Ensure your images meet our requirements: JPG, PNG, or WebP format, and under 15MB in size. For logos, use square images (1:1 ratio) for best results. If you're still having issues, try using a different browser or device, or compress the image before uploading."},{id:3,question:"My digital card isn't displaying correctly on mobile devices. What's wrong?",answer:"Dukancard is designed to be fully responsive across all devices. Try refreshing the page or clearing your browser cache. If the issue persists, check if you're using an outdated browser version. For specific display issues, take a screenshot and contact our support team."},{id:4,question:"I'm seeing error messages when trying to save my card information. What should I do?",answer:"Error messages typically indicate specific issues that need to be addressed. Read the error message carefully for guidance. Common issues include required fields left empty or invalid format for phone numbers or URLs. If you're still stuck, contact our support team with details of the error message."},{id:5,question:"The QR code for my card isn't working when scanned. How can I fix this?",answer:"Ensure you've downloaded the QR code at a high resolution and it's not distorted when printed. When scanning, make sure there's adequate lighting and the camera is focused on the QR code. If the issue persists, try regenerating the QR code from Business Management > Manage Card in your dashboard."},{id:6,question:"My changes to my digital card aren't showing up on the public view. Why?",answer:"Changes should appear immediately, but sometimes there might be a slight delay due to caching. Try refreshing the page using Ctrl+F5 (Windows) or Cmd+Shift+R (Mac). If changes still don't appear after a few minutes, log out and back in, then check again."},{id:7,question:"I'm having trouble with the verification email. What should I do?",answer:"Check your spam or junk folder for the verification email. If you don't see it, you can request a new verification email from the login page. Ensure you're checking the correct email account and that your email address was entered correctly during registration."},{id:8,question:"The website is loading slowly for me. How can I improve this?",answer:"Slow loading can be caused by various factors. Try clearing your browser cache, closing unnecessary tabs or applications, and ensuring you have a stable internet connection. If the issue persists across different devices and networks, please report it to our support team."},{id:9,question:"I accidentally deleted a product. Can I recover it?",answer:"Currently, there's no self-service option to recover deleted products. We recommend creating a new product entry with the same information in the Products & Services section. In the future, always double-check before confirming deletion. For urgent recovery needs, contact our support team as soon as possible."},{id:10,question:"My analytics data isn't updating. What's happening?",answer:"Analytics data typically updates within a few minutes but may take longer during high traffic periods. If you notice no updates after 24 hours, try clearing your browser cache and refreshing the page. If the issue persists, contact our support team with details of the problem."}],p=[{id:"account-login",title:"Account & Login Issues",icon:(0,r.jsx)(o.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Login Problems",steps:["Double-check that you're using the correct email address you registered with.","Ensure caps lock is off and you're entering the correct password.","Try clearing your browser cache and cookies, then attempt to log in again.","If your account is locked after multiple failed attempts, wait 30 minutes before trying again."],image:"/support/login-issues.jpg",imageAlt:"Login troubleshooting",tip:"Check your spam folder if you don't see the password reset email within a few minutes."},{title:"Registration & Verification Issues",steps:["Check your spam or junk folder for the verification email.","If you don't see it after 15 minutes, return to the login page and click 'Resend Verification Email'.","Ensure you entered your email address correctly during registration.","If verification links don't work, try copying and pasting the link directly into your browser address bar.","For social login issues, ensure you're allowing pop-ups from our site and that you're logged into your social account."],image:"/support/registration-issues.jpg",imageAlt:"Registration troubleshooting",tip:"Verification links expire after 24 hours. Request a new verification email if your link has expired."}]},{id:"content-display",title:"Content & Display Issues",icon:(0,r.jsx)(i.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Image Upload Problems",steps:["Ensure your images are in JPG, PNG, or WebP format and under 15MB in size.","Check your internet connection stability before uploading.","For logos, use square images (1:1 ratio) for best results.","For product images, ensure they have good resolution (at least 800x800 pixels recommended).","If upload seems stuck, refresh the page and try again with a smaller or compressed version."],image:"/support/image-upload-issues.jpg",imageAlt:"Image upload troubleshooting",tip:"If images are consistently disappearing, contact support with details of the issue."},{title:"Card Display Issues",steps:["For mobile display issues, try viewing in portrait mode and refreshing the page.","Clear your browser cache using Ctrl+F5 (Windows) or Cmd+Shift+R (Mac).","Ensure your mobile browser is up to date.","Avoid using special characters or excessive formatting in text fields.","For QR code issues, ensure you've downloaded it at high resolution and it's not distorted when printed."],image:"/support/display-issues.jpg",imageAlt:"Display troubleshooting",tip:"Changes to your card should appear immediately, but may take a few minutes due to caching. Log out and back in if changes don't appear."}]},{id:"performance-errors",title:"Performance & Error Messages",icon:(0,r.jsx)(a.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Website Performance Issues",steps:["Clear your browser cache and cookies to improve loading times.","Close unnecessary tabs or applications that might be using system resources.","Ensure you have a stable internet connection.","Try using a different browser or device to see if the issue persists.","Check your browser settings to ensure JavaScript is enabled."],image:"/support/performance-issues.jpg",imageAlt:"Performance troubleshooting",tip:"Disable any ad blockers or browser extensions that might interfere with the site's functionality."},{title:"Error Messages & Codes",steps:["For 404 errors, check the URL for typos and report broken links to our support team.","For 500 errors, try refreshing the page or coming back later.","Read form validation error messages carefully as they indicate what needs to be fixed.","For payment errors, check that your card details are correct and not expired.","Contact our support team if errors persist for more than an hour."],image:"/support/error-messages.jpg",imageAlt:"Error message troubleshooting",tip:"Common form issues include required fields left empty, invalid email formats, or password requirements not met."}]}];function g(){let e=[{title:"Business Card Setup",description:"Learn how to create and customize your digital business card",icon:(0,r.jsx)(n.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/business-card-setup"},{title:"Product Management",description:"Learn how to add and manage products in your digital storefront",icon:(0,r.jsx)(c.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/product-management"},{title:"Account & Billing",description:"Manage your account, subscription, and billing information",icon:(0,r.jsx)(u.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/account-billing"}];return(0,r.jsx)(l.A,{title:"Technical Issues & Troubleshooting",description:"Find solutions to common technical problems and learn how to troubleshoot issues with your digital business card.",icon:(0,r.jsx)(d.A,{className:"w-12 h-12 text-[var(--brand-gold)]"}),quickHelp:[{title:"Common Login Issues:",items:[{text:"Clear browser cache and cookies"},{text:"Check spam folder for verification emails"}]},{title:"Display & Image Issues:",items:[{text:"Use Ctrl+F5 (Windows) or Cmd+Shift+R (Mac) to refresh"},{text:"Ensure images are under 15MB in JPG, PNG, or WebP format"},{text:"Try using a different browser if issues persist"}]}],guideSections:p,faqs:h,relatedResources:e,navigationButtons:[{label:"Account & Login",href:"#account-login"},{label:"Content & Display",href:"#content-display"},{label:"Performance & Errors",href:"#performance-errors"},{label:"FAQs",href:"#faqs"}]})}},32919:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},34869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},37108:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},53904:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94271:(e,t,s)=>{Promise.resolve().then(s.bind(s,26528))}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,1893,3580,8441,1684,7358],()=>t(94271)),_N_E=e.O()}]);