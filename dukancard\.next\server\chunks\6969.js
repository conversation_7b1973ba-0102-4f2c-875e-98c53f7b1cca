"use strict";exports.id=6969,exports.ids=[6969],exports.modules={1759:(e,t,r)=>{r.d(t,{Fc:()=>o,TN:()=>S,XL:()=>u});var s=r(37413);r(61120);var i=r(75986);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,c=i.$;var l=r(66819);let n=((e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return c(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,n=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let c=a(t)||a(s);return i[e][c]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return c(e,n,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...o}[t]):({...l,...o})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(n({variant:t}),e),...r})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,l.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function S({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},7486:(e,t,r)=>{r.r(t),r.d(t,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>s.B,"4034994114ec4af82b8beb7b7ae0ca9d7cc384e91d":()=>i.J3,"406d3eedcd0777bc7a4d097a7c3736c79aa39001bd":()=>i.WK,"406e2a2a96626c0d97e44e258eff56ae8b5143ec6b":()=>i.e8,"408a3af28cdbf5a633d1470e3aa9845d5dfdf000ea":()=>i.Q$,"40b79faef168e2af792fe9df79d32b29f3f0fe259d":()=>i.Wr,"40bb17f02c4644681936fdd90a24f199b1d28e2381":()=>i.PU,"70696c60ec3cbc3cc67d74b3aabf05284aceb2c9d3":()=>i.h6});var s=r(64275),i=r(32293)},22637:(e,t,r)=>{r.d(t,{Qc:()=>c,Ws:()=>a});var s=r(32032),i=r(30468);let a={async fetchSubscriptions(e,t=1,r=20,a=""){try{let c=await (0,s.createClient)(),l=c.from(i.CG.SUBSCRIPTIONS).select(`
          ${i.cZ.ID},
          ${i.CG.BUSINESS_PROFILES}!inner (
            ${i.cZ.ID},
            ${i.cZ.BUSINESS_NAME}
          )
        `,{count:"exact",head:!0}).eq(i.cZ.USER_ID,e);a&&a.trim()&&(l=l.ilike(`${i.CG.BUSINESS_PROFILES}.${i.cZ.BUSINESS_NAME}`,`%${a.trim()}%`));let{count:n,error:o}=await l;if(o)throw Error(`Failed to get subscriptions count: ${o.message}`);if(!n||0===n)return{items:[],totalCount:0,hasMore:!1,currentPage:t};let u=c.from(i.CG.SUBSCRIPTIONS).select(`
          ${i.cZ.ID},
          ${i.cZ.BUSINESS_PROFILE_ID},
          ${i.CG.BUSINESS_PROFILES}!inner (*)
        `).eq(i.cZ.USER_ID,e);a&&a.trim()&&(u=u.ilike(`${i.CG.BUSINESS_PROFILES}.${i.cZ.BUSINESS_NAME}`,`%${a.trim()}%`));let S=(t-1)*r;console.log(`[subscriptionsService.fetchSubscriptions] Applying pagination: offset=${S}, limit=${r}`),u=u.range(S,S+r-1);let{data:d,error:_}=await u;if(console.log(`[subscriptionsService.fetchSubscriptions] Query returned ${d?.length} items`),_)throw Error(`Failed to fetch subscriptions: ${_.message}`);let E=(d||[]).map(e=>({id:e.id,business_profiles:Array.isArray(e.business_profiles)?e.business_profiles[0]:e.business_profiles})),I=n>S+r;return{items:E,totalCount:n,hasMore:I,currentPage:t}}catch(e){throw console.error("Error in fetchSubscriptions:",e),e}},async unsubscribe(e){try{let t=await (0,s.createClient)(),{error:r}=await t.from(i.CG.SUBSCRIPTIONS).delete().eq(i.cZ.ID,e);if(r)throw Error(`Failed to unsubscribe: ${r.message}`)}catch(e){throw console.error("Error in unsubscribe:",e),e}},async fetchBusinessFollowers(e,t=1,r=10){try{let a=await (0,s.createClient)(),c=(t-1)*r,{count:l,error:n}=await a.from(i.CG.SUBSCRIPTIONS).select("*",{count:"exact",head:!0}).eq(i.cZ.BUSINESS_PROFILE_ID,e);if(n)throw Error("Failed to fetch subscription count");if(!l||0===l)return{items:[],totalCount:0,hasMore:!1,currentPage:t};let{data:o,error:u}=await a.from(i.CG.SUBSCRIPTIONS).select(`${i.cZ.ID}, ${i.cZ.USER_ID}, ${i.cZ.CREATED_AT}`).eq(i.cZ.BUSINESS_PROFILE_ID,e).order(i.cZ.CREATED_AT,{ascending:!1}).range(c,c+r-1);if(u)throw Error("Failed to fetch subscriptions");if(!o||0===o.length)return{items:[],totalCount:l||0,hasMore:!1,currentPage:t};let S=o.map(e=>e.user_id),[d,_]=await Promise.all([a.from(i.CG.CUSTOMER_PROFILES).select(`${i.cZ.ID}, ${i.cZ.NAME}, ${i.cZ.AVATAR_URL}`).in(i.cZ.ID,S),a.from(i.CG.BUSINESS_PROFILES).select(`${i.cZ.ID}, ${i.cZ.BUSINESS_NAME}, ${i.cZ.BUSINESS_SLUG}, ${i.cZ.LOGO_URL}, ${i.cZ.CITY}, ${i.cZ.STATE}, ${i.cZ.PINCODE}, ${i.cZ.ADDRESS_LINE}`).in(i.cZ.ID,S)]);if(d.error)throw Error("Failed to fetch customer profiles");if(_.error)throw Error("Failed to fetch business profiles");let E=new Map(d.data?.map(e=>[e.id,e])||[]),I=new Map(_.data?.map(e=>[e.id,e])||[]),f=o.map(e=>{let t=E.get(e.user_id),r=I.get(e.user_id);return t?{id:e.id,profile:{id:t.id,name:t.name,slug:null,avatar_url:t.avatar_url,city:null,state:null,pincode:null,address_line:null,type:"customer"}}:r?{id:e.id,profile:{id:r.id,name:r.business_name,slug:r.business_slug,logo_url:r.logo_url,city:r.city,state:r.state,pincode:r.pincode,address_line:r.address_line,type:"business"}}:null}).filter(e=>null!==e),m=l>c+r;return{items:f,totalCount:l||0,hasMore:m,currentPage:t}}catch(e){throw e}}},c={async fetchLikes(e,t=1,r=20,a=""){try{let c=await (0,s.createClient)(),l=c.from(i.CG.LIKES).select(`
          ${i.cZ.ID},
          ${i.CG.BUSINESS_PROFILES}!inner (*)
        `).eq(i.cZ.USER_ID,e);a&&a.trim()&&(l=l.ilike(`${i.CG.BUSINESS_PROFILES}.${i.cZ.BUSINESS_NAME}`,`%${a.trim()}%`));let n=c.from(i.CG.LIKES).select(`
          ${i.cZ.ID},
          ${i.CG.BUSINESS_PROFILES}!inner (
            ${i.cZ.ID},
            ${i.cZ.BUSINESS_NAME}
          )
        `,{count:"exact",head:!0}).eq(i.cZ.USER_ID,e);a&&a.trim()&&(n=n.ilike(`${i.CG.BUSINESS_PROFILES}.${i.cZ.BUSINESS_NAME}`,`%${a.trim()}%`));let{count:o,error:u}=await n;if(u)throw Error(`Failed to get likes count: ${u.message}`);if(!o||0===o)return{items:[],totalCount:0,hasMore:!1,currentPage:t};let S=(t-1)*r;console.log(`[likesService.fetchLikes] Applying pagination: offset=${S}, limit=${r}`),l=l.range(S,S+r-1);let{data:d,error:_}=await l;if(console.log(`[likesService.fetchLikes] Query returned ${d?.length} items`),_)throw Error(`Failed to fetch likes: ${_.message}`);let E=(d||[]).map(e=>({id:e.id,business_profiles:Array.isArray(e.business_profiles)?e.business_profiles[0]:e.business_profiles})),I=o>S+r;return{items:E,totalCount:o,hasMore:I,currentPage:t}}catch(e){throw console.error("Error in fetchLikes:",e),e}},async unlike(e){try{let t=await (0,s.createClient)(),{error:r}=await t.from(i.CG.LIKES).delete().eq(i.cZ.ID,e);if(r)throw Error(`Failed to unlike: ${r.message}`)}catch(e){throw console.error("Error in unlike:",e),e}},async fetchBusinessLikesReceived(e,t=1,r=10){try{let a=await (0,s.createClient)(),{count:c,error:l}=await a.from(i.CG.LIKES).select(i.cZ.ID,{count:"exact",head:!0}).eq(i.cZ.BUSINESS_PROFILE_ID,e);if(l)throw Error("Failed to get total count");if(!c||0===c)return{items:[],totalCount:0,hasMore:!1,currentPage:t};let n=(t-1)*r;console.log(`[likesService.fetchBusinessLikesReceived] Applying pagination: from=${n}, limit=${r}`);let{data:o,error:u}=await a.from(i.CG.LIKES).select(`${i.cZ.ID}, ${i.cZ.USER_ID}`).eq(i.cZ.BUSINESS_PROFILE_ID,e).range(n,n+r-1);if(console.log(`[likesService.fetchBusinessLikesReceived] Query returned ${o?.length} items`),u)throw Error("Failed to fetch likes");if(!o||0===o.length)return{items:[],totalCount:c,hasMore:!1,currentPage:t};let S=o.map(e=>e.user_id),[d,_]=await Promise.all([a.from(i.CG.CUSTOMER_PROFILES).select(`${i.cZ.ID}, ${i.cZ.NAME}, ${i.cZ.EMAIL}, ${i.cZ.AVATAR_URL}`).in(i.cZ.ID,S),a.from(i.CG.BUSINESS_PROFILES).select(`${i.cZ.ID}, ${i.cZ.BUSINESS_NAME}, ${i.cZ.BUSINESS_SLUG}, ${i.cZ.LOGO_URL}, ${i.cZ.CITY}, ${i.cZ.STATE}, ${i.cZ.PINCODE}, ${i.cZ.ADDRESS_LINE}, ${i.cZ.LOCALITY}`).in(i.cZ.ID,S)]),E=new Map(d.data?.map(e=>[e.id,e])||[]),I=new Map(_.data?.map(e=>[e.id,e])||[]),f=o.map(e=>{let t=E.get(e.user_id),r=I.get(e.user_id);return t?{id:e.id,user_id:e.user_id,customer_profiles:t,profile_type:"customer"}:r?{id:e.id,user_id:e.user_id,business_profiles:r,profile_type:"business"}:null}).filter(e=>null!==e),m=c>n+r;return{items:f,totalCount:c,hasMore:m,currentPage:t}}catch(e){throw e}}}},26373:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(61120);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:l="",children:n,iconNode:o,...u},S)=>(0,s.createElement)("svg",{ref:S,...c,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:a("lucide",l),...u},[...o.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),n=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...c},n)=>(0,s.createElement)(l,{ref:n,iconNode:t,className:a(`lucide-${i(e)}`,r),...c}));return r.displayName=`${e}`,r}},26919:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(26373).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},30468:(e,t,r)=>{r.d(t,{CG:()=>s,SC:()=>i,cZ:()=>a});let s={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},i={BUSINESS:"business",CUSTOMERS:"customers"},a={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},54781:(e,t,r)=>{r.d(t,{E:()=>a});var s=r(37413),i=r(66819);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,i.cn)("bg-accent animate-pulse rounded-md",e),...t})}},56748:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("Compass",[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},66819:(e,t,r)=>{r.d(t,{cn:()=>a,gV:()=>c});var s=r(75986),i=r(8974);function a(...e){return(0,i.QP)((0,s.$)(e))}function c(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}}};