exports.id=3226,exports.ids=[3226],exports.modules={5903:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var s=t(60687),a=t(43210),i=t(30474),n=t(85814),l=t.n(n),o=t(71463),c=t(77882),u=t(88920),d=t(96241);let _=e=>null==e?null:e.toLocaleString("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}),p={hidden:{opacity:0,y:10},show:{opacity:1,y:0}},b={initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1,transition:{duration:.5,type:"spring",stiffness:400,damping:10}},hover:{scale:1.05,rotate:-2,transition:{type:"spring",stiffness:500}}};function m({product:e,isLink:r=!0}){let[t,n]=(0,a.useState)(!1),m=_(e.base_price),g=_(e.discounted_price),f=m,h=null,x=0,y="number"==typeof e.discounted_price&&e.discounted_price>0,v="number"==typeof e.base_price&&e.base_price>0;y&&v&&e.discounted_price<e.base_price?(f=g,h=m,x=Math.round((e.base_price-e.discounted_price)/e.base_price*100)):(f=m,h=null,x=0),f||(f="Price unavailable");let w=x>0,[A,j]=(0,a.useState)(!1),k=!e.is_available,N=(0,s.jsx)(c.P.div,{variants:p,initial:"hidden",animate:"show",className:"w-full overflow-hidden",children:(0,s.jsx)("div",{className:"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg",children:(0,s.jsxs)("div",{className:"relative w-full overflow-hidden rounded-lg",children:[(0,s.jsxs)("div",{className:"relative w-full overflow-hidden rounded-t-xl",children:[(()=>{let r=e.image_url;if(e.images&&Array.isArray(e.images)&&e.images.length>0){let t="number"==typeof e.featured_image_index?Math.min(e.featured_image_index,e.images.length-1):0;r=e.images[t]}return r&&!t?(0,s.jsxs)("div",{className:"overflow-hidden",children:[!A&&(0,s.jsx)(o.E,{className:"absolute inset-0 rounded-t-xl"}),(0,s.jsx)(c.P.div,{className:"w-full",children:(0,s.jsx)(i.default,{src:r,alt:e.name??"Product image",width:500,height:750,className:`w-full aspect-square object-cover ${k?"filter grayscale opacity-70 transition-all duration-500":""} ${A?"opacity-100":"opacity-0"} max-w-full`,loading:"lazy",onError:()=>n(!0),onLoad:()=>j(!0),quality:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",placeholder:"blur",style:{objectFit:"cover"}})})]}):(0,s.jsx)("div",{className:"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl",children:(0,s.jsx)("svg",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})(),k&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40",children:(0,s.jsx)("div",{className:"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground",children:(0,s.jsx)("span",{className:"font-medium tracking-wide uppercase text-xs sm:text-sm",children:"Out of Stock"})})}),w&&(0,s.jsx)(u.N,{children:(0,s.jsx)(c.P.div,{variants:b,initial:"initial",animate:"animate",whileHover:"hover",className:(0,d.cn)("absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg","bg-destructive","text-destructive-foreground border border-destructive-foreground/20","transform-gpu"),children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("span",{className:"text-[7px] sm:text-[9px] md:text-[10px] font-medium",children:"SAVE"}),(0,s.jsxs)("span",{className:"text-[9px] sm:text-xs md:text-sm leading-none",children:[x,"%"]})]})},`discount-badge-${e.id}`)})]}),(0,s.jsxs)("div",{className:"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1",children:[(0,s.jsx)("p",{className:"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden",children:e.name??"Unnamed Product"}),e.description&&(0,s.jsx)("p",{className:"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate",children:e.description}),(0,s.jsx)("div",{className:"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1",children:(0,s.jsxs)("div",{className:"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full",children:[f&&(0,s.jsx)("p",{className:"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full",children:f}),h&&(0,s.jsx)("p",{className:"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500",children:h})]})})]})]})})});return r&&"business_slug"in e&&e.business_slug?(0,s.jsx)(l(),{href:`/${e.business_slug}/product/${e.slug||e.id}`,className:"block h-full",children:N}):N}},9904:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(60687),a=t(77882),i=t(71463);function n(){return(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4",children:Array.from({length:6}).map((e,r)=>(0,s.jsx)(l,{index:r},r))})}function l({index:e}){return(0,s.jsxs)(a.P.div,{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm p-2 sm:p-3 md:p-4 h-full flex flex-col w-full",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*e},children:[(0,s.jsxs)("div",{className:"flex flex-col items-center text-center mb-4",children:[(0,s.jsx)(i.E,{className:"h-12 w-12 sm:h-16 sm:w-16 rounded-full flex-shrink-0 mb-2"}),(0,s.jsx)("div",{className:"space-y-2 w-full px-2",children:(0,s.jsx)(i.E,{className:"h-5 w-3/4 mx-auto"})})]}),(0,s.jsxs)("div",{className:"space-y-2 mb-3 flex-grow",children:[(0,s.jsxs)("div",{className:"flex gap-1.5 sm:gap-2 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg p-2 sm:p-2.5 border border-neutral-100 dark:border-neutral-800",children:[(0,s.jsx)(i.E,{className:"h-4 w-4 rounded-full flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{className:"flex-1 space-y-1.5",children:[(0,s.jsx)(i.E,{className:"h-3 w-full"}),(0,s.jsx)(i.E,{className:"h-3 w-4/5"}),(0,s.jsx)(i.E,{className:"h-3 w-3/5"})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-center mt-2",children:(0,s.jsx)(i.E,{className:"h-6 w-24 rounded-full"})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t border-neutral-100 dark:border-neutral-800 mt-auto",children:[(0,s.jsx)(i.E,{className:"h-4 w-12"}),(0,s.jsx)(i.E,{className:"h-4 w-12"}),(0,s.jsx)(i.E,{className:"h-4 w-12"})]})]})}},10747:(e,r,t)=>{"use strict";function s(e,r=!1){if(r)switch(e){case"name_asc":case"name_desc":return"name";case"price_asc":case"price_desc":return"price";default:return"created_at"}switch(e){case"name_asc":case"name_desc":return"name";case"price_asc":case"price_desc":return"base_price";case"likes_desc":return"likes_count";case"subscriptions_desc":return"subscriptions_count";case"rating_desc":return"average_rating";default:return"created_at"}}function a(e){switch(e){case"name_asc":case"price_asc":case"created_asc":return!0;default:return!1}}t.d(r,{o:()=>a,z:()=>s})},18265:(e,r,t)=>{"use strict";t.d(r,{W:()=>n});var s=t(43210),a=t(99292);let i={some:0,all:1};function n(e,{root:r,margin:t,amount:l,once:o=!1,initial:c=!1}={}){let[u,d]=(0,s.useState)(c);return(0,s.useEffect)(()=>{if(!e.current||o&&u)return;let s={root:r&&r.current||void 0,margin:t,amount:l};return function(e,r,{root:t,margin:s,amount:n="some"}={}){let l=(0,a.K)(e),o=new WeakMap,c=new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target);if(!!t!==e.isIntersecting)if(e.isIntersecting){let t=r(e.target,e);"function"==typeof t?o.set(e.target,t):c.unobserve(e.target)}else"function"==typeof t&&(t(e),o.delete(e.target))})},{root:t,rootMargin:s,threshold:"number"==typeof n?n:i[n]});return l.forEach(e=>c.observe(e)),()=>c.disconnect()}(e.current,()=>(d(!0),o?void 0:()=>d(!1)),s)},[r,e,t,o,l]),u}},40382:(e,r,t)=>{"use strict";t.d(r,{$v:()=>o,W3:()=>u,eN:()=>c});var s=t(91199);t(42087);var a=t(76881),i=t(10747),n=t(72633);function l(e){let r=null;return e.business_profiles&&(Array.isArray(e.business_profiles)?r=e.business_profiles[0]?.business_slug||null:e.business_profiles&&"object"==typeof e.business_profiles&&(r=e.business_profiles.business_slug||null)),{id:e.id,business_id:e.business_id,name:e.name||"",description:e.description||"",base_price:Number(e.base_price)||0,discounted_price:e.discounted_price?Number(e.discounted_price):void 0,product_type:e.product_type||"physical",is_available:!!e.is_available,image_url:e.image_url,created_at:e.created_at,updated_at:e.updated_at,slug:e.slug,business_slug:r,featured_image_index:0,images:[]}}async function o(e){let r=await (0,n.N)({...e,viewType:"products"});return r.error?{error:r.error}:r.data?.products?{data:{products:r.data.products,totalCount:r.data.totalCount,hasMore:r.data.hasMore,nextPage:r.data.nextPage}}:{error:"No product data found"}}async function c(e){let{page:r=1,limit:t=20,sortBy:s="created_desc",productType:n=null,pincode:o=null,locality:c=null,productName:u=null,category:d=null}=e,_=await (0,a.createClient)(),{data:{user:p}}=await _.auth.getUser(),b=!!p;try{let e=await (0,a.createClient)(),_=(r-1)*t,p=e.from("business_profiles").select("id").eq("status","online");o&&(p=p.eq("pincode",o)),c&&(p=p.eq("locality",c)),d&&d.trim()&&(p=p.eq("business_category",d.trim()));let{data:m,error:g}=await p;if(g)return console.error("Error fetching valid businesses:",g),{error:"Failed to fetch valid businesses"};if(!m||0===m.length)return{data:{products:[],isAuthenticated:b,totalCount:0,hasMore:!1,nextPage:null}};let f=m.map(e=>e.id),h=e.from("products_services").select("id",{count:"exact"}).in("business_id",f).eq("is_available",!0);n&&(h=h.eq("product_type",n)),u&&u.trim().length>0&&(h=h.ilike("name",`%${u.trim()}%`));let{count:x,error:y}=await h;if(y)return console.error("Error counting products:",y),{error:"Failed to count products"};let v=e.from("products_services").select(`
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug)
      `).in("business_id",f).eq("is_available",!0);n&&(v=v.eq("product_type",n)),u&&u.trim().length>0&&(v=v.ilike("name",`%${u.trim()}%`));let w=(0,i.z)(s,!0),A=(0,i.o)(s);v=(v="price"===w?A?v.order("discounted_price",{ascending:!0,nullsFirst:!1}).order("base_price",{ascending:!0,nullsFirst:!1}):v.order("discounted_price",{ascending:!1,nullsFirst:!1}).order("base_price",{ascending:!1,nullsFirst:!1}):v.order(w,{ascending:A})).range(_,_+t-1);let{data:j,error:k}=await v;if(k)return console.error("Error fetching products:",k),{error:"Failed to fetch products"};let N=j.map(l),E=x||0,q=E>_+N.length;return{data:{products:N,isAuthenticated:b,totalCount:E,hasMore:q,nextPage:q?r+1:null}}}catch(e){return console.error("Unexpected error in fetchAllProducts:",e),{error:"An unexpected error occurred"}}}async function u(e){let{businessIds:r,page:t=1,limit:s=20,sortBy:n="created_desc",productType:o=null}=e;if(!r||0===r.length)return{error:"No business IDs provided"};let c=await (0,a.createClient)(),{data:{user:u}}=await c.auth.getUser(),d=!!u;try{let e=(t-1)*s,{data:a,error:u}=await c.from("business_profiles").select("id").in("id",r).eq("status","online");if(u)return console.error("Error filtering valid businesses:",u),{error:"Failed to filter valid businesses"};if(!a||0===a.length)return{data:{products:[],isAuthenticated:d,totalCount:0,hasMore:!1,nextPage:null}};let _=a.map(e=>e.id),p=c.from("products_services").select("id",{count:"exact"}).in("business_id",_).eq("is_available",!0);o&&(p=p.eq("product_type",o));let{count:b,error:m}=await p;if(m)return console.error("Error counting products:",m),{error:"Failed to count products"};let g=c.from("products_services").select(`
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug)
      `).in("business_id",_).eq("is_available",!0);o&&(g=g.eq("product_type",o));let f=(0,i.z)(n,!0),h=(0,i.o)(n);g=(g="price"===f?h?g.order("discounted_price",{ascending:!0,nullsFirst:!1}).order("base_price",{ascending:!0,nullsFirst:!1}):g.order("discounted_price",{ascending:!1,nullsFirst:!1}).order("base_price",{ascending:!1,nullsFirst:!1}):g.order(f,{ascending:h})).range(e,e+s-1);let{data:x,error:y}=await g;if(y)return console.error("Error fetching products:",y),{error:"Failed to fetch products"};let v=x.map(l),w=b||0,A=w>e+v.length;return{data:{products:v,isAuthenticated:d,totalCount:w,hasMore:A,nextPage:A?t+1:null}}}catch(e){return console.error("Unexpected error in fetchProductsByBusinessIds:",e),{error:"An unexpected error occurred"}}}(0,t(33331).D)([o,c,u]),(0,s.A)(o,"4018b43028809df977ce7bb88f96c0d45dd8e2c788",null),(0,s.A)(c,"409ef2f4e1a556b4e8644e19f5827625904001c1b1",null),(0,s.A)(u,"40e579c4350f6fcc557e3e669f6ed90ab2eb870447",null)},41862:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},56399:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=56399,e.exports=r},68267:(e,r,t)=>{"use strict";t.d(r,{Q:()=>l,m:()=>o});var s=t(91199);t(42087);var a=t(76881),i=t(89281),n=t(72633);async function l(e){let r=await (0,n.N)({...e,viewType:"cards"});return r.error?{error:r.error}:r.data?.businesses?{data:{businesses:r.data.businesses,totalCount:r.data.totalCount,hasMore:r.data.hasMore,nextPage:r.data.nextPage}}:{error:"No business data found"}}async function o(e){let{businessName:r,pincode:t,locality:s,page:n=1,limit:l=20,sortBy:o="created_desc",category:c=null}=e,u=await (0,a.createClient)(),{data:{user:d}}=await u.auth.getUser();try{let{data:e,count:a,error:u}=await (0,i.Kz)(r,t,s,n,l,o,c);if(u)return console.error("Search Businesses By Name Error:",u),{error:u};let _=a||0,p=_>(n-1)*l+(e?.length||0),b=p?n+1:null;return{data:{businesses:e?.map(e=>({id:e.id,business_name:e.business_name??"",contact_email:"",has_active_subscription:"active"===e.subscription_status||"authenticated"===e.subscription_status,trial_end_date:e.trial_end_date??null,created_at:e.created_at??void 0,updated_at:e.updated_at??void 0,logo_url:e.logo_url??"",member_name:e.member_name??"",title:e.title??"",address_line:e.address_line??"",city:e.city??"",state:e.state??"",pincode:e.pincode??"",locality:e.locality??"",phone:e.phone??"",business_category:e.business_category??"",instagram_url:e.instagram_url??"",facebook_url:e.facebook_url??"",whatsapp_number:e.whatsapp_number??"",about_bio:e.about_bio??"",status:"online"===e.status?"online":"offline",business_slug:e.business_slug??"",total_likes:e.total_likes??0,total_subscriptions:e.total_subscriptions??0,average_rating:e.average_rating??0,theme_color:e.theme_color??"#D4AF37",delivery_info:e.delivery_info??"",business_hours:e.business_hours,established_year:e.established_year??null,website_url:"",linkedin_url:"",twitter_url:"",youtube_url:"",call_number:""}))??[],isAuthenticated:!!d,totalCount:_,hasMore:p,nextPage:b}}}catch(e){return console.error("Search Businesses Exception:",e),{error:"An unexpected error occurred during the search."}}}(0,t(33331).D)([l,o]),(0,s.A)(l,"4027ebed4ed3330c943c58b278d97ba413e3a31d66",null),(0,s.A)(o,"404b91e3dc66700c3a24f1fde5c90ed9d11b03a1f0",null)},68988:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(96241);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},70319:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var s=t(60687),a=t(85814),i=t.n(a),n=t(30474),l=t(18265),o=t(77882),c=t(97992),u=t(67760),d=t(23026),_=t(64398),p=t(96241),b=t(43210);function m({business:e,index:r}){let[t,a]=(0,b.useState)(!1),m=(0,b.useRef)(null),g=(0,l.W)(m,{once:!1,amount:.2}),f=()=>{let r=e.address_line||"",t=[e.locality,e.city].filter(Boolean).join(", "),s=[e.state,e.pincode?`PIN: ${e.pincode}`:null].filter(Boolean).join(", ");return{addressLine:r,localityCity:t,statePin:s,hasAddress:!!(r||t||s)}};return(0,s.jsx)(o.P.div,{ref:m,initial:{opacity:0,y:20},animate:g?{opacity:1,y:0}:{},transition:{duration:.4,delay:.05*r},className:"h-full",onHoverStart:()=>a(!0),onHoverEnd:()=>a(!1),children:(0,s.jsx)(i(),{href:`/${e.business_slug}`,className:"block h-full",children:(0,s.jsxs)(o.P.div,{className:"relative h-full overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm transition-all duration-300",animate:{boxShadow:t?"0 10px 25px -5px rgba(var(--brand-gold-rgb), 0.2), 0 8px 10px -6px rgba(var(--brand-gold-rgb), 0.1)":"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",borderColor:t?"rgba(var(--brand-gold-rgb), 0.3)":""},children:[(0,s.jsxs)("div",{className:"p-3 sm:p-4 flex flex-col h-full",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center text-center mb-4",children:[(0,s.jsxs)("div",{className:"relative flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-full overflow-hidden border-2 border-[var(--brand-gold)]/30 mb-2",children:[e.logo_url&&""!==e.logo_url.trim()?(0,s.jsx)(n.default,{src:e.logo_url,alt:e.business_name||"Business logo",fill:!0,className:"object-cover",sizes:"(max-width: 640px) 48px, 64px"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] text-xl font-semibold",children:e.business_name?.charAt(0)||"B"}),t&&(0,s.jsx)(o.P.div,{className:"absolute inset-0 rounded-full border-2 border-[var(--brand-gold)]",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3}})]}),(0,s.jsx)("div",{className:"min-w-0 w-full px-2",children:(0,s.jsxs)(o.P.h3,{className:"font-semibold text-neutral-900 dark:text-white line-clamp-1 text-sm sm:text-base md:text-lg relative inline-block mx-auto",animate:{color:t?"var(--brand-gold)":""},transition:{duration:.2},children:[e.business_name,t&&(0,s.jsx)(o.P.div,{className:"absolute -bottom-1 left-0 h-0.5 bg-[var(--brand-gold)]",initial:{width:0},animate:{width:"100%"},transition:{duration:.3}})]})})]}),(0,s.jsx)("div",{className:"space-y-2 mb-3 flex-grow",children:(0,s.jsxs)("div",{className:"flex gap-1.5 sm:gap-2 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg p-2 sm:p-2.5 border border-neutral-100 dark:border-neutral-800",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-neutral-500 dark:text-neutral-400 flex-shrink-0 mt-0.5"}),(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(()=>{let{addressLine:e,localityCity:r,statePin:t,hasAddress:a}=f();return a?(0,s.jsxs)(s.Fragment,{children:[e&&(0,s.jsx)("p",{className:"text-xs font-medium text-neutral-700 dark:text-neutral-300 line-clamp-1",children:e}),r&&(0,s.jsx)("p",{className:"text-xs text-neutral-600 dark:text-neutral-400 line-clamp-1 mt-0.5",children:r}),t&&(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-500 line-clamp-1 mt-0.5",children:t})]}):(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-500 italic",children:"Location not specified"})})()})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs border-t border-neutral-100 dark:border-neutral-800 pt-2 mt-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 text-rose-500 dark:text-rose-400",children:[(0,s.jsx)(u.A,{className:"h-3.5 w-3.5"}),(0,s.jsx)("span",{children:(0,p.gY)(e.total_likes||0)})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 text-blue-500 dark:text-blue-400",children:[(0,s.jsx)(d.A,{className:"h-3.5 w-3.5"}),(0,s.jsx)("span",{children:(0,p.gY)(e.total_subscriptions||0)})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 text-amber-500 dark:text-amber-400",children:[(0,s.jsx)(_.A,{className:"h-3.5 w-3.5"}),(0,s.jsx)("span",{children:(e.average_rating||0).toFixed(1)})]})]})]}),(0,s.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/5 to-blue-500/5 dark:from-[var(--brand-gold)]/10 dark:to-blue-500/10 pointer-events-none",initial:{opacity:0},animate:{opacity:+!!t},transition:{duration:.3}})]})})})}},71463:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var s=t(60687),a=t(96241);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",e),...r})}},72633:(e,r,t)=>{"use strict";t.d(r,{N:()=>x});var s=t(91199);t(42087);var a=t(76881),i=t(68267),n=t(40382),l=t(68567);let o=l.Ik({pincode:l.Yj().regex(/^\d{6}$/,{message:"Pincode must be exactly 6 digits."}),locality:l.Yj().optional().nullable()}),c=l.Ik({city:l.Yj().min(2,{message:"City name must be at least 2 characters."}),locality:l.Yj().optional().nullable()}),u=l.Ik({businessName:l.Yj().min(1,{message:"Business name is required."})}),d=l.Ik({businessName:l.Yj().optional().nullable(),pincode:l.Yj().regex(/^\d{6}$/,{message:"Pincode must be exactly 6 digits."}).optional().nullable(),city:l.Yj().optional().nullable(),locality:l.Yj().optional().nullable(),category:l.Yj().optional().nullable()}),_=l.Ik({page:l.ai().int().positive().default(1),limit:l.ai().int().positive().max(50).default(20)}),p=l.Ik({sortBy:l.k5(["name_asc","name_desc","created_asc","created_desc","likes_asc","likes_desc","subscriptions_asc","subscriptions_desc","rating_asc","rating_desc"]).default("created_desc")});o.extend({viewType:l.k5(["cards","products"]),..._.shape,...p.shape}),u.extend({viewType:l.k5(["cards","products"]),..._.shape,...p.shape}),d.extend({viewType:l.k5(["cards","products"]),..._.shape,...p.shape});var b=t(56528),m=t(89281),g=t(10747),f=t(33331);async function h(e){let r,t,{pincode:s,city:i,locality:n,viewType:l,page:u=1,limit:d=20,sortBy:_="created_desc",productType:p=null,category:f=null}=e;if(!s&&!i)return{error:"Either pincode or city is required."};let h=await (0,a.createClient)(),x=[];if(s){let e=o.safeParse({pincode:s});if(!e.success)return{error:"Invalid Pincode format. Must be 6 digits."};let a=e.data.pincode,i=await (0,b.tz)(a);if(i.error||!i.city||!i.state)return{error:i.error||"Pincode not found."};r=i.city,t=i.state,x=[a]}else{if(!i)return{error:"Either pincode or city is required."};let e=c.safeParse({city:i});if(!e.success)return{error:"Invalid city name. Must be at least 2 characters."};let s=e.data.city;r=s,x=[];try{let{data:e}=await h.from("pincodes").select("StateName").ilike("DivisionName",`%${s}%`).limit(1);t=e&&e.length>0?e[0].StateName:""}catch(e){console.error("Error getting state for city:",e),t=""}}let{data:{user:y}}=await h.auth.getUser(),v=!!y;try{if(await new Promise(e=>setTimeout(e,100)),"cards"===l){if(i&&0===x.length){let e=(u-1)*d,s=`
          id, business_name, logo_url, member_name, title,
          address_line, city, state, pincode, locality, phone, business_category, instagram_url,
          facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
          delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
          trial_end_date, created_at, updated_at, contact_email
        `,a=h.from("business_profiles").select("id",{count:"exact"}).eq("city",i).eq("status","online");f&&f.trim()&&(a=a.eq("business_category",f.trim()));let{count:n,error:l}=await a;if(l)return console.error("City Business Count Error:",l),{error:"Database error counting businesses by city."};let o=h.from("business_profiles").select(s).eq("city",i).eq("status","online").range(e,e+d-1).order((0,g.z)(_),{ascending:(0,g.o)(_)});f&&f.trim()&&(o=o.eq("business_category",f.trim()));let{data:c,error:p}=await o;if(p)return console.error("City Business Query Error:",p),{error:"Database error fetching businesses by city."};return{data:{location:{city:r,state:t},businesses:c.map(e=>({id:e.id,business_name:e.business_name??"",contact_email:"",created_at:e.created_at??void 0,updated_at:e.updated_at??void 0,logo_url:e.logo_url??"",member_name:e.member_name??"",title:e.title??"",address_line:e.address_line??"",city:e.city??"",state:e.state??"",pincode:e.pincode??"",locality:e.locality??"",phone:e.phone??"",business_category:e.business_category??"",instagram_url:e.instagram_url??"",facebook_url:e.facebook_url??"",whatsapp_number:e.whatsapp_number??"",about_bio:e.about_bio??"",status:"online"===e.status?"online":"offline",business_slug:e.business_slug??"",total_likes:e.total_likes??0,total_subscriptions:e.total_subscriptions??0,average_rating:e.average_rating??0,theme_color:e.theme_color??"#D4AF37",delivery_info:e.delivery_info??"",business_hours:e.business_hours,established_year:null,website_url:"",linkedin_url:"",twitter_url:"",youtube_url:"",call_number:""})),isAuthenticated:v,totalCount:n||0,hasMore:(n||0)>u*d,nextPage:(n||0)>u*d?u+1:null}}}let{data:e,count:s,error:a}=await (0,m.Cv)(x,n,u,d,_);if(a)return console.error("Search Discover (Cards) Error:",a),{error:a};let l=s||0,o=l>u*d,c=o?u+1:null,p=e?.map(e=>({id:e.id,business_name:e.business_name??"",contact_email:"",has_active_subscription:"active"===e.subscription_status,trial_end_date:e.trial_end_date??null,created_at:e.created_at??void 0,updated_at:e.updated_at??void 0,logo_url:e.logo_url??"",member_name:e.member_name??"",title:e.title??"",address_line:e.address_line??"",city:e.city??"",state:e.state??"",pincode:e.pincode??"",locality:e.locality??"",phone:e.phone??"",business_category:e.business_category??"",instagram_url:e.instagram_url??"",facebook_url:e.facebook_url??"",whatsapp_number:e.whatsapp_number??"",about_bio:e.about_bio??"",status:"online"===e.status?"online":"offline",business_slug:e.business_slug??"",total_likes:e.total_likes??0,total_subscriptions:e.total_subscriptions??0,average_rating:e.average_rating??0,theme_color:e.theme_color??"#D4AF37",delivery_info:e.delivery_info??"",business_hours:e.business_hours,established_year:e.established_year??null,website_url:"",linkedin_url:"",twitter_url:"",youtube_url:"",call_number:""}))??[];return{data:{location:{city:r,state:t},businesses:p,isAuthenticated:v,totalCount:l,hasMore:o,nextPage:c}}}{let e=[];if(i&&0===x.length){let r=h.from("business_profiles").select("id").eq("city",i).eq("status","online");f&&f.trim()&&(r=r.eq("business_category",f.trim()));let{data:t,error:s}=await r;if(s)return console.error("City Business IDs Error:",s),{error:"Database error fetching business IDs by city."};e=t.map(e=>e.id)}else{let{data:r,error:t}=await (0,m.ZB)(x,n,_);if(t)return console.error("Search Discover (Product IDs) Error:",t),{error:t};e=r||[]}if(!e||0===e.length)return{data:{location:{city:r,state:t},products:[],isAuthenticated:v,totalCount:0,hasMore:!1,nextPage:null}};let s=h.from("products_services").select("id",{count:"exact"}).in("business_id",e||[]).eq("is_available",!0);p&&(s=s.eq("product_type",p));let{count:a,error:l}=await s;if(l)return console.error("Search Discover (Product Count) Error:",l),{error:"Database error counting products."};let o=(u-1)*d,c=a||0,b=c>u*d,y=b?u+1:null,w=h.from("products_services").select(`
          id, business_id, name, description, base_price, discounted_price, product_type,
          is_available, image_url, created_at, updated_at, slug,
          business_profiles!business_id(business_slug)
        `).in("business_id",e||[]).eq("is_available",!0);p&&(w=w.eq("product_type",p)),w=w.range(o,o+d-1);let A=(0,g.z)(_,!0),j=(0,g.o)(_);w="price"===A?j?w.order("discounted_price",{ascending:!0,nullsFirst:!1}).order("base_price",{ascending:!0,nullsFirst:!1}):w.order("discounted_price",{ascending:!1,nullsFirst:!1}).order("base_price",{ascending:!1,nullsFirst:!1}):w.order(A,{ascending:j});let{data:k,error:N}=await w;if(N)return console.error("Search Discover (Products) Error:",N),{error:"Database error fetching nearby products."};let E=k?.map(e=>{let r=null;return e.business_profiles&&(Array.isArray(e.business_profiles)&&e.business_profiles.length>0?r=e.business_profiles[0].business_slug:"object"==typeof e.business_profiles&&null!==e.business_profiles&&(r=e.business_profiles.business_slug)),{id:e.id,business_id:e.business_id??void 0,name:e.name??"",description:e.description??"",base_price:e.base_price??0,discounted_price:e.discounted_price??null,product_type:e.product_type??"physical",is_available:e.is_available??!0,image_url:e.image_url,created_at:e.created_at||void 0,updated_at:e.updated_at||void 0,business_slug:r,featured_image_index:0,images:[],slug:e.slug||void 0}})??[];return{data:{location:{city:r,state:t},products:E,isAuthenticated:v,totalCount:c,hasMore:b,nextPage:y}}}}catch(e){return console.error("Search Discover Exception:",e),{error:"An unexpected error occurred during the search."}}}async function x(e){let{businessName:r,productName:t,pincode:s,city:l,locality:o,category:c,viewType:u,page:d=1,limit:_="products"===u?20:5,businessSort:p="created_desc",productSort:b="created_desc",productType:m=null}=e,g=await (0,a.createClient)(),{data:{user:f}}=await g.auth.getUser(),x=!!f;try{if("products"===u&&t&&t.trim().length>0){let e=await (0,n.eN)({page:d,limit:_,sortBy:b,productType:m,pincode:s,locality:o,productName:t,category:c});if(e.error)return{error:e.error};return{data:{products:e.data?.products||[],isAuthenticated:x,totalCount:e.data?.totalCount||0,hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}if(r&&r.trim().length>0){let e=await (0,i.m)({businessName:r,pincode:s,locality:o,page:d,limit:_,sortBy:p,category:c});if(e.error)return{error:e.error};if("cards"===u)return{data:{businesses:e.data?.businesses||[],isAuthenticated:x,totalCount:e.data?.totalCount||0,hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}};{let r=e.data?.businesses.map(e=>e.id);if(!r||0===r.length)return{data:{products:[],isAuthenticated:x,totalCount:0,hasMore:!1,nextPage:null}};let t=await (0,n.W3)({businessIds:r,page:d,limit:_,sortBy:b,productType:m});if(t.error)return{error:t.error};return{data:{products:t.data?.products||[],isAuthenticated:x,totalCount:t.data?.totalCount||0,hasMore:t.data?.hasMore||!1,nextPage:t.data?.nextPage||null}}}}if(s||l)return await h({pincode:s||void 0,city:l||void 0,locality:o,viewType:u,page:d,limit:_,sortBy:"products"===u?b:p,productType:"products"===u?m:null,category:c});else if("cards"===u){let e=await (0,i.m)({page:d,limit:_,sortBy:p,category:c});if(e.error)return{error:e.error};return{data:{businesses:e.data?.businesses||[],isAuthenticated:x,totalCount:e.data?.totalCount||0,hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}else{let e=await (0,n.eN)({page:d,limit:_,sortBy:b,productType:m,pincode:s||void 0,locality:o,category:c});if(e.error)return{error:e.error};return{data:{products:e.data?.products||[],isAuthenticated:x,totalCount:e.data?.totalCount||0,hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}}catch(e){return console.error("Search Discover Combined Exception:",e),{error:"An unexpected error occurred during the search."}}}(0,f.D)([h]),(0,s.A)(h,"4052aea927c48b504eb2b926f4f5b1fbbf4db51fc5",null),(0,f.D)([x]),(0,s.A)(x,"40edb48823df0fd876d80ab1b1ef92ac50df45d091",null)},76881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{createClient:()=>a});var s=t(63014);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!r)throw Error("Supabase environment variables are not set.");let a=null,i=null;try{let{headers:e,cookies:r}=await Promise.all([t.e(4208),t.e(4659)]).then(t.bind(t,74208));a=await e(),i=await r()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let r=e.get("x-test-auth-state"),t=e.get("x-test-user-type"),s="customer"===t||"business"===t,a=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===r?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===r?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,r,t,s,a){let i=()=>{var i,n,l,o,c;return i=e,n=r,l=t,o=s,c=a,"customer_profiles"===i?{data:l&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:l&&"business"===n?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===n?{id:"test-subscription-id",plan_id:c,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:r=>n(e),eq:(r,t)=>n(e),neq:(r,t)=>n(e),gt:(r,t)=>n(e),gte:(r,t)=>n(e),lt:(r,t)=>n(e),lte:(r,t)=>n(e),like:(r,t)=>n(e),ilike:(r,t)=>n(e),is:(r,t)=>n(e),in:(r,t)=>n(e),contains:(r,t)=>n(e),containedBy:(r,t)=>n(e),rangeGt:(r,t)=>n(e),rangeGte:(r,t)=>n(e),rangeLt:(r,t)=>n(e),rangeLte:(r,t)=>n(e),rangeAdjacent:(r,t)=>n(e),overlaps:(r,t)=>n(e),textSearch:(r,t)=>n(e),match:r=>n(e),not:(r,t,s)=>n(e),or:r=>n(e),filter:(r,t,s)=>n(e),order:(r,t)=>n(e),limit:(r,t)=>n(e),range:(r,t,s)=>n(e),abortSignal:r=>n(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let r=i();return e?e(r):r},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:r=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async r=>{let t={data:Array.isArray(e)?e:[e],error:null};return r?r(t):t}}),then:async r=>{let t={data:Array.isArray(e)?e:[e],error:null};return r?r(t):t}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,r)=>n()}})(e,t,s,a,i)}}(a):i?(0,s.createServerClient)(e,r,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:r,value:t,options:s}of e)await i.set(r,t,s)}catch{}}}}):(0,s.createServerClient)(e,r,{cookies:{getAll:()=>[],setAll(){}}})}},89281:(e,r,t)=>{"use strict";t.d(r,{ZB:()=>d,Kz:()=>_,Cv:()=>u});var s=t(91199);t(42087);var a=t(76881),i=t(33331);async function n(e){if(!e)return{error:"Business slug is required."};try{let r=await (0,a.createClient)(),{data:t,error:s}=await r.from("business_profiles").select(`
        *,
        payment_subscriptions!business_profile_id (
          plan_id,
          subscription_status
        )
      `).eq("business_slug",e).maybeSingle();if(s)return console.error("Secure Fetch Error:",s),{error:`Failed to fetch business profile: ${s.message}`};if(!t)return{error:"Profile not found."};return{data:{...t,subscription_status:t.payment_subscriptions?.subscription_status||null,plan_id:t.payment_subscriptions?.plan_id||null}}}catch(e){return console.error("Exception in getSecureBusinessProfileBySlug:",e),{error:"An unexpected error occurred."}}}async function l(e){if(!e)return{error:"Business slug is required."};try{let r=await (0,a.createClient)(),{data:t,error:s}=await r.from("business_profiles").select(`
        *,
        products_services (
          id, name, description, base_price, discounted_price, is_available, image_url, created_at, updated_at, product_type
        )
      `).eq("business_slug",e).maybeSingle();if(s)return console.error("Secure Fetch Error:",s),{error:`Failed to fetch business profile: ${s.message}`};if(!t)return{error:"Profile not found."};return{data:{...t,products_services:t.products_services||[]}}}catch(e){return console.error("Exception in getSecureBusinessProfileWithProductsBySlug:",e),{error:"An unexpected error occurred."}}}function o(e,r){switch(r){case"name_asc":return e.order("business_name",{ascending:!0});case"name_desc":return e.order("business_name",{ascending:!1});case"created_asc":return e.order("created_at",{ascending:!0});case"created_desc":default:return e.order("created_at",{ascending:!1});case"likes_asc":return e.order("total_likes",{ascending:!0});case"likes_desc":return e.order("total_likes",{ascending:!1});case"subscriptions_asc":return e.order("total_subscriptions",{ascending:!0});case"subscriptions_desc":return e.order("total_subscriptions",{ascending:!1});case"rating_asc":return e.order("average_rating",{ascending:!0});case"rating_desc":return e.order("average_rating",{ascending:!1})}}function c(){return new Date().toISOString()}async function u(e,r,t=1,s=10,i="created_desc"){if(!e||Array.isArray(e)&&0===e.length)return{error:"At least one pincode is required."};let n=Array.isArray(e)?e:[e];try{let e=await (0,a.createClient)(),l=(t-1)*s,c=e.from("business_profiles").select("id",{count:"exact"}).in("pincode",n).eq("status","online");r&&c.eq("locality",r);let{count:u,error:d}=await c;if(d)return console.error("Count Error:",d),{error:"Database error counting profiles."};if(!u||0===u)return{data:[],count:0};let _=`
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year,
      custom_branding, custom_ads
    `,p=e.from("business_profiles").select(_).in("pincode",n).eq("status","online").range(l,l+s-1);r&&p.eq("locality",r),p=o(p,i);let{data:b,error:m}=await p;if(m)return console.error("Query Error:",m),{error:"Database error fetching profiles."};return{data:b.map(e=>({...e,has_active_subscription:!1,total_visits:0,today_visits:0,yesterday_visits:0,visits_7_days:0,visits_30_days:0,city_slug:null,state_slug:null,locality_slug:null,gallery:null,latitude:null,longitude:null,subscription_status:null,plan_id:null})),count:u||0}}catch(e){return console.error("Exception in getSecureBusinessProfilesForDiscover:",e),{error:"An unexpected error occurred."}}}async function d(e,r,t="created_desc"){if(!e||Array.isArray(e)&&0===e.length)return{error:"At least one pincode is required."};let s=Array.isArray(e)?e:[e];try{let e=(await (0,a.createClient)()).from("business_profiles").select("id").in("pincode",s).eq("status","online");r&&e.eq("locality",r),e=o(e,t);let{data:i,error:n}=await e;if(n)return console.error("Query Error:",n),{error:"Database error fetching profile IDs."};return{data:i.map(e=>e.id)}}catch(e){return console.error("Exception in getSecureBusinessProfileIdsForDiscover:",e),{error:"An unexpected error occurred."}}}async function _(e,r,t,s=1,i=20,n="created_desc",l){try{let u=await (0,a.createClient)();c();let d=(s-1)*i,_=`
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year,
      custom_branding, custom_ads
    `,p=u.from("business_profiles").select("id",{count:"exact"}).eq("status","online"),b=u.from("business_profiles").select(_).eq("status","online");if(e){let r=e.trim();r&&(p=p.ilike("business_name",`%${r}%`),b=b.ilike("business_name",`%${r}%`))}r&&(p=p.eq("pincode",r),b=b.eq("pincode",r)),t&&(p=p.eq("locality",t),b=b.eq("locality",t)),l&&l.trim()&&(p=p.eq("business_category",l.trim()),b=b.eq("business_category",l.trim())),b=(b=o(b,n)).range(d,d+i-1);let[m,g]=await Promise.all([p,b]),{count:f,error:h}=m,{data:x,error:y}=g;if(h)return console.error("Count Error:",h),{error:"Database error counting profiles."};if(y)return console.error("Data Error:",y),{error:"Database error fetching profiles."};if(!f||0===f||!x||0===x.length)return{data:[],count:0};let v=x.map(e=>e.id),{data:w,error:A}=await u.from("payment_subscriptions").select("business_profile_id, subscription_status, plan_id").in("business_profile_id",v).order("created_at",{ascending:!1});A&&console.error("Error fetching subscription data:",A);let j=function(e){let r=new Map;return e&&e.forEach(e=>{r.has(e.business_profile_id)||r.set(e.business_profile_id,{subscription_status:e.subscription_status,plan_id:e.plan_id})}),r}(w);return{data:x.map(e=>{let r=j.get(e.id)||{subscription_status:null,plan_id:null};return{...e,has_active_subscription:"active"===r.subscription_status,total_visits:0,today_visits:0,yesterday_visits:0,visits_7_days:0,visits_30_days:0,city_slug:null,state_slug:null,locality_slug:null,gallery:null,latitude:null,longitude:null,subscription_status:r.subscription_status,plan_id:r.plan_id}}),count:f}}catch(e){return console.error("Exception in getSecureBusinessProfiles:",e),{error:"An unexpected error occurred."}}}async function p(){try{let e=await (0,a.createClient)(),{data:r,error:t}=await e.from("business_profiles").select("business_slug, updated_at").eq("status","online").not("business_slug","is",null);if(t)return{error:"Database error fetching profiles."};if(!r||0===r.length)return{data:[]};let s=new Map;return r.forEach(e=>{e.business_slug&&s.set(e.business_slug,{business_slug:e.business_slug,updated_at:e.updated_at})}),{data:Array.from(s.values())}}catch(e){return{error:"An unexpected error occurred."}}}async function b(e){try{let r=await (0,a.createClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return{hasAccess:!1,error:"Authentication required"};let{data:i,error:n}=await r.from("business_profiles").select("id").eq("id",e).eq("id",t.id).maybeSingle();if(n)return console.error("Access check error:",n),{hasAccess:!1,error:"Error checking access"};return{hasAccess:!!i}}catch(e){return console.error("Exception in checkBusinessProfileAccess:",e),{hasAccess:!1,error:"An unexpected error occurred"}}}async function m(){try{let e=await (0,a.createClient)(),{data:{user:r},error:t}=await e.auth.getUser();if(t||!r)return{error:"Authentication required"};let{data:s,error:i}=await e.from("business_profiles").select("id").eq("id",r.id).maybeSingle();if(i)return console.error("Profile ID fetch error:",i),{error:"Error fetching profile ID"};if(!s)return{error:"Business profile not found"};return{profileId:s.id}}catch(e){return console.error("Exception in getCurrentUserBusinessProfileId:",e),{error:"An unexpected error occurred"}}}async function g(e,r=1,t=20,s="created_desc"){try{let i=await (0,a.createClient)();c();let n=(r-1)*t,l=`
      id,
      business_name,
      logo_url,
      member_name,
      title,
      address_line,
      city,
      state,
      pincode,
      locality,
      phone,
      instagram_url,
      facebook_url,
      whatsapp_number,
      about_bio,
      status,
      business_slug,
      theme_color,
      delivery_info,
      business_hours,
      business_category,
      total_likes,
      total_subscriptions,
      average_rating,
      created_at,
      updated_at,
      trial_end_date,
      contact_email,
      established_year,
      custom_branding,
      custom_ads,
      has_active_subscription,
      total_visits,
      today_visits,
      yesterday_visits
    `,o=i.from("business_profiles").select(l,{count:"exact"}).eq("status","online");e.pincode?o=o.eq("pincode",e.pincode):e.city?o=o.eq("city",e.city):e.state&&(o=o.eq("state",e.state)),e.locality&&(o=o.ilike("locality",`%${e.locality}%`)),o=(o="created_desc"===s?o.order("created_at",{ascending:!1}):"created_asc"===s?o.order("created_at",{ascending:!0}):"name_asc"===s?o.order("business_name",{ascending:!0}):"name_desc"===s?o.order("business_name",{ascending:!1}):"rating_desc"===s?o.order("average_rating",{ascending:!1}):"likes_desc"===s?o.order("total_likes",{ascending:!1}):o.order("created_at",{ascending:!1})).range(n,n+t-1);let{data:u,count:d,error:_}=await o;if(_)return console.error("Error fetching businesses by location:",_),{error:"Database error fetching businesses."};return{data:u,count:d||0}}catch(e){return console.error("Unexpected error in getSecureBusinessProfilesByLocation:",e),{error:"An unexpected error occurred."}}}(0,i.D)([n,l]),(0,s.A)(n,"407031dcd9eee2e858cfbb811d905061dca994df13",null),(0,s.A)(l,"40274d19c8c06b8c99a2839744992d7bcecb2341c5",null),(0,i.D)([u,d]),(0,s.A)(u,"7c76d89983ca9f1290f11616f3bb631fd67faec14d",null),(0,s.A)(d,"70e4edcef42c86cf3a9ba8c209ba199083b0a30092",null),(0,i.D)([_]),(0,s.A)(_,"7f1ff705112688da08e7c134e2df7387375812d40e",null),(0,i.D)([p]),(0,s.A)(p,"0056cc8508beee15907e2741a09bee834aa2799373",null),(0,i.D)([b,m]),(0,s.A)(b,"4088dc18235d8e33be19d97fc9c00c650f82e08953",null),(0,s.A)(m,"00a8d295fac0c253c5942cbbd729c108ff5d62d666",null),(0,i.D)([g]),(0,s.A)(g,"788721cac1d66dc0019d95bd960521fed5169baa89",null)}};