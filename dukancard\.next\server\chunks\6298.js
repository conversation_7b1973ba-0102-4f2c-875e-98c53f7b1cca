exports.id=6298,exports.ids=[5453,6298],exports.modules={8033:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(95453);async function n(e,t=1,r=10){try{console.log(`[RAZORPAY_DEBUG] Fetching invoices for subscription: ${e}, page: ${t}, count: ${r}`);let n=(0,s.bG)(),a=await fetch(`${s.ST.replace("/v2","/v1")}/invoices?subscription_id=${e}&count=${r}&skip=${(t-1)*r}`,{method:"GET",headers:n}),i=await a.json();if(!a.ok)return console.error("[RAZORPAY_ERROR] Error fetching invoices:",i),{success:!1,error:i};return console.log(`[RAZORPAY_DEBUG] Successfully fetched ${i.count} invoices for subscription ${e}`),{success:!0,data:i}}catch(e){return console.error("[RAZORPAY_ERROR] Exception fetching invoices:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>n});var s=r(34386);async function n(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let n=null,a=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));n=await e(),a=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||n&&"true"===n.get("x-playwright-testing"))&&n?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,n=e.get("x-test-business-slug"),a=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,n){let a=()=>{var a,i,o,l,u;return a=e,i=t,o=r,l=s,u=n,"customer_profiles"===a?{data:o&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===a?{data:o&&"business"===i?{id:"test-user-id",business_slug:l||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===a?{data:"business"===i?{id:"test-subscription-id",plan_id:u,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===a?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,r)=>i(e),neq:(t,r)=>i(e),gt:(t,r)=>i(e),gte:(t,r)=>i(e),lt:(t,r)=>i(e),lte:(t,r)=>i(e),like:(t,r)=>i(e),ilike:(t,r)=>i(e),is:(t,r)=>i(e),in:(t,r)=>i(e),contains:(t,r)=>i(e),containedBy:(t,r)=>i(e),rangeGt:(t,r)=>i(e),rangeGte:(t,r)=>i(e),rangeLt:(t,r)=>i(e),rangeLte:(t,r)=>i(e),rangeAdjacent:(t,r)=>i(e),overlaps:(t,r)=>i(e),textSearch:(t,r)=>i(e),match:t=>i(e),not:(t,r,s)=>i(e),or:t=>i(e),filter:(t,r,s)=>i(e),order:(t,r)=>i(e),limit:(t,r)=>i(e),range:(t,r,s)=>i(e),abortSignal:t=>i(e),single:async()=>a(),maybeSingle:async()=>a(),then:async e=>{let t=a();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,r,s,n,a)}}(n):a?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await a.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await a.set(t,r,s)}catch{}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},39727:()=>{},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},78335:()=>{},95453:(e,t,r)=>{"use strict";r.d(t,{ST:()=>a,bG:()=>o,t6:()=>l});var s=r(55511),n=r.n(s);let a="https://api.razorpay.com/v2",i=()=>{let e,t;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",t=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!t)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!t,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:t}},o=()=>{let{keyId:e,keySecret:t}=i(),r=Buffer.from(`${e}:${t}`).toString("base64");return{Authorization:`Basic ${r}`,"Content-Type":"application/json"}},l=(e,t,r)=>{try{let s=n().createHmac("sha256",r).update(e).digest("hex");return n().timingSafeEqual(Buffer.from(t),Buffer.from(s))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};