"use strict";exports.id=1374,exports.ids=[1374],exports.modules={19559:(e,a,t)=>{t.r(a),t.d(a,{default:()=>m});var r=t(37413);t(61120);var i=t(14890),s=t(60644),n=t(11637),d=t(95006),l=t(92506),o=t(46501),c=t(21886),x=t(23392);function m({children:e}){return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(x.<PERSON>,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,r.jsx)(s.default,{}),(0,r.jsx)(d.default,{}),(0,r.jsx)(n.default,{}),(0,r.jsx)(l.default,{}),(0,r.jsx)(o.default,{}),(0,r.jsx)(c.default,{excludePaths:["/dashboard"]})]})})})}},28559:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},38429:(e,a,t)=>{t.d(a,{A:()=>l});var r=t(60687),i=t(96241),s=t(77882),n=t(43210),d=t(18265);function l({title:e,highlightWords:a=[],className:t,subtitle:l,align:o="center",size:c="large"}){let x=(0,n.useRef)(null),m=(0,d.W)(x,{once:!0,amount:.3}),u=e.split(" ");return(0,r.jsxs)("div",{className:(0,i.cn)({left:"text-left",center:"text-center",right:"text-right"}[o],t),children:[(0,r.jsx)("h2",{ref:x,className:(0,i.cn)({small:"text-2xl md:text-3xl lg:text-4xl",medium:"text-3xl md:text-4xl lg:text-5xl",large:"text-4xl md:text-5xl lg:text-6xl"}[c],"font-bold text-foreground tracking-tight leading-tight"),children:u.map((e,t)=>{let i=a.some(a=>e.toLowerCase().includes(a.toLowerCase())),n=.1+.05*t;return(0,r.jsx)("span",{className:"inline-block mr-[0.3em]",children:i?(0,r.jsxs)(s.P.span,{className:"inline-block relative text-[var(--brand-gold)]",initial:{opacity:0,y:20},animate:m?{opacity:1,y:0}:{},transition:{duration:.5,delay:n},children:[e,(0,r.jsx)(s.P.span,{className:"absolute bottom-0 left-0 h-1 bg-[var(--brand-gold)] rounded-full",initial:{width:0},animate:m?{width:"100%"}:{},transition:{duration:.5,delay:.5+.05*t}}),(0,r.jsx)(s.P.span,{className:"absolute inset-0 rounded-lg -z-10 bg-[var(--brand-gold)]/20",animate:{opacity:[.2,.3,.2]},transition:{duration:3,repeat:1/0,repeatType:"reverse"}})]}):(0,r.jsx)(s.P.span,{className:"inline-block",initial:{opacity:0,y:20},animate:m?{opacity:1,y:0}:{},transition:{duration:.5,delay:n},children:e})},t)})}),l&&(0,r.jsxs)(s.P.p,{className:"text-lg md:text-xl text-muted-foreground mt-4 max-w-3xl mx-auto",initial:{opacity:0,y:10},animate:m?{opacity:1,y:0}:{},transition:{duration:.5,delay:.05*u.length+.2},children:[l,(0,r.jsx)(s.P.span,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 dark:via-white/5 to-transparent",initial:{x:"-100%"},animate:m?{x:"100%"}:{},transition:{duration:1.5,delay:.05*u.length+.5,ease:"easeInOut"}})]})]})}},54670:(e,a,t)=>{t.d(a,{C:()=>r});let r={name:"Dukancard",description:"Create and share digital business cards, showcase products, and connect with customers.",url:"https://dukancard.in",ogImage:"https://dukancard.in/opengraph-image.png",contact:{email:"<EMAIL>",phone:"+91 8458060663",address:{street:"Bisra Road",city:"Rourkela",state:"Odisha",postalCode:"769001",country:"India",full:"Bisra Road, Rourkela, Odisha - 769001"},hours:"Monday - Friday: 9:00 AM - 6:00 PM"},legal:{privacyPolicy:"/privacy",termsOfService:"/terms",refundPolicy:"/refund"},support:{email:"<EMAIL>",phone:"+91 8458060663",helpCenter:"/support"},advertising:{email:"<EMAIL>",phone:"+************",page:"/advertise"}}},55192:(e,a,t)=>{t.d(a,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>s,aR:()=>n,wL:()=>c});var r=t(60687);t(43210);var i=t(96241);function s({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function n({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function d({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...a})}function l({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...a})}function o({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...a})}function c({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,i.cn)("flex items-center px-6 [.border-t]:pt-6",e),...a})}},63539:(e,a,t)=>{t.d(a,{A:()=>f});var r=t(60687),i=t(43210),s=t(18265),n=t(77882),d=t(85814),l=t.n(d),o=t(28559),c=t(70334),x=t(24934),m=t(55192),u=t(38429),h=t(88920),b=t(65668),p=t(78272),g=t(96241);function v({faqs:e,title:a="Frequently Asked Questions",subtitle:t="Find answers to common questions"}){let d=(0,i.useRef)(null),l=(0,s.W)(d,{once:!0,amount:.2}),[o,c]=(0,i.useState)(null),x=e=>{c(o===e?null:e)},m={hidden:{opacity:0,y:20},visible:e=>({opacity:1,y:0,transition:{duration:.5,delay:.1*e,ease:"easeOut"}})};return(0,r.jsx)("section",{ref:d,id:"faqs",className:"py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden max-w-7xl mx-auto",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("div",{className:"text-center mb-12",children:(0,r.jsx)(u.A,{title:a,highlightWords:["Questions"],subtitle:t,size:"medium"})}),(0,r.jsx)(n.P.div,{className:"absolute top-20 left-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5",initial:{opacity:0,scale:.5,rotate:-10},animate:l?{opacity:1,scale:1,rotate:0}:{},transition:{duration:.7,delay:.2},children:(0,r.jsx)(b.A,{size:120})}),(0,r.jsx)(n.P.div,{className:"absolute top-1/3 right-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5",initial:{opacity:0,scale:.5,rotate:10},animate:l?{opacity:1,scale:1,rotate:0}:{},transition:{duration:.7,delay:.4},children:(0,r.jsx)(b.A,{size:80})}),(0,r.jsx)("div",{className:"space-y-4 relative z-10",children:e.map((e,a)=>(0,r.jsxs)(n.P.div,{variants:m,custom:a,initial:"hidden",animate:l?"visible":"hidden",className:"border border-border/50 rounded-2xl overflow-hidden bg-card/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsxs)("button",{onClick:()=>x(a),className:(0,g.cn)("flex items-center justify-between w-full p-6 text-left transition-all duration-300",o===a?"bg-gradient-to-r from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/5":"hover:bg-muted/30"),children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-foreground pr-4 leading-relaxed",children:e.question}),(0,r.jsx)(n.P.div,{animate:{rotate:180*(o===a)},transition:{duration:.3},className:(0,g.cn)("flex-shrink-0 ml-2 p-2 rounded-full transition-all duration-300",o===a?"bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] shadow-lg":"text-muted-foreground hover:bg-muted/50"),children:(0,r.jsx)(p.A,{size:20})})]}),(0,r.jsx)(h.N,{children:o===a&&(0,r.jsx)(n.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},className:"overflow-hidden",children:(0,r.jsx)("div",{className:"px-6 pb-6 border-t border-border/30",children:(0,r.jsx)(n.P.div,{initial:{y:10,opacity:0},animate:{y:0,opacity:1},transition:{duration:.3,delay:.1},className:"pt-4",children:(0,r.jsx)("p",{className:"text-muted-foreground leading-relaxed text-base",children:e.answer})})})})})]},e.id))}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:l?{opacity:1,y:0}:{},transition:{duration:.5,delay:.8},className:"text-center mt-16",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/5 rounded-2xl p-8 border border-[var(--brand-gold)]/20",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Still have questions?"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Our support team is ready to help you with any questions or concerns."}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Contact us at"," ",(0,r.jsx)("span",{className:"text-[var(--brand-gold)] font-semibold",children:"<EMAIL>"})]})]})})]})})}function f({title:e,description:a,icon:t,quickHelp:d,guideSections:h,faqs:b,relatedResources:p,navigationButtons:g}){let f=(0,i.useRef)(null),j=(0,s.W)(f,{once:!0,amount:.3}),y={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6}}},N={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return(0,r.jsxs)("div",{className:"min-h-screen py-12 px-4 md:px-6 lg:px-8 bg-background",children:[(0,r.jsxs)("section",{ref:f,className:"max-w-7xl mx-auto mb-16 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,r.jsx)("div",{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-3/4 h-3/4 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/3 rounded-full blur-3xl"})}),(0,r.jsxs)(n.P.div,{initial:"hidden",animate:j?"visible":"hidden",variants:y,className:"text-center relative z-10",children:[(0,r.jsx)(n.P.div,{initial:{opacity:0,x:-20},animate:j?{opacity:1,x:0}:{},transition:{duration:.5},children:(0,r.jsxs)(l(),{href:"/support",className:"inline-flex items-center text-muted-foreground hover:text-[var(--brand-gold)] mb-8 transition-colors group",children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform"}),"Back to Support Center"]})}),(0,r.jsx)(n.P.div,{className:"flex justify-center mb-8",initial:{opacity:0,scale:.8},animate:j?{opacity:1,scale:1}:{},transition:{duration:.6,delay:.2},children:(0,r.jsx)("div",{className:"bg-gradient-to-br from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/10 p-6 rounded-2xl backdrop-blur-sm border border-[var(--brand-gold)]/20",children:t})}),(0,r.jsx)(u.A,{title:e,highlightWords:[e.split(" ").pop()||""],subtitle:a,size:"medium",className:"mb-8"}),(0,r.jsx)(n.P.div,{className:"flex flex-wrap justify-center gap-3 mb-12",initial:{opacity:0,y:20},animate:j?{opacity:1,y:0}:{},transition:{duration:.6,delay:.6},children:g.map((e,a)=>(0,r.jsx)(n.P.div,{initial:{opacity:0,scale:.9},animate:j?{opacity:1,scale:1}:{},transition:{duration:.3,delay:.7+.1*a},children:(0,r.jsx)(x.$,{asChild:!0,variant:"outline",className:"border-[var(--brand-gold)]/50 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 hover:border-[var(--brand-gold)] transition-all duration-300",children:(0,r.jsx)("a",{href:e.href,children:e.label})})},e.href))}),(0,r.jsxs)(n.P.div,{className:"bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 max-w-4xl mx-auto shadow-lg",initial:{opacity:0,y:30},animate:j?{opacity:1,y:0}:{},transition:{duration:.6,delay:.8},children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-foreground mb-6 flex items-center justify-center",children:[(0,r.jsx)("span",{className:"bg-[var(--brand-gold)]/10 p-2 rounded-lg mr-3",children:"\uD83D\uDCA1"}),"Quick Help"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 text-left",children:d.map((e,a)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:a%2==0?-20:20},animate:j?{opacity:1,x:0}:{},transition:{duration:.5,delay:1+.1*a},children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground mb-4 text-lg",children:e.title}),(0,r.jsx)("ul",{className:"space-y-3",children:e.items.map((e,a)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0 mt-0.5 text-sm font-medium",children:"→"}),(0,r.jsx)("span",{className:"text-muted-foreground leading-relaxed",children:e.text})]},a))})]},a))})]})]})]}),(0,r.jsx)("section",{className:"max-w-7xl mx-auto mb-16",children:h.map((e,a)=>(0,r.jsxs)(n.P.div,{id:e.id,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:y,className:"mb-20",children:[(0,r.jsxs)(n.P.div,{className:"flex items-center mb-12",initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/10 p-4 rounded-xl mr-6 backdrop-blur-sm border border-[var(--brand-gold)]/20",children:e.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-foreground",children:e.title}),(0,r.jsx)("div",{className:"h-1 w-20 bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] rounded-full mt-2"})]})]}),(0,r.jsx)("div",{className:"grid gap-8",children:e.content.map((e,a)=>(0,r.jsx)(n.P.div,{variants:N,initial:"hidden",whileInView:"visible",viewport:{once:!0},transition:{delay:.1*a},className:"bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300",children:(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-foreground mb-6 flex items-center",children:[(0,r.jsx)("span",{className:"bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] rounded-lg w-8 h-8 flex items-center justify-center mr-3 text-sm font-bold",children:a+1}),e.title]}),(0,r.jsx)("ol",{className:"space-y-4 mb-6",children:e.steps.map((e,a)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] rounded-full w-7 h-7 flex items-center justify-center mr-4 flex-shrink-0 mt-0.5 text-sm font-semibold",children:a+1}),(0,r.jsx)("span",{className:"text-muted-foreground leading-relaxed",children:e})]},a))}),e.tip&&(0,r.jsxs)("div",{className:"bg-gradient-to-r from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/5 border-l-4 border-[var(--brand-gold)] p-6 rounded-r-xl",children:[(0,r.jsxs)("h4",{className:"text-[var(--brand-gold)] font-bold mb-3 flex items-center",children:[(0,r.jsx)("span",{className:"mr-2",children:"\uD83D\uDCA1"}),"Pro Tip"]}),(0,r.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:e.tip})]})]})},a))})]},e.id))}),(0,r.jsx)(v,{faqs:b}),p&&p.length>0&&(0,r.jsxs)("section",{className:"max-w-7xl mx-auto mb-16",children:[(0,r.jsx)(n.P.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:y,className:"text-center mb-12",children:(0,r.jsx)(u.A,{title:"Related Resources",highlightWords:["Resources"],subtitle:"Explore these guides to get the most out of your Dukancard experience",size:"medium"})}),(0,r.jsx)(n.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:p.map((e,a)=>(0,r.jsx)(n.P.div,{variants:N,children:(0,r.jsx)(l(),{href:e.href,className:"block h-full group",children:(0,r.jsxs)(m.Zp,{className:"h-full bg-card/50 backdrop-blur-sm border border-border/50 hover:border-[var(--brand-gold)]/50 hover:shadow-xl transition-all duration-300 cursor-pointer group-hover:scale-105",children:[(0,r.jsxs)(m.aR,{className:"pb-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/10 p-4 rounded-xl w-fit group-hover:scale-110 transition-transform duration-300",children:e.icon}),(0,r.jsx)(m.ZB,{className:"text-xl mt-4 group-hover:text-[var(--brand-gold)] transition-colors",children:e.title}),(0,r.jsx)(m.BT,{className:"text-muted-foreground leading-relaxed",children:e.description})]}),(0,r.jsx)(m.wL,{className:"pt-0",children:(0,r.jsxs)(x.$,{variant:"ghost",className:"w-full justify-start p-0 text-[var(--brand-gold)] hover:text-[var(--brand-gold)] hover:bg-transparent group-hover:translate-x-1 transition-transform",children:["View Guide ",(0,r.jsx)(c.A,{className:"ml-2 h-4 w-4"})]})})]})})},a))})]})]})}}};