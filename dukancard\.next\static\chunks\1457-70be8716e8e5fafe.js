"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1457],{41457:(e,r,t)=>{t.d(r,{A:()=>B});var s=t(95155);t(12115);var a=t(28695),l=t(60760),n=t(97168),d=t(88482),i=t(10238),o=t(81586),c=t(5937),u=t(90232),m=t(17580),x=t(37108),h=t(71539),f=t(5196),g=t(53311),p=t(51154),b=t(14186),j=t(47863),v=t(66474),N=t(85339),w=t(37777),y=t(53999),P=t(56018);let A={hidden:{opacity:0,y:20},visible:e=>({opacity:1,y:0,transition:{duration:.5,delay:.1*e,ease:"easeOut"}})},k=(e,r)=>{let t={className:(0,y.cn)("w-5 h-5",r?"text-[var(--brand-gold)]":"Enterprise Plan"===e?"text-purple-600":"text-muted-foreground")};switch(e){case"Free Plan":return(0,s.jsx)(i.A,{...t});case"Basic Plan":return(0,s.jsx)(o.A,{...t});case"Growth Plan":return(0,s.jsx)(c.A,{...t});case"Pro Plan":return(0,s.jsx)(u.A,{...t});case"Enterprise Plan":return(0,s.jsx)(m.A,{...t});default:return null}},C=e=>{switch(e){case"Growth Plan":return"Basic";case"Pro Plan":return"Growth";case"Enterprise Plan":return"Pro";default:return null}},E=e=>e.filter(e=>!e.includes("❌")),_=(e,r)=>{let t={className:(0,y.cn)("w-4 h-4 mr-2 flex-shrink-0",r?"text-[var(--brand-gold)]":"text-green-500")};return e.includes("Product")?(0,s.jsx)(x.A,{...t}):e.includes("Analytics")?(0,s.jsx)(h.A,{...t}):(0,s.jsx)(f.A,{...t})};function B(e){let{plan:r,onButtonClick:t,index:i,isLoading:o=!1,isCurrentPlan:c=!1,buttonTextOverride:m}=e,{featuresExpanded:h,toggleFeatures:f}=(0,P.k)(),B="enterprise"===r.id,F=!r.available&&!B||c||o,Z=r.button;m?Z=m:c&&(Z="Current Plan");let G=C(r.name),S=E(r.features);return(0,s.jsx)(a.P.div,{custom:i,variants:A,className:"w-full mx-auto",children:(0,s.jsxs)(d.Zp,{className:(0,y.cn)("bg-card border flex flex-col h-full w-full relative transition-all duration-300 rounded-xl p-3 md:p-4",r.featured?"border-[var(--brand-gold)] shadow-lg dark:shadow-[var(--brand-gold)]/15 pt-6 md:pt-8":B?"border-purple-500 shadow-lg dark:shadow-purple-500/15 pt-3 md:pt-4":"border-border pt-3 md:pt-4",!r.available&&"opacity-70"),children:[r.featured&&r.mostPopular&&(0,s.jsx)("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] px-4 py-1 rounded-full text-xs font-semibold shadow-md z-10",children:"Most Popular"}),(0,s.jsxs)("div",{className:"flex items-center mb-3",children:[(0,s.jsx)("div",{className:(0,y.cn)("p-1.5 rounded-full mr-2",r.featured?"bg-[var(--brand-gold)]/15":B?"bg-purple-500/15":"bg-muted"),children:k(r.name,r.featured||B)}),(0,s.jsx)("h3",{className:"text-lg md:text-xl font-semibold text-foreground",children:r.name})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex items-baseline gap-1",children:[(0,s.jsx)("span",{className:"text-2xl md:text-3xl font-bold text-foreground",children:r.price}),(0,s.jsx)("span",{className:"text-muted-foreground text-sm",children:r.period})]}),r.savings&&(0,s.jsxs)("span",{className:"text-green-600 text-sm block mt-1 font-medium",children:[(0,s.jsx)(g.A,{className:"inline-block w-3 h-3 mr-1"}),r.savings]}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2 text-sm",children:r.description})]}),(0,s.jsx)(n.$,{onClick:()=>!F&&(null==t?void 0:t(r)),size:"lg",className:(0,y.cn)("w-full mb-4 rounded-lg font-semibold flex items-center justify-center gap-2 transition-colors duration-200",F?"bg-muted text-muted-foreground cursor-not-allowed":B?"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white":r.featured?"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]":"bg-primary hover:bg-primary/90 text-primary-foreground"),disabled:F,children:o?(0,s.jsx)(p.A,{className:"w-5 h-5 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:[!r.available&&!c&&(0,s.jsx)(b.A,{className:"w-4 h-4"}),c&&(0,s.jsx)(u.A,{className:"w-4 h-4"}),Z]})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("button",{onClick:f,className:(0,y.cn)("flex items-center justify-center w-full mb-4 py-1 px-2 rounded-md text-sm font-medium transition-all","border border-transparent hover:border-border",h?"text-foreground bg-muted/50":"text-muted-foreground hover:text-foreground"),children:[(0,s.jsxs)("span",{className:"mr-1",children:[h?"Hide":"Show"," features"]}),h?(0,s.jsx)(j.A,{className:"w-4 h-4"}):(0,s.jsx)(v.A,{className:"w-4 h-4"})]}),!h&&(0,s.jsxs)("ul",{className:"space-y-3",children:[G&&(0,s.jsx)("li",{className:"flex items-start",children:(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[(0,s.jsx)("div",{className:(0,y.cn)("p-1 rounded-full mr-2 flex-shrink-0",r.featured?"bg-[var(--brand-gold)]/15":"bg-muted"),children:(0,s.jsx)(x.A,{className:(0,y.cn)("w-3 h-3",r.featured?"text-[var(--brand-gold)]":"text-foreground")})}),(0,s.jsxs)("span",{className:"font-medium",children:["Everything in ",G]})]})}),S.slice(0,G?2:3).map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center text-sm",children:[_(e,r.featured),(0,s.jsx)("span",{children:e})]},t)),S.length>(G?2:3)&&(0,s.jsxs)("li",{className:"text-sm text-muted-foreground text-center italic",children:[S.length-(G?2:3)," more features..."]})]}),(0,s.jsx)(l.N,{children:h&&(0,s.jsx)(a.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"overflow-hidden",children:(0,s.jsxs)("ul",{className:"space-y-3",children:[G&&(0,s.jsx)("li",{className:"flex items-start bg-muted/30 p-2 rounded-md",children:(0,s.jsxs)("div",{className:"flex items-center text-sm font-medium",children:[(0,s.jsx)("div",{className:(0,y.cn)("p-1 rounded-full mr-2 flex-shrink-0",r.featured?"bg-[var(--brand-gold)]/15":"bg-muted"),children:(0,s.jsx)(x.A,{className:(0,y.cn)("w-3 h-3",r.featured?"text-[var(--brand-gold)]":"text-foreground")})}),(0,s.jsxs)("span",{children:["Everything in ",G]})]})}),S.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center text-sm",children:[_(e,r.featured),(0,s.jsx)("span",{children:e})]},t))]})})})]}),!r.available&&!c&&!B&&(0,s.jsx)(w.Bc,{children:(0,s.jsxs)(w.m_,{children:[(0,s.jsx)(w.k$,{asChild:!0,children:(0,s.jsx)("div",{className:"absolute top-3 right-3 cursor-help",children:(0,s.jsx)(N.A,{className:"w-5 h-5 text-muted-foreground hover:text-foreground"})})}),(0,s.jsx)(w.ZI,{className:"bg-popover border-border text-popover-foreground",children:(0,s.jsx)("p",{children:"free"===r.id?"Free plan is automatically managed by the system.":"This plan is coming soon."})})]})})]})},r.id)}},56018:(e,r,t)=>{t.d(r,{S:()=>n,k:()=>d});var s=t(95155),a=t(12115);let l=(0,a.createContext)(void 0);function n(e){let{children:r}=e,[t,n]=(0,a.useState)(!1);return(0,s.jsx)(l.Provider,{value:{featuresExpanded:t,setFeaturesExpanded:n,toggleFeatures:()=>{n(!t)}},children:r})}function d(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("usePricingCard must be used within a PricingCardProvider");return e}},88482:(e,r,t)=>{t.d(r,{BT:()=>i,Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>n,wL:()=>c});var s=t(95155);t(12115);var a=t(53999);function l(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}function c(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}}}]);