"use strict";exports.id=3243,exports.ids=[3243,5453],exports.modules={8233:(e,t,r)=>{r.d(t,{KH:()=>a,jr:()=>s});var n=r(32032);async function a(e,t,r,a){try{let t=await (0,n.createClient)(),{data:r,error:a}=await t.from("processed_webhook_events").select("event_id, event_type, status, processed_at").eq("event_id",e).maybeSingle();if(a)return console.error("[RAZORPAY_WEBHOOK_IDEMPOTENCY] Error checking processed events:",a),!1;if(r)return console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Event ${e} already processed at ${r.processed_at} with status ${r.status}`),!0;return console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Event ${e} not found in processed events - allowing processing`),!1}catch(e){return console.error("[RAZORPAY_WEBHOOK_IDEMPOTENCY] Exception checking processed events:",e),!1}}async function s(e,t,r,a="processed",o,i,c){try{let s=await (0,n.createClient)(),c=null,d=i||null;t.startsWith("subscription.")?(c="subscription",d=d||r.payload.subscription?.id||null):t.startsWith("payment.")?(c="payment",d=d||r.payload.payment?.entity?.id||null):t.startsWith("refund.")?(c="refund",d=d||r.payload.refund?.entity?.id||null):t.startsWith("invoice.")&&(c="invoice",d=d||r.payload.payment?.entity?.id||null);let{error:l}=await s.from("processed_webhook_events").upsert({event_id:e,event_type:t,entity_type:c,entity_id:d,payload:{...r,_metadata:{entity_type:c,entity_id:d,processed_timestamp:new Date().toISOString()}},status:a,error_message:o,processed_at:new Date().toISOString(),created_at:new Date().toISOString()},{onConflict:"event_id"});if(l)return console.error("[RAZORPAY_WEBHOOK_IDEMPOTENCY] Error marking event as processed:",l),!1;return console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Successfully marked event ${e} as ${a}`),!0}catch(e){return console.error("[RAZORPAY_WEBHOOK_IDEMPOTENCY] Exception marking event as processed:",e),!1}}},24703:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{e_:()=>_,et:()=>u,rt:()=>l,u$:()=>d});var a=r(11337),s=r(65193),o=r(32032),i=r(28485),c=e([i]);async function d(e,t,n){let c=null;try{let t=e.payload.payment;if(!t)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let d=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Payment authorized: ${d}`);let l=t.entity.notes?.subscription_id;if(!l)return{success:!0,message:"Not a subscription payment, no update needed"};let u=(0,s.extractWebhookTimestamp)(e);c={subscriptionId:l,eventType:"payment.authorized",eventId:n||`payment_auth_${d}_${Date.now()}`,payload:e,webhookTimestamp:u};let _=await i.webhookProcessor.processWebhookEvent(c);if(!_.shouldProcess)return{success:_.success,message:_.message};let p=t.entity.notes?.old_subscription_id;p&&console.log(`[RAZORPAY_WEBHOOK] Plan switch payment detected: ${p} -> ${l}`);let{getSubscription:y}=await r.e(1546).then(r.bind(r,31546)),O=await y(l);O.success&&O.data||console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for ${l}:`,O.error);let m=O.data;try{let{updateSubscription:e}=await r.e(1546).then(r.bind(r,31546)),t=await e(l,{notes:{...m?.notes||{},last_payment_id:d}});t.success||console.error(`[RAZORPAY_WEBHOOK] Error updating subscription notes for ${l}:`,t.error)}catch(e){console.error("[RAZORPAY_WEBHOOK] Error updating subscription notes:",e)}let g=(0,o.createClient)(),E=await g;return await (0,s.nV)(E,l,a.SO._AUTHENTICATED,{last_payment_id:d,last_payment_date:new Date(1e3*t.entity.created_at).toISOString(),last_payment_method:t.entity.method,cancellation_requested_at:null,...m&&{razorpay_customer_id:m.customer_id||null,subscription_start_date:m.current_start?new Date(1e3*m.current_start).toISOString():null,subscription_expiry_time:m.current_end?new Date(1e3*m.current_end).toISOString():null,subscription_charge_time:m.charge_at?new Date(1e3*m.charge_at).toISOString():null}})}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling payment authorized:",e),{success:!1,message:`Error handling payment authorized: ${e instanceof Error?e.message:String(e)}`}}}async function l(e,t,n){try{let t=e.payload.payment;if(!t)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let n=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Payment captured: ${n}`);let i=t.entity.notes?.subscription_id;if(!i)return{success:!0,message:"Not a subscription payment, no update needed"};let{getSubscription:c}=await r.e(1546).then(r.bind(r,31546)),d=await c(i);d.success&&d.data||console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for ${i}:`,d.error);let l=d.data;try{let{updateSubscription:e}=await r.e(1546).then(r.bind(r,31546)),t=await e(i,{notes:{...l?.notes||{},last_payment_id:n}});t.success||console.error(`[RAZORPAY_WEBHOOK] Error updating subscription notes for ${i}:`,t.error)}catch(e){console.error("[RAZORPAY_WEBHOOK] Error updating subscription notes:",e)}let u=await (0,o.createClient)();return await (0,s.nV)(u,i,a.SO._ACTIVE,{last_payment_id:n,last_payment_date:new Date(1e3*t.entity.created_at).toISOString(),last_payment_method:t.entity.method,cancellation_requested_at:null,...l&&{razorpay_customer_id:l.customer_id||null,subscription_start_date:l.current_start?new Date(1e3*l.current_start).toISOString():null,subscription_expiry_time:l.current_end?new Date(1e3*l.current_end).toISOString():null,subscription_charge_time:l.charge_at?new Date(1e3*l.charge_at).toISOString():null}})}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling payment captured:",e),{success:!1,message:`Error handling payment captured: ${e instanceof Error?e.message:String(e)}`}}}async function u(e,t){try{let{webhookProcessor:n}=await Promise.resolve().then(r.bind(r,28485)),{extractWebhookTimestamp:a,SUBSCRIPTION_STATUS:s}=await Promise.resolve().then(r.bind(r,65193)),o=e.payload.payment;if(!o)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let i=o.entity.id;console.log(`[RAZORPAY_WEBHOOK] Payment failed: ${i}`);let c=o.entity.notes?.subscription_id;if(!c)return{success:!0,message:"Not a subscription payment, no update needed"};let d=a(e),l=await n.updateSubscriptionStatus(c,s.PAYMENT_FAILED,{last_payment_id:i,last_payment_date:new Date(1e3*o.entity.created_at).toISOString(),last_payment_method:o.entity.method},d);return l.success?await n.markEventAsSuccess(t.eventId,l.message):await n.markEventAsFailed(t.eventId,l.message),l}catch(n){console.error("[RAZORPAY_WEBHOOK] Error handling payment failed:",n);let e=`Error handling payment failed: ${n instanceof Error?n.message:String(n)}`;if(t){let{webhookProcessor:n}=await Promise.resolve().then(r.bind(r,28485));await n.markEventAsFailed(t.eventId,e)}return{success:!1,message:e}}}async function _(e,t){try{let{webhookProcessor:n}=await Promise.resolve().then(r.bind(r,28485)),{extractWebhookTimestamp:a,SUBSCRIPTION_STATUS:s}=await Promise.resolve().then(r.bind(r,65193)),o=e.payload.invoice,i=e.payload.payment;if(!o)return console.error("[RAZORPAY_WEBHOOK] Invoice data not found in payload"),{success:!1,message:"Invoice data not found in payload"};if(!i)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let c=o.entity.id,d=i.entity.id,l=o.entity.subscription_id;if(console.log(`[RAZORPAY_WEBHOOK] Invoice paid: ${c}, Payment: ${d}, Subscription: ${l}`),!l)return{success:!0,message:"Not a subscription invoice, no update needed"};let u=i.entity.method,_=a(e),p=await n.updateSubscriptionStatus(l,s.ACTIVE,{last_payment_id:d,last_payment_date:new Date(1e3*i.entity.created_at).toISOString(),last_payment_method:u,cancellation_requested_at:null},_);return p.success?await n.markEventAsSuccess(t.eventId,p.message):await n.markEventAsFailed(t.eventId,p.message),p}catch(n){console.error("[RAZORPAY_WEBHOOK] Error handling invoice paid:",n);let e=`Error handling invoice paid: ${n instanceof Error?n.message:String(n)}`;if(t){let{webhookProcessor:n}=await Promise.resolve().then(r.bind(r,28485));await n.markEventAsFailed(t.eventId,e)}return{success:!1,message:e}}}i=(c.then?(await c)():c)[0],n()}catch(e){n(e)}})},33243:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{handleRazorpayWebhook:()=>a.S});var a=r(33419),s=e([a]);a=(s.then?(await s)():s)[0],n()}catch(e){n(e)}})},33419:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{S:()=>_});var a=r(32032),s=r(95453),o=r(11337),i=r(42651),c=r(8233),d=r(67332),l=r(92021),u=e([d,l]);async function _(e,t,n,u,_){try{let p,y,O=e.payload.subscription?.id||e.payload.payment?.entity?.notes?.subscription_id||void 0;if(e.event.startsWith("subscription.")){let{extractEntityId:t}=await r.e(5357).then(r.bind(r,55357));p=t(e)||void 0}else e.event.startsWith("payment.")?p=e.payload.payment?.entity?.id:e.event.startsWith("refund.")?p=e.payload.refund?.entity?.id:e.event.startsWith("invoice.")&&(p=e.payload.payment?.entity?.id);if(!n&&t&&""!==t.trim()){let r=process.env.RAZORPAY_WEBHOOK_SECRET;if(!r){console.error("[RAZORPAY_WEBHOOK] Webhook secret not configured");let t=await (0,i.m0)(e.event,p,O,"Webhook secret not configured",e);return{success:!1,message:"Webhook secret not configured",error_id:t.error_id}}let n=u||JSON.stringify(e);if(!(0,s.t6)(n,t,r)){console.error("[RAZORPAY_WEBHOOK] Invalid webhook signature");let t=await (0,i.m0)(e.event,p,O,"Invalid webhook signature",e);return{success:!1,message:"Invalid webhook signature",error_id:t.error_id}}}else n||""!==t||console.log("[RAZORPAY_WEBHOOK] Skipping signature verification for testing");let m=e.event,g=await (0,a.createClient)();if(!n&&_&&await (0,c.KH)(_,m,e,g))return{success:!0,message:"Event already processed (idempotent)"};try{switch(m){case o.mz._SUBSCRIPTION_AUTHENTICATED:y=await (0,l.h)(e,g,_);break;case o.mz._SUBSCRIPTION_ACTIVATED:y=await (0,l.J0)(e,g,_);break;case o.mz._SUBSCRIPTION_CHARGED:y=await (0,l.Y4)(e,g,_);break;case o.mz._SUBSCRIPTION_PENDING:y=await (0,l.VA)(e,g,_);break;case o.mz._SUBSCRIPTION_HALTED:y=await (0,l.s1)(e,g,_);break;case o.mz._SUBSCRIPTION_CANCELLED:y=await (0,l.X7)(e,g,_);break;case o.mz._SUBSCRIPTION_COMPLETED:y=await (0,l.je)(e,g,_);break;case o.mz._SUBSCRIPTION_EXPIRED:y=await (0,l.$O)(e,g,_);break;case o.mz._SUBSCRIPTION_UPDATED:y=await (0,l.Tb)(e,g,_);break;case o.mz._PAYMENT_AUTHORIZED:y=await (0,d.u$)(e,g,_);break;case o.mz._PAYMENT_CAPTURED:y=await (0,d.rt)(e,g,_);break;case o.mz._PAYMENT_FAILED:y=await (0,d.et)(e,{eventId:_||`payment_failed_${Date.now()}`});break;case o.mz._INVOICE_PAID:y=await (0,d.e_)(e,{eventId:_||`invoice_paid_${Date.now()}`});break;case o.mz._REFUND_CREATED:y=await (0,d.j_)(e,g,_);break;case o.mz._REFUND_PROCESSED:y=await (0,d.F9)(e,g,_);break;case o.mz._REFUND_FAILED:y=await (0,d.eA)(e,g,_);break;default:y={success:!0,message:"Event acknowledged but not processed"}}return n&&y.success&&await (0,i.z5)(n,"resolved"),!n&&y.success&&_&&await (0,c.jr)(_,m,e,"processed",void 0,p),y}catch(a){if(console.error(`[RAZORPAY_WEBHOOK] Error processing ${m} webhook:`,a),n){let e=a instanceof Error?a.message:String(a);return await (0,i.z5)(n,"retrying",void 0,e),{success:!1,message:`Error processing webhook: ${e}`,error_id:n}}let t=a instanceof Error?a.message:String(a),r=await (0,i.m0)(m,p,O,t,e,g);return{success:!1,message:`Error processing webhook: ${t}`,error_id:r.error_id}}}catch(n){console.error("[RAZORPAY_WEBHOOK] Unhandled error in webhook handler:",n);let t=n instanceof Error?n.message:String(n),r=await (0,i.m0)(e.event||"unknown",void 0,void 0,t,e);return{success:!1,message:`Unhandled error in webhook handler: ${t}`,error_id:r.error_id}}}[d,l]=u.then?(await u)():u,n()}catch(e){n(e)}})},37931:(e,t,r)=>{r.a(e,async(e,t)=>{try{var n=r(92021),a=e([n]);n=(a.then?(await a)():a)[0],t()}catch(e){t(e)}})},42651:(e,t,r)=>{r.d(t,{gz:()=>o,m0:()=>a,z5:()=>s});var n=r(32032);async function a(e,t,r,a,s,o){try{let i=o||await (0,n.createClient)(),c={event_type:e,event_id:t,subscription_id:r,error_message:a,payload:s,retry_count:0,status:"pending",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:d,error:l}=await i.from("webhook_error_logs").insert(c).select("id").single();if(l)return console.error("[RAZORPAY_WEBHOOK] Error logging webhook error:",l),{success:!1};return{success:!0,error_id:d?.id}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Exception logging webhook error:",e),{success:!1}}}async function s(e,t,r,a,s){try{let o=s||await (0,n.createClient)(),i={status:t,updated_at:new Date().toISOString()};void 0!==r&&(i.retry_count=r),a&&(i.error_message=a);let{error:c}=await o.from("webhook_error_logs").update(i).eq("id",e);if(c)return console.error("[RAZORPAY_WEBHOOK] Error updating webhook error log:",c),{success:!1};return{success:!0}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Exception updating webhook error log:",e),{success:!1}}}async function o(e=3,t){try{let r=t||await (0,n.createClient)(),{data:a,error:s}=await r.from("webhook_error_logs").select("*").in("status",["pending","retrying"]).lt("retry_count",e).order("created_at",{ascending:!0});if(s)return console.error("[RAZORPAY_WEBHOOK] Error getting pending webhook errors:",s),[];return(a||[]).map(e=>({...e,event_id:e.event_id||void 0}))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Exception getting pending webhook errors:",e),[]}}},61225:(e,t,r)=>{async function n(e,t,r){try{let t=e.payload.refund;if(!t)return console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload"),{success:!1,message:"Refund data not found in payload"};let r=t.entity.id,n=t.entity.payment_id,a=t.entity.amount/100,s=t.entity.currency;return t.entity.status,console.log(`[RAZORPAY_WEBHOOK] Refund created: ${r} for payment ${n} - ${a} ${s}`),t.entity.speed_requested,t.entity.notes&&Object.keys(t.entity.notes).length,{success:!0,message:`Refund created event processed for refund ${r}`}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling refund created:",e),{success:!1,message:`Error handling refund created: ${e instanceof Error?e.message:String(e)}`}}}async function a(e,t,r){try{let t=e.payload.refund;if(!t)return console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload"),{success:!1,message:"Refund data not found in payload"};let r=t.entity.id,n=t.entity.status;return t.entity.speed_processed,console.log(`[RAZORPAY_WEBHOOK] Refund processed: ${r} - status: ${n}`),{success:!0,message:`Refund processed event handled for refund ${r}`}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling refund processed:",e),{success:!1,message:`Error handling refund processed: ${e instanceof Error?e.message:String(e)}`}}}async function s(e,t,r){try{let t=e.payload.refund;if(!t)return console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload"),{success:!1,message:"Refund data not found in payload"};let r=t.entity.id,n=t.entity.status,a=t.entity.payment_id;return console.log(`[RAZORPAY_WEBHOOK] Refund failed: ${r} for payment ${a} - status: ${n}`),t.entity.notes&&Object.keys(t.entity.notes).length,{success:!0,message:`Refund failed event handled for refund ${r}`}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling refund failed:",e),{success:!1,message:`Error handling refund failed: ${e instanceof Error?e.message:String(e)}`}}}r.d(t,{F9:()=>a,eA:()=>s,j_:()=>n})},67332:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{F9:()=>i.F9,eA:()=>i.eA,e_:()=>o.e_,et:()=>o.et,j_:()=>i.j_,rt:()=>o.rt,u$:()=>o.u$});var a=r(33419),s=r(37931),o=r(24703),i=r(61225);r(65193);var c=e([a,s,o]);[a,s,o]=c.then?(await c)():c,n()}catch(e){n(e)}})},92021:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{$O:()=>u.handleSubscriptionExpired,J0:()=>s.J,Tb:()=>_.handleSubscriptionUpdated,VA:()=>i.V,X7:()=>d.handleSubscriptionCancelled,Y4:()=>o.Y,h:()=>a.h,je:()=>l.handleSubscriptionCompleted,s1:()=>c.handleSubscriptionHalted});var a=r(10567),s=r(28539),o=r(30260),i=r(14055),c=r(5432),d=r(69423),l=r(301),u=r(47249),_=r(75447),p=e([a,s,o,i,c,d,l,u,_]);[a,s,o,i,c,d,l,u,_]=p.then?(await p)():p,n()}catch(e){n(e)}})},95453:(e,t,r)=>{r.d(t,{ST:()=>s,bG:()=>i,t6:()=>c});var n=r(55511),a=r.n(n);let s="https://api.razorpay.com/v2",o=()=>{let e,t;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",t=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!t)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!t,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:t}},i=()=>{let{keyId:e,keySecret:t}=o(),r=Buffer.from(`${e}:${t}`).toString("base64");return{Authorization:`Basic ${r}`,"Content-Type":"application/json"}},c=(e,t,r)=>{try{let n=a().createHmac("sha256",r).update(e).digest("hex");return a().timingSafeEqual(Buffer.from(t),Buffer.from(n))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}}};