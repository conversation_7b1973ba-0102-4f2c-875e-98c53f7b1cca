"use strict";exports.id=1219,exports.ids=[1219],exports.modules={21219:(o,e,a)=>{a.d(e,{openRazorpaySubscriptionCheckout:()=>l});let r=()=>new Promise(o=>{if(console.log("[RAZORPAY_SDK] Checking if Razorpay SDK is already loaded..."),window.Razorpay){console.log("[RAZORPAY_SDK] Razorpay SDK already loaded"),o(!0);return}console.log("[RAZORPAY_SDK] Loading Razorpay SDK from CDN...");let e=document.createElement("script");e.src="https://checkout.razorpay.com/v1/checkout.js",e.async=!0,e.onload=()=>{console.log("[RAZORPAY_SDK] Razorpay SDK loaded successfully from CDN"),o(!0)},e.onerror=()=>{console.error("[RAZORPAY_ERROR] Failed to load <PERSON><PERSON>pay SDK from CDN"),o(!1)},document.body.appendChild(e),console.log("[RAZORPAY_SDK] Script tag added to document body")}),n=async o=>{try{if(console.log("[RAZORPAY_SDK] Loading Razorpay SDK..."),!await r())throw console.error("[RAZORPAY_SDK] Failed to load Razorpay SDK"),Error("Failed to load Razorpay SDK");console.log("[RAZORPAY_SDK] Razorpay SDK loaded successfully"),console.log("[RAZORPAY_SDK] window.Razorpay available:",!!window.Razorpay),console.log("[RAZORPAY_SDK] Creating Razorpay instance with options:",{key:o.key,subscription_id:o.subscription_id,name:o.name,description:o.description});let e=new window.Razorpay(o);return console.log("[RAZORPAY_SDK] Razorpay instance created successfully:",!!e),e}catch(o){return console.error("[RAZORPAY_ERROR] Error initializing Razorpay checkout:",o),null}},l=async(o,e,a,r=6e5)=>new Promise(async(l,i)=>{let s=null,t=!1;s=setTimeout(()=>{t||(t=!0,i({timeout:!0,message:`Payment timeout after ${r/1e3} seconds. Please try again.`}))},r);let c=o=>{t||(t=!0,s&&clearTimeout(s),l(o))},R=o=>{t||(t=!0,s&&clearTimeout(s),i(o))};try{console.log("[RAZORPAY_SDK] Initializing Razorpay checkout with options:",{keyId:o,subscriptionId:e,name:a.name,description:a.description||"Subscription Payment"});let r=await n({key:o,subscription_id:e,name:a.name,description:a.description||"Subscription Payment",prefill:a.prefill,notes:a.notes,theme:a.theme||{color:"#6366f1"},handler:o=>{console.log("[RAZORPAY_SDK] Payment handler called with response:",o),c(o)},modal:{ondismiss:()=>{console.log("[RAZORPAY_SDK] Modal dismissed by user"),R({cancelled:!0,message:"Payment cancelled by user"})},escape:!1,animation:!0},amount:0,currency:"INR"});if(!r){console.error("[RAZORPAY_ERROR] Failed to initialize Razorpay checkout"),R(Error("Failed to initialize Razorpay checkout"));return}console.log("[RAZORPAY_SDK] Razorpay instance created successfully, opening modal..."),r.open(),console.log("[RAZORPAY_SDK] razorpay.open() called successfully")}catch(o){console.error("[RAZORPAY_SDK] Error in openRazorpaySubscriptionCheckout:",o),R(o)}})}};