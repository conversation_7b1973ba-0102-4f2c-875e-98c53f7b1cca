"use strict";exports.id=5265,exports.ids=[5265],exports.modules={21121:(e,s,a)=>{a.d(s,{s:()=>w});var t=a(60687),r=a(43210),l=a(85814),n=a.n(l),i=a(38782),d=a(24861),o=a(47696),c=a(59821),u=a(17971),m=a(40083),x=a(42154),h=a(70373),b=a(55629),g=a(24934),p=a(96241);let j=e=>{if(!e)return"?";let s=e.trim().split(/\s+/);return 1===s.length&&s[0]?s[0].charAt(0).toUpperCase():s.length>1&&s[0]&&s[s.length-1]?s[0].charAt(0).toUpperCase()+s[s.length-1].charAt(0).toUpperCase():"?"};function N({user:e,businessName:s}){let{isMobile:a}=(0,d.cL)(),l=j(s),[n,i]=(0,r.useState)(!1),o=e.avatar,c=s||"Business",N=e.name||"Member";return(0,t.jsx)(d.wZ,{className:(0,p.cn)(a?"pb-16":n?"pb-14":""),children:(0,t.jsx)(d.FX,{children:(0,t.jsxs)(b.rI,{children:[(0,t.jsx)(b.ty,{asChild:!0,children:(0,t.jsxs)(d.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,t.jsxs)(h.eu,{className:"h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30",children:[o?(0,t.jsx)(h.BK,{src:o,alt:c}):null,(0,t.jsx)(h.q5,{className:"rounded-lg bg-muted border border-[var(--brand-gold)]/30 text-xs",children:l})]}),(0,t.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,t.jsx)("span",{className:"truncate font-semibold",children:c}),(0,t.jsx)("span",{className:"truncate text-xs text-muted-foreground",children:N})]}),(0,t.jsx)(u.A,{className:"ml-auto size-4"})]})}),(0,t.jsxs)(b.SQ,{className:"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg",side:a?"bottom":"right",align:"end",sideOffset:4,children:[(0,t.jsx)(b.lp,{className:"p-0 font-normal",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,t.jsxs)(h.eu,{className:"h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30",children:[o?(0,t.jsx)(h.BK,{src:o,alt:c}):null,(0,t.jsx)(h.q5,{className:"rounded-lg bg-muted border border-[var(--brand-gold)]/30 text-xs",children:l})]}),(0,t.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,t.jsx)("span",{className:"truncate font-semibold",children:c}),(0,t.jsx)("span",{className:"truncate text-xs text-muted-foreground",children:N})]})]})}),(0,t.jsx)(b.mB,{}),(0,t.jsx)("form",{action:x.B,className:"w-full px-2 py-1.5",children:(0,t.jsxs)(g.$,{variant:"ghost",type:"submit",className:"w-full justify-start p-0 h-auto font-normal cursor-pointer",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Log out"]})})]})]})})})}var f=a(14952),v=a(8936);function w({businessName:e,logoUrl:s,memberName:a,userPlan:l,...u}){let m=[{title:"Feed",icon:"LayoutList",url:"/dashboard/business",items:[]},{title:"Overview",icon:"LayoutDashboard",url:"/dashboard/business/overview",items:[]},{title:"Business Management",icon:"Store",items:[{title:"Manage Card",icon:"CreditCard",url:"/dashboard/business/card"},{title:"Products & Services",icon:"Package",url:"/dashboard/business/products"},{title:"Gallery",icon:"Image",url:"/dashboard/business/gallery",badge:"free"===l?"1 Photo":"basic"===l?"3 Photos":void 0,badgeVariant:"secondary"}]},{title:"Insights",icon:"BarChart3",items:[{title:"Analytics",icon:"BarChart3",url:"/dashboard/business/analytics",badge:"free"===l?"Basic+":void 0,badgeVariant:"upgrade"}]},{title:"Social",icon:"Users",items:[{title:"Likes",icon:"Heart",url:"/dashboard/business/likes"},{title:"Subscriptions",icon:"Users",url:"/dashboard/business/subscriptions"},{title:"Reviews",icon:"Star",url:"/dashboard/business/reviews"}]},{title:"Account",icon:"User",items:[{title:"Manage Plan",icon:"WalletCards",url:"/dashboard/business/plan"},{title:"Settings",icon:"Settings",url:"/dashboard/business/settings"}]}];return(0,t.jsxs)(d.Bx,{collapsible:"icon",...u,children:[(0,t.jsx)(d.Gh,{className:"border-b border-border/50",children:(0,t.jsx)("div",{className:"flex items-center px-2 py-4",children:(0,t.jsxs)(n(),{href:"/?view=home",className:"flex flex-col group transition-all duration-200 hover:opacity-80",children:[(0,t.jsxs)("span",{className:"font-bold text-lg text-[var(--brand-gold)]",children:["Dukan",(0,t.jsx)("span",{className:"text-foreground",children:"card"})]}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:"Business Portal"})]})})}),(0,t.jsx)(d.Yv,{className:"px-2",children:(0,t.jsx)(d.Cn,{children:(0,t.jsx)(d.wZ,{className:"space-y-1",children:m.map((e,s)=>0===s||1===s?(0,t.jsx)(d.FX,{children:(0,t.jsx)(d.Uj,{asChild:!0,tooltip:e.title,className:"h-10 rounded-lg",children:(0,t.jsxs)(i.d,{href:e.url||"#",className:"flex items-center gap-3",children:[e.icon&&v.K[e.icon]&&r.createElement(v.K[e.icon],{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:e.title})]})})},e.title):(0,t.jsx)(o.Nt,{defaultOpen:2===s,className:"group/collapsible",children:(0,t.jsxs)(d.FX,{children:[(0,t.jsx)(o.R6,{asChild:!0,children:(0,t.jsxs)(d.Uj,{className:"h-10 rounded-lg",children:[e.icon&&v.K[e.icon]&&r.createElement(v.K[e.icon],{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:e.title}),(0,t.jsx)(f.A,{className:"ml-auto h-4 w-4 text-muted-foreground group-data-[state=open]/collapsible:rotate-90"})]})}),(0,t.jsx)(o.Ke,{className:"transition-all duration-200",children:(0,t.jsx)(d.q9,{className:"ml-4 mt-1 space-y-1 border-l border-border/30 pl-4",children:e.items.map(e=>(0,t.jsx)(d.Fg,{children:(0,t.jsx)(d.Cp,{asChild:!0,className:"h-9 rounded-md",children:(0,t.jsxs)(i.d,{href:e.url||"#",className:"flex items-center gap-3",children:[e.icon&&v.K[e.icon]&&r.createElement(v.K[e.icon],{className:"h-3.5 w-3.5 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.title}),e.badge&&(0,t.jsx)(c.E,{variant:"upgrade"===e.badgeVariant?"default":e.badgeVariant||"default",className:`ml-auto text-xs ${"upgrade"===e.badgeVariant&&"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"}`,children:e.badge})]})})},e.title))})})]})},e.title))})})}),(0,t.jsx)(d.CG,{className:"border-t border-border/50 p-2",children:(0,t.jsx)(N,{user:{name:a,avatar:s},businessName:e})}),(0,t.jsx)(d.jM,{})]})}},52529:(e,s,a)=>{a.d(s,{Q:()=>i});var t=a(60687),r=a(43210);let l=(0,a(30036).default)(async()=>{},{loadableGenerated:{modules:["app\\context\\PaymentMethodLimitationsContext.tsx -> @/app/(dashboard)/dashboard/business/plan/components/PaymentMethodLimitationsDialog"]},ssr:!1,loading:()=>null}),n=(0,r.createContext)(void 0);function i({children:e}){let[s,a]=(0,r.useState)(!1),[i,d]=(0,r.useState)(!1),[o,c]=(0,r.useState)("UPI"),[u,m]=(0,r.useState)(null);return(0,t.jsxs)(n.Provider,{value:{openDialog:(e="UPI",s)=>{c(e),d(!0),s&&m(()=>s)}},children:[e,s&&(0,t.jsx)(l,{open:i,onOpenChange:d,onContinue:()=>{d(!1),u&&(u(),m(null))},paymentMethod:o})]})}}};