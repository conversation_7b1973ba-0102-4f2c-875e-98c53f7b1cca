exports.id=3037,exports.ids=[3037],exports.modules={4952:(e,t,r)=>{"use strict";r.d(t,{Y:()=>a});var s=r(43210);function a(e={}){let{threshold:t=10,initialDirection:r="up"}=e,[n,i]=(0,s.useState)({scrollDirection:r,isScrolled:!1,scrollY:0});return n}},15251:(e,t,r)=>{"use strict";r.d(t,{a:()=>a});var s=r(43210);function a(){let[e,t]=(0,s.useState)(void 0);return e??!1}},24934:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>o});var s=r(60687);r(43210);var a=r(8730),n=r(24224),i=r(96241);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let d=n?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...l})}},35069:(e,t,r)=>{Promise.resolve().then(r.bind(r,97102)),Promise.resolve().then(r.bind(r,88452)),Promise.resolve().then(r.bind(r,80363)),Promise.resolve().then(r.bind(r,23392))},37826:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>m,Es:()=>g,HM:()=>c,L3:()=>f,c7:()=>h,lG:()=>o,rr:()=>p,zM:()=>l});var s=r(60687);r(43210);var a=r(10991),n=r(11860),i=r(96241);function o({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({...e}){return(0,s.jsx)(a.bm,{"data-slot":"dialog-close",...e})}function u({className:e,...t}){return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function m({className:e,children:t,hideClose:r=!1,...o}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(u,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,!r&&(0,s.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function g({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,s.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function p({className:e,...t}){return(0,s.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},38398:(e,t,r)=>{"use strict";r.d(t,{U:()=>a});var s=r(79384);function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";return e&&t?(0,s.createBrowserClient)(e,t):(console.error("Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined."),(0,s.createBrowserClient)("",""))}},38606:(e,t,r)=>{"use strict";r.d(t,{default:()=>I});var s=r(60687),a=r(43210),n=r(85814),i=r.n(n),o=r(16189),l=r(77882),d=r(96241),c=r(15251),u=r(32192),m=r(99270),h=r(16103),g=r(46001),f=r(58869),p=r(59821),x=r(37826),b=r(24934),v=r(51361),j=r(16023),y=r(52581),w=r(14765);function N(e){let t=function(e){let t;if(!e||"string"!=typeof e)return{isValid:!1,error:"Invalid QR code data"};let r=e.trim();if(!r)return{isValid:!1,error:"Empty QR code data"};try{let e=r.startsWith("http")?r:`https://${r}`;t=new URL(e)}catch(e){return{isValid:!1,error:"QR code does not contain a valid URL"}}if(!["dukancard.in","www.dukancard.in"].includes(t.hostname.toLowerCase()))return{isValid:!1,error:"QR code is not from Dukancard"};let s=t.pathname.split("/").filter(e=>e.length>0);if(0===s.length)return{isValid:!1,error:"QR code does not contain a business profile URL"};let a=s[0],n=function(e){if(!e||"string"!=typeof e)return{isValid:!1,error:"Business slug is required"};let t=e.trim();return t?t.length<3||t.length>50?{isValid:!1,error:"Business slug must be between 3 and 50 characters"}:/^[a-z0-9-]+$/.test(t)?t.startsWith("-")||t.endsWith("-")?{isValid:!1,error:"Business slug cannot start or end with a hyphen"}:t.includes("--")?{isValid:!1,error:"Business slug cannot contain consecutive hyphens"}:{isValid:!0}:{isValid:!1,error:"Business slug can only contain lowercase letters, numbers, and hyphens"}:{isValid:!1,error:"Business slug cannot be empty"}}(a);return n.isValid?{isValid:!0,businessSlug:a,url:t.toString()}:{isValid:!1,error:n.error||"Invalid business URL format"}}(e);if(!t.isValid){let e=t.error;return t.error?.includes("not from Dukancard")?e="This QR code is not from Dukancard. Please scan a valid Dukancard business QR code.":t.error?.includes("not contain a valid URL")?e="Invalid QR code format. Please scan a valid Dukancard business QR code.":t.error?.includes("business profile URL")&&(e="This QR code does not link to a business profile. Please scan a valid Dukancard business QR code."),{...t,error:e}}return t}function k(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia&&"function"==typeof navigator.mediaDevices.getUserMedia)}function C(){return window.isSecureContext||"https:"===location.protocol||"localhost"===location.hostname}async function S(e={video:!0}){if(!k())throw Error("Camera not supported in this browser");if(!C())throw Error("Camera access requires HTTPS or localhost");try{let t=await navigator.mediaDevices.getUserMedia(e),r={hasCamera:!0,hasPermission:!0,isSecureContext:C(),supportedConstraints:navigator.mediaDevices.getSupportedConstraints()};return{stream:t,capabilities:r}}catch(s){let e="Failed to access camera",t=!1;s instanceof Error&&("NotAllowedError"===s.name?(e="Camera access denied by user",t=!1):"NotFoundError"===s.name?e="No camera found on this device":"NotReadableError"===s.name?e="Camera is already in use by another application":"OverconstrainedError"===s.name?e="Camera does not support the requested constraints":"SecurityError"===s.name&&(e="Camera access blocked due to security restrictions"));let r={hasCamera:s instanceof Error&&"NotFoundError"!==s.name,hasPermission:t,isSecureContext:C(),supportedConstraints:navigator.mediaDevices?.getSupportedConstraints()||null,error:e};throw{error:e,capabilities:r}}}r(93099);let R=({onScanSuccess:e,onScanError:t,onClose:r,className:n=""})=>{let i=(0,a.useRef)(null),o=(0,a.useRef)(null),[l,d]=(0,a.useState)(!1),[c,u]=(0,a.useState)(null),[m,h]=(0,a.useState)(null),[g,f]=(0,a.useState)(!1),p="qr-scanner-region";return((0,a.useEffect)(()=>{(async()=>{try{let{stream:e,capabilities:r}=await S();h(r),e.getTracks().forEach(e=>e.stop()),r.error&&(u(r.error),t?.(r.error))}catch(r){let e=r.error||(r instanceof Error?r.message:"Failed to get camera access");h(r.capabilities||{hasCamera:!1,hasPermission:!1,isSecureContext:!1,supportedConstraints:null,error:e}),u(e),t?.(e)}})()},[t]),(0,a.useEffect)(()=>{if(m&&!m.error&&!l)return(async()=>{try{d(!0),u(null);let r={fps:10,qrbox:{width:250,height:250},aspectRatio:1,disableFlip:!1,supportedScanTypes:[w.a8.SCAN_TYPE_CAMERA],showTorchButtonIfSupported:!0,showZoomSliderIfSupported:!1,defaultZoomValueIfSupported:1,colorScheme:"dark"},s=async r=>{if(!g){f(!0);try{let s=N(r);if(!s.isValid){let e=s.error||"Invalid QR code";u(e),t?.(e),setTimeout(()=>{f(!1)},2e3);return}let a=s.businessSlug;e(a)}catch(r){let e=r instanceof Error?r.message:"Failed to process QR code";u(e),t?.(e),f(!1)}}},a=new w.TF(p,r,!1);i.current=a,a.render(s,e=>{console.debug("QR scan error:",e)})}catch(r){let e=r instanceof Error?r.message:"Failed to initialize QR scanner";u(e),t?.(e),d(!1)}})(),()=>{i.current&&(i.current.clear().catch(e=>{console.error("Error clearing QR scanner:",e)}),i.current=null),d(!1),f(!1)}},[m,e,t,g,l]),(0,a.useEffect)(()=>()=>{i.current&&i.current.clear().catch(e=>{console.error("Error clearing QR scanner on unmount:",e)})},[]),c)?(0,s.jsxs)("div",{className:`flex flex-col items-center justify-center p-8 text-center ${n}`,children:[(0,s.jsx)("div",{className:"text-red-500 mb-4",children:(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"Camera Access Required"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:c}),r&&(0,s.jsx)("button",{onClick:r,className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors",children:"Close"})]}):m?(0,s.jsxs)("div",{className:`relative ${n}`,children:[g&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black/50 flex items-center justify-center z-10 rounded-lg",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-foreground",children:"Processing QR code..."})]})}),(0,s.jsx)("div",{id:p,ref:o,className:"w-full"}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Point your camera at a Dukancard QR code"})})]}):(0,s.jsx)("div",{className:`flex items-center justify-center p-8 ${n}`,children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Checking camera availability..."})]})})},P=({isOpen:e,onClose:t,onScanSuccess:r})=>{let[n,i]=(0,a.useState)(!1),[o,l]=(0,a.useState)("camera"),d=(0,a.useCallback)(e=>{i(!0),setTimeout(()=>{y.oR.success("QR code scanned successfully!"),r(e),t(),i(!1)},500)},[r,t]),c=(0,a.useCallback)(e=>{console.error("QR scan error:",e),y.oR.error(e)},[]),u=(0,a.useCallback)(async e=>{let t=e.target.files?.[0];if(t){if(!t.type.startsWith("image/"))return void y.oR.error("Please select an image file");i(!0);try{let e;try{e=new w.j9("qr-file-scanner-region");let r=await e.scanFile(t);console.log("Html5Qrcode scan result:",r);let s=N(r);if(console.log("QR code validation result:",s),!s.isValid){let e=s.error||"Invalid QR code";y.oR.error(e),i(!1);return}let a=s.businessSlug;d(a)}catch(t){console.error("QR code scan from image failed:",t);let e="Failed to process image";"string"==typeof t?e=t.includes("QR code not found")?"No Dukancard QR code found in the image. Please try another image.":t.includes("no multiformat readers")||t.includes("no multiformat readers")?"No QR code found in the image. Please ensure it's a clear image of a Dukancard QR code.":t.includes("Image parse error")?"Could not read the image file. Please ensure it's a valid image.":t:t instanceof Error&&(e=t.message),y.oR.error(e),i(!1)}finally{if(e)try{e.clear()}catch(e){console.error("Error clearing html5QrCode",e)}}}catch(e){console.error("Outer catch: Failed to process image",e),y.oR.error("Failed to process image"),i(!1)}e.target.value=""}},[]),m=(0,a.useCallback)(()=>{n||t()},[n,t]);return(0,s.jsx)(x.lG,{open:e,onOpenChange:m,children:(0,s.jsxs)(x.Cf,{className:"sm:max-w-md w-full max-h-[90vh] overflow-hidden",children:[(0,s.jsx)(x.c7,{className:"pb-4",children:(0,s.jsx)(x.L3,{className:"text-lg font-semibold",children:"Scan QR Code"})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex rounded-lg bg-muted p-1",children:[(0,s.jsxs)("button",{onClick:()=>l("camera"),disabled:n,className:`flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${"camera"===o?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),"Camera"]}),(0,s.jsxs)("button",{onClick:()=>l("upload"),disabled:n,className:`flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${"upload"===o?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),"Upload"]})]}),(0,s.jsx)("div",{className:"relative",children:"camera"===o?(0,s.jsx)(R,{onScanSuccess:d,onScanError:c,onClose:m,className:"min-h-[300px]"}):(0,s.jsxs)("div",{className:"min-h-[300px] flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-8",children:[(0,s.jsx)(j.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Upload QR Code Image"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:"Select an image containing a Dukancard QR code"}),(0,s.jsxs)("label",{htmlFor:"qr-upload",className:"cursor-pointer",children:[(0,s.jsx)(b.$,{asChild:!0,disabled:n,children:(0,s.jsx)("span",{children:n?"Processing...":"Choose Image"})}),(0,s.jsx)("input",{id:"qr-upload",type:"file",accept:"image/*",onChange:u,disabled:n,className:"hidden"})]})]})}),(0,s.jsx)("div",{id:"qr-file-scanner-region",style:{display:"none"}}),(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"camera"===o?"Position the QR code within the camera frame":"Upload an image containing a Dukancard QR code"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Only Dukancard business QR codes are supported"})]}),n&&(0,s.jsx)("div",{className:"absolute inset-0 bg-background/80 flex items-center justify-center z-50 rounded-lg",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-foreground font-medium",children:"Processing QR code..."}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Please wait"})]})})]})]})})};r(38398);let A=({href:e,icon:t,label:r,isActive:a,isTablet:n=!1,badge:o,disabled:l=!1,onClick:c,isSpecial:u=!1})=>{let m=(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:(0,d.cn)("relative mb-1 transition-all duration-200",u&&"bg-[var(--brand-gold)] rounded-full p-3 -mt-2 shadow-lg"),children:[(0,s.jsx)("div",{className:(0,d.cn)(u&&"text-white"),children:t}),o&&(0,s.jsx)(p.E,{variant:"outline",className:"absolute -top-2 -right-3 px-1 py-0 text-[7px] bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]",children:o})]}),(0,s.jsx)("span",{className:(0,d.cn)("transition-all",n?"text-[9px]":"text-[10px]",u&&"text-[var(--brand-gold)] font-medium"),children:r})]}),h=(0,d.cn)("flex flex-col items-center justify-center flex-1 py-2 text-xs transition-colors cursor-pointer",a?"text-[var(--brand-gold)]":"text-muted-foreground hover:text-[var(--brand-gold)]",l&&"opacity-70 pointer-events-none",u&&"transform hover:scale-105");return l?(0,s.jsx)("div",{className:h,children:m}):c?(0,s.jsx)("button",{onClick:c,className:h,children:m}):(0,s.jsx)(i(),{href:e,className:h,children:m})};function I(){let e=(0,o.usePathname)(),t=(0,o.useRouter)(),r=(0,c.a)(),[n,i]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),[b,v]=(0,a.useState)(null),[j,y]=(0,a.useState)(null);if(!r&&!n)return null;let w="/login",N=!1,k="/",C=!1;e.startsWith("/dashboard/business")?(w="/dashboard/business/card",N="/dashboard/business/card"===e,k="/dashboard/business",C="/dashboard/business"===e):e.startsWith("/dashboard/customer")?(w="/dashboard/customer/profile",N=e.includes("/dashboard/customer/profile"),k="/dashboard/customer",C="/dashboard/customer"===e):e.startsWith("/login")||e.startsWith("/choose-role")||e.startsWith("/onboarding")?(w=e,N=!0,k="/",C="/"===e):"business"===j?(w="/dashboard/business/card",N=!1,k="/dashboard/business",C=!1):"customer"===j?(w="/dashboard/customer/profile",N=!1,k="/dashboard/customer",C=!1):(w="/login",N="/login"===e,k="/",C="/"===e);let S=[{key:"home",href:k,icon:(0,s.jsx)(u.A,{size:20}),label:"Home",isActive:C},{key:"discover",href:"/discover",icon:(0,s.jsx)(m.A,{size:20}),label:"Discover",isActive:"/discover"===e},{key:"scan",icon:(0,s.jsx)(h.A,{size:20}),label:"Scan",isActive:!1,onClick:()=>{x(!0)},isSpecial:!0},{key:"dukan-ai",href:"#",icon:(0,s.jsx)(g.A,{size:20}),label:"Dukan AI",isActive:!1,badge:"Soon",disabled:!0},{key:"account",href:w,icon:(0,s.jsx)(f.A,{size:20}),label:"Account",isActive:N}];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.P.div,{initial:{y:100},animate:{y:0},transition:{duration:.3},className:(0,d.cn)("fixed bottom-0 left-0 right-0 z-50 flex items-center justify-around bg-background/95 backdrop-blur-lg border-t border-border/80 px-2",n?"h-14":"h-16"),children:S.map(e=>(0,s.jsx)(A,{href:e.href,icon:e.icon,label:e.label,isActive:e.isActive,isTablet:n,badge:e.badge,disabled:e.disabled,onClick:e.onClick,isSpecial:e.isSpecial},e.key))}),(0,s.jsx)(P,{isOpen:p,onClose:()=>{x(!1)},onScanSuccess:e=>{x(!1),t.push(`/${e}`)}})]})}},39727:()=>{},41956:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>u});var s=r(60687);r(43210);var a=r(21134),n=r(363),i=r(34318),o=r(10218),l=r(15251),d=r(24934),c=r(55629);function u({variant:e="default"}={}){let{setTheme:t,theme:r}=(0,o.D)();return(0,l.a)()&&"default"===e?(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 border border-neutral-200/50 dark:border-neutral-700/50 backdrop-blur-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-muted text-foreground",children:"light"===r?(0,s.jsx)(a.A,{className:"h-5 w-5"}):"dark"===r?(0,s.jsx)(n.A,{className:"h-5 w-5"}):(0,s.jsx)(i.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"font-medium text-foreground",children:"Theme"}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:r||"system"})]})]}),(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsx)(d.$,{variant:"ghost",size:"sm",className:"h-8 px-3",children:"Change"})}),(0,s.jsxs)(c.SQ,{align:"end",className:"w-40",children:[(0,s.jsxs)(c._2,{onClick:()=>t("light"),children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Light"})]}),(0,s.jsxs)(c._2,{onClick:()=>t("dark"),children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Dark"})]}),(0,s.jsxs)(c._2,{onClick:()=>t("system"),children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"System"})]})]})]})]})}):"dashboard"===e?(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"ghost",className:"h-9 w-9 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0",children:[(0,s.jsx)(a.A,{className:"h-[1.1rem] w-[1.1rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(n.A,{className:"absolute h-[1.1rem] w-[1.1rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsxs)(c.SQ,{align:"end",children:[(0,s.jsxs)(c._2,{onClick:()=>t("light"),children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Light"})]}),(0,s.jsxs)(c._2,{onClick:()=>t("dark"),children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Dark"})]}),(0,s.jsxs)(c._2,{onClick:()=>t("system"),children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"System"})]})]})]}):(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"outline",size:"icon",children:[(0,s.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(n.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsxs)(c.SQ,{align:"end",children:[(0,s.jsxs)(c._2,{onClick:()=>t("light"),children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Light"})]}),(0,s.jsxs)(c._2,{onClick:()=>t("dark"),children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Dark"})]}),(0,s.jsxs)(c._2,{onClick:()=>t("system"),children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"System"})]})]})]})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},47990:()=>{},55629:(e,t,r)=>{"use strict";r.d(t,{SQ:()=>l,_2:()=>d,lp:()=>c,mB:()=>u,rI:()=>i,ty:()=>o});var s=r(60687);r(43210);var a=r(12325),n=r(96241);function i({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...e})}function l({className:e,sideOffset:t=4,...r}){return(0,s.jsx)(a.ZL,{children:(0,s.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function d({className:e,inset:t,variant:r="default",...i}){return(0,s.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function c({className:e,inset:t,...r}){return(0,s.jsx)(a.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function u({className:e,...t}){return(0,s.jsx)(a.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,generateMetadata:()=>c});var s=r(37413),a=r(59896),n=r.n(a);r(82704);var i=r(23392),o=r(80363),l=r(97102),d=r(88452);async function c(){let e="http://localhost:3000",t="Dukancard - Your Digital Business Card Solution",r="Create a digital business card with Dukancard to showcase your shop, services, or portfolio. Boost your online presence, connect with customers, and grow your business across India.",s=`${e}/opengraph-image.png`;return{metadataBase:new URL(e),title:{default:t,template:"%s - Dukancard"},description:r,keywords:"digital business card, online presence, small business India, shop digital card, freelancer portfolio, Tier 2 cities, Tier 3 cities, Dukancard, local business growth, QR code business card",robots:"index, follow",alternates:{canonical:e},openGraph:{title:t,description:r,url:e,siteName:"Dukancard",type:"website",locale:"en_IN",images:[{url:s,width:1200,height:630,alt:"Dukancard - Digital Business Card Platform"}]},twitter:{card:"summary_large_image",title:t,description:r,image:s}}}function u({children:e}){return(0,s.jsxs)("html",{lang:"en",className:n().variable,suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)(l.default,{}),(0,s.jsx)(d.default,{})]}),(0,s.jsx)("body",{className:"antialiased flex flex-col min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:(0,s.jsxs)(i.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[e,(0,s.jsx)(o.Toaster,{})]})})]})}},59102:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),a=r(72600);function n(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{id:"meta-pixel",strategy:"beforeInteractive",children:`
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window, document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');
          fbq('init', '700491699058296');
          fbq('track', 'PageView');
        `}),(0,s.jsx)("noscript",{children:(0,s.jsx)("img",{height:"1",width:"1",style:{display:"none"},src:"https://www.facebook.com/tr?id=700491699058296&ev=PageView&noscript=1",alt:"Meta Pixel tracking"})})]})}},59821:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),n=r(24224),i=r(96241);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}},62685:(e,t,r)=>{Promise.resolve().then(r.bind(r,70716)),Promise.resolve().then(r.bind(r,59102)),Promise.resolve().then(r.bind(r,94593)),Promise.resolve().then(r.bind(r,10218))},63665:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},70058:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=70058,e.exports=t},70716:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),a=r(72600);function n(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{src:"https://www.googletagmanager.com/gtag/js?id=G-8FDFQL6BX3",strategy:"beforeInteractive"}),(0,s.jsx)(a.default,{id:"google-analytics",strategy:"beforeInteractive",children:`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-8FDFQL6BX3');
        `})]})}},73393:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},80363:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\ui\\sonner.tsx","Toaster")},82704:()=>{},88452:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\MetaPixel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx","default")},90253:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/png",width:1200,height:630,url:(0,s.fillMetadataSegment)(".",await e.params,"opengraph-image.png")+"?0d89c6c5718897f1"}]},93099:()=>{},94593:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>i});var s=r(60687),a=r(10218),n=r(52581);let i=({...e})=>{let{theme:t="system"}=(0,a.D)();return(0,s.jsx)(n.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},96241:(e,t,r)=>{"use strict";r.d(t,{M0:()=>c,Yq:()=>u,cn:()=>n,gV:()=>i,gY:()=>d,kY:()=>o,vA:()=>l,vv:()=>m});var s=r(49384),a=r(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}function i(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function o(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),r=e.substring(e.length-2),s="*".repeat(e.length-4);return`${t}${s}${r}`}function l(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),r=t[0],s=t[1];if(r.length<=2||s.length<=2||!s.includes("."))return"Email Hidden";let a=r.substring(0,2)+"*".repeat(r.length-2),n=s.split("."),i=n[0],o=n.slice(1).join("."),l=i.substring(0,2)+"*".repeat(i.length-2);return`${a}@${l}.${o}`}function d(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),r=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let s=r.length-1;s>=0;s--)if(t>=r[s].value)return(e/r[s].value).toFixed(1).replace(/\.0$/,"")+r[s].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e,t=!1){if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let r={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(r.hour="2-digit",r.minute="2-digit",r.hour12=!0),e.toLocaleString("en-IN",r)}function m(e,t="INR"){if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch{return`${t} ${e.toFixed(2)}`}}},97102:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx","default")}};