(()=>{var e={};e.id=4417,e.ids=[4417],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3589:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},6475:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return s.callServer},createServerReference:function(){return i},findSourceMapURL:function(){return a.findSourceMapURL}});let s=r(11264),a=r(11448),i=r(19357).createServerReference},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31131:(e,t,r)=>{Promise.resolve().then(r.bind(r,24133)),Promise.resolve().then(r.bind(r,44145))},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>a});var s=r(34386);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let a=null,i=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));a=await e(),i=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,a=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,a){let i=()=>{var i,n,l,o,u;return i=e,n=t,l=r,o=s,u=a,"customer_profiles"===i?{data:l&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:l&&"business"===n?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===n?{id:"test-subscription-id",plan_id:u,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,r)=>n(e),neq:(t,r)=>n(e),gt:(t,r)=>n(e),gte:(t,r)=>n(e),lt:(t,r)=>n(e),lte:(t,r)=>n(e),like:(t,r)=>n(e),ilike:(t,r)=>n(e),is:(t,r)=>n(e),in:(t,r)=>n(e),contains:(t,r)=>n(e),containedBy:(t,r)=>n(e),rangeGt:(t,r)=>n(e),rangeGte:(t,r)=>n(e),rangeLt:(t,r)=>n(e),rangeLte:(t,r)=>n(e),rangeAdjacent:(t,r)=>n(e),overlaps:(t,r)=>n(e),textSearch:(t,r)=>n(e),match:t=>n(e),not:(t,r,s)=>n(e),or:t=>n(e),filter:(t,r,s)=>n(e),order:(t,r)=>n(e),limit:(t,r)=>n(e),range:(t,r,s)=>n(e),abortSignal:t=>n(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let t=i();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,r,s,a,i)}}(a):i?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await i.set(t,r,s)}catch{}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},33873:e=>{"use strict";e.exports=require("path")},34289:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let u={children:["",{children:["[cardSlug]",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81206)),"C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,48236)),"C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,36024)),"C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[cardSlug]/gallery/page",pathname:"/[cardSlug]/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},34631:e=>{"use strict";e.exports=require("tls")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44145:(e,t,r)=>{"use strict";r.d(t,{default:()=>b});var s=r(60687),a=r(43210),i=r(30474),n=r(85814),l=r.n(n),o=r(77882),u=r(88920),c=r(28559),d=r(17740),p=r(41862),m=r(11860),g=r(24934),h=r(6475);let x=(0,h.createServerReference)("70c9dfab15ca22682df83077abf481dbf2eb62e561",h.callServer,void 0,h.findSourceMapURL,"getBusinessGalleryImagesPaginated");var f=r(37472);function b({businessProfile:e,galleryImages:t,totalCount:r,totalPages:n,currentPage:h,hasNextPage:b,hasPrevPage:y,userPlan:v,isAuthenticated:w,currentUserId:_}){let[P,j]=(0,a.useState)(null),[k,A]=(0,a.useState)(0),[S,C]=(0,a.useState)(!1),[N,q]=(0,a.useState)(t),[I,G]=(0,a.useState)(h),[z,E]=(0,a.useState)(!1),[T,$]=(0,a.useState)(b),{ref:O,inView:M}=(0,f.Wx)({threshold:.1,triggerOnce:!0}),{ref:R,inView:U}=(0,f.Wx)({threshold:.1});(0,a.useCallback)(async()=>{if(!z&&T){E(!0);try{let t=I+1,r=await x(e.business_slug,t,20);r.images&&r.images.length>0?(q(e=>[...e,...r.images]),G(t),$(r.hasNextPage)):$(!1)}catch(e){console.error("Error loading more images:",e),$(!1)}finally{E(!1)}}},[e.business_slug,I,z,T]);let B=(e,t)=>{j(e),A(t)},D=()=>{j(null)},L={hidden:{opacity:0,scale:.8},visible:{opacity:1,scale:1}};return(0,s.jsxs)("div",{className:"w-full max-w-screen-xl mx-auto px-4 sm:px-6 py-8",children:[(0,s.jsx)("div",{className:"flex items-center mb-6",children:(0,s.jsx)(l(),{href:`/${e.business_slug}`,passHref:!0,children:(0,s.jsxs)(g.$,{variant:"ghost",size:"sm",className:"gap-1",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Back to Business Card"})]})})}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("h1",{className:"text-2xl sm:text-3xl font-bold mb-2",children:[e.business_name," Gallery"]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Browse all photos"})]}),(0,s.jsx)(o.P.div,{ref:O,className:"grid grid-cols-2 sm:grid-cols-4 gap-4",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:M?"visible":"hidden",children:N.map((t,r)=>(0,s.jsxs)(o.P.div,{className:"aspect-square relative overflow-hidden rounded-xl cursor-pointer group",onClick:()=>B(t.url,r),variants:L,whileHover:{scale:1.03},transition:{duration:.3},children:[(0,s.jsx)(i.default,{src:t.url,alt:`${e.business_name} gallery image ${r+1}`,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-105",sizes:"(max-width: 640px) 50vw, 25vw"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,s.jsx)("div",{className:"absolute bottom-2 right-2 bg-black/50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,s.jsx)(d.A,{className:"w-4 h-4"})})})]},t.id))}),T&&(0,s.jsx)("div",{ref:R,className:"flex justify-center py-8",children:z?(0,s.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 animate-spin"}),(0,s.jsx)("span",{children:"Loading more images..."})]}):(0,s.jsx)("div",{className:"text-muted-foreground text-sm",children:"Scroll down to load more images"})}),!T&&N.length>0&&(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"text-muted-foreground text-sm",children:"You've reached the end of the gallery"})}),(0,s.jsx)(u.N,{children:P&&(0,s.jsxs)(o.P.div,{className:"fixed inset-0 z-50 bg-black/90 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:D,children:[(0,s.jsx)("button",{className:"absolute top-4 right-4 z-10 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors",onClick:e=>{e.stopPropagation(),D()},children:(0,s.jsx)(m.A,{className:"w-6 h-6"})}),(0,s.jsx)("div",{className:"relative w-full h-full max-w-4xl max-h-[80vh] mx-auto p-4 flex items-center justify-center",onClick:e=>e.stopPropagation(),children:(0,s.jsx)(i.default,{src:P,alt:`${e.business_name} gallery image ${k+1}`,fill:!0,className:"object-contain",sizes:"100vw",priority:!0})})]})})]})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63617:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\[cardSlug]\\\\gallery\\\\GalleryPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx","default")},65475:(e,t,r)=>{Promise.resolve().then(r.bind(r,62299)),Promise.resolve().then(r.bind(r,63617))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81206:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,generateMetadata:()=>c});var s=r(37413),a=r(39916),i=r(32032),n=r(11659),l=r(82944),o=r(63617),u=r(62299);async function c({params:e}){let{cardSlug:t}=await e,r=`http://localhost:3000/${t}/gallery`,{data:s,error:i}=await (0,n.fE)(t);(i||!s)&&(0,a.notFound)();let l=s.business_name||"Business";return{title:`${l} - Photo Gallery | Dukancard`,description:`View the photo gallery for ${l}. Browse all images showcasing their products, services, and business.`,openGraph:{title:`${l} - Photo Gallery | Dukancard`,description:`View the photo gallery for ${l}. Browse all images showcasing their products, services, and business.`,url:r,siteName:"Dukancard",type:"website"},twitter:{card:"summary_large_image",title:`${l} - Photo Gallery | Dukancard`,description:`View the photo gallery for ${l}. Browse all images showcasing their products, services, and business.`}}}async function d({params:e}){let{cardSlug:t}=await e,r=await (0,i.createClient)(),{data:c,error:d}=await (0,n.fE)(t);if((d||!c)&&(console.error(`Error fetching profile for slug ${t}:`,d),(0,a.notFound)()),"online"!==c.status)return console.log(`Business profile ${t} is not online (status: ${c.status}).`),(0,s.jsx)(u.default,{});let{data:{user:p}}=await r.auth.getUser(),m=p?.id||null,g=!!p,{data:h}=await r.from("payment_subscriptions").select("plan_id, status").eq("business_profile_id",c.id).order("created_at",{ascending:!1}).limit(1).maybeSingle(),x=h?.plan_id||"free",{images:f,totalCount:b,totalPages:y,currentPage:v,hasNextPage:w,hasPrevPage:_,error:P}=await (0,l.yR)(t,1,20);return(P&&console.error(`Error fetching gallery images for ${t}:`,P),f&&0!==b)?(0,s.jsx)("div",{className:"min-h-screen flex flex-col",children:(0,s.jsx)(o.default,{businessProfile:{business_slug:c.business_slug||"",business_name:c.business_name||"",id:c.id},galleryImages:f,totalCount:b,totalPages:y,currentPage:v,hasNextPage:w,hasPrevPage:_,userPlan:x,isAuthenticated:g,currentUserId:m})}):(0,a.notFound)()}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,9398,4386,6724,2997,1107,7065,1753,3703,3037,6177,1659,7345],()=>r(34289));module.exports=s})();