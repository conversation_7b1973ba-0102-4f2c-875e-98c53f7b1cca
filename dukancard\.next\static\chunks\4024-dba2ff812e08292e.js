"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4024],{12105:(e,a,r)=>{r.d(a,{default:()=>o});var t=r(95155),s=r(12115),l=r(47924),n=r(54416),c=r(89852),i=r(97168),d=r(53999);function o(e){let{onSearch:a,initialSearchTerm:r="",className:o,placeholder:u="Search..."}=e,[m,x]=(0,s.useState)(r);return(0,s.useEffect)(()=>{x(r)},[r]),(0,t.jsx)("form",{onSubmit:e=>{e.preventDefault(),"function"==typeof a&&a(m)},className:(0,d.cn)("relative w-full",o),children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(l.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-neutral-500 dark:text-neutral-400"}),(0,t.jsx)(c.p,{type:"text",placeholder:u,value:m,onChange:e=>{x(e.target.value)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),"function"==typeof a&&a(m))},className:"pl-10 pr-10 h-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"}),m&&(0,t.jsxs)(i.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>{x(""),"function"==typeof a&&a("")},className:"absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Clear search"})]})]})})}},16481:(e,a,r)=>{r.d(a,{W:()=>s});var t=r(34477);let s=(0,t.createServerReference)("406d3eedcd0777bc7a4d097a7c3736c79aa39001bd",t.callServer,void 0,t.findSourceMapURL,"unsubscribeFromBusiness")},17659:(e,a,r)=>{r.d(a,{default:()=>v});var t=r(95155),s=r(12115),l=r(6874),n=r.n(l),c=r(97168),i=r(69663),d=r(71007),o=r(33786),u=r(23227),m=r(51154),x=r(75494),h=r(16481),b=r(56671),p=r(28695),f=r(53999);function v(e){var a,r,l;let{subscriptionId:v,profile:g,onUnsubscribeSuccess:j,showUnsubscribe:N=!0,variant:k="default"}=e,[w,y]=(0,s.useState)(!1),[A,S]=(0,s.useState)(!1),C=async()=>{if(j){y(!0);try{if(!g.id)return void b.oR.error("Cannot unsubscribe: Missing profile ID");let e=await (0,h.W)(g.id);e.success?(b.oR.success("Unsubscribed from ".concat(g.name||"profile",".")),j(v)):b.oR.error("Failed to unsubscribe: ".concat(e.error||"Unknown error"))}catch(e){b.oR.error("An unexpected error occurred")}finally{y(!1)}}},M=[g.locality,g.city,g.state].filter(Boolean).join(", "),E="business"===g.type?"/".concat(g.slug):"/profile/".concat(g.slug),_=g.logo_url||g.avatar_url;return(0,t.jsxs)(p.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:(0,f.cn)("rounded-lg border p-0 overflow-hidden transition-all duration-300","bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",A?"shadow-md transform -translate-y-1":"shadow-sm","compact"===k&&"max-w-sm"),onMouseEnter:()=>S(!0),onMouseLeave:()=>S(!1),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:(0,f.cn)("w-full bg-gradient-to-r from-blue-500/20 to-[var(--brand-gold)]/20 dark:from-blue-900/30 dark:to-[var(--brand-gold)]/30","compact"===k?"h-16":"h-20"),children:(0,t.jsx)("div",{className:"absolute inset-0 opacity-10 dark:opacity-20 bg-repeat",style:{backgroundImage:'url("/decorative/card-texture.svg")',backgroundSize:"200px"}})}),(0,t.jsx)("div",{className:(0,f.cn)("absolute left-4","compact"===k?"-bottom-4":"-bottom-6"),children:(0,t.jsx)("div",{className:"p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800",children:(0,t.jsxs)(i.eu,{className:(0,f.cn)("border border-neutral-200 dark:border-neutral-700 shadow-sm","compact"===k?"h-12 w-12":"h-16 w-16"),children:[_?(0,t.jsx)(i.BK,{src:_,alt:null!=(r=g.name)?r:"Profile"}):null,(0,t.jsx)(i.q5,{className:(0,f.cn)("bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 text-blue-600 dark:text-blue-300 font-semibold","compact"===k?"text-lg":"text-xl"),children:"customer"===g.type?(0,t.jsx)(d.A,{className:"compact"===k?"h-4 w-4":"h-6 w-6"}):null!=(l=null==(a=g.name)?void 0:a.charAt(0).toUpperCase())?l:"P"})]})})})]}),(0,t.jsx)("div",{className:(0,f.cn)("px-4 pb-4","compact"===k?"pt-6":"pt-8"),children:(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("h3",{className:(0,f.cn)("font-semibold text-neutral-800 dark:text-neutral-200 group flex items-center gap-1","compact"===k?"text-base":"text-lg"),children:g.slug?(0,t.jsxs)(n(),{href:E,className:"hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1",target:"_blank",children:[g.name,(0,t.jsx)(o.A,{className:"h-3.5 w-3.5 opacity-70"})]}):(0,t.jsx)("span",{children:g.name})}),M&&(0,t.jsxs)("p",{className:(0,f.cn)("text-neutral-500 dark:text-neutral-400 mt-1 flex items-center","compact"===k?"text-xs":"text-sm"),children:[(0,t.jsx)("span",{className:"inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2"}),M]}),"customer"===g.type&&(0,t.jsxs)("p",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1 flex items-center",children:[(0,t.jsx)(d.A,{className:"h-3 w-3 mr-1"}),"Customer"]}),"business"===g.type&&(0,t.jsxs)("p",{className:"text-xs text-green-600 dark:text-green-400 mt-1 flex items-center",children:[(0,t.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Business"]})]}),N&&j&&(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:C,disabled:w,className:(0,f.cn)("mt-2 w-full border-neutral-200 dark:border-neutral-700 transition-all duration-200","hover:bg-red-50 hover:text-red-600 hover:border-red-200","dark:hover:bg-red-950/30 dark:hover:text-red-400 dark:hover:border-red-900/50"),children:[w?(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Unsubscribe"]})]})})]})}},23861:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("Bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},31609:(e,a,r)=>{r.d(a,{default:()=>u});var t=r(95155),s=r(12115),l=r(6874),n=r.n(l),c=r(97168),i=r(23861),d=r(5456),o=r(17659);function u(e){let{initialSubscriptions:a,onUnsubscribeSuccess:r,showUnsubscribe:l=!0,variant:u="default",emptyMessage:m="No subscriptions found.",emptyDescription:x="Subscribe to profiles to see them here.",showDiscoverButton:h=!1}=e,[b,p]=(0,s.useState)(a);(0,s.useEffect)(()=>{p(a)},[a]);let f=e=>{p(a=>a.filter(a=>a.id!==e)),r&&r(e)};return 0===b.length?(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-20 text-center",children:[(0,t.jsxs)("div",{className:"relative mb-8",children:[(0,t.jsx)("div",{className:"absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"}),(0,t.jsx)("div",{className:"relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg",children:(0,t.jsx)(i.A,{className:"w-10 h-10 text-primary"})})]}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3",children:m}),(0,t.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2",children:x}),h&&(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(c.$,{asChild:!0,variant:"outline",className:"gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(n(),{href:"/discover",target:"_blank",rel:"noopener noreferrer",children:[(0,t.jsx)(d.A,{className:"w-4 h-4"}),"Discover Businesses"]})})})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:b.map((e,a)=>{let r=e.profile;return r?(0,t.jsx)("div",{className:"transform transition-all duration-200 hover:scale-[1.02]",children:(0,t.jsx)(o.default,{subscriptionId:e.id,profile:r,onUnsubscribeSuccess:l?f:void 0,showUnsubscribe:l,variant:u})},e.id):null})})}},62105:(e,a,r)=>{r.d(a,{default:()=>i});var t=r(95155),s=r(97168),l=r(42355),n=r(13052),c=r(53999);function i(e){let{currentPage:a,totalPages:r,onPageChange:i,className:d}=e;if(r<=1)return null;let o=(()=>{let e=[];if(r<=5)for(let a=1;a<=r;a++)e.push(a);else{e.push(1);let t=Math.max(2,a-1),s=Math.min(r-1,a+1);a<=3&&(s=Math.min(r-1,4)),a>=r-2&&(t=Math.max(2,r-3)),t>2&&e.push(-1);for(let a=t;a<=s;a++)e.push(a);s<r-1&&e.push(-2),e.push(r)}return e})();return(0,t.jsxs)("div",{className:(0,c.cn)("flex items-center justify-center space-x-1",d),children:[(0,t.jsxs)(s.$,{variant:"outline",size:"icon",onClick:()=>i(a-1),disabled:1===a,className:"h-8 w-8 p-0 border-neutral-200 dark:border-neutral-800",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Previous page"})]}),o.map((e,r)=>e<0?(0,t.jsx)("span",{className:"px-2 text-neutral-500 dark:text-neutral-400",children:"..."},"ellipsis-".concat(r)):(0,t.jsx)(s.$,{variant:a===e?"default":"outline",size:"sm",onClick:()=>i(e),className:(0,c.cn)("h-8 w-8 p-0",a===e?"bg-blue-500 hover:bg-blue-600 text-white border-transparent":"border-neutral-200 dark:border-neutral-800"),children:e},e)),(0,t.jsxs)(s.$,{variant:"outline",size:"icon",onClick:()=>i(a+1),disabled:a===r,className:"h-8 w-8 p-0 border-neutral-200 dark:border-neutral-800",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Next page"})]})]})}},75494:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(19946).A)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},94024:(e,a,r)=>{r.d(a,{A$:()=>n.default,Qd:()=>l.default,__:()=>s.default,fo:()=>t.SubscriptionListSkeleton}),r(17659);var t=r(95746),s=r(12105),l=r(62105),n=r(31609)},95746:(e,a,r)=>{r.d(a,{SubscriptionListSkeleton:()=>i,default:()=>c});var t=r(95155),s=r(28695),l=r(27737),n=r(53999);function c(e){let{index:a=0,variant:r="default"}=e;return(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.05*a},className:(0,n.cn)("rounded-lg border p-0 overflow-hidden transition-all duration-300","bg-white dark:bg-black border-neutral-200 dark:border-neutral-800","shadow-sm","compact"===r&&"max-w-sm"),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(l.E,{className:(0,n.cn)("w-full","compact"===r?"h-16":"h-20")}),(0,t.jsx)("div",{className:(0,n.cn)("absolute left-4","compact"===r?"-bottom-4":"-bottom-6"),children:(0,t.jsx)("div",{className:"p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800",children:(0,t.jsx)(l.E,{className:(0,n.cn)("rounded-full","compact"===r?"h-12 w-12":"h-16 w-16")})})})]}),(0,t.jsx)("div",{className:(0,n.cn)("px-4 pb-4","compact"===r?"pt-6":"pt-8"),children:(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)(l.E,{className:(0,n.cn)("mb-2","compact"===r?"h-5 w-32":"h-6 w-40")}),(0,t.jsx)(l.E,{className:(0,n.cn)("mt-1","compact"===r?"h-3 w-24":"h-4 w-32")})]}),(0,t.jsx)(l.E,{className:(0,n.cn)("w-full mt-2 rounded-md","compact"===r?"h-8":"h-9")})]})})]})}function i(e){let{variant:a="default",count:r=6}=e;return(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:r}).map((e,r)=>(0,t.jsx)(c,{index:r,variant:a},r))})}}}]);