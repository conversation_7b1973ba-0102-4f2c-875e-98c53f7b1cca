"use strict";exports.id=33,exports.ids=[33],exports.modules={10033:(t,e,i)=>{i.d(e,{SubscriptionStateManager:()=>r,TQ:()=>a});var s=i(80223);class r{static shouldHaveActiveSubscription(t,e=s.v.FREE){return e!==s.v.FREE&&t!==s.d.TRIAL&&[s.d.ACTIVE].includes(t)}static isTerminalStatus(t){return[s.d.CANCELLED,s.d.EXPIRED,s.d.COMPLETED].includes(t)}static isTrialStatus(t){return t===s.d.TRIAL}static isFreeStatus(t,e){return e===s.v.FREE||"free"===t}static getAccessLevel(t,e=s.v.FREE){return e===s.v.FREE?"free":t===s.d.TRIAL?"trial":this.shouldHaveActiveSubscription(t,e)?"paid":"free"}static isActivePaidSubscription(t,e=s.v.FREE){return this.shouldHaveActiveSubscription(t,e)}static isValidStatusTransition(t,e){return!this.isTerminalStatus(t)||!!this.isTerminalStatus(e)}}function a(t){return r.isTerminalStatus(t)}},80223:(t,e,i)=>{i.d(e,{d:()=>s,v:()=>r});let s={ACTIVE:"active",AUTHENTICATED:"authenticated",TRIAL:"trial",PENDING:"pending",HALTED:"halted",CANCELLED:"cancelled",EXPIRED:"expired",COMPLETED:"completed",PAYMENT_FAILED:"payment_failed",CANCELLATION_SCHEDULED:"cancellation_scheduled"},r={FREE:"free",BASIC:"basic",GROWTH:"growth",PRO:"pro",ENTERPRISE:"enterprise"}}};