"use strict";exports.id=634,exports.ids=[634],exports.modules={78119:(e,t,n)=>{n.d(t,{dK:()=>h,wE:()=>m});var r=n(43210),l=Object.defineProperty,o=Object.defineProperties,a=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,c=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&c(e,n,t[n]);if(i)for(var n of i(t))s.call(t,n)&&c(e,n,t[n]);return e},d=(e,t)=>o(e,a(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&s.call(e,r)&&(n[r]=e[r]);return n},h=r.createContext({}),m=r.forwardRef((e,t)=>{var n,l,o,a,i,{value:u,onChange:s,maxLength:c,textAlign:m="left",pattern:b,placeholder:w,inputMode:E="numeric",onComplete:x,pushPasswordManagerStrategy:S="increase-width",pasteTransformer:y,containerClassName:P,noScriptCSSFallback:C=v,render:M,children:R}=e,k=f(e,["value","onChange","maxLength","textAlign","pattern","placeholder","inputMode","onComplete","pushPasswordManagerStrategy","pasteTransformer","containerClassName","noScriptCSSFallback","render","children"]);let[D,j]=r.useState("string"==typeof k.defaultValue?k.defaultValue:""),T=null!=u?u:D,W=function(e){let t=r.useRef();return r.useEffect(()=>{t.current=e}),t.current}(T),B=r.useCallback(e=>{null==s||s(e),j(e)},[s]),I=r.useMemo(()=>b?"string"==typeof b?new RegExp(b):b:null,[b]),O=r.useRef(null),A=r.useRef(null),L=r.useRef({value:T,onChange:B,isIOS:"undefined"!=typeof window&&(null==(l=null==(n=null==window?void 0:window.CSS)?void 0:n.supports)?void 0:l.call(n,"-webkit-touch-callout","none"))}),$=r.useRef({prev:[null==(o=O.current)?void 0:o.selectionStart,null==(a=O.current)?void 0:a.selectionEnd,null==(i=O.current)?void 0:i.selectionDirection]});r.useImperativeHandle(t,()=>O.current,[]),r.useEffect(()=>{let e=O.current,t=A.current;if(!e||!t)return;function n(){if(document.activeElement!==e){G(null),U(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,l=e.maxLength,o=e.value,a=$.current.prev,i=-1,u=-1,s;if(0!==o.length&&null!==t&&null!==n){let e=t===n,r=t===o.length&&o.length<l;if(e&&!r){if(0===t)i=0,u=1,s="forward";else if(t===l)i=t-1,u=t,s="backward";else if(l>1&&o.length>1){let e=0;if(null!==a[0]&&null!==a[1]){s=t<a[1]?"backward":"forward";let n=a[0]===a[1]&&a[0]<l;"backward"!==s||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&O.current.setSelectionRange(i,u,s)}let c=-1!==i?i:t,p=-1!==u?u:n,d=null!=s?s:r;G(c),U(p),$.current.prev=[c,p,d]}if(L.current.value!==e.value&&L.current.onChange(e.value),$.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&N(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";g(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),g(e.sheet,`[data-input-otp]:autofill { ${t} }`),g(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),g(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),g(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let l=new ResizeObserver(r);return l.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),l.disconnect()}},[]);let[F,_]=r.useState(!1),[H,N]=r.useState(!1),[z,G]=r.useState(null),[V,U]=r.useState(null);r.useEffect(()=>{!function(e){setTimeout(e,0),setTimeout(e,10),setTimeout(e,50)}(()=>{var e,t,n,r;null==(e=O.current)||e.dispatchEvent(new Event("input"));let l=null==(t=O.current)?void 0:t.selectionStart,o=null==(n=O.current)?void 0:n.selectionEnd,a=null==(r=O.current)?void 0:r.selectionDirection;null!==l&&null!==o&&(G(l),U(o),$.current.prev=[l,o,a])})},[T,H]),r.useEffect(()=>{void 0!==W&&T!==W&&W.length<c&&T.length===c&&(null==x||x(T))},[c,x,W,T]);let Q=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:l}){let[o,a]=r.useState(!1),[i,u]=r.useState(!1),[s,c]=r.useState(!1),p=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&o&&i,[o,i,n]),d=r.useCallback(()=>{let r=e.current,l=t.current;if(!r||!l||s||"none"===n)return;let o=r.getBoundingClientRect().left+r.offsetWidth,i=r.getBoundingClientRect().top+r.offsetHeight/2;0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(o-18,i)===r||(a(!0),c(!0))},[e,t,s,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){u(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let l=setInterval(r,1e3);return()=>{clearInterval(l)}},[e,n]),r.useEffect(()=>{let e=l||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(d,0),o=setTimeout(d,2e3),a=setTimeout(d,5e3),i=setTimeout(()=>{c(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(o),clearTimeout(a),clearTimeout(i)}},[t,l,n,d]),{hasPWMBadge:o,willPushPWMBadge:p,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:A,inputRef:O,pushPasswordManagerStrategy:S,isFocused:H}),X=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,c);if(t.length>0&&I&&!I.test(t))return void e.preventDefault();"string"==typeof W&&t.length<W.length&&document.dispatchEvent(new Event("selectionchange")),B(t)},[c,B,W,I]),q=r.useCallback(()=>{var e;if(O.current){let t=Math.min(O.current.value.length,c-1),n=O.current.value.length;null==(e=O.current)||e.setSelectionRange(t,n),G(t),U(n)}N(!0)},[c]),K=r.useCallback(e=>{var t,n;let r=O.current;if(!y&&(!L.current.isIOS||!e.clipboardData||!r))return;let l=e.clipboardData.getData("text/plain"),o=y?y(l):l;e.preventDefault();let a=null==(t=O.current)?void 0:t.selectionStart,i=null==(n=O.current)?void 0:n.selectionEnd,u=(a!==i?T.slice(0,a)+o+T.slice(i):T.slice(0,a)+o+T.slice(a)).slice(0,c);if(u.length>0&&I&&!I.test(u))return;r.value=u,B(u);let s=Math.min(u.length,c-1),p=u.length;r.setSelectionRange(s,p),G(s),U(p)},[c,B,I,T]),J=r.useMemo(()=>({position:"relative",cursor:k.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[k.disabled]),Y=r.useMemo(()=>({position:"absolute",inset:0,width:Q.willPushPWMBadge?`calc(100% + ${Q.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:Q.willPushPWMBadge?`inset(0 ${Q.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:m,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[Q.PWM_BADGE_SPACE_WIDTH,Q.willPushPWMBadge,m]),Z=r.useMemo(()=>r.createElement("input",d(p({autoComplete:k.autoComplete||"one-time-code"},k),{"data-input-otp":!0,"data-input-otp-placeholder-shown":0===T.length||void 0,"data-input-otp-mss":z,"data-input-otp-mse":V,inputMode:E,pattern:null==I?void 0:I.source,"aria-placeholder":w,style:Y,maxLength:c,value:T,ref:O,onPaste:e=>{var t;K(e),null==(t=k.onPaste)||t.call(k,e)},onChange:X,onMouseOver:e=>{var t;_(!0),null==(t=k.onMouseOver)||t.call(k,e)},onMouseLeave:e=>{var t;_(!1),null==(t=k.onMouseLeave)||t.call(k,e)},onFocus:e=>{var t;q(),null==(t=k.onFocus)||t.call(k,e)},onBlur:e=>{var t;N(!1),null==(t=k.onBlur)||t.call(k,e)}})),[X,q,K,E,Y,c,V,z,k,null==I?void 0:I.source,T]),ee=r.useMemo(()=>({slots:Array.from({length:c}).map((e,t)=>{var n;let r=H&&null!==z&&null!==V&&(z===V&&t===z||t>=z&&t<V),l=void 0!==T[t]?T[t]:null;return{char:l,placeholderChar:void 0!==T[0]?null:null!=(n=null==w?void 0:w[t])?n:null,isActive:r,hasFakeCaret:r&&null===l}}),isFocused:H,isHovering:!k.disabled&&F}),[H,F,c,V,z,k.disabled,T]),et=r.useMemo(()=>M?M(ee):r.createElement(h.Provider,{value:ee},R),[R,ee,M]);return r.createElement(r.Fragment,null,null!==C&&r.createElement("noscript",null,r.createElement("style",null,C)),r.createElement("div",{ref:A,"data-input-otp-container":!0,style:J,className:P},et,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},Z)))});function g(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}m.displayName="Input";var v=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`},78148:(e,t,n)=>{n.d(t,{b:()=>i});var r=n(43210),l=n(3416),o=n(60687),a=r.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},88920:(e,t,n)=>{n.d(t,{N:()=>v});var r=n(60687),l=n(43210),o=n(12157),a=n(72789),i=n(15124),u=n(21279),s=n(32582);class c extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=e instanceof HTMLElement&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p({children:e,isPresent:t,anchorX:n}){let o=(0,l.useId)(),a=(0,l.useRef)(null),i=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,l.useContext)(s.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:r,top:l,left:s,right:c}=i.current;if(t||!a.current||!e||!r)return;let p="left"===n?`left: ${s}`:`right: ${c}`;a.current.dataset.motionPopId=o;let d=document.createElement("style");return u&&(d.nonce=u),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${p}px !important;
            top: ${l}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),(0,r.jsx)(c,{isPresent:t,childRef:a,sizeRef:i,children:l.cloneElement(e,{ref:a})})}let d=({children:e,initial:t,isPresent:n,onExitComplete:o,custom:i,presenceAffectsLayout:s,mode:c,anchorX:d})=>{let h=(0,a.M)(f),m=(0,l.useId)(),g=!0,v=(0,l.useMemo)(()=>(g=!1,{id:m,initial:t,isPresent:n,custom:i,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;o&&o()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[n,h,o]);return s&&g&&(v={...v}),(0,l.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[n]),l.useEffect(()=>{n||h.size||!o||o()},[n]),"popLayout"===c&&(e=(0,r.jsx)(p,{isPresent:n,anchorX:d,children:e})),(0,r.jsx)(u.t.Provider,{value:v,children:e})};function f(){return new Map}var h=n(86044);let m=e=>e.key||"";function g(e){let t=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&t.push(e)}),t}let v=({children:e,custom:t,initial:n=!0,onExitComplete:u,presenceAffectsLayout:s=!0,mode:c="sync",propagate:p=!1,anchorX:f="left"})=>{let[v,b]=(0,h.xQ)(p),w=(0,l.useMemo)(()=>g(e),[e]),E=p&&!v?[]:w.map(m),x=(0,l.useRef)(!0),S=(0,l.useRef)(w),y=(0,a.M)(()=>new Map),[P,C]=(0,l.useState)(w),[M,R]=(0,l.useState)(w);(0,i.E)(()=>{x.current=!1,S.current=w;for(let e=0;e<M.length;e++){let t=m(M[e]);E.includes(t)?y.delete(t):!0!==y.get(t)&&y.set(t,!1)}},[M,E.length,E.join("-")]);let k=[];if(w!==P){let e=[...w];for(let t=0;t<M.length;t++){let n=M[t],r=m(n);E.includes(r)||(e.splice(t,0,n),k.push(n))}return"wait"===c&&k.length&&(e=k),R(g(e)),C(w),null}let{forceRender:D}=(0,l.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:M.map(e=>{let l=m(e),o=(!p||!!v)&&(w===M||E.includes(l));return(0,r.jsx)(d,{isPresent:o,initial:(!x.current||!!n)&&void 0,custom:t,presenceAffectsLayout:s,mode:c,onExitComplete:o?void 0:()=>{if(!y.has(l))return;y.set(l,!0);let e=!0;y.forEach(t=>{t||(e=!1)}),e&&(D?.(),R(S.current),p&&b?.(),u&&u())},anchorX:f,children:e},l)})})}}};