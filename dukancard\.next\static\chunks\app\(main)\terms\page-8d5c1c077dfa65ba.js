(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2603],{1243:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("Triangle<PERSON>lert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},34869:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},52719:(e,t,i)=>{"use strict";i.d(t,{default:()=>D});var r=i(95155),a=i(12115),n=i(28695),o=i(6874),s=i.n(o),c=i(57434),l=i(19946);let d=(0,l.A)("FileCode",[["path",{d:"M10 12.5 8 15l2 2.5",key:"1tg20x"}],["path",{d:"m14 12.5 2 2.5-2 2.5",key:"yinavb"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z",key:"1mlx9k"}]]);var h=i(71007);let u=(0,l.A)("Key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var y=i(1243),p=i(81586);let m=(0,l.A)("Scale",[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"7g6ntu"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"ijws7r"}],["path",{d:"M7 21h10",key:"1b0cd5"}],["path",{d:"M12 3v18",key:"108xh3"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",key:"3gwbw2"}]]);var f=i(34869),g=i(62525),v=i(53904),x=i(71539),b=i(28883),j=i(88482),w=i(50609),k=i(27902),A=i(26575),S=i(51186),N=i(46622),T=i(27938);let M=[{id:"introduction",title:"Introduction"},{id:"definitions",title:"Definitions"},{id:"account",title:"Account Registration"},{id:"content",title:"User Content"},{id:"prohibited",title:"Prohibited Activities"},{id:"payment",title:"Payment Terms"},{id:"intellectual",title:"Intellectual Property"},{id:"disclaimer",title:"Disclaimer of Warranties"},{id:"limitation",title:"Limitation of Liability"},{id:"termination",title:"Termination"},{id:"changes",title:"Changes to Terms"},{id:"contact",title:"Contact Us"}],C=[{title:"Privacy Policy",href:"/privacy"},{title:"Cookie Policy",href:"/cookies"},{title:"Refund Policy",href:"/refund"}];function D(){let e=(0,a.useRef)(null);return(0,a.useEffect)(()=>{window.scrollTo(0,0)},[]),(0,r.jsx)("div",{ref:e,className:"min-h-screen bg-white dark:bg-black",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(w.A,{title:"Terms of Service",lastUpdated:"May 19, 2025",variant:"blue"}),(0,r.jsx)("div",{className:"container mx-auto px-4 max-w-4xl pb-16",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"w-full lg:w-1/4 order-1",children:(0,r.jsx)("div",{className:"lg:sticky lg:top-24 self-start",children:(0,r.jsx)(A.A,{items:M})})}),(0,r.jsxs)("div",{className:"w-full lg:w-3/4 order-2",children:[(0,r.jsx)(j.Zp,{className:"p-6 md:p-8 border border-border shadow-sm mb-8",children:(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"prose prose-neutral dark:prose-invert max-w-none",children:(0,r.jsx)("p",{className:"text-lg",children:"Welcome to Dukancard. These Terms of Service govern your access to and use of our website, mobile applications, and services. By using our services, you agree to be bound by these terms."})})}),(0,r.jsx)(k.A,{id:"introduction",title:"1. Introduction",icon:(0,r.jsx)(c.A,{className:"h-6 w-6"}),delay:0,children:(0,r.jsx)("p",{children:'Welcome to Dukancard. These Terms of Service ("Terms") govern your access to and use of the Dukancard website, mobile applications, and services (collectively, the "Service"). By accessing or using the Service, you agree to be bound by these Terms. If you do not agree to these Terms, you may not access or use the Service.'})}),(0,r.jsxs)(k.A,{id:"definitions",title:"2. Definitions",icon:(0,r.jsx)(d,{className:"h-6 w-6"}),delay:1,children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:'"Dukancard"'})," refers to the digital business card platform operated by Dukancard."]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:'"User"'})," refers to any individual or entity that accesses or uses the Service."]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:'"Content"'})," refers to any information, text, graphics, photos, or other materials uploaded, downloaded, or appearing on the Service."]})]}),(0,r.jsxs)(k.A,{id:"account",title:"3. Account Registration",icon:(0,r.jsx)(h.A,{className:"h-6 w-6"}),delay:2,children:[(0,r.jsx)("p",{children:"To use certain features of the Service, you may be required to register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete."}),(0,r.jsx)("p",{children:"You are responsible for safeguarding your password and for all activities that occur under your account. You agree to notify Dukancard immediately of any unauthorized use of your account."})]}),(0,r.jsxs)(k.A,{id:"content",title:"4. User Content",icon:(0,r.jsx)(u,{className:"h-6 w-6"}),delay:3,children:[(0,r.jsx)("p",{children:"Our Service allows you to post, link, store, share and otherwise make available certain information, text, graphics, videos, or other material. You are responsible for the Content that you post on or through the Service, including its legality, reliability, and appropriateness."}),(0,r.jsx)("p",{children:"By posting Content on or through the Service, you represent and warrant that: (i) the Content is yours (you own it) and/or you have the right to use it and the right to grant us the rights and license as provided in these Terms, and (ii) that the posting of your Content on or through the Service does not violate the privacy rights, publicity rights, copyrights, contract rights or any other rights of any person or entity."})]}),(0,r.jsx)(N.A,{variant:"blue",className:"my-12"}),(0,r.jsxs)(k.A,{id:"prohibited",title:"5. Prohibited Activities",icon:(0,r.jsx)(y.A,{className:"h-6 w-6"}),delay:4,children:[(0,r.jsx)("p",{children:"You agree not to engage in any of the following prohibited activities:"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 mb-6",children:[(0,r.jsx)("li",{children:"Using the Service for any illegal purpose or in violation of any local, state, national, or international law."}),(0,r.jsx)("li",{children:"Harassing, abusing, or harming another person, or engaging in any other conduct that restricts or inhibits anyone's use or enjoyment of the Service."}),(0,r.jsx)("li",{children:"Impersonating any person or entity, or falsely stating or otherwise misrepresenting your affiliation with a person or entity."}),(0,r.jsx)("li",{children:"Interfering with or disrupting the Service or servers or networks connected to the Service."}),(0,r.jsx)("li",{children:"Attempting to gain unauthorized access to the Service, other accounts, computer systems, or networks connected to the Service."})]})]}),(0,r.jsxs)(k.A,{id:"payment",title:"6. Payment Terms",icon:(0,r.jsx)(p.A,{className:"h-6 w-6"}),delay:5,children:[(0,r.jsx)("p",{children:"Certain aspects of the Service may be provided for a fee. You will be required to select a payment plan and provide accurate information regarding your payment method. You agree to pay Dukancard the amount that is specified in the payment plan in accordance with the terms of such plan."}),(0,r.jsx)("p",{children:"All payments are processed securely through Razorpay, our payment gateway partner. Payments will be charged on the day you sign up for a payment plan and will cover the use of that service for the period as indicated. Payment plans will automatically renew unless cancelled in accordance with these Terms."}),(0,r.jsx)("p",{children:"By subscribing to our paid plans, you authorize Razorpay to charge your payment method for the subscription fees on a recurring basis until you cancel your subscription."}),(0,r.jsxs)("p",{children:["For more information about our refund policy, please see our"," ",(0,r.jsx)(s(),{href:"/refund",className:"text-blue-500 hover:underline",children:"Refund Policy"}),"."]})]}),(0,r.jsxs)(k.A,{id:"intellectual",title:"7. Intellectual Property",icon:(0,r.jsx)(m,{className:"h-6 w-6"}),delay:6,children:[(0,r.jsx)("p",{children:"The Service and its original content (excluding Content provided by users), features, and functionality are and will remain the exclusive property of Dukancard and its licensors. The Service is protected by copyright, trademark, and other laws of both India and foreign countries."}),(0,r.jsx)("p",{children:"Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of Dukancard."})]}),(0,r.jsx)(k.A,{id:"disclaimer",title:"8. Disclaimer of Warranties",icon:(0,r.jsx)(f.A,{className:"h-6 w-6"}),delay:7,children:(0,r.jsx)("p",{children:'Your use of the Service is at your sole risk. The Service is provided on an "AS IS" and "AS AVAILABLE" basis. The Service is provided without warranties of any kind, whether express or implied, including, but not limited to, implied warranties of merchantability, fitness for a particular purpose, non-infringement, or course of performance.'})}),(0,r.jsx)(k.A,{id:"limitation",title:"9. Limitation of Liability",icon:(0,r.jsx)(g.A,{className:"h-6 w-6"}),delay:8,children:(0,r.jsx)("p",{children:"In no event shall Dukancard, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from (i) your access to or use of or inability to access or use the Service; (ii) any conduct or content of any third party on the Service; (iii) any content obtained from the Service; and (iv) unauthorized access, use, or alteration of your transmissions or content, whether based on warranty, contract, tort (including negligence), or any other legal theory, whether or not we have been informed of the possibility of such damage."})}),(0,r.jsxs)(k.A,{id:"termination",title:"10. Termination",icon:(0,r.jsx)(v.A,{className:"h-6 w-6"}),delay:9,children:[(0,r.jsx)("p",{children:"We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms."}),(0,r.jsx)("p",{children:"If you wish to terminate your account, you may simply discontinue using the Service or contact us to request account deletion."})]}),(0,r.jsxs)(k.A,{id:"changes",title:"11. Changes to Terms",icon:(0,r.jsx)(x.A,{className:"h-6 w-6"}),delay:10,children:[(0,r.jsx)("p",{children:"Dukancard reserves the right, at its sole discretion, to modify or replace these Terms at any time. If a revision is material, Dukancard will provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at Dukancard's sole discretion."}),(0,r.jsx)("p",{children:"By continuing to access or use the Service after those revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, you must stop using the Service."})]}),(0,r.jsxs)(k.A,{id:"contact",title:"12. Contact Us",icon:(0,r.jsx)(b.A,{className:"h-6 w-6"}),delay:11,children:[(0,r.jsx)("p",{children:"If you have any questions about these Terms, please contact us:"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 mb-6",children:[(0,r.jsxs)("li",{children:["By email: ",T.C.contact.email]}),(0,r.jsxs)("li",{children:["By phone: ",T.C.contact.phone]}),(0,r.jsxs)("li",{children:["By mail: ",T.C.contact.address.full]})]})]})]})]})}),(0,r.jsx)(N.A,{variant:"gold",className:"my-8"}),(0,r.jsx)(S.A,{relatedLinks:C})]})})}},53904:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},57434:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},62525:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},71007:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},71539:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},72112:(e,t,i)=>{Promise.resolve().then(i.bind(i,52719))},81586:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,1199,102,8441,1684,7358],()=>t(72112)),_N_E=e.O()}]);