exports.id=5918,exports.ids=[5918],exports.modules={369:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\customer\\\\CustomerDashboardClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx","default")},16498:(e,s,t)=>{Promise.resolve().then(t.bind(t,369))},19975:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var r=t(60687);t(43210);var a=t(27625),l=t(41956),n=t(38606),o=t(24861),i=t(83139),d=t(96241);function c({children:e,initialUserName:s,initialUserAvatarUrl:t}){return(0,r.jsxs)(o.GB,{children:[(0,r.jsx)(i.O,{userName:s,userAvatarUrl:t}),(0,r.jsxs)(o.sF,{children:[(0,r.jsxs)(a.default,{userName:s,businessName:null,logoUrl:t,children:[(0,r.jsx)(o.x2,{className:"ml-auto md:ml-0"})," ",(0,r.jsx)(l.ThemeToggle,{variant:"dashboard"})]}),(0,r.jsx)("main",{className:(0,d.cn)("flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto","pb-20 sm:pb-18 md:pb-6","bg-white dark:bg-black"),children:e}),(0,r.jsx)(n.default,{})]})]})}},78050:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,generateMetadata:()=>n});var r=t(37413);t(61120);var a=t(32032),l=t(369);async function n(){return{title:"Customer Dashboard",robots:"noindex, nofollow"}}async function o({children:e}){let s=await (0,a.createClient)(),t=null,n=null,{data:{user:o}}=await s.auth.getUser();if(o){let{data:e,error:r}=await s.from("customer_profiles").select("name, avatar_url").eq("id",o.id).single();r?(console.error("Error fetching customer profile:",r.message),t="User"):(t=e?.name||"User",n=e?.avatar_url)}else t="User";return(0,r.jsx)(l.default,{initialUserName:t,initialUserAvatarUrl:n,children:e})}},79706:(e,s,t)=>{Promise.resolve().then(t.bind(t,19975))},83139:(e,s,t)=>{"use strict";t.d(s,{O:()=>v});var r=t(60687),a=t(43210),l=t(85814),n=t.n(l),o=t(38782),i=t(24861),d=t(47696),c=t(17971),m=t(40083),u=t(42154),x=t(70373),h=t(55629),p=t(24934),b=t(96241);let f=e=>{if(!e)return"?";let s=e.trim().split(/\s+/);return 1===s.length&&s[0]?s[0].charAt(0).toUpperCase():s.length>1&&s[0]&&s[s.length-1]?s[0].charAt(0).toUpperCase()+s[s.length-1].charAt(0).toUpperCase():"?"};function j({user:e}){let{isMobile:s}=(0,i.cL)(),t=f(e.name),l=e.name||"User",[n,o]=(0,a.useState)(!1);return(0,r.jsx)(i.wZ,{className:(0,b.cn)(s?"pb-16":n?"pb-14":""),children:(0,r.jsx)(i.FX,{children:(0,r.jsxs)(h.rI,{children:[(0,r.jsx)(h.ty,{asChild:!0,children:(0,r.jsxs)(i.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsxs)(x.eu,{className:"h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30",children:[e.avatar?(0,r.jsx)(x.BK,{src:e.avatar,alt:l}):null,(0,r.jsx)(x.q5,{className:"rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 text-blue-800 dark:text-blue-200 text-xs border border-[var(--brand-gold)]/30",children:t})]}),(0,r.jsx)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:(0,r.jsx)("span",{className:"truncate font-semibold",children:l})}),(0,r.jsx)(c.A,{className:"ml-auto size-4"})]})}),(0,r.jsxs)(h.SQ,{className:"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg",side:s?"bottom":"right",align:"end",sideOffset:4,children:[(0,r.jsx)(h.lp,{className:"p-0 font-normal",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsxs)(x.eu,{className:"h-8 w-8 rounded-lg",children:[e.avatar?(0,r.jsx)(x.BK,{src:e.avatar,alt:l}):null,(0,r.jsx)(x.q5,{className:"rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 text-blue-800 dark:text-blue-200 text-xs",children:t})]}),(0,r.jsx)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:(0,r.jsx)("span",{className:"truncate font-semibold",children:l})})]})}),(0,r.jsx)(h.mB,{}),(0,r.jsx)("form",{action:u.B,className:"w-full px-2 py-1.5",children:(0,r.jsxs)(p.$,{variant:"ghost",type:"submit",className:"w-full justify-start p-0 h-auto font-normal cursor-pointer",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Log out"]})})]})]})})})}var g=t(14952),N=t(8936);function v({userName:e,userAvatarUrl:s,...t}){return(0,r.jsxs)(i.Bx,{collapsible:"icon",...t,children:[(0,r.jsx)(i.Gh,{className:"border-b border-border/50",children:(0,r.jsx)("div",{className:"flex items-center px-2 py-4",children:(0,r.jsxs)(n(),{href:"/?view=home",className:"flex flex-col group transition-all duration-200 hover:opacity-80",children:[(0,r.jsxs)("span",{className:"font-bold text-lg text-[var(--brand-gold)]",children:["Dukan",(0,r.jsx)("span",{className:"text-foreground",children:"card"})]}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Customer Portal"})]})})}),(0,r.jsx)(i.Yv,{className:"px-2",children:(0,r.jsx)(i.Cn,{children:(0,r.jsx)(i.wZ,{className:"space-y-1",children:[{title:"Feed",icon:"LayoutList",url:"/dashboard/customer",items:[]},{title:"Overview",icon:"LayoutDashboard",url:"/dashboard/customer/overview",items:[]},{title:"Social",icon:"Users",items:[{title:"Subscriptions",icon:"Bell",url:"/dashboard/customer/subscriptions"},{title:"My Likes",icon:"Heart",url:"/dashboard/customer/likes"},{title:"My Reviews",icon:"Star",url:"/dashboard/customer/reviews"}]},{title:"Account",icon:"User",items:[{title:"Profile",icon:"User",url:"/dashboard/customer/profile"},{title:"Settings",icon:"Settings",url:"/dashboard/customer/settings"}]}].map((e,s)=>0===s||1===s?(0,r.jsx)(i.FX,{children:(0,r.jsx)(i.Uj,{asChild:!0,tooltip:e.title,className:"h-10 rounded-lg",children:(0,r.jsxs)(o.d,{href:e.url||"#",className:"flex items-center gap-3",children:[e.icon&&N.K[e.icon]&&a.createElement(N.K[e.icon],{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"font-medium",children:e.title})]})})},e.title):(0,r.jsx)(d.Nt,{defaultOpen:2===s,className:"group/collapsible",children:(0,r.jsxs)(i.FX,{children:[(0,r.jsx)(d.R6,{asChild:!0,children:(0,r.jsxs)(i.Uj,{className:"h-10 rounded-lg",children:[e.icon&&N.K[e.icon]&&a.createElement(N.K[e.icon],{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"font-medium",children:e.title}),(0,r.jsx)(g.A,{className:"ml-auto h-4 w-4 text-muted-foreground group-data-[state=open]/collapsible:rotate-90"})]})}),(0,r.jsx)(d.Ke,{className:"transition-all duration-200",children:(0,r.jsx)(i.q9,{className:"ml-4 mt-1 space-y-1 border-l border-border/30 pl-4",children:e.items.map(e=>(0,r.jsx)(i.Fg,{children:(0,r.jsx)(i.Cp,{asChild:!0,className:"h-9 rounded-md",children:(0,r.jsxs)(o.d,{href:e.url||"#",className:"flex items-center gap-3",children:[e.icon&&N.K[e.icon]&&a.createElement(N.K[e.icon],{className:"h-3.5 w-3.5 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.title})]})})},e.title))})})]})},e.title))})})}),(0,r.jsx)(i.CG,{className:"border-t border-border/50 p-2",children:(0,r.jsx)(j,{user:{name:e,avatar:s}})}),(0,r.jsx)(i.jM,{})]})}}};