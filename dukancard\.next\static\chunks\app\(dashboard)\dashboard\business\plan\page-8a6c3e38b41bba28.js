(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5874],{27737:(e,a,t)=>{"use strict";t.d(a,{E:()=>n});var r=t(95155),s=t(53999);function n(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",a),...t})}},55747:(e,a,t)=>{"use strict";t.d(a,{C:()=>o,z:()=>l});var r=t(95155);t(12115);var s=t(54059),n=t(9428),i=t(53999);function l(e){let{className:a,...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"radio-group",className:(0,i.cn)("grid gap-3",a),...t})}function o(e){let{className:a,...t}=e;return(0,r.jsx)(s.q7,{"data-slot":"radio-group-item",className:(0,i.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,r.jsx)(s.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,r.jsx)(n.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},73317:(e,a,t)=>{"use strict";t.d(a,{Z:()=>n});var r=t(75168);class s{subscribeToTable(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0,{event:s="*",filter:n,schema:i="public"}=t,l=r||"".concat(e,"-").concat(s,"-").concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9));this.subscriptions.has(l)&&this.unsubscribe(l);let o=this.supabase.channel(l).on("postgres_changes",{event:s,schema:i,table:e,...n&&{filter:n}},a).subscribe();return this.subscriptions.set(l,o),{unsubscribe:()=>this.unsubscribe(l),channel:o}}subscribeToBusinessActivities(e,a,t){let r="business-activities-".concat(e).concat(t?"-".concat(t):"");return this.subscribeToTable("business_activities",a,{event:"INSERT",filter:"business_profile_id=eq.".concat(e)},r)}subscribeToBusinessProfile(e,a,t){let r="business-profile-".concat(e).concat(t?"-".concat(t):"");return this.subscribeToTable("business_profiles",a,{event:"*",filter:"id=eq.".concat(e)},r)}subscribeToMonthlyMetrics(e,a,t){let r="monthly-metrics-".concat(e).concat(t?"-".concat(t):"");return this.subscribeToTable("monthly_visit_metrics",a,{event:"*",filter:"business_profile_id=eq.".concat(e)},r)}subscribeToPaymentSubscriptions(e,a,t){let r="payment-subscriptions-".concat(e).concat(t?"-".concat(t):"");return this.subscribeToTable("payment_subscriptions",a,{event:"*",filter:"business_profile_id=eq.".concat(e)},r)}unsubscribe(e){let a=this.subscriptions.get(e);a&&(this.supabase.removeChannel(a),this.subscriptions.delete(e))}unsubscribeAll(){this.subscriptions.forEach((e,a)=>{this.supabase.removeChannel(e)}),this.subscriptions.clear()}getActiveSubscriptionCount(){return this.subscriptions.size}getActiveChannelIds(){return Array.from(this.subscriptions.keys())}constructor(){this.subscriptions=new Map,this.supabase=(0,r.U)()}}let n=new s},75202:(e,a,t)=>{"use strict";t.d(a,{default:()=>eH});var r=t(95155),s=t(12115);let n=(0,s.createContext)(void 0);function i(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useSubscriptionProcessing must be used within a SubscriptionProcessingProvider");return e}function l(e){let{children:a}=e,[t,i]=(0,s.useState)("idle"),[l,o]=(0,s.useState)(""),d=(0,s.useRef)(new Set),c=(0,s.useCallback)(()=>{d.current.forEach(e=>{clearTimeout(e)}),d.current.clear()},[]),u=(0,s.useCallback)((e,a)=>{let t=setTimeout(()=>{e(),d.current.delete(t)},a);return d.current.add(t),t},[]),m=(0,s.useCallback)(()=>{i("idle"),o(""),c()},[c]),p=(0,s.useCallback)((e,a)=>{e?i("subscription_created"):i("error"),a?o(a):o(e?"Your subscription has been successfully created.":"There was an error processing your subscription. Please try again."),e||u(()=>{m()},5e3)},[u,m]);(0,s.useEffect)(()=>()=>{c()},[c]);let b=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Waiting for payment confirmation...";return i("waiting_for_webhook"),o(e),u(()=>{i(e=>"waiting_for_webhook"===e?(console.log("[SUBSCRIPTION_PROCESSING] Auto-resetting webhook waiting state after timeout"),o(""),"idle"):e)},3e4)},[u]),x=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Subscription authorized. Payment will be processed when your trial ends.";i("future_payment_authorized"),o(e),u(()=>{m()},8e3)},[u,m]),h=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Your subscription has been halted.";i("subscription_halted"),o(e),u(()=>{m()},8e3)},[u,m]),g=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Your subscription has been cancelled successfully.";i("subscription_cancelled"),o(e),u(()=>{m()},8e3)},[u,m]),f=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Your subscription has been completed.";i("subscription_completed"),o(e),u(()=>{m()},8e3)},[u,m]),v=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Your subscription has expired.";i("subscription_expired"),o(e),u(()=>{m()},8e3)},[u,m]),y=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Razorpay servers are currently experiencing issues. Please try again in a few minutes.";i("razorpay_server_error"),o(e),u(()=>{m()},1e4)},[u,m]),j=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Your subscription has been paused. You can resume it anytime.";i("subscription_paused"),o(e),u(()=>{m()},8e3)},[u,m]),w=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Your subscription has been resumed successfully.";i("subscription_resumed"),o(e),u(()=>{m()},8e3)},[u,m]);return(0,r.jsx)(n.Provider,{value:{status:t,message:l,startProcessing:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please wait while we process your subscription";i("processing"),o(e)},completeProcessing:p,resetProcessing:m,setRazorpayServerError:y,setWaitingForWebhook:b,setPaymentAuthorized:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Payment authorized, setting up your subscription...";i("payment_authorized"),o(e)},setPaymentCaptured:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Payment captured, activating your subscription...";i("payment_captured"),o(e)},setFuturePaymentAuthorized:x,setPaymentMethodUpdated:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Updating your payment method...";i("processing"),o(e)},setSubscriptionCreated:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Subscription created, waiting for activation...";i("subscription_created"),o(e)},setSubscriptionAuthenticated:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Subscription authenticated, waiting for activation...";i("subscription_authenticated"),o(e)},setSubscriptionActive:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Your subscription is active!";i("subscription_active"),o(e)},setSubscriptionActivated:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Subscription activated successfully!";i("subscription_activated"),o(e)},setSubscriptionPending:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Your subscription is pending...";i("subscription_pending"),o(e)},setSubscriptionHalted:h,setSubscriptionCancelled:g,setSubscriptionCompleted:f,setSubscriptionExpired:v,setSubscriptionPaused:j,setSubscriptionResumed:w,setRefundProcessing:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Processing your refund request...";i("refund_processing"),o(e)},setRefundProcessed:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Refund processed successfully!";i("refund_processed"),o(e)}},children:a})}var o=t(74466),d=t(54416),c=t(53999);let u=(0,o.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),m=s.forwardRef((e,a)=>{let{className:t,variant:s,title:n,description:i,action:l,...o}=e;return(0,r.jsxs)("div",{ref:a,className:(0,c.cn)(u({variant:s}),t),...o,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[n&&(0,r.jsx)("div",{className:"text-sm font-semibold",children:n}),i&&(0,r.jsx)("div",{className:"text-sm opacity-90",children:i})]}),l,(0,r.jsxs)("button",{className:"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})});m.displayName="Toast";let p=(0,s.createContext)(void 0);function b(e){let{children:a}=e,[t,n]=(0,s.useState)([]);return(0,r.jsxs)(p.Provider,{value:{toast:e=>{let a=Math.random().toString(36).substring(2,9);n(t=>[...t,{...e,id:a}]),setTimeout(()=>{n(e=>e.filter(e=>e.id!==a))},5e3)}},children:[a,(0,r.jsx)("div",{className:"fixed bottom-0 right-0 z-50 flex flex-col gap-2 p-4 md:max-w-[420px]",children:t.map((e,a)=>(0,r.jsx)(m,{...e},a))})]})}var x=t(35695),h=t(56671),g=t(73317),f=t(28695),v=t(53311),y=t(81586),j=t(60760);let w=e=>{var a;let{tabs:t,defaultTab:n,value:i,onChange:l,className:o,tabClassName:d,activeTabClassName:u,inactiveTabClassName:m,indicatorClassName:p,indicatorLayoutId:b="activeTab",children:x}=e,[h,g]=(0,s.useState)(i||n||(null==(a=t[0])?void 0:a.id)),[v,y]=(0,s.useState)(null);(0,s.useEffect)(()=>{i&&i!==h&&g(i)},[i,h]);let w=e=>{g(e),null==l||l(e)};return(0,r.jsxs)("div",{className:(0,c.cn)("w-full space-y-4",o),children:[(0,r.jsxs)("div",{className:"relative flex items-center justify-center p-1 rounded-xl bg-gradient-to-br from-white/80 to-white/40 dark:from-black/40 dark:to-black/20 backdrop-blur-sm border border-neutral-200/80 dark:border-neutral-800/80 shadow-sm",children:[(0,r.jsx)("div",{className:"absolute inset-0 overflow-hidden rounded-xl",children:Array.from({length:5}).map((e,a)=>(0,r.jsx)(f.P.div,{className:"absolute w-1 h-1 rounded-full bg-primary/30",initial:{x:"".concat(100*Math.random(),"%"),y:"".concat(100*Math.random(),"%"),opacity:.2},animate:{y:["".concat(100*Math.random(),"%"),"".concat(100*Math.random(),"%")],opacity:[.1,.3,.1]},transition:{repeat:1/0,duration:5*Math.random()+5,ease:"easeInOut"}},a))}),(0,r.jsx)("div",{className:"relative z-10 flex w-full rounded-lg p-1",children:t.map(e=>{let a=h===e.id,t=v===e.id;return(0,r.jsxs)(f.P.button,{className:(0,c.cn)("relative flex-1 flex items-center justify-center gap-2 py-2.5 px-3 text-sm font-medium rounded-lg transition-all duration-200 z-10",d,a?(0,c.cn)("text-primary-foreground",u):(0,c.cn)("text-muted-foreground hover:text-foreground",m)),onClick:()=>w(e.id),onMouseEnter:()=>y(e.id),onMouseLeave:()=>y(null),whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,r.jsxs)("div",{className:"relative z-10 flex items-center gap-2",children:[e.icon&&(0,r.jsx)(f.P.div,{animate:{rotate:t?[0,-5,5,0]:0,scale:t?[1,1.1,1]:1},transition:{duration:.5},children:e.icon}),(0,r.jsx)("span",{children:e.label})]}),a&&(0,r.jsx)(f.P.div,{className:(0,c.cn)("absolute inset-0 rounded-lg bg-primary shadow-md",p),layoutId:b,transition:{type:"spring",bounce:.2,duration:.6}})]},e.id)})})]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(j.N,{mode:"wait",children:s.Children.map(x,e=>{if(!s.isValidElement(e))return null;let a=e.props;return a.value!==h?null:(0,r.jsx)(f.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.3},className:"w-full",children:e},a.value)})})})]})},N=e=>{let{value:a,children:t,className:s}=e;return(0,r.jsx)("div",{className:(0,c.cn)("w-full",s),"data-value":a,children:t})};var k=t(51154),P=t(43453),A=t(54861),C=t(85339),S=t(14186),E=t(53904);function I(){let{status:e,message:a,resetProcessing:t}=i();return(0,s.useEffect)(()=>{if("idle"===e)return void h.oR.dismiss();let s=(e=>{switch(e){case"processing":return{type:"loading",icon:(0,r.jsx)(k.A,{className:"h-5 w-5 animate-spin"}),title:"Processing",description:a,duration:0};case"success":return{type:"success",icon:(0,r.jsx)(P.A,{className:"h-5 w-5"}),title:"Success",description:a,duration:5e3};case"error":return{type:"error",icon:(0,r.jsx)(A.A,{className:"h-5 w-5"}),title:"Error",description:a,duration:5e3};case"razorpay_server_error":return{type:"error",icon:(0,r.jsx)(C.A,{className:"h-5 w-5"}),title:"Server Error",description:a,duration:8e3};case"waiting_for_webhook":return{type:"loading",icon:(0,r.jsx)(S.A,{className:"h-5 w-5 text-yellow-500"}),title:"Waiting",description:a,duration:0};case"payment_authorized":case"payment_captured":return{type:"success",icon:(0,r.jsx)(y.A,{className:"h-5 w-5"}),title:"Payment",description:a,duration:5e3};case"future_payment_authorized":return{type:"success",icon:(0,r.jsx)(S.A,{className:"h-5 w-5"}),title:"Subscription Authorized",description:a,duration:8e3};case"subscription_created":case"subscription_authenticated":case"subscription_active":case"subscription_activated":return{type:"success",icon:(0,r.jsx)(P.A,{className:"h-5 w-5"}),title:"Subscription",description:a,duration:5e3};case"subscription_resumed":return{type:"success",icon:(0,r.jsx)(P.A,{className:"h-5 w-5 text-green-500"}),title:"Subscription Resumed",description:a,duration:8e3};case"subscription_pending":return{type:"info",icon:(0,r.jsx)(S.A,{className:"h-5 w-5"}),title:"Subscription",description:a,duration:5e3};case"subscription_halted":return{type:"info",icon:(0,r.jsx)(C.A,{className:"h-5 w-5"}),title:"Subscription Halted",description:a,duration:5e3};case"subscription_cancelled":return{type:"info",icon:(0,r.jsx)(A.A,{className:"h-5 w-5 text-red-500"}),title:"Subscription Cancelled",description:a,duration:8e3};case"subscription_completed":return{type:"info",icon:(0,r.jsx)(P.A,{className:"h-5 w-5 text-green-500"}),title:"Subscription Completed",description:a,duration:5e3};case"subscription_expired":return{type:"info",icon:(0,r.jsx)(S.A,{className:"h-5 w-5 text-amber-500"}),title:"Subscription Expired",description:a,duration:5e3};case"subscription_paused":return{type:"info",icon:(0,r.jsx)(C.A,{className:"h-5 w-5 text-amber-500"}),title:"Subscription Paused",description:a,duration:8e3};case"refund_processing":return{type:"loading",icon:(0,r.jsx)(E.A,{className:"h-5 w-5 animate-spin"}),title:"Refund",description:a,duration:0};case"refund_processed":return{type:"success",icon:(0,r.jsx)(P.A,{className:"h-5 w-5"}),title:"Refund",description:a,duration:5e3};default:return{type:"info",icon:(0,r.jsx)(C.A,{className:"h-5 w-5"}),title:"Information",description:a,duration:5e3}}})(e),n="subscription-".concat(e,"-").concat(Date.now());switch(s.type){case"success":h.oR.success(s.title,{id:n,description:s.description,duration:s.duration,icon:s.icon});break;case"error":h.oR.error(s.title,{id:n,description:s.description,duration:s.duration,icon:s.icon});break;case"loading":h.oR.loading(s.title,{id:n,description:s.description,duration:s.duration,icon:s.icon});break;default:h.oR.info(s.title,{id:n,description:s.description,duration:s.duration,icon:s.icon})}if("error"===e||"razorpay_server_error"===e){let e=setTimeout(()=>{t()},5e3);return()=>clearTimeout(e)}},[e,a,t]),null}var R=t(41457),T=t(56018),_=t(97168);function D(e){let{billingCycle:a,setBillingCycle:t}=e;return(0,r.jsxs)("div",{className:"relative inline-flex",children:[(0,r.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/30 rounded-full blur-md"}),(0,r.jsxs)("div",{className:"space-x-2 bg-white/80 dark:bg-black/50 backdrop-blur-sm p-1.5 rounded-full border border-neutral-200/50 dark:border-neutral-800/50 inline-flex shadow-md relative z-10",children:[(0,r.jsx)(_.$,{onClick:()=>t("monthly"),variant:"monthly"===a?"default":"ghost",size:"sm",className:"cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ".concat("monthly"===a?"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md":"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"),children:(0,r.jsx)("span",{className:"monthly"===a?"font-medium":"font-normal",children:"Monthly"})}),(0,r.jsxs)(_.$,{onClick:()=>t("yearly"),variant:"yearly"===a?"default":"ghost",size:"sm",className:"cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2 ".concat("yearly"===a?"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md":"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"),children:[(0,r.jsx)("span",{className:"yearly"===a?"font-medium":"font-normal",children:"Yearly"}),(0,r.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ".concat("yearly"===a?"bg-white/90 text-[var(--brand-gold)]":"bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400"),children:"Save 20%"})]})]})]})}function z(e){let{subscriptionStatus:a,billingCycle:t,setBillingCycle:s,plans:n,currentPlanId:i,currentPlanCycle:l,loadingStates:o,onPlanAction:d}=e,c={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4}}},u=e=>e.id===i&&t===l&&("active"===a||"authenticated"===a),m=e=>"free"===e.id||!e.available;return(0,r.jsx)(T.S,{children:(0,r.jsxs)(f.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black backdrop-blur-sm shadow-lg transition-all duration-300 hover:shadow-xl relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent dark:from-blue-500/10 dark:to-transparent pointer-events-none"}),(0,r.jsxs)("div",{className:"relative z-10 p-4 sm:p-6 md:p-8",children:[(0,r.jsx)(f.P.div,{variants:c,className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,r.jsx)(v.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Available Plans"}),(0,r.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"inactive"===a?"Select a subscription plan that best fits your business needs and unlock premium features":"View and compare available subscription options to find the perfect fit for your business"})]})]})}),(0,r.jsx)(f.P.div,{variants:c,className:"flex justify-center mb-8",children:(0,r.jsx)(D,{billingCycle:t,setBillingCycle:s})}),(0,r.jsx)(f.P.div,{variants:c,children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 md:gap-4 items-stretch",children:n.map((e,a)=>{let s,n=u(e),l=m(e);l?"free"===e.id&&(s="free"===i?"Current Plan":"Auto Managed"):n||(s="View Plan");let c={...e,available:!l&&e.available};return(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)(R.A,{plan:c,index:a,isLoading:o[e.id],isCurrentPlan:n||"free"===e.id&&"free"===i,buttonTextOverride:s,onButtonClick:()=>d(e)})},"".concat(e.id,"-").concat(t))})})})]})]})})}var L=t(1243),O=t(8830),M=t(1117);function H(e){let{trialEndDate:a}=e,t=new Date(a),s=(0,O.m)(t,{addSuffix:!0}),n=t.getTime()-Date.now()<2592e5;return(0,r.jsx)("div",{className:"w-full opacity-100 transition-all duration-500",style:{transform:"translateY(0)"},children:(0,r.jsxs)("div",{className:"w-full rounded-lg border ".concat(n?"border-amber-200 dark:border-amber-800/50 bg-gradient-to-r from-amber-50 to-amber-100/70 dark:from-amber-950/40 dark:to-amber-900/30":"border-blue-200 dark:border-blue-800/50 bg-gradient-to-r from-blue-50 to-blue-100/70 dark:from-blue-950/40 dark:to-blue-900/30"," backdrop-blur-sm shadow-sm relative overflow-hidden"),children:[(0,r.jsx)("div",{className:"absolute inset-0 pointer-events-none",children:(0,r.jsx)("div",{className:"absolute inset-0 ".concat(n?"bg-amber-400/5 dark:bg-amber-400/10":"bg-blue-400/5 dark:bg-blue-400/10"," rounded-full blur-3xl")})}),(0,r.jsx)("div",{className:"relative z-10 p-4",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[n?(0,r.jsx)(L.A,{className:"h-5 w-5 flex-shrink-0 text-amber-600 dark:text-amber-500 mt-0.5"}):(0,r.jsx)(S.A,{className:"h-5 w-5 flex-shrink-0 text-blue-600 dark:text-blue-500 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-base font-semibold ".concat(n?"text-amber-800 dark:text-amber-400":"text-blue-800 dark:text-blue-400"),children:n?"Your trial is ending soon!":"You're on a free trial"}),(0,r.jsxs)("p",{className:"text-sm mt-1 ".concat(n?"text-amber-700 dark:text-amber-500":"text-blue-700 dark:text-blue-500"),children:["Your trial period ends ",s,". Choose a plan below to continue using premium features."]})]})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"rounded-md p-3 ".concat(n?"bg-amber-100/50 dark:bg-amber-900/30 border border-amber-200/50 dark:border-amber-800/30":"bg-blue-100/50 dark:bg-blue-900/30 border border-blue-200/50 dark:border-blue-800/30"),children:(0,r.jsx)(M.A,{endDate:a,label:"Trial ends in:",tooltipText:"Your trial will end on this date. Choose a plan to continue using premium features."})})})]})})]})})}var U=t(99840),F=t(88482);let Y=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("span",{ref:a,className:(0,c.cn)("absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0",t),style:{clip:"rect(0 0 0 0)",clipPath:"inset(50%)"},...s})});function B(e){let{isOpen:a,message:t="Waiting for confirmation from Razorpay",description:n="Please wait while we receive webhook confirmation. This may take a moment."}=e,[i,l]=s.useState("");return s.useEffect(()=>{if(!a)return;let e=setInterval(()=>{l(e=>e.length>=3?"":e+".")},500);return()=>clearInterval(e)},[a]),(0,r.jsx)(U.lG,{open:a,modal:!0,children:(0,r.jsxs)(U.Cf,{className:"max-w-md mx-auto p-0 border-none shadow-lg",hideClose:!0,onEscapeKeyDown:e=>e.preventDefault(),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(U.L3,{asChild:!0,children:(0,r.jsx)(Y,{children:t})}),(0,r.jsx)(F.Zp,{className:"overflow-hidden border-primary/20",children:(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute -inset-4 rounded-full bg-primary/10 animate-pulse blur-xl opacity-75"}),(0,r.jsx)(k.A,{className:"h-12 w-12 animate-spin text-primary relative z-10"})]}),(0,r.jsxs)("h3",{className:"mt-6 text-lg font-medium text-foreground",children:[t,(0,r.jsx)("span",{className:"inline-block w-8 text-left",children:i})]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:n}),(0,r.jsx)("p",{className:"mt-4 text-xs text-muted-foreground italic",children:"This may take a few moments. Please don't refresh the page."})]})})})]})})}function G(e){let{isVisible:a,message:t="Waiting for confirmation from Razorpay",description:s="Please wait while we receive webhook confirmation. This may take a moment."}=e;return(0,r.jsx)(B,{isOpen:a,message:t,description:s})}Y.displayName="VisuallyHidden";var q=t(74695),W=t(30529);function V(e){let{subscriptionId:a,status:t,planName:s,planCycle:n,amount:i,currency:l="INR",nextBillingDate:o,lastPaymentDate:d,lastPaymentMethod:c,createdAt:u,expiresAt:m,pausedAt:p,cancellationRequestedAt:b,cancelledAt:x,isEligibleForRefund:h,subscriptionStartDate:g,subscriptionChargeTime:v,trialEndDate:w}=e,N=e=>{if(!e)return"Not available";try{return new Date(e).toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})}catch(e){return"Invalid date"}},k=e=>{if(!e)return"";try{return(0,O.m)(new Date(e),{addSuffix:!0})}catch(e){return""}},C=(e=>{switch(e){case W.dH.ACTIVE:return{color:"bg-green-500",textColor:"text-green-700 dark:text-green-300",borderColor:"border-green-200 dark:border-green-800",bgColor:"bg-green-50 dark:bg-green-900/20",icon:(0,r.jsx)(P.A,{className:"h-5 w-5 text-green-500"}),text:"Active",description:"Your subscription is active and all features are available."};case W.dH.AUTHENTICATED:return{color:"bg-blue-500",textColor:"text-blue-700 dark:text-blue-300",borderColor:"border-blue-200 dark:border-blue-800",bgColor:"bg-blue-50 dark:bg-blue-900/20",icon:(0,r.jsx)(P.A,{className:"h-5 w-5 text-blue-500"}),text:"Authenticated",description:"Your subscription has been authenticated and will be activated soon."};case W.dH.PENDING:return{color:"bg-yellow-500",textColor:"text-yellow-700 dark:text-yellow-300",borderColor:"border-yellow-200 dark:border-yellow-800",bgColor:"bg-yellow-50 dark:bg-yellow-900/20",icon:(0,r.jsx)(S.A,{className:"h-5 w-5 text-yellow-500"}),text:"Pending",description:"Your subscription is pending activation."};case W.dH.HALTED:case"paused":return{color:"bg-orange-500",textColor:"text-orange-700 dark:text-orange-300",borderColor:"border-orange-200 dark:border-orange-800",bgColor:"bg-orange-50 dark:bg-orange-900/20",icon:(0,r.jsx)(q.A,{className:"h-5 w-5 text-orange-500"}),text:"Paused",description:"Your subscription has been temporarily paused."};case W.dH.CANCELLED:return{color:"bg-red-500",textColor:"text-red-700 dark:text-red-300",borderColor:"border-red-200 dark:border-red-800",bgColor:"bg-red-50 dark:bg-red-900/20",icon:(0,r.jsx)(A.A,{className:"h-5 w-5 text-red-500"}),text:"Cancelled",description:"Your subscription has been cancelled."};default:return{color:"bg-gray-500",textColor:"text-gray-700 dark:text-gray-300",borderColor:"border-gray-200 dark:border-gray-800",bgColor:"bg-gray-50 dark:bg-gray-900/20",icon:(0,r.jsx)(L.A,{className:"h-5 w-5 text-gray-500"}),text:"Unknown",description:"Subscription status is unknown."}}})(t),E=(e=>{if(!e)return t===W.dH.AUTHENTICATED?{icon:(0,r.jsx)(S.A,{className:"h-4 w-4 text-blue-500"}),text:"Pending"}:{icon:(0,r.jsx)(y.A,{className:"h-4 w-4"}),text:"Not available"};let a=e.toLowerCase();return"card"===a?{icon:(0,r.jsx)(y.A,{className:"h-4 w-4 text-blue-500"}),text:"Credit/Debit Card"}:"upi"===a?{icon:(0,r.jsx)(y.A,{className:"h-4 w-4 text-green-500"}),text:"UPI"}:"netbanking"===a?{icon:(0,r.jsx)(y.A,{className:"h-4 w-4 text-purple-500"}),text:"Net Banking"}:"wallet"===a?{icon:(0,r.jsx)(y.A,{className:"h-4 w-4 text-orange-500"}),text:"Wallet"}:{icon:(0,r.jsx)(y.A,{className:"h-4 w-4"}),text:e}})(c);return(0,r.jsx)(j.N,{children:(0,r.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full",children:(0,r.jsxs)("div",{className:"overflow-hidden bg-white dark:bg-black border border-neutral-200/80 dark:border-neutral-800/80 rounded-lg relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent dark:from-blue-500/10 dark:to-transparent pointer-events-none"}),(0,r.jsxs)("div",{className:"p-0 relative z-10",children:[(0,r.jsx)("div",{className:"p-6 bg-white dark:bg-black border-b border-neutral-200/80 dark:border-neutral-800/80",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(f.P.div,{whileHover:{scale:1.1,rotate:5},transition:{type:"spring",stiffness:400,damping:10},className:"w-12 h-12 rounded-xl flex items-center justify-center ".concat(C.color," bg-opacity-20 dark:bg-opacity-30"),children:(0,r.jsx)("div",{className:"text-[var(--primary)]",children:C.icon})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold",children:"Your Subscription"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:C.description})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 p-4 rounded-xl bg-white/50 dark:bg-black/30 border border-neutral-200/80 dark:border-neutral-800/80",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider",children:"Plan"}),(0,r.jsxs)("span",{className:"font-medium",children:[s," (",n,")"]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider",children:"Amount"}),(0,r.jsxs)("span",{className:"font-medium",children:["₹ ",i.toLocaleString()]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider",children:"Payment Method"}),(0,r.jsxs)("span",{className:"font-medium flex items-center gap-1",children:[E.icon," ",E.text]})]})]})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)(f.P.div,{className:"relative overflow-hidden rounded-xl bg-gradient-to-br from-white/90 to-white/70 dark:from-black/90 dark:to-black/70 border border-neutral-200/80 dark:border-neutral-800/80",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)(f.P.div,{className:"absolute rounded-full ".concat(C.color," blur-md"),initial:{x:"50%",y:"30%",scale:1,opacity:.6},animate:{scale:[1,1.05,1],opacity:[.6,.7,.6]},transition:{repeat:1/0,repeatType:"reverse",duration:8,ease:"easeInOut"},style:{width:"120px",height:"120px",transform:"translate(-50%, -50%)"}}),Array.from({length:12}).map((e,a)=>{let t,s,n=80*Math.random()+20,i=.3*Math.random()+.1;a<4?(t=100*Math.random(),s=30*Math.random()):a<8?(t=100*Math.random(),s=30+40*Math.random()):(t=100*Math.random(),s=70+30*Math.random());let l=3+Math.floor(3*Math.random()),o=Array.from({length:l},()=>100*Math.random()),d=Array.from({length:l},()=>100*Math.random()),c=15+(20*Math.random()+15)*(1-.5*(1-(n-20)/80)),u=5*Math.random(),m=Math.floor(3*Math.random())+1;return(0,r.jsx)(f.P.div,{className:"absolute rounded-full ".concat(C.color," ").concat("blur-".concat(1===m?"md":2===m?"lg":"xl")),initial:{left:"".concat(t,"%"),top:"".concat(s,"%"),opacity:i,x:"0%",y:"0%"},animate:{x:o.map(e=>"".concat(e-t,"%")),y:d.map(e=>"".concat(e-s,"%")),opacity:[i,...Array(l-1).fill(1.2*i),i],scale:[1,...Array(l-1).fill(0).map(()=>1+.3*Math.random()),1]},transition:{repeat:1/0,repeatType:"reverse",duration:c,ease:"easeInOut",delay:u,times:Array.from({length:l+1},(e,a)=>a/l)},style:{width:"".concat(n,"px"),height:"".concat(n,"px"),zIndex:Math.floor(10*Math.random())}},"circle-".concat(a))}),Array.from({length:8}).map((e,a)=>{let t,s,n=15*Math.random()+5;a<2?(t=40*Math.random(),s=40*Math.random()):a<4?(t=60+40*Math.random(),s=40*Math.random()):(t=a<6?40*Math.random():60+40*Math.random(),s=60+40*Math.random());let i=Array.from({length:4},()=>100*Math.random()),l=Array.from({length:4},()=>100*Math.random()),o=15*Math.random()+10;return(0,r.jsx)(f.P.div,{className:"absolute rounded-full ".concat(C.color," blur-sm"),initial:{left:"".concat(t,"%"),top:"".concat(s,"%"),opacity:.2,x:"0%",y:"0%"},animate:{x:i.map(e=>"".concat(e-t,"%")),y:l.map(e=>"".concat(e-s,"%")),opacity:[.1,.3,.2,.3,.1]},transition:{repeat:1/0,repeatType:"mirror",duration:o,ease:"easeInOut",delay:2*Math.random()},style:{width:"".concat(n,"px"),height:"".concat(n,"px")}},"particle-".concat(a))})]}),(0,r.jsxs)("div",{className:"relative z-10 p-8 flex flex-col items-center text-center",children:[(0,r.jsxs)(f.P.div,{className:"w-24 h-24 rounded-full ".concat(C.color," flex items-center justify-center mb-8 relative"),initial:{scale:.8},animate:{scale:1},transition:{type:"spring",stiffness:300,damping:15},children:[(0,r.jsx)("div",{className:"absolute inset-0 ".concat(C.color," rounded-full blur-lg opacity-50")}),(0,r.jsx)(f.P.div,{className:"absolute inset-0 ".concat(C.color," rounded-full"),initial:{scale:.85,opacity:.5},animate:{scale:[.85,1.15,.85],opacity:[.5,0,.5]},transition:{repeat:1/0,duration:3,ease:"easeInOut"}}),(0,r.jsx)("div",{className:"text-white relative z-10",children:(0,r.jsx)("div",{className:"text-[2.5rem]",children:C.icon})})]}),(0,r.jsxs)(f.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-3xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80",children:C.text}),(0,r.jsx)("p",{className:"text-muted-foreground text-base",children:C.description})]}),(0,r.jsxs)(f.P.div,{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 w-full mt-6",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:[u&&(0,r.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Created"}),(0,r.jsx)("span",{className:"font-medium",children:N(u)}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:k(u)})]}),t===W.dH.CANCELLED&&x&&(0,r.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Cancelled"}),(0,r.jsx)("span",{className:"font-medium",children:N(x)}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:k(x)})]}),t===W.dH.ACTIVE&&d&&(0,r.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Last Payment"}),(0,r.jsx)("span",{className:"font-medium",children:N(d)}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:k(d)})]}),t===W.dH.ACTIVE&&h&&(0,r.jsxs)("div",{className:"bg-purple-50/30 dark:bg-purple-900/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-purple-100/30 dark:border-purple-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-purple-600 dark:text-purple-400 uppercase tracking-wider mb-1",children:"Refund Eligible"}),(0,r.jsx)("span",{className:"font-medium text-purple-700 dark:text-purple-300",children:"7-Day Refund Window"}),(0,r.jsx)("span",{className:"text-xs text-purple-600/80 dark:text-purple-400/80",children:"Full refund available"})]}),w&&new Date(w)>new Date&&(0,r.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Trial Ends"}),(0,r.jsx)("span",{className:"font-medium",children:N(w)}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:k(w)})]}),"authenticated"===t&&g&&(0,r.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Starts On"}),(0,r.jsx)("span",{className:"font-medium",children:N(g)}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:k(g)})]}),"authenticated"===t&&!g&&o&&(0,r.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Starts On"}),(0,r.jsx)("span",{className:"font-medium",children:N(o)}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:k(o)})]}),"authenticated"===t&&v&&(0,r.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"First Payment On"}),(0,r.jsx)("span",{className:"font-medium",children:N(v)}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:k(v)})]}),(t===W.dH.ACTIVE||t===W.dH.AUTHENTICATED)&&m&&(0,r.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Current Period Ends"}),(0,r.jsx)("span",{className:"font-medium",children:N(m)}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:k(m)})]})]})]})]})})]})]})})})}var $=t(88524),Z=t(46308),K=t(57434),X=t(69074),J=t(91788),Q=t(75168),ee=t(93432);function ea(){let{subscriptionId:e,planId:a=""}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[n,i]=(0,s.useState)([]),[l,o]=(0,s.useState)(!0),[d,u]=(0,s.useState)(null),[m,p]=(0,s.useState)(1),[b]=(0,s.useState)(5),[x,g]=(0,s.useState)({page:1,count:b,totalCount:0,totalPages:0}),v={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3}}},y=(0,s.useCallback)(async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;o(!0),u(null);try{let t=await fetch("/api/subscriptions/".concat(e,"/invoices?page=").concat(a,"&count=").concat(b)),r=await t.json();if(!t.ok)throw Error(r.error||"Failed to fetch invoices");r.success&&r.invoices&&r.invoices.items?(i(r.invoices.items),r.pagination?g(r.pagination):g({page:a,count:b,totalCount:r.invoices.count||0,totalPages:Math.ceil((r.invoices.count||0)/b)})):r.success&&r.invoices&&Array.isArray(r.invoices)?(i(r.invoices),g({page:a,count:b,totalCount:r.invoices.length,totalPages:Math.ceil(r.invoices.length/b)})):(i([]),console.log("[RAZORPAY_DEBUG] No invoices found or unexpected response format:",r),g({page:1,count:b,totalCount:0,totalPages:0}))}catch(e){console.error("[RAZORPAY_ERROR] Error fetching invoices:",e),u(e instanceof Error?e.message:"Failed to fetch invoices")}finally{o(!1)}},[b]);(0,s.useEffect)(()=>{(async()=>{let{SubscriptionStateManager:r}=await Promise.resolve().then(t.bind(t,30529));if(r.isFreeStatus("",a)){o(!1),i([]);return}let s=async()=>{if(!e)try{let e=(0,Q.U)(),{data:a,error:t}=await e.from("payment_subscriptions").select("razorpay_subscription_id, plan_id").eq("subscription_status","active").limit(1);if(t)throw t;if(a&&a.length>0&&r.isFreeStatus("",a[0].plan_id))return void o(!1);a&&a.length>0&&a[0].razorpay_subscription_id?y(a[0].razorpay_subscription_id,m):o(!1)}catch(e){console.error("[RAZORPAY_ERROR] Error fetching subscription ID:",e),u(e instanceof Error?e.message:"Failed to fetch subscription details"),o(!1)}};e?y(e,m):s()})()},[e,y,m,a]);let j=(e,a)=>new Intl.NumberFormat("en-IN",{style:"currency",currency:a||"INR",minimumFractionDigits:2}).format(e/100),w=a=>{a<1||a>x.totalPages||a===m||(p(a),e&&y(e,a))};return(0,r.jsxs)(F.Zp,{className:"border shadow-sm transition-all duration-300 hover:shadow-md h-full bg-white dark:bg-black",children:[(0,r.jsx)(F.aR,{className:"bg-gradient-to-br from-transparent to-blue-50/50 dark:to-blue-900/10",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.P.div,{whileHover:{rotate:[0,-10,10,-5,0],transition:{duration:.5}},className:"p-2 rounded-full bg-blue-100/50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400",children:(0,r.jsx)(Z.A,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(F.ZB,{children:"Invoice History"}),(0,r.jsx)(F.BT,{children:"View and download your invoice history"})]})]}),e&&(0,r.jsxs)(_.$,{variant:"outline",size:"sm",onClick:()=>{y(e,m),h.oR.info("Refreshing invoice history...")},disabled:l,className:"bg-white dark:bg-black/20 border-blue-200 dark:border-blue-800/50 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200",children:[(0,r.jsx)(E.A,{className:(0,c.cn)("w-4 h-4 mr-2 text-blue-600 dark:text-blue-400",l&&"animate-spin")}),"Refresh"]})]})}),(0,r.jsx)(F.Wu,{className:"p-0",children:(0,r.jsx)(f.P.div,{variants:v,className:"p-4 sm:p-6",children:l?(0,r.jsxs)(f.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"space-y-4 py-4",children:[(0,r.jsx)(f.P.div,{className:"flex items-center justify-center mb-6",animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},children:(0,r.jsx)(E.A,{className:"w-6 h-6 text-blue-500"})}),(0,r.jsx)("div",{className:"h-10 w-full bg-blue-100/50 dark:bg-blue-900/20 rounded-md animate-pulse"}),(0,r.jsx)("div",{className:"h-16 w-full bg-blue-100/30 dark:bg-blue-900/10 rounded-md animate-pulse"}),(0,r.jsx)("div",{className:"h-16 w-full bg-blue-100/20 dark:bg-blue-900/5 rounded-md animate-pulse"})]},"loading"):d?(0,r.jsxs)(f.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,r.jsx)(f.P.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:300,damping:20},className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-full mb-4",children:(0,r.jsx)(K.A,{className:"w-8 h-8 text-red-500 dark:text-red-400"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2 text-red-600 dark:text-red-400",children:"Error Loading Invoices"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-sm mb-4",children:d}),e&&(0,r.jsxs)(_.$,{variant:"outline",size:"sm",onClick:()=>{y(e,m),h.oR.info("Retrying invoice fetch...")},className:"bg-white dark:bg-black/20 border-red-200 dark:border-red-800/50 hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400",children:[(0,r.jsx)(E.A,{className:"w-4 h-4 mr-2"}),"Try Again"]})]},"error"):0===n.length?(0,r.jsxs)(f.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"flex flex-col items-center justify-center py-10 text-center",children:[(0,r.jsx)(f.P.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:300,damping:20},className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-full mb-4",children:(0,r.jsx)(Z.A,{className:"w-10 h-10 text-blue-500 dark:text-blue-400"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Invoices Yet"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-sm",children:W.SubscriptionStateManager.isFreeStatus("",a)?"Free plan users don't have invoices. Upgrade to a paid plan to see invoices here.":"Invoices will appear here after your first payment"})]},"empty"):(0,r.jsxs)(f.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.05}}},initial:"hidden",animate:"visible",className:"overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-800",children:[(0,r.jsxs)($.XI,{children:[(0,r.jsx)($.A0,{className:"bg-gray-50 dark:bg-black/90",children:(0,r.jsxs)($.Hj,{children:[(0,r.jsx)($.nd,{className:"font-medium",children:"Invoice #"}),(0,r.jsx)($.nd,{className:"font-medium",children:"Date"}),(0,r.jsx)($.nd,{className:"font-medium",children:"Amount"}),(0,r.jsx)($.nd,{className:"font-medium",children:"Status"}),(0,r.jsx)($.nd,{className:"font-medium text-right",children:"Actions"})]})}),(0,r.jsx)($.BF,{children:n.map((e,a)=>(0,r.jsxs)(f.P.tr,{variants:v,className:(0,c.cn)("transition-colors hover:bg-blue-50/50 dark:hover:bg-blue-900/10",a%2==0?"bg-white dark:bg-black":"bg-blue-50/20 dark:bg-black/80"),children:[(0,r.jsx)($.nA,{className:"font-medium text-blue-700 dark:text-blue-300",children:e.id.replace("inv_","#")}),(0,r.jsx)($.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(X.A,{className:"w-4 h-4 text-blue-500 dark:text-blue-400"}),(0,r.jsx)("span",{children:(0,c.Yq)(new Date(1e3*e.date))})]})}),(0,r.jsx)($.nA,{className:"font-semibold",children:j(e.amount,e.currency)}),(0,r.jsx)($.nA,{children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("paid"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"pending"===e.status||"issued"===e.status?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,r.jsx)($.nA,{className:"text-right",children:e.short_url?(0,r.jsxs)(_.$,{variant:"ghost",size:"sm",className:"h-8 px-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20",onClick:()=>window.open(e.short_url,"_blank"),children:[(0,r.jsx)(J.A,{className:"h-4 w-4 mr-1"}),(0,r.jsx)("span",{className:"sr-only sm:not-sr-only sm:inline-block",children:"View"})]}):(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Not available"})})]},e.id))})]}),x.totalPages>1&&(0,r.jsx)("div",{className:"flex justify-center mt-4 pb-2",children:(0,r.jsx)(ee.dK,{children:(0,r.jsxs)(ee.Iu,{children:[m>1&&(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.Eb,{href:"#",onClick:e=>{e.preventDefault(),w(m-1)},className:"border-gray-200 dark:border-gray-800 hover:border-gray-400 dark:hover:border-gray-600 transition-colors"})}),m>2&&(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.n$,{href:"#",onClick:e=>{e.preventDefault(),w(1)},className:1===m?"bg-gray-100 dark:bg-gray-900":"",children:"1"})}),m>3&&(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.M_,{})}),m>1&&(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.n$,{href:"#",onClick:e=>{e.preventDefault(),w(m-1)},children:m-1})}),(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.n$,{href:"#",isActive:!0,onClick:e=>e.preventDefault(),children:m})}),m<x.totalPages&&(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.n$,{href:"#",onClick:e=>{e.preventDefault(),w(m+1)},children:m+1})}),m<x.totalPages-2&&(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.M_,{})}),m<x.totalPages-1&&(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.n$,{href:"#",onClick:e=>{e.preventDefault(),w(x.totalPages)},children:x.totalPages})}),m<x.totalPages&&(0,r.jsx)(ee.cU,{children:(0,r.jsx)(ee.WA,{href:"#",onClick:e=>{e.preventDefault(),w(m+1)},className:"border-gray-200 dark:border-gray-800 hover:border-gray-400 dark:hover:border-gray-600 transition-colors"})})]})})})]})})}),n.length>0&&(0,r.jsx)(F.wL,{className:"bg-white dark:bg-black border-t border-gray-200 dark:border-gray-800 py-3 px-6 text-xs text-muted-foreground",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.P.div,{whileHover:{rotate:360},transition:{duration:.5},className:"text-blue-500 dark:text-blue-400",children:(0,r.jsx)(Z.A,{className:"w-4 h-4"})}),(0,r.jsx)("span",{children:"Invoices are generated automatically by Razorpay when payments are processed."})]})})]})}var et=t(28833),er=t(381),es=t(75525),en=t(40646),ei=t(55747),el=t(82714),eo=t(49026),ed=t(88145);function ec(e){let{variant:a="gold",intensity:t="medium",className:n=""}=e,[i,l]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1);(0,s.useEffect)(()=>{l(!0);let e=()=>{d(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]);let c=(()=>{switch(t){case"low":return{light:.05,dark:.1};case"high":return{light:.15,dark:.25};default:return{light:.1,dark:.15}}})(),{primary:u,secondary:m}=(()=>{switch(a){case"blue":return{primary:"bg-blue-500/".concat(c.light," dark:bg-blue-500/").concat(c.dark),secondary:"bg-blue-300/5 dark:bg-blue-600/10"};case"gradient":return{primary:"bg-gradient-to-br from-[var(--brand-gold)]/".concat(c.light," to-blue-500/").concat(c.light," dark:from-[var(--brand-gold)]/").concat(c.dark," dark:to-blue-500/").concat(c.dark),secondary:"bg-transparent"};default:return{primary:"bg-[var(--brand-gold)]/".concat(c.light," dark:bg-[var(--brand-gold)]/").concat(c.dark),secondary:"bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10"}}})();return i?(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none ".concat(n),children:[(0,r.jsxs)(f.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"absolute inset-0",children:[(0,r.jsx)(f.P.div,{animate:{y:[0,-8,0],scale:[1,1.05,1]},transition:{duration:8,repeat:1/0,repeatType:"reverse",ease:"easeInOut"},className:"absolute top-0 right-0 w-[70%] h-[70%] rounded-full blur-3xl opacity-70 ".concat(u),style:{filter:o?"blur(40px)":"blur(60px)"}}),(0,r.jsx)(f.P.div,{animate:{y:[0,10,0],x:[0,-5,0],scale:[1,1.03,1]},transition:{duration:10,repeat:1/0,repeatType:"reverse",ease:"easeInOut"},className:"absolute bottom-0 left-0 w-[50%] h-[50%] rounded-full blur-3xl opacity-50 ".concat(m),style:{filter:o?"blur(30px)":"blur(50px)"}})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[url('/noise.svg')] opacity-5 dark:opacity-10 mix-blend-overlay"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-blue-500/5 dark:from-neutral-900/5 dark:to-blue-500/10"})]}):null}var eu=t(51362),em=t(92138);function ep(e){let{children:a,className:t="",variant:n="primary",size:i="default",showArrow:l=!1,roundedFull:o=!1,...d}=e,{resolvedTheme:u}=(0,eu.D)(),[m,p]=(0,s.useState)(!1),[b,x]=(0,s.useState)(!1);(0,s.useEffect)(()=>{x(!0)},[]);let h=!!b&&"dark"===u,g=(0,c.cn)("absolute -inset-0.5",o?"rounded-full":"rounded-md","bg-gradient-to-r from-[var(--brand-gold)]/40 to-[var(--brand-gold)]/60","blur-[2px]","max-w-full w-auto");return(0,r.jsxs)("div",{className:"relative group inline-flex max-w-full w-auto",children:[(0,r.jsx)("div",{className:g,style:{opacity:.5}}),b&&(0,r.jsx)(()=>b?(0,r.jsx)(f.P.div,{className:(0,c.cn)("absolute -inset-0.5",o?"rounded-full":"rounded-md","bg-gradient-to-r from-[var(--brand-gold)]/40 to-[var(--brand-gold)]/60",h?"blur-sm":"blur-[2px]","max-w-full w-auto"),initial:{opacity:.5},animate:{opacity:[.5,.7,.5],boxShadow:h?["0 0 10px 1px rgba(var(--brand-gold-rgb), 0.2)","0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)","0 0 10px 1px rgba(var(--brand-gold-rgb), 0.2)"]:["0 0 5px 1px rgba(var(--brand-gold-rgb), 0.1)","0 0 10px 2px rgba(var(--brand-gold-rgb), 0.2)","0 0 5px 1px rgba(var(--brand-gold-rgb), 0.1)"]},transition:{duration:2,repeat:1/0,repeatType:"loop",ease:"easeInOut"}}):null,{}),(0,r.jsx)("div",{className:"relative max-w-full w-full sm:w-auto",children:(0,r.jsxs)(_.$,{variant:"primary"===n?"default":n,size:i,className:(0,c.cn)("relative z-10 font-medium overflow-hidden max-w-full w-full sm:w-auto",o?"rounded-full":"rounded-md","primary"===n&&"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900","outline"===n&&"border border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/5",t),onMouseEnter:()=>b&&p(!0),onMouseLeave:()=>b&&p(!1),...d,children:[(0,r.jsx)("span",{className:"absolute inset-0 w-full h-full overflow-hidden",children:(0,r.jsx)("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none"})}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 relative z-10",children:[(0,r.jsx)("span",{children:a}),l&&(0,r.jsx)("div",{style:{transform:b&&m?"translateX(5px)":"translateX(0px)",transition:"transform 0.3s"},children:(0,r.jsx)(em.A,{className:"w-4 h-4"})})]})]})})]})}function eb(e){let{isOpen:a,onClose:t,onRequestRefund:n,isLoading:i,isEligibleForRefund:l}=e,[o,d]=(0,s.useState)(!1),[c,u]=(0,s.useState)("normal"),m={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}};(0,s.useEffect)(()=>{d(!0)},[]);let p=async()=>{await n(c)};return(0,r.jsx)(U.lG,{open:a,onOpenChange:e=>{e||t()},children:(0,r.jsx)(U.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl",children:(0,r.jsxs)(f.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,staggerChildren:.1}}},className:"relative overflow-hidden",children:[o&&(0,r.jsx)(ec,{variant:"gold",intensity:"medium"}),(0,r.jsxs)(U.c7,{className:"p-6 pb-2 z-10 relative",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-purple-500/15 text-purple-500",children:(0,r.jsx)(E.A,{className:"w-5 h-5"})}),(0,r.jsx)(ed.E,{variant:"outline",className:"border-purple-500/50 text-purple-500 px-2 py-0.5",children:l?"Payment Issue":"Refund Request"})]}),(0,r.jsx)(U.L3,{className:"text-2xl font-bold",children:"Request Refund"})]}),(0,r.jsxs)("div",{className:"p-6 pt-2 space-y-4 z-10 relative",children:[(0,r.jsx)(f.P.p,{variants:m,className:"text-muted-foreground",children:l?"You are eligible for a refund due to a payment issue or subscription problem. Your subscription will be cancelled immediately if the refund is approved.":"Request a refund for your subscription. Note that refunds are only processed for payment issues or subscription problems."}),(0,r.jsx)(f.P.div,{variants:m,children:(0,r.jsxs)(eo.Fc,{className:"bg-white/50 dark:bg-black/50 border border-neutral-200 dark:border-neutral-800",children:[(0,r.jsx)(es.A,{className:"h-4 w-4 text-[var(--brand-gold)]"}),(0,r.jsx)(eo.XL,{className:"font-medium",children:"Refund Information"}),(0,r.jsx)(eo.TN,{className:"text-sm",children:(0,r.jsxs)("p",{className:"mb-2",children:[l?"Your full refund will be processed back to your original payment method and your subscription will be cancelled immediately.":"Your refund request will be reviewed. If approved, it will be processed back to your original payment method."," ","Processing time depends on your bank and the refund speed selected."]})})]})}),(0,r.jsxs)(f.P.div,{variants:m,className:"space-y-3",children:[(0,r.jsx)("h4",{className:"text-sm font-medium",children:"Select Refund Speed"}),(0,r.jsxs)(ei.z,{value:c,onValueChange:e=>u(e),className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-3 rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80 transition-colors",children:[(0,r.jsx)(ei.C,{value:"normal",id:"normal"}),(0,r.jsxs)(el.J,{htmlFor:"normal",className:"flex flex-col cursor-pointer",children:[(0,r.jsx)("span",{className:"font-medium",children:"Normal"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Processed within 5-7 business days"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-3 rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80 transition-colors",children:[(0,r.jsx)(ei.C,{value:"optimum",id:"optimum"}),(0,r.jsxs)(el.J,{htmlFor:"optimum",className:"flex flex-col cursor-pointer",children:[(0,r.jsxs)("span",{className:"flex items-center font-medium",children:["Instant",(0,r.jsx)("span",{className:"ml-2 text-xs px-1.5 py-0.5 rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",children:"Recommended"})]}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Processed immediately when possible, otherwise normal speed"})]})]})]})]})]}),(0,r.jsxs)(U.Es,{className:"p-6 pt-2 flex flex-col sm:flex-row gap-2 sm:justify-between",children:[(0,r.jsx)(f.P.div,{variants:m,className:"w-full sm:w-auto",children:(0,r.jsx)(_.$,{variant:"outline",onClick:t,disabled:i,className:"w-full sm:w-auto border-neutral-200 dark:border-neutral-800",children:"Cancel"})}),(0,r.jsx)(f.P.div,{variants:m,className:"w-full sm:w-auto",children:(0,r.jsx)(ep,{onClick:p,disabled:i,className:"w-full sm:w-auto",children:i?(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Processing..."]}):(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(en.A,{className:"w-4 h-4 mr-2"}),"Request Refund"]})})})]})]})})})}var ex=t(71539);function eh(e){let{isOpen:a,onClose:t,onCancelSubscription:n,isLoading:i,isWithinRefundWindow:l,subscriptionStatus:o,effectiveCancellationDate:d,authenticatedSubscription:u}=e,[m,p]=(0,s.useState)(!1),b={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}};(0,s.useEffect)(()=>{p(!0)},[]);let x=async e=>{await n(e)};return(0,r.jsx)(U.lG,{open:a,onOpenChange:e=>{e||t()},children:(0,r.jsx)(U.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl",children:(0,r.jsxs)(f.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,staggerChildren:.1}}},className:"relative overflow-hidden",children:[m&&(0,r.jsx)(ec,{variant:"gold",intensity:"medium"}),(0,r.jsxs)(U.c7,{className:"p-6 pb-2 z-10 relative",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-red-500/15 text-red-500",children:(0,r.jsx)(A.A,{className:"w-5 h-5"})}),(0,r.jsx)(ed.E,{variant:"outline",className:"border-red-500/50 text-red-500 px-2 py-0.5",children:"Subscription Management"})]}),(0,r.jsx)(U.L3,{className:"text-2xl font-bold",children:"Manage Subscription"})]}),(0,r.jsxs)("div",{className:"p-6 pt-2 space-y-4 z-10 relative",children:[(0,r.jsx)(f.P.p,{variants:b,className:"text-muted-foreground",children:"Select an action for your subscription"}),(0,r.jsxs)(f.P.div,{variants:b,className:"space-y-3",children:["active"===o&&(0,r.jsx)(f.P.div,{whileHover:{y:-2},whileTap:{scale:.98},className:(0,c.cn)("p-4 rounded-lg border border-neutral-200 dark:border-neutral-800","bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80","transition-all duration-200 cursor-pointer",(i||!!d)&&"opacity-50 cursor-not-allowed"),onClick:()=>!i&&!d&&x(!1),children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-amber-50 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400 mt-0.5",children:(0,r.jsx)(S.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:"Cancel at End of Billing Cycle"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Continue using until your current period ends"})]})]})}),"active"===o&&(0,r.jsx)(f.P.div,{whileHover:{y:-2},whileTap:{scale:.98},className:(0,c.cn)("p-4 rounded-lg border border-neutral-200 dark:border-neutral-800","bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80","transition-all duration-200 cursor-pointer",i&&"opacity-50 cursor-not-allowed"),onClick:()=>!i&&x(!0),children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 mt-0.5",children:(0,r.jsx)(ex.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:"Cancel Immediately"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Cancel now with no refund for current period"})]})]})}),"active"===o&&l&&(0,r.jsx)(f.P.div,{whileHover:{y:-2},whileTap:{scale:.98},className:(0,c.cn)("p-4 rounded-lg border border-neutral-200 dark:border-neutral-800","bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80","transition-all duration-200 cursor-pointer",i&&"opacity-50 cursor-not-allowed"),onClick:()=>!i&&x(!0),children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 mt-0.5",children:(0,r.jsx)(C.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:"Cancel with Refund"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Cancel immediately and request a refund"})]})]})}),"authenticated"===o&&(null==u?void 0:u.id)&&(0,r.jsx)(f.P.div,{whileHover:{y:-2},whileTap:{scale:.98},className:(0,c.cn)("p-4 rounded-lg border border-neutral-200 dark:border-neutral-800","bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80","transition-all duration-200 cursor-pointer",i&&"opacity-50 cursor-not-allowed"),onClick:()=>!i&&x(!0),children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 mt-0.5",children:(0,r.jsx)(X.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:"Cancel Future Subscription"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Cancel your upcoming subscription"})]})]})})]}),("active"===o||"authenticated"===o)&&(0,r.jsx)(f.P.div,{variants:b,children:(0,r.jsxs)(eo.Fc,{className:"bg-white/50 dark:bg-black/50 border border-red-200 dark:border-red-800/50",children:[(0,r.jsx)(C.A,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)(eo.XL,{className:"font-medium",children:"Important"}),(0,r.jsx)(eo.TN,{className:"text-sm",children:(0,r.jsxs)("ul",{className:"list-disc pl-4 space-y-1",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Cancel at End of Billing Cycle:"})," You'll keep access to premium features until your current billing period ends."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Cancel Immediately:"})," Your access to premium features will end now with no refund for the current period."]}),l&&(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Cancel with Refund:"})," Your access will end now and you'll receive a refund for the current period."]})]})})]})})]}),(0,r.jsx)(U.Es,{className:"p-6 pt-2 flex justify-center",children:(0,r.jsx)(f.P.div,{variants:b,className:"w-full sm:w-auto",children:(0,r.jsx)(ep,{variant:"outline",onClick:t,disabled:i,className:"w-full sm:w-auto",children:i?(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Processing..."]}):(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(en.A,{className:"w-4 h-4 mr-2"}),"Close"]})})})})]})})})}function eg(e){let{status:a,isEligibleForRefund:t=!1,onCancelSubscription:n,onPauseSubscription:i,onResumeSubscription:l,onRequestRefund:o,subscriptionId:d,planId:c=""}=e,[u,m]=(0,s.useState)(!1),[p,b]=(0,s.useState)(!1),[x,h]=(0,s.useState)(!1),[g]=(0,s.useState)("normal"),[v,y]=(0,s.useState)(null),[w,N]=(0,s.useState)(!1),k=async()=>{if(i){m(!0);try{await i()}catch(e){console.error("Error pausing subscription:",e)}finally{m(!1)}}},P=async()=>{if(l){m(!0);try{await l()}catch(e){console.error("Error resuming subscription:",e)}finally{m(!1)}}},C=async()=>{if(o){m(!0);try{await o(g),h(!1)}catch(e){console.error("Error requesting refund:",e)}finally{m(!1)}}},S=W.SubscriptionStateManager.isFreeStatus(a,c),I="paused"===a||a===W.dH.HALTED,R=!S||I;console.log("[SUBSCRIPTION_ACTIONS] Button visibility logic:",{status:a,planId:c,isFreePlan:S,isPausedSubscription:I,shouldShowButtons:R,subscriptionId:d});let T=[W.dH.ACTIVE,W.dH.AUTHENTICATED,"paused",W.dH.HALTED],_=[W.dH.ACTIVE,W.dH.CANCELLED],D=R&&T.includes(a)&&n,z=R&&a===W.dH.ACTIVE&&i,L=R&&("paused"===a||a===W.dH.HALTED)&&l,O=R&&t&&_.includes(a)&&o;if(!D&&!z&&!L&&!O)return null;let M=[z&&{id:"pause",icon:(0,r.jsx)(q.A,{className:"h-5 w-5"}),label:"Pause Subscription",description:"Temporarily pause your subscription",color:"bg-amber-500",textColor:"text-amber-600 dark:text-amber-400",bgColor:"bg-amber-50 dark:bg-amber-900/20",borderColor:"border-amber-100 dark:border-amber-800/30",action:k},L&&{id:"resume",icon:(0,r.jsx)(et.A,{className:"h-5 w-5"}),label:"Resume Subscription",description:"Resume your paused subscription",color:"bg-green-500",textColor:"text-green-600 dark:text-green-400",bgColor:"bg-green-50 dark:bg-green-900/20",borderColor:"border-green-100 dark:border-green-800/30",action:P},O&&{id:"refund",icon:(0,r.jsx)(E.A,{className:"h-5 w-5"}),label:"Request Refund",description:"Request a refund for your subscription",color:"bg-purple-500",textColor:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-50 dark:bg-purple-900/20",borderColor:"border-purple-100 dark:border-purple-800/30",action:()=>h(!0)},D&&{id:"cancel",icon:(0,r.jsx)(A.A,{className:"h-6 w-6"}),label:"Cancel Subscription",description:"Cancel your subscription permanently",color:"bg-red-500",textColor:"text-red-600 dark:text-red-400",bgColor:"bg-red-50 dark:bg-red-900/20",borderColor:"border-red-100 dark:border-red-800/30",action:()=>b(!0)}].filter(Boolean);return(0,r.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"w-full",children:(0,r.jsxs)(F.Zp,{className:"overflow-hidden backdrop-blur-sm bg-white/80 dark:bg-black/50 border border-neutral-200/80 dark:border-neutral-800/80 shadow-sm",children:[(0,r.jsx)(F.aR,{className:"pb-2 bg-gradient-to-br from-white/90 to-white/70 dark:from-black/70 dark:to-black/50 border-b border-neutral-200/80 dark:border-neutral-800/80",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.P.div,{whileHover:{rotate:360},transition:{duration:.5},className:"p-2 rounded-full bg-primary/10 text-primary",children:(0,r.jsx)(er.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(F.ZB,{className:"text-xl",children:"Manage Subscription"}),(0,r.jsx)(F.BT,{children:"Manage your subscription settings and payment methods"})]})]})}),(0,r.jsxs)(F.Wu,{className:"p-4",children:[(0,r.jsx)("div",{className:"flex flex-col items-center",children:M.map(e=>(0,r.jsxs)(f.P.div,{whileHover:{y:-5},whileTap:{scale:.98},onHoverStart:()=>y(e.id),onHoverEnd:()=>y(null),className:"relative overflow-hidden rounded-xl ".concat(e.bgColor," border ").concat(e.borderColor," p-4 cursor-pointer transition-all duration-300 w-full max-w-sm mx-auto mb-4 ").concat("cancel"===e.id?"shadow-md hover:shadow-lg":""),onClick:e.action,children:[(0,r.jsx)(j.N,{children:v===e.id&&(0,r.jsx)(f.P.div,{initial:{opacity:0,scale:0},animate:{opacity:.05,scale:1},exit:{opacity:0,scale:0},transition:{duration:.3},className:"absolute inset-0 rounded-full ".concat(e.color),style:{top:"50%",left:"50%",width:"150%",height:"150%",transform:"translate(-50%, -50%)"}})}),(0,r.jsxs)("div",{className:"relative z-10 flex flex-col items-center text-center space-y-2",children:[(0,r.jsx)("div",{className:"rounded-full ".concat(e.color," text-white ").concat("cancel"===e.id?"p-4":"p-3"),children:e.icon}),(0,r.jsx)("h3",{className:"font-medium ".concat(e.textColor," ").concat("cancel"===e.id?"text-lg":""),children:e.label}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.id))}),(0,r.jsx)(eh,{isOpen:p,onClose:()=>b(!1),onCancelSubscription:async e=>{if(b(!1),await new Promise(e=>setTimeout(e,300)),N(e),n)try{m(!0),await n(e)}catch(e){console.error("Error cancelling subscription:",e)}finally{m(!1)}},isLoading:u,isWithinRefundWindow:t,subscriptionStatus:a,effectiveCancellationDate:null,authenticatedSubscription:"authenticated"===a?{id:"authenticated"}:null}),(0,r.jsx)(eb,{isOpen:x,onClose:()=>h(!1),onRequestRefund:C,isLoading:u,isEligibleForRefund:t})]})]})})}function ef(e){let{activeTab:a,onChange:t}=e;return(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"relative inline-flex",children:[(0,r.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/30 rounded-full blur-md"}),(0,r.jsxs)("div",{className:"space-x-2 bg-white/80 dark:bg-black/50 backdrop-blur-sm p-1.5 rounded-full border border-neutral-200/50 dark:border-neutral-800/50 inline-flex shadow-md relative z-10",children:[(0,r.jsx)(_.$,{onClick:()=>t("overview"),variant:"overview"===a?"default":"ghost",size:"sm",className:"cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ".concat("overview"===a?"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md":"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"),children:(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"overview"===a?"font-medium":"font-normal",children:"Overview"})]})}),(0,r.jsx)(_.$,{onClick:()=>t("payments"),variant:"payments"===a?"default":"ghost",size:"sm",className:"cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ".concat("payments"===a?"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md":"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"),children:(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"payments"===a?"font-medium":"font-normal",children:"Payments"})]})})]})]})})}function ev(e){let{subscriptionId:a,status:n,planName:l,planId:o,planCycle:d,amount:c,currency:u="INR",nextBillingDate:m,lastPaymentDate:p,lastPaymentStatus:b,lastPaymentMethod:g,createdAt:f,expiresAt:y,pausedAt:j,cancellationRequestedAt:w,cancelledAt:N,isEligibleForRefund:k=!1,subscriptionStartDate:P,subscriptionChargeTime:A,trialEndDate:C}=e,S=(0,x.useRouter)(),[E,I]=(0,s.useState)(!1),[R,T]=(0,s.useState)("overview"),[_,D]=(0,s.useState)([]),[z,L]=(0,s.useState)(!1),[O,M]=(0,s.useState)(!1),H=(0,s.useRef)(!1),{status:U,setSubscriptionCancelled:F,setSubscriptionPaused:Y,setSubscriptionResumed:B,setWaitingForWebhook:q,completeProcessing:W,resetProcessing:$}=i(),Z=e=>{if(!e)return"unknown";let a=e.toLowerCase();return a.includes("active")?"active":a.includes("authenticated")?"authenticated":a.includes("pending")?"pending":a.includes("halted")||a.includes("paused")?"paused":a.includes("cancelled")?"cancelled":a.includes("completed")?"completed":a.includes("expired")?"expired":a.includes("initialized")?"initialized":a.includes("failed")?"payment_failed":"unknown"},K=(0,s.useCallback)(async()=>{if(a&&!O){L(!0);try{let e=await fetch("/api/subscription/".concat(a,"/payments"));if(!e.ok)throw M(!0),Error("Failed to fetch payment history");let t=await e.json();if(!t.success)throw M(!0),Error(t.error||"Failed to fetch payment history");D(t.data||[]),M(!1)}catch(e){console.error("Error fetching payment history:",e),M(!0),H.current||(h.oR.error("Failed to load payment history. Please try again."),H.current=!0)}finally{L(!1)}}},[a,O]);(0,s.useEffect)(()=>{a&&!O&&K()},[K,a,O]);let X=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!a)return;I(!0),F("Cancelling your subscription...");let r=async()=>{let e=new KeyboardEvent("keydown",{key:"Escape",code:"Escape",keyCode:27,which:27,bubbles:!0});document.dispatchEvent(e),await new Promise(e=>setTimeout(e,300)),q("Waiting for confirmation from Razorpay. Please don't refresh the page.")};await r();try{let{cancelSubscription:a}=await t.e(5232).then(t.bind(t,35232)),r=await a(e);if(!r.success)throw Error(r.error||"Failed to cancel subscription");W(!0,e?"Your subscription has been cancelled immediately.":"Your subscription will be cancelled at the end of the billing cycle."),$(),setTimeout(()=>{S.refresh()},2e3)}catch(e){console.error("Error cancelling subscription:",e),W(!1,e instanceof Error?e.message:"Failed to cancel subscription"),setTimeout(()=>{$()},3e3)}finally{I(!1)}},J=async()=>{if(a){I(!0),Y("Pausing your subscription...");try{let{pauseSubscription:e}=await t.e(5232).then(t.bind(t,35232)),a=await e();if(!a.success)throw Error(a.error||"Failed to pause subscription");W(!0,"Your subscription has been paused successfully."),setTimeout(()=>{S.refresh()},2e3)}catch(e){console.error("Error pausing subscription:",e),W(!1,e instanceof Error?e.message:"Failed to pause subscription")}finally{I(!1)}}},Q=async()=>{if(a){I(!0),B("Resuming your subscription...");try{let{activateSubscription:e}=await t.e(5232).then(t.bind(t,35232)),a=await e();if(!a||void 0===a.success)throw console.error("Invalid response from activateSubscription:",a),Error("Received invalid response from server");if(!a.success)throw Error(a.error||"Failed to resume subscription");h.oR.success("Your subscription has been resumed successfully."),h.oR.dismiss(),$(),setTimeout(()=>{S.refresh()},2e3)}catch(e){console.error("Error resuming subscription:",e),W(!1,e instanceof Error?e.message:"Failed to resume subscription")}finally{I(!1)}}},ee=async e=>{h.oR.info("Refund Request Process",{description:"Please contact our support team via email to request a refund for payment issues or subscription problems.",duration:5e3}),$()};return a?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(G,{isVisible:"waiting_for_webhook"===U,message:"Waiting for confirmation from Razorpay",description:"Please wait while we receive webhook confirmation. This may take a moment."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,r.jsx)(v.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"My Subscription"}),(0,r.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Manage your subscription details and payment information"})]})]}),(0,r.jsx)("div",{className:"mb-8 w-full flex justify-center",children:(0,r.jsx)(ef,{activeTab:R,onChange:T})}),"overview"===R&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(V,{subscriptionId:a,status:Z(n),planName:l,planCycle:d,amount:c,currency:u,nextBillingDate:m,lastPaymentDate:p,lastPaymentMethod:g,createdAt:f,expiresAt:y,pausedAt:j,cancellationRequestedAt:w,cancelledAt:N,isEligibleForRefund:k,subscriptionStartDate:P,subscriptionChargeTime:A,trialEndDate:C}),(0,r.jsx)(eg,{subscriptionId:a,status:Z(n),isEligibleForRefund:k,onCancelSubscription:X,onPauseSubscription:J,onResumeSubscription:Q,onRequestRefund:ee,planId:o})]}),"payments"===R&&(0,r.jsx)("div",{className:"space-y-6",children:"free"!==o?(0,r.jsx)(ea,{subscriptionId:a,planId:o}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h2",{className:"text-xl font-bold mb-4",children:"No Invoice History"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-6",children:"Free plan users don't have invoices. Upgrade to a paid plan to see your invoice history."})]})})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"No Active Subscription"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-6",children:"You don't have an active subscription. Subscribe to a plan to get started."}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(ep,{onClick:()=>S.push("/dashboard/business/plan"),size:"lg",className:"px-8 py-2",showArrow:!0,roundedFull:!0,children:"View Plans"})})]})}function ey(e){let{userId:a,currentSubscriptionId:t,subscriptionStatus:s,currentPlanDetails:n,currentPlanCycle:i,nextBillingDate:l,subscriptionStartDate:o,subscriptionExpiryTime:d,subscriptionChargeTime:c,lastPaymentMethod:u,cancellationRequestedAt:m,cancelledAt:p,trialEndDate:b,isEligibleForRefund:x,setActiveTab:h}=e;return t?(0,r.jsx)(ev,{userId:a,subscriptionId:t,status:s,planName:(null==n?void 0:n.name)||"Unknown Plan",planId:(null==n?void 0:n.id)||"free",planCycle:i,amount:n?parseInt(("monthly"===i?n.price:n.yearlyPrice||n.price).replace(/[^\d]/g,"")):0,currency:"INR",nextBillingDate:l,lastPaymentDate:o,lastPaymentStatus:"SUCCESS",lastPaymentMethod:u,createdAt:o,expiresAt:d,pausedAt:"paused"===s?o:null,cancellationRequestedAt:m,cancelledAt:p,isEligibleForRefund:x,subscriptionStartDate:o,subscriptionChargeTime:c,trialEndDate:b}):(0,r.jsx)("div",{className:"text-center py-12 px-4 border rounded-lg bg-muted/20",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-primary",children:[(0,r.jsx)("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),(0,r.jsx)("circle",{cx:"9",cy:"7",r:"4"}),(0,r.jsx)("path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}),(0,r.jsx)("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),(0,r.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"No Active Subscription"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md",children:"You don't have an active subscription yet. Choose a plan that fits your business needs to unlock premium features."}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(ep,{onClick:()=>h("plans"),size:"lg",className:"px-8 py-2",showArrow:!0,roundedFull:!0,children:"Browse Plans"})})]})})}var ej=t(5937);let ew={hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,staggerChildren:.1}}},eN={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},ek={hover:{scale:1.03},tap:{scale:.98}},eP=e=>{let a={className:"w-6 h-6 text-[var(--brand-gold)]"};return e.includes("Basic")?(0,r.jsx)(y.A,{...a}):e.includes("Growth")?(0,r.jsx)(ej.A,{...a}):e.includes("Pro")?(0,r.jsx)(es.A,{...a}):(0,r.jsx)(v.A,{...a})};function eA(e){let{isOpen:a,onClose:t,plan:n,trialEndDate:l,_onSubscribe:o,isLoading:d,razorpaySubscriptionId:u}=e,{resetProcessing:m}=i(),p=!!u,b=!!(l&&new Date(l)>new Date),x=(0,s.useRef)(!0),[h,g]=(0,s.useState)(!1),[v,j]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!1),[P,A]=(0,s.useState)(!1),[C,S]=(0,s.useState)(!1),E=!v&&(void 0!==d?d:w),I=E||C,R=!C&&!E;(0,s.useEffect)(()=>(g(!0),()=>{x.current=!1}),[]),(0,s.useEffect)(()=>{a||(console.log("[DIALOG] Dialog closed - resetting all states"),void 0===d&&N(!1),j(!1),m(),A(!1))},[a,d,m]),(0,s.useEffect)(()=>{if(a){console.log("[DIALOG] Dialog opened - ensuring Razorpay state is reset"),j(!1),A(!0);let e=setTimeout(()=>{A(!1)},500);return()=>clearTimeout(e)}A(!1)},[a]),(0,s.useEffect)(()=>{if(v){console.log("[DIALOG] Razorpay modal detected as open, setting up fallback timer");let e=setTimeout(()=>{console.log("[DIALOG] Fallback timer triggered - checking if Razorpay modal is still open");let e=document.querySelector('iframe[name^="rzp_"]'),a=document.querySelector(".razorpay-backdrop"),t=document.querySelector(".razorpay-container"),r=document.querySelector(".razorpay-modal");e||a||t||r?console.log("[DIALOG] Fallback: Razorpay modal still detected, keeping state"):(console.log("[DIALOG] Fallback: Razorpay modal no longer detected, resetting state"),j(!1))},3e4);return()=>{clearTimeout(e)}}},[v]),(0,s.useEffect)(()=>{if(a){let e=setInterval(()=>{try{let e=document.querySelector('iframe[name^="rzp_"]'),a=document.querySelector(".razorpay-backdrop"),t=document.querySelector(".razorpay-container"),r=document.querySelector(".razorpay-modal"),s=document.querySelector("#razorpay-checkout-frame"),n=document.querySelector(".razorpay-overlay"),i=document.querySelector(".razorpay-wrapper"),l=Array.from(document.querySelectorAll("iframe")).filter(e=>{try{let a=window.getComputedStyle(e);return parseInt(a.zIndex)>1e3}catch(e){return!1}}),o=!!(e||a||t||r||s||n||i||l.length>0);o!==v&&(console.log("[DIALOG] Razorpay modal state changed:",o),console.log("[DIALOG] Razorpay elements found:",{razorpayFrame:!!e,razorpayBackdrop:!!a,razorpayContainer:!!t,razorpayModal:!!r,highZIndexIframes:l.length}),o?console.log("[DIALOG] Razorpay modal opened - hiding our dialog to prevent interference"):console.log("[DIALOG] Razorpay modal closed - showing our dialog again"),j(o))}catch(e){console.warn("[DIALOG] Error checking for Razorpay modal:",e),v&&(console.log("[DIALOG] Resetting Razorpay state due to detection error"),j(!1))}},300);return()=>{clearInterval(e)}}},[v,a]);let T=async()=>{if(console.log("\uD83D\uDD25 [DIALOG] BUTTON CLICKED - handleSubscribe called!"),console.log("[DIALOG] Button state:",{isProcessing:C,externalLoading:d,isButtonDisabled:I}),C||void 0!==d&&d)return void console.log("[DIALOG] Already processing subscription, ignoring duplicate click");try{S(!0),void 0===d&&N(!0),j(!1),console.log("[DIALOG] Starting subscription process with create/cancel flow..."),await o()}catch(e){console.error("[DIALOG] Subscription error:",e),void 0===d&&x.current&&N(!1),j(!1)}finally{S(!1)}},D=a&&!v;return(0,s.useEffect)(()=>{console.log("[DIALOG] Dialog state:",{isOpen:a,isRazorpayOpen:v,shouldShowDialog:D,canCloseDialog:R,isProcessing:C,isLoading:E,isDialogJustOpened:P})},[a,v,D,R,C,E,P]),(0,r.jsx)(U.lG,{open:D,onOpenChange:e=>{if(console.log("[DIALOG] onOpenChange called with:",e,{canCloseDialog:R,isRazorpayOpen:v,isDialogJustOpened:P,isOpen:a}),!e&&P)return void console.log("[DIALOG] Dialog close ignored - dialog was just opened, preventing immediate closure");e||!R||v?e||R?!e&&v?console.log("[DIALOG] Dialog close ignored - Razorpay modal is open"):e&&!a&&console.log("[DIALOG] Dialog trying to open but isOpen is false - this might indicate a state issue"):console.log("[DIALOG] Dialog closing prevented - processing UPI/netbanking subscription"):(console.log("[DIALOG] Dialog closing allowed"),void 0===d&&N(!1),m(),t())},children:(0,r.jsx)(U.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl",children:(0,r.jsxs)(f.P.div,{initial:"hidden",animate:"visible",variants:ew,className:"relative overflow-hidden",children:[h&&(0,r.jsx)(ec,{variant:"gold",intensity:"medium"}),(0,r.jsx)(U.c7,{className:"p-6 pb-4 border-b border-neutral-200 dark:border-neutral-800",children:(0,r.jsxs)(f.P.div,{variants:eN,className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-[var(--brand-gold)]/15",children:eP(n.name)}),(0,r.jsxs)("div",{children:[(0,r.jsx)(U.L3,{className:"text-xl font-semibold text-foreground",children:n.name}),n.recommended&&(0,r.jsx)(ed.E,{variant:"outline",className:"mt-1 text-xs bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] border-[var(--brand-gold)]/20",children:"Recommended"})]})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)(f.P.div,{variants:eN,className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-foreground",children:n.price}),(0,r.jsx)("span",{className:"text-muted-foreground text-sm",children:n.period})]}),n.savings&&(0,r.jsxs)("div",{className:"text-green-600 dark:text-green-400 text-sm font-medium flex items-center gap-1.5",children:[(0,r.jsx)(en.A,{className:"w-4 h-4"}),n.savings]})]}),(0,r.jsx)(f.P.div,{variants:eN,children:(0,r.jsxs)("div",{className:"text-xs text-muted-foreground p-3 bg-neutral-100 dark:bg-neutral-900 rounded-lg",children:[(0,r.jsx)("p",{className:"mb-1 font-medium",children:p?"Plan Switch Notice:":"Subscription Note:"}),(0,r.jsx)("p",{children:p?"Switching plans will cancel your current subscription and create a new one. You will lose access to your previous plan benefits immediately.":b?"Your subscription will be created and payment will be processed when your trial ends.":"A new subscription will be created for the selected plan."})]})})]}),(0,r.jsx)(f.P.div,{variants:eN,children:(0,r.jsxs)(U.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 p-6 pt-4 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-950",children:[(0,r.jsx)(f.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,r.jsx)(_.$,{variant:"outline",onClick:()=>{R?(console.log("[DIALOG] Cancel button clicked - closing dialog"),void 0===d&&N(!1),m(),t()):console.log("[DIALOG] Cancel button clicked but dialog close prevented - processing UPI/netbanking subscription")},disabled:!R,className:"w-full sm:w-auto py-6 rounded-xl transition-all duration-200",children:"Cancel"})}),(0,r.jsx)(f.P.div,{whileHover:"hover",whileTap:"tap",variants:ek,className:"w-full sm:w-auto",children:(0,r.jsxs)(_.$,{onClick:T,className:(0,c.cn)("w-full relative overflow-hidden py-6","bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90","text-black dark:text-neutral-900 font-medium transition-all duration-200","shadow-[0_0_15px_rgba(var(--brand-gold-rgb),0.5)] hover:shadow-[0_0_20px_rgba(var(--brand-gold-rgb),0.7)]","rounded-xl"),disabled:I,children:[h&&!E&&!C&&(0,r.jsx)(f.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear"}}),h&&!E&&!C&&(0,r.jsx)("div",{className:"absolute inset-0 w-full h-full opacity-75 pointer-events-none",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-[var(--brand-gold)]/20 blur-md rounded-xl"})}),E||C?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(k.A,{className:"w-4 h-4 animate-spin"}),(0,r.jsx)("span",{children:C?"Starting...":"Processing..."})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(y.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:p?"Switch Plan":"Subscribe Now"})]})]})})]})})]})})})}var eC=t(10238),eS=t(78039),eE=t(27682);function eI(e){let{isOpen:a,onClose:t,plan:n,onActivateTrial:l,isLoading:o,billingCycle:d="monthly"}=e,{resetProcessing:u}=i(),[m,p]=(0,s.useState)(!1),[b,x]=(0,s.useState)(!1),h=(0,eS.P)(new Date,1),g=(0,eE.GP)(h,"dd MMM yyyy"),y=void 0!==o?o:b,j={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}};(0,s.useEffect)(()=>{p(!0)},[]);let w=async()=>{try{void 0===o&&x(!0),await l(),void 0===o&&x(!1)}catch(e){console.error("Error activating trial:",e),void 0===o&&x(!1)}};return(0,r.jsx)(U.lG,{open:a,onOpenChange:e=>{e||(void 0===o&&x(!1),u(),t())},children:(0,r.jsx)(U.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl",children:(0,r.jsxs)(f.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,staggerChildren:.1}}},className:"relative overflow-hidden",children:[m&&(0,r.jsx)(ec,{variant:"gold",intensity:"medium"}),(0,r.jsxs)(U.c7,{className:"p-6 pb-0 z-10 relative",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-[var(--brand-gold)]/15 text-[var(--brand-gold)]",children:(0,r.jsx)(eC.A,{className:"w-5 h-5"})}),(0,r.jsx)(ed.E,{variant:"outline",className:"border-[var(--brand-gold)]/50 text-[var(--brand-gold)] px-2 py-0.5",children:"Special Offer"})]}),(0,r.jsx)(U.L3,{className:"text-2xl font-bold",children:"1 Month Free Trial"})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)(f.P.div,{variants:j,className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]",children:(0,r.jsx)(v.A,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:n.name}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[n.price,n.period]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-950/30 dark:to-purple-950/30 p-4 rounded-lg border border-blue-200/50 dark:border-blue-800/30 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)(eC.A,{className:"w-5 h-5 text-[var(--brand-gold)] mt-0.5 flex-shrink-0"}),(0,r.jsxs)("p",{className:"text-sm",children:["As a first-time paid subscriber, you'll get a ",(0,r.jsx)("span",{className:"font-semibold text-[var(--brand-gold)]",children:"1-month free trial"})," of the ",n.name,". No payment required now!"]})]}),"yearly"===d&&(0,r.jsxs)("div",{className:"flex items-start gap-2 bg-amber-50 dark:bg-amber-950/30 border border-amber-200/50 dark:border-amber-800/30 p-3 rounded-lg text-sm text-amber-800 dark:text-amber-300",children:[(0,r.jsx)(en.A,{className:"w-4 h-4 mt-0.5 flex-shrink-0"}),(0,r.jsx)("p",{children:"Your trial will be for the monthly plan. After your trial ends, you can switch to the yearly plan if you prefer."})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,r.jsx)(X.A,{className:"w-4 h-4 text-muted-foreground"}),(0,r.jsxs)("span",{children:["Trial ends on ",(0,r.jsx)("span",{className:"font-medium",children:g})]})]})]})]}),(0,r.jsx)(f.P.div,{variants:j,className:"bg-muted/30 p-4 rounded-lg border border-neutral-200 dark:border-neutral-800",children:(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["After your trial ends, you'll need to add a payment method to continue using the ",n.name,".","yearly"===d?" You can choose to continue with the monthly plan or switch to the yearly plan at that time.":" We'll remind you before your trial expires."]})})]}),(0,r.jsx)(f.P.div,{variants:j,children:(0,r.jsxs)(U.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 p-6 pt-4 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-950",children:[(0,r.jsx)(f.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,r.jsx)(_.$,{variant:"outline",onClick:t,className:"w-full sm:w-auto py-6 rounded-xl transition-all duration-200",children:"Cancel"})}),(0,r.jsx)(f.P.div,{whileHover:"hover",whileTap:"tap",variants:{hover:{scale:1.03},tap:{scale:.98}},className:"w-full sm:w-auto",children:(0,r.jsxs)(_.$,{onClick:w,className:(0,c.cn)("w-full relative overflow-hidden py-6","bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90","text-black dark:text-neutral-900 font-medium transition-all duration-200","shadow-[0_0_15px_rgba(var(--brand-gold-rgb),0.5)] hover:shadow-[0_0_20px_rgba(var(--brand-gold-rgb),0.7)]","rounded-xl"),disabled:y,children:[m&&!y&&(0,r.jsx)(f.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear"}}),m&&!y&&(0,r.jsx)("div",{className:"absolute inset-0 w-full h-full opacity-75 pointer-events-none",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-[var(--brand-gold)]/20 blur-md rounded-xl"})}),y?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(k.A,{className:"w-4 h-4 animate-spin"}),(0,r.jsx)("span",{children:"Activating..."})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(eC.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Activate Free Trial"})]})]})})]})})]})})})}function eR(e){let{dialogPlan:a,isPlanDialogOpen:t,isFirstTimePaidPlanDialogOpen:s,dialogLoading:n,billingCycle:i,subscriptionStatus:l,trialEndDate:o,razorpaySubscriptionId:d,setIsPlanDialogOpen:c,setIsFirstTimePaidPlanDialogOpen:u,setDialogLoading:m,handleDialogSubscribe:p,handleActivateTrial:b,resetProcessing:x}=e;return console.log("[DIALOG_MANAGER] Dialog states:",{dialogPlan:null==a?void 0:a.id,isPlanDialogOpen:t,isFirstTimePaidPlanDialogOpen:s,dialogLoading:n}),(0,r.jsxs)(r.Fragment,{children:[a&&(0,r.jsx)(eA,{isOpen:t,onClose:()=>{console.log("[DIALOG_MANAGER] Closing SimplifiedPlanActionDialog"),c(!1),m(!1),x()},plan:a,trialEndDate:"authenticated"===l?null:o,_onSubscribe:p,isLoading:n,razorpaySubscriptionId:d}),a&&(0,r.jsx)(eI,{isOpen:s,onClose:()=>{u(!1),m(!1),x()},plan:a,billingCycle:i,onActivateTrial:b,isLoading:n})]})}var eT=t(34477);let e_=(0,eT.createServerReference)("7f717d13f4790849609e3952f2c335295c24779be8",eT.callServer,void 0,eT.findSourceMapURL,"activateTrialForFirstTimePaidSubscriber");function eD(e){let{userId:a,currentPlanDetails:n,subscriptionStatus:l,trialEndDate:o,monthlyPlans:d,yearlyPlans:c,currentSubscriptionId:u,nextBillingDate:m,cancellationRequestedAt:p,cancelledAt:b,planCycle:g,subscriptionStartDate:j,subscriptionExpiryTime:k,subscriptionChargeTime:P,isEligibleForRefund:A,lastPaymentMethod:C,razorpaySubscriptionId:S}=e,[E,R]=(0,s.useState)("subscription"),T=function(e){let{currentSubscriptionId:a,subscriptionStatus:r,currentPlanDetails:n,currentPlanCycle:l,lastPaymentMethod:o,razorpaySubscriptionId:d,isEligibleForFreeTrial:c}=e,u=(0,x.useRouter)(),[m,p]=(0,s.useState)(null),[b,g]=(0,s.useState)(!1),[f,v]=(0,s.useState)(!1),[y,j]=(0,s.useState)(!1),[w,N]=(0,s.useState)(l),{completeProcessing:k,startProcessing:P,resetProcessing:A,setSubscriptionCreated:C,setFuturePaymentAuthorized:S}=i(),E=async e=>{let{createSubscription:a}=await t.e(5232).then(t.bind(t,35232)),r=await a("free",w);if(!r.success){let e=r.error||"Failed to switch to free plan. Please try again.";return h.oR.error("Subscription Error",{description:e}),k(!1,e),!1}return h.oR.success("Plan Updated",{description:"Your plan has been updated to the Free plan."}),C("Your plan has been updated to the Free plan."),j(!1),g(!1),setTimeout(()=>{u.refresh()},1500),!0};return{dialogPlan:m,isPlanDialogOpen:b,isFirstTimePaidPlanDialogOpen:f,dialogLoading:y,billingCycle:w,setDialogPlan:p,setIsPlanDialogOpen:g,setIsFirstTimePaidPlanDialogOpen:v,setDialogLoading:j,setBillingCycle:N,handlePlanAction:e=>{if(console.log("[PLAN_SWITCH_DEBUG] handlePlanAction called with plan:",e.id),console.log("[PLAN_SWITCH_DEBUG] Current subscription status:",r),console.log("[PLAN_SWITCH_DEBUG] isEligibleForFreeTrial:",c),console.log("[PLAN_SWITCH_DEBUG] currentPlanDetails:",null==n?void 0:n.id),"enterprise"===e.id)return void u.push("/contact");p(e),console.log("[PLAN_SWITCH_DEBUG] Setting dialog plan to:",e.id),console.log("[PLAN_SWITCH_DEBUG] Current dialog states before opening:",{isPlanDialogOpen:b,isFirstTimePaidPlanDialogOpen:f,dialogLoading:y}),c&&"free"!==e.id&&("inactive"===r||(null==n?void 0:n.id)==="free")?(console.log("[PLAN_SWITCH_DEBUG] Opening first-time paid plan dialog"),v(!0),console.log("[PLAN_SWITCH_DEBUG] First-time paid plan dialog should now be open")):(console.log("[PLAN_SWITCH_DEBUG] Opening regular subscription dialog"),g(!0),console.log("[PLAN_SWITCH_DEBUG] Regular subscription dialog should now be open"))},determineSubscriptionFlow:()=>({hasRazorpaySubscription:!!d,isCardPayment:!1,isUpiOrEmandateOrUnknown:!0,paymentMethod:(null==o?void 0:o.toLowerCase())||"unknown"}),validateSubscriptionRequest:e=>{if(a){if(("authenticated"===r||"active"===r)&&e.id===(null==n?void 0:n.id)&&w===l)return h.oR.error("Current Plan Selected",{description:"You are already subscribed to this plan. Please choose a different plan or cycle."}),k(!1,"You are already subscribed to this plan. Please choose a different plan or cycle."),!1;if("paused"===r||"halted"===r)return h.oR.error("Subscription Paused",{description:"Your subscription is currently paused. Please resume your subscription before changing plans."}),k(!1,"Your subscription is currently paused. Please resume your subscription before changing plans."),!1}return!0},handleFreePlanSubscription:E,completeProcessing:k,startProcessing:P,resetProcessing:A,setSubscriptionCreated:C,setFuturePaymentAuthorized:S}}({currentSubscriptionId:u,subscriptionStatus:l,currentPlanDetails:n,currentPlanCycle:g,lastPaymentMethod:C,razorpaySubscriptionId:S,isEligibleForFreeTrial:null===o}),{handleDialogSubscribe:_}=function(e){let a=(0,x.useRouter)(),r=(0,s.useRef)(!1),{currentSubscriptionId:n,subscriptionStatus:i,currentPlanDetails:l,currentPlanCycle:o,lastPaymentMethod:d,razorpaySubscriptionId:c,trialEndDate:u,dialogPlan:m,billingCycle:p,setDialogLoading:b,setIsPlanDialogOpen:g,startProcessing:f,completeProcessing:v,resetProcessing:y,setSubscriptionCreated:j,setFuturePaymentAuthorized:w,validateSubscriptionRequest:N,handleFreePlanSubscription:k,setActiveTab:P}=e;return{handleDialogSubscribe:async()=>{if(console.log("\uD83D\uDD25 [SUBSCRIPTION_HANDLER] handleDialogSubscribe CALLED - BUTTON CLICKED!"),r.current)return void console.log("[SUBSCRIPTION_HANDLER] Request already in progress, ignoring duplicate call");if(console.log("[CENTRALIZED_SUBSCRIPTION] handleDialogSubscribe called"),console.log("[CENTRALIZED_SUBSCRIPTION] Current state:",{dialogPlan:null==m?void 0:m.id,currentSubscriptionId:n,subscriptionStatus:i,lastPaymentMethod:d,razorpaySubscriptionId:c}),m){r.current=!0,b(!0);try{if("free"===m.id&&await k(m))return;if(!N(m))return void b(!1);let r="req_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11));console.log("[SUBSCRIPTION_HANDLER] Creating subscription with request ID:",r);let o=async()=>await fetch("/api/subscriptions/centralized",{method:"POST",headers:{"Content-Type":"application/json","X-Request-ID":r},body:JSON.stringify({planId:m.id,planCycle:p})}),d=!!n&&("authenticated"===i||"active"===i),c=!!u&&new Date(u)>new Date;console.log("[SUBSCRIPTION_HANDLER] Trial detection:",{trialEndDate:u,isOnTrial:c,subscriptionStatus:i,hasExistingSubscription:d}),console.log("[SUBSCRIPTION_HANDLER] Using simplified flow - always execute immediately"),console.log("[SUBSCRIPTION_HANDLER] ✅ EXECUTING SUBSCRIPTION ACTION IMMEDIATELY"),console.log("[SUBSCRIPTION_HANDLER] Execution context:",{isOnTrial:c,hasExistingSubscription:d,subscriptionStatus:i,dialogPlan:null==m?void 0:m.id,billingCycle:p});try{var e,s,l;console.log("[SUBSCRIPTION_HANDLER] \uD83D\uDE80 Starting subscription creation process..."),f("Processing your subscription request..."),console.log("[SUBSCRIPTION_HANDLER] Making API call to create subscription");let r=await o();console.log("[SUBSCRIPTION_HANDLER] API call completed, parsing response");let n=await r.json();if(console.log("[SUBSCRIPTION_HANDLER] Response data:",n),!n.success){let e=n.error||"Failed to create subscription. Please try again.";h.oR.error("Subscription Error",{description:e}),v(!1,e);return}let i=(null==(e=n.data)?void 0:e.id)||(null==(s=n.data)?void 0:s.subscription_id);if(console.log("[SUBSCRIPTION_HANDLER] Checking subscription ID:",{subscriptionId:i,hasId:!!i,dataKeys:Object.keys(n.data||{}),fullData:n.data}),i){console.log("[SUBSCRIPTION_HANDLER] Got subscription ID:",i),console.log("[SUBSCRIPTION_HANDLER] Proceeding to open Razorpay modal..."),f("Opening payment authorization...");let e=await fetch("/api/razorpay/key");if(!e.ok)throw Error("Failed to fetch Razorpay key ID");let r=await e.json();if(!r.success||!r.key_id)throw Error("Invalid Razorpay key ID response");console.log("[SUBSCRIPTION_HANDLER] Importing Razorpay SDK...");let{openRazorpaySubscriptionCheckout:s}=await t.e(4919).then(t.bind(t,44919));console.log("[SUBSCRIPTION_HANDLER] Opening Razorpay modal with:",{keyId:r.key_id,subscriptionId:i,planName:m.name,billingCycle:p}),console.log("[SUBSCRIPTION_HANDLER] About to call openRazorpaySubscriptionCheckout...");let n=await s(r.key_id,i,{name:"Dukancard",description:"".concat(m.name," Plan (").concat(p,")"),theme:{color:"#6366f1"},prefill:{name:"",email:"",contact:""},notes:{}});if(console.log("[SUBSCRIPTION_HANDLER] openRazorpaySubscriptionCheckout completed with response:",n),console.log("[SUBSCRIPTION_HANDLER] Razorpay modal response:",n),n.razorpay_payment_id){f("Confirming payment...");let{confirmSubscriptionPayment:e}=await t.e(3570).then(t.bind(t,3570)),r=await e(i,n.razorpay_payment_id);if(!r.success)throw Error(r.error||"Failed to confirm payment");(null==(l=r.data)?void 0:l.is_future_payment)===!0?w("Your subscription has been authorized. Payment will be processed when your trial ends."):h.oR.success("Payment Authorized",{description:"Your ".concat(m.name," plan subscription has been authorized successfully.")}),j("Your ".concat(m.name," plan subscription has been created and authorized successfully.")),P("subscription"),g(!1),setTimeout(()=>{a.refresh()},1500)}}else throw Error("No subscription ID returned from server")}catch(e){console.error("Payment error:",e),e&&"object"==typeof e&&"cancelled"in e?(console.log("User cancelled the payment process"),h.oR.dismiss(),y()):(h.oR.error("Payment Authorization Failed",{description:e instanceof Error?e.message:"Failed to authorize payment. Please try again."}),v(!1,"Payment authorization failed. Please try again."))}}catch(a){console.error("Error in subscription handler:",a);let e=a instanceof Error?a.message:"An unexpected error occurred. Please try again.";h.oR.error("Subscription Error",{description:e}),v(!1,e)}finally{b(!1),r.current=!1}}}}}({currentSubscriptionId:u,subscriptionStatus:l,currentPlanDetails:n,currentPlanCycle:g,lastPaymentMethod:C,razorpaySubscriptionId:S,trialEndDate:o,dialogPlan:T.dialogPlan,billingCycle:T.billingCycle,setDialogLoading:T.setDialogLoading,setIsPlanDialogOpen:T.setIsPlanDialogOpen,startProcessing:T.startProcessing,completeProcessing:T.completeProcessing,resetProcessing:T.resetProcessing,setSubscriptionCreated:T.setSubscriptionCreated,setFuturePaymentAuthorized:T.setFuturePaymentAuthorized,validateSubscriptionRequest:T.validateSubscriptionRequest,handleFreePlanSubscription:T.handleFreePlanSubscription,setActiveTab:R}),{handleActivateTrial:D}=function(e){let{dialogPlan:a,billingCycle:t,setDialogLoading:r,setIsFirstTimePaidPlanDialogOpen:s,setActiveTab:n,startProcessing:i,completeProcessing:l,setSubscriptionCreated:o}=e,d=(0,x.useRouter)();return{handleActivateTrial:async()=>{if(a){r(!0);try{i("Activating your free trial..."),s(!1);let e=await e_(a.id,t);if(!e.success){let a=e.error||"Failed to activate trial. Please try again.";h.oR.error("Trial Activation Error",{description:a}),l(!1,a);return}h.oR.success("Trial Activated",{description:"Your 1-month free trial of the ".concat(a.name," plan has been activated successfully.")}),o("Your ".concat(a.name," plan trial has been activated successfully.")),n("subscription"),r(!1),setTimeout(()=>{d.refresh()},1500)}catch(a){let e=a instanceof Error?a.message:"An unexpected error occurred. Please try again.";h.oR.error("Trial Activation Error",{description:e}),l(!1,e),r(!1)}}}}}({dialogPlan:T.dialogPlan,billingCycle:T.billingCycle,setDialogLoading:T.setDialogLoading,setIsFirstTimePaidPlanDialogOpen:T.setIsFirstTimePaidPlanDialogOpen,setActiveTab:R,startProcessing:T.startProcessing,completeProcessing:T.completeProcessing,setSubscriptionCreated:T.setSubscriptionCreated});!function(e){let{setActiveTab:a}=e,t=(0,x.useRouter)(),r=(0,x.useSearchParams)();(0,s.useEffect)(()=>{let e=r.get("subscription_success"),s=r.get("subscription_id"),n=r.get("timestamp");if("true"===e&&s&&n){h.oR.success("Subscription ".concat(s," has been successfully authorized!"),{description:"Your subscription is now active.",duration:5e3}),a("subscription"),setTimeout(()=>{t.refresh()},1e3);let e=window.location.pathname;window.history.replaceState({},"",e)}let i=r.get("subscription_error"),l=r.get("error");if("true"===i&&l){h.oR.error("Subscription authorization failed",{description:decodeURIComponent(l),duration:8e3}),a("plans"),setTimeout(()=>{var e;null==(e=document.querySelector('[value="plans"]'))||e.scrollIntoView({behavior:"smooth"})},100);let e=window.location.pathname;window.history.replaceState({},"",e)}let o=r.get("upgrade");if(o){a("plans"),"analytics"===o?h.oR.info("Analytics Feature Requires Upgrade",{description:"The analytics feature is not available on the free tier. Please upgrade to Basic plan or higher to access analytics.",duration:8e3}):h.oR.info("Plan Upgrade Required",{description:"Please upgrade your plan to access additional features.",duration:5e3}),setTimeout(()=>{var e;null==(e=document.querySelector('[value="plans"]'))||e.scrollIntoView({behavior:"smooth"})},100);let e=window.location.pathname;window.history.replaceState({},"",e)}},[r,t,a])}({setActiveTab:R});let L="monthly"===T.billingCycle?d:c,O={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24,duration:.5}}};return(0,r.jsxs)(f.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.1,duration:.6,ease:"easeOut"}}},className:"space-y-6 max-w-7xl mx-auto",children:[o&&new Date(o)>new Date&&(0,r.jsx)(f.P.div,{variants:O,children:(0,r.jsx)(H,{trialEndDate:o})}),(0,r.jsx)(f.P.div,{variants:O,children:(0,r.jsx)("div",{className:"rounded-xl border bg-white dark:bg-black backdrop-blur-sm shadow-lg transition-all duration-300 relative overflow-hidden p-4 sm:p-5 md:p-6",children:(0,r.jsxs)(w,{tabs:[{id:"subscription",label:"My Subscription",icon:(0,r.jsx)(v.A,{className:"h-4 w-4"})},{id:"plans",label:"Plans",icon:(0,r.jsx)(y.A,{className:"h-4 w-4"})}],value:E,onChange:e=>{R(e)},className:"w-full",indicatorLayoutId:"mainPageTabs",children:[(0,r.jsx)(N,{value:"plans",className:"space-y-6",children:(0,r.jsx)(z,{subscriptionStatus:l,billingCycle:T.billingCycle,setBillingCycle:T.setBillingCycle,plans:L,currentPlanId:null==n?void 0:n.id,currentPlanCycle:g,loadingStates:{},onPlanAction:T.handlePlanAction})}),(0,r.jsx)(N,{value:"subscription",className:"space-y-6",children:(0,r.jsx)(ey,{userId:a,currentSubscriptionId:u,subscriptionStatus:l,currentPlanDetails:n,currentPlanCycle:g,nextBillingDate:m,subscriptionStartDate:j,subscriptionExpiryTime:k,subscriptionChargeTime:P,lastPaymentMethod:C,cancellationRequestedAt:p,cancelledAt:b,trialEndDate:o,isEligibleForRefund:void 0!==A&&A,setActiveTab:R})})]})})}),(0,r.jsx)(eR,{dialogPlan:T.dialogPlan,isPlanDialogOpen:T.isPlanDialogOpen,isFirstTimePaidPlanDialogOpen:T.isFirstTimePaidPlanDialogOpen,dialogLoading:T.dialogLoading,billingCycle:T.billingCycle,subscriptionStatus:l,trialEndDate:o,razorpaySubscriptionId:S,setIsPlanDialogOpen:T.setIsPlanDialogOpen,setIsFirstTimePaidPlanDialogOpen:T.setIsFirstTimePaidPlanDialogOpen,setDialogLoading:T.setDialogLoading,handleDialogSubscribe:_,handleActivateTrial:D,resetProcessing:T.resetProcessing}),(0,r.jsx)(I,{})]})}function ez(e){return(0,r.jsx)(eD,{...e})}var eL=t(27737);function eO(){let e={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24,duration:.5}}};return(0,r.jsxs)(f.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.1,duration:.6,ease:"easeOut"}}},className:"space-y-6 max-w-7xl mx-auto",children:[(0,r.jsx)(f.P.div,{variants:e,children:(0,r.jsx)("div",{className:"rounded-lg border bg-white dark:bg-black p-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(eL.E,{className:"h-10 w-10 rounded-full"}),(0,r.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,r.jsx)(eL.E,{className:"h-5 w-3/4"}),(0,r.jsx)(eL.E,{className:"h-4 w-1/2"})]})]})})}),(0,r.jsx)(f.P.div,{variants:e,children:(0,r.jsxs)("div",{className:"rounded-lg border bg-white dark:bg-black p-4 sm:p-5 md:p-6",children:[(0,r.jsxs)("div",{className:"flex space-x-2 border-b pb-2 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 relative",children:[(0,r.jsx)(eL.E,{className:"h-4 w-4 rounded-full"}),(0,r.jsx)(eL.E,{className:"h-5 w-32"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 relative",children:[(0,r.jsx)(eL.E,{className:"h-4 w-4 rounded-full"}),(0,r.jsx)(eL.E,{className:"h-5 w-20"})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"rounded-xl border p-4 sm:p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)(eL.E,{className:"h-8 w-8 rounded-full"}),(0,r.jsxs)("div",{children:[(0,r.jsx)(eL.E,{className:"h-5 w-40 mb-1"}),(0,r.jsx)(eL.E,{className:"h-4 w-24"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(eL.E,{className:"h-4 w-24"}),(0,r.jsx)(eL.E,{className:"h-4 w-32"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(eL.E,{className:"h-4 w-28"}),(0,r.jsx)(eL.E,{className:"h-4 w-24"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(eL.E,{className:"h-4 w-20"}),(0,r.jsx)(eL.E,{className:"h-4 w-36"})]})]})]}),(0,r.jsxs)("div",{className:"rounded-xl border p-4 sm:p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)(eL.E,{className:"h-8 w-8 rounded-full"}),(0,r.jsxs)("div",{children:[(0,r.jsx)(eL.E,{className:"h-5 w-36 mb-1"}),(0,r.jsx)(eL.E,{className:"h-4 w-48"})]})]}),(0,r.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,a)=>(0,r.jsxs)("div",{className:"flex justify-between items-center py-2 border-b",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(eL.E,{className:"h-4 w-32"}),(0,r.jsx)(eL.E,{className:"h-3 w-24"})]}),(0,r.jsx)(eL.E,{className:"h-6 w-16 rounded-full"})]},a))})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3 justify-center mt-4",children:[(0,r.jsx)(eL.E,{className:"h-10 w-32 rounded-full"}),(0,r.jsx)(eL.E,{className:"h-10 w-32 rounded-full"})]})]})]})})]})}function eM(e){let a=(0,x.useRouter)(),[t,n]=(0,s.useState)(!1),[i,l]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{l(!0);let e=setTimeout(()=>{d(!1)},800);return()=>clearTimeout(e)},[]),(0,s.useEffect)(()=>{if(!i||!e.userId)return;console.log("[REALTIME_PLAN] Setting up real-time subscription for user:",e.userId);let t=g.Z.subscribeToPaymentSubscriptions(e.userId,async e=>{if(console.log("[REALTIME_PLAN] Subscription update received:",e),"UPDATE"===e.eventType&&e.new&&"cancelled_at"in e.new&&e.new.cancelled_at&&e.old&&!("cancelled_at"in e.old&&e.old.cancelled_at))console.log("[REALTIME_PLAN] Subscription cancelled:",e.new),h.oR.info("Subscription Cancelled",{description:"Your subscription cancellation has been confirmed by Razorpay."}),n(!0),setTimeout(()=>n(!1),1e3),a.refresh();else if(e.new&&"subscription_status"in e.new){let t=function(e){switch(e.toLowerCase()){case"active":return"Your subscription is now active!";case"authenticated":return"Your payment has been authorized. Your subscription will start on the scheduled date.";case"created":return"Your subscription has been created. Please complete the payment process.";case"pending":return"Your subscription is pending payment authorization.";case"halted":return"Your subscription has been halted due to payment issues. Please update your payment method.";case"cancelled":return"Your subscription has been cancelled. You can subscribe again anytime.";case"pending_cancellation":return"Your subscription will be cancelled at the end of the current billing cycle.";case"completed":return"Your subscription has been completed. Thank you for being a subscriber!";case"expired":return"Your subscription has expired. Please renew to continue using premium features.";case"paused":return"Your subscription has been paused. You can resume it anytime.";default:return null}}(e.new.subscription_status);t&&h.oR.info("Subscription Updated",{description:t}),n(!0),setTimeout(()=>n(!1),1e3),a.refresh()}},"plan-page");return()=>{console.log("[REALTIME_PLAN] Cleaning up subscription"),t.unsubscribe()}},[e.userId,i,a]),o)?(0,r.jsx)(eO,{}):(0,r.jsx)("div",{className:i?"transition-all duration-300 ".concat(t?"bg-primary/5 rounded-xl p-2 -m-2":""):"transition-all duration-300",children:(0,r.jsx)(ez,{...e})})}function eH(e){return(0,r.jsx)(b,{children:(0,r.jsx)(l,{children:(0,r.jsx)(eM,{...e})})})}},82714:(e,a,t)=>{"use strict";t.d(a,{J:()=>i});var r=t(95155);t(12115);var s=t(40968),n=t(53999);function i(e){let{className:a,...t}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},88524:(e,a,t)=>{"use strict";t.d(a,{A0:()=>i,BF:()=>l,Hj:()=>o,XI:()=>n,nA:()=>c,nd:()=>d});var r=t(95155);t(12115);var s=t(53999);function n(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",a),...t})})}function i(e){let{className:a,...t}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",a),...t})}function l(e){let{className:a,...t}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",a),...t})}function o(e){let{className:a,...t}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...t})}function d(e){let{className:a,...t}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}function c(e){let{className:a,...t}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}},93432:(e,a,t)=>{"use strict";t.d(a,{Eb:()=>b,Iu:()=>u,M_:()=>h,WA:()=>x,cU:()=>m,dK:()=>c,n$:()=>p});var r=t(95155),s=t(12115),n=t(42355),i=t(13052),l=t(5623),o=t(53999),d=t(97168);let c=e=>{let{className:a,...t}=e;return(0,r.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,o.cn)("mx-auto flex w-full justify-center",a),...t})},u=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("ul",{ref:a,className:(0,o.cn)("flex flex-row items-center gap-1",t),...s})});u.displayName="PaginationContent";let m=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("li",{ref:a,className:(0,o.cn)("",t),...s})});m.displayName="PaginationItem";let p=e=>{let{className:a,isActive:t,size:s="icon",...n}=e;return(0,r.jsx)("a",{"aria-current":t?"page":void 0,className:(0,o.cn)((0,d.r)({variant:t?"outline":"ghost",size:s}),a,t&&"bg-muted hover:bg-muted pointer-events-none"),...n})};p.displayName="PaginationLink";let b=e=>{let{className:a,...t}=e;return(0,r.jsxs)(p,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",a),...t,children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Previous"})]})};b.displayName="PaginationPrevious";let x=e=>{let{className:a,...t}=e;return(0,r.jsxs)(p,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",a),...t,children:[(0,r.jsx)("span",{children:"Next"}),(0,r.jsx)(i.A,{className:"h-4 w-4"})]})};x.displayName="PaginationNext";let h=e=>{let{className:a,...t}=e;return(0,r.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",a),...t,children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"More pages"})]})};h.displayName="PaginationEllipsis"},94203:(e,a,t)=>{Promise.resolve().then(t.bind(t,75202))},99840:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>b,HM:()=>c,L3:()=>x,c7:()=>p,lG:()=>l,rr:()=>h,zM:()=>o});var r=t(95155);t(12115);var s=t(45821),n=t(54416),i=t(53999);function l(e){let{...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function d(e){let{...a}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function c(e){let{...a}=e;return(0,r.jsx)(s.bm,{"data-slot":"dialog-close",...a})}function u(e){let{className:a,...t}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function m(e){let{className:a,children:t,hideClose:l=!1,...o}=e;return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[t,!l&&(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function b(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function x(e){let{className:a,...t}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...t})}function h(e){let{className:a,...t}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...t})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8695,2290,6671,375,5152,7665,1884,67,781,106,8830,9010,7332,7066,1457,4092,8441,1684,7358],()=>a(94203)),_N_E=e.O()}]);