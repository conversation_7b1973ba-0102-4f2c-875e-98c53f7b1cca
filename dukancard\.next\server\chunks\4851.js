"use strict";exports.id=4851,exports.ids=[4851],exports.modules={3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},28695:(e,t,r)=>{r.d(t,{UC:()=>eA,YJ:()=>eH,In:()=>eM,q7:()=>eV,VF:()=>eW,p4:()=>eO,JU:()=>e_,ZL:()=>eL,bL:()=>eI,wn:()=>eG,PP:()=>eF,l9:()=>eP,WT:()=>eD,LM:()=>eB});var n=r(43210),l=r(51215),o=r(67969),a=r(70569),i=r(72031),s=r(98599),d=r(11273),u=r(43),c=r(31355),p=r(1359),f=r(32547),h=r(19344),v=r(55509),m=r(25028),g=r(3416),w=r(60687),x=Symbol("radix.slottable");function y(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===x}var S=r(13495),b=r(65551),C=r(66156),j=r(83721),R=r(69024),k=r(63376),T=r(11490),N=[" ","Enter","ArrowUp","ArrowDown"],E=[" ","Enter"],I="Select",[P,D,M]=(0,i.N)(I),[L,A]=(0,d.A)(I,[M,v.Bk]),B=(0,v.Bk)(),[H,_]=L(I),[V,O]=L(I),W=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:g,form:x}=e,y=B(t),[S,C]=n.useState(null),[j,R]=n.useState(null),[k,T]=n.useState(!1),N=(0,u.jH)(c),[E,D]=(0,b.i)({prop:l,defaultProp:o??!1,onChange:a,caller:I}),[M,L]=(0,b.i)({prop:i,defaultProp:s,onChange:d,caller:I}),A=n.useRef(null),_=!S||x||!!S.closest("form"),[O,W]=n.useState(new Set),F=Array.from(O).map(e=>e.props.value).join(";");return(0,w.jsx)(v.bL,{...y,children:(0,w.jsxs)(H,{required:g,scope:t,trigger:S,onTriggerChange:C,valueNode:j,onValueNodeChange:R,valueNodeHasChildren:k,onValueNodeHasChildrenChange:T,contentId:(0,h.B)(),value:M,onValueChange:L,open:E,onOpenChange:D,dir:N,triggerPointerDownPosRef:A,disabled:m,children:[(0,w.jsx)(P.Provider,{scope:t,children:(0,w.jsx)(V,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{W(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),_?(0,w.jsxs)(ek,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:M,onChange:e=>L(e.target.value),disabled:m,form:x,children:[void 0===M?(0,w.jsx)("option",{value:""}):null,Array.from(O)]},F):null]})})};W.displayName=I;var F="SelectTrigger",G=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=B(r),d=_(F,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=D(r),f=n.useRef("touch"),[h,m,x]=eN(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eE(t,e,r);void 0!==n&&d.onValueChange(n.value)}),y=e=>{u||(d.onOpenChange(!0),x()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,w.jsx)(v.Mz,{asChild:!0,...i,children:(0,w.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eT(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&N.includes(e.key)&&(y(),e.preventDefault())})})})});G.displayName=F;var K="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=_(K,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,C.N)(()=>{u(c)},[u,c]),(0,w.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eT(d.value)?(0,w.jsx)(w.Fragment,{children:a}):o})});U.displayName=K;var q=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});q.displayName="SelectIcon";var z=e=>(0,w.jsx)(m.Z,{asChild:!0,...e});z.displayName="SelectPortal";var Z="SelectContent",Y=n.forwardRef((e,t)=>{let r=_(Z,e.__scopeSelect),[o,a]=n.useState();return((0,C.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,w.jsx)(Q,{...e,ref:t}):o?l.createPortal((0,w.jsx)(J,{scope:e.__scopeSelect,children:(0,w.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,w.jsx)("div",{children:e.children})})}),o):null});Y.displayName=Z;var[J,X]=L(Z),$=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var o;let e,a,i=(o=r,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,s.t)(t,i):i),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,a=n.Children.toArray(l),i=a.find(y);if(i){let e=i.props.children,l=a.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,w.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,w.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:x,collisionPadding:y,sticky:S,hideWhenDetached:b,avoidCollisions:C,...j}=e,R=_(Z,r),[N,E]=n.useState(null),[I,P]=n.useState(null),M=(0,s.s)(t,e=>E(e)),[L,A]=n.useState(null),[B,H]=n.useState(null),V=D(r),[O,W]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(N)return(0,k.Eq)(N)},[N]),(0,p.Oh)();let G=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===n&&I&&(I.scrollTop=I.scrollHeight),r?.focus(),document.activeElement!==l))return},[V,I]),K=n.useCallback(()=>G([L,N]),[G,L,N]);n.useEffect(()=>{O&&K()},[O,K]);let{onOpenChange:U,triggerPointerDownPosRef:q}=R;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,U,q]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[z,Y]=eN(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eE(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),X=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&(A(e),n&&(F.current=!0))},[R.value]),Q=n.useCallback(()=>N?.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&H(e)},[R.value]),en="popper"===l?et:ee,el=en===et?{side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:x,collisionPadding:y,sticky:S,hideWhenDetached:b,avoidCollisions:C}:{};return(0,w.jsx)(J,{scope:r,content:N,viewport:I,onViewportChange:P,itemRefCallback:X,selectedItem:L,onItemLeave:Q,itemTextRefCallback:er,focusSelectedItem:K,selectedItemText:B,position:l,isPositioned:O,searchRef:z,children:(0,w.jsx)(T.A,{as:$,allowPinchZoom:!0,children:(0,w.jsx)(f.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,w.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,w.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>W(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,a.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>G(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=_(Z,r),d=X(Z,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.s)(t,e=>f(e)),v=D(r),m=n.useRef(!1),x=n.useRef(!0),{viewport:y,selectedItem:S,selectedItemText:b,focusSelectedItem:j}=d,R=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&S&&b){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(a,[10,Math.max(10,c-d)]);u.style.minWidth=s+"px",u.style.left=p+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(a,[10,Math.max(10,c-d)]);u.style.minWidth=s+"px",u.style.right=p+"px"}let a=v(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=f+h+d+parseInt(c.paddingBottom,10)+g,x=Math.min(5*S.offsetHeight,w),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),k=e.top+e.height/2-10,T=S.offsetHeight/2,N=f+h+(S.offsetTop+T);if(N<=k){let e=a.length>0&&S===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-k,T+(e?R:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+g);u.style.height=N+t+"px"}else{let e=a.length>0&&S===a[0].ref.current;u.style.top="0px";let t=Math.max(k,f+y.offsetTop+(e?j:0)+T);u.style.height=t+(w-N)+"px",y.scrollTop=N-k+y.offsetTop}u.style.margin="10px 0",u.style.minHeight=x+"px",u.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,y,S,b,i.dir,l]);(0,C.N)(()=>R(),[R]);let[k,T]=n.useState();(0,C.N)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===x.current&&(R(),j?.(),x.current=!1)},[R,j]);return(0,w.jsx)(er,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,w.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,w.jsx)(g.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});ee.displayName="SelectItemAlignedPosition";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=B(r);return(0,w.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});et.displayName="SelectPopperPosition";var[er,en]=L(Z,{}),el="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=X(el,r),d=en(el,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,w.jsx)(P.Slot,{scope:r,children:(0,w.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=el;var ea="SelectGroup",[ei,es]=L(ea),ed=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.B)();return(0,w.jsx)(ei,{scope:r,id:l,children:(0,w.jsx)(g.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});ed.displayName=ea;var eu="SelectLabel",ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=es(eu,r);return(0,w.jsx)(g.sG.div,{id:l.id,...n,ref:t})});ec.displayName=eu;var ep="SelectItem",[ef,eh]=L(ep),ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=_(ep,r),c=X(ep,r),p=u.value===l,[f,v]=n.useState(i??""),[m,x]=n.useState(!1),y=(0,s.s)(t,e=>c.itemRefCallback?.(e,l,o)),S=(0,h.B)(),b=n.useRef("touch"),C=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,w.jsx)(ef,{scope:r,value:l,disabled:o,textId:S,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,w.jsx)(P.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,w.jsx)(g.sG.div,{role:"option","aria-labelledby":S,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>x(!0)),onBlur:(0,a.m)(d.onBlur,()=>x(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{b.current=e.pointerType,o?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(E.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ev.displayName=ep;var em="SelectItemText",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=_(em,r),u=X(em,r),c=eh(em,r),p=O(em,r),[f,h]=n.useState(null),v=(0,s.s)(t,e=>h(e),c.onItemTextChange,e=>u.itemTextRefCallback?.(e,c.value,c.disabled)),m=f?.textContent,x=n.useMemo(()=>(0,w.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:S}=p;return(0,C.N)(()=>(y(x),()=>S(x)),[y,S,x]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(g.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});eg.displayName=em;var ew="SelectItemIndicator",ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eh(ew,r).isSelected?(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ex.displayName=ew;var ey="SelectScrollUpButton",eS=n.forwardRef((e,t)=>{let r=X(ey,e.__scopeSelect),l=en(ey,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,C.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(ej,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eS.displayName=ey;var eb="SelectScrollDownButton",eC=n.forwardRef((e,t)=>{let r=X(eb,e.__scopeSelect),l=en(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,C.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(ej,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eC.displayName=eb;var ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=X("SelectScrollButton",r),s=n.useRef(null),d=D(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,C.N)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eR="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=B(r),o=_(eR,r),a=X(eR,r);return o.open&&"popper"===a.position?(0,w.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=eR;var ek=n.forwardRef(({__scopeSelect:e,value:t,...r},l)=>{let o=n.useRef(null),a=(0,s.s)(l,o),i=(0,j.Z)(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,w.jsx)(g.sG.select,{...r,style:{...R.Qg,...r.style},ref:a,defaultValue:t})});function eT(e){return""===e||void 0===e}function eN(e){let t=(0,S.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eE(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}ek.displayName="SelectBubbleInput";var eI=W,eP=G,eD=U,eM=q,eL=z,eA=Y,eB=eo,eH=ed,e_=ec,eV=ev,eO=eg,eW=ex,eF=eS,eG=eC},67969:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},83721:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(43210);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};