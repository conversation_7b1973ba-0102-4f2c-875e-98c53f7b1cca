(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9361],{381:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2564:(e,t,r)=>{"use strict";r.d(t,{Qg:()=>l,bL:()=>s});var a=r(12115),n=r(63540),i=r(95155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),o=a.forwardRef((e,t)=>(0,i.jsx)(n.sG.span,{...e,ref:t,style:{...l,...e.style}}));o.displayName="VisuallyHidden";var s=o},4217:(e,t,r)=>{var a=r(36713),n=/^\s+/;e.exports=function(e){return e?e.slice(0,a(e)+1).replace(n,""):e}},5623:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},7985:(e,t,r)=>{e.exports="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g},11004:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Infinity",[["path",{d:"M6 16c5 0 7-8 12-8a4 4 0 0 1 0 8c-5 0-7-8-12-8a4 4 0 1 0 0 8",key:"18ogeb"}]])},13052:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13717:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14541:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]])},20570:(e,t,r)=>{var a=r(24376),n=Object.prototype,i=n.hasOwnProperty,l=n.toString,o=a?a.toStringTag:void 0;e.exports=function(e){var t=i.call(e,o),r=e[o];try{e[o]=void 0;var a=!0}catch(e){}var n=l.call(e);return a&&(t?e[o]=r:delete e[o]),n}},21492:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},24376:(e,t,r)=>{e.exports=r(82500).Symbol},31787:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Table2",[["path",{d:"M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18",key:"gugj83"}]])},33109:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},36713:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},36815:(e,t,r)=>{var a=r(4217),n=r(67460),i=r(70771),l=0/0,o=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,c=/^0o[0-7]+$/i,d=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return l;if(n(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=n(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=a(e);var r=s.test(e);return r||c.test(e)?d(e.slice(2),r?2:8):o.test(e)?l:+e}},37108:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},37475:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("IndianRupee",[["path",{d:"M6 3h12",key:"ggurg9"}],["path",{d:"M6 8h12",key:"6g4wlu"}],["path",{d:"m6 13 8.5 8",key:"u1kupk"}],["path",{d:"M6 13h3",key:"wdp6ag"}],["path",{d:"M9 13c6.667 0 6.667-10 0-10",key:"1nkvk2"}]])},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var a=r(12115),n=r(63540),i=r(95155),l=a.forwardRef((e,t)=>(0,i.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var o=l},42355:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},43453:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},45964:(e,t,r)=>{var a=r(67460),n=r(76685),i=r(36815),l=Math.max,o=Math.min;e.exports=function(e,t,r){var s,c,d,u,p,y,h=0,f=!1,v=!1,m=!0;if("function"!=typeof e)throw TypeError("Expected a function");function g(t){var r=s,a=c;return s=c=void 0,h=t,u=e.apply(a,r)}function b(e){var r=e-y,a=e-h;return void 0===y||r>=t||r<0||v&&a>=d}function x(){var e,r,a,i=n();if(b(i))return A(i);p=setTimeout(x,(e=i-y,r=i-h,a=t-e,v?o(a,d-r):a))}function A(e){return(p=void 0,m&&s)?g(e):(s=c=void 0,u)}function k(){var e,r=n(),a=b(r);if(s=arguments,c=this,y=r,a){if(void 0===p)return h=e=y,p=setTimeout(x,t),f?g(e):u;if(v)return clearTimeout(p),p=setTimeout(x,t),g(y)}return void 0===p&&(p=setTimeout(x,t)),u}return t=i(t)||0,a(r)&&(f=!!r.leading,d=(v="maxWait"in r)?l(i(r.maxWait)||0,t):d,m="trailing"in r?!!r.trailing:m),k.cancel=function(){void 0!==p&&clearTimeout(p),h=0,s=y=c=p=void 0},k.flush=function(){return void 0===p?u:A(n())},k}},47924:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},48611:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},49103:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},54861:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55868:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},57615:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Package2",[["path",{d:"M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z",key:"1ront0"}],["path",{d:"m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9",key:"19h2x1"}],["path",{d:"M12 3v6",key:"1holv5"}]])},60704:(e,t,r)=>{"use strict";r.d(t,{B8:()=>N,bL:()=>C,l9:()=>R});var a=r(12115),n=r(85185),i=r(46081),l=r(89196),o=r(28905),s=r(63540),c=r(94315),d=r(5845),u=r(61285),p=r(95155),y="Tabs",[h,f]=(0,i.A)(y,[l.RG]),v=(0,l.RG)(),[m,g]=h(y),b=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:o,activationMode:h="automatic",...f}=e,v=(0,c.jH)(o),[g,b]=(0,d.i)({prop:a,onChange:n,defaultProp:null!=i?i:"",caller:y});return(0,p.jsx)(m,{scope:r,baseId:(0,u.B)(),value:g,onValueChange:b,orientation:l,dir:v,activationMode:h,children:(0,p.jsx)(s.sG.div,{dir:v,"data-orientation":l,...f,ref:t})})});b.displayName=y;var x="TabsList",A=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,i=g(x,r),o=v(r);return(0,p.jsx)(l.bL,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:a,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});A.displayName=x;var k="TabsTrigger",w=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:i=!1,...o}=e,c=g(k,r),d=v(r),u=M(c.baseId,a),y=D(c.baseId,a),h=a===c.value;return(0,p.jsx)(l.q7,{asChild:!0,...d,focusable:!i,active:h,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":y,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...o,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||i||!e||c.onValueChange(a)})})})});w.displayName=k;var j="TabsContent";function M(e,t){return"".concat(e,"-trigger-").concat(t)}function D(e,t){return"".concat(e,"-content-").concat(t)}a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:i,children:l,...c}=e,d=g(j,r),u=M(d.baseId,n),y=D(d.baseId,n),h=n===d.value,f=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(o.C,{present:i||h,children:r=>{let{present:a}=r;return(0,p.jsx)(s.sG.div,{"data-state":h?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:y,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:a&&l})}})}).displayName=j;var C=b,N=A,R=w},62278:(e,t,r)=>{"use strict";r.d(t,{rc:()=>q,ZD:()=>L,UC:()=>V,VY:()=>z,hJ:()=>S,ZL:()=>O,bL:()=>T,hE:()=>I});var a=r(12115),n=r(46081),i=r(6101),l=r(45821),o=r(85185),s=r(95155),c=Symbol("radix.slottable"),d="AlertDialog",[u,p]=(0,n.A)(d,[l.Hs]),y=(0,l.Hs)(),h=e=>{let{__scopeAlertDialog:t,...r}=e,a=y(t);return(0,s.jsx)(l.bL,{...a,...r,modal:!0})};h.displayName=d,a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=y(r);return(0,s.jsx)(l.l9,{...n,...a,ref:t})}).displayName="AlertDialogTrigger";var f=e=>{let{__scopeAlertDialog:t,...r}=e,a=y(t);return(0,s.jsx)(l.ZL,{...a,...r})};f.displayName="AlertDialogPortal";var v=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=y(r);return(0,s.jsx)(l.hJ,{...n,...a,ref:t})});v.displayName="AlertDialogOverlay";var m="AlertDialogContent",[g,b]=u(m),x=function(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=c,t}("AlertDialogContent"),A=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...c}=e,d=y(r),u=a.useRef(null),p=(0,i.s)(t,u),h=a.useRef(null);return(0,s.jsx)(l.G$,{contentName:m,titleName:k,docsSlug:"alert-dialog",children:(0,s.jsx)(g,{scope:r,cancelRef:h,children:(0,s.jsxs)(l.UC,{role:"alertdialog",...d,...c,ref:p,onOpenAutoFocus:(0,o.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=h.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(x,{children:n}),(0,s.jsx)(R,{contentRef:u})]})})})});A.displayName=m;var k="AlertDialogTitle",w=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=y(r);return(0,s.jsx)(l.hE,{...n,...a,ref:t})});w.displayName=k;var j="AlertDialogDescription",M=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=y(r);return(0,s.jsx)(l.VY,{...n,...a,ref:t})});M.displayName=j;var D=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,n=y(r);return(0,s.jsx)(l.bm,{...n,...a,ref:t})});D.displayName="AlertDialogAction";var C="AlertDialogCancel",N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:n}=b(C,r),o=y(r),c=(0,i.s)(t,n);return(0,s.jsx)(l.bm,{...o,...a,ref:c})});N.displayName=C;var R=e=>{let{contentRef:t}=e,r="`".concat(m,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(m,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(m,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return a.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},T=h,O=f,S=v,V=A,q=D,L=N,I=w,z=M},64439:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},66932:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},67460:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},70771:(e,t,r)=>{var a=r(98233),n=r(48611);e.exports=function(e){return"symbol"==typeof e||n(e)&&"[object Symbol]"==a(e)}},76685:(e,t,r)=>{var a=r(82500);e.exports=function(){return a.Date.now()}},82500:(e,t,r)=>{var a=r(7985),n="object"==typeof self&&self&&self.Object===Object&&self;e.exports=a||n||Function("return this")()},84616:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},98233:(e,t,r)=>{var a=r(24376),n=r(20570),i=r(64439),l=a?a.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":l&&l in Object(e)?n(e):i(e)}}}]);