(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5213],{5793:(e,t,a)=>{Promise.resolve().then(a.bind(a,97647)),Promise.resolve().then(a.bind(a,22522))},22522:(e,t,a)=>{"use strict";a.d(t,{default:()=>c});var r=a(95155),s=a(12115),n=a(28695),l=a(37108),i=a(5937),o=a(97168),d=a(35695);function c(){let e=(0,d.useRouter)(),[t,a]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{a(!0)},[]),(0,r.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center p-4 bg-white dark:bg-black",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-md w-full bg-white dark:bg-neutral-900 rounded-xl shadow-lg p-8 border border-neutral-200 dark:border-neutral-800 text-center",children:[(0,r.jsx)("div",{className:"mb-6 flex justify-center",children:(0,r.jsx)("div",{className:"p-4 bg-neutral-100 dark:bg-neutral-800 rounded-full",children:(0,r.jsx)(l.A,{className:"h-12 w-12 text-[var(--brand-gold)]"})})}),(0,r.jsx)("h2",{className:"text-2xl font-bold mb-3 text-neutral-900 dark:text-neutral-100",children:"Product Unavailable"}),(0,r.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 mb-6",children:"This product is currently unavailable because the business is offline or in private mode. You cannot view or purchase this product at the moment."}),(0,r.jsxs)("div",{className:"relative group",children:[t&&(0,r.jsx)(n.P.div,{className:"absolute -inset-0.5 rounded-md blur-md",style:{background:"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))"},initial:{opacity:.7},animate:{opacity:[.7,.9,.7],boxShadow:["0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)","0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)","0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)"]},transition:{duration:2,repeat:1/0,repeatType:"reverse"}}),(0,r.jsx)(o.$,{onClick:()=>e.push("/discover"),className:"relative w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-8 py-6 h-12 rounded-md shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer",children:(0,r.jsxs)("span",{className:"relative z-10 flex items-center justify-center gap-2",children:["Discover Products",(0,r.jsx)(i.A,{className:"h-5 w-5"})]})})]})]})})}},27737:(e,t,a)=>{"use strict";a.d(t,{E:()=>n});var r=a(95155),s=a(53999);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",t),...a})}},53999:(e,t,a)=>{"use strict";a.d(t,{M0:()=>c,Yq:()=>u,cn:()=>n,gV:()=>l,gY:()=>d,kY:()=>i,vA:()=>o,vv:()=>m});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function l(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function i(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),a=e.substring(e.length-2),r="*".repeat(e.length-4);return"".concat(t).concat(r).concat(a)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),a=t[0],r=t[1];if(a.length<=2||r.length<=2||!r.includes("."))return"Email Hidden";let s=a.substring(0,2)+"*".repeat(a.length-2),n=r.split("."),l=n[0],i=n.slice(1).join("."),o=l.substring(0,2)+"*".repeat(l.length-2);return"".concat(s,"@").concat(o,".").concat(i)}function d(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),a=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let r=a.length-1;r>=0;r--)if(t>=a[r].value)return(e/a[r].value).toFixed(1).replace(/\.0$/,"")+a[r].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let a={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(a.hour="2-digit",a.minute="2-digit",a.hour12=!0),e.toLocaleString("en-IN",a)}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(a){return"".concat(t," ").concat(e.toFixed(2))}}},61141:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var r=a(95155),s=a(12115),n=a(66766),l=a(6874),i=a.n(l),o=a(27737),d=a(28695),c=a(60760),u=a(53999);let m=e=>null==e?null:e.toLocaleString("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}),x={hidden:{opacity:0,y:10},show:{opacity:1,y:0}},h={initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1,transition:{duration:.5,type:"spring",stiffness:400,damping:10}},hover:{scale:1.05,rotate:-2,transition:{type:"spring",stiffness:500}}};function p(e){var t;let{product:a,isLink:l=!0}=e,[p,v]=(0,s.useState)(!1),b=m(a.base_price),g=m(a.discounted_price),f=b,j=null,y=0,w="number"==typeof a.discounted_price&&a.discounted_price>0,N="number"==typeof a.base_price&&a.base_price>0;w&&N&&a.discounted_price<a.base_price?(f=g,j=b,y=Math.round((a.base_price-a.discounted_price)/a.base_price*100)):(f=b,j=null,y=0),f||(f="Price unavailable");let k=y>0,[_,A]=(0,s.useState)(!1),E=!a.is_available,C=(0,r.jsx)(d.P.div,{variants:x,initial:"hidden",animate:"show",className:"w-full overflow-hidden",children:(0,r.jsx)("div",{className:"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg",children:(0,r.jsxs)("div",{className:"relative w-full overflow-hidden rounded-lg",children:[(0,r.jsxs)("div",{className:"relative w-full overflow-hidden rounded-t-xl",children:[(()=>{var e;let t=a.image_url;if(a.images&&Array.isArray(a.images)&&a.images.length>0){let e="number"==typeof a.featured_image_index?Math.min(a.featured_image_index,a.images.length-1):0;t=a.images[e]}return!t||p?(0,r.jsx)("div",{className:"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl",children:(0,r.jsx)("svg",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}):(0,r.jsxs)("div",{className:"overflow-hidden",children:[!_&&(0,r.jsx)(o.E,{className:"absolute inset-0 rounded-t-xl"}),(0,r.jsx)(d.P.div,{className:"w-full",children:(0,r.jsx)(n.default,{src:t,alt:null!=(e=a.name)?e:"Product image",width:500,height:750,className:"w-full aspect-square object-cover ".concat(E?"filter grayscale opacity-70 transition-all duration-500":""," ").concat(_?"opacity-100":"opacity-0"," max-w-full"),loading:"lazy",onError:()=>v(!0),onLoad:()=>A(!0),quality:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",placeholder:"blur",style:{objectFit:"cover"}})})]})})(),E&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40",children:(0,r.jsx)("div",{className:"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground",children:(0,r.jsx)("span",{className:"font-medium tracking-wide uppercase text-xs sm:text-sm",children:"Out of Stock"})})}),k&&(0,r.jsx)(c.N,{children:(0,r.jsx)(d.P.div,{variants:h,initial:"initial",animate:"animate",whileHover:"hover",className:(0,u.cn)("absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg","bg-destructive","text-destructive-foreground border border-destructive-foreground/20","transform-gpu"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("span",{className:"text-[7px] sm:text-[9px] md:text-[10px] font-medium",children:"SAVE"}),(0,r.jsxs)("span",{className:"text-[9px] sm:text-xs md:text-sm leading-none",children:[y,"%"]})]})},"discount-badge-".concat(a.id))})]}),(0,r.jsxs)("div",{className:"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1",children:[(0,r.jsx)("p",{className:"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden",children:null!=(t=a.name)?t:"Unnamed Product"}),a.description&&(0,r.jsx)("p",{className:"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate",children:a.description}),(0,r.jsx)("div",{className:"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1",children:(0,r.jsxs)("div",{className:"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full",children:[f&&(0,r.jsx)("p",{className:"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full",children:f}),j&&(0,r.jsx)("p",{className:"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500",children:j})]})})]})]})})});return l&&"business_slug"in a&&a.business_slug?(0,r.jsx)(i(),{href:"/".concat(a.business_slug,"/product/").concat(a.slug||a.id),className:"block h-full",children:C}):C}},82147:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(95155);a(12115);let s=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,r.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"})})},86894:(e,t,a)=>{"use strict";a.d(t,{A7:()=>h,FN:()=>m,Oj:()=>v,Q8:()=>p,Wk:()=>x});var r=a(95155),s=a(12115),n=a(85005),l=a(35169),i=a(92138),o=a(53999),d=a(97168);let c=s.createContext(null);function u(){let e=s.useContext(c);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}function m(e){let{orientation:t="horizontal",opts:a,setApi:l,plugins:i,className:d,children:u,...m}=e,[x,h]=(0,n.A)({...a,axis:"horizontal"===t?"x":"y"},i),[p,v]=s.useState(!1),[b,g]=s.useState(!1),f=s.useCallback(e=>{e&&(v(e.canScrollPrev()),g(e.canScrollNext()))},[]),j=s.useCallback(()=>{null==h||h.scrollPrev()},[h]),y=s.useCallback(()=>{null==h||h.scrollNext()},[h]),w=s.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),j()):"ArrowRight"===e.key&&(e.preventDefault(),y())},[j,y]);return s.useEffect(()=>{h&&l&&l(h)},[h,l]),s.useEffect(()=>{if(h)return f(h),h.on("reInit",f),h.on("select",f),()=>{null==h||h.off("select",f)}},[h,f]),(0,r.jsx)(c.Provider,{value:{carouselRef:x,api:h,opts:a,orientation:t||((null==a?void 0:a.axis)==="y"?"vertical":"horizontal"),scrollPrev:j,scrollNext:y,canScrollPrev:p,canScrollNext:b},children:(0,r.jsx)("div",{onKeyDownCapture:w,className:(0,o.cn)("relative",d),role:"region","aria-roledescription":"carousel","data-slot":"carousel",...m,children:u})})}function x(e){let{className:t,...a}=e,{carouselRef:s,orientation:n}=u();return(0,r.jsx)("div",{ref:s,className:"overflow-hidden","data-slot":"carousel-content",children:(0,r.jsx)("div",{className:(0,o.cn)("flex","horizontal"===n?"-ml-4":"-mt-4 flex-col",t),...a})})}function h(e){let{className:t,...a}=e,{orientation:s}=u();return(0,r.jsx)("div",{role:"group","aria-roledescription":"slide","data-slot":"carousel-item",className:(0,o.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===s?"pl-4":"pt-4",t),...a})}function p(e){let{className:t,variant:a="outline",size:s="icon",...n}=e,{orientation:i,scrollPrev:c,canScrollPrev:m}=u();return(0,r.jsxs)(d.$,{"data-slot":"carousel-previous",variant:a,size:s,className:(0,o.cn)("absolute size-8 rounded-full","horizontal"===i?"top-1/2 -left-12 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",t),disabled:!m,onClick:c,...n,children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}function v(e){let{className:t,variant:a="outline",size:s="icon",...n}=e,{orientation:l,scrollNext:c,canScrollNext:m}=u();return(0,r.jsxs)(d.$,{"data-slot":"carousel-next",variant:a,size:s,className:(0,o.cn)("absolute size-8 rounded-full","horizontal"===l?"top-1/2 -right-12 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",t),disabled:!m,onClick:c,...n,children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Next slide"})]})}},88145:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var r=a(95155);a(12115);var s=a(99708),n=a(74466),l=a(53999);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:n=!1,...o}=e,d=n?s.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(i({variant:a}),t),...o})}},97168:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>i});var r=a(95155);a(12115);var s=a(99708),n=a(74466),l=a(53999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,l.cn)(i({variant:a,size:n,className:t})),...d})}},97647:(e,t,a)=>{"use strict";a.d(t,{default:()=>K});var r=a(95155),s=a(12115),n=a(66766),l=a(28695),i=a(57340),o=a(54481),d=a(37108),c=a(81284),u=a(66516),m=a(88145),x=a(97168),h=a(27737),p=a(49026),v=a(99708),b=a(13052),g=a(53999);function f(e){let{...t}=e;return(0,r.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...t})}function j(e){let{className:t,...a}=e;return(0,r.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,g.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",t),...a})}function y(e){let{className:t,...a}=e;return(0,r.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,g.cn)("inline-flex items-center gap-1.5",t),...a})}function w(e){let{asChild:t,className:a,...s}=e,n=t?v.DX:"a";return(0,r.jsx)(n,{"data-slot":"breadcrumb-link",className:(0,g.cn)("hover:text-foreground transition-colors",a),...s})}function N(e){let{className:t,...a}=e;return(0,r.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,g.cn)("text-foreground font-normal",t),...a})}function k(e){let{children:t,className:a,...s}=e;return(0,r.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,g.cn)("[&>svg]:size-3.5",a),...s,children:null!=t?t:(0,r.jsx)(b.A,{})})}var _=a(86894),A=a(82147);function E(e){let{whatsappNumber:t,productName:a,businessName:n,productUrl:l}=e,[i,o]=(0,s.useState)(!1),d=t.replace(/\D/g,""),c=encodeURIComponent("Hi ".concat(n,", I'm interested in your product \"").concat(a,'" that I saw on your Dukancard (').concat(l,"). Can you provide more information?")),u="https://wa.me/".concat(d,"?text=").concat(c);return(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-green-500/30 to-green-600/30 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium flex items-center justify-center gap-3 py-6 rounded-xl border border-green-500/20 transition-all duration-300",onMouseEnter:()=>o(!0),onMouseLeave:()=>o(!1),onClick:()=>window.open(u,"_blank"),children:[(0,r.jsx)("div",{className:"absolute inset-0 w-full h-full overflow-hidden rounded-xl",children:(0,r.jsx)("div",{className:"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"})}),(0,r.jsx)(A.A,{className:"w-5 h-5 ".concat(i?"animate-spin-slow":"")}),(0,r.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"WhatsApp"})]})]})}function C(){return(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-neutral-400/20 to-neutral-600/20 rounded-xl blur-md opacity-50 transition-opacity duration-300"}),(0,r.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-neutral-500 to-neutral-600 text-white font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-80 rounded-xl border border-neutral-500/10 transition-all duration-300",disabled:!0,children:[(0,r.jsx)(A.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"WhatsApp Unavailable"})]})]})}var S=a(19420);function P(e){let{phoneNumber:t,_businessName:a}=e,[n,l]=(0,s.useState)(!1),i=t.replace(/\D/g,"");return(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-blue-600/30 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium flex items-center justify-center gap-3 py-6 rounded-xl border border-blue-500/20 transition-all duration-300",onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),onClick:()=>window.open("tel:".concat(i),"_blank"),children:[(0,r.jsx)("div",{className:"absolute inset-0 w-full h-full overflow-hidden rounded-xl",children:(0,r.jsx)("div",{className:"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"})}),(0,r.jsx)(S.A,{className:"w-5 h-5 ".concat(n?"animate-pulse":"")}),(0,r.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"Call"})]})]})}function L(){return(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-neutral-400/20 to-neutral-600/20 rounded-xl blur-md opacity-50 transition-opacity duration-300"}),(0,r.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-neutral-500 to-neutral-600 text-white font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-80 rounded-xl border border-neutral-500/10 transition-all duration-300",disabled:!0,children:[(0,r.jsx)(S.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"Call Unavailable"})]})]})}var z=a(27809);function M(){let[e,t]=(0,s.useState)(!1);return(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/20 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] hover:from-[var(--brand-gold-dark)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-90 rounded-xl border border-[var(--brand-gold)]/20 transition-all duration-300",onMouseEnter:()=>t(!0),onMouseLeave:()=>t(!1),disabled:!0,children:[(0,r.jsx)("div",{className:"absolute inset-0 w-full h-full overflow-hidden rounded-xl",children:(0,r.jsx)("div",{className:"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"})}),(0,r.jsx)(z.A,{className:"w-5 h-5 ".concat(e?"animate-pulse":"")}),(0,r.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"Buy Now"})]})]})}var D=a(54416),F=a(6262),I=a(8619),T=a(60760);function O(e){let{isOpen:t,onClose:a,imageUrl:i,altText:d}=e,[c,u]=(0,s.useState)(1),[m,x]=(0,s.useState)(!0),h=(0,s.useRef)(null),p=(0,s.useRef)(null),[v,b]=(0,s.useState)({top:0,right:0,bottom:0,left:0}),g=(0,I.d)(0),f=(0,I.d)(0);!function(e,t,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{minScale:n=.5,maxScale:l=3}=r,[,i]=(0,s.useState)([]),[o,d]=(0,s.useState)(null),[c,u]=(0,s.useState)(1),[m,x]=(0,s.useState)(!1),[h,p]=(0,s.useState)(0),v=e=>{if(e.length<2)return 0;let t=e[0].clientX-e[1].clientX,a=e[0].clientY-e[1].clientY;return Math.sqrt(t*t+a*a)};(0,s.useEffect)(()=>{let r=e.current;if(!r)return;let s=e=>{p(Date.now());let t=Array.from(e.touches);i(t),2===t.length&&(d(v(t)),u(a),x(!0),e.preventDefault())},b=e=>{let r=Array.from(e.touches),s=Date.now();!(s-h<16)&&(p(s),2===r.length&&o&&o>0)&&(t(a+(Math.min(Math.max(c*(v(r)/o),n),l)-a)*.3),e.preventDefault(),e.stopPropagation())},g=e=>{let r=Array.from(e.touches);i(r),r.length<2&&(d(null),x(!1));let s=Date.now();s-h<300&&0===r.length&&!m&&(a>1?t(1):t(2)),p(s)};r.addEventListener("touchstart",s,{passive:!1}),r.addEventListener("touchmove",b,{passive:!1}),r.addEventListener("touchend",g,{passive:!1}),r.addEventListener("touchcancel",g,{passive:!1});let f=e=>{(a>1||m)&&(e.preventDefault(),e.stopPropagation())},j=r.parentElement;return j&&a>1&&j.addEventListener("touchmove",f,{passive:!1}),()=>{r.removeEventListener("touchstart",s),r.removeEventListener("touchmove",b),r.removeEventListener("touchend",g),r.removeEventListener("touchcancel",g),j&&a>1&&j.removeEventListener("touchmove",f)}},[e,o,c,a,t,n,l,m,h])}(p,u,c,{minScale:.5,maxScale:3,scaleStep:.1}),(0,s.useEffect)(()=>{if(!t)return;let e=()=>{if(c>1&&p.current&&h.current){let e=h.current.getBoundingClientRect(),t=p.current.getBoundingClientRect(),a=t.width*c,r=t.height*c,s=Math.max(0,(a-e.width)/2),n=Math.max(0,(r-e.height)/2);b({left:-s-50,right:s+50,top:-n-50,bottom:n+50})}else b({top:0,right:0,bottom:0,left:0}),g.set(0),f.set(0)};e();let a=setTimeout(e,100);return()=>clearTimeout(a)},[c,t,g,f]),(0,s.useEffect)(()=>{t&&(u(1),x(!0),g.set(0),f.set(0))},[t,g,f]),(0,s.useEffect)(()=>{if(c>1&&c<2){let e=1-(c-1),t=g.get(),a=f.get(),r=t*(1-e),s=a*(1-e),n=setTimeout(()=>{g.set(r),f.set(s)},10);return()=>clearTimeout(n)}c<=1&&(g.set(0),f.set(0))},[c,g,f]),(0,s.useEffect)(()=>{let e=e=>{t&&("Escape"===e.key?a():"+"===e.key||"="===e.key?u(e=>Math.min(e+.25,3)):("-"===e.key||"_"===e.key)&&u(e=>Math.max(e-.25,.5)))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[t,a]),(0,s.useEffect)(()=>{let e=e=>{let a;t&&h.current&&(e.preventDefault(),u(e.deltaY<0?Math.min(c+.1,3):Math.max(c-.1,.5)))},a=()=>{c>1&&(document.body.style.cursor="grabbing")},r=()=>{document.body.style.cursor=""},s=h.current;return s&&(s.addEventListener("wheel",e,{passive:!1}),s.addEventListener("mousedown",a),window.addEventListener("mouseup",r)),()=>{s&&(s.removeEventListener("wheel",e),s.removeEventListener("mousedown",a),window.removeEventListener("mouseup",r)),document.body.style.cursor=""}},[t,c]),(0,s.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[t]);let j=()=>u(e=>Math.min(e+.25,3)),y=()=>u(e=>Math.max(e-.25,.5));return(0,r.jsx)(T.N,{children:t&&(0,r.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center bg-black p-4",onClick:a,children:[(0,r.jsx)("button",{onClick:a,className:"absolute top-4 right-4 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors","aria-label":"Close",children:(0,r.jsx)(D.A,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 z-10 flex items-center gap-4 bg-black/50 rounded-full p-2",children:[(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),y()},className:"p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors","aria-label":"Zoom out",disabled:c<=.5,children:(0,r.jsx)(F.A,{className:"w-5 h-5"})}),(0,r.jsxs)("span",{className:"text-white text-sm font-medium",children:[Math.round(100*c),"%"]}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),j()},className:"p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors","aria-label":"Zoom in",disabled:c>=3,children:(0,r.jsx)(o.A,{className:"w-5 h-5"})})]}),(0,r.jsxs)(l.P.div,{ref:h,className:"relative w-full max-w-4xl h-[80vh] overflow-hidden touch-none",onClick:e=>e.stopPropagation(),initial:{scale:.9},animate:{scale:1},exit:{scale:.9},style:{touchAction:"none"},onTouchStart:e=>{c>1&&e.stopPropagation()},children:[m&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-10 h-10 border-4 border-neutral-300 border-t-[var(--brand-gold)] rounded-full animate-spin"})}),(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center overflow-hidden",children:(0,r.jsx)(l.P.div,{ref:p,drag:c>1,dragConstraints:v,dragElastic:0,dragMomentum:!1,dragTransition:{power:.1,timeConstant:200},style:{x:g,y:f,scale:c,touchAction:"none",cursor:c>1?"grab":"default"},className:"relative touch-none will-change-transform",whileDrag:{cursor:"grabbing"},onDoubleClick:e=>{if(c>1)u(1);else{let t=e.currentTarget.getBoundingClientRect(),a=e.clientX-t.left,r=e.clientY-t.top,s=t.width/2,n=t.height/2;u(2),g.set(-((a-s)*.5)),f.set(-((r-n)*.5))}},children:(0,r.jsx)("div",{className:"relative",style:{pointerEvents:"none"},children:(0,r.jsx)(n.default,{src:i,alt:d,width:1200,height:1800,className:"max-w-none object-contain select-none",onLoad:()=>x(!1),priority:!0,draggable:!1,unoptimized:!0,style:{userSelect:"none",WebkitUserSelect:"none"}})})})})]})]})})}var U=a(5196),R=a(34704);function B(e){let{variants:t,selectedVariant:a,onVariantSelect:n,className:i,disabled:o=!1}=e,[d,c]=(0,s.useState)({}),[u,x]=(0,s.useState)({});(0,s.useEffect)(()=>{if(!t||0===t.length)return;let e={},a={};t.forEach(t=>{Object.entries(t.variant_values||{}).forEach(t=>{let[a,r]=t;e[a]||(e[a]=new Set),e[a].add(r)})}),Object.entries(e).forEach(e=>{let[r,s]=e,n=(0,R.Ou)(r);a[r]=Array.from(s).map(e=>{let a=t.filter(t=>t.variant_values&&t.variant_values[r]===e),s=n.find(t=>t.value===e);return{type:r,value:e,display_value:(null==s?void 0:s.display_value)||e,color_code:null==s?void 0:s.color_code,available:a.some(e=>e.is_available),variants:a}}).sort((e,t)=>{let a=n.find(t=>t.value===e.value),r=n.find(e=>e.value===t.value);return a&&r?a.sort_order-r.sort_order:e.display_value.localeCompare(t.display_value)})}),c(a)},[t,u]),(0,s.useEffect)(()=>{if(0===Object.keys(u).length){null!==a&&n(null);return}let e=t.find(e=>Object.entries(u).every(t=>{let[a,r]=t;return e.variant_values&&e.variant_values[a]===r}));e&&e!==a?n(e):e||null===a||n(null)},[u,t,a,n]);let h=(e,t)=>{o||x(a=>{let r={...a};return r[e]===t?delete r[e]:r[e]=t,r})},p=(e,a)=>{if(0===Object.keys(u).length)return t.some(t=>t.is_available&&t.variant_values&&t.variant_values[e]===a);let r={...u,[e]:a};return t.some(e=>!!e.is_available&&Object.entries(r).every(t=>{let[a,r]=t;return e.variant_values&&e.variant_values[a]===r}))};return t&&0!==t.length?(0,r.jsx)("div",{className:(0,g.cn)("space-y-6",i),children:Object.entries(d).map(e=>{var t;let[a,s]=e;return(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-base font-semibold text-neutral-900 dark:text-neutral-100 capitalize",children:a}),u[a]&&(0,r.jsx)(m.E,{variant:"secondary",className:"text-xs font-medium",children:(null==(t=s.find(e=>e.value===u[a]))?void 0:t.display_value)||u[a]})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-3",children:s.map(e=>{let t=u[a]===e.value,s=p(a,e.value),n="color"===a.toLowerCase();return(0,r.jsx)(l.P.div,{whileHover:s&&!o?{scale:1.05}:{},whileTap:s&&!o?{scale:.95}:{},className:"relative",children:n&&e.color_code?(0,r.jsx)("button",{onClick:()=>h(a,e.value),disabled:!s&&!t||o,className:(0,g.cn)("relative w-12 h-12 rounded-full transition-all duration-200 border-2","border-neutral-300 dark:border-neutral-600",t&&"ring-2 ring-[var(--brand-gold)] ring-offset-2 ring-offset-white dark:ring-offset-neutral-900",!s&&!t&&"opacity-60 cursor-not-allowed",(s||t)&&"cursor-pointer hover:scale-105"),style:{backgroundColor:e.color_code},title:t?"".concat(e.display_value," (Click to deselect)"):e.display_value,children:(0,r.jsx)(T.N,{children:t&&(0,r.jsx)(l.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{duration:.2},className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)(U.A,{className:(0,g.cn)("h-5 w-5 drop-shadow-sm","#FFFFFF"===e.color_code||"#FFFF00"===e.color_code||"#FFC0CB"===e.color_code?"text-neutral-800":"text-white")})})})}):(0,r.jsx)("button",{onClick:()=>h(a,e.value),disabled:!s&&!t||o,className:(0,g.cn)("relative px-4 py-2.5 rounded-lg transition-all duration-200 font-medium text-sm border-2","border-neutral-300 dark:border-neutral-600",t&&["bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)]","border-[var(--brand-gold)] ring-2 ring-[var(--brand-gold)]/30"],!s&&!t&&["opacity-60 cursor-not-allowed","text-neutral-400 dark:text-neutral-600"],(s||t)&&!t&&["text-neutral-700 dark:text-neutral-300","hover:scale-105 hover:border-neutral-400 dark:hover:border-neutral-500"],(s||t)&&"cursor-pointer"),children:(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[e.display_value,(0,r.jsx)(T.N,{children:t&&(0,r.jsx)(l.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{duration:.2},children:(0,r.jsx)(U.A,{className:"h-4 w-4"})})})]})})},"".concat(a,"-").concat(e.value))})})]},a)})}):null}var $=a(6874),V=a.n($);let Y=e=>{try{return new URL(e),!0}catch(e){return!1}};function W(e){let{topAdData:t,itemVariants:a,businessCustomAd:s,userPlan:i}=e,o=s&&"object"==typeof s&&!0===s.enabled&&s.image_url&&"string"==typeof s.image_url&&""!==s.image_url.trim()&&Y(s.image_url),d=(null==s?void 0:s.link_url)&&"string"==typeof s.link_url&&""!==s.link_url.trim()&&Y(s.link_url),c=("pro"===i||"enterprise"===i)&&o,u=!c&&t&&t.imageUrl;return(0,r.jsx)(l.P.div,{variants:a,className:"w-full",children:(0,r.jsx)("div",{className:"flex-shrink-0 w-full overflow-hidden",children:c?d?(0,r.jsx)(V(),{href:s.link_url,target:"_blank",rel:"noopener noreferrer",className:"block w-full overflow-hidden",children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)(n.default,{src:s.image_url,alt:"Business Advertisement",width:1200,height:675,className:"w-full h-auto object-contain max-w-full",unoptimized:!0}),(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:"Sponsored"})]})}):(0,r.jsx)("div",{className:"block w-full overflow-hidden",children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)(n.default,{src:s.image_url,alt:"Business Advertisement",width:1200,height:675,className:"w-full h-auto object-contain max-w-full",unoptimized:!0}),(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:"Sponsored"})]})}):u?(0,r.jsx)(V(),{href:t.linkUrl||"#",target:"_blank",rel:"noopener noreferrer",className:"block w-full overflow-hidden",children:(0,r.jsx)("div",{className:"relative w-full",children:(0,r.jsx)(n.default,{src:t.imageUrl,alt:"Advertisement",width:1200,height:675,className:"w-full h-auto object-contain max-w-full",unoptimized:!0})})}):(0,r.jsx)("div",{className:"border border-dashed rounded-lg p-4 flex items-center justify-center w-full text-neutral-500 bg-neutral-50 dark:bg-neutral-800/50 min-h-[300px]"})})})}function q(e){let{product:t,variants:a=[],businessSlug:v,businessName:b,whatsappNumber:A,phoneNumber:S,topAdData:z,businessCustomAd:D,userPlan:F}=e,[I,T]=(0,s.useState)({}),[U,R]=(0,s.useState)({}),[$,V]=(0,s.useState)(""),[Y,q]=(0,s.useState)(!1),[X,H]=(0,s.useState)(!1),[K,Q]=(0,s.useState)(null),[Z,G]=(0,s.useState)(),[J,ee]=(0,s.useState)(),[et,ea]=(0,s.useState)([]),[er,es]=(0,s.useState)(0),[en,el]=(0,s.useState)(null),[ei,eo]=(0,s.useState)(t);(0,s.useEffect)(()=>{V(window.location.href);let e=[];if(en&&en.images&&en.images.length>0?e.push(...en.images):(ei.images&&Array.isArray(ei.images)&&e.push(...ei.images),0===e.length&&ei.image_url&&e.push(ei.image_url)),ea(e),e.length>0){let t="number"==typeof ei.featured_image_index?Math.min(ei.featured_image_index,e.length-1):0;es(t),Q(e[t]),Z&&Z.scrollTo(t)}},[ei.images,ei.image_url,ei.featured_image_index,en,Z]),(0,s.useEffect)(()=>{Z&&Z.scrollTo(er)},[Z,er]),(0,s.useEffect)(()=>{Z&&J&&(Z.reInit(),J.reInit(),setTimeout(()=>{Z.scrollTo(er),J.scrollTo(er)},100))},[et,Z,J,er]),(0,s.useEffect)(()=>{if(!Z||!J)return;let e=()=>{let e=Z.selectedScrollSnap();es(e),Q(et[e]),J.scrollTo(e)};return Z.on("select",e),Z.on("reInit",e),()=>{Z.off("select",e),Z.off("reInit",e)}},[Z,J,et]);let ed=ei.base_price?(0,g.vv)(ei.base_price):"Price not set",ec=ei.discounted_price?(0,g.vv)(ei.discounted_price):null,eu=0;ei.discounted_price&&ei.base_price&&ei.discounted_price<ei.base_price&&(eu=Math.round((ei.base_price-ei.discounted_price)/ei.base_price*100));let em={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return(0,r.jsxs)(l.P.div,{className:"w-full mx-auto flex flex-col",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",children:[X&&K&&(0,r.jsx)(O,{isOpen:X,onClose:()=>H(!1),imageUrl:K,altText:t.name||"Product image"}),(0,r.jsx)(l.P.div,{variants:em,className:"mb-5",children:(0,r.jsx)(f,{children:(0,r.jsxs)(j,{children:[(0,r.jsx)(y,{children:(0,r.jsx)(w,{href:"/",children:(0,r.jsx)(i.A,{className:"w-4 h-4"})})}),(0,r.jsx)(k,{}),(0,r.jsx)(y,{children:(0,r.jsx)(w,{href:"/".concat(v),children:b})}),(0,r.jsx)(k,{}),(0,r.jsx)(y,{children:(0,r.jsx)(N,{children:t.name})})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 lg:gap-16",children:[(0,r.jsxs)(l.P.div,{variants:{hidden:{opacity:0,x:-30},visible:{opacity:1,x:0,transition:{duration:.7,ease:"easeOut"}}},className:"relative md:sticky md:top-20 self-start md:px-4",children:[(0,r.jsx)(_.FN,{className:"w-full mb-4",opts:{align:"start",loop:!0},setApi:G,children:(0,r.jsx)(_.Wk,{children:et.length>0?et.map((e,a)=>(0,r.jsx)(_.A7,{children:(0,r.jsxs)("div",{className:"relative w-full aspect-square overflow-hidden bg-neutral-50 dark:bg-neutral-900 group rounded-xl border border-neutral-200 dark:border-neutral-800 cursor-pointer",onClick:()=>{e&&!U[e]&&H(!0)},children:[!I[e]&&!U[e]&&(0,r.jsx)(h.E,{className:"absolute inset-0"}),U[e]?(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"w-20 h-20 text-neutral-300 dark:text-neutral-700"})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.default,{src:e,alt:t.name,fill:!0,className:"object-cover transition-all duration-500 ".concat(I[e]?"opacity-100":"opacity-0"," group-hover:scale-105"),onLoad:()=>T(t=>({...t,[e]:!0})),onError:()=>R(t=>({...t,[e]:!0})),sizes:"(max-width: 768px) 100vw, 50vw",priority:!0}),I[e]&&(0,r.jsx)("div",{className:"absolute bottom-3 right-3 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer",children:(0,r.jsx)(o.A,{className:"w-5 h-5"})})]}),eu>0&&(0,r.jsx)(l.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:400,damping:10},className:"absolute top-4 right-4 bg-red-500 text-white px-3 py-1.5 rounded-lg font-bold text-sm shadow-lg",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("span",{className:"text-xs font-medium sm:text-[0.6rem]",children:"SAVE"}),(0,r.jsxs)("span",{className:"text-sm leading-none sm:text-xs",children:[eu,"%"]})]})})]})},"main-".concat(e,"-").concat(a))):(0,r.jsx)(_.A7,{children:(0,r.jsx)("div",{className:"relative w-full aspect-square overflow-hidden bg-neutral-50 dark:bg-neutral-900 group rounded-xl border border-neutral-200 dark:border-neutral-800 cursor-pointer mb-4 flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"w-20 h-20 text-neutral-300 dark:text-neutral-700"})})})})},"main-carousel-".concat(et.length,"-").concat(et[0]||"empty")),et.length>1&&(0,r.jsx)(_.FN,{className:"w-full",opts:{align:"start",loop:!0},setApi:ee,children:(0,r.jsx)(_.Wk,{className:"justify-center",children:et.map((e,a)=>(0,r.jsx)(_.A7,{className:"basis-1/4 md:basis-1/5 lg:basis-1/6",children:(0,r.jsx)("div",{className:"relative aspect-square overflow-hidden rounded-md cursor-pointer border-2 ".concat(K===e?"border-[var(--brand-gold)]":"border-transparent"),onClick:()=>es(a),children:(0,r.jsx)(n.default,{src:e,alt:"".concat(t.name||"Product"," - Image ").concat(a+1),fill:!0,className:"object-cover",sizes:"(max-width: 768px) 25vw, 10vw",onLoad:()=>T(t=>({...t,[e]:!0})),onError:()=>R(t=>({...t,[e]:!0}))})})},"thumb-".concat(e,"-").concat(a)))})},"thumbnail-carousel-".concat(et.length,"-").concat(et[0]||"empty"))]}),(0,r.jsxs)(l.P.div,{variants:{hidden:{opacity:0,x:30},visible:{opacity:1,x:0,transition:{duration:.7,ease:"easeOut",delay:.2}}},className:"flex flex-col space-y-5 p-6 md:p-8 lg:p-10 md:sticky md:top-20 self-start md:px-4",children:[(0,r.jsx)("h1",{className:"text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 leading-tight",children:t.name}),(0,r.jsx)(m.E,{variant:"secondary",className:"w-fit capitalize",children:t.product_type}),(0,r.jsx)("div",{className:"flex items-baseline gap-3",children:ec?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50",children:ec}),(0,r.jsx)("span",{className:"text-sm md:text-base line-through text-neutral-500 dark:text-neutral-400",children:ed})]}):(0,r.jsx)("span",{className:"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50",children:ed})}),(0,r.jsxs)(p.Fc,{variant:"default",className:"mt-4 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)(p.XL,{children:"Important Price Information"}),(0,r.jsx)(p.TN,{children:"Prices are indicative and may vary in-store. Visit us or contact directly for final deals and confirmation."})]}),a&&a.length>0&&(0,r.jsx)(l.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},className:"mt-8",children:(0,r.jsx)(B,{variants:a,selectedVariant:en,onVariantSelect:e=>{el(e),e?eo({...t,base_price:e.base_price||t.base_price,discounted_price:e.discounted_price||t.discounted_price,images:e.images&&e.images.length>0?e.images:t.images,featured_image_index:e.images&&e.images.length>0?e.featured_image_index:t.featured_image_index}):eo(t)}})}),(0,r.jsxs)("div",{className:"mt-6 pt-5 border-t border-neutral-200 dark:border-neutral-800 space-y-4",children:[(0,r.jsx)(M,{}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)("div",{children:A?(0,r.jsx)(E,{whatsappNumber:A,productName:t.name,businessName:b,productUrl:$||"".concat("http://localhost:3000","/").concat(v,"/product/").concat(t.slug||t.id)}):(0,r.jsx)(C,{})}),(0,r.jsx)("div",{children:S?(0,r.jsx)(P,{phoneNumber:S,_businessName:b}):(0,r.jsx)(L,{})})]})]}),t.description&&(0,r.jsxs)("div",{className:"mt-6 text-neutral-700 dark:text-neutral-300",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Description"}),(0,r.jsx)("p",{className:"whitespace-pre-line leading-relaxed",children:t.description})]}),(0,r.jsxs)("div",{className:"mt-4 relative group",children:[(0,r.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-neutral-500/20 to-neutral-600/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsxs)(x.$,{variant:"outline",className:"relative w-full flex items-center justify-center gap-3 py-5 border border-neutral-300/50 dark:border-neutral-700/50 hover:border-[var(--brand-gold)]/50 dark:hover:border-[var(--brand-gold)]/50 rounded-xl transition-all duration-300 bg-white/50 dark:bg-black/50 backdrop-blur-sm",onMouseEnter:()=>q(!0),onMouseLeave:()=>q(!1),onClick:()=>{let e=$||"".concat("http://localhost:3000","/").concat(v,"/product/").concat(t.slug||t.id),r="Check out ".concat(t.name," from ").concat(b," on Dukancard");if(en&&a.length>0){let e=Object.entries(en.variant_values||{}).map(e=>{let[t,a]=e;return"".concat(t,": ").concat(a)}).join(", ");r="Check out ".concat(t.name," (").concat(e,") from ").concat(b," on Dukancard")}navigator.share?navigator.share({title:t.name,text:r,url:e}):(navigator.clipboard.writeText(e),alert("Link copied to clipboard!"))},children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-[var(--brand-gold)] transition-all duration-300 ".concat(Y?"rotate-12":"")}),(0,r.jsx)("span",{className:"text-base font-medium tracking-wide",children:"Share"})]})]})]})]}),(0,r.jsx)(l.P.div,{variants:em,className:"mt-8 md:mt-12",children:(0,r.jsx)(W,{topAdData:z,itemVariants:em,businessCustomAd:D,userPlan:F})})]})}var X=a(61141);function H(e){let{businessProducts:t,otherBusinessProducts:a,businessSlug:s}=e,n={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},i={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return(0,r.jsxs)("div",{className:"mt-8 sm:mt-12 md:mt-16 space-y-8 sm:space-y-12 md:space-y-16 md:px-4",children:[t.length>0&&(0,r.jsxs)(l.P.section,{variants:n,initial:"hidden",animate:"visible",className:"space-y-6",children:[(0,r.jsx)(l.P.div,{variants:i,className:"flex items-center justify-between",children:(0,r.jsxs)("h2",{className:"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50 relative",children:["More from this business",(0,r.jsx)("span",{className:"absolute -bottom-2 left-0 w-16 h-1 bg-primary rounded-full"})]})}),(0,r.jsx)(l.P.div,{variants:i,className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6",children:t.map(e=>(0,r.jsx)(V(),{href:"/".concat(s,"/product/").concat(e.slug||e.id),className:"block h-full transform transition-transform duration-300 hover:-translate-y-1",children:(0,r.jsx)(X.A,{product:e,isLink:!1})},e.id))})]}),a.length>0&&(0,r.jsxs)(l.P.section,{variants:n,initial:"hidden",animate:"visible",className:"space-y-6",children:[(0,r.jsx)(l.P.div,{variants:i,className:"flex items-center justify-between",children:(0,r.jsxs)("h2",{className:"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50 relative",children:["You might also like",(0,r.jsx)("span",{className:"absolute -bottom-2 left-0 w-16 h-1 bg-primary rounded-full"})]})}),(0,r.jsx)(l.P.div,{variants:i,className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6",children:a.map(e=>(0,r.jsx)(V(),{href:"/".concat(e.business_slug,"/product/").concat(e.slug||e.id),className:"block h-full transform transition-transform duration-300 hover:-translate-y-1",children:(0,r.jsx)(X.A,{product:e,isLink:!1})},e.id))})]})]})}function K(e){let{product:t,variants:a=[],businessSlug:n,businessName:l,whatsappNumber:i,phoneNumber:o,businessProducts:d,otherBusinessProducts:c,topAdData:u,businessCustomAd:m,userPlan:x}=e;return(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:"Loading product details..."}),children:(0,r.jsxs)("div",{className:"w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-8 min-h-screen max-w-7xl",children:[(0,r.jsx)(q,{product:t,variants:a,businessSlug:n,businessName:l,whatsappNumber:i,phoneNumber:o,topAdData:u,businessCustomAd:m,userPlan:x}),(d.length>0||c.length>0)&&(0,r.jsx)(H,{businessProducts:d.slice(0,12),otherBusinessProducts:c.slice(0,12),businessSlug:n})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,6766,9862,7067,933,8441,1684,7358],()=>t(5793)),_N_E=e.O()}]);