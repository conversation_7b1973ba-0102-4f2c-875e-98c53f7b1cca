(()=>{var e={};e.id=4352,e.ids=[4352],e.modules={2792:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>g,routeModule:()=>y,serverHooks:()=>v,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>m});var a={};i.r(a),i.d(a,{GET:()=>h});var s=i(96559),n=i(48088),c=i(37719),o=i(32190),r=(i(32032),i(97329)),l=i(33873);function p(e,t,i,a={}){return"subscription.charged"===e?{entity:"event",account_id:"acc_test",event:e,contains:["payment"],created_at:Math.floor(Date.now()/1e3),payload:{payment:{entity:{id:`pay_test_${Date.now()}`,amount:99900,currency:"INR",status:"captured",method:a.payment_method||"card",captured:!0,subscription_id:t,notes:{business_profile_id:i,plan_type:a.plan_id||"growth",plan_cycle:a.plan_cycle||"monthly",...a.old_subscription_id?{old_subscription_id:a.old_subscription_id}:{}},created_at:Math.floor(Date.now()/1e3)}}}}:"subscription.halted"===e?{entity:"event",account_id:"acc_test",event:e,contains:["subscription"],created_at:Math.floor(Date.now()/1e3),payload:{subscription:{entity:{id:t,status:"halted",payment_method:a.payment_method||"card",notes:{business_profile_id:i,plan_type:a.plan_id||"growth",plan_cycle:a.plan_cycle||"monthly",...a.payment_method?{payment_method:a.payment_method}:{}},current_start:Math.floor(Date.now()/1e3),current_end:Math.floor(Date.now()/1e3)+2592e3,charge_at:Math.floor(Date.now()/1e3)+2592e3,customer_id:`cust_test_${Date.now()}`}}}}:{entity:"event",account_id:"acc_test",event:e,contains:["subscription"],created_at:Math.floor(Date.now()/1e3),payload:{subscription:{entity:{id:t,status:({"subscription.authenticated":"authenticated","subscription.activated":"active","subscription.charged":"active","subscription.pending":"pending","subscription.halted":"halted","subscription.cancelled":"cancelled","subscription.completed":"completed","subscription.expired":"expired","subscription.updated":"active"})[e]||"active",payment_method:a.payment_method||"card",notes:{business_profile_id:i,plan_type:a.plan_id||"growth",plan_cycle:a.plan_cycle||"monthly",...a.payment_method?{payment_method:a.payment_method}:{},...a.old_subscription_id?{old_subscription_id:a.old_subscription_id}:{},...a.last_payment_id?{last_payment_id:a.last_payment_id}:{}},current_start:Math.floor(Date.now()/1e3),current_end:Math.floor(Date.now()/1e3)+2592e3,charge_at:Math.floor(Date.now()/1e3)+2592e3,customer_id:`cust_test_${Date.now()}`,...a.current_start?{current_start:a.current_start}:{},...a.current_end?{current_end:a.current_end}:{},...a.charge_at?{charge_at:a.charge_at}:{}}}}}}(0,r.config)({path:(0,l.resolve)(process.cwd(),".env.local")});class d{constructor(e){this.businessId=e}async initialize(){let{createClient:e}=await Promise.resolve().then(i.bind(i,32032));this.supabase=e()}async runScenario(e,t=!1){let i=Date.now();try{console.log(`
🧪 Testing: ${e.name}`),console.log(`   Description: ${e.description}`),await this.setupInitialState(e);let a=await this.getCurrentState();console.log(`   Initial: ${a.subscription_status}/${a.plan_id}/${a.has_active_subscription}`);let s=await this.sendWebhook(e);console.log(`   Webhook: ${s.success?"✅":"❌"} ${s.message}`);let n=null,c=t||e.isIdempotencyTest;c&&s.success&&(n=await this.testIdempotency(e),console.log(`   Idempotency: ${n.success?"✅":"❌"} ${n.message}`));let o=await this.getCurrentState();console.log(`   Final: ${o.subscription_status}/${o.plan_id}/${o.has_active_subscription}`);let r=this.validateStateTransition(e,o),l=e.shouldSucceed?s.success&&r&&(!c||n?.success===!0):s.success&&s.message.includes("STATE REJECTION")&&r;return{scenario:e,success:l,message:l?`Scenario passed - state transition correct${c?" and idempotency working":""}`:`Scenario failed - expected state not reached${n&&!n.success?" or idempotency failed":""}`,duration:(Date.now()-i)/1e3,details:{initialState:a,finalState:o,webhookResult:s,idempotencyResult:n,stateMatches:r}}}catch(t){return{scenario:e,success:!1,message:`Scenario failed with error: ${t instanceof Error?t.message:String(t)}`,duration:(Date.now()-i)/1e3,details:{initialState:{},finalState:{},webhookResult:{success:!1,message:"Error occurred"},idempotencyResult:null,stateMatches:!1}}}}async setupInitialState(e){let t=`sub_test_${e.id}_${Date.now()}`;e.isCreateNewCancelOld?await this.setupCreateNewCancelOldScenario(e,t):await this.setupStandardScenario(e,t)}async setupStandardScenario(e,t){let i={};"halted_to_active_restore_original_plan"===e.id&&(i.original_plan_id="premium",i.original_plan_cycle="yearly",i.subscription_paused_at=new Date().toISOString()),"delayed_webhook_processing"===e.id&&(i.original_plan_id="premium",i.original_plan_cycle="yearly",i.subscription_paused_at=new Date().toISOString()),"active_to_halted_with_original_plan_storage"===e.id&&(i.original_plan_id=null,i.original_plan_cycle=null,i.subscription_paused_at=null),await this.supabase.from("payment_subscriptions").update({razorpay_subscription_id:t,plan_id:e.initialState.plan_id,plan_cycle:e.initialState.plan_cycle,subscription_status:e.initialState.subscription_status,cancelled_at:null,last_payment_method:e.paymentMethod||"card",updated_at:new Date().toISOString(),...i}).eq("business_profile_id",this.businessId),await this.supabase.from("business_profiles").update({has_active_subscription:e.initialState.has_active_subscription,updated_at:new Date().toISOString()}).eq("id",this.businessId)}async setupCreateNewCancelOldScenario(e,t){let i=`sub_old_${e.id}_${Date.now()}`;"subscription.activated"===e.webhookEvent?(await this.supabase.from("payment_subscriptions").update({razorpay_subscription_id:i,plan_id:e.initialState.plan_id,plan_cycle:e.initialState.plan_cycle,subscription_status:e.initialState.subscription_status,cancelled_at:null,last_payment_method:e.paymentMethod||"upi",original_plan_id:null,original_plan_cycle:null,subscription_paused_at:null,updated_at:new Date().toISOString()}).eq("business_profile_id",this.businessId),e.oldSubscriptionId=i):"subscription.cancelled"===e.webhookEvent&&(await this.supabase.from("payment_subscriptions").update({razorpay_subscription_id:i,plan_id:e.initialState.plan_id,plan_cycle:e.initialState.plan_cycle,subscription_status:e.initialState.subscription_status,cancelled_at:null,last_payment_method:e.paymentMethod||"upi",original_plan_id:null,original_plan_cycle:null,subscription_paused_at:null,updated_at:new Date().toISOString()}).eq("business_profile_id",this.businessId),e.oldSubscriptionId=i),await this.supabase.from("business_profiles").update({has_active_subscription:e.initialState.has_active_subscription,updated_at:new Date().toISOString()}).eq("id",this.businessId)}async sendWebhook(e){return e.isCreateNewCancelOld?await this.sendCreateNewCancelOldWebhook(e):await this.sendStandardWebhook(e)}async sendStandardWebhook(e){let t=`sub_test_${e.id}_${Date.now()}`;await this.supabase.from("payment_subscriptions").update({razorpay_subscription_id:t,updated_at:new Date().toISOString()}).eq("business_profile_id",this.businessId);let a=p(e.webhookEvent,t,this.businessId,{plan_id:e.initialState.plan_id,plan_cycle:e.initialState.plan_cycle,payment_method:e.paymentMethod||"card"}),s=`test_${e.id}_${Date.now()}`,{handleRazorpayWebhook:n}=await Promise.all([i.e(5193),i.e(8485),i.e(9423),i.e(5569),i.e(3243)]).then(i.bind(i,33243));return await n(a,"",void 0,void 0,s)}async sendCreateNewCancelOldWebhook(e){if("subscription.activated"===e.webhookEvent){let t=`sub_new_${e.id}_${Date.now()}`;await this.supabase.from("payment_subscriptions").update({razorpay_subscription_id:t,plan_id:e.expectedFinalState.plan_id,plan_cycle:e.expectedFinalState.plan_cycle,subscription_status:"authenticated",last_payment_method:e.paymentMethod||"upi",original_plan_id:null,original_plan_cycle:null,subscription_paused_at:null,updated_at:new Date().toISOString()}).eq("business_profile_id",this.businessId);let a=p(e.webhookEvent,t,this.businessId,{plan_id:e.expectedFinalState.plan_id,plan_cycle:e.expectedFinalState.plan_cycle,payment_method:e.paymentMethod||"upi",old_subscription_id:e.oldSubscriptionId}),s=`test_${e.id}_${Date.now()}`,{handleRazorpayWebhook:n}=await Promise.all([i.e(5193),i.e(8485),i.e(9423),i.e(5569),i.e(3243)]).then(i.bind(i,33243));return await n(a,"",void 0,void 0,s)}if("subscription.cancelled"===e.webhookEvent){let t=e.oldSubscriptionId||`sub_old_${e.id}_${Date.now()}`,a=p(e.webhookEvent,t,this.businessId,{plan_id:e.initialState.plan_id,plan_cycle:e.initialState.plan_cycle,payment_method:e.paymentMethod||"upi"}),s=`test_${e.id}_${Date.now()}`,{handleRazorpayWebhook:n}=await Promise.all([i.e(5193),i.e(8485),i.e(9423),i.e(5569),i.e(3243)]).then(i.bind(i,33243));return await n(a,"",void 0,void 0,s)}return{success:!1,message:`Unsupported webhook event for create-new-cancel-old: ${e.webhookEvent}`}}async getWebhookHandler(e){try{switch(e){case"subscription.authenticated":let{handleSubscriptionAuthenticated:t}=await Promise.all([i.e(5193),i.e(8485),i.e(9423),i.e(5569),i.e(4422)]).then(i.bind(i,84422));return t;case"subscription.activated":let{handleSubscriptionActivated:a}=await Promise.all([i.e(5193),i.e(8485),i.e(9423),i.e(5569),i.e(4422)]).then(i.bind(i,84422));return a;case"subscription.charged":let{handleSubscriptionCharged:s}=await Promise.all([i.e(5193),i.e(8485),i.e(9423),i.e(5569),i.e(4422)]).then(i.bind(i,84422));return s;case"subscription.pending":let{handleSubscriptionPending:n}=await Promise.all([i.e(5193),i.e(8485),i.e(9423),i.e(5569),i.e(4422)]).then(i.bind(i,84422));return n;case"subscription.halted":let{handleSubscriptionHalted:c}=await Promise.all([i.e(5193),i.e(8485),i.e(5432)]).then(i.bind(i,5432));return c;case"subscription.cancelled":let{handleSubscriptionCancelled:o}=await Promise.all([i.e(5193),i.e(8485),i.e(9423)]).then(i.bind(i,69423));return o;case"subscription.completed":let{handleSubscriptionCompleted:r}=await Promise.all([i.e(5193),i.e(8485),i.e(301)]).then(i.bind(i,301));return r;case"subscription.expired":let{handleSubscriptionExpired:l}=await Promise.all([i.e(5193),i.e(8485),i.e(7249)]).then(i.bind(i,47249));return l;case"subscription.updated":let{handleSubscriptionUpdated:p}=await Promise.all([i.e(5193),i.e(8485),i.e(5447)]).then(i.bind(i,75447));return p;default:return null}}catch(t){return console.error(`Error loading handler for ${e}:`,t),null}}async getCurrentState(){let{data:e}=await this.supabase.from("payment_subscriptions").select("subscription_status, plan_id, plan_cycle, razorpay_subscription_id, razorpay_customer_id").eq("business_profile_id",this.businessId).single(),{data:t}=await this.supabase.from("business_profiles").select("has_active_subscription, trial_end_date").eq("id",this.businessId).single();return{subscription_status:e?.subscription_status,plan_id:e?.plan_id,plan_cycle:e?.plan_cycle,has_active_subscription:t?.has_active_subscription,razorpay_subscription_id:e?.razorpay_subscription_id,razorpay_customer_id:e?.razorpay_customer_id,trial_end_date:t?.trial_end_date}}async testIdempotency(e){try{if(e.isIdempotencyTest)return await this.testComprehensiveIdempotency(e);return await this.testBasicIdempotency(e)}catch(e){return{success:!1,message:`Idempotency test failed: ${e instanceof Error?e.message:"Unknown error"}`}}}async testBasicIdempotency(e){let t=await this.getCurrentState(),i=`test_${e.id}_${Date.now()}_duplicate`,a=await this.getProcessedEventsCount(i),s=await this.sendWebhookWithEventId(e,i),n=await this.getCurrentState(),c=await this.getProcessedEventsCount(i),o=JSON.stringify(t)===JSON.stringify(n),r=s.success&&(s.message.includes("already processed")||s.message.includes("idempotent")||o),l=c<=a+1,p=r&&o&&l;return{success:p,message:p?"Duplicate webhook handled idempotently":"Idempotency failed - duplicate processing detected",details:{stateBeforeDuplicate:t,stateAfterDuplicate:n,stateUnchanged:o,duplicateResult:s,processedEventsBefore:a,processedEventsAfter:c,correctEventCount:l}}}async testComprehensiveIdempotency(e){try{let t=`test_${e.id}_${Date.now()}_comprehensive`,i=[];for(let a=0;a<3;a++){let a=await this.sendWebhookWithEventId(e,t);i.push(a),await new Promise(e=>setTimeout(e,50))}let a=await this.getProcessedEventsCount(t),s=i[0].success,n=i.slice(1).every(e=>e.success&&(e.message.includes("already processed")||e.message.includes("idempotent"))),c=s&&n&&1===a;return{success:c,message:c?"Comprehensive idempotency test passed":"Comprehensive idempotency test failed",details:{results:i,processedCount:a,firstSucceeded:s,subsequentIgnored:n}}}catch(e){return{success:!1,message:`Comprehensive idempotency test failed: ${e instanceof Error?e.message:"Unknown error"}`}}}async sendWebhookWithEventId(e,t){let{data:a}=await this.supabase.from("payment_subscriptions").select("razorpay_subscription_id").eq("business_profile_id",this.businessId).single(),s=a?.razorpay_subscription_id||`sub_test_${e.id}_${Date.now()}`,n=p(e.webhookEvent,s,this.businessId,{plan_id:e.initialState.plan_id,plan_cycle:e.initialState.plan_cycle,payment_method:e.paymentMethod||"card"}),{handleRazorpayWebhook:c}=await Promise.all([i.e(5193),i.e(8485),i.e(9423),i.e(5569),i.e(3243)]).then(i.bind(i,33243));return await c(n,"",void 0,void 0,t)}async getProcessedEventsCount(e){try{let{data:t,error:i}=await this.supabase.from("processed_webhook_events").select("event_id").eq("event_id",e);if(i)return console.error("Error fetching processed events:",i),0;return t?.length||0}catch(e){return console.error("Error in getProcessedEventsCount:",e),0}}validateStateTransition(e,t){let i=e.expectedFinalState,a=t.subscription_status===i.subscription_status&&t.plan_id===i.plan_id&&t.plan_cycle===i.plan_cycle&&t.has_active_subscription===i.has_active_subscription,s=!0;return"razorpay_subscription_id"in i&&(s=s&&t.razorpay_subscription_id===i.razorpay_subscription_id),"razorpay_customer_id"in i&&(s=s&&t.razorpay_customer_id===i.razorpay_customer_id),"trial_end_date"in i&&(s="PRESERVED"===i.trial_end_date?s&&null!==t.trial_end_date:s&&t.trial_end_date===i.trial_end_date),a&&s}}let u=[{id:"trial_to_authenticated",name:"Trial → Authenticated (Plan Selection)",description:"User selects a plan during trial period",initialState:{subscription_status:"trial",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.authenticated",expectedFinalState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"trial"},{id:"trial_to_active",name:"Trial → Active (Direct Payment)",description:"User pays immediately during trial (rare case)",initialState:{subscription_status:"trial",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"trial"},{id:"trial_expired",name:"Trial → Expired → Free",description:"Trial period expires without payment",initialState:{subscription_status:"trial",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.expired",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"trial"},{id:"authenticated_to_active",name:"Authenticated → Active (Payment Success)",description:"User completes payment after plan selection",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"transition",paymentMethod:"card"},{id:"authenticated_upi_to_active",name:"Authenticated UPI → Active (Payment Success)",description:"User completes UPI payment after plan selection",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"transition",paymentMethod:"upi"},{id:"authenticated_emandate_to_active",name:"Authenticated E-Mandate → Active (Payment Success)",description:"User completes E-Mandate payment after plan selection",initialState:{subscription_status:"authenticated",plan_id:"basic",plan_cycle:"yearly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"basic",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"transition",paymentMethod:"emandate"},{id:"authenticated_cancelled",name:"Authenticated → Cancelled → Trial",description:"User cancels before payment (Plan A cancellation)",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"trial",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1,razorpay_subscription_id:null,razorpay_customer_id:null,trial_end_date:"PRESERVED"},shouldSucceed:!0,category:"transition"},{id:"authenticated_charged",name:"Authenticated → Charged → Active",description:"Payment is charged for authenticated subscription",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.charged",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"transition"},{id:"authenticated_pending",name:"Authenticated → Pending (Invalid Transition)",description:"Payment review event should be rejected for authenticated subscription",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.pending",expectedFinalState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!1,category:"transition"},{id:"active_charged",name:"Active → Charged (Renewal)",description:"Recurring payment for active subscription",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.charged",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid"},{id:"active_cancelled",name:"Active → Cancelled → Free",description:"User cancels active subscription",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid"},{id:"active_halted",name:"Active → Halted (Payment Failed)",description:"Subscription paused due to payment failure",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.halted",expectedFinalState:{subscription_status:"halted",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid"},{id:"active_expired",name:"Active → Expired → Free",description:"Subscription expires and downgrades to free",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.expired",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid"},{id:"active_completed",name:"Active → Completed → Free",description:"Subscription completes all billing cycles",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.completed",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid"},{id:"active_updated",name:"Active → Updated (Plan/Cycle Change)",description:"Active subscription is updated with new plan details",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.updated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"active_upi_plan_change_new_activated",name:"Active UPI → New Subscription Activated (Create-New-Cancel-Old)",description:"UPI subscription plan change via new subscription activation",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"upi",isCreateNewCancelOld:!0},{id:"active_emandate_plan_change_new_activated",name:"Active E-Mandate → New Subscription Activated (Create-New-Cancel-Old)",description:"E-Mandate subscription plan change via new subscription activation",initialState:{subscription_status:"active",plan_id:"basic",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"emandate",isCreateNewCancelOld:!0},{id:"active_upi_old_cancelled_after_new_active",name:"Active UPI → Old Subscription Cancelled (After New Active)",description:"Old UPI subscription cancelled after new subscription becomes active",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"upi",isCreateNewCancelOld:!0},{id:"halted_reactivated",name:"Halted → Reactivated",description:"Halted subscription is reactivated after payment",initialState:{subscription_status:"halted",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"transition"},{id:"halted_cancelled",name:"Halted → Cancelled → Free",description:"User cancels halted subscription",initialState:{subscription_status:"halted",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"transition"},{id:"halted_expired",name:"Halted → Expired → Free",description:"Halted subscription expires",initialState:{subscription_status:"halted",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.expired",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"transition"},{id:"race_authenticated_vs_cancelled",name:"Race Condition: Authenticated vs Cancelled",description:"User cancels subscription while authentication webhook is processing",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"trial",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"transition",paymentMethod:"card"},{id:"race_active_vs_halted",name:"Race Condition: Active vs Halted",description:"Payment fails while renewal charge webhook is processing",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.halted",expectedFinalState:{subscription_status:"halted",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"card_plan_upgrade_monthly_to_yearly",name:"Card: Plan Upgrade Monthly → Yearly",description:"Card subscription upgraded from monthly to yearly cycle",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.updated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"card_plan_downgrade_premium_to_basic",name:"Card: Plan Downgrade Premium → Basic",description:"Card subscription downgraded from premium to basic plan",initialState:{subscription_status:"active",plan_id:"premium",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.updated",expectedFinalState:{subscription_status:"active",plan_id:"premium",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"upi_plan_change_with_cycle_change",name:"UPI: Plan + Cycle Change (Create-New-Cancel-Old)",description:"UPI subscription changes both plan and cycle requiring new subscription",initialState:{subscription_status:"active",plan_id:"basic",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"upi",isCreateNewCancelOld:!0},{id:"emandate_cycle_change_only",name:"E-Mandate: Cycle Change Only (Create-New-Cancel-Old)",description:"E-Mandate subscription changes only billing cycle",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"emandate",isCreateNewCancelOld:!0},{id:"trial_to_premium_yearly_direct",name:"Trial → Premium Yearly (Direct Activation)",description:"Trial user directly activates premium yearly subscription",initialState:{subscription_status:"trial",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"trial",paymentMethod:"card"},{id:"authenticated_to_pending_invalid",name:"Invalid: Authenticated → Pending (Should Reject)",description:"Authenticated users cannot go to pending state - invalid transition",initialState:{subscription_status:"authenticated",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},webhookEvent:"subscription.pending",expectedFinalState:{subscription_status:"authenticated",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},shouldSucceed:!1,category:"transition",paymentMethod:"upi"},{id:"pending_to_active_after_review",name:"Pending → Active (Review Approved)",description:"Pending payment approved and subscription activated",initialState:{subscription_status:"pending",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"transition",paymentMethod:"upi"},{id:"active_to_halted_with_original_plan_storage",name:"Active → Halted (Store Original Plan)",description:"Active subscription halted with original plan details stored",initialState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},webhookEvent:"subscription.halted",expectedFinalState:{subscription_status:"halted",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"halted_to_active_restore_original_plan",name:"Halted → Active (Restore Original Plan)",description:"Halted subscription reactivated with original premium yearly plan",initialState:{subscription_status:"halted",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"transition",paymentMethod:"card"},{id:"upi_multiple_subscriptions_coordination",name:"UPI: Multiple Subscriptions Coordination",description:"New UPI subscription activated while old one exists",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"upi",isCreateNewCancelOld:!0},{id:"emandate_old_subscription_cleanup",name:"E-Mandate: Old Subscription Cleanup",description:"Old E-Mandate subscription cancelled after new one is active",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"emandate",isCreateNewCancelOld:!0},{id:"expired_trial_to_free_plan",name:"Expired Trial → Free Plan",description:"Trial expires and user is downgraded to free plan",initialState:{subscription_status:"trial",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.expired",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"trial"},{id:"completed_subscription_to_free",name:"Completed Subscription → Free",description:"Subscription completes all billing cycles and downgrades to free",initialState:{subscription_status:"active",plan_id:"basic",plan_cycle:"yearly",has_active_subscription:!0},webhookEvent:"subscription.completed",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"invalid_free_to_authenticated",name:"Invalid: Free → Authenticated (Should Reject)",description:"Free plan users cannot directly authenticate without trial",initialState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.authenticated",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!1,category:"free"},{id:"invalid_completed_to_active",name:"Invalid: Completed → Active (Should Reject)",description:"Completed subscriptions cannot be reactivated directly",initialState:{subscription_status:"completed",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"completed",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!1,category:"transition"},{id:"delayed_webhook_processing",name:"Delayed Webhook Processing",description:"Webhook arrives late due to network issues, should be processed correctly",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"transition",paymentMethod:"card"},{id:"rapid_successive_webhooks",name:"Rapid Successive Webhooks",description:"Multiple webhooks arrive in quick succession, test idempotency",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.charged",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"payment_failure_during_trial_conversion",name:"Payment Failure During Trial Conversion",description:"Payment fails when user tries to convert from trial to paid",initialState:{subscription_status:"authenticated",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},webhookEvent:"subscription.halted",expectedFinalState:{subscription_status:"halted",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"transition",paymentMethod:"card"},{id:"payment_failure_on_renewal",name:"Payment Failure on Renewal",description:"Existing customer payment fails on renewal, should preserve original plan",initialState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},webhookEvent:"subscription.halted",expectedFinalState:{subscription_status:"halted",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"user_cancels_immediately_after_payment",name:"User Cancels Immediately After Payment",description:"User pays and immediately cancels, should handle gracefully",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"user_changes_plan_multiple_times",name:"User Changes Plan Multiple Times",description:"User rapidly changes plans, test system stability",initialState:{subscription_status:"active",plan_id:"basic",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.updated",expectedFinalState:{subscription_status:"active",plan_id:"basic",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"subscription_expires_during_usage",name:"Subscription Expires During Active Usage",description:"Subscription expires while user is actively using the platform",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.expired",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"subscription_completed_early",name:"Subscription Completed Early",description:"Subscription completes before expected end date",initialState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},webhookEvent:"subscription.completed",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"upi_payment_review_delay",name:"UPI Payment Under Review for Extended Period",description:"UPI payment stuck in pending state for extended time - invalid transition",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.pending",expectedFinalState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!1,category:"transition",paymentMethod:"upi"},{id:"emandate_registration_failure",name:"E-Mandate Registration Failure",description:"E-Mandate registration fails after user setup",initialState:{subscription_status:"authenticated",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"trial",plan_id:"premium",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"transition",paymentMethod:"emandate"},{id:"subscription_status_mismatch",name:"Subscription Status Mismatch Recovery",description:"System recovers from inconsistent subscription status",initialState:{subscription_status:"pending",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"transition",paymentMethod:"card"},{id:"plan_id_corruption_recovery",name:"Plan ID Corruption Recovery",description:"System handles corrupted plan ID gracefully",initialState:{subscription_status:"active",plan_id:"invalid_plan",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.updated",expectedFinalState:{subscription_status:"active",plan_id:"invalid_plan",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"enterprise_customer_downgrade",name:"Enterprise Customer Downgrades",description:"High-value enterprise customer downgrades to basic plan",initialState:{subscription_status:"active",plan_id:"enterprise",plan_cycle:"yearly",has_active_subscription:!0},webhookEvent:"subscription.updated",expectedFinalState:{subscription_status:"active",plan_id:"enterprise",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"enterprise_payment_failure",name:"Enterprise Payment Failure",description:"Enterprise customer payment fails, should preserve access temporarily",initialState:{subscription_status:"active",plan_id:"enterprise",plan_cycle:"yearly",has_active_subscription:!0},webhookEvent:"subscription.halted",expectedFinalState:{subscription_status:"halted",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"concurrent_plan_change_and_cancellation",name:"Concurrent Plan Change and Cancellation",description:"User changes plan while cancellation is processing",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"concurrent_payment_and_expiry",name:"Concurrent Payment and Expiry",description:"Payment processes while subscription is expiring",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.charged",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"rapid_trial_to_premium_conversion",name:"Rapid Trial to Premium Conversion",description:"User rapidly converts from trial to premium within seconds",initialState:{subscription_status:"trial",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!0},shouldSucceed:!0,category:"trial",paymentMethod:"card"},{id:"rapid_plan_cycling",name:"Rapid Plan Cycling",description:"User rapidly cycles through different plans",initialState:{subscription_status:"active",plan_id:"basic",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.updated",expectedFinalState:{subscription_status:"active",plan_id:"basic",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"paid",paymentMethod:"card"},{id:"trial_expiry_with_pending_payment",name:"Trial Expiry with Pending Payment",description:"Trial expires while payment is still pending",initialState:{subscription_status:"trial",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.expired",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"trial"},{id:"authenticated_user_direct_expiry",name:"Authenticated User Direct Expiry",description:"Authenticated user subscription expires without activation",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.expired",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"transition"},{id:"idempotency_duplicate_activated",name:"Duplicate Subscription Activated",description:"Duplicate activation webhook should be ignored",initialState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.activated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"idempotency",isIdempotencyTest:!0},{id:"idempotency_duplicate_charged",name:"Duplicate Subscription Charged",description:"Duplicate charge webhook should be ignored",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.charged",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"idempotency",isIdempotencyTest:!0},{id:"idempotency_duplicate_cancelled",name:"Duplicate Subscription Cancelled",description:"Duplicate cancellation webhook should be ignored",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.cancelled",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"idempotency",isIdempotencyTest:!0},{id:"idempotency_duplicate_halted",name:"Duplicate Subscription Halted",description:"Duplicate halt webhook should be ignored",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.halted",expectedFinalState:{subscription_status:"halted",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"idempotency",isIdempotencyTest:!0},{id:"idempotency_duplicate_expired",name:"Duplicate Subscription Expired",description:"Duplicate expiry webhook should be ignored",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.expired",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"idempotency",isIdempotencyTest:!0},{id:"idempotency_duplicate_completed",name:"Duplicate Subscription Completed",description:"Duplicate completion webhook should be ignored",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.completed",expectedFinalState:{subscription_status:"active",plan_id:"free",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"idempotency",isIdempotencyTest:!0},{id:"idempotency_duplicate_updated",name:"Duplicate Subscription Updated",description:"Duplicate update webhook should be ignored",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.updated",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"idempotency",isIdempotencyTest:!0,paymentMethod:"card"},{id:"idempotency_duplicate_authenticated",name:"Duplicate Subscription Authenticated",description:"Duplicate authentication webhook should be ignored",initialState:{subscription_status:"trial",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},webhookEvent:"subscription.authenticated",expectedFinalState:{subscription_status:"authenticated",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!1},shouldSucceed:!0,category:"idempotency",isIdempotencyTest:!0},{id:"idempotency_duplicate_pending",name:"Duplicate Subscription Pending",description:"Duplicate pending webhook should be ignored (invalid transition)",initialState:{subscription_status:"authenticated",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},webhookEvent:"subscription.pending",expectedFinalState:{subscription_status:"authenticated",plan_id:"premium",plan_cycle:"yearly",has_active_subscription:!1},shouldSucceed:!1,category:"idempotency",isIdempotencyTest:!0},{id:"network_webhook_delay",name:"Webhook Delivery Delay (>5 minutes)",description:"Late webhook delivery due to network issues",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.charged",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"network",isEdgeCaseTest:!0},{id:"network_db_connection_failure",name:"Database Connection Failure During Processing",description:"Database connection fails during webhook processing",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.charged",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"network",isEdgeCaseTest:!0},{id:"volume_basic_load",name:"Basic Load Test",description:"Basic webhook processing under normal load",initialState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},webhookEvent:"subscription.charged",expectedFinalState:{subscription_status:"active",plan_id:"growth",plan_cycle:"monthly",has_active_subscription:!0},shouldSucceed:!0,category:"volume",isEdgeCaseTest:!1}];function _(e){return u.filter(t=>t.category===e)}async function h(e){try{return o.NextResponse.json({success:!1,error:"Test endpoints only available in development"},{status:403})}catch(e){return console.error("[SCENARIO_TEST_API] Error running subscription scenario tests:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test/subscription-scenarios/route",pathname:"/api/test/subscription-scenarios",filename:"route",bundlePath:"app/api/test/subscription-scenarios/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\test\\subscription-scenarios\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:b,workUnitAsyncStorage:m,serverHooks:v}=y;function g(){return(0,c.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:m})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,i)=>{"use strict";i.r(t),i.d(t,{createClient:()=>s});var a=i(34386);async function s(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let s=null,n=null;try{let{headers:e,cookies:t}=await i.e(4999).then(i.bind(i,44999));s=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||s&&"true"===s.get("x-playwright-testing"))&&s?function(e){let t=e.get("x-test-auth-state"),i=e.get("x-test-user-type"),a="customer"===i||"business"===i,s=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,i,a,s){let n=()=>{var n,c,o,r,l;return n=e,c=t,o=i,r=a,l=s,"customer_profiles"===n?{data:o&&"customer"===c?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:o&&"business"===c?{id:"test-user-id",business_slug:r||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===c?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===c?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},c=e=>({select:t=>c(e),eq:(t,i)=>c(e),neq:(t,i)=>c(e),gt:(t,i)=>c(e),gte:(t,i)=>c(e),lt:(t,i)=>c(e),lte:(t,i)=>c(e),like:(t,i)=>c(e),ilike:(t,i)=>c(e),is:(t,i)=>c(e),in:(t,i)=>c(e),contains:(t,i)=>c(e),containedBy:(t,i)=>c(e),rangeGt:(t,i)=>c(e),rangeGte:(t,i)=>c(e),rangeLt:(t,i)=>c(e),rangeLte:(t,i)=>c(e),rangeAdjacent:(t,i)=>c(e),overlaps:(t,i)=>c(e),textSearch:(t,i)=>c(e),match:t=>c(e),not:(t,i,a)=>c(e),or:t=>c(e),filter:(t,i,a)=>c(e),order:(t,i)=>c(e),limit:(t,i)=>c(e),range:(t,i,a)=>c(e),abortSignal:t=>c(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>c(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let i={data:Array.isArray(e)?e:[e],error:null};return t?t(i):i}}),then:async t=>{let i={data:Array.isArray(e)?e:[e],error:null};return t?t(i):i}}),update:e=>c(e),upsert:e=>c(e),delete:()=>c(),rpc:(e,t)=>c()}})(e,i,a,s,n)}}(s):n?(0,a.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:i,options:a}of e)await n.set(t,i,a)}catch{}}}}):(0,a.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77336:e=>{"use strict";e.exports=JSON.parse('{"name":"dotenv","version":"16.5.0","description":"Loads environment variables from .env file","main":"lib/main.js","types":"lib/main.d.ts","exports":{".":{"types":"./lib/main.d.ts","require":"./lib/main.js","default":"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},"scripts":{"dts-check":"tsc --project tests/types/tsconfig.json","lint":"standard","pretest":"npm run lint && npm run dts-check","test":"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov","prerelease":"npm test","release":"standard-version"},"repository":{"type":"git","url":"git://github.com/motdotla/dotenv.git"},"homepage":"https://github.com/motdotla/dotenv#readme","funding":"https://dotenvx.com","keywords":["dotenv","env",".env","environment","variables","config","settings"],"readmeFilename":"README.md","license":"BSD-2-Clause","devDependencies":{"@types/node":"^18.11.3","decache":"^4.6.2","sinon":"^14.0.1","standard":"^17.0.0","standard-version":"^9.5.0","tap":"^19.2.0","typescript":"^4.8.4"},"engines":{"node":">=12"},"browser":{"fs":false}}')},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},97329:(e,t,i)=>{let a=i(29021),s=i(33873),n=i(21820),c=i(55511),o=i(77336).version,r=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function l(e){console.log(`[dotenv@${o}][DEBUG] ${e}`)}function p(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function d(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let i of e.path)a.existsSync(i)&&(t=i.endsWith(".vault")?i:`${i}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=s.resolve(process.cwd(),".env.vault");return a.existsSync(t)?t:null}function u(e){return"~"===e[0]?s.join(n.homedir(),e.slice(1)):e}let _={configDotenv:function(e){let t,i=s.resolve(process.cwd(),".env"),n="utf8",c=!!(e&&e.debug);e&&e.encoding?n=e.encoding:c&&l("No encoding is specified. UTF-8 is used by default");let o=[i];if(e&&e.path)if(Array.isArray(e.path))for(let t of(o=[],e.path))o.push(u(t));else o=[u(e.path)];let r={};for(let i of o)try{let t=_.parse(a.readFileSync(i,{encoding:n}));_.populate(r,t,e)}catch(e){c&&l(`Failed to load ${i} ${e.message}`),t=e}let p=process.env;return(e&&null!=e.processEnv&&(p=e.processEnv),_.populate(p,r,e),t)?{parsed:r,error:t}:{parsed:r}},_configVault:function(e){e&&e.debug&&l("Loading env from encrypted .env.vault");let t=_._parseVault(e),i=process.env;return e&&null!=e.processEnv&&(i=e.processEnv),_.populate(i,t,e),{parsed:t}},_parseVault:function(e){let t,i=d(e),a=_.configDotenv({path:i});if(!a.parsed){let e=Error(`MISSING_DATA: Cannot parse ${i} for an unknown reason`);throw e.code="MISSING_DATA",e}let s=p(e).split(","),n=s.length;for(let e=0;e<n;e++)try{let i=s[e].trim(),n=function(e,t){let i;try{i=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let a=i.password;if(!a){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let s=i.searchParams.get("environment");if(!s){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let n=`DOTENV_VAULT_${s.toUpperCase()}`,c=e.parsed[n];if(!c){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${n} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:c,key:a}}(a,i);t=_.decrypt(n.ciphertext,n.key);break}catch(t){if(e+1>=n)throw t}return _.parse(t)},config:function(e){if(0===p(e).length)return _.configDotenv(e);let t=d(e);if(!t){var i;return i=`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`,console.log(`[dotenv@${o}][WARN] ${i}`),_.configDotenv(e)}return _._configVault(e)},decrypt:function(e,t){let i=Buffer.from(t.slice(-64),"hex"),a=Buffer.from(e,"base64"),s=a.subarray(0,12),n=a.subarray(-16);a=a.subarray(12,-16);try{let e=c.createDecipheriv("aes-256-gcm",i,s);return e.setAuthTag(n),`${e.update(a)}${e.final()}`}catch(a){let e=a instanceof RangeError,t="Invalid key length"===a.message,i="Unsupported state or unable to authenticate data"===a.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(i){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw a}},parse:function(e){let t,i={},a=e.toString();for(a=a.replace(/\r\n?/mg,"\n");null!=(t=r.exec(a));){let e=t[1],a=t[2]||"",s=(a=a.trim())[0];a=a.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===s&&(a=(a=a.replace(/\\n/g,"\n")).replace(/\\r/g,"\r")),i[e]=a}return i},populate:function(e,t,i={}){let a=!!(i&&i.debug),s=!!(i&&i.override);if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let i of Object.keys(t))Object.prototype.hasOwnProperty.call(e,i)?(!0===s&&(e[i]=t[i]),a&&(!0===s?l(`"${i}" is already defined and WAS overwritten`):l(`"${i}" is already defined and was NOT overwritten`))):e[i]=t[i]}};e.exports.configDotenv=_.configDotenv,e.exports._configVault=_._configVault,e.exports._parseVault=_._parseVault,e.exports.config=_.config,e.exports.decrypt=_.decrypt,e.exports.parse=_.parse,e.exports.populate=_.populate,e.exports=_}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[4447,9398,4386,580],()=>i(2792));module.exports=a})();