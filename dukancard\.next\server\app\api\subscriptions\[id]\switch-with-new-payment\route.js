"use strict";(()=>{var e={};e.id=2886,e.ids=[2886],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},12254:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{POST:()=>u});var a=t(96559),c=t(48088),i=t(37719),o=t(32190),n=t(32032),d=t(45638);async function u(e,{params:r}){try{let{id:t}=await r;console.log(`[API_DEBUG] switch-with-new-payment called for subscription: ${t}`);let{planId:s,planCycle:a,paymentMethod:c}=await e.json();if(console.log("[API_DEBUG] Request body:",{planId:s,planCycle:a,paymentMethod:c}),!s||!a||!c)return o.NextResponse.json({success:!1,error:"Missing required parameters"},{status:400});let i=await (0,n.createClient)(),{data:{user:u},error:p}=await i.auth.getUser();if(p||!u)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let b=await (0,d._)(t,s,a,c);if(!b.success)return o.NextResponse.json({success:!1,error:b.error},{status:400});return o.NextResponse.json({success:!0,data:b.data},{status:200})}catch(e){return console.error("[RAZORPAY_ERROR] Error switching subscription with new payment:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:c.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/switch-with-new-payment/route",pathname:"/api/subscriptions/[id]/switch-with-new-payment",filename:"route",bundlePath:"app/api/subscriptions/[id]/switch-with-new-payment/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch-with-new-payment\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:f,serverHooks:x}=p;function w(){return(0,i.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:f})}},27842:(e,r,t)=>{t.r(r),t.d(r,{"00d4d8ffb0bd97f113de12ba4267247cf5a3b0b04c":()=>a.HW,"00efacc006752966ec1e7203fd5cdd059c8b2b6e30":()=>a.Ve,"401c3b86049e891affa0506ade0f31baeb2c3d455d":()=>a.GN,"404c603e0f39faf7ce2bc96725f060ce4e5faa5728":()=>a.bh,"4055b2843ad80548c3c3a2f5c81868718477c83ed4":()=>a.Oe,"40fb7d10f8dedc0c32f3187581b16bbca4c23379d6":()=>a.WX,"40fea71ecc67261c02da2d172b819e0eef02d3b41a":()=>a.$y,"6034db8eca1b50b78925a7248b950085ef2be979fc":()=>a.EP,"609589fd4be3c72f3931a1f9c536368933ba8b83da":()=>a.h,"609ab29520f078ddd91513228ac9d4ec796f522cb0":()=>a.m3,"60f426948b8391e4c3e4b292fc9538ce7045fbe366":()=>a.lC,"7079684d5710a60a6f3b6c187d7182292bb2ca9359":()=>s.switchAuthenticatedSubscription,"70d5ac2f53a3a59b11b18e95dcb79d8273fb1231ca":()=>s.g,"7821496fbfb4e8bbdb4fd0cc9aeebfc2e46e676840":()=>s._});var s=t(45638),a=t(27659)},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,9398,4386,6724,580,2186,9209,7365,5638],()=>t(12254));module.exports=s})();