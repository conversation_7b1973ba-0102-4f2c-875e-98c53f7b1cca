"use strict";exports.id=1606,exports.ids=[1606],exports.modules={51358:(e,t,n)=>{n.d(t,{JR:()=>C,_G:()=>d,be:()=>o,gB:()=>g,gl:()=>x,kL:()=>s});var r=n(43210),l=n.n(r),i=n(57601),a=n(62478);function o(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function u(e){return null!==e&&e>=0}let s=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=o(t,r,n),a=t[l],u=i[l];return u&&a?{x:u.left-a.left,y:u.top-a.top,scaleX:u.width/a.width,scaleY:u.height/a.height}:null},c={scaleX:1,scaleY:1},d=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:a}=e,o=null!=(t=i[n])?t:r;if(!o)return null;if(l===n){let e=i[a];return e?{x:0,y:n<a?e.top+e.height-(o.top+o.height):e.top-o.top,...c}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=a?{x:0,y:-o.height-u,...c}:l<n&&l>=a?{x:0,y:o.height+u,...c}:{x:0,y:0,...c}},f="Sortable",h=l().createContext({activeIndex:-1,containerId:f,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:s,disabled:{draggable:!1,droppable:!1}});function g(e){let{children:t,id:n,items:o,strategy:u=s,disabled:c=!1}=e,{active:d,dragOverlay:g,droppableRects:p,over:v,measureDroppableContainers:b}=(0,i.fF)(),m=(0,a.YG)(f,n),y=null!==g.rect,w=(0,r.useMemo)(()=>o.map(e=>"object"==typeof e&&"id"in e?e.id:e),[o]),x=null!=d,E=d?w.indexOf(d.id):-1,D=v?w.indexOf(v.id):-1,C=(0,r.useRef)(w),S=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(w,C.current),M=-1!==D&&-1===E||S,k="boolean"==typeof c?{draggable:c,droppable:c}:c;(0,a.Es)(()=>{S&&x&&b(w)},[S,w,x,b]),(0,r.useEffect)(()=>{C.current=w},[w]);let R=(0,r.useMemo)(()=>({activeIndex:E,containerId:m,disabled:k,disableTransforms:M,items:w,overIndex:D,useDragOverlay:y,sortedRects:w.reduce((e,t,n)=>{let r=p.get(t);return r&&(e[n]=r),e},Array(w.length)),strategy:u}),[E,m,k.draggable,k.droppable,M,w,D,p,y,u]);return l().createElement(h.Provider,{value:R},t)}let p=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return o(n,r,l).indexOf(t)},v=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},b={duration:200,easing:"ease"},m="transform",y=a.Ks.Transition.toString({property:m,duration:0,easing:"linear"}),w={roleDescription:"sortable"};function x(e){var t,n,l,o;let{animateLayoutChanges:s=v,attributes:c,disabled:d,data:f,getNewIndex:g=p,id:x,strategy:E,resizeObserverConfig:D,transition:C=b}=e,{items:S,containerId:M,activeIndex:k,disabled:R,disableTransforms:T,sortedRects:L,overIndex:O,useDragOverlay:N,strategy:z}=(0,r.useContext)(h),A=(t=d,n=R,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(l=null==t?void 0:t.draggable)?l:n.draggable,droppable:null!=(o=null==t?void 0:t.droppable)?o:n.droppable}),I=S.indexOf(x),P=(0,r.useMemo)(()=>({sortable:{containerId:M,index:I,items:S},...f}),[M,f,I,S]),Y=(0,r.useMemo)(()=>S.slice(S.indexOf(x)),[S,x]),{rect:j,node:W,isOver:B,setNodeRef:K}=(0,i.zM)({id:x,data:P,disabled:A.droppable,resizeObserverConfig:{updateMeasurementsFor:Y,...D}}),{active:F,activatorEvent:X,activeNodeRect:U,attributes:G,setNodeRef:_,listeners:V,isDragging:H,over:q,setActivatorNodeRef:J,transform:Q}=(0,i.PM)({id:x,data:P,attributes:{...w,...c},disabled:A.draggable}),Z=(0,a.jn)(K,_),$=!!F,ee=$&&!T&&u(k)&&u(O),et=!N&&H,en=et&&ee?Q:null,er=ee?null!=en?en:(null!=E?E:z)({rects:L,activeNodeRect:U,activeIndex:k,overIndex:O,index:I}):null,el=u(k)&&u(O)?g({id:x,items:S,activeIndex:k,overIndex:O}):I,ei=null==F?void 0:F.id,ea=(0,r.useRef)({activeId:ei,items:S,newIndex:el,containerId:M}),eo=S!==ea.current.items,eu=s({active:F,containerId:M,isDragging:H,isSorting:$,id:x,index:I,items:S,newIndex:ea.current.newIndex,previousItems:ea.current.items,previousContainerId:ea.current.containerId,transition:C,wasDragging:null!=ea.current.activeId}),es=function(e){let{disabled:t,index:n,node:l,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,a.Es)(()=>{if(!t&&n!==c.current&&l.current){let e=o.current;if(e){let t=(0,i.Sj)(l.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,l,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:I,node:W,rect:j});return(0,r.useEffect)(()=>{$&&ea.current.newIndex!==el&&(ea.current.newIndex=el),M!==ea.current.containerId&&(ea.current.containerId=M),S!==ea.current.items&&(ea.current.items=S)},[$,el,M,S]),(0,r.useEffect)(()=>{if(ei===ea.current.activeId)return;if(null!=ei&&null==ea.current.activeId){ea.current.activeId=ei;return}let e=setTimeout(()=>{ea.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:F,activeIndex:k,attributes:G,data:P,rect:j,index:I,newIndex:el,items:S,isOver:B,isSorting:$,isDragging:H,listeners:V,node:W,overIndex:O,over:q,setNodeRef:Z,setActivatorNodeRef:J,setDroppableNodeRef:K,setDraggableNodeRef:_,transform:null!=es?es:er,transition:es||eo&&ea.current.newIndex===I?y:(!et||(0,a.kx)(X))&&C&&($||eu)?a.Ks.Transition.toString({...C,property:m}):void 0}}function E(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let D=[i.vL.Down,i.vL.Right,i.vL.Up,i.vL.Left],C=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:l,droppableContainers:o,over:u,scrollableAncestors:s}}=t;if(D.includes(e.code)){if(e.preventDefault(),!n||!r)return;let t=[];o.getEnabled().forEach(n=>{if(!n||null!=n&&n.disabled)return;let a=l.get(n.id);if(a)switch(e.code){case i.vL.Down:r.top<a.top&&t.push(n);break;case i.vL.Up:r.top>a.top&&t.push(n);break;case i.vL.Left:r.left>a.left&&t.push(n);break;case i.vL.Right:r.left<a.left&&t.push(n)}});let c=(0,i.y$)({active:n,collisionRect:r,droppableRects:l,droppableContainers:t,pointerCoordinates:null}),d=(0,i.Vy)(c,"id");if(d===(null==u?void 0:u.id)&&c.length>1&&(d=c[1].id),null!=d){let e=o.get(n.id),t=o.get(d),u=t?l.get(t.id):null,c=null==t?void 0:t.node.current;if(c&&u&&e&&t){let n=(0,i.sl)(c).some((e,t)=>s[t]!==e),l=S(e,t),o=function(e,t){return!!E(e)&&!!E(t)&&!!S(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),d=n||!l?{x:0,y:0}:{x:o?r.width-u.width:0,y:o?r.height-u.height:0},f={x:u.left,y:u.top};return d.x&&d.y?f:(0,a.Re)(f,d)}}}};function S(e,t){return!!E(e)&&!!E(t)&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}},57601:(e,t,n)=>{let r;n.d(t,{Mp:()=>eX,Hd:()=>e6,vL:()=>o,uN:()=>es,AN:()=>eg,fp:()=>I,y$:()=>P,Sj:()=>F,Vy:()=>z,sl:()=>U,fF:()=>eV,PM:()=>e_,zM:()=>eq,MS:()=>M,FR:()=>k});var l,i,a,o,u,s,c,d,f,h,g=n(43210),p=n.n(g),v=n(51215),b=n(62478);let m={display:"none"};function y(e){let{id:t,value:n}=e;return p().createElement("div",{id:t,style:m},n)}function w(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return p().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let x=(0,g.createContext)(null),E={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},D={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function C(e){let{announcements:t=D,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=E}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,g.useState)("");return{announce:(0,g.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,b.YG)("DndLiveRegion"),[u,s]=(0,g.useState)(!1);(0,g.useEffect)(()=>{s(!0)},[]);var c=(0,g.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let d=(0,g.useContext)(x);if((0,g.useEffect)(()=>{if(!d)throw Error("useDndMonitor must be used within a children of <DndContext>");return d(c)},[c,d]),!u)return null;let f=p().createElement(p().Fragment,null,p().createElement(y,{id:r,value:l.draggable}),p().createElement(w,{id:o,announcement:a}));return n?(0,v.createPortal)(f,n):f}function S(){}function M(e,t){return(0,g.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function k(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,g.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let R=Object.freeze({x:0,y:0});function T(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function L(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function O(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function N(e){let{left:t,top:n,height:r,width:l}=e;return[{x:t,y:n},{x:t+l,y:n},{x:t,y:n+r},{x:t+l,y:n+r}]}function z(e,t){if(!e||0===e.length)return null;let[n]=e;return t?n[t]:n}function A(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let I=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=A(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=T(A(r),l);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(L)},P=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=N(t),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=N(r),a=Number((l.reduce((e,t,r)=>e+T(n[r],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:a}})}}return i.sort(L)},Y=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(O)};function j(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:R}let W=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1);function B(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let K={ignoreTransform:!1};function F(e,t){void 0===t&&(t=K);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,b.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=B(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function X(e){return F(e,{ignoreTransform:!0})}function U(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,b.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,b.sb)(l)||(0,b.xZ)(l)||n.includes(l))return n;let a=(0,b.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,b.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,b.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function G(e){let[t]=U(e,1);return null!=t?t:null}function _(e){return b.Sw&&e?(0,b.l6)(e)?e:(0,b.Ll)(e)?(0,b.wz)(e)||e===(0,b.TW)(e).scrollingElement?window:(0,b.sb)(e)?e:null:null:null}function V(e){return(0,b.l6)(e)?e.scrollX:e.scrollLeft}function H(e){return(0,b.l6)(e)?e.scrollY:e.scrollTop}function q(e){return{x:V(e),y:H(e)}}function J(e){return!!b.Sw&&!!e&&e===document.scrollingElement}function Q(e){let t={x:0,y:0},n=J(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let Z={x:.2,y:.2};function $(e){return e.reduce((e,t)=>(0,b.WQ)(e,q(t)),R)}function ee(e,t){if(void 0===t&&(t=F),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);G(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let et=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+V(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+H(t),0)}]];class en{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=U(t),r=$(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,et))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class er{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function el(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function ei(e){e.preventDefault()}function ea(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let eo={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},eu=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class es{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new er((0,b.TW)(t)),this.windowListeners=new er((0,b.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&ee(n),t(R)}handleKeyDown(e){if((0,b.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=eo,coordinateGetter:i=eu,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u))return void this.handleEnd(e);if(l.cancel.includes(u))return void this.handleCancel(e);let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:R;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,b.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:f,minScroll:h}=Q(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(l===o.Right?g.right-g.width/2:g.right,Math.max(l===o.Right?g.left:g.left+g.width/2,d.x)),y:Math.min(l===o.Down?g.bottom-g.height/2:g.bottom,Math.max(l===o.Down?g.top:g.top+g.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,b=l===o.Down&&!c||l===o.Up&&!i;if(v&&p.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=f.x||l===o.Left&&e>=h.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-f.x:n.scrollLeft-h.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(b&&p.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=f.y||l===o.Up&&e>=h.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-f.y:n.scrollTop-h.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,b.WQ)((0,b.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function ec(e){return!!(e&&"distance"in e)}function ed(e){return!!(e&&"delay"in e)}es.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=eo,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class ef{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,b.zk)(e);return e instanceof t?e:(0,b.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,b.TW)(i),this.documentListeners=new er(this.document),this.listeners=new er(n),this.windowListeners=new er((0,b.zk)(i)),this.initialCoordinates=null!=(r=(0,b.e_)(l))?r:R,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,ei),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,ei),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(ed(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(ec(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,ea,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,b.e_)(e))?t:R,u=(0,b.Re)(r,o);if(!n&&a){if(ec(a)){if(null!=a.tolerance&&el(u,a.tolerance))return this.handleCancel();if(el(u,a.distance))return this.handleStart()}return ed(a)&&el(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let eh={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class eg extends ef{constructor(e){let{event:t}=e;super(e,eh,(0,b.TW)(t.target))}}eg.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let ep={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(u||(u={}));class ev extends ef{constructor(e){super(e,ep,(0,b.TW)(e.event.target))}}ev.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==u.RightClick&&(null==r||r({event:n}),!0)}}];let eb={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class em extends ef{constructor(e){super(e,eb)}static setup(){return window.addEventListener(eb.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(eb.move.name,e)};function e(){}}}em.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(s||(s={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let ey={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(f||(f={})).Optimized="optimized";let ew=new Map;function ex(e,t){return(0,b.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function eE(e){let{callback:t,disabled:n}=e,r=(0,b._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function eD(e){return new en(F(e),e)}function eC(e,t,n){void 0===t&&(t=eD);let[r,l]=(0,g.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,b._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=eE({callback:i});return(0,b.Es)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let eS=[];function eM(e,t){void 0===t&&(t=[]);let n=(0,g.useRef)(null);return(0,g.useEffect)(()=>{n.current=null},t),(0,g.useEffect)(()=>{let t=e!==R;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,b.Re)(e,n.current):R}function ek(e){return(0,g.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eR=[];function eT(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,b.sb)(t)?t:e}let eL=[{sensor:eg,options:{}},{sensor:es,options:{}}],eO={current:{}},eN={draggable:{measure:X},droppable:{measure:X,strategy:d.WhileDragging,frequency:f.Optimized},dragOverlay:{measure:F}};class ez extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eA={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new ez,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:S},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eN,measureDroppableContainers:S,windowRect:null,measuringScheduled:!1},eI={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:S,draggableNodes:new Map,over:null,measureDroppableContainers:S},eP=(0,g.createContext)(eI),eY=(0,g.createContext)(eA);function ej(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new ez}}}function eW(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new ez(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new ez(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new ez(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eB(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,g.useContext)(eP),i=(0,b.ZC)(r),a=(0,b.ZC)(null==n?void 0:n.id);return(0,g.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,b.kx)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,b.ag)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}function eK(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}let eF=(0,g.createContext)({...R,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(h||(h={}));let eX=(0,g.memo)(function(e){var t,n,r,a,o,u;let{id:f,accessibility:m,autoScroll:y=!0,children:w,sensors:E=eL,collisionDetection:D=Y,measuring:S,modifiers:M,...k}=e,[T,L]=(0,g.useReducer)(eW,void 0,ej),[O,N]=function(){let[e]=(0,g.useState)(()=>new Set),t=(0,g.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,g.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[A,I]=(0,g.useState)(h.Uninitialized),P=A===h.Initialized,{draggable:{active:B,nodes:K,translate:X},droppable:{containers:V}}=T,H=null!=B?K.get(B):null,ee=(0,g.useRef)({initial:null,translated:null}),et=(0,g.useMemo)(()=>{var e;return null!=B?{id:B,data:null!=(e=null==H?void 0:H.data)?e:eO,rect:ee}:null},[B,H]),er=(0,g.useRef)(null),[el,ei]=(0,g.useState)(null),[ea,eo]=(0,g.useState)(null),eu=(0,b.YN)(k,Object.values(k)),es=(0,b.YG)("DndDescribedBy",f),ec=(0,g.useMemo)(()=>V.getEnabled(),[V]),ed=(0,g.useMemo)(()=>({draggable:{...eN.draggable,...null==S?void 0:S.draggable},droppable:{...eN.droppable,...null==S?void 0:S.droppable},dragOverlay:{...eN.dragOverlay,...null==S?void 0:S.dragOverlay}}),[null==S?void 0:S.draggable,null==S?void 0:S.droppable,null==S?void 0:S.dragOverlay]),{droppableRects:ef,measureDroppableContainers:eh,measuringScheduled:eg}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,g.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,g.useRef)(e),f=function(){switch(s){case d.Always:return!1;case d.BeforeDragging:return n;default:return!n}}(),h=(0,b.YN)(f),p=(0,g.useCallback)(function(e){void 0===e&&(e=[]),h.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[h]),v=(0,g.useRef)(null),m=(0,b.KG)(t=>{if(f&&!n)return ew;if(!t||t===ew||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new en(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,f,u]);return(0,g.useEffect)(()=>{c.current=e},[e]),(0,g.useEffect)(()=>{f||p()},[n,f]),(0,g.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,g.useEffect)(()=>{f||"number"!=typeof o||null!==v.current||(v.current=setTimeout(()=>{p(),v.current=null},o))},[o,f,p,...r]),{droppableRects:m,measureDroppableContainers:p,measuringScheduled:null!=i}}(ec,{dragging:P,dependencies:[X.x,X.y],config:ed.droppable}),ep=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,b.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(K,B),ev=(0,g.useMemo)(()=>ea?(0,b.e_)(ea):null,[ea]),eb=function(){let e=(null==el?void 0:el.autoScrollEnabled)===!1,t="object"==typeof y?!1===y.enabled:!1===y,n=P&&!e&&!t;return"object"==typeof y?{...y,enabled:n}:{enabled:n}}(),em=ex(ep,ed.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,g.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,b.Es)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=j(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=G(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=B?K.get(B):null,config:eb.layoutShiftCompensation,initialRect:em,measure:ed.draggable.measure});let eD=eC(ep,ed.draggable.measure,em),ez=eC(ep?ep.parentElement:null),eA=(0,g.useRef)({activatorEvent:null,active:null,activeNode:ep,collisionRect:null,collisions:null,droppableRects:ef,draggableNodes:K,draggingNode:null,draggingNodeRect:null,droppableContainers:V,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eI=V.getNodeFor(null==(t=eA.current.over)?void 0:t.id),eX=function(e){let{measure:t}=e,[n,r]=(0,g.useState)(null),l=eE({callback:(0,g.useCallback)(e=>{for(let{target:n}of e)if((0,b.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,g.useCallback)(e=>{let n=eT(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,b.lk)(i);return(0,g.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:ed.dragOverlay.measure}),eU=null!=(n=eX.nodeRef.current)?n:ep,eG=P?null!=(r=eX.rect)?r:eD:null,e_=!!(eX.nodeRef.current&&eX.rect),eV=function(e){let t=ex(e);return j(e,t)}(e_?null:eD),eH=ek(eU?(0,b.zk)(eU):null),eq=function(e){let t=(0,g.useRef)(e),n=(0,b.KG)(n=>e?n&&n!==eS&&e&&t.current&&e.parentNode===t.current.parentNode?n:U(e):eS,[e]);return(0,g.useEffect)(()=>{t.current=e},[e]),n}(P?null!=eI?eI:ep:null),eJ=function(e,t){void 0===t&&(t=F);let[n]=e,r=ek(n?(0,b.zk)(n):null),[l,i]=(0,g.useState)(eR);function a(){i(()=>e.length?e.map(e=>J(e)?r:new en(t(e),e)):eR)}let o=eE({callback:a});return(0,b.Es)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(eq),eQ=eK(M,{transform:{x:X.x-eV.x,y:X.y-eV.y,scaleX:1,scaleY:1},activatorEvent:ea,active:et,activeNodeRect:eD,containerNodeRect:ez,draggingNodeRect:eG,over:eA.current.over,overlayNodeRect:eX.rect,scrollableAncestors:eq,scrollableAncestorRects:eJ,windowRect:eH}),eZ=ev?(0,b.WQ)(ev,X):null,e$=function(e){let[t,n]=(0,g.useState)(null),r=(0,g.useRef)(e),l=(0,g.useCallback)(e=>{let t=_(e.target);t&&n(e=>e?(e.set(t,q(t)),new Map(e)):null)},[]);return(0,g.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=_(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,q(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=_(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,g.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,b.WQ)(e,t),R):$(e):R,[e,t])}(eq),e0=eM(e$),e1=eM(e$,[eD]),e2=(0,b.WQ)(eQ,e0),e5=eG?W(eG,eQ):null,e6=et&&e5?D({active:et,collisionRect:e5,droppableRects:ef,droppableContainers:ec,pointerCoordinates:eZ}):null,e4=z(e6,"id"),[e8,e3]=(0,g.useState)(null),e9=(o=e_?eQ:(0,b.WQ)(eQ,e1),u=null!=(a=null==e8?void 0:e8.rect)?a:null,{...o,scaleX:u&&eD?u.width/eD.width:1,scaleY:u&&eD?u.height/eD.height:1}),e7=(0,g.useRef)(null),te=(0,g.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==er.current)return;let i=K.get(er.current);if(!i)return;let a=e.nativeEvent,o=new n({active:er.current,activeNode:i,event:a,options:r,context:eA,onAbort(e){if(!K.get(e))return;let{onDragAbort:t}=eu.current,n={id:e};null==t||t(n),O({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!K.get(e))return;let{onDragPending:l}=eu.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),O({type:"onDragPending",event:i})},onStart(e){let t=er.current;if(null==t)return;let n=K.get(t);if(!n)return;let{onDragStart:r}=eu.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:ee}};(0,v.unstable_batchedUpdates)(()=>{null==r||r(i),I(h.Initializing),L({type:l.DragStart,initialCoordinates:e,active:t}),O({type:"onDragStart",event:i}),ei(e7.current),eo(a)})},onMove(e){L({type:l.DragMove,coordinates:e})},onEnd:u(l.DragEnd),onCancel:u(l.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=eA.current,o=null;if(t&&i){let{cancelDrop:u}=eu.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof u&&await Promise.resolve(u(o))&&(e=l.DragCancel)}er.current=null,(0,v.unstable_batchedUpdates)(()=>{L({type:e}),I(h.Uninitialized),e3(null),ei(null),eo(null),e7.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=eu.current[t];null==e||e(o),O({type:t,event:o})}})}}e7.current=o},[K]),tt=(0,g.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=K.get(r);null!==er.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},er.current=r,te(n,t))},[K,te]),tn=(0,g.useMemo)(()=>E.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:tt(e.handler,t)}))]},[]),[E,tt]);(0,g.useEffect)(()=>{if(!b.Sw)return;let e=E.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},E.map(e=>{let{sensor:t}=e;return t})),(0,b.Es)(()=>{eD&&A===h.Initializing&&I(h.Initialized)},[eD,A]),(0,g.useEffect)(()=>{let{onDragMove:e}=eu.current,{active:t,activatorEvent:n,collisions:r,over:l}=eA.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e2.x,y:e2.y},over:l};(0,v.unstable_batchedUpdates)(()=>{null==e||e(i),O({type:"onDragMove",event:i})})},[e2.x,e2.y]),(0,g.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eA.current;if(!e||null==er.current||!t||!l)return;let{onDragOver:i}=eu.current,a=r.get(e4),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,v.unstable_batchedUpdates)(()=>{e3(o),null==i||i(u),O({type:"onDragOver",event:u})})},[e4]),(0,b.Es)(()=>{eA.current={activatorEvent:ea,active:et,activeNode:ep,collisionRect:e5,collisions:e6,droppableRects:ef,draggableNodes:K,draggingNode:eU,draggingNodeRect:eG,droppableContainers:V,over:e8,scrollableAncestors:eq,scrollAdjustedTranslate:e2},ee.current={initial:eG,translated:e5}},[et,ep,e6,e5,K,eU,eG,ef,V,e8,eq,e2]),function(e){let{acceleration:t,activator:n=s.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:u=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:f,scrollableAncestorRects:h,delta:p,threshold:v}=e,m=function(e){let{delta:t,disabled:n}=e,r=(0,b.ZC)(t);return(0,b.KG)(e=>{if(n||!r||!e)return ey;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:p,disabled:!a}),[y,w]=(0,b.$$)(),x=(0,g.useRef)({x:0,y:0}),E=(0,g.useRef)({x:0,y:0}),D=(0,g.useMemo)(()=>{switch(n){case s.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case s.DraggableRect:return l}},[n,l,d]),C=(0,g.useRef)(null),S=(0,g.useCallback)(()=>{let e=C.current;if(!e)return;let t=x.current.x*E.current.x,n=x.current.y*E.current.y;e.scrollBy(t,n)},[]),M=(0,g.useMemo)(()=>u===c.TreeOrder?[...f].reverse():f,[u,f]);(0,g.useEffect)(()=>{if(!a||!f.length||!D)return void w();for(let e of M){if((null==r?void 0:r(e))===!1)continue;let n=h[f.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===l&&(l=Z);let{isTop:c,isBottom:d,isLeft:f,isRight:h}=Q(e),g={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!c&&a<=t.top+v.height?(g.y=i.Backward,p.y=r*Math.abs((t.top+v.height-a)/v.height)):!d&&s>=t.bottom-v.height&&(g.y=i.Forward,p.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!h&&u>=t.right-v.width?(g.x=i.Forward,p.x=r*Math.abs((t.right-v.width-u)/v.width)):!f&&o<=t.left+v.width&&(g.x=i.Backward,p.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:g,speed:p}}(e,n,D,t,v);for(let e of["x","y"])m[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){w(),C.current=e,y(S,o),x.current=a,E.current=l;return}}x.current={x:0,y:0},E.current={x:0,y:0},w()},[t,S,r,w,a,o,JSON.stringify(D),JSON.stringify(m),y,f,M,h,JSON.stringify(v)])}({...eb,delta:X,draggingRect:e5,pointerCoordinates:eZ,scrollableAncestors:eq,scrollableAncestorRects:eJ});let tr=(0,g.useMemo)(()=>({active:et,activeNode:ep,activeNodeRect:eD,activatorEvent:ea,collisions:e6,containerNodeRect:ez,dragOverlay:eX,draggableNodes:K,droppableContainers:V,droppableRects:ef,over:e8,measureDroppableContainers:eh,scrollableAncestors:eq,scrollableAncestorRects:eJ,measuringConfiguration:ed,measuringScheduled:eg,windowRect:eH}),[et,ep,eD,ea,e6,ez,eX,K,V,ef,e8,eh,eq,eJ,ed,eg,eH]),tl=(0,g.useMemo)(()=>({activatorEvent:ea,activators:tn,active:et,activeNodeRect:eD,ariaDescribedById:{draggable:es},dispatch:L,draggableNodes:K,over:e8,measureDroppableContainers:eh}),[ea,tn,et,eD,L,es,K,e8,eh]);return p().createElement(x.Provider,{value:N},p().createElement(eP.Provider,{value:tl},p().createElement(eY.Provider,{value:tr},p().createElement(eF.Provider,{value:e9},w)),p().createElement(eB,{disabled:(null==m?void 0:m.restoreFocus)===!1})),p().createElement(C,{...m,hiddenTextDescribedById:es}))}),eU=(0,g.createContext)(null),eG="button";function e_(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,b.YG)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:f}=(0,g.useContext)(eP),{role:h=eG,roleDescription:p="draggable",tabIndex:v=0}=null!=l?l:{},m=(null==u?void 0:u.id)===t,y=(0,g.useContext)(m?eF:eU),[w,x]=(0,b.lk)(),[E,D]=(0,b.lk)(),C=(0,g.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),S=(0,b.YN)(n);return(0,b.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:E,data:S}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,g.useMemo)(()=>({role:h,tabIndex:v,"aria-disabled":r,"aria-pressed":!!m&&h===eG||void 0,"aria-roledescription":p,"aria-describedby":c.draggable}),[r,h,v,m,p,c.draggable]),isDragging:m,listeners:r?void 0:C,node:w,over:f,setNodeRef:x,setActivatorNodeRef:D,transform:y}}function eV(){return(0,g.useContext)(eY)}let eH={timeout:25};function eq(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=(0,b.YG)("Droppable"),{active:o,dispatch:u,over:s,measureDroppableContainers:c}=(0,g.useContext)(eP),d=(0,g.useRef)({disabled:n}),f=(0,g.useRef)(!1),h=(0,g.useRef)(null),p=(0,g.useRef)(null),{disabled:v,updateMeasurementsFor:m,timeout:y}={...eH,...i},w=(0,b.YN)(null!=m?m:r),x=eE({callback:(0,g.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),p.current=null},y)},[y]),disabled:v||!o}),E=(0,g.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),f.current=!1),e&&x.observe(e))},[x]),[D,C]=(0,b.lk)(E),S=(0,b.YN)(t);return(0,g.useEffect)(()=>{x&&D.current&&(x.disconnect(),f.current=!1,x.observe(D.current))},[D,x]),(0,g.useEffect)(()=>(u({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:D,rect:h,data:S}}),()=>u({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,g.useEffect)(()=>{n!==d.current.disabled&&(u({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)},[r,a,n,u]),{active:o,rect:h,isOver:(null==s?void 0:s.id)===r,node:D,over:s,setNodeRef:C}}function eJ(e){let{animation:t,children:n}=e,[r,l]=(0,g.useState)(null),[i,a]=(0,g.useState)(null),o=(0,b.ZC)(n);return n||r||!o||l(o),(0,b.Es)(()=>{if(!i)return;let e=null==r?void 0:r.key,n=null==r?void 0:r.props.id;if(null==e||null==n)return void l(null);Promise.resolve(t(n,i)).then(()=>{l(null)})},[t,r,i]),p().createElement(p().Fragment,null,n,r?(0,g.cloneElement)(r,{ref:a}):null)}let eQ={x:0,y:0,scaleX:1,scaleY:1};function eZ(e){let{children:t}=e;return p().createElement(eP.Provider,{value:eI},p().createElement(eF.Provider,{value:eQ},t))}let e$={position:"fixed",touchAction:"none"},e0=e=>(0,b.kx)(e)?"transform 250ms ease":void 0,e1=(0,g.forwardRef)((e,t)=>{let{as:n,activatorEvent:r,adjustScale:l,children:i,className:a,rect:o,style:u,transform:s,transition:c=e0}=e;if(!o)return null;let d=l?s:{...s,scaleX:1,scaleY:1},f={...e$,width:o.width,height:o.height,top:o.top,left:o.left,transform:b.Ks.Transform.toString(d),transformOrigin:l&&r?function(e,t){let n=(0,b.e_)(e);if(!n)return"0 0";let r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}(r,o):void 0,transition:"function"==typeof c?c(r):c,...u};return p().createElement(n,{className:a,style:f,ref:t},i)}),e2={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:n}}=e;return[{transform:b.Ks.Transform.toString(t)},{transform:b.Ks.Transform.toString(n)}]},sideEffects:(r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}})},e5=0,e6=p().memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:l,transition:i,modifiers:a,wrapperElement:o="div",className:u,zIndex:s=999}=e,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:h,draggableNodes:v,droppableContainers:m,dragOverlay:y,over:w,measuringConfiguration:x,scrollableAncestors:E,scrollableAncestorRects:D,windowRect:C}=eV(),S=(0,g.useContext)(eF),M=function(e){return(0,g.useMemo)(()=>{if(null!=e)return++e5},[e])}(null==d?void 0:d.id),k=eK(a,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:h,draggingNodeRect:y.rect,over:w,overlayNodeRect:y.rect,scrollableAncestors:E,scrollableAncestorRects:D,transform:S,windowRect:C}),R=ex(f),T=function(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:l}=e;return(0,b._q)((e,i)=>{if(null===t)return;let a=n.get(e);if(!a)return;let o=a.node.current;if(!o)return;let u=eT(i);if(!u)return;let{transform:s}=(0,b.zk)(i).getComputedStyle(i),c=B(s);if(!c)return;let d="function"==typeof t?t:function(e){let{duration:t,easing:n,sideEffects:r,keyframes:l}={...e2,...e};return e=>{let{active:i,dragOverlay:a,transform:o,...u}=e;if(!t)return;let s={x:a.rect.left-i.rect.left,y:a.rect.top-i.rect.top},c={scaleX:1!==o.scaleX?i.rect.width*o.scaleX/a.rect.width:1,scaleY:1!==o.scaleY?i.rect.height*o.scaleY/a.rect.height:1},d={x:o.x-s.x,y:o.y-s.y,...c},f=l({...u,active:i,dragOverlay:a,transform:{initial:o,final:d}}),[h]=f,g=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(g))return;let p=null==r?void 0:r({active:i,dragOverlay:a,...u}),v=a.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise(e=>{v.onfinish=()=>{null==p||p(),e()}})}}(t);return ee(o,l.draggable.measure),d({active:{id:e,data:a.data,node:o,rect:l.draggable.measure(o)},draggableNodes:n,dragOverlay:{node:i,rect:l.dragOverlay.measure(u)},droppableContainers:r,measuringConfiguration:l,transform:c})})}({config:r,draggableNodes:v,droppableContainers:m,measuringConfiguration:x}),L=R?y.setRef:void 0;return p().createElement(eZ,null,p().createElement(eJ,{animation:T},d&&M?p().createElement(e1,{key:M,id:d.id,ref:L,as:o,activatorEvent:c,adjustScale:t,className:u,transition:i,rect:R,style:{zIndex:s,...l},transform:k},n):null))})},62478:(e,t,n)=>{n.d(t,{$$:()=>p,Es:()=>h,KG:()=>b,Ks:()=>k,Ll:()=>o,Re:()=>C,Sw:()=>i,TW:()=>f,WQ:()=>D,YG:()=>x,YN:()=>v,ZC:()=>y,_q:()=>g,ag:()=>T,e_:()=>M,jn:()=>l,kx:()=>S,l6:()=>a,lk:()=>m,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(43210);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function f(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let h=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return h(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return h(()=>{n.current!==e&&(n.current=e)},t),n}function b(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function m(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function y(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function E(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let D=E(1),C=E(-1);function S(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function M(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let k=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[k.Translate.toString(e),k.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),R="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function T(e){return e.matches(R)?e:e.querySelector(R)}},81381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},88233:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};