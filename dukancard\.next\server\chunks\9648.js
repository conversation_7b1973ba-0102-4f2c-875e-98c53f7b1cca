exports.id=9648,exports.ids=[9648],exports.modules={3956:(e,a,t)=>{Promise.resolve().then(t.bind(t,64522))},8839:(e,a,t)=>{"use strict";t.d(a,{A:()=>z});var s=t(60687),r=t(43210),i=t.n(r),n=t(77882),l=t(88920),d=t(19080),c=t(96474),o=t(84027),u=t(14952),m=t(93661),p=t(63143),x=t(12597),h=t(13861),b=t(88233),g=t(24934),v=t(96752),f=t(55629),j=t(59821),_=t(30474);let N={formatVariantValues:e=>Object.entries(e).map(([e,a])=>`${e}: ${a}`).join(", "),getDisplayPrice:e=>e.discounted_price??e.base_price??null,getProductDisplayPrice:e=>e.discounted_price??e.base_price??null,hasDiscount:e=>!!(e.base_price&&e.discounted_price&&e.discounted_price<e.base_price),getDiscountPercentage:e=>N.hasDiscount(e)&&e.base_price&&e.discounted_price?Math.round((e.base_price-e.discounted_price)/e.base_price*100):null,getFeaturedImageUrl:e=>{if(!e.images||0===e.images.length)return null;let a=Math.min(e.featured_image_index||0,e.images.length-1);return e.images[a]||e.images[0]||null},getVariantSummary:e=>{if(0===e.length)return"No variants";let a=e.filter(e=>e.is_available).length,t=e.length;return a===t?`${t} variant${1===t?"":"s"}`:`${a}/${t} variant${1===t?"":"s"} available`}};var y=t(96241),k=t(23928),w=t(43649),S=t(11860),A=t(41862),C=t(13964),P=t(37826),$=t(63974),D=t(68988),L=t(39390),B=t(93437),I=t(78377),U=t(52581);function R({variants:e,selectedVariantIds:a,onSelectionChange:t,onBulkUpdate:i,disabled:d=!1,className:c}){let[u,m]=(0,r.useState)(!1),[p,b]=(0,r.useState)(""),[v,f]=(0,r.useState)(""),[_,N]=(0,r.useState)(!1),R=e.filter(e=>a.includes(e.id)),M=a.length>0,V=e=>{a.includes(e)?t(a.filter(a=>a!==e)):t([...a,e])},z=async()=>{if(p&&0!==a.length){N(!0);try{let e;if("update_base_price"===p||"update_discounted_price"===p||"apply_discount"===p){let a=parseFloat(v);if(isNaN(a)||a<0)return void U.oR.error("Please enter a valid positive number");e=a}await i(a,p,e),b(""),f(""),m(!1),t([]),U.oR.success(`Successfully updated ${a.length} variant${a.length>1?"s":""}`)}catch(e){console.error("Bulk operation failed:",e),U.oR.error("Failed to perform bulk operation. Please try again.")}finally{N(!1)}}},Y="update_base_price"===p||"update_discounted_price"===p||"apply_discount"===p;return(0,s.jsxs)("div",{className:(0,y.cn)("space-y-4",c),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(B.S,{checked:a.length===e.length&&e.length>0,onCheckedChange:()=>{a.length===e.length?t([]):t(e.map(e=>e.id))},disabled:d||0===e.length}),(0,s.jsx)("span",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:0===a.length?"Select variants for bulk operations":`${a.length} of ${e.length} selected`})]}),(0,s.jsxs)(P.lG,{open:u,onOpenChange:m,children:[(0,s.jsx)(P.zM,{asChild:!0,children:(0,s.jsxs)(g.$,{variant:"outline",size:"sm",disabled:!M||d,className:"gap-2",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"Bulk Operations"]})}),(0,s.jsxs)(P.Cf,{className:"max-w-md",children:[(0,s.jsxs)(P.c7,{children:[(0,s.jsx)(P.L3,{children:"Bulk Variant Operations"}),(0,s.jsxs)(P.rr,{children:["Perform operations on ",a.length," selected variant",a.length>1?"s":""]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(L.J,{className:"text-sm font-medium",children:"Selected Variants"}),(0,s.jsx)("div",{className:"max-h-32 overflow-y-auto space-y-1 p-2 border rounded-md bg-neutral-50 dark:bg-neutral-800",children:R.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,s.jsx)("span",{className:"truncate",children:e.variant_name}),(0,s.jsx)(j.E,{variant:e.is_available?"default":"secondary",className:"text-xs",children:e.is_available?"Available":"Unavailable"})]},e.id))})]}),(0,s.jsx)(I.w,{}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(L.J,{htmlFor:"operation",children:"Operation"}),(0,s.jsxs)($.l6,{value:p,onValueChange:e=>b(e),children:[(0,s.jsx)($.bq,{children:(0,s.jsx)($.yv,{placeholder:"Choose an operation"})}),(0,s.jsxs)($.gC,{children:[(0,s.jsx)($.eb,{value:"enable",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),"Enable Variants"]})}),(0,s.jsx)($.eb,{value:"disable",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"Disable Variants"]})}),(0,s.jsx)($.eb,{value:"update_base_price",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),"Update Base Price"]})}),(0,s.jsx)($.eb,{value:"update_discounted_price",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),"Update Discounted Price"]})}),(0,s.jsx)($.eb,{value:"apply_discount",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),"Apply Discount"]})})]})]})]}),p&&(0,s.jsx)("div",{className:"p-3 bg-blue-50 dark:bg-blue-950 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:(()=>{switch(p){case"enable":return"Make selected variants available for purchase";case"disable":return"Make selected variants unavailable for purchase";case"update_base_price":return"Set a new base price for selected variants";case"update_discounted_price":return"Set a new discounted price for selected variants";case"apply_discount":return"Apply a percentage discount to base prices of selected variants";default:return"Choose an operation to perform on selected variants"}})()})}),(0,s.jsx)(l.N,{children:Y&&(0,s.jsxs)(n.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"space-y-2",children:[(0,s.jsx)(L.J,{htmlFor:"value",children:(()=>{switch(p){case"update_base_price":return"New Base Price (₹)";case"update_discounted_price":return"New Discounted Price (₹)";case"apply_discount":return"Discount Percentage (%)";default:return"Value"}})()}),(0,s.jsx)(D.p,{id:"value",type:"number",step:"apply_discount"===p?"1":"0.01",min:"0",max:"apply_discount"===p?"100":void 0,placeholder:(()=>{switch(p){case"update_base_price":return"Enter new base price";case"update_discounted_price":return"Enter new discounted price";case"apply_discount":return"Enter discount percentage (e.g., 10 for 10%)";default:return"Enter value"}})(),value:v,onChange:e=>f(e.target.value)})]})}),("disable"===p||"update_base_price"===p||"update_discounted_price"===p)&&(0,s.jsxs)("div",{className:"flex items-start gap-2 p-3 bg-amber-50 dark:bg-amber-950 rounded-md",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("p",{className:"text-sm text-amber-800 dark:text-amber-200",children:["This operation will affect ",a.length," ","variant",a.length>1?"s":"",". Make sure you want to proceed."]})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,s.jsxs)(g.$,{variant:"outline",onClick:()=>m(!1),disabled:_,className:"flex-1",children:[(0,s.jsx)(S.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,s.jsx)(g.$,{onClick:z,disabled:!p||_||Y&&!v,className:"flex-1 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:_?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(A.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Apply"]})})]})]})]})]})]}),e.length>0&&(0,s.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,s.jsxs)("div",{className:(0,y.cn)("flex items-center gap-3 p-3 border rounded-lg transition-colors",a.includes(e.id)?"bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800":"bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800"),children:[(0,s.jsx)(B.S,{checked:a.includes(e.id),onCheckedChange:()=>V(e.id),disabled:d}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h4",{className:"font-medium text-sm text-neutral-900 dark:text-neutral-100 truncate",children:e.variant_name}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)(j.E,{variant:e.is_available?"default":"secondary",className:"text-xs",children:e.is_available?"Available":"Unavailable"})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(e.variant_values||{}).map(([e,a])=>(0,s.jsxs)(j.E,{variant:"outline",className:"text-xs",children:[e,": ",String(a)]},`${e}-${a}`))}),(0,s.jsx)("div",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e.discounted_price?(0,s.jsxs)("span",{className:"text-green-600 dark:text-green-400",children:["₹",e.discounted_price.toLocaleString()]}):e.base_price?(0,s.jsxs)("span",{children:["₹",e.base_price.toLocaleString()]}):(0,s.jsx)("span",{className:"text-neutral-400",children:"No price set"})})]})]})]},e.id))})]})}var M=t(6475);let V=(0,M.createServerReference)("70d838791e5769544191e6b0dbbeb36c46196105ea",M.callServer,void 0,M.findSourceMapURL,"bulkUpdateVariants");function z({variants:e,onAddVariant:a,onEditVariant:t,onDeleteVariant:k,onToggleVariantAvailability:w,className:S}){let[A,C]=(0,r.useState)(new Set),[P,$]=(0,r.useState)([]),[D,L]=(0,r.useState)(!1),B=e=>{let a=new Set(A);a.has(e)?a.delete(e):a.add(e),C(a)},I=e=>e?e.toLocaleString("en-IN",{style:"currency",currency:"INR"}):"-",M=e=>(0,s.jsx)(j.E,{variant:e?"default":"secondary",className:(0,y.cn)("text-xs font-medium",e?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"),children:e?"Available":"Unavailable"}),z=async(e,a,t)=>{try{let s=await V(e,a,t);s.success?(U.oR.success(s.message),$([])):(U.oR.error(s.message),s.errors&&s.errors.length>0&&s.errors.forEach(e=>U.oR.error(e)))}catch(e){console.error("Bulk update error:",e),U.oR.error("Failed to perform bulk operation. Please try again.")}};return 0===e.length?(0,s.jsx)("div",{className:(0,y.cn)("rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900",S),children:(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)(d.A,{className:"mx-auto h-12 w-12 text-neutral-400 dark:text-neutral-600 mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2",children:"No variants yet"}),(0,s.jsx)("p",{className:"text-neutral-500 dark:text-neutral-400 mb-4",children:"Create variants to offer different options for this product."}),a&&(0,s.jsxs)(g.$,{type:"button",onClick:a,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Add First Variant"]})]})}):(0,s.jsxs)("div",{className:(0,y.cn)("rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 overflow-hidden",S),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 text-neutral-600 dark:text-neutral-400"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-100",children:"Product Variants"}),(0,s.jsx)(j.E,{variant:"secondary",className:"text-xs",children:e.length})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.length>1&&(0,s.jsxs)(g.$,{type:"button",onClick:()=>L(!D),variant:"outline",size:"sm",children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4"}),D?"Hide":"Bulk Operations"]}),a&&(0,s.jsxs)(g.$,{type:"button",onClick:a,size:"sm",className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Add Variant"]})]})]}),D&&e.length>1&&(0,s.jsx)("div",{className:"p-4 border-b border-neutral-200 dark:border-neutral-700 bg-white dark:bg-black",children:(0,s.jsx)(R,{variants:e,selectedVariantIds:P,onSelectionChange:$,onBulkUpdate:z})}),(0,s.jsxs)(v.XI,{children:[(0,s.jsx)(v.A0,{children:(0,s.jsxs)(v.Hj,{className:"hover:bg-neutral-50 dark:hover:bg-neutral-900",children:[(0,s.jsx)(v.nd,{className:"w-[40px]"}),(0,s.jsx)(v.nd,{className:"text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Variant"}),(0,s.jsx)(v.nd,{className:"text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Properties"}),(0,s.jsx)(v.nd,{className:"text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Base Price"}),(0,s.jsx)(v.nd,{className:"text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Sale Price"}),(0,s.jsx)(v.nd,{className:"text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Status"}),(0,s.jsx)(v.nd,{className:"text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Actions"})]})}),(0,s.jsx)(v.BF,{children:e.map(e=>{let a=A.has(e.id),r=N.getDisplayPrice(e),c=N.hasDiscount(e),o=e.images&&e.images.length>0;return(0,s.jsxs)(i().Fragment,{children:[(0,s.jsxs)(n.P.tr,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"hover:bg-neutral-50 dark:hover:bg-neutral-900 transition-colors",children:[(0,s.jsx)(v.nA,{className:"p-2",children:(0,s.jsx)(g.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>B(e.id),className:"h-6 w-6 p-0",children:(0,s.jsx)(n.P.div,{animate:{rotate:90*!!a},transition:{duration:.2},children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})})}),(0,s.jsx)(v.nA,{className:"font-medium text-xs sm:text-sm text-neutral-800 dark:text-neutral-100",children:(0,s.jsx)("div",{className:"max-w-xs truncate",children:e.variant_name})}),(0,s.jsx)(v.nA,{className:"text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(e.variant_values||{}).map(([e,a])=>(0,s.jsxs)(j.E,{variant:"outline",className:"text-xs",children:[String(e),": ",String(a)]},`${e}-${a}`))})}),(0,s.jsx)(v.nA,{className:"text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:I(e.base_price)}),(0,s.jsx)(v.nA,{className:"text-right text-xs sm:text-sm",children:c?(0,s.jsxs)("div",{className:"flex flex-col items-end",children:[(0,s.jsx)("span",{className:"text-green-600 dark:text-green-400 font-medium",children:I(e.discounted_price)}),(0,s.jsx)("span",{className:"text-xs text-neutral-500 line-through",children:I(e.base_price)})]}):(0,s.jsx)("span",{className:"text-neutral-600 dark:text-neutral-400",children:I(r)})}),(0,s.jsx)(v.nA,{className:"text-center",children:M(e.is_available)}),(0,s.jsx)(v.nA,{className:"text-right",children:(0,s.jsxs)(f.rI,{children:[(0,s.jsx)(f.ty,{asChild:!0,children:(0,s.jsx)(g.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(f.SQ,{align:"end",children:[t&&(0,s.jsxs)(f._2,{onClick:()=>t(e),children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),w&&(0,s.jsx)(f._2,{onClick:()=>w(e.id,!e.is_available),children:e.is_available?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Make Unavailable"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Make Available"]})}),k&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.mB,{}),(0,s.jsxs)(f._2,{onClick:()=>k(e.id),className:"text-red-600 dark:text-red-400",children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]})})]}),(0,s.jsx)(l.N,{children:a&&(0,s.jsx)(n.P.tr,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"bg-neutral-50 dark:bg-neutral-900",children:(0,s.jsx)(v.nA,{colSpan:7,className:"p-0",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-100",children:"Variant Details"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Variant Name"}),(0,s.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e.variant_name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Properties"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:Object.entries(e.variant_values||{}).map(([e,a])=>(0,s.jsxs)(j.E,{variant:"outline",className:"text-xs",children:[(0,s.jsxs)("span",{className:"font-medium",children:[String(e),":"]})," ",String(a)]},`${e}-${a}`))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Base Price"}),(0,s.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:I(e.base_price)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Sale Price"}),(0,s.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:I(e.discounted_price)})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Availability"}),(0,s.jsx)("div",{className:"mt-1",children:M(e.is_available)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Created"}),(0,s.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:new Date(e.created_at).toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-100",children:"Variant Images"}),o?(0,s.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-3",children:e.images?.map((a,t)=>(0,s.jsxs)("div",{className:(0,y.cn)("relative aspect-square rounded-lg overflow-hidden border-2",t===e.featured_image_index?"border-[var(--brand-gold)]":"border-neutral-200 dark:border-neutral-700"),children:[(0,s.jsx)(_.default,{src:a,alt:`${e.variant_name} - Image ${t+1}`,fill:!0,className:"object-cover"}),t===e.featured_image_index&&(0,s.jsx)("div",{className:"absolute top-2 left-2",children:(0,s.jsx)(j.E,{className:"bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] text-xs",children:"Featured"})})]},t))}):(0,s.jsxs)("div",{className:"text-center py-8 border-2 border-dashed border-neutral-200 dark:border-neutral-700 rounded-lg",children:[(0,s.jsx)(d.A,{className:"mx-auto h-8 w-8 text-neutral-400 dark:text-neutral-600 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400",children:"No images for this variant"}),(0,s.jsx)("p",{className:"text-xs text-neutral-400 dark:text-neutral-500 mt-1",children:"Product images will be used as fallback"})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 mt-6 pt-4 border-t border-neutral-200 dark:border-neutral-700",children:[t&&(0,s.jsxs)(g.$,{type:"button",onClick:()=>t(e),variant:"outline",size:"sm",children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Edit Variant"]}),w&&(0,s.jsx)(g.$,{onClick:()=>w(e.id,!e.is_available),variant:"outline",size:"sm",children:e.is_available?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Make Unavailable"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Make Available"]})}),k&&(0,s.jsxs)(g.$,{onClick:()=>k(e.id),variant:"outline",size:"sm",className:"text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950",children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Delete Variant"]})]})]})})},`expanded-${e.id}`)})]},e.id)})})]})]})}},20670:(e,a,t)=>{"use strict";t.d(a,{Nh:()=>r,RL:()=>i});var s=t(79209);let r=e=>s.NB.map(a=>(function(e,a){let t="enterprise"===e.id,s=e.pricing.monthly,r=e.pricing.yearly;return{id:e.id,name:`${e.name} Plan`,razorpayPlanIds:{monthly:e.razorpayPlanIds.monthly||null,yearly:e.razorpayPlanIds.yearly||null},price:t?"Contact Sales":"monthly"===a?`₹${e.pricing.monthly.toLocaleString("en-IN")}`:`₹${e.pricing.yearly.toLocaleString("en-IN")}`,yearlyPrice:t?"Contact Sales":`₹${e.pricing.yearly.toLocaleString("en-IN")}`,period:t?"":"monthly"===a?"/month":"/year",savings:t?void 0:"yearly"===a?`Save ₹${(t?0:12*s-r).toLocaleString("en-IN")}`:void 0,description:e.description,features:e.features.map(e=>{if("Product Listings"===e.name&&e.included){let a="unlimited"===e.limit?"Unlimited":e.limit;return`Product/Service Listings (up to ${a})`}return e.included?e.name:`❌ ${e.name}`}),button:t?"Contact Sales":"Subscribe Now",available:!0,featured:"free"===e.id||"basic"===e.id||"growth"===e.id||"pro"===e.id,recommended:e.recommended||!1,mostPopular:e.recommended||!1}})(a,e));r("monthly");let i=e=>(0,s.dI)(e)},23928:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},37507:(e,a,t)=>{"use strict";t.d(a,{sX:()=>c});var s=t(45880);let r={id:s.Yj().uuid().optional(),business_id:s.Yj().uuid().optional(),product_type:s.k5(["physical","service"]).default("physical"),name:s.Yj().min(1,{message:"Product/Service name is required."}).max(100,{message:"Name cannot exceed 100 characters."}),description:s.Yj().max(500,{message:"Description cannot exceed 500 characters."}).optional().or(s.eu("")),base_price:s.au.number({required_error:"Base price is required.",invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}),discounted_price:s.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:s.zM().default(!0),image_url:s.Yj().url({message:"Invalid image URL format."}).optional().nullable(),images:s.YO(s.Yj()).optional().nullable(),featured_image_index:s.ai().int().min(0).optional().nullable(),slug:s.Yj().optional(),created_at:s.Yj().optional(),updated_at:s.Yj().optional()},i=s.Ik(r);i.omit({id:!0,business_id:!0,created_at:!0,updated_at:!0,image_url:!0}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),i.partial().refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]});let n=s.g1(s.Yj(),s.Yj()).refine(e=>Object.keys(e).length>0,{message:"At least one variant type-value pair is required."}).refine(e=>Object.keys(e).length<=5,{message:"Maximum of 5 variant types allowed per product."}),l={id:s.Yj().uuid().optional(),product_id:s.Yj().uuid(),variant_name:s.Yj().min(1,{message:"Variant name is required."}).max(100,{message:"Variant name cannot exceed 100 characters."}),variant_values:n,base_price:s.au.number({invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}).optional().nullable(),discounted_price:s.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:s.zM().default(!0),images:s.YO(s.Yj().url()).optional().nullable().default([]),featured_image_index:s.ai().int().min(0).optional().nullable(),created_at:s.p6().optional(),updated_at:s.p6().optional()},d=s.Ik(l),c=d.omit({id:!0,created_at:!0,updated_at:!0}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]});d.partial().refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),s.Ik({product_id:s.Yj().uuid(),variant_types_values:s.g1(s.Yj(),s.YO(s.Yj())).refine(e=>Object.keys(e).length>0,{message:"At least one variant type with values is required."}).refine(e=>Object.keys(e).length<=5,{message:"Maximum of 5 variant types allowed per product."})})},42933:(e,a,t)=>{"use strict";t.d(a,{ki:()=>i,yQ:()=>d.y,i4:()=>l,eF:()=>o,Y6:()=>r,G3:()=>n,Uz:()=>c}),t(37507);var s=t(6475);let r=(0,s.createServerReference)("787e564da2fc6345f07ba27b32c3991bc3e2ebbaad",s.callServer,void 0,s.findSourceMapURL,"getProductServices"),i=(0,s.createServerReference)("4028d9eae4d852411a8378996608e072b40402706b",s.callServer,void 0,s.findSourceMapURL,"addProductService"),n=(0,s.createServerReference)("60628b2893a8cf908260d34968479634cae65b8c58",s.callServer,void 0,s.findSourceMapURL,"updateProductService"),l=(0,s.createServerReference)("4003407ec6313e0e5df396bd13f3f069cdc5666b21",s.callServer,void 0,s.findSourceMapURL,"deleteProductService");var d=t(95588);let c=(0,s.createServerReference)("604381af5c96732d294fa7a1b7bd35fc6aa1a9781b",s.callServer,void 0,s.findSourceMapURL,"updateProductVariant"),o=(0,s.createServerReference)("40f582f4b6156ae356636c0f4aa3c00d2946c8f64b",s.callServer,void 0,s.findSourceMapURL,"deleteProductVariant")},45488:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d});var s=t(37413);t(61120);var r=t(32032),i=t(64522);let n=["member_name","title","business_name","business_category","contact_email","phone","address_line","pincode","city","state","locality"];var l=t(39916);async function d({children:e}){let a=await (0,r.createClient)(),t=null,d=null,c=null,o=null,{data:{user:u}}=await a.auth.getUser();if(u){let{data:e,error:s}=await a.from("business_profiles").select(`
        business_name,
        logo_url,
        member_name,
        title,
        business_category,
        contact_email,
        phone,
        address_line,
        pincode,
        city,
        state,
        locality
      `).eq("id",u.id).single(),{data:r}=await a.from("payment_subscriptions").select("plan_id").eq("business_profile_id",u.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(s)console.error("Error fetching business profile in layout:",s.message);else if(e){t=e.business_name,d=e.logo_url,c=e.member_name,o=r?.plan_id||"free";let a=function(e){if(!e)return{isComplete:!1,missingFields:[...n],missingFieldLabels:["Your name","Your title","Business name","Business category","Contact email","Primary phone","Address line","Pincode","City","State","Locality/area"]};let a=[],t=[],s={member_name:"Your name",title:"Your title",business_name:"Business name",business_category:"Business category",contact_email:"Contact email",phone:"Primary phone",address_line:"Address line",pincode:"Pincode",city:"City",state:"State",locality:"Locality/area"};return n.forEach(r=>{let i=e[r];i&&""!==String(i).trim()||(a.push(r),t.push(s[r]))}),{isComplete:0===a.length,missingFields:a,missingFieldLabels:t}}(e);if(!a.isComplete){let e=function(e){if(0===e.length)return"";if(1===e.length)return`Please complete your ${e[0].toLowerCase()} to access the dashboard.`;if(2===e.length)return`Please complete your ${e[0].toLowerCase()} and ${e[1].toLowerCase()} to access the dashboard.`;let a=e[e.length-1],t=e.slice(0,-1);return`Please complete your ${t.map(e=>e.toLowerCase()).join(", ")}, and ${a.toLowerCase()} to access the dashboard.`}(a.missingFieldLabels);(0,l.redirect)(`/onboarding?message=${encodeURIComponent(e)}`)}}}else console.warn("No user found in business dashboard layout.");return(0,s.jsx)(i.default,{businessName:t,logoUrl:d,memberName:c,userPlan:o,children:e})}},61192:(e,a,t)=>{"use strict";t.d(a,{default:()=>u});var s=t(60687);t(43210);var r=t(27625),i=t(41956),n=t(38606),l=t(24861),d=t(21121),c=t(96241),o=t(52529);function u({children:e,businessName:a,logoUrl:t,memberName:u,userPlan:m}){return(0,s.jsx)(o.Q,{children:(0,s.jsxs)(l.GB,{children:[(0,s.jsx)(d.s,{businessName:a,logoUrl:t,memberName:u,userPlan:m}),(0,s.jsxs)(l.sF,{children:[(0,s.jsxs)(r.default,{businessName:a,logoUrl:t,userName:u,children:[(0,s.jsx)(l.x2,{className:"ml-auto md:ml-0"})," ",(0,s.jsx)(i.ThemeToggle,{variant:"dashboard"})]}),(0,s.jsx)("main",{className:(0,c.cn)("flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto","pb-20 sm:pb-18 md:pb-6","bg-white dark:bg-black"),children:e}),(0,s.jsx)(n.default,{})]})]})})}},63143:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64522:(e,a,t)=>{"use strict";t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\components\\\\BusinessDashboardClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx","default")},64591:(e,a,t)=>{"use strict";t.d(a,{J:()=>o,O:()=>c});var s=t(91199);t(42087);var r=t(76881),i=t(7944),n=t(68567),l=t(33331);let d=n.z.object({variantIds:n.z.array(n.z.string().uuid()).min(1,"At least one variant must be selected"),operation:n.z.enum(["enable","disable","update_base_price","update_discounted_price","apply_discount"]),value:n.z.number().min(0).optional()});async function c(e,a,t){try{let s=d.parse({variantIds:e,operation:a,value:t}),n=await (0,r.createClient)(),{data:{user:l},error:c}=await n.auth.getUser();if(c||!l)return{success:!1,message:"Authentication required",updatedCount:0};let{data:o,error:u}=await n.from("product_variants").select(`
        id,
        product_id,
        base_price,
        discounted_price,
        is_available,
        products_services!inner(business_id)
      `).in("id",s.variantIds);if(u)return console.error("Error fetching variants:",u),{success:!1,message:"Failed to fetch variants",updatedCount:0};if(!o||0===o.length)return{success:!1,message:"No variants found",updatedCount:0};let m=new Set;for(let e of o){let a=e.products_services;a?.business_id&&m.add(a.business_id)}let{data:p,error:x}=await n.from("business_profiles").select("id").eq("id",l.id).single();if(x||!p)return{success:!1,message:"Business not found",updatedCount:0};if(!m.has(p.id))return{success:!1,message:"Unauthorized: Variants do not belong to your business",updatedCount:0};let h={};switch(s.operation){case"enable":h={is_available:!0};break;case"disable":h={is_available:!1};break;case"update_base_price":if(void 0===s.value)return{success:!1,message:"Base price value is required",updatedCount:0};h={base_price:s.value};break;case"update_discounted_price":if(void 0===s.value)return{success:!1,message:"Discounted price value is required",updatedCount:0};h={discounted_price:s.value};break;case"apply_discount":if(void 0===s.value)return{success:!1,message:"Discount percentage is required",updatedCount:0};let b=s.value,g=o.map(async e=>{if(!e.base_price)return{success:!1,error:`Variant ${e.id} has no base price`};let a=e.base_price*(1-b/100),{error:t}=await n.from("product_variants").update({discounted_price:a}).eq("id",e.id);return{success:!t,error:t?.message}}),v=await Promise.all(g),f=v.filter(e=>e.success).length,j=v.filter(e=>!e.success).map(e=>e.error).filter(Boolean);return(0,i.revalidatePath)("/dashboard/business/products"),{success:f>0,message:`Applied ${b}% discount to ${f} variant${f>1?"s":""}`,updatedCount:f,errors:j.length>0?j:void 0};default:return{success:!1,message:"Invalid operation",updatedCount:0}}let{data:_,error:N}=await n.from("product_variants").update(h).in("id",s.variantIds).select("id");if(N)return console.error("Error updating variants:",N),{success:!1,message:"Failed to update variants",updatedCount:0};let y=_?.length||0;(0,i.revalidatePath)("/dashboard/business/products");let k="";switch(s.operation){case"enable":k=`Enabled ${y} variant${y>1?"s":""}`;break;case"disable":k=`Disabled ${y} variant${y>1?"s":""}`;break;case"update_base_price":k=`Updated base price for ${y} variant${y>1?"s":""}`;break;case"update_discounted_price":k=`Updated discounted price for ${y} variant${y>1?"s":""}`}return{success:!0,message:k,updatedCount:y}}catch(e){if(console.error("Bulk variant operation error:",e),e instanceof n.z.ZodError)return{success:!1,message:"Invalid input data",updatedCount:0,errors:e.errors.map(e=>e.message)};return{success:!1,message:"An unexpected error occurred",updatedCount:0}}}async function o(e,a){try{let t=await (0,r.createClient)(),{data:s,error:i}=await t.from("product_variants").select(`
        id,
        products_services!inner(
          business_id
        )
      `).in("id",e);if(i||!s)return!1;return s.every(e=>e.products_services.business_id===a)}catch(e){return!1}}(0,l.D)([c,o]),(0,s.A)(c,"70d838791e5769544191e6b0dbbeb36c46196105ea",null),(0,s.A)(o,"60c57f5cb6147b25d698420a59de21049ed634d243",null)},79988:(e,a,t)=>{Promise.resolve().then(t.bind(t,61192))},93437:(e,a,t)=>{"use strict";t.d(a,{S:()=>l});var s=t(60687);t(43210);var r=t(40211),i=t(13964),n=t(96241);function l({className:e,...a}){return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},93661:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},95588:(e,a,t)=>{"use strict";t.d(a,{y:()=>r});var s=t(6475);let r=(0,s.createServerReference)("40e0b77a8dc6165a7703d56966ab44b8bb7215bc26",s.callServer,void 0,s.findSourceMapURL,"addProductVariant")},96474:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96752:(e,a,t)=>{"use strict";t.d(a,{A0:()=>n,BF:()=>l,Hj:()=>d,XI:()=>i,nA:()=>o,nd:()=>c});var s=t(60687);t(43210);var r=t(96241);function i({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",e),...a})})}function n({className:e,...a}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...a})}function l({className:e,...a}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...a})}function d({className:e,...a}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...a})}function c({className:e,...a}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}function o({className:e,...a}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}}};