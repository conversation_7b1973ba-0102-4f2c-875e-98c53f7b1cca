"use strict";exports.id=8382,exports.ids=[8382],exports.modules={4331:(e,a,r)=>{r.d(a,{G7:()=>o,L$:()=>u,h_:()=>p,oI:()=>d,uB:()=>l,xL:()=>c});var t=r(60687);r(43210);var s=r(70965),i=r(99270),n=r(96241);function l({className:e,...a}){return(0,t.jsx)(s.uB,{"data-slot":"command",className:(0,n.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...a})}function o({className:e,...a}){return(0,t.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,t.jsx)(i.A,{className:"size-4 shrink-0 opacity-50"}),(0,t.jsx)(s.uB.Input,{"data-slot":"command-input",className:(0,n.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...a})]})}function d({className:e,...a}){return(0,t.jsx)(s.uB.List,{"data-slot":"command-list",className:(0,n.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",e),...a})}function c({...e}){return(0,t.jsx)(s.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e})}function u({className:e,...a}){return(0,t.jsx)(s.uB.Group,{"data-slot":"command-group",className:(0,n.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...a})}function p({className:e,...a}){return(0,t.jsx)(s.uB.Item,{"data-slot":"command-item",className:(0,n.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}r(37826)},5811:(e,a,r)=>{r.r(a),r.d(a,{"00029e81b58a5a97fdfca38c6b984b28c00ce6cc90":()=>L,"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>t.B,"4003407ec6313e0e5df396bd13f3f069cdc5666b21":()=>I,"4028d9eae4d852411a8378996608e072b40402706b":()=>N,"403a75d9205d3ac636b80de9ab8dbe80951e5d71b9":()=>F,"4055b8f6815995dd3ae4b7db159041d6254736584f":()=>C,"405fdaee2ff8ff13e45b4811c849ef0b8a06eea405":()=>R,"406284e9ef6a2be8ebf3ce5826f7572619ff00844e":()=>D,"40e0b77a8dc6165a7703d56966ab44b8bb7215bc26":()=>A,"40e15bace5efcde3c132186e902abe40403e1fa4ff":()=>O,"40f582f4b6156ae356636c0f4aa3c00d2946c8f64b":()=>$,"601694daaf4792dcf464368d8457fcd1259c923cf2":()=>U,"602335d5c6ca713548cda36b584c6afe685e032e60":()=>T,"604381af5c96732d294fa7a1b7bd35fc6aa1a9781b":()=>P,"605db269f98f874bac1f27e3d6b4e08414266e3ac8":()=>E,"60628b2893a8cf908260d34968479634cae65b8c58":()=>k,"60c57f5cb6147b25d698420a59de21049ed634d243":()=>s.J,"70d838791e5769544191e6b0dbbeb36c46196105ea":()=>s.O,"70ffeec93e6ac09c9ae41698e2e6418acc4707a28d":()=>V,"787e564da2fc6345f07ba27b32c3991bc3e2ebbaad":()=>o,"7c3952b17920109cf4045de4541c0edf3828caac5d":()=>j,"7e7db967276a6dd3efc6189832b27978b012cea709":()=>w,"7f5d6d667d4cc9c503574c5b1be4effe09dd0b5cce":()=>y});var t=r(64275),s=r(64591),i=r(91199);r(42087);var n=r(76881),l=r(33331);async function o(e=1,a=10,r={},t="created_desc"){let s=await (0,n.createClient)(),{data:{user:i},error:l}=await s.auth.getUser();if(l||!i)return{error:"User not authenticated."};let d=(e-1)*a,c=s.from("products_services").select(`
      id,
      business_id,
      product_type,
      name,
      description,
      base_price,
      discounted_price,
      is_available,
      image_url,
      images,
      featured_image_index,
      created_at,
      updated_at,
      slug,
      product_variants(id, is_available)
    `,{count:"exact"}).eq("business_id",i.id);switch(r.searchTerm&&(c=c.or(`name.ilike.%${r.searchTerm}%,description.ilike.%${r.searchTerm}%`)),void 0!==r.hasVariants&&(c=r.hasVariants?c.not("product_variants","is",null):c.is("product_variants",null)),r.productType&&(c=c.eq("product_type",r.productType)),t){case"created_asc":c=c.order("created_at",{ascending:!0});break;case"price_asc":c=c.order("discounted_price",{ascending:!0,nullsFirst:!1}).order("base_price",{ascending:!0,nullsFirst:!1});break;case"price_desc":c=c.order("discounted_price",{ascending:!1,nullsFirst:!1}).order("base_price",{ascending:!1,nullsFirst:!1});break;case"name_asc":c=c.order("name",{ascending:!0});break;case"name_desc":c=c.order("name",{ascending:!1});break;case"available_first":c=c.order("is_available",{ascending:!1}).order("created_at",{ascending:!1});break;case"unavailable_first":c=c.order("is_available",{ascending:!0}).order("created_at",{ascending:!1});break;default:c=c.order("created_at",{ascending:!1})}c=c.range(d,d+a-1);let{data:u,error:p,count:m}=await c;if(p)return console.error("Fetch Products Error:",p),{error:"Failed to fetch products/services."};let g=(u||[]).map(e=>{let a=e.product_variants||[],r=a.length,t=a.filter(e=>e.is_available).length;return{id:e.id,business_id:e.business_id,product_type:e.product_type,name:e.name,description:e.description,base_price:e.base_price,discounted_price:e.discounted_price,is_available:e.is_available,image_url:e.image_url,images:e.images||[],featured_image_index:e.featured_image_index,slug:e.slug,created_at:new Date(e.created_at).toISOString(),updated_at:new Date(e.updated_at).toISOString(),variant_count:r,has_variants:r>0,available_variant_count:t}});return"variant_count_asc"===t?g.sort((e,a)=>e.variant_count-a.variant_count):"variant_count_desc"===t&&g.sort((e,a)=>a.variant_count-e.variant_count),{data:g,count:m??0}}(0,l.D)([o]),(0,i.A)(o,"787e564da2fc6345f07ba27b32c3991bc3e2ebbaad",null);var d=r(7944),c=r(68567);let u={id:c.Yj().uuid().optional(),business_id:c.Yj().uuid().optional(),product_type:c.k5(["physical","service"]).default("physical"),name:c.Yj().min(1,{message:"Product/Service name is required."}).max(100,{message:"Name cannot exceed 100 characters."}),description:c.Yj().max(500,{message:"Description cannot exceed 500 characters."}).optional().or(c.eu("")),base_price:c.au.number({required_error:"Base price is required.",invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}),discounted_price:c.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:c.zM().default(!0),image_url:c.Yj().url({message:"Invalid image URL format."}).optional().nullable(),images:c.YO(c.Yj()).optional().nullable(),featured_image_index:c.ai().int().min(0).optional().nullable(),slug:c.Yj().optional(),created_at:c.Yj().optional(),updated_at:c.Yj().optional()},p=c.Ik(u),m=p.omit({id:!0,business_id:!0,created_at:!0,updated_at:!0,image_url:!0}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),g=p.partial().refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),h=c.g1(c.Yj(),c.Yj()).refine(e=>Object.keys(e).length>0,{message:"At least one variant type-value pair is required."}).refine(e=>Object.keys(e).length<=5,{message:"Maximum of 5 variant types allowed per product."}),f={id:c.Yj().uuid().optional(),product_id:c.Yj().uuid(),variant_name:c.Yj().min(1,{message:"Variant name is required."}).max(100,{message:"Variant name cannot exceed 100 characters."}),variant_values:h,base_price:c.au.number({invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}).optional().nullable(),discounted_price:c.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:c.zM().default(!0),images:c.YO(c.Yj().url()).optional().nullable().default([]),featured_image_index:c.ai().int().min(0).optional().nullable(),created_at:c.p6().optional(),updated_at:c.p6().optional()},v=c.Ik(f),b=v.omit({id:!0,created_at:!0,updated_at:!0}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),x=v.partial().refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]});c.Ik({product_id:c.Yj().uuid(),variant_types_values:c.g1(c.Yj(),c.YO(c.Yj())).refine(e=>Object.keys(e).length>0,{message:"At least one variant type with values is required."}).refine(e=>Object.keys(e).length<=5,{message:"Maximum of 5 variant types allowed per product."})});var _=r(50937);async function y(e,a,r,t=[],s=[],i="base",l){if(!e||"string"!=typeof e)return console.error("Invalid userId provided to handleMultipleImageUpload:",e),{urls:[],error:`Invalid userId: expected string, got ${typeof e}`};if(!a||"string"!=typeof a)return console.error("Invalid productId provided to handleMultipleImageUpload:",a),{urls:[],error:`Invalid productId: expected string, got ${typeof a}`};if("variant"===i&&(!l||"string"!=typeof l))return console.error("Invalid variantId provided for variant path type:",l),{urls:[],error:`Invalid variantId: expected string when pathType is 'variant', got ${typeof l}`};let o=r.filter(e=>null!==e);if(console.log("=== Image Upload Handler Debug ==="),console.log(`Path type: ${i}, Variant ID: ${l||"N/A"}`),console.log(`Valid image files: ${o.length}`),console.log(`Existing images: ${t.length}`),console.log(`Removed indices: ${s.length}`),0===o.length&&0===s.length)return console.log("No image operations to perform, returning existing URLs"),{urls:t};if(o.length>5)return{urls:[],error:`Maximum of 5 images allowed per ${"variant"===i?"variant":"product"}.`};let d=o.reduce((e,a)=>e+a.size,0);if(d>0x4b00000){let e=(d/1048576).toFixed(1);return{urls:[],error:`Total upload size (${e}MB) exceeds the 75MB limit for ${"variant"===i?"variant":"product"} images.`}}let c="business",u=[...t],p=await (0,n.createClient)();for(let e of s)if(e>=0&&e<t.length){let a=t[e];if(a){try{console.log(`Removing image at index ${e}: ${a}`);let r=new URL(a),t=r.pathname.split("/"),s=t.findIndex(e=>"business"===e);if(-1!==s&&s<t.length-1){let a=t.slice(s+1).join("/").split("?")[0];console.log(`Attempting to delete from storage path: ${a}`);let{error:r}=await p.storage.from(c).remove([a]);r&&"The resource was not found"!==r.message?(console.error(`Error deleting image at index ${e}:`,r),console.error("Delete error details:",r)):console.log(`Successfully deleted image at path: ${a}`)}else console.warn(`Could not extract storage path from URL: ${a}`),console.warn(`URL pathname: ${r.pathname}`),console.warn("Path parts:",t)}catch(a){console.error(`Error processing image URL for deletion at index ${e}:`,a)}u[e]=""}}let m=[...u.filter(e=>""!==e)],g=m.length;for(let t=0;t<r.length;t++){let s=r[t];if(s)try{let r=new Date().getTime()+Math.floor(1e3*Math.random()),n=g+t,o="variant"===i&&l?(0,_.Vl)(e,a,l,n,r):(0,_.jA)(e,a,n,r);console.log(`Uploading image ${t} (index ${n}) to path: ${o}`);let d=Buffer.from(await s.arrayBuffer()),{error:u}=await p.storage.from(c).upload(o,d,{contentType:s.type,upsert:!0});if(u){console.error(`Failed to upload image ${t}:`,u);continue}let{data:h}=p.storage.from(c).getPublicUrl(o);if(!h?.publicUrl){console.error(`Could not retrieve public URL for image ${t}`);continue}console.log(`Successfully uploaded image ${t}, URL: ${h.publicUrl}`),m.push(h.publicUrl)}catch(e){console.error(`Error processing image ${t}:`,e)}}return console.log(`Image upload complete. Returning ${m.length} URLs:`,m),console.log("=== End Image Upload Handler Debug ==="),{urls:m}}async function j(e,a,r,t=[],s=[]){return y(e,a,r,t,s,"base")}async function w(e,a,r,t,s=[],i=[]){return y(e,a,t,s,i,"variant",r)}async function N(e){let a=await (0,n.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,error:"User not authenticated."};let s={},i=[],l=0;for(let[a,r]of e.entries())if(a.startsWith("productImage_")){let e=parseInt(a.split("_")[1],10);if(r instanceof File&&r.size>0){for(;i.length<=e;)i.push(null);i[e]=r}}else"featuredImageIndex"===a?l=parseInt(r,10)||0:s[a]=r;s.product_type&&"string"!=typeof s.product_type&&(s.product_type=String(s.product_type)),s.base_price&&(s.base_price=Number(s.base_price)),s.discounted_price&&(s.discounted_price=Number(s.discounted_price)),s.is_available="true"===s.is_available||"on"===s.is_available;let o=m.safeParse(s);if(!o.success){console.error("Add Product Validation Error:",o.error.flatten().fieldErrors);let e=Object.entries(o.error.flatten().fieldErrors).map(([e,a])=>`${e}: ${Array.isArray(a)?a.join(", "):a}`).join("; ");return{success:!1,error:`Invalid data: ${e}`}}let c={...o.data,business_id:r.id,image_url:null},{data:u,error:p}=await a.from("products_services").insert(c).select("id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug").single();if(p||!u)return p?.message?.includes("Cannot make product available")&&p?.message?.includes("reached the limit")?{success:!1,error:p.message}:{success:!1,error:`Failed to add product/service: ${p?.message}`};let g=u.id,h=u.image_url,f=u.images||[];if(i.length>0){console.log("Starting image upload with:",{userId:r.id,userIdType:typeof r.id,productId:g,productIdType:typeof g,imageCount:i.length});let e=await j(r.id,g,i);if(e.error)return console.error(`Image upload failed: ${e.error}`),{success:!1,error:`Product created, but image upload failed: ${e.error}`};if((f=e.urls).length>0){let e=Math.min(l,f.length-1),r={image_url:h=f[e],images:Array.isArray(f)?f.filter(e=>null!==e):[],featured_image_index:e},{data:t,error:s}=await a.from("products_services").update(r).eq("id",g).select("id, image_url, images, featured_image_index");if(s)return console.error(`Failed to update product with image URLs: ${s.message}`),{success:!1,error:`Product created, images uploaded, but failed to save URLs: ${s.message}`}}}(0,d.revalidatePath)("/dashboard/business/products");let{data:v,error:b}=await a.from("products_services").select("id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug").eq("id",g).single();return b&&console.error(`Failed to fetch latest product data: ${b.message}`),{success:!0,data:{...v||u,image_url:v?.image_url||h,images:v?.images||(Array.isArray(f)?f.filter(e=>null!==e):[]),featured_image_index:v?.featured_image_index||l,created_at:v?.created_at||u.created_at,updated_at:v?.updated_at||u.updated_at,slug:v?.slug||u.slug}}}async function k(e,a){let r,t,s=await (0,n.createClient)(),{data:{user:i},error:l}=await s.auth.getUser();if(l||!i)return{success:!1,error:"User not authenticated."};let o={},c=[],u=0,p=[];for(let[e,r]of a.entries())if(e.startsWith("productImage_")){let a=parseInt(e.split("_")[1],10);if(r instanceof File&&r.size>0){for(;c.length<=a;)c.push(null);c[a]=r}}else if("featuredImageIndex"===e)u=parseInt(r,10)||0;else if("removedImageIndices"===e)try{let e=JSON.parse(r);Array.isArray(e)&&e.forEach(e=>{"number"==typeof e&&p.push(e)})}catch(e){console.error("Error parsing removedImageIndices:",e)}else o[e]=r;o.product_type&&"string"!=typeof o.product_type&&(o.product_type=String(o.product_type)),o.base_price&&(o.base_price=Number(o.base_price)),o.discounted_price&&(o.discounted_price=Number(o.discounted_price)),o.is_available="true"===o.is_available||"on"===o.is_available;let m=g.safeParse(o);if(!m.success){console.error("Update Product Validation Error:",m.error.flatten().fieldErrors);let e=Object.entries(m.error.flatten().fieldErrors).map(([e,a])=>`${e}: ${Array.isArray(a)?a.join(", "):a}`).join("; ");return{success:!1,error:`Invalid data: ${e}`}}let h={...m.data};h.updated_at=new Date().toISOString(),delete h.id,delete h.business_id,delete h.created_at,delete h.image_url;let{data:f,error:v}=await s.from("products_services").select("image_url, images, featured_image_index, name").eq("id",e).eq("business_id",i.id).single();if(v)return{success:!1,error:`Failed to fetch current product data: ${v.message}`};if(m.data.name||f.name,c.length>0||p.length>0){let a=f?.images||[],r=await j(i.id,e,c,a,p);if(r.error)return{success:!1,error:`Failed to update images: ${r.error}`};if(Array.isArray(t=r.urls)&&t.length>0){let e=Math.min(u,t.length-1);h.image_url=t[e],h.images=t,h.featured_image_index=e}else h.image_url=null,h.images=[],h.featured_image_index=0}try{let{data:a,error:r}=await s.from("products_services").update(h).eq("id",e).eq("business_id",i.id).select("id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug").single();if(r||!a){if(console.error(`Failed to update product ${e}:`,r),r?.message?.includes("Cannot make product available")&&r?.message?.includes("reached the limit"))return{success:!1,error:r.message};if(r?.code==="PGRST116")return{success:!1,error:"Product/Service not found or you don't have permission to edit it."};return{success:!1,error:`Failed to update product/service: ${r?.message}`}}return(0,d.revalidatePath)("/dashboard/business/products"),{success:!0,data:a}}catch(a){return console.error(`Unexpected error updating product ${e}:`,a),{success:!1,error:`Unexpected error updating product: ${a instanceof Error?a.message:String(a)}`}}}async function I(e){let a=await (0,n.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,error:"User not authenticated."};if(!e)return{success:!1,error:"Item ID is required."};let{data:s}=await a.from("products_services").select("images, name").eq("id",e).eq("business_id",r.id).single();if(!s)return{success:!1,error:"Product not found."};let i="business";try{if(s.images&&Array.isArray(s.images)&&s.images.length>0){for(let e of s.images)if(e)try{let r=new URL(e).pathname.split("/"),t=r.findIndex(e=>"business"===e);if(-1!==t&&t<r.length-1){let e=r.slice(t+1).join("/").split("?")[0];await a.storage.from(i).remove([e]),console.log(`Attempted to delete image from URL: ${e}`)}}catch(a){console.error(`Error processing image URL for deletion: ${e}`,a)}}let t=(0,_.getScalableUserPath)(r.id),{data:n,error:l}=await a.storage.from(i).list(`${t}/products`);if(l)console.error("Error listing product files:",l);else if(n){let r=[];for(let s of n)if(s.name===e){let{data:e,error:n}=await a.storage.from(i).list(`${t}/products/${s.name}`);if(n)console.error(`Error listing files in product folder ${s.name}:`,n);else if(e)for(let a of e)r.push(`${t}/products/${s.name}/${a.name}`)}if(r.length>0)for(let e=0;e<r.length;e+=100){let t=r.slice(e,e+100),{error:s}=await a.storage.from(i).remove(t);s&&"The resource was not found"!==s.message?console.warn(`Could not delete some product files (batch ${e}): ${s.message}`):console.log(`Successfully deleted batch ${e} of product files`)}}}catch(e){console.error("Error handling product file deletion:",e)}let{error:l}=await a.from("products_services").delete().eq("id",e).eq("business_id",r.id);return l?(console.error("Delete Product Error:",l),{success:!1,error:`Failed to delete product/service: ${l.message}`}):((0,d.revalidatePath)("/dashboard/business/products"),{success:!0})}(0,l.D)([y,j,w]),(0,i.A)(y,"7f5d6d667d4cc9c503574c5b1be4effe09dd0b5cce",null),(0,i.A)(j,"7c3952b17920109cf4045de4541c0edf3828caac5d",null),(0,i.A)(w,"7e7db967276a6dd3efc6189832b27978b012cea709",null),(0,l.D)([N]),(0,i.A)(N,"4028d9eae4d852411a8378996608e072b40402706b",null),(0,l.D)([k]),(0,i.A)(k,"60628b2893a8cf908260d34968479634cae65b8c58",null),(0,l.D)([I]),(0,i.A)(I,"4003407ec6313e0e5df396bd13f3f069cdc5666b21",null);var S=r(35157);async function A(e){let a=await (0,n.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,error:"User not authenticated."};try{let t,s=e.get("product_id"),i=e.get("variant_name"),n=e.get("variant_values"),l=e.get("base_price"),o=e.get("discounted_price"),c="true"===e.get("is_available"),u=parseInt(e.get("featured_image_index"))||0;try{t=JSON.parse(n||"{}")}catch(e){return{success:!1,error:"Invalid variant values format."}}let p={product_id:s,variant_name:i,variant_values:t,base_price:l&&""!==l.trim()&&"undefined"!==l?parseFloat(l):null,discounted_price:o&&""!==o.trim()&&"undefined"!==o?parseFloat(o):null,is_available:c,featured_image_index:u},m=b.safeParse(p);if(!m.success){console.error("Add Variant Validation Error:",m.error.flatten().fieldErrors);let e=m.error.flatten().fieldErrors,a=Object.entries(e).map(([e,a])=>`${e}: ${Array.isArray(a)?a.join(", "):a}`).join("; ");return{success:!1,error:`Invalid data: ${a}`}}let{data:g,error:h}=await a.from("products_services").select("id, business_id").eq("id",s).eq("business_id",r.id).single();if(h||!g)return{success:!1,error:"Product not found or access denied."};let{data:f,error:v}=await a.from("product_variants").select("id, variant_values").eq("product_id",s);if(v)return console.error("Error checking existing variants:",v),{success:!1,error:"Failed to validate variant uniqueness."};if(f&&f.length>0&&f.find(e=>{let a="string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values,r=Object.keys(a).sort(),s=Object.keys(t).sort();return r.length===s.length&&r.every(e=>s.includes(e)&&a[e]===t[e])}))return{success:!1,error:"A variant with this combination already exists."};let x=[],_=[];for(let[a,r]of(console.log("=== AddVariant FormData Debug ==="),e.entries()))console.log(`FormData key: ${a}, value type: ${typeof r}, value:`,r),a.startsWith("images[")&&r instanceof File&&r.size>0&&(console.log(`Found image file: ${a}, size: ${r.size}, name: ${r.name}`),_.push(r));console.log(`Total image files extracted: ${_.length}`),console.log("=== End AddVariant FormData Debug ===");let y={product_id:s,variant_name:i,variant_values:t,base_price:m.data.base_price,discounted_price:m.data.discounted_price,is_available:c,images:[],featured_image_index:m.data.featured_image_index??0},{data:j,error:N}=await a.from("product_variants").insert(y).select().single();if(N)return console.error("Error inserting variant:",N),{success:!1,error:`Failed to create variant: ${N.message}`};if(_.length>0){console.log(`Uploading ${_.length} images for variant ${j.id} in product ${s}`);let e=await w(r.id,s,j.id,_);if(e.error)return console.error("Image upload failed:",e.error),await a.from(S.CG.PRODUCT_VARIANTS).delete().eq(S.cZ.ID,j.id),{success:!1,error:e.error||"Failed to upload images."};x=e.urls||[],console.log(`Successfully uploaded ${x.length} images:`,x);let t={images:x,featured_image_index:Math.min(u,Math.max(0,x.length-1))};console.log(`Updating variant ${j.id} with:`,t);let{error:i}=await a.from("product_variants").update(t).eq("id",j.id);if(i)return console.error("Error updating variant with images:",i),{success:!1,error:"Failed to update variant with images."};console.log("Successfully updated variant with images")}let k={...j,images:x,featured_image_index:x.length>0?Math.min(u,Math.max(0,x.length-1)):0};return(0,d.revalidatePath)("/dashboard/business/products"),(0,d.revalidatePath)(`/dashboard/business/products/${s}`),{success:!0,data:{...k,variant_values:"string"==typeof k.variant_values?JSON.parse(k.variant_values):k.variant_values,created_at:new Date(k.created_at),updated_at:new Date(k.updated_at)}}}catch(e){return console.error("Unexpected error in addProductVariant:",e),{success:!1,error:"An unexpected error occurred."}}}async function E(e,a){let r=await (0,n.createClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return{success:!1,error:"User not authenticated."};try{let{data:s,error:i}=await r.from("products_services").select("id, business_id").eq("id",e).eq("business_id",t.id).single();if(i||!s)return{success:!1,error:"Product not found or access denied."};let n=a.map(a=>({product_id:e,variant_name:a.variant_name,variant_values:JSON.stringify(a.variant_values),base_price:a.base_price||null,discounted_price:a.discounted_price||null,is_available:a.is_available??!0,images:[],featured_image_index:void 0!==a.featured_image_index?a.featured_image_index:null})),{data:l,error:o}=await r.from("product_variants").insert(n).select();if(o)return console.error("Error inserting variants:",o),{success:!1,error:"Failed to create variants."};return(0,d.revalidatePath)("/dashboard/business/products"),(0,d.revalidatePath)(`/dashboard/business/products/${e}`),{success:!0,data:l?.map(e=>({...e,variant_values:"string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values,created_at:new Date(e.created_at),updated_at:new Date(e.updated_at)}))||[]}}catch(e){return console.error("Unexpected error in addMultipleVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function U(e,a){try{if(!e||!a||0===Object.keys(a).length)return{success:!1,error:"Invalid input parameters."};if(Object.keys(a).length>5)return{success:!1,error:"Maximum of 5 variant types allowed per product."};let r=Object.keys(a),t=r.map(e=>a[e]).reduce((e,a)=>e.flatMap(e=>a.map(a=>[...e,a])),[[]]).map(e=>{let a={};return r.forEach((r,t)=>{a[r]=e[t]}),{variant_name:e.join(" "),variant_values:a}});return{success:!0,combinations:t}}catch(e){return console.error("Error generating variant combinations:",e),{success:!1,error:"Failed to generate combinations."}}}async function P(e,a){let r=await (0,n.createClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return{success:!1,error:"User not authenticated."};try{let s,{data:i,error:n}=await r.from("product_variants").select(`
        id,
        product_id,
        variant_name,
        variant_values,
        base_price,
        discounted_price,
        is_available,
        images,
        featured_image_index,
        products_services!inner(business_id)
      `).eq("id",e).single();if(n||!i)return{success:!1,error:"Variant not found."};if(i.products_services.business_id!==t.id)return{success:!1,error:"Access denied."};let l=a.get("variant_name"),o=a.get("variant_values"),c=a.get("base_price"),u=a.get("discounted_price"),p=a.get("is_available"),m=a.get("featured_image_index"),g=a.get("remove_images");if(o)try{s=JSON.parse(o)}catch(e){return{success:!1,error:"Invalid variant values format."}}let h={id:e,product_id:i.product_id};null!==l&&(h.variant_name=l),s&&(h.variant_values=s),null!==c&&(h.base_price=c&&""!==c.trim()&&"undefined"!==c?parseFloat(c):null),null!==u&&(h.discounted_price=u&&""!==u.trim()&&"undefined"!==u?parseFloat(u):null),null!==p&&(h.is_available="true"===p),null!==m&&(h.featured_image_index=parseInt(m)||0);let f=x.safeParse(h);if(!f.success){console.error("Update Variant Validation Error:",f.error.flatten().fieldErrors);let e=f.error.flatten().fieldErrors,a=Object.entries(e).map(([e,a])=>`${e}: ${Array.isArray(a)?a.join(", "):a}`).join("; ");return{success:!1,error:`Invalid data: ${a}`}}if(s){let{data:a,error:t}=await r.from("product_variants").select("id, variant_values").eq("product_id",i.product_id).neq("id",e);if(t)return console.error("Error checking existing variants:",t),{success:!1,error:"Failed to validate variant uniqueness."};if(a&&a.length>0&&a.find(e=>{let a="string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values,r=Object.keys(a).sort(),t=Object.keys(s).sort();return r.length===t.length&&r.every(e=>t.includes(e)&&a[e]===s[e])}))return{success:!1,error:"A variant with this combination already exists."}}let v=[...i.images||[]],b=[];if(g)try{b=JSON.parse(g),console.log(`Removing images at indices: ${b.join(", ")}`)}catch(e){console.error("Error parsing remove_images:",e)}let _=[];for(let[e,r]of(console.log("=== UpdateVariant FormData Debug ==="),a.entries()))console.log(`FormData key: ${e}, value type: ${typeof r}, value:`,r),e.startsWith("new_images[")&&r instanceof File&&r.size>0&&(console.log(`Found new image file: ${e}, size: ${r.size}, name: ${r.name}`),_.push(r)),e.startsWith("images[")&&r instanceof File&&r.size>0&&(console.log(`Found image file (images[] pattern): ${e}, size: ${r.size}, name: ${r.name}`),_.push(r));console.log(`Total image files extracted: ${_.length}`),console.log(`Original images before processing: ${v.length}`,v),console.log("=== End UpdateVariant FormData Debug ===");let y=v;if(_.length>0||b.length>0){console.log(`Processing images for variant ${e}: ${_.length} uploads, ${b.length} deletions`);let a=await w(t.id,i.product_id,e,_,v,b);if(a.error)return console.error("Image operation failed:",a.error),{success:!1,error:a.error||"Failed to process images."};y=a.urls||[],console.log(`Successfully processed images. Final images: ${y.length}`,y)}else console.log("No image operations to perform");let j={};void 0!==f.data.variant_name&&(j.variant_name=f.data.variant_name),void 0!==f.data.variant_values&&(j.variant_values=f.data.variant_values),void 0!==f.data.base_price&&(j.base_price=f.data.base_price),void 0!==f.data.discounted_price&&(j.discounted_price=f.data.discounted_price),void 0!==f.data.is_available&&(j.is_available=f.data.is_available),j.images=y,j.featured_image_index=Math.min(f.data.featured_image_index||0,Math.max(0,y.length-1)),console.log(`Updating variant ${e} with:`,{images:j.images,featured_image_index:j.featured_image_index,imageCount:y.length});let{data:N,error:k}=await r.from("product_variants").update(j).eq("id",e).select().single();if(k)return console.error("Error updating variant:",k),{success:!1,error:"Failed to update variant."};return console.log("Successfully updated variant:",N),(0,d.revalidatePath)("/dashboard/business/products"),(0,d.revalidatePath)(`/dashboard/business/products/${i.product_id}`),{success:!0,data:{...N,variant_values:"string"==typeof N.variant_values?JSON.parse(N.variant_values):N.variant_values,featured_image_index:N.featured_image_index??0,created_at:new Date(N.created_at),updated_at:new Date(N.updated_at)}}}catch(e){return console.error("Unexpected error in updateProductVariant:",e),{success:!1,error:"An unexpected error occurred."}}}async function C(e){let a=await (0,n.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,error:"User not authenticated."};try{let t=0,s=0;for(let i of e)try{let{data:e,error:n}=await a.from("product_variants").select(`
            id,
            products_services!inner(business_id)
          `).eq("id",i.id).single();if(n||!e||e.products_services.business_id!==r.id){s++;continue}let{error:l}=await a.from("product_variants").update(i.data).eq("id",i.id);l?s++:t++}catch(e){s++}return(0,d.revalidatePath)("/dashboard/business/products"),{success:!0,updated_count:t,failed_count:s}}catch(e){return console.error("Unexpected error in updateMultipleVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function $(e){let a=await (0,n.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,error:"User not authenticated."};try{let{data:t,error:s}=await a.from("product_variants").select(`
        id,
        product_id,
        is_available,
        images,
        products_services!inner(business_id)
      `).eq("id",e).single();if(s||!t)return{success:!1,error:"Variant not found."};if(t.products_services.business_id!==r.id)return{success:!1,error:"Access denied."};let{data:i,error:n}=await a.from("product_variants").select("id, is_available").eq("product_id",t.product_id);if(n)return console.error("Error counting variants:",n),{success:!1,error:"Failed to validate variant deletion."};let l=i?.filter(a=>a.is_available&&a.id!==e).length||0;if(0===l&&i&&i.length>1)return{success:!1,error:"Cannot delete the last available variant. At least one variant must remain available."};if(t.images&&Array.isArray(t.images)&&t.images.length>0){let s="business";for(let r of(console.log(`Deleting ${t.images.length} images for variant ${e}`),t.images))if(r)try{let e=new URL(r).pathname.split("/"),t=e.findIndex(e=>"business"===e);if(-1!==t&&t<e.length-1){let r=e.slice(t+1).join("/").split("?")[0],{error:i}=await a.storage.from(s).remove([r]);i&&"The resource was not found"!==i.message?console.error(`Error deleting variant image: ${r}`,i):console.log(`Successfully deleted variant image: ${r}`)}}catch(e){console.error(`Error processing variant image URL for deletion: ${r}`,e)}try{let i=(0,_.getScalableUserPath)(r.id),n=`${i}/products/${t.product_id}/${e}`,{data:l,error:o}=await a.storage.from(s).list(n);if(!o&&l&&l.length>0){let e=l.map(e=>`${n}/${e.name}`),{error:r}=await a.storage.from(s).remove(e);r&&"The resource was not found"!==r.message?console.error(`Error deleting variant folder: ${n}`,r):console.log(`Successfully deleted variant folder: ${n}`)}}catch(a){console.error(`Error cleaning up variant folder for ${e}:`,a)}}let{error:o}=await a.from("product_variants").delete().eq("id",e);if(o){if(console.error("Error deleting variant:",o),o.message.includes("at least one variant must be available"))return{success:!1,error:"Cannot delete the last available variant."};return{success:!1,error:"Failed to delete variant."}}return(0,d.revalidatePath)("/dashboard/business/products"),(0,d.revalidatePath)(`/dashboard/business/products/${t.product_id}`),{success:!0}}catch(e){return console.error("Unexpected error in deleteProductVariant:",e),{success:!1,error:"An unexpected error occurred."}}}async function R(e){let a=await (0,n.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,error:"User not authenticated."};try{let t=0,s=0,i=[],{data:n,error:l}=await a.from("product_variants").select(`
        id,
        product_id,
        is_available,
        variant_name,
        images,
        products_services!inner(business_id)
      `).in("id",e);if(l)return{success:!1,error:"Failed to fetch variant information."};if(!n||0===n.length)return{success:!1,error:"No variants found."};if(n.filter(e=>e.products_services.business_id!==r.id).length>0)return{success:!1,error:"Access denied to some variants."};let o=n.reduce((e,a)=>(e[a.product_id]||(e[a.product_id]=[]),e[a.product_id].push(a),e),{});for(let[e,n]of Object.entries(o)){let{data:l,error:o}=await a.from("product_variants").select("id, is_available").eq("product_id",e);if(o){n.forEach(e=>{i.push(`Failed to validate deletion for variant ${e.variant_name}`),s++});continue}let d=l?.filter(e=>e.is_available).length||0,c=n.filter(e=>e.is_available).length,u=d-c;if(0===u&&l&&l.length>n.length){n.forEach(e=>{i.push(`Cannot delete variant ${e.variant_name}: would leave no available variants`),s++});continue}for(let e of n)try{if(e.images&&Array.isArray(e.images)&&e.images.length>0){let t="business";for(let r of e.images)if(r)try{let e=new URL(r).pathname.split("/"),s=e.findIndex(e=>"business"===e);if(-1!==s&&s<e.length-1){let r=e.slice(s+1).join("/").split("?")[0];await a.storage.from(t).remove([r])}}catch(a){console.error(`Error deleting image for variant ${e.variant_name}:`,a)}try{let s=(0,_.getScalableUserPath)(r.id),i=`${s}/products/${e.product_id}/${e.id}`,{data:n}=await a.storage.from(t).list(i);if(n&&n.length>0){let e=n.map(e=>`${i}/${e.name}`);await a.storage.from(t).remove(e)}}catch(a){console.error(`Error cleaning up variant folder for ${e.variant_name}:`,a)}}let{error:n}=await a.from("product_variants").delete().eq("id",e.id);n?(i.push(`Failed to delete variant ${e.variant_name}: ${n.message}`),s++):t++}catch(a){i.push(`Unexpected error deleting variant ${e.variant_name}`),s++}}return(0,d.revalidatePath)("/dashboard/business/products"),{success:t>0,deleted_count:t,failed_count:s,errors:i.length>0?i:void 0}}catch(e){return console.error("Unexpected error in deleteMultipleVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function D(e){let a=await (0,n.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,error:"User not authenticated."};try{let{data:t,error:s}=await a.from("products_services").select("id, business_id").eq("id",e).eq("business_id",r.id).single();if(s||!t)return{success:!1,error:"Product not found or access denied."};let{count:i,error:n}=await a.from("product_variants").select("*",{count:"exact",head:!0}).eq("product_id",e);n&&console.error("Error counting variants:",n);let{error:l}=await a.from("product_variants").delete().eq("product_id",e);if(l)return console.error("Error deleting product variants:",l),{success:!1,error:"Failed to delete product variants."};return{success:!0,deleted_count:i||0}}catch(e){return console.error("Unexpected error in deleteAllProductVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function O(e){let a=await (0,n.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,error:"User not authenticated."};try{let{data:t,error:s}=await a.rpc("get_product_with_variants",{product_uuid:e});if(s)return console.error("Error calling get_product_with_variants:",s),{success:!1,error:"Failed to fetch product data."};if(!t||0===t.length)return{success:!1,error:"Product not found."};let i=t[0],{data:n,error:l}=await a.from("products_services").select("business_id").eq("id",e).eq("business_id",r.id).single();if(l||!n)return{success:!1,error:"Product not found or access denied."};let o={id:i.product_id,business_id:r.id,product_type:"physical",name:i.product_name,description:i.product_description,base_price:i.product_base_price,discounted_price:i.product_discounted_price,is_available:i.product_is_available,images:i.product_images||[],featured_image_index:i.product_featured_image_index||0,slug:void 0,created_at:i.product_created_at||new Date().toISOString(),updated_at:i.product_updated_at||new Date().toISOString(),variant_count:Number(i.variant_count)||0,variants:Array.isArray(i.variants)?i.variants:[]};return{success:!0,data:o}}catch(e){return console.error("Unexpected error in getProductWithVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function F(e){let a=await (0,n.createClient)();try{let{data:r,error:t}=await a.rpc("get_available_product_variants",{product_uuid:e});if(t)return console.error("Error calling get_available_product_variants:",t),{success:!1,error:"Failed to fetch variant data."};return{success:!0,data:r||[]}}catch(e){return console.error("Unexpected error in getAvailableProductVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function T(e,a={}){let r=await (0,n.createClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return{success:!1,error:"User not authenticated."};try{let{data:s,error:i}=await r.from("products_services").select("business_id").eq("id",e).eq("business_id",t.id).single();if(i||!s)return{success:!1,error:"Product not found or access denied."};let n=r.from("product_variants").select("*",{count:"exact"}).eq("product_id",e);switch(!a.includeUnavailable&&(n=n.eq("is_available",!0)),a.sortBy){case"created_asc":n=n.order("created_at",{ascending:!0});break;case"created_desc":default:n=n.order("created_at",{ascending:!1});break;case"name_asc":n=n.order("variant_name",{ascending:!0});break;case"name_desc":n=n.order("variant_name",{ascending:!1});break;case"price_asc":n=n.order("base_price",{ascending:!0,nullsFirst:!1});break;case"price_desc":n=n.order("base_price",{ascending:!1,nullsFirst:!0})}a.limit&&(n=n.limit(a.limit)),a.offset&&(n=n.range(a.offset,a.offset+(a.limit||10)-1));let{data:l,error:o,count:d}=await n;if(o)return console.error("Error fetching variants:",o),{success:!1,error:"Failed to fetch variants."};let c=l?.map(e=>({...e,variant_values:"string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values}))||[];return{success:!0,data:c,count:d||0}}catch(e){return console.error("Unexpected error in getProductVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function L(){let e=await (0,n.createClient)(),{data:{user:a},error:r}=await e.auth.getUser();if(r||!a)return{success:!1,error:"User not authenticated."};try{let{data:r,error:t}=await e.rpc("get_business_variant_stats",{business_uuid:a.id});if(t)return console.error("Error calling get_business_variant_stats:",t),{success:!1,error:"Failed to fetch variant statistics."};if(!r||0===r.length)return{success:!0,data:{total_products:0,products_with_variants:0,total_variants:0,available_variants:0}};let s=r[0];return{success:!0,data:{total_products:Number(s.total_products)||0,products_with_variants:Number(s.products_with_variants)||0,total_variants:Number(s.total_variants)||0,available_variants:Number(s.available_variants)||0}}}catch(e){return console.error("Unexpected error in getBusinessVariantStats:",e),{success:!1,error:"An unexpected error occurred."}}}async function V(e,a,r){let t=await (0,n.createClient)(),{data:{user:s},error:i}=await t.auth.getUser();if(i||!s)return{success:!1,error:"User not authenticated."};try{let{data:i,error:n}=await t.from("products_services").select("business_id").eq("id",e).eq("business_id",s.id).single();if(n||!i)return{success:!1,error:"Product not found or access denied."};let{data:l,error:o}=await t.rpc("is_variant_combination_unique",{product_uuid:e,variant_vals:a,exclude_variant_id:r||void 0});if(o)return console.error("Error calling is_variant_combination_unique:",o),{success:!1,error:"Failed to validate variant uniqueness."};return{success:!0,isUnique:l}}catch(e){return console.error("Unexpected error in checkVariantCombinationUnique:",e),{success:!1,error:"An unexpected error occurred."}}}(0,l.D)([A,E,U]),(0,i.A)(A,"40e0b77a8dc6165a7703d56966ab44b8bb7215bc26",null),(0,i.A)(E,"605db269f98f874bac1f27e3d6b4e08414266e3ac8",null),(0,i.A)(U,"601694daaf4792dcf464368d8457fcd1259c923cf2",null),(0,l.D)([P,C]),(0,i.A)(P,"604381af5c96732d294fa7a1b7bd35fc6aa1a9781b",null),(0,i.A)(C,"4055b8f6815995dd3ae4b7db159041d6254736584f",null),(0,l.D)([$,R,D]),(0,i.A)($,"40f582f4b6156ae356636c0f4aa3c00d2946c8f64b",null),(0,i.A)(R,"405fdaee2ff8ff13e45b4811c849ef0b8a06eea405",null),(0,i.A)(D,"406284e9ef6a2be8ebf3ce5826f7572619ff00844e",null),(0,l.D)([O,F,T,L,V]),(0,i.A)(O,"40e15bace5efcde3c132186e902abe40403e1fa4ff",null),(0,i.A)(F,"403a75d9205d3ac636b80de9ab8dbe80951e5d71b9",null),(0,i.A)(T,"602335d5c6ca713548cda36b584c6afe685e032e60",null),(0,i.A)(L,"00029e81b58a5a97fdfca38c6b984b28c00ce6cc90",null),(0,i.A)(V,"70ffeec93e6ac09c9ae41698e2e6418acc4707a28d",null)},12597:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15616:(e,a,r)=>{r.d(a,{T:()=>i});var t=r(60687);r(43210);var s=r(96241);function i({className:e,...a}){return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...a})}},28559:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},33135:(e,a,r)=>{r.d(a,{AM:()=>n,Wv:()=>l,hl:()=>o});var t=r(60687);r(43210);var s=r(52676),i=r(96241);function n({...e}){return(0,t.jsx)(s.bL,{"data-slot":"popover",...e})}function l({...e}){return(0,t.jsx)(s.l9,{"data-slot":"popover-trigger",...e})}function o({className:e,align:a="center",sideOffset:r=4,...n}){return(0,t.jsx)(s.ZL,{children:(0,t.jsx)(s.UC,{"data-slot":"popover-content",align:a,sideOffset:r,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...n})})}},35157:(e,a,r)=>{r.d(a,{CG:()=>t,SC:()=>s,cZ:()=>i});let t={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},s={BUSINESS:"business",CUSTOMERS:"customers"},i={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},40211:(e,a,r)=>{r.d(a,{C1:()=>k,bL:()=>N});var t=r(43210),s=r(98599),i=r(11273),n=r(70569),l=r(65551),o=r(83721),d=r(18853),c=r(46059),u=r(3416),p=r(60687),m="Checkbox",[g,h]=(0,i.A)(m),[f,v]=g(m),b=t.forwardRef((e,a)=>{let{__scopeCheckbox:r,name:i,checked:o,defaultChecked:d,required:c,disabled:g,value:h="on",onCheckedChange:v,form:b,...x}=e,[_,N]=t.useState(null),k=(0,s.s)(a,e=>N(e)),I=t.useRef(!1),S=!_||b||!!_.closest("form"),[A,E]=(0,l.i)({prop:o,defaultProp:d??!1,onChange:v,caller:m}),U=t.useRef(A);return t.useEffect(()=>{let e=_?.form;if(e){let a=()=>E(U.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[_,E]),(0,p.jsxs)(f,{scope:r,state:A,disabled:g,children:[(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":j(A)?"mixed":A,"aria-required":c,"data-state":w(A),"data-disabled":g?"":void 0,disabled:g,value:h,...x,ref:k,onKeyDown:(0,n.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.m)(e.onClick,e=>{E(e=>!!j(e)||!e),S&&(I.current=e.isPropagationStopped(),I.current||e.stopPropagation())})}),S&&(0,p.jsx)(y,{control:_,bubbles:!I.current,name:i,value:h,checked:A,required:c,disabled:g,form:b,style:{transform:"translateX(-100%)"},defaultChecked:!j(d)&&d})]})});b.displayName=m;var x="CheckboxIndicator",_=t.forwardRef((e,a)=>{let{__scopeCheckbox:r,forceMount:t,...s}=e,i=v(x,r);return(0,p.jsx)(c.C,{present:t||j(i.state)||!0===i.state,children:(0,p.jsx)(u.sG.span,{"data-state":w(i.state),"data-disabled":i.disabled?"":void 0,...s,ref:a,style:{pointerEvents:"none",...e.style}})})});_.displayName=x;var y=t.forwardRef(({__scopeCheckbox:e,control:a,checked:r,bubbles:i=!0,defaultChecked:n,...l},c)=>{let m=t.useRef(null),g=(0,s.s)(m,c),h=(0,o.Z)(r),f=(0,d.X)(a);t.useEffect(()=>{let e=m.current;if(!e)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==r&&a){let t=new Event("click",{bubbles:i});e.indeterminate=j(r),a.call(e,!j(r)&&r),e.dispatchEvent(t)}},[h,r,i]);let v=t.useRef(!j(r)&&r);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:n??v.current,...l,tabIndex:-1,ref:g,style:{...l.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return"indeterminate"===e}function w(e){return j(e)?"indeterminate":e?"checked":"unchecked"}y.displayName="CheckboxBubbleInput";var N=b,k=_},40214:(e,a,r)=>{r.d(a,{d:()=>w});var t=r(60687),s=r(43210),i=r(70569),n=r(98599),l=r(11273),o=r(65551),d=r(83721),c=r(18853),u=r(3416),p="Switch",[m,g]=(0,l.A)(p),[h,f]=m(p),v=s.forwardRef((e,a)=>{let{__scopeSwitch:r,name:l,checked:d,defaultChecked:c,required:m,disabled:g,value:f="on",onCheckedChange:v,form:b,...x}=e,[j,w]=s.useState(null),N=(0,n.s)(a,e=>w(e)),k=s.useRef(!1),I=!j||b||!!j.closest("form"),[S,A]=(0,o.i)({prop:d,defaultProp:c??!1,onChange:v,caller:p});return(0,t.jsxs)(h,{scope:r,checked:S,disabled:g,children:[(0,t.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":m,"data-state":y(S),"data-disabled":g?"":void 0,disabled:g,value:f,...x,ref:N,onClick:(0,i.m)(e.onClick,e=>{A(e=>!e),I&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),I&&(0,t.jsx)(_,{control:j,bubbles:!k.current,name:l,value:f,checked:S,required:m,disabled:g,form:b,style:{transform:"translateX(-100%)"}})]})});v.displayName=p;var b="SwitchThumb",x=s.forwardRef((e,a)=>{let{__scopeSwitch:r,...s}=e,i=f(b,r);return(0,t.jsx)(u.sG.span,{"data-state":y(i.checked),"data-disabled":i.disabled?"":void 0,...s,ref:a})});x.displayName=b;var _=s.forwardRef(({__scopeSwitch:e,control:a,checked:r,bubbles:i=!0,...l},o)=>{let u=s.useRef(null),p=(0,n.s)(u,o),m=(0,d.Z)(r),g=(0,c.X)(a);return s.useEffect(()=>{let e=u.current;if(!e)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==r&&a){let t=new Event("click",{bubbles:i});a.call(e,r),e.dispatchEvent(t)}},[m,r,i]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...l,tabIndex:-1,ref:p,style:{...l.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}_.displayName="SwitchBubbleInput";var j=r(96241);function w({className:e,...a}){return(0,t.jsx)(v,{"data-slot":"switch",className:(0,j.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(x,{"data-slot":"switch-thumb",className:(0,j.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},41974:(e,a,r)=>{r.d(a,{A:()=>X});var t=r(60687),s=r(27605),i=r(63442),n=r(45880),l=r(77882),o=r(19080),d=r(41862),c=r(24934),u=r(58164),p=r(68988),m=r(15616),g=r(40214),h=r(63974),f=r(43210),v=r(52581),b=r(28640);function x({initialImageUrls:e=null,initialFeaturedIndex:a=0,maxImages:r=5}={}){let[t,s]=(0,f.useState)("idle"),[i,n]=(0,f.useState)(null),[l,o]=(0,f.useState)([]),d=e?.map((e,a)=>({id:`existing-${a}`,file:null,previewUrl:e,isUploading:!1,isNew:!1,originalIndex:a}))||[{id:`featured-${Date.now()}`,file:null,previewUrl:"",isUploading:!1,isNew:!0}],[c,u]=(0,f.useState)(d),[p,m]=(0,f.useState)(a<d.length?a:0),[g,h]=(0,f.useState)(null),_=async e=>{if(!g)return void h(null);let{targetIndex:a,originalFile:r}=g;if(h(null),e)try{let t=r?.name||`product-image-${Date.now()}.jpg`,i=new File([e],t,{type:"image/png",lastModified:Date.now()}),n=await (0,b.compressImageUltraAggressiveClient)(i,{maxDimension:800,targetSizeKB:100}),l=new File([n.blob],t,{type:n.blob.type}),o=URL.createObjectURL(l);u(e=>{let r=[...e];return r[a]?.previewUrl&&r[a].isNew&&URL.revokeObjectURL(r[a].previewUrl),r[a]={...r[a],file:l,previewUrl:o,isNew:!0},r}),s("success")}catch(e){console.error("Error processing cropped image:",e),v.oR.error("Failed to process cropped image"),s("error"),n("Failed to process cropped image")}};return{images:c,featuredImageIndex:p,removedOriginalIndices:l,imageToCrop:g,imageUploadStatus:t,imageUploadError:i,imageErrorDisplay:"error"===t&&i?i:null,addImageSlot:()=>{if(c.length>=r)return void v.oR.error(`Maximum of ${r} images allowed`);u(e=>[...e,{id:`new-${Date.now()}`,file:null,previewUrl:"",isUploading:!1,isNew:!0}])},removeImage:e=>{u(a=>{let r=[...a],t=r[e];return(t?.originalIndex!==void 0&&o(e=>[...e,t.originalIndex]),r.length<=1)?r[0]?.previewUrl&&(r[0].previewUrl&&r[0].isNew&&URL.revokeObjectURL(r[0].previewUrl),r[0]={id:`featured-${Date.now()}`,file:null,previewUrl:"",isUploading:!1,isNew:!0},m(0)):(e===p?r.length>1&&m(0):e<p&&m(p-1),r[e].previewUrl&&r[e].isNew&&URL.revokeObjectURL(r[e].previewUrl),r.splice(e,1)),r})},setAsFeatured:e=>{e>=0&&e<c.length&&m(e)},handleFileSelect:async(e,a)=>{try{if(!["image/jpeg","image/png","image/webp","image/gif"].includes(e.type)){v.oR.error("Please select a valid image file (JPG, PNG, WebP, or GIF)."),s("error"),n("Please select a valid image file (JPG, PNG, WebP, or GIF).");return}if(e.size>0xf00000){v.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}),s("error"),n("Image is too large. Please select a smaller image (max 15MB).");return}h({dataUrl:URL.createObjectURL(e),originalFile:e,targetIndex:a})}catch(e){console.error("Error processing image:",e),v.oR.error("Failed to process image. Please try again."),s("error"),n("Failed to process image. Please try again.")}},handleCropComplete:_,handleCropDialogClose:()=>{g?.dataUrl&&URL.revokeObjectURL(g.dataUrl),h(null)}}}var _=r(47473),y=r(37826),j=r(58709);let w=e=>new Promise((a,r)=>{let t=new Image;t.addEventListener("load",()=>a(t)),t.addEventListener("error",e=>r(e)),t.setAttribute("crossOrigin","anonymous"),t.src=e});async function N(e,a){let r=await w(e),t=document.createElement("canvas"),s=t.getContext("2d");if(!s)return null;let i=r.naturalWidth/r.width,n=r.naturalHeight/r.height,l=window.devicePixelRatio||1;return t.width=a.width*l*i,t.height=a.height*l*n,s.setTransform(l,0,0,l,0,0),s.imageSmoothingQuality="high",s.drawImage(r,a.x*i,a.y*n,a.width*i,a.height*n,0,0,a.width*i,a.height*n),new Promise(e=>{t.toBlob(e,"image/png")})}function k({imgSrc:e,onCropComplete:a,onClose:r,isOpen:s}){let[i,n]=(0,f.useState)({x:0,y:0}),[l,o]=(0,f.useState)(1),[u,p]=(0,f.useState)(null),[m,g]=(0,f.useState)(!1),h=(0,f.useCallback)((e,a)=>{p(a)},[]),v=async()=>{if(!e||!u){console.warn("Image source or crop area not available."),a(null);return}g(!0);try{let r=await N(e,u);a(r)}catch(e){console.error("Error cropping image:",e),a(null)}finally{g(!1)}};return(0,t.jsx)(y.lG,{open:s,onOpenChange:e=>!e&&r(),children:(0,t.jsxs)(y.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsx)(y.c7,{children:(0,t.jsx)(y.L3,{children:"Crop Your Product Image"})}),(0,t.jsx)("div",{className:"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800",children:e?(0,t.jsx)(_.Ay,{image:e,crop:i,zoom:l,aspect:1,cropShape:"rect",showGrid:!0,onCropChange:n,onZoomChange:o,onCropComplete:h}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsx)("p",{children:"Loading image..."})})}),(0,t.jsx)("div",{className:"px-4 pb-4",children:(0,t.jsx)(j.A,{min:1,max:3,step:.1,value:[l],onValueChange:e=>o(e[0]),className:"w-full","aria-label":"Zoom slider"})}),(0,t.jsxs)(y.Es,{children:[(0,t.jsx)(c.$,{variant:"outline",onClick:r,disabled:m,children:"Cancel"}),(0,t.jsxs)(c.$,{onClick:v,disabled:m,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[m?(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Crop Image"]})]})]})})}var I=r(30474),S=r(96474),A=r(9005),E=r(64398),U=r(88233),P=r(96241),C=r(88920);function $({images:e,featuredImageIndex:a,disabled:r=!1,maxImages:s=5,onAddImage:i,onRemoveImage:n,onSetFeatured:o,onFileSelect:d,errorDisplay:m}){let[g,h]=(0,f.useState)(null),[b,x]=(0,f.useState)(!1),[_,y]=(0,f.useState)(null),j=(e,a)=>{let r=e.target.files?.[0];r&&d(r,a),e.target.value=""},w=e=>{if(r)return;let a=document.getElementById(`product-image-${e}`);a&&a.click()},N=(e,a)=>{e.preventDefault(),e.stopPropagation(),x(!0),"number"==typeof a&&y(a)},k=e=>{e.preventDefault(),e.stopPropagation(),e.currentTarget.contains(e.relatedTarget)||(x(!1),y(null))},$=(e,a)=>{e.preventDefault(),e.stopPropagation(),b||x(!0),"number"==typeof a&&y(a)},R=(e,a)=>{if(e.preventDefault(),e.stopPropagation(),x(!1),y(null),r)return;let t=e.dataTransfer.files;if(t.length>0){let e=t[0];if(!["image/jpeg","image/png","image/webp","image/gif"].includes(e.type))return void v.oR.error("Please drop a valid image file (JPG, PNG, WebP, or GIF).");if(e.size>0xf00000)return void v.oR.error("Image too large",{description:"Please select an image smaller than 15MB"});d(e,a)}},D={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}},exit:{opacity:0,scale:.8,transition:{duration:.2}}},O={rest:{scale:1},hover:{scale:1.1},tap:{scale:.95}};return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsxs)("div",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 flex justify-between items-center",children:[(0,t.jsxs)("span",{children:["Product Images (",e.length,"/",s,")"]}),(0,t.jsx)("div",{className:"flex gap-2",children:e.length<s&&(0,t.jsx)(l.P.div,{initial:"rest",whileHover:"hover",whileTap:"tap",variants:O,children:(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:i,disabled:r||e.length>=s,className:"h-8 px-2 text-xs",children:[(0,t.jsx)(S.A,{className:"h-3.5 w-3.5 mr-1"}),"Add Image"]})})})]}),(0,t.jsx)(l.P.div,{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 md:gap-4",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.08,delayChildren:.1}}},initial:"hidden",animate:"visible",children:(0,t.jsxs)(C.N,{mode:"popLayout",children:[e.map((s,i)=>{let d=i===a,u=g===i,m=_===i;return(0,t.jsx)(l.P.div,{layout:!0,variants:D,exit:"exit",onHoverStart:()=>h(i),onHoverEnd:()=>h(null),onDragEnter:e=>N(e,i),onDragLeave:k,onDragOver:e=>$(e,i),onDrop:e=>R(e,i),onClick:()=>w(i),className:(0,P.cn)("relative border rounded-lg overflow-hidden group transition-all duration-200 cursor-pointer",d?"border-amber-500 dark:border-amber-400 border-2 col-span-2 sm:col-span-1 md:col-span-1 order-first":"border-neutral-200 dark:border-neutral-700",m&&"border-amber-500 dark:border-amber-400 border-2 bg-amber-50 dark:bg-amber-950/20",!r&&"hover:border-amber-400 dark:hover:border-amber-500"),children:(0,t.jsxs)("div",{className:(0,P.cn)("aspect-square bg-neutral-100 dark:bg-neutral-800 relative transition-all duration-200",m&&"bg-amber-50 dark:bg-amber-950/20"),children:[s.previewUrl?(0,t.jsx)("div",{className:"w-full h-full",children:(0,t.jsx)(I.default,{src:s.previewUrl,alt:`Product image ${i+1}`,fill:!0,className:"object-cover"})}):(0,t.jsxs)("div",{className:"w-full h-full flex flex-col items-center justify-center text-neutral-400 dark:text-neutral-500 p-2",children:[(0,t.jsx)(l.P.div,{animate:{rotate:10*!!u,scale:m?1.1:1},transition:{duration:.3},children:(0,t.jsx)(A.A,{className:(0,P.cn)("mb-1 transition-colors duration-200",d?"h-10 w-10":"h-8 w-8",m&&"text-amber-500")})}),(0,t.jsx)("span",{className:(0,P.cn)("text-center transition-colors duration-200",d?"text-sm":"text-xs",m&&"text-amber-500"),children:m?"Drop image here":d?"Featured Image":`Add image ${i+1}`}),!m&&(0,t.jsx)("span",{className:(0,P.cn)("text-center mt-1 opacity-60",d?"text-xs":"text-[10px]"),children:"Click or drag & drop"})]}),d&&(0,t.jsx)(l.P.div,{className:"absolute top-2 left-2 bg-amber-500 text-white rounded-full p-1.5",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:500,damping:15},children:(0,t.jsx)(E.A,{className:"h-4 w-4"})}),(s.previewUrl||e.length>1)&&(0,t.jsx)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:+!!u,scale:u?1:.8},transition:{duration:.2},className:"absolute top-2 right-2 z-10 md:opacity-0 md:group-hover:opacity-100",children:(0,t.jsx)(l.P.div,{variants:O,initial:"rest",whileHover:"hover",whileTap:"tap",children:(0,t.jsx)(c.$,{type:"button",variant:"destructive",size:"icon",onClick:e=>{e.stopPropagation(),n(i)},disabled:r,className:(0,P.cn)("rounded-full",d?"h-7 w-7":"h-6 w-6"),children:(0,t.jsx)(U.A,{className:d?"h-3.5 w-3.5":"h-3 w-3"})})})}),!d&&s.previewUrl&&(0,t.jsx)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:+!!u,scale:u?1:.8},transition:{duration:.2},className:"absolute top-2 left-2 z-10 md:opacity-0 md:group-hover:opacity-100",children:(0,t.jsx)(l.P.div,{variants:O,initial:"rest",whileHover:"hover",whileTap:"tap",children:(0,t.jsx)(c.$,{type:"button",variant:"outline",size:"icon",onClick:e=>{e.stopPropagation(),o(i)},disabled:r,className:"h-6 w-6 rounded-full bg-amber-500 text-white hover:bg-amber-600 border-none",children:(0,t.jsx)(E.A,{className:"h-3 w-3"})})})}),(0,t.jsx)(p.p,{id:`product-image-${i}`,type:"file",accept:"image/jpeg,image/png,image/webp,image/gif",onChange:e=>j(e,i),className:"hidden",disabled:r})]})},s.id)}),e.length<s&&e.some(e=>e.previewUrl)&&(0,t.jsxs)(l.P.button,{variants:D,whileHover:{scale:1.05,borderColor:"var(--amber-500)"},whileTap:{scale:.95},type:"button",onClick:i,disabled:r,className:(0,P.cn)("aspect-square border-2 border-dashed rounded-lg flex flex-col items-center justify-center transition-colors","border-neutral-200 dark:border-neutral-700 text-neutral-400 dark:text-neutral-500","hover:border-amber-500 dark:hover:border-amber-500"),children:[(0,t.jsx)(l.P.div,{animate:{y:[0,-5,0]},transition:{repeat:1/0,repeatType:"loop",duration:2,repeatDelay:1},children:(0,t.jsx)(S.A,{className:"h-8 w-8 mb-1"})}),(0,t.jsx)("span",{className:"text-xs",children:"Add Image"})]},"add-button")]})})]}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,t.jsxs)(u.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400",children:["Upload up to ",s," images (JPG, PNG, WebP, or GIF, max 15MB each). Click on any image card or drag & drop files to upload. Images will be automatically cropped to square (1:1) aspect ratio. Select one image as featured to display in listings."]}),m&&(0,t.jsx)(l.P.p,{initial:{opacity:0},animate:{opacity:1},className:"text-xs text-red-500 dark:text-red-400 mt-1",children:m})]})]})}var R=r(8839),D=r(39390),O=r(3018),F=r(43649),T=r(37360),L=r(11860),V=r(71463),q=r(37507),M=r(4331),B=r(33135),z=r(17971),G=r(13964),Y=r(38632);function J({productId:e,initialData:a,onSubmit:r,isSubmitting:n,onCancel:u}){let[m,h]=(0,f.useState)(!0),[b,_]=(0,f.useState)(a?.variant_values||{}),[y,j]=(0,f.useState)([]),[w,N]=(0,f.useState)(!1),I=!!a,[A,E]=(0,f.useState)(null),[U,R]=(0,f.useState)(null),{images:V,featuredImageIndex:J,removedOriginalIndices:H,imageToCrop:Z,imageErrorDisplay:X,addImageSlot:K,removeImage:Q,setAsFeatured:ee,handleFileSelect:ea,handleCropComplete:er,handleCropDialogClose:et}=x({initialImageUrls:a?.images||null,initialFeaturedIndex:a?.featured_image_index||0,maxImages:5}),es=(0,s.mN)({resolver:(0,i.u)(q.sX),defaultValues:{product_id:e,variant_name:a?.variant_name||"",variant_values:a?.variant_values||{},base_price:a?.base_price||void 0,discounted_price:a?.discounted_price||void 0,is_available:a?.is_available??!0,images:a?.images||[],featured_image_index:a?.featured_image_index||0}}),ei=e=>{let a=[];Object.entries(e).forEach(([e,r])=>{e.trim()||a.push("Variant type names cannot be empty"),r.trim()||a.push("Variant values cannot be empty"),e.trim().length>50&&a.push("Variant type names cannot exceed 50 characters"),r.trim().length>50&&a.push("Variant values cannot exceed 50 characters")});let r=Object.keys(e).map(e=>e.trim().toLowerCase()),t=new Set(r);return r.length!==t.size&&a.push("Duplicate variant type names are not allowed"),0===Object.keys(e).length&&a.push("At least one variant property is required"),[...new Set(a)]},en=(e,a)=>{let r=[];return void 0!==e&&e<=0&&r.push("Base price must be greater than 0"),void 0!==a&&a<=0&&r.push("Discounted price must be greater than 0"),e&&a&&a>=e&&r.push("Discounted price must be less than base price"),r},el=async e=>{try{N(!0);let t=ei(e.variant_values||{}),s=en(e.base_price||void 0,e.discounted_price||void 0),i=[...t,...s];if(i.length>0){j(i),N(!1),v.oR.error("Please fix the validation errors before submitting");return}j([]);let n=V.map(e=>e.file),l=I&&a?.id?{...e,id:a.id}:e;await r(l,n,J,H,V),j([]),N(!1)}catch(e){console.error("Error submitting variant form:",e),N(!1),v.oR.error("Failed to save variant. Please try again.")}},eo=e=>{_(a=>{let r={...a};return delete r[e],r})},ed=(e,a)=>{if(e===a)return;let r=a.trim();return r.length>50?void v.oR.error("Variant type name cannot exceed 50 characters"):Object.keys(b).filter(a=>a!==e).map(e=>e.trim().toLowerCase()).includes(r.toLowerCase())?void v.oR.error("Variant type name already exists"):void _(a=>{let t={...a},s=t[e];return delete t[e],t[r]=s,t})},ec=(e,a)=>{let r=a.trim();if(r.length>50)return void v.oR.error("Variant value cannot exceed 50 characters");_(a=>({...a,[e]:r}))};return m?(0,t.jsx)(W,{}):(0,t.jsxs)("div",{className:"space-y-6",children:[y.length>0&&(0,t.jsxs)(O.Fc,{variant:"destructive",children:[(0,t.jsx)(F.A,{className:"h-4 w-4"}),(0,t.jsx)(O.TN,{children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:"Please fix the following errors:"}),(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1",children:y.map((e,a)=>(0,t.jsx)("li",{className:"text-sm",children:e},a))})]})})]}),(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(C.N,{mode:"wait",children:[(0,t.jsx)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Variant Name"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(T.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400"}),(0,t.jsx)(p.p,{placeholder:"e.g., Red Large, 64GB Blue, Cotton Medium",className:"pl-10",value:es.watch("variant_name")||"",onChange:e=>es.setValue("variant_name",e.target.value)})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"A descriptive name for this variant combination"})]})},"variant-name"),(0,t.jsx)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.2},children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Variant Properties"}),(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{if(Object.keys(b).length>=5)return void v.oR.error("Maximum of 5 variant types allowed per product");let e=Object.keys(b).map(e=>e.toLowerCase()),a=(0,Y.MJ)().find(a=>!e.includes(a.name.toLowerCase())),r=a?a.name:`type_${Object.keys(b).length+1}`;_(e=>({...e,[r]:""}))},disabled:Object.keys(b).length>=5,className:"h-8 px-2 text-xs",children:[(0,t.jsx)(S.A,{className:"h-3.5 w-3.5 mr-1"}),"Add Property"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[Object.entries(b).map(([e,a])=>{let r=(0,Y.Ou)(e);return(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"flex gap-2 items-center",children:[(0,t.jsxs)(B.AM,{open:A===e,onOpenChange:a=>E(a?e:null),children:[(0,t.jsx)(B.Wv,{asChild:!0,children:(0,t.jsxs)(c.$,{variant:"outline",role:"combobox","aria-expanded":A===e,className:"flex-1 justify-between bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900",children:[e?(0,Y.MJ)().find(a=>a.name===e)?.display_name||e:"Select type...",(0,t.jsx)(z.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,t.jsx)(B.hl,{className:"w-[300px] p-0 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700",children:(0,t.jsxs)(M.uB,{className:"bg-white dark:bg-black",children:[(0,t.jsx)(M.G7,{placeholder:"Search variant type...",className:"h-9 bg-white dark:bg-black border-none focus:ring-0 focus:ring-offset-0 focus:border-transparent focus:outline-none"}),(0,t.jsxs)(M.oI,{className:"max-h-[200px] bg-white dark:bg-black",children:[(0,t.jsx)(M.xL,{className:"bg-white dark:bg-black",children:"No variant type found."}),(0,t.jsx)(M.L$,{className:"bg-white dark:bg-black",children:(0,Y.MJ)().map(a=>(0,t.jsxs)(M.h_,{value:a.name,onSelect:a=>{ed(e,a),E(null)},className:"bg-white dark:bg-black hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer",children:[(0,t.jsxs)("div",{className:"flex flex-col flex-1",children:[(0,t.jsx)("span",{className:"font-medium",children:a.display_name}),a.description&&(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:a.description})]}),(0,t.jsx)(G.A,{className:(0,P.cn)("ml-auto h-4 w-4",e===a.name?"opacity-100":"opacity-0")})]},a.name))})]})]})})]}),"color"===e.toLowerCase()&&r.length>0?(0,t.jsxs)(B.AM,{open:U===e,onOpenChange:a=>R(a?e:null),children:[(0,t.jsx)(B.Wv,{asChild:!0,children:(0,t.jsxs)(c.$,{variant:"outline",role:"combobox","aria-expanded":U===e,className:"flex-1 justify-between bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900",children:[a?r.find(e=>e.value===a)?.display_value||a:"Select color...",(0,t.jsx)(z.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,t.jsx)(B.hl,{className:"w-[300px] p-0 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700",children:(0,t.jsxs)(M.uB,{className:"bg-white dark:bg-black",children:[(0,t.jsx)(M.G7,{placeholder:"Search color...",className:"h-9 bg-white dark:bg-black border-none focus:ring-0 focus:ring-offset-0 focus:border-transparent focus:outline-none"}),(0,t.jsxs)(M.oI,{className:"max-h-[200px] bg-white dark:bg-black",children:[(0,t.jsx)(M.xL,{className:"bg-white dark:bg-black",children:"No color found."}),(0,t.jsx)(M.L$,{className:"bg-white dark:bg-black",children:r.map(r=>(0,t.jsxs)(M.h_,{value:r.value,onSelect:a=>{ec(e,a),R(null)},className:"bg-white dark:bg-black hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[r.color_code&&(0,t.jsx)("div",{className:"w-4 h-4 rounded-full border border-neutral-300",style:{backgroundColor:r.color_code}}),(0,t.jsx)("span",{children:r.display_value})]}),(0,t.jsx)(G.A,{className:(0,P.cn)("ml-auto h-4 w-4",a===r.value?"opacity-100":"opacity-0")})]},r.value))})]})]})})]}):(0,t.jsx)(p.p,{type:"text",placeholder:`Enter ${e.toLowerCase()}...`,value:a||"",onChange:a=>ec(e,a.target.value),className:"flex-1 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700"}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>eo(e),className:"h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]},e)}),0===Object.keys(b).length&&(0,t.jsx)("div",{className:"text-sm text-neutral-500 text-center py-4 border-2 border-dashed border-neutral-200 dark:border-neutral-700 rounded-lg",children:'No variant properties added yet. Click "Add Property" to start.'})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add up to 5 variant properties (e.g., color: red, size: large)"})]})},"variant-values"),(0,t.jsxs)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3},className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Base Price (₹)"}),(0,t.jsx)(p.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",value:es.watch("base_price")||"",onChange:e=>es.setValue("base_price",e.target.value?parseFloat(e.target.value):void 0)}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Leave empty to use product's base price"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Discounted Price (₹)"}),(0,t.jsx)(p.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",value:es.watch("discounted_price")||"",onChange:e=>es.setValue("discounted_price",e.target.value?parseFloat(e.target.value):void 0)}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Optional discounted price for this variant"})]})]},"pricing"),(0,t.jsx)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},children:(0,t.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(D.J,{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Available for Purchase"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Toggle to make this variant available to customers"})]}),(0,t.jsx)(g.d,{checked:es.watch("is_available")||!1,onCheckedChange:e=>es.setValue("is_available",e),className:"data-[state=checked]:bg-primary"})]})},"availability"),(0,t.jsx)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.5},children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Variant Images"}),(0,t.jsx)($,{images:V,featuredImageIndex:J,disabled:n,maxImages:5,onAddImage:K,onRemoveImage:Q,onSetFeatured:ee,onFileSelect:ea,errorDisplay:X}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add images specific to this variant. If no images are added, the product's main images will be used."})]})},"images")]}),(0,t.jsxs)("div",{className:"flex gap-3 pt-4 border-t",children:[u&&(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:u,disabled:n,className:"flex-1",children:"Cancel"}),(0,t.jsx)(c.$,{type:"button",disabled:n||w||0===Object.keys(b).length||y.length>0,className:"flex-1 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",onClick:()=>{el(es.getValues())},children:n||w?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),w?"Validating...":I?"Updating...":"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4"}),I?"Update Variant":"Create Variant"]})})]})]})}),(0,t.jsx)(k,{imgSrc:Z?.dataUrl||null,onCropComplete:er,onClose:et,isOpen:!!Z})]})}function W(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(V.E,{className:"h-5 w-24"}),(0,t.jsx)(V.E,{className:"h-10 w-full rounded-md"}),(0,t.jsx)(V.E,{className:"h-3 w-48"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(V.E,{className:"h-5 w-32"}),(0,t.jsx)(V.E,{className:"h-8 w-24 rounded-md"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(V.E,{className:"h-10 flex-1 rounded-md"}),(0,t.jsx)(V.E,{className:"h-10 flex-1 rounded-md"}),(0,t.jsx)(V.E,{className:"h-8 w-8 rounded-md"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(V.E,{className:"h-10 flex-1 rounded-md"}),(0,t.jsx)(V.E,{className:"h-10 flex-1 rounded-md"}),(0,t.jsx)(V.E,{className:"h-8 w-8 rounded-md"})]})]}),(0,t.jsx)(V.E,{className:"h-3 w-64"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(V.E,{className:"h-5 w-20"}),(0,t.jsx)(V.E,{className:"h-10 w-full rounded-md"}),(0,t.jsx)(V.E,{className:"h-3 w-40"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(V.E,{className:"h-5 w-28"}),(0,t.jsx)(V.E,{className:"h-10 w-full rounded-md"}),(0,t.jsx)(V.E,{className:"h-3 w-44"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(V.E,{className:"h-5 w-32"}),(0,t.jsx)(V.E,{className:"h-3 w-56"})]}),(0,t.jsx)(V.E,{className:"h-6 w-12 rounded-full"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(V.E,{className:"h-5 w-24"}),(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[(0,t.jsx)(V.E,{className:"h-32 w-32 rounded-lg"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(V.E,{className:"h-10 w-full rounded-md"}),(0,t.jsx)(V.E,{className:"h-3 w-full mt-2"})]})]}),(0,t.jsx)(V.E,{className:"h-3 w-80"})]}),(0,t.jsxs)("div",{className:"flex gap-3 pt-4 border-t",children:[(0,t.jsx)(V.E,{className:"h-10 flex-1 rounded-md"}),(0,t.jsx)(V.E,{className:"h-10 flex-1 rounded-md"})]})]})}var H=r(84027);let Z=n.Ik({product_type:n.k5(["physical","service"],{required_error:"Please select a product type."}).default("physical"),name:n.Yj().min(1,{message:"Product/Service name is required."}).max(100,{message:"Name cannot exceed 100 characters."}),description:n.Yj().max(500,{message:"Description cannot exceed 500 characters."}).optional().or(n.eu("")),base_price:n.au.number({required_error:"Base price is required.",invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}),discounted_price:n.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:n.zM().default(!0)}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]});function X({initialData:e,initialVariants:a,onSubmit:r,isSubmitting:n,isEditing:b,planLimit:_,currentAvailableCount:y}){let[j,w]=(0,f.useState)(a||[]),[N,I]=(0,f.useState)(!1),[A,E]=(0,f.useState)(null),[U,P]=(0,f.useState)(!1),C={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},{images:D,featuredImageIndex:O,imageToCrop:F,imageErrorDisplay:T,addImageSlot:V,removeImage:q,setAsFeatured:M,handleFileSelect:B,handleCropComplete:z,handleCropDialogClose:G}=x({initialImageUrls:e?.images||(e?.image_url?[e.image_url]:null),initialFeaturedIndex:e?.featured_image_index||0,maxImages:5}),Y=(0,s.mN)({resolver:(0,i.u)(Z),mode:"onChange",defaultValues:{product_type:e?.product_type??"physical",name:e?.name??"",description:e?.description??"",base_price:e?.base_price!==void 0?e.base_price:void 0,discounted_price:e?.discounted_price!==void 0?e.discounted_price:null,is_available:e?.is_available??!0}}),W=()=>{E(null),I(!0)},X=()=>{I(!1),E(null)},K=async(a,r,t,s,i)=>{try{let n=r?.filter(e=>null!==e)||[];if(A){let e=[];if(i&&i.length>0)e=i.map(e=>e.previewUrl).filter(e=>""!==e);else{let a=A.images||[];if(e=s&&s.length>0?a.filter((e,a)=>!s.includes(a)):[...a],n.length>0){let a=n.map(e=>URL.createObjectURL(e));e.push(...a)}}w(r=>r.map(r=>r.id===A.id?{...r,variant_name:a.variant_name||"",variant_values:a.variant_values||{},base_price:a.base_price??null,discounted_price:a.discounted_price??null,is_available:a.is_available??!0,images:e,featured_image_index:Math.min(t||0,Math.max(0,e.length-1)),updated_at:new Date().toISOString(),_imageFiles:n.length>0?n:r._imageFiles,_removedImageIndices:s&&s.length>0?s:r._removedImageIndices}:r)),v.oR.success("Variant updated successfully")}else{let r=n.length>0?n.map(e=>URL.createObjectURL(e)):[],s={id:`temp-${Date.now()}`,product_id:e?.id||"",variant_name:a.variant_name||"",variant_values:a.variant_values||{},base_price:a.base_price??null,discounted_price:a.discounted_price??null,is_available:a.is_available??!0,images:r,featured_image_index:t||0,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),_imageFiles:n};w(e=>[...e,s]),v.oR.success("Variant added successfully")}I(!1),E(null)}catch(e){console.error("Error saving variant:",e),v.oR.error("Failed to save variant")}};return(0,t.jsxs)(l.P.div,{className:"space-y-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.05}}},initial:"hidden",animate:"visible",children:[(0,t.jsx)(l.P.div,{variants:C,className:"py-6 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-50",children:b?"Product Details":"Product Information"}),(0,t.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 leading-relaxed",children:b?"Update the details for this item. All fields marked with * are required.":"Fill in the details for the new product or service. All fields marked with * are required."})]})}),(0,t.jsx)(l.P.div,{variants:C,children:(0,t.jsx)(u.lV,{...Y,children:(0,t.jsxs)("form",{onSubmit:Y.handleSubmit(a=>{let t=D.map(e=>e.file),s=[],i=e?.images?.length||+!!e?.image_url;if(i>0){let e=new Set(D.filter(e=>void 0!==e.originalIndex).map(e=>e.originalIndex));for(let a=0;a<i;a++)e.has(a)||s.push(a)}console.log("Initial image count:",i),console.log("Current images:",D),console.log("Removed image indices:",s),console.log("Variants to be created:",j),r({...a,variants:j},t,O,s)}),className:"space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"pb-4 border-b border-neutral-200/60 dark:border-neutral-800/60",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-50",children:"Basic Information"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mt-1",children:"Essential details about your product or service"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(l.P.div,{variants:C,className:"md:col-span-2",children:(0,t.jsx)(u.zB,{control:Y.control,name:"product_type",render:({field:e})=>(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"Product Type *"}),(0,t.jsxs)(h.l6,{disabled:n,onValueChange:e.onChange,defaultValue:e.value,children:[(0,t.jsx)(u.MJ,{children:(0,t.jsx)(h.bq,{className:"w-full",children:(0,t.jsx)(h.yv,{placeholder:"Select product type"})})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"physical",children:"Physical Product"}),(0,t.jsx)(h.eb,{value:"service",children:"Service"})]})]}),(0,t.jsx)(u.Rr,{children:"Choose whether this is a physical product or a service."}),(0,t.jsx)(u.C5,{})]})})}),(0,t.jsx)(l.P.div,{variants:C,className:"md:col-span-2",children:(0,t.jsx)(u.zB,{control:Y.control,name:"name",render:({field:e})=>(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"Name *"}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(p.p,{placeholder:"Enter product/service name",...e,disabled:n,className:"h-10"})}),(0,t.jsx)(u.Rr,{children:"The name of your product or service (max 100 characters)."}),(0,t.jsx)(u.C5,{})]})})}),(0,t.jsx)(l.P.div,{variants:C,className:"md:col-span-2",children:(0,t.jsx)(u.zB,{control:Y.control,name:"description",render:({field:e})=>(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"Description"}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(m.T,{placeholder:"Enter product/service description",className:"resize-none min-h-[120px]",...e,disabled:n})}),(0,t.jsx)(u.Rr,{children:"Describe your product or service (max 500 characters)."}),(0,t.jsx)(u.C5,{})]})})}),(0,t.jsx)(l.P.div,{variants:C,children:(0,t.jsx)(u.zB,{control:Y.control,name:"base_price",render:({field:{value:e,onChange:a,...r}})=>(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"Base Price (₹) *"}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(p.p,{type:"number",placeholder:"Enter base price",value:void 0===e?"":e,onChange:e=>{let r=e.target.value;a(""===r?void 0:Number(r))},...r,disabled:n,className:"h-10"})}),(0,t.jsx)(u.Rr,{children:"The regular price of your product or service."}),(0,t.jsx)(u.C5,{})]})})}),(0,t.jsx)(l.P.div,{variants:C,children:(0,t.jsx)(u.zB,{control:Y.control,name:"discounted_price",render:({field:{value:e,onChange:a,...r}})=>(0,t.jsxs)(u.eI,{children:[(0,t.jsx)(u.lR,{children:"Discounted Price (₹)"}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(p.p,{type:"number",placeholder:"Enter discounted price (optional)",value:null===e?"":e,onChange:e=>{let r=e.target.value;a(""===r?null:Number(r))},...r,disabled:n,className:"h-10"})}),(0,t.jsx)(u.Rr,{children:"Optional: A special or sale price (must be less than base price)."}),(0,t.jsx)(u.C5,{})]})})}),(0,t.jsx)(l.P.div,{variants:C,className:"md:col-span-2",children:(0,t.jsx)(u.zB,{control:Y.control,name:"is_available",render:({field:a})=>{let r=void 0!==_&&void 0!==y&&y>=_&&!a.value&&!e?.is_available;return(0,t.jsxs)(u.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(u.lR,{className:"text-base",children:"Available"}),(0,t.jsx)(u.Rr,{children:r?`You've reached your plan limit of ${_} available products.`:"Toggle whether this product/service is currently available."})]}),(0,t.jsx)(u.MJ,{children:(0,t.jsx)(g.d,{checked:a.value,onCheckedChange:a.onChange,disabled:n||r})})]})}})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"pb-4 border-b border-neutral-200/60 dark:border-neutral-800/60",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-50",children:"Product Images"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mt-1",children:"Upload high-quality images to showcase your product"})]}),(0,t.jsx)(l.P.div,{variants:C,children:(0,t.jsx)($,{images:D,featuredImageIndex:O,onAddImage:V,onRemoveImage:q,onSetFeatured:M,onFileSelect:B,disabled:n,errorDisplay:T})})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"pb-4 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-50",children:"Product Variants"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mt-1",children:b?"Manage different variations of this product (e.g., size, color, style)":"Add different variations of this product (e.g., size, color, style). Variants will be saved when you create the product."})]}),(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>P(!U),className:"flex items-center gap-2",children:[(0,t.jsx)(H.A,{className:"h-4 w-4"}),U?"Hide":"Show"," Variants"]})]})}),(0,t.jsx)(l.P.div,{variants:C,children:U&&(0,t.jsxs)("div",{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 bg-white dark:bg-black",children:[(0,t.jsx)("div",{className:"flex flex-col sm:flex-row gap-2 mb-4",children:(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:W,className:"flex items-center gap-2",disabled:N,children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Add Variant"]})}),N&&(0,t.jsxs)("div",{className:"mb-6 p-4 border border-neutral-200 dark:border-neutral-700 rounded-lg bg-white dark:bg-black",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:A?"Edit Variant":"Add New Variant"}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",onClick:X,className:"h-8 w-8 p-0",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]}),(0,t.jsx)(J,{productId:e?.id||"draft",initialData:A,onSubmit:K,isSubmitting:!1,onCancel:X})]}),j.length>0?(0,t.jsx)(R.A,{productId:e?.id||"draft",variants:j,onAddVariant:W,onEditVariant:e=>{E(e),I(!0)},onDeleteVariant:e=>{w(a=>a.filter(a=>a.id!==e)),v.oR.success("Variant deleted successfully")},onToggleVariantAvailability:(e,a)=>{w(r=>r.map(r=>r.id===e?{...r,is_available:a}:r)),v.oR.success(`Variant ${a?"enabled":"disabled"} successfully`)}}):(0,t.jsxs)("div",{className:"text-center py-8 text-neutral-500 dark:text-neutral-400",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,t.jsx)("p",{className:"text-sm",children:"No variants created yet"}),(0,t.jsx)("p",{className:"text-xs mt-1",children:"Add variants to offer different options for this product"})]})]})})]}),(0,t.jsx)("div",{className:"pt-8 border-t border-neutral-200/60 dark:border-neutral-800/60",children:(0,t.jsxs)(l.P.div,{variants:C,className:"flex flex-col sm:flex-row gap-3 justify-end",children:[(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>window.history.back(),disabled:n,className:"order-2 sm:order-1",children:"Cancel"}),(0,t.jsx)(c.$,{type:"submit",disabled:n,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium order-1 sm:order-2",size:"lg",children:n?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,t.jsx)("span",{children:b?"Updating...":"Creating..."})]}):(0,t.jsx)(t.Fragment,{children:b?"Update Product":"Create Product"})})]})})]})})}),(0,t.jsx)(k,{isOpen:!!F,imgSrc:F?.dataUrl||null,onCropComplete:z,onClose:G})]})}},43649:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},88233:(e,a,r)=>{r.d(a,{A:()=>t});let t=(0,r(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};