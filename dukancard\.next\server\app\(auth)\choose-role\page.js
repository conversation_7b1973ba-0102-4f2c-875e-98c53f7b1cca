(()=>{var e={};e.id=4313,e.ids=[4313],e.modules={163:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unstable_rethrow",{enumerable:!0,get:function(){return s}});let s=t(71042).unstable_rethrow;("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11637:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\BottomNav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx","default")},11997:e=>{"use strict";e.exports=require("punycode")},14952:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},22255:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\MinimalHeader.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx","default")},22327:(e,r,t)=>{Promise.resolve().then(t.bind(t,22697))},22697:(e,r,t)=>{"use strict";t.d(r,{default:()=>x});var s=t(60687),a=t(43210),n=t(16189),o=t(77882),i=t(24934),l=t(55192),d=t(41862),c=t(58869),u=t(14952),p=t(57800),m=t(52581),f=t(6475);let h=(0,f.createServerReference)("705eb1a7220c6029620523c3d27ad07960d1c732cd",f.callServer,void 0,f.findSourceMapURL,"createCustomerProfileAction");function x({userId:e,redirectSlug:r,message:t}){let f=(0,n.useRouter)();(0,n.useSearchParams)();let[x,b]=(0,a.useTransition)(),[g,v]=(0,a.useState)(null),[_,y]=(0,a.useState)(r||null),[S,E]=(0,a.useState)(t||null),R={hover:{scale:1.02,boxShadow:"0 10px 25px -5px rgba(212, 175, 55, 0.15)",transition:{type:"spring",stiffness:300}},tap:{scale:.98}};return(0,s.jsx)("div",{className:"w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6",children:(0,s.jsx)(o.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},className:"w-full max-w-[90%] sm:max-w-md md:max-w-lg",children:(0,s.jsxs)(l.Zp,{className:"overflow-hidden border dark:border-[#D4AF37]/30 border-[#D4AF37]/20 bg-white dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black shadow-lg rounded-xl",children:[(0,s.jsxs)(l.aR,{className:"text-center relative pb-3 sm:pb-6 px-3 sm:px-6 pt-4 sm:pt-6",children:[(0,s.jsx)(o.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.2,duration:.5},children:(0,s.jsx)("div",{className:"flex justify-center items-center",children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsxs)("div",{className:"flex items-center mb-1 sm:mb-2",children:[(0,s.jsxs)("span",{className:"font-bold text-base sm:text-lg md:text-xl text-[var(--brand-gold)]",children:["Dukan",(0,s.jsx)("span",{className:"text-foreground dark:text-white",children:"card"})]}),(0,s.jsx)(o.P.span,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.8,duration:.5},children:(0,s.jsx)("span",{className:"ml-1"})})]}),(0,s.jsx)(l.ZB,{className:"text-xl sm:text-2xl md:text-3xl font-bold text-neutral-800 dark:text-white",children:"Choose Your Role"})]})})}),(0,s.jsx)(l.BT,{className:"text-neutral-500 dark:text-neutral-400 pt-1 sm:pt-2 text-xs sm:text-sm md:text-base max-w-md mx-auto",children:"Select how you'll be using our platform. This is a one-time setup that cannot be changed later."})]}),(0,s.jsxs)(l.Wu,{className:"space-y-3 sm:space-y-4 md:space-y-6 px-3 sm:px-6 md:px-8 pb-4 sm:pb-6 md:pb-8",children:[(0,s.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.5},children:(0,s.jsx)(o.P.div,{variants:R,whileHover:"hover",whileTap:"tap",children:(0,s.jsx)("div",{className:"group",children:(0,s.jsxs)(i.$,{onClick:()=>{v("customer"),b(async()=>{let r=await h(e,_,S);r?.error&&(m.oR.error(`Failed to set up account: ${r.error}`),v(null))})},className:"cursor-pointer w-full h-auto py-2 sm:py-3 md:py-6 px-2 sm:px-3 md:px-5 bg-neutral-50 hover:bg-neutral-100 dark:bg-neutral-800/70 dark:hover:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:border-[#D4AF37]/50 dark:hover:border-[#D4AF37]/50 text-neutral-800 dark:text-white flex items-center justify-between rounded-lg sm:rounded-xl group-hover:shadow-md transition-all",disabled:x,children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 md:space-x-4 max-w-[85%]",children:[(0,s.jsx)("div",{className:"bg-[#D4AF37]/10 dark:bg-[#D4AF37]/20 p-1.5 sm:p-2 md:p-3 rounded-md sm:rounded-lg flex-shrink-0",children:x&&"customer"===g?(0,s.jsx)(d.A,{"data-testid":"customer-loader",className:"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37] animate-spin"}):(0,s.jsx)(c.A,{className:"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37]"})}),(0,s.jsxs)("div",{className:"text-left min-w-0",children:[(0,s.jsx)("span",{className:"text-sm sm:text-base md:text-lg font-semibold block mb-0 sm:mb-0.5 md:mb-1 truncate",children:"As a Customer"}),(0,s.jsx)("p",{className:"text-xs sm:text-xs md:text-sm text-neutral-500 dark:text-neutral-400 line-clamp-2",children:"Browse and connect with businesses."})]})]}),(0,s.jsx)(u.A,{className:"w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-neutral-400 dark:text-neutral-500 group-hover:text-[#D4AF37] transition-colors mr-1 sm:mr-1 md:mr-2 flex-shrink-0"})]})})})}),(0,s.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5,duration:.5},children:(0,s.jsx)(o.P.div,{variants:R,whileHover:"hover",whileTap:"tap",children:(0,s.jsx)("div",{className:"group",children:(0,s.jsxs)(i.$,{onClick:()=>{v("business");let e="/onboarding",r=new URLSearchParams;_&&r.append("redirect",_),S&&r.append("message",S),r.toString()&&(e+=`?${r.toString()}`),f.push(e)},className:"cursor-pointer w-full h-auto py-2 sm:py-3 md:py-6 px-2 sm:px-3 md:px-5 bg-neutral-50 hover:bg-neutral-100 dark:bg-neutral-800/70 dark:hover:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:border-[#D4AF37]/50 dark:hover:border-[#D4AF37]/50 text-neutral-800 dark:text-white flex items-center justify-between rounded-lg sm:rounded-xl group-hover:shadow-md transition-all",disabled:x,children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 md:space-x-4 max-w-[85%]",children:[(0,s.jsx)("div",{className:"bg-[#D4AF37]/10 dark:bg-[#D4AF37]/20 p-1.5 sm:p-2 md:p-3 rounded-md sm:rounded-lg flex-shrink-0",children:x&&"business"===g?(0,s.jsx)(d.A,{"data-testid":"business-loader",className:"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37] animate-spin"}):(0,s.jsx)(p.A,{className:"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37]"})}),(0,s.jsxs)("div",{className:"text-left min-w-0",children:[(0,s.jsx)("span",{className:"text-sm sm:text-base md:text-lg font-semibold block mb-0 sm:mb-0.5 md:mb-1 truncate",children:"As a Business"}),(0,s.jsx)("p",{className:"text-xs sm:text-xs md:text-sm text-neutral-500 dark:text-neutral-400 line-clamp-2",children:"Create your digital card and store."})]})]}),(0,s.jsx)(u.A,{className:"w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-neutral-400 dark:text-neutral-500 group-hover:text-[#D4AF37] transition-colors mr-1 sm:mr-1 md:mr-2 flex-shrink-0"})]})})})}),(0,s.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.7,duration:.5},children:(0,s.jsx)("div",{className:"pt-1 sm:pt-2 md:pt-4 text-center",children:(0,s.jsx)("p",{className:"text-[10px] sm:text-xs md:text-sm text-red-500 dark:text-red-400 font-medium",children:"Note: This choice is permanent and cannot be changed later."})})})]})]})})})}},27470:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(37413);t(61120);var a=t(22255),n=t(23392),o=t(82662),i=t(11637),l=t(92506),d=t(32032);async function c(){let e=await (0,d.createClient)();try{let{data:{user:r},error:t}=await e.auth.getUser();if(t)return console.error("Error fetching authenticated user:",t.message),{user:null,error:"User not found or authentication error."};return{user:r,error:null}}catch(e){return console.error("Unexpected error fetching authenticated user:",e),{user:null,error:"An unexpected error occurred."}}}async function u({children:e}){let{user:r}=await c(),t=r?.user_metadata?.full_name??r?.user_metadata?.name??null;return(0,s.jsx)(n.ThemeProvider,{attribute:"class",children:(0,s.jsxs)("div",{className:"min-h-screen flex flex-col bg-background",children:[(0,s.jsxs)(a.default,{userName:t,children:[null," ",null," ",(0,s.jsx)(o.ThemeToggle,{variant:"dashboard"})]}),(0,s.jsx)("main",{className:"flex-grow flex items-center justify-center",children:e}),(0,s.jsx)(i.default,{}),(0,s.jsx)(l.default,{})]})})}},27625:(e,r,t)=>{"use strict";t.d(r,{default:()=>h});var s=t(60687),a=t(43210),n=t.n(a),o=t(16189),i=t(70373),l=t(55629),d=t(24934),c=t(40083),u=t(42154),p=t(4952);let m=(e,r)=>{let t=e||r;if(!t)return"?";let s=t.trim().split(/\s+/);return 1===s.length?s[0].charAt(0).toUpperCase():s[0].charAt(0).toUpperCase()+s[s.length-1].charAt(0).toUpperCase()},f=e=>{let r=e.split("/").filter(Boolean);if(r.includes("customer"))switch(r[r.length-1]){case"customer":return"Feed";case"overview":return"Overview";case"likes":return"My Likes";case"subscriptions":return"Subscriptions";case"reviews":return"My Reviews";case"profile":return"Profile";case"settings":return"Settings";default:return"Dashboard"}if(r.includes("business"))switch(r[r.length-1]){case"business":return"Feed";case"overview":return"Overview";case"analytics":return"Analytics";case"card":return"Manage Card";case"products":return"Products & Services";case"gallery":return"Gallery";case"subscriptions":return"Subscriptions";case"likes":return"Likes";case"reviews":return"Reviews";case"activities":return"Activities";case"settings":return"Settings";case"plan":return"Plan Management";default:return"Business Dashboard"}return"Dashboard"},h=({children:e,businessName:r,logoUrl:t,userName:a})=>{let h=(0,o.usePathname)(),x=f(h),{scrollDirection:b,isScrolled:g}=(0,p.Y)({threshold:50}),v=g&&"down"===b&&(h.startsWith("/dashboard/")||"/discover"===h||h.startsWith("/post/")),_=h.includes("/choose-role")||h.includes("/onboarding"),y=m(a,r),S="User";a&&r?S=`${r} (${a})`:a?S=a:r&&(S=r);let E=n().Children.toArray(e),R=E[0],w=E[1],j=E[2];return(0,s.jsx)("header",{className:`sticky top-0 z-40 w-full border-b border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80 transition-transform duration-300 ease-in-out ${v?"-translate-y-full":"translate-y-0"}`,children:(0,s.jsxs)("div",{className:"container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[R,(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:x})}),w]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[_?(0,s.jsx)("form",{action:u.B,children:(0,s.jsxs)(d.$,{type:"submit",variant:"ghost",className:"flex items-center gap-2 h-10 px-3 rounded-lg",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})}):(0,s.jsxs)(l.rI,{children:[(0,s.jsx)(l.ty,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"ghost",className:"cursor-pointer flex items-center gap-3 h-10 px-3 rounded-lg focus-visible:ring-0 focus-visible:ring-offset-0",children:[(0,s.jsxs)(i.eu,{className:"h-8 w-8 border-2 border-border",children:[t?(0,s.jsx)(i.BK,{src:t,alt:a||r||"User"}):null,(0,s.jsx)(i.q5,{className:"bg-gradient-to-br from-primary/10 to-primary/5 text-primary font-semibold text-sm",children:y})]}),(0,s.jsx)("span",{className:"hidden sm:block text-sm font-medium text-foreground max-w-32 truncate",children:r||a||"User"})]})}),(0,s.jsxs)(l.SQ,{className:"w-64",align:"end",forceMount:!0,children:[" ",(0,s.jsx)(l.lp,{className:"font-normal",children:(0,s.jsxs)("p",{className:"text-sm font-medium leading-none truncate py-2",children:[S," "]})}),(0,s.jsx)(l.mB,{}),(0,s.jsx)("form",{action:u.B,className:"w-full px-2 py-1.5",children:(0,s.jsxs)(d.$,{type:"submit",variant:"ghost",className:"w-full justify-start font-normal text-sm h-auto py-1 cursor-pointer",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})})]})]}),j," "]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,r,t)=>{"use strict";t.d(r,{CG:()=>s,SC:()=>a,cZ:()=>n});let s={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},a={BUSINESS:"business",CUSTOMERS:"customers"},n={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},32688:(e,r,t)=>{"use strict";t.d(r,{C:()=>s});let s={name:"Dukancard",description:"Create and share digital business cards, showcase products, and connect with customers.",url:"https://dukancard.in",ogImage:"https://dukancard.in/opengraph-image.png",contact:{email:"<EMAIL>",phone:"+91 8458060663",address:{street:"Bisra Road",city:"Rourkela",state:"Odisha",postalCode:"769001",country:"India",full:"Bisra Road, Rourkela, Odisha - 769001"},hours:"Monday - Friday: 9:00 AM - 6:00 PM"},legal:{privacyPolicy:"/privacy",termsOfService:"/terms",refundPolicy:"/refund"},support:{email:"<EMAIL>",phone:"+91 8458060663",helpCenter:"/support"},advertising:{email:"<EMAIL>",phone:"+************",page:"/advertise"}}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34849:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(auth)\\\\choose-role\\\\ChooseRoleClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx","default")},35157:(e,r,t)=>{"use strict";t.d(r,{CG:()=>s,SC:()=>a,cZ:()=>n});let s={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},a={BUSINESS:"business",CUSTOMERS:"customers"},n={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},39916:(e,r,t)=>{"use strict";var s=t(97576);t.o(s,"notFound")&&t.d(r,{notFound:function(){return s.notFound}}),t.o(s,"redirect")&&t.d(r,{redirect:function(){return s.redirect}})},42154:(e,r,t)=>{"use strict";t.d(r,{B:()=>a});var s=t(6475);let a=(0,s.createServerReference)("00a3593a777ba7988175db3502399fe90e4a6ac663",s.callServer,void 0,s.findSourceMapURL,"signOutUser")},46417:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),o=t.n(n),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["(auth)",{children:["choose-role",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,65262)),"C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,27470)),"C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(auth)/choose-role/page",pathname:"/choose-role",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48976:(e,r,t)=>{"use strict";function s(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"forbidden",{enumerable:!0,get:function(){return s}}),t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},51779:(e,r,t)=>{Promise.resolve().then(t.bind(t,92506)),Promise.resolve().then(t.bind(t,11637)),Promise.resolve().then(t.bind(t,22255)),Promise.resolve().then(t.bind(t,82662)),Promise.resolve().then(t.bind(t,23392))},55192:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>c});var s=t(60687);t(43210);var a=t(96241);function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57800:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},62079:(e,r,t)=>{Promise.resolve().then(t.bind(t,34849))},62765:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"notFound",{enumerable:!0,get:function(){return a}});let s=""+t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(s),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=s,e}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64275:(e,r,t)=>{"use strict";t.d(r,{B:()=>o});var s=t(91199);t(42087);var a=t(76881),n=t(90141);async function o(){let e=await (0,a.createClient)();try{let{error:r}=await e.auth.signOut(),s=await Promise.all([t.e(4208),t.e(4659)]).then(t.bind(t,74208)).then(e=>e.cookies());for(let e of["sb-access-token","sb-refresh-token"])try{s.set(e,"",{expires:new Date(0),maxAge:-1})}catch{}}catch{}return(0,n.redirect)("/login?logged_out=true")}(0,t(33331).D)([o]),(0,s.A)(o,"00a3593a777ba7988175db3502399fe90e4a6ac663",null)},65262:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,generateMetadata:()=>u});var s=t(37413);t(61120);var a=t(34849),n=t(39916),o=t(32032),i=t(30468);async function l(){let e=await (0,o.createClient)();try{let{data:{user:r},error:t}=await e.auth.getUser();if(t)return console.error("Error fetching authenticated user:",t.message),{user:null,error:"User not found or authentication error."};return{user:r,error:null}}catch(e){return console.error("Unexpected error fetching authenticated user:",e),{user:null,error:"An unexpected error occurred."}}}async function d(e){let r=await (0,o.createClient)();try{let{data:t,error:s}=await r.from(i.CG.CUSTOMER_PROFILES).select(i.cZ.ID).eq(i.cZ.ID,e).maybeSingle();if(s)return console.error("Error checking existing profile:",s.message),{exists:!1,error:"Database error checking profile."};return{exists:!!t,error:null}}catch(e){return console.error("Unexpected error checking profile:",e),{exists:!1,error:"An unexpected error occurred."}}}async function c(e){let r=await (0,o.createClient)();try{let{data:t,error:s}=await r.from(i.CG.BUSINESS_PROFILES).select(i.cZ.ID).eq(i.cZ.ID,e).maybeSingle();if(s)return console.error("Error checking existing business profile:",s.message),{exists:!1,error:"Database error checking business profile."};return{exists:!!t,error:null}}catch(e){return console.error("Unexpected error checking business profile:",e),{exists:!1,error:"An unexpected error occurred."}}}async function u(){return{title:"Choose Your Role",description:"Select how you will use Dukancard.",robots:"noindex, nofollow"}}async function p({searchParams:e}){let{redirect:r,message:t}=await e,{user:o,error:i}=await l();if(!o)return(0,n.redirect)("/login");let[u,p]=await Promise.all([d(o.id),c(o.id)]);if(u.error||p.error)return(0,n.redirect)("/login?message=Error checking profile status");if(u.exists||p.exists){let e=u.exists?"customer":"business";return(0,n.redirect)("business"===e?"/dashboard/business":"/dashboard/customer")}return(0,s.jsx)(a.default,{userId:o.id,redirectSlug:r||null,message:t||null})}},65851:(e,r,t)=>{Promise.resolve().then(t.bind(t,91164)),Promise.resolve().then(t.bind(t,38606)),Promise.resolve().then(t.bind(t,27625)),Promise.resolve().then(t.bind(t,41956)),Promise.resolve().then(t.bind(t,10218))},70899:(e,r,t)=>{"use strict";function s(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unauthorized",{enumerable:!0,get:function(){return s}}),t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},71042:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unstable_rethrow",{enumerable:!0,get:function(){return function e(r){if((0,o.isNextRouterError)(r)||(0,n.isBailoutToCSRError)(r)||(0,l.isDynamicServerError)(r)||(0,i.isDynamicPostpone)(r)||(0,a.isPostpone)(r)||(0,s.isHangingPromiseRejectionError)(r))throw r;r instanceof Error&&"cause"in r&&e(r.cause)}}});let s=t(68388),a=t(52637),n=t(51846),o=t(31162),i=t(84971),l=t(98479);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82662:(e,r,t)=>{"use strict";t.d(r,{ThemeToggle:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx","ThemeToggle")},86897:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return l},redirect:function(){return i}});let s=t(52836),a=t(49026),n=t(19121).actionAsyncStorage;function o(e,r,t){void 0===t&&(t=s.RedirectStatusCode.TemporaryRedirect);let n=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n.digest=a.REDIRECT_ERROR_CODE+";"+r+";"+e+";"+t+";",n}function i(e,r){var t;throw null!=r||(r=(null==n||null==(t=n.getStore())?void 0:t.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,r,s.RedirectStatusCode.TemporaryRedirect)}function l(e,r){throw void 0===r&&(r=a.RedirectType.replace),o(e,r,s.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},90795:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>s.B,"705eb1a7220c6029620523c3d27ad07960d1c732cd":()=>p});var s=t(64275),a=t(91199);t(42087);var n=t(90141),o=t(7944),i=t(35157),l=t(76881);async function d(){let e=await (0,l.createClient)();try{let{data:{user:r},error:t}=await e.auth.getUser();if(t)return console.error("Error fetching authenticated user:",t.message),{user:null,error:"User not found or authentication error."};return{user:r,error:null}}catch(e){return console.error("Unexpected error fetching authenticated user:",e),{user:null,error:"An unexpected error occurred."}}}async function c(e){let r=await (0,l.createClient)();try{let{data:t,error:s}=await r.from(i.CG.CUSTOMER_PROFILES).select(i.cZ.ID).eq(i.cZ.ID,e).maybeSingle();if(s)return console.error("Error checking existing profile:",s.message),{exists:!1,error:"Database error checking profile."};return{exists:!!t,error:null}}catch(e){return console.error("Unexpected error checking profile:",e),{exists:!1,error:"An unexpected error occurred."}}}async function u(e){let r=await (0,l.createClient)();try{let{data:t,error:s}=await r.from(i.CG.CUSTOMER_PROFILES).insert([e]).select().single();if(s)return console.error("Error creating user profile:",s.message),{data:null,error:s.message};return{data:Array.isArray(t)?t[0]||null:t||null,error:null}}catch(e){return console.error("Unexpected error creating user profile:",e),{data:null,error:"An unexpected error occurred."}}}async function p(e,r=null,t=null){if(!e)return{error:"User ID is required."};let{user:s,error:a}=await d();if(a||!s)return{error:"User not found or authentication error."};let{exists:l,error:m}=await c(e);if(m)return{error:m};l&&(r?(0,n.redirect)(`/${r}`):(0,n.redirect)("/dashboard/customer"));let{error:f}=await u({[i.cZ.ID]:e,[i.cZ.NAME]:s.user_metadata?.full_name??s.user_metadata?.name??null,[i.cZ.EMAIL]:s.email??null});if(f)return console.error("Error creating customer profile:",f),{error:"Failed to create profile."};(0,o.revalidatePath)("/choose-role"),(0,o.revalidatePath)("/dashboard/customer"),r?(0,n.redirect)(`/${r}`):(0,n.redirect)("/dashboard/customer")}(0,t(33331).D)([p]),(0,a.A)(p,"705eb1a7220c6029620523c3d27ad07960d1c732cd",null)},91164:(e,r,t)=>{"use strict";t.d(r,{default:()=>p});var s=t(60687),a=t(43210),n=t(85814),o=t.n(n),i=t(20798),l=t(77882),d=t(32688),c=t(16189);let u={writingMode:"vertical-rl",textOrientation:"mixed",transform:"rotate(180deg)",letterSpacing:"0.05em",fontSize:"0.8rem",fontWeight:600};function p(){let[e,r]=(0,a.useState)(!1),[t,n]=(0,a.useState)(!1),p=(0,c.usePathname)(),m=p?.includes("/dashboard"),f="/advertise"===p;return!t||m||f?null:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"fixed right-0 top-1/2 -translate-y-1/2 z-40 hidden sm:block",children:(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(l.P.div,{className:"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-l-lg blur-md",animate:{opacity:e?.8:.5},transition:{duration:.3}}),(0,s.jsx)(o(),{href:d.C.advertising.page,children:(0,s.jsx)(l.P.div,{className:"relative",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1),whileHover:{scale:1.03},whileTap:{scale:.98},children:(0,s.jsxs)("div",{className:"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 px-2 py-6 rounded-l-lg font-medium text-xs relative overflow-hidden shadow-lg flex flex-col items-center justify-center w-10 h-32",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center gap-2",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{style:u,children:"Advertise"})]}),(0,s.jsx)(l.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear",repeatDelay:1}})]})})})]})}),(0,s.jsx)("div",{className:"fixed left-4 bottom-20 z-40 sm:hidden",children:(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(l.P.div,{className:"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full blur-md",animate:{opacity:.6},transition:{duration:.3}}),(0,s.jsx)(o(),{href:d.C.advertising.page,children:(0,s.jsx)(l.P.div,{className:"relative",whileTap:{scale:.95},children:(0,s.jsxs)("div",{className:"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 p-2 rounded-full font-medium text-xs relative overflow-hidden shadow-md flex items-center justify-center w-10 h-10",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),(0,s.jsx)(l.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear",repeatDelay:1}})]})})})]})})]})}},91645:e=>{"use strict";e.exports=require("net")},92506:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\AdvertiseButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},97576:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return n.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect},unauthorized:function(){return i.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let s=t(86897),a=t(49026),n=t(62765),o=t(48976),i=t(70899),l=t(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,9398,4386,6724,2997,1107,3206,9190,7793,3037,3739],()=>t(46417));module.exports=s})();