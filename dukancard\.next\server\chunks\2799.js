exports.id=2799,exports.ids=[2799],exports.modules={3018:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>l,TN:()=>d,XL:()=>o});var r=s(60687);s(43210);var a=s(24224),i=s(96241);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...s})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-title",className:(0,i.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},3956:(e,t,s)=>{Promise.resolve().then(s.bind(s,64522))},8160:(e,t,s)=>{"use strict";s.d(t,{dH:()=>r,SubscriptionStateManager:()=>i});let r={ACTIVE:"active",AUTHENTICATED:"authenticated",TRIAL:"trial",PENDING:"pending",HALTED:"halted",CANCELLED:"cancelled",EXPIRED:"expired",COMPLETED:"completed",PAYMENT_FAILED:"payment_failed",CANCELLATION_SCHEDULED:"cancellation_scheduled"},a={FREE:"free"};class i{static shouldHaveActiveSubscription(e,t=a.FREE){return t!==a.FREE&&e!==r.TRIAL&&[r.ACTIVE].includes(e)}static isTerminalStatus(e){return[r.CANCELLED,r.EXPIRED,r.COMPLETED].includes(e)}static isTrialStatus(e){return e===r.TRIAL}static isFreeStatus(e,t){return t===a.FREE||"free"===e}static getAccessLevel(e,t=a.FREE){return t===a.FREE?"free":e===r.TRIAL?"trial":this.shouldHaveActiveSubscription(e,t)?"paid":"free"}static isActivePaidSubscription(e,t=a.FREE){return this.shouldHaveActiveSubscription(e,t)}static isValidStatusTransition(e,t){return!this.isTerminalStatus(e)||!!this.isTerminalStatus(t)}}s(19101);async function n(e){try{let t=await createClient();console.log(`[ATOMIC_TRANSACTION] Using atomic RPC for subscription ${e.subscription_id}`);let{data:s,error:r}=await t.rpc("update_subscription_atomic",{p_subscription_id:e.subscription_id,p_new_status:e.subscription_status,p_business_profile_id:e.business_profile_id,p_has_active_subscription:e.has_active_subscription,p_additional_data:e.additional_data||{},p_webhook_timestamp:e.additional_data?.last_webhook_timestamp||void 0});if(r)return console.error(`[ATOMIC_TRANSACTION] RPC error for ${e.subscription_id}:`,r),{success:!1,message:`RPC error: ${r.message}`};if(!s?.success)return console.error(`[ATOMIC_TRANSACTION] RPC function returned error for ${e.subscription_id}:`,s),{success:!1,message:s?.error||"Unknown RPC error"};return console.log(`[ATOMIC_TRANSACTION] Successfully updated subscription ${e.subscription_id} atomically`),{success:!0,message:"Atomic transaction completed successfully via RPC"}}catch(e){return console.error("[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:",e),{success:!1,message:`Atomic transaction exception: ${e instanceof Error?e.message:String(e)}`}}}},19101:(e,t,s)=>{"use strict";s.d(t,{U:()=>a});var r=s(79384);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let a=null,i=null;try{let{headers:e,cookies:t}=await s.e(9277).then(s.bind(s,9277));a=await e(),i=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,a=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,a){let i=()=>{var i,n,l,o,d;return i=e,n=t,l=s,o=r,d=a,"customer_profiles"===i?{data:l&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:l&&"business"===n?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===n?{id:"test-subscription-id",plan_id:d,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,s)=>n(e),neq:(t,s)=>n(e),gt:(t,s)=>n(e),gte:(t,s)=>n(e),lt:(t,s)=>n(e),lte:(t,s)=>n(e),like:(t,s)=>n(e),ilike:(t,s)=>n(e),is:(t,s)=>n(e),in:(t,s)=>n(e),contains:(t,s)=>n(e),containedBy:(t,s)=>n(e),rangeGt:(t,s)=>n(e),rangeGte:(t,s)=>n(e),rangeLt:(t,s)=>n(e),rangeLte:(t,s)=>n(e),rangeAdjacent:(t,s)=>n(e),overlaps:(t,s)=>n(e),textSearch:(t,s)=>n(e),match:t=>n(e),not:(t,s,r)=>n(e),or:t=>n(e),filter:(t,s,r)=>n(e),order:(t,s)=>n(e),limit:(t,s)=>n(e),range:(t,s,r)=>n(e),abortSignal:t=>n(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let t=i();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,s,r,a,i)}}(a):i?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await i.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},20670:(e,t,s)=>{"use strict";s.d(t,{Nh:()=>a,RL:()=>i});var r=s(79209);let a=e=>r.NB.map(t=>(function(e,t){let s="enterprise"===e.id,r=e.pricing.monthly,a=e.pricing.yearly;return{id:e.id,name:`${e.name} Plan`,razorpayPlanIds:{monthly:e.razorpayPlanIds.monthly||null,yearly:e.razorpayPlanIds.yearly||null},price:s?"Contact Sales":"monthly"===t?`₹${e.pricing.monthly.toLocaleString("en-IN")}`:`₹${e.pricing.yearly.toLocaleString("en-IN")}`,yearlyPrice:s?"Contact Sales":`₹${e.pricing.yearly.toLocaleString("en-IN")}`,period:s?"":"monthly"===t?"/month":"/year",savings:s?void 0:"yearly"===t?`Save ₹${(s?0:12*r-a).toLocaleString("en-IN")}`:void 0,description:e.description,features:e.features.map(e=>{if("Product Listings"===e.name&&e.included){let t="unlimited"===e.limit?"Unlimited":e.limit;return`Product/Service Listings (up to ${t})`}return e.included?e.name:`❌ ${e.name}`}),button:s?"Contact Sales":"Subscribe Now",available:!0,featured:"free"===e.id||"basic"===e.id||"growth"===e.id||"pro"===e.id,recommended:e.recommended||!1,mostPopular:e.recommended||!1}})(t,e));a("monthly");let i=e=>(0,r.dI)(e)},45488:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(37413);s(61120);var a=s(32032),i=s(64522);let n=["member_name","title","business_name","business_category","contact_email","phone","address_line","pincode","city","state","locality"];var l=s(39916);async function o({children:e}){let t=await (0,a.createClient)(),s=null,o=null,d=null,u=null,{data:{user:c}}=await t.auth.getUser();if(c){let{data:e,error:r}=await t.from("business_profiles").select(`
        business_name,
        logo_url,
        member_name,
        title,
        business_category,
        contact_email,
        phone,
        address_line,
        pincode,
        city,
        state,
        locality
      `).eq("id",c.id).single(),{data:a}=await t.from("payment_subscriptions").select("plan_id").eq("business_profile_id",c.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(r)console.error("Error fetching business profile in layout:",r.message);else if(e){s=e.business_name,o=e.logo_url,d=e.member_name,u=a?.plan_id||"free";let t=function(e){if(!e)return{isComplete:!1,missingFields:[...n],missingFieldLabels:["Your name","Your title","Business name","Business category","Contact email","Primary phone","Address line","Pincode","City","State","Locality/area"]};let t=[],s=[],r={member_name:"Your name",title:"Your title",business_name:"Business name",business_category:"Business category",contact_email:"Contact email",phone:"Primary phone",address_line:"Address line",pincode:"Pincode",city:"City",state:"State",locality:"Locality/area"};return n.forEach(a=>{let i=e[a];i&&""!==String(i).trim()||(t.push(a),s.push(r[a]))}),{isComplete:0===t.length,missingFields:t,missingFieldLabels:s}}(e);if(!t.isComplete){let e=function(e){if(0===e.length)return"";if(1===e.length)return`Please complete your ${e[0].toLowerCase()} to access the dashboard.`;if(2===e.length)return`Please complete your ${e[0].toLowerCase()} and ${e[1].toLowerCase()} to access the dashboard.`;let t=e[e.length-1],s=e.slice(0,-1);return`Please complete your ${s.map(e=>e.toLowerCase()).join(", ")}, and ${t.toLowerCase()} to access the dashboard.`}(t.missingFieldLabels);(0,l.redirect)(`/onboarding?message=${encodeURIComponent(e)}`)}}}else console.warn("No user found in business dashboard layout.");return(0,r.jsx)(i.default,{businessName:s,logoUrl:o,memberName:d,userPlan:u,children:e})}},61192:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(60687);s(43210);var a=s(27625),i=s(41956),n=s(38606),l=s(24861),o=s(21121),d=s(96241),u=s(52529);function c({children:e,businessName:t,logoUrl:s,memberName:c,userPlan:m}){return(0,r.jsx)(u.Q,{children:(0,r.jsxs)(l.GB,{children:[(0,r.jsx)(o.s,{businessName:t,logoUrl:s,memberName:c,userPlan:m}),(0,r.jsxs)(l.sF,{children:[(0,r.jsxs)(a.default,{businessName:t,logoUrl:s,userName:c,children:[(0,r.jsx)(l.x2,{className:"ml-auto md:ml-0"})," ",(0,r.jsx)(i.ThemeToggle,{variant:"dashboard"})]}),(0,r.jsx)("main",{className:(0,d.cn)("flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto","pb-20 sm:pb-18 md:pb-6","bg-white dark:bg-black"),children:e}),(0,r.jsx)(n.default,{})]})]})})}},64522:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\components\\\\BusinessDashboardClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx","default")},77493:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(60687),a=s(43210),i=s(62307),n=s(87163),l=s(80189);let o=({endDate:e,label:t="Trial ends in:",tooltipText:s="Your trial will expire soon. Upgrade to continue using all features.",className:o})=>{let[d,u]=(0,a.useState)(new Date),[c,m]=(0,a.useState)([]),b=(0,a.useMemo)(()=>{let t;return isNaN((t="string"==typeof e?new Date(e):e).getTime())?null:t},[e]);if((0,a.useEffect)(()=>{if(!b)return;let e=()=>{let e=new Date;if(u(e),b&&e<b){let t=(0,i.F)({start:e,end:b}),s=[],r=b.getTime()-e.getTime(),a=Math.floor(r/315576e5),n=r%315576e5,l=Math.floor(n/2630016e3),o=Math.floor((n%=2630016e3)/864e5);n%=864e5;let d=t.hours||0,u=t.minutes||0,c=t.seconds||0,p=[];a>0&&p.push({label:"years",value:a}),l>0&&p.push({label:"months",value:l}),o>0&&p.push({label:"days",value:o}),p.push({label:"hrs",value:d},{label:"min",value:u},{label:"sec",value:c});let g=p.pop();s.push(...p.slice(0,5)),g&&s.push(g),m(s)}};e();let t=setInterval(e,1e3);return()=>clearInterval(t)},[b]),!b||d>=b)return null;let p=c.some(e=>"months"===e.label||"years"===e.label)?{gradient:"from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-900/20",border:"border-blue-200 dark:border-blue-800/50",text:"text-blue-600 dark:text-blue-400",shadow:"shadow-blue-200/20 dark:shadow-blue-900/20",glow:"after:bg-blue-500/10 dark:after:bg-blue-400/10"}:c.some(e=>"days"===e.label)?{gradient:"from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/5 dark:from-[var(--brand-gold)]/30 dark:to-[var(--brand-gold)]/10",border:"border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/20",text:"text-[var(--brand-gold)] dark:text-[var(--brand-gold)]",shadow:"shadow-[var(--brand-gold)]/10 dark:shadow-[var(--brand-gold)]/10",glow:"after:bg-[var(--brand-gold)]/10 dark:after:bg-[var(--brand-gold)]/10"}:{gradient:"from-amber-100 to-amber-50 dark:from-amber-900/40 dark:to-amber-900/20",border:"border-amber-200 dark:border-amber-800/50",text:"text-amber-600 dark:text-amber-400",shadow:"shadow-amber-200/20 dark:shadow-amber-900/20",glow:"after:bg-amber-500/10 dark:after:bg-amber-400/10"};return(0,r.jsxs)("div",{className:`flex flex-col items-center w-full ${o||""}`,children:[(0,r.jsx)(l.Bc,{delayDuration:100,children:(0,r.jsxs)(l.m_,{children:[(0,r.jsx)(l.k$,{asChild:!0,children:(0,r.jsxs)("div",{className:"flex items-center gap-1.5 mb-1.5",children:[(0,r.jsx)("div",{className:"transition-transform hover:scale-110",children:(0,r.jsx)(n.A,{className:`w-4 h-4 ${p.text}`})}),(0,r.jsx)("span",{className:"text-xs font-medium text-muted-foreground",children:t})]})}),(0,r.jsx)(l.ZI,{side:"bottom",className:"bg-neutral-800/95 dark:bg-neutral-950/95 backdrop-blur-sm border border-neutral-700/50 dark:border-[var(--brand-gold)]/20 text-white text-xs p-3 rounded-lg shadow-lg",children:(0,r.jsx)("p",{children:s})})]})}),(0,r.jsx)("div",{className:"flex gap-1 xs:gap-1.5 sm:gap-2 flex-wrap justify-center",children:c.map((e,t)=>(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsxs)("div",{className:`relative w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 md:w-11 md:h-11 overflow-hidden rounded-md ${p.shadow} shadow-md after:absolute after:inset-0 after:rounded-md after:opacity-30 after:blur-xl ${p.glow}`,children:[(0,r.jsx)("div",{className:"absolute top-0 left-0 right-0 h-[1px] bg-white/40 dark:bg-white/10 z-10"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-[1px] bg-black/10 dark:bg-black/20 z-10"}),(0,r.jsx)("div",{className:"absolute top-1/2 left-0 right-0 h-[1px] bg-black/10 dark:bg-white/10 z-10"}),(0,r.jsx)("div",{className:`absolute inset-0 flex items-center justify-center bg-gradient-to-b ${p.gradient} rounded-md border ${p.border}`,children:(0,r.jsx)("span",{className:"text-xs xs:text-sm sm:text-base md:text-lg font-mono font-bold text-neutral-800 dark:text-neutral-200",children:e.value.toString().padStart(2,"0")})},`${e.label}-${e.value}`)]}),(0,r.jsx)("span",{className:"text-[7px] xs:text-[8px] sm:text-[9px] font-medium text-muted-foreground mt-0.5",children:e.label})]},t))})]})}},79988:(e,t,s)=>{Promise.resolve().then(s.bind(s,61192))}};