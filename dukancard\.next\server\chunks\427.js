"use strict";exports.id=427,exports.ids=[427],exports.modules={8174:(e,i,n)=>{n.d(i,{NB:()=>d,getPlanByRazorpayPlanId:()=>r,qD:()=>a});let s={free:{monthly:"free-plan-monthly",yearly:"free-plan-yearly"},basic:{monthly:"plan_QO9rDMTSLeT34b",yearly:"plan_QO9sfgFBnEFATA"},growth:{monthly:"plan_QbnOd77S3FVeUc",yearly:"plan_QbnOuE7iogOGTq"},pro:{monthly:"plan_QbnP83wvmzUOqM",yearly:"plan_QbnPJinNt66Pik"},enterprise:{monthly:"enterprise-plan-monthly-razorpay",yearly:"enterprise-plan-yearly-razorpay"}},d=[{id:"free",name:"Free",description:"Basic features for individuals and startups",razorpayPlanIds:s.free,pricing:{monthly:0,yearly:0},features:[{name:"Digital Business Card",included:!0,description:"Simple digital business card with contact information"},{name:"QR Code for Sharing",included:!0,description:"Shareable QR code for your business card"},{name:"Social Media Links",included:!0,description:"Add links to your social media profiles"},{name:"Product Listings",included:!0,limit:5,description:"Showcase your products or services (limited to 5)"},{name:"Customer Subscriptions",included:!0,description:"Allow customers to subscribe to your business"},{name:"Ratings & Reviews",included:!0,description:"Collect and display customer reviews"},{name:"Like Feature",included:!0,description:"Let customers like your business card"},{name:"Basic Analytics",included:!1,description:"View basic metrics like views and clicks"},{name:"Default Theme",included:!0,description:"Use the default Dukancard theme"},{name:"Delivery Hours",included:!0,description:"Set and display your delivery hours"},{name:"Business Hours",included:!0,description:"Set and display your business hours"},{name:"Theme Customization",included:!1,description:"Customize your card theme and colors"},{name:"Enhanced Analytics",included:!1,description:"View detailed metrics including product views"},{name:"Advanced Analytics",included:!1,description:"Access comprehensive business insights"},{name:"Photo Gallery",included:!0,limit:1,description:"Upload and display 1 image in your gallery"},{name:"Dukancard Branding",included:!0,description:"Dukancard branding on your business card"}]},{id:"basic",name:"Basic",description:"Essential features for small businesses",recommended:!1,razorpayPlanIds:s.basic,pricing:{monthly:99,yearly:999},features:[{name:"Digital Business Card",included:!0,description:"Basic digital business card with contact information"},{name:"QR Code for Sharing",included:!0,description:"Shareable QR code for your business card"},{name:"Social Media Links",included:!0,description:"Add links to your social media profiles"},{name:"Product Listings",included:!0,limit:15,description:"Showcase your products or services (up to 15)"},{name:"Customer Subscriptions",included:!0,description:"Allow customers to subscribe to your business"},{name:"Ratings & Reviews",included:!0,description:"Collect and display customer reviews"},{name:"Like Feature",included:!0,description:"Let customers like your business card"},{name:"Basic Analytics",included:!0,description:"View basic metrics like views and clicks"},{name:"Default Theme",included:!0,description:"Use the default Dukancard theme"},{name:"Delivery Hours",included:!0,description:"Set and display your delivery hours"},{name:"Business Hours",included:!0,description:"Set and display your business hours"},{name:"Theme Customization",included:!1,description:"Customize your card theme and colors"},{name:"Enhanced Analytics",included:!1,description:"View detailed metrics including product views"},{name:"Advanced Analytics",included:!1,description:"Access comprehensive business insights"},{name:"Photo Gallery",included:!0,limit:3,description:"Upload and display up to 3 images in your gallery"},{name:"Dukancard Branding",included:!0,description:"Dukancard branding on your business card"}]},{id:"growth",name:"Growth",description:"Advanced features for growing businesses",recommended:!0,razorpayPlanIds:s.growth,pricing:{monthly:499,yearly:4990},features:[{name:"Digital Business Card",included:!0,description:"Premium digital business card with enhanced features"},{name:"QR Code for Sharing",included:!0,description:"Shareable QR code for your business card"},{name:"Social Media Links",included:!0,description:"Add links to your social media profiles"},{name:"Product Listings",included:!0,limit:50,description:"Showcase your products or services (up to 50)"},{name:"Customer Subscriptions",included:!0,description:"Allow customers to subscribe to your business"},{name:"Ratings & Reviews",included:!0,description:"Collect and display customer reviews"},{name:"Like Feature",included:!0,description:"Let customers like your business card"},{name:"Basic Analytics",included:!0,description:"View basic metrics like views and clicks"},{name:"Default Theme",included:!0,description:"Use the default Dukancard theme"},{name:"Delivery Hours",included:!0,description:"Set and display your delivery hours"},{name:"Business Hours",included:!0,description:"Set and display your business hours"},{name:"Theme Customization",included:!1,description:"Customize your card theme and colors"},{name:"Enhanced Analytics",included:!0,description:"View detailed metrics including product views"},{name:"Advanced Analytics",included:!1,description:"Access comprehensive business insights"},{name:"Photo Gallery",included:!0,limit:10,description:"Upload and display up to 10 images"},{name:"Dukancard Branding",included:!0,description:"Dukancard branding on your business card"}]},{id:"pro",name:"Pro",description:"Premium features for established businesses",razorpayPlanIds:s.pro,pricing:{monthly:1999,yearly:19990},features:[{name:"Digital Business Card",included:!0,description:"Elite digital business card with premium features"},{name:"QR Code for Sharing",included:!0,description:"Shareable QR code for your business card"},{name:"Social Media Links",included:!0,description:"Add links to your social media profiles"},{name:"Product Listings",included:!0,limit:"unlimited",description:"Showcase unlimited products or services"},{name:"Customer Subscriptions",included:!0,description:"Allow customers to subscribe to your business"},{name:"Ratings & Reviews",included:!0,description:"Collect and display customer reviews"},{name:"Like Feature",included:!0,description:"Let customers like your business card"},{name:"Basic Analytics",included:!0,description:"View basic metrics like views and clicks"},{name:"Default Theme",included:!0,description:"Use the default Dukancard theme"},{name:"Delivery Hours",included:!0,description:"Set and display your delivery hours"},{name:"Business Hours",included:!0,description:"Set and display your business hours"},{name:"Theme Customization",included:!0,description:"Customize your card theme and colors"},{name:"Enhanced Analytics",included:!0,description:"View detailed metrics including product views"},{name:"Advanced Analytics",included:!0,description:"Access comprehensive business insights"},{name:"Photo Gallery",included:!0,limit:50,description:"Upload and display up to 50 images"},{name:"Priority Support",included:!0,description:"Priority email and chat support"},{name:"Dukancard Branding",included:!1,description:"No Dukancard branding on your business card"}]},{id:"enterprise",name:"Enterprise",description:"Custom solutions for large businesses",razorpayPlanIds:s.enterprise,pricing:{monthly:0,yearly:0},features:[{name:"Digital Business Card",included:!0,description:"Enterprise-grade digital business card with all premium features"},{name:"QR Code for Sharing",included:!0,description:"Shareable QR code for your business card"},{name:"Social Media Links",included:!0,description:"Add links to your social media profiles"},{name:"Product Listings",included:!0,limit:"unlimited",description:"Showcase unlimited products or services"},{name:"Customer Subscriptions",included:!0,description:"Allow customers to subscribe to your business"},{name:"Ratings & Reviews",included:!0,description:"Collect and display customer reviews"},{name:"Like Feature",included:!0,description:"Let customers like your business card"},{name:"Basic Analytics",included:!0,description:"View basic metrics like views and clicks"},{name:"Default Theme",included:!0,description:"Use the default Dukancard theme"},{name:"Delivery Hours",included:!0,description:"Set and display your delivery hours"},{name:"Business Hours",included:!0,description:"Set and display your business hours"},{name:"Theme Customization",included:!0,description:"Customize your card theme and colors"},{name:"Enhanced Analytics",included:!0,description:"View detailed metrics including product views"},{name:"Advanced Analytics",included:!0,description:"Access comprehensive business insights"},{name:"Photo Gallery",included:!0,limit:100,description:"Upload and display up to 100 images"},{name:"Dedicated Account Manager",included:!0,description:"Get a dedicated account manager"},{name:"Custom Analytics Dashboard",included:!0,description:"Get a custom analytics dashboard"},{name:"24/7 Priority Support",included:!0,description:"24/7 priority support"},{name:"White-Label Option",included:!0,description:"Use your own branding instead of Dukancard"},{name:"Dukancard Branding",included:!1,description:"No Dukancard branding on your business card"}]}];function r(e){return d.find(i=>i.razorpayPlanIds.monthly===e||i.razorpayPlanIds.yearly===e)}function a(e,i){if("free"===e)return"monthly"===i?"free-plan-monthly":"free-plan-yearly";if("basic"===e)return"monthly"===i?"plan_QO9rDMTSLeT34b":"plan_QO9sfgFBnEFATA";if("growth"===e)return"monthly"===i?"plan_QbnOd77S3FVeUc":"plan_QbnOuE7iogOGTq";if("pro"===e)return"monthly"===i?"plan_QbnP83wvmzUOqM":"plan_QbnPJinNt66Pik";throw Error(`Invalid plan selected: ${e}`)}},11798:(e,i,n)=>{n.d(i,{ew:()=>d});var s=n(63032);class d{static shouldHaveActiveSubscription(e,i=s.v.FREE){return i!==s.v.FREE&&e!==s.d.TRIAL&&[s.d.ACTIVE].includes(e)}static isTerminalStatus(e){return[s.d.CANCELLED,s.d.EXPIRED,s.d.COMPLETED].includes(e)}static isTrialStatus(e){return e===s.d.TRIAL}static isFreeStatus(e,i){return i===s.v.FREE||"free"===e}static getAccessLevel(e,i=s.v.FREE){return i===s.v.FREE?"free":e===s.d.TRIAL?"trial":this.shouldHaveActiveSubscription(e,i)?"paid":"free"}static isActivePaidSubscription(e,i=s.v.FREE){return this.shouldHaveActiveSubscription(e,i)}static isValidStatusTransition(e,i){return!this.isTerminalStatus(e)||!!this.isTerminalStatus(i)}}},14719:(e,i,n)=>{n.d(i,{A:()=>s});let s=(0,n(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},39390:(e,i,n)=>{n.d(i,{J:()=>a});var s=n(60687);n(43210);var d=n(78148),r=n(96241);function a({className:e,...i}){return(0,s.jsx)(d.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...i})}},60043:(e,i,n)=>{n.d(i,{Nh:()=>d,kn:()=>r});var s=n(8174);let d=e=>s.NB.map(i=>(function(e,i){let n="enterprise"===e.id,s=e.pricing.monthly,d=e.pricing.yearly;return{id:e.id,name:`${e.name} Plan`,razorpayPlanIds:{monthly:e.razorpayPlanIds.monthly||null,yearly:e.razorpayPlanIds.yearly||null},price:n?"Contact Sales":"monthly"===i?`₹${e.pricing.monthly.toLocaleString("en-IN")}`:`₹${e.pricing.yearly.toLocaleString("en-IN")}`,yearlyPrice:n?"Contact Sales":`₹${e.pricing.yearly.toLocaleString("en-IN")}`,period:n?"":"monthly"===i?"/month":"/year",savings:n?void 0:"yearly"===i?`Save ₹${(n?0:12*s-d).toLocaleString("en-IN")}`:void 0,description:e.description,features:e.features.map(e=>{if("Product Listings"===e.name&&e.included){let i="unlimited"===e.limit?"Unlimited":e.limit;return`Product/Service Listings (up to ${i})`}return e.included?e.name:`❌ ${e.name}`}),button:n?"Contact Sales":"Subscribe Now",available:!0,featured:"free"===e.id||"basic"===e.id||"growth"===e.id||"pro"===e.id,recommended:e.recommended||!1,mostPopular:e.recommended||!1}})(i,e)),r=d("monthly")},63032:(e,i,n)=>{n.d(i,{d:()=>s,v:()=>d});let s={ACTIVE:"active",AUTHENTICATED:"authenticated",TRIAL:"trial",PENDING:"pending",HALTED:"halted",CANCELLED:"cancelled",EXPIRED:"expired",COMPLETED:"completed",PAYMENT_FAILED:"payment_failed",CANCELLATION_SCHEDULED:"cancellation_scheduled"},d={FREE:"free",BASIC:"basic",GROWTH:"growth",PRO:"pro",ENTERPRISE:"enterprise"}},78148:(e,i,n)=>{n.d(i,{b:()=>t});var s=n(43210),d=n(3416),r=n(60687),a=s.forwardRef((e,i)=>(0,r.jsx)(d.sG.label,{...e,ref:i,onMouseDown:i=>{i.target.closest("button, input, select, textarea")||(e.onMouseDown?.(i),!i.defaultPrevented&&i.detail>1&&i.preventDefault())}}));a.displayName="Label";var t=a}};