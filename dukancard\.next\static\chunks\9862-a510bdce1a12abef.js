"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9862],{35169:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,n)=>{var r=n(18999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},37108:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},60760:(e,t,n)=>{n.d(t,{N:()=>y});var r=n(95155),i=n(12115),o=n(90869),u=n(82885),c=n(97494),a=n(80845),s=n(51508);class l extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=e instanceof HTMLElement&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f(e){let{children:t,isPresent:n,anchorX:o}=e,u=(0,i.useId)(),c=(0,i.useRef)(null),a=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=(0,i.useContext)(s.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:s}=a.current;if(n||!c.current||!e||!t)return;c.current.dataset.motionPopId=u;let l=document.createElement("style");return f&&(l.nonce=f),document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(u,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(s),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[n]),(0,r.jsx)(l,{isPresent:n,childRef:c,sizeRef:a,children:i.cloneElement(t,{ref:c})})}let d=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:c,custom:s,presenceAffectsLayout:l,mode:d,anchorX:m}=e,h=(0,u.M)(p),g=(0,i.useId)(),y=!0,v=(0,i.useMemo)(()=>(y=!1,{id:g,initial:n,isPresent:o,custom:s,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;c&&c()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[o,h,c]);return l&&y&&(v={...v}),(0,i.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[o]),i.useEffect(()=>{o||h.size||!c||c()},[o]),"popLayout"===d&&(t=(0,r.jsx)(f,{isPresent:o,anchorX:m,children:t})),(0,r.jsx)(a.t.Provider,{value:v,children:t})};function p(){return new Map}var m=n(32082);let h=e=>e.key||"";function g(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let y=e=>{let{children:t,custom:n,initial:a=!0,onExitComplete:s,presenceAffectsLayout:l=!0,mode:f="sync",propagate:p=!1,anchorX:y="left"}=e,[v,x]=(0,m.xQ)(p),b=(0,i.useMemo)(()=>g(t),[t]),w=p&&!v?[]:b.map(h),E=(0,i.useRef)(!0),S=(0,i.useRef)(b),k=(0,u.M)(()=>new Map),[A,M]=(0,i.useState)(b),[P,L]=(0,i.useState)(b);(0,c.E)(()=>{E.current=!1,S.current=b;for(let e=0;e<P.length;e++){let t=h(P[e]);w.includes(t)?k.delete(t):!0!==k.get(t)&&k.set(t,!1)}},[P,w.length,w.join("-")]);let O=[];if(b!==A){let e=[...b];for(let t=0;t<P.length;t++){let n=P[t],r=h(n);w.includes(r)||(e.splice(t,0,n),O.push(n))}return"wait"===f&&O.length&&(e=O),L(g(e)),M(b),null}let{forceRender:D}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:P.map(e=>{let t=h(e),i=(!p||!!v)&&(b===P||w.includes(t));return(0,r.jsx)(d,{isPresent:i,initial:(!E.current||!!a)&&void 0,custom:n,presenceAffectsLayout:l,mode:f,onExitComplete:i?void 0:()=>{if(!k.has(t))return;k.set(t,!0);let e=!0;k.forEach(t=>{t||(e=!1)}),e&&(null==D||D(),L(S.current),p&&(null==x||x()),s&&s())},anchorX:y,children:e},t)})})}},85005:(e,t,n)=>{n.d(t,{A:()=>A});var r=n(12115);function i(e){return"[object Object]"===Object.prototype.toString.call(e)||Array.isArray(e)}function o(e,t){let n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&JSON.stringify(Object.keys(e.breakpoints||{}))===JSON.stringify(Object.keys(t.breakpoints||{}))&&n.every(n=>{let r=e[n],u=t[n];return"function"==typeof r?`${r}`==`${u}`:i(r)&&i(u)?o(r,u):r===u})}function u(e){return e.concat().sort((e,t)=>e.name>t.name?1:-1).map(e=>e.options)}function c(e){return"number"==typeof e}function a(e){return"string"==typeof e}function s(e){return"boolean"==typeof e}function l(e){return"[object Object]"===Object.prototype.toString.call(e)}function f(e){return Math.abs(e)}function d(e){return Math.sign(e)}function p(e){return y(e).map(Number)}function m(e){return e[h(e)]}function h(e){return Math.max(0,e.length-1)}function g(e,t=0){return Array.from(Array(e),(e,n)=>t+n)}function y(e){return Object.keys(e)}function v(e,t){return void 0!==t.MouseEvent&&e instanceof t.MouseEvent}function x(){let e=[],t={add:function(n,r,i,o={passive:!0}){let u;return"addEventListener"in n?(n.addEventListener(r,i,o),u=()=>n.removeEventListener(r,i,o)):(n.addListener(i),u=()=>n.removeListener(i)),e.push(u),t},clear:function(){e=e.filter(e=>e())}};return t}function b(e=0,t=0){let n=f(e-t);function r(n){return n<e||n>t}return{length:n,max:t,min:e,constrain:function(n){return r(n)?n<e?e:t:n},reachedAny:r,reachedMax:function(e){return e>t},reachedMin:function(t){return t<e},removeOffset:function(e){return n?e-n*Math.ceil((e-t)/n):e}}}function w(e){let t=e;function n(e){return c(e)?e:e.get()}return{get:function(){return t},set:function(e){t=n(e)},add:function(e){t+=n(e)},subtract:function(e){t-=n(e)}}}function E(e,t){let n="x"===e.scroll?function(e){return`translate3d(${e}px,0px,0px)`}:function(e){return`translate3d(0px,${e}px,0px)`},r=t.style,i=null,o=!1;return{clear:function(){!o&&(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))},to:function(t){if(o)return;let u=Math.round(100*e.direction(t))/100;u!==i&&(r.transform=n(u),i=u)},toggleActive:function(e){o=!e}}}let S={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function k(e,t,n){let r,i,o,u,A,M=e.ownerDocument,P=M.defaultView,L=function(e){function t(e,t){return function e(t,n){return[t,n].reduce((t,n)=>(y(n).forEach(r=>{let i=t[r],o=n[r],u=l(i)&&l(o);t[r]=u?e(i,o):o}),t),{})}(e,t||{})}return{mergeOptions:t,optionsAtMedia:function(n){let r=n.breakpoints||{},i=y(r).filter(t=>e.matchMedia(t).matches).map(e=>r[e]).reduce((e,n)=>t(e,n),{});return t(n,i)},optionsMediaQueries:function(t){return t.map(e=>y(e.breakpoints||{})).reduce((e,t)=>e.concat(t),[]).map(e.matchMedia)}}}(P),O=(A=[],{init:function(e,t){return(A=t.filter(({options:e})=>!1!==L.optionsAtMedia(e).active)).forEach(t=>t.init(e,L)),t.reduce((e,t)=>Object.assign(e,{[t.name]:t}),{})},destroy:function(){A=A.filter(e=>e.destroy())}}),D=x(),I=function(){let e,t={},n={init:function(t){e=t},emit:function(r){return(t[r]||[]).forEach(t=>t(e,r)),n},off:function(e,r){return t[e]=(t[e]||[]).filter(e=>e!==r),n},on:function(e,r){return t[e]=(t[e]||[]).concat([r]),n},clear:function(){t={}}};return n}(),{mergeOptions:j,optionsAtMedia:F,optionsMediaQueries:R}=L,{on:C,off:z,emit:T}=I,N=!1,H=j(S,k.globalOptions),V=j(H),q=[];function B(t,n){if(N)return;V=F(H=j(H,t)),q=n||q;let{container:l,slides:S}=V;o=(a(l)?e.querySelector(l):l)||e.children[0];let k=a(S)?o.querySelectorAll(S):S;u=[].slice.call(k||o.children),r=function t(n){let r=function(e,t,n,r,i,o,u){let l,S,{align:k,axis:A,direction:M,startIndex:P,loop:L,duration:O,dragFree:D,dragThreshold:I,inViewThreshold:j,slidesToScroll:F,skipSnaps:R,containScroll:C,watchResize:z,watchSlides:T,watchDrag:N,watchFocus:H}=o,V={measure:function(e){let{offsetTop:t,offsetLeft:n,offsetWidth:r,offsetHeight:i}=e;return{top:t,right:n+r,bottom:t+i,left:n,width:r,height:i}}},q=V.measure(t),B=n.map(V.measure),U=function(e,t){let n="rtl"===t,r="y"===e,i=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(e){let{height:t,width:n}=e;return r?t:n},direction:function(e){return e*i}}}(A,M),$=U.measureSize(q),X={measure:function(e){return e/100*$}},_=function(e,t){let n={start:function(){return 0},center:function(e){return(t-e)/2},end:function(e){return t-e}};return{measure:function(r,i){return a(e)?n[e](r):e(t,r,i)}}}(k,$),Q=!L&&!!C,{slideSizes:J,slideSizesWithGaps:W,startGap:Y,endGap:G}=function(e,t,n,r,i,o){let{measureSize:u,startEdge:c,endEdge:a}=e,s=n[0]&&i,l=function(){if(!s)return 0;let e=n[0];return f(t[c]-e[c])}(),d=s?parseFloat(o.getComputedStyle(m(r)).getPropertyValue(`margin-${a}`)):0,p=n.map(u),g=n.map((e,t,n)=>{let r=t===h(n);return t?r?p[t]+d:n[t+1][c]-e[c]:p[t]+l}).map(f);return{slideSizes:p,slideSizesWithGaps:g,startGap:l,endGap:d}}(U,q,B,n,L||!!C,i),K=function(e,t,n,r,i,o,u,a,s){let{startEdge:l,endEdge:d,direction:g}=e,y=c(n);return{groupSlides:function(e){return y?p(e).filter(e=>e%n==0).map(t=>e.slice(t,t+n)):e.length?p(e).reduce((n,c,s)=>{let p=m(n)||0,y=c===h(e),v=i[l]-o[p][l],x=i[l]-o[c][d],b=r||0!==p?0:g(u),w=f(x-(!r&&y?g(a):0)-(v+b));return s&&w>t+2&&n.push(c),y&&n.push(e.length),n},[]).map((t,n,r)=>{let i=Math.max(r[n-1]||0);return e.slice(i,t)}):[]}}}(U,$,F,L,q,B,Y,G,0),{snaps:Z,snapsAligned:ee}=function(e,t,n,r,i){let{startEdge:o,endEdge:u}=e,{groupSlides:c}=i,a=c(r).map(e=>m(e)[u]-e[0][o]).map(f).map(t.measure),s=r.map(e=>n[o]-e[o]).map(e=>-f(e)),l=c(s).map(e=>e[0]).map((e,t)=>e+a[t]);return{snaps:s,snapsAligned:l}}(U,_,q,B,K),et=-m(Z)+m(W),{snapsContained:en,scrollContainLimit:er}=function(e,t,n,r,i){let o=b(-t+e,0),u=n.map((e,t)=>{let{min:r,max:i}=o,u=o.constrain(e),c=t===h(n);return t?c||function(e,t){return 1>=f(e-t)}(r,u)?r:function(e,t){return 1>=f(e-t)}(i,u)?i:u:i}).map(e=>parseFloat(e.toFixed(3))),c=function(){let e=u[0],t=m(u);return b(u.lastIndexOf(e),u.indexOf(t)+1)}();function a(e,t){return 1>=f(e-t)}return{snapsContained:function(){if(t<=e+2)return[o.max];if("keepSnaps"===r)return u;let{min:n,max:i}=c;return u.slice(n,i)}(),scrollContainLimit:c}}($,et,ee,C,0),ei=Q?en:ee,{limit:eo}=function(e,t,n){let r=t[0];return{limit:b(n?r-e:m(t),r)}}(et,ei,L),eu=function e(t,n,r){let{constrain:i}=b(0,t),o=t+1,u=c(n);function c(e){return r?f((o+e)%o):i(e)}function a(){return e(t,u,r)}let s={get:function(){return u},set:function(e){return u=c(e),s},add:function(e){return a().set(u+e)},clone:a};return s}(h(ei),P,L),ec=eu.clone(),ea=p(n),es=({dragHandler:e,scrollBody:t,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(e.pointerDown()),t.seek()},el=({scrollBody:e,translate:t,location:n,offsetLocation:r,previousLocation:i,scrollLooper:o,slideLooper:u,dragHandler:c,animation:a,eventHandler:s,scrollBounds:l,options:{loop:f}},d)=>{let p=e.settled(),m=!l.shouldConstrain(),h=f?p:p&&m,g=h&&!c.pointerDown();g&&a.stop();let y=n.get()*d+i.get()*(1-d);r.set(y),f&&(o.loop(e.direction()),u.loop()),t.to(r.get()),g&&s.emit("settle"),h||s.emit("scroll")},ef=function(e,t,n,r){let i=x(),o=1e3/60,u=null,c=0,a=0;function s(e){if(!a)return;u||(u=e,n(),n());let i=e-u;for(u=e,c+=i;c>=o;)n(),c-=o;r(c/o),a&&(a=t.requestAnimationFrame(s))}function l(){t.cancelAnimationFrame(a),u=null,c=0,a=0}return{init:function(){i.add(e,"visibilitychange",()=>{e.hidden&&(u=null,c=0)})},destroy:function(){l(),i.clear()},start:function(){a||(a=t.requestAnimationFrame(s))},stop:l,update:n,render:r}}(r,i,()=>es(eA),e=>el(eA,e)),ed=ei[eu.get()],ep=w(ed),em=w(ed),eh=w(ed),eg=w(ed),ey=function(e,t,n,r,i,o){let u=0,c=0,a=i,s=.68,l=e.get(),p=0;function m(e){return a=e,g}function h(e){return s=e,g}let g={direction:function(){return c},duration:function(){return a},velocity:function(){return u},seek:function(){let t=r.get()-e.get(),i=0;return a?(n.set(e),u+=t/a,u*=s,l+=u,e.add(u),i=l-p):(u=0,n.set(r),e.set(r),i=t),c=d(i),p=l,g},settled:function(){return .001>f(r.get()-t.get())},useBaseFriction:function(){return h(.68)},useBaseDuration:function(){return m(i)},useFriction:h,useDuration:m};return g}(ep,eh,em,eg,O,.68),ev=function(e,t,n,r,i){let{reachedAny:o,removeOffset:u,constrain:c}=r;function a(e){return e.concat().sort((e,t)=>f(e)-f(t))[0]}function s(t,r){let i=[t,t+n,t-n];if(!e)return t;if(!r)return a(i);let o=i.filter(e=>d(e)===r);return o.length?a(o):m(i)-n}return{byDistance:function(n,r){let a=i.get()+n,{index:l,distance:d}=function(n){let r=e?u(n):c(n),{index:i}=t.map((e,t)=>({diff:s(e-r,0),index:t})).sort((e,t)=>f(e.diff)-f(t.diff))[0];return{index:i,distance:r}}(a),p=!e&&o(a);if(!r||p)return{index:l,distance:n};let m=n+s(t[l]-d,0);return{index:l,distance:m}},byIndex:function(e,n){let r=s(t[e]-i.get(),n);return{index:e,distance:r}},shortcut:s}}(L,ei,et,eo,eg),ex=function(e,t,n,r,i,o,u){function c(i){let c=i.distance,a=i.index!==t.get();o.add(c),c&&(r.duration()?e.start():(e.update(),e.render(1),e.update())),a&&(n.set(t.get()),t.set(i.index),u.emit("select"))}return{distance:function(e,t){c(i.byDistance(e,t))},index:function(e,n){let r=t.clone().set(e);c(i.byIndex(r.get(),n))}}}(ef,eu,ec,ey,ev,eg,u),eb=function(e){let{max:t,length:n}=e;return{get:function(e){return n?-((e-t)/n):0}}}(eo),ew=x(),eE=function(e,t,n,r){let i,o={},u=null,c=null,a=!1;return{init:function(){i=new IntersectionObserver(e=>{a||(e.forEach(e=>{o[t.indexOf(e.target)]=e}),u=null,c=null,n.emit("slidesInView"))},{root:e.parentElement,threshold:r}),t.forEach(e=>i.observe(e))},destroy:function(){i&&i.disconnect(),a=!0},get:function(e=!0){if(e&&u)return u;if(!e&&c)return c;let t=y(o).reduce((t,n)=>{let r=parseInt(n),{isIntersecting:i}=o[r];return(e&&i||!e&&!i)&&t.push(r),t},[]);return e&&(u=t),e||(c=t),t}}}(t,n,u,j),{slideRegistry:eS}=function(e,t,n,r,i,o){let{groupSlides:u}=i,{min:c,max:a}=r;return{slideRegistry:function(){let r=u(o);return 1===n.length?[o]:e&&"keepSnaps"!==t?r.slice(c,a).map((e,t,n)=>{let r=t===h(n);return t?r?g(h(o)-m(n)[0]+1,m(n)[0]):e:g(m(n[0])+1)}):r}()}}(Q,C,ei,er,K,ea),ek=function(e,t,n,r,i,o,u,a){let l={passive:!0,capture:!0},f=0;function d(e){"Tab"===e.code&&(f=new Date().getTime())}return{init:function(p){a&&(o.add(document,"keydown",d,!1),t.forEach((t,d)=>{o.add(t,"focus",t=>{(s(a)||a(p,t))&&function(t){if(new Date().getTime()-f>10)return;u.emit("slideFocusStart"),e.scrollLeft=0;let o=n.findIndex(e=>e.includes(t));c(o)&&(i.useDuration(0),r.index(o,0),u.emit("slideFocus"))}(d)},l)}))}}}(e,n,eS,ex,ey,ew,u,H),eA={ownerDocument:r,ownerWindow:i,eventHandler:u,containerRect:q,slideRects:B,animation:ef,axis:U,dragHandler:function(e,t,n,r,i,o,u,c,a,l,p,m,h,g,y,w,E,S,k){let{cross:A,direction:M}=e,P=["INPUT","SELECT","TEXTAREA"],L={passive:!1},O=x(),D=x(),I=b(50,225).constrain(g.measure(20)),j={mouse:300,touch:400},F={mouse:500,touch:600},R=y?43:25,C=!1,z=0,T=0,N=!1,H=!1,V=!1,q=!1;function B(e){if(!v(e,r)&&e.touches.length>=2)return U(e);let t=o.readPoint(e),n=o.readPoint(e,A),u=f(t-z),a=f(n-T);if(!H&&!q&&(!e.cancelable||!(H=u>a)))return U(e);let s=o.pointerMove(e);u>w&&(V=!0),l.useFriction(.3).useDuration(.75),c.start(),i.add(M(s)),e.preventDefault()}function U(e){let t=p.byDistance(0,!1).index!==m.get(),n=o.pointerUp(e)*(y?F:j)[q?"mouse":"touch"],r=function(e,t){let n=m.add(-1*d(e)),r=p.byDistance(e,!y).distance;return y||f(e)<I?r:E&&t?.5*r:p.byIndex(n.get(),0).distance}(M(n),t),i=function(e,t){var n,r;if(0===e||0===t||f(e)<=f(t))return 0;let i=(n=f(e),r=f(t),f(n-r));return f(i/e)}(n,r);H=!1,N=!1,D.clear(),l.useDuration(R-10*i).useFriction(.68+i/50),a.distance(r,!y),q=!1,h.emit("pointerUp")}function $(e){V&&(e.stopPropagation(),e.preventDefault(),V=!1)}return{init:function(e){k&&O.add(t,"dragstart",e=>e.preventDefault(),L).add(t,"touchmove",()=>void 0,L).add(t,"touchend",()=>void 0).add(t,"touchstart",c).add(t,"mousedown",c).add(t,"touchcancel",U).add(t,"contextmenu",U).add(t,"click",$,!0);function c(c){(s(k)||k(e,c))&&function(e){let c=v(e,r);if((q=c,V=y&&c&&!e.buttons&&C,C=f(i.get()-u.get())>=2,!c||0===e.button)&&!function(e){let t=e.nodeName||"";return P.includes(t)}(e.target)){N=!0,o.pointerDown(e),l.useFriction(0).useDuration(0),i.set(u);let r=q?n:t;D.add(r,"touchmove",B,L).add(r,"touchend",U).add(r,"mousemove",B,L).add(r,"mouseup",U),z=o.readPoint(e),T=o.readPoint(e,A),h.emit("pointerDown")}}(c)}},destroy:function(){O.clear(),D.clear()},pointerDown:function(){return N}}}(U,e,r,i,eg,function(e,t){let n,r;function i(e){return e.timeStamp}function o(n,r){let i=r||e.scroll,o=`client${"x"===i?"X":"Y"}`;return(v(n,t)?n:n.touches[0])[o]}return{pointerDown:function(e){return n=e,r=e,o(e)},pointerMove:function(e){let t=o(e)-o(r),u=i(e)-i(n)>170;return r=e,u&&(n=e),t},pointerUp:function(e){if(!n||!r)return 0;let t=o(r)-o(n),u=i(e)-i(n),c=i(e)-i(r)>170,a=t/u;return u&&!c&&f(a)>.1?a:0},readPoint:o}}(U,i),ep,ef,ex,ey,ev,eu,u,X,D,I,R,0,N),eventStore:ew,percentOfView:X,index:eu,indexPrevious:ec,limit:eo,location:ep,offsetLocation:eh,previousLocation:em,options:o,resizeHandler:function(e,t,n,r,i,o,u){let c,a,l=[e].concat(r),d=[],p=!1;function m(e){return i.measureSize(u.measure(e))}return{init:function(i){o&&(a=m(e),d=r.map(m),c=new ResizeObserver(n=>{(s(o)||o(i,n))&&function(n){for(let o of n){if(p)return;let n=o.target===e,u=r.indexOf(o.target),c=n?a:d[u];if(f(m(n?e:r[u])-c)>=.5){i.reInit(),t.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{l.forEach(e=>c.observe(e))}))},destroy:function(){p=!0,c&&c.disconnect()}}}(t,u,i,n,U,z,V),scrollBody:ey,scrollBounds:function(e,t,n,r,i){let o=i.measure(10),u=i.measure(50),c=b(.1,.99),a=!1;function s(){return!a&&!!e.reachedAny(n.get())&&!!e.reachedAny(t.get())}return{shouldConstrain:s,constrain:function(i){if(!s())return;let a=e.reachedMin(t.get())?"min":"max",l=f(e[a]-t.get()),d=n.get()-t.get(),p=c.constrain(l/u);n.subtract(d*p),!i&&f(d)<o&&(n.set(e.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(e){a=!e}}}(eo,eh,eg,ey,X),scrollLooper:function(e,t,n,r){let{reachedMin:i,reachedMax:o}=b(t.min+.1,t.max+.1);return{loop:function(t){if(!(1===t?o(n.get()):-1===t&&i(n.get())))return;let u=-1*t*e;r.forEach(e=>e.add(u))}}}(et,eo,eh,[ep,eh,em,eg]),scrollProgress:eb,scrollSnapList:ei.map(eb.get),scrollSnaps:ei,scrollTarget:ev,scrollTo:ex,slideLooper:function(e,t,n,r,i,o,u,c,a){let s=p(i),l=p(i).reverse(),f=h(m(l,u[0]),n,!1).concat(h(m(s,t-u[0]-1),-n,!0));function d(e,t){return e.reduce((e,t)=>e-i[t],t)}function m(e,t){return e.reduce((e,n)=>d(e,t)>0?e.concat([n]):e,[])}function h(i,u,s){let l=o.map((e,n)=>({start:e-r[n]+.5+u,end:e+t-.5+u}));return i.map(t=>{let r=s?0:-n,i=s?n:0,o=l[t][s?"end":"start"];return{index:t,loopPoint:o,slideLocation:w(-1),translate:E(e,a[t]),target:()=>c.get()>o?r:i}})}return{canLoop:function(){return f.every(({index:e})=>.1>=d(s.filter(t=>t!==e),t))},clear:function(){f.forEach(e=>e.translate.clear())},loop:function(){f.forEach(e=>{let{target:t,translate:n,slideLocation:r}=e,i=t();i!==r.get()&&(n.to(i),r.set(i))})},loopPoints:f}}(U,$,et,J,W,Z,ei,eh,n),slideFocus:ek,slidesHandler:(S=!1,{init:function(e){T&&(l=new MutationObserver(t=>{!S&&(s(T)||T(e,t))&&function(t){for(let n of t)if("childList"===n.type){e.reInit(),u.emit("slidesChanged");break}}(t)})).observe(t,{childList:!0})},destroy:function(){l&&l.disconnect(),S=!0}}),slidesInView:eE,slideIndexes:ea,slideRegistry:eS,slidesToScroll:K,target:eg,translate:E(U,t)};return eA}(e,o,u,M,P,n,I);return n.loop&&!r.slideLooper.canLoop()?t(Object.assign({},n,{loop:!1})):r}(V),R([H,...q.map(({options:e})=>e)]).forEach(e=>D.add(e,"change",U)),V.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(Q),r.eventHandler.init(Q),r.resizeHandler.init(Q),r.slidesHandler.init(Q),r.options.loop&&r.slideLooper.loop(),o.offsetParent&&u.length&&r.dragHandler.init(Q),i=O.init(Q,q))}function U(e,t){let n=_();$(),B(j({startIndex:n},e),t),I.emit("reInit")}function $(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),O.destroy(),D.clear()}function X(e,t,n){V.active&&!N&&(r.scrollBody.useBaseFriction().useDuration(!0===t?0:V.duration),r.scrollTo.index(e,n||0))}function _(){return r.index.get()}let Q={canScrollNext:function(){return r.index.add(1).get()!==_()},canScrollPrev:function(){return r.index.add(-1).get()!==_()},containerNode:function(){return o},internalEngine:function(){return r},destroy:function(){N||(N=!0,D.clear(),$(),I.emit("destroy"),I.clear())},off:z,on:C,emit:T,plugins:function(){return i},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:U,rootNode:function(){return e},scrollNext:function(e){X(r.index.add(1).get(),e,-1)},scrollPrev:function(e){X(r.index.add(-1).get(),e,1)},scrollProgress:function(){return r.scrollProgress.get(r.offsetLocation.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:X,selectedScrollSnap:_,slideNodes:function(){return u},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return B(t,n),setTimeout(()=>I.emit("init"),0),Q}function A(e={},t=[]){let n=(0,r.useRef)(e),i=(0,r.useRef)(t),[c,a]=(0,r.useState)(),[s,l]=(0,r.useState)(),f=(0,r.useCallback)(()=>{c&&c.reInit(n.current,i.current)},[c]);return(0,r.useEffect)(()=>{o(n.current,e)||(n.current=e,f())},[e,f]),(0,r.useEffect)(()=>{!function(e,t){if(e.length!==t.length)return!1;let n=u(e),r=u(t);return n.every((e,t)=>o(e,r[t]))}(i.current,t)&&(i.current=t,f())},[t,f]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&s){k.globalOptions=A.globalOptions;let e=k(s,n.current,i.current);return a(e),()=>e.destroy()}a(void 0)},[s,a]),[l,c]}k.globalOptions=void 0,A.globalOptions=void 0},92138:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}}]);