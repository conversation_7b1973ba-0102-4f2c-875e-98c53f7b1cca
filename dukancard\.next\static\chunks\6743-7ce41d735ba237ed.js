(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6743],{39048:(e,s,r)=>{"use strict";r.d(s,{Y:()=>t});var a=r(12115);function t(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{threshold:s=10,initialDirection:r="up"}=e,[t,n]=(0,a.useState)({scrollDirection:r,isScrolled:!1,scrollY:0});return(0,a.useEffect)(()=>{let e=window.scrollY,r=!1,a=()=>{let a=window.scrollY,i=a>e?"down":"up",o=a>s;(i!==t.scrollDirection||Math.abs(a-e)>s||o!==t.isScrolled)&&n({scrollDirection:i,isScrolled:o,scrollY:a}),e=a>0?a:0,r=!1},i=()=>{r||(requestAnimationFrame(a),r=!0)};return window.addEventListener("scroll",i),()=>window.removeEventListener("scroll",i)},[t.scrollDirection,t.isScrolled,s]),t}},53533:()=>{},66674:(e,s,r)=>{"use strict";r.d(s,{ThemeToggle:()=>u});var a=r(95155);r(12115);var t=r(62098),n=r(93509),i=r(14738),o=r(51362),l=r(87691),c=r(97168),d=r(67133);function u(){let{variant:e="default"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{setTheme:s,theme:r}=(0,o.D)();return(0,l.a)()&&"default"===e?(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 border border-neutral-200/50 dark:border-neutral-700/50 backdrop-blur-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-muted text-foreground",children:"light"===r?(0,a.jsx)(t.A,{className:"h-5 w-5"}):"dark"===r?(0,a.jsx)(n.A,{className:"h-5 w-5"}):(0,a.jsx)(i.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium text-foreground",children:"Theme"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:r||"system"})]})]}),(0,a.jsxs)(d.rI,{children:[(0,a.jsx)(d.ty,{asChild:!0,children:(0,a.jsx)(c.$,{variant:"ghost",size:"sm",className:"h-8 px-3",children:"Change"})}),(0,a.jsxs)(d.SQ,{align:"end",className:"w-40",children:[(0,a.jsxs)(d._2,{onClick:()=>s("light"),children:[(0,a.jsx)(t.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Light"})]}),(0,a.jsxs)(d._2,{onClick:()=>s("dark"),children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Dark"})]}),(0,a.jsxs)(d._2,{onClick:()=>s("system"),children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"System"})]})]})]})]})}):"dashboard"===e?(0,a.jsxs)(d.rI,{children:[(0,a.jsx)(d.ty,{asChild:!0,children:(0,a.jsxs)(c.$,{variant:"ghost",className:"h-9 w-9 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0",children:[(0,a.jsx)(t.A,{className:"h-[1.1rem] w-[1.1rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(n.A,{className:"absolute h-[1.1rem] w-[1.1rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,a.jsxs)(d.SQ,{align:"end",children:[(0,a.jsxs)(d._2,{onClick:()=>s("light"),children:[(0,a.jsx)(t.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Light"})]}),(0,a.jsxs)(d._2,{onClick:()=>s("dark"),children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Dark"})]}),(0,a.jsxs)(d._2,{onClick:()=>s("system"),children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"System"})]})]})]}):(0,a.jsxs)(d.rI,{children:[(0,a.jsx)(d.ty,{asChild:!0,children:(0,a.jsxs)(c.$,{variant:"outline",size:"icon",children:[(0,a.jsx)(t.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(n.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,a.jsxs)(d.SQ,{align:"end",children:[(0,a.jsxs)(d._2,{onClick:()=>s("light"),children:[(0,a.jsx)(t.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Light"})]}),(0,a.jsxs)(d._2,{onClick:()=>s("dark"),children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Dark"})]}),(0,a.jsxs)(d._2,{onClick:()=>s("system"),children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"System"})]})]})]})}},75168:(e,s,r)=>{"use strict";r.d(s,{U:()=>t});var a=r(20067);function t(){let e="https://rnjolcoecogzgglnblqn.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";return e&&s?(0,a.createBrowserClient)(e,s):(console.error("Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined."),(0,a.createBrowserClient)("",""))}},87691:(e,s,r)=>{"use strict";r.d(s,{a:()=>t});var a=r(12115);function t(){let[e,s]=(0,a.useState)(void 0);return(0,a.useEffect)(()=>{{let e=()=>{s(window.innerWidth<768)};e();let r=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{e()};return r.addEventListener("change",a),()=>r.removeEventListener("change",a)}},[]),null!=e&&e}},95875:(e,s,r)=>{"use strict";r.d(s,{default:()=>D});var a=r(95155),t=r(12115),n=r(6874),i=r.n(n),o=r(35695),l=r(28695),c=r(53999),d=r(87691),u=r(57340),m=r(47924),h=r(97939),x=r(5937),f=r(71007),g=r(88145),b=r(99840),p=r(97168),j=r(84355),v=r(29869),N=r(56671),w=r(98015);function y(e){let s=function(e){let s;if(!e||"string"!=typeof e)return{isValid:!1,error:"Invalid QR code data"};let r=e.trim();if(!r)return{isValid:!1,error:"Empty QR code data"};try{let e=r.startsWith("http")?r:"https://".concat(r);s=new URL(e)}catch(e){return{isValid:!1,error:"QR code does not contain a valid URL"}}if(!["dukancard.in","www.dukancard.in"].includes(s.hostname.toLowerCase()))return{isValid:!1,error:"QR code is not from Dukancard"};let a=s.pathname.split("/").filter(e=>e.length>0);if(0===a.length)return{isValid:!1,error:"QR code does not contain a business profile URL"};let t=a[0],n=function(e){if(!e||"string"!=typeof e)return{isValid:!1,error:"Business slug is required"};let s=e.trim();return s?s.length<3||s.length>50?{isValid:!1,error:"Business slug must be between 3 and 50 characters"}:/^[a-z0-9-]+$/.test(s)?s.startsWith("-")||s.endsWith("-")?{isValid:!1,error:"Business slug cannot start or end with a hyphen"}:s.includes("--")?{isValid:!1,error:"Business slug cannot contain consecutive hyphens"}:{isValid:!0}:{isValid:!1,error:"Business slug can only contain lowercase letters, numbers, and hyphens"}:{isValid:!1,error:"Business slug cannot be empty"}}(t);return n.isValid?{isValid:!0,businessSlug:t,url:s.toString()}:{isValid:!1,error:n.error||"Invalid business URL format"}}(e);if(!s.isValid){var r,a,t;let e=s.error;return(null==(r=s.error)?void 0:r.includes("not from Dukancard"))?e="This QR code is not from Dukancard. Please scan a valid Dukancard business QR code.":(null==(a=s.error)?void 0:a.includes("not contain a valid URL"))?e="Invalid QR code format. Please scan a valid Dukancard business QR code.":(null==(t=s.error)?void 0:t.includes("business profile URL"))&&(e="This QR code does not link to a business profile. Please scan a valid Dukancard business QR code."),{...s,error:e}}return s}function k(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia&&"function"==typeof navigator.mediaDevices.getUserMedia)}function C(){return window.isSecureContext||"https:"===location.protocol||"localhost"===location.hostname}async function R(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{video:!0};if(!k())throw Error("Camera not supported in this browser");if(!C())throw Error("Camera access requires HTTPS or localhost");try{let s=await navigator.mediaDevices.getUserMedia(e),r={hasCamera:!0,hasPermission:!0,isSecureContext:C(),supportedConstraints:navigator.mediaDevices.getSupportedConstraints()};return{stream:s,capabilities:r}}catch(t){var s;let e="Failed to access camera",r=!1;t instanceof Error&&("NotAllowedError"===t.name?(e="Camera access denied by user",r=!1):"NotFoundError"===t.name?e="No camera found on this device":"NotReadableError"===t.name?e="Camera is already in use by another application":"OverconstrainedError"===t.name?e="Camera does not support the requested constraints":"SecurityError"===t.name&&(e="Camera access blocked due to security restrictions"));let a={hasCamera:t instanceof Error&&"NotFoundError"!==t.name,hasPermission:r,isSecureContext:C(),supportedConstraints:(null==(s=navigator.mediaDevices)?void 0:s.getSupportedConstraints())||null,error:e};throw{error:e,capabilities:a}}}r(53533);let S=e=>{let{onScanSuccess:s,onScanError:r,onClose:n,className:i=""}=e,o=(0,t.useRef)(null),l=(0,t.useRef)(null),[c,d]=(0,t.useState)(!1),[u,m]=(0,t.useState)(null),[h,x]=(0,t.useState)(null),[f,g]=(0,t.useState)(!1),b="qr-scanner-region";return((0,t.useEffect)(()=>{(async()=>{try{let{stream:e,capabilities:s}=await R();x(s),e.getTracks().forEach(e=>e.stop()),s.error&&(m(s.error),null==r||r(s.error))}catch(s){let e=s.error||(s instanceof Error?s.message:"Failed to get camera access");x(s.capabilities||{hasCamera:!1,hasPermission:!1,isSecureContext:!1,supportedConstraints:null,error:e}),m(e),null==r||r(e)}})()},[r]),(0,t.useEffect)(()=>{if(h&&!h.error&&!c)return(async()=>{try{d(!0),m(null);let e={fps:10,qrbox:{width:250,height:250},aspectRatio:1,disableFlip:!1,supportedScanTypes:[w.a8.SCAN_TYPE_CAMERA],showTorchButtonIfSupported:!0,showZoomSliderIfSupported:!1,defaultZoomValueIfSupported:1,colorScheme:"dark"},a=async e=>{if(!f){g(!0);try{let a=y(e);if(!a.isValid){let e=a.error||"Invalid QR code";m(e),null==r||r(e),setTimeout(()=>{g(!1)},2e3);return}let t=a.businessSlug;s(t)}catch(s){let e=s instanceof Error?s.message:"Failed to process QR code";m(e),null==r||r(e),g(!1)}}},t=new w.TF(b,e,!1);o.current=t,t.render(a,e=>{console.debug("QR scan error:",e)})}catch(s){let e=s instanceof Error?s.message:"Failed to initialize QR scanner";m(e),null==r||r(e),d(!1)}})(),()=>{o.current&&(o.current.clear().catch(e=>{console.error("Error clearing QR scanner:",e)}),o.current=null),d(!1),g(!1)}},[h,s,r,f,c]),(0,t.useEffect)(()=>()=>{o.current&&o.current.clear().catch(e=>{console.error("Error clearing QR scanner on unmount:",e)})},[]),u)?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-8 text-center ".concat(i),children:[(0,a.jsx)("div",{className:"text-red-500 mb-4",children:(0,a.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"Camera Access Required"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:u}),n&&(0,a.jsx)("button",{onClick:n,className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors",children:"Close"})]}):h?(0,a.jsxs)("div",{className:"relative ".concat(i),children:[f&&(0,a.jsx)("div",{className:"absolute inset-0 bg-black/50 flex items-center justify-center z-10 rounded-lg",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-foreground",children:"Processing QR code..."})]})}),(0,a.jsx)("div",{id:b,ref:l,className:"w-full"}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Point your camera at a Dukancard QR code"})})]}):(0,a.jsx)("div",{className:"flex items-center justify-center p-8 ".concat(i),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Checking camera availability..."})]})})},A=e=>{let{isOpen:s,onClose:r,onScanSuccess:n}=e,[i,o]=(0,t.useState)(!1),[l,c]=(0,t.useState)("camera"),d=(0,t.useCallback)(e=>{o(!0),setTimeout(()=>{N.oR.success("QR code scanned successfully!"),n(e),r(),o(!1)},500)},[n,r]),u=(0,t.useCallback)(e=>{console.error("QR scan error:",e),N.oR.error(e)},[]),m=(0,t.useCallback)(async e=>{var s;let r=null==(s=e.target.files)?void 0:s[0];if(r){if(!r.type.startsWith("image/"))return void N.oR.error("Please select an image file");o(!0);try{let e;try{e=new w.j9("qr-file-scanner-region");let s=await e.scanFile(r);console.log("Html5Qrcode scan result:",s);let a=y(s);if(console.log("QR code validation result:",a),!a.isValid){let e=a.error||"Invalid QR code";N.oR.error(e),o(!1);return}let t=a.businessSlug;d(t)}catch(s){console.error("QR code scan from image failed:",s);let e="Failed to process image";"string"==typeof s?e=s.includes("QR code not found")?"No Dukancard QR code found in the image. Please try another image.":s.includes("no multiformat readers")||s.includes("no multiformat readers")?"No QR code found in the image. Please ensure it's a clear image of a Dukancard QR code.":s.includes("Image parse error")?"Could not read the image file. Please ensure it's a valid image.":s:s instanceof Error&&(e=s.message),N.oR.error(e),o(!1)}finally{if(e)try{e.clear()}catch(e){console.error("Error clearing html5QrCode",e)}}}catch(e){console.error("Outer catch: Failed to process image",e),N.oR.error("Failed to process image"),o(!1)}e.target.value=""}},[]),h=(0,t.useCallback)(()=>{i||r()},[i,r]);return(0,a.jsx)(b.lG,{open:s,onOpenChange:h,children:(0,a.jsxs)(b.Cf,{className:"sm:max-w-md w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)(b.c7,{className:"pb-4",children:(0,a.jsx)(b.L3,{className:"text-lg font-semibold",children:"Scan QR Code"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex rounded-lg bg-muted p-1",children:[(0,a.jsxs)("button",{onClick:()=>c("camera"),disabled:i,className:"flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat("camera"===l?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"),children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),"Camera"]}),(0,a.jsxs)("button",{onClick:()=>c("upload"),disabled:i,className:"flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat("upload"===l?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"),children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),"Upload"]})]}),(0,a.jsx)("div",{className:"relative",children:"camera"===l?(0,a.jsx)(S,{onScanSuccess:d,onScanError:u,onClose:h,className:"min-h-[300px]"}):(0,a.jsxs)("div",{className:"min-h-[300px] flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-8",children:[(0,a.jsx)(v.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Upload QR Code Image"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:"Select an image containing a Dukancard QR code"}),(0,a.jsxs)("label",{htmlFor:"qr-upload",className:"cursor-pointer",children:[(0,a.jsx)(p.$,{asChild:!0,disabled:i,children:(0,a.jsx)("span",{children:i?"Processing...":"Choose Image"})}),(0,a.jsx)("input",{id:"qr-upload",type:"file",accept:"image/*",onChange:m,disabled:i,className:"hidden"})]})]})}),(0,a.jsx)("div",{id:"qr-file-scanner-region",style:{display:"none"}}),(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"camera"===l?"Position the QR code within the camera frame":"Upload an image containing a Dukancard QR code"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Only Dukancard business QR codes are supported"})]}),i&&(0,a.jsx)("div",{className:"absolute inset-0 bg-background/80 flex items-center justify-center z-50 rounded-lg",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-foreground font-medium",children:"Processing QR code..."}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Please wait"})]})})]})]})})};var E=r(75168);let Q=e=>{let{href:s,icon:r,label:t,isActive:n,isTablet:o=!1,badge:l,disabled:d=!1,onClick:u,isSpecial:m=!1}=e,h=(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:(0,c.cn)("relative mb-1 transition-all duration-200",m&&"bg-[var(--brand-gold)] rounded-full p-3 -mt-2 shadow-lg"),children:[(0,a.jsx)("div",{className:(0,c.cn)(m&&"text-white"),children:r}),l&&(0,a.jsx)(g.E,{variant:"outline",className:"absolute -top-2 -right-3 px-1 py-0 text-[7px] bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]",children:l})]}),(0,a.jsx)("span",{className:(0,c.cn)("transition-all",o?"text-[9px]":"text-[10px]",m&&"text-[var(--brand-gold)] font-medium"),children:t})]}),x=(0,c.cn)("flex flex-col items-center justify-center flex-1 py-2 text-xs transition-colors cursor-pointer",n?"text-[var(--brand-gold)]":"text-muted-foreground hover:text-[var(--brand-gold)]",d&&"opacity-70 pointer-events-none",m&&"transform hover:scale-105");return d?(0,a.jsx)("div",{className:x,children:h}):u?(0,a.jsx)("button",{onClick:u,className:x,children:h}):(0,a.jsx)(i(),{href:s,className:x,children:h})};function D(){let e=(0,o.usePathname)(),s=(0,o.useRouter)(),r=(0,d.a)(),[n,i]=(0,t.useState)(!1),[g,b]=(0,t.useState)(!1),[p,j]=(0,t.useState)(null),[v,N]=(0,t.useState)(null);if((0,t.useEffect)(()=>{let e=()=>{i(window.innerWidth>=768&&window.innerWidth<1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,t.useEffect)(()=>{(async()=>{let e=(0,E.U)(),{data:{user:s}}=await e.auth.getUser();if(s){let{data:r}=await e.from("business_profiles").select("business_slug").eq("id",s.id).single();if(r)N("business"),j(r.business_slug);else{let{data:r}=await e.from("customer_profiles").select("id").eq("user_id",s.id).single();r&&N("customer")}}})()},[]),!r&&!n)return null;let w="/login",y=!1,k="/",C=!1;e.startsWith("/dashboard/business")?(w="/dashboard/business/card",y="/dashboard/business/card"===e,k="/dashboard/business",C="/dashboard/business"===e):e.startsWith("/dashboard/customer")?(w="/dashboard/customer/profile",y=e.includes("/dashboard/customer/profile"),k="/dashboard/customer",C="/dashboard/customer"===e):e.startsWith("/login")||e.startsWith("/choose-role")||e.startsWith("/onboarding")?(w=e,y=!0,k="/",C="/"===e):"business"===v?(w="/dashboard/business/card",y=!1,k="/dashboard/business",C=!1):"customer"===v?(w="/dashboard/customer/profile",y=!1,k="/dashboard/customer",C=!1):(w="/login",y="/login"===e,k="/",C="/"===e);let R=[{key:"home",href:k,icon:(0,a.jsx)(u.A,{size:20}),label:"Home",isActive:C},{key:"discover",href:"/discover",icon:(0,a.jsx)(m.A,{size:20}),label:"Discover",isActive:"/discover"===e},{key:"scan",icon:(0,a.jsx)(h.A,{size:20}),label:"Scan",isActive:!1,onClick:()=>{b(!0)},isSpecial:!0},{key:"dukan-ai",href:"#",icon:(0,a.jsx)(x.A,{size:20}),label:"Dukan AI",isActive:!1,badge:"Soon",disabled:!0},{key:"account",href:w,icon:(0,a.jsx)(f.A,{size:20}),label:"Account",isActive:y}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.P.div,{initial:{y:100},animate:{y:0},transition:{duration:.3},className:(0,c.cn)("fixed bottom-0 left-0 right-0 z-50 flex items-center justify-around bg-background/95 backdrop-blur-lg border-t border-border/80 px-2",n?"h-14":"h-16"),children:R.map(e=>(0,a.jsx)(Q,{href:e.href,icon:e.icon,label:e.label,isActive:e.isActive,isTablet:n,badge:e.badge,disabled:e.disabled,onClick:e.onClick,isSpecial:e.isSpecial},e.key))}),(0,a.jsx)(A,{isOpen:g,onClose:()=>{b(!1)},onScanSuccess:e=>{b(!1),s.push("/".concat(e))}})]})}}}]);