(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2802],{7558:(e,t,s)=>{"use strict";s.d(t,{default:()=>C});var a=s(95155),r=s(12115),o=s(3096),i=s(28695),l=s(60760),n=s(85339),c=s(51154),d=s(21433),u=s(49026),m=s(97168),g=s(30400),h=s(58685),p=s(42676),x=s(43694),y=s(54416),f=s(27213),b=s(4516),v=s(12486),j=s(69663),w=s(56671),_=s(75168),N=s(85040),k=s(35695),P=s(66766),F=s(36481);function A(e){let{customerName:t,onPostCreated:o}=e,[n,d]=(0,r.useState)(!1),[u,g]=(0,r.useState)(""),[h,p]=(0,r.useState)(null),[x,A]=(0,r.useState)(!1),[C,R]=(0,r.useState)(null),[S,U]=(0,r.useState)(t||""),[E,q]=(0,r.useState)(null),[D,L]=(0,r.useState)(null),[M,z]=(0,r.useState)(null),I=(0,r.useRef)(null),O=(0,k.useRouter)(),B=u.length,$=B>2e3;(0,r.useEffect)(()=>{(async()=>{let e=(0,_.U)(),{data:{user:s}}=await e.auth.getUser();if(s){let{data:a}=await e.from("customer_profiles").select("name, avatar_url").eq("id",s.id).single();a&&(U(a.name||t||"User"),R(a.avatar_url))}})()},[t]),(0,r.useEffect)(()=>{I.current&&(I.current.style.height="auto",I.current.style.height="".concat(I.current.scrollHeight,"px"))},[u]),(0,r.useEffect)(()=>{n&&I.current&&I.current.focus()},[n]);let T=()=>{q(null),D&&URL.revokeObjectURL(D),L(null),p(null),z(null)},W=e=>{let t=e.match(/(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^\s]*)?)/i);return t?t[1]:null},Y=e=>{g(e);let t=W(e);t&&t!==M?(z(t),E||p(t)):!t&&M&&(z(null),E||p(null))},K=async()=>{let e=(0,_.U)(),{data:{user:t}}=await e.auth.getUser();if(!t)return w.oR.error("Please log in to continue"),!1;let{data:s,error:a}=await e.from("customer_profiles").select("name, pincode, city, state, locality").eq("id",t.id).single();return a?(w.oR.error("Failed to check customer profile"),!1):(null==s?void 0:s.name)&&""!==s.name.trim()?!!(null==s?void 0:s.pincode)&&!!(null==s?void 0:s.city)&&!!(null==s?void 0:s.state)&&!!(null==s?void 0:s.locality)||(w.oR.error("Please complete your address in your profile before creating posts"),O.push("/dashboard/customer/profile"),!1):(w.oR.error("Please complete your name in your profile before creating posts"),O.push("/dashboard/customer/profile"),!1)},H=async()=>{if(!u.trim()&&!D&&!M)return void w.oR.error("Please add some content or an image");if($)return void w.oR.error("Post content is too long");A(!0);try{let e;if(!await K())return void A(!1);if(E&&!h){let{compressImageUltraAggressiveClient:t}=await s.e(196).then(s.bind(s,90196)),a=await t(E,{maxDimension:1200,targetSizeKB:100}),r=new File([a.blob],E.name,{type:a.blob.type}),o=await (0,N.r0)({content:u.trim(),image_url:null,mentioned_business_ids:[]});if(o.success&&o.data){let t=o.data;try{let{uploadCustomerPostImage:a}=await s.e(7155).then(s.bind(s,77155)),o=new FormData;o.append("imageFile",r);let i=await a(o,t.id,t.created_at);if(i.success&&i.url)e=await (0,N.ce)(t.id,{content:u.trim(),image_url:i.url,mentioned_business_ids:[]});else{console.error("Image upload failed:",i.error);try{let{deleteCustomerPost:e}=await Promise.resolve().then(s.bind(s,85040));await e(t.id),console.log("Rolled back post creation due to image upload failure")}catch(e){console.error("Failed to rollback post creation:",e)}w.oR.error("Failed to upload image. Please try again."),A(!1);return}}catch(e){console.error("Image upload error:",e);try{let{deleteCustomerPost:e}=await Promise.resolve().then(s.bind(s,85040));await e(t.id),console.log("Rolled back post creation due to image upload error")}catch(e){console.error("Failed to rollback post creation:",e)}w.oR.error("Failed to upload image. Please try again."),A(!1);return}}else e=o}else e=await (0,N.r0)({content:u.trim(),image_url:h,mentioned_business_ids:[]});e.success?(w.oR.success("Post created successfully!"),g(""),p(null),T(),d(!1),null==o||o()):w.oR.error(e.error||"Failed to create post")}catch(e){console.error("Error creating post:",e),w.oR.error("Failed to create post")}finally{A(!1)}};return(0,a.jsxs)(i.P.div,{layout:!0,className:"bg-white dark:bg-black overflow-hidden md:border md:border-gray-200 md:dark:border-gray-700 md:rounded-xl md:shadow-sm",children:[(0,a.jsx)(l.N,{mode:"wait",children:!n&&(0,a.jsx)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer",onClick:()=>{d(!0)},children:[(0,a.jsxs)(j.eu,{className:"w-10 h-10 ring-2 ring-[#D4AF37]/30",children:[(0,a.jsx)(j.BK,{src:C||void 0,alt:S}),(0,a.jsx)(j.q5,{className:"bg-[#D4AF37] text-white text-sm font-medium",children:S.charAt(0).toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex-1 bg-gray-50 dark:bg-gray-800 rounded-full px-4 py-3 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:["What's on your mind, ",S,"?"]})]})},"collapsed")}),(0,a.jsx)(l.N,{mode:"wait",children:n&&(0,a.jsxs)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(j.eu,{className:"w-10 h-10 ring-2 ring-[#D4AF37]/30",children:[(0,a.jsx)(j.BK,{src:C||void 0,alt:S}),(0,a.jsx)(j.q5,{className:"bg-[#D4AF37] text-white text-sm font-medium",children:S.charAt(0).toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100 text-sm",children:S}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Public post"})]})]}),(0,a.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>{u.trim()||h||d(!1)},className:"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsx)(F.A,{})}),(0,a.jsx)("textarea",{ref:I,value:u,onChange:e=>Y(e.target.value),placeholder:"What's on your mind, ".concat(S,"?"),className:"w-full resize-none border-none outline-none text-lg placeholder-gray-500 dark:placeholder-gray-400 bg-transparent text-gray-900 dark:text-gray-100 min-h-[80px]",style:{maxHeight:"300px"}}),B>0&&(0,a.jsxs)("div",{className:"text-xs mt-2 text-right ".concat($?"text-red-500":"text-gray-400"),children:[B,"/",2e3]})]}),(0,a.jsx)(l.N,{children:(D||M)&&(0,a.jsx)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"px-4 pb-4",children:(0,a.jsxs)("div",{className:"relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700",children:[(0,a.jsx)(P.default,{src:D||M||"",alt:"Post image preview",width:500,height:300,className:"w-full h-auto max-h-96 object-cover",onError:T}),(0,a.jsx)("button",{onClick:T,className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1.5 hover:bg-opacity-70 transition-all",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),M&&!D&&(0,a.jsx)("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded",children:"Auto-detected image"})]})})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];return s?s.type.startsWith("image/")?s.size>5242880?void w.oR.error("Image size must be less than 5MB"):void(q(s),L(URL.createObjectURL(s)),p(null)):void w.oR.error("Please select an image file"):void 0},className:"hidden",id:"image-upload"}),(0,a.jsx)("label",{htmlFor:"image-upload",children:(0,a.jsx)(m.$,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-[#D4AF37] dark:text-gray-400 dark:hover:text-[#D4AF37] h-9 px-3",asChild:!0,children:(0,a.jsxs)("span",{children:[(0,a.jsx)(f.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Photo"})]})})}),(0,a.jsxs)(m.$,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 h-9 px-3",disabled:!0,children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Location"})]})]}),(0,a.jsx)(m.$,{onClick:H,disabled:x||!u.trim()&&!D&&!h&&!M||$,className:"bg-[#D4AF37] hover:bg-[#B8941F] text-white px-6 py-2 rounded-full font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Posting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Post"]})})]})]},"expanded")})]})}function C(e){let{initialPosts:t,initialTotalCount:s,initialHasMore:y,initialFilter:f="smart",citySlug:b,stateSlug:v,localitySlug:j,pincode:w,userName:_="Valued Customer"}=e,[N,k]=(0,r.useState)(t),[P,F]=(0,r.useState)(s),[C,R]=(0,r.useState)(y),[S,U]=(0,r.useState)(1),[E,q]=(0,r.useState)(!1),[D,L]=(0,r.useState)(f),{ref:M,inView:z}=(0,o.Wx)(),I=(0,r.useCallback)(async()=>{if(C&&!E){q(!0);try{var e;let t=S+1,s=await (0,d._)({filter:D,page:t,city_slug:b,state_slug:v,locality_slug:j,pincode:w});s.success&&(null==(e=s.data)?void 0:e.items)&&(k(e=>{let t=new Set(e.map(e=>e.id)),a=s.data.items.filter(e=>!t.has(e.id));return[...e,...a]}),R(s.data.hasMore||!1),F(s.data.totalCount||0),U(t))}catch(e){console.error("Error loading more posts:",e)}finally{q(!1)}}},[C,E,S,D,b,v,j,w]);(0,r.useEffect)(()=>{z&&C&&!E&&I()},[z,C,E,I]);let O=async e=>{if(e!==D){q(!0),L(e),k([]);try{let t=await (0,d._)({filter:e,page:1,city_slug:b,state_slug:v,locality_slug:j,pincode:w});t.success&&t.data&&(k(t.data.items),R(t.data.hasMore),F(t.data.totalCount),U(1))}catch(e){console.error("Error changing filter:",e)}finally{q(!1)}}},B=async()=>{try{var e;let t=await (0,d._)({filter:D,page:1,city_slug:b,state_slug:v,locality_slug:j,pincode:w});t.success&&(null==(e=t.data)?void 0:e.items)&&(k(t.data.items),R(t.data.hasMore||!1),F(t.data.totalCount||0),U(1))}catch(e){console.error("Error refreshing feed:",e)}},$=(e,t)=>{k(s=>s.map(s=>s.id===e?{...s,content:t}:s))},T=e=>{k(t=>t.filter(t=>t.id!==e)),F(e=>Math.max(0,e-1))},W=(e,t)=>{k(s=>s.map(s=>s.id===e?{...s,product_ids:t}:s))};return(0,a.jsxs)(x.A,{children:[(0,a.jsx)(p.A,{activeFilter:D,onFilterChange:O,isLoading:E}),(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:(0,a.jsx)(A,{customerName:_,onPostCreated:B})}),(0,a.jsx)(l.N,{mode:"wait",children:0!==N.length||E?(0,a.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"space-y-0",children:[E&&0===N.length&&(0,a.jsx)(a.Fragment,{children:Array.from({length:10}).map((e,t)=>(0,a.jsx)(h.default,{index:t,showImage:Math.random()>.3,showProducts:Math.random()>.7},"skeleton-".concat(t)))}),N.map((e,t)=>(0,a.jsx)(g.A,{post:e,index:t,onPostUpdate:$,onPostDelete:T,onProductsUpdate:W},e.id)),C&&(0,a.jsx)("div",{ref:M,className:"flex justify-center items-center py-8",children:E&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-neutral-500",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 animate-spin"}),(0,a.jsx)("span",{className:"text-sm",children:"Loading more posts..."})]})}),C&&!E&&(0,a.jsx)("div",{className:"flex justify-center mt-8 mb-4",children:(0,a.jsx)(m.$,{variant:"outline",onClick:I,disabled:E,className:"bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900",children:"Load More Posts"})})]},"posts-list"):(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:(0,a.jsxs)(u.Fc,{className:"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)(u.XL,{children:"No posts found"}),(0,a.jsx)(u.TN,{children:(()=>{switch(D){case"smart":return"No posts available in your smart feed. Try subscribing to businesses or check other filters.";case"subscribed":return"Subscribe to businesses to see their posts here.";case"locality":return"No posts from businesses in your locality yet.";case"pincode":return"No posts from businesses in your pincode yet.";case"city":return"No posts from businesses in your city yet.";case"state":return"No posts from businesses in your state yet.";default:return"No posts available at the moment."}})()})]})},"empty-state")})]})]})}},74766:(e,t,s)=>{Promise.resolve().then(s.bind(s,7558))},85040:(e,t,s)=>{"use strict";s.d(t,{r0:()=>r,deleteCustomerPost:()=>i,ce:()=>o});var a=s(75168);async function r(e){let t=await (0,a.U)(),{data:{user:s},error:r}=await t.auth.getUser();if(r||!s)return{success:!1,message:"Authentication required",error:"You must be logged in to create a post"};let{data:o,error:i}=await t.from("customer_profiles").select("id, city_slug, state_slug, locality_slug, pincode, avatar_url").eq("id",s.id).single();if(i||!o)return{success:!1,message:"Customer profile not found",error:"You must have a customer profile to create a post"};let l={customer_id:s.id,content:e.content,image_url:e.image_url||null,city_slug:o.city_slug,state_slug:o.state_slug,locality_slug:o.locality_slug,pincode:o.pincode,mentioned_business_ids:e.mentioned_business_ids||[]},{data:n,error:c}=await t.from("customer_posts").insert(l).select().single();return c?(console.error("Error creating customer post:",c),{success:!1,message:"Failed to create post",error:c.message}):{success:!0,message:"Post created successfully",data:n}}async function o(e,t){let s=await (0,a.U)(),{data:{user:r},error:o}=await s.auth.getUser();if(o||!r)return{success:!1,message:"Authentication required",error:"You must be logged in to update a post"};let{data:i,error:l}=await s.from("customer_posts").select("id").eq("id",e).eq("customer_id",r.id).single();if(l||!i)return{success:!1,message:"Post not found",error:"The post does not exist or you do not have permission to update it"};let n={content:t.content,image_url:t.image_url||null,mentioned_business_ids:t.mentioned_business_ids||[],updated_at:new Date().toISOString()},{data:c,error:d}=await s.from("customer_posts").update(n).eq("id",e).select().single();return d?(console.error("Error updating customer post:",d),{success:!1,message:"Failed to update post",error:d.message}):{success:!0,message:"Post updated successfully",data:c}}async function i(e){let t=await (0,a.U)(),{data:{user:r},error:o}=await t.auth.getUser();if(o||!r)return{success:!1,message:"Authentication required",error:"You must be logged in to delete a post"};let{data:i,error:l}=await t.from("customer_posts").select("id, created_at, image_url").eq("id",e).eq("customer_id",r.id).single();if(l||!i)return{success:!1,message:"Post not found",error:"The post does not exist or you do not have permission to delete it"};try{let{deleteCustomerPostMedia:t}=await s.e(1094).then(s.bind(s,41094)),a=await t(r.id,e,i.created_at);if(!a.success&&a.error)return console.error("Error deleting customer post media:",a.error),{success:!1,message:"Failed to delete post images",error:"Cannot delete post: ".concat(a.error)}}catch(e){return console.error("Error deleting customer post media:",e),{success:!1,message:"Failed to delete post images",error:"Cannot delete post: Failed to clean up associated images"}}let{error:n}=await t.from("customer_posts").delete().eq("id",e);return n?(console.error("Error deleting customer post:",n),{success:!1,message:"Failed to delete post",error:n.message}):{success:!0,message:"Post deleted successfully"}}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,2290,6671,375,5152,7665,1884,67,6215,6766,106,4081,8830,62,8703,356,4203,8441,1684,7358],()=>t(74766)),_N_E=e.O()}]);