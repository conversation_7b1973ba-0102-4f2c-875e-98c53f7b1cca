(()=>{var e={};e.id=2802,e.ids=[2802],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7250:(e,t,r)=>{Promise.resolve().then(r.bind(r,18524))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18524:(e,t,r)=>{"use strict";r.d(t,{default:()=>F});var s=r(60687),a=r(43210),i=r(37472),o=r(77882),n=r(88920),l=r(93613),d=r(41862),c=r(27124),u=r(3018),m=r(24934),p=r(51536),f=r(72945),g=r(73066),h=r(9912),b=r(11860),y=r(9005),x=r(97992),w=r(27900),v=r(70373),j=r(52581),_=r(38398),P=r(67527),N=r(16189),k=r(30474),C=r(17071);function A({customerName:e,onPostCreated:t}){let[i,l]=(0,a.useState)(!1),[c,u]=(0,a.useState)(""),[p,f]=(0,a.useState)(null),[g,h]=(0,a.useState)(!1),[A,F]=(0,a.useState)(null),[q,U]=(0,a.useState)(e||""),[R,E]=(0,a.useState)(null),[S,M]=(0,a.useState)(null),[z,D]=(0,a.useState)(null),$=(0,a.useRef)(null),G=(0,N.useRouter)(),I=c.length,L=I>2e3,V=()=>{E(null),S&&URL.revokeObjectURL(S),M(null),f(null),D(null)},B=e=>{let t=e.match(/(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^\s]*)?)/i);return t?t[1]:null},O=e=>{u(e);let t=B(e);t&&t!==z?(D(t),R||f(t)):!t&&z&&(D(null),R||f(null))},T=async()=>{let e=(0,_.U)(),{data:{user:t}}=await e.auth.getUser();if(!t)return j.oR.error("Please log in to continue"),!1;let{data:r,error:s}=await e.from("customer_profiles").select("name, pincode, city, state, locality").eq("id",t.id).single();return s?(j.oR.error("Failed to check customer profile"),!1):r?.name&&""!==r.name.trim()?!!r?.pincode&&!!r?.city&&!!r?.state&&!!r?.locality||(j.oR.error("Please complete your address in your profile before creating posts"),G.push("/dashboard/customer/profile"),!1):(j.oR.error("Please complete your name in your profile before creating posts"),G.push("/dashboard/customer/profile"),!1)},W=async()=>{if(!c.trim()&&!S&&!z)return void j.oR.error("Please add some content or an image");if(L)return void j.oR.error("Post content is too long");h(!0);try{let e;if(!await T())return void h(!1);if(R&&!p){let{compressImageUltraAggressiveClient:t}=await r.e(8640).then(r.bind(r,28640)),s=await t(R,{maxDimension:1200,targetSizeKB:100}),a=new File([s.blob],R.name,{type:s.blob.type}),i=await (0,P.r0)({content:c.trim(),image_url:null,mentioned_business_ids:[]});if(i.success&&i.data){let t=i.data;try{let{uploadCustomerPostImage:s}=await r.e(9829).then(r.bind(r,9829)),i=new FormData;i.append("imageFile",a);let o=await s(i,t.id,t.created_at);if(o.success&&o.url)e=await (0,P.ce)(t.id,{content:c.trim(),image_url:o.url,mentioned_business_ids:[]});else{console.error("Image upload failed:",o.error);try{let{deleteCustomerPost:e}=await Promise.resolve().then(r.bind(r,67527));await e(t.id),console.log("Rolled back post creation due to image upload failure")}catch(e){console.error("Failed to rollback post creation:",e)}j.oR.error("Failed to upload image. Please try again."),h(!1);return}}catch(e){console.error("Image upload error:",e);try{let{deleteCustomerPost:e}=await Promise.resolve().then(r.bind(r,67527));await e(t.id),console.log("Rolled back post creation due to image upload error")}catch(e){console.error("Failed to rollback post creation:",e)}j.oR.error("Failed to upload image. Please try again."),h(!1);return}}else e=i}else e=await (0,P.r0)({content:c.trim(),image_url:p,mentioned_business_ids:[]});e.success?(j.oR.success("Post created successfully!"),u(""),f(null),V(),l(!1),t?.()):j.oR.error(e.error||"Failed to create post")}catch(e){console.error("Error creating post:",e),j.oR.error("Failed to create post")}finally{h(!1)}};return(0,s.jsxs)(o.P.div,{layout:!0,className:"bg-white dark:bg-black overflow-hidden md:border md:border-gray-200 md:dark:border-gray-700 md:rounded-xl md:shadow-sm",children:[(0,s.jsx)(n.N,{mode:"wait",children:!i&&(0,s.jsx)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer",onClick:()=>{l(!0)},children:[(0,s.jsxs)(v.eu,{className:"w-10 h-10 ring-2 ring-[#D4AF37]/30",children:[(0,s.jsx)(v.BK,{src:A||void 0,alt:q}),(0,s.jsx)(v.q5,{className:"bg-[#D4AF37] text-white text-sm font-medium",children:q.charAt(0).toUpperCase()})]}),(0,s.jsxs)("div",{className:"flex-1 bg-gray-50 dark:bg-gray-800 rounded-full px-4 py-3 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:["What's on your mind, ",q,"?"]})]})},"collapsed")}),(0,s.jsx)(n.N,{mode:"wait",children:i&&(0,s.jsxs)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(v.eu,{className:"w-10 h-10 ring-2 ring-[#D4AF37]/30",children:[(0,s.jsx)(v.BK,{src:A||void 0,alt:q}),(0,s.jsx)(v.q5,{className:"bg-[#D4AF37] text-white text-sm font-medium",children:q.charAt(0).toUpperCase()})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100 text-sm",children:q}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Public post"})]})]}),(0,s.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>{c.trim()||p||l(!1)},className:"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("div",{className:"mb-3",children:(0,s.jsx)(C.A,{})}),(0,s.jsx)("textarea",{ref:$,value:c,onChange:e=>O(e.target.value),placeholder:`What's on your mind, ${q}?`,className:"w-full resize-none border-none outline-none text-lg placeholder-gray-500 dark:placeholder-gray-400 bg-transparent text-gray-900 dark:text-gray-100 min-h-[80px]",style:{maxHeight:"300px"}}),I>0&&(0,s.jsxs)("div",{className:`text-xs mt-2 text-right ${L?"text-red-500":"text-gray-400"}`,children:[I,"/",2e3]})]}),(0,s.jsx)(n.N,{children:(S||z)&&(0,s.jsx)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"px-4 pb-4",children:(0,s.jsxs)("div",{className:"relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700",children:[(0,s.jsx)(k.default,{src:S||z||"",alt:"Post image preview",width:500,height:300,className:"w-full h-auto max-h-96 object-cover",onError:V}),(0,s.jsx)("button",{onClick:V,className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1.5 hover:bg-opacity-70 transition-all",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})}),z&&!S&&(0,s.jsx)("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded",children:"Auto-detected image"})]})})}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let t=e.target.files?.[0];return t?t.type.startsWith("image/")?t.size>5242880?void j.oR.error("Image size must be less than 5MB"):void(E(t),M(URL.createObjectURL(t)),f(null)):void j.oR.error("Please select an image file"):void 0},className:"hidden",id:"image-upload"}),(0,s.jsx)("label",{htmlFor:"image-upload",children:(0,s.jsx)(m.$,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-[#D4AF37] dark:text-gray-400 dark:hover:text-[#D4AF37] h-9 px-3",asChild:!0,children:(0,s.jsxs)("span",{children:[(0,s.jsx)(y.A,{className:"h-5 w-5 mr-1"}),(0,s.jsx)("span",{className:"text-sm",children:"Photo"})]})})}),(0,s.jsxs)(m.$,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 h-9 px-3",disabled:!0,children:[(0,s.jsx)(x.A,{className:"h-5 w-5 mr-1"}),(0,s.jsx)("span",{className:"text-sm",children:"Location"})]})]}),(0,s.jsx)(m.$,{onClick:W,disabled:g||!c.trim()&&!S&&!p&&!z||L,className:"bg-[#D4AF37] hover:bg-[#B8941F] text-white px-6 py-2 rounded-full font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Posting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Post"]})})]})]},"expanded")})]})}function F({initialPosts:e,initialTotalCount:t,initialHasMore:r,initialFilter:b="smart",citySlug:y,stateSlug:x,localitySlug:w,pincode:v,userName:j="Valued Customer"}){let[_,P]=(0,a.useState)(e),[N,k]=(0,a.useState)(t),[C,F]=(0,a.useState)(r),[q,U]=(0,a.useState)(1),[R,E]=(0,a.useState)(!1),[S,M]=(0,a.useState)(b),{ref:z,inView:D}=(0,i.Wx)(),$=(0,a.useCallback)(async()=>{if(C&&!R){E(!0);try{let e=q+1,t=await (0,c._)({filter:S,page:e,city_slug:y,state_slug:x,locality_slug:w,pincode:v});t.success&&t.data?.items&&(P(e=>{let r=new Set(e.map(e=>e.id)),s=t.data.items.filter(e=>!r.has(e.id));return[...e,...s]}),F(t.data.hasMore||!1),k(t.data.totalCount||0),U(e))}catch(e){console.error("Error loading more posts:",e)}finally{E(!1)}}},[C,R,q,S,y,x,w,v]),G=async e=>{if(e!==S){E(!0),M(e),P([]);try{let t=await (0,c._)({filter:e,page:1,city_slug:y,state_slug:x,locality_slug:w,pincode:v});t.success&&t.data&&(P(t.data.items),F(t.data.hasMore),k(t.data.totalCount),U(1))}catch(e){console.error("Error changing filter:",e)}finally{E(!1)}}},I=async()=>{try{let e=await (0,c._)({filter:S,page:1,city_slug:y,state_slug:x,locality_slug:w,pincode:v});e.success&&e.data?.items&&(P(e.data.items),F(e.data.hasMore||!1),k(e.data.totalCount||0),U(1))}catch(e){console.error("Error refreshing feed:",e)}},L=(e,t)=>{P(r=>r.map(r=>r.id===e?{...r,content:t}:r))},V=e=>{P(t=>t.filter(t=>t.id!==e)),k(e=>Math.max(0,e-1))},B=(e,t)=>{P(r=>r.map(r=>r.id===e?{...r,product_ids:t}:r))};return(0,s.jsxs)(h.A,{children:[(0,s.jsx)(g.A,{activeFilter:S,onFilterChange:G,isLoading:R}),(0,s.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,s.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:(0,s.jsx)(A,{customerName:j,onPostCreated:I})}),(0,s.jsx)(n.N,{mode:"wait",children:0!==_.length||R?(0,s.jsxs)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"space-y-0",children:[R&&0===_.length&&(0,s.jsx)(s.Fragment,{children:Array.from({length:10}).map((e,t)=>(0,s.jsx)(f.default,{index:t,showImage:Math.random()>.3,showProducts:Math.random()>.7},`skeleton-${t}`))}),_.map((e,t)=>(0,s.jsx)(p.A,{post:e,index:t,onPostUpdate:L,onPostDelete:V,onProductsUpdate:B},e.id)),C&&(0,s.jsx)("div",{ref:z,className:"flex justify-center items-center py-8",children:R&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-neutral-500",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 animate-spin"}),(0,s.jsx)("span",{className:"text-sm",children:"Loading more posts..."})]})}),C&&!R&&(0,s.jsx)("div",{className:"flex justify-center mt-8 mb-4",children:(0,s.jsx)(m.$,{variant:"outline",onClick:$,disabled:R,className:"bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900",children:"Load More Posts"})})]},"posts-list"):(0,s.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:(0,s.jsxs)(u.Fc,{className:"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(u.XL,{children:"No posts found"}),(0,s.jsx)(u.TN,{children:(()=>{switch(S){case"smart":return"No posts available in your smart feed. Try subscribing to businesses or check other filters.";case"subscribed":return"Subscribe to businesses to see their posts here.";case"locality":return"No posts from businesses in your locality yet.";case"pincode":return"No posts from businesses in your pincode yet.";case"city":return"No posts from businesses in your city yet.";case"state":return"No posts from businesses in your state yet.";default:return"No posts available at the moment."}})()})]})},"empty-state")})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24107:(e,t,r)=>{"use strict";function s(e){let{pincode:t,state:r,city:s,locality:a}=e;return!!(t&&""!==t.trim()&&r&&""!==r.trim()&&s&&""!==s.trim()&&a&&""!==a.trim())}function a(e){let t=[];return e.pincode&&""!==e.pincode.trim()||t.push("pincode"),e.state&&""!==e.state.trim()||t.push("state"),e.city&&""!==e.city.trim()||t.push("city"),e.locality&&""!==e.locality.trim()||t.push("locality"),t}function i(e){if(0===e.length)return"";let t=e.map(e=>{switch(e){case"pincode":return"Pincode";case"state":return"State";case"city":return"City";case"locality":return"Locality";default:return e}});if(1===t.length)return`Please update your ${t[0]} in your profile.`;{if(2===t.length)return`Please update your ${t.join(" and ")} in your profile.`;let e=t.pop();return`Please update your ${t.join(", ")}, and ${e} in your profile.`}}r.d(t,{Gs:()=>s,SJ:()=>a,zp:()=>i})},24911:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\components\\\\feed\\\\ModernCustomerFeedList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44202:(e,t,r)=>{Promise.resolve().then(r.bind(r,24911))},44992:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"40397ca1727b354672455d32692e626008a169d968":()=>s.WO,"40930e22958ec27cc9c1459e0e63b2994bcf186979":()=>s.Xd,"40ab7a7f21cb4460091c8fb4edccd341d3b9116d41":()=>s.G_,"40e6b7c827369932950dcd3c394b2c641f20ee0597":()=>s.Hh,"40fbda5e049b7decd80deacff63f96dfec9a104b5a":()=>s.PD,"608517e8bbf255550985eb946a3ce32616cdf4cb5a":()=>s.kJ});var s=r(54694)},54694:(e,t,r)=>{"use strict";r.d(t,{G_:()=>d,Hh:()=>l,PD:()=>n,WO:()=>c,Xd:()=>m,kJ:()=>u});var s=r(67218);r(79130);var a=r(32032),i=r(24107),o=r(39916);async function n(e){let t=await (0,a.createClient)();try{let{data:r,error:s}=await t.from("customer_profiles").select("pincode, state, city, locality, address").eq("id",e).single();if(s)return console.error("Error fetching customer profile for address validation:",s),{isValid:!1,message:"Unable to verify your address information. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your address information"};let a={pincode:r?.pincode,state:r?.state,city:r?.city,locality:r?.locality,address:r?.address};if(!(0,i.Gs)(a)){let e=(0,i.SJ)(a),t=(0,i.zp)(e),r=`/dashboard/customer/profile?message=${encodeURIComponent(t)}`;return{isValid:!1,missingFields:e,message:t,redirectUrl:r}}return{isValid:!0}}catch(e){return console.error("Unexpected error during address validation:",e),{isValid:!1,message:"An error occurred while validating your address. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your address information"}}}async function l(e){let t=await n(e);!t.isValid&&t.redirectUrl&&(0,o.redirect)(t.redirectUrl)}async function d(e){let t=await (0,a.createClient)();try{let{data:r,error:s}=await t.from("customer_profiles").select("name").eq("id",e).single();if(s)return console.error("Error fetching customer profile for name validation:",s),{isValid:!1,message:"Unable to verify your profile information. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your profile information"};if(!(r?.name&&""!==r.name.trim())){let e="Please complete your name in your profile to access the dashboard.",t=`/dashboard/customer/profile?message=${encodeURIComponent(e)}`;return{isValid:!1,message:e,redirectUrl:t}}return{isValid:!0}}catch(e){return console.error("Unexpected error during name validation:",e),{isValid:!1,message:"An error occurred while validating your profile. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your profile information"}}}async function c(e){let t=await d(e);!t.isValid&&t.redirectUrl&&(0,o.redirect)(t.redirectUrl)}async function u(e,t=!1){await c(e),t||await l(e)}async function m(e){let t=await (0,a.createClient)();try{let{data:r,error:s}=await t.from("customer_profiles").select("pincode, state, city, locality, address").eq("id",e).single();if(s)return console.error("Error fetching customer address data:",s),{error:"Failed to fetch address data"};return{data:{pincode:r?.pincode,state:r?.state,city:r?.city,locality:r?.locality,address:r?.address}}}catch(e){return console.error("Unexpected error fetching address data:",e),{error:"An unexpected error occurred"}}}(0,r(17478).D)([n,l,d,c,u,m]),(0,s.A)(n,"40fbda5e049b7decd80deacff63f96dfec9a104b5a",null),(0,s.A)(l,"40e6b7c827369932950dcd3c394b2c641f20ee0597",null),(0,s.A)(d,"40ab7a7f21cb4460091c8fb4edccd341d3b9116d41",null),(0,s.A)(c,"40397ca1727b354672455d32692e626008a169d968",null),(0,s.A)(u,"608517e8bbf255550985eb946a3ce32616cdf4cb5a",null),(0,s.A)(m,"40930e22958ec27cc9c1459e0e63b2994bcf186979",null)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56423:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["customer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,88057)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,78050)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/customer/page",pathname:"/dashboard/customer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>s.B,"4009d2933c186901a1403c78560cd329314f35e005":()=>i.ys,"401bed28fefc4fa316c9657ce0fb9ce951b2f6cefe":()=>o.E,"4034b523293d3ffd37df154f2b315894750110b2c1":()=>o.H,"404b1d9bf7310a5b14e59bf18e153b291565613a40":()=>a.w,"4078b27f89338bdea95f74b22fd4d22c4710876eab":()=>i.pD,"60183dfe46fbe995d7961a9cf8fe9545820e57eb60":()=>i.Rl,"60e85a41c7f76e7af6443414547e080e8dfaf27c80":()=>i.yf,"60fef8195d22c743fbc958b9c6e7229ba7a09ebfb2":()=>i.gg,"70699055c7810af32c22cef9732c4ad735eab53330":()=>c});var s=r(64275),a=r(16111),i=r(33635),o=r(26471),n=r(91199);r(42087);var l=r(76881),d=r(50937);async function c(e,t,r){let s=await (0,l.createClient)(),{data:{user:a},error:i}=await s.auth.getUser();if(i||!a)return{success:!1,error:"User not authenticated."};let o=a.id,n=e.get("imageFile");if(!n)return{success:!1,error:"No image file provided."};if(!["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(n.type))return{success:!1,error:"Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed."};if(n.size>0xf00000)return{success:!1,error:"File size exceeds 15MB limit."};let{data:c,error:u}=await s.from("customer_posts").select("id, customer_id").eq("id",t).eq("customer_id",o).single();if(u||!c)return{success:!1,error:"Post not found or you don't have permission to upload images for this post."};try{let e=Date.now()+Math.floor(1e3*Math.random()),s="customers",a=(0,d.jt)(o,t,0,e,r),i=Buffer.from(await n.arrayBuffer()),c=await (0,l.createClient)(),{error:u}=await c.storage.from(s).upload(a,i,{contentType:n.type,upsert:!0});if(u)return console.error("Customer Post Image Upload Error:",u),{success:!1,error:`Failed to upload image: ${u.message}`};let{data:m}=c.storage.from(s).getPublicUrl(a);if(!m?.publicUrl)return{success:!1,error:"Could not retrieve public URL after upload."};return{success:!0,url:m.publicUrl}}catch(e){return console.error("Error processing customer post image:",e),{success:!1,error:"Failed to process image. Please try a different image."}}}(0,r(33331).D)([c]),(0,n.A)(c,"70699055c7810af32c22cef9732c4ad735eab53330",null)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67527:(e,t,r)=>{"use strict";r.d(t,{r0:()=>a,deleteCustomerPost:()=>o,ce:()=>i});var s=r(38398);async function a(e){let t=await (0,s.U)(),{data:{user:r},error:a}=await t.auth.getUser();if(a||!r)return{success:!1,message:"Authentication required",error:"You must be logged in to create a post"};let{data:i,error:o}=await t.from("customer_profiles").select("id, city_slug, state_slug, locality_slug, pincode, avatar_url").eq("id",r.id).single();if(o||!i)return{success:!1,message:"Customer profile not found",error:"You must have a customer profile to create a post"};let n={customer_id:r.id,content:e.content,image_url:e.image_url||null,city_slug:i.city_slug,state_slug:i.state_slug,locality_slug:i.locality_slug,pincode:i.pincode,mentioned_business_ids:e.mentioned_business_ids||[]},{data:l,error:d}=await t.from("customer_posts").insert(n).select().single();return d?(console.error("Error creating customer post:",d),{success:!1,message:"Failed to create post",error:d.message}):{success:!0,message:"Post created successfully",data:l}}async function i(e,t){let r=await (0,s.U)(),{data:{user:a},error:i}=await r.auth.getUser();if(i||!a)return{success:!1,message:"Authentication required",error:"You must be logged in to update a post"};let{data:o,error:n}=await r.from("customer_posts").select("id").eq("id",e).eq("customer_id",a.id).single();if(n||!o)return{success:!1,message:"Post not found",error:"The post does not exist or you do not have permission to update it"};let l={content:t.content,image_url:t.image_url||null,mentioned_business_ids:t.mentioned_business_ids||[],updated_at:new Date().toISOString()},{data:d,error:c}=await r.from("customer_posts").update(l).eq("id",e).select().single();return c?(console.error("Error updating customer post:",c),{success:!1,message:"Failed to update post",error:c.message}):{success:!0,message:"Post updated successfully",data:d}}async function o(e){let t=await (0,s.U)(),{data:{user:a},error:i}=await t.auth.getUser();if(i||!a)return{success:!1,message:"Authentication required",error:"You must be logged in to delete a post"};let{data:o,error:n}=await t.from("customer_posts").select("id, created_at, image_url").eq("id",e).eq("customer_id",a.id).single();if(n||!o)return{success:!1,message:"Post not found",error:"The post does not exist or you do not have permission to delete it"};try{let{deleteCustomerPostMedia:t}=await r.e(2388).then(r.bind(r,22388)),s=await t(a.id,e,o.created_at);if(!s.success&&s.error)return console.error("Error deleting customer post media:",s.error),{success:!1,message:"Failed to delete post images",error:`Cannot delete post: ${s.error}`}}catch(e){return console.error("Error deleting customer post media:",e),{success:!1,message:"Failed to delete post images",error:"Cannot delete post: Failed to clean up associated images"}}let{error:l}=await t.from("customer_posts").delete().eq("id",e);return l?(console.error("Error deleting customer post:",l),{success:!1,message:"Failed to delete post",error:l.message}):{success:!0,message:"Post deleted successfully"}}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88057:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),a=r(32032),i=r(39916),o=r(24911),n=r(54694),l=r(14109);let d={title:"Feed",description:"View your personalized feed and stay updated"};async function c(){let e,t=await (0,a.createClient)(),{data:{user:r},error:d}=await t.auth.getUser();(d||!r)&&(0,i.redirect)("/login?message=Please log in to view your dashboard"),await (0,n.kJ)(r.id);let{data:c,error:u}=await t.from("customer_profiles").select("name").eq("id",r.id).single();u&&console.error("Error fetching customer profile:",u);let{data:m}=await t.from("subscriptions").select(`
      business_profiles (
        city_slug
      )
    `).eq("user_id",r.id).limit(1);if(m&&m.length>0&&m[0].business_profiles){let t=m[0].business_profiles;e="object"==typeof t&&null!==t&&"city_slug"in t&&t.city_slug||void 0}let p="smart",f=await (0,l._)({filter:p,page:1,limit:10}),g=f.success&&f.data?.items||[],h=!!f.success&&(f.data?.hasMore||!1);f.success||console.error("Error fetching initial posts:",f.error);let b=c?.name||"Valued Customer";return(0,s.jsx)(o.default,{initialPosts:g,initialTotalCount:0,initialHasMore:h,initialFilter:p,citySlug:e,userName:b})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,1753,399,2836,4212,8072,1606,3701,3037,3739,9538,5918,4308,3496],()=>r(56423));module.exports=s})();