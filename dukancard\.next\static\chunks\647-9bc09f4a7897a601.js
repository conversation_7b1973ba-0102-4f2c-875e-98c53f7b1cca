"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[647],{8619:(e,t,n)=>{n.d(t,{d:()=>s});var r=n(60098),o=n(12115),i=n(51508),l=n(82885);function s(e){let t=(0,l.M)(()=>(0,r.OQ)(e)),{isStatic:n}=(0,o.useContext)(i.Q);if(n){let[,n]=(0,o.useState)(e);(0,o.useEffect)(()=>t.on("change",n),[])}return t}},58829:(e,t,n)=>{n.d(t,{G:()=>a});var r=n(6775),o=n(82885),i=n(69515),l=n(97494),s=n(8619);function f(e,t){let n=(0,s.d)(t()),r=()=>n.set(t());return r(),(0,l.E)(()=>{let t=()=>i.Gt.preRender(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,i.WG)(r)}}),n}var c=n(60098);function a(e,t,n,o){if("function"==typeof e){c.bt.current=[],e();let t=f(c.bt.current,e);return c.bt.current=void 0,t}let i="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),n=t?0:-1,o=e[0+n],i=e[1+n],l=e[2+n],s=e[3+n],f=(0,r.G)(i,l,s);return t?f(o):f}(t,n,o);return Array.isArray(e)?u(e,i):u([e],([e])=>i(e))}function u(e,t){let n=(0,o.M)(()=>[]);return f(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}},74001:(e,t,n)=>{let r,o;n.d(t,{L:()=>$});var i=n(60098),l=n(54542),s=n(12115),f=n(69515);function c(e,t){let n,r=()=>{let{currentTime:r}=t,o=(null===r?0:r.value)/100;n!==o&&e(o),n=o};return f.Gt.preUpdate(r,!0),()=>(0,f.WG)(r)}var a=n(91116),u=n(42198);let g=new WeakMap;function d({target:e,contentRect:t,borderBoxSize:n}){g.get(e)?.forEach(r=>{r({target:e,contentSize:t,get size(){if(n){let{inlineSize:e,blockSize:t}=n[0];return{width:e,height:t}}if(e instanceof SVGElement&&"getBBox"in e)return e.getBBox();return{width:e.offsetWidth,height:e.offsetHeight}}})})}function h(e){e.forEach(d)}let p=new Set;var m=n(45818),y=n(62923);let v=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),w=()=>({time:0,x:v(),y:v()}),E={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function W(e,t,n,r){let o=n[t],{length:i,position:l}=E[t],s=o.current,f=n.time;o.current=e[`scroll${l}`],o.scrollLength=e[`scroll${i}`]-e[`client${i}`],o.offset.length=0,o.offset[0]=0,o.offset[1]=o.scrollLength,o.progress=(0,m.q)(0,o.scrollLength,o.current);let c=r-f;o.velocity=c>50?0:(0,y.f)(o.current-s,c)}var x=n(6775),b=n(71784),L=n(53678);let O={start:0,center:.5,end:1};function G(e,t,n=0){let r=0;if(e in O&&(e=O[e]),"string"==typeof e){let t=parseFloat(e);e.endsWith("px")?r=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?r=t/100*document.documentElement.clientWidth:e.endsWith("vh")?r=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(r=t*e),n+r}let H=[0,0],k={All:[[0,0],[1,1]]},z={x:0,y:0},B=new WeakMap,M=new WeakMap,S=new WeakMap,A=e=>e===document.documentElement?window:e;function N(e,{container:t=document.documentElement,...n}={}){let i=S.get(t);i||(i=new Set,S.set(t,i));let l=function(e,t,n,r={}){return{measure:()=>(function(e,t=e,n){if(n.x.targetOffset=0,n.y.targetOffset=0,t!==e){let r=t;for(;r&&r!==e;)n.x.targetOffset+=r.offsetLeft,n.y.targetOffset+=r.offsetTop,r=r.offsetParent}n.x.targetLength=t===e?t.scrollWidth:t.clientWidth,n.y.targetLength=t===e?t.scrollHeight:t.clientHeight,n.x.containerLength=e.clientWidth,n.y.containerLength=e.clientHeight})(e,r.target,n),update:t=>{W(e,"x",n,t),W(e,"y",n,t),n.time=t,(r.offset||r.target)&&function(e,t,n){let{offset:r=k.All}=n,{target:o=e,axis:i="y"}=n,l="y"===i?"height":"width",s=o!==e?function(e,t){let n={x:0,y:0},r=e;for(;r&&r!==t;)if(r instanceof HTMLElement)n.x+=r.offsetLeft,n.y+=r.offsetTop,r=r.offsetParent;else if("svg"===r.tagName){let e=r.getBoundingClientRect(),t=(r=r.parentElement).getBoundingClientRect();n.x+=e.left-t.left,n.y+=e.top-t.top}else if(r instanceof SVGGraphicsElement){let{x:e,y:t}=r.getBBox();n.x+=e,n.y+=t;let o=null,i=r.parentNode;for(;!o;)"svg"===i.tagName&&(o=i),i=r.parentNode;r=o}else break;return n}(o,e):z,f=o===e?{width:e.scrollWidth,height:e.scrollHeight}:"getBBox"in o&&"svg"!==o.tagName?o.getBBox():{width:o.clientWidth,height:o.clientHeight},c={width:e.clientWidth,height:e.clientHeight};t[i].offset.length=0;let a=!t[i].interpolate,u=r.length;for(let e=0;e<u;e++){let n=function(e,t,n,r){let o=Array.isArray(e)?e:H,i=0,l=0;return"number"==typeof e?o=[e,e]:"string"==typeof e&&(o=(e=e.trim()).includes(" ")?e.split(" "):[e,O[e]?e:"0"]),(i=G(o[0],n,r))-G(o[1],t)}(r[e],c[l],f[l],s[i]);a||n===t[i].interpolatorOffsets[e]||(a=!0),t[i].offset[e]=n}a&&(t[i].interpolate=(0,x.G)(t[i].offset,(0,b.Z)(r),{clamp:!1}),t[i].interpolatorOffsets=[...t[i].offset]),t[i].progress=(0,L.q)(0,1,t[i].interpolate(t[i].current))}(e,n,r)},notify:()=>t(n)}}(t,e,w(),n);if(i.add(l),!B.has(t)){let e=()=>{for(let e of i)e.measure()},n=()=>{for(let e of i)e.update(f.uv.timestamp)},l=()=>{for(let e of i)e.notify()},s=()=>{f.Gt.read(e,!1,!0),f.Gt.read(n,!1,!0),f.Gt.preUpdate(l,!1,!0)};B.set(t,s);let c=A(t);window.addEventListener("resize",s,{passive:!0}),t!==document.documentElement&&M.set(t,"function"==typeof t?(p.add(t),o||(o=()=>{let e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};p.forEach(e=>e(t))},window.addEventListener("resize",o)),()=>{p.delete(t),!p.size&&o&&(o=void 0)}):function(e,t){r||"undefined"!=typeof ResizeObserver&&(r=new ResizeObserver(h));let n=(0,u.K)(e);return n.forEach(e=>{let n=g.get(e);n||(n=new Set,g.set(e,n)),n.add(t),r?.observe(e)}),()=>{n.forEach(e=>{let n=g.get(e);n?.delete(t),n?.size||r?.unobserve(e)})}}(t,s)),c.addEventListener("scroll",s,{passive:!0})}let s=B.get(t);return f.Gt.read(s,!1,!0),()=>{(0,f.WG)(s);let e=S.get(t);if(!e||(e.delete(l),e.size))return;let n=B.get(t);B.delete(t),n&&(A(t).removeEventListener("scroll",n),M.get(t)?.(),window.removeEventListener("resize",n))}}let T=new Map;function P({source:e,container:t,...n}){let{axis:r}=n;e&&(t=e);let o=T.get(t)??new Map;T.set(t,o);let i=n.target??"self",l=o.get(i)??{},s=r+(n.offset??[]).join(",");return l[s]||(l[s]=!n.target&&(0,a.J)()?new ScrollTimeline({source:t,axis:r}):function(e){let t={value:0},n=N(n=>{t.value=100*n[e.axis].progress},e);return{currentTime:t,cancel:n}}({container:t,...n})),l[s]}var Q=n(82885),R=n(97494);function C(e,t){(0,l.$)(!!(!t||t.current),`You have defined a ${e} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \`layoutEffect: false\` option.`)}let Y=()=>({scrollX:(0,i.OQ)(0),scrollY:(0,i.OQ)(0),scrollXProgress:(0,i.OQ)(0),scrollYProgress:(0,i.OQ)(0)});function $({container:e,target:t,layoutEffect:n=!0,...r}={}){let o=(0,Q.M)(Y);return(n?R.E:s.useEffect)(()=>(C("target",t),C("container",e),function(e,{axis:t="y",container:n=document.documentElement,...r}={}){var o,i;n===document.documentElement&&("y"===t&&n.scrollHeight===n.clientHeight||"x"===t&&n.scrollWidth===n.clientWidth)&&(n=document.body);let l={axis:t,container:n,...r};return"function"==typeof e?(o=e,i=l,2===o.length?N(e=>{o(e[i.axis].progress,e)},i):c(o,P(i))):function(e,t){let n=P(t);return e.attachTimeline({timeline:t.target?void 0:n,observe:e=>(e.pause(),c(t=>{e.time=e.duration*t},n))})}(e,l)}((e,{x:t,y:n})=>{o.scrollX.set(t.current),o.scrollXProgress.set(t.progress),o.scrollY.set(n.current),o.scrollYProgress.set(n.progress)},{...r,container:e?.current||void 0,target:t?.current||void 0})),[e,t,JSON.stringify(r.offset)]),o}},76604:(e,t,n)=>{n.d(t,{W:()=>l});var r=n(12115),o=n(42198);let i={some:0,all:1};function l(e,{root:t,margin:n,amount:s,once:f=!1,initial:c=!1}={}){let[a,u]=(0,r.useState)(c);return(0,r.useEffect)(()=>{if(!e.current||f&&a)return;let r={root:t&&t.current||void 0,margin:n,amount:s};return function(e,t,{root:n,margin:r,amount:l="some"}={}){let s=(0,o.K)(e),f=new WeakMap,c=new IntersectionObserver(e=>{e.forEach(e=>{let n=f.get(e.target);if(!!n!==e.isIntersecting)if(e.isIntersecting){let n=t(e.target,e);"function"==typeof n?f.set(e.target,n):c.unobserve(e.target)}else"function"==typeof n&&(n(e),f.delete(e.target))})},{root:n,rootMargin:r,threshold:"number"==typeof l?l:i[l]});return s.forEach(e=>c.observe(e)),()=>c.disconnect()}(e.current,()=>(u(!0),f?void 0:()=>u(!1)),r)},[t,e,n,f,s]),a}},92138:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}}]);