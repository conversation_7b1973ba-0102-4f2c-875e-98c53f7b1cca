"use strict";exports.id=2376,exports.ids=[2376],exports.modules={19101:(e,t,r)=>{r.d(t,{U:()=>a});var s=r(79384);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let a=null,i=null;try{let{headers:e,cookies:t}=await r.e(9277).then(r.bind(r,9277));a=await e(),i=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,a=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,a){let i=()=>{var i,n,l,u,o;return i=e,n=t,l=r,u=s,o=a,"customer_profiles"===i?{data:l&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:l&&"business"===n?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===n?{id:"test-subscription-id",plan_id:o,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,r)=>n(e),neq:(t,r)=>n(e),gt:(t,r)=>n(e),gte:(t,r)=>n(e),lt:(t,r)=>n(e),lte:(t,r)=>n(e),like:(t,r)=>n(e),ilike:(t,r)=>n(e),is:(t,r)=>n(e),in:(t,r)=>n(e),contains:(t,r)=>n(e),containedBy:(t,r)=>n(e),rangeGt:(t,r)=>n(e),rangeGte:(t,r)=>n(e),rangeLt:(t,r)=>n(e),rangeLte:(t,r)=>n(e),rangeAdjacent:(t,r)=>n(e),overlaps:(t,r)=>n(e),textSearch:(t,r)=>n(e),match:t=>n(e),not:(t,r,s)=>n(e),or:t=>n(e),filter:(t,r,s)=>n(e),order:(t,r)=>n(e),limit:(t,r)=>n(e),range:(t,r,s)=>n(e),abortSignal:t=>n(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let t=i();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,r,s,a,i)}}(a):i?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await i.set(t,r,s)}catch{}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},22376:(e,t,r)=>{r.d(t,{uploadBusinessPostImage:()=>i});var s=r(19101),a=r(65646);async function i(e,t,r){let i=await (0,s.U)(),{data:{user:n},error:l}=await i.auth.getUser();if(l||!n)return{success:!1,error:"User not authenticated."};let u=n.id,o=e.get("imageFile");if(!o)return{success:!1,error:"No image file provided."};if(o.size>0xf00000){let e=(o.size/1048576).toFixed(1);return{success:!1,error:`File size (${e}MB) exceeds the 15MB limit.`}}if(!["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(o.type))return{success:!1,error:"Invalid file type. Please select JPG, PNG, WebP, or GIF images."};if(!o.name||""===o.name.trim())return{success:!1,error:"Invalid file name."};let{data:c,error:d}=await i.from("business_profiles").select("id").eq("id",u).single();if(d||!c)return{success:!1,error:"Business profile not found. Please complete your business profile first."};try{let e=Date.now()+Math.floor(1e3*Math.random()),i="business",n=(0,a.RE)(u,t,0,e,r),l=Buffer.from(await o.arrayBuffer()),c=await (0,s.U)(),{error:d}=await c.storage.from(i).upload(n,l,{contentType:o.type,upsert:!0});if(d)return console.error("Business Post Image Upload Error:",d),{success:!1,error:`Failed to upload image: ${d.message}`};let{data:g}=c.storage.from(i).getPublicUrl(n);if(!g?.publicUrl)return{success:!1,error:"Could not retrieve public URL after upload."};return{success:!0,url:g.publicUrl}}catch(e){return console.error("Error processing business post image:",e),{success:!1,error:"Failed to process image. Please try a different image."}}}},65646:(e,t,r)=>{function s(e){if(!e||"string"!=typeof e)throw Error(`Invalid userId: expected string, got ${typeof e}. Value: ${e}`);if(e.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${e}`);let t=e.substring(0,2).toLowerCase(),r=e.substring(2,4).toLowerCase();return`users/${t}/${r}/${e}`}function a(e,t,r,a,i){let n=s(e),l=i?new Date(i):new Date,u=l.getFullYear(),o=String(l.getMonth()+1).padStart(2,"0");return`${n}/posts/${u}/${o}/${t}/image_${r}_${a}.webp`}function i(e,t,r){let a=s(e),i=new Date(r),n=i.getFullYear(),l=String(i.getMonth()+1).padStart(2,"0");return`${a}/posts/${n}/${l}/${t}`}r.d(t,{EK:()=>i,RE:()=>a})}};