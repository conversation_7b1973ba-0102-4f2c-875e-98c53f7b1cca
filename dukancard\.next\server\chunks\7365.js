exports.id=7365,exports.ids=[7365],exports.modules={27659:(e,t,r)=>{"use strict";r.d(t,{$y:()=>b,EP:()=>y,GN:()=>o,HW:()=>a,Oe:()=>_,Ve:()=>d,WX:()=>f,bh:()=>p,h:()=>u,lC:()=>c,m3:()=>l});var n=r(67218);r(79130);var s=r(32032),i=r(62351);async function a(){let e=await (0,s.createClient)(),{data:t,error:r}=await e.auth.getUser();return r||!t.user?null:t.user}async function l(e,t="*"){let r,n=await (0,s.createClient)(),{count:i,error:a}=await n.from("business_profiles").select("*",{count:"exact",head:!0}).eq("id",e);if(a)console.error(`Error checking if business profile exists for user ${e}:`,a);else if(0===i)return console.error(`Business profile does not exist for user ${e}`),null;let u=t;"*"===t||t.includes("id")||(u=`id, ${t}`);let{data:o,error:c}=await n.from("business_profiles").select(u).eq("id",e).single();return c?(console.error(`Error fetching business profile for user ${e}:`,c),null):o?("object"==typeof o&&null!==o?"id"in(r={...o})||(r.id=e):(console.error(`Profile data is not an object: ${typeof o}`),r={id:e}),r):(console.error(`No business profile data returned for user ${e}`),null)}async function u(e,t="*"){let r=await (0,s.createClient)(),n=t;"*"===t||t.includes("id")||(n=`id, ${t}`),n.includes("has_active_subscription")&&(n=(n=(n=(n=n.replace("has_active_subscription","")).replace(/,\s*,/g,",")).replace(/,\s*$/g,"")).replace(/^\s*,/,""));let{data:i,error:a}=await r.from("payment_subscriptions").select(n).eq("business_profile_id",e).order("created_at",{ascending:!1}).limit(1).maybeSingle();return a?(console.error(`Error fetching subscription for user ${e}:`,a),null):i?"object"==typeof i&&null!==i?i:(console.error(`Subscription data is not an object: ${typeof i}`),null):null}async function o(e="*"){let t=await a();if(!t)return console.error("No user found in auth session"),{user:null,profile:null,subscription:null,error:"User not authenticated"};let r=["trial_end_date","subscription_start_date","cancellation_requested_at","subscription_paused_at"],n="id, has_active_subscription",s=e;if("*"!==e){let t=e.split(",").map(e=>e.trim()),i=t.filter(e=>!r.includes(e)),a=t.filter(e=>r.includes(e));a.length>0&&(n+=`, ${a.join(", ")}`),s=i.length>0?i.join(", "):"id"}let i=await l(t.id,n);if(!i)return console.error(`Could not fetch business profile for user ${t.id}`),{user:t,profile:null,subscription:null,error:"Business profile not found. Please complete onboarding first."};let c=await u(t.id,s);return{user:t,profile:i,subscription:c,error:null}}async function c(e,t){let{SubscriptionStateManager:n}=await r.e(5193).then(r.bind(r,65193));if(t)return n.isTrialStatus(t.subscription_status);let s=e.trial_end_date?new Date(e.trial_end_date):null;return null!==s&&s>new Date}async function d(){(0,i.revalidatePath)("/dashboard/business/plan"),(0,i.revalidatePath)("/dashboard/business")}async function f(e){return{success:!1,error:e}}async function b(e){return{success:!0,data:e}}async function p(e){return{success:!1,error:e}}async function _(e){return{success:!0,data:e}}async function y(e,t){let r=await (0,s.createClient)(),{data:n,error:i}=await r.from("payment_subscriptions").select("id").eq("business_profile_id",e).eq("razorpay_subscription_id",t).maybeSingle();return i?(console.error(`Error checking subscription ownership: ${i.message}`),!1):null!==n}(0,r(17478).D)([a,l,u,o,c,d,f,b,p,_,y]),(0,n.A)(a,"00d4d8ffb0bd97f113de12ba4267247cf5a3b0b04c",null),(0,n.A)(l,"609ab29520f078ddd91513228ac9d4ec796f522cb0",null),(0,n.A)(u,"609589fd4be3c72f3931a1f9c536368933ba8b83da",null),(0,n.A)(o,"401c3b86049e891affa0506ade0f31baeb2c3d455d",null),(0,n.A)(c,"60f426948b8391e4c3e4b292fc9538ce7045fbe366",null),(0,n.A)(d,"00efacc006752966ec1e7203fd5cdd059c8b2b6e30",null),(0,n.A)(f,"40fb7d10f8dedc0c32f3187581b16bbca4c23379d6",null),(0,n.A)(b,"40fea71ecc67261c02da2d172b819e0eef02d3b41a",null),(0,n.A)(p,"404c603e0f39faf7ce2bc96725f060ce4e5faa5728",null),(0,n.A)(_,"4055b2843ad80548c3c3a2f5c81868718477c83ed4",null),(0,n.A)(y,"6034db8eca1b50b78925a7248b950085ef2be979fc",null)},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>s});var n=r(34386);async function s(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let s=null,i=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));s=await e(),i=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||s&&"true"===s.get("x-playwright-testing"))&&s?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),n="customer"===r||"business"===r,s=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,n,s){let i=()=>{var i,a,l,u,o;return i=e,a=t,l=r,u=n,o=s,"customer_profiles"===i?{data:l&&"customer"===a?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:l&&"business"===a?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===a?{id:"test-subscription-id",plan_id:o,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===a?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},a=e=>({select:t=>a(e),eq:(t,r)=>a(e),neq:(t,r)=>a(e),gt:(t,r)=>a(e),gte:(t,r)=>a(e),lt:(t,r)=>a(e),lte:(t,r)=>a(e),like:(t,r)=>a(e),ilike:(t,r)=>a(e),is:(t,r)=>a(e),in:(t,r)=>a(e),contains:(t,r)=>a(e),containedBy:(t,r)=>a(e),rangeGt:(t,r)=>a(e),rangeGte:(t,r)=>a(e),rangeLt:(t,r)=>a(e),rangeLte:(t,r)=>a(e),rangeAdjacent:(t,r)=>a(e),overlaps:(t,r)=>a(e),textSearch:(t,r)=>a(e),match:t=>a(e),not:(t,r,n)=>a(e),or:t=>a(e),filter:(t,r,n)=>a(e),order:(t,r)=>a(e),limit:(t,r)=>a(e),range:(t,r,n)=>a(e),abortSignal:t=>a(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let t=i();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>a(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>a(e),upsert:e=>a(e),delete:()=>a(),rpc:(e,t)=>a()}})(e,r,n,s,i)}}(s):i?(0,n.createServerClient)(e,t,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:t,value:r,options:n}of e)await i.set(t,r,n)}catch{}}}}):(0,n.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},39727:()=>{},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},71437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return s},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return i}});let n=r(74722),s=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>s.find(t=>e.startsWith(t)))}function a(e){let t,r,i;for(let n of e.split("/"))if(r=s.find(e=>n.startsWith(e))){[t,i]=e.split(r,2);break}if(!t||!r||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":i="/"===t?"/"+i:t+"/"+i;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=a.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:i}}},74722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return a}});let n=r(85531),s=r(35499);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,s.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},78335:()=>{},85531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},96487:()=>{}};