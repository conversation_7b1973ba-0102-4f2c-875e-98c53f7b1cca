"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2290],{6101:(e,r,t)=>{t.d(r,{s:()=>o,t:()=>i});var n=t(12115);function l(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function i(...e){return r=>{let t=!1,n=e.map(e=>{let n=l(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():l(e[r],null)}}}}function o(...e){return n.useCallback(i(...e),e)}},19946:(e,r,t)=>{t.d(r,{A:()=>a});var n=t(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:l=24,strokeWidth:u=2,absoluteStrokeWidth:a,className:s="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:r,...o,width:l,height:l,stroke:t,strokeWidth:a?24*Number(u)/Number(l):u,className:i("lucide",s),...f},[...d.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),a=(e,r)=>{let t=(0,n.forwardRef)((t,o)=>{let{className:a,...s}=t;return(0,n.createElement)(u,{ref:o,iconNode:r,className:i("lucide-".concat(l(e)),a),...s})});return t.displayName="".concat(e),t}},46081:(e,r,t)=>{t.d(r,{A:()=>o,q:()=>i});var n=t(12115),l=t(95155);function i(e,r){let t=n.createContext(r),i=e=>{let{children:r,...i}=e,o=n.useMemo(()=>i,Object.values(i));return(0,l.jsx)(t.Provider,{value:o,children:r})};return i.displayName=e+"Provider",[i,function(l){let i=n.useContext(t);if(i)return i;if(void 0!==r)return r;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function o(e,r=[]){let t=[],i=()=>{let r=t.map(e=>n.createContext(e));return function(t){let l=t?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...t,[e]:l}}),[t,l])}};return i.scopeName=e,[function(r,i){let o=n.createContext(i),u=t.length;t=[...t,i];let a=r=>{let{scope:t,children:i,...a}=r,s=t?.[e]?.[u]||o,c=n.useMemo(()=>a,Object.values(a));return(0,l.jsx)(s.Provider,{value:c,children:i})};return a.displayName=r+"Provider",[a,function(t,l){let a=l?.[e]?.[u]||o,s=n.useContext(a);if(s)return s;if(void 0!==i)return i;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=t.reduce((r,{useScope:t,scopeName:n})=>{let l=t(e)[`__scope${n}`];return{...r,...l}},{});return n.useMemo(()=>({[`__scope${r.scopeName}`]:l}),[l])}};return t.scopeName=r.scopeName,t}(i,...r)]}},52712:(e,r,t)=>{t.d(r,{N:()=>l});var n=t(12115),l=globalThis?.document?n.useLayoutEffect:()=>{}},63540:(e,r,t)=>{t.d(r,{sG:()=>s,hO:()=>c});var n=t(12115),l=t(47650),i=t(6101),o=t(95155),u=Symbol("radix.slottable");function a(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...l}=e;if(n.isValidElement(t)){var o;let e,u,a=(o=t,(u=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(u=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,r){let t={...r};for(let n in r){let l=e[n],i=r[n];/^on[A-Z]/.test(n)?l&&i?t[n]=(...e)=>{i(...e),l(...e)}:l&&(t[n]=l):"style"===n?t[n]={...l,...i}:"className"===n&&(t[n]=[l,i].filter(Boolean).join(" "))}return{...e,...t}}(l,t.props);return t.type!==n.Fragment&&(s.ref=r?(0,i.t)(r,a):a),n.cloneElement(t,s)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:l,...i}=e,u=n.Children.toArray(l),s=u.find(a);if(s){let e=s.props.children,l=u.map(r=>r!==s?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,o.jsx)(r,{...i,ref:t,children:l})});return t.displayName=`${e}.Slot`,t}(`Primitive.${r}`),l=n.forwardRef((e,n)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?t:r,{...i,ref:n})});return l.displayName=`Primitive.${r}`,{...e,[r]:l}},{});function c(e,r){e&&l.flushSync(()=>e.dispatchEvent(r))}},74466:(e,r,t)=>{t.d(r,{F:()=>o});var n=t(52596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,o=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:u}=r,a=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],n=null==u?void 0:u[e];if(null===r)return null;let i=l(r)||l(n);return o[e][i]}),s=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return i(e,a,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...l}=r;return Object.entries(l).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...u,...s}[r]):({...u,...s})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},99708:(e,r,t)=>{t.d(r,{DX:()=>o});var n=t(12115),l=t(6101),i=t(95155),o=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...i}=e;if(n.isValidElement(t)){var o;let e,u,a=(o=t,(u=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(u=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,r){let t={...r};for(let n in r){let l=e[n],i=r[n];/^on[A-Z]/.test(n)?l&&i?t[n]=(...e)=>{let r=i(...e);return l(...e),r}:l&&(t[n]=l):"style"===n?t[n]={...l,...i}:"className"===n&&(t[n]=[l,i].filter(Boolean).join(" "))}return{...e,...t}}(i,t.props);return t.type!==n.Fragment&&(s.ref=r?(0,l.t)(r,a):a),n.cloneElement(t,s)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:l,...o}=e,u=n.Children.toArray(l),s=u.find(a);if(s){let e=s.props.children,l=u.map(r=>r!==s?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(r,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,i.jsx)(r,{...o,ref:t,children:l})});return t.displayName=`${e}.Slot`,t}("Slot"),u=Symbol("radix.slottable");function a(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);