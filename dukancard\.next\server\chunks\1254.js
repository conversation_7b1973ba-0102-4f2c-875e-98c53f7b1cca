"use strict";exports.id=1254,exports.ids=[1254],exports.modules={1254:(t,e,E)=>{E.d(e,{EdgeSubscriptionStateManager:()=>s});let i={ACTIVE:"active",AUTHENTICATED:"authenticated",CANCELLED:"cancelled",COMPLETED:"completed",EXPIRED:"expired",TRIAL:"trial"},r={FREE:"free"};class s{static shouldHaveActiveSubscription(t,e=r.FREE){return e!==r.FREE&&t!==i.TRIAL&&t===i.ACTIVE}static getAccessLevel(t,e=r.FREE){return e===r.FREE?"free":t===i.TRIAL?"trial":t===i.ACTIVE||t===i.AUTHENTICATED?"paid":"free"}static isTerminalStatus(t){return[i.CANCELLED,i.COMPLETED,i.EXPIRED].includes(t)}static isTrialStatus(t){return t===i.TRIAL}static isFreeStatus(t,e){return e===r.FREE}static isActivePaidSubscription(t,e=r.FREE){return e!==r.FREE&&(t===i.ACTIVE||t===i.AUTHENTICATED)}}}};