(()=>{var e={};e.id=9212,e.ids=[9212],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,t,s)=>{"use strict";s.d(t,{CG:()=>r,SC:()=>i,cZ:()=>a});let r={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},i={BUSINESS:"business",CUSTOMERS:"customers"},a={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},32032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{createClient:()=>i});var r=s(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let i=null,a=null;try{let{headers:e,cookies:t}=await s.e(4999).then(s.bind(s,44999));i=await e(),a=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,i=e.get("x-test-business-slug"),a=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,i){let a=()=>{var a,n,o,c,l;return a=e,n=t,o=s,c=r,l=i,"customer_profiles"===a?{data:o&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===a?{data:o&&"business"===n?{id:"test-user-id",business_slug:c||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===a?{data:"business"===n?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===a?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,s)=>n(e),neq:(t,s)=>n(e),gt:(t,s)=>n(e),gte:(t,s)=>n(e),lt:(t,s)=>n(e),lte:(t,s)=>n(e),like:(t,s)=>n(e),ilike:(t,s)=>n(e),is:(t,s)=>n(e),in:(t,s)=>n(e),contains:(t,s)=>n(e),containedBy:(t,s)=>n(e),rangeGt:(t,s)=>n(e),rangeGte:(t,s)=>n(e),rangeLt:(t,s)=>n(e),rangeLte:(t,s)=>n(e),rangeAdjacent:(t,s)=>n(e),overlaps:(t,s)=>n(e),textSearch:(t,s)=>n(e),match:t=>n(e),not:(t,s,r)=>n(e),or:t=>n(e),filter:(t,s,r)=>n(e),order:(t,s)=>n(e),limit:(t,s)=>n(e),range:(t,s,r)=>n(e),abortSignal:t=>n(e),single:async()=>a(),maybeSingle:async()=>a(),then:async e=>{let t=a();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,s,r,i,a)}}(i):a?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await a.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await a.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},38787:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>S,routeModule:()=>y,serverHooks:()=>v,workAsyncStorage:()=>I,workUnitAsyncStorage:()=>b});var r={};s.r(r),s.d(r,{GET:()=>h,HEAD:()=>m});var i=s(96559),a=s(48088),n=s(37719),o=s(32190),c=s(32032);async function l(e=24){let t=await (0,c.createClient)();try{let s,{data:r,error:i}=await t.rpc("get_webhook_error_stats");if(i||!r||0===r.length)return console.error("[MONITORING] Error fetching webhook stats:",i),{total_events:0,successful_events:0,failed_events:0,retrying_events:0,success_rate:0};let a=r[0];if(24!==e){let r=new Date(Date.now()-60*e*6e4).toISOString(),{data:i,error:a}=await t.from("processed_webhook_events").select("status, processed_at, created_at").gte("created_at",r).eq("status","processed");if(!a&&i&&i.length>0){let e=i.filter(e=>e.processed_at&&e.created_at);e.length>0&&(s=e.reduce((e,t)=>{let s=new Date(t.created_at).getTime(),r=new Date(t.processed_at).getTime();return e+(r-s)},0)/e.length)}}return{total_events:Number(a.total_events),successful_events:Number(a.successful_events),failed_events:Number(a.failed_events),retrying_events:Number(a.retrying_events),success_rate:Number(a.success_rate),average_processing_time:s}}catch(e){return console.error("[MONITORING] Exception getting webhook metrics:",e),{total_events:0,successful_events:0,failed_events:0,retrying_events:0,success_rate:0}}}async function u(){let e=await (0,c.createClient)(),t=[];try{let{data:s,error:r}=await e.rpc("find_subscription_inconsistencies");if(r)return console.error("[MONITORING] Error checking subscription consistencies:",r),t;if(s&&s.length>0)for(let e of s)t.push({type:"SUBSCRIPTION_INCONSISTENCY",severity:"HIGH",message:`Subscription inconsistency detected for business ${e.business_profile_id}: ${e.inconsistency_type}`,entity_id:e.business_profile_id,subscription_id:e.razorpay_subscription_id,metadata:{subscription_status:e.subscription_status,has_active_subscription:e.has_active_subscription,plan_id:e.plan_id,inconsistency_type:e.inconsistency_type}})}catch(e){console.error("[MONITORING] Exception checking subscription consistencies:",e)}return t}async function _(){let e=[];try{let t=await l(1);t.total_events>10&&t.success_rate<95&&e.push({type:"HIGH_FAILURE_RATE",severity:t.success_rate<80?"CRITICAL":"HIGH",message:`Webhook success rate is ${t.success_rate.toFixed(2)}% (${t.successful_events}/${t.total_events})`,metadata:{...t}}),t.average_processing_time&&t.average_processing_time>3e4&&e.push({type:"HIGH_FAILURE_RATE",severity:"MEDIUM",message:`High webhook processing time: ${(t.average_processing_time/1e3).toFixed(2)} seconds`,metadata:{average_processing_time:t.average_processing_time}})}catch(e){console.error("[MONITORING] Exception checking webhook health:",e)}return e}async function p(e){try{console.error(`[CRITICAL_ALERT] ${e.type}: ${e.message}`,{severity:e.severity,entity_id:e.entity_id,subscription_id:e.subscription_id,metadata:e.metadata});let t=await (0,c.createClient)();await t.from("system_alerts").insert({alert_type:e.type,severity:e.severity,message:e.message,entity_id:e.entity_id,subscription_id:e.subscription_id,metadata:e.metadata,created_at:new Date().toISOString()})}catch(e){console.error("[MONITORING] Error logging critical alert:",e)}}async function d(){let e=[];try{let t=await l(24),s=await _();e.push(...s);let r=await u();for(let t of(e.push(...r),e))("CRITICAL"===t.severity||"HIGH"===t.severity)&&await p(t);return{healthy:0===e.filter(e=>"CRITICAL"===e.severity||"HIGH"===e.severity).length,alerts:e,metrics:t}}catch(e){return console.error("[MONITORING] Exception during health check:",e),{healthy:!1,alerts:[{type:"HIGH_FAILURE_RATE",severity:"CRITICAL",message:`Health check failed: ${e instanceof Error?e.message:String(e)}`}],metrics:{total_events:0,successful_events:0,failed_events:0,retrying_events:0,success_rate:0}}}}var g=s(30468);async function h(e){try{let t=e.headers.get("x-api-key")||"",s=process.env.HEALTH_CHECK_API_KEY||process.env.WEBHOOK_RETRY_API_KEY;if(!s||t!==s)return console.error("[HEALTH_CHECK] Invalid or missing API key"),o.NextResponse.json({success:!1,message:"Invalid or missing API key"},{status:401});console.log("[HEALTH_CHECK] Starting comprehensive health check");let r=await d(),i=[];r.healthy||i.push("Immediate attention required - critical issues detected"),r.metrics.success_rate<95&&i.push("Investigate webhook processing issues"),r.alerts.some(e=>"SUBSCRIPTION_INCONSISTENCY"===e.type)&&i.push("Review and fix subscription inconsistencies"),r.metrics.average_processing_time&&r.metrics.average_processing_time>3e4&&i.push("Optimize webhook processing performance");let a=await (0,c.createClient)(),{data:n,error:l}=await a.rpc("get_subscription_health_metrics");l&&console.error("[HEALTH_CHECK] Error getting subscription health metrics:",l);let u={healthy:r.healthy,timestamp:new Date().toISOString(),metrics:{webhook:{total_events:r.metrics.total_events,successful_events:r.metrics.successful_events,failed_events:r.metrics.failed_events,retrying_events:r.metrics.retrying_events,success_rate:r.metrics.success_rate,...r.metrics.average_processing_time&&{average_processing_time_ms:Math.round(r.metrics.average_processing_time)}},...n&&n.length>0&&{subscriptions:{total_subscriptions:Number(n[0].total_subscriptions),active_subscriptions:Number(n[0].active_subscriptions),trial_subscriptions:Number(n[0].trial_subscriptions),cancelled_subscriptions:Number(n[0].cancelled_subscriptions),expired_subscriptions:Number(n[0].expired_subscriptions),inconsistent_subscriptions:Number(n[0].inconsistent_subscriptions),health_score:Number(n[0].health_score)}}},alerts:r.alerts.map(e=>({type:e.type,severity:e.severity,message:e.message,...e.entity_id&&{entity_id:e.entity_id},...e.subscription_id&&{subscription_id:e.subscription_id},...e.metadata&&{metadata:e.metadata}})),recommendations:i,summary:{critical_alerts:r.alerts.filter(e=>"CRITICAL"===e.severity).length,high_alerts:r.alerts.filter(e=>"HIGH"===e.severity).length,medium_alerts:r.alerts.filter(e=>"MEDIUM"===e.severity).length,low_alerts:r.alerts.filter(e=>"LOW"===e.severity).length}};return console.log(`[HEALTH_CHECK] Health check completed - Healthy: ${r.healthy}, Alerts: ${r.alerts.length}`),o.NextResponse.json(u,{status:r.healthy?200:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(t){console.error("[HEALTH_CHECK] Error during health check:",t);let e=t instanceof Error?t.message:String(t);return o.NextResponse.json({healthy:!1,timestamp:new Date().toISOString(),error:"Health check failed",message:e,recommendations:["Check system logs","Verify database connectivity","Review monitoring setup"]},{status:500})}}async function m(e){try{let t=e.headers.get("x-api-key")||"",s=process.env.HEALTH_CHECK_API_KEY||process.env.WEBHOOK_RETRY_API_KEY;if(!s||t!==s)return new o.NextResponse(null,{status:401});let r=await (0,c.createClient)(),{data:i,error:a}=await r.from(g.CG.SYSTEM_ALERTS).select("id").eq("severity","CRITICAL").eq("resolved",!1).limit(1);if(a)return new o.NextResponse(null,{status:500});let n=i&&i.length>0;return new o.NextResponse(null,{status:n?503:200,headers:{"X-Health-Status":n?"unhealthy":"healthy","X-Critical-Alerts":String(i?.length||0)}})}catch(e){return console.error("[HEALTH_CHECK] Error during HEAD health check:",e),new o.NextResponse(null,{status:500})}}let y=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/health/subscription/route",pathname:"/api/health/subscription",filename:"route",bundlePath:"app/api/health/subscription/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\health\\subscription\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:I,workUnitAsyncStorage:b,serverHooks:v}=y;function S(){return(0,n.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:b})}},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,9398,4386,580],()=>s(38787));module.exports=r})();