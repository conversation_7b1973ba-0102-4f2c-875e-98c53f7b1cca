"use strict";exports.id=1546,exports.ids=[1546],exports.modules={31546:(e,r,s)=>{s.d(r,{xH:()=>p,cancelSubscription:()=>i,createSubscription:()=>c,bC:()=>l,getSubscription:()=>n,$H:()=>t,pauseSubscription:()=>u,fK:()=>R,updateSubscription:()=>a});var o=s(95453);async function c(e){try{let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions`,{method:"POST",headers:{...r,"Content-Type":"application/json"},body:JSON.stringify(e)}),c=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error creating subscription:",c),{success:!1,error:c};return{success:!0,data:c}}catch(e){return console.error("[RAZORPAY_ERROR] Exception creating subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function n(e){try{let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}`,{method:"GET",headers:r}),c=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error fetching subscription:",c),{success:!1,error:c};return{success:!0,data:c}}catch(e){return console.error("[RAZORPAY_ERROR] Exception fetching subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function t(e={}){try{let r=(0,o.bG)(),s=Object.entries(e).map(([e,r])=>`${e}=${encodeURIComponent(r)}`).join("&"),c=`${o.ST.replace("/v2","/v1")}/subscriptions${s?`?${s}`:""}`,n=await fetch(c,{method:"GET",headers:r}),t=await n.json();if(!n.ok)return console.error("[RAZORPAY_ERROR] Error fetching subscriptions:",t),{success:!1,error:t};return{success:!0,data:t}}catch(e){return console.error("[RAZORPAY_ERROR] Exception fetching subscriptions:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function i(e,r=!1){try{console.log(`[RAZORPAY_DEBUG] Cancelling subscription with ID: ${e}`);let s=(0,o.bG)(),c=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}/cancel`,{method:"POST",headers:{...s,"Content-Type":"application/json"},body:JSON.stringify(r?{cancel_at_cycle_end:1}:{})}),n=await c.json();if(!c.ok)return console.error("[RAZORPAY_ERROR] Error cancelling subscription:",n),{success:!1,error:n};return console.log(`[RAZORPAY_DEBUG] Successfully cancelled subscription: ${n.id}`),{success:!0,data:n}}catch(e){return console.error("[RAZORPAY_ERROR] Exception cancelling subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function a(e,r){try{console.log(`[RAZORPAY_DEBUG] Updating subscription with ID: ${e}`);let s=(0,o.bG)(),c=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}`,{method:"PATCH",headers:{...s,"Content-Type":"application/json"},body:JSON.stringify(r)}),n=await c.json();if(!c.ok)return console.error("[RAZORPAY_ERROR] Error updating subscription:",n),{success:!1,error:n};return console.log(`[RAZORPAY_DEBUG] Successfully updated subscription: ${n.id}`),{success:!0,data:n}}catch(e){return console.error("[RAZORPAY_ERROR] Exception updating subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function u(e,r="now",s=!1){let c;for(let n=1;n<=3;n++)try{console.log(`[RAZORPAY_DEBUG] Pausing subscription with ID: ${e} (attempt ${n}/3)`);let t=(0,o.bG)(),i=s?{}:{pause_at:r};console.log("[RAZORPAY_DEBUG] Request body for pause:",i);let a=new AbortController,u=setTimeout(()=>a.abort(),3e4),R=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}/pause`,{method:"POST",headers:{...t,"Content-Type":"application/json"},body:JSON.stringify(i),signal:a.signal});clearTimeout(u);let l=await R.json();if(!R.ok){if(console.error(`[RAZORPAY_ERROR] Error pausing subscription (attempt ${n}):`,l),R.status>=400&&R.status<500||(c=l,3===n))return{success:!1,error:l};let e=1e3*Math.pow(2,n-1);console.log(`[RAZORPAY_DEBUG] Retrying in ${e}ms...`),await new Promise(r=>setTimeout(r,e));continue}return console.log(`[RAZORPAY_DEBUG] Successfully paused subscription: ${l.id}`),{success:!0,data:l}}catch(e){if(console.error(`[RAZORPAY_ERROR] Exception pausing subscription (attempt ${n}):`,e),c=e,e instanceof Error&&"AbortError"===e.name)return{success:!1,error:{message:"Request timeout - please try again",code:"TIMEOUT_ERROR",type:"EXCEPTION"}};if(n<3){let e=1e3*Math.pow(2,n-1);console.log(`[RAZORPAY_DEBUG] Network error, retrying in ${e}ms...`),await new Promise(r=>setTimeout(r,e));continue}}return{success:!1,error:{message:c instanceof Error?c.message:"Network error after multiple retries",code:"NETWORK_ERROR",type:"EXCEPTION"}}}async function R(e){try{console.log(`[RAZORPAY_DEBUG] Resuming subscription with ID: ${e}`);let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}/resume`,{method:"POST",headers:{...r,"Content-Type":"application/json"},body:JSON.stringify({resume_at:"now"})}),c=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error resuming subscription:",c),{success:!1,error:c};return console.log(`[RAZORPAY_DEBUG] Successfully resumed subscription: ${c.id}`),{success:!0,data:c}}catch(e){return console.error("[RAZORPAY_ERROR] Exception resuming subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function l(e){try{console.log(`[RAZORPAY_DEBUG] Fetching scheduled changes for subscription with ID: ${e}`);let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}/retrieve_scheduled_changes`,{method:"GET",headers:r}),c=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error fetching scheduled changes:",c),{success:!1,error:c};return console.log(`[RAZORPAY_DEBUG] Successfully fetched scheduled changes for subscription: ${c.id}`),{success:!0,data:c}}catch(e){return console.error("[RAZORPAY_ERROR] Exception fetching scheduled changes:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function p(e){try{console.log(`[RAZORPAY_DEBUG] Cancelling scheduled changes for subscription with ID: ${e}`);let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}/cancel_scheduled_changes`,{method:"POST",headers:{...r,"Content-Type":"application/json"},body:JSON.stringify({})}),c=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error cancelling scheduled changes:",c),{success:!1,error:c};return console.log(`[RAZORPAY_DEBUG] Successfully cancelled scheduled changes for subscription: ${c.id}`),{success:!0,data:c}}catch(e){return console.error("[RAZORPAY_ERROR] Exception cancelling scheduled changes:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}}};