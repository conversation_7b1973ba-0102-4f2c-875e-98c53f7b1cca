(()=>{var e={};e.id=743,e.ids=[743],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,t,s)=>{"use strict";s.d(t,{CG:()=>r,SC:()=>i,cZ:()=>a});let r={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},i={BUSINESS:"business",CUSTOMERS:"customers"},a={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},32032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{createClient:()=>i});var r=s(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let i=null,a=null;try{let{headers:e,cookies:t}=await s.e(4999).then(s.bind(s,44999));i=await e(),a=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,i=e.get("x-test-business-slug"),a=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,i){let a=()=>{var a,n,u,o,l;return a=e,n=t,u=s,o=r,l=i,"customer_profiles"===a?{data:u&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===a?{data:u&&"business"===n?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===a?{data:"business"===n?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===a?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,s)=>n(e),neq:(t,s)=>n(e),gt:(t,s)=>n(e),gte:(t,s)=>n(e),lt:(t,s)=>n(e),lte:(t,s)=>n(e),like:(t,s)=>n(e),ilike:(t,s)=>n(e),is:(t,s)=>n(e),in:(t,s)=>n(e),contains:(t,s)=>n(e),containedBy:(t,s)=>n(e),rangeGt:(t,s)=>n(e),rangeGte:(t,s)=>n(e),rangeLt:(t,s)=>n(e),rangeLte:(t,s)=>n(e),rangeAdjacent:(t,s)=>n(e),overlaps:(t,s)=>n(e),textSearch:(t,s)=>n(e),match:t=>n(e),not:(t,s,r)=>n(e),or:t=>n(e),filter:(t,s,r)=>n(e),order:(t,s)=>n(e),limit:(t,s)=>n(e),range:(t,s,r)=>n(e),abortSignal:t=>n(e),single:async()=>a(),maybeSingle:async()=>a(),then:async e=>{let t=a();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,s,r,i,a)}}(i):a?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await a.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await a.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},42412:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>I,routeModule:()=>p,serverHooks:()=>S,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>_});var r={};s.r(r),s.d(r,{GET:()=>c});var i=s(96559),a=s(48088),n=s(37719),u=s(32032),o=s(32190),l=s(30468);async function c(){try{let e=await (0,u.createClient)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return o.NextResponse.json({isBusiness:!1,error:"Not authenticated"},{status:401});let{data:r,error:i}=await e.from(l.CG.BUSINESS_PROFILES).select(l.cZ.ID).eq(l.cZ.ID,t.id).maybeSingle();if(i)return console.error("Error checking business profile:",i),o.NextResponse.json({isBusiness:!1,error:"Database error"},{status:500});return o.NextResponse.json({isBusiness:!!r})}catch(e){return console.error("Unexpected error in check-user-type API:",e),o.NextResponse.json({isBusiness:!1,error:"Server error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/check-user-type/route",pathname:"/api/check-user-type",filename:"route",bundlePath:"app/api/check-user-type/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:_,serverHooks:S}=p;function I(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:_})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,9398,4386,580],()=>s(42412));module.exports=r})();