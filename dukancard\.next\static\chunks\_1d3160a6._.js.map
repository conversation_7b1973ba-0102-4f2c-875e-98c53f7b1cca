{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Cleans and formats phone number from Supabase auth.users table format\r\n * Handles various formats: +918458060663, 918458060663, 8458060663\r\n * Returns clean 10-digit phone number or null if invalid\r\n *\r\n * @param phone - Phone number from Supabase auth.users table\r\n * @returns Clean 10-digit phone number or null if invalid\r\n */\r\nexport function cleanPhoneFromAuth(phone: string | null | undefined): string | null {\r\n  if (!phone) return null;\r\n\r\n  let processedPhone = phone.trim();\r\n\r\n  // Remove +91 prefix if present\r\n  if (processedPhone.startsWith('+91')) {\r\n    processedPhone = processedPhone.substring(3);\r\n  }\r\n  // Remove 91 prefix if it's a 12-digit number starting with 91\r\n  else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {\r\n    processedPhone = processedPhone.substring(2);\r\n  }\r\n\r\n  // Validate it's a 10-digit number\r\n  if (/^\\d{10}$/.test(processedPhone)) {\r\n    return processedPhone;\r\n  }\r\n\r\n  return null; // Invalid format\r\n}\r\n\r\n/**\r\n * Masks a phone number, showing first and last two digits.\r\n * Example: 9123456789 -> 91******89\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskPhoneNumber(phone: string | null | undefined): string {\r\n  if (!phone || phone.length < 4) {\r\n    return \"Invalid Phone\"; // Or return empty string or original if preferred\r\n  }\r\n  const firstTwo = phone.substring(0, 2);\r\n  const lastTwo = phone.substring(phone.length - 2);\r\n  const maskedPart = \"*\".repeat(phone.length - 4);\r\n  return `${firstTwo}${maskedPart}${lastTwo}`;\r\n}\r\n\r\n/**\r\n * Masks an email address.\r\n * Example: <EMAIL> -> ex****@do****.com\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskEmail(email: string | null | undefined): string {\r\n  if (!email || !email.includes(\"@\")) {\r\n    return \"Invalid Email\"; // Or return empty string or original\r\n  }\r\n  const parts = email.split(\"@\");\r\n  const username = parts[0];\r\n  const domain = parts[1];\r\n\r\n  if (username.length <= 2 || domain.length <= 2 || !domain.includes(\".\")) {\r\n    return \"Email Hidden\"; // Simple mask for very short/invalid emails\r\n  }\r\n\r\n  const maskedUsername =\r\n    username.substring(0, 2) + \"*\".repeat(username.length - 2);\r\n\r\n  const domainParts = domain.split(\".\");\r\n  const domainName = domainParts[0];\r\n  const domainTld = domainParts.slice(1).join(\".\"); // Handle multiple parts like .co.uk\r\n\r\n  const maskedDomainName =\r\n    domainName.substring(0, 2) + \"*\".repeat(domainName.length - 2);\r\n\r\n  return `${maskedUsername}@${maskedDomainName}.${domainTld}`;\r\n}\r\n\r\n/**\r\n * Formats a number using the Indian numbering system with short notations.\r\n * Supports: K (Thousand), L (Lakh), Cr (Crore), Ar (Arab), Khar (Kharab), Neel, Padma, Shankh, etc.\r\n * Examples:\r\n *   1_200 -> \"1.2K\"\r\n *   1_20_000 -> \"1.2L\"\r\n *   1_20_00_000 -> \"1.2Cr\"\r\n *   1_20_00_00_000 -> \"1.2Ar\"\r\n *   1_20_00_00_00_000 -> \"1.2Khar\"\r\n *   1_20_00_00_00_00_000 -> \"1.2Neel\"\r\n *   1_20_00_00_00_00_00_000 -> \"1.2Padma\"\r\n *   1_20_00_00_00_00_00_00_000 -> \"1.2Shankh\"\r\n */\r\nexport function formatIndianNumberShort(num: number): string {\r\n  if (num === null || num === undefined || isNaN(num)) return \"0\";\r\n  const absNum = Math.abs(num);\r\n\r\n  // Indian units and their values\r\n  const units = [\r\n    { value: 1e5, symbol: \"L\" }, // Lakh\r\n    { value: 1e7, symbol: \"Cr\" }, // Crore\r\n    { value: 1e9, symbol: \"Ar\" }, // Arab\r\n    { value: 1e11, symbol: \"Khar\" }, // Kharab\r\n    { value: 1e13, symbol: \"Neel\" }, // Neel\r\n    { value: 1e15, symbol: \"Padma\" }, // Padma\r\n    { value: 1e17, symbol: \"Shankh\" }, // Shankh\r\n  ];\r\n\r\n  // For thousands (K), use western style for sub-lakh\r\n  if (absNum < 1e5) {\r\n    if (absNum >= 1e3) {\r\n      return (num / 1e3).toFixed(1).replace(/\\.0$/, \"\") + \"K\";\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  // Find the largest unit that fits\r\n  for (let i = units.length - 1; i >= 0; i--) {\r\n    if (absNum >= units[i].value) {\r\n      return (\r\n        (num / units[i].value).toFixed(1).replace(/\\.0$/, \"\") + units[i].symbol\r\n      );\r\n    }\r\n  }\r\n\r\n  // Fallback (should not reach here)\r\n  return num.toString();\r\n}\r\n\r\n/**\r\n * Formats an address from BusinessCardData into a single string\r\n */\r\nexport function formatAddress(data: BusinessCardData): string {\r\n  const addressParts = [\r\n    data.address_line,\r\n    data.locality,\r\n    data.city,\r\n    data.state,\r\n    data.pincode,\r\n  ].filter(Boolean);\r\n\r\n  return addressParts.join(\", \") || \"Address not available\";\r\n}\r\n\r\n/**\r\n * Formats a date in a user-friendly format with Indian Standard Time (IST)\r\n * @param date The date to format\r\n * @param includeTime Whether to include time in the formatted string\r\n * @returns Formatted date string in IST\r\n */\r\nexport function formatDate(date: Date, includeTime: boolean = false): string {\r\n  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  const options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    timeZone: \"Asia/Kolkata\", // Explicitly set timezone to IST\r\n  };\r\n\r\n  if (includeTime) {\r\n    options.hour = \"2-digit\";\r\n    options.minute = \"2-digit\";\r\n    options.hour12 = true;\r\n  }\r\n\r\n  return date.toLocaleString(\"en-IN\", options);\r\n}\r\n\r\n/**\r\n * Formats a currency amount with the appropriate currency symbol\r\n * @param amount The amount to format\r\n * @param currency The currency code (e.g., INR, USD)\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number,\r\n  currency: string = \"INR\"\r\n): string {\r\n  if (amount === null || amount === undefined || isNaN(amount)) {\r\n    return \"Invalid amount\";\r\n  }\r\n\r\n  try {\r\n    return new Intl.NumberFormat(\"en-IN\", {\r\n      style: \"currency\",\r\n      currency: currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Catch any error without using the error variable\r\n    // Fallback in case of invalid currency code\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Formats a string to title case (first letter of each word capitalized)\r\n * @param text The text to format\r\n * @returns The text in title case\r\n */\r\nexport function toTitleCase(text: string): string {\r\n  if (!text) return \"\";\r\n\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,mBAAmB,KAAgC;IACjE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,iBAAiB,MAAM,IAAI;IAE/B,+BAA+B;IAC/B,IAAI,eAAe,UAAU,CAAC,QAAQ;QACpC,iBAAiB,eAAe,SAAS,CAAC;IAC5C,OAEK,IAAI,eAAe,MAAM,KAAK,MAAM,eAAe,UAAU,CAAC,OAAO;QACxE,iBAAiB,eAAe,SAAS,CAAC;IAC5C;IAEA,kCAAkC;IAClC,IAAI,WAAW,IAAI,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,OAAO,MAAM,iBAAiB;AAChC;AAOO,SAAS,gBAAgB,KAAgC;IAC9D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,iBAAiB,kDAAkD;IAC5E;IACA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG;IACpC,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG;IAC/C,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG;IAC7C,OAAO,GAAG,WAAW,aAAa,SAAS;AAC7C;AAOO,SAAS,UAAU,KAAgC;IACxD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,OAAO,iBAAiB,qCAAqC;IAC/D;IACA,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvE,OAAO,gBAAgB,4CAA4C;IACrE;IAEA,MAAM,iBACJ,SAAS,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG;IAE1D,MAAM,cAAc,OAAO,KAAK,CAAC;IACjC,MAAM,aAAa,WAAW,CAAC,EAAE;IACjC,MAAM,YAAY,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,oCAAoC;IAEtF,MAAM,mBACJ,WAAW,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,WAAW,MAAM,GAAG;IAE9D,OAAO,GAAG,eAAe,CAAC,EAAE,iBAAiB,CAAC,EAAE,WAAW;AAC7D;AAeO,SAAS,wBAAwB,GAAW;IACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,MAAM,MAAM,OAAO;IAC5D,MAAM,SAAS,KAAK,GAAG,CAAC;IAExB,gCAAgC;IAChC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAQ;QAC/B;YAAE,OAAO;YAAM,QAAQ;QAAS;KACjC;IAED,oDAAoD;IACpD,IAAI,SAAS,KAAK;QAChB,IAAI,UAAU,KAAK;YACjB,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACtD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,OACE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;QAE3E;IACF;IAEA,mCAAmC;IACnC,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,cAAc,IAAsB;IAClD,MAAM,eAAe;QACnB,KAAK,YAAY;QACjB,KAAK,QAAQ;QACb,KAAK,IAAI;QACT,KAAK,KAAK;QACV,KAAK,OAAO;KACb,CAAC,MAAM,CAAC;IAET,OAAO,aAAa,IAAI,CAAC,SAAS;AACpC;AAQO,SAAS,WAAW,IAAU,EAAE,cAAuB,KAAK;IACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;QAC7D,OAAO;IACT;IAEA,MAAM,UAAsC;QAC1C,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,IAAI,aAAa;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,KAAK,cAAc,CAAC,SAAS;AACtC;AAQO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,IAAI,WAAW,QAAQ,WAAW,aAAa,MAAM,SAAS;QAC5D,OAAO;IACT;IAEA,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,mDAAmD;QACnD,4CAA4C;QAC5C,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAC3C;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AAChD", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-pointer items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,gdACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAMsB,cAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/hooks/useScrollDirection.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\n\ninterface UseScrollDirectionOptions {\n  threshold?: number;\n  initialDirection?: 'up' | 'down';\n}\n\ninterface ScrollState {\n  scrollDirection: 'up' | 'down';\n  isScrolled: boolean;\n  scrollY: number;\n}\n\nexport function useScrollDirection(options: UseScrollDirectionOptions = {}): ScrollState {\n  const { threshold = 10, initialDirection = 'up' } = options;\n  \n  const [scrollState, setScrollState] = useState<ScrollState>({\n    scrollDirection: initialDirection,\n    isScrolled: false,\n    scrollY: 0,\n  });\n\n  useEffect(() => {\n    let lastScrollY = window.scrollY;\n    let ticking = false;\n\n    const updateScrollDirection = () => {\n      const scrollY = window.scrollY;\n      const direction = scrollY > lastScrollY ? 'down' : 'up';\n      const isScrolled = scrollY > threshold;\n\n      // Only update if the scroll direction has changed or crossed the threshold\n      if (\n        direction !== scrollState.scrollDirection ||\n        Math.abs(scrollY - lastScrollY) > threshold ||\n        isScrolled !== scrollState.isScrolled\n      ) {\n        setScrollState({\n          scrollDirection: direction,\n          isScrolled,\n          scrollY,\n        });\n      }\n\n      lastScrollY = scrollY > 0 ? scrollY : 0;\n      ticking = false;\n    };\n\n    const onScroll = () => {\n      if (!ticking) {\n        requestAnimationFrame(updateScrollDirection);\n        ticking = true;\n      }\n    };\n\n    window.addEventListener('scroll', onScroll);\n\n    return () => window.removeEventListener('scroll', onScroll);\n  }, [scrollState.scrollDirection, scrollState.isScrolled, threshold]);\n\n  return scrollState;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAeO,SAAS,mBAAmB,UAAqC,CAAC,CAAC;;IACxE,MAAM,EAAE,YAAY,EAAE,EAAE,mBAAmB,IAAI,EAAE,GAAG;IAEpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,iBAAiB;QACjB,YAAY;QACZ,SAAS;IACX;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,cAAc,OAAO,OAAO;YAChC,IAAI,UAAU;YAEd,MAAM;sEAAwB;oBAC5B,MAAM,UAAU,OAAO,OAAO;oBAC9B,MAAM,YAAY,UAAU,cAAc,SAAS;oBACnD,MAAM,aAAa,UAAU;oBAE7B,2EAA2E;oBAC3E,IACE,cAAc,YAAY,eAAe,IACzC,KAAK,GAAG,CAAC,UAAU,eAAe,aAClC,eAAe,YAAY,UAAU,EACrC;wBACA,eAAe;4BACb,iBAAiB;4BACjB;4BACA;wBACF;oBACF;oBAEA,cAAc,UAAU,IAAI,UAAU;oBACtC,UAAU;gBACZ;;YAEA,MAAM;yDAAW;oBACf,IAAI,CAAC,SAAS;wBACZ,sBAAsB;wBACtB,UAAU;oBACZ;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;gDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;uCAAG;QAAC,YAAY,eAAe;QAAE,YAAY,UAAU;QAAE;KAAU;IAEnE,OAAO;AACT;GAhDgB", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/MinimalHeader.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { usePathname } from \"next/navigation\";\r\n// Removed unused imports: useRouter, createClient\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { LogOut } from \"lucide-react\";\r\n// Removed unused ThemeToggle import\r\nimport { signOutUser } from \"@/app/auth/actions\"; // Import the server action\r\nimport { useScrollDirection } from \"@/hooks/useScrollDirection\";\r\n// Removed unused Sheet import\r\n\r\ninterface MinimalHeaderProps {\r\n  children?: React.ReactNode;\r\n  // Updated props\r\n  businessName?: string | null; // For business context or fallback\r\n  logoUrl?: string | null;\r\n  userName?: string | null; // Added for user's actual name\r\n}\r\n\r\n// Helper to get initials - prioritize userName if available, else businessName\r\nconst getInitials = (\r\n  userName?: string | null,\r\n  businessName?: string | null\r\n): string => {\r\n  const nameToUse = userName || businessName; // Use user name first for avatar\r\n  if (!nameToUse) return \"?\";\r\n  const names = nameToUse.trim().split(/\\s+/);\r\n  if (names.length === 1) return names[0].charAt(0).toUpperCase();\r\n  return (\r\n    names[0].charAt(0).toUpperCase() +\r\n    names[names.length - 1].charAt(0).toUpperCase()\r\n  );\r\n};\r\n\r\n// Helper to get current page name from pathname\r\nconst getPageName = (pathname: string): string => {\r\n  const pathSegments = pathname.split(\"/\").filter(Boolean);\r\n\r\n  // Handle customer dashboard routes\r\n  if (pathSegments.includes(\"customer\")) {\r\n    const lastSegment = pathSegments[pathSegments.length - 1];\r\n\r\n    switch (lastSegment) {\r\n      case \"customer\":\r\n        return \"Feed\";\r\n      case \"overview\":\r\n        return \"Overview\";\r\n      case \"likes\":\r\n        return \"My Likes\";\r\n      case \"subscriptions\":\r\n        return \"Subscriptions\";\r\n      case \"reviews\":\r\n        return \"My Reviews\";\r\n      case \"profile\":\r\n        return \"Profile\";\r\n      case \"settings\":\r\n        return \"Settings\";\r\n      default:\r\n        return \"Dashboard\";\r\n    }\r\n  }\r\n\r\n  // Handle business dashboard routes\r\n  if (pathSegments.includes(\"business\")) {\r\n    const lastSegment = pathSegments[pathSegments.length - 1];\r\n\r\n    switch (lastSegment) {\r\n      case \"business\":\r\n        return \"Feed\";\r\n      case \"overview\":\r\n        return \"Overview\";\r\n      case \"analytics\":\r\n        return \"Analytics\";\r\n      case \"card\":\r\n        return \"Manage Card\";\r\n      case \"products\":\r\n        return \"Products & Services\";\r\n      case \"gallery\":\r\n        return \"Gallery\";\r\n      case \"subscriptions\":\r\n        return \"Subscriptions\";\r\n      case \"likes\":\r\n        return \"Likes\";\r\n      case \"reviews\":\r\n        return \"Reviews\";\r\n      case \"activities\":\r\n        return \"Activities\";\r\n      case \"settings\":\r\n        return \"Settings\";\r\n      case \"plan\":\r\n        return \"Plan Management\";\r\n      default:\r\n        return \"Business Dashboard\";\r\n    }\r\n  }\r\n\r\n  // Default fallback\r\n  return \"Dashboard\";\r\n};\r\n\r\nconst MinimalHeader: React.FC<MinimalHeaderProps> = ({\r\n  children,\r\n  businessName: propBusinessName,\r\n  logoUrl: propLogoUrl,\r\n  userName: propUserName, // Added prop\r\n}) => {\r\n  // Use props directly for now - context can be added back later if needed\r\n  const businessName = propBusinessName;\r\n  const logoUrl = propLogoUrl;\r\n  const userName = propUserName;\r\n\r\n  // Get current pathname and page name\r\n  const pathname = usePathname();\r\n  const currentPageName = getPageName(pathname);\r\n\r\n  // Add scroll direction hook for header collapse\r\n  const { scrollDirection, isScrolled } = useScrollDirection({ threshold: 50 });\r\n\r\n  // Determine if header should be hidden based on scroll and page\r\n  const shouldHideHeader = isScrolled && scrollDirection === 'down' &&\r\n    (pathname.startsWith('/dashboard/') || pathname === '/discover' || pathname.startsWith('/post/'));\r\n\r\n  // Check if we're on onboarding or choose-role pages\r\n  const isOnboardingOrChooseRole =\r\n    pathname.includes(\"/choose-role\") || pathname.includes(\"/onboarding\");\r\n\r\n  // Initials logic updated to prioritize userName\r\n  const initials = getInitials(userName, businessName);\r\n\r\n  // Determine display name for the dropdown\r\n  let displayName = \"User\"; // Default fallback\r\n  if (userName && businessName) {\r\n    displayName = `${businessName} (${userName})`; // Business context\r\n  } else if (userName) {\r\n    displayName = userName; // Customer context\r\n  } else if (businessName) {\r\n    displayName = businessName; // Fallback if only business name exists\r\n  }\r\n\r\n  // Access children assuming specific order from layout: [Sheet, Button, ThemeToggle]\r\n  const childArray = React.Children.toArray(children);\r\n  const mobileSheetTrigger = childArray[0]; // Assumes Sheet is the first child\r\n  const desktopToggleButton = childArray[1]; // Assumes Button is the second child\r\n  const themeToggleElement = childArray[2]; // Assumes ThemeToggle is the third child\r\n\r\n  return (\r\n    <header className={`sticky top-0 z-40 w-full border-b border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80 transition-transform duration-300 ease-in-out ${\r\n      shouldHideHeader ? '-translate-y-full' : 'translate-y-0'\r\n    }`}>\r\n      {/* Enhanced container with better spacing */}\r\n      <div className=\"container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6 lg:px-8\">\r\n        {/* Left Section: Mobile Toggle -> Logo -> Desktop Toggle */}\r\n        <div className=\"flex items-center space-x-2 md:space-x-4\">\r\n          {/* Render Mobile Sheet Trigger First */}\r\n          {mobileSheetTrigger}\r\n\r\n          {/* Current page name instead of logo */}\r\n          <div className=\"flex items-center\">\r\n            <h1 className=\"text-xl font-semibold text-foreground\">\r\n              {currentPageName}\r\n            </h1>\r\n          </div>\r\n\r\n          {/* Render Desktop Toggle Button after logo */}\r\n          {desktopToggleButton}\r\n        </div>\r\n\r\n        {/* Right Section: User Menu + Theme Toggle */}\r\n        <div className=\"flex items-center space-x-2 md:space-x-4\">\r\n          {/* Conditional rendering based on page type */}\r\n          {isOnboardingOrChooseRole ? (\r\n            /* Simple logout button for onboarding/choose-role pages */\r\n            <form action={signOutUser}>\r\n              <Button\r\n                type=\"submit\"\r\n                variant=\"ghost\"\r\n                className=\"flex items-center gap-2 h-10 px-3 rounded-lg\"\r\n              >\r\n                <LogOut className=\"h-4 w-4\" />\r\n                <span>Log out</span>\r\n              </Button>\r\n            </form>\r\n          ) : (\r\n            /* Full dropdown menu for dashboard pages */\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className=\"cursor-pointer flex items-center gap-3 h-10 px-3 rounded-lg focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n                >\r\n                  <Avatar className=\"h-8 w-8 border-2 border-border\">\r\n                    {/* Use logoUrl for avatar image only if it exists */}\r\n                    {logoUrl ? (\r\n                      <AvatarImage\r\n                        src={logoUrl}\r\n                        alt={userName || businessName || \"User\"}\r\n                      />\r\n                    ) : null}\r\n                    <AvatarFallback className=\"bg-gradient-to-br from-primary/10 to-primary/5 text-primary font-semibold text-sm\">\r\n                      {initials}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  {/* Display business name for business dashboard, user name for customer dashboard */}\r\n                  <span className=\"hidden sm:block text-sm font-medium text-foreground max-w-32 truncate\">\r\n                    {businessName || userName || \"User\"}\r\n                  </span>\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent className=\"w-64\" align=\"end\" forceMount>\r\n                {\" \"}\r\n                {/* Increased width slightly */}\r\n                {/* Updated Dropdown Label */}\r\n                <DropdownMenuLabel className=\"font-normal\">\r\n                  <p className=\"text-sm font-medium leading-none truncate py-2\">\r\n                    {displayName} {/* Use the combined/correct display name */}\r\n                  </p>\r\n                  {/* Optionally add email back if needed, maybe only for customers? */}\r\n                  {/* {userName && !businessName && userEmail && (\r\n                    <p className=\"text-xs leading-none text-muted-foreground truncate pt-1\">\r\n                      {userEmail}\r\n                    </p>\r\n                  )} */}\r\n                </DropdownMenuLabel>\r\n                {/* Removed email display and extra separator */}\r\n                {/* <DropdownMenuSeparator /> */}\r\n                {/* Add links to profile/settings if needed */}\r\n                {/* <DropdownMenuItem asChild>\r\n                  <Link href=\"/dashboard/profile\">\r\n                    <UserIcon className=\"mr-2 h-4 w-4\" />\r\n                    <span>Profile</span>\r\n                  </Link>\r\n                </DropdownMenuItem> */}\r\n                <DropdownMenuSeparator />\r\n                {/* Logout Button using Server Action */}\r\n                <form action={signOutUser} className=\"w-full px-2 py-1.5\">\r\n                  <Button\r\n                    type=\"submit\"\r\n                    variant=\"ghost\"\r\n                    className=\"w-full justify-start font-normal text-sm h-auto py-1 cursor-pointer\"\r\n                  >\r\n                    <LogOut className=\"mr-2 h-4 w-4\" />\r\n                    <span>Log out</span>\r\n                  </Button>\r\n                </form>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          )}\r\n          {themeToggleElement} {/* Render theme toggle last */}\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default MinimalHeader;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,kDAAkD;AAClD;AACA;AAOA;AACA;AACA,oCAAoC;AACpC,oQAAkD,2BAA2B;AAC7E;;;AAjBA;;;;;;;;;AA4BA,+EAA+E;AAC/E,MAAM,cAAc,CAClB,UACA;IAEA,MAAM,YAAY,YAAY,cAAc,iCAAiC;IAC7E,IAAI,CAAC,WAAW,OAAO;IACvB,MAAM,QAAQ,UAAU,IAAI,GAAG,KAAK,CAAC;IACrC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;IAC7D,OACE,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAC9B,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;AAEjD;AAEA,gDAAgD;AAChD,MAAM,cAAc,CAAC;IACnB,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAEhD,mCAAmC;IACnC,IAAI,aAAa,QAAQ,CAAC,aAAa;QACrC,MAAM,cAAc,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAEzD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mCAAmC;IACnC,IAAI,aAAa,QAAQ,CAAC,aAAa;QACrC,MAAM,cAAc,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAEzD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mBAAmB;IACnB,OAAO;AACT;AAEA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,EACR,cAAc,gBAAgB,EAC9B,SAAS,WAAW,EACpB,UAAU,YAAY,EACvB;;IACC,yEAAyE;IACzE,MAAM,eAAe;IACrB,MAAM,UAAU;IAChB,MAAM,WAAW;IAEjB,qCAAqC;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,kBAAkB,YAAY;IAEpC,gDAAgD;IAChD,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE;QAAE,WAAW;IAAG;IAE3E,gEAAgE;IAChE,MAAM,mBAAmB,cAAc,oBAAoB,UACzD,CAAC,SAAS,UAAU,CAAC,kBAAkB,aAAa,eAAe,SAAS,UAAU,CAAC,SAAS;IAElG,oDAAoD;IACpD,MAAM,2BACJ,SAAS,QAAQ,CAAC,mBAAmB,SAAS,QAAQ,CAAC;IAEzD,gDAAgD;IAChD,MAAM,WAAW,YAAY,UAAU;IAEvC,0CAA0C;IAC1C,IAAI,cAAc,QAAQ,mBAAmB;IAC7C,IAAI,YAAY,cAAc;QAC5B,cAAc,GAAG,aAAa,EAAE,EAAE,SAAS,CAAC,CAAC,EAAE,mBAAmB;IACpE,OAAO,IAAI,UAAU;QACnB,cAAc,UAAU,mBAAmB;IAC7C,OAAO,IAAI,cAAc;QACvB,cAAc,cAAc,wCAAwC;IACtE;IAEA,oFAAoF;IACpF,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC1C,MAAM,qBAAqB,UAAU,CAAC,EAAE,EAAE,mCAAmC;IAC7E,MAAM,sBAAsB,UAAU,CAAC,EAAE,EAAE,qCAAqC;IAChF,MAAM,qBAAqB,UAAU,CAAC,EAAE,EAAE,yCAAyC;IAEnF,qBACE,6LAAC;QAAO,WAAW,CAAC,+KAA+K,EACjM,mBAAmB,sBAAsB,iBACzC;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;wBAEZ;sCAGD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CACX;;;;;;;;;;;wBAKJ;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;wBAEZ,2BACC,yDAAyD,iBACzD,6LAAC;4BAAK,QAAQ,sJAAA,CAAA,cAAW;sCACvB,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;;;;;mCAIV,0CAA0C,iBAC1C,6LAAC,wIAAA,CAAA,eAAY;;8CACX,6LAAC,wIAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,6LAAC,8HAAA,CAAA,SAAM;gDAAC,WAAU;;oDAEf,wBACC,6LAAC,8HAAA,CAAA,cAAW;wDACV,KAAK;wDACL,KAAK,YAAY,gBAAgB;;;;;+DAEjC;kEACJ,6LAAC,8HAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB;;;;;;;;;;;;0DAIL,6LAAC;gDAAK,WAAU;0DACb,gBAAgB,YAAY;;;;;;;;;;;;;;;;;8CAInC,6LAAC,wIAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;wCACzD;sDAGD,6LAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6LAAC;gDAAE,WAAU;;oDACV;oDAAY;;;;;;;;;;;;sDAkBjB,6LAAC,wIAAA,CAAA,wBAAqB;;;;;sDAEtB,6LAAC;4CAAK,QAAQ,sJAAA,CAAA,cAAW;4CAAE,WAAU;sDACnC,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAMf;wBAAmB;;;;;;;;;;;;;;;;;;AAK9B;GAxJM;;QAYa,qIAAA,CAAA,cAAW;QAIY,8HAAA,CAAA,qBAAkB;;;KAhBtD;uCA0JS", "debugId": null}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/hooks/use-mobile.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  // Initialize with undefined to avoid hydration mismatch\r\n  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined);\r\n\r\n  useEffect(() => {\r\n    // Check if window is available (client-side)\r\n    if (typeof window !== \"undefined\") {\r\n      const checkMobile = () => {\r\n        setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n      };\r\n\r\n      // Initial check\r\n      checkMobile();\r\n\r\n      // Set up media query listener\r\n      const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n      const onChange = () => {\r\n        checkMobile();\r\n      };\r\n\r\n      mql.addEventListener(\"change\", onChange);\r\n      return () => mql.removeEventListener(\"change\", onChange);\r\n    }\r\n  }, []);\r\n\r\n  // Return false during SSR to avoid hydration issues\r\n  return isMobile ?? false;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,wDAAwD;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,6CAA6C;YAC7C,wCAAmC;gBACjC,MAAM;yDAAc;wBAClB,YAAY,OAAO,UAAU,GAAG;oBAClC;;gBAEA,gBAAgB;gBAChB;gBAEA,8BAA8B;gBAC9B,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;gBACvE,MAAM;sDAAW;wBACf;oBACF;;gBAEA,IAAI,gBAAgB,CAAC,UAAU;gBAC/B;6CAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;YACjD;QACF;gCAAG,EAAE;IAEL,oDAAoD;IACpD,OAAO,YAAY;AACrB;GA3BgB", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ThemeToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Moon, Sun, Monitor } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface ThemeToggleProps {\r\n  variant?: \"default\" | \"dashboard\";\r\n}\r\n\r\nexport function ThemeToggle({ variant = \"default\" }: ThemeToggleProps = {}) {\r\n  const { setTheme, theme } = useTheme();\r\n  const isMobile = useIsMobile();\r\n\r\n  // Mobile version with modern card design (only for default variant, not dashboard)\r\n  if (isMobile && variant === \"default\") {\r\n    return (\r\n      <div className=\"w-full\">\r\n        <div className=\"flex items-center justify-between p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 border border-neutral-200/50 dark:border-neutral-700/50 backdrop-blur-sm\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <div className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-muted text-foreground\">\r\n              {theme === \"light\" ? (\r\n                <Sun className=\"h-5 w-5\" />\r\n              ) : theme === \"dark\" ? (\r\n                <Moon className=\"h-5 w-5\" />\r\n              ) : (\r\n                <Monitor className=\"h-5 w-5\" />\r\n              )}\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"font-medium text-foreground\">Theme</span>\r\n              <span className=\"text-xs text-muted-foreground capitalize\">\r\n                {theme || \"system\"}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-8 px-3\">\r\n                Change\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-40\">\r\n              <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n                <Sun className=\"mr-2 h-4 w-4\" />\r\n                <span>Light</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n                <Moon className=\"mr-2 h-4 w-4\" />\r\n                <span>Dark</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n                <Monitor className=\"mr-2 h-4 w-4\" />\r\n                <span>System</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Dashboard variant - simplified icon-only button matching avatar size\r\n  if (variant === \"dashboard\") {\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" className=\"h-9 w-9 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0\">\r\n            <Sun className=\"h-[1.1rem] w-[1.1rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n            <Moon className=\"absolute h-[1.1rem] w-[1.1rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n            <span className=\"sr-only\">Toggle theme</span>\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n            <Sun className=\"mr-2 h-4 w-4\" />\r\n            <span>Light</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n            <Moon className=\"mr-2 h-4 w-4\" />\r\n            <span>Dark</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n            <Monitor className=\"mr-2 h-4 w-4\" />\r\n            <span>System</span>\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  }\r\n\r\n  // Desktop version (original)\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"outline\" size=\"icon\">\r\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n          <span className=\"sr-only\">Toggle theme</span>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\">\r\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n          <Sun className=\"mr-2 h-4 w-4\" />\r\n          <span>Light</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n          <Moon className=\"mr-2 h-4 w-4\" />\r\n          <span>Dark</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n          <Monitor className=\"mr-2 h-4 w-4\" />\r\n          <span>System</span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;AAmBO,SAAS,YAAY,EAAE,UAAU,SAAS,EAAoB,GAAG,CAAC,CAAC;;IACxE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAE3B,mFAAmF;IACnF,IAAI,YAAY,YAAY,WAAW;QACrC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,UAAU,wBACT,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;2CACb,UAAU,uBACZ,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAGvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,6LAAC;wCAAK,WAAU;kDACb,SAAS;;;;;;;;;;;;;;;;;;kCAIhB,6LAAC,wIAAA,CAAA,eAAY;;0CACX,6LAAC,wIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAIzD,6LAAC,wIAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAM,WAAU;;kDACzC,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOpB;IAEA,uEAAuE;IACvE,IAAI,YAAY,aAAa;QAC3B,qBACE,6LAAC,wIAAA,CAAA,eAAY;;8BACX,6LAAC,wIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,WAAU;;0CAChC,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAG9B,6LAAC,wIAAA,CAAA,sBAAmB;oBAAC,OAAM;;sCACzB,6LAAC,wIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,wIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,wIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,6BAA6B;IAC7B,qBACE,6LAAC,wIAAA,CAAA,eAAY;;0BACX,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GA3GgB;;QACc,mJAAA,CAAA,WAAQ;QACnB,yHAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\ninterface DialogContentProps extends React.ComponentProps<typeof DialogPrimitive.Content> {\r\n  hideClose?: boolean;\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  hideClose = false,\r\n  ...props\r\n}: DialogContentProps) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {!hideClose && (\r\n          <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer\">\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAoBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,YAAY,KAAK,EACjB,GAAG,OACgB;IACnB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,CAAC,2BACA,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MA3BS;AA6BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1810, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/qrCodeUtils.ts"], "sourcesContent": ["/**\n * QR Code validation utilities for Next.js\n * Adapted from React Native implementation for web use\n */\n\nexport interface QRCodeValidationResult {\n  isValid: boolean;\n  error?: string;\n  businessSlug?: string;\n  url?: string;\n}\n\n/**\n * Validates if a business slug has the correct format\n * @param slug - The business slug to validate\n * @returns Validation result\n */\nfunction validateBusinessSlug(slug: string): { isValid: boolean; error?: string } {\n  if (!slug || typeof slug !== 'string') {\n    return {\n      isValid: false,\n      error: 'Business slug is required'\n    };\n  }\n\n  const cleanSlug = slug.trim();\n\n  if (!cleanSlug) {\n    return {\n      isValid: false,\n      error: 'Business slug cannot be empty'\n    };\n  }\n\n  // Check length (3-50 characters)\n  if (cleanSlug.length < 3 || cleanSlug.length > 50) {\n    return {\n      isValid: false,\n      error: 'Business slug must be between 3 and 50 characters'\n    };\n  }\n\n  // Check format: lowercase letters, numbers, hyphens only\n  const slugPattern = /^[a-z0-9-]+$/;\n  if (!slugPattern.test(cleanSlug)) {\n    return {\n      isValid: false,\n      error: 'Business slug can only contain lowercase letters, numbers, and hyphens'\n    };\n  }\n\n  // Cannot start or end with hyphen\n  if (cleanSlug.startsWith('-') || cleanSlug.endsWith('-')) {\n    return {\n      isValid: false,\n      error: 'Business slug cannot start or end with a hyphen'\n    };\n  }\n\n  // Cannot have consecutive hyphens\n  if (cleanSlug.includes('--')) {\n    return {\n      isValid: false,\n      error: 'Business slug cannot contain consecutive hyphens'\n    };\n  }\n\n  return {\n    isValid: true\n  };\n}\n\n/**\n * Validates if a QR code contains a valid dukancard.in URL\n * @param qrData - The raw data from the QR code scan\n * @returns Validation result with business slug if valid\n */\nexport function validateQRCodeUrl(qrData: string): QRCodeValidationResult {\n  if (!qrData || typeof qrData !== 'string') {\n    return {\n      isValid: false,\n      error: 'Invalid QR code data'\n    };\n  }\n\n  // Clean the data\n  const cleanData = qrData.trim();\n\n  if (!cleanData) {\n    return {\n      isValid: false,\n      error: 'Empty QR code data'\n    };\n  }\n\n  // Check if it's a valid URL\n  let url: URL;\n  try {\n    // Handle cases where the QR code might not have a protocol\n    const urlString = cleanData.startsWith('http') ? cleanData : `https://${cleanData}`;\n    url = new URL(urlString);\n  } catch (_error) {\n    return {\n      isValid: false,\n      error: 'QR code does not contain a valid URL'\n    };\n  }\n\n  // Check if it's a dukancard.in domain\n  const validDomains = ['dukancard.in', 'www.dukancard.in'];\n  if (!validDomains.includes(url.hostname.toLowerCase())) {\n    return {\n      isValid: false,\n      error: 'QR code is not from Dukancard'\n    };\n  }\n\n  // Extract the business slug from the path\n  const pathSegments = url.pathname.split('/').filter(segment => segment.length > 0);\n  \n  if (pathSegments.length === 0) {\n    return {\n      isValid: false,\n      error: 'QR code does not contain a business profile URL'\n    };\n  }\n\n  const businessSlug = pathSegments[0];\n\n  // Validate the business slug format\n  const slugValidation = validateBusinessSlug(businessSlug);\n  if (!slugValidation.isValid) {\n    return {\n      isValid: false,\n      error: slugValidation.error || 'Invalid business URL format'\n    };\n  }\n\n  return {\n    isValid: true,\n    businessSlug,\n    url: url.toString()\n  };\n}\n\n/**\n * Generates a dukancard.in URL for a business slug\n * @param businessSlug - The business slug\n * @returns Complete dukancard.in URL\n */\nexport function generateDukancardUrl(businessSlug: string): string {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in';\n  return `${baseUrl}/${businessSlug}`;\n}\n\n/**\n * Extracts business slug from a dukancard.in URL\n * @param url - The dukancard.in URL\n * @returns Business slug if valid, null otherwise\n */\nexport function extractBusinessSlugFromUrl(url: string): string | null {\n  const validation = validateQRCodeUrl(url);\n  return validation.isValid ? validation.businessSlug! : null;\n}\n\n/**\n * Checks if a URL is a valid dukancard.in business URL\n * @param url - The URL to check\n * @returns True if it's a valid dukancard business URL\n */\nexport function isDukancardBusinessUrl(url: string): boolean {\n  const validation = validateQRCodeUrl(url);\n  return validation.isValid;\n}\n\n/**\n * Validates QR code data and provides user-friendly error messages\n * @param qrData - The raw QR code data\n * @returns User-friendly validation result\n */\nexport function validateQRCodeForUser(qrData: string): QRCodeValidationResult {\n  const result = validateQRCodeUrl(qrData);\n  \n  if (!result.isValid) {\n    // Provide more user-friendly error messages\n    let userFriendlyError = result.error;\n    \n    if (result.error?.includes('not from Dukancard')) {\n      userFriendlyError = 'This QR code is not from Dukancard. Please scan a valid Dukancard business QR code.';\n    } else if (result.error?.includes('not contain a valid URL')) {\n      userFriendlyError = 'Invalid QR code format. Please scan a valid Dukancard business QR code.';\n    } else if (result.error?.includes('business profile URL')) {\n      userFriendlyError = 'This QR code does not link to a business profile. Please scan a valid Dukancard business QR code.';\n    }\n    \n    return {\n      ...result,\n      error: userFriendlyError\n    };\n  }\n  \n  return result;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAoJiB;AA3IlB;;;;CAIC,GACD,SAAS,qBAAqB,IAAY;IACxC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,MAAM,YAAY,KAAK,IAAI;IAE3B,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,iCAAiC;IACjC,IAAI,UAAU,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,IAAI;QACjD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yDAAyD;IACzD,MAAM,cAAc;IACpB,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY;QAChC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,IAAI,UAAU,UAAU,CAAC,QAAQ,UAAU,QAAQ,CAAC,MAAM;QACxD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,IAAI,UAAU,QAAQ,CAAC,OAAO;QAC5B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;IACX;AACF;AAOO,SAAS,kBAAkB,MAAc;IAC9C,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,YAAY,OAAO,IAAI;IAE7B,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,IAAI;IACJ,IAAI;QACF,2DAA2D;QAC3D,MAAM,YAAY,UAAU,UAAU,CAAC,UAAU,YAAY,CAAC,QAAQ,EAAE,WAAW;QACnF,MAAM,IAAI,IAAI;IAChB,EAAE,OAAO,QAAQ;QACf,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,sCAAsC;IACtC,MAAM,eAAe;QAAC;QAAgB;KAAmB;IACzD,IAAI,CAAC,aAAa,QAAQ,CAAC,IAAI,QAAQ,CAAC,WAAW,KAAK;QACtD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,0CAA0C;IAC1C,MAAM,eAAe,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,GAAG;IAEhF,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,MAAM,eAAe,YAAY,CAAC,EAAE;IAEpC,oCAAoC;IACpC,MAAM,iBAAiB,qBAAqB;IAC5C,IAAI,CAAC,eAAe,OAAO,EAAE;QAC3B,OAAO;YACL,SAAS;YACT,OAAO,eAAe,KAAK,IAAI;QACjC;IACF;IAEA,OAAO;QACL,SAAS;QACT;QACA,KAAK,IAAI,QAAQ;IACnB;AACF;AAOO,SAAS,qBAAqB,YAAoB;IACvD,MAAM,UAAU,6DAAoC;IACpD,OAAO,GAAG,QAAQ,CAAC,EAAE,cAAc;AACrC;AAOO,SAAS,2BAA2B,GAAW;IACpD,MAAM,aAAa,kBAAkB;IACrC,OAAO,WAAW,OAAO,GAAG,WAAW,YAAY,GAAI;AACzD;AAOO,SAAS,uBAAuB,GAAW;IAChD,MAAM,aAAa,kBAAkB;IACrC,OAAO,WAAW,OAAO;AAC3B;AAOO,SAAS,sBAAsB,MAAc;IAClD,MAAM,SAAS,kBAAkB;IAEjC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,4CAA4C;QAC5C,IAAI,oBAAoB,OAAO,KAAK;QAEpC,IAAI,OAAO,KAAK,EAAE,SAAS,uBAAuB;YAChD,oBAAoB;QACtB,OAAO,IAAI,OAAO,KAAK,EAAE,SAAS,4BAA4B;YAC5D,oBAAoB;QACtB,OAAO,IAAI,OAAO,KAAK,EAAE,SAAS,yBAAyB;YACzD,oBAAoB;QACtB;QAEA,OAAO;YACL,GAAG,MAAM;YACT,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1973, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/cameraUtils.ts"], "sourcesContent": ["/**\n * Camera detection and permission utilities for web browsers\n */\n\nexport interface CameraCapabilities {\n  hasCamera: boolean;\n  hasPermission: boolean | null; // null means unknown/not requested yet\n  isSecureContext: boolean;\n  supportedConstraints: MediaTrackSupportedConstraints | null;\n  error?: string;\n}\n\nexport interface CameraDevice {\n  deviceId: string;\n  label: string;\n  kind: 'videoinput';\n  groupId: string;\n}\n\n/**\n * Check if the browser supports camera access\n * @returns True if getUserMedia is supported\n */\nexport function isCameraSupported(): boolean {\n  return !!(\n    navigator.mediaDevices &&\n    navigator.mediaDevices.getUserMedia &&\n    typeof navigator.mediaDevices.getUserMedia === 'function'\n  );\n}\n\n/**\n * Check if the current context is secure (HTTPS or localhost)\n * Camera access requires secure context\n * @returns True if context is secure\n */\nexport function isSecureContext(): boolean {\n  return window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';\n}\n\n/**\n * Get available camera devices\n * @returns Promise with array of camera devices\n */\nexport async function getCameraDevices(): Promise<CameraDevice[]> {\n  if (!isCameraSupported()) {\n    throw new Error('Camera not supported in this browser');\n  }\n\n  try {\n    const devices = await navigator.mediaDevices.enumerateDevices();\n    return devices\n      .filter(device => device.kind === 'videoinput')\n      .map(device => ({\n        deviceId: device.deviceId,\n        label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,\n        kind: device.kind as 'videoinput',\n        groupId: device.groupId\n      }));\n  } catch (error) {\n    console.error('Error enumerating camera devices:', error);\n    throw new Error('Failed to enumerate camera devices');\n  }\n}\n\n/**\n * Check camera permission status\n * @returns Permission state: 'granted', 'denied', 'prompt', or null if not supported\n */\nexport async function checkCameraPermission(): Promise<PermissionState | null> {\n  if (!navigator.permissions || !navigator.permissions.query) {\n    return null; // Permissions API not supported\n  }\n\n  try {\n    const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });\n    return permission.state;\n  } catch (error) {\n    console.warn('Error checking camera permission:', error);\n    return null;\n  }\n}\n\n/**\n * Request camera access and check capabilities\n * @param constraints - Optional camera constraints\n * @returns Promise with camera capabilities\n */\nexport async function requestCameraAccess(\n  constraints: MediaStreamConstraints = { video: true }\n): Promise<{ stream: MediaStream; capabilities: CameraCapabilities }> {\n  if (!isCameraSupported()) {\n    throw new Error('Camera not supported in this browser');\n  }\n\n  if (!isSecureContext()) {\n    throw new Error('Camera access requires HTTPS or localhost');\n  }\n\n  try {\n    const stream = await navigator.mediaDevices.getUserMedia(constraints);\n    \n    const capabilities: CameraCapabilities = {\n      hasCamera: true,\n      hasPermission: true,\n      isSecureContext: isSecureContext(),\n      supportedConstraints: navigator.mediaDevices.getSupportedConstraints()\n    };\n\n    return { stream, capabilities };\n  } catch (error: unknown) {\n    let errorMessage = 'Failed to access camera';\n    let hasPermission = false;\n\n    if (error instanceof Error) {\n      if (error.name === 'NotAllowedError') {\n        errorMessage = 'Camera access denied by user';\n        hasPermission = false;\n      } else if (error.name === 'NotFoundError') {\n        errorMessage = 'No camera found on this device';\n      } else if (error.name === 'NotReadableError') {\n        errorMessage = 'Camera is already in use by another application';\n      } else if (error.name === 'OverconstrainedError') {\n        errorMessage = 'Camera does not support the requested constraints';\n      } else if (error.name === 'SecurityError') {\n        errorMessage = 'Camera access blocked due to security restrictions';\n      }\n    }\n\n    const capabilities: CameraCapabilities = {\n      hasCamera: error instanceof Error ? error.name !== 'NotFoundError' : false,\n      hasPermission,\n      isSecureContext: isSecureContext(),\n      supportedConstraints: navigator.mediaDevices?.getSupportedConstraints() || null,\n      error: errorMessage\n    };\n\n    throw { error: errorMessage, capabilities };\n  }\n}\n\n/**\n * Get comprehensive camera capabilities without requesting access\n * @returns Promise with camera capabilities\n */\nexport async function getCameraCapabilities(): Promise<CameraCapabilities> {\n  const capabilities: CameraCapabilities = {\n    hasCamera: false,\n    hasPermission: null,\n    isSecureContext: isSecureContext(),\n    supportedConstraints: null\n  };\n\n  if (!isCameraSupported()) {\n    capabilities.error = 'Camera not supported in this browser';\n    return capabilities;\n  }\n\n  if (!isSecureContext()) {\n    capabilities.error = 'Camera access requires HTTPS or localhost';\n    return capabilities;\n  }\n\n  capabilities.supportedConstraints = navigator.mediaDevices.getSupportedConstraints();\n\n  try {\n    // Check permission status\n    const permissionStatus = await checkCameraPermission();\n    capabilities.hasPermission = permissionStatus === 'granted';\n\n    // Try to enumerate devices to check if camera exists\n    const devices = await getCameraDevices();\n    capabilities.hasCamera = devices.length > 0;\n\n    if (!capabilities.hasCamera) {\n      capabilities.error = 'No camera found on this device';\n    }\n  } catch (error: unknown) {\n    capabilities.error = error instanceof Error ? error.message : 'Failed to check camera capabilities';\n  }\n\n  return capabilities;\n}\n\n/**\n * Stop a media stream and release camera resources\n * @param stream - The media stream to stop\n */\nexport function stopCameraStream(stream: MediaStream): void {\n  if (stream) {\n    stream.getTracks().forEach(track => {\n      track.stop();\n    });\n  }\n}\n\n/**\n * Check if the device is likely a mobile device\n * @returns True if device appears to be mobile\n */\nexport function isMobileDevice(): boolean {\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n\n/**\n * Get preferred camera constraints for QR scanning\n * @param preferredDeviceId - Optional preferred camera device ID\n * @returns Camera constraints optimized for QR scanning\n */\nexport function getQRScanConstraints(preferredDeviceId?: string): MediaStreamConstraints {\n  const constraints: MediaStreamConstraints = {\n    video: {\n      width: { ideal: 1280 },\n      height: { ideal: 720 },\n      facingMode: isMobileDevice() ? 'environment' : 'user', // Back camera on mobile, front on desktop\n      frameRate: { ideal: 30 }\n    }\n  };\n\n  if (preferredDeviceId) {\n    (constraints.video as MediaTrackConstraints).deviceId = { exact: preferredDeviceId };\n  }\n\n  return constraints;\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;AAqBM,SAAS;IACd,OAAO,CAAC,CAAC,CACP,UAAU,YAAY,IACtB,UAAU,YAAY,CAAC,YAAY,IACnC,OAAO,UAAU,YAAY,CAAC,YAAY,KAAK,UACjD;AACF;AAOO,SAAS;IACd,OAAO,OAAO,eAAe,IAAI,SAAS,QAAQ,KAAK,YAAY,SAAS,QAAQ,KAAK;AAC3F;AAMO,eAAe;IACpB,IAAI,CAAC,qBAAqB;QACxB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,UAAU,MAAM,UAAU,YAAY,CAAC,gBAAgB;QAC7D,OAAO,QACJ,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK,cACjC,GAAG,CAAC,CAAA,SAAU,CAAC;gBACd,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI;gBAC9D,MAAM,OAAO,IAAI;gBACjB,SAAS,OAAO,OAAO;YACzB,CAAC;IACL,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,IAAI,MAAM;IAClB;AACF;AAMO,eAAe;IACpB,IAAI,CAAC,UAAU,WAAW,IAAI,CAAC,UAAU,WAAW,CAAC,KAAK,EAAE;QAC1D,OAAO,MAAM,gCAAgC;IAC/C;IAEA,IAAI;QACF,MAAM,aAAa,MAAM,UAAU,WAAW,CAAC,KAAK,CAAC;YAAE,MAAM;QAA2B;QACxF,OAAO,WAAW,KAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,qCAAqC;QAClD,OAAO;IACT;AACF;AAOO,eAAe,oBACpB,cAAsC;IAAE,OAAO;AAAK,CAAC;IAErD,IAAI,CAAC,qBAAqB;QACxB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,mBAAmB;QACtB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;QAEzD,MAAM,eAAmC;YACvC,WAAW;YACX,eAAe;YACf,iBAAiB;YACjB,sBAAsB,UAAU,YAAY,CAAC,uBAAuB;QACtE;QAEA,OAAO;YAAE;YAAQ;QAAa;IAChC,EAAE,OAAO,OAAgB;QACvB,IAAI,eAAe;QACnB,IAAI,gBAAgB;QAEpB,IAAI,iBAAiB,OAAO;YAC1B,IAAI,MAAM,IAAI,KAAK,mBAAmB;gBACpC,eAAe;gBACf,gBAAgB;YAClB,OAAO,IAAI,MAAM,IAAI,KAAK,iBAAiB;gBACzC,eAAe;YACjB,OAAO,IAAI,MAAM,IAAI,KAAK,oBAAoB;gBAC5C,eAAe;YACjB,OAAO,IAAI,MAAM,IAAI,KAAK,wBAAwB;gBAChD,eAAe;YACjB,OAAO,IAAI,MAAM,IAAI,KAAK,iBAAiB;gBACzC,eAAe;YACjB;QACF;QAEA,MAAM,eAAmC;YACvC,WAAW,iBAAiB,QAAQ,MAAM,IAAI,KAAK,kBAAkB;YACrE;YACA,iBAAiB;YACjB,sBAAsB,UAAU,YAAY,EAAE,6BAA6B;YAC3E,OAAO;QACT;QAEA,MAAM;YAAE,OAAO;YAAc;QAAa;IAC5C;AACF;AAMO,eAAe;IACpB,MAAM,eAAmC;QACvC,WAAW;QACX,eAAe;QACf,iBAAiB;QACjB,sBAAsB;IACxB;IAEA,IAAI,CAAC,qBAAqB;QACxB,aAAa,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,mBAAmB;QACtB,aAAa,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,aAAa,oBAAoB,GAAG,UAAU,YAAY,CAAC,uBAAuB;IAElF,IAAI;QACF,0BAA0B;QAC1B,MAAM,mBAAmB,MAAM;QAC/B,aAAa,aAAa,GAAG,qBAAqB;QAElD,qDAAqD;QACrD,MAAM,UAAU,MAAM;QACtB,aAAa,SAAS,GAAG,QAAQ,MAAM,GAAG;QAE1C,IAAI,CAAC,aAAa,SAAS,EAAE;YAC3B,aAAa,KAAK,GAAG;QACvB;IACF,EAAE,OAAO,OAAgB;QACvB,aAAa,KAAK,GAAG,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAChE;IAEA,OAAO;AACT;AAMO,SAAS,iBAAiB,MAAmB;IAClD,IAAI,QAAQ;QACV,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA;YACzB,MAAM,IAAI;QACZ;IACF;AACF;AAMO,SAAS;IACd,OAAO,iEAAiE,IAAI,CAAC,UAAU,SAAS;AAClG;AAOO,SAAS,qBAAqB,iBAA0B;IAC7D,MAAM,cAAsC;QAC1C,OAAO;YACL,OAAO;gBAAE,OAAO;YAAK;YACrB,QAAQ;gBAAE,OAAO;YAAI;YACrB,YAAY,mBAAmB,gBAAgB;YAC/C,WAAW;gBAAE,OAAO;YAAG;QACzB;IACF;IAEA,IAAI,mBAAmB;QACpB,YAAY,KAAK,CAA2B,QAAQ,GAAG;YAAE,OAAO;QAAkB;IACrF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2146, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/qr/QRScanner.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { Html5QrcodeScanner, Html5QrcodeScanType } from 'html5-qrcode';\r\nimport { validateQRCodeForUser } from '@/lib/utils/qrCodeUtils';\r\nimport { requestCameraAccess } from '@/lib/utils/cameraUtils';\r\nimport type { CameraCapabilities } from '@/lib/utils/cameraUtils';\r\nimport './qr-scanner.css';\r\n\r\ninterface QRScannerProps {\r\n  onScanSuccess: (_businessSlug: string) => void;\r\n  onScanError?: (_error: string) => void;\r\n  onClose?: () => void;\r\n  className?: string;\r\n}\r\n\r\nconst QRScanner: React.FC<QRScannerProps> = ({\r\n  onScanSuccess,\r\n  onScanError,\r\n  onClose,\r\n  className = \"\"\r\n}) => {\r\n  const scannerRef = useRef<Html5QrcodeScanner | null>(null);\r\n  const elementRef = useRef<HTMLDivElement>(null);\r\n  const [isScanning, setIsScanning] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [capabilities, setCapabilities] = useState<CameraCapabilities | null>(null);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n\r\n  const qrCodeRegionId = \"qr-scanner-region\";\r\n\r\n  // Request camera access and check capabilities on mount\r\n  useEffect(() => {\r\n    const initializeCamera = async () => {\r\n      try {\r\n        const { stream, capabilities: newCaps } = await requestCameraAccess();\r\n        setCapabilities(newCaps);\r\n        // Stop the stream immediately after getting capabilities, as html5-qrcode will create its own\r\n        stream.getTracks().forEach(track => track.stop());\r\n\r\n        if (newCaps.error) {\r\n          setError(newCaps.error);\r\n          onScanError?.(newCaps.error);\r\n        }\r\n      } catch (err: any) {\r\n        const errorMsg = err.error || (err instanceof Error ? err.message : 'Failed to get camera access');\r\n        const caps = err.capabilities || { hasCamera: false, hasPermission: false, isSecureContext: false, supportedConstraints: null, error: errorMsg };\r\n        setCapabilities(caps);\r\n        setError(errorMsg);\r\n        onScanError?.(errorMsg);\r\n      }\r\n    };\r\n\r\n    initializeCamera();\r\n  }, [onScanError]);\r\n\r\n  // Initialize scanner when capabilities are ready\r\n  useEffect(() => {\r\n    if (!capabilities || capabilities.error || isScanning) {\r\n      return;\r\n    }\r\n\r\n    const initializeScanner = async () => {\r\n      try {\r\n        setIsScanning(true);\r\n        setError(null);\r\n\r\n        // Create scanner configuration\r\n        const config = {\r\n          fps: 10,\r\n          qrbox: {\r\n            width: 250,\r\n            height: 250,\r\n          },\r\n          aspectRatio: 1.0,\r\n          disableFlip: false,\r\n          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],\r\n          showTorchButtonIfSupported: true,\r\n          showZoomSliderIfSupported: false,\r\n          defaultZoomValueIfSupported: 1,\r\n          // Custom styling to match theme\r\n          colorScheme: 'dark',\r\n        };\r\n\r\n        // Success callback\r\n        const onScanSuccessCallback = async (decodedText: string) => {\r\n          if (isProcessing) return; // Prevent multiple scans\r\n          \r\n          setIsProcessing(true);\r\n          \r\n          try {\r\n            // Validate the QR code\r\n            const validation = validateQRCodeForUser(decodedText);\r\n            \r\n            if (!validation.isValid) {\r\n              const errorMessage = validation.error || 'Invalid QR code';\r\n              setError(errorMessage);\r\n              onScanError?.(errorMessage);\r\n              \r\n              // Reset processing after a delay\r\n              setTimeout(() => {\r\n                setIsProcessing(false);\r\n              }, 2000);\r\n              return;\r\n            }\r\n\r\n            // Extract business slug and call success callback\r\n            const businessSlug = validation.businessSlug!;\r\n            onScanSuccess(businessSlug);\r\n            \r\n          } catch (err: unknown) {\r\n            const errorMessage = err instanceof Error ? err.message : 'Failed to process QR code';\r\n            setError(errorMessage);\r\n            onScanError?.(errorMessage);\r\n            setIsProcessing(false);\r\n          }\r\n        };\r\n\r\n        // Error callback\r\n        const onScanErrorCallback = (errorMessage: string) => {\r\n          // Only log errors, don't show them to user (too noisy)\r\n          console.debug('QR scan error:', errorMessage);\r\n        };\r\n\r\n        // Create and start scanner\r\n        const scanner = new Html5QrcodeScanner(\r\n          qrCodeRegionId,\r\n          config,\r\n          false // verbose\r\n        );\r\n\r\n        scannerRef.current = scanner;\r\n        scanner.render(onScanSuccessCallback, onScanErrorCallback);\r\n\r\n      } catch (err: unknown) {\r\n        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize QR scanner';\r\n        setError(errorMessage);\r\n        onScanError?.(errorMessage);\r\n        setIsScanning(false);\r\n      }\r\n    };\r\n\r\n    initializeScanner();\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      if (scannerRef.current) {\r\n        scannerRef.current.clear().catch((err) => {\r\n          console.error('Error clearing QR scanner:', err);\r\n        });\r\n        scannerRef.current = null;\r\n      }\r\n      setIsScanning(false);\r\n      setIsProcessing(false);\r\n    };\r\n  }, [capabilities, onScanSuccess, onScanError, isProcessing, isScanning]);\r\n\r\n  // Cleanup on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (scannerRef.current) {\r\n        scannerRef.current.clear().catch((err) => {\r\n          console.error('Error clearing QR scanner on unmount:', err);\r\n        });\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  if (error) {\r\n    return (\r\n      <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>\r\n        <div className=\"text-red-500 mb-4\">\r\n          <svg className=\"w-16 h-16 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n          </svg>\r\n        </div>\r\n        <h3 className=\"text-lg font-semibold text-foreground mb-2\">Camera Access Required</h3>\r\n        <p className=\"text-muted-foreground mb-4\">{error}</p>\r\n        {onClose && (\r\n          <button\r\n            onClick={onClose}\r\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors\"\r\n          >\r\n            Close\r\n          </button>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!capabilities) {\r\n    return (\r\n      <div className={`flex items-center justify-center p-8 ${className}`}>\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\r\n          <p className=\"text-muted-foreground\">Checking camera availability...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {isProcessing && (\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center z-10 rounded-lg\">\r\n          <div className=\"bg-white rounded-lg p-6 text-center\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\r\n            <p className=\"text-foreground\">Processing QR code...</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      <div \r\n        id={qrCodeRegionId} \r\n        ref={elementRef}\r\n        className=\"w-full\"\r\n      />\r\n      \r\n      <div className=\"mt-4 text-center\">\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Point your camera at a Dukancard QR code\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QRScanner;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;;AAgBA,MAAM,YAAsC,CAAC,EAC3C,aAAa,EACb,WAAW,EACX,OAAO,EACP,YAAY,EAAE,EACf;;IACC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA6B;IACrD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB;IAEvB,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;wDAAmB;oBACvB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,cAAc,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD;wBAClE,gBAAgB;wBAChB,8FAA8F;wBAC9F,OAAO,SAAS,GAAG,OAAO;oEAAC,CAAA,QAAS,MAAM,IAAI;;wBAE9C,IAAI,QAAQ,KAAK,EAAE;4BACjB,SAAS,QAAQ,KAAK;4BACtB,cAAc,QAAQ,KAAK;wBAC7B;oBACF,EAAE,OAAO,KAAU;wBACjB,MAAM,WAAW,IAAI,KAAK,IAAI,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG,6BAA6B;wBACjG,MAAM,OAAO,IAAI,YAAY,IAAI;4BAAE,WAAW;4BAAO,eAAe;4BAAO,iBAAiB;4BAAO,sBAAsB;4BAAM,OAAO;wBAAS;wBAC/I,gBAAgB;wBAChB,SAAS;wBACT,cAAc;oBAChB;gBACF;;YAEA;QACF;8BAAG;QAAC;KAAY;IAEhB,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,gBAAgB,aAAa,KAAK,IAAI,YAAY;gBACrD;YACF;YAEA,MAAM;yDAAoB;oBACxB,IAAI;wBACF,cAAc;wBACd,SAAS;wBAET,+BAA+B;wBAC/B,MAAM,SAAS;4BACb,KAAK;4BACL,OAAO;gCACL,OAAO;gCACP,QAAQ;4BACV;4BACA,aAAa;4BACb,aAAa;4BACb,oBAAoB;gCAAC,iJAAA,CAAA,sBAAmB,CAAC,gBAAgB;6BAAC;4BAC1D,4BAA4B;4BAC5B,2BAA2B;4BAC3B,6BAA6B;4BAC7B,gCAAgC;4BAChC,aAAa;wBACf;wBAEA,mBAAmB;wBACnB,MAAM;2FAAwB,OAAO;gCACnC,IAAI,cAAc,QAAQ,yBAAyB;gCAEnD,gBAAgB;gCAEhB,IAAI;oCACF,uBAAuB;oCACvB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD,EAAE;oCAEzC,IAAI,CAAC,WAAW,OAAO,EAAE;wCACvB,MAAM,eAAe,WAAW,KAAK,IAAI;wCACzC,SAAS;wCACT,cAAc;wCAEd,iCAAiC;wCACjC;2GAAW;gDACT,gBAAgB;4CAClB;0GAAG;wCACH;oCACF;oCAEA,kDAAkD;oCAClD,MAAM,eAAe,WAAW,YAAY;oCAC5C,cAAc;gCAEhB,EAAE,OAAO,KAAc;oCACrB,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;oCAC1D,SAAS;oCACT,cAAc;oCACd,gBAAgB;gCAClB;4BACF;;wBAEA,iBAAiB;wBACjB,MAAM;yFAAsB,CAAC;gCAC3B,uDAAuD;gCACvD,QAAQ,KAAK,CAAC,kBAAkB;4BAClC;;wBAEA,2BAA2B;wBAC3B,MAAM,UAAU,IAAI,uKAAA,CAAA,qBAAkB,CACpC,gBACA,QACA,MAAM,UAAU;;wBAGlB,WAAW,OAAO,GAAG;wBACrB,QAAQ,MAAM,CAAC,uBAAuB;oBAExC,EAAE,OAAO,KAAc;wBACrB,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAC1D,SAAS;wBACT,cAAc;wBACd,cAAc;oBAChB;gBACF;;YAEA;YAEA,mBAAmB;YACnB;uCAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,WAAW,OAAO,CAAC,KAAK,GAAG,KAAK;mDAAC,CAAC;gCAChC,QAAQ,KAAK,CAAC,8BAA8B;4BAC9C;;wBACA,WAAW,OAAO,GAAG;oBACvB;oBACA,cAAc;oBACd,gBAAgB;gBAClB;;QACF;8BAAG;QAAC;QAAc;QAAe;QAAa;QAAc;KAAW;IAEvE,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;uCAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,WAAW,OAAO,CAAC,KAAK,GAAG,KAAK;mDAAC,CAAC;gCAChC,QAAQ,KAAK,CAAC,yCAAyC;4BACzD;;oBACF;gBACF;;QACF;8BAAG,EAAE;IAEL,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAW,CAAC,0DAA0D,EAAE,WAAW;;8BACtF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAyB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAChF,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAC3D,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;gBAC1C,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAMT;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAW,CAAC,qCAAqC,EAAE,WAAW;sBACjE,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;YACpC,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAKrC,6LAAC;gBACC,IAAI;gBACJ,KAAK;gBACL,WAAU;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgC;;;;;;;;;;;;;;;;;AAMrD;GAjNM;KAAA;uCAmNS", "debugId": null}}, {"offset": {"line": 2504, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/qr/QRScannerModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback } from 'react';\r\nimport { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Upload, Camera } from 'lucide-react';\r\nimport { toast } from 'sonner';\r\nimport QRScanner from './QRScanner';\r\nimport { Html5Qrcode } from 'html5-qrcode';\r\nimport { validateQRCodeForUser } from '@/lib/utils/qrCodeUtils';\r\n\r\n\r\ninterface QRScannerModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onScanSuccess: (_businessSlug: string) => void;\r\n}\r\n\r\nconst QRScannerModal: React.FC<QRScannerModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onScanSuccess\r\n}) => {\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [scanMode, setScanMode] = useState<'camera' | 'upload'>('camera');\r\n\r\n  const handleScanSuccess = useCallback((businessSlug: string) => {\r\n    setIsProcessing(true);\r\n    \r\n    // Add a small delay to show success state\r\n    setTimeout(() => {\r\n      toast.success('QR code scanned successfully!');\r\n      onScanSuccess(businessSlug);\r\n      onClose();\r\n      setIsProcessing(false);\r\n    }, 500);\r\n  }, [onScanSuccess, onClose]);\r\n\r\n  const handleScanError = useCallback((error: string) => {\r\n    console.error('QR scan error:', error);\r\n    toast.error(error);\r\n  }, []);\r\n\r\n  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    // Check if file is an image\r\n    if (!file.type.startsWith('image/')) {\r\n      toast.error('Please select an image file');\r\n      return;\r\n    }\r\n\r\n    setIsProcessing(true);\r\n\r\n    try {\r\n      let html5QrCode: Html5Qrcode | undefined; // Declare outside to ensure scope for finally\r\n      try {\r\n        html5QrCode = new Html5Qrcode(\"qr-file-scanner-region\");\r\n        const qrCodeResult = await html5QrCode.scanFile(file);\r\n        console.log('Html5Qrcode scan result:', qrCodeResult);\r\n        \r\n        // Validate the QR code\r\n        const validation = validateQRCodeForUser(qrCodeResult);\r\n        console.log('QR code validation result:', validation);\r\n        \r\n        if (!validation.isValid) {\r\n          const errorMessage = validation.error || 'Invalid QR code';\r\n          toast.error(errorMessage);\r\n          setIsProcessing(false);\r\n          return;\r\n        }\r\n\r\n        // Extract business slug and call success callback\r\n        const businessSlug = validation.businessSlug!;\r\n        handleScanSuccess(businessSlug);\r\n\r\n      } catch (err: any) {\r\n        console.error('QR code scan from image failed:', err);\r\n        let errorMessage = 'Failed to process image';\r\n        if (typeof err === 'string') {\r\n          if (err.includes('QR code not found')) {\r\n            errorMessage = 'No Dukancard QR code found in the image. Please try another image.';\r\n          } else if (err.includes('no multiformat readers')) {\r\n            errorMessage = 'No QR code found in the image. Please ensure it\\'s a clear image of a Dukancard QR code.';\r\n          } else if (err.includes('no multiformat readers')) {\r\n            errorMessage = 'No QR code found in the image. Please ensure it\\'s a clear image of a Dukancard QR code.';\r\n          } else if (err.includes('Image parse error')) {\r\n            errorMessage = 'Could not read the image file. Please ensure it\\'s a valid image.';\r\n          } else {\r\n            errorMessage = err; // Fallback to raw error message if it\\'s a string\r\n          }\r\n        } else if (err instanceof Error) {\r\n          errorMessage = err.message;\r\n        }\r\n        toast.error(errorMessage);\r\n        setIsProcessing(false);\r\n      } finally {\r\n        if (html5QrCode) {\r\n          try {\r\n            html5QrCode.clear();\r\n          } catch (e: unknown) {\r\n            console.error(\"Error clearing html5QrCode\", e);\r\n          }\r\n        }\r\n      }\r\n    } catch (_error) {\r\n      console.error('Outer catch: Failed to process image', _error);\r\n      toast.error('Failed to process image');\r\n      setIsProcessing(false);\r\n    }\r\n\r\n    // Reset file input\r\n    event.target.value = '';\r\n  }, []);\r\n\r\n  const handleClose = useCallback(() => {\r\n    if (!isProcessing) {\r\n      onClose();\r\n    }\r\n  }, [isProcessing, onClose]);\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={handleClose}>\r\n      <DialogContent className=\"sm:max-w-md w-full max-h-[90vh] overflow-hidden\">\r\n        <DialogHeader className=\"pb-4\">\r\n          <DialogTitle className=\"text-lg font-semibold\">\r\n            Scan QR Code\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4\">\r\n          {/* Mode Toggle */}\r\n          <div className=\"flex rounded-lg bg-muted p-1\">\r\n            <button\r\n              onClick={() => setScanMode('camera')}\r\n              disabled={isProcessing}\r\n              className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${\r\n                scanMode === 'camera'\r\n                  ? 'bg-background text-foreground shadow-sm'\r\n                  : 'text-muted-foreground hover:text-foreground'\r\n              }`}\r\n            >\r\n              <Camera className=\"h-4 w-4\" />\r\n              Camera\r\n            </button>\r\n            <button\r\n              onClick={() => setScanMode('upload')}\r\n              disabled={isProcessing}\r\n              className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${\r\n                scanMode === 'upload'\r\n                  ? 'bg-background text-foreground shadow-sm'\r\n                  : 'text-muted-foreground hover:text-foreground'\r\n              }`}\r\n            >\r\n              <Upload className=\"h-4 w-4\" />\r\n              Upload\r\n            </button>\r\n          </div>\r\n\r\n          {/* Scanner Content */}\r\n          <div className=\"relative\">\r\n            {scanMode === 'camera' ? (\r\n              <QRScanner\r\n                onScanSuccess={handleScanSuccess}\r\n                onScanError={handleScanError}\r\n                onClose={handleClose}\r\n                className=\"min-h-[300px]\"\r\n              />\r\n            ) : (\r\n              <div className=\"min-h-[300px] flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-8\">\r\n                <Upload className=\"h-12 w-12 text-muted-foreground mb-4\" />\r\n                <h3 className=\"text-lg font-semibold mb-2\">Upload QR Code Image</h3>\r\n                <p className=\"text-muted-foreground text-center mb-4\">\r\n                  Select an image containing a Dukancard QR code\r\n                </p>\r\n                <label htmlFor=\"qr-upload\" className=\"cursor-pointer\">\r\n                  <Button asChild disabled={isProcessing}>\r\n                    <span>\r\n                      {isProcessing ? 'Processing...' : 'Choose Image'}\r\n                    </span>\r\n                  </Button>\r\n                  <input\r\n                    id=\"qr-upload\"\r\n                    type=\"file\"\r\n                    accept=\"image/*\"\r\n                    onChange={handleFileUpload}\r\n                    disabled={isProcessing}\r\n                    className=\"hidden\"\r\n                  />\r\n                </label>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Hidden div for Html5Qrcode file scanning */}\r\n          <div id=\"qr-file-scanner-region\" style={{ display: 'none' }} />\r\n\r\n          {/* Instructions */}\r\n          <div className=\"text-center space-y-2\">\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {scanMode === 'camera' \r\n                ? 'Position the QR code within the camera frame'\r\n                : 'Upload an image containing a Dukancard QR code'\r\n              }\r\n            </p>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Only Dukancard business QR codes are supported\r\n            </p>\r\n          </div>\r\n\r\n          {/* Processing Overlay */}\r\n          {isProcessing && (\r\n            <div className=\"absolute inset-0 bg-background/80 flex items-center justify-center z-50 rounded-lg\">\r\n              <div className=\"text-center\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\r\n                <p className=\"text-foreground font-medium\">Processing QR code...</p>\r\n                <p className=\"text-sm text-muted-foreground\">Please wait</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default QRScannerModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;;AATA;;;;;;;;;AAkBA,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,OAAO,EACP,aAAa,EACd;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACrC,gBAAgB;YAEhB,0CAA0C;YAC1C;iEAAW;oBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,cAAc;oBACd;oBACA,gBAAgB;gBAClB;gEAAG;QACL;wDAAG;QAAC;QAAe;KAAQ;IAE3B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACnC,QAAQ,KAAK,CAAC,kBAAkB;YAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;sDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO;YAC1C,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;YACpC,IAAI,CAAC,MAAM;YAEX,4BAA4B;YAC5B,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,gBAAgB;YAEhB,IAAI;gBACF,IAAI,aAAsC,8CAA8C;gBACxF,IAAI;oBACF,cAAc,IAAI,4JAAA,CAAA,cAAW,CAAC;oBAC9B,MAAM,eAAe,MAAM,YAAY,QAAQ,CAAC;oBAChD,QAAQ,GAAG,CAAC,4BAA4B;oBAExC,uBAAuB;oBACvB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD,EAAE;oBACzC,QAAQ,GAAG,CAAC,8BAA8B;oBAE1C,IAAI,CAAC,WAAW,OAAO,EAAE;wBACvB,MAAM,eAAe,WAAW,KAAK,IAAI;wBACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,gBAAgB;wBAChB;oBACF;oBAEA,kDAAkD;oBAClD,MAAM,eAAe,WAAW,YAAY;oBAC5C,kBAAkB;gBAEpB,EAAE,OAAO,KAAU;oBACjB,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,IAAI,eAAe;oBACnB,IAAI,OAAO,QAAQ,UAAU;wBAC3B,IAAI,IAAI,QAAQ,CAAC,sBAAsB;4BACrC,eAAe;wBACjB,OAAO,IAAI,IAAI,QAAQ,CAAC,2BAA2B;4BACjD,eAAe;wBACjB,OAAO,IAAI,IAAI,QAAQ,CAAC,2BAA2B;4BACjD,eAAe;wBACjB,OAAO,IAAI,IAAI,QAAQ,CAAC,sBAAsB;4BAC5C,eAAe;wBACjB,OAAO;4BACL,eAAe,KAAK,kDAAkD;wBACxE;oBACF,OAAO,IAAI,eAAe,OAAO;wBAC/B,eAAe,IAAI,OAAO;oBAC5B;oBACA,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,gBAAgB;gBAClB,SAAU;oBACR,IAAI,aAAa;wBACf,IAAI;4BACF,YAAY,KAAK;wBACnB,EAAE,OAAO,GAAY;4BACnB,QAAQ,KAAK,CAAC,8BAA8B;wBAC9C;oBACF;gBACF;YACF,EAAE,OAAO,QAAQ;gBACf,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;YAClB;YAEA,mBAAmB;YACnB,MAAM,MAAM,CAAC,KAAK,GAAG;QACvB;uDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC9B,IAAI,CAAC,cAAc;gBACjB;YACF;QACF;kDAAG;QAAC;QAAc;KAAQ;IAE1B,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC,8HAAA,CAAA,cAAW;wBAAC,WAAU;kCAAwB;;;;;;;;;;;8BAKjD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,UAAU;oCACV,WAAW,CAAC,yGAAyG,EACnH,aAAa,WACT,4CACA,+CACJ;;sDAEF,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,UAAU;oCACV,WAAW,CAAC,yGAAyG,EACnH,aAAa,WACT,4CACA,+CACJ;;sDAEF,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAMlC,6LAAC;4BAAI,WAAU;sCACZ,aAAa,yBACZ,6LAAC,iIAAA,CAAA,UAAS;gCACR,eAAe;gCACf,aAAa;gCACb,SAAS;gCACT,WAAU;;;;;qDAGZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;kDAGtD,6LAAC;wCAAM,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC,8HAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,UAAU;0DACxB,cAAA,6LAAC;8DACE,eAAe,kBAAkB;;;;;;;;;;;0DAGtC,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,QAAO;gDACP,UAAU;gDACV,UAAU;gDACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAQpB,6LAAC;4BAAI,IAAG;4BAAyB,OAAO;gCAAE,SAAS;4BAAO;;;;;;sCAG1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,aAAa,WACV,iDACA;;;;;;8CAGN,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;wBAM9C,8BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D;GA/MM;KAAA;uCAiNS", "debugId": null}}, {"offset": {"line": 2894, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr';\nimport { Database } from '@/types/supabase';\n\nexport function createClient() {\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n  if (!supabaseUrl || !supabaseAnonKey) {\n    console.error(\"Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined.\");\n    return createBrowserClient<Database>(\"\", \"\");\n  }\n\n  return createBrowserClient<Database>(\n    supabaseUrl,\n    supabaseAnonKey\n  );\n}\n"], "names": [], "mappings": ";;;AAIsB;AAJtB;AAAA;;AAGO,SAAS;IACd,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAGtC;IAEA,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EACvB,aACA;AAEJ", "debugId": null}}, {"offset": {"line": 2918, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/BottomNav.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { motion } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Home, Search, User, Store, QrCode } from \"lucide-react\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport QRScannerModal from \"@/components/qr/QRScannerModal\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\n\r\ninterface BottomNavItemProps {\r\n  href?: string;\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  isActive: boolean;\r\n  isTablet?: boolean;\r\n  badge?: string;\r\n  disabled?: boolean;\r\n  onClick?: () => void;\r\n  isSpecial?: boolean;\r\n}\r\n\r\nconst BottomNavItem = ({\r\n  href,\r\n  icon,\r\n  label,\r\n  isActive,\r\n  isTablet = false,\r\n  badge,\r\n  disabled = false,\r\n  onClick,\r\n  isSpecial = false\r\n}: BottomNavItemProps) => {\r\n  const content = (\r\n    <>\r\n      <div className={cn(\r\n        \"relative mb-1 transition-all duration-200\",\r\n        isSpecial && \"bg-[var(--brand-gold)] rounded-full p-3 -mt-2 shadow-lg\"\r\n      )}>\r\n        <div className={cn(\r\n          isSpecial && \"text-white\"\r\n        )}>\r\n          {icon}\r\n        </div>\r\n        {badge && (\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"absolute -top-2 -right-3 px-1 py-0 text-[7px] bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]\"\r\n          >\r\n            {badge}\r\n          </Badge>\r\n        )}\r\n      </div>\r\n      <span className={cn(\r\n        \"transition-all\",\r\n        isTablet ? \"text-[9px]\" : \"text-[10px]\",\r\n        isSpecial && \"text-[var(--brand-gold)] font-medium\"\r\n      )}>{label}</span>\r\n    </>\r\n  );\r\n\r\n  const itemClassName = cn(\r\n    \"flex flex-col items-center justify-center flex-1 py-2 text-xs transition-colors cursor-pointer\",\r\n    isActive\r\n      ? \"text-[var(--brand-gold)]\"\r\n      : \"text-muted-foreground hover:text-[var(--brand-gold)]\",\r\n    disabled && \"opacity-70 pointer-events-none\",\r\n    isSpecial && \"transform hover:scale-105\"\r\n  );\r\n\r\n  if (disabled) {\r\n    return (\r\n      <div className={itemClassName}>\r\n        {content}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (onClick) {\r\n    return (\r\n      <button onClick={onClick} className={itemClassName}>\r\n        {content}\r\n      </button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Link href={href!} className={itemClassName}>\r\n      {content}\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default function BottomNav() {\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const isMobile = useIsMobile();\r\n  const [isTablet, setIsTablet] = useState(false);\r\n  const [isQRScannerOpen, setIsQRScannerOpen] = useState(false);\r\n  const [_businessSlug, setBusinessSlug] = useState<string | null>(null);\r\n  const [userType, setUserType] = useState<'business' | 'customer' | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (typeof window === \"undefined\") return;\r\n\r\n    // Check if device is a tablet (between 768px and 1024px)\r\n    const checkTablet = () => {\r\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n    };\r\n\r\n    // Initial check\r\n    checkTablet();\r\n\r\n    // Add event listener for resize\r\n    window.addEventListener('resize', checkTablet);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkTablet);\r\n  }, []);\r\n\r\n  // Fetch user type and business slug for proper navigation\r\n  useEffect(() => {\r\n    const fetchUserInfo = async () => {\r\n      const supabase = createClient();\r\n      const { data: { user } } = await supabase.auth.getUser();\r\n\r\n      if (user) {\r\n        // Check if user has business profile\r\n        const { data: businessProfile } = await supabase\r\n          .from('business_profiles')\r\n          .select('business_slug')\r\n          .eq('id', user.id)\r\n          .single();\r\n\r\n        if (businessProfile) {\r\n          setUserType('business');\r\n          setBusinessSlug(businessProfile.business_slug);\r\n        } else {\r\n          // Check for customer profile\r\n          const { data: customerProfile } = await supabase\r\n            .from('customer_profiles')\r\n            .select('id')\r\n            .eq('user_id', user.id)\r\n            .single();\r\n\r\n          if (customerProfile) {\r\n            setUserType('customer');\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchUserInfo();\r\n  }, []);\r\n\r\n  // QR Scanner handlers\r\n  const handleQRScanPress = () => {\r\n    setIsQRScannerOpen(true);\r\n  };\r\n\r\n  const handleQRScanSuccess = (businessSlug: string) => {\r\n    setIsQRScannerOpen(false);\r\n    // Navigate to business card page\r\n    router.push(`/${businessSlug}`);\r\n  };\r\n\r\n  const handleQRScannerClose = () => {\r\n    setIsQRScannerOpen(false);\r\n  };\r\n\r\n  // Don't render on desktop\r\n  if (!isMobile && !isTablet) {\r\n    return null;\r\n  }\r\n\r\n  // Determine navigation links based on current path and user context\r\n  let accountLink = \"/login\";\r\n  let accountIsActive = false;\r\n  let homeLink = \"/\";\r\n  let homeIsActive = false;\r\n\r\n  // If user is in business dashboard\r\n  if (pathname.startsWith(\"/dashboard/business\")) {\r\n    // Business users: Account button goes to their dashboard card page, Home goes to business feed\r\n    accountLink = \"/dashboard/business/card\";\r\n    accountIsActive = pathname === \"/dashboard/business/card\";\r\n    homeLink = \"/dashboard/business\";\r\n    homeIsActive = pathname === \"/dashboard/business\";\r\n  }\r\n  // If user is in customer dashboard\r\n  else if (pathname.startsWith(\"/dashboard/customer\")) {\r\n    // Customer users: Account button goes to profile, Home goes to customer feed\r\n    accountLink = \"/dashboard/customer/profile\";\r\n    accountIsActive = pathname.includes(\"/dashboard/customer/profile\");\r\n    homeLink = \"/dashboard/customer\";\r\n    homeIsActive = pathname === \"/dashboard/customer\";\r\n  }\r\n  // If user is in auth or onboarding flow\r\n  else if (pathname.startsWith(\"/login\") ||\r\n           pathname.startsWith(\"/choose-role\") || pathname.startsWith(\"/onboarding\")) {\r\n    accountLink = pathname; // Keep current page\r\n    accountIsActive = true;\r\n    homeLink = \"/\";\r\n    homeIsActive = pathname === \"/\";\r\n  }\r\n  // For public pages, determine based on user type\r\n  else {\r\n    if (userType === 'business') {\r\n      accountLink = \"/dashboard/business/card\";\r\n      accountIsActive = false;\r\n      homeLink = \"/dashboard/business\";\r\n      homeIsActive = false;\r\n    } else if (userType === 'customer') {\r\n      accountLink = \"/dashboard/customer/profile\";\r\n      accountIsActive = false;\r\n      homeLink = \"/dashboard/customer\";\r\n      homeIsActive = false;\r\n    } else {\r\n      accountLink = \"/login\";\r\n      accountIsActive = pathname === \"/login\";\r\n      homeLink = \"/\";\r\n      homeIsActive = pathname === \"/\";\r\n    }\r\n  }\r\n\r\n  // QR Scanner is always available (no authentication required for scanning)\r\n\r\n  // Unified navigation items\r\n  const navItems = [\r\n    {\r\n      key: \"home\",\r\n      href: homeLink,\r\n      icon: <Home size={20} />,\r\n      label: \"Home\",\r\n      isActive: homeIsActive\r\n    },\r\n    {\r\n      key: \"discover\",\r\n      href: \"/discover\",\r\n      icon: <Search size={20} />,\r\n      label: \"Discover\",\r\n      isActive: pathname === \"/discover\"\r\n    },\r\n    {\r\n      key: \"scan\",\r\n      icon: <QrCode size={20} />,\r\n      label: \"Scan\",\r\n      isActive: false,\r\n      onClick: handleQRScanPress,\r\n      isSpecial: true\r\n    },\r\n    {\r\n      key: \"dukan-ai\",\r\n      href: \"#\",\r\n      icon: <Store size={20} />,\r\n      label: \"Dukan AI\",\r\n      isActive: false,\r\n      badge: \"Soon\",\r\n      disabled: true\r\n    },\r\n    {\r\n      key: \"account\",\r\n      href: accountLink,\r\n      icon: <User size={20} />,\r\n      label: \"Account\",\r\n      isActive: accountIsActive\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <motion.div\r\n        initial={{ y: 100 }}\r\n        animate={{ y: 0 }}\r\n        transition={{ duration: 0.3 }}\r\n        className={cn(\r\n          \"fixed bottom-0 left-0 right-0 z-50 flex items-center justify-around bg-background/95 backdrop-blur-lg border-t border-border/80 px-2\",\r\n          isTablet ? \"h-14\" : \"h-16\"\r\n        )}\r\n      >\r\n        {navItems.map((item) => (\r\n          <BottomNavItem\r\n            key={item.key}\r\n            href={item.href}\r\n            icon={item.icon}\r\n            label={item.label}\r\n            isActive={item.isActive}\r\n            isTablet={isTablet}\r\n            badge={item.badge}\r\n            disabled={item.disabled}\r\n            onClick={item.onClick}\r\n            isSpecial={item.isSpecial}\r\n          />\r\n        ))}\r\n      </motion.div>\r\n\r\n      {/* QR Scanner Modal */}\r\n      <QRScannerModal\r\n        isOpen={isQRScannerOpen}\r\n        onClose={handleQRScannerClose}\r\n        onScanSuccess={handleQRScanSuccess}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AA0BA,MAAM,gBAAgB,CAAC,EACrB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,KAAK,EACL,WAAW,KAAK,EAChB,OAAO,EACP,YAAY,KAAK,EACE;IACnB,MAAM,wBACJ;;0BACE,6LAAC;gBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,6CACA,aAAa;;kCAEb,6LAAC;wBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,aAAa;kCAEZ;;;;;;oBAEF,uBACC,6LAAC,6HAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,WAAU;kCAET;;;;;;;;;;;;0BAIP,6LAAC;gBAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAChB,kBACA,WAAW,eAAe,eAC1B,aAAa;0BACX;;;;;;;;IAIR,MAAM,gBAAgB,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACrB,kGACA,WACI,6BACA,wDACJ,YAAY,kCACZ,aAAa;IAGf,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAI,WAAW;sBACb;;;;;;IAGP;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAO,SAAS;YAAS,WAAW;sBAClC;;;;;;IAGP;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM;QAAO,WAAW;kBAC3B;;;;;;AAGP;KArEM;AAuES,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAEzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,uCAAmC;;YAAM;YAEzC,yDAAyD;YACzD,MAAM;mDAAc;oBAClB,YAAY,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;gBAC9D;;YAEA,gBAAgB;YAChB;YAEA,gCAAgC;YAChC,OAAO,gBAAgB,CAAC,UAAU;YAElC,UAAU;YACV;uCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;8BAAG,EAAE;IAEL,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;qDAAgB;oBACpB,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;oBAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBAEtD,IAAI,MAAM;wBACR,qCAAqC;wBACrC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBAET,IAAI,iBAAiB;4BACnB,YAAY;4BACZ,gBAAgB,gBAAgB,aAAa;wBAC/C,OAAO;4BACL,6BAA6B;4BAC7B,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;4BAET,IAAI,iBAAiB;gCACnB,YAAY;4BACd;wBACF;oBACF;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,iCAAiC;QACjC,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc;IAChC;IAEA,MAAM,uBAAuB;QAC3B,mBAAmB;IACrB;IAEA,0BAA0B;IAC1B,IAAI,CAAC,YAAY,CAAC,UAAU;QAC1B,OAAO;IACT;IAEA,oEAAoE;IACpE,IAAI,cAAc;IAClB,IAAI,kBAAkB;IACtB,IAAI,WAAW;IACf,IAAI,eAAe;IAEnB,mCAAmC;IACnC,IAAI,SAAS,UAAU,CAAC,wBAAwB;QAC9C,+FAA+F;QAC/F,cAAc;QACd,kBAAkB,aAAa;QAC/B,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK,IAAI,SAAS,UAAU,CAAC,wBAAwB;QACnD,6EAA6E;QAC7E,cAAc;QACd,kBAAkB,SAAS,QAAQ,CAAC;QACpC,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK,IAAI,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,mBAAmB,SAAS,UAAU,CAAC,gBAAgB;QAClF,cAAc,UAAU,oBAAoB;QAC5C,kBAAkB;QAClB,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK;QACH,IAAI,aAAa,YAAY;YAC3B,cAAc;YACd,kBAAkB;YAClB,WAAW;YACX,eAAe;QACjB,OAAO,IAAI,aAAa,YAAY;YAClC,cAAc;YACd,kBAAkB;YAClB,WAAW;YACX,eAAe;QACjB,OAAO;YACL,cAAc;YACd,kBAAkB,aAAa;YAC/B,WAAW;YACX,eAAe,aAAa;QAC9B;IACF;IAEA,2EAA2E;IAE3E,2BAA2B;IAC3B,MAAM,WAAW;QACf;YACE,KAAK;YACL,MAAM;YACN,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,OAAO;YACP,UAAU,aAAa;QACzB;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,6MAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,OAAO;YACP,UAAU;YACV,SAAS;YACT,WAAW;QACb;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE;;0BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;gBAAI;gBAClB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wIACA,WAAW,SAAS;0BAGrB,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;wBAEC,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,UAAU;wBACV,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,SAAS,KAAK,OAAO;wBACrB,WAAW,KAAK,SAAS;uBATpB,KAAK,GAAG;;;;;;;;;;0BAenB,6LAAC,sIAAA,CAAA,UAAc;gBACb,QAAQ;gBACR,SAAS;gBACT,eAAe;;;;;;;;AAIvB;GAnNwB;;QACL,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACP,yHAAA,CAAA,cAAW;;;MAHN", "debugId": null}}, {"offset": {"line": 3271, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 3339, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none cursor-pointer\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 3566, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 3663, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeftIcon } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (_open: boolean) => void // Keep prefix\r\n  openMobile: boolean\r\n  setOpenMobile: (_open: boolean) => void // Disabled warning for unused type param\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean\r\n  open?: boolean\r\n  onOpenChange?: (_open: boolean) => void // Disabled warning for unused type param\r\n}) {\r\n  const isMobile = useIsMobile()\r\n  const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen)\r\n  const open = openProp ?? _open\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((_value: boolean) => boolean)) => { // Prefixed inner 'value' parameter\r\n      const openState = typeof value === \"function\" ? value(open) : value\r\n      if (setOpenProp) {\r\n        setOpenProp(openState)\r\n      } else {\r\n        _setOpen(openState)\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      if (typeof document !== \"undefined\") {\r\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n      }\r\n    },\r\n    [setOpenProp, open]\r\n  )\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((_open) => !_open) : setOpen((_open) => !_open) // Disabled warning for unused inner params\r\n  }, [isMobile, setOpen, setOpenMobile])\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    if (typeof window === \"undefined\") return;\r\n\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault()\r\n        toggleSidebar()\r\n      }\r\n    }\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown)\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n  }, [toggleSidebar])\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  )\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  )\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\"\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-white dark:bg-black text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-white dark:bg-black group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex cursor-pointer\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  isActive?: boolean\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  const { isMobile, state } = useSidebar()\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n\r\n  if (!tooltip) {\r\n    return button\r\n  }\r\n\r\n  if (typeof tooltip === \"string\") {\r\n    tooltip = {\r\n      children: tooltip,\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={state !== \"collapsed\" || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  showOnHover?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n  size?: \"sm\" | \"md\"\r\n  isActive?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,IAAI,OAAO,aAAa,aAAa;gBACnC,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;YACpG;QACF;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WAAW;8DAAc,CAAC,QAAU,CAAC;+DAAS;8DAAQ,CAAC,QAAU,CAAC;6DAAO,2CAA2C;;QAC7H;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACd,uCAAmC;;YAAM;YAEzC,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,+HAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IApGS;;QAaU,yHAAA,CAAA,cAAW;;;KAbrB;AAsGT,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,6HAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,6HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,6HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,6HAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,8HAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kQACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,6LAAC,6HAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,6LAAC,iIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,o0BACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;;IAChD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAO;;0BACN,6LAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,+HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;IAhDS;;QAcqB;;;OAdrB;AAkDT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mWACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS;AAgCT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OApBS;AAsBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ggBACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS", "debugId": null}}, {"offset": {"line": 4421, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/sidebar/SidebarLink.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\n\r\ninterface SidebarLinkProps {\r\n  href: string;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport function SidebarLink({ href, children, className }: SidebarLinkProps) {\r\n  const { isMobile, setOpenMobile } = useSidebar();\r\n\r\n  const handleClick = () => {\r\n    // Only close the sidebar on mobile devices\r\n    if (isMobile) {\r\n      setOpenMobile(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link href={href} className={className} onClick={handleClick}>\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAYO,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAoB;;IACzE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD;IAE7C,MAAM,cAAc;QAClB,2CAA2C;QAC3C,IAAI,UAAU;YACZ,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAW;QAAW,SAAS;kBAC9C;;;;;;AAGP;GAfgB;;QACsB,+HAAA,CAAA,aAAU;;;KADhC", "debugId": null}}, {"offset": {"line": 4469, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,0KAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS", "debugId": null}}, {"offset": {"line": 4526, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/sidebar/NavBusinessUser.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  ChevronsUpDown,\r\n  LogOut,\r\n  // Import other icons if needed for dropdown items\r\n} from \"lucide-react\";\r\nimport { signOutUser } from \"@/app/auth/actions\"; // Assuming this action is correct\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\nimport { Button } from \"@/components/ui/button\"; // Import Button for the form\r\nimport { cn } from \"@/lib/utils\";\r\n\r\n// Helper to get initials (can be moved to utils if used elsewhere)\r\nconst getInitials = (name: string | null | undefined): string => {\r\n  if (!name) return \"?\";\r\n  const names = name.trim().split(/\\s+/);\r\n  if (names.length === 1 && names[0]) return names[0].charAt(0).toUpperCase();\r\n  if (names.length > 1 && names[0] && names[names.length - 1]) {\r\n    return (\r\n      names[0].charAt(0).toUpperCase() +\r\n      names[names.length - 1].charAt(0).toUpperCase()\r\n    );\r\n  }\r\n  return \"?\";\r\n};\r\n\r\n\r\ninterface NavBusinessUserProps {\r\n  user: {\r\n    name: string | null; // Member name\r\n    avatar: string | null; // Business logo URL\r\n  };\r\n  businessName: string | null;\r\n}\r\n\r\nexport function NavBusinessUser({ user, businessName }: NavBusinessUserProps) {\r\n  const { isMobile } = useSidebar();\r\n  const businessInitials = getInitials(businessName);\r\n  const [isTablet, setIsTablet] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Check if device is a tablet (between 768px and 1024px)\r\n    const checkTablet = () => {\r\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n    };\r\n\r\n    // Initial check\r\n    checkTablet();\r\n\r\n    // Add event listener for resize\r\n    window.addEventListener('resize', checkTablet);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkTablet);\r\n  }, []);\r\n\r\n  // Use business logo/initials for the main trigger avatar\r\n  const displayAvatar = user.avatar;\r\n  const displayFallback = businessInitials;\r\n  const displayName = businessName || \"Business\";\r\n  const displaySubText = user.name || \"Member\";\r\n\r\n  // Calculate bottom padding based on device type\r\n  const bottomPadding = isMobile ? \"pb-16\" : isTablet ? \"pb-14\" : \"\";\r\n\r\n  return (\r\n    <SidebarMenu className={cn(bottomPadding)}>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n            >\r\n              <Avatar className=\"h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30\">\r\n                {displayAvatar ? (\r\n                  <AvatarImage src={displayAvatar} alt={displayName} />\r\n                ) : null}\r\n                <AvatarFallback className=\"rounded-lg bg-muted border border-[var(--brand-gold)]/30 text-xs\">\r\n                  {displayFallback}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                <span className=\"truncate font-semibold\">{displayName}</span>\r\n                <span className=\"truncate text-xs text-muted-foreground\">{displaySubText}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto size-4\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                 <Avatar className=\"h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30\">\r\n                    {displayAvatar ? (\r\n                      <AvatarImage src={displayAvatar} alt={displayName} />\r\n                    ) : null}\r\n                    <AvatarFallback className=\"rounded-lg bg-muted border border-[var(--brand-gold)]/30 text-xs\">\r\n                      {displayFallback}\r\n                    </AvatarFallback>\r\n                 </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-semibold\">{displayName}</span>\r\n                   <span className=\"truncate text-xs text-muted-foreground\">{displaySubText}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            {/* Add any relevant dropdown items here if needed */}\r\n            {/* <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <Settings /> Settings // Example\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator /> */}\r\n            <form action={signOutUser} className=\"w-full px-2 py-1.5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                type=\"submit\"\r\n                className=\"w-full justify-start p-0 h-auto font-normal cursor-pointer\"\r\n              >\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                Log out\r\n              </Button>\r\n            </form>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAKA,oQAAkD,kCAAkC;AACpF;AACA;AAOA;AAMA,8NAAiD,6BAA6B;AAC9E;;;AAxBA;;;;;;;;;AA0BA,mEAAmE;AACnE,MAAM,cAAc,CAAC;IACnB,IAAI,CAAC,MAAM,OAAO;IAClB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;IACzE,IAAI,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,EAAE;QAC3D,OACE,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAC9B,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;IAEjD;IACA,OAAO;AACT;AAWO,SAAS,gBAAgB,EAAE,IAAI,EAAE,YAAY,EAAwB;;IAC1E,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,mBAAmB,YAAY;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,yDAAyD;YACzD,MAAM;yDAAc;oBAClB,YAAY,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;gBAC9D;;YAEA,gBAAgB;YAChB;YAEA,gCAAgC;YAChC,OAAO,gBAAgB,CAAC,UAAU;YAElC,UAAU;YACV;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,gBAAgB,KAAK,MAAM;IACjC,MAAM,kBAAkB;IACxB,MAAM,cAAc,gBAAgB;IACpC,MAAM,iBAAiB,KAAK,IAAI,IAAI;IAEpC,gDAAgD;IAChD,MAAM,gBAAgB,WAAW,UAAU,WAAW,UAAU;IAEhE,qBACE,6LAAC,+HAAA,CAAA,cAAW;QAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;kBACzB,cAAA,6LAAC,+HAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC,wIAAA,CAAA,eAAY;;kCACX,6LAAC,wIAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,+HAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,6LAAC,8HAAA,CAAA,SAAM;oCAAC,WAAU;;wCACf,8BACC,6LAAC,8HAAA,CAAA,cAAW;4CAAC,KAAK;4CAAe,KAAK;;;;;mDACpC;sDACJ,6LAAC,8HAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB;;;;;;;;;;;;8CAGL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;sDAC1C,6LAAC;4CAAK,WAAU;sDAA0C;;;;;;;;;;;;8CAE5D,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6LAAC,wIAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,6LAAC,wIAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACZ,6LAAC,8HAAA,CAAA,SAAM;4CAAC,WAAU;;gDACd,8BACC,6LAAC,8HAAA,CAAA,cAAW;oDAAC,KAAK;oDAAe,KAAK;;;;;2DACpC;8DACJ,6LAAC,8HAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGP,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;8DACzC,6LAAC;oDAAK,WAAU;8DAA0C;;;;;;;;;;;;;;;;;;;;;;;0CAIjE,6LAAC,wIAAA,CAAA,wBAAqB;;;;;0CAQtB,6LAAC;gCAAK,QAAQ,sJAAA,CAAA,cAAW;gCAAE,WAAU;0CACnC,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GAnGgB;;QACO,+HAAA,CAAA,aAAU;;;KADjB", "debugId": null}}, {"offset": {"line": 4813, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/sidebar/NavBusinessMain.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { usePathname } from 'next/navigation';\r\nimport { SidebarLink } from \"./SidebarLink\";\r\nimport {\r\n  ChevronRight,\r\n  LayoutDashboard,\r\n  CreditCard,\r\n  Package,\r\n  BarChart3,\r\n  Settings,\r\n  WalletCards,\r\n  Tag,\r\n  Heart,\r\n  Bell,\r\n  Store,\r\n  Users,\r\n  User,\r\n  Activity,\r\n  Star,\r\n  Image,\r\n  LayoutList,\r\n  FileEdit,\r\n  type LucideIcon\r\n} from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\n\r\n// Map string icon names to actual Lucide components\r\nexport const iconMap: { [key: string]: LucideIcon } = {\r\n  LayoutDashboard,\r\n  CreditCard,\r\n  Package,\r\n  BarChart3,\r\n  Settings,\r\n  WalletCards,\r\n  Tag,\r\n  Heart,\r\n  Bell,\r\n  Store,\r\n  Users,\r\n  User,\r\n  Activity,\r\n  Star,\r\n  Image,\r\n  LayoutList,\r\n  FileEdit,\r\n};\r\n\r\ninterface NavItemData {\r\n  href: string;\r\n  icon: string;\r\n  label: string;\r\n  isActive?: boolean;\r\n  badge?: string;\r\n  badgeVariant?: \"default\" | \"secondary\" | \"destructive\" | \"outline\" | \"upgrade\";\r\n  items?: {\r\n    href: string;\r\n    label: string;\r\n  }[];\r\n}\r\n\r\ninterface NavBusinessMainProps {\r\n  items: NavItemData[];\r\n}\r\n\r\nexport function NavBusinessMain({ items }: NavBusinessMainProps) {\r\n  const pathname = usePathname();\r\n\r\n  return (\r\n    <SidebarGroup>\r\n      {/* <SidebarGroupLabel>Platform</SidebarGroupLabel> // Optional label */}\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          const IconComponent = iconMap[item.icon];\r\n          // Determine active state based on current path\r\n          const isActive = pathname === item.href || (item.href !== \"/dashboard/business\" && pathname.startsWith(item.href));\r\n\r\n          // If item has sub-items, render as collapsible\r\n          if (item.items && item.items.length > 0) {\r\n            return (\r\n              <Collapsible\r\n                key={item.label}\r\n                asChild\r\n                defaultOpen={isActive}\r\n                className=\"group/collapsible\"\r\n              >\r\n                <SidebarMenuItem>\r\n                  <CollapsibleTrigger asChild>\r\n                    {/* Apply brand gold styling more directly to active state */}\r\n                    <SidebarMenuButton tooltip={item.label} isActive={isActive} className={cn(isActive && \"bg-accent text-[var(--brand-gold)] dark:bg-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]\")}>\r\n                      {IconComponent && <IconComponent className={cn(isActive ? \"text-[var(--brand-gold)]\" : \"\")}/>}\r\n                      <span>{item.label}</span>\r\n                      <ChevronRight className=\"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90\" />\r\n                    </SidebarMenuButton>\r\n                  </CollapsibleTrigger>\r\n                  <CollapsibleContent>\r\n                    <SidebarMenuSub>\r\n                      {item.items.map((subItem) => {\r\n                         const isSubActive = pathname === subItem.href;\r\n                         return (\r\n                            <SidebarMenuSubItem key={subItem.label}>\r\n                              <SidebarMenuSubButton asChild isActive={isSubActive}>\r\n                                <SidebarLink href={subItem.href}>\r\n                                  <span>{subItem.label}</span>\r\n                                </SidebarLink>\r\n                              </SidebarMenuSubButton>\r\n                            </SidebarMenuSubItem>\r\n                         );\r\n                      })}\r\n                    </SidebarMenuSub>\r\n                  </CollapsibleContent>\r\n                </SidebarMenuItem>\r\n              </Collapsible>\r\n            );\r\n          }\r\n\r\n          // Otherwise, render as a direct link\r\n          return (\r\n            <SidebarMenuItem key={item.label}>\r\n               {/* Apply brand gold styling more directly to active state */}\r\n              <SidebarMenuButton asChild tooltip={item.label} isActive={isActive} className={cn(isActive && \"bg-accent text-[var(--brand-gold)] dark:bg-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]\")}>\r\n                <SidebarLink href={item.href}>\r\n                  {IconComponent && <IconComponent className={cn(isActive ? \"text-[var(--brand-gold)]\" : \"\")}/>}\r\n                  <span>{item.label}</span>\r\n                  {item.badge && (\r\n                    <Badge\r\n                      variant={item.badgeVariant === \"upgrade\" ? \"default\" : item.badgeVariant || \"default\"}\r\n                      className={cn(\r\n                        \"ml-2 text-xs\",\r\n                        item.badgeVariant === \"upgrade\" && \"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300\"\r\n                      )}\r\n                    >\r\n                      {item.badge}\r\n                    </Badge>\r\n                  )}\r\n                </SidebarLink>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;AAKA;AASA;;;AAzCA;;;;;;;;AA4CO,MAAM,UAAyC;IACpD,iBAAA,+NAAA,CAAA,kBAAe;IACf,YAAA,qNAAA,CAAA,aAAU;IACV,SAAA,2MAAA,CAAA,UAAO;IACP,WAAA,qNAAA,CAAA,YAAS;IACT,UAAA,6MAAA,CAAA,WAAQ;IACR,aAAA,uNAAA,CAAA,cAAW;IACX,KAAA,mMAAA,CAAA,MAAG;IACH,OAAA,uMAAA,CAAA,QAAK;IACL,MAAA,qMAAA,CAAA,OAAI;IACJ,OAAA,uMAAA,CAAA,QAAK;IACL,OAAA,uMAAA,CAAA,QAAK;IACL,MAAA,qMAAA,CAAA,OAAI;IACJ,UAAA,6MAAA,CAAA,WAAQ;IACR,MAAA,qMAAA,CAAA,OAAI;IACJ,OAAA,uMAAA,CAAA,QAAK;IACL,YAAA,qNAAA,CAAA,aAAU;IACV,UAAA,gNAAA,CAAA,WAAQ;AACV;AAmBO,SAAS,gBAAgB,EAAE,KAAK,EAAwB;;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC,+HAAA,CAAA,eAAY;kBAEX,cAAA,6LAAC,+HAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,MAAM,gBAAgB,OAAO,CAAC,KAAK,IAAI,CAAC;gBACxC,+CAA+C;gBAC/C,MAAM,WAAW,aAAa,KAAK,IAAI,IAAK,KAAK,IAAI,KAAK,yBAAyB,SAAS,UAAU,CAAC,KAAK,IAAI;gBAEhH,+CAA+C;gBAC/C,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,qBACE,6LAAC,mIAAA,CAAA,cAAW;wBAEV,OAAO;wBACP,aAAa;wBACb,WAAU;kCAEV,cAAA,6LAAC,+HAAA,CAAA,kBAAe;;8CACd,6LAAC,mIAAA,CAAA,qBAAkB;oCAAC,OAAO;8CAEzB,cAAA,6LAAC,+HAAA,CAAA,oBAAiB;wCAAC,SAAS,KAAK,KAAK;wCAAE,UAAU;wCAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;4CACnF,+BAAiB,6LAAC;gDAAc,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,6BAA6B;;;;;;0DACvF,6LAAC;0DAAM,KAAK,KAAK;;;;;;0DACjB,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG5B,6LAAC,mIAAA,CAAA,qBAAkB;8CACjB,cAAA,6LAAC,+HAAA,CAAA,iBAAc;kDACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC;4CACd,MAAM,cAAc,aAAa,QAAQ,IAAI;4CAC7C,qBACG,6LAAC,+HAAA,CAAA,qBAAkB;0DACjB,cAAA,6LAAC,+HAAA,CAAA,uBAAoB;oDAAC,OAAO;oDAAC,UAAU;8DACtC,cAAA,6LAAC,wIAAA,CAAA,cAAW;wDAAC,MAAM,QAAQ,IAAI;kEAC7B,cAAA,6LAAC;sEAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;+CAHD,QAAQ,KAAK;;;;;wCAQ5C;;;;;;;;;;;;;;;;;uBA3BD,KAAK,KAAK;;;;;gBAiCrB;gBAEA,qCAAqC;gBACrC,qBACE,6LAAC,+HAAA,CAAA,kBAAe;8BAEd,cAAA,6LAAC,+HAAA,CAAA,oBAAiB;wBAAC,OAAO;wBAAC,SAAS,KAAK,KAAK;wBAAE,UAAU;wBAAU,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kCAC5F,cAAA,6LAAC,wIAAA,CAAA,cAAW;4BAAC,MAAM,KAAK,IAAI;;gCACzB,+BAAiB,6LAAC;oCAAc,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,6BAA6B;;;;;;8CACvF,6LAAC;8CAAM,KAAK,KAAK;;;;;;gCAChB,KAAK,KAAK,kBACT,6LAAC,6HAAA,CAAA,QAAK;oCACJ,SAAS,KAAK,YAAY,KAAK,YAAY,YAAY,KAAK,YAAY,IAAI;oCAC5E,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gBACA,KAAK,YAAY,KAAK,aAAa;8CAGpC,KAAK,KAAK;;;;;;;;;;;;;;;;;mBAdC,KAAK,KAAK;;;;;YAqBpC;;;;;;;;;;;AAIR;GA9EgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 5060, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/sidebar/BusinessAppSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { SidebarLink } from \"./SidebarLink\";\r\nimport {\r\n  Sidebar,\r\n  <PERSON>barContent,\r\n  SidebarFooter,\r\n  SidebarHeader,\r\n  SidebarRail,\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuItem,\r\n  SidebarMenuButton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { NavBusinessUser } from \"./NavBusinessUser\";\r\nimport { ChevronRight } from \"lucide-react\";\r\n// Import the icon map from NavBusinessMain\r\nimport { iconMap } from \"./NavBusinessMain\";\r\nimport { realtimeService } from \"@/lib/services/realtimeService\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\n\r\n// Define types for navigation items\r\ninterface NavItem {\r\n  title: string;\r\n  icon?: string;\r\n  url?: string;\r\n  badge?: string;\r\n  badgeVariant?:\r\n    | \"default\"\r\n    | \"secondary\"\r\n    | \"destructive\"\r\n    | \"outline\"\r\n    | \"upgrade\";\r\n}\r\n\r\ninterface NavSection {\r\n  title: string;\r\n  icon?: string;\r\n  url?: string;\r\n  items: NavItem[];\r\n}\r\n\r\n// Define props if necessary, e.g., business data\r\ninterface BusinessAppSidebarProps extends React.ComponentProps<typeof Sidebar> {\r\n  businessName: string | null;\r\n  logoUrl: string | null;\r\n  memberName: string | null;\r\n  userPlan: string | null;\r\n}\r\n\r\nexport function BusinessAppSidebar({\r\n  businessName: propBusinessName,\r\n  logoUrl: propLogoUrl,\r\n  memberName: propMemberName,\r\n  userPlan: propUserPlan,\r\n  ...props\r\n}: BusinessAppSidebarProps) {\r\n  // Use props data directly\r\n  const businessName = propBusinessName;\r\n  const logoUrl = propLogoUrl;\r\n  const memberName = propMemberName;\r\n  const userPlan = propUserPlan; // Keep using prop for plan as it comes from subscription data\r\n\r\n  // Prepare data structure for nav components\r\n  const userData = {\r\n    name: memberName,\r\n    // email: memberEmail, // Assuming email might be available later\r\n    avatar: logoUrl, // Use logoUrl for avatar for now\r\n  };\r\n\r\n  // Define main navigation items for business dashboard with collapsible sections\r\n  const navData: NavSection[] = [\r\n    {\r\n      title: \"Feed\",\r\n      icon: \"LayoutList\",\r\n      url: \"/dashboard/business\",\r\n      items: [],\r\n    },\r\n    {\r\n      title: \"Overview\",\r\n      icon: \"LayoutDashboard\",\r\n      url: \"/dashboard/business/overview\",\r\n      items: [],\r\n    },\r\n    {\r\n      title: \"Business Management\",\r\n      icon: \"Store\",\r\n      items: [\r\n        {\r\n          title: \"Manage Card\",\r\n          icon: \"CreditCard\",\r\n          url: \"/dashboard/business/card\",\r\n        },\r\n        {\r\n          title: \"Products & Services\",\r\n          icon: \"Package\",\r\n          url: \"/dashboard/business/products\",\r\n        },\r\n        {\r\n          title: \"Gallery\",\r\n          icon: \"Image\",\r\n          url: \"/dashboard/business/gallery\",\r\n          badge:\r\n            userPlan === \"free\"\r\n              ? \"1 Photo\"\r\n              : userPlan === \"basic\"\r\n              ? \"3 Photos\"\r\n              : undefined,\r\n          badgeVariant: \"secondary\" as const,\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Insights\",\r\n      icon: \"BarChart3\",\r\n      items: [\r\n        {\r\n          title: \"Analytics\",\r\n          icon: \"BarChart3\",\r\n          url: \"/dashboard/business/analytics\",\r\n          badge: userPlan === \"free\" ? \"Basic+\" : undefined,\r\n          badgeVariant: \"upgrade\" as const,\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Social\",\r\n      icon: \"Users\",\r\n      items: [\r\n        {\r\n          title: \"Likes\",\r\n          icon: \"Heart\",\r\n          url: \"/dashboard/business/likes\",\r\n        },\r\n        {\r\n          title: \"Subscriptions\",\r\n          icon: \"Users\",\r\n          url: \"/dashboard/business/subscriptions\",\r\n        },\r\n        {\r\n          title: \"Reviews\",\r\n          icon: \"Star\",\r\n          url: \"/dashboard/business/reviews\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Account\",\r\n      icon: \"User\",\r\n      items: [\r\n        {\r\n          title: \"Manage Plan\",\r\n          icon: \"WalletCards\",\r\n          url: \"/dashboard/business/plan\",\r\n        },\r\n        {\r\n          title: \"Settings\",\r\n          icon: \"Settings\",\r\n          url: \"/dashboard/business/settings\",\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <Sidebar collapsible=\"icon\" {...props}>\r\n      <SidebarHeader className=\"border-b border-border/50\">\r\n        <div className=\"flex items-center px-2 py-4\">\r\n          <Link\r\n            href=\"/?view=home\"\r\n            className=\"flex flex-col group transition-all duration-200 hover:opacity-80\"\r\n          >\r\n            <span className=\"font-bold text-lg text-[var(--brand-gold)]\">\r\n              Dukan<span className=\"text-foreground\">card</span>\r\n            </span>\r\n            <span className=\"text-xs text-muted-foreground\">\r\n              Business Portal\r\n            </span>\r\n          </Link>\r\n        </div>\r\n      </SidebarHeader>\r\n      <SidebarContent className=\"px-2\">\r\n        <SidebarGroup>\r\n          <SidebarMenu className=\"space-y-1\">\r\n            {navData.map((section, index) => {\r\n              // For Feed and Overview (first two items), render as direct links\r\n              if (index === 0 || index === 1) {\r\n                return (\r\n                  <SidebarMenuItem key={section.title}>\r\n                    <SidebarMenuButton\r\n                      asChild\r\n                      tooltip={section.title}\r\n                      className=\"h-10 rounded-lg\"\r\n                    >\r\n                      <SidebarLink\r\n                        href={section.url || \"#\"}\r\n                        className=\"flex items-center gap-3\"\r\n                      >\r\n                        {section.icon &&\r\n                          iconMap[section.icon] &&\r\n                          React.createElement(iconMap[section.icon], {\r\n                            className: \"h-4 w-4 text-muted-foreground\",\r\n                          })}\r\n                        <span className=\"font-medium\">{section.title}</span>\r\n                      </SidebarLink>\r\n                    </SidebarMenuButton>\r\n                  </SidebarMenuItem>\r\n                );\r\n              }\r\n\r\n              // For other sections, render as collapsible\r\n              return (\r\n                <Collapsible\r\n                  key={section.title}\r\n                  defaultOpen={index === 2} // Open the Business Management section by default\r\n                  className=\"group/collapsible\"\r\n                >\r\n                  <SidebarMenuItem>\r\n                    <CollapsibleTrigger asChild>\r\n                      <SidebarMenuButton className=\"h-10 rounded-lg\">\r\n                        {section.icon &&\r\n                          iconMap[section.icon] &&\r\n                          React.createElement(iconMap[section.icon], {\r\n                            className: \"h-4 w-4 text-muted-foreground\",\r\n                          })}\r\n                        <span className=\"font-medium\">{section.title}</span>\r\n                        <ChevronRight className=\"ml-auto h-4 w-4 text-muted-foreground group-data-[state=open]/collapsible:rotate-90\" />\r\n                      </SidebarMenuButton>\r\n                    </CollapsibleTrigger>\r\n                    <CollapsibleContent className=\"transition-all duration-200\">\r\n                      <SidebarMenuSub className=\"ml-4 mt-1 space-y-1 border-l border-border/30 pl-4\">\r\n                        {section.items.map((item) => (\r\n                          <SidebarMenuSubItem key={item.title}>\r\n                            <SidebarMenuSubButton\r\n                              asChild\r\n                              className=\"h-9 rounded-md\"\r\n                            >\r\n                              <SidebarLink\r\n                                href={item.url || \"#\"}\r\n                                className=\"flex items-center gap-3\"\r\n                              >\r\n                                {item.icon &&\r\n                                  iconMap[item.icon] &&\r\n                                  React.createElement(iconMap[item.icon], {\r\n                                    className:\r\n                                      \"h-3.5 w-3.5 text-muted-foreground\",\r\n                                  })}\r\n                                <span className=\"text-sm font-medium\">\r\n                                  {item.title}\r\n                                </span>\r\n                                {item.badge && (\r\n                                  <Badge\r\n                                    variant={\r\n                                      item.badgeVariant === \"upgrade\"\r\n                                        ? \"default\"\r\n                                        : item.badgeVariant || \"default\"\r\n                                    }\r\n                                    className={`ml-auto text-xs ${\r\n                                      item.badgeVariant === \"upgrade\" &&\r\n                                      \"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300\"\r\n                                    }`}\r\n                                  >\r\n                                    {item.badge}\r\n                                  </Badge>\r\n                                )}\r\n                              </SidebarLink>\r\n                            </SidebarMenuSubButton>\r\n                          </SidebarMenuSubItem>\r\n                        ))}\r\n                      </SidebarMenuSub>\r\n                    </CollapsibleContent>\r\n                  </SidebarMenuItem>\r\n                </Collapsible>\r\n              );\r\n            })}\r\n          </SidebarMenu>\r\n        </SidebarGroup>\r\n      </SidebarContent>\r\n      <SidebarFooter className=\"border-t border-border/50 p-2\">\r\n        <NavBusinessUser user={userData} businessName={businessName} />\r\n      </SidebarFooter>\r\n      <SidebarRail />\r\n    </Sidebar>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAcA;AAKA;AACA;AACA;AACA,2CAA2C;AAC3C;AA5BA;;;;;;;;;;;AA6DO,SAAS,mBAAmB,EACjC,cAAc,gBAAgB,EAC9B,SAAS,WAAW,EACpB,YAAY,cAAc,EAC1B,UAAU,YAAY,EACtB,GAAG,OACqB;IACxB,0BAA0B;IAC1B,MAAM,eAAe;IACrB,MAAM,UAAU;IAChB,MAAM,aAAa;IACnB,MAAM,WAAW,cAAc,8DAA8D;IAE7F,4CAA4C;IAC5C,MAAM,WAAW;QACf,MAAM;QACN,iEAAiE;QACjE,QAAQ;IACV;IAEA,gFAAgF;IAChF,MAAM,UAAwB;QAC5B;YACE,OAAO;YACP,MAAM;YACN,KAAK;YACL,OAAO,EAAE;QACX;QACA;YACE,OAAO;YACP,MAAM;YACN,KAAK;YACL,OAAO,EAAE;QACX;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;oBACL,OACE,aAAa,SACT,YACA,aAAa,UACb,aACA;oBACN,cAAc;gBAChB;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;oBACL,OAAO,aAAa,SAAS,WAAW;oBACxC,cAAc;gBAChB;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;aACD;QACH;KACD;IAED,qBACE,6LAAC,+HAAA,CAAA,UAAO;QAAC,aAAY;QAAQ,GAAG,KAAK;;0BACnC,6LAAC,+HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAK,WAAU;;oCAA6C;kDACtD,6LAAC;wCAAK,WAAU;kDAAkB;;;;;;;;;;;;0CAEzC,6LAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;0BAMtD,6LAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,6LAAC,+HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,+HAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,QAAQ,GAAG,CAAC,CAAC,SAAS;4BACrB,kEAAkE;4BAClE,IAAI,UAAU,KAAK,UAAU,GAAG;gCAC9B,qBACE,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,oBAAiB;wCAChB,OAAO;wCACP,SAAS,QAAQ,KAAK;wCACtB,WAAU;kDAEV,cAAA,6LAAC,wIAAA,CAAA,cAAW;4CACV,MAAM,QAAQ,GAAG,IAAI;4CACrB,WAAU;;gDAET,QAAQ,IAAI,IACX,4IAAA,CAAA,UAAO,CAAC,QAAQ,IAAI,CAAC,kBACrB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,4IAAA,CAAA,UAAO,CAAC,QAAQ,IAAI,CAAC,EAAE;oDACzC,WAAW;gDACb;8DACF,6LAAC;oDAAK,WAAU;8DAAe,QAAQ,KAAK;;;;;;;;;;;;;;;;;mCAf5B,QAAQ,KAAK;;;;;4BAoBvC;4BAEA,4CAA4C;4BAC5C,qBACE,6LAAC,mIAAA,CAAA,cAAW;gCAEV,aAAa,UAAU;gCACvB,WAAU;0CAEV,cAAA,6LAAC,+HAAA,CAAA,kBAAe;;sDACd,6LAAC,mIAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,6LAAC,+HAAA,CAAA,oBAAiB;gDAAC,WAAU;;oDAC1B,QAAQ,IAAI,IACX,4IAAA,CAAA,UAAO,CAAC,QAAQ,IAAI,CAAC,kBACrB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,4IAAA,CAAA,UAAO,CAAC,QAAQ,IAAI,CAAC,EAAE;wDACzC,WAAW;oDACb;kEACF,6LAAC;wDAAK,WAAU;kEAAe,QAAQ,KAAK;;;;;;kEAC5C,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG5B,6LAAC,mIAAA,CAAA,qBAAkB;4CAAC,WAAU;sDAC5B,cAAA,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC,+HAAA,CAAA,qBAAkB;kEACjB,cAAA,6LAAC,+HAAA,CAAA,uBAAoB;4DACnB,OAAO;4DACP,WAAU;sEAEV,cAAA,6LAAC,wIAAA,CAAA,cAAW;gEACV,MAAM,KAAK,GAAG,IAAI;gEAClB,WAAU;;oEAET,KAAK,IAAI,IACR,4IAAA,CAAA,UAAO,CAAC,KAAK,IAAI,CAAC,kBAClB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,4IAAA,CAAA,UAAO,CAAC,KAAK,IAAI,CAAC,EAAE;wEACtC,WACE;oEACJ;kFACF,6LAAC;wEAAK,WAAU;kFACb,KAAK,KAAK;;;;;;oEAEZ,KAAK,KAAK,kBACT,6LAAC,6HAAA,CAAA,QAAK;wEACJ,SACE,KAAK,YAAY,KAAK,YAClB,YACA,KAAK,YAAY,IAAI;wEAE3B,WAAW,CAAC,gBAAgB,EAC1B,KAAK,YAAY,KAAK,aACtB,wEACA;kFAED,KAAK,KAAK;;;;;;;;;;;;;;;;;uDA9BI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;+BAnBtC,QAAQ,KAAK;;;;;wBA6DxB;;;;;;;;;;;;;;;;0BAIN,6LAAC,+HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,6LAAC,4IAAA,CAAA,kBAAe;oBAAC,MAAM;oBAAU,cAAc;;;;;;;;;;;0BAEjD,6LAAC,+HAAA,CAAA,cAAW;;;;;;;;;;;AAGlB;KA1OgB", "debugId": null}}, {"offset": {"line": 5448, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/context/PaymentMethodLimitationsContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode, useEffect } from \"react\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\n// Dynamically import the dialog to prevent SSR issues\r\nconst PaymentMethodLimitationsDialog = dynamic(\r\n  () => import(\"@/app/(dashboard)/dashboard/business/plan/components/PaymentMethodLimitationsDialog\").then(mod => ({ default: mod.PaymentMethodLimitationsDialog })),\r\n  {\r\n    ssr: false,\r\n    loading: () => null // Return null during loading to prevent hydration issues\r\n  }\r\n);\r\n\r\ninterface PaymentMethodLimitationsContextType {\r\n  openDialog: (_paymentMethod?: string, _onContinueAction?: () => void) => void;\r\n}\r\n\r\nconst PaymentMethodLimitationsContext = createContext<\r\n  PaymentMethodLimitationsContextType | undefined\r\n>(undefined);\r\n\r\nexport function PaymentMethodLimitationsProvider({\r\n  children,\r\n}: {\r\n  children: ReactNode;\r\n}) {\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [dialogOpen, setDialogOpen] = useState(false);\r\n  const [paymentMethod, setPaymentMethod] = useState<string>(\"UPI\");\r\n  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);\r\n\r\n  // Set client flag to prevent SSR issues\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  const openDialog = (\r\n    method: string = \"UPI\",\r\n    onContinueAction?: () => void\r\n  ) => {\r\n    setPaymentMethod(method);\r\n    setDialogOpen(true);\r\n    if (onContinueAction) {\r\n      setPendingAction(() => onContinueAction);\r\n    }\r\n  };\r\n\r\n  const handleContinue = () => {\r\n    setDialogOpen(false);\r\n    if (pendingAction) {\r\n      pendingAction();\r\n      setPendingAction(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <PaymentMethodLimitationsContext.Provider\r\n      value={{\r\n        openDialog,\r\n      }}\r\n    >\r\n      {children}\r\n      {isClient && (\r\n        <PaymentMethodLimitationsDialog\r\n          open={dialogOpen}\r\n          onOpenChange={setDialogOpen}\r\n          onContinue={handleContinue}\r\n          paymentMethod={paymentMethod}\r\n        />\r\n      )}\r\n    </PaymentMethodLimitationsContext.Provider>\r\n  );\r\n}\r\n\r\nexport function usePaymentMethodLimitations() {\r\n  const context = useContext(PaymentMethodLimitationsContext);\r\n  if (context === undefined) {\r\n    throw new Error(\r\n      \"usePaymentMethodLimitations must be used within a PaymentMethodLimitationsProvider\"\r\n    );\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;AAHA;;;AAKA,sDAAsD;AACtD,MAAM,iCAAiC,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAC3C,IAAM,gNAA8F,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,8BAA8B;QAAC,CAAC;;;;;;IAE9J,KAAK;IACL,SAAS,IAAM,KAAK,yDAAyD;;KAJ3E;AAYN,MAAM,gDAAkC,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAElD;AAEK,SAAS,iCAAiC,EAC/C,QAAQ,EAGT;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAExE,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sDAAE;YACR,YAAY;QACd;qDAAG,EAAE;IAEL,MAAM,aAAa,CACjB,SAAiB,KAAK,EACtB;QAEA,iBAAiB;QACjB,cAAc;QACd,IAAI,kBAAkB;YACpB,iBAAiB,IAAM;QACzB;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,IAAI,eAAe;YACjB;YACA,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC,gCAAgC,QAAQ;QACvC,OAAO;YACL;QACF;;YAEC;YACA,0BACC,6LAAC;gBACC,MAAM;gBACN,cAAc;gBACd,YAAY;gBACZ,eAAe;;;;;;;;;;;;AAKzB;GAnDgB;MAAA;AAqDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT;IARgB", "debugId": null}}, {"offset": {"line": 5547, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClientLayout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\"; // Removed useState\r\nimport MinimalHeader from \"@/app/components/MinimalHeader\";\r\nimport { ThemeToggle } from \"@/app/components/ThemeToggle\";\r\nimport BottomNav from \"@/app/components/BottomNav\"; // Import BottomNav\r\nimport {\r\n  SidebarProvider,\r\n  SidebarInset,\r\n  SidebarTrigger,\r\n} from \"@/components/ui/sidebar\"; // Added shadcn sidebar imports\r\nimport { BusinessAppSidebar } from \"@/components/sidebar/BusinessAppSidebar\"; // Added new sidebar import\r\nimport { cn } from \"@/lib/utils\";\r\nimport { PaymentMethodLimitationsProvider } from \"@/app/context/PaymentMethodLimitationsContext\";\r\n\r\n// Define props type\r\ninterface BusinessDashboardClientLayoutProps {\r\n  children: React.ReactNode;\r\n  businessName: string | null;\r\n  logoUrl: string | null;\r\n  memberName: string | null;\r\n  userPlan: string | null;\r\n}\r\n\r\nexport default function BusinessDashboardClientLayout({\r\n  children,\r\n  businessName,\r\n  logoUrl,\r\n  memberName,\r\n  userPlan,\r\n}: BusinessDashboardClientLayoutProps) {\r\n  // Removed old state and handlers\r\n\r\n  return (\r\n    <PaymentMethodLimitationsProvider>\r\n      <SidebarProvider>\r\n        <BusinessAppSidebar\r\n          businessName={businessName}\r\n          logoUrl={logoUrl}\r\n          memberName={memberName}\r\n          userPlan={userPlan}\r\n        />\r\n        <SidebarInset>\r\n          {/* Header is now inside SidebarInset */}\r\n          <MinimalHeader\r\n            businessName={businessName}\r\n            logoUrl={logoUrl}\r\n            userName={memberName} // Pass memberName to userName prop\r\n          >\r\n            {/* Sidebar Trigger replaces old buttons */}\r\n            <SidebarTrigger className=\"ml-auto md:ml-0\" />{\" \"}\r\n            {/* Adjust margin as needed */}\r\n            {/* Removed old Sheet and Desktop Collapse Button */}\r\n            <ThemeToggle variant=\"dashboard\" />\r\n          </MinimalHeader>\r\n\r\n          {/* Main Content Area */}\r\n          <main\r\n            className={cn(\r\n              \"flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto\",\r\n              // Bottom padding to account for bottom navigation\r\n              \"pb-20 sm:pb-18 md:pb-6\", // 20 = 80px (mobile nav + spacing), 18 = 72px (tablet nav + spacing), 6 = 24px (desktop)\r\n              \"bg-white dark:bg-black\" // Use white for light mode and black for dark mode\r\n            )}\r\n          >\r\n            {children}\r\n          </main>\r\n          <BottomNav />\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </PaymentMethodLimitationsProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA,sOAAoD,mBAAmB;AACvE,gOAIkC,+BAA+B;AACjE,gQAA8E,2BAA2B;AACzG;AACA;AAbA;;;;;;;;;AAwBe,SAAS,8BAA8B,EACpD,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,UAAU,EACV,QAAQ,EAC2B;IACnC,iCAAiC;IAEjC,qBACE,6LAAC,qJAAA,CAAA,mCAAgC;kBAC/B,cAAA,6LAAC,+HAAA,CAAA,kBAAe;;8BACd,6LAAC,+IAAA,CAAA,qBAAkB;oBACjB,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,UAAU;;;;;;8BAEZ,6LAAC,+HAAA,CAAA,eAAY;;sCAEX,6LAAC,sIAAA,CAAA,UAAa;4BACZ,cAAc;4BACd,SAAS;4BACT,UAAU;;8CAGV,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;gCAAqB;8CAG/C,6LAAC,oIAAA,CAAA,cAAW;oCAAC,SAAQ;;;;;;;;;;;;sCAIvB,6LAAC;4BACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+CACA,kDAAkD;4BAClD,0BACA,yBAAyB,mDAAmD;;sCAG7E;;;;;;sCAEH,6LAAC,kIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;AAKpB;KAhDwB", "debugId": null}}]}