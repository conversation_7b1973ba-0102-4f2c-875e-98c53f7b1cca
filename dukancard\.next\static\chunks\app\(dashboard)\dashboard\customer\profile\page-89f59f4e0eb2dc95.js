(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196,2408],{2219:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155),s=r(12115),l=r(54073),n=r(53999);function i(e){let{className:t,defaultValue:r,value:i,min:o=0,max:d=100,...c}=e,u=s.useMemo(()=>Array.isArray(i)?i:Array.isArray(r)?r:[o,d],[i,r,o,d]);return(0,a.jsxs)(l.bL,{"data-slot":"slider",defaultValue:r,value:i,min:o,max:d,className:(0,n.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",t),...c,children:[(0,a.jsx)(l.<PERSON>,{"data-slot":"slider-track",className:(0,n.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,a.jsx)(l.Q6,{"data-slot":"slider-range",className:(0,n.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:u.length},(e,t)=>(0,a.jsx)(l.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},t))]})}},2767:(e,t,r)=>{Promise.resolve().then(r.bind(r,72604))},16559:(e,t,r)=>{"use strict";r.d(t,{$v:()=>g,EO:()=>c,Lt:()=>i,Rx:()=>f,Zr:()=>h,ck:()=>m,r7:()=>x,wd:()=>u});var a=r(95155);r(12115);var s=r(62278),l=r(53999),n=r(97168);function i(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"alert-dialog",...t})}function o(e){let{...t}=e;return(0,a.jsx)(s.ZL,{"data-slot":"alert-dialog-portal",...t})}function d(e){let{className:t,...r}=e;return(0,a.jsx)(s.hJ,{"data-slot":"alert-dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(d,{}),(0,a.jsx)(s.UC,{"data-slot":"alert-dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...r})]})}function u(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...r})}function m(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...r})}function x(e){let{className:t,...r}=e;return(0,a.jsx)(s.hE,{"data-slot":"alert-dialog-title",className:(0,l.cn)("text-lg font-semibold",t),...r})}function g(e){let{className:t,...r}=e;return(0,a.jsx)(s.VY,{"data-slot":"alert-dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...r})}function f(e){let{className:t,...r}=e;return(0,a.jsx)(s.rc,{className:(0,l.cn)((0,n.r)(),t),...r})}function h(e){let{className:t,...r}=e;return(0,a.jsx)(s.ZD,{className:(0,l.cn)((0,n.r)({variant:"outline"}),t),...r})}},29058:(e,t,r)=>{"use strict";r.d(t,{t:()=>s});var a=r(34477);let s=(0,a.createServerReference)("40d32ea8edc6596cf0013772648e4fa734c9679198",a.callServer,void 0,a.findSourceMapURL,"getPincodeDetails")},30070:(e,t,r)=>{"use strict";r.d(t,{C5:()=>v,MJ:()=>h,Rr:()=>p,eI:()=>g,lR:()=>f,lV:()=>d,zB:()=>u});var a=r(95155),s=r(12115),l=r(99708),n=r(62177),i=r(53999),o=r(82714);let d=n.Op,c=s.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(n.xI,{...t})})},m=()=>{let e=s.useContext(c),t=s.useContext(x),{getFieldState:r}=(0,n.xW)(),a=(0,n.lN)({name:e.name}),l=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...l}},x=s.createContext({});function g(e){let{className:t,...r}=e,l=s.useId();return(0,a.jsx)(x.Provider,{value:{id:l},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",t),...r})})}function f(e){let{className:t,...r}=e,{error:s,formItemId:l}=m();return(0,a.jsx)(o.J,{"data-slot":"form-label","data-error":!!s,className:(0,i.cn)("data-[error=true]:text-destructive",t),htmlFor:l,...r})}function h(e){let{...t}=e,{error:r,formItemId:s,formDescriptionId:n,formMessageId:i}=m();return(0,a.jsx)(l.DX,{"data-slot":"form-control",id:s,"aria-describedby":r?"".concat(n," ").concat(i):"".concat(n),"aria-invalid":!!r,...t})}function p(e){let{className:t,...r}=e,{formDescriptionId:s}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,i.cn)("text-muted-foreground text-sm",t),...r})}function v(e){var t;let{className:r,...s}=e,{error:l,formMessageId:n}=m(),o=l?String(null!=(t=null==l?void 0:l.message)?t:""):s.children;return o?(0,a.jsx)("p",{"data-slot":"form-message",id:n,className:(0,i.cn)("text-destructive text-sm",r),...s,children:o}):null}},49026:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>i,TN:()=>d,XL:()=>o});var a=r(95155);r(12115);var s=r(74466),l=r(53999);let n=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(n({variant:r}),t),...s})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,l.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},53999:(e,t,r)=>{"use strict";r.d(t,{M0:()=>c,Yq:()=>u,cn:()=>l,gV:()=>n,gY:()=>d,kY:()=>i,vA:()=>o,vv:()=>m});var a=r(52596),s=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function n(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function i(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),r=e.substring(e.length-2),a="*".repeat(e.length-4);return"".concat(t).concat(a).concat(r)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),r=t[0],a=t[1];if(r.length<=2||a.length<=2||!a.includes("."))return"Email Hidden";let s=r.substring(0,2)+"*".repeat(r.length-2),l=a.split("."),n=l[0],i=l.slice(1).join("."),o=n.substring(0,2)+"*".repeat(n.length-2);return"".concat(s,"@").concat(o,".").concat(i)}function d(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),r=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let a=r.length-1;a>=0;a--)if(t>=r[a].value)return(e/r[a].value).toFixed(1).replace(/\.0$/,"")+r[a].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let r={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(r.hour="2-digit",r.minute="2-digit",r.hour12=!0),e.toLocaleString("en-IN",r)}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(r){return"".concat(t," ").concat(e.toFixed(2))}}},69663:(e,t,r)=>{"use strict";r.d(t,{BK:()=>i,eu:()=>n,q5:()=>o});var a=r(95155);r(12115);var s=r(54011),l=r(53999);function n(e){let{className:t,...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)(s._V,{"data-slot":"avatar-image",className:(0,l.cn)("aspect-square size-full",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)(s.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...r})}},72604:(e,t,r)=>{"use strict";r.d(t,{default:()=>Q});var a=r(95155),s=r(12115),l=r(71007),n=r(4516),i=r(51154),o=r(4229),d=r(28695),c=r(62177),u=r(90221),m=r(55594),x=r(34477);let g=(0,x.createServerReference)("60e580379af773ff2a64323c535c68faa2379cad73",x.callServer,void 0,x.findSourceMapURL,"updateCustomerProfile");var f=r(97168),h=r(89852),p=r(82714),v=r(56671),b=r(53999);let j=m.z.object({name:m.z.string().min(1,"Name cannot be empty").max(100,"Name is too long")}),y=(0,s.forwardRef)(function(e,t){var r,n;let{initialName:d,hideSubmitButton:m=!1}=e,[x,y]=(0,s.useTransition)(),[N,w]=(0,s.useState)({message:null,errors:{},success:!1}),C=(0,c.mN)({resolver:(0,u.u)(j),defaultValues:{name:d||""},mode:"onChange"});(0,s.useImperativeHandle)(t,()=>({getFormData:()=>C.getValues(),validateForm:()=>{C.trigger();let e=Object.keys(C.formState.errors).length>0,t=C.getValues();return!e&&!!(t.name&&t.name.trim().length>0)},getFormErrors:()=>C.formState.errors})),(0,s.useEffect)(()=>{console.log("Form state changed:",N),(null!==N.message||Object.keys(N.errors||{}).length>0)&&(console.log("Response received from server"),N.success?v.oR.success(N.message||"Profile updated successfully!"):N.success||N.errors&&0!==Object.keys(N.errors).length||v.oR.error(N.message))},[N]),(0,s.useEffect)(()=>{d&&C.reset({name:d})},[d,C]);let k=async e=>{console.log("Form submission started");let t=new FormData;t.append("name",e.name),y(async()=>{try{console.log("Dispatching form data to server action");let e=await g({message:null,errors:{},success:!1},t);console.log("Server action completed:",e),w(e)}catch(e){console.error("Error submitting form:",e),w({message:"An unexpected error occurred. Please try again.",success:!1,errors:{}}),v.oR.error("An unexpected error occurred. Please try again.")}})};return(0,a.jsxs)("form",{onSubmit:C.handleSubmit(k),className:"space-y-4","data-testid":"profile-form",children:[N.message&&!N.success&&0===Object.keys(N.errors||{}).length&&(0,a.jsx)("div",{className:"p-3 rounded-md bg-red-50 dark:bg-red-950/30 text-red-600 dark:text-red-400 text-sm",children:N.message}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"name",className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Full Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500"}),(0,a.jsx)(h.p,{id:"name",...C.register("name"),className:(0,b.cn)("pl-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800","focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]","transition-all duration-200",x&&"opacity-70"),placeholder:"Your full name","aria-invalid":!!C.formState.errors.name||!!(null==(r=N.errors)?void 0:r.name),"aria-describedby":"name-error",disabled:x})]}),C.formState.errors.name&&(0,a.jsx)("p",{id:"name-error",className:"text-sm font-medium text-red-500 dark:text-red-400 mt-1",children:C.formState.errors.name.message}),(null==(n=N.errors)?void 0:n.name)&&(0,a.jsx)("p",{id:"name-error-server",className:"text-sm font-medium text-red-500 dark:text-red-400 mt-1",children:N.errors.name.join(", ")})]}),!m&&(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)(f.$,{type:"submit",disabled:x||!C.formState.isValid,className:"bg-primary hover:bg-primary/90 text-primary-foreground",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})})]})});var N=r(35695);let w=(0,x.createServerReference)("60c58026dfab279127ad6b1e1e5ce60cc0117e6ee3",x.callServer,void 0,x.findSourceMapURL,"updateCustomerAddress");var C=r(81284),k=r(34869),A=r(23227),R=r(49026),S=r(95784),z=r(30070),P=r(29058);let F=m.z.object({address:m.z.string().max(100,{message:"Address cannot exceed 100 characters."}).optional().or(m.z.literal("")),pincode:m.z.string().min(1,{message:"Pincode is required"}).regex(/^\d{6}$/,{message:"Must be a valid 6-digit pincode"}),city:m.z.string().min(1,{message:"City is required"}).refine(e=>e.trim().length>0,{message:"City cannot be empty"}),state:m.z.string().min(1,{message:"State is required"}).refine(e=>e.trim().length>0,{message:"State cannot be empty"}),locality:m.z.string().min(1,{message:"Locality is required"}).refine(e=>e.trim().length>0,{message:"Locality cannot be empty"})}),L=(0,s.forwardRef)((e,t)=>{let{initialData:r,hideSubmitButton:l=!1}=e,[d,m]=(0,s.useTransition)(),[x,g]=(0,s.useState)({message:null,success:!1,errors:{}}),p=(0,N.useSearchParams)().get("message"),b=(0,c.mN)({resolver:(0,u.u)(F),defaultValues:{address:(null==r?void 0:r.address)||"",pincode:(null==r?void 0:r.pincode)||"",city:(null==r?void 0:r.city)||"",state:(null==r?void 0:r.state)||"",locality:(null==r?void 0:r.locality)||""}});(0,s.useImperativeHandle)(t,()=>({getFormData:()=>b.getValues(),validateForm:()=>(b.trigger(),0===Object.keys(b.formState.errors).length),getFormErrors:()=>b.formState.errors}));let{isPincodeLoading:j,availableLocalities:y,handlePincodeChange:L}=function(e){let{form:t,initialPincode:r,initialLocality:a}=e,[l,n]=(0,s.useState)(!1),[i,o]=(0,s.useState)([]),d=(0,s.useCallback)(async e=>{if(6!==e.length)return;n(!0),o([]),t.setValue("locality",""),t.setValue("city",""),t.setValue("state","");let r=await (0,P.t)(e);n(!1),r.error?v.oR.error(r.error):r.city&&r.state&&r.localities&&(t.setValue("city",r.city,{shouldValidate:!0}),t.setValue("state",r.state,{shouldValidate:!0}),o(r.localities),1===r.localities.length&&t.setValue("locality",r.localities[0],{shouldValidate:!0,shouldDirty:!0}),v.oR.success("City and State auto-filled. Please select your locality."))},[t]);return(0,s.useEffect)(()=>{r&&6===r.length&&(async e=>{n(!0),o([]);try{let r=await (0,P.t)(e);r.error?(v.oR.error("Failed to fetch details for pincode ".concat(e,": ").concat(r.error)),o([])):r.city&&r.state&&r.localities?(t.setValue("city",r.city,{shouldValidate:!0}),t.setValue("state",r.state,{shouldValidate:!0}),o(r.localities),a&&(r.localities.some(e=>e.toLowerCase()===a.toLowerCase())?t.setValue("locality",a,{shouldValidate:!0,shouldDirty:!1}):(t.setValue("locality","",{shouldValidate:!0,shouldDirty:!0}),v.oR.warning('The locality "'.concat(a,'" is not available for pincode ').concat(e,". Please select a valid locality."))))):(o([]),v.oR.warning("No localities found for pincode ".concat(e,".")),a&&t.setValue("locality","",{shouldValidate:!0,shouldDirty:!0}))}catch(e){console.error("Error fetching pincode details:",e),v.oR.error("An unexpected error occurred while fetching pincode details."),o([]),a&&t.setValue("locality","",{shouldValidate:!0,shouldDirty:!0})}finally{n(!1)}})(r)},[r,a,t]),{isPincodeLoading:l,availableLocalities:i,handlePincodeChange:d}}({form:b,initialPincode:null==r?void 0:r.pincode,initialLocality:null==r?void 0:r.locality});return(0,s.useEffect)(()=>{p&&v.oR.info(p)},[p]),(0,s.useEffect)(()=>{x.message&&(x.success?(v.oR.success(x.message),g({message:null,success:!1,errors:{}})):v.oR.error(x.message))},[x]),(0,a.jsxs)("div",{className:"space-y-6",children:[p&&(0,a.jsxs)(R.Fc,{className:"border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 text-amber-600 dark:text-amber-400"}),(0,a.jsx)(R.TN,{className:"text-amber-800 dark:text-amber-200",children:p})]}),(0,a.jsx)(z.lV,{...b,children:(0,a.jsxs)("form",{onSubmit:b.handleSubmit(e=>{let t=new FormData;t.append("address",e.address||""),t.append("pincode",e.pincode),t.append("city",e.city),t.append("state",e.state),t.append("locality",e.locality),m(async()=>{try{let e=await w({message:null,errors:{},success:!1},t);g(e)}catch(e){console.error("Error submitting address form:",e),g({message:"An unexpected error occurred. Please try again.",success:!1,errors:{}})}})}),className:"space-y-4",children:[(0,a.jsx)(z.zB,{control:b.control,name:"address",render:e=>{var t;let{field:r}=e;return(0,a.jsxs)(z.eI,{children:[(0,a.jsx)(z.lR,{className:"text-sm font-medium",children:"Address (Optional)"}),(0,a.jsx)(z.MJ,{children:(0,a.jsx)(h.p,{placeholder:"e.g., House/Flat No., Street Name",...r,value:null!=(t=r.value)?t:"",className:"w-full"})}),(0,a.jsx)(z.Rr,{className:"text-xs text-muted-foreground",children:"Your street address or building details"}),(0,a.jsx)(z.C5,{})]})}}),(0,a.jsx)(z.zB,{control:b.control,name:"pincode",render:e=>{var t;let{field:r}=e;return(0,a.jsxs)(z.eI,{children:[(0,a.jsxs)(z.lR,{className:"text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 text-primary"}),"Pincode *"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(z.MJ,{className:"flex-1",children:(0,a.jsx)(h.p,{placeholder:"e.g., 751001",...r,value:null!=(t=r.value)?t:"",maxLength:6,type:"number",onChange:e=>{r.onChange(e),6===e.target.value.length&&L(e.target.value)},onInput:e=>{let t=e.target;t.value=t.value.replace(/[^0-9]/g,"")}})}),j&&(0,a.jsx)(i.A,{className:"h-4 w-4 animate-spin text-primary"})]}),(0,a.jsx)(z.Rr,{className:"text-xs text-muted-foreground",children:"6-digit pincode to auto-fill city and state"}),(0,a.jsx)(z.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,a.jsx)(z.zB,{control:b.control,name:"city",render:e=>{var t;let{field:r}=e;return(0,a.jsxs)(z.eI,{children:[(0,a.jsxs)(z.lR,{className:"text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 text-primary/50"}),"City *"]}),(0,a.jsx)(z.MJ,{children:(0,a.jsx)(h.p,{placeholder:"Auto-filled from Pincode",...r,value:null!=(t=r.value)?t:"",className:"bg-muted cursor-not-allowed",readOnly:!0})}),(0,a.jsx)(z.C5,{})]})}}),(0,a.jsx)(z.zB,{control:b.control,name:"state",render:e=>{var t;let{field:r}=e;return(0,a.jsxs)(z.eI,{children:[(0,a.jsxs)(z.lR,{className:"text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 text-primary/50"}),"State *"]}),(0,a.jsx)(z.MJ,{children:(0,a.jsx)(h.p,{placeholder:"Auto-filled from Pincode",...r,value:null!=(t=r.value)?t:"",className:"bg-muted cursor-not-allowed",readOnly:!0})}),(0,a.jsx)(z.C5,{})]})}})]}),(0,a.jsx)(z.zB,{control:b.control,name:"locality",render:e=>{var t;let{field:r}=e;return(0,a.jsxs)(z.eI,{children:[(0,a.jsxs)(z.lR,{className:"text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-primary"}),"Locality / Area *"]}),(0,a.jsxs)(S.l6,{onValueChange:r.onChange,value:null!=(t=r.value)?t:"",disabled:0===y.length,children:[(0,a.jsx)(z.MJ,{children:(0,a.jsx)(S.bq,{disabled:0===y.length,className:"w-full",children:(0,a.jsx)(S.yv,{placeholder:0===y.length?"Enter Pincode first":"Select your locality"})})}),(0,a.jsx)(S.gC,{className:"w-full",children:y.map(e=>(0,a.jsx)(S.eb,{value:e,children:e},e))})]}),(0,a.jsx)(z.Rr,{className:"text-xs text-muted-foreground",children:"Select the specific area within the pincode"}),(0,a.jsx)(z.C5,{})]})}}),!l&&(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)(f.$,{type:"submit",disabled:d,className:"bg-primary hover:bg-primary/90 text-primary-foreground",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Update Address"]})})})]})})]})});L.displayName="AddressForm";var D=r(69663),B=r(84355),U=r(54416);let E=(0,x.createServerReference)("40def710293f250d0dac140f014b0c26aab495bbd7",x.callServer,void 0,x.findSourceMapURL,"uploadAvatarAndGetUrl"),I=(0,x.createServerReference)("4067b0c981cd7953a4ec579dd3c31e777ec76be460",x.callServer,void 0,x.findSourceMapURL,"updateAvatarUrl"),V=(0,x.createServerReference)("401ab8dc2d3f647115d61a12212dbd9df058de4b39",x.callServer,void 0,x.findSourceMapURL,"deleteCustomerAvatar");var M=r(90196),q=r(95733),_=r(16559);function O(e){let{isOpen:t,onClose:r,onConfirm:s,isDeleting:l=!1}=e;return(0,a.jsx)(_.Lt,{open:t,onOpenChange:r,children:(0,a.jsxs)(_.EO,{children:[(0,a.jsxs)(_.wd,{children:[(0,a.jsx)(_.r7,{children:"Remove Profile Picture"}),(0,a.jsx)(_.$v,{children:"Are you sure you want to remove your profile picture? This action cannot be undone."})]}),(0,a.jsxs)(_.ck,{children:[(0,a.jsx)(_.Zr,{disabled:l,children:"Cancel"}),(0,a.jsx)(_.Rx,{onClick:s,disabled:l,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:l?"Removing...":"Remove"})]})]})})}function J(e){let{initialAvatarUrl:t,userName:r,onUpdateAvatar:l}=e,[n,o]=(0,s.useState)(!1),{localPreviewUrl:c,isAvatarUploading:u,imageToCrop:m,onFileSelect:x,handleCropComplete:g,handleCropDialogClose:f,handleAvatarDelete:p,avatarErrorDisplay:j}=function(e){let{onUpdateAvatar:t}=e,[r,a]=(0,s.useState)("idle"),[l,n]=(0,s.useState)(null),[i,o]=(0,s.useState)(null),[d,c]=(0,s.useTransition)(),[u,m]=(0,s.useState)(null),[x,g]=(0,s.useState)(null),f=async e=>{a("uploading"),n(null),c(async()=>{let r=new FormData;r.append("avatarFile",e);let s=await E(r);if(s.success&&s.url){let e=s.url;a("success"),o(null),i&&URL.revokeObjectURL(i),v.oR.success("Avatar uploaded successfully!");try{let r=await I(e);r.success||v.oR.error("Avatar uploaded, but failed to save URL: ".concat(r.error)),r.success&&t(e)}catch(e){console.error("Error saving avatar URL:",e),v.oR.error("Error saving avatar URL after upload.")}}else{a("error");let e=s.error||"Failed to upload avatar.";n(e),o(null),i&&URL.revokeObjectURL(i),e.includes("File size must be less than 15MB")?v.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}):e.includes("Invalid file type")?v.oR.error("Invalid file type",{description:"Please select a JPG, PNG, WebP, or GIF image"}):v.oR.error("Upload failed",{description:e})}})},h=async e=>{if(m(null),e&&x)try{let t=new File([e],x.name,{type:"image/png"}),r=await (0,M.q)(t,{maxDimension:400,targetSizeKB:45}),a=new File([r.blob],x.name,{type:r.blob.type}),s=URL.createObjectURL(a);o(s),f(a)}catch(t){console.error("Image compression failed:",t),v.oR.error("Failed to process image. Please try a different image."),g(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")}else{g(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")}};return{avatarUploadStatus:r,avatarUploadError:l,localPreviewUrl:i,isAvatarUploading:d,imageToCrop:u,onFileSelect:e=>{if(i&&(URL.revokeObjectURL(i),o(null)),e){if(e.size>0xf00000){v.oR.error("File size must be less than 15MB."),a("idle"),n("File size must be less than 15MB."),o(null);return}g(e);let t=new FileReader;t.onloadend=()=>{m(t.result)},t.readAsDataURL(e)}else a("idle"),n(null),o(null)},handleAvatarUpload:f,handleCropComplete:h,handleCropDialogClose:()=>{m(null),g(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")},handleAvatarDelete:async e=>{c(async()=>{try{a("uploading"),n(null);let r=await V(e);r.success?(a("success"),o(null),t(""),v.oR.success("Avatar deleted successfully!")):(a("error"),n(r.error||"Failed to delete avatar"),v.oR.error(r.error||"Failed to delete avatar"))}catch(t){a("error");let e=t instanceof Error?t.message:"Failed to delete avatar";n(e),v.oR.error(e)}})},avatarErrorDisplay:"error"===r&&l?l:null}}({initialAvatarUrl:t,onUpdateAvatar:l}),y=(e=>{if(!e)return"U";let t=e.split(/\s+/);return 1===t.length?e.substring(0,2).toUpperCase():(t[0].charAt(0)+t[t.length-1].charAt(0)).toUpperCase()})(r),N=!!(c||t);return(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsxs)(d.P.div,{className:"relative",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300,damping:15},children:[(0,a.jsxs)(D.eu,{className:(0,b.cn)("h-32 w-32","border-4 border-primary/20","shadow-2xl","ring-4 ring-primary/10","transition-all duration-300","hover:shadow-3xl hover:ring-primary/20"),children:[c||t?(0,a.jsx)(D.BK,{src:c||t,alt:r||"User"}):null,(0,a.jsx)(D.q5,{className:(0,b.cn)("bg-gradient-to-br from-primary/10 to-primary/20 dark:from-primary/20 dark:to-primary/30","text-primary dark:text-primary text-2xl font-semibold","border border-primary/20"),children:y})]}),(0,a.jsxs)(d.P.label,{htmlFor:"avatar-upload",className:(0,b.cn)("absolute bottom-0 right-0 p-2 rounded-full","bg-primary text-primary-foreground cursor-pointer","hover:bg-primary/90 transition-colors","shadow-lg hover:shadow-xl","border-2 border-background"),whileHover:{scale:1.1},whileTap:{scale:.95},children:[(0,a.jsx)(B.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"sr-only",children:"Upload avatar"})]}),N&&(0,a.jsxs)(d.P.button,{onClick:()=>{o(!0)},className:(0,b.cn)("absolute top-0 right-0 p-2 rounded-full","bg-destructive text-destructive-foreground cursor-pointer","hover:bg-destructive/90 transition-colors","shadow-lg hover:shadow-xl","border-2 border-background"),whileHover:{scale:1.1},whileTap:{scale:.95},disabled:u,children:[(0,a.jsx)(U.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Remove avatar"})]}),(0,a.jsx)(h.p,{id:"avatar-upload",type:"file",accept:"image/png, image/jpeg, image/gif, image/webp",className:"hidden",onChange:e=>{var t;return x((null==(t=e.target.files)?void 0:t[0])||null)},disabled:u})]}),u&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-neutral-500 dark:text-neutral-400",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Uploading..."]}),j&&(0,a.jsx)("div",{className:"text-sm text-red-500",children:j}),(0,a.jsx)(q.A,{isOpen:!!m,imgSrc:m,onCropComplete:g,onClose:f}),(0,a.jsx)(O,{isOpen:n,onClose:()=>{o(!1)},onConfirm:()=>{let e=c||t;e&&p(e),o(!1)},isDeleting:u})]})}var T=r(99840),Z=r(28883),$=r(19420),W=r(85339),Y=r(40646);function H(e){let{hasCompleteAddress:t=!1}=e,[r,l]=(0,s.useState)(!1),[i,o]=(0,s.useState)([]),c=(0,N.useSearchParams)(),u=(0,N.useRouter)();(0,s.useEffect)(()=>{let e=c.get("missing"),r=c.get("message");if(e||r){let r=e?e.split(","):[],a=[];t||a.push("address"),o(a.length>0?a:r),l(!0);let s=window.location.pathname;u.replace(s,{scroll:!1})}},[c,t,u]);let m=e=>{switch(e){case"email":return{icon:(0,a.jsx)(Z.A,{className:"w-5 h-5"}),label:"Email Address",description:"Required for account notifications and password reset",color:"text-[#C29D5B]",bgColor:"bg-[#C29D5B]/10",borderColor:"border-[#C29D5B]/20"};case"phone":return{icon:(0,a.jsx)($.A,{className:"w-5 h-5"}),label:"Mobile Number",description:"Required for account access and verification",color:"text-[#C29D5B]",bgColor:"bg-[#C29D5B]/10",borderColor:"border-[#C29D5B]/20"};case"address":return{icon:(0,a.jsx)(n.A,{className:"w-5 h-5"}),label:"Address Information",description:"Required for location-based services",color:"text-[#C29D5B]",bgColor:"bg-[#C29D5B]/10",borderColor:"border-[#C29D5B]/20"};default:return{icon:(0,a.jsx)(W.A,{className:"w-5 h-5"}),label:e,description:"Required information",color:"text-[#C29D5B]",bgColor:"bg-[#C29D5B]/10",borderColor:"border-[#C29D5B]/20"}}};return 0===i.length?null:(0,a.jsx)(T.lG,{open:r,onOpenChange:l,children:(0,a.jsxs)(T.Cf,{className:"sm:max-w-lg max-w-[calc(100vw-2rem)] mx-auto p-0 gap-0 overflow-hidden border-0 shadow-2xl",children:[(0,a.jsxs)("div",{className:"relative bg-gradient-to-br from-[#C29D5B] to-[#B08A4A] px-6 py-8 text-white",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/5"}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-3 rounded-xl bg-white/20 backdrop-blur-sm",children:(0,a.jsx)(W.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(T.L3,{className:"text-xl font-bold text-white mb-1",children:"Complete Your Profile"}),(0,a.jsx)(T.rr,{className:"text-white/80 text-sm",children:"Just a few more details to get started"})]})]})})})]}),(0,a.jsxs)("div",{className:"px-6 py-6",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed",children:"Please add the following required information to unlock all dashboard features and ensure the best experience."})}),(0,a.jsx)("div",{className:"space-y-4 mb-8",children:i.map((e,t)=>{let r=m(e);return(0,a.jsxs)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*t},className:"group relative flex items-start gap-4 p-4 rounded-xl border-2 ".concat(r.borderColor," ").concat(r.bgColor," hover:shadow-md transition-all duration-200"),children:[(0,a.jsx)("div",{className:"flex-shrink-0 p-3 rounded-lg ".concat(r.color," bg-white shadow-sm"),children:r.icon}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-foreground mb-1",children:r.label}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed",children:r.description})]}),(0,a.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full border-2 border-[#C29D5B]/30 bg-white group-hover:border-[#C29D5B] transition-colors duration-200"})]},e)})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(f.$,{onClick:()=>{l(!1)},className:"w-full h-12 bg-gradient-to-r from-[#C29D5B] to-[#B08A4A] hover:from-[#B08A4A] hover:to-[#9A7A3A] text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 border-0",children:[(0,a.jsx)(Y.A,{className:"w-5 h-5 mr-2"}),"Got it, let me complete my profile"]}),(0,a.jsx)("p",{className:"text-xs text-center text-muted-foreground px-4 leading-relaxed",children:"You can update these details using the forms below. All information is securely stored and protected."})]})]})]})})}var K=r(88482);let G=(0,x.createServerReference)("60536d5d116a56d61492068181924f65f50300cc5a",x.callServer,void 0,x.findSourceMapURL,"updateCustomerProfileAndAddress");function Q(e){let{initialName:t,initialAvatarUrl:r,initialAddressData:c,hasCompleteAddress:u=!1}=e,[m,x]=(0,s.useState)(r||void 0),g=(0,s.useRef)(null),h=(0,s.useRef)(null),[p,b]=(0,s.useTransition)();console.log("ProfilePageClient - current isPending state:",p);let[j,N]=(0,s.useState)({message:null,errors:{},success:!1}),w=async()=>{var e,t,r,a,s,l,n;let i=null==(e=g.current)?void 0:e.getFormData(),o=null==(t=h.current)?void 0:t.getFormData();if(!(null!=(l=null==(r=g.current)?void 0:r.validateForm())&&l))return void v.oR.error("Please check your profile information. Name is required.");let d=null==(n=null==(a=h.current)?void 0:a.validateForm())||n;if(!(null==i||null==(s=i.name)?void 0:s.trim()))return void v.oR.error("Name is required");if(o&&(o.pincode||o.city||o.state||o.locality)&&!d)return void v.oR.error("Please complete all required address fields or leave them empty");let c=new FormData;c.append("name",i.name),o&&(c.append("address",o.address||""),c.append("pincode",o.pincode||""),c.append("city",o.city||""),c.append("state",o.state||""),c.append("locality",o.locality||"")),b(async()=>{try{let e=await G({message:null,errors:{},success:!1},c);N(e),console.log("ProfilePageClient - formState after server action:",e),e.success?v.oR.success(e.message||"Profile updated successfully!"):v.oR.error(e.message||"Failed to update profile")}catch(e){console.error("Error submitting unified form:",e),N({message:"An unexpected error occurred. Please try again.",success:!1,errors:{}}),v.oR.error("An unexpected error occurred. Please try again.")}})},C={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(H,{hasCompleteAddress:u}),(0,a.jsxs)(d.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,staggerChildren:.1}}},className:"space-y-8",children:[(0,a.jsx)(d.P.div,{variants:C,className:"space-y-6",children:(0,a.jsx)("div",{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,a.jsx)(l.A,{className:"w-5 h-5 text-primary"})}),(0,a.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Customer Profile"})]}),(0,a.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 dark:from-neutral-100 dark:to-neutral-400 bg-clip-text text-transparent",children:"Profile Management"}),(0,a.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 text-lg",children:"Manage your personal information and preferences"})]})})}),(0,a.jsxs)("div",{className:"grid gap-8 grid-cols-1 lg:grid-cols-2",children:[(0,a.jsxs)(d.P.div,{variants:C,className:"space-y-8",children:[(0,a.jsx)(K.Zp,{className:"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsx)(K.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"text-center space-y-6",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(J,{initialAvatarUrl:m,userName:t,onUpdateAvatar:e=>x(e)})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-foreground",children:t||"Customer"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Dukancard Customer"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-neutral-200/60 dark:border-neutral-800/60",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:u?"✓":"○"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Address"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:m?"✓":"○"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Avatar"})]})]})]})})}),(0,a.jsxs)(K.Zp,{className:"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)(K.aR,{className:"border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-500/5 border border-blue-500/20",children:(0,a.jsx)(l.A,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:"Personal Information"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Update your name and personal details"})]})]})}),(0,a.jsx)(K.Wu,{className:"p-8",children:(0,a.jsx)(y,{ref:g,initialName:t,hideSubmitButton:!0})})]})]}),(0,a.jsx)(d.P.div,{variants:C,className:"space-y-8",children:(0,a.jsxs)(K.Zp,{className:"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300 h-fit",children:[(0,a.jsx)(K.aR,{className:"border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/10 to-emerald-500/5 border border-emerald-500/20",children:(0,a.jsx)(n.A,{className:"w-5 h-5 text-emerald-600 dark:text-emerald-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:"Address Information"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Update your address details (optional)"})]})]})}),(0,a.jsx)(K.Wu,{className:"p-8",children:(0,a.jsx)(L,{ref:h,initialData:c||void 0,hideSubmitButton:!0})})]})})]}),(0,a.jsxs)(d.P.div,{variants:C,className:"space-y-6",children:[(0,a.jsx)(K.Zp,{className:"bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 shadow-lg",children:(0,a.jsx)(K.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row items-center justify-between gap-6",children:[(0,a.jsxs)("div",{className:"text-center lg:text-left",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"Ready to save your changes?"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Make sure all information is correct before saving"})]}),(0,a.jsx)(f.$,{onClick:w,disabled:p,size:"lg",className:"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200 min-w-[160px]",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"Save Profile"]})})]})})}),j.message&&!j.success&&(0,a.jsx)(d.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-4 bg-destructive/10 border border-destructive/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-destructive",children:j.message})})]})]})]})}},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var a=r(95155);r(12115);var s=r(40968),l=r(53999);function n(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>n,wL:()=>c});var a=r(95155);r(12115);var s=r(53999);function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var a=r(95155);r(12115);var s=r(53999);function l(e){let{className:t,type:r,...l}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},90196:(e,t,r)=>{"use strict";async function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{format:r="webp",targetSizeKB:a=100,maxDimension:s=800,quality:l=.8}=t;return new Promise((t,n)=>{let i=new Image;i.onload=()=>{try{let o=document.createElement("canvas"),d=o.getContext("2d");if(!d)return void n(Error("Could not get canvas context"));let{width:c,height:u}=i;(c>s||u>s)&&(c>u?(u=u*s/c,c=s):(c=c*s/u,u=s)),o.width=c,o.height=u,d.drawImage(i,0,0,c,u);let m=l,x=0,g=()=>{o.toBlob(r=>{if(!r)return void n(Error("Failed to create blob"));let s=r.size/1024;if(s<=a||x>=5||m<=.1){let a=e.size/r.size;t({blob:r,finalSizeKB:Math.round(100*s)/100,compressionRatio:Math.round(100*a)/100,dimensions:{width:c,height:u}})}else x++,m=Math.max(.1,m-.15),g()},"image/".concat(r),m)};g()}catch(e){n(e)}},i.onerror=()=>n(Error("Failed to load image")),i.src=URL.createObjectURL(e)})}async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.size/1048576,s=100,l=800,n=.7;return r<=2?(n=.7,l=800,s=90):r<=5?(n=.55,l=700,s=80):r<=10?(n=.45,l=600,s=70):(n=.35,l=550,s=60),a(e,{...t,targetSizeKB:t.targetSizeKB||s,maxDimension:t.maxDimension||l,quality:t.quality||n})}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return a(e,{targetSizeKB:50,maxDimension:400,quality:.7,...t})}r.d(t,{compressImageUltraAggressiveClient:()=>s,q:()=>l})},95733:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(95155),s=r(12115),l=r(3159),n=r(97168),i=r(99840),o=r(51154),d=r(2219);let c=e=>new Promise((t,r)=>{let a=new Image;a.addEventListener("load",()=>t(a)),a.addEventListener("error",e=>r(e)),a.setAttribute("crossOrigin","anonymous"),a.src=e});async function u(e,t){let r=await c(e),a=document.createElement("canvas"),s=a.getContext("2d");if(!s)return null;let l=r.naturalWidth/r.width,n=r.naturalHeight/r.height,i=window.devicePixelRatio||1;return a.width=t.width*i*l,a.height=t.height*i*n,s.setTransform(i,0,0,i,0,0),s.imageSmoothingQuality="high",s.drawImage(r,t.x*l,t.y*n,t.width*l,t.height*n,0,0,t.width*l,t.height*n),new Promise(e=>{a.toBlob(e,"image/png")})}function m(e){let{imgSrc:t,onCropComplete:r,onClose:c,isOpen:m}=e,[x,g]=(0,s.useState)({x:0,y:0}),[f,h]=(0,s.useState)(1),[p,v]=(0,s.useState)(null),[b,j]=(0,s.useState)(!1),y=(0,s.useCallback)((e,t)=>{v(t)},[]),N=async()=>{if(!t||!p){console.warn("Image source or crop area not available."),r(null);return}j(!0);try{let e=await u(t,p);r(e)}catch(e){console.error("Error cropping image:",e),r(null)}finally{j(!1)}};return(0,s.useEffect)(()=>{m&&h(1)},[m]),(0,a.jsx)(i.lG,{open:m,onOpenChange:e=>!e&&c(),children:(0,a.jsxs)(i.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsx)(i.c7,{children:(0,a.jsx)(i.L3,{children:"Crop Your Logo"})}),(0,a.jsx)("div",{className:"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800",children:t?(0,a.jsx)(l.Ay,{image:t,crop:x,zoom:f,aspect:1,cropShape:"round",showGrid:!1,onCropChange:g,onZoomChange:h,onCropComplete:y}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)("p",{children:"Loading image..."})})}),(0,a.jsx)("div",{className:"px-4 pb-4",children:(0,a.jsx)(d.A,{min:1,max:3,step:.1,value:[f],onValueChange:e=>h(e[0]),className:"w-full","aria-label":"Zoom slider"})}),(0,a.jsxs)(i.Es,{children:[(0,a.jsx)(n.$,{variant:"outline",onClick:c,disabled:b,children:"Cancel"}),(0,a.jsxs)(n.$,{onClick:N,disabled:b,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[b?(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Crop Image"]})]})]})})}},95784:(e,t,r)=>{"use strict";r.d(t,{TR:()=>g,bq:()=>m,eb:()=>f,gC:()=>x,l6:()=>d,s3:()=>c,yv:()=>u});var a=r(95155);r(12115);var s=r(43433),l=r(66474),n=r(5196),i=r(47863),o=r(53999);function d(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,a.jsx)(s.YJ,{"data-slot":"select-group",...t})}function u(e){let{...t}=e;return(0,a.jsx)(s.WT,{"data-slot":"select-value",...t})}function m(e){let{className:t,size:r="default",children:n,...i}=e;return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[n,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:r,position:l="popper",...n}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...n,children:[(0,a.jsx)(h,{}),(0,a.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,a.jsx)(p,{})]})})}function g(e){let{className:t,...r}=e;return(0,a.jsx)(s.JU,{"data-slot":"select-label",className:(0,o.cn)("text-muted-foreground px-2 py-1.5 text-xs",t),...r})}function f(e){let{className:t,children:r,...l}=e;return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:r})]})}function h(e){let{className:t,...r}=e;return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"size-4"})})}function p(e){let{className:t,...r}=e;return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(l.A,{className:"size-4"})})}},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>i});var a=r(95155);r(12115);var s=r(99708),l=r(74466),n=r(53999);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:l,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:r,size:l,className:t})),...d})}},99840:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>m,Es:()=>g,HM:()=>c,L3:()=>f,c7:()=>x,lG:()=>i,rr:()=>h,zM:()=>o});var a=r(95155);r(12115);var s=r(45821),l=r(54416),n=r(53999);function i(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,a.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,a.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{...t}=e;return(0,a.jsx)(s.bm,{"data-slot":"dialog-close",...t})}function u(e){let{className:t,...r}=e;return(0,a.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...r})}function m(e){let{className:t,children:r,hideClose:i=!1,...o}=e;return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(u,{}),(0,a.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[r,!i&&(0,a.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,a.jsx)(l.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...r})}function g(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...r})}function f(e){let{className:t,...r}=e;return(0,a.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...r})}function h(e){let{className:t,...r}=e;return(0,a.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,2290,6671,375,5152,7665,1884,6199,4577,221,864,9731,8441,1684,7358],()=>t(2767)),_N_E=e.O()}]);