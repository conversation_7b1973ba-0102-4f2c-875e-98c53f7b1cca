(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8385],{5289:(e,a,r)=>{Promise.resolve().then(r.bind(r,9743))},9743:(e,a,r)=>{"use strict";r.d(a,{default:()=>m});var t=r(95155),i=r(12115),s=r(35695),n=r(28695),d=r(56671),l=r(37108),c=r(35169),o=r(97168),p=r(8174),u=r(23451);function m(e){let{product:a,variants:r,planLimit:m,currentAvailableCount:g}=e,v=(0,s.useRouter)(),[f,x]=(0,i.useTransition)(),h=(null==r?void 0:r.map(e=>e.id))||[],_={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.4}}},b=async(e,r,t,i)=>{x(async()=>{let s=new FormData;Object.entries(e).forEach(e=>{let[a,r]=e;null!=r&&("boolean"==typeof r?s.append(a,r.toString()):s.append(a,String(r)))}),void 0!==t&&s.append("featuredImageIndex",String(t)),i&&i.length>0&&s.append("removedImageIndices",JSON.stringify(i)),r&&r.length>0&&r.forEach((e,a)=>{e&&s.append("productImage_".concat(a),e)});try{let r=await (0,u.G3)(a.id,s);if(r.success&&r.data){if(e.variants){d.oR.success("Product updated! Processing variants...");let r=0,t=0,i=e.variants.map(e=>e.id);for(let e of h.filter(e=>!i.includes(e)&&!e.startsWith("temp-")))try{let a=await (0,u.eF)(e);a.success?r++:(t++,console.error("Failed to delete variant:",e,a.error))}catch(a){t++,console.error("Error deleting variant:",e,a)}for(let i of e.variants)try{var n;let e=new FormData;if(e.append("product_id",a.id),e.append("variant_name",i.variant_name),e.append("variant_values",JSON.stringify(i.variant_values)),void 0!==i.base_price&&null!==i.base_price&&i.base_price>0&&e.append("base_price",i.base_price.toString()),void 0!==i.discounted_price&&null!==i.discounted_price&&i.discounted_price>0&&e.append("discounted_price",i.discounted_price.toString()),e.append("is_available",i.is_available?"true":"false"),e.append("featured_image_index",(null!=(n=i.featured_image_index)?n:0).toString()),i._removedImageIndices&&i._removedImageIndices.length>0&&e.append("remove_images",JSON.stringify(i._removedImageIndices)),i._imageFiles&&i._imageFiles.length>0&&i._imageFiles.forEach((a,r)=>{a&&e.append("images[".concat(r,"]"),a)}),i.id.startsWith("temp-")){let a=await (0,u.yQ)(e);a.success?r++:(t++,console.error("Failed to create variant:",i.variant_name,a.error))}else{let a=await (0,u.Uz)(i.id,e);a.success?r++:(t++,console.error("Failed to update variant:",i.variant_name,a.error))}}catch(e){t++,console.error("Error processing variant:",i.variant_name,e)}r>0&&0===t?d.oR.success("Product and all variants updated successfully!"):r>0?d.oR.warning("Product updated! ".concat(r," variant").concat(r>1?"s":""," processed successfully, ").concat(t," failed.")):t>0&&d.oR.warning("Product updated, but all ".concat(t," variant").concat(t>1?"s":""," failed to process."))}else d.oR.success("Product updated successfully!");v.push("/dashboard/business/products")}else{let e=r.error||"Failed to update product";e.includes("Image exceeds 15MB limit")?d.oR.error("Image too large",{description:"Please select images smaller than 15MB each"}):e.includes("Invalid file type")?d.oR.error("Invalid file type",{description:"Please select JPG, PNG, WebP, or GIF images"}):e.includes("Body exceeded")?d.oR.error("Upload size limit exceeded",{description:"Please try uploading fewer images or smaller file sizes"}):d.oR.error("Failed to update product",{description:e})}}catch(e){console.error("Error updating product:",e),d.oR.error("An unexpected error occurred")}})};return(0,t.jsxs)(n.P.div,{className:"space-y-8",variants:_,initial:"hidden",animate:"visible",children:[(0,t.jsxs)(n.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",variants:{hidden:{opacity:0,y:-20},visible:{opacity:1,y:0,transition:{delay:.1,duration:.4,type:"spring",stiffness:200,damping:20}}},children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,t.jsx)(l.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,t.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Product Management"})]}),(0,t.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Edit Product"}),(0,t.jsxs)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:['Update details for "',a.name,'" with advanced features and inventory management.']})]}),(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsxs)(o.$,{variant:"outline",onClick:()=>v.push("/dashboard/business/products"),className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),"Back to Products"]})})]}),(0,t.jsx)(n.P.div,{variants:_,children:(0,t.jsx)(p.A,{initialData:a,initialVariants:r,onSubmit:b,isSubmitting:f,isEditing:!0,planLimit:m,currentAvailableCount:g})})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8695,2290,6671,375,5152,7665,1884,6215,6766,6199,4577,221,4081,864,346,9635,795,933,1845,8441,1684,7358],()=>a(5289)),_N_E=e.O()}]);