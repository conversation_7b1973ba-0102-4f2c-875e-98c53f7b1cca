"use strict";exports.id=9423,exports.ids=[9423],exports.modules={30255:(e,s,t)=>{t.d(s,{DE:()=>i,VC:()=>a,clearRazorpayColumnsAfterCancellation:()=>o,lJ:()=>n});var r=t(32032);async function n(e){let s=await (0,r.createClient)();try{let{data:r,error:n}=await s.from("payment_subscriptions").select("business_profile_id, subscription_status, plan_id").eq("razorpay_subscription_id",e).single();if(n||!r)return{consistent:!1,details:`Subscription not found: ${n?.message||"Unknown error"}`};let{data:a,error:i}=await s.from("business_profiles").select("has_active_subscription").eq("id",r.business_profile_id).single();if(i||!a)return{consistent:!1,details:`Business profile not found: ${i?.message||"Unknown error"}`};let{SubscriptionStateManager:o}=await t.e(33).then(t.bind(t,10033)),c=o.shouldHaveActiveSubscription(r.subscription_status,r.plan_id||"free"),l=a.has_active_subscription===c;return{consistent:l,details:l?"Subscription and business profile are consistent":`Inconsistency detected: subscription_status=${r.subscription_status}, has_active_subscription=${a.has_active_subscription}`}}catch(e){return{consistent:!1,details:`Error checking consistency: ${e instanceof Error?e.message:String(e)}`}}}async function a(e,s,t){let n=await (0,r.createClient)(),a=new Date().toISOString();try{let{data:r,error:i}=await n.rpc("update_subscription_atomic",{p_subscription_id:e,p_new_status:"active",p_business_profile_id:s,p_has_active_subscription:!1,p_additional_data:{plan_id:"free",plan_cycle:"monthly",subscription_start_date:a,cancelled_at:a,razorpay_subscription_id:null,razorpay_customer_id:null,razorpay_plan_id:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,subscription_expiry_time:null,subscription_charge_time:null,cancellation_requested_at:null,cancellation_reason:null,subscription_paused_at:null,original_plan_id:null,original_plan_cycle:null},p_webhook_timestamp:void 0});if(i||!r?.success)return console.error(`[ATOMIC_DOWNGRADE_ERROR] Atomic RPC failed for subscription ${e}. Reason: ${t}. Error:`,i||r?.error),{success:!1,message:`Atomic downgrade failed: ${i?.message||r?.error}`};return{success:!0,message:`Subscription downgraded to free plan due to ${t}`}}catch(s){return console.error(`[ATOMIC_DOWNGRADE] Exception during downgrade for subscription ${e}:`,s),{success:!1,message:`Exception during downgrade: ${s instanceof Error?s.message:String(s)}`}}}async function i(e,s){let t=await (0,r.createClient)();try{let{data:r,error:n}=await t.from("payment_subscriptions").select("plan_id, plan_cycle").eq("razorpay_subscription_id",e).maybeSingle();if(n)return console.error(`[REVERT_TO_TRIAL] Error fetching current subscription ${e}:`,n),{success:!1,message:`Error fetching current subscription: ${n.message}`};if(!r)return console.error(`[REVERT_TO_TRIAL] Subscription ${e} not found in database`),{success:!1,message:"Subscription not found in database"};let{data:a,error:i}=await t.rpc("update_subscription_atomic",{p_subscription_id:e,p_new_status:"trial",p_business_profile_id:s,p_has_active_subscription:!1,p_additional_data:{plan_id:r.plan_id,plan_cycle:"monthly",last_payment_id:null,last_payment_date:null,last_payment_method:null,subscription_start_date:null,subscription_expiry_time:null,subscription_charge_time:null,cancelled_at:null,cancellation_requested_at:null,cancellation_reason:null,subscription_paused_at:null,original_plan_id:null,original_plan_cycle:null},p_webhook_timestamp:void 0});if(i||!a?.success)return console.error(`[REVERT_TO_TRIAL] Atomic RPC failed for subscription ${e}:`,i||a?.error),{success:!1,message:`Atomic revert to trial failed: ${i?.message||a?.error}`};return{success:!0,message:`Subscription reverted to trial status with plan ${r.plan_id} and monthly cycle enforced - Razorpay IDs preserved until webhook confirmation`}}catch(s){return console.error(`[REVERT_TO_TRIAL] Exception during revert to trial for subscription ${e}:`,s),{success:!1,message:`Exception during revert to trial: ${s instanceof Error?s.message:String(s)}`}}}async function o(e,s){let t=await (0,r.createClient)(),n=new Date().toISOString();try{let{error:r}=await t.from("payment_subscriptions").update({razorpay_subscription_id:null,razorpay_customer_id:null,subscription_start_date:null,subscription_expiry_time:null,subscription_charge_time:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,cancellation_requested_at:null,cancellation_reason:null,subscription_paused_at:null,cancelled_at:null,last_webhook_timestamp:null,original_plan_id:null,original_plan_cycle:null,updated_at:n}).eq("business_profile_id",s).eq("razorpay_subscription_id",e);if(r)return console.error(`[CLEAR_RAZORPAY_COLUMNS] Error clearing Razorpay columns for subscription ${e}:`,r),{success:!1,message:`Error clearing Razorpay columns: ${r.message}`};return{success:!0,message:"Razorpay IDs and other columns cleared after webhook confirmation"}}catch(s){return console.error(`[CLEAR_RAZORPAY_COLUMNS] Exception during column clearing for subscription ${e}:`,s),{success:!1,message:`Exception during column clearing: ${s instanceof Error?s.message:String(s)}`}}}},69423:(e,s,t)=>{t.a(e,async(e,r)=>{try{t.d(s,{handleSubscriptionCancelled:()=>l});var n=t(32032),a=t(65193),i=t(28485),o=t(30255),c=e([i]);async function l(e,s,r){let c=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let l=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription cancelled: ${l}`);let u=(0,a.extractWebhookTimestamp)(e);c={subscriptionId:l,eventType:"subscription.cancelled",eventId:r||`cancelled_${l}_${Date.now()}`,payload:e,webhookTimestamp:u};let p=await i.webhookProcessor.processWebhookEvent(c);if(!p.shouldProcess)return{success:p.success,message:p.message};let _=await (0,n.createClient)(),{data:d,error:b}=await _.from("payment_subscriptions").select("subscription_status, plan_id, business_profile_id").eq("razorpay_subscription_id",l).maybeSingle();if(b)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${l}:`,b),{success:!1,message:`Error fetching subscription: ${b.message}`};if(!d)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${l}, skipping cancellation processing`),{success:!0,message:"No subscription found to cancel"};if("authenticated"===d.subscription_status){console.log(`[RAZORPAY_WEBHOOK] Reverting authenticated subscription ${l} to trial (Plan A cancellation)`);let e=await (0,o.DE)(l,d.business_profile_id);if(!e.success)return await i.webhookProcessor.markEventAsFailed(c.eventId,e.message),e;{console.log(`[RAZORPAY_WEBHOOK] Clearing Razorpay IDs for cancelled authenticated subscription ${l}`);let{clearRazorpayColumnsAfterCancellation:e}=await Promise.resolve().then(t.bind(t,30255)),s=await e(l,d.business_profile_id);return s.success?console.log(`[RAZORPAY_WEBHOOK] Successfully cleared Razorpay IDs for subscription ${l}`):console.error(`[RAZORPAY_WEBHOOK] Failed to clear Razorpay IDs: ${s.message}`),await i.webhookProcessor.markEventAsSuccess(c.eventId,"Authenticated subscription reverted to trial and Razorpay IDs cleared"),{success:!0,message:"Authenticated subscription reverted to trial and Razorpay IDs cleared"}}}if("trial"===d.subscription_status&&"free"!==d.plan_id)return console.log(`[RAZORPAY_WEBHOOK] Subscription ${l} already in trial status (Plan A cancellation already processed)`),await i.webhookProcessor.markEventAsSuccess(c.eventId,"Authenticated subscription already reverted to trial (idempotent)"),{success:!0,message:"Authenticated subscription already reverted to trial (idempotent)"};{console.log(`[RAZORPAY_WEBHOOK] Downgrading cancelled subscription ${l} to free plan`);let e=await (0,o.VC)(l,d.business_profile_id,"cancelled");if(e.success)return await i.webhookProcessor.markEventAsSuccess(c.eventId,"Subscription cancelled and downgraded to free plan"),{success:!0,message:"Subscription cancelled and downgraded to free plan"};return await i.webhookProcessor.markEventAsFailed(c.eventId,e.message),e}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription cancelled:",e),{success:!1,message:`Error handling subscription cancelled: ${e instanceof Error?e.message:String(e)}`}}}i=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})}};