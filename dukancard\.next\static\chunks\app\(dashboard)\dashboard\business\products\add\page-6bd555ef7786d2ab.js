(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[549],{23141:(e,a,t)=>{Promise.resolve().then(t.bind(t,79960))},79960:(e,a,t)=>{"use strict";t.d(a,{default:()=>x});var r=t(95155),i=t(12115),s=t(35695),n=t(28695),d=t(56671),l=t(86151),c=t(35169),o=t(97168),u=t(8174),p=t(23451),m=t(99249);function x(e){let{planLimit:a,currentCount:t,currentAvailableCount:x}=e,g=(0,s.useRouter)(),[h,v]=(0,i.useTransition)(),f={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.4}}},b=async(e,a,t,r)=>{v(async()=>{let r=new FormData;Object.entries(e).forEach(e=>{let[a,t]=e;null!=t&&("boolean"==typeof t?r.append(a,t.toString()):r.append(a,String(t)))}),void 0!==t&&r.append("featuredImageIndex",String(t)),a&&a.length>0&&a.forEach((e,a)=>{e&&r.append("productImage_".concat(a),e)});try{let a=await (0,p.ki)(r);if(a.success&&a.data){if(e.variants&&e.variants.length>0){d.oR.success("Product created! Adding variants...");let t=0,r=0;for(let s of e.variants)try{var i;let e=new FormData;e.append("product_id",a.data.id),e.append("variant_name",s.variant_name),e.append("variant_values",JSON.stringify(s.variant_values)),void 0!==s.base_price&&null!==s.base_price&&s.base_price>0&&e.append("base_price",s.base_price.toString()),void 0!==s.discounted_price&&null!==s.discounted_price&&s.discounted_price>0&&e.append("discounted_price",s.discounted_price.toString()),e.append("is_available",s.is_available?"true":"false"),e.append("featured_image_index",(null!=(i=s.featured_image_index)?i:0).toString()),s._imageFiles&&s._imageFiles.length>0&&s._imageFiles.forEach((a,t)=>{a&&e.append("images[".concat(t,"]"),a)});let n=await (0,m.y)(e);n.success?t++:(r++,console.error("Failed to create variant:",s.variant_name,n.error))}catch(e){r++,console.error("Error creating variant:",s.variant_name,e)}t>0&&d.oR.success("Product and ".concat(t," variant").concat(t>1?"s":""," created successfully!")),r>0&&d.oR.warning("Product created, but ".concat(r," variant").concat(r>1?"s":""," failed to create. You can add them later by editing the product."))}else d.oR.success("Product added successfully!");g.push("/dashboard/business/products")}else{let e=a.error||"Failed to add product";e.includes("Image exceeds 15MB limit")?d.oR.error("Image too large",{description:"Please select images smaller than 15MB each"}):e.includes("Invalid file type")?d.oR.error("Invalid file type",{description:"Please select JPG, PNG, WebP, or GIF images"}):e.includes("Body exceeded")?d.oR.error("Upload size limit exceeded",{description:"Please try uploading fewer images or smaller file sizes"}):d.oR.error("Failed to add product",{description:e})}}catch(e){console.error("Error adding product:",e),d.oR.error("An unexpected error occurred")}})};return(0,r.jsxs)(n.P.div,{className:"space-y-8",variants:f,initial:"hidden",animate:"visible",children:[(0,r.jsxs)(n.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",variants:{hidden:{opacity:0,y:-20},visible:{opacity:1,y:0,transition:{delay:.1,duration:.4,type:"spring",stiffness:200,damping:20}}},children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,r.jsx)(l.A,{className:"w-5 h-5 text-primary"})}),(0,r.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,r.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Product Management"})]}),(0,r.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Add New Product"}),(0,r.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Create a new product or service to showcase in your business profile with advanced features and inventory management."})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(o.$,{variant:"outline",onClick:()=>g.push("/dashboard/business/products"),className:"flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),"Back to Products"]}),(0,r.jsx)("div",{className:"flex flex-col gap-2",children:(0,r.jsx)("div",{className:"flex items-center justify-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm font-medium",children:[(0,r.jsxs)("span",{className:"text-neutral-500 dark:text-neutral-400",children:["Available:"," "]}),(0,r.jsx)("span",{className:"text-neutral-800 dark:text-neutral-200",children:x}),(0,r.jsxs)("span",{className:"text-neutral-500 dark:text-neutral-400",children:[" ","of"," "]}),(0,r.jsx)("span",{className:"".concat(x>=a?"text-red-500 dark:text-red-400 font-bold":"text-neutral-800 dark:text-neutral-200"),children:a===1/0?"Unlimited":a})]})})})]})]}),(0,r.jsx)(n.P.div,{variants:f,children:(0,r.jsx)(u.A,{onSubmit:b,isSubmitting:h,isEditing:!1,planLimit:a,currentAvailableCount:x})})]})}},86151:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8695,2290,6671,375,5152,7665,1884,6215,6766,6199,4577,221,4081,864,346,9635,795,933,1845,8441,1684,7358],()=>a(23141)),_N_E=e.O()}]);