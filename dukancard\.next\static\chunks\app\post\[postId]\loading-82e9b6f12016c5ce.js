(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1958],{27737:(e,s,a)=>{"use strict";a.d(s,{E:()=>n});var l=a(95155),t=a(53999);function n(e){let{className:s,...a}=e;return(0,l.jsx)("div",{"data-slot":"skeleton",className:(0,t.cn)("bg-accent animate-pulse rounded-md",s),...a})}},53999:(e,s,a)=>{"use strict";a.d(s,{M0:()=>u,Yq:()=>o,cn:()=>n,gV:()=>r,gY:()=>d,kY:()=>i,vA:()=>c,vv:()=>m});var l=a(52596),t=a(39688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.QP)((0,l.$)(s))}function r(e){if(!e)return null;let s=e.trim();return(s.startsWith("+91")?s=s.substring(3):12===s.length&&s.startsWith("91")&&(s=s.substring(2)),/^\d{10}$/.test(s))?s:null}function i(e){if(!e||e.length<4)return"Invalid Phone";let s=e.substring(0,2),a=e.substring(e.length-2),l="*".repeat(e.length-4);return"".concat(s).concat(l).concat(a)}function c(e){if(!e||!e.includes("@"))return"Invalid Email";let s=e.split("@"),a=s[0],l=s[1];if(a.length<=2||l.length<=2||!l.includes("."))return"Email Hidden";let t=a.substring(0,2)+"*".repeat(a.length-2),n=l.split("."),r=n[0],i=n.slice(1).join("."),c=r.substring(0,2)+"*".repeat(r.length-2);return"".concat(t,"@").concat(c,".").concat(i)}function d(e){if(null==e||isNaN(e))return"0";let s=Math.abs(e),a=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(s<1e5)return s>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let l=a.length-1;l>=0;l--)if(s>=a[l].value)return(e/a[l].value).toFixed(1).replace(/\.0$/,"")+a[l].symbol;return e.toString()}function u(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function o(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let a={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return s&&(a.hour="2-digit",a.minute="2-digit",a.hour12=!0),e.toLocaleString("en-IN",a)}function m(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:s,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(a){return"".concat(s," ").concat(e.toFixed(2))}}},58685:(e,s,a)=>{"use strict";a.d(s,{default:()=>i});var l=a(95155),t=a(28695),n=a(27737),r=a(53999);function i(e){let{index:s=0,showImage:a=!0,showProducts:i=!1}=e;return(0,l.jsxs)(t.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4,delay:.1*s,ease:"easeOut"}}},initial:"hidden",animate:"visible",className:(0,r.cn)("bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800","shadow-sm overflow-hidden mb-4 md:mb-6"),children:[(0,l.jsx)("div",{className:"p-4 pb-3",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3 flex-1",children:[(0,l.jsx)(n.E,{className:"h-12 w-12 rounded-full"}),(0,l.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,l.jsx)(n.E,{className:"h-4 w-32"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(n.E,{className:"h-3 w-24"}),(0,l.jsx)(n.E,{className:"h-3 w-1 rounded-full"}),(0,l.jsx)(n.E,{className:"h-3 w-16"})]})]})]}),(0,l.jsx)(n.E,{className:"h-8 w-8 rounded-full"})]})}),(0,l.jsxs)("div",{className:"px-4 pb-3 space-y-2",children:[(0,l.jsx)(n.E,{className:"h-4 w-full"}),(0,l.jsx)(n.E,{className:"h-4 w-3/4"}),(0,l.jsx)(n.E,{className:"h-4 w-1/2"})]}),a&&(0,l.jsx)("div",{className:"relative w-full",children:(0,l.jsx)(n.E,{className:"w-full aspect-[4/3]"})}),i&&(0,l.jsxs)("div",{className:"px-4 pt-4 space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(n.E,{className:"h-6 w-6 rounded-lg"}),(0,l.jsx)(n.E,{className:"h-4 w-32"})]}),(0,l.jsx)("div",{className:"grid grid-cols-2 gap-3",children:Array.from({length:2}).map((e,s)=>(0,l.jsxs)("div",{className:"bg-neutral-50 dark:bg-neutral-900 rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden",children:[(0,l.jsx)(n.E,{className:"w-full aspect-square"}),(0,l.jsxs)("div",{className:"p-3 space-y-2",children:[(0,l.jsx)(n.E,{className:"h-4 w-full"}),(0,l.jsx)(n.E,{className:"h-4 w-16"})]})]},s))})]}),(0,l.jsx)("div",{className:"p-4 pt-3",children:(0,l.jsxs)("div",{className:"flex gap-3",children:[(0,l.jsx)(n.E,{className:"h-10 flex-1"}),(0,l.jsx)(n.E,{className:"h-10 flex-1"})]})})]})}},99231:(e,s,a)=>{Promise.resolve().then(a.bind(a,58685))}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,8695,8441,1684,7358],()=>s(99231)),_N_E=e.O()}]);