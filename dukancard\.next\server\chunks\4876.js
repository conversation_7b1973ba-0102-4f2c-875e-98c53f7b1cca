"use strict";exports.id=4876,exports.ids=[4876],exports.modules={0:(e,t,r)=>{r.d(t,{LikeListSkeleton:()=>o,default:()=>i});var s=r(60687),a=r(77882),n=r(71463),l=r(96241);function i({index:e=0,variant:t="default"}){return(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.05*e},className:(0,l.cn)("rounded-lg border p-0 overflow-hidden transition-all duration-300","bg-white dark:bg-black border-neutral-200 dark:border-neutral-800","shadow-sm","compact"===t&&"max-w-sm"),children:[(0,s.jsx)("div",{className:"absolute inset-0 pointer-events-none opacity-5 dark:opacity-10",style:{backgroundImage:'url("/decorative/card-texture.svg")',backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}}),(0,s.jsxs)("div",{className:"relative z-10 p-4",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(n.E,{className:"h-12 w-12 rounded-full"}),(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsx)("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)(n.E,{className:"h-5 w-32 mb-2"}),(0,s.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,s.jsx)(n.E,{className:"h-3 w-3 rounded-full"}),(0,s.jsx)(n.E,{className:"h-3 w-16"})]}),(0,s.jsx)(n.E,{className:"h-3 w-24 mt-1"})]})})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,s.jsx)(n.E,{className:"h-8 w-20"}),(0,s.jsx)(n.E,{className:"h-8 w-16"})]})]})]})}function o({variant:e="default",count:t=6}){return(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:t}).map((t,r)=>(0,s.jsx)(i,{index:r,variant:e},r))})}},980:(e,t,r)=>{r.d(t,{Ku:()=>s.default,_n:()=>n.default,bJ:()=>a.LikeListSkeleton,o8:()=>i.default,ur:()=>l.default});var s=r(92913),a=r(0),n=r(3487),l=r(27531),i=r(78723)},1958:(e,t,r)=>{r.d(t,{J:()=>a});var s=r(6475);let a=(0,s.createServerReference)("4034994114ec4af82b8beb7b7ae0ca9d7cc384e91d",s.callServer,void 0,s.findSourceMapURL,"unlikeBusiness")},3487:(e,t,r)=>{r.d(t,{default:()=>c});var s=r(60687),a=r(43210),n=r(99270),l=r(11860),i=r(68988),o=r(24934),d=r(96241);function c({onSearch:e,initialSearchTerm:t="",className:r,placeholder:c="Search by name..."}){let[u,m]=(0,a.useState)(t);return(0,s.jsx)("form",{onSubmit:t=>{t.preventDefault(),e(u.trim())},className:(0,d.cn)("relative w-full",r),children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-neutral-500 dark:text-neutral-400"}),(0,s.jsx)(i.p,{type:"text",placeholder:c,value:u,onChange:e=>{m(e.target.value)},onKeyDown:t=>{"Enter"===t.key&&(t.preventDefault(),e(u.trim()))},className:"pl-10 pr-10 h-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 focus:ring-2 focus:ring-rose-500 dark:focus:ring-rose-600"}),u&&(0,s.jsx)(o.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>{m(""),e("")},className:"absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})]})})}},27531:(e,t,r)=>{r.d(t,{default:()=>o});var s=r(60687),a=r(24934),n=r(47033),l=r(14952),i=r(96241);function o({currentPage:e,totalPages:t,onPageChange:r,className:o}){if(t<=1)return null;let d=(()=>{let r=[];if(t<=5)for(let e=1;e<=t;e++)r.push(e);else{let s=Math.max(1,e-2),a=Math.min(t,e+2);for(let e=s;e<=a;e++)r.push(e);s>1&&(s>2&&r.unshift("..."),r.unshift(1)),a<t&&(a<t-1&&r.push("..."),r.push(t))}return r})();return(0,s.jsxs)("div",{className:(0,i.cn)("flex items-center justify-center gap-2",o),children:[(0,s.jsx)(a.$,{variant:"outline",size:"sm",onClick:()=>r(e-1),disabled:e<=1,className:"h-8 w-8 p-0",children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}),d.map((t,n)=>(0,s.jsx)("div",{children:"..."===t?(0,s.jsx)("span",{className:"px-2 text-neutral-500 dark:text-neutral-400",children:"..."}):(0,s.jsx)(a.$,{variant:e===t?"default":"outline",size:"sm",onClick:()=>r(t),className:"h-8 w-8 p-0",children:t})},n)),(0,s.jsx)(a.$,{variant:"outline",size:"sm",onClick:()=>r(e+1),disabled:e>=t,className:"h-8 w-8 p-0",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})]})}},33755:(e,t,r)=>{r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\likes\\\\LikeCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx","default")},44533:(e,t,r)=>{r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\likes\\\\LikeSearch.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx","default")},78723:(e,t,r)=>{r.d(t,{default:()=>u});var s=r(60687),a=r(43210),n=r(85814),l=r.n(n),i=r(24934),o=r(67760),d=r(56748),c=r(92913);function u({initialLikes:e,onUnlikeSuccess:t,showUnlike:r=!0,variant:n="default",emptyMessage:u="No likes found.",emptyDescription:m="Like profiles to see them here.",showDiscoverButton:p=!1,showVisitButton:x=!0,showAddress:h=!0,showRedirectIcon:f=!1}){let[b,k]=(0,a.useState)(e),v=e=>{k(t=>t.filter(t=>t.id!==e)),t&&t(e)};return 0===b.length?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-20 text-center",children:[(0,s.jsxs)("div",{className:"relative mb-8",children:[(0,s.jsx)("div",{className:"absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"}),(0,s.jsx)("div",{className:"relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg",children:(0,s.jsx)(o.A,{className:"w-10 h-10 text-primary"})})]}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3",children:u}),(0,s.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2",children:m}),(0,s.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-500 max-w-md leading-relaxed mb-8",children:"Discover amazing businesses and show your support by liking them."}),p&&(0,s.jsx)(i.$,{asChild:!0,variant:"outline",className:"gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",children:(0,s.jsxs)(l(),{href:"/businesses",target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),"Discover Businesses"]})})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map((e,t)=>{let a=e.profile;return a?(0,s.jsx)(c.default,{likeId:e.id,profile:a,onUnlikeSuccess:r?v:void 0,showUnlike:r,variant:n,showVisitButton:x,showAddress:h,showRedirectIcon:f},e.id):null})})}},81469:(e,t,r)=>{r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\likes\\\\LikePagination.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx","default")},85838:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(26373).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},89142:(e,t,r)=>{r.d(t,{bJ:()=>s.LikeListSkeleton}),r(33755);var s=r(91114);r(44533),r(81469),r(92401)},91114:(e,t,r)=>{r.d(t,{LikeListSkeleton:()=>n,default:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\likes\\\\LikeCardSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx","default"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call LikeListSkeleton() from the server but LikeListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx","LikeListSkeleton")},92401:(e,t,r)=>{r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\likes\\\\LikeList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx","default")},92913:(e,t,r)=>{r.d(t,{default:()=>k});var s=r(60687),a=r(43210),n=r(85814),l=r.n(n),i=r(24934),o=r(70373),d=r(25334),c=r(17313),u=r(58869),m=r(41862),p=r(67760),x=r(1958),h=r(52581),f=r(77882),b=r(96241);function k({likeId:e,profile:t,onUnlikeSuccess:r,showUnlike:n=!0,variant:k="default",showVisitButton:v=!0,showAddress:g=!0,showRedirectIcon:j=!1}){let[w,N]=(0,a.useState)(!1),y=async()=>{if(n&&r){N(!0);try{let s=await (0,x.J)(t.id);s.success?(h.oR.success(`${"business"===t.type?"Business":"Profile"} unliked successfully`),r(e)):h.oR.error(s.error||`Failed to unlike ${t.type}`)}catch(e){console.error("Error unliking:",e),h.oR.error("An unexpected error occurred")}finally{N(!1)}}},C=t.slug?`/${t.slug}`:"#",L=t.logo_url||t.avatar_url,A=t.name||"Unknown";return(0,s.jsxs)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},whileHover:{y:-5,transition:{duration:.2}},className:(0,b.cn)("rounded-lg border p-0 overflow-hidden transition-all duration-300","bg-white dark:bg-black border-neutral-200 dark:border-neutral-800","shadow-sm hover:shadow-md","compact"===k&&"max-w-sm"),children:[(0,s.jsx)("div",{className:"absolute inset-0 pointer-events-none opacity-5 dark:opacity-10",style:{backgroundImage:'url("/decorative/card-texture.svg")',backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}}),(0,s.jsxs)("div",{className:"relative z-10 p-4",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3",children:["business"===t.type&&t.slug?(0,s.jsx)(l(),{href:C,target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,s.jsxs)(o.eu,{className:"h-12 w-12 border border-neutral-200 dark:border-neutral-800",children:[L?(0,s.jsx)(o.BK,{src:L,alt:A}):null,(0,s.jsx)(o.q5,{className:"bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-300",children:A[0]?.toUpperCase()||"?"})]})}):(0,s.jsxs)(o.eu,{className:"h-12 w-12 border border-neutral-200 dark:border-neutral-800",children:[L?(0,s.jsx)(o.BK,{src:L,alt:A}):null,(0,s.jsx)(o.q5,{className:"bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-300",children:A[0]?.toUpperCase()||"?"})]}),(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsx)("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:"business"===t.type&&t.slug?(0,s.jsxs)(l(),{href:C,target:"_blank",rel:"noopener noreferrer",className:"group flex items-center gap-1",children:[(0,s.jsx)("h3",{className:"font-medium text-neutral-800 dark:text-neutral-100 truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:A}),j&&(0,s.jsx)(d.A,{className:"w-3 h-3 text-neutral-400 group-hover:text-blue-500 transition-colors flex-shrink-0"})]}):(0,s.jsx)("h3",{className:"font-medium text-neutral-800 dark:text-neutral-100 truncate",children:A})}),(0,s.jsx)("div",{className:"flex items-center gap-1 mt-1",children:"business"===t.type?(0,s.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium",children:[(0,s.jsx)(c.A,{className:"h-3 w-3"}),"Business"]}):(0,s.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium",children:[(0,s.jsx)(u.A,{className:"h-3 w-3"}),"Customer"]})})]})})})]}),g&&(0,s.jsxs)("div",{className:"mt-2 ml-15",children:[" ",(0,s.jsxs)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center",children:[(0,s.jsx)("span",{className:"inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2"}),(e=>{if(!g)return null;let t=[e.locality,e.city,e.state].filter(Boolean);return t.length>0?t.join(", "):"Location not specified"})(t)]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[v&&"business"===t.type&&t.slug?(0,s.jsx)(i.$,{asChild:!0,variant:"outline",size:"sm",className:"text-xs h-8",children:(0,s.jsx)(l(),{href:C,target:"_blank",rel:"noopener noreferrer",children:"Visit Card"})}):(0,s.jsx)("div",{}),n&&r&&(0,s.jsxs)(i.$,{variant:"ghost",size:"sm",className:"text-xs h-8 text-rose-500 hover:text-rose-600 hover:bg-rose-50 dark:hover:bg-rose-900/20",onClick:y,disabled:w,children:[w?(0,s.jsx)(m.A,{className:"h-3.5 w-3.5 mr-1.5 animate-spin"}):(0,s.jsx)(p.A,{className:"h-3.5 w-3.5 mr-1.5 fill-current"}),"Unlike"]})]})]})]})}}};