(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196,6923],{2219:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var s=t(95155),r=t(12115),l=t(54073),n=t(53999);function i(e){let{className:a,defaultValue:t,value:i,min:o=0,max:d=100,...c}=e,m=r.useMemo(()=>Array.isArray(i)?i:Array.isArray(t)?t:[o,d],[i,t,o,d]);return(0,s.jsxs)(l.bL,{"data-slot":"slider",defaultValue:t,value:i,min:o,max:d,className:(0,n.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",a),...c,children:[(0,s.jsx)(l.<PERSON>,{"data-slot":"slider-track",className:(0,n.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,s.jsx)(l.Q6,{"data-slot":"slider-range",className:(0,n.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:m.length},(e,a)=>(0,s.jsx)(l.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},a))]})}},29058:(e,a,t)=>{"use strict";t.d(a,{t:()=>r});var s=t(34477);let r=(0,s.createServerReference)("40d32ea8edc6596cf0013772648e4fa734c9679198",s.callServer,void 0,s.findSourceMapURL,"getPincodeDetails")},30070:(e,a,t)=>{"use strict";t.d(a,{C5:()=>f,MJ:()=>g,Rr:()=>b,eI:()=>h,lR:()=>p,lV:()=>d,zB:()=>m});var s=t(95155),r=t(12115),l=t(99708),n=t(62177),i=t(53999),o=t(82714);let d=n.Op,c=r.createContext({}),m=e=>{let{...a}=e;return(0,s.jsx)(c.Provider,{value:{name:a.name},children:(0,s.jsx)(n.xI,{...a})})},u=()=>{let e=r.useContext(c),a=r.useContext(x),{getFieldState:t}=(0,n.xW)(),s=(0,n.lN)({name:e.name}),l=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=a;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...l}},x=r.createContext({});function h(e){let{className:a,...t}=e,l=r.useId();return(0,s.jsx)(x.Provider,{value:{id:l},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",a),...t})})}function p(e){let{className:a,...t}=e,{error:r,formItemId:l}=u();return(0,s.jsx)(o.J,{"data-slot":"form-label","data-error":!!r,className:(0,i.cn)("data-[error=true]:text-destructive",a),htmlFor:l,...t})}function g(e){let{...a}=e,{error:t,formItemId:r,formDescriptionId:n,formMessageId:i}=u();return(0,s.jsx)(l.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?"".concat(n," ").concat(i):"".concat(n),"aria-invalid":!!t,...a})}function b(e){let{className:a,...t}=e,{formDescriptionId:r}=u();return(0,s.jsx)("p",{"data-slot":"form-description",id:r,className:(0,i.cn)("text-muted-foreground text-sm",a),...t})}function f(e){var a;let{className:t,...r}=e,{error:l,formMessageId:n}=u(),o=l?String(null!=(a=null==l?void 0:l.message)?a:""):r.children;return o?(0,s.jsx)("p",{"data-slot":"form-message",id:n,className:(0,i.cn)("text-destructive text-sm",t),...r,children:o}):null}},55747:(e,a,t)=>{"use strict";t.d(a,{C:()=>o,z:()=>i});var s=t(95155);t(12115);var r=t(54059),l=t(9428),n=t(53999);function i(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"radio-group",className:(0,n.cn)("grid gap-3",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)(r.q7,{"data-slot":"radio-group-item",className:(0,n.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(r.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,s.jsx)(l.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},58251:(e,a,t)=>{Promise.resolve().then(t.bind(t,75587))},60823:(e,a,t)=>{"use strict";t.d(a,{AM:()=>n,Wv:()=>i,hl:()=>o});var s=t(95155);t(12115);var r=t(98176),l=t(53999);function n(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"popover",...a})}function i(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"popover-trigger",...a})}function o(e){let{className:a,align:t="center",sideOffset:n=4,...i}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{"data-slot":"popover-content",align:t,sideOffset:n,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",a),...i})})}},75587:(e,a,t)=>{"use strict";t.d(a,{default:()=>eZ});var s=t(95155),r=t(12115),l=t(62177),n=t(90221),i=t(56671),o=t(28695),d=t(38164),c=t(97168);function m(e){let a=e.getBoundingClientRect(),t=window.scrollY+a.top-150,s=window.scrollY,r=t-s;e.classList.add("error-highlight"),setTimeout(()=>{e.classList.remove("error-highlight")},3e3);let l=e=>1-Math.pow(1-e,3),n=performance.now();requestAnimationFrame(function e(a){let t=Math.min((a-n)/800,1),i=l(t);window.scrollTo({top:s+r*i,behavior:"auto"}),t<1&&requestAnimationFrame(e)})}var u=t(28882),x=t(34477);let h=(0,x.createServerReference)("40aa31586df3ce75d0e0d55282c9fb7a8889f88f1d",x.callServer,void 0,x.findSourceMapURL,"updateBusinessCard"),p=(0,x.createServerReference)("402541af2e999f9ab59f3228ba7ab00103f86ac358",x.callServer,void 0,x.findSourceMapURL,"uploadLogoAndGetUrl"),g=(0,x.createServerReference)("407aa7558d8d3c2a2485318248f6b61b88e81c9984",x.callServer,void 0,x.findSourceMapURL,"updateLogoUrl"),b=(0,x.createServerReference)("00a881513113b70ae43ac568b9503ff7a79d1663cb",x.callServer,void 0,x.findSourceMapURL,"deleteLogoUrl");var f=t(90196),j=t(29058),v=t(71007),y=t(17576),N=t(23227),w=t(43332),k=t(69074),C=t(28883),A=t(29869),_=t(62525),S=t(51154),R=t(81284),L=t(81497),D=t(89852),P=t(99474),z=t(30070),U=t(99828),T=t(66766);function I(e){var a;let{form:t,onFileSelect:r,isLogoUploading:l=!1,onLogoDelete:n}=e,i=(null==(a=t.watch("about_bio"))?void 0:a.length)||0;return(0,s.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,s.jsx)(v.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Basic Information"}),(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Let's start with the essential details for your business card"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,s.jsx)(z.zB,{control:t.control,name:"member_name",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 text-primary"}),"Your Name",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(D.p,{placeholder:"e.g., Rajesh Mahapatra",...t,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:50}),(0,s.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[(null==(a=t.value)?void 0:a.length)||0,"/50"]})]})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your full name as it will appear on the card"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:t.control,name:"title",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(y.A,{className:"h-3.5 w-3.5 text-primary"}),"Your Title/Designation",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(D.p,{placeholder:"e.g., Owner, Manager, Developer",...t,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:50}),(0,s.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[(null==(a=t.value)?void 0:a.length)||0,"/50"]})]})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your position or role within the business"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})]}),(0,s.jsx)(z.zB,{control:t.control,name:"business_name",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(N.A,{className:"h-3.5 w-3.5 text-primary"}),"Business Name",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(D.p,{placeholder:"e.g., Mahapatra Kirana & General Store",...t,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:100}),(0,s.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[(null==(a=t.value)?void 0:a.length)||0,"/100"]})]})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"The name of your business or organization"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:t.control,name:"business_category",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(w.A,{className:"h-3.5 w-3.5 text-primary"}),"Business Category",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)(U.A,{value:a.value||"",onChange:a.onChange,placeholder:"Select a business category",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"The category that best describes your business"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:t.control,name:"established_year",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(k.A,{className:"h-3.5 w-3.5 text-primary"}),"Established Year"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(D.p,{placeholder:"e.g., 2015",...a,type:"number",min:"1800",max:new Date().getFullYear(),value:a.value||"",onChange:e=>{let t=e.target.value;a.onChange(""===t?null:parseInt(t,10))},className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"The year your business was established (will be displayed on your card)"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:t.control,name:"contact_email",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(C.A,{className:"h-3.5 w-3.5 text-primary"}),"Contact Email",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(D.p,{placeholder:"e.g., <EMAIL>",...t,type:"email",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"}),(0,s.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[(null==(a=t.value)?void 0:a.length)||0,"/100"]})]})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Email address for customers to contact you (required)"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:t.control,name:"logo_url",render:()=>{var e;let a=t.watch("logo_url"),i=a&&""!==a.trim()?a:null,o=i?null==(e=i.split("/").pop())?void 0:e.split("?")[0]:null;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(A.A,{className:"h-3.5 w-3.5 text-primary"}),"Logo / Profile Photo"]}),o&&(0,s.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-gradient-to-r from-neutral-50 to-neutral-100 dark:from-neutral-800/50 dark:to-neutral-800 border border-neutral-200 dark:border-neutral-700",children:[i&&(0,s.jsx)("div",{className:"h-10 w-10 sm:h-12 sm:w-12 rounded-md border border-neutral-200 dark:border-neutral-700 overflow-hidden bg-white dark:bg-black flex items-center justify-center shadow-sm",children:(0,s.jsx)(T.default,{src:i,alt:"Current logo",className:"h-full w-full object-contain",width:48,height:48,priority:!0})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-xs font-medium text-neutral-800 dark:text-neutral-200 truncate",children:o}),(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400",children:"Current logo"})]}),n&&!l&&(0,s.jsx)("button",{type:"button",onClick:n,className:"p-1.5 rounded-full bg-red-50 dark:bg-red-900/20 text-red-500 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors cursor-pointer",title:"Delete logo","aria-label":"Delete logo",children:(0,s.jsx)(_.A,{className:"h-3.5 w-3.5"})}),l&&(0,s.jsx)("div",{className:"p-1.5 rounded-full bg-neutral-100 dark:bg-neutral-800",children:(0,s.jsx)(S.A,{className:"h-3.5 w-3.5 animate-spin text-neutral-500 dark:text-neutral-400"})})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)("label",{className:"flex flex-col items-center justify-center w-full h-20 sm:h-24 border-2 border-dashed ".concat(l?"border-primary/30 bg-primary/5":"border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/50 hover:bg-neutral-100 dark:hover:bg-neutral-800"," rounded-lg ").concat(l?"cursor-wait":"cursor-pointer"," transition-all duration-300"),children:[(0,s.jsx)("div",{className:"flex flex-col items-center justify-center pt-3 pb-3 sm:pt-4 sm:pb-4",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"p-1.5 sm:p-2 rounded-full bg-primary/10 mb-1 sm:mb-2",children:(0,s.jsx)(S.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-primary animate-spin"})}),(0,s.jsx)("p",{className:"text-xs text-primary font-medium",children:"Uploading logo..."}),(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 text-center mt-0.5 max-w-xs px-4",children:"Please wait while we process your image"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"p-1.5 sm:p-2 rounded-full bg-primary/10 mb-1 sm:mb-2 hover:bg-primary/20 transition-colors",children:(0,s.jsx)(A.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-primary"})}),(0,s.jsx)("p",{className:"text-xs text-neutral-700 dark:text-neutral-300 font-medium",children:i?"Replace logo":"Drop your logo here"}),(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 text-center mt-0.5 max-w-xs px-4",children:"PNG, JPG, GIF or WEBP (Max. 15MB)"})]})}),(0,s.jsx)(D.p,{type:"file",accept:"image/png, image/jpeg, image/gif, image/webp",className:"hidden",onChange:e=>{var a;return r((null==(a=e.target.files)?void 0:a[0])||null)},value:void 0,disabled:l})]})})}),(0,s.jsxs)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1",children:[(0,s.jsx)(R.A,{className:"w-3 h-3"}),"Your logo will be displayed prominently on your business card"]}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:t.control,name:"about_bio",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(L.A,{className:"h-3.5 w-3.5 text-primary"}),"About / Bio"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(P.T,{placeholder:"Tell us about your business or yourself...",...a,className:"min-h-[80px] sm:min-h-[100px] rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 p-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none transition-all",maxLength:100}),(0,s.jsxs)("div",{className:"absolute right-2 bottom-2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[i,"/100"]})]})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"A short description that will be displayed on your card"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})]}),(0,s.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm",children:(0,s.jsx)(R.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300",children:"Pro Tip"}),(0,s.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed",children:"Keep your business name and title concise for better readability on your digital business card. A clear, professional photo or logo helps with brand recognition and creates a memorable first impression."})]})]})})]})}var M=t(4516),V=t(19420),B=t(34869),E=t(95784);function F(e){let{form:a,isPincodeLoading:t,availableLocalities:r,onPincodeChange:l}=e;return(0,s.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,s.jsx)(M.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Contact & Location"}),(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Add your contact information and business location details"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,s.jsx)(z.zB,{control:a.control,name:"phone",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(V.A,{className:"h-3.5 w-3.5 text-primary"}),"Primary Phone",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(D.p,{placeholder:"9876543210",type:"tel",pattern:"[0-9]*",inputMode:"numeric",...a,onChange:e=>{let t=e.target.value.replace(/^\+91/,"");(t=t.replace(/\D/g,"")).length<=10&&a.onChange(t)},onKeyDown:e=>{let a=/^[0-9]$/.test(e.key),t=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);a||t||e.preventDefault()},className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your primary contact number for customers"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:a.control,name:"address_line",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(N.A,{className:"h-3.5 w-3.5 text-primary"}),"Address Line",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(D.p,{placeholder:"e.g., Shop No. 12, Main Road",...t,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:100}),(0,s.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[(null==(a=t.value)?void 0:a.length)||0,"/100"]})]})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your street address or landmark"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})]}),(0,s.jsx)(z.zB,{control:a.control,name:"pincode",render:e=>{var a;let{field:r}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(B.A,{className:"h-3.5 w-3.5 text-primary"}),"Pincode",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[(0,s.jsx)(z.MJ,{className:"flex-1",children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(D.p,{placeholder:"e.g., 751001",...r,value:null!=(a=r.value)?a:"",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:6,type:"number",onChange:e=>{r.onChange(e),6===e.target.value.length&&l(e.target.value)},onInput:e=>{let a=e.target;a.value=a.value.replace(/[^0-9]/g,"")}})})}),t&&(0,s.jsx)("div",{className:"p-1.5 rounded-md bg-neutral-100 dark:bg-neutral-800",children:(0,s.jsx)(S.A,{className:"h-4 w-4 sm:h-5 sm:w-5 animate-spin text-primary"})})]}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"6-digit pincode to auto-fill city and state"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,s.jsx)(z.zB,{control:a.control,name:"city",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(M.A,{className:"h-3.5 w-3.5 text-primary/50"}),"City",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(D.p,{placeholder:"Auto-filled from Pincode",...t,value:null!=(a=t.value)?a:"",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-100/80 dark:bg-neutral-800/40 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 cursor-not-allowed",readOnly:!0})})}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:a.control,name:"state",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(M.A,{className:"h-3.5 w-3.5 text-primary/50"}),"State",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(D.p,{placeholder:"Auto-filled from Pincode",...t,value:null!=(a=t.value)?a:"",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-100/80 dark:bg-neutral-800/40 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 cursor-not-allowed",readOnly:!0})})}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})]}),(0,s.jsx)(z.zB,{control:a.control,name:"locality",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(M.A,{className:"h-3.5 w-3.5 text-primary"}),"Locality / Area",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)(E.l6,{onValueChange:t.onChange,value:null!=(a=t.value)?a:"",disabled:0===r.length,children:[(0,s.jsx)(z.MJ,{children:(0,s.jsx)(E.bq,{className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",disabled:0===r.length,children:(0,s.jsx)(E.yv,{placeholder:0===r.length?"Enter Pincode first":"Select your locality"})})}),(0,s.jsx)(E.gC,{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg",children:r.map(e=>(0,s.jsx)(E.eb,{value:e,className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20",children:e},e))})]}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Select the specific area within the pincode"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})]}),(0,s.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm",children:(0,s.jsx)(R.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300",children:"Location Tip"}),(0,s.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed",children:'Adding accurate location details helps customers find you easily. Pincode auto-fills city and state for consistency. Add your Google Maps URL to show a "Get Directions" button on your public card.'})]})]})})]})}var O=t(33127),J=t(40133);function q(e){let{form:a,currentUserPlan:t}=e;return(0,s.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,s.jsx)(O.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Appearance"}),(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Customize your card's visual appearance"})]})]}),(0,s.jsx)("div",{className:"flex flex-col gap-4 sm:gap-6",children:(0,s.jsx)(z.zB,{control:a.control,name:"theme_color",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(O.A,{className:"h-3.5 w-3.5 text-primary"}),"Theme Color"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[(0,s.jsxs)("div",{className:"relative flex-grow",children:[(0,s.jsx)(O.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,s.jsx)(D.p,{type:"text",...a,className:"pl-10 w-full font-mono uppercase rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 ".concat("pro"!==t&&"enterprise"!==t?"opacity-60 cursor-not-allowed":""),value:a.value||"#F5D76E",onChange:e=>{if("pro"===t||"enterprise"===t){let t=e.target.value;(""===t||/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})$/.test(t))&&a.onChange(t)}},disabled:"pro"!==t&&"enterprise"!==t})]}),(0,s.jsx)(D.p,{type:"color",...a,className:"h-8 sm:h-10 w-10 sm:w-12 p-1 rounded-md ".concat("pro"===t||"enterprise"===t?"cursor-pointer":"cursor-not-allowed opacity-60"," border border-neutral-200 dark:border-neutral-700 hover:border-primary dark:hover:border-primary transition-all duration-200"),value:a.value||"#F5D76E",disabled:"pro"!==t&&"enterprise"!==t}),(0,s.jsx)("div",{className:"h-8 sm:h-10 w-8 sm:w-10 rounded-md border border-neutral-200 dark:border-neutral-700 shadow-inner transition-transform hover:scale-105",style:{backgroundColor:a.value||"#F5D76E"}}),("pro"===t||"enterprise"===t)&&(0,s.jsx)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>a.onChange(""),className:"h-8 sm:h-10 px-2 sm:px-3 text-xs hover:bg-red-50 hover:border-red-200 hover:text-red-600 dark:hover:bg-red-950/20 dark:hover:border-red-800 dark:hover:text-red-400 transition-colors",title:"Reset to default color",children:(0,s.jsx)(J.A,{className:"h-3 w-3 sm:h-3.5 sm:w-3.5"})})]})}),(0,s.jsxs)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1",children:[(0,s.jsx)(R.A,{className:"w-3 h-3"}),"pro"===t||"enterprise"===t?"Select the primary accent color for your card":"Theme customization is only available for Pro and Enterprise plans"]}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})}),("basic"===t||"growth"===t||"trial"===t)&&(0,s.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-blue-100 dark:border-blue-900/30 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 mt-0.5 shadow-sm",children:(0,s.jsx)(R.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-xs sm:text-sm font-medium text-blue-800 dark:text-blue-300",children:"Pro Plan Unlocks Custom Theme Colors"}),(0,s.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-400 mt-0.5 sm:mt-1 leading-relaxed",children:"Upgrade to Pro for more customization options, including custom theme colors for your card."})]}),(0,s.jsx)("button",{className:"ml-auto px-3 sm:px-4 py-1.5 sm:py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-xs sm:text-sm font-medium transition-colors",children:"Upgrade"})]})})]})}var Y=t(78749),$=t(92657),G=t(93500),W=t(27213),H=t(90088),K=t(88145),Z=t(76037),Q=t(51362),X=t(62098),ee=t(93509),ea=t(54416),et=t(88482);function es(e){let{theme:a,imageUrl:t,isUploading:l,isDeleting:n,isDragging:i,onFileSelect:o,onDelete:d,onDrop:m,onDragOver:u,onDragLeave:x}=e,h=(0,r.useRef)(null),p="light"===a?X.A:ee.A,g="light"===a?"Light":"Dark",b="light"===a?"text-yellow-600":"text-blue-600",f="light"===a?"border-yellow-200 dark:border-yellow-800":"border-blue-200 dark:border-blue-800";return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p,{className:"h-4 w-4 ".concat(b)}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[g," Theme"]})]}),(0,s.jsx)(et.Zp,{className:"border-dashed border-2 transition-colors cursor-pointer ".concat(i?"border-primary bg-primary/5":"border-muted-foreground/25 hover:border-primary/50"),onDrop:e=>m(e,a),onDragOver:e=>u(e,a),onDragLeave:e=>x(e,a),onClick:()=>{var e;return!l&&!n&&(null==(e=h.current)?void 0:e.click())},children:(0,s.jsxs)(et.Wu,{className:"p-4",children:[l?(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-lg bg-muted flex items-center justify-center mx-auto mb-2",children:(0,s.jsx)(S.A,{className:"h-5 w-5 text-muted-foreground animate-spin"})}),(0,s.jsx)("p",{className:"text-xs font-medium mb-1",children:"Uploading..."}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Processing ",g.toLowerCase()," theme image"]})]}):t?(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-full h-16 overflow-hidden rounded-lg border-2 ".concat(f," ").concat("light"===a?"bg-yellow-50 dark:bg-yellow-950/20":"bg-blue-50 dark:bg-blue-950/20"),children:(0,s.jsx)(T.default,{src:t,alt:"".concat(g," theme header"),width:200,height:64,className:"w-full h-full object-contain"})}),(0,s.jsx)(c.$,{type:"button",variant:"destructive",size:"sm",onClick:e=>{e.stopPropagation(),d(a)},disabled:n,className:"absolute top-1 right-1 h-6 w-6 p-0",children:n?(0,s.jsx)(S.A,{className:"h-3 w-3 animate-spin"}):(0,s.jsx)(ea.A,{className:"h-3 w-3"})})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-xs font-medium ".concat(b),children:[g," theme image uploaded"]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Click to replace"})]})]}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-lg bg-muted flex items-center justify-center mx-auto mb-2",children:(0,s.jsx)(A.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,s.jsxs)("p",{className:"text-xs font-medium mb-1",children:["Upload ",g," Image"]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mb-1",children:"Drag & drop or click"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"PNG • Max 5MB"})]}),(0,s.jsx)("input",{ref:h,type:"file",accept:"image/*",onChange:e=>{var t;return o((null==(t=e.target.files)?void 0:t[0])||null,a)},className:"hidden",disabled:l||n})]})})]})}function er(e){let{form:a,currentUserPlan:t}=e,l="pro"===t||"enterprise"===t,{resolvedTheme:n}=(0,Q.D)(),[o,d]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1),x=e=>{e.trim()?a.setValue("custom_branding.hide_dukancard_branding",!0):a.setValue("custom_branding.hide_dukancard_branding",!1)},h=e=>{e||(a.clearErrors("custom_branding.custom_header_text"),a.trigger("custom_branding"))},p=async(e,t)=>{if(e){if(!e.type.startsWith("image/"))return void i.oR.error("Please upload a valid image file");if(e.size>5242880){let a=(e.size/1048576).toFixed(1);i.oR.error("Image size (".concat(a,"MB) is too large. Please choose an image smaller than 5MB for optimal performance."));return}try{let s=await (0,f.q)(e,{maxDimension:400,targetSizeKB:150}),r=new File([s.blob],e.name,{type:s.blob.type}),l=URL.createObjectURL(r);"light"===t?(a.setValue("custom_branding.custom_header_image_light_url",l,{shouldDirty:!0}),a.setValue("custom_branding.pending_light_header_file",r,{shouldDirty:!0})):(a.setValue("custom_branding.custom_header_image_dark_url",l,{shouldDirty:!0}),a.setValue("custom_branding.pending_dark_header_file",r,{shouldDirty:!0})),a.setValue("custom_branding.hide_dukancard_branding",!0,{shouldDirty:!0}),i.oR.success("".concat("light"===t?"Light":"Dark",' theme header image compressed and ready. Click "Save Changes" to upload.'))}catch(e){console.error("Image compression failed:",e),i.oR.error("Failed to process image. Please try a different image.")}}},g=e=>{"light"===e?(a.setValue("custom_branding.custom_header_image_light_url","",{shouldDirty:!0}),a.setValue("custom_branding.pending_light_header_file",null,{shouldDirty:!0})):(a.setValue("custom_branding.custom_header_image_dark_url","",{shouldDirty:!0}),a.setValue("custom_branding.pending_dark_header_file",null,{shouldDirty:!0})),i.oR.success("".concat("light"===e?"Light":"Dark"," theme header image will be removed when you save changes"))},b=(e,a)=>{e.preventDefault(),("light"===a?d:u)(!1);let t=e.dataTransfer.files;t.length>0&&p(t[0],a)},j=(e,a)=>{e.preventDefault(),("light"===a?d:u)(!0)},v=(e,a)=>{e.preventDefault(),("light"===a?d:u)(!1)};return l?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(O.A,{className:"h-5 w-5 text-primary"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-200",children:"Custom Branding"}),(0,s.jsx)(K.E,{variant:"default",className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:null==t?void 0:t.toUpperCase()})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(z.zB,{control:a.control,name:"custom_branding.hide_dukancard_branding",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsxs)(z.lR,{className:"text-base font-medium flex items-center gap-2",children:[a.value?(0,s.jsx)(Y.A,{className:"h-4 w-4"}):(0,s.jsx)($.A,{className:"h-4 w-4"}),"Hide Dukancard Branding"]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Remove Dukancard branding from your business card"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)(H.d,{checked:a.value,onCheckedChange:e=>{a.onChange(e),h(e)}})})]})}}),(0,s.jsx)(Z.w,{}),(0,s.jsx)(z.zB,{control:a.control,name:"custom_branding.custom_header_text",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(G.A,{className:"h-4 w-4 text-primary"}),"Custom Header Text"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)(D.p,{...a,value:a.value||"",onChange:e=>{a.onChange(e.target.value),x(e.target.value)},placeholder:"e.g., Powered by YourBrand",maxLength:50})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:'Custom text to display in the header instead of Dukancard branding (max 50 characters). Required when "Hide Dukancard Branding" is enabled.'}),(0,s.jsx)(z.C5,{})]})}}),(0,s.jsx)(Z.w,{}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(W.A,{className:"h-4 w-4 text-primary","aria-label":"Theme specific header"}),"Theme-Specific Header Images (Alternative to Text)"]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Upload custom images for light and dark themes. Images will automatically switch based on the user's theme preference. PNG format recommended for best quality."}),(0,s.jsx)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)("span",{className:"w-2 h-2 rounded-full ".concat("light"===n?"bg-yellow-500":"bg-gray-400")}),"Current: ","light"===n?"Light":"dark"===n?"Dark":"System"]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(es,{theme:"light",imageUrl:a.watch("custom_branding.custom_header_image_light_url"),isUploading:!1,isDeleting:!1,isDragging:o,onFileSelect:p,onDelete:g,onDrop:b,onDragOver:j,onDragLeave:v}),(0,s.jsx)(es,{theme:"dark",imageUrl:a.watch("custom_branding.custom_header_image_dark_url"),isUploading:!1,isDeleting:!1,isDragging:m,onFileSelect:p,onDelete:g,onDrop:b,onDragOver:j,onDragLeave:v})]})]}),(0,s.jsx)(Z.w,{}),(0,s.jsx)("div",{className:"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,s.jsx)(O.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1",children:"Theme Color Customization"}),(0,s.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["Customize your business card's theme color in the ",(0,s.jsx)("strong",{children:"Appearance"})," section above. Pro and Enterprise users can choose any custom color, while other plans use the default gold theme."]})]})]})})]})]}):(0,s.jsxs)("div",{className:"space-y-4 p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-950/20",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(O.A,{className:"h-5 w-5 text-amber-600 dark:text-amber-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-amber-800 dark:text-amber-200",children:"Custom Branding"}),(0,s.jsx)(K.E,{variant:"secondary",className:"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200",children:"Pro Feature"})]}),(0,s.jsx)("p",{className:"text-sm text-amber-700 dark:text-amber-300",children:"Upgrade to Pro or Enterprise plan to access custom branding features including custom logos, watermarks, colors, and the ability to hide Dukancard branding."}),(0,s.jsx)(c.$,{variant:"outline",className:"border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20",children:"Upgrade to Pro"})]})}let el=(0,x.createServerReference)("40aaafea41f50b748ef546f36a08d4542ed53be507",x.callServer,void 0,x.findSourceMapURL,"uploadCustomAdImage"),en=(0,x.createServerReference)("00c08d23d995424fbe98469845e395857a3ae1a19c",x.callServer,void 0,x.findSourceMapURL,"deleteCustomAd");var ei=t(3159),eo=t(99840),ed=t(56586),ec=t(2219);let em=e=>new Promise((a,t)=>{let s=new Image;s.addEventListener("load",()=>a(s)),s.addEventListener("error",e=>t(e)),s.setAttribute("crossOrigin","anonymous"),s.src=e});async function eu(e,a){let t=await em(e),s=document.createElement("canvas"),r=s.getContext("2d");if(!r)return null;let l=t.naturalWidth/t.width,n=t.naturalHeight/t.height,i=window.devicePixelRatio||1;return s.width=a.width*i*l,s.height=a.height*i*n,r.setTransform(i,0,0,i,0,0),r.imageSmoothingQuality="high",r.drawImage(t,a.x*l,a.y*n,a.width*l,a.height*n,0,0,a.width*l,a.height*n),new Promise(e=>{s.toBlob(e,"image/png")})}function ex(e){let{imgSrc:a,onCropComplete:t,onClose:l,isOpen:n,isUploading:i=!1}=e,[o,d]=(0,r.useState)({x:0,y:0}),[m,u]=(0,r.useState)(1),[x,h]=(0,r.useState)(null),[p,g]=(0,r.useState)(!1),b=(0,r.useCallback)((e,a)=>{h(a)},[]),f=async()=>{if(!a||!x){console.warn("Image source or crop area not available."),t(null);return}g(!0);try{let e=await eu(a,x);t(e)}catch(e){console.error("Error cropping image:",e),t(null)}finally{g(!1)}};return(0,r.useEffect)(()=>{n&&(u(1),d({x:0,y:0}))},[n]),(0,s.jsx)(eo.lG,{open:n,onOpenChange:e=>!e&&!i&&!p&&l(),children:(0,s.jsxs)(eo.Cf,{className:"sm:max-w-[700px]",children:[(0,s.jsx)(eo.c7,{children:(0,s.jsxs)(eo.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(ed.A,{className:"h-5 w-5 text-primary"}),"Crop Your Advertisement"]})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"relative h-[45vh] md:h-[55vh] w-full bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden border-2 border-primary/20",children:a?(0,s.jsx)(ei.Ay,{image:a,crop:o,zoom:m,aspect:16/9,cropShape:"rect",showGrid:!0,onCropChange:d,onZoomChange:u,onCropComplete:b,style:{containerStyle:{borderRadius:"8px",overflow:"hidden"},cropAreaStyle:{border:"2px solid hsl(var(--primary))",borderRadius:"4px"}}}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(S.A,{className:"h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading image..."})]})})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:["Zoom: ",Math.round(100*m),"%"]}),(0,s.jsx)(ec.A,{value:[m],onValueChange:e=>u(e[0]),min:1,max:3,step:.1,className:"w-full"})]}),(0,s.jsx)("div",{className:"bg-neutral-50 dark:bg-neutral-900 rounded-lg p-3 border",children:(0,s.jsx)("div",{className:"flex items-center justify-center text-sm",children:(0,s.jsx)("span",{className:"text-muted-foreground",children:"Cropping to 16:9 aspect ratio for advertisement display"})})})]}),(0,s.jsxs)(eo.Es,{className:"gap-2",children:[(0,s.jsx)(c.$,{variant:"outline",onClick:l,disabled:p||i,children:"Cancel"}),(0,s.jsx)(c.$,{onClick:f,disabled:p||i||!x,className:"min-w-[120px]",children:p||i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.A,{className:"mr-2 h-4 w-4 animate-spin"}),p?"Processing...":"Uploading..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(ed.A,{className:"mr-2 h-4 w-4"}),"Upload Ad"]})})]})]})})}function eh(e){let{form:a,currentUserPlan:t}=e,[l,n]=(0,r.useState)(!1),[o,m]=(0,r.useState)(!1),[u,x]=(0,r.useState)(!1),[h,p]=(0,r.useState)(null),[g,b]=(0,r.useState)(null),j=(0,r.useRef)(null),v="pro"===t||"enterprise"===t,y=(0,r.useCallback)(e=>{if(!e)return;if(!e.type.startsWith("image/"))return void i.oR.error("Please select a valid image file");if(e.size>5242880)return void i.oR.error("Image file size must be less than 5MB. Please choose a smaller file.");b(e);let a=new FileReader;a.onloadend=()=>{p(a.result)},a.readAsDataURL(e)},[]),N=(0,r.useCallback)(e=>{var a;if(e.preventDefault(),x(!1),!v||l)return;let t=null==(a=e.dataTransfer.files)?void 0:a[0];t&&y(t)},[v,l,y]),w=(0,r.useCallback)(e=>{e.preventDefault(),v&&!l&&x(!0)},[v,l]),k=(0,r.useCallback)(e=>{e.preventDefault(),x(!1)},[]),C=async e=>{if(!g||!e){p(null),b(null);return}n(!0);try{let t=new File([e],g.name,{type:"image/png",lastModified:Date.now()}),s=await (0,f.compressImageUltraAggressiveClient)(t,{maxDimension:1200,targetSizeKB:100}),r=new File([s.blob],g.name,{type:s.blob.type}),l=new FormData;l.append("image",r);let n=await el(l);n.success&&n.url?(a.setValue("custom_ads.image_url",n.url),a.setValue("custom_ads.enabled",!0),a.setValue("custom_ads.uploaded_at",new Date().toISOString()),i.oR.success("Custom ad uploaded successfully!")):i.oR.error(n.error||"Failed to upload custom ad")}catch(e){console.error("Upload error:",e),i.oR.error("Failed to upload custom ad")}finally{n(!1),p(null),b(null)}},_=e=>{a.setValue("custom_ads.enabled",e,{shouldDirty:!0})},R=async()=>{m(!0);try{let e=await en();e.success?(a.setValue("custom_ads.enabled",!1),a.setValue("custom_ads.image_url",""),a.setValue("custom_ads.link_url",""),a.setValue("custom_ads.uploaded_at",""),i.oR.success("Custom ad deleted successfully")):i.oR.error(e.error||"Failed to delete custom ad")}catch(e){console.error("Delete error:",e),i.oR.error("Failed to delete custom ad")}finally{m(!1)}};if(!v)return(0,s.jsxs)("div",{className:"space-y-4 p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-950/20",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(W.A,{className:"h-5 w-5 text-amber-600 dark:text-amber-400","aria-label":"Custom ads feature"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-amber-800 dark:text-amber-200",children:"Custom Ads"}),(0,s.jsx)(K.E,{variant:"secondary",className:"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200",children:"Pro Feature"})]}),(0,s.jsx)("p",{className:"text-sm text-amber-700 dark:text-amber-300",children:"Upgrade to Pro or Enterprise plan to upload custom advertisement images that will be displayed on your public business card page."}),(0,s.jsx)(c.$,{variant:"outline",className:"border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20",children:"Upgrade to Pro"})]});let L=a.watch("custom_ads.image_url"),P=a.watch("custom_ads.enabled");return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(W.A,{className:"h-5 w-5 text-primary","aria-label":"Custom advertisement"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-200",children:"Custom Advertisement"}),(0,s.jsx)(K.E,{variant:"default",className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:null==t?void 0:t.toUpperCase()})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(z.zB,{control:a.control,name:"custom_ads.enabled",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsxs)(z.lR,{className:"text-base font-medium flex items-center gap-2",children:[a.value?(0,s.jsx)($.A,{className:"h-4 w-4"}):(0,s.jsx)(Y.A,{className:"h-4 w-4"}),"Show Custom Ad"]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Display your custom advertisement on the public business card page"})]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)(H.d,{checked:a.value,onCheckedChange:e=>{a.onChange(e),_(e)},disabled:!L})})]})}}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(z.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(A.A,{className:"h-4 w-4 text-primary"}),"Advertisement Image"]}),(0,s.jsx)(et.Zp,{className:"border-dashed border-2 transition-colors cursor-pointer ".concat(u?"border-primary bg-primary/5":"border-muted-foreground/25 hover:border-primary/50"),onDrop:N,onDragOver:w,onDragLeave:k,onClick:()=>{var e;return!l&&!o&&(null==(e=j.current)?void 0:e.click())},children:(0,s.jsxs)(et.Wu,{className:"p-6",children:[l?(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-4 border-2 border-primary/20",children:(0,s.jsx)(S.A,{className:"h-8 w-8 text-primary animate-spin"})}),(0,s.jsx)("p",{className:"text-sm font-medium mb-2 text-primary",children:"Uploading Custom Ad..."}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Compressing and optimizing image"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Uploading to secure storage"})]}),(0,s.jsx)("div",{className:"mt-3 w-full bg-muted rounded-full h-1.5",children:(0,s.jsx)("div",{className:"bg-primary h-1.5 rounded-full animate-pulse",style:{width:"70%"}})})]}):L?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-full aspect-[16/9] overflow-hidden rounded-lg border-2 border-green-200 dark:border-green-800",children:(0,s.jsx)(T.default,{src:L,alt:"Custom advertisement",width:400,height:225,className:"w-full h-full object-cover"})}),(0,s.jsx)(c.$,{type:"button",variant:"destructive",size:"sm",onClick:e=>{e.stopPropagation(),R()},disabled:o,className:"absolute top-2 right-2",children:o?(0,s.jsx)(S.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(ea.A,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{className:"absolute bottom-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded flex items-center gap-1",children:[(0,s.jsx)($.A,{className:"h-3 w-3"}),P?"Live":"Draft"]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-green-700 dark:text-green-300",children:"Custom ad uploaded successfully"}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[P?"Visible on your public card":"Enable to show on public card"," • Click to replace"]})]})]}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-lg bg-muted flex items-center justify-center mx-auto mb-3",children:(0,s.jsx)(A.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,s.jsx)("p",{className:"text-sm font-medium mb-1",children:"Upload Custom Ad"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mb-2",children:"Drag & drop or click to select"}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,s.jsx)("strong",{children:"Aspect Ratio:"})," 16:9 (recommended)"]})]}),(0,s.jsx)("input",{ref:j,type:"file",accept:"image/*",onChange:e=>{var a;return y((null==(a=e.target.files)?void 0:a[0])||null)},className:"hidden",disabled:l||o})]})})]}),(0,s.jsx)(z.zB,{control:a.control,name:"custom_ads.link_url",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 text-primary"}),"Advertisement Link (Optional)"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)(D.p,{...a,value:a.value||"",onChange:e=>{a.onChange(e.target.value)},placeholder:"https://example.com"})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:'Make your ad clickable by adding a website URL. Changes will be saved when you click "Save Changes".'}),(0,s.jsx)(z.C5,{})]})}})]}),(0,s.jsx)(ex,{isOpen:!!h,imgSrc:h,onCropComplete:C,onClose:()=>{l||(p(null),b(null))},isUploading:l})]})}var ep=t(14186),eg=t(29799),eb=t(95139),ef=t(82714),ej=t(74126);let ev=e=>{if(!e||e.length<5)return e;let[a,t]=e.split(":"),s=parseInt(a,10);return isNaN(s)?e:"".concat(s%12||12,":").concat(t," ").concat(s>=12?"PM":"AM")},ey={isOpen:!1,openTime:"09:00",closeTime:"18:00"},eN={monday:{...ey},tuesday:{...ey},wednesday:{...ey},thursday:{...ey},friday:{...ey},saturday:{...ey},sunday:{...ey}},ew={weekdays:{label:"Mon-Fri",days:["monday","tuesday","wednesday","thursday","friday"]},weekend:{label:"Sat-Sun",days:["saturday","sunday"]},all:{label:"All Days",days:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]}};function ek(e){let{value:a,onChange:t}=e,[l,n]=(0,r.useState)(()=>{try{if(!a)return{...eN};let e={...eN};return Object.keys(eN).forEach(t=>{if(a[t]&&"object"==typeof a[t]){let s=a[t];e[t]={isOpen:"boolean"==typeof s.isOpen&&s.isOpen,openTime:"string"==typeof s.openTime?s.openTime:"09:00",closeTime:"string"==typeof s.closeTime?s.closeTime:"18:00"}}}),e}catch(e){return console.error("Error parsing business hours:",e),{...eN}}}),[i,o]=(0,r.useState)(()=>({weekdays:ew.weekdays.days.some(e=>l[e].isOpen),weekend:ew.weekend.days.some(e=>l[e].isOpen),all:Object.values(l).some(e=>e.isOpen)})),[d,m]=(0,r.useState)(()=>{let e=e=>{let a=ew[e].days.find(e=>l[e].isOpen);return a?{openTime:l[a].openTime,closeTime:l[a].closeTime}:{openTime:"09:00",closeTime:"18:00"}};return{weekdays:e("weekdays"),weekend:e("weekend"),all:e("all")}}),u=(e,a,s,r)=>{o(t=>({...t,[e]:a})),void 0!==s&&void 0!==r&&m(a=>({...a,[e]:{openTime:s,closeTime:r}}));let i={...l};ew[e].days.forEach(t=>{i[t]={isOpen:a,openTime:s||d[e].openTime,closeTime:r||d[e].closeTime}}),n(i),t(i)};return(0,s.jsxs)("div",{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 overflow-hidden",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-neutral-200 dark:border-neutral-700",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ep.A,{className:"h-4 w-4 text-neutral-500"}),(0,s.jsx)("h3",{className:"text-sm font-medium",children:"Business Hours"})]})}),(0,s.jsx)("div",{className:"p-4 space-y-4",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-3 bg-neutral-100 dark:bg-neutral-800",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(eb.S,{id:"weekdays-open",checked:i.weekdays,onCheckedChange:e=>{u("weekdays",!0===e)}}),(0,s.jsx)(ef.J,{htmlFor:"weekdays-open",className:"font-medium cursor-pointer",children:ew.weekdays.label})]})}),i.weekdays&&(0,s.jsx)("div",{className:"p-3 bg-white dark:bg-neutral-900",children:(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,s.jsx)(ef.J,{className:"text-xs text-neutral-500 w-full sm:w-auto",children:"Hours:"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-1 flex-wrap",children:[(0,s.jsx)(D.p,{type:"time",value:d.weekdays.openTime,onChange:e=>{u("weekdays",!0,e.target.value,d.weekdays.closeTime)},className:"w-32 py-1 px-2 text-sm"}),(0,s.jsx)("span",{className:"text-neutral-500 text-xs",children:"to"}),(0,s.jsx)(D.p,{type:"time",value:d.weekdays.closeTime,onChange:e=>{u("weekdays",!0,d.weekdays.openTime,e.target.value)},className:"w-32 py-1 px-2 text-sm"}),(0,s.jsxs)("span",{className:"text-neutral-500 text-xs ml-2",children:[ev(d.weekdays.openTime)," -"," ",ev(d.weekdays.closeTime)]})]})]})})]}),(0,s.jsxs)("div",{className:"rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-3 bg-neutral-100 dark:bg-neutral-800",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(eb.S,{id:"weekend-open",checked:i.weekend,onCheckedChange:e=>{u("weekend",!0===e)}}),(0,s.jsx)(ef.J,{htmlFor:"weekend-open",className:"font-medium cursor-pointer",children:ew.weekend.label})]})}),i.weekend&&(0,s.jsx)("div",{className:"p-3 bg-white dark:bg-neutral-900",children:(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,s.jsx)(ef.J,{className:"text-xs text-neutral-500 w-full sm:w-auto",children:"Hours:"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-1 flex-wrap",children:[(0,s.jsx)(D.p,{type:"time",value:d.weekend.openTime,onChange:e=>{u("weekend",!0,e.target.value,d.weekend.closeTime)},className:"w-32 py-1 px-2 text-sm"}),(0,s.jsx)("span",{className:"text-neutral-500 text-xs",children:"to"}),(0,s.jsx)(D.p,{type:"time",value:d.weekend.closeTime,onChange:e=>{u("weekend",!0,d.weekend.openTime,e.target.value)},className:"w-32 py-1 px-2 text-sm"}),(0,s.jsxs)("span",{className:"text-neutral-500 text-xs ml-2",children:[ev(d.weekend.openTime)," -"," ",ev(d.weekend.closeTime)]})]})]})})]})]})})]}),(0,s.jsx)("div",{className:"flex justify-end p-4 border-t border-neutral-200 dark:border-neutral-700",children:(0,s.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{n({...eN}),o({weekdays:!1,weekend:!1,all:!1}),m({weekdays:{openTime:"09:00",closeTime:"18:00"},weekend:{openTime:"09:00",closeTime:"18:00"},all:{openTime:"09:00",closeTime:"18:00"}}),t({...eN})},className:"text-xs",children:[(0,s.jsx)(ej.A,{className:"h-3 w-3 mr-1"}),"Reset All"]})})]})}function eC(e){let{form:a}=e;return(0,s.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,s.jsx)(ep.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Business Details"}),(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Add your business hours and delivery information"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,s.jsx)(z.zB,{control:a.control,name:"business_hours",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(ep.A,{className:"h-3.5 w-3.5 text-primary"}),"Business Hours"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsx)(ek,{value:a.value,onChange:a.onChange})}),(0,s.jsxs)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1",children:[(0,s.jsx)(R.A,{className:"w-3 h-3"}),"Set your business hours to let customers know when you're open"]}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:a.control,name:"delivery_info",render:e=>{var a;let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(eg.A,{className:"h-3.5 w-3.5 text-primary"}),"Delivery Info"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eg.A,{className:"absolute left-3 top-3 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,s.jsx)(P.T,{placeholder:"e.g., Free delivery within 5km, Delivery charges apply...",...t,className:"min-h-[80px] pl-10 rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm resize-none transition-all duration-200",maxLength:100}),(0,s.jsxs)("div",{className:"absolute right-2 bottom-2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[(null==(a=t.value)?void 0:a.length)||0,"/100"]})]})}),(0,s.jsxs)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1",children:[(0,s.jsx)(R.A,{className:"w-3 h-3"}),"Delivery details shown on your card"]}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})]})]})}var eA=t(75684),e_=t(10488);function eS(e){let{form:a}=e,t=a.watch("phone"),[l,n]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{l&&a.setValue("whatsapp_number",t||"",{shouldValidate:!0,shouldDirty:!0})},[l,t,a]),(0,s.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,s.jsx)(d.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Links"}),(0,s.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Add your social media and communication links"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,s.jsx)(z.zB,{control:a.control,name:"whatsapp_number",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(L.A,{className:"h-3.5 w-3.5 text-primary"}),"WhatsApp Number"]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(L.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,s.jsx)(D.p,{type:"tel",pattern:"[0-9]*",inputMode:"numeric",placeholder:"9876543210",...a,onChange:e=>{let t=e.target.value.replace(/^\+91/,"");(t=t.replace(/\D/g,"")).length<=10&&a.onChange(t)},onKeyDown:e=>{let a=/^[0-9]$/.test(e.key),t=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);a||t||e.preventDefault()},className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",disabled:l})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(eb.S,{id:"copy_whatsapp",checked:l,onCheckedChange:e=>n(!!e),disabled:!t}),(0,s.jsx)(ef.J,{htmlFor:"copy_whatsapp",className:"text-xs font-normal cursor-pointer text-neutral-700 dark:text-neutral-300",children:"Use primary phone"})]})]}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Used to generate wa.me link. Enter 10-digit mobile number."}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),(0,s.jsx)(z.zB,{control:a.control,name:"instagram_url",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(eA.A,{className:"h-3.5 w-3.5 text-primary"}),"Instagram Profile URL"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eA.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,s.jsx)(D.p,{type:"url",placeholder:"https://instagram.com/yourprofile",...a,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})]})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your Instagram profile link"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})]}),(0,s.jsx)(z.zB,{control:a.control,name:"facebook_url",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-1 sm:space-y-2",children:[(0,s.jsxs)(z.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,s.jsx)(e_.A,{className:"h-3.5 w-3.5 text-primary"}),"Facebook Page URL"]}),(0,s.jsx)(z.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(e_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,s.jsx)(D.p,{type:"url",placeholder:"https://facebook.com/yourpage",...a,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})]})}),(0,s.jsx)(z.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your Facebook page link"}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}})]}),(0,s.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm",children:(0,s.jsx)(d.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300",children:"Links Tip"}),(0,s.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed",children:"Including your WhatsApp number and social media links makes it easier for customers to connect with you instantly."})]})]})})]})}var eR=t(85339),eL=t(5196),eD=t(40646),eP=t(54861),ez=t(45964),eU=t.n(ez);let eT=(0,x.createServerReference)("407090e0ad54740b8bc9265b7b903efb29a22d1ef2",x.callServer,void 0,x.findSourceMapURL,"checkSlugAvailability");var eI=t(55747);function eM(e){let{form:a,canGoOnline:t,isSubscriptionHalted:l=!1,onSlugCheckingChange:n}=e,[i,c]=(0,r.useState)(!1),[m,x]=(0,r.useState)(null),[h,p]=(0,r.useState)(""),g=(0,r.useCallback)(()=>{let e=a.getValues();return u.b4.filter(a=>!e[a]||""===String(e[a]).trim())},[a]);(0,r.useEffect)(()=>{console.log("StatusSlugSection - canGoOnline:",t),console.log("Missing fields:",g())},[t,g]);let b=(0,r.useCallback)(async e=>{if(!e||e.length<3||!/^[a-z0-9-]+$/.test(e))return void x(null);if(a.getValues("business_slug")!==e||!0!==m){c(!0),null==n||n(!0);try{let{available:a}=await eT(e);x(a)}catch(e){console.error("Error checking slug availability:",e),x(!1)}finally{c(!1),null==n||n(!1)}}},[a,m,n]),f=(0,r.useMemo)(()=>eU()(b,500),[b]);(0,r.useEffect)(()=>()=>{f.cancel()},[f]),(0,r.useEffect)(()=>{h&&f(h)},[h,f]),(0,r.useEffect)(()=>{!1===m?a.setError("business_slug",{type:"manual",message:"This URL slug is already taken."}):!0===m&&a.clearErrors("business_slug")},[m,a]);let j=(0,r.useRef)(!1);(0,r.useEffect)(()=>{if(!j.current){let e=a.getValues("business_slug");e&&e.length>=3&&p(e),j.current=!0}},[a]);let y={checking:i,available:m};return(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4},className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-lg p-4 sm:p-5 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-xl",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-5 sm:mb-6 pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,s.jsx)("div",{className:"p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary self-start shadow-sm",children:(0,s.jsx)(B.A,{className:"w-5 sm:w-6 h-5 sm:h-6"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-base sm:text-xl font-semibold text-neutral-800 dark:text-neutral-100",children:"Card Status & URL"}),(0,s.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400 mt-1",children:"Configure your card's visibility and unique URL"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,s.jsx)(z.zB,{control:a.control,name:"status",render:e=>{let{field:a}=e;return(0,s.jsxs)(z.eI,{className:"space-y-3 sm:space-y-4",children:[(0,s.jsxs)(z.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2",children:[(0,s.jsx)(B.A,{className:"h-4 w-4 text-primary"}),"Card Status"]}),(0,s.jsxs)(eI.z,{onValueChange:a.onChange,value:a.value,className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5",children:[(0,s.jsx)(ef.J,{htmlFor:"status-offline",className:"cursor-pointer",children:(0,s.jsxs)(o.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},transition:{type:"spring",stiffness:400,damping:17},className:"relative h-full rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-sm\n                      ".concat("offline"===a.value?"border-[--theme-color] bg-[--theme-color]/5 shadow-[--theme-color]/10":"border-neutral-200 dark:border-neutral-700 hover:border-neutral-300 dark:hover:border-neutral-600","\n                    "),children:["offline"===a.value&&(0,s.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-[--theme-color]"}),(0,s.jsxs)("div",{className:"p-4 sm:p-5",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-full ".concat("offline"===a.value?"bg-[--theme-color]/20":"bg-neutral-100 dark:bg-neutral-800"),children:(0,s.jsx)(Y.A,{className:"h-5 w-5 ".concat("offline"===a.value?"text-[--theme-color]":"text-neutral-500 dark:text-neutral-400")})}),(0,s.jsx)("h4",{className:"text-base font-semibold text-neutral-800 dark:text-neutral-100",children:"Offline (Private)"})]}),(0,s.jsx)(eI.C,{value:"offline",id:"status-offline",className:"translate-y-0"})]}),(0,s.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed",children:"Your card is not publicly visible. Use for expired plans or private cards."})]})]})}),(0,s.jsx)(ef.J,{htmlFor:"status-online",className:"".concat(t?"cursor-pointer":"cursor-not-allowed"),children:(0,s.jsxs)(o.P.div,{whileHover:t?{scale:1.02}:{},whileTap:t?{scale:.98}:{},transition:{type:"spring",stiffness:400,damping:17},className:"relative h-full rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-sm\n                      ".concat("online"===a.value?"border-[--theme-color] bg-[--theme-color]/5 shadow-[--theme-color]/10":"border-neutral-200 dark:border-neutral-700","\n                      ").concat(t?"hover:border-neutral-300 dark:hover:border-neutral-600":"opacity-70","\n                    "),children:["online"===a.value&&(0,s.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-[--theme-color]"}),(0,s.jsxs)("div",{className:"p-4 sm:p-5",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-full ".concat("online"===a.value?"bg-[--theme-color]/20":"bg-neutral-100 dark:bg-neutral-800"),children:(0,s.jsx)($.A,{className:"h-5 w-5 ".concat("online"===a.value?"text-[--theme-color]":"text-neutral-500 dark:text-neutral-400")})}),(0,s.jsx)("h4",{className:"text-base font-semibold text-neutral-800 dark:text-neutral-100",children:"Online (Public)"})]}),(0,s.jsx)(eI.C,{value:"online",id:"status-online",disabled:!t,className:"translate-y-0"})]}),(0,s.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed",children:"Make your card accessible via a unique URL. Requires an active plan and unique slug."}),l&&(0,s.jsxs)("div",{className:"mt-3 p-3 rounded-lg bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800/50",children:[(0,s.jsxs)("p",{className:"text-xs text-red-700 dark:text-red-400 flex items-center font-medium mb-2",children:[(0,s.jsx)(eR.A,{className:"w-3.5 h-3.5 mr-1.5 flex-shrink-0"}),"Subscription Paused"]}),(0,s.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400",children:"Your subscription is currently paused. You cannot set your card to online status until you resume your subscription. Please visit the Plan page to resume your subscription."})]}),!t&&!l&&(0,s.jsxs)("div",{className:"mt-3 p-3 rounded-lg bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800/50",children:[(0,s.jsxs)("p",{className:"text-xs text-amber-700 dark:text-amber-400 flex items-center font-medium mb-2",children:[(0,s.jsx)(eR.A,{className:"w-3.5 h-3.5 mr-1.5 flex-shrink-0"}),"Required fields to go online:"]}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-x-3 gap-y-1.5",children:(()=>{let e=g(),a={member_name:(0,s.jsx)(v.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),title:(0,s.jsx)(v.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),business_name:(0,s.jsx)(N.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),phone:(0,s.jsx)(V.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),address_line:(0,s.jsx)(N.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),pincode:(0,s.jsx)(M.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),city:(0,s.jsx)(M.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),state:(0,s.jsx)(M.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),locality:(0,s.jsx)(M.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"})},t={member_name:"Your Name",title:"Your Title",business_name:"Business Name",phone:"Primary Phone",address_line:"Address Line",pincode:"Pincode",city:"City",state:"State",locality:"Locality"};return u.b4.map(r=>{let l=e.includes(r);return(0,s.jsxs)("p",{className:"text-xs flex items-center ".concat(l?"text-red-500 dark:text-red-400 font-medium":"text-green-500 dark:text-green-400"),children:[a[r],t[r],!l&&(0,s.jsx)(eL.A,{className:"w-3 h-3 ml-1 flex-shrink-0"})]},r)})})()})]})]})]})})]}),(0,s.jsx)(z.C5,{className:"text-xs text-red-500"})]})}}),"online"===a.watch("status")&&(0,s.jsx)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,s.jsx)(z.zB,{control:a.control,name:"business_slug",render:e=>{let{field:t}=e;return(0,s.jsxs)(z.eI,{className:"space-y-3 sm:space-y-4 mt-2",children:[(0,s.jsxs)(z.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 text-primary"}),"Unique Card URL"]}),(0,s.jsx)("div",{className:"p-4 sm:p-5 rounded-xl border border-neutral-200 dark:border-neutral-700 bg-neutral-50/50 dark:bg-neutral-800/30 shadow-sm",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-0 rounded-lg overflow-hidden border-2 border-neutral-200 dark:border-neutral-700 focus-within:border-primary/70 focus-within:ring-2 focus-within:ring-primary/20 transition-all duration-200 shadow-sm",children:[(0,s.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 px-3 sm:px-4 py-3 border-r border-neutral-200 dark:border-neutral-700 text-neutral-600 dark:text-neutral-300 text-sm font-medium flex items-center",children:[(0,s.jsx)(B.A,{className:"h-4 w-4 mr-2 text-neutral-500 dark:text-neutral-400"}),"dukancard.in/"]}),(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(z.MJ,{children:(0,s.jsx)(D.p,{placeholder:"your-unique-name",...t,className:"w-full border-0 bg-white dark:bg-black py-3 px-4 text-base focus:ring-0 shadow-none",onChange:e=>{let a=e.target.value.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,"");t.onChange(a),p(a),x(null)}})}),(0,s.jsxs)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:[y.checking&&(0,s.jsx)("div",{className:"bg-neutral-100 dark:bg-neutral-800 p-1 rounded-full",children:(0,s.jsx)(S.A,{className:"w-5 h-5 text-primary animate-spin"})}),!y.checking&&!0===y.available&&(0,s.jsx)("div",{className:"bg-green-50 dark:bg-green-900/30 p-1 rounded-full",children:(0,s.jsx)(eD.A,{className:"w-5 h-5 text-green-500 dark:text-green-400"})}),!y.checking&&!1===y.available&&(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/30 p-1 rounded-full",children:(0,s.jsx)(eP.A,{className:"w-5 h-5 text-red-500 dark:text-red-400"})})]})]})]}),(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("div",{className:"px-3 py-2 rounded-lg bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-sm font-mono flex items-center",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2 text-neutral-500 dark:text-neutral-400"}),"https://dukancard.in/",t.value||"your-unique-name"]})}),(0,s.jsxs)("div",{className:"flex items-center h-6 px-1",children:[y.checking&&(0,s.jsxs)("p",{className:"text-sm text-neutral-500 flex items-center",children:[(0,s.jsx)(S.A,{className:"w-3.5 h-3.5 mr-2 animate-spin"}),"Checking availability..."]}),!y.checking&&!0===y.available&&!a.formState.errors.business_slug&&(0,s.jsxs)("p",{className:"text-sm text-green-600 dark:text-green-400 flex items-center",children:[(0,s.jsx)(eD.A,{className:"w-3.5 h-3.5 mr-2"}),"URL is available!"]}),!y.checking&&!1===y.available&&(0,s.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,s.jsx)(eR.A,{className:"w-3.5 h-3.5 mr-2"}),"URL is already taken."]}),!y.checking&&null===y.available&&(0,s.jsxs)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 flex items-center",children:[(0,s.jsx)(R.A,{className:"w-3.5 h-3.5 mr-2"}),"Lowercase letters, numbers, hyphens only. Min 3 chars."]})]})]})}),(0,s.jsx)(z.C5,{className:"text-sm text-red-500"})]})}})})]}),(0,s.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,s.jsx)("div",{className:"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm",children:(0,s.jsx)(R.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300",children:"Status & URL Tip"}),(0,s.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed",children:"Choose a unique, memorable slug for your card's URL to make it easy to share. Set to 'Online' to make your card publicly accessible."}),(0,s.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-1.5 leading-relaxed",children:"To set your card status to 'Online', you must fill in all required fields: your name, title, business name, primary phone, address line, pincode, city, state, and locality."})]})]})})]})}function eV(e){let{form:a,canGoOnline:t,currentUserPlan:r,onFileSelect:l,isPincodeLoading:n,availableLocalities:i,onPincodeChange:o,isLogoUploading:d,onLogoDelete:c,isSubscriptionHalted:m,onSlugCheckingChange:u}=e;return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)(I,{form:a,onFileSelect:l,isLogoUploading:d,onLogoDelete:c}),(0,s.jsx)(F,{form:a,isPincodeLoading:n,availableLocalities:i,onPincodeChange:o}),(0,s.jsx)(q,{form:a,currentUserPlan:r}),(0,s.jsx)(er,{form:a,currentUserPlan:r}),(0,s.jsx)(eh,{form:a,currentUserPlan:r}),(0,s.jsx)(eC,{form:a}),(0,s.jsx)(eS,{form:a}),(0,s.jsx)(eM,{form:a,slugStatus:{checking:!1,available:null,message:null},canGoOnline:t,isSubscriptionHalted:m,onSlugCheckingChange:u})]})}var eB=t(48061),eE=t(60787),eF=t(53999);let eO={hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.5,delay:.1,ease:"easeOut"}}};function eJ(e){var a,t,r;let{cardData:l,logoUploadStatus:n,localPreviewUrl:i,userPlan:d,cardPreviewRef:c}=e;return(0,s.jsxs)(o.P.div,{variants:eO,initial:"hidden",animate:"visible",style:{width:"100%",display:"flex",flexDirection:"column",alignItems:"center"},ref:c,children:[(0,s.jsx)(eB.A,{data:l,logoUploadStatus:n,localPreviewUrl:i,userPlan:d,isAuthenticated:!0,totalLikes:null!=(a=l.total_likes)?a:0,totalSubscriptions:null!=(t=l.total_subscriptions)?t:0,averageRating:null!=(r=l.average_rating)?r:0}),(0,s.jsx)(eE.A,{businessSlug:l.business_slug||"",businessName:l.business_name||"",ownerName:l.member_name||"",businessAddress:(0,eF.M0)(l),themeColor:l.theme_color||"#F59E0B",className:"mt-6"})]})}var eq=t(95733);function eY(e){let{isOpen:a,isDeleting:t,onClose:r,onConfirm:l}=e;return(0,s.jsx)(eo.lG,{open:a,onOpenChange:e=>!e&&!t&&r(),children:(0,s.jsxs)(eo.Cf,{className:"sm:max-w-md",children:[(0,s.jsxs)(eo.c7,{children:[(0,s.jsxs)(eo.L3,{className:"flex items-center gap-2 text-red-600 dark:text-red-500",children:[(0,s.jsx)(_.A,{className:"h-5 w-5"}),"Delete Logo"]}),(0,s.jsx)(eo.rr,{children:"Are you sure you want to delete your logo? This action cannot be undone."})]}),(0,s.jsx)("div",{className:"py-4",children:(0,s.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Your logo will be permanently removed from your business card and from our storage."})}),(0,s.jsxs)(eo.Es,{children:[(0,s.jsx)(c.$,{variant:"outline",onClick:r,disabled:t,className:"border-neutral-200 dark:border-neutral-700",children:"Cancel"}),(0,s.jsx)(c.$,{variant:"destructive",onClick:l,disabled:t,className:"bg-red-600 hover:bg-red-700 text-white",children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete Logo"})]})]})})}function e$(e){let{form:a,isPending:t,isLogoUploading:r,isCheckingSlug:l=!1,isPincodeLoading:n=!1,onSave:i}=e;return(0,s.jsxs)("div",{className:"mt-8 flex flex-col space-y-4",children:[(0,s.jsx)("div",{className:"h-px w-full bg-neutral-200 dark:bg-neutral-800"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("div",{className:"text-sm text-neutral-500 dark:text-neutral-400",children:a.formState.isDirty?(0,s.jsxs)("span",{className:"flex items-center text-amber-600 dark:text-amber-400",children:[(0,s.jsxs)("span",{className:"relative flex h-2 w-2 mr-2",children:[(0,s.jsx)("span",{className:"animate-ping absolute inline-flex h-full w-full rounded-full bg-amber-400 opacity-75"}),(0,s.jsx)("span",{className:"relative inline-flex rounded-full h-2 w-2 bg-amber-500"})]}),"You have unsaved changes"]}):(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"relative flex h-2 w-2 mr-2",children:(0,s.jsx)("span",{className:"relative inline-flex rounded-full h-2 w-2 bg-green-500"})}),"All changes saved"]})}),(0,s.jsxs)(c.$,{type:"button",disabled:t||r||l||n||Object.keys(a.formState.errors).length>0,onClick:i,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] disabled:opacity-50 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md",children:[t||r||l||n?(0,s.jsx)(S.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,t?"Saving...":r?"Uploading...":l?"Checking URL...":n?"Loading Location...":"Save Changes"]})]})]})}var eG=t(60760),eW=t(1243),eH=t(4229);function eK(e){let{form:a,isPending:t,isLogoUploading:l,isCheckingSlug:n=!1,isPincodeLoading:i=!1,onSave:d,onDiscard:m}=e,[u,x]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=setTimeout(()=>{x(!0)},5e3);return()=>clearTimeout(e)},[]),(0,r.useEffect)(()=>{if(u&&a.formState.isDirty&&!h){let e=setTimeout(()=>{a.formState.isDirty&&p(!0)},500);return()=>clearTimeout(e)}},[a.formState.isDirty,u,h]),(0,r.useEffect)(()=>{a.formState.isDirty||p(!1)},[a.formState.isDirty]);let g=u&&h&&a.formState.isDirty;return(0,s.jsx)(eG.N,{children:g&&(0,s.jsx)(o.P.div,{initial:{opacity:0,y:-100},animate:{opacity:1,y:0},exit:{opacity:0,y:-100},transition:{duration:.3,ease:"easeOut"},className:"fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[calc(100%-2rem)] max-w-md",children:(0,s.jsx)("div",{className:"bg-white dark:bg-neutral-900 border border-amber-200 dark:border-amber-800 rounded-xl shadow-xl backdrop-blur-sm p-4 ring-1 ring-black/5 dark:ring-white/10",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(eW.A,{className:"h-5 w-5 text-amber-500"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-neutral-900 dark:text-neutral-100",children:"You have unsaved changes"}),(0,s.jsx)("p",{className:"text-xs text-neutral-600 dark:text-neutral-400",children:"Don't forget to save your changes before leaving"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(c.$,{size:"sm",variant:"outline",onClick:m,disabled:t||l||n||i,className:"text-xs px-2 py-1 h-7",children:[(0,s.jsx)(ea.A,{className:"h-3 w-3 mr-1"}),"Discard"]}),(0,s.jsx)(c.$,{size:"sm",onClick:d,disabled:t||l||n||i||Object.keys(a.formState.errors).length>0,className:"text-xs px-3 py-1.5 h-8 bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] hover:from-[var(--brand-gold-light)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] font-medium shadow-sm hover:shadow-md transition-all duration-200 border-0",children:t||l||n||i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.A,{className:"h-3 w-3 mr-1.5 animate-spin"}),t?"Saving...":l?"Uploading...":n?"Checking...":i?"Loading...":"Saving..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eH.A,{className:"h-3 w-3 mr-1.5"}),"Save"]})})]})]})})})})}function eZ(e){let{initialData:a,currentUserPlan:t,subscriptionStatus:x}=e,[v,y]=(0,r.useState)(!1),[N,w]=(0,r.useState)(()=>{let e={...u.kc,...a};return{...e,member_name:e.member_name||"",title:e.title||"",business_name:e.business_name||"",status:e.status||"offline"}}),[k,C]=(0,r.useState)(()=>{let e={...u.kc,...a};return{...e,member_name:e.member_name||"",title:e.title||"",business_name:e.business_name||"",status:e.status||"offline"}}),[A,_]=(0,r.useTransition)(),[S,R]=(0,r.useState)(!1),[L,D]=(0,r.useState)(!1),P=(0,r.useRef)(null),U=(0,r.useMemo)(()=>{var e;return{...u.kc,...a,locality:null!=(e=null==a?void 0:a.locality)?e:"",custom_branding:{...u.kc.custom_branding,...(null==a?void 0:a.custom_branding)||{}},custom_ads:{...u.kc.custom_ads,...(null==a?void 0:a.custom_ads)||{}}}},[a]),T=(0,l.mN)({resolver:(0,n.u)(u.Mo),defaultValues:U,mode:"onChange",resetOptions:{keepDirtyValues:!1,keepErrors:!1}}),I=T.watch(),M="halted"===x,V=!M&&u.b4.every(e=>I[e]&&""!==String(I[e]).trim()),{isPincodeLoading:B,availableLocalities:E,handlePincodeChange:F}=function(e){let{form:a,initialPincode:t,initialLocality:s}=e,[l,n]=(0,r.useState)(!1),[o,d]=(0,r.useState)([]),c=(0,r.useCallback)(async e=>{if(6!==e.length)return;n(!0),d([]),a.setValue("locality",""),a.setValue("city",""),a.setValue("state","");let t=await (0,j.t)(e);n(!1),t.error?i.oR.error(t.error):t.city&&t.state&&t.localities&&(a.setValue("city",t.city,{shouldValidate:!0}),a.setValue("state",t.state,{shouldValidate:!0}),d(t.localities),1===t.localities.length&&a.setValue("locality",t.localities[0],{shouldValidate:!0,shouldDirty:!1}),i.oR.success("City and State auto-filled. Please select your locality."))},[a]);return(0,r.useEffect)(()=>{t&&6===t.length&&(async e=>{n(!0),d([]);try{let t=await (0,j.t)(e);if(t.error)i.oR.error("Failed to fetch details for pincode ".concat(e,": ").concat(t.error)),d([]);else if(t.city&&t.state&&t.localities)if(a.setValue("city",t.city,{shouldValidate:!0}),a.setValue("state",t.state,{shouldValidate:!0}),d(t.localities),s){let r=s.trim().toLowerCase();t.localities.some(e=>e.trim().toLowerCase()===r)||(i.oR.warning('Saved locality "'.concat(s,'" is not valid for pincode ').concat(e,". Please re-select.")),a.setValue("locality","",{shouldValidate:!0,shouldDirty:!1}))}else 1===t.localities.length&&a.setValue("locality",t.localities[0],{shouldValidate:!0,shouldDirty:!1});else d([]),i.oR.warning("No localities found for pincode ".concat(e,".")),s&&a.setValue("locality","",{shouldValidate:!0,shouldDirty:!1})}catch(e){console.error("Error fetching pincode details:",e),i.oR.error("An unexpected error occurred while fetching pincode details."),d([]),s&&a.setValue("locality","",{shouldValidate:!0,shouldDirty:!1})}finally{n(!1)}})(t)},[t,s,a]),{isPincodeLoading:l,availableLocalities:o,handlePincodeChange:c}}({form:T,initialPincode:null==a?void 0:a.pincode,initialLocality:null==a?void 0:a.locality}),{logoUploadStatus:O,localPreviewUrl:J,isLogoUploading:q,imageToCrop:Y,onFileSelect:$,handleCropComplete:G,handleCropDialogClose:W,handleLogoDelete:H,logoErrorDisplay:K,isDeleteDialogOpen:Z,isDeleting:Q,closeDeleteDialog:X,confirmLogoDelete:ee}=function(e){let{form:a,initialLogoUrl:t="",onUpdateCardData:s}=e,[l,n]=(0,r.useState)("idle"),[o,d]=(0,r.useState)(null),[c,m]=(0,r.useState)(null),[u,x]=(0,r.useTransition)(),[h,j]=(0,r.useState)(null),[v,y]=(0,r.useState)(null),N=async e=>{n("uploading"),d(null),x(async()=>{try{let r=await (0,f.compressImageUltraAggressiveClient)(e,{maxDimension:500,targetSizeKB:60}),l=new File([r.blob],e.name,{type:r.blob.type}),o=new FormData;o.append("logoFile",l);let u=await p(o);if(u.success&&u.url){let e=u.url;a.setValue("logo_url",e,{shouldDirty:!0,shouldTouch:!0}),s({logo_url:e}),n("success"),m(null),c&&URL.revokeObjectURL(c),i.oR.success("Logo uploaded successfully!");try{let t=await g(e);t.success||i.oR.error("Logo uploaded, but failed to save URL: ".concat(t.error)),t.success&&(s({logo_url:e}),a.reset({...a.getValues(),logo_url:e}))}catch(e){console.error("Error saving logo URL:",e),i.oR.error("Error saving logo URL after upload.")}}else{n("error");let e=u.error||"Failed to upload logo.";d(e),m(null),c&&URL.revokeObjectURL(c),e.includes("File size must be less than 15MB")?i.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}):e.includes("Invalid file type")?i.oR.error("Invalid file type",{description:"Please select a JPG, PNG, WebP, or GIF image"}):i.oR.error("Upload failed",{description:e}),a.setValue("logo_url",t||"",{shouldDirty:!1})}}catch(e){console.error("Image compression failed:",e),n("error"),d("Failed to process image. Please try a different image."),i.oR.error("Image processing failed",{description:"Please try a different image or reduce the file size."})}})},[w,k]=(0,r.useState)(!1),[C,A]=(0,r.useState)(!1),_=()=>{a.getValues("logo_url")&&k(!0)},S=async()=>{A(!0),n("uploading"),d(null),x(async()=>{try{let e=await b();if(e.success){a.setValue("logo_url",null,{shouldDirty:!0,shouldTouch:!0}),s({logo_url:null}),n("idle"),m(null),c&&URL.revokeObjectURL(c);let e=document.querySelector('input[type="file"]');e&&(e.value=""),i.oR.success("Logo deleted successfully!"),a.reset({...a.getValues(),logo_url:null})}else n("error"),d(e.error||"Failed to delete logo."),i.oR.error(e.error||"Failed to delete logo.")}catch(e){console.error("Error deleting logo:",e),n("error"),d("An unexpected error occurred while deleting the logo."),i.oR.error("An unexpected error occurred while deleting the logo.")}finally{A(!1),k(!1)}})};return{logoUploadStatus:l,logoUploadError:o,localPreviewUrl:c,isLogoUploading:u,imageToCrop:h,onFileSelect:e=>{if(c&&(URL.revokeObjectURL(c),m(null)),e){if(e.size>0xf00000){i.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}),a.setValue("logo_url",null,{shouldDirty:!0}),s({logo_url:null}),n("idle"),d("File size must be less than 15MB."),m(null);return}y(e);let t=new FileReader;t.onloadend=()=>{j(t.result)},t.readAsDataURL(e)}else a.setValue("logo_url",null,{shouldDirty:!0}),s({logo_url:null}),n("idle"),d(null),m(null)},handleLogoUpload:N,handleCropComplete:e=>{if(j(null),e&&v){let a=new File([e],v.name,{type:"image/webp"});m(URL.createObjectURL(a)),N(a)}else{console.log("Cropping cancelled or failed."),y(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")}},handleCropDialogClose:()=>{j(null),y(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")},handleLogoDelete:_,logoErrorDisplay:"error"===l&&o?o:null,isDeleteDialogOpen:w,isDeleting:C,openDeleteDialog:_,closeDeleteDialog:()=>{C||k(!1)},confirmLogoDelete:S}}({form:T,initialLogoUrl:(null==a?void 0:a.logo_url)||"",onUpdateCardData:e=>w(a=>({...a,...e}))});(0,r.useEffect)(()=>{y(!0)},[]),(0,r.useEffect)(()=>{if(!v)return;let e=setTimeout(()=>{R(!0),T.reset(U,{keepDirtyValues:!1,keepErrors:!1,keepDirty:!1,keepIsSubmitted:!1}),setTimeout(()=>R(!1),100)},100);return()=>clearTimeout(e)},[v,T,U]),(0,r.useEffect)(()=>{let e=T.watch(e=>{S||w(a=>({...a,...e}))});return()=>{e.unsubscribe(),J&&URL.revokeObjectURL(J)}},[T,J,S]);let ea=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],a=T.getValues();return(e?u.b4:u.Uc).filter(e=>!a[e]||""===String(a[e]).trim())},et=e=>({member_name:"Your Name",title:"Your Title",business_name:"Business Name",phone:"Primary Phone",address_line:"Address Line",pincode:"Pincode",city:"City",state:"State",locality:"Locality",contact_email:"Contact Email",business_category:"Business Category"})[e]||e,es=e=>{if(Object.keys(T.formState.errors).length>0){!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"business-card-form";setTimeout(()=>{let a=document.getElementById(e);if(!a)return;let t=a.querySelectorAll('[role="alert"]');if(!t||0===t.length){let e=a.querySelectorAll('.error-field, [aria-invalid="true"]');return e&&e.length>0?void m(e[0]):void 0}m(t[0])},100)}("business-card-form"),i.oR.error((0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"Cannot save business card"}),(0,s.jsx)("p",{className:"text-sm mb-1",children:"Please fix the validation errors"})]}));return}let a=ea(!1);if(a.length>0){let e=a.map(et);i.oR.error((0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"Cannot save business card"}),(0,s.jsx)("p",{className:"text-sm mb-1",children:"Please fill in the following required fields:"}),(0,s.jsx)("ul",{className:"text-sm list-disc pl-4",children:e.map((e,a)=>(0,s.jsx)("li",{children:e},a))})]}));let t=a[0];T.setFocus(t);return}if("online"===e.status&&M)return void i.oR.error((0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"Cannot set card to online status"}),(0,s.jsx)("p",{className:"text-sm mb-1",children:"Your subscription is currently paused. Please resume your subscription to set your card online."})]}));if("online"===e.status&&!V&&!M){let e=ea(!0),a=e.map(et);i.oR.error((0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"Cannot set card to online status"}),(0,s.jsx)("p",{className:"text-sm mb-1",children:"Please fill in the following required fields:"}),(0,s.jsx)("ul",{className:"text-sm list-disc pl-4",children:a.map((e,a)=>(0,s.jsx)("li",{children:e},a))})]}));let t=e[0];T.setFocus(t);return}_(async()=>{let a=await h(e);a.success&&a.data?(i.oR.success("Business card updated successfully!"),w(a.data),C(a.data),R(!0),T.reset(a.data,{keepDirtyValues:!1,keepErrors:!1,keepDirty:!1,keepIsSubmitted:!1}),setTimeout(()=>R(!1),100)):i.oR.error(a.error||"Failed to update business card.")})},er=(0,r.useCallback)(e=>{D(e)},[]),el=async()=>{if(A||L||B)return;if(Object.keys(T.formState.errors).length>0)return void i.oR.error("Please fix the form errors before saving");let e=T.getValues(),a=u.Mo.safeParse(e);if(!a.success){console.error("Validation failed:",a.error),i.oR.error("Please fix the form errors before saving");return}es(a.data)};return v?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eK,{form:T,isPending:A,isLogoUploading:q,isCheckingSlug:L,isPincodeLoading:B,onSave:el,onDiscard:()=>{R(!0),T.reset(k,{keepDirtyValues:!1,keepErrors:!1,keepDirty:!1,keepIsSubmitted:!1}),w(k),setTimeout(()=>R(!1),100),i.oR.info("Changes discarded")}}),(0,s.jsx)(z.lV,{...T,children:(0,s.jsxs)("form",{onSubmit:e=>e.preventDefault(),className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-8 lg:hidden",children:[(0,s.jsx)(eJ,{cardData:N,logoUploadStatus:O,localPreviewUrl:J,userPlan:"trial"===t?"basic":null!=t?t:void 0,cardPreviewRef:P}),(0,s.jsxs)(o.P.div,{initial:"hidden",animate:"visible",className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6",children:[(0,s.jsx)("div",{className:"p-3 rounded-xl bg-muted hidden sm:block",children:(0,s.jsx)(d.A,{className:"w-6 h-6 text-foreground"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Edit Business Card"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:"Customize your digital business card below. Changes reflect in real-time."})]}),(0,s.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e=T.getValues("business_slug");e?window.open("/".concat(e),"_blank"):i.oR.error("Please set a business slug first.")},disabled:!T.getValues("business_slug"),className:"flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),"View Public Card"]})]}),(0,s.jsx)(eV,{form:T,canGoOnline:V,currentUserPlan:t,onFileSelect:$,isPincodeLoading:B,availableLocalities:E,onPincodeChange:F,isLogoUploading:q,onLogoDelete:H,isSubscriptionHalted:M,onSlugCheckingChange:er}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 sm:gap-3 mt-6",children:[K&&(0,s.jsx)("p",{className:"text-xs text-red-500 dark:text-red-400 text-right",children:K}),(0,s.jsx)(e$,{form:T,isPending:A,isLogoUploading:q,isCheckingSlug:L,isPincodeLoading:B,onSave:el})]})]})]}),(0,s.jsxs)("div",{className:"hidden lg:block space-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6",children:[(0,s.jsx)("div",{className:"p-3 rounded-xl bg-muted",children:(0,s.jsx)(d.A,{className:"w-6 h-6 text-foreground"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Edit Business Card"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:"Customize your digital business card below. Changes reflect in real-time."})]}),(0,s.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e=T.getValues("business_slug");e?window.open("/".concat(e),"_blank"):i.oR.error("Please set a business slug first.")},disabled:!T.getValues("business_slug"),className:"flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),"View Public Card"]})]}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8 sm:gap-12 pb-12 relative",children:[(0,s.jsx)("div",{className:"flex-[1] w-full lg:w-1/2 lg:sticky lg:top-24 self-start",children:(0,s.jsx)(eJ,{cardData:N,logoUploadStatus:O,localPreviewUrl:J,userPlan:"trial"===t?"basic":null!=t?t:void 0,cardPreviewRef:P})}),(0,s.jsxs)(o.P.div,{initial:"hidden",animate:"visible",className:"flex-[2] space-y-6",style:{flex:2,width:"100%",position:"sticky",top:"6rem",alignSelf:"flex-start"},children:[(0,s.jsx)(eV,{form:T,canGoOnline:V,currentUserPlan:t,onFileSelect:$,isPincodeLoading:B,availableLocalities:E,onPincodeChange:F,isLogoUploading:q,onLogoDelete:H,isSubscriptionHalted:M,onSlugCheckingChange:er}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 sm:gap-3 mt-6",children:[K&&(0,s.jsx)("p",{className:"text-xs text-red-500 dark:text-red-400 text-right",children:K}),(0,s.jsx)(e$,{form:T,isPending:A,isLogoUploading:q,isCheckingSlug:L,isPincodeLoading:B,onSave:el})]})]})]})]})]})}),(0,s.jsx)(eq.A,{isOpen:!!Y,imgSrc:Y,onClose:W,onCropComplete:G}),(0,s.jsx)(eY,{isOpen:Z,isDeleting:Q,onClose:X,onConfirm:ee})]}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--brand-gold)] mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading editor..."})]})})}},76037:(e,a,t)=>{"use strict";t.d(a,{w:()=>n});var s=t(95155);t(12115);var r=t(87489),l=t(53999);function n(e){let{className:a,orientation:t="horizontal",decorative:n=!0,...i}=e;return(0,s.jsx)(r.b,{"data-slot":"separator-root",decorative:n,orientation:t,className:(0,l.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...i})}},82714:(e,a,t)=>{"use strict";t.d(a,{J:()=>n});var s=t(95155);t(12115);var r=t(40968),l=t(53999);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},88482:(e,a,t)=>{"use strict";t.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>n,wL:()=>c});var s=t(95155);t(12115);var r=t(53999);function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",a),...t})}},90088:(e,a,t)=>{"use strict";t.d(a,{d:()=>n});var s=t(95155);t(12115);var r=t(4884),l=t(53999);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},90196:(e,a,t)=>{"use strict";async function s(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{format:t="webp",targetSizeKB:s=100,maxDimension:r=800,quality:l=.8}=a;return new Promise((a,n)=>{let i=new Image;i.onload=()=>{try{let o=document.createElement("canvas"),d=o.getContext("2d");if(!d)return void n(Error("Could not get canvas context"));let{width:c,height:m}=i;(c>r||m>r)&&(c>m?(m=m*r/c,c=r):(c=c*r/m,m=r)),o.width=c,o.height=m,d.drawImage(i,0,0,c,m);let u=l,x=0,h=()=>{o.toBlob(t=>{if(!t)return void n(Error("Failed to create blob"));let r=t.size/1024;if(r<=s||x>=5||u<=.1){let s=e.size/t.size;a({blob:t,finalSizeKB:Math.round(100*r)/100,compressionRatio:Math.round(100*s)/100,dimensions:{width:c,height:m}})}else x++,u=Math.max(.1,u-.15),h()},"image/".concat(t),u)};h()}catch(e){n(e)}},i.onerror=()=>n(Error("Failed to load image")),i.src=URL.createObjectURL(e)})}async function r(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.size/1048576,r=100,l=800,n=.7;return t<=2?(n=.7,l=800,r=90):t<=5?(n=.55,l=700,r=80):t<=10?(n=.45,l=600,r=70):(n=.35,l=550,r=60),s(e,{...a,targetSizeKB:a.targetSizeKB||r,maxDimension:a.maxDimension||l,quality:a.quality||n})}async function l(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return s(e,{targetSizeKB:50,maxDimension:400,quality:.7,...a})}t.d(a,{compressImageUltraAggressiveClient:()=>r,q:()=>l})},95139:(e,a,t)=>{"use strict";t.d(a,{S:()=>i});var s=t(95155);t(12115);var r=t(76981),l=t(5196),n=t(53999);function i(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(l.A,{className:"size-3.5"})})})}},95733:(e,a,t)=>{"use strict";t.d(a,{A:()=>u});var s=t(95155),r=t(12115),l=t(3159),n=t(97168),i=t(99840),o=t(51154),d=t(2219);let c=e=>new Promise((a,t)=>{let s=new Image;s.addEventListener("load",()=>a(s)),s.addEventListener("error",e=>t(e)),s.setAttribute("crossOrigin","anonymous"),s.src=e});async function m(e,a){let t=await c(e),s=document.createElement("canvas"),r=s.getContext("2d");if(!r)return null;let l=t.naturalWidth/t.width,n=t.naturalHeight/t.height,i=window.devicePixelRatio||1;return s.width=a.width*i*l,s.height=a.height*i*n,r.setTransform(i,0,0,i,0,0),r.imageSmoothingQuality="high",r.drawImage(t,a.x*l,a.y*n,a.width*l,a.height*n,0,0,a.width*l,a.height*n),new Promise(e=>{s.toBlob(e,"image/png")})}function u(e){let{imgSrc:a,onCropComplete:t,onClose:c,isOpen:u}=e,[x,h]=(0,r.useState)({x:0,y:0}),[p,g]=(0,r.useState)(1),[b,f]=(0,r.useState)(null),[j,v]=(0,r.useState)(!1),y=(0,r.useCallback)((e,a)=>{f(a)},[]),N=async()=>{if(!a||!b){console.warn("Image source or crop area not available."),t(null);return}v(!0);try{let e=await m(a,b);t(e)}catch(e){console.error("Error cropping image:",e),t(null)}finally{v(!1)}};return(0,r.useEffect)(()=>{u&&g(1)},[u]),(0,s.jsx)(i.lG,{open:u,onOpenChange:e=>!e&&c(),children:(0,s.jsxs)(i.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsx)(i.c7,{children:(0,s.jsx)(i.L3,{children:"Crop Your Logo"})}),(0,s.jsx)("div",{className:"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800",children:a?(0,s.jsx)(l.Ay,{image:a,crop:x,zoom:p,aspect:1,cropShape:"round",showGrid:!1,onCropChange:h,onZoomChange:g,onCropComplete:y}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("p",{children:"Loading image..."})})}),(0,s.jsx)("div",{className:"px-4 pb-4",children:(0,s.jsx)(d.A,{min:1,max:3,step:.1,value:[p],onValueChange:e=>g(e[0]),className:"w-full","aria-label":"Zoom slider"})}),(0,s.jsxs)(i.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:c,disabled:j,children:"Cancel"}),(0,s.jsxs)(n.$,{onClick:N,disabled:j,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[j?(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Crop Image"]})]})]})})}},95784:(e,a,t)=>{"use strict";t.d(a,{TR:()=>h,bq:()=>u,eb:()=>p,gC:()=>x,l6:()=>d,s3:()=>c,yv:()=>m});var s=t(95155);t(12115);var r=t(43433),l=t(66474),n=t(5196),i=t(47863),o=t(53999);function d(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.YJ,{"data-slot":"select-group",...a})}function m(e){let{...a}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...a})}function u(e){let{className:a,size:t="default",children:n,...i}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...i,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:a,children:t,position:l="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:l,...n,children:[(0,s.jsx)(g,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(b,{})]})})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(r.JU,{"data-slot":"select-label",className:(0,o.cn)("text-muted-foreground px-2 py-1.5 text-xs",a),...t})}function p(e){let{className:a,children:t,...l}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...l,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function g(e){let{className:a,...t}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}function b(e){let{className:a,...t}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}},99474:(e,a,t)=>{"use strict";t.d(a,{T:()=>l});var s=t(95155);t(12115);var r=t(53999);function l(e){let{className:a,...t}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...t})}},99828:(e,a,t)=>{"use strict";t.d(a,{A:()=>u});var s=t(95155),r=t(12115),l=t(10081),n=t(5196),i=t(53999),o=t(97168),d=t(44895),c=t(60823),m=t(88945);function u(e){var a;let{value:t,onChange:u,placeholder:x="Select a category...",className:h,disabled:p=!1}=e,[g,b]=r.useState(!1);return(0,s.jsxs)(c.AM,{open:g,onOpenChange:b,children:[(0,s.jsx)(c.Wv,{asChild:!0,children:(0,s.jsxs)(o.$,{variant:"outline",role:"combobox","aria-expanded":g,className:(0,i.cn)("w-full justify-between h-12 text-sm",h),disabled:p,children:[t?null==(a=m.qW.find(e=>e.name===t))?void 0:a.name:x,(0,s.jsx)(l.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,s.jsx)(c.hl,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:(0,s.jsxs)(d.uB,{children:[(0,s.jsx)(d.G7,{placeholder:"Search category...",className:"h-9 border-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"}),(0,s.jsxs)(d.oI,{className:"max-h-[300px]",children:[(0,s.jsx)(d.xL,{children:"No category found."}),(0,s.jsx)(d.L$,{children:m.qW.map(e=>(0,s.jsxs)(d.h_,{value:e.name,onSelect:e=>{u(e===t?"":e),b(!1)},children:[(0,s.jsx)(e.icon,{className:"mr-2 h-4 w-4"}),e.name,(0,s.jsx)(n.A,{className:(0,i.cn)("ml-auto h-4 w-4",t===e.name?"opacity-100":"opacity-0")})]},e.slug))})]})]})})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8695,2290,6671,375,5152,7665,1884,6215,6766,781,6199,4577,221,4081,864,9417,346,1602,9635,7203,365,4490,4927,8441,1684,7358],()=>a(58251)),_N_E=e.O()}]);