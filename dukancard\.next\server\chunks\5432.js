"use strict";exports.id=5432,exports.ids=[5432],exports.modules={5432:(s,e,t)=>{t.a(s,async(s,i)=>{try{t.d(e,{handleSubscriptionHalted:()=>c});var a=t(32032),n=t(65193),r=t(28485),o=t(94230),l=s([r]);async function c(s,e,t){let i=null;try{let e=s.payload.subscription;if(!e||!e.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let l=e.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription halted: ${l}`);let c=(0,n.extractWebhookTimestamp)(s);i={subscriptionId:l,eventType:"subscription.halted",eventId:t||`halted_${l}_${Date.now()}`,payload:s,webhookTimestamp:c};let p=await r.webhookProcessor.processWebhookEvent(i);if(!p.shouldProcess)return{success:p.success,message:p.message};let u=await (0,a.createClient)(),{data:d,error:_}=await u.from("payment_subscriptions").select("subscription_status, plan_id, plan_cycle, business_profile_id").eq("razorpay_subscription_id",l).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${l}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!d)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${l}, skipping halted processing`),{success:!0,message:"No subscription found to halt"};if("free"===d.plan_id||(0,n.isTerminalStatus)(d.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription ${l} is in terminal state (plan_id: ${d.plan_id}, status: ${d.subscription_status}), skipping halted update`),{success:!0,message:"Subscription is in terminal state, skipping halted update"};console.log(`[RAZORPAY_WEBHOOK] Halting subscription ${l}, preserving original plan ${d.plan_id}/${d.plan_cycle}`);let b=new Date().toISOString(),g=await (0,o.M)({subscription_id:l,business_profile_id:d.business_profile_id,subscription_status:"halted",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",original_plan_id:d.plan_id,original_plan_cycle:d.plan_cycle,subscription_paused_at:b,updated_at:b}});if(g.success)return await r.webhookProcessor.markEventAsSuccess(i.eventId,"Subscription halted and temporarily downgraded to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully halted subscription ${l} with original plan preserved`),{success:!0,message:"Subscription halted and temporarily downgraded to free plan"};return await r.webhookProcessor.markEventAsFailed(i.eventId,g.message),console.error(`[RAZORPAY_WEBHOOK] Failed to halt subscription ${l}:`,g.message),g}catch(s){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription halted:",s),{success:!1,message:`Error handling subscription halted: ${s instanceof Error?s.message:String(s)}`}}}r=(l.then?(await l)():l)[0],i()}catch(s){i(s)}})}};