"use strict";exports.id=7817,exports.ids=[7817],exports.modules={17313:(e,r,s)=>{s.d(r,{A:()=>t});let t=(0,s(62688).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},25334:(e,r,s)=>{s.d(r,{A:()=>t});let t=(0,s(62688).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},32293:(e,r,s)=>{s.d(r,{J3:()=>l,PU:()=>b,Q$:()=>n,WK:()=>o,Wr:()=>c,e8:()=>d,h6:()=>u});var t=s(91199);s(42087);var a=s(76881),i=s(7944);async function n(e){let r=await (0,a.createClient)(),{data:{user:s},error:t}=await r.auth.getUser();if(t||!s)return{success:!1,error:"User not authenticated."};if(s.id===e)return{success:!1,error:"You cannot subscribe to your own business card."};let{data:n}=await r.from("business_profiles").select("id").eq("id",s.id).maybeSingle();try{let{error:t}=await r.from("subscriptions").insert({user_id:s.id,business_profile_id:e});if(t){if("23505"===t.code)return console.log(`User ${s.id} already subscribed to business ${e}.`),{success:!0};throw console.error("Error inserting subscription:",t),Error(t.message)}let{data:a}=await r.from("business_profiles").select("business_slug").eq("id",e).single();return a?.business_slug&&(0,i.revalidatePath)(`/${a.business_slug}`),(0,i.revalidatePath)("/dashboard/customer"),n&&((0,i.revalidatePath)("/dashboard/business"),(0,i.revalidatePath)("/dashboard/business/subscriptions")),(0,i.revalidatePath)("/dashboard/business/activities"),{success:!0}}catch(e){return console.error("Unexpected error in subscribeToBusiness:",e),{success:!1,error:e instanceof Error?e.message:"An unexpected error occurred."}}}async function o(e){let r=await (0,a.createClient)(),{data:{user:s},error:t}=await r.auth.getUser();if(t||!s)return{success:!1,error:"User not authenticated."};if(s.id===e)return{success:!1,error:"You cannot unsubscribe from your own business card."};let{data:n}=await r.from("business_profiles").select("id").eq("id",s.id).maybeSingle();try{let{error:t}=await r.from("subscriptions").delete().match({user_id:s.id,business_profile_id:e});if(t)throw console.error("Error deleting subscription:",t),Error(t.message);let{data:a}=await r.from("business_profiles").select("business_slug").eq("id",e).single();return a?.business_slug&&(0,i.revalidatePath)(`/${a.business_slug}`),(0,i.revalidatePath)("/dashboard/customer"),n&&((0,i.revalidatePath)("/dashboard/business"),(0,i.revalidatePath)("/dashboard/business/subscriptions")),(0,i.revalidatePath)("/dashboard/business/activities"),{success:!0}}catch(e){return console.error("Unexpected error in unsubscribeFromBusiness:",e),{success:!1,error:e instanceof Error?e.message:"An unexpected error occurred."}}}async function u(e,r,s){let t=await (0,a.createClient)(),{data:{user:n},error:o}=await t.auth.getUser();if(o||!n)return{success:!1,error:"User not authenticated."};if(n.id===e)return{success:!1,error:"You cannot review your own business card."};if(r<1||r>5)return{success:!1,error:"Rating must be between 1 and 5."};try{let{error:a}=await t.from("ratings_reviews").upsert({user_id:n.id,business_profile_id:e,rating:r,review_text:s,updated_at:new Date().toISOString()},{onConflict:"user_id, business_profile_id"});if(a)throw console.error("Error submitting review:",a),Error(a.message);let{data:o}=await t.from("business_profiles").select("business_slug").eq("id",e).single();return o?.business_slug&&(0,i.revalidatePath)(`/${o.business_slug}`),(0,i.revalidatePath)("/dashboard/customer"),(0,i.revalidatePath)("/dashboard/business/activities"),{success:!0}}catch(e){return console.error("Unexpected error in submitReview:",e),{success:!1,error:e instanceof Error?e.message:"An unexpected error occurred."}}}async function c(e){let r=await (0,a.createClient)(),{data:{user:s},error:t}=await r.auth.getUser();if(t||!s)return{success:!1,error:"User not authenticated."};try{let{error:t}=await r.from("ratings_reviews").delete().match({user_id:s.id,business_profile_id:e});if(t)throw console.error("Error deleting review:",t),Error(t.message);let{data:a}=await r.from("business_profiles").select("business_slug").eq("id",e).single();return a?.business_slug&&(0,i.revalidatePath)(`/${a.business_slug}`),(0,i.revalidatePath)("/dashboard/customer"),(0,i.revalidatePath)("/dashboard/business/activities"),{success:!0}}catch(e){return console.error("Unexpected error in deleteReview:",e),{success:!1,error:e instanceof Error?e.message:"An unexpected error occurred."}}}async function d(e){let r=await (0,a.createClient)(),{data:{user:s},error:t}=await r.auth.getUser();if(t||!s)return{success:!1,error:"User not authenticated."};if(s.id===e)return{success:!1,error:"You cannot like your own business card."};try{let{error:t}=await r.from("likes").insert({user_id:s.id,business_profile_id:e});if(t){if("23505"===t.code)return console.log(`User ${s.id} already liked business ${e}.`),{success:!0};throw console.error("Error inserting like:",t),Error(t.message)}let{data:a}=await r.from("business_profiles").select("business_slug").eq("id",e).single();a?.business_slug&&(0,i.revalidatePath)(`/${a.business_slug}`);let{data:n}=await r.from("business_profiles").select("id").eq("id",s.id).maybeSingle();return n&&((0,i.revalidatePath)("/dashboard/business"),(0,i.revalidatePath)("/dashboard/business/likes")),(0,i.revalidatePath)("/dashboard/business/activities"),{success:!0}}catch(e){return console.error("Unexpected error in likeBusiness:",e),{success:!1,error:e instanceof Error?e.message:"An unexpected error occurred."}}}async function l(e){let r=await (0,a.createClient)(),{data:{user:s},error:t}=await r.auth.getUser();if(t||!s)return{success:!1,error:"User not authenticated."};if(s.id===e)return{success:!1,error:"You cannot unlike your own business card."};try{let{error:t}=await r.from("likes").delete().match({user_id:s.id,business_profile_id:e});if(t)throw console.error("Error deleting like:",t),Error(t.message);let{data:a}=await r.from("business_profiles").select("business_slug").eq("id",e).single();a?.business_slug&&(0,i.revalidatePath)(`/${a.business_slug}`);let{data:n}=await r.from("business_profiles").select("id").eq("id",s.id).maybeSingle();return n&&((0,i.revalidatePath)("/dashboard/business"),(0,i.revalidatePath)("/dashboard/business/likes")),(0,i.revalidatePath)("/dashboard/business/activities"),{success:!0}}catch(e){return console.error("Unexpected error in unlikeBusiness:",e),{success:!1,error:e instanceof Error?e.message:"An unexpected error occurred."}}}async function b(e){let r=await (0,a.createClient)(),s=null,{data:{user:t}}=await r.auth.getUser();t&&(s=t.id);let i={isSubscribed:!1,hasLiked:!1,userRating:null,userReview:null};if(!s)return i;try{let[t,a,i]=await Promise.all([r.from("subscriptions").select("id",{count:"exact",head:!0}).match({user_id:s,business_profile_id:e}),r.from("likes").select("id",{count:"exact",head:!0}).match({user_id:s,business_profile_id:e}),r.from("ratings_reviews").select("rating, review_text").match({user_id:s,business_profile_id:e}).maybeSingle()]);if(t.error)throw Error(`Subscription fetch error: ${t.error.message}`);if(a.error)throw Error(`Like fetch error: ${a.error.message}`);if(i.error)throw Error(`Review fetch error: ${i.error.message}`);let n=i.data;return{isSubscribed:(t.count??0)>0,hasLiked:(a.count??0)>0,userRating:n?.rating??null,userReview:n?.review_text??null}}catch(r){console.error("Error fetching interaction status:",r);let e=r instanceof Error?r.message:"An unexpected error occurred.";return{...i,error:e}}}(0,s(33331).D)([n,o,u,c,d,l,b]),(0,t.A)(n,"408a3af28cdbf5a633d1470e3aa9845d5dfdf000ea",null),(0,t.A)(o,"406d3eedcd0777bc7a4d097a7c3736c79aa39001bd",null),(0,t.A)(u,"70696c60ec3cbc3cc67d74b3aabf05284aceb2c9d3",null),(0,t.A)(c,"40b79faef168e2af792fe9df79d32b29f3f0fe259d",null),(0,t.A)(d,"406e2a2a96626c0d97e44e258eff56ae8b5143ec6b",null),(0,t.A)(l,"4034994114ec4af82b8beb7b7ae0ca9d7cc384e91d",null),(0,t.A)(b,"40bb17f02c4644681936fdd90a24f199b1d28e2381",null)},47033:(e,r,s)=>{s.d(r,{A:()=>t});let t=(0,s(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])}};