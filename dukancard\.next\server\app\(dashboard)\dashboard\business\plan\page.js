(()=>{var e={};e.id=5874,e.ids=[5874],e.modules={788:(e,r,t)=>{"use strict";t.d(r,{default:()=>eW});var a=t(60687),s=t(43210),i=t.n(s);let n=(0,s.createContext)(void 0);function o(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useSubscriptionProcessing must be used within a SubscriptionProcessingProvider");return e}function l({children:e}){let[r,t]=(0,s.useState)("idle"),[i,o]=(0,s.useState)(""),l=(0,s.useRef)(new Set),c=(0,s.useCallback)(()=>{l.current.forEach(e=>{clearTimeout(e)}),l.current.clear()},[]),d=(0,s.useCallback)((e,r)=>{let t=setTimeout(()=>{e(),l.current.delete(t)},r);return l.current.add(t),t},[]),u=(0,s.useCallback)(()=>{t("idle"),o(""),c()},[c]),p=(0,s.useCallback)((e,r)=>{e?t("subscription_created"):t("error"),r?o(r):o(e?"Your subscription has been successfully created.":"There was an error processing your subscription. Please try again."),e||d(()=>{u()},5e3)},[d,u]),b=(0,s.useCallback)((e="Waiting for payment confirmation...")=>(t("waiting_for_webhook"),o(e),d(()=>{t(e=>"waiting_for_webhook"===e?(console.log("[SUBSCRIPTION_PROCESSING] Auto-resetting webhook waiting state after timeout"),o(""),"idle"):e)},3e4)),[d]),m=(0,s.useCallback)((e="Subscription authorized. Payment will be processed when your trial ends.")=>{t("future_payment_authorized"),o(e),d(()=>{u()},8e3)},[d,u]),h=(0,s.useCallback)((e="Your subscription has been halted.")=>{t("subscription_halted"),o(e),d(()=>{u()},8e3)},[d,u]),f=(0,s.useCallback)((e="Your subscription has been cancelled successfully.")=>{t("subscription_cancelled"),o(e),d(()=>{u()},8e3)},[d,u]),g=(0,s.useCallback)((e="Your subscription has been completed.")=>{t("subscription_completed"),o(e),d(()=>{u()},8e3)},[d,u]),x=(0,s.useCallback)((e="Your subscription has expired.")=>{t("subscription_expired"),o(e),d(()=>{u()},8e3)},[d,u]),y=(0,s.useCallback)((e="Razorpay servers are currently experiencing issues. Please try again in a few minutes.")=>{t("razorpay_server_error"),o(e),d(()=>{u()},1e4)},[d,u]),_=(0,s.useCallback)((e="Your subscription has been paused. You can resume it anytime.")=>{t("subscription_paused"),o(e),d(()=>{u()},8e3)},[d,u]),v=(0,s.useCallback)((e="Your subscription has been resumed successfully.")=>{t("subscription_resumed"),o(e),d(()=>{u()},8e3)},[d,u]);return(0,a.jsx)(n.Provider,{value:{status:r,message:i,startProcessing:(e="Please wait while we process your subscription")=>{t("processing"),o(e)},completeProcessing:p,resetProcessing:u,setRazorpayServerError:y,setWaitingForWebhook:b,setPaymentAuthorized:(e="Payment authorized, setting up your subscription...")=>{t("payment_authorized"),o(e)},setPaymentCaptured:(e="Payment captured, activating your subscription...")=>{t("payment_captured"),o(e)},setFuturePaymentAuthorized:m,setPaymentMethodUpdated:(e="Updating your payment method...")=>{t("processing"),o(e)},setSubscriptionCreated:(e="Subscription created, waiting for activation...")=>{t("subscription_created"),o(e)},setSubscriptionAuthenticated:(e="Subscription authenticated, waiting for activation...")=>{t("subscription_authenticated"),o(e)},setSubscriptionActive:(e="Your subscription is active!")=>{t("subscription_active"),o(e)},setSubscriptionActivated:(e="Subscription activated successfully!")=>{t("subscription_activated"),o(e)},setSubscriptionPending:(e="Your subscription is pending...")=>{t("subscription_pending"),o(e)},setSubscriptionHalted:h,setSubscriptionCancelled:f,setSubscriptionCompleted:g,setSubscriptionExpired:x,setSubscriptionPaused:_,setSubscriptionResumed:v,setRefundProcessing:(e="Processing your refund request...")=>{t("refund_processing"),o(e)},setRefundProcessed:(e="Refund processed successfully!")=>{t("refund_processed"),o(e)}},children:e})}var c=t(24224),d=t(11860),u=t(96241);let p=(0,c.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),b=s.forwardRef(({className:e,variant:r,title:t,description:s,action:i,...n},o)=>(0,a.jsxs)("div",{ref:o,className:(0,u.cn)(p({variant:r}),e),...n,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[t&&(0,a.jsx)("div",{className:"text-sm font-semibold",children:t}),s&&(0,a.jsx)("div",{className:"text-sm opacity-90",children:s})]}),i,(0,a.jsxs)("button",{className:"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]}));b.displayName="Toast";let m=(0,s.createContext)(void 0);function h({children:e}){let[r,t]=(0,s.useState)([]);return(0,a.jsxs)(m.Provider,{value:{toast:e=>{let r=Math.random().toString(36).substring(2,9);t(t=>[...t,{...e,id:r}]),setTimeout(()=>{t(e=>e.filter(e=>e.id!==r))},5e3)}},children:[e,(0,a.jsx)("div",{className:"fixed bottom-0 right-0 z-50 flex flex-col gap-2 p-4 md:max-w-[420px]",children:r.map((e,r)=>(0,a.jsx)(b,{...e},r))})]})}var f=t(16189),g=t(52581);t(35233);var x=t(77882),y=t(56085),_=t(85778),v=t(88920);let w=({tabs:e,defaultTab:r,value:t,onChange:n,className:o,tabClassName:l,activeTabClassName:c,inactiveTabClassName:d,indicatorClassName:p,indicatorLayoutId:b="activeTab",children:m})=>{let[h,f]=(0,s.useState)(t||r||e[0]?.id),[g,y]=(0,s.useState)(null);(0,s.useEffect)(()=>{t&&t!==h&&f(t)},[t,h]);let _=e=>{f(e),n?.(e)};return(0,a.jsxs)("div",{className:(0,u.cn)("w-full space-y-4",o),children:[(0,a.jsxs)("div",{className:"relative flex items-center justify-center p-1 rounded-xl bg-gradient-to-br from-white/80 to-white/40 dark:from-black/40 dark:to-black/20 backdrop-blur-sm border border-neutral-200/80 dark:border-neutral-800/80 shadow-sm",children:[(0,a.jsx)("div",{className:"absolute inset-0 overflow-hidden rounded-xl",children:Array.from({length:5}).map((e,r)=>(0,a.jsx)(x.P.div,{className:"absolute w-1 h-1 rounded-full bg-primary/30",initial:{x:`${100*Math.random()}%`,y:`${100*Math.random()}%`,opacity:.2},animate:{y:[`${100*Math.random()}%`,`${100*Math.random()}%`],opacity:[.1,.3,.1]},transition:{repeat:1/0,duration:5*Math.random()+5,ease:"easeInOut"}},r))}),(0,a.jsx)("div",{className:"relative z-10 flex w-full rounded-lg p-1",children:e.map(e=>{let r=h===e.id,t=g===e.id;return(0,a.jsxs)(x.P.button,{className:(0,u.cn)("relative flex-1 flex items-center justify-center gap-2 py-2.5 px-3 text-sm font-medium rounded-lg transition-all duration-200 z-10",l,r?(0,u.cn)("text-primary-foreground",c):(0,u.cn)("text-muted-foreground hover:text-foreground",d)),onClick:()=>_(e.id),onMouseEnter:()=>y(e.id),onMouseLeave:()=>y(null),whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-2",children:[e.icon&&(0,a.jsx)(x.P.div,{animate:{rotate:t?[0,-5,5,0]:0,scale:t?[1,1.1,1]:1},transition:{duration:.5},children:e.icon}),(0,a.jsx)("span",{children:e.label})]}),r&&(0,a.jsx)(x.P.div,{className:(0,u.cn)("absolute inset-0 rounded-lg bg-primary shadow-md",p),layoutId:b,transition:{type:"spring",bounce:.2,duration:.6}})]},e.id)})})]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(v.N,{mode:"wait",children:i().Children.map(m,e=>{if(!i().isValidElement(e))return null;let r=e.props;return r.value!==h?null:(0,a.jsx)(x.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.3},className:"w-full",children:e},r.value)})})})]})},N=({value:e,children:r,className:t})=>(0,a.jsx)("div",{className:(0,u.cn)("w-full",t),"data-value":e,children:r});var j=t(41862),C=t(14719),k=t(35071),S=t(93613),P=t(48730),A=t(78122);function I(){let{status:e,message:r,resetProcessing:t}=o();return null}var R=t(19059),E=t(47262),T=t(24934);function D({billingCycle:e,setBillingCycle:r}){return(0,a.jsxs)("div",{className:"relative inline-flex",children:[(0,a.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/30 rounded-full blur-md"}),(0,a.jsxs)("div",{className:"space-x-2 bg-white/80 dark:bg-black/50 backdrop-blur-sm p-1.5 rounded-full border border-neutral-200/50 dark:border-neutral-800/50 inline-flex shadow-md relative z-10",children:[(0,a.jsx)(T.$,{onClick:()=>r("monthly"),variant:"monthly"===e?"default":"ghost",size:"sm",className:`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${"monthly"===e?"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md":"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"}`,children:(0,a.jsx)("span",{className:"monthly"===e?"font-medium":"font-normal",children:"Monthly"})}),(0,a.jsxs)(T.$,{onClick:()=>r("yearly"),variant:"yearly"===e?"default":"ghost",size:"sm",className:`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2 ${"yearly"===e?"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md":"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"}`,children:[(0,a.jsx)("span",{className:"yearly"===e?"font-medium":"font-normal",children:"Yearly"}),(0,a.jsx)("span",{className:`text-xs px-1.5 py-0.5 rounded-full ${"yearly"===e?"bg-white/90 text-[var(--brand-gold)]":"bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400"}`,children:"Save 20%"})]})]})]})}function O({subscriptionStatus:e,billingCycle:r,setBillingCycle:t,plans:s,currentPlanId:i,currentPlanCycle:n,loadingStates:o,onPlanAction:l}){let c={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4}}},d=t=>t.id===i&&r===n&&("active"===e||"authenticated"===e),u=e=>"free"===e.id||!e.available;return(0,a.jsx)(E.S,{children:(0,a.jsxs)(x.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black backdrop-blur-sm shadow-lg transition-all duration-300 hover:shadow-xl relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent dark:from-blue-500/10 dark:to-transparent pointer-events-none"}),(0,a.jsxs)("div",{className:"relative z-10 p-4 sm:p-6 md:p-8",children:[(0,a.jsx)(x.P.div,{variants:c,className:"mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,a.jsx)(y.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Available Plans"}),(0,a.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"inactive"===e?"Select a subscription plan that best fits your business needs and unlock premium features":"View and compare available subscription options to find the perfect fit for your business"})]})]})}),(0,a.jsx)(x.P.div,{variants:c,className:"flex justify-center mb-8",children:(0,a.jsx)(D,{billingCycle:r,setBillingCycle:t})}),(0,a.jsx)(x.P.div,{variants:c,children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 md:gap-4 items-stretch",children:s.map((e,t)=>{let s,n=d(e),c=u(e);c?"free"===e.id&&(s="free"===i?"Current Plan":"Auto Managed"):n||(s="View Plan");let p={...e,available:!c&&e.available};return(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(R.A,{plan:p,index:t,isLoading:o[e.id],isCurrentPlan:n||"free"===e.id&&"free"===i,buttonTextOverride:s,onButtonClick:()=>l(e)})},`${e.id}-${r}`)})})})]})]})})}var $=t(43649),U=t(74212),z=t(77493);function W({trialEndDate:e}){let r=new Date(e),t=(0,U.m)(r,{addSuffix:!0}),s=r.getTime()-Date.now()<2592e5;return(0,a.jsx)("div",{className:"w-full opacity-100 transition-all duration-500",style:{transform:"translateY(0)"},children:(0,a.jsxs)("div",{className:`w-full rounded-lg border ${s?"border-amber-200 dark:border-amber-800/50 bg-gradient-to-r from-amber-50 to-amber-100/70 dark:from-amber-950/40 dark:to-amber-900/30":"border-blue-200 dark:border-blue-800/50 bg-gradient-to-r from-blue-50 to-blue-100/70 dark:from-blue-950/40 dark:to-blue-900/30"} backdrop-blur-sm shadow-sm relative overflow-hidden`,children:[(0,a.jsx)("div",{className:"absolute inset-0 pointer-events-none",children:(0,a.jsx)("div",{className:`absolute inset-0 ${s?"bg-amber-400/5 dark:bg-amber-400/10":"bg-blue-400/5 dark:bg-blue-400/10"} rounded-full blur-3xl`})}),(0,a.jsx)("div",{className:"relative z-10 p-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[s?(0,a.jsx)($.A,{className:"h-5 w-5 flex-shrink-0 text-amber-600 dark:text-amber-500 mt-0.5"}):(0,a.jsx)(P.A,{className:"h-5 w-5 flex-shrink-0 text-blue-600 dark:text-blue-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:`text-base font-semibold ${s?"text-amber-800 dark:text-amber-400":"text-blue-800 dark:text-blue-400"}`,children:s?"Your trial is ending soon!":"You're on a free trial"}),(0,a.jsxs)("p",{className:`text-sm mt-1 ${s?"text-amber-700 dark:text-amber-500":"text-blue-700 dark:text-blue-500"}`,children:["Your trial period ends ",t,". Choose a plan below to continue using premium features."]})]})]}),(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:`rounded-md p-3 ${s?"bg-amber-100/50 dark:bg-amber-900/30 border border-amber-200/50 dark:border-amber-800/30":"bg-blue-100/50 dark:bg-blue-900/30 border border-blue-200/50 dark:border-blue-800/30"}`,children:(0,a.jsx)(z.A,{endDate:e,label:"Trial ends in:",tooltipText:"Your trial will end on this date. Choose a plan to continue using premium features."})})})]})})]})})}var M=t(37826),B=t(55192);let q=s.forwardRef(({className:e,...r},t)=>(0,a.jsx)("span",{ref:t,className:(0,u.cn)("absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0",e),style:{clip:"rect(0 0 0 0)",clipPath:"inset(50%)"},...r}));function L({isOpen:e,message:r="Waiting for confirmation from Razorpay",description:t="Please wait while we receive webhook confirmation. This may take a moment."}){let[s,n]=i().useState("");return(0,a.jsx)(M.lG,{open:e,modal:!0,children:(0,a.jsxs)(M.Cf,{className:"max-w-md mx-auto p-0 border-none shadow-lg",hideClose:!0,onEscapeKeyDown:e=>e.preventDefault(),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,a.jsx)(M.L3,{asChild:!0,children:(0,a.jsx)(q,{children:r})}),(0,a.jsx)(B.Zp,{className:"overflow-hidden border-primary/20",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute -inset-4 rounded-full bg-primary/10 animate-pulse blur-xl opacity-75"}),(0,a.jsx)(j.A,{className:"h-12 w-12 animate-spin text-primary relative z-10"})]}),(0,a.jsxs)("h3",{className:"mt-6 text-lg font-medium text-foreground",children:[r,(0,a.jsx)("span",{className:"inline-block w-8 text-left",children:s})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:t}),(0,a.jsx)("p",{className:"mt-4 text-xs text-muted-foreground italic",children:"This may take a few moments. Please don't refresh the page."})]})})})]})})}function H({isVisible:e,message:r="Waiting for confirmation from Razorpay",description:t="Please wait while we receive webhook confirmation. This may take a moment."}){return(0,a.jsx)(L,{isOpen:e,message:r,description:t})}q.displayName="VisuallyHidden";var X=t(62688);let F=(0,X.A)("CirclePause",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"10",x2:"10",y1:"15",y2:"9",key:"c1nkhi"}],["line",{x1:"14",x2:"14",y1:"15",y2:"9",key:"h65svq"}]]);var G=t(8160);function Y({subscriptionId:e,status:r,planName:t,planCycle:s,amount:i,currency:n="INR",nextBillingDate:o,lastPaymentDate:l,lastPaymentMethod:c,createdAt:d,expiresAt:u,pausedAt:p,cancellationRequestedAt:b,cancelledAt:m,isEligibleForRefund:h,subscriptionStartDate:f,subscriptionChargeTime:g,trialEndDate:y}){let w=e=>{if(!e)return"Not available";try{return new Date(e).toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})}catch(e){return"Invalid date"}},N=e=>{if(!e)return"";try{return(0,U.m)(new Date(e),{addSuffix:!0})}catch(e){return""}},j=(e=>{switch(e){case G.dH.ACTIVE:return{color:"bg-green-500",textColor:"text-green-700 dark:text-green-300",borderColor:"border-green-200 dark:border-green-800",bgColor:"bg-green-50 dark:bg-green-900/20",icon:(0,a.jsx)(C.A,{className:"h-5 w-5 text-green-500"}),text:"Active",description:"Your subscription is active and all features are available."};case G.dH.AUTHENTICATED:return{color:"bg-blue-500",textColor:"text-blue-700 dark:text-blue-300",borderColor:"border-blue-200 dark:border-blue-800",bgColor:"bg-blue-50 dark:bg-blue-900/20",icon:(0,a.jsx)(C.A,{className:"h-5 w-5 text-blue-500"}),text:"Authenticated",description:"Your subscription has been authenticated and will be activated soon."};case G.dH.PENDING:return{color:"bg-yellow-500",textColor:"text-yellow-700 dark:text-yellow-300",borderColor:"border-yellow-200 dark:border-yellow-800",bgColor:"bg-yellow-50 dark:bg-yellow-900/20",icon:(0,a.jsx)(P.A,{className:"h-5 w-5 text-yellow-500"}),text:"Pending",description:"Your subscription is pending activation."};case G.dH.HALTED:case"paused":return{color:"bg-orange-500",textColor:"text-orange-700 dark:text-orange-300",borderColor:"border-orange-200 dark:border-orange-800",bgColor:"bg-orange-50 dark:bg-orange-900/20",icon:(0,a.jsx)(F,{className:"h-5 w-5 text-orange-500"}),text:"Paused",description:"Your subscription has been temporarily paused."};case G.dH.CANCELLED:return{color:"bg-red-500",textColor:"text-red-700 dark:text-red-300",borderColor:"border-red-200 dark:border-red-800",bgColor:"bg-red-50 dark:bg-red-900/20",icon:(0,a.jsx)(k.A,{className:"h-5 w-5 text-red-500"}),text:"Cancelled",description:"Your subscription has been cancelled."};default:return{color:"bg-gray-500",textColor:"text-gray-700 dark:text-gray-300",borderColor:"border-gray-200 dark:border-gray-800",bgColor:"bg-gray-50 dark:bg-gray-900/20",icon:(0,a.jsx)($.A,{className:"h-5 w-5 text-gray-500"}),text:"Unknown",description:"Subscription status is unknown."}}})(r),S=(e=>{if(!e)return r===G.dH.AUTHENTICATED?{icon:(0,a.jsx)(P.A,{className:"h-4 w-4 text-blue-500"}),text:"Pending"}:{icon:(0,a.jsx)(_.A,{className:"h-4 w-4"}),text:"Not available"};let t=e.toLowerCase();return"card"===t?{icon:(0,a.jsx)(_.A,{className:"h-4 w-4 text-blue-500"}),text:"Credit/Debit Card"}:"upi"===t?{icon:(0,a.jsx)(_.A,{className:"h-4 w-4 text-green-500"}),text:"UPI"}:"netbanking"===t?{icon:(0,a.jsx)(_.A,{className:"h-4 w-4 text-purple-500"}),text:"Net Banking"}:"wallet"===t?{icon:(0,a.jsx)(_.A,{className:"h-4 w-4 text-orange-500"}),text:"Wallet"}:{icon:(0,a.jsx)(_.A,{className:"h-4 w-4"}),text:e}})(c);return(0,a.jsx)(v.N,{children:(0,a.jsx)(x.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full",children:(0,a.jsxs)("div",{className:"overflow-hidden bg-white dark:bg-black border border-neutral-200/80 dark:border-neutral-800/80 rounded-lg relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent dark:from-blue-500/10 dark:to-transparent pointer-events-none"}),(0,a.jsxs)("div",{className:"p-0 relative z-10",children:[(0,a.jsx)("div",{className:"p-6 bg-white dark:bg-black border-b border-neutral-200/80 dark:border-neutral-800/80",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(x.P.div,{whileHover:{scale:1.1,rotate:5},transition:{type:"spring",stiffness:400,damping:10},className:`w-12 h-12 rounded-xl flex items-center justify-center ${j.color} bg-opacity-20 dark:bg-opacity-30`,children:(0,a.jsx)("div",{className:"text-[var(--primary)]",children:j.icon})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:"Your Subscription"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:j.description})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 p-4 rounded-xl bg-white/50 dark:bg-black/30 border border-neutral-200/80 dark:border-neutral-800/80",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider",children:"Plan"}),(0,a.jsxs)("span",{className:"font-medium",children:[t," (",s,")"]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider",children:"Amount"}),(0,a.jsxs)("span",{className:"font-medium",children:["₹ ",i.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider",children:"Payment Method"}),(0,a.jsxs)("span",{className:"font-medium flex items-center gap-1",children:[S.icon," ",S.text]})]})]})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)(x.P.div,{className:"relative overflow-hidden rounded-xl bg-gradient-to-br from-white/90 to-white/70 dark:from-black/90 dark:to-black/70 border border-neutral-200/80 dark:border-neutral-800/80",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,a.jsx)(x.P.div,{className:`absolute rounded-full ${j.color} blur-md`,initial:{x:"50%",y:"30%",scale:1,opacity:.6},animate:{scale:[1,1.05,1],opacity:[.6,.7,.6]},transition:{repeat:1/0,repeatType:"reverse",duration:8,ease:"easeInOut"},style:{width:"120px",height:"120px",transform:"translate(-50%, -50%)"}}),Array.from({length:12}).map((e,r)=>{let t,s,i=80*Math.random()+20,n=.3*Math.random()+.1;r<4?(t=100*Math.random(),s=30*Math.random()):r<8?(t=100*Math.random(),s=30+40*Math.random()):(t=100*Math.random(),s=70+30*Math.random());let o=3+Math.floor(3*Math.random()),l=Array.from({length:o},()=>100*Math.random()),c=Array.from({length:o},()=>100*Math.random()),d=15+(20*Math.random()+15)*(1-.5*(1-(i-20)/80)),u=5*Math.random(),p=Math.floor(3*Math.random())+1,b=`blur-${1===p?"md":2===p?"lg":"xl"}`;return(0,a.jsx)(x.P.div,{className:`absolute rounded-full ${j.color} ${b}`,initial:{left:`${t}%`,top:`${s}%`,opacity:n,x:"0%",y:"0%"},animate:{x:l.map(e=>`${e-t}%`),y:c.map(e=>`${e-s}%`),opacity:[n,...Array(o-1).fill(1.2*n),n],scale:[1,...Array(o-1).fill(0).map(()=>1+.3*Math.random()),1]},transition:{repeat:1/0,repeatType:"reverse",duration:d,ease:"easeInOut",delay:u,times:Array.from({length:o+1},(e,r)=>r/o)},style:{width:`${i}px`,height:`${i}px`,zIndex:Math.floor(10*Math.random())}},`circle-${r}`)}),Array.from({length:8}).map((e,r)=>{let t,s,i=15*Math.random()+5;r<2?(t=40*Math.random(),s=40*Math.random()):r<4?(t=60+40*Math.random(),s=40*Math.random()):(t=r<6?40*Math.random():60+40*Math.random(),s=60+40*Math.random());let n=Array.from({length:4},()=>100*Math.random()),o=Array.from({length:4},()=>100*Math.random()),l=15*Math.random()+10;return(0,a.jsx)(x.P.div,{className:`absolute rounded-full ${j.color} blur-sm`,initial:{left:`${t}%`,top:`${s}%`,opacity:.2,x:"0%",y:"0%"},animate:{x:n.map(e=>`${e-t}%`),y:o.map(e=>`${e-s}%`),opacity:[.1,.3,.2,.3,.1]},transition:{repeat:1/0,repeatType:"mirror",duration:l,ease:"easeInOut",delay:2*Math.random()},style:{width:`${i}px`,height:`${i}px`}},`particle-${r}`)})]}),(0,a.jsxs)("div",{className:"relative z-10 p-8 flex flex-col items-center text-center",children:[(0,a.jsxs)(x.P.div,{className:`w-24 h-24 rounded-full ${j.color} flex items-center justify-center mb-8 relative`,initial:{scale:.8},animate:{scale:1},transition:{type:"spring",stiffness:300,damping:15},children:[(0,a.jsx)("div",{className:`absolute inset-0 ${j.color} rounded-full blur-lg opacity-50`}),(0,a.jsx)(x.P.div,{className:`absolute inset-0 ${j.color} rounded-full`,initial:{scale:.85,opacity:.5},animate:{scale:[.85,1.15,.85],opacity:[.5,0,.5]},transition:{repeat:1/0,duration:3,ease:"easeInOut"}}),(0,a.jsx)("div",{className:"text-white relative z-10",children:(0,a.jsx)("div",{className:"text-[2.5rem]",children:j.icon})})]}),(0,a.jsxs)(x.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-3xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80",children:j.text}),(0,a.jsx)("p",{className:"text-muted-foreground text-base",children:j.description})]}),(0,a.jsxs)(x.P.div,{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 w-full mt-6",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:[d&&(0,a.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Created"}),(0,a.jsx)("span",{className:"font-medium",children:w(d)}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:N(d)})]}),r===G.dH.CANCELLED&&m&&(0,a.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Cancelled"}),(0,a.jsx)("span",{className:"font-medium",children:w(m)}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:N(m)})]}),r===G.dH.ACTIVE&&l&&(0,a.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Last Payment"}),(0,a.jsx)("span",{className:"font-medium",children:w(l)}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:N(l)})]}),r===G.dH.ACTIVE&&h&&(0,a.jsxs)("div",{className:"bg-purple-50/30 dark:bg-purple-900/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-purple-100/30 dark:border-purple-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-purple-600 dark:text-purple-400 uppercase tracking-wider mb-1",children:"Refund Eligible"}),(0,a.jsx)("span",{className:"font-medium text-purple-700 dark:text-purple-300",children:"7-Day Refund Window"}),(0,a.jsx)("span",{className:"text-xs text-purple-600/80 dark:text-purple-400/80",children:"Full refund available"})]}),y&&new Date(y)>new Date&&(0,a.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Trial Ends"}),(0,a.jsx)("span",{className:"font-medium",children:w(y)}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:N(y)})]}),"authenticated"===r&&f&&(0,a.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Starts On"}),(0,a.jsx)("span",{className:"font-medium",children:w(f)}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:N(f)})]}),"authenticated"===r&&!f&&o&&(0,a.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Starts On"}),(0,a.jsx)("span",{className:"font-medium",children:w(o)}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:N(o)})]}),"authenticated"===r&&g&&(0,a.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"First Payment On"}),(0,a.jsx)("span",{className:"font-medium",children:w(g)}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:N(g)})]}),(r===G.dH.ACTIVE||r===G.dH.AUTHENTICATED)&&u&&(0,a.jsxs)("div",{className:"bg-white/30 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 flex flex-col items-center border border-white/10 dark:border-neutral-800/30",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground uppercase tracking-wider mb-1",children:"Current Period Ends"}),(0,a.jsx)("span",{className:"font-medium",children:w(u)}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:N(u)})]})]})]})]})})]})]})})})}var V=t(96752);let Z=(0,X.A)("Receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]]);var K=t(10022),J=t(40228),Q=t(31158);t(38398);var ee=t(74354);function er({subscriptionId:e,planId:r=""}={}){let[t,i]=(0,s.useState)([]),[n,o]=(0,s.useState)(!0),[l,c]=(0,s.useState)(null),[d,p]=(0,s.useState)(1),[b]=(0,s.useState)(5),[m,h]=(0,s.useState)({page:1,count:b,totalCount:0,totalPages:0}),f={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3}}},y=(0,s.useCallback)(async(e,r=1)=>{o(!0),c(null);try{let t=await fetch(`/api/subscriptions/${e}/invoices?page=${r}&count=${b}`),a=await t.json();if(!t.ok)throw Error(a.error||"Failed to fetch invoices");a.success&&a.invoices&&a.invoices.items?(i(a.invoices.items),a.pagination?h(a.pagination):h({page:r,count:b,totalCount:a.invoices.count||0,totalPages:Math.ceil((a.invoices.count||0)/b)})):a.success&&a.invoices&&Array.isArray(a.invoices)?(i(a.invoices),h({page:r,count:b,totalCount:a.invoices.length,totalPages:Math.ceil(a.invoices.length/b)})):(i([]),console.log("[RAZORPAY_DEBUG] No invoices found or unexpected response format:",a),h({page:1,count:b,totalCount:0,totalPages:0}))}catch(e){console.error("[RAZORPAY_ERROR] Error fetching invoices:",e),c(e instanceof Error?e.message:"Failed to fetch invoices")}finally{o(!1)}},[b]),_=(e,r)=>new Intl.NumberFormat("en-IN",{style:"currency",currency:r||"INR",minimumFractionDigits:2}).format(e/100),v=r=>{r<1||r>m.totalPages||r===d||(p(r),e&&y(e,r))};return(0,a.jsxs)(B.Zp,{className:"border shadow-sm transition-all duration-300 hover:shadow-md h-full bg-white dark:bg-black",children:[(0,a.jsx)(B.aR,{className:"bg-gradient-to-br from-transparent to-blue-50/50 dark:to-blue-900/10",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.P.div,{whileHover:{rotate:[0,-10,10,-5,0],transition:{duration:.5}},className:"p-2 rounded-full bg-blue-100/50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400",children:(0,a.jsx)(Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(B.ZB,{children:"Invoice History"}),(0,a.jsx)(B.BT,{children:"View and download your invoice history"})]})]}),e&&(0,a.jsxs)(T.$,{variant:"outline",size:"sm",onClick:()=>{y(e,d),g.oR.info("Refreshing invoice history...")},disabled:n,className:"bg-white dark:bg-black/20 border-blue-200 dark:border-blue-800/50 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200",children:[(0,a.jsx)(A.A,{className:(0,u.cn)("w-4 h-4 mr-2 text-blue-600 dark:text-blue-400",n&&"animate-spin")}),"Refresh"]})]})}),(0,a.jsx)(B.Wu,{className:"p-0",children:(0,a.jsx)(x.P.div,{variants:f,className:"p-4 sm:p-6",children:n?(0,a.jsxs)(x.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"space-y-4 py-4",children:[(0,a.jsx)(x.P.div,{className:"flex items-center justify-center mb-6",animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},children:(0,a.jsx)(A.A,{className:"w-6 h-6 text-blue-500"})}),(0,a.jsx)("div",{className:"h-10 w-full bg-blue-100/50 dark:bg-blue-900/20 rounded-md animate-pulse"}),(0,a.jsx)("div",{className:"h-16 w-full bg-blue-100/30 dark:bg-blue-900/10 rounded-md animate-pulse"}),(0,a.jsx)("div",{className:"h-16 w-full bg-blue-100/20 dark:bg-blue-900/5 rounded-md animate-pulse"})]},"loading"):l?(0,a.jsxs)(x.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,a.jsx)(x.P.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:300,damping:20},className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-full mb-4",children:(0,a.jsx)(K.A,{className:"w-8 h-8 text-red-500 dark:text-red-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2 text-red-600 dark:text-red-400",children:"Error Loading Invoices"}),(0,a.jsx)("p",{className:"text-muted-foreground max-w-sm mb-4",children:l}),e&&(0,a.jsxs)(T.$,{variant:"outline",size:"sm",onClick:()=>{y(e,d),g.oR.info("Retrying invoice fetch...")},className:"bg-white dark:bg-black/20 border-red-200 dark:border-red-800/50 hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400",children:[(0,a.jsx)(A.A,{className:"w-4 h-4 mr-2"}),"Try Again"]})]},"error"):0===t.length?(0,a.jsxs)(x.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"flex flex-col items-center justify-center py-10 text-center",children:[(0,a.jsx)(x.P.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:300,damping:20},className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-full mb-4",children:(0,a.jsx)(Z,{className:"w-10 h-10 text-blue-500 dark:text-blue-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Invoices Yet"}),(0,a.jsx)("p",{className:"text-muted-foreground max-w-sm",children:G.SubscriptionStateManager.isFreeStatus("",r)?"Free plan users don't have invoices. Upgrade to a paid plan to see invoices here.":"Invoices will appear here after your first payment"})]},"empty"):(0,a.jsxs)(x.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.05}}},initial:"hidden",animate:"visible",className:"overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-800",children:[(0,a.jsxs)(V.XI,{children:[(0,a.jsx)(V.A0,{className:"bg-gray-50 dark:bg-black/90",children:(0,a.jsxs)(V.Hj,{children:[(0,a.jsx)(V.nd,{className:"font-medium",children:"Invoice #"}),(0,a.jsx)(V.nd,{className:"font-medium",children:"Date"}),(0,a.jsx)(V.nd,{className:"font-medium",children:"Amount"}),(0,a.jsx)(V.nd,{className:"font-medium",children:"Status"}),(0,a.jsx)(V.nd,{className:"font-medium text-right",children:"Actions"})]})}),(0,a.jsx)(V.BF,{children:t.map((e,r)=>(0,a.jsxs)(x.P.tr,{variants:f,className:(0,u.cn)("transition-colors hover:bg-blue-50/50 dark:hover:bg-blue-900/10",r%2==0?"bg-white dark:bg-black":"bg-blue-50/20 dark:bg-black/80"),children:[(0,a.jsx)(V.nA,{className:"font-medium text-blue-700 dark:text-blue-300",children:e.id.replace("inv_","#")}),(0,a.jsx)(V.nA,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(J.A,{className:"w-4 h-4 text-blue-500 dark:text-blue-400"}),(0,a.jsx)("span",{children:(0,u.Yq)(new Date(1e3*e.date))})]})}),(0,a.jsx)(V.nA,{className:"font-semibold",children:_(e.amount,e.currency)}),(0,a.jsx)(V.nA,{children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"paid"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"pending"===e.status||"issued"===e.status?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,a.jsx)(V.nA,{className:"text-right",children:e.short_url?(0,a.jsxs)(T.$,{variant:"ghost",size:"sm",className:"h-8 px-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20",onClick:()=>window.open(e.short_url,"_blank"),children:[(0,a.jsx)(Q.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"sr-only sm:not-sr-only sm:inline-block",children:"View"})]}):(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Not available"})})]},e.id))})]}),m.totalPages>1&&(0,a.jsx)("div",{className:"flex justify-center mt-4 pb-2",children:(0,a.jsx)(ee.dK,{children:(0,a.jsxs)(ee.Iu,{children:[d>1&&(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.Eb,{href:"#",onClick:e=>{e.preventDefault(),v(d-1)},className:"border-gray-200 dark:border-gray-800 hover:border-gray-400 dark:hover:border-gray-600 transition-colors"})}),d>2&&(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.n$,{href:"#",onClick:e=>{e.preventDefault(),v(1)},className:1===d?"bg-gray-100 dark:bg-gray-900":"",children:"1"})}),d>3&&(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.M_,{})}),d>1&&(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.n$,{href:"#",onClick:e=>{e.preventDefault(),v(d-1)},children:d-1})}),(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.n$,{href:"#",isActive:!0,onClick:e=>e.preventDefault(),children:d})}),d<m.totalPages&&(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.n$,{href:"#",onClick:e=>{e.preventDefault(),v(d+1)},children:d+1})}),d<m.totalPages-2&&(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.M_,{})}),d<m.totalPages-1&&(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.n$,{href:"#",onClick:e=>{e.preventDefault(),v(m.totalPages)},children:m.totalPages})}),d<m.totalPages&&(0,a.jsx)(ee.cU,{children:(0,a.jsx)(ee.WA,{href:"#",onClick:e=>{e.preventDefault(),v(d+1)},className:"border-gray-200 dark:border-gray-800 hover:border-gray-400 dark:hover:border-gray-600 transition-colors"})})]})})})]})})}),t.length>0&&(0,a.jsx)(B.wL,{className:"bg-white dark:bg-black border-t border-gray-200 dark:border-gray-800 py-3 px-6 text-xs text-muted-foreground",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.P.div,{whileHover:{rotate:360},transition:{duration:.5},className:"text-blue-500 dark:text-blue-400",children:(0,a.jsx)(Z,{className:"w-4 h-4"})}),(0,a.jsx)("span",{children:"Invoices are generated automatically by Razorpay when payments are processed."})]})})]})}let et=(0,X.A)("CirclePlay",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]]);var ea=t(84027),es=t(99891),ei=t(5336),en=t(21761),eo=t(39390),el=t(3018),ec=t(59821);function ed({variant:e="gold",intensity:r="medium",className:t=""}){let[i,n]=(0,s.useState)(!1),[o,l]=(0,s.useState)(!1),c=(()=>{switch(r){case"low":return{light:.05,dark:.1};case"high":return{light:.15,dark:.25};default:return{light:.1,dark:.15}}})(),{primary:d,secondary:u}=(()=>{switch(e){case"blue":return{primary:`bg-blue-500/${c.light} dark:bg-blue-500/${c.dark}`,secondary:"bg-blue-300/5 dark:bg-blue-600/10"};case"gradient":return{primary:`bg-gradient-to-br from-[var(--brand-gold)]/${c.light} to-blue-500/${c.light} dark:from-[var(--brand-gold)]/${c.dark} dark:to-blue-500/${c.dark}`,secondary:"bg-transparent"};default:return{primary:`bg-[var(--brand-gold)]/${c.light} dark:bg-[var(--brand-gold)]/${c.dark}`,secondary:"bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10"}}})();return i?(0,a.jsxs)("div",{className:`absolute inset-0 overflow-hidden pointer-events-none ${t}`,children:[(0,a.jsxs)(x.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"absolute inset-0",children:[(0,a.jsx)(x.P.div,{animate:{y:[0,-8,0],scale:[1,1.05,1]},transition:{duration:8,repeat:1/0,repeatType:"reverse",ease:"easeInOut"},className:`absolute top-0 right-0 w-[70%] h-[70%] rounded-full blur-3xl opacity-70 ${d}`,style:{filter:o?"blur(40px)":"blur(60px)"}}),(0,a.jsx)(x.P.div,{animate:{y:[0,10,0],x:[0,-5,0],scale:[1,1.03,1]},transition:{duration:10,repeat:1/0,repeatType:"reverse",ease:"easeInOut"},className:`absolute bottom-0 left-0 w-[50%] h-[50%] rounded-full blur-3xl opacity-50 ${u}`,style:{filter:o?"blur(30px)":"blur(50px)"}})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-[url('/noise.svg')] opacity-5 dark:opacity-10 mix-blend-overlay"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-blue-500/5 dark:from-neutral-900/5 dark:to-blue-500/10"})]}):null}var eu=t(10218),ep=t(70334);function eb({children:e,className:r="",variant:t="primary",size:i="default",showArrow:n=!1,roundedFull:o=!1,...l}){let{resolvedTheme:c}=(0,eu.D)(),[d,p]=(0,s.useState)(!1),[b,m]=(0,s.useState)(!1),h=!!b&&"dark"===c,f=(0,u.cn)("absolute -inset-0.5",o?"rounded-full":"rounded-md","bg-gradient-to-r from-[var(--brand-gold)]/40 to-[var(--brand-gold)]/60","blur-[2px]","max-w-full w-auto");return(0,a.jsxs)("div",{className:"relative group inline-flex max-w-full w-auto",children:[(0,a.jsx)("div",{className:f,style:{opacity:.5}}),b&&(0,a.jsx)(()=>b?(0,a.jsx)(x.P.div,{className:(0,u.cn)("absolute -inset-0.5",o?"rounded-full":"rounded-md","bg-gradient-to-r from-[var(--brand-gold)]/40 to-[var(--brand-gold)]/60",h?"blur-sm":"blur-[2px]","max-w-full w-auto"),initial:{opacity:.5},animate:{opacity:[.5,.7,.5],boxShadow:h?["0 0 10px 1px rgba(var(--brand-gold-rgb), 0.2)","0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)","0 0 10px 1px rgba(var(--brand-gold-rgb), 0.2)"]:["0 0 5px 1px rgba(var(--brand-gold-rgb), 0.1)","0 0 10px 2px rgba(var(--brand-gold-rgb), 0.2)","0 0 5px 1px rgba(var(--brand-gold-rgb), 0.1)"]},transition:{duration:2,repeat:1/0,repeatType:"loop",ease:"easeInOut"}}):null,{}),(0,a.jsx)("div",{className:"relative max-w-full w-full sm:w-auto",children:(0,a.jsxs)(T.$,{variant:"primary"===t?"default":t,size:i,className:(0,u.cn)("relative z-10 font-medium overflow-hidden max-w-full w-full sm:w-auto",o?"rounded-full":"rounded-md","primary"===t&&"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900","outline"===t&&"border border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/5",r),onMouseEnter:()=>b&&p(!0),onMouseLeave:()=>b&&p(!1),...l,children:[(0,a.jsx)("span",{className:"absolute inset-0 w-full h-full overflow-hidden",children:(0,a.jsx)("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none"})}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 relative z-10",children:[(0,a.jsx)("span",{children:e}),n&&(0,a.jsx)("div",{style:{transform:b&&d?"translateX(5px)":"translateX(0px)",transition:"transform 0.3s"},children:(0,a.jsx)(ep.A,{className:"w-4 h-4"})})]})]})})]})}function em({isOpen:e,onClose:r,onRequestRefund:t,isLoading:i,isEligibleForRefund:n}){let[o,l]=(0,s.useState)(!1),[c,d]=(0,s.useState)("normal"),u={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},p=async()=>{await t(c)};return(0,a.jsx)(M.lG,{open:e,onOpenChange:e=>{e||r()},children:(0,a.jsx)(M.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl",children:(0,a.jsxs)(x.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,staggerChildren:.1}}},className:"relative overflow-hidden",children:[o&&(0,a.jsx)(ed,{variant:"gold",intensity:"medium"}),(0,a.jsxs)(M.c7,{className:"p-6 pb-2 z-10 relative",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-purple-500/15 text-purple-500",children:(0,a.jsx)(A.A,{className:"w-5 h-5"})}),(0,a.jsx)(ec.E,{variant:"outline",className:"border-purple-500/50 text-purple-500 px-2 py-0.5",children:n?"Payment Issue":"Refund Request"})]}),(0,a.jsx)(M.L3,{className:"text-2xl font-bold",children:"Request Refund"})]}),(0,a.jsxs)("div",{className:"p-6 pt-2 space-y-4 z-10 relative",children:[(0,a.jsx)(x.P.p,{variants:u,className:"text-muted-foreground",children:n?"You are eligible for a refund due to a payment issue or subscription problem. Your subscription will be cancelled immediately if the refund is approved.":"Request a refund for your subscription. Note that refunds are only processed for payment issues or subscription problems."}),(0,a.jsx)(x.P.div,{variants:u,children:(0,a.jsxs)(el.Fc,{className:"bg-white/50 dark:bg-black/50 border border-neutral-200 dark:border-neutral-800",children:[(0,a.jsx)(es.A,{className:"h-4 w-4 text-[var(--brand-gold)]"}),(0,a.jsx)(el.XL,{className:"font-medium",children:"Refund Information"}),(0,a.jsx)(el.TN,{className:"text-sm",children:(0,a.jsxs)("p",{className:"mb-2",children:[n?"Your full refund will be processed back to your original payment method and your subscription will be cancelled immediately.":"Your refund request will be reviewed. If approved, it will be processed back to your original payment method."," ","Processing time depends on your bank and the refund speed selected."]})})]})}),(0,a.jsxs)(x.P.div,{variants:u,className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Select Refund Speed"}),(0,a.jsxs)(en.z,{value:c,onValueChange:e=>d(e),className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-3 rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80 transition-colors",children:[(0,a.jsx)(en.C,{value:"normal",id:"normal"}),(0,a.jsxs)(eo.J,{htmlFor:"normal",className:"flex flex-col cursor-pointer",children:[(0,a.jsx)("span",{className:"font-medium",children:"Normal"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Processed within 5-7 business days"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-3 rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80 transition-colors",children:[(0,a.jsx)(en.C,{value:"optimum",id:"optimum"}),(0,a.jsxs)(eo.J,{htmlFor:"optimum",className:"flex flex-col cursor-pointer",children:[(0,a.jsxs)("span",{className:"flex items-center font-medium",children:["Instant",(0,a.jsx)("span",{className:"ml-2 text-xs px-1.5 py-0.5 rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",children:"Recommended"})]}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Processed immediately when possible, otherwise normal speed"})]})]})]})]})]}),(0,a.jsxs)(M.Es,{className:"p-6 pt-2 flex flex-col sm:flex-row gap-2 sm:justify-between",children:[(0,a.jsx)(x.P.div,{variants:u,className:"w-full sm:w-auto",children:(0,a.jsx)(T.$,{variant:"outline",onClick:r,disabled:i,className:"w-full sm:w-auto border-neutral-200 dark:border-neutral-800",children:"Cancel"})}),(0,a.jsx)(x.P.div,{variants:u,className:"w-full sm:w-auto",children:(0,a.jsx)(eb,{onClick:p,disabled:i,className:"w-full sm:w-auto",children:i?(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Processing..."]}):(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(ei.A,{className:"w-4 h-4 mr-2"}),"Request Refund"]})})})]})]})})})}var eh=t(45583);function ef({isOpen:e,onClose:r,onCancelSubscription:t,isLoading:i,isWithinRefundWindow:n,subscriptionStatus:o,effectiveCancellationDate:l,authenticatedSubscription:c}){let[d,p]=(0,s.useState)(!1),b={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},m=async e=>{await t(e)};return(0,a.jsx)(M.lG,{open:e,onOpenChange:e=>{e||r()},children:(0,a.jsx)(M.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl",children:(0,a.jsxs)(x.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,staggerChildren:.1}}},className:"relative overflow-hidden",children:[d&&(0,a.jsx)(ed,{variant:"gold",intensity:"medium"}),(0,a.jsxs)(M.c7,{className:"p-6 pb-2 z-10 relative",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-red-500/15 text-red-500",children:(0,a.jsx)(k.A,{className:"w-5 h-5"})}),(0,a.jsx)(ec.E,{variant:"outline",className:"border-red-500/50 text-red-500 px-2 py-0.5",children:"Subscription Management"})]}),(0,a.jsx)(M.L3,{className:"text-2xl font-bold",children:"Manage Subscription"})]}),(0,a.jsxs)("div",{className:"p-6 pt-2 space-y-4 z-10 relative",children:[(0,a.jsx)(x.P.p,{variants:b,className:"text-muted-foreground",children:"Select an action for your subscription"}),(0,a.jsxs)(x.P.div,{variants:b,className:"space-y-3",children:["active"===o&&(0,a.jsx)(x.P.div,{whileHover:{y:-2},whileTap:{scale:.98},className:(0,u.cn)("p-4 rounded-lg border border-neutral-200 dark:border-neutral-800","bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80","transition-all duration-200 cursor-pointer",(i||!!l)&&"opacity-50 cursor-not-allowed"),onClick:()=>!i&&!l&&m(!1),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-amber-50 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400 mt-0.5",children:(0,a.jsx)(P.A,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Cancel at End of Billing Cycle"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Continue using until your current period ends"})]})]})}),"active"===o&&(0,a.jsx)(x.P.div,{whileHover:{y:-2},whileTap:{scale:.98},className:(0,u.cn)("p-4 rounded-lg border border-neutral-200 dark:border-neutral-800","bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80","transition-all duration-200 cursor-pointer",i&&"opacity-50 cursor-not-allowed"),onClick:()=>!i&&m(!0),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 mt-0.5",children:(0,a.jsx)(eh.A,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Cancel Immediately"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Cancel now with no refund for current period"})]})]})}),"active"===o&&n&&(0,a.jsx)(x.P.div,{whileHover:{y:-2},whileTap:{scale:.98},className:(0,u.cn)("p-4 rounded-lg border border-neutral-200 dark:border-neutral-800","bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80","transition-all duration-200 cursor-pointer",i&&"opacity-50 cursor-not-allowed"),onClick:()=>!i&&m(!0),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 mt-0.5",children:(0,a.jsx)(S.A,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Cancel with Refund"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Cancel immediately and request a refund"})]})]})}),"authenticated"===o&&c?.id&&(0,a.jsx)(x.P.div,{whileHover:{y:-2},whileTap:{scale:.98},className:(0,u.cn)("p-4 rounded-lg border border-neutral-200 dark:border-neutral-800","bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80","transition-all duration-200 cursor-pointer",i&&"opacity-50 cursor-not-allowed"),onClick:()=>!i&&m(!0),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 mt-0.5",children:(0,a.jsx)(J.A,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Cancel Future Subscription"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Cancel your upcoming subscription"})]})]})})]}),("active"===o||"authenticated"===o)&&(0,a.jsx)(x.P.div,{variants:b,children:(0,a.jsxs)(el.Fc,{className:"bg-white/50 dark:bg-black/50 border border-red-200 dark:border-red-800/50",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 text-red-500"}),(0,a.jsx)(el.XL,{className:"font-medium",children:"Important"}),(0,a.jsx)(el.TN,{className:"text-sm",children:(0,a.jsxs)("ul",{className:"list-disc pl-4 space-y-1",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Cancel at End of Billing Cycle:"})," You'll keep access to premium features until your current billing period ends."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Cancel Immediately:"})," Your access to premium features will end now with no refund for the current period."]}),n&&(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Cancel with Refund:"})," Your access will end now and you'll receive a refund for the current period."]})]})})]})})]}),(0,a.jsx)(M.Es,{className:"p-6 pt-2 flex justify-center",children:(0,a.jsx)(x.P.div,{variants:b,className:"w-full sm:w-auto",children:(0,a.jsx)(eb,{variant:"outline",onClick:r,disabled:i,className:"w-full sm:w-auto",children:i?(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Processing..."]}):(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(ei.A,{className:"w-4 h-4 mr-2"}),"Close"]})})})})]})})})}function eg({status:e,isEligibleForRefund:r=!1,onCancelSubscription:t,onPauseSubscription:i,onResumeSubscription:n,onRequestRefund:o,subscriptionId:l,planId:c=""}){let[d,u]=(0,s.useState)(!1),[p,b]=(0,s.useState)(!1),[m,h]=(0,s.useState)(!1),[f]=(0,s.useState)("normal"),[g,y]=(0,s.useState)(null),[_,w]=(0,s.useState)(!1),N=async()=>{if(i){u(!0);try{await i()}catch(e){console.error("Error pausing subscription:",e)}finally{u(!1)}}},j=async()=>{if(n){u(!0);try{await n()}catch(e){console.error("Error resuming subscription:",e)}finally{u(!1)}}},C=async()=>{if(o){u(!0);try{await o(f),h(!1)}catch(e){console.error("Error requesting refund:",e)}finally{u(!1)}}},S=G.SubscriptionStateManager.isFreeStatus(e,c),P="paused"===e||e===G.dH.HALTED,I=!S||P;console.log("[SUBSCRIPTION_ACTIONS] Button visibility logic:",{status:e,planId:c,isFreePlan:S,isPausedSubscription:P,shouldShowButtons:I,subscriptionId:l});let R=[G.dH.ACTIVE,G.dH.AUTHENTICATED,"paused",G.dH.HALTED],E=[G.dH.ACTIVE,G.dH.CANCELLED],T=I&&R.includes(e)&&t,D=I&&e===G.dH.ACTIVE&&i,O=I&&("paused"===e||e===G.dH.HALTED)&&n,$=I&&r&&E.includes(e)&&o;if(!T&&!D&&!O&&!$)return null;let U=[D&&{id:"pause",icon:(0,a.jsx)(F,{className:"h-5 w-5"}),label:"Pause Subscription",description:"Temporarily pause your subscription",color:"bg-amber-500",textColor:"text-amber-600 dark:text-amber-400",bgColor:"bg-amber-50 dark:bg-amber-900/20",borderColor:"border-amber-100 dark:border-amber-800/30",action:N},O&&{id:"resume",icon:(0,a.jsx)(et,{className:"h-5 w-5"}),label:"Resume Subscription",description:"Resume your paused subscription",color:"bg-green-500",textColor:"text-green-600 dark:text-green-400",bgColor:"bg-green-50 dark:bg-green-900/20",borderColor:"border-green-100 dark:border-green-800/30",action:j},$&&{id:"refund",icon:(0,a.jsx)(A.A,{className:"h-5 w-5"}),label:"Request Refund",description:"Request a refund for your subscription",color:"bg-purple-500",textColor:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-50 dark:bg-purple-900/20",borderColor:"border-purple-100 dark:border-purple-800/30",action:()=>h(!0)},T&&{id:"cancel",icon:(0,a.jsx)(k.A,{className:"h-6 w-6"}),label:"Cancel Subscription",description:"Cancel your subscription permanently",color:"bg-red-500",textColor:"text-red-600 dark:text-red-400",bgColor:"bg-red-50 dark:bg-red-900/20",borderColor:"border-red-100 dark:border-red-800/30",action:()=>b(!0)}].filter(Boolean);return(0,a.jsx)(x.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"w-full",children:(0,a.jsxs)(B.Zp,{className:"overflow-hidden backdrop-blur-sm bg-white/80 dark:bg-black/50 border border-neutral-200/80 dark:border-neutral-800/80 shadow-sm",children:[(0,a.jsx)(B.aR,{className:"pb-2 bg-gradient-to-br from-white/90 to-white/70 dark:from-black/70 dark:to-black/50 border-b border-neutral-200/80 dark:border-neutral-800/80",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.P.div,{whileHover:{rotate:360},transition:{duration:.5},className:"p-2 rounded-full bg-primary/10 text-primary",children:(0,a.jsx)(ea.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(B.ZB,{className:"text-xl",children:"Manage Subscription"}),(0,a.jsx)(B.BT,{children:"Manage your subscription settings and payment methods"})]})]})}),(0,a.jsxs)(B.Wu,{className:"p-4",children:[(0,a.jsx)("div",{className:"flex flex-col items-center",children:U.map(e=>(0,a.jsxs)(x.P.div,{whileHover:{y:-5},whileTap:{scale:.98},onHoverStart:()=>y(e.id),onHoverEnd:()=>y(null),className:`relative overflow-hidden rounded-xl ${e.bgColor} border ${e.borderColor} p-4 cursor-pointer transition-all duration-300 w-full max-w-sm mx-auto mb-4 ${"cancel"===e.id?"shadow-md hover:shadow-lg":""}`,onClick:e.action,children:[(0,a.jsx)(v.N,{children:g===e.id&&(0,a.jsx)(x.P.div,{initial:{opacity:0,scale:0},animate:{opacity:.05,scale:1},exit:{opacity:0,scale:0},transition:{duration:.3},className:`absolute inset-0 rounded-full ${e.color}`,style:{top:"50%",left:"50%",width:"150%",height:"150%",transform:"translate(-50%, -50%)"}})}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col items-center text-center space-y-2",children:[(0,a.jsx)("div",{className:`rounded-full ${e.color} text-white ${"cancel"===e.id?"p-4":"p-3"}`,children:e.icon}),(0,a.jsx)("h3",{className:`font-medium ${e.textColor} ${"cancel"===e.id?"text-lg":""}`,children:e.label}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.id))}),(0,a.jsx)(ef,{isOpen:p,onClose:()=>b(!1),onCancelSubscription:async e=>{if(b(!1),await new Promise(e=>setTimeout(e,300)),w(e),t)try{u(!0),await t(e)}catch(e){console.error("Error cancelling subscription:",e)}finally{u(!1)}},isLoading:d,isWithinRefundWindow:r,subscriptionStatus:e,effectiveCancellationDate:null,authenticatedSubscription:"authenticated"===e?{id:"authenticated"}:null}),(0,a.jsx)(em,{isOpen:m,onClose:()=>h(!1),onRequestRefund:C,isLoading:d,isEligibleForRefund:r})]})]})})}function ex({activeTab:e,onChange:r}){return(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"relative inline-flex",children:[(0,a.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/30 rounded-full blur-md"}),(0,a.jsxs)("div",{className:"space-x-2 bg-white/80 dark:bg-black/50 backdrop-blur-sm p-1.5 rounded-full border border-neutral-200/50 dark:border-neutral-800/50 inline-flex shadow-md relative z-10",children:[(0,a.jsx)(T.$,{onClick:()=>r("overview"),variant:"overview"===e?"default":"ghost",size:"sm",className:`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${"overview"===e?"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md":"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"}`,children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"overview"===e?"font-medium":"font-normal",children:"Overview"})]})}),(0,a.jsx)(T.$,{onClick:()=>r("payments"),variant:"payments"===e?"default":"ghost",size:"sm",className:`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${"payments"===e?"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md":"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"}`,children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"payments"===e?"font-medium":"font-normal",children:"Payments"})]})})]})]})})}function ey({subscriptionId:e,status:r,planName:i,planId:n,planCycle:l,amount:c,currency:d="INR",nextBillingDate:u,lastPaymentDate:p,lastPaymentStatus:b,lastPaymentMethod:m,createdAt:h,expiresAt:x,pausedAt:_,cancellationRequestedAt:v,cancelledAt:w,isEligibleForRefund:N=!1,subscriptionStartDate:j,subscriptionChargeTime:C,trialEndDate:k}){let S=(0,f.useRouter)(),[P,A]=(0,s.useState)(!1),[I,R]=(0,s.useState)("overview"),[E,T]=(0,s.useState)([]),[D,O]=(0,s.useState)(!1),[$,U]=(0,s.useState)(!1),z=(0,s.useRef)(!1),{status:W,setSubscriptionCancelled:M,setSubscriptionPaused:B,setSubscriptionResumed:q,setWaitingForWebhook:L,completeProcessing:X,resetProcessing:F}=o(),G=e=>{if(!e)return"unknown";let r=e.toLowerCase();return r.includes("active")?"active":r.includes("authenticated")?"authenticated":r.includes("pending")?"pending":r.includes("halted")||r.includes("paused")?"paused":r.includes("cancelled")?"cancelled":r.includes("completed")?"completed":r.includes("expired")?"expired":r.includes("initialized")?"initialized":r.includes("failed")?"payment_failed":"unknown"};(0,s.useCallback)(async()=>{if(e&&!$){O(!0);try{let r=await fetch(`/api/subscription/${e}/payments`);if(!r.ok)throw U(!0),Error("Failed to fetch payment history");let t=await r.json();if(!t.success)throw U(!0),Error(t.error||"Failed to fetch payment history");T(t.data||[]),U(!1)}catch(e){console.error("Error fetching payment history:",e),U(!0),z.current||(g.oR.error("Failed to load payment history. Please try again."),z.current=!0)}finally{O(!1)}}},[e,$]);let V=async(r=!1)=>{if(!e)return;A(!0),M("Cancelling your subscription...");let a=async()=>{let e=new KeyboardEvent("keydown",{key:"Escape",code:"Escape",keyCode:27,which:27,bubbles:!0});document.dispatchEvent(e),await new Promise(e=>setTimeout(e,300)),L("Waiting for confirmation from Razorpay. Please don't refresh the page.")};await a();try{let{cancelSubscription:e}=await t.e(6021).then(t.bind(t,66021)),a=await e(r);if(!a.success)throw Error(a.error||"Failed to cancel subscription");X(!0,r?"Your subscription has been cancelled immediately.":"Your subscription will be cancelled at the end of the billing cycle."),F(),setTimeout(()=>{S.refresh()},2e3)}catch(e){console.error("Error cancelling subscription:",e),X(!1,e instanceof Error?e.message:"Failed to cancel subscription"),setTimeout(()=>{F()},3e3)}finally{A(!1)}},Z=async()=>{if(e){A(!0),B("Pausing your subscription...");try{let{pauseSubscription:e}=await t.e(6021).then(t.bind(t,66021)),r=await e();if(!r.success)throw Error(r.error||"Failed to pause subscription");X(!0,"Your subscription has been paused successfully."),setTimeout(()=>{S.refresh()},2e3)}catch(e){console.error("Error pausing subscription:",e),X(!1,e instanceof Error?e.message:"Failed to pause subscription")}finally{A(!1)}}},K=async()=>{if(e){A(!0),q("Resuming your subscription...");try{let{activateSubscription:e}=await t.e(6021).then(t.bind(t,66021)),r=await e();if(!r||void 0===r.success)throw console.error("Invalid response from activateSubscription:",r),Error("Received invalid response from server");if(!r.success)throw Error(r.error||"Failed to resume subscription");g.oR.success("Your subscription has been resumed successfully."),g.oR.dismiss(),F(),setTimeout(()=>{S.refresh()},2e3)}catch(e){console.error("Error resuming subscription:",e),X(!1,e instanceof Error?e.message:"Failed to resume subscription")}finally{A(!1)}}},J=async e=>{g.oR.info("Refund Request Process",{description:"Please contact our support team via email to request a refund for payment issues or subscription problems.",duration:5e3}),F()};return e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(H,{isVisible:"waiting_for_webhook"===W,message:"Waiting for confirmation from Razorpay",description:"Please wait while we receive webhook confirmation. This may take a moment."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,a.jsx)(y.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"My Subscription"}),(0,a.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Manage your subscription details and payment information"})]})]}),(0,a.jsx)("div",{className:"mb-8 w-full flex justify-center",children:(0,a.jsx)(ex,{activeTab:I,onChange:R})}),"overview"===I&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(Y,{subscriptionId:e,status:G(r),planName:i,planCycle:l,amount:c,currency:d,nextBillingDate:u,lastPaymentDate:p,lastPaymentMethod:m,createdAt:h,expiresAt:x,pausedAt:_,cancellationRequestedAt:v,cancelledAt:w,isEligibleForRefund:N,subscriptionStartDate:j,subscriptionChargeTime:C,trialEndDate:k}),(0,a.jsx)(eg,{subscriptionId:e,status:G(r),isEligibleForRefund:N,onCancelSubscription:V,onPauseSubscription:Z,onResumeSubscription:K,onRequestRefund:J,planId:n})]}),"payments"===I&&(0,a.jsx)("div",{className:"space-y-6",children:"free"!==n?(0,a.jsx)(er,{subscriptionId:e,planId:n}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-xl font-bold mb-4",children:"No Invoice History"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"Free plan users don't have invoices. Upgrade to a paid plan to see your invoice history."})]})})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"No Active Subscription"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"You don't have an active subscription. Subscribe to a plan to get started."}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(eb,{onClick:()=>S.push("/dashboard/business/plan"),size:"lg",className:"px-8 py-2",showArrow:!0,roundedFull:!0,children:"View Plans"})})]})}function e_({userId:e,currentSubscriptionId:r,subscriptionStatus:t,currentPlanDetails:s,currentPlanCycle:i,nextBillingDate:n,subscriptionStartDate:o,subscriptionExpiryTime:l,subscriptionChargeTime:c,lastPaymentMethod:d,cancellationRequestedAt:u,cancelledAt:p,trialEndDate:b,isEligibleForRefund:m,setActiveTab:h}){return r?(0,a.jsx)(ey,{userId:e,subscriptionId:r,status:t,planName:s?.name||"Unknown Plan",planId:s?.id||"free",planCycle:i,amount:s?parseInt(("monthly"===i?s.price:s.yearlyPrice||s.price).replace(/[^\d]/g,"")):0,currency:"INR",nextBillingDate:n,lastPaymentDate:o,lastPaymentStatus:"SUCCESS",lastPaymentMethod:d,createdAt:o,expiresAt:l,pausedAt:"paused"===t?o:null,cancellationRequestedAt:u,cancelledAt:p,isEligibleForRefund:m,subscriptionStartDate:o,subscriptionChargeTime:c,trialEndDate:b}):(0,a.jsx)("div",{className:"text-center py-12 px-4 border rounded-lg bg-muted/20",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-primary",children:[(0,a.jsx)("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),(0,a.jsx)("circle",{cx:"9",cy:"7",r:"4"}),(0,a.jsx)("path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}),(0,a.jsx)("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),(0,a.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"No Active Subscription"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md",children:"You don't have an active subscription yet. Choose a plan that fits your business needs to unlock premium features."}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(eb,{onClick:()=>h("plans"),size:"lg",className:"px-8 py-2",showArrow:!0,roundedFull:!0,children:"Browse Plans"})})]})})}var ev=t(46001);let ew={hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,staggerChildren:.1}}},eN={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},ej={hover:{scale:1.03},tap:{scale:.98}},eC=e=>{let r={className:"w-6 h-6 text-[var(--brand-gold)]"};return e.includes("Basic")?(0,a.jsx)(_.A,{...r}):e.includes("Growth")?(0,a.jsx)(ev.A,{...r}):e.includes("Pro")?(0,a.jsx)(es.A,{...r}):(0,a.jsx)(y.A,{...r})};function ek({isOpen:e,onClose:r,plan:t,trialEndDate:i,_onSubscribe:n,isLoading:l,razorpaySubscriptionId:c}){let{resetProcessing:d}=o(),p=!!c,b=!!(i&&new Date(i)>new Date),m=(0,s.useRef)(!0),[h,f]=(0,s.useState)(!1),[g,y]=(0,s.useState)(!1),[v,w]=(0,s.useState)(!1),[N,C]=(0,s.useState)(!1),[k,S]=(0,s.useState)(!1),P=!g&&(void 0!==l?l:v),A=P||k,I=!k&&!P,R=async()=>{if(console.log("\uD83D\uDD25 [DIALOG] BUTTON CLICKED - handleSubscribe called!"),console.log("[DIALOG] Button state:",{isProcessing:k,externalLoading:l,isButtonDisabled:A}),k||void 0!==l&&l)return void console.log("[DIALOG] Already processing subscription, ignoring duplicate click");try{S(!0),void 0===l&&w(!0),y(!1),console.log("[DIALOG] Starting subscription process with create/cancel flow..."),await n()}catch(e){console.error("[DIALOG] Subscription error:",e),void 0===l&&m.current&&w(!1),y(!1)}finally{S(!1)}};return(0,a.jsx)(M.lG,{open:e&&!g,onOpenChange:t=>{if(console.log("[DIALOG] onOpenChange called with:",t,{canCloseDialog:I,isRazorpayOpen:g,isDialogJustOpened:N,isOpen:e}),!t&&N)return void console.log("[DIALOG] Dialog close ignored - dialog was just opened, preventing immediate closure");t||!I||g?t||I?!t&&g?console.log("[DIALOG] Dialog close ignored - Razorpay modal is open"):t&&!e&&console.log("[DIALOG] Dialog trying to open but isOpen is false - this might indicate a state issue"):console.log("[DIALOG] Dialog closing prevented - processing UPI/netbanking subscription"):(console.log("[DIALOG] Dialog closing allowed"),void 0===l&&w(!1),d(),r())},children:(0,a.jsx)(M.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl",children:(0,a.jsxs)(x.P.div,{initial:"hidden",animate:"visible",variants:ew,className:"relative overflow-hidden",children:[h&&(0,a.jsx)(ed,{variant:"gold",intensity:"medium"}),(0,a.jsx)(M.c7,{className:"p-6 pb-4 border-b border-neutral-200 dark:border-neutral-800",children:(0,a.jsxs)(x.P.div,{variants:eN,className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-[var(--brand-gold)]/15",children:eC(t.name)}),(0,a.jsxs)("div",{children:[(0,a.jsx)(M.L3,{className:"text-xl font-semibold text-foreground",children:t.name}),t.recommended&&(0,a.jsx)(ec.E,{variant:"outline",className:"mt-1 text-xs bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] border-[var(--brand-gold)]/20",children:"Recommended"})]})]})}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)(x.P.div,{variants:eN,className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-foreground",children:t.price}),(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:t.period})]}),t.savings&&(0,a.jsxs)("div",{className:"text-green-600 dark:text-green-400 text-sm font-medium flex items-center gap-1.5",children:[(0,a.jsx)(ei.A,{className:"w-4 h-4"}),t.savings]})]}),(0,a.jsx)(x.P.div,{variants:eN,children:(0,a.jsxs)("div",{className:"text-xs text-muted-foreground p-3 bg-neutral-100 dark:bg-neutral-900 rounded-lg",children:[(0,a.jsx)("p",{className:"mb-1 font-medium",children:p?"Plan Switch Notice:":"Subscription Note:"}),(0,a.jsx)("p",{children:p?"Switching plans will cancel your current subscription and create a new one. You will lose access to your previous plan benefits immediately.":b?"Your subscription will be created and payment will be processed when your trial ends.":"A new subscription will be created for the selected plan."})]})})]}),(0,a.jsx)(x.P.div,{variants:eN,children:(0,a.jsxs)(M.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 p-6 pt-4 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-950",children:[(0,a.jsx)(x.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,a.jsx)(T.$,{variant:"outline",onClick:()=>{I?(console.log("[DIALOG] Cancel button clicked - closing dialog"),void 0===l&&w(!1),d(),r()):console.log("[DIALOG] Cancel button clicked but dialog close prevented - processing UPI/netbanking subscription")},disabled:!I,className:"w-full sm:w-auto py-6 rounded-xl transition-all duration-200",children:"Cancel"})}),(0,a.jsx)(x.P.div,{whileHover:"hover",whileTap:"tap",variants:ej,className:"w-full sm:w-auto",children:(0,a.jsxs)(T.$,{onClick:R,className:(0,u.cn)("w-full relative overflow-hidden py-6","bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90","text-black dark:text-neutral-900 font-medium transition-all duration-200","shadow-[0_0_15px_rgba(var(--brand-gold-rgb),0.5)] hover:shadow-[0_0_20px_rgba(var(--brand-gold-rgb),0.7)]","rounded-xl"),disabled:A,children:[h&&!P&&!k&&(0,a.jsx)(x.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear"}}),h&&!P&&!k&&(0,a.jsx)("div",{className:"absolute inset-0 w-full h-full opacity-75 pointer-events-none",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-[var(--brand-gold)]/20 blur-md rounded-xl"})}),P||k?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:k?"Starting...":"Processing..."})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(_.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:p?"Switch Plan":"Subscribe Now"})]})]})})]})})]})})})}var eS=t(80428),eP=t(96987),eA=t(93929);function eI({isOpen:e,onClose:r,plan:t,onActivateTrial:i,isLoading:n,billingCycle:l="monthly"}){let{resetProcessing:c}=o(),[d,p]=(0,s.useState)(!1),[b,m]=(0,s.useState)(!1),h=(0,eP.P)(new Date,1),f=(0,eA.GP)(h,"dd MMM yyyy"),g=void 0!==n?n:b,_={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},v=async()=>{try{void 0===n&&m(!0),await i(),void 0===n&&m(!1)}catch(e){console.error("Error activating trial:",e),void 0===n&&m(!1)}};return(0,a.jsx)(M.lG,{open:e,onOpenChange:e=>{e||(void 0===n&&m(!1),c(),r())},children:(0,a.jsx)(M.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl",children:(0,a.jsxs)(x.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,staggerChildren:.1}}},className:"relative overflow-hidden",children:[d&&(0,a.jsx)(ed,{variant:"gold",intensity:"medium"}),(0,a.jsxs)(M.c7,{className:"p-6 pb-0 z-10 relative",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-[var(--brand-gold)]/15 text-[var(--brand-gold)]",children:(0,a.jsx)(eS.A,{className:"w-5 h-5"})}),(0,a.jsx)(ec.E,{variant:"outline",className:"border-[var(--brand-gold)]/50 text-[var(--brand-gold)] px-2 py-0.5",children:"Special Offer"})]}),(0,a.jsx)(M.L3,{className:"text-2xl font-bold",children:"1 Month Free Trial"})]}),(0,a.jsxs)("div",{className:"p-6 space-y-4",children:[(0,a.jsxs)(x.P.div,{variants:_,className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]",children:(0,a.jsx)(y.A,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:t.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[t.price,t.period]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-950/30 dark:to-purple-950/30 p-4 rounded-lg border border-blue-200/50 dark:border-blue-800/30 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(eS.A,{className:"w-5 h-5 text-[var(--brand-gold)] mt-0.5 flex-shrink-0"}),(0,a.jsxs)("p",{className:"text-sm",children:["As a first-time paid subscriber, you'll get a ",(0,a.jsx)("span",{className:"font-semibold text-[var(--brand-gold)]",children:"1-month free trial"})," of the ",t.name,". No payment required now!"]})]}),"yearly"===l&&(0,a.jsxs)("div",{className:"flex items-start gap-2 bg-amber-50 dark:bg-amber-950/30 border border-amber-200/50 dark:border-amber-800/30 p-3 rounded-lg text-sm text-amber-800 dark:text-amber-300",children:[(0,a.jsx)(ei.A,{className:"w-4 h-4 mt-0.5 flex-shrink-0"}),(0,a.jsx)("p",{children:"Your trial will be for the monthly plan. After your trial ends, you can switch to the yearly plan if you prefer."})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(J.A,{className:"w-4 h-4 text-muted-foreground"}),(0,a.jsxs)("span",{children:["Trial ends on ",(0,a.jsx)("span",{className:"font-medium",children:f})]})]})]})]}),(0,a.jsx)(x.P.div,{variants:_,className:"bg-muted/30 p-4 rounded-lg border border-neutral-200 dark:border-neutral-800",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["After your trial ends, you'll need to add a payment method to continue using the ",t.name,".","yearly"===l?" You can choose to continue with the monthly plan or switch to the yearly plan at that time.":" We'll remind you before your trial expires."]})})]}),(0,a.jsx)(x.P.div,{variants:_,children:(0,a.jsxs)(M.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 p-6 pt-4 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-950",children:[(0,a.jsx)(x.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,a.jsx)(T.$,{variant:"outline",onClick:r,className:"w-full sm:w-auto py-6 rounded-xl transition-all duration-200",children:"Cancel"})}),(0,a.jsx)(x.P.div,{whileHover:"hover",whileTap:"tap",variants:{hover:{scale:1.03},tap:{scale:.98}},className:"w-full sm:w-auto",children:(0,a.jsxs)(T.$,{onClick:v,className:(0,u.cn)("w-full relative overflow-hidden py-6","bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90","text-black dark:text-neutral-900 font-medium transition-all duration-200","shadow-[0_0_15px_rgba(var(--brand-gold-rgb),0.5)] hover:shadow-[0_0_20px_rgba(var(--brand-gold-rgb),0.7)]","rounded-xl"),disabled:g,children:[d&&!g&&(0,a.jsx)(x.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear"}}),d&&!g&&(0,a.jsx)("div",{className:"absolute inset-0 w-full h-full opacity-75 pointer-events-none",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-[var(--brand-gold)]/20 blur-md rounded-xl"})}),g?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"Activating..."})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(eS.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Activate Free Trial"})]})]})})]})})]})})})}function eR({dialogPlan:e,isPlanDialogOpen:r,isFirstTimePaidPlanDialogOpen:t,dialogLoading:s,billingCycle:i,subscriptionStatus:n,trialEndDate:o,razorpaySubscriptionId:l,setIsPlanDialogOpen:c,setIsFirstTimePaidPlanDialogOpen:d,setDialogLoading:u,handleDialogSubscribe:p,handleActivateTrial:b,resetProcessing:m}){return console.log("[DIALOG_MANAGER] Dialog states:",{dialogPlan:e?.id,isPlanDialogOpen:r,isFirstTimePaidPlanDialogOpen:t,dialogLoading:s}),(0,a.jsxs)(a.Fragment,{children:[e&&(0,a.jsx)(ek,{isOpen:r,onClose:()=>{console.log("[DIALOG_MANAGER] Closing SimplifiedPlanActionDialog"),c(!1),u(!1),m()},plan:e,trialEndDate:"authenticated"===n?null:o,_onSubscribe:p,isLoading:s,razorpaySubscriptionId:l}),e&&(0,a.jsx)(eI,{isOpen:t,onClose:()=>{d(!1),u(!1),m()},plan:e,billingCycle:i,onActivateTrial:b,isLoading:s})]})}var eE=t(6475);let eT=(0,eE.createServerReference)("7f717d13f4790849609e3952f2c335295c24779be8",eE.callServer,void 0,eE.findSourceMapURL,"activateTrialForFirstTimePaidSubscriber");function eD(e){let{userId:r,currentPlanDetails:i,subscriptionStatus:n,trialEndDate:l,monthlyPlans:c,yearlyPlans:d,currentSubscriptionId:u,nextBillingDate:p,cancellationRequestedAt:b,cancelledAt:m,planCycle:h,subscriptionStartDate:v,subscriptionExpiryTime:j,subscriptionChargeTime:C,isEligibleForRefund:k,lastPaymentMethod:S,razorpaySubscriptionId:P}=e,[A,R]=(0,s.useState)("subscription"),E=function({currentSubscriptionId:e,subscriptionStatus:r,currentPlanDetails:a,currentPlanCycle:i,lastPaymentMethod:n,razorpaySubscriptionId:l,isEligibleForFreeTrial:c}){let d=(0,f.useRouter)(),[u,p]=(0,s.useState)(null),[b,m]=(0,s.useState)(!1),[h,x]=(0,s.useState)(!1),[y,_]=(0,s.useState)(!1),[v,w]=(0,s.useState)(i),{completeProcessing:N,startProcessing:j,resetProcessing:C,setSubscriptionCreated:k,setFuturePaymentAuthorized:S}=o(),P=async e=>{let{createSubscription:r}=await t.e(6021).then(t.bind(t,66021)),a=await r("free",v);if(!a.success){let e=a.error||"Failed to switch to free plan. Please try again.";return g.oR.error("Subscription Error",{description:e}),N(!1,e),!1}return g.oR.success("Plan Updated",{description:"Your plan has been updated to the Free plan."}),k("Your plan has been updated to the Free plan."),_(!1),m(!1),setTimeout(()=>{d.refresh()},1500),!0};return{dialogPlan:u,isPlanDialogOpen:b,isFirstTimePaidPlanDialogOpen:h,dialogLoading:y,billingCycle:v,setDialogPlan:p,setIsPlanDialogOpen:m,setIsFirstTimePaidPlanDialogOpen:x,setDialogLoading:_,setBillingCycle:w,handlePlanAction:e=>{if(console.log("[PLAN_SWITCH_DEBUG] handlePlanAction called with plan:",e.id),console.log("[PLAN_SWITCH_DEBUG] Current subscription status:",r),console.log("[PLAN_SWITCH_DEBUG] isEligibleForFreeTrial:",c),console.log("[PLAN_SWITCH_DEBUG] currentPlanDetails:",a?.id),"enterprise"===e.id)return void d.push("/contact");p(e),console.log("[PLAN_SWITCH_DEBUG] Setting dialog plan to:",e.id),console.log("[PLAN_SWITCH_DEBUG] Current dialog states before opening:",{isPlanDialogOpen:b,isFirstTimePaidPlanDialogOpen:h,dialogLoading:y}),c&&"free"!==e.id&&("inactive"===r||a?.id==="free")?(console.log("[PLAN_SWITCH_DEBUG] Opening first-time paid plan dialog"),x(!0),console.log("[PLAN_SWITCH_DEBUG] First-time paid plan dialog should now be open")):(console.log("[PLAN_SWITCH_DEBUG] Opening regular subscription dialog"),m(!0),console.log("[PLAN_SWITCH_DEBUG] Regular subscription dialog should now be open"))},determineSubscriptionFlow:()=>({hasRazorpaySubscription:!!l,isCardPayment:!1,isUpiOrEmandateOrUnknown:!0,paymentMethod:n?.toLowerCase()||"unknown"}),validateSubscriptionRequest:t=>{if(e){if(("authenticated"===r||"active"===r)&&t.id===a?.id&&v===i)return g.oR.error("Current Plan Selected",{description:"You are already subscribed to this plan. Please choose a different plan or cycle."}),N(!1,"You are already subscribed to this plan. Please choose a different plan or cycle."),!1;if("paused"===r||"halted"===r)return g.oR.error("Subscription Paused",{description:"Your subscription is currently paused. Please resume your subscription before changing plans."}),N(!1,"Your subscription is currently paused. Please resume your subscription before changing plans."),!1}return!0},handleFreePlanSubscription:P,completeProcessing:N,startProcessing:j,resetProcessing:C,setSubscriptionCreated:k,setFuturePaymentAuthorized:S}}({currentSubscriptionId:u,subscriptionStatus:n,currentPlanDetails:i,currentPlanCycle:h,lastPaymentMethod:S,razorpaySubscriptionId:P,isEligibleForFreeTrial:null===l}),{handleDialogSubscribe:T}=function(e){let r=(0,f.useRouter)(),a=(0,s.useRef)(!1),{currentSubscriptionId:i,subscriptionStatus:n,currentPlanDetails:o,currentPlanCycle:l,lastPaymentMethod:c,razorpaySubscriptionId:d,trialEndDate:u,dialogPlan:p,billingCycle:b,setDialogLoading:m,setIsPlanDialogOpen:h,startProcessing:x,completeProcessing:y,resetProcessing:_,setSubscriptionCreated:v,setFuturePaymentAuthorized:w,validateSubscriptionRequest:N,handleFreePlanSubscription:j,setActiveTab:C}=e;return{handleDialogSubscribe:async()=>{if(console.log("\uD83D\uDD25 [SUBSCRIPTION_HANDLER] handleDialogSubscribe CALLED - BUTTON CLICKED!"),a.current)return void console.log("[SUBSCRIPTION_HANDLER] Request already in progress, ignoring duplicate call");if(console.log("[CENTRALIZED_SUBSCRIPTION] handleDialogSubscribe called"),console.log("[CENTRALIZED_SUBSCRIPTION] Current state:",{dialogPlan:p?.id,currentSubscriptionId:i,subscriptionStatus:n,lastPaymentMethod:c,razorpaySubscriptionId:d}),p){a.current=!0,m(!0);try{if("free"===p.id&&await j(p))return;if(!N(p))return void m(!1);let e=`req_${Date.now()}_${Math.random().toString(36).substring(2,11)}`;console.log("[SUBSCRIPTION_HANDLER] Creating subscription with request ID:",e);let a=async()=>await fetch("/api/subscriptions/centralized",{method:"POST",headers:{"Content-Type":"application/json","X-Request-ID":e},body:JSON.stringify({planId:p.id,planCycle:b})}),s=!!i&&("authenticated"===n||"active"===n),o=!!u&&new Date(u)>new Date;console.log("[SUBSCRIPTION_HANDLER] Trial detection:",{trialEndDate:u,isOnTrial:o,subscriptionStatus:n,hasExistingSubscription:s}),console.log("[SUBSCRIPTION_HANDLER] Using simplified flow - always execute immediately"),console.log("[SUBSCRIPTION_HANDLER] ✅ EXECUTING SUBSCRIPTION ACTION IMMEDIATELY"),console.log("[SUBSCRIPTION_HANDLER] Execution context:",{isOnTrial:o,hasExistingSubscription:s,subscriptionStatus:n,dialogPlan:p?.id,billingCycle:b});try{console.log("[SUBSCRIPTION_HANDLER] \uD83D\uDE80 Starting subscription creation process..."),x("Processing your subscription request..."),console.log("[SUBSCRIPTION_HANDLER] Making API call to create subscription");let e=await a();console.log("[SUBSCRIPTION_HANDLER] API call completed, parsing response");let s=await e.json();if(console.log("[SUBSCRIPTION_HANDLER] Response data:",s),!s.success){let e=s.error||"Failed to create subscription. Please try again.";g.oR.error("Subscription Error",{description:e}),y(!1,e);return}let i=s.data?.id||s.data?.subscription_id;if(console.log("[SUBSCRIPTION_HANDLER] Checking subscription ID:",{subscriptionId:i,hasId:!!i,dataKeys:Object.keys(s.data||{}),fullData:s.data}),i){console.log("[SUBSCRIPTION_HANDLER] Got subscription ID:",i),console.log("[SUBSCRIPTION_HANDLER] Proceeding to open Razorpay modal..."),x("Opening payment authorization...");let e=await fetch("/api/razorpay/key");if(!e.ok)throw Error("Failed to fetch Razorpay key ID");let a=await e.json();if(!a.success||!a.key_id)throw Error("Invalid Razorpay key ID response");console.log("[SUBSCRIPTION_HANDLER] Importing Razorpay SDK...");let{openRazorpaySubscriptionCheckout:s}=await t.e(1219).then(t.bind(t,21219));console.log("[SUBSCRIPTION_HANDLER] Opening Razorpay modal with:",{keyId:a.key_id,subscriptionId:i,planName:p.name,billingCycle:b}),console.log("[SUBSCRIPTION_HANDLER] About to call openRazorpaySubscriptionCheckout...");let n=await s(a.key_id,i,{name:"Dukancard",description:`${p.name} Plan (${b})`,theme:{color:"#6366f1"},prefill:{name:"",email:"",contact:""},notes:{}});if(console.log("[SUBSCRIPTION_HANDLER] openRazorpaySubscriptionCheckout completed with response:",n),console.log("[SUBSCRIPTION_HANDLER] Razorpay modal response:",n),n.razorpay_payment_id){x("Confirming payment...");let{confirmSubscriptionPayment:e}=await t.e(3684).then(t.bind(t,63684)),a=await e(i,n.razorpay_payment_id);if(!a.success)throw Error(a.error||"Failed to confirm payment");a.data?.is_future_payment===!0?w("Your subscription has been authorized. Payment will be processed when your trial ends."):g.oR.success("Payment Authorized",{description:`Your ${p.name} plan subscription has been authorized successfully.`}),v(`Your ${p.name} plan subscription has been created and authorized successfully.`),C("subscription"),h(!1),setTimeout(()=>{r.refresh()},1500)}}else throw Error("No subscription ID returned from server")}catch(e){console.error("Payment error:",e),e&&"object"==typeof e&&"cancelled"in e?(console.log("User cancelled the payment process"),g.oR.dismiss(),_()):(g.oR.error("Payment Authorization Failed",{description:e instanceof Error?e.message:"Failed to authorize payment. Please try again."}),y(!1,"Payment authorization failed. Please try again."))}}catch(r){console.error("Error in subscription handler:",r);let e=r instanceof Error?r.message:"An unexpected error occurred. Please try again.";g.oR.error("Subscription Error",{description:e}),y(!1,e)}finally{m(!1),a.current=!1}}}}}({currentSubscriptionId:u,subscriptionStatus:n,currentPlanDetails:i,currentPlanCycle:h,lastPaymentMethod:S,razorpaySubscriptionId:P,trialEndDate:l,dialogPlan:E.dialogPlan,billingCycle:E.billingCycle,setDialogLoading:E.setDialogLoading,setIsPlanDialogOpen:E.setIsPlanDialogOpen,startProcessing:E.startProcessing,completeProcessing:E.completeProcessing,resetProcessing:E.resetProcessing,setSubscriptionCreated:E.setSubscriptionCreated,setFuturePaymentAuthorized:E.setFuturePaymentAuthorized,validateSubscriptionRequest:E.validateSubscriptionRequest,handleFreePlanSubscription:E.handleFreePlanSubscription,setActiveTab:R}),{handleActivateTrial:D}=function({dialogPlan:e,billingCycle:r,setDialogLoading:t,setIsFirstTimePaidPlanDialogOpen:a,setActiveTab:s,startProcessing:i,completeProcessing:n,setSubscriptionCreated:o}){let l=(0,f.useRouter)();return{handleActivateTrial:async()=>{if(e){t(!0);try{i("Activating your free trial..."),a(!1);let c=await eT(e.id,r);if(!c.success){let e=c.error||"Failed to activate trial. Please try again.";g.oR.error("Trial Activation Error",{description:e}),n(!1,e);return}g.oR.success("Trial Activated",{description:`Your 1-month free trial of the ${e.name} plan has been activated successfully.`}),o(`Your ${e.name} plan trial has been activated successfully.`),s("subscription"),t(!1),setTimeout(()=>{l.refresh()},1500)}catch(r){let e=r instanceof Error?r.message:"An unexpected error occurred. Please try again.";g.oR.error("Trial Activation Error",{description:e}),n(!1,e),t(!1)}}}}}({dialogPlan:E.dialogPlan,billingCycle:E.billingCycle,setDialogLoading:E.setDialogLoading,setIsFirstTimePaidPlanDialogOpen:E.setIsFirstTimePaidPlanDialogOpen,setActiveTab:R,startProcessing:E.startProcessing,completeProcessing:E.completeProcessing,setSubscriptionCreated:E.setSubscriptionCreated});!function({setActiveTab:e}){(0,f.useRouter)(),(0,f.useSearchParams)()}({setActiveTab:R});let $="monthly"===E.billingCycle?c:d,U={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24,duration:.5}}};return(0,a.jsxs)(x.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.1,duration:.6,ease:"easeOut"}}},className:"space-y-6 max-w-7xl mx-auto",children:[l&&new Date(l)>new Date&&(0,a.jsx)(x.P.div,{variants:U,children:(0,a.jsx)(W,{trialEndDate:l})}),(0,a.jsx)(x.P.div,{variants:U,children:(0,a.jsx)("div",{className:"rounded-xl border bg-white dark:bg-black backdrop-blur-sm shadow-lg transition-all duration-300 relative overflow-hidden p-4 sm:p-5 md:p-6",children:(0,a.jsxs)(w,{tabs:[{id:"subscription",label:"My Subscription",icon:(0,a.jsx)(y.A,{className:"h-4 w-4"})},{id:"plans",label:"Plans",icon:(0,a.jsx)(_.A,{className:"h-4 w-4"})}],value:A,onChange:e=>{R(e)},className:"w-full",indicatorLayoutId:"mainPageTabs",children:[(0,a.jsx)(N,{value:"plans",className:"space-y-6",children:(0,a.jsx)(O,{subscriptionStatus:n,billingCycle:E.billingCycle,setBillingCycle:E.setBillingCycle,plans:$,currentPlanId:i?.id,currentPlanCycle:h,loadingStates:{},onPlanAction:E.handlePlanAction})}),(0,a.jsx)(N,{value:"subscription",className:"space-y-6",children:(0,a.jsx)(e_,{userId:r,currentSubscriptionId:u,subscriptionStatus:n,currentPlanDetails:i,currentPlanCycle:h,nextBillingDate:p,subscriptionStartDate:v,subscriptionExpiryTime:j,subscriptionChargeTime:C,lastPaymentMethod:S,cancellationRequestedAt:b,cancelledAt:m,trialEndDate:l,isEligibleForRefund:void 0!==k&&k,setActiveTab:R})})]})})}),(0,a.jsx)(eR,{dialogPlan:E.dialogPlan,isPlanDialogOpen:E.isPlanDialogOpen,isFirstTimePaidPlanDialogOpen:E.isFirstTimePaidPlanDialogOpen,dialogLoading:E.dialogLoading,billingCycle:E.billingCycle,subscriptionStatus:n,trialEndDate:l,razorpaySubscriptionId:P,setIsPlanDialogOpen:E.setIsPlanDialogOpen,setIsFirstTimePaidPlanDialogOpen:E.setIsFirstTimePaidPlanDialogOpen,setDialogLoading:E.setDialogLoading,handleDialogSubscribe:T,handleActivateTrial:D,resetProcessing:E.resetProcessing}),(0,a.jsx)(I,{})]})}function eO(e){return(0,a.jsx)(eD,{...e})}var e$=t(71463);function eU(){let e={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24,duration:.5}}};return(0,a.jsxs)(x.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.1,duration:.6,ease:"easeOut"}}},className:"space-y-6 max-w-7xl mx-auto",children:[(0,a.jsx)(x.P.div,{variants:e,children:(0,a.jsx)("div",{className:"rounded-lg border bg-white dark:bg-black p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(e$.E,{className:"h-10 w-10 rounded-full"}),(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsx)(e$.E,{className:"h-5 w-3/4"}),(0,a.jsx)(e$.E,{className:"h-4 w-1/2"})]})]})})}),(0,a.jsx)(x.P.div,{variants:e,children:(0,a.jsxs)("div",{className:"rounded-lg border bg-white dark:bg-black p-4 sm:p-5 md:p-6",children:[(0,a.jsxs)("div",{className:"flex space-x-2 border-b pb-2 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 relative",children:[(0,a.jsx)(e$.E,{className:"h-4 w-4 rounded-full"}),(0,a.jsx)(e$.E,{className:"h-5 w-32"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 relative",children:[(0,a.jsx)(e$.E,{className:"h-4 w-4 rounded-full"}),(0,a.jsx)(e$.E,{className:"h-5 w-20"})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"rounded-xl border p-4 sm:p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(e$.E,{className:"h-8 w-8 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(e$.E,{className:"h-5 w-40 mb-1"}),(0,a.jsx)(e$.E,{className:"h-4 w-24"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(e$.E,{className:"h-4 w-24"}),(0,a.jsx)(e$.E,{className:"h-4 w-32"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(e$.E,{className:"h-4 w-28"}),(0,a.jsx)(e$.E,{className:"h-4 w-24"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(e$.E,{className:"h-4 w-20"}),(0,a.jsx)(e$.E,{className:"h-4 w-36"})]})]})]}),(0,a.jsxs)("div",{className:"rounded-xl border p-4 sm:p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(e$.E,{className:"h-8 w-8 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(e$.E,{className:"h-5 w-36 mb-1"}),(0,a.jsx)(e$.E,{className:"h-4 w-48"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,r)=>(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(e$.E,{className:"h-4 w-32"}),(0,a.jsx)(e$.E,{className:"h-3 w-24"})]}),(0,a.jsx)(e$.E,{className:"h-6 w-16 rounded-full"})]},r))})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3 justify-center mt-4",children:[(0,a.jsx)(e$.E,{className:"h-10 w-32 rounded-full"}),(0,a.jsx)(e$.E,{className:"h-10 w-32 rounded-full"})]})]})]})})]})}function ez(e){(0,f.useRouter)();let[r,t]=(0,s.useState)(!1),[i,n]=(0,s.useState)(!1),[o,l]=(0,s.useState)(!0),c=i?`transition-all duration-300 ${r?"bg-primary/5 rounded-xl p-2 -m-2":""}`:"transition-all duration-300";return o?(0,a.jsx)(eU,{}):(0,a.jsx)("div",{className:c,children:(0,a.jsx)(eO,{...e})})}function eW(e){return(0,a.jsx)(h,{children:(0,a.jsx)(l,{children:(0,a.jsx)(ez,{...e})})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3589:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9907:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\plan\\\\PlanPageWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\PlanPageWrapper.tsx","default")},10022:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10941:(e,r,t)=>{Promise.resolve().then(t.bind(t,9907))},11997:e=>{"use strict";e.exports=require("punycode")},12468:(e,r,t)=>{"use strict";t.d(r,{$y:()=>b,EP:()=>f,GN:()=>c,Ve:()=>u,WX:()=>p,lC:()=>d});var a=t(91199);t(42087);var s=t(76881),i=t(7944);async function n(){let e=await (0,s.createClient)(),{data:r,error:t}=await e.auth.getUser();return t||!r.user?null:r.user}async function o(e,r="*"){let t,a=await (0,s.createClient)(),{count:i,error:n}=await a.from("business_profiles").select("*",{count:"exact",head:!0}).eq("id",e);if(n)console.error(`Error checking if business profile exists for user ${e}:`,n);else if(0===i)return console.error(`Business profile does not exist for user ${e}`),null;let l=r;"*"===r||r.includes("id")||(l=`id, ${r}`);let{data:c,error:d}=await a.from("business_profiles").select(l).eq("id",e).single();return d?(console.error(`Error fetching business profile for user ${e}:`,d),null):c?("object"==typeof c&&null!==c?"id"in(t={...c})||(t.id=e):(console.error(`Profile data is not an object: ${typeof c}`),t={id:e}),t):(console.error(`No business profile data returned for user ${e}`),null)}async function l(e,r="*"){let t=await (0,s.createClient)(),a=r;"*"===r||r.includes("id")||(a=`id, ${r}`),a.includes("has_active_subscription")&&(a=(a=(a=(a=a.replace("has_active_subscription","")).replace(/,\s*,/g,",")).replace(/,\s*$/g,"")).replace(/^\s*,/,""));let{data:i,error:n}=await t.from("payment_subscriptions").select(a).eq("business_profile_id",e).order("created_at",{ascending:!1}).limit(1).maybeSingle();return n?(console.error(`Error fetching subscription for user ${e}:`,n),null):i?"object"==typeof i&&null!==i?i:(console.error(`Subscription data is not an object: ${typeof i}`),null):null}async function c(e="*"){let r=await n();if(!r)return console.error("No user found in auth session"),{user:null,profile:null,subscription:null,error:"User not authenticated"};let t=["trial_end_date","subscription_start_date","cancellation_requested_at","subscription_paused_at"],a="id, has_active_subscription",s=e;if("*"!==e){let r=e.split(",").map(e=>e.trim()),i=r.filter(e=>!t.includes(e)),n=r.filter(e=>t.includes(e));n.length>0&&(a+=`, ${n.join(", ")}`),s=i.length>0?i.join(", "):"id"}let i=await o(r.id,a);if(!i)return console.error(`Could not fetch business profile for user ${r.id}`),{user:r,profile:null,subscription:null,error:"Business profile not found. Please complete onboarding first."};let d=await l(r.id,s);return{user:r,profile:i,subscription:d,error:null}}async function d(e,r){let{SubscriptionStateManager:a}=await t.e(4527).then(t.bind(t,44527));if(r)return a.isTrialStatus(r.subscription_status);let s=e.trial_end_date?new Date(e.trial_end_date):null;return null!==s&&s>new Date}async function u(){(0,i.revalidatePath)("/dashboard/business/plan"),(0,i.revalidatePath)("/dashboard/business")}async function p(e){return{success:!1,error:e}}async function b(e){return{success:!0,data:e}}async function m(e){return{success:!1,error:e}}async function h(e){return{success:!0,data:e}}async function f(e,r){let t=await (0,s.createClient)(),{data:a,error:i}=await t.from("payment_subscriptions").select("id").eq("business_profile_id",e).eq("razorpay_subscription_id",r).maybeSingle();return i?(console.error(`Error checking subscription ownership: ${i.message}`),!1):null!==a}(0,t(33331).D)([n,o,l,c,d,u,p,b,m,h,f]),(0,a.A)(n,"00d4d8ffb0bd97f113de12ba4267247cf5a3b0b04c",null),(0,a.A)(o,"609ab29520f078ddd91513228ac9d4ec796f522cb0",null),(0,a.A)(l,"609589fd4be3c72f3931a1f9c536368933ba8b83da",null),(0,a.A)(c,"401c3b86049e891affa0506ade0f31baeb2c3d455d",null),(0,a.A)(d,"60f426948b8391e4c3e4b292fc9538ce7045fbe366",null),(0,a.A)(u,"00efacc006752966ec1e7203fd5cdd059c8b2b6e30",null),(0,a.A)(p,"40fb7d10f8dedc0c32f3187581b16bbca4c23379d6",null),(0,a.A)(b,"40fea71ecc67261c02da2d172b819e0eef02d3b41a",null),(0,a.A)(m,"404c603e0f39faf7ce2bc96725f060ce4e5faa5728",null),(0,a.A)(h,"4055b2843ad80548c3c3a2f5c81868718477c83ed4",null),(0,a.A)(f,"6034db8eca1b50b78925a7248b950085ef2be979fc",null)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21761:(e,r,t)=>{"use strict";t.d(r,{z:()=>W,C:()=>M});var a=t(60687),s=t(43210),i=t(70569),n=t(98599),o=t(11273),l=t(3416),c=t(72942),d=t(65551),u=t(43),p=t(18853),b=t(83721),m=t(46059),h="Radio",[f,g]=(0,o.A)(h),[x,y]=f(h),_=s.forwardRef((e,r)=>{let{__scopeRadio:t,name:o,checked:c=!1,required:d,disabled:u,value:p="on",onCheck:b,form:m,...h}=e,[f,g]=s.useState(null),y=(0,n.s)(r,e=>g(e)),_=s.useRef(!1),v=!f||m||!!f.closest("form");return(0,a.jsxs)(x,{scope:t,checked:c,disabled:u,children:[(0,a.jsx)(l.sG.button,{type:"button",role:"radio","aria-checked":c,"data-state":j(c),"data-disabled":u?"":void 0,disabled:u,value:p,...h,ref:y,onClick:(0,i.m)(e.onClick,e=>{c||b?.(),v&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})}),v&&(0,a.jsx)(N,{control:f,bubbles:!_.current,name:o,value:p,checked:c,required:d,disabled:u,form:m,style:{transform:"translateX(-100%)"}})]})});_.displayName=h;var v="RadioIndicator",w=s.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:s,...i}=e,n=y(v,t);return(0,a.jsx)(m.C,{present:s||n.checked,children:(0,a.jsx)(l.sG.span,{"data-state":j(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:r})})});w.displayName=v;var N=s.forwardRef(({__scopeRadio:e,control:r,checked:t,bubbles:i=!0,...o},c)=>{let d=s.useRef(null),u=(0,n.s)(d,c),m=(0,b.Z)(t),h=(0,p.X)(r);return s.useEffect(()=>{let e=d.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==t&&r){let a=new Event("click",{bubbles:i});r.call(e,t),e.dispatchEvent(a)}},[m,t,i]),(0,a.jsx)(l.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:t,...o,tabIndex:-1,ref:u,style:{...o.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}N.displayName="RadioBubbleInput";var C=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],k="RadioGroup",[S,P]=(0,o.A)(k,[c.RG,g]),A=(0,c.RG)(),I=g(),[R,E]=S(k),T=s.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:s,defaultValue:i,value:n,required:o=!1,disabled:p=!1,orientation:b,dir:m,loop:h=!0,onValueChange:f,...g}=e,x=A(t),y=(0,u.jH)(m),[_,v]=(0,d.i)({prop:n,defaultProp:i??"",onChange:f,caller:k});return(0,a.jsx)(R,{scope:t,name:s,required:o,disabled:p,value:_,onValueChange:v,children:(0,a.jsx)(c.bL,{asChild:!0,...x,orientation:b,dir:y,loop:h,children:(0,a.jsx)(l.sG.div,{role:"radiogroup","aria-required":o,"aria-orientation":b,"data-disabled":p?"":void 0,dir:y,...g,ref:r})})})});T.displayName=k;var D="RadioGroupItem",O=s.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:o,...l}=e,d=E(D,t),u=d.disabled||o,p=A(t),b=I(t),m=s.useRef(null),h=(0,n.s)(r,m),f=d.value===l.value,g=s.useRef(!1);return s.useEffect(()=>{let e=e=>{C.includes(e.key)&&(g.current=!0)},r=()=>g.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,a.jsx)(c.q7,{asChild:!0,...p,focusable:!u,active:f,children:(0,a.jsx)(_,{disabled:u,required:d.required,checked:f,...b,...l,name:d.name,ref:h,onCheck:()=>d.onValueChange(l.value),onKeyDown:(0,i.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,i.m)(l.onFocus,()=>{g.current&&m.current?.click()})})})});O.displayName=D;var $=s.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...s}=e,i=I(t);return(0,a.jsx)(w,{...i,...s,ref:r})});$.displayName="RadioGroupIndicator";let U=(0,t(62688).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var z=t(96241);function W({className:e,...r}){return(0,a.jsx)(T,{"data-slot":"radio-group",className:(0,z.cn)("grid gap-3",e),...r})}function M({className:e,...r}){return(0,a.jsx)(O,{"data-slot":"radio-group-item",className:(0,z.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,a.jsx)($,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,a.jsx)(U,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},22889:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,metadata:()=>u});var a=t(37413);t(61120);var s=t(32032),i=t(39916),n=t(26919),o=t(20670),l=t(9907);let c=(e,r="monthly")=>{if(e)return(0,o.Nh)(r).find(r=>r.id===e)};async function d(e,r,a){if(!e)return"inactive";let{SubscriptionStateManager:s,SUBSCRIPTION_STATUS:i}=await t.e(5193).then(t.bind(t,65193)),n=new Date,o=e.trial_end_date?new Date(e.trial_end_date):null;if(r?.subscription_status){let e=r.subscription_status,t=r.plan_id||"free";if(console.log(`[PLAN_PAGE] Using database subscription status: ${e}, plan: ${t}`),r.subscription_paused_at)return"paused";if(e===i.TRIAL)if(o&&o>n)return"trial";else return s.shouldHaveActiveSubscription(e,t)?"active":"inactive";if("free"===t&&e===i.ACTIVE)return"inactive";switch(e){case i.ACTIVE:return"active";case i.AUTHENTICATED:return"authenticated";case i.PENDING:return"pending";case i.HALTED:return"halted";case i.CANCELLED:return"cancelled";case i.EXPIRED:return"expired";case i.COMPLETED:return"completed";default:return console.warn(`[PLAN_PAGE] Unknown subscription status: ${e}`),"inactive"}}return o&&o>n?(console.log(`[PLAN_PAGE] User is in trial period until: ${o}`),"trial"):(console.log("[PLAN_PAGE] No active subscription found, defaulting to inactive"),"inactive")}let u={title:"Manage Plan",robots:"noindex, nofollow"};async function p(){let e=await (0,s.createClient)(),{data:{user:r}}=await e.auth.getUser();if(!r)return(0,i.redirect)("/login?message=Authentication required");let{data:t,error:u}=await e.from("business_profiles").select("trial_end_date").eq("id",r.id).single();if(u||!t){console.error("Error fetching business profile:",u?.message);let e="Error Fetching Plan",r="Could not load your current plan details. Please try again later or contact support.";return u&&("PGRST116"===u.code?(e="Business Profile Not Found",r="Your business profile could not be found. Please complete the onboarding process first."):"42P01"===u.code?(e="Database Configuration Error",r="There was an issue with the database configuration. Please contact support."):"42703"===u.code&&(e="Database Schema Error",r="There was an issue with the database schema. Please contact support."),console.error("Profile error details:",{code:u.code,message:u.message,details:u.details,hint:u.hint})),(0,a.jsx)("div",{className:"space-y-8",children:(0,a.jsxs)("div",{className:"p-6 border border-destructive dark:border-red-700/50 rounded-lg bg-destructive/5",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-destructive mb-4",children:[(0,a.jsx)(n.A,{className:"w-5 h-5"}),(0,a.jsx)("h2",{className:"text-lg font-semibold",children:e})]}),(0,a.jsx)("p",{className:"text-foreground",children:r}),u&&(0,a.jsxs)("p",{className:"mt-2 text-sm text-muted-foreground",children:["Error code: ",u.code||"Unknown"]})]})})}let{data:p,error:b}=await e.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status, subscription_start_date, subscription_expiry_time, subscription_charge_time, last_payment_date, last_payment_method, subscription_paused_at, plan_id, plan_cycle, cancellation_requested_at, cancelled_at").eq("business_profile_id",r.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();b&&console.error("Error fetching subscription data:",b),console.log("[PLAN_PAGE_DEBUG] Subscription data from database:",{subscription_id:p?.razorpay_subscription_id,last_payment_method:p?.last_payment_method,subscription_status:p?.subscription_status,plan_id:p?.plan_id,plan_cycle:p?.plan_cycle});let m=await d(t,p,p?.subscription_status||null),h=p?.plan_cycle==="yearly"?"yearly":"monthly",f=c(p?.plan_id||null,h),g=(0,o.Nh)("monthly"),x=(0,o.Nh)("yearly"),y=p?.subscription_charge_time||null;try{"active"===m&&p?.subscription_charge_time?y=new Date(p.subscription_charge_time).toLocaleString("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0}):"authenticated"===m&&p?.subscription_start_date?y=new Date(p.subscription_start_date).toLocaleString("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0}):p?.subscription_expiry_time&&(y=new Date(p.subscription_expiry_time).toLocaleString("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0}))}catch(e){console.error("Error formatting next billing date:",e),y=null}let _=p?.razorpay_subscription_id||null,v="authenticated"===m?p?.subscription_start_date:null,w=p?.subscription_expiry_time||null,N=p?.cancellation_requested_at||null,j=p?.cancelled_at||null;return(0,a.jsx)(l.default,{userId:r.id,currentPlanDetails:f,subscriptionStatus:m,trialEndDate:t.trial_end_date,subscriptionEndDate:w,monthlyPlans:g,yearlyPlans:x,currentSubscriptionId:_,nextBillingDate:y,cancellationRequestedAt:N,cancelledAt:j,planCycle:h,authenticatedSubscriptionStartDate:v,subscriptionStartDate:p?.subscription_start_date||null,subscriptionExpiryTime:p?.subscription_expiry_time||null,subscriptionChargeTime:p?.subscription_charge_time||null,isEligibleForRefund:!1,lastPaymentMethod:p?.last_payment_method||null,razorpaySubscriptionId:p?.razorpay_subscription_id||null})}},26373:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var a=t(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:o="",children:l,iconNode:c,...d},u)=>(0,a.createElement)("svg",{ref:u,...n,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:i("lucide",o),...d},[...c.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(l)?l:[l]])),l=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...n},l)=>(0,a.createElement)(o,{ref:l,iconNode:r,className:i(`lucide-${s(e)}`,t),...n}));return t.displayName=`${e}`,t}},26919:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},35233:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var a=t(38398);class s{subscribeToTable(e,r,t={},a){let{event:s="*",filter:i,schema:n="public"}=t,o=a||`${e}-${s}-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;this.subscriptions.has(o)&&this.unsubscribe(o);let l=this.supabase.channel(o).on("postgres_changes",{event:s,schema:n,table:e,...i&&{filter:i}},r).subscribe();return this.subscriptions.set(o,l),{unsubscribe:()=>this.unsubscribe(o),channel:l}}subscribeToBusinessActivities(e,r,t){let a=`business-activities-${e}${t?`-${t}`:""}`;return this.subscribeToTable("business_activities",r,{event:"INSERT",filter:`business_profile_id=eq.${e}`},a)}subscribeToBusinessProfile(e,r,t){let a=`business-profile-${e}${t?`-${t}`:""}`;return this.subscribeToTable("business_profiles",r,{event:"*",filter:`id=eq.${e}`},a)}subscribeToMonthlyMetrics(e,r,t){let a=`monthly-metrics-${e}${t?`-${t}`:""}`;return this.subscribeToTable("monthly_visit_metrics",r,{event:"*",filter:`business_profile_id=eq.${e}`},a)}subscribeToPaymentSubscriptions(e,r,t){let a=`payment-subscriptions-${e}${t?`-${t}`:""}`;return this.subscribeToTable("payment_subscriptions",r,{event:"*",filter:`business_profile_id=eq.${e}`},a)}unsubscribe(e){let r=this.subscriptions.get(e);r&&(this.supabase.removeChannel(r),this.subscriptions.delete(e))}unsubscribeAll(){this.subscriptions.forEach((e,r)=>{this.supabase.removeChannel(e)}),this.subscriptions.clear()}getActiveSubscriptionCount(){return this.subscriptions.size}getActiveChannelIds(){return Array.from(this.subscriptions.keys())}constructor(){this.subscriptions=new Map,this.supabase=(0,a.U)()}}let i=new s},43649:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},45583:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},47033:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56085:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74354:(e,r,t)=>{"use strict";t.d(r,{Eb:()=>m,Iu:()=>u,M_:()=>f,WA:()=>h,cU:()=>p,dK:()=>d,n$:()=>b});var a=t(60687),s=t(43210),i=t(47033),n=t(14952),o=t(93661),l=t(96241),c=t(24934);let d=({className:e,...r})=>(0,a.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,l.cn)("mx-auto flex w-full justify-center",e),...r}),u=s.forwardRef(({className:e,...r},t)=>(0,a.jsx)("ul",{ref:t,className:(0,l.cn)("flex flex-row items-center gap-1",e),...r}));u.displayName="PaginationContent";let p=s.forwardRef(({className:e,...r},t)=>(0,a.jsx)("li",{ref:t,className:(0,l.cn)("",e),...r}));p.displayName="PaginationItem";let b=({className:e,isActive:r,size:t="icon",...s})=>(0,a.jsx)("a",{"aria-current":r?"page":void 0,className:(0,l.cn)((0,c.r)({variant:r?"outline":"ghost",size:t}),e,r&&"bg-muted hover:bg-muted pointer-events-none"),...s});b.displayName="PaginationLink";let m=({className:e,...r})=>(0,a.jsxs)(b,{"aria-label":"Go to previous page",size:"default",className:(0,l.cn)("gap-1 pl-2.5",e),...r,children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]});m.displayName="PaginationPrevious";let h=({className:e,...r})=>(0,a.jsxs)(b,{"aria-label":"Go to next page",size:"default",className:(0,l.cn)("gap-1 pr-2.5",e),...r,children:[(0,a.jsx)("span",{children:"Next"}),(0,a.jsx)(n.A,{className:"h-4 w-4"})]});h.displayName="PaginationNext";let f=({className:e,...r})=>(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,l.cn)("flex h-9 w-9 items-center justify-center",e),...r,children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"More pages"})]});f.displayName="PaginationEllipsis"},75244:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>a.B,"605cc4f7e9bbdd0eaaa2eae3d3f8f5b05914069395":()=>eo,"7f02caa19bcafe6bae86b1dc451230c99861e2cdf0":()=>Y,"7f0960190c6b1636eb84677ab012eeada3f8934a0a":()=>V,"7f19b2379bee7670cbbb662d8e94d8f8dd26ddb3b1":()=>q,"7f1f7961c9f8a58420526adffdaa27e3308dc75754":()=>ei,"7f56c16e65d0869c9d29f2e63fbc48236c32362379":()=>ea,"7f70bd80686f0c9d7fab146a4dbdac7588b07b4b79":()=>M,"7f717d13f4790849609e3952f2c335295c24779be8":()=>en,"7f7d456f1a5f4a8bb233e9d2b27c9af0f37f738be8":()=>L,"7f8a4faff06b2e1252cf75f98fd991807b6c6614e2":()=>G,"7f91924a1c5fb862e2c825711f6d44618a2c264c4e":()=>F,"7f98d85a4208358a3de69da1ebc9d14ac080c95371":()=>J,"7f9d54d21eac1e2add4865faf5c9684045ad61ed20":()=>X,"7f9fa7c8dbb4717caf9fbb8065c7b41fc881188656":()=>et,"7fc091e31ca53c4b62d6348b4fe33853f824667edf":()=>Z,"7fc9e7c70462f4ca8ea16fecf905b79d190e17bfc4":()=>H,"7fca6ebfe3c2d2c008e43e6f0940071857f65e5609":()=>Q,"7fd0ae55deb61cb5505c47884caaa7d0c2f9642171":()=>er,"7fd1428fdb38165c54dd2be1270fa30bb6afdc7638":()=>ee,"7fd3be778ca1daea3efdb63365e128d56dd7b08fde":()=>B,"7fe1e5383e9f7d8b9cbf540c021937d2fb9850d057":()=>es,"7fe59050d3b0088d7b9e7e29c9b24472f25477c42a":()=>K});var a=t(64275),s=t(91199);t(42087);var i=t(96763),n=t(12468),o=t(8174),l=t(33331);async function c(e,r,a,s){let l;try{l=(0,o.qD)(r,a)}catch(e){return(0,n.WX)(e instanceof Error?e.message:"Invalid plan selected")}let c=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:{user:d},error:u}=await c.auth.getUser();if(u||!d)return(0,n.WX)("User not authenticated");let{data:p,error:b}=await c.from("business_profiles").select("business_name, contact_email, phone, trial_end_date").eq("id",e).single();if(b)return console.error("Error fetching profile:",b),(0,n.WX)("Error fetching user profile");let m=null,h=p.contact_email||d.email||"";if(h){let{findCustomerByEmail:r,createCustomer:a}=await t.e(3291).then(t.bind(t,93291)),s=await r(h);if(s.success&&s.data)m=s.data.id;else{let r=await a({name:p.business_name||d.user_metadata?.full_name||"Customer",email:h,contact:p.phone||"",notes:{user_id:e,business_name:p.business_name||""}});r.success&&r.data?m=r.data.id:console.error("Failed to create customer:",r.error)}}let f={plan_id:l,total_count:"monthly"===a?120:10,customer_notify:!0,notes:{business_profile_id:e,plan_type:r,plan_cycle:a},start_at:void 0,...m&&{customer_id:m}};s&&s>new Date&&(f.start_at=Math.floor(s.getTime()/1e3));let g=await (0,i.createSubscription)(f);return g.success?(0,n.$y)({...g.data,requires_authorization:!0,message:"Please complete payment authorization to activate your subscription."}):(console.error(`Error creating subscription: ${g.error}`),(0,n.WX)(String(g.error||"Unknown error")))}async function d(e,r){let{user:a,profile:s,error:i}=await (0,n.GN)("has_active_subscription, trial_end_date");if(i)return(0,n.WX)(i);if(s&&s.has_active_subscription){let e=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:r,error:s}=await e.from("payment_subscriptions").select("subscription_status").eq("business_profile_id",a?.id||"").eq("subscription_status","halted").maybeSingle();return(s&&console.error("Error checking for halted subscription:",s),r)?(0,n.WX)("You have a paused subscription. Please resume your existing subscription before creating a new one."):(0,n.WX)("User already has an active subscription")}let o=s?.trial_end_date?new Date(s.trial_end_date):null,l=!!s&&(0,n.lC)(s);if(!a)return(0,n.WX)("User not found");let d=await c(a.id,e,r,l&&o?o:void 0);return((0,n.Ve)(),d.success&&d.data)?"requires_authorization"in d.data?d:(0,n.$y)({...d.data,requires_authorization:!0,message:"Please complete payment authorization to activate your subscription."}):!d.success&&d.error&&(console.error(`Error creating subscription: ${d.error}`),"string"==typeof d.error&&(d.error.includes("subscription_already_exists")||d.error.includes("SUBSCRIPTION_ALREADY_EXIST"))&&d.data)?(0,n.$y)({...d.data,requires_authorization:!0,message:"Please complete payment authorization for your existing subscription."}):d}async function u(e,r,a){let s,{user:l,error:c}=await (0,n.GN)();if(c)return(0,n.WX)(c);if(!l)return(0,n.WX)("User not found");let d=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:u,error:p}=await d.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",l.id).eq("razorpay_subscription_id",e).maybeSingle();if(p)return console.error("Error fetching subscription:",p),(0,n.WX)("Error fetching subscription details");if(!u?.razorpay_subscription_id)return(0,n.WX)("Subscription does not belong to user");let{data:b,error:m}=await d.from("business_profiles").select("business_name, contact_email, phone, trial_end_date").eq("id",l.id).single();if(m)return console.error("Error fetching profile:",m),(0,n.WX)("Error fetching user profile");let h=b?.trial_end_date?new Date(b.trial_end_date):null,f=h&&h>new Date,g=null,x=b.contact_email||l.email||"";if(x){let{findCustomerByEmail:e,createCustomer:r}=await t.e(3291).then(t.bind(t,93291)),a=await e(x);if(a.success&&a.data)console.log("[CANCEL_AND_CREATE] Found existing customer:",g=a.data.id);else{let e=await r({name:b.business_name||"Customer",email:x,contact:b.phone||"",notes:{user_id:l.id,business_name:b.business_name||""}});e.success&&e.data?console.log("[CANCEL_AND_CREATE] Created new customer:",g=e.data.id):console.error("Failed to create customer:",e.error)}}try{s=(0,o.qD)(r,a)}catch(e){return(0,n.WX)(e instanceof Error?e.message:"Invalid plan selected")}let y={plan_id:s,total_count:"monthly"===a?120:10,customer_notify:!0,notes:{business_profile_id:l.id,old_subscription_id:u.razorpay_subscription_id,plan_type:r,plan_cycle:a,is_plan_switch:"true"},start_at:void 0,...g&&{customer_id:g}};f&&h&&(y.start_at=Math.floor(h.getTime()/1e3));let _=await (0,i.createSubscription)(y);return _.success&&_.data?.id?((0,n.Ve)(),(0,n.$y)({message:"New subscription created. Please complete the payment authorization. Your previous subscription will be cancelled automatically after successful activation.",subscription_id:_.data.id,short_url:_.data.short_url,requires_authorization:!0})):(console.error("Error creating new subscription:",_.error),(0,n.WX)("Could not create new subscription"))}(0,l.D)([d,u]),(0,s.A)(d,"602fc42cf7faa777c98df3e27fa695393e5d8fdce6",null),(0,s.A)(u,"700d6b9fefc03908325de61c64af56a6cbddb19429",null);var p=t(63032);async function b(e=!1){let{user:r,profile:a,error:s}=await (0,n.GN)("has_active_subscription");if(s)return(0,n.WX)(s);let o=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:l,error:c}=await o.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",r?.id||"").eq("subscription_status",p.d.ACTIVE).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(c)return console.error("Error fetching active subscription:",c),(0,n.WX)("Error fetching subscription details");let d=l;if(!d){let{data:e,error:t}=await o.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",r?.id||"").eq("subscription_status",p.d.AUTHENTICATED).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(t)return console.error("Error fetching authenticated subscription:",t),(0,n.WX)("Error fetching subscription details");d=e}if(!a||!d?.razorpay_subscription_id)return(0,n.WX)("User does not have an active or authenticated subscription");if(d.subscription_status===p.d.AUTHENTICATED){console.log("[SUBSCRIPTION_DEBUG] Handling cancellation for authenticated subscription using pause endpoint");let{pauseSubscription:e}=await Promise.resolve().then(t.bind(t,96763)),r=await e(d.razorpay_subscription_id,"now",!0);r.success?console.log("[SUBSCRIPTION_DEBUG] Successfully paused/cancelled authenticated subscription"):(console.error("[SUBSCRIPTION_DEBUG] Error pausing authenticated subscription:",r.error),console.log("[SUBSCRIPTION_DEBUG] Proceeding with database update despite API failure"));let{error:a}=await o.from("payment_subscriptions").update({cancellation_requested_at:new Date().toISOString()}).eq("razorpay_subscription_id",d.razorpay_subscription_id);return a?(console.error("Error updating subscription status:",a),(0,n.WX)("Failed to update subscription status")):(console.log("[SUBSCRIPTION_CANCEL] Skipping immediate has_active_subscription update - will be handled by webhook"),(0,n.Ve)(),(0,n.$y)({message:"Your future subscription has been cancelled successfully."}))}let u=await (0,i.cancelSubscription)(d.razorpay_subscription_id,!e);if(!u.success)return"object"==typeof u.error&&null!==u.error&&"error"in u.error&&"object"==typeof u.error.error&&null!==u.error.error&&"description"in u.error.error&&"Subscription cannot be cancelled since no billing cycle is going on"===u.error.error.description?(console.log("[SUBSCRIPTION_DEBUG] Cannot cancel subscription - no billing cycle is going on."),(0,n.WX)("This subscription cannot be cancelled because no billing cycle is in progress. If you're on a trial, your subscription will automatically expire at the end of the trial period.")):(0,n.WX)("string"==typeof u.error?u.error:u.error?JSON.stringify(u.error):"Failed to cancel subscription");let{error:m}=await o.from("payment_subscriptions").update({cancellation_requested_at:new Date().toISOString()}).eq("razorpay_subscription_id",d.razorpay_subscription_id);return m&&console.error("Error updating subscription status:",m),(0,n.Ve)(),(0,n.$y)({message:e?"Your subscription has been cancelled immediately.":"Your subscription will be cancelled at the end of the current billing cycle."})}async function m(e,r){let a,{user:s,profile:l,error:c}=await (0,n.GN)("has_active_subscription");if(c)return(0,n.WX)(c);let d=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient());if(!s)return(0,n.WX)("User not found");let{data:u,error:p}=await d.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status, plan_id, plan_cycle").eq("business_profile_id",s.id).eq("subscription_status","active").order("created_at",{ascending:!1}).limit(1).maybeSingle();if(p)return console.error("Error fetching subscription:",p),(0,n.WX)("Error fetching subscription details");if(!l||!l.has_active_subscription||!u?.razorpay_subscription_id)return(0,n.WX)("User does not have an active subscription");if(u?.plan_id===e&&u?.plan_cycle===r)return(0,n.$y)({no_change:!0});try{a=(0,o.qD)(e,r)}catch(e){return(0,n.WX)(e instanceof Error?e.message:"Invalid plan selected")}let b=u.plan_cycle!==r,m={plan_id:a,schedule_change_at:"now",customer_notify:!0};if(b){console.log(`[SUBSCRIPTION_CHANGE] Different period detected (${u.plan_cycle} -> ${r}), fetching current subscription details`);let e=await (0,i.getSubscription)(u.razorpay_subscription_id);if(!e.success||!e.data)return console.error("[SUBSCRIPTION_CHANGE] Failed to fetch current subscription details:",e.error),(0,n.WX)("Failed to fetch current subscription details for period change");{let t,a=e.data.remaining_count;m.remaining_count=t="monthly"===u.plan_cycle&&"yearly"===r?Math.min(Math.ceil(a/12),10):"yearly"===u.plan_cycle&&"monthly"===r?Math.min(12*a,120):a,console.log(`[SUBSCRIPTION_CHANGE] Converting remaining_count from ${a} (${u.plan_cycle}) to ${t} (${r})`)}}let h=await (0,i.nV)(u.razorpay_subscription_id,m);if(!h.success)return(0,n.WX)("string"==typeof h.error?h.error:h.error?JSON.stringify(h.error):"Failed to update subscription plan");let{EdgeSubscriptionStateManager:f}=await t.e(1254).then(t.bind(t,1254)),g=f.shouldHaveActiveSubscription(u.subscription_status,e),{data:x,error:y}=await d.rpc("update_subscription_atomic",{p_subscription_id:u.razorpay_subscription_id,p_new_status:u.subscription_status,p_business_profile_id:s.id,p_has_active_subscription:g,p_additional_data:{plan_id:e,plan_cycle:r},p_webhook_timestamp:void 0});return(y||!x?.success)&&console.error("Error updating subscription atomically:",y||x?.error),(0,n.Ve)(),("growth"===u.plan_id&&"basic"===e||"growth"===u.plan_id&&"free"===e||"basic"===u.plan_id&&"free"===e)&&(console.log(`[PLAN_CHANGE] Plan downgrade detected from ${u.plan_id} to ${e}`),console.log("[PLAN_CHANGE] Product availability will be automatically handled by database trigger")),(0,n.$y)({message:"Your subscription plan has been updated successfully."})}async function h(e,r,a=!1){let s;console.log("[CARD_PAYMENT_DEBUG] changePlan function called");let{user:l,profile:c,error:d}=await (0,n.GN)("has_active_subscription, trial_end_date");if(d)return(0,n.WX)(d);let u=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:p,error:b}=await u.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status, plan_id, plan_cycle").eq("business_profile_id",l?.id||"").eq("subscription_status","active").order("created_at",{ascending:!1}).limit(1).maybeSingle();if(b)return console.error("Error fetching subscription:",b),(0,n.WX)("Error fetching subscription details");let m=c?.trial_end_date?new Date(c.trial_end_date):null,f=m&&m>new Date;if(p?.razorpay_subscription_id&&f){let a=await (0,i.getSubscription)(p.razorpay_subscription_id);if(a.success&&a.data?.status==="authenticated"){console.log("[SUBSCRIPTION_DEBUG] User is on trial with authenticated subscription, using switchAuthenticatedSubscription");let{switchAuthenticatedSubscription:a}=await Promise.resolve().then(t.bind(t,96851));return await a(p.razorpay_subscription_id,e,r)}}if(!c||!c.has_active_subscription||!p?.razorpay_subscription_id)return(0,n.WX)("User does not have an active subscription");if(p?.plan_id===e&&p?.plan_cycle===r)return(0,n.$y)({no_change:!0});console.log(`[PAYMENT_METHOD] Handling plan change for subscription ${p.razorpay_subscription_id}`);try{s=(0,o.qD)(e,r)}catch(e){return(0,n.WX)(e instanceof Error?e.message:"Invalid plan selected")}let g=await (0,i.getSubscription)(p.razorpay_subscription_id);if(!g.success)return(0,n.WX)("Failed to get subscription details");g.data?.current_end&&new Date(1e3*g.data.current_end).toISOString();let x=p.plan_cycle!==r,y={plan_id:s,schedule_change_at:"now",customer_notify:!0};if(x){if(console.log(`[SUBSCRIPTION_CHANGE] Different period detected (${p.plan_cycle} -> ${r}), using already fetched subscription details`),!g.success||!g.data)return console.error("[SUBSCRIPTION_CHANGE] Subscription details not available for period change"),(0,n.WX)("Failed to get subscription details for period change");{let e,t=g.data.remaining_count;y.remaining_count=e="monthly"===p.plan_cycle&&"yearly"===r?Math.min(Math.ceil(t/12),10):"yearly"===p.plan_cycle&&"monthly"===r?Math.min(12*t,120):t,console.log(`[SUBSCRIPTION_CHANGE] Converting remaining_count from ${t} (${p.plan_cycle}) to ${e} (${r})`)}}let _=await (0,i.nV)(p.razorpay_subscription_id,y);return((0,n.Ve)(),_.success)?(("pro"===p.plan_id&&("growth"===e||"basic"===e||"free"===e)||"growth"===p.plan_id&&("basic"===e||"free"===e)||"basic"===p.plan_id&&"free"===e)&&(console.log(`[PLAN_CHANGE] Plan downgrade detected from ${p.plan_id} to ${e}`),console.log("[PLAN_CHANGE] Product availability will be automatically handled by database trigger")),(0,n.$y)({message:"Your subscription plan has been updated successfully."})):(0,n.WX)("string"==typeof _.error?_.error:_.error?JSON.stringify(_.error):"Failed to update subscription")}(0,l.D)([b]),(0,s.A)(b,"4003ade3b6923f301d5a65613bf1a992697d53576e",null),(0,l.D)([m,h]),(0,s.A)(m,"606f301b9bd1816ac99cda9708278fb5914e2f7acf",null),(0,s.A)(h,"70852f8222ff7fb8074e9327679b253e9774a5b33b",null);var f=t(96851);async function g(e){let{user:r,error:a}=await (0,n.GN)();if(a||!r)return(0,n.WX)(a||"User not found");if(!await (0,n.EP)(r.id,e))return(0,n.WX)("Subscription does not belong to user");let s=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:o,error:l}=await s.from("payment_subscriptions").select("razorpay_subscription_id").eq("business_profile_id",r?.id||"").eq("subscription_status","active").order("created_at",{ascending:!1}).limit(1).maybeSingle();if(l||!o?.razorpay_subscription_id)return console.error("Error fetching subscription:",l),(0,n.WX)("Could not find active subscription");let c=await (0,i.cancelSubscription)(o.razorpay_subscription_id,!0);if(!c.success)return console.error("Error scheduling cancellation:",c.error),(0,n.WX)("Could not schedule cancellation");let{error:d}=await s.from("payment_subscriptions").update({subscription_status:"cancellation_scheduled",updated_at:new Date().toISOString()}).eq("razorpay_subscription_id",o.razorpay_subscription_id);return d&&console.error("Error updating subscription status:",d),(0,n.Ve)(),(0,n.$y)({message:"Your subscription is scheduled to cancel at the end of the billing cycle. You can subscribe to a new plan after the current one ends."})}async function x(e,r){let a,{user:s,profile:o,error:l}=await (0,n.GN)("has_active_subscription");if(l)return(0,n.WX)(l);let c=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:d,error:u}=await c.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",s?.id||"").eq("subscription_status","active").order("created_at",{ascending:!1}).limit(1).maybeSingle();if(u)return console.error("Error fetching active subscription:",u),(0,n.WX)("Error fetching subscription details");let p=d;if(!p){let{data:e,error:r}=await c.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",s?.id||"").eq("subscription_status","authenticated").order("created_at",{ascending:!1}).limit(1).maybeSingle();if(r)return console.error("Error fetching authenticated subscription:",r),(0,n.WX)("Error fetching subscription details");p=e}if(!o||!p?.razorpay_subscription_id)return(0,n.WX)("User does not have an active or authenticated subscription");if("pause"===e)a=await (0,n.$y)({message:"Pause action not implemented for Razorpay yet"});else if("resume"===e)a=await (0,n.$y)({message:"Resume action not implemented for Razorpay yet"});else if("cancel"===e)if("authenticated"===p.subscription_status){console.log("[SUBSCRIPTION_DEBUG] Handling cancellation for authenticated subscription in manageSubscription using pause endpoint");let{pauseSubscription:e}=await Promise.resolve().then(t.bind(t,96763)),r=await e(p.razorpay_subscription_id,"now",!0);r.success?console.log("[SUBSCRIPTION_DEBUG] Successfully paused/cancelled authenticated subscription"):(console.error("[SUBSCRIPTION_DEBUG] Error pausing authenticated subscription:",r.error),console.log("[SUBSCRIPTION_DEBUG] Proceeding with database update despite API failure"));let{error:s}=await c.from("payment_subscriptions").update({subscription_status:"cancelled",cancellation_requested_at:new Date().toISOString()}).eq("razorpay_subscription_id",p.razorpay_subscription_id);s?(console.error("Error updating subscription status:",s),a=await (0,n.WX)("Failed to update subscription status")):(console.log("[SUBSCRIPTION_MANAGE] Skipping immediate has_active_subscription update - will be handled by webhook"),a=await (0,n.$y)({message:"Your future subscription has been cancelled successfully."}))}else{let e=await (0,i.cancelSubscription)(p.razorpay_subscription_id,!!r?.cancelAtCycleEnd);if(e.success){let{error:e}=await c.from("payment_subscriptions").update({cancellation_requested_at:new Date().toISOString()}).eq("razorpay_subscription_id",p.razorpay_subscription_id);e&&console.error("Error updating subscription status:",e),a=await (0,n.$y)({message:r?.cancelAtCycleEnd?"Your subscription will be cancelled at the end of the current billing cycle.":"Your subscription has been cancelled immediately."})}else a=await (0,n.$y)({message:"string"==typeof e.error?e.error:e.error?JSON.stringify(e.error):"Failed to cancel subscription"})}else a="change_plan"===e?await (0,n.$y)({message:"Please use the appropriate function to change your subscription plan."}):await (0,n.$y)({message:`Unsupported action: ${e}`});return(0,n.Ve)(),a}(0,l.D)([g]),(0,s.A)(g,"400416c92e6b07e302700b08d80bc690112b846975",null),(0,l.D)([x]),(0,s.A)(x,"605fb44676962fd7eff28370b5f105696003ebf7ca",null);var y=t(76881),_=t(60043);async function v(){await (0,y.createClient)();try{let{user:e,profile:r,error:t}=await (0,n.GN)("has_active_subscription");if(t)return(0,n.WX)(t);if(!r||!r.has_active_subscription)return(0,n.WX)("User does not have an active subscription");let a=await (0,y.createClient)(),{data:s,error:o}=await a.from("payment_subscriptions").select("razorpay_subscription_id").eq("business_profile_id",r.id).eq("subscription_status","active");if(o||!s||0===s.length)return console.error("[RAZORPAY_ERROR] Error fetching subscription:",o),(0,n.WX)("Could not find an active subscription");let l=s[0].razorpay_subscription_id;if(!l)return(0,n.WX)("No active subscription found");let c=await (0,i.pauseSubscription)(l,"now");if(!c.success)return console.error("[RAZORPAY_ERROR] Error pausing subscription:",c.error),(0,n.WX)("Failed to pause subscription");let{data:d,error:u}=await a.from("payment_subscriptions").select("plan_id, plan_cycle").eq("razorpay_subscription_id",l).single();if(u)return console.error("[RAZORPAY_ERROR] Error fetching current subscription:",u),(0,n.WX)("Failed to fetch current subscription details");let b=new Date().toISOString();console.log(`[RAZORPAY_DEBUG] Pausing subscription: storing original plan ${d.plan_id}/${d.plan_cycle} and downgrading to free`);let{error:m}=await a.from("payment_subscriptions").update({subscription_status:p.d.ACTIVE,subscription_paused_at:b,updated_at:b,original_plan_id:d.plan_id,original_plan_cycle:d.plan_cycle,plan_id:"free",plan_cycle:"monthly"}).eq("razorpay_subscription_id",l);if(m)return console.error("[RAZORPAY_ERROR] Error updating subscription record:",m),(0,n.WX)("Failed to update subscription record");console.log("[RAZORPAY_DEBUG] Successfully downgraded subscription to free plan and stored original plan"),console.log("[RAZORPAY_DEBUG] Updating business profile: setting has_active_subscription to false");let{data:h,error:f}=await a.rpc("update_subscription_atomic",{p_subscription_id:l,p_new_status:p.d.ACTIVE,p_business_profile_id:e.id,p_has_active_subscription:!1,p_additional_data:{subscription_paused_at:b,status:"offline"},p_webhook_timestamp:void 0});return f||!h?.success?console.error("[RAZORPAY_ERROR] Error updating subscription atomically:",f||h?.error):console.log(`[RAZORPAY_DEBUG] Successfully updated subscription ${l} and business profile ${e.id} atomically`),(0,n.Ve)(),(0,n.$y)({message:"Subscription paused successfully",subscription:c.data})}catch(e){return console.error("[RAZORPAY_ERROR] Error in pauseUserSubscription:",e),(0,n.WX)(e instanceof Error?e.message:"An unknown error occurred")}}async function w(){let e=await (0,y.createClient)();try{let{user:r,profile:t,error:a}=await (0,n.GN)("has_active_subscription");if(a)return(0,n.WX)(a);if(!t)return(0,n.WX)("User profile not found");let{data:s,error:o}=await e.from("payment_subscriptions").select("razorpay_subscription_id, subscription_paused_at, original_plan_id, original_plan_cycle").eq("business_profile_id",r.id).not("subscription_paused_at","is",null).limit(1);if(o||!s||0===s.length)return console.error("[RAZORPAY_ERROR] Error fetching paused subscription:",o),(0,n.WX)("Could not find a paused subscription");let l=s[0].razorpay_subscription_id;if(!l)return(0,n.WX)("No paused subscription found");let c=await (0,i.fK)(l);if(!c.success)return console.error("[RAZORPAY_ERROR] Error resuming subscription:",c.error),(0,n.WX)("Failed to resume subscription");let d=s[0].original_plan_id,u=s[0].original_plan_cycle;if(!d||!u)return console.error("[RAZORPAY_ERROR] No original plan found for restoration"),(0,n.WX)("Cannot resume subscription: original plan not found");console.log(`[RAZORPAY_DEBUG] Resuming subscription: restoring original plan ${d}/${u}`);let{error:b}=await e.from("payment_subscriptions").update({plan_id:d,plan_cycle:u,subscription_paused_at:null,original_plan_id:null,original_plan_cycle:null,updated_at:new Date().toISOString()}).eq("razorpay_subscription_id",l);if(b)return console.error("[RAZORPAY_ERROR] Error restoring original plan:",b),(0,n.WX)("Failed to restore original plan");console.log(`[RAZORPAY_DEBUG] Successfully restored original plan ${d}/${u}`),console.log("[RAZORPAY_DEBUG] Updating business profile: setting has_active_subscription to true");let{data:m,error:h}=await e.rpc("update_subscription_atomic",{p_subscription_id:l,p_new_status:p.d.ACTIVE,p_business_profile_id:r.id,p_has_active_subscription:!0,p_additional_data:{},p_webhook_timestamp:void 0});h||!m?.success?console.error("[RAZORPAY_ERROR] Error updating subscription atomically:",h||m?.error):(console.log(`[RAZORPAY_DEBUG] Successfully updated subscription ${l} and business profile ${r.id} atomically`),console.log("[RAZORPAY_DEBUG] Note: Business profile status remains offline until user explicitly sets it back to online")),(0,n.Ve)();let f=await (0,n.$y)({message:"Subscription resumed successfully",subscription:c.data});return console.log("[RAZORPAY_DEBUG] Returning response from activateUserSubscription:",f),f}catch(r){console.error("[RAZORPAY_ERROR] Error in activateUserSubscription:",r);let e=await (0,n.WX)(r instanceof Error?r.message:"An unknown error occurred");return console.log("[RAZORPAY_DEBUG] Returning error response from activateUserSubscription:",e),e}}async function N(){try{let e,{user:r,profile:t,error:a}=await (0,n.GN)(`
      has_active_subscription,
      subscription_start_date,
      trial_end_date,
      cancellation_requested_at,
      subscription_paused_at
    `);if(a)return(0,n.WX)(a);if(!t)return(0,n.WX)("User profile not found");let s=await (0,y.createClient)(),{data:o,error:l}=await s.from("payment_subscriptions").select("*").eq("business_profile_id",r.id).order("created_at",{ascending:!1}).limit(1);l&&console.error("[RAZORPAY_ERROR] Error fetching subscription:",l);let c=null,d=null,u=null;if(o&&o.length>0&&(d=o[0].plan_id,u=o[0].plan_cycle,d&&u&&(e=(0,_.Nh)(u).find(e=>e.id===d)),o[0].razorpay_subscription_id)){let e=o[0].razorpay_subscription_id,r=await (0,i.getSubscription)(e);r.success&&(c=r.data)}return(0,n.$y)({...t,subscription:o&&o.length>0?o[0]:null,currentPlan:e,subscriptionStatus:c,isWithinRefundWindow:!1})}catch(e){return console.error("[RAZORPAY_ERROR] Error in getSubscriptionDetails:",e),(0,n.WX)(e instanceof Error?e.message:"An unknown error occurred")}}(0,l.D)([v,w,N]),(0,s.A)(v,"0000e15c1719658c205d3e481df3b7ac43814c02f5",null),(0,s.A)(w,"00c407420a03c814f27f65775fa96386fe3e64f93d",null),(0,s.A)(N,"00ce5b6fb4cbd9d1603cdbe456b7303e14bdacb958",null);let j=async(e,r,t,a)=>({success:!1,error:"Not implemented with Razorpay yet"}),C=async(e,r,t,a,s,i)=>({success:!1,error:"Not implemented with Razorpay yet"}),k=async(e,r,t)=>({success:!1,error:"Not implemented with Razorpay yet",data:void 0}),S=async(e,r)=>({success:!1,error:"Not implemented with Razorpay yet",data:void 0}),P=async(e,r,t,a,s)=>({success:!1,error:"Not implemented with Razorpay yet"});async function A(e,r,t,a){let s,{user:i,error:o}=await (0,n.GN)();if(o||!i)return(0,n.WX)(o||"User not authenticated");if(!await (0,n.EP)(i.id,e))return(0,n.WX)("User does not have this subscription");let l={};if(l[t.type]=t.details,"AUTH"===r){if(!a.sessionId)return(0,n.WX)("sessionId is required for AUTH payment type");s=await j(i.id,e,a.sessionId,l)}else{if(!a.amount||!a.scheduleDate)return(0,n.WX)("amount and scheduleDate are required for CHARGE payment type");s=await C(i.id,e,a.amount,a.scheduleDate,l,a.remarks)}return(0,n.Ve)(),s}async function I(e,r,t){return A(e,"AUTH",t,{sessionId:r})}async function R(e,r,t,a,s){return A(e,"CHARGE",a,{amount:r,scheduleDate:t,remarks:s})}async function E(e,r,t,a){let{user:s,error:i}=await (0,n.GN)();if(i||!s)return(0,n.WX)(i||"User not authenticated");if(!await (0,n.EP)(s.id,e))return(0,n.WX)("User does not have this subscription");if("RETRY"===t&&!a?.next_scheduled_time)return(0,n.WX)("next_scheduled_time is required for RETRY action");let o=await P(s.id,e,r,t,a);return(0,n.Ve)(),o}async function T(e,r,t){return E(e,r,"RETRY",{next_scheduled_time:t})}async function D(e,r){return E(e,r,"CANCEL")}async function O(e,r){let{user:t,error:a}=await (0,n.GN)();if(a||!t)return(0,n.WX)(a||"User not authenticated");if(!await (0,n.EP)(t.id,e))return(0,n.WX)("User does not have this subscription");let s=await k(t.id,e,r);return{success:s.success,data:s.data,error:s.error}}async function $(e){let{user:r,error:t}=await (0,n.GN)();if(t||!r)return(0,n.WX)(t||"User not authenticated");if(!await (0,n.EP)(r.id,e))return(0,n.WX)("User does not have this subscription");let a=await S(r.id,e);return{success:a.success,data:a.data,error:a.error}}(0,l.D)([A,I,R,E,T,T,D,O,$]),(0,s.A)(A,"7868f9b75da0fce3cf03ee5995e45aed9b996509fd",null),(0,s.A)(I,"70df848a69a942cea43205dbbaadb08a62b41d3b17",null),(0,s.A)(R,"7c177aae625a04d15ff68e1401679c63d076417596",null),(0,s.A)(E,"78104dcc5f2c2045aa21ef9ef436e71685d838732d",null),(0,s.A)(T,"7096f3d0a1f98e451772bf87ca11c939f9b6574dec",null),(0,s.A)(T,"7f0272e00bf5a2abfb451a603d8546f4e05972d1a3",null),(0,s.A)(D,"600fa583ffee10e2b63ff28dea3825c0e9bb7e98f4",null),(0,s.A)(O,"607a4728eb334f71e732b827aeda3f3f9ab0f3e83e",null),(0,s.A)($,"40dde1078b8072c03b1fffb9082fda5f7bd88e39af",null);var U=t(7944),z=t(11798);async function W(e,r){if("free"===e)return(0,n.WX)("Cannot activate trial for free plan");let t=await (0,y.createClient)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return console.error("Error fetching user:",s),(0,n.WX)("User not authenticated");let{data:i,error:o}=await t.from("business_profiles").select("trial_end_date").eq("id",a.id).single();if(o)return console.error("Error fetching profile:",o),(0,n.WX)("Error fetching user profile");if(null!==i.trial_end_date)return(0,n.WX)("You are not eligible for a free trial. You have previously subscribed to a paid plan.");let l=new Date(Date.now()+2592e6),c=z.ew.shouldHaveActiveSubscription(p.d.TRIAL,e);console.log(`[TRIAL_ACTIVATION] Trial activation for plan ${e}: has_active_subscription = ${c}`);try{let s={business_profile_id:a.id,subscription_status:"trial",plan_id:e,plan_cycle:"monthly",subscription_start_date:new Date().toISOString(),subscription_expiry_time:l.toISOString(),created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:i,error:o}=await t.rpc("update_subscription_atomic",{p_subscription_id:null,p_new_status:"trial",p_business_profile_id:a.id,p_has_active_subscription:c,p_additional_data:{...s,trial_end_date:l.toISOString()},p_webhook_timestamp:void 0});if(o||!i?.success)return console.error("Error activating trial atomically:",o||i?.error),(0,n.WX)("Error activating trial");return(0,U.revalidatePath)("/dashboard/business/plan"),(0,U.revalidatePath)("/dashboard/business"),(0,n.$y)({message:"Trial activated successfully",trialEndDate:l.toISOString(),planId:e,planCycle:"monthly",userSelectedPlanCycle:r})}catch(e){return console.error("Error in trial activation transaction:",e),(0,n.WX)("An unexpected error occurred while activating your trial")}}let M=d,B=u,q=b,L=h,H=m,X=g,F=x,G=f.switchAuthenticatedSubscription,Y=f.g,V=f._,Z=v,K=w,J=N,Q=A,ee=I,er=R,et=O,ea=E,es=T,ei=D,en=W;async function eo(e,r){try{console.log(`[SUBSCRIPTION_CONFIRM] Processing subscription ${e}`);let a=await (0,y.createClient)(),{data:{user:s},error:i}=await a.auth.getUser();if(i||!s)return console.error("[SUBSCRIPTION_CONFIRM] Authentication error:",i),(0,n.WX)("Authentication required");let{data:o,error:l}=await a.from("business_profiles").select("id, has_active_subscription").eq("id",s.id).single();if(l||!o)return console.error("[SUBSCRIPTION_CONFIRM] Profile error:",l),(0,n.WX)("Could not fetch business profile");let{data:c}=await a.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",s.id).order("created_at",{ascending:!1}).limit(1).maybeSingle(),{data:d}=await a.from("payment_subscriptions").select("id").eq("razorpay_subscription_id",e).maybeSingle();if(c?.razorpay_subscription_id&&c.razorpay_subscription_id!==e&&!d){let{getSubscription:r}=await Promise.resolve().then(t.bind(t,96763)),a=await r(e);if("expired"===c.subscription_status||"cancelled"===c.subscription_status);else{let t=!1;if(a.success){if(a.data?.notes&&"true"===a.data.notes.is_plan_switch)t=!0;else if(a.data?.start_at){let e=a.data.start_at,r=Math.floor(Date.now()/1e3);e>r&&(t=!0)}else if(a.data?.customer_id){let{data:e}=await r(c.razorpay_subscription_id);e&&e.customer_id===a.data.customer_id&&(t=!0)}}if(!t)return console.error(`[SUBSCRIPTION_CONFIRM] Subscription ID mismatch: ${c.razorpay_subscription_id} != ${e}`),(0,n.WX)("Subscription ID mismatch")}}let{data:u,error:p}=await a.from("payment_subscriptions").select("*").eq("razorpay_subscription_id",e).maybeSingle(),b=await (0,y.createClient)();if(p)return console.error("[SUBSCRIPTION_CONFIRM] Subscription error:",p),(0,n.WX)("Could not fetch subscription details");if(u){let a="active";try{let{getSubscription:r}=await Promise.resolve().then(t.bind(t,96763)),s=await r(e);if(s.success&&s.data){let e=s.data.start_at,r=Math.floor(Date.now()/1e3);e&&e>r&&(a="authenticated")}}catch(e){console.error("[SUBSCRIPTION_CONFIRM] Error checking future payment for update:",e)}let{getSubscription:i}=await Promise.resolve().then(t.bind(t,96763)),o=await i(e),l={last_payment_id:r,last_payment_date:new Date().toISOString(),subscription_status:a,updated_at:new Date().toISOString()};if(o.success&&o.data){let e=o.data;l.razorpay_customer_id=e.customer_id||null,l.subscription_start_date=e.current_start?new Date(1e3*e.current_start).toISOString():null,l.subscription_expiry_time=e.current_end?new Date(1e3*e.current_end).toISOString():null,l.subscription_charge_time=e.charge_at?new Date(1e3*e.charge_at).toISOString():null}let{data:c,error:d}=await b.rpc("update_subscription_atomic",{p_subscription_id:u.razorpay_subscription_id,p_new_status:u.subscription_status,p_business_profile_id:s.id,p_has_active_subscription:!0,p_additional_data:l,p_webhook_timestamp:void 0});if(d||!c?.success)return console.error("[SUBSCRIPTION_CONFIRM] Error updating subscription atomically:",d||c?.error),(0,n.WX)("Could not update subscription atomically")}else{console.log(`[SUBSCRIPTION_CONFIRM] No subscription record found for ${e}, creating one`);let{getSubscription:i}=await Promise.resolve().then(t.bind(t,96763)),o=await i(e);if(!o.success||!o.data)return console.error(`[SUBSCRIPTION_CONFIRM] Failed to get subscription details from Razorpay for ${e}`),(0,n.WX)("Failed to get subscription details from Razorpay");let l=o.data.notes?.plan_type,c=o.data.notes?.plan_cycle;if(!l||!c){let{getPlanByRazorpayPlanId:e}=await Promise.resolve().then(t.bind(t,8174)),r=e(o.data.plan_id);r?(l=r.id,c=r.razorpayPlanIds.monthly===o.data.plan_id?"monthly":"yearly"):(l="basic",c="monthly")}let d="active";try{let e=o.data.start_at,r=Math.floor(Date.now()/1e3);e&&e>r&&(d="authenticated")}catch(e){console.error("[SUBSCRIPTION_CONFIRM] Error checking future payment:",e)}let{data:u,error:p}=await a.from("payment_subscriptions").select("id, razorpay_subscription_id").eq("business_profile_id",s.id).maybeSingle();if(p)return console.error("[SUBSCRIPTION_CONFIRM] Error checking for existing business subscription:",p),(0,n.WX)("Could not check for existing subscription");let b={razorpay_subscription_id:e,razorpay_customer_id:o.data.customer_id||null,subscription_status:d,plan_id:l,plan_cycle:c,subscription_start_date:o.data.current_start?new Date(1e3*o.data.current_start).toISOString():null,subscription_expiry_time:o.data.current_end?new Date(1e3*o.data.current_end).toISOString():null,subscription_charge_time:o.data.charge_at?new Date(1e3*o.data.charge_at).toISOString():null,updated_at:new Date().toISOString(),last_payment_id:r,last_payment_date:new Date().toISOString()};if(u){let{data:r,error:s}=await a.from("payment_subscriptions").select("subscription_status").eq("id",u.id).single();s&&console.error("[SUBSCRIPTION_CONFIRM] Error fetching existing subscription details:",s);let i=r?.subscription_status==="expired"||r?.subscription_status==="cancelled";if(u.razorpay_subscription_id!==e&&!i&&u.razorpay_subscription_id){let e,a=r?.subscription_status||"active";if(console.log(`[SUBSCRIPTION_CONFIRM] Existing subscription ${u.razorpay_subscription_id} has status: ${a}`),"active"===a){console.log(`[SUBSCRIPTION_CONFIRM] Cancelling ACTIVE subscription ${u.razorpay_subscription_id}`);let{cancelSubscription:r}=await Promise.resolve().then(t.bind(t,96763));e=await r(u.razorpay_subscription_id,!1)}else if("authenticated"===a){console.log(`[SUBSCRIPTION_CONFIRM] Cancelling AUTHENTICATED subscription ${u.razorpay_subscription_id} using pause`);let{pauseSubscription:r}=await Promise.resolve().then(t.bind(t,96763));e=await r(u.razorpay_subscription_id,"now",!0)}else{console.log(`[SUBSCRIPTION_CONFIRM] Cancelling subscription ${u.razorpay_subscription_id} with status ${a} using cancel as fallback`);let{cancelSubscription:r}=await Promise.resolve().then(t.bind(t,96763));e=await r(u.razorpay_subscription_id,!1)}e.success?console.log(`[SUBSCRIPTION_CONFIRM] Successfully cancelled old subscription ${u.razorpay_subscription_id} (status: ${a})`):console.error(`[SUBSCRIPTION_CONFIRM] Error cancelling old subscription ${u.razorpay_subscription_id}:`,e.error)}else u.razorpay_subscription_id;let{error:o}=await a.from("payment_subscriptions").update(b).eq("id",u.id);if(o)return console.error("[SUBSCRIPTION_CONFIRM] Error updating existing subscription:",o),(0,n.WX)("Could not update existing subscription")}else{let e={...b,business_profile_id:s.id,created_at:new Date().toISOString()},{error:r}=await a.from("payment_subscriptions").insert(e);if(r){if(console.error("[SUBSCRIPTION_CONFIRM] Error creating subscription record:",r),"23505"!==r.code)return(0,n.WX)("Could not create subscription record");{console.log("[SUBSCRIPTION_CONFIRM] Unique constraint violation, trying to update instead");let{data:e,error:r}=await a.from("payment_subscriptions").select("id").eq("business_profile_id",s.id).single();if(r)return console.error("[SUBSCRIPTION_CONFIRM] Error fetching subscription in race condition:",r),(0,n.WX)("Could not create or update subscription record");let{error:t}=await a.from("payment_subscriptions").update(b).eq("id",e.id);if(t)return console.error("[SUBSCRIPTION_CONFIRM] Error updating subscription in race condition:",t),(0,n.WX)("Could not update subscription record in race condition")}}}}let{data:m,error:h}=await a.from("payment_subscriptions").select("subscription_status, plan_id").eq("razorpay_subscription_id",e).single();if(h)return console.error("[SUBSCRIPTION_CONFIRM] Error fetching current subscription status:",h),(0,n.WX)("Could not fetch current subscription status");let{SubscriptionStateManager:f}=await t.e(4527).then(t.bind(t,44527)),g=f.shouldHaveActiveSubscription(m.subscription_status,m.plan_id||"free"),{data:x,error:_}=await a.rpc("update_subscription_atomic",{p_subscription_id:e,p_new_status:m.subscription_status,p_business_profile_id:s.id,p_has_active_subscription:g,p_additional_data:{},p_webhook_timestamp:void 0});if(_||!x?.success)return console.error("[SUBSCRIPTION_CONFIRM] Atomic update error:",_||x?.error),(0,n.WX)("Could not update subscription and business profile atomically");(0,n.Ve)();let v=!1,w="Subscription payment confirmed successfully";try{let{getSubscription:r}=await Promise.resolve().then(t.bind(t,96763)),a=await r(e);if(a.success&&a.data){let e=a.data.start_at,r=Math.floor(Date.now()/1e3);e&&e>r&&(v=!0,w="Subscription authorized successfully. Payment will be processed when your trial ends.")}}catch(e){console.error("[SUBSCRIPTION_CONFIRM] Error checking future payment:",e)}return(0,n.$y)({message:w,subscription_id:e,payment_id:r,is_future_payment:v})}catch(e){return console.error("[SUBSCRIPTION_CONFIRM] Unexpected error:",e),(0,n.WX)(e instanceof Error?e.message:"An unexpected error occurred")}}(0,l.D)([M,B,q,L,H,X,F,G,Y,V,Z,K,J,Q,ee,er,et,ea,es,ei,en]),(0,s.A)(M,"7f70bd80686f0c9d7fab146a4dbdac7588b07b4b79",null),(0,s.A)(B,"7fd3be778ca1daea3efdb63365e128d56dd7b08fde",null),(0,s.A)(q,"7f19b2379bee7670cbbb662d8e94d8f8dd26ddb3b1",null),(0,s.A)(L,"7f7d456f1a5f4a8bb233e9d2b27c9af0f37f738be8",null),(0,s.A)(H,"7fc9e7c70462f4ca8ea16fecf905b79d190e17bfc4",null),(0,s.A)(X,"7f9d54d21eac1e2add4865faf5c9684045ad61ed20",null),(0,s.A)(F,"7f91924a1c5fb862e2c825711f6d44618a2c264c4e",null),(0,s.A)(G,"7f8a4faff06b2e1252cf75f98fd991807b6c6614e2",null),(0,s.A)(Y,"7f02caa19bcafe6bae86b1dc451230c99861e2cdf0",null),(0,s.A)(V,"7f0960190c6b1636eb84677ab012eeada3f8934a0a",null),(0,s.A)(Z,"7fc091e31ca53c4b62d6348b4fe33853f824667edf",null),(0,s.A)(K,"7fe59050d3b0088d7b9e7e29c9b24472f25477c42a",null),(0,s.A)(J,"7f98d85a4208358a3de69da1ebc9d14ac080c95371",null),(0,s.A)(Q,"7fca6ebfe3c2d2c008e43e6f0940071857f65e5609",null),(0,s.A)(ee,"7fd1428fdb38165c54dd2be1270fa30bb6afdc7638",null),(0,s.A)(er,"7fd0ae55deb61cb5505c47884caaa7d0c2f9642171",null),(0,s.A)(et,"7f9fa7c8dbb4717caf9fbb8065c7b41fc881188656",null),(0,s.A)(ea,"7f56c16e65d0869c9d29f2e63fbc48236c32362379",null),(0,s.A)(es,"7fe1e5383e9f7d8b9cbf540c021937d2fb9850d057",null),(0,s.A)(ei,"7f1f7961c9f8a58420526adffdaa27e3308dc75754",null),(0,s.A)(en,"7f717d13f4790849609e3952f2c335295c24779be8",null),(0,l.D)([eo]),(0,s.A)(eo,"605cc4f7e9bbdd0eaaa2eae3d3f8f5b05914069395",null)},78122:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81085:(e,r,t)=>{Promise.resolve().then(t.bind(t,788))},81630:e=>{"use strict";e.exports=require("http")},82999:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["plan",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,22889)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/plan/page",pathname:"/dashboard/business/plan",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83721:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});var a=t(43210);function s(e){let r=a.useRef({value:e,previous:e});return a.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},88920:(e,r,t)=>{"use strict";t.d(r,{N:()=>g});var a=t(60687),s=t(43210),i=t(12157),n=t(72789),o=t(15124),l=t(21279),c=t(32582);class d extends s.Component{getSnapshotBeforeUpdate(e){let r=this.props.childRef.current;if(r&&e.isPresent&&!this.props.isPresent){let e=r.offsetParent,t=e instanceof HTMLElement&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=r.offsetHeight||0,a.width=r.offsetWidth||0,a.top=r.offsetTop,a.left=r.offsetLeft,a.right=t-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function u({children:e,isPresent:r,anchorX:t}){let i=(0,s.useId)(),n=(0,s.useRef)(null),o=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,s.useContext)(c.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:a,top:s,left:c,right:d}=o.current;if(r||!n.current||!e||!a)return;let u="left"===t?`left: ${c}`:`right: ${d}`;n.current.dataset.motionPopId=i;let p=document.createElement("style");return l&&(p.nonce=l),document.head.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${u}px !important;
            top: ${s}px !important;
          }
        `),()=>{document.head.removeChild(p)}},[r]),(0,a.jsx)(d,{isPresent:r,childRef:n,sizeRef:o,children:s.cloneElement(e,{ref:n})})}let p=({children:e,initial:r,isPresent:t,onExitComplete:i,custom:o,presenceAffectsLayout:c,mode:d,anchorX:p})=>{let m=(0,n.M)(b),h=(0,s.useId)(),f=!0,g=(0,s.useMemo)(()=>(f=!1,{id:h,initial:r,isPresent:t,custom:o,onExitComplete:e=>{for(let r of(m.set(e,!0),m.values()))if(!r)return;i&&i()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[t,m,i]);return c&&f&&(g={...g}),(0,s.useMemo)(()=>{m.forEach((e,r)=>m.set(r,!1))},[t]),s.useEffect(()=>{t||m.size||!i||i()},[t]),"popLayout"===d&&(e=(0,a.jsx)(u,{isPresent:t,anchorX:p,children:e})),(0,a.jsx)(l.t.Provider,{value:g,children:e})};function b(){return new Map}var m=t(86044);let h=e=>e.key||"";function f(e){let r=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&r.push(e)}),r}let g=({children:e,custom:r,initial:t=!0,onExitComplete:l,presenceAffectsLayout:c=!0,mode:d="sync",propagate:u=!1,anchorX:b="left"})=>{let[g,x]=(0,m.xQ)(u),y=(0,s.useMemo)(()=>f(e),[e]),_=u&&!g?[]:y.map(h),v=(0,s.useRef)(!0),w=(0,s.useRef)(y),N=(0,n.M)(()=>new Map),[j,C]=(0,s.useState)(y),[k,S]=(0,s.useState)(y);(0,o.E)(()=>{v.current=!1,w.current=y;for(let e=0;e<k.length;e++){let r=h(k[e]);_.includes(r)?N.delete(r):!0!==N.get(r)&&N.set(r,!1)}},[k,_.length,_.join("-")]);let P=[];if(y!==j){let e=[...y];for(let r=0;r<k.length;r++){let t=k[r],a=h(t);_.includes(a)||(e.splice(r,0,t),P.push(t))}return"wait"===d&&P.length&&(e=P),S(f(e)),C(y),null}let{forceRender:A}=(0,s.useContext)(i.L);return(0,a.jsx)(a.Fragment,{children:k.map(e=>{let s=h(e),i=(!u||!!g)&&(y===k||_.includes(s));return(0,a.jsx)(p,{isPresent:i,initial:(!v.current||!!t)&&void 0,custom:r,presenceAffectsLayout:c,mode:d,onExitComplete:i?void 0:()=>{if(!N.has(s))return;N.set(s,!0);let e=!0;N.forEach(r=>{r||(e=!1)}),e&&(A?.(),S(w.current),u&&x?.(),l&&l())},anchorX:b,children:e},s)})})}},91645:e=>{"use strict";e.exports=require("net")},93661:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")},96752:(e,r,t)=>{"use strict";t.d(r,{A0:()=>n,BF:()=>o,Hj:()=>l,XI:()=>i,nA:()=>d,nd:()=>c});var a=t(60687);t(43210);var s=t(96241);function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...r})})}function n({className:e,...r}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...r})}function o({className:e,...r}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...r})}function l({className:e,...r}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...r})}function c({className:e,...r}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}function d({className:e,...r}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}},96851:(e,r,t)=>{"use strict";t.d(r,{_:()=>l,g:()=>o,switchAuthenticatedSubscription:()=>n});var a=t(91199);t(42087);var s=t(12468),i=t(8174);async function n(e,r,a){let n,{user:o,profile:l,error:c}=await (0,s.GN)("has_active_subscription, trial_end_date");if(c)return(0,s.WX)(c);if(!o)return(0,s.WX)("User not found");let d=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:u,error:p}=await d.from("payment_subscriptions").select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id
    `).eq("business_profile_id",o.id).eq("razorpay_subscription_id",e).maybeSingle();if(p)return console.error("Error fetching subscription:",p),(0,s.WX)("Error fetching subscription details");if(!u?.razorpay_subscription_id)return(0,s.WX)("Subscription does not belong to user");if("authenticated"!==u.subscription_status)return(0,s.WX)("Subscription is not in authenticated state");if(u.plan_id===r&&u.plan_cycle===a)return(0,s.WX)("You are already subscribed to this plan. Please choose a different plan or cycle.");console.log("[SUBSCRIPTION_SWITCH] Creating new subscription without cancelling the old one yet"),console.log("[SUBSCRIPTION_SWITCH] Old subscription ID will be stored in notes for webhook handler to process"),console.log("[SUBSCRIPTION_SWITCH] Creating new subscription with plan:",r,a);let b=l?.trial_end_date?new Date(l.trial_end_date):null,m=b&&b>new Date;try{n=(0,i.qD)(r,a)}catch(e){return(0,s.WX)(e instanceof Error?e.message:"Invalid plan selected")}let{getSubscription:h}=await Promise.resolve().then(t.bind(t,96763)),f=await h(e),g=null;if(f.success&&f.data?.customer_id)g=f.data.customer_id,console.log(`[SUBSCRIPTION_SWITCH] Found customer ID from existing subscription: ${g}`);else{console.log("[SUBSCRIPTION_SWITCH] No customer ID found in existing subscription, checking profile");let{findCustomerByEmail:e,createCustomer:r}=await t.e(3291).then(t.bind(t,93291)),{data:a}=await d.from("business_profiles").select("business_name, contact_email, phone").eq("id",o.id).single(),s=l?.contact_email||o.email||"";if(s){console.log(`[SUBSCRIPTION_SWITCH] Checking if customer with email ${s} already exists`);let t=await e(s);if(t.success&&t.data)g=t.data.id,console.log(`[SUBSCRIPTION_SWITCH] Found existing customer with ID: ${g}`);else{console.log("[SUBSCRIPTION_SWITCH] No existing customer found, creating new customer");let e=await r({name:l?.business_name||"Customer",email:s,contact:l?.phone||"",notes:{user_id:o.id,business_name:l?.business_name||""}});e.success&&e.data?(g=e.data.id,console.log(`[SUBSCRIPTION_SWITCH] Created new customer with ID: ${g}`)):console.error("[SUBSCRIPTION_SWITCH] Failed to create customer:",e.error)}}}let{data:x}=await d.from("business_profiles").select("business_name, contact_email, phone").eq("id",o.id).single(),{createSubscription:y}=await Promise.resolve().then(t.bind(t,96763)),_={plan_id:n,total_count:"monthly"===a?120:10,quantity:1,customer_notify:!0,notes:{business_profile_id:o.id,old_subscription_id:e,plan_type:r,plan_cycle:a,is_plan_switch:"true",user_id:o.id,business_name:x?.business_name||"",email:x?.contact_email||o.email||"",phone:x?.phone||"",prorated_refund_amount:"",original_plan_id:"",original_plan_cycle:"",original_payment_id:""},start_at:void 0,...g&&{customer_id:g}};if(m&&b){let e=Math.floor(b.getTime()/1e3);_.start_at=e,console.log(`[SUBSCRIPTION_SWITCH] User is on trial until ${b.toISOString()}, setting start_at to ${e}`)}else console.log("[SUBSCRIPTION_SWITCH] No trial or trial has ended, immediate payment required");let v=await y(_);return v.success&&v.data?.id?((0,s.Ve)(),(0,s.$y)({message:"New subscription created. Please complete the payment authorization.",id:v.data.id,short_url:v.data.short_url,requires_authorization:!0})):(console.error("[SUBSCRIPTION_SWITCH] Error creating new subscription:",v.error),(0,s.WX)("Could not create new subscription"))}async function o(e,r,a){let{user:i,error:n}=await (0,s.GN)("has_active_subscription, trial_end_date");if(n)return(0,s.WX)(n);if(!i)return(0,s.WX)("User not found");let o=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:c,error:d}=await o.from("payment_subscriptions").select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id,
      last_payment_method
    `).eq("business_profile_id",i.id).eq("razorpay_subscription_id",e).maybeSingle();if(d)return console.error("Error fetching subscription:",d),(0,s.WX)("Error fetching subscription details");if(!c?.razorpay_subscription_id)return(0,s.WX)("Subscription does not belong to user");if("active"!==c.subscription_status)return(0,s.WX)("Subscription is not in active state");if(c.plan_id===r&&c.plan_cycle===a)return(0,s.WX)("You are already subscribed to this plan. Please choose a different plan or cycle.");let u=c.last_payment_method?.toLowerCase()||"";return console.log(`[SUBSCRIPTION_SWITCH] Using simplified create/cancel flow for all payment methods. Payment method: ${u}`),l(e,r,a,u)}async function l(e,r,a,n){let o,{user:l,error:c}=await (0,s.GN)("has_active_subscription, trial_end_date");if(c)return(0,s.WX)(c);if(!l)return(0,s.WX)("User not found");let d=await Promise.resolve().then(t.bind(t,76881)).then(e=>e.createClient()),{data:u,error:p}=await d.from("payment_subscriptions").select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id,
      last_payment_method
    `).eq("business_profile_id",l.id).eq("razorpay_subscription_id",e).maybeSingle();if(p)return console.error("Error fetching subscription:",p),(0,s.WX)("Error fetching subscription details");if(!u?.razorpay_subscription_id)return(0,s.WX)("Subscription does not belong to user");if("active"!==u.subscription_status)return(0,s.WX)("Subscription is not in active state");if(u.plan_id===r&&u.plan_cycle===a)return(0,s.WX)("You are already subscribed to this plan. Please choose a different plan or cycle.");try{o=(0,i.qD)(r,a)}catch(e){return(0,s.WX)(e instanceof Error?e.message:"Invalid plan selected")}let{getSubscription:b}=await Promise.resolve().then(t.bind(t,96763)),m=await b(e),h=null;if(m.success&&m.data?.customer_id)h=m.data.customer_id,console.log(`[SUBSCRIPTION_SWITCH] Found customer ID from existing subscription: ${h}`);else{console.log("[SUBSCRIPTION_SWITCH] No customer ID found in existing subscription, checking profile");let{findCustomerByEmail:e,createCustomer:r}=await t.e(3291).then(t.bind(t,93291)),{data:a}=await d.from("business_profiles").select("business_name, contact_email, phone").eq("id",l.id).single(),s=a?.contact_email||l.email||"";if(s){console.log(`[SUBSCRIPTION_SWITCH] Checking if customer with email ${s} already exists`);let t=await e(s);if(t.success&&t.data)h=t.data.id,console.log(`[SUBSCRIPTION_SWITCH] Found existing customer with ID: ${h}`);else{console.log("[SUBSCRIPTION_SWITCH] No existing customer found, creating new customer");let e=await r({name:a?.business_name||"Customer",email:s,contact:a?.phone||"",notes:{user_id:l.id,business_name:a?.business_name||""}});e.success&&e.data?(h=e.data.id,console.log(`[SUBSCRIPTION_SWITCH] Created new customer with ID: ${h}`)):console.error("[SUBSCRIPTION_SWITCH] Failed to create customer:",e.error)}}}let{data:f}=await d.from("business_profiles").select("business_name, contact_email, phone").eq("id",l.id).single();console.log(`[SUBSCRIPTION_SWITCH] Creating new subscription for plan switch. Old subscription ${e} will be cancelled after new subscription becomes active`);let{createSubscription:g}=await Promise.resolve().then(t.bind(t,96763)),x={plan_id:o,total_count:"monthly"===a?120:10,quantity:1,customer_notify:!0,notes:{business_profile_id:l.id,old_subscription_id:e,plan_type:r,plan_cycle:a,is_plan_switch:"true",user_id:l.id,business_name:f?.business_name||"",email:f?.contact_email||l.email||"",phone:f?.phone||"",payment_method:n,original_plan_id:u.plan_id,original_plan_cycle:u.plan_cycle},...h&&{customer_id:h}},y=await g(x);return y.success&&y.data?.id?((0,s.Ve)(),(0,s.$y)({message:"A new subscription has been created for your plan switch. Please complete the payment authorization to activate your new plan. Your current subscription will remain active until the new one is confirmed.",id:y.data.id,short_url:y.data.short_url,requires_authorization:!0})):(console.error("[SUBSCRIPTION_SWITCH] Error creating new subscription:",y.error),(0,s.WX)("Could not create new subscription"))}(0,t(33331).D)([n,o,l]),(0,a.A)(n,"7079684d5710a60a6f3b6c187d7182292bb2ca9359",null),(0,a.A)(o,"70d5ac2f53a3a59b11b18e95dcb79d8273fb1231ca",null),(0,a.A)(l,"7821496fbfb4e8bbdb4fd0cc9aeebfc2e46e676840",null)},99891:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,6380,399,4212,2392,802,3037,3739,9538,5265,9209,9134,2799,427,6763],()=>t(82999));module.exports=a})();