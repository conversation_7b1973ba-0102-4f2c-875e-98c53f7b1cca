"use strict";exports.id=4308,exports.ids=[937,4308],exports.modules={868:(e,s,t)=>{t.d(s,{E:()=>a});var r=t(6475);let a=(0,r.createServerReference)("401bed28fefc4fa316c9657ce0fb9ce951b2f6cefe",r.callServer,void 0,r.findSourceMapURL,"getSelectedProducts")},4331:(e,s,t)=>{t.d(s,{G7:()=>o,L$:()=>u,h_:()=>m,oI:()=>d,uB:()=>l,xL:()=>c});var r=t(60687);t(43210);var a=t(70965),i=t(99270),n=t(96241);function l({className:e,...s}){return(0,r.jsx)(a.uB,{"data-slot":"command",className:(0,n.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...s})}function o({className:e,...s}){return(0,r.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,r.jsx)(i.A,{className:"size-4 shrink-0 opacity-50"}),(0,r.jsx)(a.uB.Input,{"data-slot":"command-input",className:(0,n.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...s})]})}function d({className:e,...s}){return(0,r.jsx)(a.uB.List,{"data-slot":"command-list",className:(0,n.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",e),...s})}function c({...e}){return(0,r.jsx)(a.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e})}function u({className:e,...s}){return(0,r.jsx)(a.uB.Group,{"data-slot":"command-group",className:(0,n.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...s})}function m({className:e,...s}){return(0,r.jsx)(a.uB.Item,{"data-slot":"command-item",className:(0,n.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}t(37826)},5767:(e,s,t)=>{t.d(s,{A:()=>a});var r=t(60687);t(43210);let a=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,r.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"})})},5903:(e,s,t)=>{t.d(s,{A:()=>h});var r=t(60687),a=t(43210),i=t(30474),n=t(85814),l=t.n(n),o=t(71463),d=t(77882),c=t(88920),u=t(96241);let m=e=>null==e?null:e.toLocaleString("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}),p={hidden:{opacity:0,y:10},show:{opacity:1,y:0}},x={initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1,transition:{duration:.5,type:"spring",stiffness:400,damping:10}},hover:{scale:1.05,rotate:-2,transition:{type:"spring",stiffness:500}}};function h({product:e,isLink:s=!0}){let[t,n]=(0,a.useState)(!1),h=m(e.base_price),f=m(e.discounted_price),g=h,b=null,v=0,_="number"==typeof e.discounted_price&&e.discounted_price>0,j="number"==typeof e.base_price&&e.base_price>0;_&&j&&e.discounted_price<e.base_price?(g=f,b=h,v=Math.round((e.base_price-e.discounted_price)/e.base_price*100)):(g=h,b=null,v=0),g||(g="Price unavailable");let w=v>0,[N,y]=(0,a.useState)(!1),S=!e.is_available,A=(0,r.jsx)(d.P.div,{variants:p,initial:"hidden",animate:"show",className:"w-full overflow-hidden",children:(0,r.jsx)("div",{className:"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg",children:(0,r.jsxs)("div",{className:"relative w-full overflow-hidden rounded-lg",children:[(0,r.jsxs)("div",{className:"relative w-full overflow-hidden rounded-t-xl",children:[(()=>{let s=e.image_url;if(e.images&&Array.isArray(e.images)&&e.images.length>0){let t="number"==typeof e.featured_image_index?Math.min(e.featured_image_index,e.images.length-1):0;s=e.images[t]}return s&&!t?(0,r.jsxs)("div",{className:"overflow-hidden",children:[!N&&(0,r.jsx)(o.E,{className:"absolute inset-0 rounded-t-xl"}),(0,r.jsx)(d.P.div,{className:"w-full",children:(0,r.jsx)(i.default,{src:s,alt:e.name??"Product image",width:500,height:750,className:`w-full aspect-square object-cover ${S?"filter grayscale opacity-70 transition-all duration-500":""} ${N?"opacity-100":"opacity-0"} max-w-full`,loading:"lazy",onError:()=>n(!0),onLoad:()=>y(!0),quality:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",placeholder:"blur",style:{objectFit:"cover"}})})]}):(0,r.jsx)("div",{className:"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl",children:(0,r.jsx)("svg",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})(),S&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40",children:(0,r.jsx)("div",{className:"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground",children:(0,r.jsx)("span",{className:"font-medium tracking-wide uppercase text-xs sm:text-sm",children:"Out of Stock"})})}),w&&(0,r.jsx)(c.N,{children:(0,r.jsx)(d.P.div,{variants:x,initial:"initial",animate:"animate",whileHover:"hover",className:(0,u.cn)("absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg","bg-destructive","text-destructive-foreground border border-destructive-foreground/20","transform-gpu"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("span",{className:"text-[7px] sm:text-[9px] md:text-[10px] font-medium",children:"SAVE"}),(0,r.jsxs)("span",{className:"text-[9px] sm:text-xs md:text-sm leading-none",children:[v,"%"]})]})},`discount-badge-${e.id}`)})]}),(0,r.jsxs)("div",{className:"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1",children:[(0,r.jsx)("p",{className:"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden",children:e.name??"Unnamed Product"}),e.description&&(0,r.jsx)("p",{className:"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate",children:e.description}),(0,r.jsx)("div",{className:"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1",children:(0,r.jsxs)("div",{className:"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full",children:[g&&(0,r.jsx)("p",{className:"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full",children:g}),b&&(0,r.jsx)("p",{className:"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500",children:b})]})})]})]})})});return s&&"business_slug"in e&&e.business_slug?(0,r.jsx)(l(),{href:`/${e.business_slug}/product/${e.slug||e.id}`,className:"block h-full",children:A}):A}},15616:(e,s,t)=>{t.d(s,{T:()=>i});var r=t(60687);t(43210);var a=t(96241);function i({className:e,...s}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...s})}},16111:(e,s,t)=>{t.d(s,{w:()=>i});var r=t(91199);t(42087);var a=t(76881);async function i(e){if(!e||0===e.length)return{success:!0,data:[]};try{let s=await (0,a.createClient)(),{data:t,error:r}=await s.from("products_services").select("id, name, base_price, discounted_price, image_url, slug").in("id",e).eq("is_available",!0);if(r)return console.error("Error fetching products by IDs:",r),{success:!1,error:"Failed to fetch products"};return{success:!0,data:t||[]}}catch(e){return console.error("Error in fetchProductsByIds:",e),{success:!1,error:"An unexpected error occurred"}}}(0,t(33331).D)([i]),(0,r.A)(i,"404b1d9bf7310a5b14e59bf18e153b291565613a40",null)},23135:(e,s,t)=>{t.d(s,{pD:()=>a,deletePost:()=>n,gg:()=>i});var r=t(6475);let a=(0,r.createServerReference)("4078b27f89338bdea95f74b22fd4d22c4710876eab",r.callServer,void 0,r.findSourceMapURL,"createPost"),i=(0,r.createServerReference)("60fef8195d22c743fbc958b9c6e7229ba7a09ebfb2",r.callServer,void 0,r.findSourceMapURL,"updatePost"),n=(0,r.createServerReference)("4009d2933c186901a1403c78560cd329314f35e005",r.callServer,void 0,r.findSourceMapURL,"deletePost");t(27124)},23809:(e,s,t)=>{t.d(s,{H:()=>a});var r=t(6475);let a=(0,r.createServerReference)("4034b523293d3ffd37df154f2b315894750110b2c1",r.callServer,void 0,r.findSourceMapURL,"searchBusinessProducts")},26471:(e,s,t)=>{t.d(s,{E:()=>n,H:()=>i});var r=t(91199);t(42087);var a=t(76881);async function i(e){try{if(!e||e.trim().length<2)return{success:!1,error:"Search query must be at least 2 characters long"};let s=await (0,a.createClient)(),{data:{user:t},error:r}=await s.auth.getUser();if(r||!t)return{success:!1,error:"Authentication required"};let i=await (0,a.createClient)(),{data:n,error:l}=await i.from("products_services").select("*").eq("business_id",t.id).eq("is_available",!0).ilike("name",`%${e.trim()}%`).order("name",{ascending:!0}).limit(10);if(l)return console.error("Error searching products:",l),{success:!1,error:"Failed to search products"};return{success:!0,data:n||[]}}catch(e){return console.error("Error in searchBusinessProducts:",e),{success:!1,error:"An unexpected error occurred"}}}async function n(e){try{if(!e||0===e.length)return{success:!0,data:[]};let s=await (0,a.createClient)(),{data:{user:t},error:r}=await s.auth.getUser();if(r||!t)return{success:!1,error:"Authentication required"};let{data:i,error:n}=await s.from("products_services").select("*").in("id",e).eq("business_id",t.id).order("name",{ascending:!0});if(n)return console.error("Error getting selected products:",n),{success:!1,error:"Failed to get selected products"};return{success:!0,data:i||[]}}catch(e){return console.error("Error in getSelectedProducts:",e),{success:!1,error:"An unexpected error occurred"}}}(0,t(33331).D)([i,n]),(0,r.A)(i,"4034b523293d3ffd37df154f2b315894750110b2c1",null),(0,r.A)(n,"401bed28fefc4fa316c9657ce0fb9ce951b2f6cefe",null)},27124:(e,s,t)=>{t.d(s,{_:()=>o});var r=t(38398),a=t(73170);let i={enterprise:5,pro:4,growth:3,basic:2,free:1};async function n(e){let s=(0,r.U)();try{let{data:t,error:r}=await s.from(a.CG.CUSTOMER_PROFILES).select("*").eq(a.cZ.ID,e).single();if(r)return console.error("Error fetching user profile:",r.message),{data:null,error:r.message};return{data:t,error:null}}catch(e){return console.error("Unexpected error fetching user profile:",e),{data:null,error:"An unexpected error occurred."}}}async function l(e,s){let t=(0,r.U)(),{filter:l="smart",page:o=1,limit:d=10,city_slug:c,state_slug:u,locality_slug:m,pincode:p}=e;try{let{data:{user:e}}=await t.auth.getUser(),r=t.from("unified_posts").select("*",{count:"exact"});switch(l){case"smart":if(e){let{data:s}=await t.from("subscriptions").select("business_profile_id").eq(a.cZ.USER_ID,e.id),i=s?.map(e=>e.business_profile_id)||[],[l,o]=await Promise.all([n(e.id),t.from(a.CG.BUSINESS_PROFILES).select(`${a.cZ.CITY_SLUG}, ${a.cZ.STATE_SLUG}, ${a.cZ.LOCALITY_SLUG}, ${a.cZ.PINCODE}`).eq(a.cZ.ID,e.id).single()]),d=l.data||o.data,c=[];i.length>0&&c.push(`and(post_source.eq.business,author_id.in.(${i.join(",")}))`),c.push(`and(post_source.eq.customer,author_id.eq.${e.id})`),c.push(`and(post_source.eq.business,author_id.eq.${e.id})`),d?.locality_slug&&c.push(`${a.cZ.LOCALITY_SLUG}.eq.${d.locality_slug}`),d?.pincode&&c.push(`${a.cZ.PINCODE}.eq.${d.pincode}`),d?.city_slug&&c.push(`${a.cZ.CITY_SLUG}.eq.${d.city_slug}`),c.length>0&&(r=r.or(c.join(",")))}break;case"subscribed":if(e){let{data:s}=await t.from("subscriptions").select("business_profile_id").eq("user_id",e.id),a=s?.map(e=>e.business_profile_id)||[];if(!(a.length>0))return{success:!0,message:"No subscribed businesses found",data:{items:[],totalCount:0,hasMore:!1,hasJustCreatedPost:!1}};r=r.eq("post_source","business").in("author_id",a)}break;case"locality":if(m)r=r.eq("locality_slug",m);else if(e){let[s,i]=await Promise.all([t.from(a.CG.CUSTOMER_PROFILES).select(a.cZ.LOCALITY_SLUG).eq(a.cZ.ID,e.id).single(),t.from(a.CG.BUSINESS_PROFILES).select(a.cZ.LOCALITY_SLUG).eq(a.cZ.ID,e.id).single()]),n=s.data?.locality_slug||i.data?.locality_slug;n&&(r=r.eq("locality_slug",n))}break;case"pincode":if(p)r=r.eq("pincode",p);else if(e){let[s,i]=await Promise.all([t.from(a.CG.CUSTOMER_PROFILES).select(a.cZ.PINCODE).eq(a.cZ.ID,e.id).single(),t.from(a.CG.BUSINESS_PROFILES).select(a.cZ.PINCODE).eq(a.cZ.ID,e.id).single()]),n=s.data?.pincode||i.data?.pincode;n&&(r=r.eq("pincode",n))}break;case"city":if(c)r=r.eq("city_slug",c);else if(e){let[s,i]=await Promise.all([t.from(a.CG.CUSTOMER_PROFILES).select(a.cZ.CITY_SLUG).eq(a.cZ.ID,e.id).single(),t.from(a.CG.BUSINESS_PROFILES).select(a.cZ.CITY_SLUG).eq(a.cZ.ID,e.id).single()]),n=s.data?.city_slug||i.data?.city_slug;n&&(r=r.eq("city_slug",n))}break;case"state":if(u)r=r.eq("state_slug",u);else if(e){let[s,i]=await Promise.all([t.from(a.CG.CUSTOMER_PROFILES).select(a.cZ.STATE_SLUG).eq(a.cZ.ID,e.id).single(),t.from(a.CG.BUSINESS_PROFILES).select(a.cZ.STATE_SLUG).eq(a.cZ.ID,e.id).single()]),n=s.data?.state_slug||i.data?.state_slug;n&&(r=r.eq("state_slug",n))}}let x=(o-1)*d,{data:h,error:f,count:g}=await r.order("created_at",{ascending:!1}).range(x,x+d-1);if(f)return console.error("Error fetching unified feed posts:",f),{success:!1,message:"Failed to fetch posts",error:f.message};let b=h?function(e,s={}){var t,r,a,n;let{enableDiversity:l=!0,maintainChronologicalFlow:o=!0}=s;if(0===e.length)return[];let d=e.filter(e=>"customer"===e.post_source),c=e.filter(e=>"business"===e.post_source),u=(r=(t=d,0===t.length?[]:t.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime())),a=function(e){if(0===e.length)return[];let s=new Map;e.forEach(e=>{s.has(e.author_id)||s.set(e.author_id,[]),s.get(e.author_id).push(e)}),s.forEach((e,t)=>{s.set(t,e.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()))});let t=[],r=[];return s.forEach((e,s)=>{e.length>0&&(t.push(e[0]),e.length>1&&r.push(...e.slice(1)))}),[...t.sort((e,s)=>{let t=e.business_plan||"free",r=s.business_plan||"free",a=i[t]||1,n=i[r]||1;return a!==n?n-a:new Date(s.created_at).getTime()-new Date(e.created_at).getTime()}),...r.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime())]}(c),n=o,0===r.length?a:0===a.length?r:n?[...r,...a].sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()):[...a,...r]);return l?function(e,s={}){let{maxConsecutiveFromSameAuthor:t=1}=s;if(e.length<=1)return e;let r=[],a=[...e],i=null,n=0;for(;a.length>0;){let e=-1;for(let s=0;s<a.length;s++)if(a[s].author_id!==i){e=s;break}if(-1===e&&n<t&&(e=0),-1===e){for(let s=0;s<a.length;s++)if(a[s].author_id!==i){e=s;break}-1===e&&(e=0)}let s=a.splice(e,1)[0];r.push(s),s.author_id===i?n++:(n=1,i=s.author_id)}return r}(u):u}(h,{enableDiversity:!0,maintainChronologicalFlow:!0}):[],v=g||0,_=b.length===d&&x+d<v;if(!s||!s.justCreatedPostId)return{success:!0,message:"Posts fetched successfully",data:{items:b,totalCount:v,hasMore:_,hasJustCreatedPost:!1}};let j=function(e,s){if(!s.justCreatedPostId)return{posts:e,hasJustCreatedPost:!1};let t=e.find(e=>e.id===s.justCreatedPostId);return t?{posts:[t,...e.filter(e=>e.id!==s.justCreatedPostId)],hasJustCreatedPost:!0,justCreatedPost:t}:{posts:e,hasJustCreatedPost:!1}}(b,s);return{success:!0,message:"Posts fetched successfully",data:{items:j.posts,totalCount:v,hasMore:_,hasJustCreatedPost:j.hasJustCreatedPost,justCreatedPost:j.justCreatedPost,creationState:s}}}catch(e){return console.error("Unexpected error in getUnifiedFeedPosts:",e),{success:!1,message:"An unexpected error occurred",error:e instanceof Error?e.message:"Unknown error"}}}async function o(e,s){return await l(e,s)}},33135:(e,s,t)=>{t.d(s,{AM:()=>n,Wv:()=>l,hl:()=>o});var r=t(60687);t(43210);var a=t(52676),i=t(96241);function n({...e}){return(0,r.jsx)(a.bL,{"data-slot":"popover",...e})}function l({...e}){return(0,r.jsx)(a.l9,{"data-slot":"popover-trigger",...e})}function o({className:e,align:s="center",sideOffset:t=4,...n}){return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"popover-content",align:s,sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...n})})}},33635:(e,s,t)=>{t.d(s,{pD:()=>c,ys:()=>x,gg:()=>p,Rl:()=>u,yf:()=>m});var r=t(91199);t(42087);var a=t(76881),i=t(7944),n=t(50937),l=t(33331);async function o(e,s,t){let r=await (0,a.createClient)(),{data:{user:i},error:l}=await r.auth.getUser();if(l||!i)return{success:!1,error:"User not authenticated."};let o=i.id,d=e.get("imageFile");if(!d)return{success:!1,error:"No image file provided."};if(!["image/jpeg","image/png","image/gif","image/webp"].includes(d.type))return{success:!1,error:"Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."};let c=d.name.toLowerCase();if(![".jpg",".jpeg",".png",".gif",".webp"].some(e=>c.endsWith(e)))return{success:!1,error:"Invalid file extension. Please upload files with .jpg, .jpeg, .png, .gif, or .webp extensions."};if(d.size>0xf00000){let e=(d.size/1048576).toFixed(2);return{success:!1,error:`File size (${e}MB) exceeds the 15MB limit. Please choose a smaller image.`}}if(d.size<100)return{success:!1,error:"File appears to be empty or corrupted. Please try a different image."};try{let e=Date.now()+Math.floor(1e3*Math.random()),r="business",i=(0,n.RE)(o,s,0,e,t),l=Buffer.from(await d.arrayBuffer()),c=(0,a.createClient)(),u=await c,{error:m}=await u.storage.from(r).upload(i,l,{contentType:d.type,upsert:!0});if(m)return console.error("Post Image Upload Error:",m),{success:!1,error:`Failed to upload image: ${m.message}`};let{data:p}=u.storage.from(r).getPublicUrl(i);if(!p?.publicUrl)return{success:!1,error:"Could not retrieve public URL after upload."};return{success:!0,url:p.publicUrl}}catch(e){return console.error("Error processing post image:",e),{success:!1,error:"Failed to process image. Please try a different image."}}}async function d(e,s,t){let r=await (0,a.createClient)();try{let a="business",i=(0,n.EK)(e,s,t),{data:l,error:o}=await r.storage.from(a).list(i,{limit:1e3,sortBy:{column:"name",order:"asc"}});if(o){if(o.message?.includes("not found")||o.message?.includes("does not exist")||o.message?.includes("The resource was not found"))return{success:!0};return console.error("Error listing post media files:",o),{success:!1,error:`Failed to list media files: ${o.message}`}}if(!l||0===l.length)return{success:!0};let d=l.map(e=>`${i}/${e.name}`),{error:c}=await r.storage.from(a).remove(d);if(c)return console.error("Error deleting post folder contents:",c),{success:!1,error:`Failed to delete post folder: ${c.message}`};return{success:!0}}catch(e){return console.error("Error in deletePostMedia:",e),{success:!1,error:"An unexpected error occurred while deleting post folder."}}}async function c(e){let s=await (0,a.createClient)(),{data:{user:t},error:r}=await s.auth.getUser();if(r||!t)return{success:!1,message:"Authentication required",error:"You must be logged in to create a post"};let{data:n,error:l}=await s.from("business_profiles").select("id, city_slug, state_slug, locality_slug, pincode, logo_url").eq("id",t.id).single();if(l||!n)return{success:!1,message:"Business profile not found",error:"You must have a business profile to create a post"};let o={business_id:t.id,content:e.content,image_url:e.image_url||null,city_slug:n.city_slug,state_slug:n.state_slug,locality_slug:n.locality_slug,pincode:n.pincode,product_ids:e.product_ids||[],mentioned_business_ids:e.mentioned_business_ids||[],author_avatar:n.logo_url},{data:d,error:c}=await s.from("business_posts").insert(o).select().single();return c?(console.error("Error creating post:",c),{success:!1,message:"Failed to create post",error:c.message}):((0,i.revalidatePath)("/dashboard/business/feed"),(0,i.revalidatePath)("/dashboard/customer/feed"),{success:!0,message:"Post created successfully",data:d})}async function u(e,s){let t=await (0,a.createClient)(),{data:{user:r},error:n}=await t.auth.getUser();if(n||!r)return{success:!1,message:"Authentication required",error:"You must be logged in to update a post"};let{data:l,error:o}=await t.from("business_posts").select("id").eq("id",e).eq("business_id",r.id).single();if(o||!l)return{success:!1,message:"Post not found",error:"The post does not exist or you do not have permission to update it"};let{data:d,error:c}=await t.from("business_posts").update({content:s.trim(),updated_at:new Date().toISOString()}).eq("id",e).select().single();return c?(console.error("Error updating post content:",c),{success:!1,message:"Failed to update post",error:c.message}):((0,i.revalidatePath)("/dashboard/business/feed"),(0,i.revalidatePath)("/dashboard/customer/feed"),(0,i.revalidatePath)("/dashboard/business/posts"),{success:!0,message:"Post updated successfully",data:d})}async function m(e,s){let t=await (0,a.createClient)(),{data:{user:r},error:n}=await t.auth.getUser();if(n||!r)return{success:!1,message:"Authentication required",error:"You must be logged in to update a post"};let{data:l,error:o}=await t.from("business_posts").select("id").eq("id",e).eq("business_id",r.id).single();if(o||!l)return{success:!1,message:"Post not found",error:"The post does not exist or you do not have permission to update it"};let{data:d,error:c}=await t.from("business_posts").update({product_ids:s,updated_at:new Date().toISOString()}).eq("id",e).select().single();return c?(console.error("Error updating post products:",c),{success:!1,message:"Failed to update post products",error:c.message}):((0,i.revalidatePath)("/dashboard/business/feed"),(0,i.revalidatePath)("/dashboard/customer/feed"),(0,i.revalidatePath)("/dashboard/business/posts"),{success:!0,message:"Post products updated successfully",data:d})}async function p(e,s){let t=await (0,a.createClient)(),{data:{user:r},error:n}=await t.auth.getUser();if(n||!r)return{success:!1,message:"Authentication required",error:"You must be logged in to update a post"};let{data:l,error:o}=await t.from("business_posts").select("id").eq("id",e).eq("business_id",r.id).single();if(o||!l)return{success:!1,message:"Post not found",error:"The post does not exist or you do not have permission to update it"};let d={content:s.content,image_url:s.image_url||null,product_ids:s.product_ids||[],mentioned_business_ids:s.mentioned_business_ids||[],updated_at:new Date().toISOString()},{data:c,error:u}=await t.from("business_posts").update(d).eq("id",e).select().single();return u?(console.error("Error updating post:",u),{success:!1,message:"Failed to update post",error:u.message}):((0,i.revalidatePath)("/dashboard/business/feed"),(0,i.revalidatePath)("/dashboard/customer/feed"),(0,i.revalidatePath)("/dashboard/business/posts"),{success:!0,message:"Post updated successfully",data:c})}async function x(e){let s=await (0,a.createClient)(),{data:{user:t},error:r}=await s.auth.getUser();if(r||!t)return{success:!1,message:"Authentication required",error:"You must be logged in to delete a post"};let{data:n,error:l}=await s.from("business_posts").select("id, created_at, image_url").eq("id",e).eq("business_id",t.id).single();if(l||!n)return{success:!1,message:"Post not found",error:"The post does not exist or you do not have permission to delete it"};try{let s=await d(t.id,e,n.created_at);if(!s.success&&s.error)return console.error("Error deleting business post media:",s.error),{success:!1,message:"Failed to delete post images",error:`Cannot delete post: ${s.error}`}}catch(e){return console.error("Error deleting business post media:",e),{success:!1,message:"Failed to delete post images",error:"Cannot delete post: Failed to clean up associated images"}}let{error:o}=await s.from("business_posts").delete().eq("id",e);return o?(console.error("Error deleting post:",o),{success:!1,message:"Failed to delete post",error:o.message}):((0,i.revalidatePath)("/dashboard/business/feed"),(0,i.revalidatePath)("/dashboard/customer/feed"),(0,i.revalidatePath)("/dashboard/business/posts"),{success:!0,message:"Post deleted successfully"})}(0,l.D)([o,d]),(0,r.A)(o,"70ebecaa983abea2bc17161cdfa02a24f193f36f02",null),(0,r.A)(d,"70d62c3c792fd639f22bad3ee66c6d5376824ec33d",null),(0,l.D)([c,u,m,p,x]),(0,r.A)(c,"4078b27f89338bdea95f74b22fd4d22c4710876eab",null),(0,r.A)(u,"60183dfe46fbe995d7961a9cf8fe9545820e57eb60",null),(0,r.A)(m,"60e85a41c7f76e7af6443414547e080e8dfaf27c80",null),(0,r.A)(p,"60fef8195d22c743fbc958b9c6e7229ba7a09ebfb2",null),(0,r.A)(x,"4009d2933c186901a1403c78560cd329314f35e005",null)},50937:(e,s,t)=>{function r(e){if(!e||"string"!=typeof e)throw Error(`Invalid userId: expected string, got ${typeof e}. Value: ${e}`);if(e.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${e}`);let s=e.substring(0,2).toLowerCase(),t=e.substring(2,4).toLowerCase();return`users/${s}/${t}/${e}`}function a(e,s,t,a){let i=r(e);return`${i}/products/${s}/base/image_${t}_${a}.webp`}function i(e,s,t,a,i){let n=r(e);return`${n}/products/${s}/${t}/image_${a}_${i}.webp`}function n(e,s,t,a,i){let n=r(e),l=i?new Date(i):new Date,o=l.getFullYear(),d=String(l.getMonth()+1).padStart(2,"0");return`${n}/posts/${o}/${d}/${s}/image_${t}_${a}.webp`}function l(e,s,t){let a=r(e),i=new Date(t),n=i.getFullYear(),l=String(i.getMonth()+1).padStart(2,"0");return`${a}/posts/${n}/${l}/${s}`}function o(e,s){let t=r(e);return`${t}/avatar/avatar_${s}.webp`}function d(e,s,t,a,i){let n=r(e),l=i?new Date(i):new Date,o=l.getFullYear(),d=String(l.getMonth()+1).padStart(2,"0");return`${n}/posts/${o}/${d}/${s}/image_${t}_${a}.webp`}function c(e,s){let t=r(e);return`${t}/ads/custom_ad_${s}.webp`}t.d(s,{EK:()=>l,JU:()=>c,RE:()=>n,Vl:()=>i,getScalableUserPath:()=>r,jA:()=>a,jt:()=>d,tS:()=>o})},51536:(e,s,t)=>{t.d(s,{A:()=>Q});var r=t(60687),a=t(85814),i=t.n(a),n=t(30474),l=t(74212),o=t(77882),d=t(70373),c=t(67760),u=t(33872),m=t(81620),p=t(48340),x=t(5767);function h({business:e,hasWhatsApp:s,hasPhone:t,_postId:a,onShare:i}){return s||t?(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"Like",disabled:!0,children:(0,r.jsx)(c.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"Comment",disabled:!0,children:(0,r.jsx)(u.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"Share",onClick:()=>{i&&i()},children:(0,r.jsx)(m.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[s&&(0,r.jsx)("button",{onClick:()=>{if(s){let s=e.whatsapp_number?.replace(/\D/g,""),t=`Hi ${e.business_name}, I saw your post and would like to know more.`;window.open(`https://wa.me/${s}?text=${encodeURIComponent(t)}`,"_blank")}},className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"WhatsApp",children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),t&&(0,r.jsx)("button",{onClick:()=>{t&&window.open(`tel:${e.phone}`,"_self")},className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"Call Now",children:(0,r.jsx)(p.A,{className:"h-5 w-5"})})]})]}):null}var f=t(5903),g=t(6475);g.callServer,g.findSourceMapURL;var b=t(41862),v=t(58869),_=t(81904),j=t(75034),w=t(88233),N=t(97992),y=t(19080),S=t(43210),A=t(96241),E=(t(38398),t(24934)),P=t(15616),C=t(11860),k=t(13964),I=t(52581),L=t(23135),T=t(81381),U=t(99270),R=t(17971),$=t(57601),D=t(51358),O=t(62478),q=t(4331),F=t(33135),B=t(868),G=t(23809);function M({product:e,onRemove:s,formatPrice:t}){let{attributes:a,listeners:i,setNodeRef:l,transform:o,transition:d,isDragging:c}=(0,D.gl)({id:e.id}),u={transform:O.Ks.Transform.toString(o),transition:d,opacity:c?.5:1};return(0,r.jsxs)("div",{ref:l,style:u,className:"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/50 rounded-lg border min-h-[60px] sm:min-h-[68px]",children:[(0,r.jsx)("div",{...a,...i,className:"cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground shrink-0",children:(0,r.jsx)(T.A,{className:"h-4 w-4"})}),(0,r.jsx)("div",{className:"relative h-8 w-8 sm:h-10 sm:w-10 shrink-0 rounded-md overflow-hidden bg-background",children:e.image_url?(0,r.jsx)(n.default,{src:e.image_url,alt:e.name,fill:!0,className:"object-cover",sizes:"(max-width: 640px) 32px, 40px"}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,r.jsx)(y.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0 pr-1 sm:pr-2",children:[(0,r.jsx)("div",{className:"font-medium text-xs sm:text-sm leading-tight mb-1",children:(0,r.jsx)("span",{className:"line-clamp-1 break-words",children:e.name})}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e.discounted_price?(0,r.jsxs)("div",{className:"flex items-center gap-1 flex-wrap",children:[(0,r.jsx)("span",{className:"text-primary font-medium",children:t(e.discounted_price)}),(0,r.jsx)("span",{className:"line-through text-xs",children:t(e.base_price)})]}):(0,r.jsx)("span",{className:"font-medium",children:t(e.base_price)})})]}),(0,r.jsx)(E.$,{variant:"ghost",size:"icon",className:"h-6 w-6 sm:h-8 sm:w-8 shrink-0 hover:bg-destructive/10 hover:text-destructive",onClick:()=>s(e.id),children:(0,r.jsx)(C.A,{className:"h-3 w-3 sm:h-4 sm:w-4"})})]})}function Y({selectedProductIds:e,onProductsChange:s}){let[t,a]=(0,S.useState)(!1),[i,l]=(0,S.useState)(""),[o,d]=(0,S.useState)(!1),[c,u]=(0,S.useState)([]),[m,p]=(0,S.useState)([]),[x,h]=(0,S.useState)(!1),f=(0,S.useRef)(null),g=(0,$.FR)((0,$.MS)($.AN),(0,$.MS)($.uN,{coordinateGetter:D.JR}));(0,S.useCallback)(async()=>{if(0===e.length)return void p([]);d(!0);try{let s=await (0,B.E)(e);if(s.success&&s.data){let t=e.map(e=>s.data?.find(s=>s.id===e)).filter(Boolean);p(t)}else console.error("Error loading selected products:",s.error),p([])}catch(e){console.error("Error loading selected products:",e),p([])}finally{d(!1)}},[e]);let v=async e=>{d(!0),h(!1);try{let s=await (0,G.H)(e);s.success&&s.data?u(s.data):(console.error("Error searching products:",s.error),u([]))}catch(e){console.error("Error searching products:",e),u([])}finally{d(!1),h(!0)}},_=t=>{let r;if(e.includes(t.id))r=e.filter(e=>e!==t.id),p(e=>e.filter(e=>e.id!==t.id));else{if(e.length>=5)return;r=[...e,t.id],p(e=>[...e,t])}s(r)},j=t=>{let r=e.filter(e=>e!==t);p(e=>e.filter(e=>e.id!==t)),s(r)},w=e=>null===e?"N/A":`₹${e.toLocaleString("en-IN")}`;return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(F.AM,{open:t,onOpenChange:e=>{a(e),e||(l(""),u([]),h(!1))},children:[(0,r.jsx)(F.Wv,{asChild:!0,children:(0,r.jsxs)(E.$,{variant:"outline",role:"combobox","aria-expanded":t,className:"w-full justify-between h-auto min-h-[40px] px-3 py-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(U.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-left text-muted-foreground",children:"Search and add products..."})]}),(0,r.jsx)(R.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,r.jsx)(F.hl,{className:"p-0",align:"start",sideOffset:4,style:{width:"var(--radix-popover-trigger-width)"},children:(0,r.jsxs)(q.uB,{children:[(0,r.jsx)(q.G7,{placeholder:"Search your products...",value:i,onValueChange:e=>{if(l(e),f.current&&clearTimeout(f.current),e.length<2){u([]),h(!1);return}f.current=setTimeout(()=>{v(e)},300)},className:"h-9 border-0 focus:ring-0 focus:ring-offset-0"}),(0,r.jsxs)(q.oI,{className:"max-h-[300px]",children:[o&&(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-6 w-6 animate-spin text-primary"}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Searching products..."})]})}),!o&&0===c.length&&(0,r.jsx)(q.xL,{children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 text-muted-foreground mb-2"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:i.length<2?"Type at least 2 characters to search":x?"No products found":""}),i.length>=2&&x&&(0,r.jsx)("span",{className:"text-xs text-muted-foreground mt-1",children:"Try a different search term"})]})}),!o&&c.length>0&&(0,r.jsx)(q.L$,{children:c.map(s=>(0,r.jsxs)(q.h_,{value:s.slug||s.id,onSelect:()=>{(e.length<5||e.includes(s.id))&&(_(s),a(!1),l(""),u([]),h(!1))},disabled:e.length>=5&&!e.includes(s.id),className:(0,A.cn)("flex items-center gap-3 p-3 cursor-pointer",e.length>=5&&!e.includes(s.id)?"opacity-50 cursor-not-allowed":""),children:[(0,r.jsx)("div",{className:"relative h-10 w-10 shrink-0 rounded-md overflow-hidden bg-muted",children:s.image_url?(0,r.jsx)(n.default,{src:s.image_url,alt:s.name,fill:!0,className:"object-cover",sizes:"40px"}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,r.jsx)(y.A,{className:"h-5 w-5 text-muted-foreground"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0 pr-2",children:[(0,r.jsx)("div",{className:"font-medium text-sm truncate mb-1",children:s.name}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:s.discounted_price?(0,r.jsxs)("div",{className:"flex items-center gap-1 flex-wrap",children:[(0,r.jsx)("span",{className:"text-primary font-medium",children:w(s.discounted_price)}),(0,r.jsx)("span",{className:"line-through text-xs",children:w(s.base_price)})]}):(0,r.jsx)("span",{className:"font-medium",children:w(s.base_price)})})]}),(0,r.jsx)(k.A,{className:(0,A.cn)("ml-auto h-4 w-4",e.includes(s.id)?"opacity-100 text-primary":"opacity-0")})]},s.id))})]})]})})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[o&&e.length>0&&0===m.length&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Loading Selected Products..."})]}),(0,r.jsx)("div",{className:"flex justify-center py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-6 w-6 animate-spin text-[var(--brand-gold)]"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Fetching product details..."})]})})]}),m.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Selected Products"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground font-normal",children:"(Drag to reorder)"})]}),(0,r.jsx)($.Mp,{sensors:g,collisionDetection:$.fp,onDragEnd:e=>{let{active:t,over:r}=e;if(t.id!==r?.id){let e=m.findIndex(e=>e.id===t.id),a=m.findIndex(e=>e.id===r?.id);if(-1!==e&&-1!==a){let t=(0,D.be)(m,e,a);p(t),s(t.map(e=>e.id))}}},children:(0,r.jsx)(D.gB,{items:m.map(e=>e.id),strategy:D._G,children:(0,r.jsx)("div",{className:"grid gap-2",children:m.map(e=>(0,r.jsx)(M,{product:e,onRemove:j,formatPrice:w},e.id))})})})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:0===e.length?"No products selected":`${e.length} product${1!==e.length?"s":""} selected`}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsxs)("span",{className:(0,A.cn)("font-medium",e.length>=5?"text-destructive":"text-muted-foreground"),children:[e.length,"/5"]}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"max"})]})]}),e.length>=5&&(0,r.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded-md",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 shrink-0"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:"Maximum limit of 5 products reached"})]})]})]})}var Z=t(88920);function z({postId:e,initialContent:s,initialProductIds:t,initialImageUrl:a,onSave:i,onCancel:n,className:l=""}){let[d,c]=(0,S.useState)(s),[u,m]=(0,S.useState)(t),[p,x]=(0,S.useState)(!1),[h,f]=(0,S.useState)(s.length),g=(0,S.useRef)(null),v=h>2e3,_=d.trim()!==s.trim(),j=JSON.stringify(u.sort())!==JSON.stringify(t.sort()),w=_||j,N=e=>{c(e),f(e.length)},A=async()=>{if(!w)return void n?.();if(v)return void I.oR.error("Content too long",{description:"Please reduce content to 2000 characters or less."});if(0===d.trim().length&&0===u.length)return void I.oR.error("Content or products required",{description:"Post must have either content or linked products."});x(!0);try{let s=await (0,L.gg)(e,{content:d.trim(),product_ids:u,image_url:a,mentioned_business_ids:[]});s.success?(I.oR.success("Post updated successfully"),i?.(d.trim(),u)):I.oR.error("Failed to update post",{description:s.error||"Please try again."})}catch(e){console.error("Error updating post:",e),I.oR.error("An unexpected error occurred")}finally{x(!1)}},T=()=>{c(s),f(s.length),m(t),n?.()};return(0,r.jsxs)("div",{className:`space-y-4 ${l}`,children:[(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(P.T,{ref:g,value:d,onChange:e=>N(e.target.value),onKeyDown:e=>{"Escape"===e.key?T():"Enter"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),A())},placeholder:"What's on your mind?",className:`min-h-[100px] resize-none ${v?"border-destructive focus:border-destructive":""}`,disabled:p}),(0,r.jsxs)("div",{className:`absolute bottom-2 right-2 text-xs ${v?"text-destructive":"text-muted-foreground"}`,children:[h,"/",2e3]})]})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-muted-foreground",children:["Linked Products (",u.length,")"]})]}),(0,r.jsx)(Y,{selectedProductIds:u,onProductsChange:m})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-2",children:[(0,r.jsxs)(E.$,{variant:"ghost",size:"sm",onClick:T,disabled:p,className:"text-muted-foreground hover:text-foreground",children:[(0,r.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"Cancel"]}),(0,r.jsx)(o.P.div,{whileHover:{scale:w&&!p?1.02:1},whileTap:{scale:w&&!p?.98:1},children:(0,r.jsx)(E.$,{size:"sm",onClick:A,disabled:!w||v||p,className:"relative overflow-hidden",style:{background:w&&!p?"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)":void 0,boxShadow:w&&!p?"0 4px 20px rgba(59, 130, 246, 0.3)":"0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)"},children:(0,r.jsx)(Z.N,{mode:"wait",children:p?(0,r.jsxs)(o.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-1 animate-spin"}),"Saving..."]},"saving"):(0,r.jsxs)(o.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 mr-1"}),"Save"]},"save")})})})]})]})}var V=t(37826);function J({isOpen:e,onOpenChange:s,postId:t,postContent:a,onDeleteSuccess:i}){let[n,l]=(0,S.useState)(!1),d=async()=>{l(!0);try{let e=await (0,L.deletePost)(t);e.success?(I.oR.success("Post deleted successfully",{description:"Your post and associated media have been removed."}),i?.(),s(!1)):I.oR.error("Failed to delete post",{description:e.error||"Please try again."})}catch(e){console.error("Error deleting post:",e),I.oR.error("An unexpected error occurred",{description:"Please try again later."})}finally{l(!1)}};return(0,r.jsx)(V.lG,{open:e,onOpenChange:s,children:(0,r.jsxs)(V.Cf,{className:"sm:max-w-md",children:[(0,r.jsxs)(V.c7,{className:"text-center pb-2",children:[(0,r.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20",children:(0,r.jsx)(o.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},children:(0,r.jsx)(w.A,{className:"h-8 w-8 text-red-500"})})}),(0,r.jsx)(V.L3,{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Delete this post?"}),(0,r.jsx)(V.rr,{className:"text-gray-500 dark:text-gray-400 mt-2",children:"This action cannot be undone."})]}),(0,r.jsxs)(V.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 pt-4",children:[(0,r.jsx)(E.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:n,className:"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200",children:"Cancel"}),(0,r.jsx)(o.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1",children:(0,r.jsx)(E.$,{type:"button",onClick:d,disabled:n,className:`
                w-full relative overflow-hidden
                bg-gradient-to-r from-red-500 to-red-600
                hover:from-red-600 hover:to-red-700
                text-white font-medium
                shadow-lg hover:shadow-xl
                transition-all duration-300
                before:absolute before:inset-0
                before:bg-gradient-to-r before:from-red-400 before:to-red-500
                before:opacity-0 hover:before:opacity-20
                before:transition-opacity before:duration-300
                ${n?"cursor-not-allowed opacity-80":""}
              `,style:{boxShadow:n?"0 4px 20px rgba(239, 68, 68, 0.3)":"0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)"},children:(0,r.jsx)(Z.N,{mode:"wait",children:n?(0,r.jsxs)(o.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Deleting..."]},"deleting"):(0,r.jsxs)(o.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Delete Post"]},"delete")})})})]})]})})}var W=t(55629);function K(e){return`/post/${e}`}function H({post:e,index:s=0,onPostUpdate:t,onPostDelete:a,onProductsUpdate:c,showActualAspectRatio:u=!1,disablePostClick:p=!1,enableImageFullscreen:x=!1}){let[g,P]=(0,S.useState)(!0),[C,k]=(0,S.useState)(!1),[L,T]=(0,S.useState)(null),[U,R]=(0,S.useState)(!0),[$,D]=(0,S.useState)(!1),[O,q]=(0,S.useState)(!1),[F,B]=(0,S.useState)(e.content),[G,M]=(0,S.useState)(e.product_ids||[]),[Y,Z]=(0,S.useState)([]),[V,H]=(0,S.useState)(!1),[Q,X]=(0,S.useState)(!1),ee=(0,S.useRef)(null),[es,et]=(0,S.useState)(!1),[er,ea]=(0,S.useState)(0),[ei,en]=(0,S.useState)(0),[el,eo]=(0,S.useState)(0),{isOwner:ed,isLoading:ec}=function({postBusinessId:e}){let[s,t]=(0,S.useState)(!1),[r,a]=(0,S.useState)(!0),[i,n]=(0,S.useState)(null);return{isOwner:s,isLoading:r,currentUserId:i}}({postBusinessId:e.business_id}),eu=e.business_profiles,em=async()=>{try{var s;let t=(s=e.id,`http://localhost:3000/post/${s}`);if(navigator.share)return void await navigator.share({title:"Check out this post on Dukancard",url:t});await navigator.clipboard.writeText(t),I.oR.success("Post link copied to clipboard!")}catch(e){console.error("Error sharing post:",e),I.oR.error("Failed to share post")}},ep=(e,s)=>{if(el>5)return void e.preventDefault();window.open(s,"_blank","noopener,noreferrer")};if(!eu)return null;let ex=(0,l.m)(new Date(e.created_at),{addSuffix:!0});return(0,r.jsxs)(o.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4,delay:.1*s,ease:"easeOut"}}},initial:"hidden",animate:"visible",className:(0,A.cn)("bg-white dark:bg-black","overflow-hidden mb-4 md:mb-6","md:rounded-xl md:border md:border-neutral-200 md:dark:border-neutral-800 md:shadow-sm md:hover:shadow-md md:transition-all md:duration-300"),children:[(0,r.jsxs)("div",{className:"p-4 pb-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[eu.business_slug?(0,r.jsxs)(i(),{href:`/${eu.business_slug}`,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-3 flex-1 min-w-0 group",children:[(0,r.jsxs)(d.eu,{className:"h-12 w-12 border-2 border-[var(--brand-gold)]/30 transition-transform group-hover:scale-105",children:[(0,r.jsx)(d.BK,{src:eu.logo_url||"",alt:eu.business_name||"Business",className:"object-cover"}),(0,r.jsx)(d.q5,{className:"bg-muted text-foreground border border-[var(--brand-gold)]/30",children:(0,r.jsx)(v.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate group-hover:text-[var(--brand-gold)] transition-colors",children:eu.business_name}),eu.business_slug&&(0,r.jsxs)("div",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:["@",eu.business_slug]}),(0,r.jsx)("div",{className:"text-xs text-neutral-400 dark:text-neutral-500 mt-1",children:ex})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[(0,r.jsxs)(d.eu,{className:"h-12 w-12 border-2 border-[var(--brand-gold)]/30",children:[(0,r.jsx)(d.BK,{src:eu.logo_url||"",alt:eu.business_name||"Customer",className:"object-cover"}),(0,r.jsx)(d.q5,{className:"bg-muted text-foreground border border-[var(--brand-gold)]/30",children:(0,r.jsx)(v.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate",children:eu.business_name}),(0,r.jsx)("div",{className:"text-xs text-neutral-400 dark:text-neutral-500 mt-1",children:ex})]})]}),(0,r.jsxs)(W.rI,{children:[(0,r.jsx)(W.ty,{asChild:!0,children:(0,r.jsxs)(E.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-full",children:[(0,r.jsx)(_.A,{className:"h-5 w-5 text-neutral-500 dark:text-neutral-400"}),(0,r.jsx)("span",{className:"sr-only",children:"Open post menu"})]})}),(0,r.jsxs)(W.SQ,{align:"end",className:"w-48",children:[(0,r.jsxs)(W._2,{onClick:em,className:"cursor-pointer",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Share post"]}),ed&&!ec&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(W.mB,{}),(0,r.jsxs)(W._2,{onClick:()=>D(!0),className:"cursor-pointer",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Edit post"]}),(0,r.jsx)(W.mB,{}),(0,r.jsxs)(W._2,{onClick:()=>q(!0),className:"text-destructive focus:text-destructive cursor-pointer",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Delete post"]})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-neutral-500 dark:text-neutral-400",children:[(0,r.jsx)(N.A,{className:"h-3.5 w-3.5 mr-1 flex-shrink-0"}),(0,r.jsx)("span",{children:(()=>{if(U)return"Loading address...";if(L){let e=[];return L.locality&&e.push(L.locality),L.city&&e.push(L.city),L.state&&e.push(L.state),L.pincode&&e.push(L.pincode),e.join(", ")}return function(e,s,t,r){let a=[];return e&&a.push(e.replace(/-/g," ")),s&&a.push(s.replace(/-/g," ")),t&&a.push(t.replace(/-/g," ")),r&&a.push(r),a.join(", ")}(e.locality_slug,e.city_slug,e.state_slug,e.pincode)})()})]})]}),(0,r.jsx)("div",{className:"px-4 pb-3",children:$?(0,r.jsx)(z,{postId:e.id,initialContent:F,initialProductIds:G,initialImageUrl:e.image_url,onSave:(s,r)=>{D(!1),B(s),M(r),t?.(e.id,s),c?.(e.id,r)},onCancel:()=>{D(!1)}}):p?(0,r.jsx)("p",{className:"text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line",children:F}):(0,r.jsx)(i(),{href:K(e.id),className:"block cursor-pointer",children:(0,r.jsx)("p",{className:"text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line",children:F})})}),e.image_url&&(p?(0,r.jsx)("div",{className:(0,A.cn)("relative w-full transition-opacity duration-200",x?"cursor-pointer hover:opacity-95":""),onClick:x?()=>X(!0):void 0,children:u?(0,r.jsxs)("div",{className:"relative w-full bg-neutral-100 dark:bg-neutral-800",children:[(0,r.jsx)(n.default,{src:e.image_url,alt:"Post image",width:800,height:600,className:(0,A.cn)("w-full h-auto object-contain transition-all duration-300",g&&"blur-sm scale-105",C&&"hidden"),onLoad:()=>P(!1),onError:()=>{k(!0),P(!1)},sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:s<3}),g&&(0,r.jsx)("div",{className:"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse"})]}):(0,r.jsxs)("div",{className:"relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800",children:[(0,r.jsx)(n.default,{src:e.image_url,alt:"Post image",fill:!0,className:(0,A.cn)("object-cover transition-all duration-300",g&&"blur-sm scale-105",C&&"hidden"),onLoad:()=>P(!1),onError:()=>{k(!0),P(!1)},sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:s<3}),g&&(0,r.jsx)("div",{className:"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse"})]})}):(0,r.jsx)(i(),{href:K(e.id),className:"block",children:(0,r.jsx)("div",{className:"relative w-full cursor-pointer hover:opacity-95 transition-opacity duration-200",children:u?(0,r.jsxs)("div",{className:"relative w-full bg-neutral-100 dark:bg-neutral-800",children:[(0,r.jsx)(n.default,{src:e.image_url,alt:"Post image",width:800,height:600,className:(0,A.cn)("w-full h-auto object-contain transition-all duration-300",g&&"blur-sm scale-105",C&&"hidden"),onLoad:()=>P(!1),onError:()=>{k(!0),P(!1)},sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:s<3}),g&&(0,r.jsx)("div",{className:"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse"})]}):(0,r.jsxs)("div",{className:"relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800",children:[(0,r.jsx)(n.default,{src:e.image_url,alt:"Post image",fill:!0,className:(0,A.cn)("object-cover transition-all duration-300",g&&"blur-sm scale-105",C&&"hidden"),onLoad:()=>P(!1),onError:()=>{k(!0),P(!1)},sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:s<3}),g&&(0,r.jsx)("div",{className:"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse"})]})})})),G.length>0&&!$&&(0,r.jsx)("div",{className:"px-4 pt-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"p-1.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-lg",children:(0,r.jsx)(y.A,{className:"h-4 w-4 text-[var(--brand-gold)]"})}),(0,r.jsxs)("h4",{className:"text-sm font-semibold text-neutral-900 dark:text-neutral-100",children:["Featured Products (",Y.length,")"]})]}),V?(0,r.jsx)("div",{className:"flex justify-center py-8",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-6 w-6 animate-spin text-[var(--brand-gold)]"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Loading products..."})]})}):(0,r.jsx)(o.P.div,{ref:ee,className:"flex gap-3 overflow-x-auto scrollbar-hide pb-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},style:{scrollbarWidth:"none",msOverflowStyle:"none"},children:Y.map(e=>(0,r.jsx)(o.P.div,{className:"flex-shrink-0 w-40",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},children:(0,r.jsx)("div",{className:"block h-full cursor-pointer",onClick:s=>{eu.business_slug&&ep(s,`/${eu.business_slug}/product/${e.slug||e.id}`)},children:(0,r.jsx)(f.A,{product:{...e,description:void 0,product_type:"physical",base_price:e.base_price||0,slug:e.slug||void 0,is_available:!0,images:e.image_url?[e.image_url]:void 0,featured_image_index:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},isLink:!1})})},e.id))})]})}),(0,r.jsx)("div",{className:"p-4 pt-3",children:(0,r.jsx)(h,{business:eu,hasWhatsApp:!!(eu.whatsapp_number&&""!==eu.whatsapp_number.trim()),hasPhone:!!(eu.phone&&""!==eu.phone.trim()),_postId:e.id,onShare:em})}),(0,r.jsx)(J,{isOpen:O,onOpenChange:q,postId:e.id,postContent:F,onDeleteSuccess:()=>{q(!1),a?.(e.id)}}),x&&Q&&e.image_url&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4",onClick:()=>X(!1),children:(0,r.jsxs)("div",{className:"relative max-w-full max-h-full",children:[(0,r.jsx)("button",{onClick:()=>X(!1),className:"absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,r.jsx)(n.default,{src:e.image_url,alt:"Post image fullscreen",width:1200,height:800,className:"max-w-full max-h-full object-contain",onClick:e=>e.stopPropagation()})]})})]})}function Q({post:e,index:s=0,onPostUpdate:t,onPostDelete:a,onProductsUpdate:i,showActualAspectRatio:n=!1,disablePostClick:l=!1,enableImageFullscreen:o=!1}){let d={id:e.id,business_id:e.author_id,content:e.content,image_url:e.image_url,created_at:e.created_at,updated_at:e.updated_at,city_slug:e.city_slug,state_slug:e.state_slug,locality_slug:e.locality_slug,pincode:e.pincode,product_ids:e.product_ids,mentioned_business_ids:e.mentioned_business_ids,business_profiles:{id:e.author_id,business_name:e.author_name||("customer"===e.post_source?"Customer":"Business"),logo_url:e.author_avatar,business_slug:e.business_slug,phone:e.phone,whatsapp_number:e.whatsapp_number,city:null,state:null}};return(0,r.jsx)(H,{post:d,index:s,onPostUpdate:t,onPostDelete:a,onProductsUpdate:i,showActualAspectRatio:n,disablePostClick:l,enableImageFullscreen:o})}},72945:(e,s,t)=>{t.d(s,{default:()=>l});var r=t(60687),a=t(77882),i=t(71463),n=t(96241);function l({index:e=0,showImage:s=!0,showProducts:t=!1}){return(0,r.jsxs)(a.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4,delay:.1*e,ease:"easeOut"}}},initial:"hidden",animate:"visible",className:(0,n.cn)("bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800","shadow-sm overflow-hidden mb-4 md:mb-6"),children:[(0,r.jsx)("div",{className:"p-4 pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 flex-1",children:[(0,r.jsx)(i.E,{className:"h-12 w-12 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)(i.E,{className:"h-4 w-32"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.E,{className:"h-3 w-24"}),(0,r.jsx)(i.E,{className:"h-3 w-1 rounded-full"}),(0,r.jsx)(i.E,{className:"h-3 w-16"})]})]})]}),(0,r.jsx)(i.E,{className:"h-8 w-8 rounded-full"})]})}),(0,r.jsxs)("div",{className:"px-4 pb-3 space-y-2",children:[(0,r.jsx)(i.E,{className:"h-4 w-full"}),(0,r.jsx)(i.E,{className:"h-4 w-3/4"}),(0,r.jsx)(i.E,{className:"h-4 w-1/2"})]}),s&&(0,r.jsx)("div",{className:"relative w-full",children:(0,r.jsx)(i.E,{className:"w-full aspect-[4/3]"})}),t&&(0,r.jsxs)("div",{className:"px-4 pt-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.E,{className:"h-6 w-6 rounded-lg"}),(0,r.jsx)(i.E,{className:"h-4 w-32"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-3",children:Array.from({length:2}).map((e,s)=>(0,r.jsxs)("div",{className:"bg-neutral-50 dark:bg-neutral-900 rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden",children:[(0,r.jsx)(i.E,{className:"w-full aspect-square"}),(0,r.jsxs)("div",{className:"p-3 space-y-2",children:[(0,r.jsx)(i.E,{className:"h-4 w-full"}),(0,r.jsx)(i.E,{className:"h-4 w-16"})]})]},s))})]}),(0,r.jsx)("div",{className:"p-4 pt-3",children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(i.E,{className:"h-10 flex-1"}),(0,r.jsx)(i.E,{className:"h-10 flex-1"})]})})]})}},73170:(e,s,t)=>{t.d(s,{CG:()=>r,SC:()=>a,cZ:()=>i});let r={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},a={BUSINESS:"business",CUSTOMERS:"customers"},i={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}}};