(()=>{var e={};e.id=8743,e.ids=[8743],e.modules={2849:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var a=r(65239),s=r(48088),n=r(88170),i=r.n(n),c=r(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);r.d(t,o);let l={children:["",{children:["locality",{children:["[localSlug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9352)),"C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,27450)),"C:\\web-app\\dukancard\\app\\locality\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/locality/[localSlug]/page",pathname:"/locality/[localSlug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7110:(e,t,r)=>{Promise.resolve().then(r.bind(r,57637)),Promise.resolve().then(r.bind(r,87986))},9352:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,dynamic:()=>d,generateMetadata:()=>u});var a=r(37413),s=r(61120),n=r(39916),i=r(87986),c=r(57637),o=r(11659),l=r(17395);let d="force-dynamic";async function u({params:e}){let{localSlug:t}=await e,{data:r,error:a}=await (0,l.u)(t);if(a||!r)return{title:"Location Not Found",description:"The requested location could not be found."};let s=`http://localhost:3000/locality/${t}`,n=`${r.OfficeName}, ${r.DivisionName}, ${r.District}, ${r.StateName} - ${r.Pincode}`;return{title:`Discover Businesses & Products in ${r.OfficeName}`,description:`Find local businesses, products, and services in ${n} with Dukancard. Search, compare, and connect with local merchants.`,alternates:{canonical:s},openGraph:{title:`Discover Businesses & Products in ${r.OfficeName}`,description:`Find local businesses, products, and services in ${n} with Dukancard. Search, compare, and connect with local merchants.`,url:s,siteName:"Dukancard",type:"website",locale:"en_IN"},twitter:{card:"summary_large_image",title:`Discover Businesses & Products in ${r.OfficeName}`,description:`Find local businesses, products, and services in ${n} with Dukancard. Search, compare, and connect with local merchants.`}}}async function p({params:e}){let{localSlug:t}=await e,{data:r,error:s}=await (0,l.u)(t);(s||!r)&&(0,n.notFound)();let c={name:r.OfficeName,pincode:r.Pincode,divisionName:r.DivisionName,district:r.District,stateName:r.StateName,slug:t},{data:d,count:u}=await (0,o.CU)({pincode:r.Pincode},1,20);return(0,a.jsx)(i.default,{locality:c,initialBusinesses:d||[],totalCount:u||0})}async function m({params:e}){return(0,a.jsx)("div",{className:"min-h-screen bg-white dark:bg-black",children:(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)(c.default,{}),children:(0,a.jsx)(p,{params:e})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14896:(e,t,r)=>{"use strict";r.d(t,{default:()=>Z});var a=r(60687),s=r(16189),n=r(43210),i=r(24232),c=r(6475);let o=(0,c.createServerReference)("408f2158ec400c093504ae89fcc291c80da2d01c4f",c.callServer,void 0,c.findSourceMapURL,"searchLocalityCombined"),l=(0,c.createServerReference)("40748292c4304238cd62a2e4c621b487ca6777a364",c.callServer,void 0,c.findSourceMapURL,"fetchMoreBusinessCardsByLocalityCombined"),d=(0,c.createServerReference)("407bc5dc4231b686ee52efd08cbc50dc48858f4866",c.callServer,void 0,c.findSourceMapURL,"fetchMoreProductsByLocalityCombined");var u=r(23608);let p=(0,n.createContext)(void 0);function m(){let e=(0,n.useContext)(p);if(void 0===e)throw Error("useLocalityContext must be used within a LocalityProvider");return e}function h({children:e,locality:t,initialBusinesses:r}){let c=(0,s.useSearchParams)(),m=c.get(i.b2)||"cards",h=c.get(i.OL)||"created_desc",f=c.get(i.v0)||"newest",b=c.get(i.ry)||"all",[x,y]=(0,n.useState)(m),[g,v]=(0,n.useState)(h),[_,w]=(0,n.useState)(f),[N,j]=(0,n.useState)(b),[A,S]=(0,n.useState)(!1),[P,C]=(0,n.useState)(!1),[k,$]=(0,n.useState)(null),[L,M]=(0,n.useState)(null),[D,q]=(0,n.useState)(r),[E,T]=(0,n.useState)([]),[B,R]=(0,n.useState)(1),[I,O]=(0,n.useState)(!1),[F,U]=(0,n.useState)(r?.length||0),[G,Z]=(0,n.useState)(!1),{handleBusinessSortChange:z,handleBusinessSearch:V,loadMoreBusinesses:H}=function(e,t,r,a,c,d,u,p,m,h,f,b,x){let y=(0,s.useSearchParams)(),[g,v]=(0,n.useTransition)();return{isPending:g,handleBusinessSortChange:t=>{h(t),v(async()=>{r(!0),f(null);try{let r=await o({localityName:e.name,pincode:e.pincode,viewType:"cards",sortBy:t,businessName:y.get(i.ho)||void 0});if(r.error)return void f(r.error);r.data&&(a(r.data),d(r.data.businesses||[]),c(r.data.isAuthenticated),u(r.data.hasMore),p(r.data.totalCount),m(1))}catch(e){console.error("Error in handleBusinessSortChange:",e),f("An unexpected error occurred. Please try again.")}finally{r(!1)}})},handleBusinessSearch:async t=>{r(!0),f(null);try{let r=await o({localityName:e.name,pincode:e.pincode,viewType:"cards",sortBy:x,businessName:t||void 0});if(r.error)return void f(r.error);r.data&&(a(r.data),d(r.data.businesses||[]),c(r.data.isAuthenticated),u(r.data.hasMore),p(r.data.totalCount),m(1))}catch(e){console.error("Error in handleBusinessSearch:",e),f("An unexpected error occurred. Please try again.")}finally{r(!1)}},loadMoreBusinesses:async()=>{if("cards"!==t)return;let r=b.length>0?Math.ceil(b.length/20)+1:1;try{let t=await l({localityName:e.name,pincode:e.pincode,page:r,sortBy:x,businessName:y.get(i.ho)||void 0});if(t.error)return void f(t.error);t.data&&(d(e=>[...e,...t.data?.businesses||[]]),u(t.data.hasMore),m(r))}catch(e){console.error("Error in loadMoreBusinesses:",e),f("An unexpected error occurred. Please try again.")}}}}(t,x,S,M,Z,q,O,U,R,v,$,D,g),{handleProductSortChange:J,handleProductSearch:X,handleProductFilterChange:Y,loadMoreProducts:K}=function(e,t,r,a,c,l,p,m,h,f,b,x,y,g,v,_){let w=(0,s.useSearchParams)(),[N,j]=(0,n.useTransition)();return{isPending:N,handleProductSortChange:t=>{f(t),j(async()=>{r(!0),x(null);try{let r=await o({localityName:e.name,pincode:e.pincode,viewType:"products",sortBy:(0,u.tV)(t),productType:"all"===_?null:_,productName:w.get(i.u0)||void 0});if(r.error)return void x(r.error);r.data&&(a(r.data),l(r.data.products||[]),c(r.data.isAuthenticated),p(r.data.hasMore),m(r.data.totalCount),h(1))}catch(e){console.error("Error in handleProductSortChange:",e),x("An unexpected error occurred. Please try again.")}finally{r(!1)}})},handleProductSearch:async t=>{r(!0),x(null);try{let r=await o({localityName:e.name,pincode:e.pincode,viewType:"products",sortBy:(0,u.tV)(v),productType:"all"===_?null:_,productName:t||void 0});if(r.error)return void x(r.error);r.data&&(a(r.data),l(r.data.products||[]),c(r.data.isAuthenticated),p(r.data.hasMore),m(r.data.totalCount),h(1))}catch(e){console.error("Error in handleProductSearch:",e),x("An unexpected error occurred. Please try again.")}finally{r(!1)}},handleProductFilterChange:t=>{b(t),j(async()=>{r(!0),x(null);try{let r=await o({localityName:e.name,pincode:e.pincode,viewType:"products",sortBy:(0,u.tV)(v),productType:"all"===t?null:t,productName:w.get(i.u0)||void 0});if(r.error)return void x(r.error);r.data&&(a(r.data),l(r.data.products||[]),c(r.data.isAuthenticated),p(r.data.hasMore),m(r.data.totalCount),h(1))}catch(e){console.error("Error in handleProductFilterChange:",e),x("An unexpected error occurred. Please try again.")}finally{r(!1)}})},loadMoreProducts:async()=>{if("products"!==t)return;let r=y.length>0?Math.ceil(y.length/20)+1:1;try{let t=await d({localityName:e.name,pincode:e.pincode,page:r,sortBy:(0,u.tV)(v),productType:"all"===_?null:_,productName:w.get(i.u0)||void 0});if(t.error)return void x(t.error);t.data&&(l(e=>[...e,...t.data?.products||[]]),p(t.data.hasMore),h(r))}catch(e){console.error("Error in loadMoreProducts:",e),x("An unexpected error occurred. Please try again.")}}}}(t,x,S,M,Z,T,O,U,R,w,j,$,E,0,_,N),{isPending:Q,handleViewChange:W,performSearch:ee}=function(e,t,r,a,s,i,c,l,d,p,m,h,f,b,x){let[y,g]=(0,n.useTransition)(),v=async()=>{a(!0),h(null);try{let r=await o({localityName:e.name,pincode:e.pincode,viewType:t,sortBy:"cards"===t?f:(0,u.tV)(b),productType:"products"===t&&"all"!==x?x:null});if(r.error)return void h(r.error);r.data&&(s(r.data),"cards"===t?c(r.data.businesses||[]):l(r.data.products||[]),i(r.data.isAuthenticated),d(r.data.hasMore),p(r.data.totalCount),m(1))}catch(e){console.error("Error in performSearch:",e),h("An unexpected error occurred. Please try again.")}finally{a(!1)}};return{isPending:y,handleViewChange:e=>{e!==t&&(r(e),g(async()=>{await v()}))},performSearch:v,loadMore:async()=>{}}}(t,x,y,S,M,Z,q,T,O,U,R,$,g,_,N),et=async()=>{if(!P){C(!0);try{"cards"===x?await H():await K()}catch(e){console.error("Error in loadMore:",e),$("An unexpected error occurred. Please try again.")}finally{C(!1)}}};return(0,a.jsx)(p.Provider,{value:{locality:t,viewType:x,sortBy:g,isSearching:A,isPending:Q,isLoadingMore:P,searchError:k,productFilterBy:N,productSortBy:_,searchResult:L,businesses:D,products:E,currentPage:B,hasMore:I,totalCount:F,isAuthenticated:G,performSearch:ee,handleViewChange:W,handleBusinessSortChange:z,handleBusinessSearch:V,handleProductSearch:X,handleProductSortChange:J,handleProductFilterChange:Y,loadMore:et},children:e})}var f=r(99270),b=r(11860),x=r(68988),y=r(24934);function g({}){let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),{viewType:r,handleBusinessSearch:c,handleProductSearch:o}=m(),l="cards"===r?t.get(i.ho)||"":t.get(i.u0)||"",[d,u]=(0,n.useState)(l),[p,h]=(0,n.useState)(!1);return(0,a.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center",children:[(0,a.jsx)("form",{onSubmit:a=>{a.preventDefault(),h(!0);let s=new URLSearchParams(t.toString());"cards"===r?(d?s.set(i.ho,d):s.delete(i.ho),s.delete(i.u0)):(d?s.set(i.u0,d):s.delete(i.u0),s.delete(i.ho)),e.push(`/locality/${t.get("localSlug")}?${s.toString()}`),"cards"===r?c(d):o(d),h(!1)},className:"w-full md:flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,a.jsx)(x.p,{type:"text",placeholder:"cards"===r?"Search businesses...":"Search products...",value:d,onChange:e=>u(e.target.value),className:"pl-10 pr-10 h-10 w-full"}),d&&(0,a.jsx)("button",{type:"button",onClick:()=>{u("");let a=new URLSearchParams(t.toString());"cards"===r?a.delete(i.ho):a.delete(i.u0),e.push(`/locality/${t.get("localSlug")}?${a.toString()}`),"cards"===r?c(""):o("")},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 w-full md:w-auto",children:[(0,a.jsx)(y.$,{type:"button",variant:"cards"===r?"default":"outline",size:"sm",onClick:()=>{let r=new URLSearchParams(t.toString());r.set(i.b2,"cards"),e.push(`/locality/${t.get("localSlug")}?${r.toString()}`)},className:"flex-1 md:flex-none",children:"Businesses"}),(0,a.jsx)(y.$,{type:"button",variant:"products"===r?"default":"outline",size:"sm",onClick:()=>{let r=new URLSearchParams(t.toString());r.set(i.b2,"products"),e.push(`/locality/${t.get("localSlug")}?${r.toString()}`)},className:"flex-1 md:flex-none",children:"Products"})]})]})})}var v=r(93613),_=r(3018);function w(){let{searchError:e}=m();return e?(0,a.jsxs)(_.Fc,{variant:"destructive",className:"mb-4",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)(_.XL,{children:"Error"}),(0,a.jsx)(_.TN,{children:e})]}):null}var N=r(41862),j=r(37857);function A({businesses:e,lastItemRef:t}){return(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:e.map((r,s)=>s===e.length-1?(0,a.jsx)("div",{ref:t,children:(0,a.jsx)(j.A,{businesses:[r],isAuthenticated:!1})},r.id):(0,a.jsx)("div",{children:(0,a.jsx)(j.A,{businesses:[r],isAuthenticated:!1})},r.id))})}var S=r(5903),P=r(85814),C=r.n(P),k=r(77882);function $({products:e,lastItemRef:t}){return(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4",children:e.map((r,s)=>{let n=`product-${r.id}`;return(0,a.jsx)(k.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*s},className:"group",ref:s===e.length-1?t:void 0,children:r.business_slug?(0,a.jsx)(C(),{href:`/${r.business_slug}/product/${r.slug||r.id}`,className:"block h-full",children:(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(S.A,{product:r,isLink:!1})})}):(0,a.jsxs)("div",{className:"relative h-full",children:[(0,a.jsx)(S.A,{product:r,isLink:!1}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-red-500/80 text-white text-xs py-1 px-2 text-center rounded-b-md",children:"Unable to link to business"})]})},n)})})}var L=r(15879),M=r(13964),D=r(63974),q=r(72185);function E(){let{sortBy:e,handleBusinessSortChange:t,isSearching:r}=(0,q.u)();return(0,a.jsx)(k.P.div,{className:"flex justify-end",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,a.jsxs)("div",{className:"inline-flex items-center bg-white dark:bg-neutral-900 px-3 py-2 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800",children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-2 text-[var(--brand-gold)]"}),(0,a.jsx)("span",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mr-2",children:"Sort by:"}),(0,a.jsxs)(D.l6,{value:e,onValueChange:e=>t(e),disabled:r,children:[(0,a.jsx)(D.bq,{className:"min-w-[180px] border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 focus:ring-offset-0",children:(0,a.jsx)(D.yv,{placeholder:"Sort by",children:{created_desc:"Newest First",name_asc:"Name (A-Z)",name_desc:"Name (Z-A)",likes_desc:"Most Liked",subscriptions_desc:"Most Subscribed",rating_desc:"Highest Rated"}[e]||"Sort by"})}),(0,a.jsxs)(D.gC,{children:[(0,a.jsxs)(D.s3,{children:[(0,a.jsx)(D.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5",children:"Date"}),(0,a.jsxs)(D.eb,{value:"created_desc",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"created_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Newest First"]})]}),(0,a.jsxs)(D.s3,{children:[(0,a.jsx)(D.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Name"}),(0,a.jsxs)(D.eb,{value:"name_asc",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"name_asc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Name (A-Z)"]}),(0,a.jsxs)(D.eb,{value:"name_desc",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"name_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Name (Z-A)"]})]}),(0,a.jsxs)(D.s3,{children:[(0,a.jsx)(D.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Popularity"}),(0,a.jsxs)(D.eb,{value:"likes_desc",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"likes_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Most Liked"]}),(0,a.jsxs)(D.eb,{value:"subscriptions_desc",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"subscriptions_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Most Subscribed"]}),(0,a.jsxs)(D.eb,{value:"rating_desc",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"rating_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Highest Rated"]})]})]})]})]})})}function T(){let{productSortBy:e,handleProductSortChange:t,isSearching:r}=(0,q.u)();return(0,a.jsx)(k.P.div,{className:"flex justify-end",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,a.jsxs)("div",{className:"inline-flex items-center bg-white dark:bg-neutral-900 px-3 py-2 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800",children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-2 text-[var(--brand-gold)]"}),(0,a.jsx)("span",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mr-2",children:"Sort by:"}),(0,a.jsxs)(D.l6,{value:e,onValueChange:e=>t(e),disabled:r,children:[(0,a.jsx)(D.bq,{className:"min-w-[180px] border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 focus:ring-offset-0",children:(0,a.jsx)(D.yv,{placeholder:"Sort by",children:{newest:"Newest First",price_low:"Price (Low to High)",price_high:"Price (High to Low)",name_asc:"Name (A-Z)",name_desc:"Name (Z-A)"}[e]||"Sort by"})}),(0,a.jsxs)(D.gC,{children:[(0,a.jsxs)(D.s3,{children:[(0,a.jsx)(D.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5",children:"Date"}),(0,a.jsxs)(D.eb,{value:"newest",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"newest"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Newest First"]})]}),(0,a.jsxs)(D.s3,{children:[(0,a.jsx)(D.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Price"}),(0,a.jsxs)(D.eb,{value:"price_low",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"price_low"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Price (Low to High)"]}),(0,a.jsxs)(D.eb,{value:"price_high",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"price_high"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Price (High to Low)"]})]}),(0,a.jsxs)(D.s3,{children:[(0,a.jsx)(D.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Name"}),(0,a.jsxs)(D.eb,{value:"name_asc",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"name_asc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Name (A-Z)"]}),(0,a.jsxs)(D.eb,{value:"name_desc",className:"relative pl-8",children:[(0,a.jsx)(M.A,{className:`absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ${"name_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0"}`}),"Name (Z-A)"]})]})]})]})]})})}function B({viewType:e}){let t=(0,s.useSearchParams)(),r="cards"===e?t.get(i.ho):t.get(i.u0);return(0,a.jsx)(k.P.div,{className:"text-center py-16 px-4 bg-transparent rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2",children:"cards"===e?"No Businesses Found":"No Products Found"}),(0,a.jsxs)("p",{className:"text-neutral-500 dark:text-neutral-400 mb-4",children:["We couldn't find any ","cards"===e?"businesses":"products",r?` with "${r}" in the name`:"",". Try adjusting your search criteria or browse all ","cards"===e?"businesses":"products","."]}),r&&(0,a.jsx)("button",{className:"inline-flex items-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-lg text-sm font-medium text-neutral-700 dark:text-neutral-300 transition-colors",onClick:()=>{console.log("Clear search clicked")},children:"Clear Search"})]})})}function R(){let{viewType:e,businesses:t,products:r,isSearching:s,isLoadingMore:i,hasMore:c,totalCount:o,loadMore:l}=m(),d=(0,n.useRef)(null),u=(0,n.useCallback)(e=>{s||i||(d.current&&d.current.disconnect(),d.current=new IntersectionObserver(e=>{e[0].isIntersecting&&c&&l()}),e&&d.current.observe(e))},[s,i,c,l]),p="cards"===e&&t.length>0||"products"===e&&r.length>0,h=s&&0===o;return(0,a.jsxs)("div",{className:"container mx-auto px-4 pb-20",children:[(0,a.jsx)("div",{className:"mb-6",children:"cards"===e?(0,a.jsx)(E,{}):(0,a.jsx)(T,{})}),h?(0,a.jsxs)("div",{className:"flex justify-center items-center py-20",children:[(0,a.jsx)(N.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2 text-lg",children:"Loading..."})]}):p?(0,a.jsxs)(a.Fragment,{children:["cards"===e?(0,a.jsx)(A,{businesses:t,lastItemRef:u}):(0,a.jsx)($,{products:r,lastItemRef:u}),i&&(0,a.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,a.jsx)(N.A,{className:"h-6 w-6 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading more..."})]})]}):(0,a.jsx)(B,{viewType:e})]})}var I=r(97992);function O(){let e=(0,s.useSearchParams)(),{searchResult:t,viewType:r,locality:n}=m(),c=e.get(i.ho),o=e.get(i.u0),l=`${n.name}, ${n.divisionName}`,d=`${n.name}, ${n.divisionName}, ${n.district}, ${n.stateName} - ${n.pincode}`,u=`All businesses and products in ${l}`,p=I.A,h=`Showing businesses and products from ${d}`,b="";if(c||o){p=f.A;let e="cards"===r?c:o;u=`Search results for "${e}" in ${l}`,h=`Showing ${"cards"===r?"businesses":"products"} matching "${e}" in ${d}`,b=e||""}if(t){let e=t.totalCount,a="cards"===r?1===e?"business":"businesses":1===e?"product":"products";h=`Found ${e} ${a} in ${d}`,b&&(h=`Found ${e} ${a} matching "${b}" in ${d}`)}return(0,a.jsxs)("div",{className:"container mx-auto px-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(p,{className:"mr-2 h-5 w-5 text-primary"}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:u})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground ml-7",children:h})]})}var F=r(32192),U=r(14952);function G(){let{locality:e}=m();return(0,a.jsxs)("nav",{className:"container mx-auto px-4 py-4 flex items-center text-sm text-muted-foreground",children:[(0,a.jsxs)(C(),{href:"/?view=home",className:"flex items-center hover:text-primary transition-colors",children:[(0,a.jsx)(F.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{children:"Home"})]}),(0,a.jsx)(U.A,{className:"h-4 w-4 mx-1"}),(0,a.jsx)(C(),{href:"/discover",className:"hover:text-primary transition-colors",children:"Discover"}),(0,a.jsx)(U.A,{className:"h-4 w-4 mx-1"}),(0,a.jsx)("span",{className:"text-foreground font-medium",children:e.name})]})}function Z({locality:e,initialBusinesses:t}){let r=(0,s.useSearchParams)(),n=r.get(i.lX),c=r.get(i.KC),o=r.get(i.Ie);return(0,a.jsx)(h,{locality:e,initialBusinesses:t,children:(0,a.jsxs)("div",{className:"relative min-h-screen overflow-hidden bg-white dark:bg-black",children:[(0,a.jsx)(G,{}),(0,a.jsx)(g,{initialValues:{pincode:n,city:c,locality:o}}),(0,a.jsx)("div",{className:"container mx-auto px-4 my-2",children:(0,a.jsx)(w,{})}),(0,a.jsx)(O,{}),(0,a.jsx)(R,{})]})})}},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17395:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});var a=r(67218);r(79130);var s=r(32032);async function n(e){if(!e||""===e.trim())return{error:"Location slug is required"};try{let t=await (0,s.createClient)(),{data:r,error:a}=await t.from("pincodes").select("Pincode, OfficeName, DivisionName, District, StateName, locality_slug").eq("locality_slug",e).limit(1);if(a)return console.error("Error fetching location by slug:",a),{error:"Database error fetching location data"};if(!r||0===r.length)return{error:"Location not found"};let n=r[0];return{data:{...n,slug:e,locality_slug:n.locality_slug||void 0}}}catch(e){return console.error("Error in getLocationBySlug:",e),{error:"An unexpected error occurred"}}}(0,r(17478).D)([n]),(0,a.A)(n,"40a94af16133b55ad0e067287a2633ecf5a927e947",null)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27416:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"0056cc8508beee15907e2741a09bee834aa2799373":()=>i.E,"00a8d295fac0c253c5942cbbd729c108ff5d62d666":()=>c.h,"40274d19c8c06b8c99a2839744992d7bcecb2341c5":()=>a.$,"407031dcd9eee2e858cfbb811d905061dca994df13":()=>a.f,"4088dc18235d8e33be19d97fc9c00c650f82e08953":()=>c.a,"40a94af16133b55ad0e067287a2633ecf5a927e947":()=>l.u,"70e4edcef42c86cf3a9ba8c209ba199083b0a30092":()=>s.Z,"788721cac1d66dc0019d95bd960521fed5169baa89":()=>o.C,"7c76d89983ca9f1290f11616f3bb631fd67faec14d":()=>s.C,"7f1ff705112688da08e7c134e2df7387375812d40e":()=>n.K});var a=r(27628),s=r(46317),n=r(711),i=r(72116),c=r(11597),o=r(66352),l=r(17395)},27450:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(37413);r(61120);var s=r(14890),n=r(60644),i=r(11637),c=r(95006),o=r(92506),l=r(46501),d=r(21886),u=r(23392);function p({children:e}){return(0,a.jsx)(u.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,a.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,a.jsx)(s.default,{}),(0,a.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,a.jsx)(n.default,{}),(0,a.jsx)(c.default,{}),(0,a.jsx)(i.default,{}),(0,a.jsx)(o.default,{}),(0,a.jsx)(l.default,{}),(0,a.jsx)(d.default,{excludePaths:["/dashboard"]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>s});var a=r(34386);async function s(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let s=null,n=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));s=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||s&&"true"===s.get("x-playwright-testing"))&&s?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),a="customer"===r||"business"===r,s=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,a,s){let n=()=>{var n,i,c,o,l;return n=e,i=t,c=r,o=a,l=s,"customer_profiles"===n?{data:c&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:c&&"business"===i?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===i?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,r)=>i(e),neq:(t,r)=>i(e),gt:(t,r)=>i(e),gte:(t,r)=>i(e),lt:(t,r)=>i(e),lte:(t,r)=>i(e),like:(t,r)=>i(e),ilike:(t,r)=>i(e),is:(t,r)=>i(e),in:(t,r)=>i(e),contains:(t,r)=>i(e),containedBy:(t,r)=>i(e),rangeGt:(t,r)=>i(e),rangeGte:(t,r)=>i(e),rangeLt:(t,r)=>i(e),rangeLte:(t,r)=>i(e),rangeAdjacent:(t,r)=>i(e),overlaps:(t,r)=>i(e),textSearch:(t,r)=>i(e),match:t=>i(e),not:(t,r,a)=>i(e),or:t=>i(e),filter:(t,r,a)=>i(e),order:(t,r)=>i(e),limit:(t,r)=>i(e),range:(t,r,a)=>i(e),abortSignal:t=>i(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,r,a,s,n)}}(s):n?(0,a.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:r,options:a}of e)await n.set(t,r,a)}catch{}}}}):(0,a.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},48811:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"4018b43028809df977ce7bb88f96c0d45dd8e2c788":()=>x.$v,"401ed51378ebbe3803853c628044cdd6d8c4ab6d17":()=>o,"4027ebed4ed3330c943c58b278d97ba413e3a31d66":()=>b.Q,"404b91e3dc66700c3a24f1fde5c90ed9d11b03a1f0":()=>b.m,"40748292c4304238cd62a2e4c621b487ca6777a364":()=>d,"407536f6380b3b438407d685769c94f32cd3ae7e82":()=>u,"407bc5dc4231b686ee52efd08cbc50dc48858f4866":()=>p,"408f2158ec400c093504ae89fcc291c80da2d01c4f":()=>h,"409ef2f4e1a556b4e8644e19f5827625904001c1b1":()=>x.eN,"40cf82c660b16b731be86293ceaaac3becb1b2acb7":()=>l,"40e579c4350f6fcc557e3e669f6ed90ab2eb870447":()=>x.W3,"40edb48823df0fd876d80ab1b1ef92ac50df45d091":()=>f.N});var a=r(91199);r(42087);var s=r(76881);function n(e){let t=e.split("_");return t.length<2?"created_at":"likes"===t[0]?"total_likes":"subscriptions"===t[0]?"total_subscriptions":"rating"===t[0]?"average_rating":"newest"===t[0]||"created"===t[0]?"created_at":"name"===t[0]?"business_name":"price"===t[0]?"price":t[0]}function i(e){return!e.endsWith("_desc")}var c=r(33331);async function o(e){let{pincode:t,businessName:r,page:a=1,limit:c=20,sortBy:o="created_desc"}=e;try{let e=await (0,s.createClient)(),l=(a-1)*c,d=`
      id,
      business_name,
      logo_url,
      member_name,
      title,
      address_line,
      city,
      state,
      pincode,
      locality,
      phone,
      instagram_url,
      facebook_url,
      whatsapp_number,
      about_bio,
      status,
      business_slug,
      theme_color,
      delivery_info,
      business_hours,
      business_category,
      total_likes,
      total_subscriptions,
      average_rating,
      created_at,
      updated_at,
      trial_end_date,
      contact_email
    `,u=e.from("business_profiles").select(d,{count:"exact"}).eq("status","online").eq("pincode",t);r&&(u=u.ilike("business_name",`%${r}%`)),u=(u=u.order(n(o),{ascending:i(o)})).range(l,l+c-1);let{data:p,count:m,error:h}=await u;if(h)return console.error("Error in fetchBusinessesByLocalitySearch:",h),{error:"An error occurred while fetching businesses."};let f=!!m&&l+c<m;return{data:{businesses:p,hasMore:f,nextPage:f?a+1:null}}}catch(e){return console.error("Error in fetchBusinessesByLocalitySearch:",e),{error:"An error occurred while fetching businesses."}}}async function l(e){let{pincode:t,page:r=1,limit:a=20,sortBy:c="created_desc"}=e;try{let e=await (0,s.createClient)(),o=(r-1)*a,l=`
      id,
      business_name,
      logo_url,
      member_name,
      title,
      address_line,
      city,
      state,
      pincode,
      locality,
      phone,
      instagram_url,
      facebook_url,
      whatsapp_number,
      about_bio,
      status,
      business_slug,
      theme_color,
      delivery_info,
      business_hours,
      business_category,
      total_likes,
      total_subscriptions,
      average_rating,
      created_at,
      updated_at,
      trial_end_date,
      contact_email
    `,d=e.from("business_profiles").select(l,{count:"exact"}).eq("status","online").eq("pincode",t);d=(d=d.order(n(c),{ascending:i(c)})).range(o,o+a-1);let{data:u,count:p,error:m}=await d;if(m)return console.error("Error in fetchBusinessesByLocalityAndLocation:",m),{error:"An error occurred while fetching businesses."};let h=!!p&&o+a<p;return{data:{businesses:u,totalCount:p||0,hasMore:h,nextPage:h?r+1:null}}}catch(e){return console.error("Error in fetchBusinessesByLocalityAndLocation:",e),{error:"An error occurred while fetching businesses."}}}async function d(e){let{localityName:t,pincode:r,businessName:a,page:s=1,limit:n=20,sortBy:i="created_desc"}=e;try{if(a){let e=await o({localityName:t,pincode:r,businessName:a,page:s,limit:n,sortBy:i});if(e.error)return{error:e.error};return{data:{businesses:e.data?.businesses||[],hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}let e=await l({localityName:t,pincode:r,page:s,limit:n,sortBy:i});if(e.error)return{error:e.error};return{data:{businesses:e.data?.businesses||[],hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}catch(e){return console.error("Error in fetchMoreBusinessCardsByLocalityCombined:",e),{error:"An error occurred while fetching businesses."}}}async function u(e){let{pincode:t,productName:r,page:a=1,limit:c=20,sortBy:o="created_desc",productType:l=null}=e;try{let e=await (0,s.createClient)(),d=(a-1)*c,u=e.from("business_profiles").select("id").eq("status","online").eq("pincode",t),{data:p,error:m}=await u;if(m)return console.error("Error fetching businesses for products:",m),{error:"Failed to fetch businesses for products."};if(!p||0===p.length)return{data:{products:[],totalCount:0,hasMore:!1,nextPage:null}};let h=p.map(e=>e.id),f=e.from("products_services").select(`
        id,
        name,
        description,
        price,
        discounted_price,
        currency,
        type,
        status,
        slug,
        created_at,
        updated_at,
        business_id,
        featured_image_url,
        business_profiles:business_id (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          pincode
        )
      `,{count:"exact"}).in("business_id",h).eq("is_available",!0);l&&(f=f.eq("type",l)),r&&(f=f.ilike("name",`%${r}%`)),f=(f=f.order(n(o),{ascending:i(o)})).range(d,d+c-1);let{data:b,count:x,error:y}=await f;if(y)return console.error("Error fetching products:",y),{error:"Failed to fetch products."};let g=!!x&&d+c<x;return{data:{products:b.map(e=>{let t=null;return e.business_profiles&&(t=Array.isArray(e.business_profiles)?e.business_profiles[0]?.business_slug||null:e.business_profiles.business_slug||null),{id:e.id,name:e.name,description:e.description,base_price:e.price,discounted_price:e.discounted_price,product_type:"physical"===e.type?"physical":"service",is_available:!0,slug:e.slug,created_at:e.created_at?new Date(e.created_at):void 0,updated_at:e.updated_at?new Date(e.updated_at):void 0,business_id:e.business_id,image_url:e.featured_image_url,business_slug:t}}),totalCount:x||0,hasMore:g,nextPage:g?a+1:null}}}catch(e){return console.error("Error in fetchProductsByLocality:",e),{error:"An error occurred while fetching products."}}}async function p(e){let{localityName:t,pincode:r,productName:a,page:s=1,limit:n=20,sortBy:i="created_desc",productType:c=null}=e;try{let e=await u({localityName:t,pincode:r,productName:a,page:s,limit:n,sortBy:i,productType:c});if(e.error)return{error:e.error};return{data:{products:e.data?.products||[],hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}catch(e){return console.error("Error in fetchMoreProductsByLocalityCombined:",e),{error:"An error occurred while fetching products."}}}async function m(e){let{localityName:t,pincode:r,viewType:a,page:n=1,limit:i=20,sortBy:c="created_desc",productType:o=null,productName:d}=e;if(!t||!r)return{error:"Locality name and pincode are required."};try{let e=await (0,s.createClient)(),{data:{session:p}}=await e.auth.getSession(),m=!!p,h={type:"pincode",value:r};if("cards"===a){let e=await l({localityName:t,pincode:r,page:n,limit:i,sortBy:c});if(e.error)return{error:e.error};return{data:{location:h,businesses:e.data?.businesses||[],isAuthenticated:m,totalCount:e.data?.totalCount||0,hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}{let e=await u({localityName:t,pincode:r,productName:d,page:n,limit:i,sortBy:c,productType:o});if(e.error)return{error:e.error};return{data:{location:h,products:e.data?.products||[],isAuthenticated:m,totalCount:e.data?.totalCount||0,hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}}catch(e){return console.error("Error in searchLocalityData:",e),{error:"An unexpected error occurred. Please try again."}}}async function h(e){let{localityName:t,pincode:r,viewType:a,sortBy:n="created_desc",productType:i=null,businessName:c,productName:l}=e;if(!t||!r)return{error:"Locality name and pincode are required."};try{let e=await (0,s.createClient)(),{data:{session:d}}=await e.auth.getSession(),p=!!d,h={type:"pincode",value:r};if("cards"===a){if(c){let e=await o({localityName:t,pincode:r,businessName:c,sortBy:n});if(e.error)return{error:e.error};return{data:{location:h,businesses:e.data?.businesses||[],isAuthenticated:p,totalCount:e.data?.businesses.length||0,hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}return m({localityName:t,pincode:r,viewType:a,sortBy:n})}if(l){let e=await u({localityName:t,pincode:r,productName:l,sortBy:n,productType:i});if(e.error)return{error:e.error};return{data:{location:h,products:e.data?.products||[],isAuthenticated:p,totalCount:e.data?.totalCount||0,hasMore:e.data?.hasMore||!1,nextPage:e.data?.nextPage||null}}}return m({localityName:t,pincode:r,viewType:a,sortBy:n,productType:i})}catch(e){return console.error("Error in searchLocalityCombined:",e),{error:"An unexpected error occurred. Please try again."}}}(0,c.D)([o,l,d]),(0,a.A)(o,"401ed51378ebbe3803853c628044cdd6d8c4ab6d17",null),(0,a.A)(l,"40cf82c660b16b731be86293ceaaac3becb1b2acb7",null),(0,a.A)(d,"40748292c4304238cd62a2e4c621b487ca6777a364",null),(0,c.D)([u,p]),(0,a.A)(u,"407536f6380b3b438407d685769c94f32cd3ae7e82",null),(0,a.A)(p,"407bc5dc4231b686ee52efd08cbc50dc48858f4866",null),(0,c.D)([m]),(0,a.A)(m,"4039f3ccbd513a1e5a5f9fb4b3d14ed45b8d73f1f8",null),(0,c.D)([h]),(0,a.A)(h,"408f2158ec400c093504ae89fcc291c80da2d01c4f",null);var f=r(72633),b=r(68267),x=r(40382)},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56528:(e,t,r)=>{"use strict";r.d(t,{JM:()=>c,eb:()=>i,tz:()=>n});var a=r(91199);r(42087);var s=r(76881);async function n(e){if(!e||!/^\d{6}$/.test(e))return{error:"Invalid Pincode format."};let t=await (0,s.createClient)();try{let{data:r,error:a}=await t.from("pincodes").select("OfficeName, DivisionName, StateName").eq("Pincode",e).order("OfficeName");if(a)return console.error("Pincode Fetch Error:",a),{error:"Database error fetching pincode details."};if(!r||0===r.length)return{error:"Pincode not found."};let s=r[0].StateName,n=r[0].DivisionName,i=[...new Set(r.map(e=>e.OfficeName))];return{data:{city:n,state:s,localities:i},city:n,state:s,localities:i}}catch(e){return console.error("Pincode Lookup Exception:",e),{error:"An unexpected error occurred during pincode lookup."}}}async function i(e){if(!e||e.length<2)return{error:"City name must be at least 2 characters."};let t=await (0,s.createClient)();try{let{data:r,error:a}=await t.from("pincodes").select("Pincode, OfficeName, StateName, DivisionName").ilike("DivisionName",`%${e}%`).order("Pincode");if(a)return console.error("City Fetch Error:",a),{error:"Database error fetching city details."};if(!r||0===r.length)return{error:"City not found."};let s=r[0].StateName,n=[...new Set(r.map(e=>e.Pincode))],i=[...new Set(r.map(e=>e.OfficeName))];return{data:{pincodes:n,state:s,localities:i},pincodes:n,state:s,localities:i}}catch(e){return console.error("City Lookup Exception:",e),{error:"An unexpected error occurred during city lookup."}}}async function c(e){if(!e||e.length<2)return{error:"Query must be at least 2 characters."};let t=await (0,s.createClient)();try{let{data:r,error:a}=await t.rpc("get_distinct_cities",{search_query:`%${e}%`,result_limit:5});if(a){console.error("City Suggestions Error:",a);try{let{data:r,error:a}=await t.from("pincodes").select("DivisionName").ilike("DivisionName",`%${e}%`).order("DivisionName").limit(100);if(a)throw a;if(!r||0===r.length)return{data:{cities:[]},cities:[]};let s=[...new Set(r.map(e=>e.DivisionName.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase())))].slice(0,5);return{data:{cities:s},cities:s}}catch(e){return console.error("Fallback City Query Error:",e),{error:"Database error fetching city suggestions."}}}if(!r||0===r.length)return{data:{cities:[]},cities:[]};let s=r.map(e=>e.city.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase()));return{data:{cities:s},cities:s}}catch(e){return console.error("City Suggestions Exception:",e),{error:"An unexpected error occurred while fetching city suggestions."}}}(0,r(33331).D)([n,i,c]),(0,a.A)(n,"40d32ea8edc6596cf0013772648e4fa734c9679198",null),(0,a.A)(i,"40142db2f2e9e23e42f1d64a8d98f054ab16849fcf",null),(0,a.A)(c,"406809393363051c82bcecb759b1153ca34eced5e4",null)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67790:(e,t,r)=>{Promise.resolve().then(r.bind(r,1119)),Promise.resolve().then(r.bind(r,14896))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87986:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\locality\\\\[localSlug]\\\\LocalityPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},97992:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,1753,8567,4851,3703,3037,6177,3226,1659,3106],()=>r(2849));module.exports=a})();