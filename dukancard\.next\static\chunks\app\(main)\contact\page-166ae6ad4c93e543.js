(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2868],{4516:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},6101:(e,a,r)=>{"use strict";r.d(a,{s:()=>i,t:()=>n});var t=r(12115);function s(e,a){if("function"==typeof e)return e(a);null!=e&&(e.current=a)}function n(...e){return a=>{let r=!1,t=e.map(e=>{let t=s(e,a);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let a=0;a<t.length;a++){let r=t[a];"function"==typeof r?r():s(e[a],null)}}}}function i(...e){return t.useCallback(n(...e),e)}},12486:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},14186:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19420:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},19946:(e,a,r)=>{"use strict";r.d(a,{A:()=>d});var t=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=function(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return a.filter((e,a,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===a).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,t.forwardRef)((e,a)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:u,...m}=e;return(0,t.createElement)("svg",{ref:a,...i,width:s,height:s,stroke:r,strokeWidth:d?24*Number(l)/Number(s):l,className:n("lucide",o),...m},[...u.map(e=>{let[a,r]=e;return(0,t.createElement)(a,r)}),...Array.isArray(c)?c:[c]])}),d=(e,a)=>{let r=(0,t.forwardRef)((r,i)=>{let{className:d,...o}=r;return(0,t.createElement)(l,{ref:i,iconNode:a,className:n("lucide-".concat(s(e)),d),...o})});return r.displayName="".concat(e),r}},27938:(e,a,r)=>{"use strict";r.d(a,{C:()=>t});let t={name:"Dukancard",description:"Create and share digital business cards, showcase products, and connect with customers.",url:"https://dukancard.in",ogImage:"https://dukancard.in/opengraph-image.png",contact:{email:"<EMAIL>",phone:"+91 8458060663",address:{street:"Bisra Road",city:"Rourkela",state:"Odisha",postalCode:"769001",country:"India",full:"Bisra Road, Rourkela, Odisha - 769001"},hours:"Monday - Friday: 9:00 AM - 6:00 PM"},legal:{privacyPolicy:"/privacy",termsOfService:"/terms",refundPolicy:"/refund"},support:{email:"<EMAIL>",phone:"+91 8458060663",helpCenter:"/support"},advertising:{email:"<EMAIL>",phone:"+************",page:"/advertise"}}},28883:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},33786:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35695:(e,a,r)=>{"use strict";var t=r(18999);r.o(t,"usePathname")&&r.d(a,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(a,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(a,{useSearchParams:function(){return t.useSearchParams}})},35742:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]])},43453:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},46622:(e,a,r)=>{"use strict";r.d(a,{A:()=>n});var t=r(95155),s=r(28695);function n(e){let{variant:a="gold",className:r=""}=e;return(0,t.jsxs)("div",{className:"w-full h-12 md:h-16 relative overflow-hidden ".concat(r),children:[(0,t.jsx)("div",{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-7xl px-4 mx-auto",children:(0,t.jsx)(s.P.div,{className:"w-full h-px bg-gradient-to-r from-transparent via-neutral-400 to-transparent dark:via-neutral-600",initial:{width:"0%",left:"50%"},whileInView:{width:"100%",left:"0%"},viewport:{once:!0},transition:{duration:1.2}})}),(0,t.jsx)(s.P.div,{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-neutral-400 dark:bg-neutral-600",initial:{scale:0,opacity:0},whileInView:{scale:1,opacity:.7},viewport:{once:!0},transition:{duration:.5,delay:.7}}),(0,t.jsx)(s.P.div,{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full border-2 border-neutral-300 dark:border-neutral-700",initial:{scale:0,opacity:0},whileInView:{scale:1,opacity:.5},viewport:{once:!0},transition:{duration:.5,delay:.8}})]})}},53999:(e,a,r)=>{"use strict";r.d(a,{M0:()=>c,Yq:()=>u,cn:()=>n,gV:()=>i,gY:()=>o,kY:()=>l,vA:()=>d,vv:()=>m});var t=r(52596),s=r(39688);function n(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,s.QP)((0,t.$)(a))}function i(e){if(!e)return null;let a=e.trim();return(a.startsWith("+91")?a=a.substring(3):12===a.length&&a.startsWith("91")&&(a=a.substring(2)),/^\d{10}$/.test(a))?a:null}function l(e){if(!e||e.length<4)return"Invalid Phone";let a=e.substring(0,2),r=e.substring(e.length-2),t="*".repeat(e.length-4);return"".concat(a).concat(t).concat(r)}function d(e){if(!e||!e.includes("@"))return"Invalid Email";let a=e.split("@"),r=a[0],t=a[1];if(r.length<=2||t.length<=2||!t.includes("."))return"Email Hidden";let s=r.substring(0,2)+"*".repeat(r.length-2),n=t.split("."),i=n[0],l=n.slice(1).join("."),d=i.substring(0,2)+"*".repeat(i.length-2);return"".concat(s,"@").concat(d,".").concat(l)}function o(e){if(null==e||isNaN(e))return"0";let a=Math.abs(e),r=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(a<1e5)return a>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let t=r.length-1;t>=0;t--)if(a>=r[t].value)return(e/r[t].value).toFixed(1).replace(/\.0$/,"")+r[t].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let r={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return a&&(r.hour="2-digit",r.minute="2-digit",r.hour12=!0),e.toLocaleString("en-IN",r)}function m(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:a,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(r){return"".concat(a," ").concat(e.toFixed(2))}}},69074:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},74466:(e,a,r)=>{"use strict";r.d(a,{F:()=>i});var t=r(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=t.$,i=(e,a)=>r=>{var t;if((null==a?void 0:a.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=a,d=Object.keys(i).map(e=>{let a=null==r?void 0:r[e],t=null==l?void 0:l[e];if(null===a)return null;let n=s(a)||s(t);return i[e][n]}),o=r&&Object.entries(r).reduce((e,a)=>{let[r,t]=a;return void 0===t||(e[r]=t),e},{});return n(e,d,null==a||null==(t=a.compoundVariants)?void 0:t.reduce((e,a)=>{let{class:r,className:t,...s}=a;return Object.entries(s).every(e=>{let[a,r]=e;return Array.isArray(r)?r.includes({...l,...o}[a]):({...l,...o})[a]===r})?[...e,r,t]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},81497:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},88482:(e,a,r)=>{"use strict";r.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var t=r(95155);r(12115);var s=r(53999);function n(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function i(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function l(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",a),...r})}function d(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",a),...r})}function o(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",a),...r})}function c(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",a),...r})}},91551:(e,a,r)=>{"use strict";r.d(a,{default:()=>O});var t=r(95155),s=r(12115),n=r(74001),i=r(58829),l=r(28695),d=r(27938),o=r(46622),c=r(28883),u=r(19420),m=r(92138),h=r(97168);function x(){let[e,a]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a(!0)},[]);let r={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},n={hidden:{opacity:0,scale:.8,y:10},visible:{opacity:1,scale:1,y:0,transition:{duration:.5,type:"spring",stiffness:100}}};return(0,t.jsxs)("section",{className:"relative py-16 md:py-24 px-4 overflow-hidden",children:[(0,t.jsxs)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute top-1/4 left-1/2 -translate-x-1/2 w-1/2 h-1/2 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/5 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute bottom-0 right-0 w-1/3 h-1/3 bg-blue-500/10 dark:bg-blue-500/5 rounded-full blur-3xl"})]}),(0,t.jsx)("div",{className:"container mx-auto max-w-7xl",children:(0,t.jsxs)(l.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},initial:"hidden",animate:"visible",className:"text-center max-w-4xl mx-auto",children:[(0,t.jsxs)(l.P.h1,{variants:r,className:"text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6",children:["Get in ",(0,t.jsxs)("span",{className:"text-[var(--brand-gold)] relative",children:["Touch",e&&(0,t.jsx)(l.P.div,{className:"absolute -bottom-1 left-0 h-1 bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full",initial:{width:0,left:"50%"},animate:{width:"100%",left:0},transition:{duration:1,delay:.5}})]})]}),(0,t.jsx)(l.P.p,{variants:r,className:"text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto",children:"Have questions about Dukancard? Our team is here to help you elevate your digital presence. Reach out to us through any of the channels below."}),(0,t.jsxs)(l.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.8}}},className:"flex flex-wrap justify-center gap-4 md:gap-6 mb-10",children:[(0,t.jsx)(l.P.div,{variants:n,children:(0,t.jsxs)(h.$,{onClick:()=>{window.location.href="mailto:".concat(d.C.contact.email)},variant:"outline",size:"lg",className:"bg-white/80 dark:bg-black/50 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group",children:[(0,t.jsx)(c.A,{className:"mr-2 h-5 w-5 text-[var(--brand-gold)] group-hover:scale-110 transition-transform"}),"Email Us"]})}),(0,t.jsx)(l.P.div,{variants:n,children:(0,t.jsxs)(h.$,{onClick:()=>{window.location.href="tel:".concat(d.C.contact.phone.replace(/\s+/g,""))},variant:"outline",size:"lg",className:"bg-white/80 dark:bg-black/50 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group",children:[(0,t.jsx)(u.A,{className:"mr-2 h-5 w-5 text-[var(--brand-gold)] group-hover:scale-110 transition-transform"}),"Call Us"]})})]}),(0,t.jsx)(l.P.div,{variants:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.5,delay:.6}},hover:{scale:1.05,transition:{duration:.2}},tap:{scale:.98,transition:{duration:.1}}},whileHover:"hover",whileTap:"tap",children:(0,t.jsxs)(h.$,{onClick:()=>{window.location.href="/register"},size:"lg",className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-6 py-2 h-auto shadow-md hover:shadow-lg transition-all duration-300",children:["Get Started",(0,t.jsx)(m.A,{className:"ml-2 h-4 w-4"})]})})]})})]})}var v=r(88482),p=r(56671),b=r(43453);let g=(0,r(19946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var f=r(4516),y=r(33786),j=r(14186),w=r(69074);function N(e){let{contactInfo:a,sectionFadeIn:r,itemFadeIn:n}=e,[i,d]=(0,s.useState)(null),o=async(e,a)=>{try{await navigator.clipboard.writeText(e),d(a),p.oR.success("".concat(a," copied to clipboard!")),setTimeout(()=>d(null),2e3)}catch(e){p.oR.error("Failed to copy to clipboard"),console.error("Failed to copy: ",e)}},m={rest:{scale:1,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},hover:{scale:1.02,boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",transition:{duration:.3,ease:"easeOut"}}},x={rest:{scale:1},hover:{scale:1.15,rotate:[0,5,-5,0],transition:{duration:.5,ease:"easeOut"}}};return(0,t.jsxs)(l.P.section,{id:"contact-info-section",variants:r,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},className:"py-16 px-4 container mx-auto max-w-7xl",children:[(0,t.jsxs)(l.P.div,{variants:n(0),className:"text-center mb-12",children:[(0,t.jsxs)("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:["Our ",(0,t.jsx)("span",{className:"text-[var(--brand-gold)]",children:"Contact Details"})]}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Reach out to us through any of these channels. We're here to help you with any questions or concerns."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 items-stretch",children:[(0,t.jsx)(l.P.div,{variants:n(1),whileHover:"hover",initial:"rest",animate:"rest",children:(0,t.jsx)(l.P.div,{variants:m,children:(0,t.jsxs)(v.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]",children:[(0,t.jsxs)("div",{className:"flex items-start mb-4",children:[(0,t.jsx)(l.P.div,{variants:x,className:"bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full",children:(0,t.jsx)(c.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-card-foreground",children:"Email Us"}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Our team usually responds within 24 hours"})]})]}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsx)("p",{className:"text-primary dark:text-[var(--brand-gold)] font-medium mb-4",children:a.email}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(h.$,{onClick:()=>{window.location.href="mailto:".concat(a.email)},variant:"outline",size:"sm",className:"flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Send Email"]}),(0,t.jsx)(h.$,{onClick:()=>o(a.email,"Email"),variant:"outline",size:"icon",className:"border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:"Email"===i?(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(g,{className:"h-4 w-4"})})]})]})]})})}),(0,t.jsx)(l.P.div,{variants:n(2),whileHover:"hover",initial:"rest",animate:"rest",children:(0,t.jsx)(l.P.div,{variants:m,children:(0,t.jsxs)(v.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]",children:[(0,t.jsxs)("div",{className:"flex items-start mb-4",children:[(0,t.jsx)(l.P.div,{variants:x,className:"bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full",children:(0,t.jsx)(u.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-card-foreground",children:"Call Us"}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Available during business hours"})]})]}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsx)("p",{className:"text-primary dark:text-[var(--brand-gold)] font-medium mb-4",children:a.phone}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(h.$,{onClick:()=>{window.location.href="tel:".concat(a.phone.replace(/\s+/g,""))},variant:"outline",size:"sm",className:"flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Call Now"]}),(0,t.jsx)(h.$,{onClick:()=>o(a.phone.replace(/\s+/g,""),"Phone"),variant:"outline",size:"icon",className:"border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:"Phone"===i?(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(g,{className:"h-4 w-4"})})]})]})]})})}),(0,t.jsx)(l.P.div,{variants:n(3),whileHover:"hover",initial:"rest",animate:"rest",children:(0,t.jsx)(l.P.div,{variants:m,children:(0,t.jsxs)(v.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]",children:[(0,t.jsxs)("div",{className:"flex items-start mb-4",children:[(0,t.jsx)(l.P.div,{variants:x,className:"bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full",children:(0,t.jsx)(f.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-card-foreground",children:"Visit Us"}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Our office location"})]})]}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsxs)("address",{className:"not-italic text-primary dark:text-[var(--brand-gold)] font-medium mb-4",children:[a.address.street,(0,t.jsx)("br",{}),a.address.city,", ",a.address.state," - ",a.address.postalCode,(0,t.jsx)("br",{}),a.address.country]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(h.$,{onClick:()=>{window.open("https://maps.app.goo.gl/m6FfJHyYLC53HZiR7","_blank")},variant:"outline",size:"sm",className:"flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"View on Map"]}),(0,t.jsx)(h.$,{onClick:()=>o(a.address.full,"Address"),variant:"outline",size:"icon",className:"border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:"Address"===i?(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(g,{className:"h-4 w-4"})})]})]})]})})}),(0,t.jsx)(l.P.div,{variants:n(4),whileHover:"hover",initial:"rest",animate:"rest",children:(0,t.jsx)(l.P.div,{variants:m,children:(0,t.jsxs)(v.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]",children:[(0,t.jsxs)("div",{className:"flex items-start mb-4",children:[(0,t.jsx)(l.P.div,{variants:x,className:"bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-card-foreground",children:"Business Hours"}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"When we're available"})]})]}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsxs)("ul",{className:"space-y-2 text-primary dark:text-[var(--brand-gold)] font-medium mb-4",children:[(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Monday - Friday:"}),(0,t.jsx)("span",{children:a.hours.split(": ")[1]})]}),(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Saturday:"}),(0,t.jsx)("span",{children:"Closed"})]}),(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Sunday:"}),(0,t.jsx)("span",{children:"Closed"})]})]}),(0,t.jsxs)(h.$,{variant:"outline",size:"sm",className:"w-full border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",onClick:()=>{let e="https://calendar.google.com/calendar/render?action=TEMPLATE&text=".concat(encodeURIComponent("Meeting with Dukancard"),"&dates=20240101T").concat("09:00".replace(":",""),"/20240101T").concat("18:00".replace(":",""));window.open(e,"_blank")},children:[(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Schedule Meeting"]})]})]})})})]})]})}var k=r(76604),A=r(35742);function P(e){let{address:a,sectionFadeIn:r,itemFadeIn:n}=e,i=(0,s.useRef)(null),d=(0,k.W)(i,{once:!0,amount:.3}),[o,c]=(0,s.useState)(!1);(0,s.useEffect)(()=>{c(!0)},[]);let u=()=>{window.open("https://maps.app.goo.gl/m6FfJHyYLC53HZiR7","_blank")};return(0,t.jsxs)(l.P.section,{id:"contact-map-section",variants:r,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},className:"py-16 px-4 container mx-auto max-w-7xl",children:[(0,t.jsxs)(l.P.div,{variants:n(0),className:"text-center mb-12",children:[(0,t.jsxs)("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:["Find ",(0,t.jsx)("span",{className:"text-[var(--brand-gold)]",children:"Us"})]}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Visit our office to meet the team and discuss your business needs in person."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)(l.P.div,{variants:n(1),className:"lg:col-span-2",ref:i,children:(0,t.jsx)(v.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 md:p-8 h-full",children:(0,t.jsx)("div",{className:"w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden border border-border dark:border-[var(--brand-gold)]/20 relative",children:(0,t.jsx)("div",{className:"absolute inset-0 bg-muted dark:bg-gradient-to-br dark:from-neutral-800 dark:to-black flex items-center justify-center",children:o&&d&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-[url('/map-grid.svg')] opacity-10 dark:opacity-20"}),(0,t.jsxs)(l.P.div,{initial:"initial",animate:"animate",variants:{initial:{y:-20,opacity:0},animate:{y:[0,-15,0],opacity:1,transition:{y:{duration:2,repeat:1/0,repeatType:"loop",ease:"easeInOut"},opacity:{duration:.5}}}},className:"relative z-10",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.A,{className:"w-16 h-16 text-[var(--brand-gold)]"}),(0,t.jsx)(l.P.div,{className:"absolute -bottom-2 left-1/2 -translate-x-1/2 w-8 h-8 bg-[var(--brand-gold)]/30 rounded-full",initial:{scale:0,opacity:1},animate:{scale:3,opacity:0},transition:{repeat:1/0,duration:2,ease:"easeOut"}})]}),(0,t.jsx)("div",{className:"absolute top-full left-1/2 -translate-x-1/2 mt-2 bg-white dark:bg-black px-3 py-1 rounded-full shadow-md border border-neutral-200 dark:border-neutral-800",children:(0,t.jsxs)("p",{className:"text-sm font-medium whitespace-nowrap",children:[a.city,", ",a.state]})})]}),(0,t.jsx)(l.P.div,{className:"absolute bottom-4 right-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:1,duration:.5},children:(0,t.jsxs)(h.$,{onClick:u,variant:"outline",size:"sm",className:"bg-white/90 dark:bg-black/70 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:scale-110 transition-transform"}),"Open in Google Maps"]})})]})})})})}),(0,t.jsx)(l.P.div,{variants:n(2),className:"lg:col-span-1",children:(0,t.jsxs)(v.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 md:p-8 h-full",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-foreground mb-6 flex items-center",children:[(0,t.jsx)(A.A,{className:"mr-2 h-5 w-5 text-[var(--brand-gold)]"}),"Directions"]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"bg-[var(--brand-gold)]/10 p-2 rounded-full",children:(0,t.jsx)(f.A,{className:"h-5 w-5 text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-foreground",children:"Our Address"}),(0,t.jsxs)("address",{className:"not-italic text-muted-foreground mt-1",children:[a.street,(0,t.jsx)("br",{}),a.city,", ",a.state," - ",a.postalCode,(0,t.jsx)("br",{}),a.country]})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"bg-[var(--brand-gold)]/10 p-2 rounded-full",children:(0,t.jsx)(j.A,{className:"h-5 w-5 text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-foreground",children:"Office Hours"}),(0,t.jsxs)("ul",{className:"text-muted-foreground mt-1 space-y-1",children:[(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Monday - Friday:"}),(0,t.jsx)("span",{children:"9:00 AM - 6:00 PM"})]}),(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Saturday - Sunday:"}),(0,t.jsx)("span",{children:"Closed"})]})]})]})]}),(0,t.jsx)(l.P.div,{variants:{rest:{scale:1},hover:{scale:1.05,transition:{duration:.2}},tap:{scale:.98,transition:{duration:.1}}},whileHover:"hover",whileTap:"tap",className:"mt-6",children:(0,t.jsxs)(h.$,{onClick:u,className:"w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium",children:[(0,t.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"Get Directions"]})}),(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-neutral-200 dark:border-neutral-800",children:(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Parking is available in the building's underground parking lot. Please call us when you arrive, and we'll guide you to our office."})})]})]})})]})]})}var C=r(35695);function M(e){let{sectionFadeIn:a,itemFadeIn:r}=e,n=(0,C.useRouter)(),[i,o]=(0,s.useState)(!1);(0,s.useEffect)(()=>{o(!0)},[]);let c={rest:{scale:1},hover:{scale:1.05,transition:{duration:.2}},tap:{scale:.98,transition:{duration:.1}}};return(0,t.jsxs)(l.P.section,{id:"contact-cta-section",variants:a,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},className:"py-16 md:py-24 px-4 relative overflow-hidden",children:[(0,t.jsxs)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute top-1/2 left-1/4 -translate-y-1/2 w-1/2 h-1/2 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/5 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute bottom-0 right-0 w-1/3 h-1/3 bg-blue-500/10 dark:bg-blue-500/5 rounded-full blur-3xl"})]}),(0,t.jsx)("div",{className:"container mx-auto max-w-5xl",children:(0,t.jsxs)("div",{className:"bg-white/80 dark:bg-black/50 backdrop-blur-sm border border-neutral-200 dark:border-neutral-800 rounded-2xl p-8 md:p-12",children:[(0,t.jsxs)(l.P.div,{variants:r(0),className:"text-center mb-8 md:mb-12",children:[(0,t.jsxs)("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:["Ready to ",(0,t.jsx)("span",{className:"text-[var(--brand-gold)]",children:"Get Started"}),"?"]}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Create your digital business card today and start connecting with customers in a whole new way."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6",children:[(0,t.jsx)(l.P.div,{variants:r(1),className:"md:col-span-1",children:(0,t.jsx)(l.P.div,{variants:c,whileHover:"hover",whileTap:"tap",children:(0,t.jsxs)(h.$,{onClick:()=>{n.push("/login")},size:"lg",className:"w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium py-2 h-auto shadow-md hover:shadow-lg transition-all duration-300",children:["Get Started",(0,t.jsx)(m.A,{className:"ml-2 h-4 w-4"})]})})}),(0,t.jsx)(l.P.div,{variants:r(2),className:"md:col-span-1",children:(0,t.jsx)(l.P.div,{variants:c,whileHover:"hover",whileTap:"tap",children:(0,t.jsxs)(h.$,{onClick:()=>{n.push("/pricing")},variant:"outline",size:"lg",className:"w-full border-neutral-300 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group py-2 h-auto",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:translate-x-1 transition-transform"}),"View Pricing"]})})}),(0,t.jsx)(l.P.div,{variants:r(4),className:"md:col-span-1",children:(0,t.jsx)(l.P.div,{variants:c,whileHover:"hover",whileTap:"tap",children:(0,t.jsxs)(h.$,{onClick:()=>window.location.href="tel:".concat(d.C.contact.phone.replace(/\s+/g,"")),variant:"outline",size:"lg",className:"w-full border-neutral-300 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group py-2 h-auto",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:rotate-12 transition-transform"}),"Call Us"]})})})]}),(0,t.jsx)(l.P.div,{variants:r(5),className:"mt-8 pt-6 border-t border-neutral-200 dark:border-neutral-800 text-center",children:(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Or reach out via email at"," ",(0,t.jsx)("a",{href:"mailto:".concat(d.C.contact.email),className:"text-[var(--brand-gold)] hover:underline",children:d.C.contact.email})]})})]})})]})}var z=r(81497),S=r(94788),E=r(12486);function I(){let[e,a]=(0,s.useState)(!1),[r,n]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a(!0);let e=()=>{n(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]);let i=e?(()=>{let e=[],a=[c.A,u.A,f.A,z.A,S.A,E.A];for(let r=0;r<12;r++){let s=a[r%a.length];e.push({id:r,icon:(0,t.jsx)(s,{}),posX:100*Math.random(),posY:100*Math.random(),size:10*Math.random()+15,opacity:.07*Math.random()+.03,duration:20*Math.random()+15,delay:5*Math.random(),moveX:(Math.random()-.5)*30,moveY:(Math.random()-.5)*30,rotate:360*Math.random(),rotateDirection:(Math.random()-.5)*360})}return e})():[];return(0,t.jsxs)("div",{className:"absolute inset-0 w-full h-full overflow-hidden pointer-events-none",children:[(0,t.jsx)(l.P.div,{className:"absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold)]/5 blur-3xl dark:bg-[var(--brand-gold)]/10",initial:{opacity:.5},animate:{opacity:.7},transition:{duration:4,repeat:1/0,repeatType:"reverse"}}),(0,t.jsx)(l.P.div,{className:"absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10",initial:{opacity:.5},animate:{opacity:.7},transition:{duration:5,repeat:1/0,repeatType:"reverse",delay:1}}),(0,t.jsx)(l.P.div,{className:"absolute bottom-0 left-1/3 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10",initial:{opacity:.5},animate:{opacity:.7},transition:{duration:6,repeat:1/0,repeatType:"reverse",delay:2}}),e&&i.map(e=>(0,t.jsx)(l.P.div,{className:"absolute text-[var(--brand-gold)]",style:{left:"".concat(e.posX,"%"),top:"".concat(e.posY,"%"),opacity:e.opacity,fontSize:"".concat(r?.7*e.size:e.size,"px")},initial:{rotate:e.rotate,x:0,y:0},animate:{rotate:e.rotate+e.rotateDirection,x:e.moveX,y:e.moveY},transition:{rotate:{duration:e.duration,repeat:1/0,ease:"linear"},x:{duration:e.duration/2,repeat:1/0,repeatType:"reverse",ease:"easeInOut",delay:e.delay},y:{duration:e.duration/3,repeat:1/0,repeatType:"reverse",ease:"easeInOut",delay:e.delay+2}},children:e.icon},e.id)),(0,t.jsx)("div",{className:"absolute inset-0 bg-[url('/grid-pattern.svg')] bg-repeat opacity-[0.015] dark:opacity-[0.03]"})]})}function O(){let[e,a]=(0,s.useState)(!1),r=(0,s.useRef)(null),{scrollYProgress:c}=(0,n.L)({target:r,offset:["start start","end start"]}),u=(0,i.G)(c,[0,.2,.8,1],[1,.8,.4,.2]),m={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,staggerChildren:.2}}},h=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5,delay:.1*e}}}};return(0,s.useEffect)(()=>{a(!0)},[]),(0,t.jsxs)("div",{ref:r,className:"min-h-screen bg-white dark:bg-black text-black dark:text-white overflow-hidden",children:[(0,t.jsx)(l.P.div,{className:"fixed inset-0 z-0 pointer-events-none",style:{opacity:u},children:(0,t.jsx)(I,{})}),(0,t.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:+!!e},transition:{duration:.5},className:"w-full pt-8 sm:pt-10 md:pt-12 mt-2 sm:mt-3 md:mt-4",children:[(0,t.jsx)(x,{}),(0,t.jsx)(o.A,{variant:"gold"}),(0,t.jsx)(N,{contactInfo:d.C.contact,sectionFadeIn:m,itemFadeIn:h}),(0,t.jsx)(o.A,{variant:"gold"}),(0,t.jsx)(P,{address:d.C.contact.address,sectionFadeIn:m,itemFadeIn:h}),(0,t.jsx)(o.A,{variant:"blue"}),(0,t.jsx)(M,{sectionFadeIn:m,itemFadeIn:h})]})]})}},94788:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(19946).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},96170:(e,a,r)=>{Promise.resolve().then(r.bind(r,91551))},97168:(e,a,r)=>{"use strict";r.d(a,{$:()=>d,r:()=>l});var t=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(53999);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:a,variant:r,size:n,asChild:d=!1,...o}=e,c=d?s.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:n,className:a})),...o})}},99708:(e,a,r)=>{"use strict";r.d(a,{DX:()=>i});var t=r(12115),s=r(6101),n=r(95155),i=function(e){let a=function(e){let a=t.forwardRef((e,a)=>{let{children:r,...n}=e;if(t.isValidElement(r)){var i;let e,l,d=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),o=function(e,a){let r={...a};for(let t in a){let s=e[t],n=a[t];/^on[A-Z]/.test(t)?s&&n?r[t]=(...e)=>{let a=n(...e);return s(...e),a}:s&&(r[t]=s):"style"===t?r[t]={...s,...n}:"className"===t&&(r[t]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==t.Fragment&&(o.ref=a?(0,s.t)(a,d):d),t.cloneElement(r,o)}return t.Children.count(r)>1?t.Children.only(null):null});return a.displayName=`${e}.SlotClone`,a}(e),r=t.forwardRef((e,r)=>{let{children:s,...i}=e,l=t.Children.toArray(s),o=l.find(d);if(o){let e=o.props.children,s=l.map(a=>a!==o?a:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,n.jsx)(a,{...i,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,s):null})}return(0,n.jsx)(a,{...i,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}("Slot"),l=Symbol("radix.slottable");function d(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8695,6671,647,8441,1684,7358],()=>a(96170)),_N_E=e.O()}]);