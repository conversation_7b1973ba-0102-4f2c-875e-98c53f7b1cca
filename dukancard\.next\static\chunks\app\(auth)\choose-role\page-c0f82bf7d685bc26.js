(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4313],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>s});var a=r(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,a=e.map(e=>{let a=n(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return a.useCallback(s(...e),e)}},13052:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17576:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...i,width:n,height:n,stroke:r,strokeWidth:o?24*Number(l)/Number(n):l,className:s("lucide",d),...m},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let r=(0,a.forwardRef)((r,i)=>{let{className:o,...d}=r;return(0,a.createElement)(l,{ref:i,iconNode:t,className:s("lucide-".concat(n(e)),o),...d})});return r.displayName="".concat(e),r}},34477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return a.callServer},createServerReference:function(){return s},findSourceMapURL:function(){return n.findSourceMapURL}});let a=r(53806),n=r(31818),s=r(34979).createServerReference},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},36283:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var a=r(95155),n=r(12115),s=r(35695),i=r(28695),l=r(97168),o=r(88482),d=r(51154),c=r(71007),u=r(13052),m=r(17576),x=r(56671),h=r(34477);let p=(0,h.createServerReference)("705eb1a7220c6029620523c3d27ad07960d1c732cd",h.callServer,void 0,h.findSourceMapURL,"createCustomerProfileAction");function f(e){let{userId:t,redirectSlug:r,message:h}=e,f=(0,s.useRouter)(),v=(0,s.useSearchParams)(),[g,b]=(0,n.useTransition)(),[y,w]=(0,n.useState)(null),[j,k]=(0,n.useState)(r||null),[N,A]=(0,n.useState)(h||null);(0,n.useEffect)(()=>{if(!j){let e=v.get("redirect");if(e)k(e),console.log("Got redirect from URL: ".concat(e));else if(1){let e=localStorage.getItem("chooseRoleRedirect");e&&(k(e),console.log("Got redirect from localStorage: ".concat(e)),localStorage.removeItem("chooseRoleRedirect"))}}if(!N){let e=v.get("message");if(e)A(e),console.log("Got message from URL: ".concat(e));else if(1){let e=localStorage.getItem("chooseRoleMessage");e&&(A(e),console.log("Got message from localStorage: ".concat(e)),localStorage.removeItem("chooseRoleMessage"))}}},[v,j,N]);let S={hover:{scale:1.02,boxShadow:"0 10px 25px -5px rgba(212, 175, 55, 0.15)",transition:{type:"spring",stiffness:300}},tap:{scale:.98}};return(0,a.jsx)("div",{className:"w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6",children:(0,a.jsx)(i.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},className:"w-full max-w-[90%] sm:max-w-md md:max-w-lg",children:(0,a.jsxs)(o.Zp,{className:"overflow-hidden border dark:border-[#D4AF37]/30 border-[#D4AF37]/20 bg-white dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black shadow-lg rounded-xl",children:[(0,a.jsxs)(o.aR,{className:"text-center relative pb-3 sm:pb-6 px-3 sm:px-6 pt-4 sm:pt-6",children:[(0,a.jsx)(i.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.2,duration:.5},children:(0,a.jsx)("div",{className:"flex justify-center items-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1 sm:mb-2",children:[(0,a.jsxs)("span",{className:"font-bold text-base sm:text-lg md:text-xl text-[var(--brand-gold)]",children:["Dukan",(0,a.jsx)("span",{className:"text-foreground dark:text-white",children:"card"})]}),(0,a.jsx)(i.P.span,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.8,duration:.5},children:(0,a.jsx)("span",{className:"ml-1"})})]}),(0,a.jsx)(o.ZB,{className:"text-xl sm:text-2xl md:text-3xl font-bold text-neutral-800 dark:text-white",children:"Choose Your Role"})]})})}),(0,a.jsx)(o.BT,{className:"text-neutral-500 dark:text-neutral-400 pt-1 sm:pt-2 text-xs sm:text-sm md:text-base max-w-md mx-auto",children:"Select how you'll be using our platform. This is a one-time setup that cannot be changed later."})]}),(0,a.jsxs)(o.Wu,{className:"space-y-3 sm:space-y-4 md:space-y-6 px-3 sm:px-6 md:px-8 pb-4 sm:pb-6 md:pb-8",children:[(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.5},children:(0,a.jsx)(i.P.div,{variants:S,whileHover:"hover",whileTap:"tap",children:(0,a.jsx)("div",{className:"group",children:(0,a.jsxs)(l.$,{onClick:()=>{w("customer"),b(async()=>{let e=await p(t,j,N);(null==e?void 0:e.error)&&(x.oR.error("Failed to set up account: ".concat(e.error)),w(null))})},className:"cursor-pointer w-full h-auto py-2 sm:py-3 md:py-6 px-2 sm:px-3 md:px-5 bg-neutral-50 hover:bg-neutral-100 dark:bg-neutral-800/70 dark:hover:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:border-[#D4AF37]/50 dark:hover:border-[#D4AF37]/50 text-neutral-800 dark:text-white flex items-center justify-between rounded-lg sm:rounded-xl group-hover:shadow-md transition-all",disabled:g,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 md:space-x-4 max-w-[85%]",children:[(0,a.jsx)("div",{className:"bg-[#D4AF37]/10 dark:bg-[#D4AF37]/20 p-1.5 sm:p-2 md:p-3 rounded-md sm:rounded-lg flex-shrink-0",children:g&&"customer"===y?(0,a.jsx)(d.A,{"data-testid":"customer-loader",className:"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37] animate-spin"}):(0,a.jsx)(c.A,{className:"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37]"})}),(0,a.jsxs)("div",{className:"text-left min-w-0",children:[(0,a.jsx)("span",{className:"text-sm sm:text-base md:text-lg font-semibold block mb-0 sm:mb-0.5 md:mb-1 truncate",children:"As a Customer"}),(0,a.jsx)("p",{className:"text-xs sm:text-xs md:text-sm text-neutral-500 dark:text-neutral-400 line-clamp-2",children:"Browse and connect with businesses."})]})]}),(0,a.jsx)(u.A,{className:"w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-neutral-400 dark:text-neutral-500 group-hover:text-[#D4AF37] transition-colors mr-1 sm:mr-1 md:mr-2 flex-shrink-0"})]})})})}),(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5,duration:.5},children:(0,a.jsx)(i.P.div,{variants:S,whileHover:"hover",whileTap:"tap",children:(0,a.jsx)("div",{className:"group",children:(0,a.jsxs)(l.$,{onClick:()=>{w("business");let e="/onboarding",t=new URLSearchParams;j&&t.append("redirect",j),N&&t.append("message",N),t.toString()&&(e+="?".concat(t.toString())),f.push(e)},className:"cursor-pointer w-full h-auto py-2 sm:py-3 md:py-6 px-2 sm:px-3 md:px-5 bg-neutral-50 hover:bg-neutral-100 dark:bg-neutral-800/70 dark:hover:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:border-[#D4AF37]/50 dark:hover:border-[#D4AF37]/50 text-neutral-800 dark:text-white flex items-center justify-between rounded-lg sm:rounded-xl group-hover:shadow-md transition-all",disabled:g,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 md:space-x-4 max-w-[85%]",children:[(0,a.jsx)("div",{className:"bg-[#D4AF37]/10 dark:bg-[#D4AF37]/20 p-1.5 sm:p-2 md:p-3 rounded-md sm:rounded-lg flex-shrink-0",children:g&&"business"===y?(0,a.jsx)(d.A,{"data-testid":"business-loader",className:"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37] animate-spin"}):(0,a.jsx)(m.A,{className:"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37]"})}),(0,a.jsxs)("div",{className:"text-left min-w-0",children:[(0,a.jsx)("span",{className:"text-sm sm:text-base md:text-lg font-semibold block mb-0 sm:mb-0.5 md:mb-1 truncate",children:"As a Business"}),(0,a.jsx)("p",{className:"text-xs sm:text-xs md:text-sm text-neutral-500 dark:text-neutral-400 line-clamp-2",children:"Create your digital card and store."})]})]}),(0,a.jsx)(u.A,{className:"w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-neutral-400 dark:text-neutral-500 group-hover:text-[#D4AF37] transition-colors mr-1 sm:mr-1 md:mr-2 flex-shrink-0"})]})})})}),(0,a.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.7,duration:.5},children:(0,a.jsx)("div",{className:"pt-1 sm:pt-2 md:pt-4 text-center",children:(0,a.jsx)("p",{className:"text-[10px] sm:text-xs md:text-sm text-red-500 dark:text-red-400 font-medium",children:"Note: This choice is permanent and cannot be changed later."})})})]})]})})})}},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{M0:()=>c,Yq:()=>u,cn:()=>s,gV:()=>i,gY:()=>d,kY:()=>l,vA:()=>o,vv:()=>m});var a=r(52596),n=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function i(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function l(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),r=e.substring(e.length-2),a="*".repeat(e.length-4);return"".concat(t).concat(a).concat(r)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),r=t[0],a=t[1];if(r.length<=2||a.length<=2||!a.includes("."))return"Email Hidden";let n=r.substring(0,2)+"*".repeat(r.length-2),s=a.split("."),i=s[0],l=s.slice(1).join("."),o=i.substring(0,2)+"*".repeat(i.length-2);return"".concat(n,"@").concat(o,".").concat(l)}function d(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),r=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let a=r.length-1;a>=0;a--)if(t>=r[a].value)return(e/r[a].value).toFixed(1).replace(/\.0$/,"")+r[a].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let r={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(r.hour="2-digit",r.minute="2-digit",r.hour12=!0),e.toLocaleString("en-IN",r)}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(r){return"".concat(t," ").concat(e.toFixed(2))}}},62305:(e,t,r)=>{Promise.resolve().then(r.bind(r,36283))},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var a=r(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=a.$,i=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],a=null==l?void 0:l[e];if(null===t)return null;let s=n(t)||n(a);return i[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return s(e,o,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>s,aR:()=>i,wL:()=>c});var a=r(95155);r(12115);var n=r(53999);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>l});var a=r(95155);r(12115);var n=r(99708),s=r(74466),i=r(53999);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,asChild:o=!1,...d}=e,c=o?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:s,className:t})),...d})}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i});var a=r(12115),n=r(6101),s=r(95155),i=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){var i;let e,l,o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let a in t){let n=e[a],s=t[a];/^on[A-Z]/.test(a)?n&&s?r[a]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[a]=n):"style"===a?r[a]={...n,...s}:"className"===a&&(r[a]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(d.ref=t?(0,n.t)(t,o):o),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:n,...i}=e,l=a.Children.toArray(n),d=l.find(o);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}("Slot"),l=Symbol("radix.slottable");function o(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6671,8441,1684,7358],()=>t(62305)),_N_E=e.O()}]);