(()=>{var e={};e.id=2199,e.ids=[2199,5453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,t,s)=>{"use strict";s.d(t,{CG:()=>r,SC:()=>i,cZ:()=>a});let r={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},i={BUSINESS:"business",CUSTOMERS:"customers"},a={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},32032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{createClient:()=>i});var r=s(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let i=null,a=null;try{let{headers:e,cookies:t}=await s.e(4999).then(s.bind(s,44999));i=await e(),a=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,i=e.get("x-test-business-slug"),a=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,i){let a=()=>{var a,n,o,u,c;return a=e,n=t,o=s,u=r,c=i,"customer_profiles"===a?{data:o&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===a?{data:o&&"business"===n?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===a?{data:"business"===n?{id:"test-subscription-id",plan_id:c,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===a?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,s)=>n(e),neq:(t,s)=>n(e),gt:(t,s)=>n(e),gte:(t,s)=>n(e),lt:(t,s)=>n(e),lte:(t,s)=>n(e),like:(t,s)=>n(e),ilike:(t,s)=>n(e),is:(t,s)=>n(e),in:(t,s)=>n(e),contains:(t,s)=>n(e),containedBy:(t,s)=>n(e),rangeGt:(t,s)=>n(e),rangeGte:(t,s)=>n(e),rangeLt:(t,s)=>n(e),rangeLte:(t,s)=>n(e),rangeAdjacent:(t,s)=>n(e),overlaps:(t,s)=>n(e),textSearch:(t,s)=>n(e),match:t=>n(e),not:(t,s,r)=>n(e),or:t=>n(e),filter:(t,s,r)=>n(e),order:(t,s)=>n(e),limit:(t,s)=>n(e),range:(t,s,r)=>n(e),abortSignal:t=>n(e),single:async()=>a(),maybeSingle:async()=>a(),then:async e=>{let t=a();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,s,r,i,a)}}(i):a?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await a.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await a.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87099:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>A,routeModule:()=>p,serverHooks:()=>R,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>S});var r={};s.r(r),s.d(r,{POST:()=>d});var i=s(96559),a=s(48088),n=s(37719),o=s(32190),u=s(32032),c=s(31546),l=s(30468);async function d(e,{params:t}){try{let{id:r}=await t,{cancelAtCycleEnd:i=!1,reason:a=null}=await e.json(),n=await (0,u.createClient)(),{data:{user:d},error:p}=await n.auth.getUser();if(p||!d)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let{data:_,error:S}=await n.from(l.CG.PAYMENT_SUBSCRIPTIONS).select("*").eq(l.cZ.RAZORPAY_SUBSCRIPTION_ID,r).single();if(S){if("PGRST116"===S.code)return o.NextResponse.json({success:!1,error:"Subscription not found"},{status:404});return console.error("[RAZORPAY_ERROR] Error fetching subscription:",S),o.NextResponse.json({success:!1,error:"Error fetching subscription"},{status:500})}if(_.business_profile_id!==d.id)return o.NextResponse.json({success:!1,error:"Unauthorized to cancel this subscription"},{status:403});if("cancelled"===_.subscription_status)return o.NextResponse.json({success:!1,error:"Subscription is already cancelled"},{status:400});let R=new Date().toISOString();if("authenticated"===_.subscription_status){console.log("[RAZORPAY_DEBUG] Handling cancellation for authenticated subscription in API route using pause endpoint");let{pauseSubscription:e}=await Promise.resolve().then(s.bind(s,31546)),t=await e(r,"now",!0);t.success?console.log("[RAZORPAY_DEBUG] Successfully paused/cancelled authenticated subscription"):(console.error("[RAZORPAY_ERROR] Error pausing authenticated subscription:",t.error),console.log("[RAZORPAY_DEBUG] Proceeding with database update despite API failure"));let{error:i}=await n.from(l.CG.PAYMENT_SUBSCRIPTIONS).update({cancellation_requested_at:R,cancellation_reason:a,updated_at:R}).eq(l.cZ.RAZORPAY_SUBSCRIPTION_ID,r);if(i)return console.error("[RAZORPAY_ERROR] Error updating subscription record:",i),o.NextResponse.json({success:!1,error:"Failed to update subscription record"},{status:500});let u=t.success?t.data:{id:r,status:"cancelled"};return o.NextResponse.json({success:!0,data:{...u,db_subscription:{id:_.id,cancellation_requested:!0,cancellation_requested_at:R,cancellation_reason:a}}},{status:200})}let A=await (0,c.cancelSubscription)(r,i);if(!A.success)return o.NextResponse.json({success:!1,error:A.error},{status:400});let{error:I}=await n.from(l.CG.PAYMENT_SUBSCRIPTIONS).update({cancellation_requested_at:R,cancellation_reason:a,updated_at:R}).eq(l.cZ.RAZORPAY_SUBSCRIPTION_ID,r);return I&&console.error("[RAZORPAY_ERROR] Error updating subscription record:",I),o.NextResponse.json({success:!0,data:{...A.data,db_subscription:{id:_.id,cancellation_requested:!0,cancellation_requested_at:R,cancellation_reason:a}}},{status:200})}catch(e){return console.error("[RAZORPAY_ERROR] Error cancelling subscription:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/cancel/route",pathname:"/api/subscriptions/[id]/cancel",filename:"route",bundlePath:"app/api/subscriptions/[id]/cancel/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\cancel\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:_,workUnitAsyncStorage:S,serverHooks:R}=p;function A(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:S})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95453:(e,t,s)=>{"use strict";s.d(t,{ST:()=>a,bG:()=>o,t6:()=>u});var r=s(55511),i=s.n(r);let a="https://api.razorpay.com/v2",n=()=>{let e,t;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",t=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!t)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!t,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:t}},o=()=>{let{keyId:e,keySecret:t}=n(),s=Buffer.from(`${e}:${t}`).toString("base64");return{Authorization:`Basic ${s}`,"Content-Type":"application/json"}},u=(e,t,s)=>{try{let r=i().createHmac("sha256",s).update(e).digest("hex");return i().timingSafeEqual(Buffer.from(t),Buffer.from(r))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,9398,4386,580,1546],()=>s(87099));module.exports=r})();