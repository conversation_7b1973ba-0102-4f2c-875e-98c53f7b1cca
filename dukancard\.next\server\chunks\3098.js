"use strict";exports.id=3098,exports.ids=[3098],exports.modules={3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},6943:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},12941:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},17135:(e,t,n)=>{n.d(t,{d:()=>s});var r=n(24342),o=n(43210),i=n(32582),l=n(72789);function s(e){let t=(0,l.M)(()=>(0,r.OQ)(e)),{isStatic:n}=(0,o.useContext)(i.Q);if(n){let[,n]=(0,o.useState)(e);(0,o.useEffect)(()=>t.on("change",n),[])}return t}},18265:(e,t,n)=>{n.d(t,{W:()=>l});var r=n(43210),o=n(99292);let i={some:0,all:1};function l(e,{root:t,margin:n,amount:s,once:f=!1,initial:c=!1}={}){let[u,a]=(0,r.useState)(c);return(0,r.useEffect)(()=>{if(!e.current||f&&u)return;let r={root:t&&t.current||void 0,margin:n,amount:s};return function(e,t,{root:n,margin:r,amount:l="some"}={}){let s=(0,o.K)(e),f=new WeakMap,c=new IntersectionObserver(e=>{e.forEach(e=>{let n=f.get(e.target);if(!!n!==e.isIntersecting)if(e.isIntersecting){let n=t(e.target,e);"function"==typeof n?f.set(e.target,n):c.unobserve(e.target)}else"function"==typeof n&&(n(e),f.delete(e.target))})},{root:n,rootMargin:r,threshold:"number"==typeof l?l:i[l]});return s.forEach(e=>c.observe(e)),()=>c.disconnect()}(e.current,()=>(a(!0),f?void 0:()=>a(!1)),r)},[t,e,n,f,s]),u}},20798:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},53342:(e,t,n)=>{n.d(t,{G:()=>u});var r=n(19331),o=n(72789),i=n(23671),l=n(15124),s=n(17135);function f(e,t){let n=(0,s.d)(t()),r=()=>n.set(t());return r(),(0,l.E)(()=>{let t=()=>i.Gt.preRender(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,i.WG)(r)}}),n}var c=n(24342);function u(e,t,n,o){if("function"==typeof e){c.bt.current=[],e();let t=f(c.bt.current,e);return c.bt.current=void 0,t}let i="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),n=t?0:-1,o=e[0+n],i=e[1+n],l=e[2+n],s=e[3+n],f=(0,r.G)(i,l,s);return t?f(o):f}(t,n,o);return Array.isArray(e)?a(e,i):a([e],([e])=>i(e))}function a(e,t){let n=(0,o.M)(()=>[]);return f(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}},70334:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},82374:(e,t,n)=>{let r,o;n.d(t,{L:()=>I});var i=n(24342),l=n(66244),s=n(43210),f=n(23671);function c(e,t){let n,r=()=>{let{currentTime:r}=t,o=(null===r?0:r.value)/100;n!==o&&e(o),n=o};return f.Gt.preUpdate(r,!0),()=>(0,f.WG)(r)}var u=n(82186),a=n(99292);let d=new WeakMap;function h({target:e,contentRect:t,borderBoxSize:n}){d.get(e)?.forEach(r=>{r({target:e,contentSize:t,get size(){if(n){let{inlineSize:e,blockSize:t}=n[0];return{width:e,height:t}}if(e instanceof SVGElement&&"getBBox"in e)return e.getBBox();return{width:e.offsetWidth,height:e.offsetHeight}}})})}function g(e){e.forEach(h)}let p=new Set;var m=n(64068),y=n(15547);let v=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),x=()=>({time:0,x:v(),y:v()}),w={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function E(e,t,n,r){let o=n[t],{length:i,position:l}=w[t],s=o.current,f=n.time;o.current=e[`scroll${l}`],o.scrollLength=e[`scroll${i}`]-e[`client${i}`],o.offset.length=0,o.offset[0]=0,o.offset[1]=o.scrollLength,o.progress=(0,m.q)(0,o.scrollLength,o.current);let c=r-f;o.velocity=c>50?0:(0,y.f)(o.current-s,c)}var M=n(19331),W=n(3098),L=n(97758);let b={start:0,center:.5,end:1};function k(e,t,n=0){let r=0;if(e in b&&(e=b[e]),"string"==typeof e){let t=parseFloat(e);e.endsWith("px")?r=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?r=t/100*document.documentElement.clientWidth:e.endsWith("vh")?r=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(r=t*e),n+r}let A=[0,0],z={All:[[0,0],[1,1]]},G={x:0,y:0},O=new WeakMap,H=new WeakMap,R=new WeakMap,S=e=>e===document.documentElement?window:e;function P(e,{container:t=document.documentElement,...n}={}){let i=R.get(t);i||(i=new Set,R.set(t,i));let l=function(e,t,n,r={}){return{measure:()=>(function(e,t=e,n){if(n.x.targetOffset=0,n.y.targetOffset=0,t!==e){let r=t;for(;r&&r!==e;)n.x.targetOffset+=r.offsetLeft,n.y.targetOffset+=r.offsetTop,r=r.offsetParent}n.x.targetLength=t===e?t.scrollWidth:t.clientWidth,n.y.targetLength=t===e?t.scrollHeight:t.clientHeight,n.x.containerLength=e.clientWidth,n.y.containerLength=e.clientHeight})(e,r.target,n),update:t=>{E(e,"x",n,t),E(e,"y",n,t),n.time=t,(r.offset||r.target)&&function(e,t,n){let{offset:r=z.All}=n,{target:o=e,axis:i="y"}=n,l="y"===i?"height":"width",s=o!==e?function(e,t){let n={x:0,y:0},r=e;for(;r&&r!==t;)if(r instanceof HTMLElement)n.x+=r.offsetLeft,n.y+=r.offsetTop,r=r.offsetParent;else if("svg"===r.tagName){let e=r.getBoundingClientRect(),t=(r=r.parentElement).getBoundingClientRect();n.x+=e.left-t.left,n.y+=e.top-t.top}else if(r instanceof SVGGraphicsElement){let{x:e,y:t}=r.getBBox();n.x+=e,n.y+=t;let o=null,i=r.parentNode;for(;!o;)"svg"===i.tagName&&(o=i),i=r.parentNode;r=o}else break;return n}(o,e):G,f=o===e?{width:e.scrollWidth,height:e.scrollHeight}:"getBBox"in o&&"svg"!==o.tagName?o.getBBox():{width:o.clientWidth,height:o.clientHeight},c={width:e.clientWidth,height:e.clientHeight};t[i].offset.length=0;let u=!t[i].interpolate,a=r.length;for(let e=0;e<a;e++){let n=function(e,t,n,r){let o=Array.isArray(e)?e:A,i=0,l=0;return"number"==typeof e?o=[e,e]:"string"==typeof e&&(o=(e=e.trim()).includes(" ")?e.split(" "):[e,b[e]?e:"0"]),(i=k(o[0],n,r))-k(o[1],t)}(r[e],c[l],f[l],s[i]);u||n===t[i].interpolatorOffsets[e]||(u=!0),t[i].offset[e]=n}u&&(t[i].interpolate=(0,M.G)(t[i].offset,(0,W.Z)(r),{clamp:!1}),t[i].interpolatorOffsets=[...t[i].offset]),t[i].progress=(0,L.q)(0,1,t[i].interpolate(t[i].current))}(e,n,r)},notify:()=>t(n)}}(t,e,x(),n);if(i.add(l),!O.has(t)){let e=()=>{for(let e of i)e.measure()},n=()=>{for(let e of i)e.update(f.uv.timestamp)},l=()=>{for(let e of i)e.notify()},s=()=>{f.Gt.read(e,!1,!0),f.Gt.read(n,!1,!0),f.Gt.preUpdate(l,!1,!0)};O.set(t,s);let c=S(t);window.addEventListener("resize",s,{passive:!0}),t!==document.documentElement&&H.set(t,"function"==typeof t?(p.add(t),o||(o=()=>{let e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};p.forEach(e=>e(t))},window.addEventListener("resize",o)),()=>{p.delete(t),!p.size&&o&&(o=void 0)}):function(e,t){r||"undefined"!=typeof ResizeObserver&&(r=new ResizeObserver(g));let n=(0,a.K)(e);return n.forEach(e=>{let n=d.get(e);n||(n=new Set,d.set(e,n)),n.add(t),r?.observe(e)}),()=>{n.forEach(e=>{let n=d.get(e);n?.delete(t),n?.size||r?.unobserve(e)})}}(t,s)),c.addEventListener("scroll",s,{passive:!0})}let s=O.get(t);return f.Gt.read(s,!1,!0),()=>{(0,f.WG)(s);let e=R.get(t);if(!e||(e.delete(l),e.size))return;let n=O.get(t);O.delete(t),n&&(S(t).removeEventListener("scroll",n),H.get(t)?.(),window.removeEventListener("resize",n))}}let B=new Map;function C({source:e,container:t,...n}){let{axis:r}=n;e&&(t=e);let o=B.get(t)??new Map;B.set(t,o);let i=n.target??"self",l=o.get(i)??{},s=r+(n.offset??[]).join(",");return l[s]||(l[s]=!n.target&&(0,u.J)()?new ScrollTimeline({source:t,axis:r}):function(e){let t={value:0},n=P(n=>{t.value=100*n[e.axis].progress},e);return{currentTime:t,cancel:n}}({container:t,...n})),l[s]}var $=n(72789),T=n(15124);function j(e,t){(0,l.$)(!!(!t||t.current),`You have defined a ${e} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \`layoutEffect: false\` option.`)}let Q=()=>({scrollX:(0,i.OQ)(0),scrollY:(0,i.OQ)(0),scrollXProgress:(0,i.OQ)(0),scrollYProgress:(0,i.OQ)(0)});function I({container:e,target:t,layoutEffect:n=!0,...r}={}){let o=(0,$.M)(Q);return(n?T.E:s.useEffect)(()=>(j("target",t),j("container",e),function(e,{axis:t="y",container:n=document.documentElement,...r}={}){var o,i;n===document.documentElement&&("y"===t&&n.scrollHeight===n.clientHeight||"x"===t&&n.scrollWidth===n.clientWidth)&&(n=document.body);let l={axis:t,container:n,...r};return"function"==typeof e?(o=e,i=l,2===o.length?P(e=>{o(e[i.axis].progress,e)},i):c(o,C(i))):function(e,t){let n=C(t);return e.attachTimeline({timeline:t.target?void 0:n,observe:e=>(e.pause(),c(t=>{e.time=e.duration*t},n))})}(e,l)}((e,{x:t,y:n})=>{o.scrollX.set(t.current),o.scrollXProgress.set(t.progress),o.scrollY.set(n.current),o.scrollYProgress.set(n.progress)},{...r,container:e?.current||void 0,target:t?.current||void 0})),[e,t,JSON.stringify(r.offset)]),o}},88920:(e,t,n)=>{n.d(t,{N:()=>y});var r=n(60687),o=n(43210),i=n(12157),l=n(72789),s=n(15124),f=n(21279),c=n(32582);class u extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=e instanceof HTMLElement&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function a({children:e,isPresent:t,anchorX:n}){let i=(0,o.useId)(),l=(0,o.useRef)(null),s=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=(0,o.useContext)(c.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:r,top:o,left:c,right:u}=s.current;if(t||!l.current||!e||!r)return;let a="left"===n?`left: ${c}`:`right: ${u}`;l.current.dataset.motionPopId=i;let d=document.createElement("style");return f&&(d.nonce=f),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${a}px !important;
            top: ${o}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),(0,r.jsx)(u,{isPresent:t,childRef:l,sizeRef:s,children:o.cloneElement(e,{ref:l})})}let d=({children:e,initial:t,isPresent:n,onExitComplete:i,custom:s,presenceAffectsLayout:c,mode:u,anchorX:d})=>{let g=(0,l.M)(h),p=(0,o.useId)(),m=!0,y=(0,o.useMemo)(()=>(m=!1,{id:p,initial:t,isPresent:n,custom:s,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;i&&i()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[n,g,i]);return c&&m&&(y={...y}),(0,o.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[n]),o.useEffect(()=>{n||g.size||!i||i()},[n]),"popLayout"===u&&(e=(0,r.jsx)(a,{isPresent:n,anchorX:d,children:e})),(0,r.jsx)(f.t.Provider,{value:y,children:e})};function h(){return new Map}var g=n(86044);let p=e=>e.key||"";function m(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let y=({children:e,custom:t,initial:n=!0,onExitComplete:f,presenceAffectsLayout:c=!0,mode:u="sync",propagate:a=!1,anchorX:h="left"})=>{let[y,v]=(0,g.xQ)(a),x=(0,o.useMemo)(()=>m(e),[e]),w=a&&!y?[]:x.map(p),E=(0,o.useRef)(!0),M=(0,o.useRef)(x),W=(0,l.M)(()=>new Map),[L,b]=(0,o.useState)(x),[k,A]=(0,o.useState)(x);(0,s.E)(()=>{E.current=!1,M.current=x;for(let e=0;e<k.length;e++){let t=p(k[e]);w.includes(t)?W.delete(t):!0!==W.get(t)&&W.set(t,!1)}},[k,w.length,w.join("-")]);let z=[];if(x!==L){let e=[...x];for(let t=0;t<k.length;t++){let n=k[t],r=p(n);w.includes(r)||(e.splice(t,0,n),z.push(n))}return"wait"===u&&z.length&&(e=z),A(m(e)),b(x),null}let{forceRender:G}=(0,o.useContext)(i.L);return(0,r.jsx)(r.Fragment,{children:k.map(e=>{let o=p(e),i=(!a||!!y)&&(x===k||w.includes(o));return(0,r.jsx)(d,{isPresent:i,initial:(!E.current||!!n)&&void 0,custom:t,presenceAffectsLayout:c,mode:u,onExitComplete:i?void 0:()=>{if(!W.has(o))return;W.set(o,!0);let e=!0;W.forEach(t=>{t||(e=!1)}),e&&(G?.(),A(M.current),a&&v?.(),f&&f())},anchorX:h,children:e},o)})})}}};