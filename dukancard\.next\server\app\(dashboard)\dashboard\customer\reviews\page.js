(()=>{var e={};e.id=2560,e.ids=[2560],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24107:(e,r,t)=>{"use strict";function a(e){let{pincode:r,state:t,city:a,locality:s}=e;return!!(r&&""!==r.trim()&&t&&""!==t.trim()&&a&&""!==a.trim()&&s&&""!==s.trim())}function s(e){let r=[];return e.pincode&&""!==e.pincode.trim()||r.push("pincode"),e.state&&""!==e.state.trim()||r.push("state"),e.city&&""!==e.city.trim()||r.push("city"),e.locality&&""!==e.locality.trim()||r.push("locality"),r}function i(e){if(0===e.length)return"";let r=e.map(e=>{switch(e){case"pincode":return"Pincode";case"state":return"State";case"city":return"City";case"locality":return"Locality";default:return e}});if(1===r.length)return`Please update your ${r[0]} in your profile.`;{if(2===r.length)return`Please update your ${r.join(" and ")} in your profile.`;let e=r.pop();return`Please update your ${r.join(", ")}, and ${e} in your profile.`}}t.d(r,{Gs:()=>a,SJ:()=>s,zp:()=>i})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36719:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["customer",{children:["reviews",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,93379)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,78050)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/customer/reviews/page",pathname:"/dashboard/customer/reviews",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},39599:(e,r,t)=>{"use strict";t.d(r,{default:()=>x});var a=t(60687),s=t(64398),i=t(43210),n=t(6475);let d=(0,n.createServerReference)("78e206bcdee75d903a5232e143de98c9e658818b5a",n.callServer,void 0,n.findSourceMapURL,"fetchCustomerReviews");var o=t(74354),c=t(3018),l=t(93613),u=t(77882),p=t(612),f=t(13040),h=t(21153);function m(){let[e,r]=(0,i.useState)([]),[t,n]=(0,i.useState)("newest"),[m,b]=(0,i.useState)(!0),[x,v]=(0,i.useState)(null),[g,y]=(0,i.useState)({currentPage:1,totalPages:1,totalCount:0,perPage:8}),w=(0,i.useCallback)(async(e,t)=>{b(!0),v(null);try{let a=await d("current_user_id",e,g.perPage,t);a.success&&a.data?(r(a.data.items),y({currentPage:a.data.currentPage,totalPages:Math.ceil(a.data.totalCount/g.perPage),totalCount:a.data.totalCount,perPage:g.perPage})):(v(a.error||"Failed to fetch reviews"),r([]),y({currentPage:1,totalPages:1,totalCount:0,perPage:g.perPage}))}catch(e){v("Failed to load reviews. Please try again."),r([])}finally{b(!1)}},[g.perPage]),P=e=>{y(r=>({...r,currentPage:e}))},j=a=>{r(e=>e.filter(e=>e.id!==a)),1===e.length&&g.currentPage>1?P(g.currentPage-1):w(g.currentPage,t)};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex justify-end mb-6",children:(0,a.jsx)(p.A,{sortBy:t,onSortChange:e=>{n(e),y(e=>({...e,currentPage:1}))},className:"sm:w-auto"})}),x&&(0,a.jsxs)(c.Fc,{variant:"destructive",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)(c.XL,{children:"Error"}),(0,a.jsx)(c.TN,{children:x})]}),m?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Array.from({length:4}).map((e,r)=>(0,a.jsx)(f.A,{index:r},r))}):e.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.map(e=>(0,a.jsx)(h.A,{review:e,onDeleteSuccess:j},e.id))}),g.totalPages>1&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(o.dK,{children:(0,a.jsxs)(o.Iu,{children:[g.currentPage>1&&(0,a.jsx)(o.cU,{children:(0,a.jsx)(u.P.div,{whileHover:{x:-2},whileTap:{scale:.95},children:(0,a.jsx)(o.Eb,{href:"#",onClick:e=>{e.preventDefault(),P(g.currentPage-1)},className:"border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200"})})}),(()=>{let e=[],{currentPage:r,totalPages:t}=g;e.push((0,a.jsx)(o.cU,{children:(0,a.jsx)(o.n$,{href:"#",onClick:e=>{e.preventDefault(),P(1)},isActive:1===r,children:"1"})},"page-1")),r>3&&e.push((0,a.jsx)(o.cU,{children:(0,a.jsx)(o.M_,{})},"ellipsis-1"));for(let s=Math.max(2,r-1);s<=Math.min(t-1,r+1);s++)s<=1||s>=t||e.push((0,a.jsx)(o.cU,{children:(0,a.jsx)(o.n$,{href:"#",onClick:e=>{e.preventDefault(),P(s)},isActive:r===s,children:s})},`page-${s}`));return r<t-2&&e.push((0,a.jsx)(o.cU,{children:(0,a.jsx)(o.M_,{})},"ellipsis-2")),t>1&&e.push((0,a.jsx)(o.cU,{children:(0,a.jsx)(o.n$,{href:"#",onClick:e=>{e.preventDefault(),P(t)},isActive:r===t,children:t})},`page-${t}`)),e})().map((e,r)=>(0,a.jsx)(u.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3,delay:.1+.05*r},children:e},`pagination-${r}`)),g.currentPage<g.totalPages&&(0,a.jsx)(o.cU,{children:(0,a.jsx)(u.P.div,{whileHover:{x:2},whileTap:{scale:.95},children:(0,a.jsx)(o.WA,{href:"#",onClick:e=>{e.preventDefault(),P(g.currentPage+1)},className:"border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200"})})})]})})})]}):(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("div",{className:"w-16 h-16 mx-auto bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center",children:(0,a.jsx)(s.A,{className:"w-6 h-6 text-amber-600 dark:text-amber-400"})})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-neutral-800 dark:text-neutral-100 mb-2",children:"No reviews found"}),(0,a.jsx)("p",{className:"text-neutral-500 dark:text-neutral-400 mb-6",children:"You haven't written any reviews yet."})]})})]})}var b=t(96241);function x({reviewsCount:e}){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-xl bg-muted hidden sm:block",children:(0,a.jsx)(s.A,{className:"w-6 h-6 text-amber-600 dark:text-amber-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Your Reviews"}),(0,a.jsxs)("p",{className:"text-muted-foreground mt-1",children:[(0,b.gY)(e)," ",1===e?"review":"reviews"," you've written for businesses"]})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(m,{})})]})}},44992:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"40397ca1727b354672455d32692e626008a169d968":()=>a.WO,"40930e22958ec27cc9c1459e0e63b2994bcf186979":()=>a.Xd,"40ab7a7f21cb4460091c8fb4edccd341d3b9116d41":()=>a.G_,"40e6b7c827369932950dcd3c394b2c641f20ee0597":()=>a.Hh,"40fbda5e049b7decd80deacff63f96dfec9a104b5a":()=>a.PD,"608517e8bbf255550985eb946a3ce32616cdf4cb5a":()=>a.kJ});var a=t(54694)},54694:(e,r,t)=>{"use strict";t.d(r,{G_:()=>c,Hh:()=>o,PD:()=>d,WO:()=>l,Xd:()=>p,kJ:()=>u});var a=t(67218);t(79130);var s=t(32032),i=t(24107),n=t(39916);async function d(e){let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.from("customer_profiles").select("pincode, state, city, locality, address").eq("id",e).single();if(a)return console.error("Error fetching customer profile for address validation:",a),{isValid:!1,message:"Unable to verify your address information. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your address information"};let s={pincode:t?.pincode,state:t?.state,city:t?.city,locality:t?.locality,address:t?.address};if(!(0,i.Gs)(s)){let e=(0,i.SJ)(s),r=(0,i.zp)(e),t=`/dashboard/customer/profile?message=${encodeURIComponent(r)}`;return{isValid:!1,missingFields:e,message:r,redirectUrl:t}}return{isValid:!0}}catch(e){return console.error("Unexpected error during address validation:",e),{isValid:!1,message:"An error occurred while validating your address. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your address information"}}}async function o(e){let r=await d(e);!r.isValid&&r.redirectUrl&&(0,n.redirect)(r.redirectUrl)}async function c(e){let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.from("customer_profiles").select("name").eq("id",e).single();if(a)return console.error("Error fetching customer profile for name validation:",a),{isValid:!1,message:"Unable to verify your profile information. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your profile information"};if(!(t?.name&&""!==t.name.trim())){let e="Please complete your name in your profile to access the dashboard.",r=`/dashboard/customer/profile?message=${encodeURIComponent(e)}`;return{isValid:!1,message:e,redirectUrl:r}}return{isValid:!0}}catch(e){return console.error("Unexpected error during name validation:",e),{isValid:!1,message:"An error occurred while validating your profile. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your profile information"}}}async function l(e){let r=await c(e);!r.isValid&&r.redirectUrl&&(0,n.redirect)(r.redirectUrl)}async function u(e,r=!1){await l(e),r||await o(e)}async function p(e){let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.from("customer_profiles").select("pincode, state, city, locality, address").eq("id",e).single();if(a)return console.error("Error fetching customer address data:",a),{error:"Failed to fetch address data"};return{data:{pincode:t?.pincode,state:t?.state,city:t?.city,locality:t?.locality,address:t?.address}}}catch(e){return console.error("Unexpected error fetching address data:",e),{error:"An unexpected error occurred"}}}(0,t(17478).D)([d,o,c,l,u,p]),(0,a.A)(d,"40fbda5e049b7decd80deacff63f96dfec9a104b5a",null),(0,a.A)(o,"40e6b7c827369932950dcd3c394b2c641f20ee0597",null),(0,a.A)(c,"40ab7a7f21cb4460091c8fb4edccd341d3b9116d41",null),(0,a.A)(l,"40397ca1727b354672455d32692e626008a169d968",null),(0,a.A)(u,"608517e8bbf255550985eb946a3ce32616cdf4cb5a",null),(0,a.A)(p,"40930e22958ec27cc9c1459e0e63b2994bcf186979",null)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74528:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\customer\\\\reviews\\\\components\\\\ReviewsPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86617:(e,r,t)=>{Promise.resolve().then(t.bind(t,39599))},91645:e=>{"use strict";e.exports=require("net")},93379:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>o});var a=t(37413),s=t(32032),i=t(39916),n=t(74528),d=t(54694);let o={title:"My Reviews - Dukancard",robots:"noindex, nofollow"};async function c(){let e=await (0,s.createClient)(),{data:{user:r},error:t}=await e.auth.getUser();(t||!r)&&(0,i.redirect)("/login?message=Please log in to view your reviews."),await (0,d.kJ)(r.id);try{let{count:t}=await e.from("ratings_reviews").select("*",{count:"exact",head:!0}).eq("user_id",r.id);return(0,a.jsx)(n.default,{reviewsCount:t||0})}catch(e){return(0,a.jsx)(n.default,{reviewsCount:0})}}},94735:e=>{"use strict";e.exports=require("events")},96939:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>a.B,"4034994114ec4af82b8beb7b7ae0ca9d7cc384e91d":()=>d.J3,"406d3eedcd0777bc7a4d097a7c3736c79aa39001bd":()=>d.WK,"406e2a2a96626c0d97e44e258eff56ae8b5143ec6b":()=>d.e8,"408a3af28cdbf5a633d1470e3aa9845d5dfdf000ea":()=>d.Q$,"40b79faef168e2af792fe9df79d32b29f3f0fe259d":()=>d.Wr,"40bb17f02c4644681936fdd90a24f199b1d28e2381":()=>d.PU,"70696c60ec3cbc3cc67d74b3aabf05284aceb2c9d3":()=>d.h6,"78e206bcdee75d903a5232e143de98c9e658818b5a":()=>n});var a=t(64275),s=t(91199);t(42087);var i=t(74866);async function n(e,r=1,t=10,a="newest"){try{let s=await i.x4.fetchReviews(e,r,t,a);return{success:!0,data:s}}catch(e){return console.error("Error in fetchCustomerReviews server action:",e),{success:!1,error:e.message}}}(0,t(33331).D)([n]),(0,s.A)(n,"78e206bcdee75d903a5232e143de98c9e658818b5a",null);var d=t(32293)},99641:(e,r,t)=>{Promise.resolve().then(t.bind(t,74528))}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,399,4212,8072,3037,3739,9538,5918,7817,1768,7378],()=>t(36719));module.exports=a})();