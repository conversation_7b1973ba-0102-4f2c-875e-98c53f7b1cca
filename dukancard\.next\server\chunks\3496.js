"use strict";exports.id=3496,exports.ids=[3496],exports.modules={3018:(e,t,s)=>{s.d(t,{Fc:()=>n,TN:()=>c,XL:()=>o});var r=s(60687);s(43210);var a=s(24224),i=s(96241);let l=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...s}){return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(l({variant:t}),e),...s})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-title",className:(0,i.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},9912:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(60687),a=s(96241);function i({children:e,className:t}){return(0,r.jsx)("div",{className:(0,a.cn)("min-h-screen bg-background",t),children:(0,r.jsx)("div",{className:"w-full py-6 lg:px-8",children:e})})}},14109:(e,t,s)=>{s.d(t,{_:()=>c});var r=s(34386);function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";return e&&t?(0,r.createBrowserClient)(e,t):(console.error("Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined."),(0,r.createBrowserClient)("",""))}var i=s(30468);let l={enterprise:5,pro:4,growth:3,basic:2,free:1};async function n(e){let t=a();try{let{data:s,error:r}=await t.from(i.CG.CUSTOMER_PROFILES).select("*").eq(i.cZ.ID,e).single();if(r)return console.error("Error fetching user profile:",r.message),{data:null,error:r.message};return{data:s,error:null}}catch(e){return console.error("Unexpected error fetching user profile:",e),{data:null,error:"An unexpected error occurred."}}}async function o(e,t){let s=a(),{filter:r="smart",page:o=1,limit:c=10,city_slug:d,state_slug:u,locality_slug:_,pincode:m}=e;try{let{data:{user:e}}=await s.auth.getUser(),a=s.from("unified_posts").select("*",{count:"exact"});switch(r){case"smart":if(e){let{data:t}=await s.from("subscriptions").select("business_profile_id").eq(i.cZ.USER_ID,e.id),r=t?.map(e=>e.business_profile_id)||[],[l,o]=await Promise.all([n(e.id),s.from(i.CG.BUSINESS_PROFILES).select(`${i.cZ.CITY_SLUG}, ${i.cZ.STATE_SLUG}, ${i.cZ.LOCALITY_SLUG}, ${i.cZ.PINCODE}`).eq(i.cZ.ID,e.id).single()]),c=l.data||o.data,d=[];r.length>0&&d.push(`and(post_source.eq.business,author_id.in.(${r.join(",")}))`),d.push(`and(post_source.eq.customer,author_id.eq.${e.id})`),d.push(`and(post_source.eq.business,author_id.eq.${e.id})`),c?.locality_slug&&d.push(`${i.cZ.LOCALITY_SLUG}.eq.${c.locality_slug}`),c?.pincode&&d.push(`${i.cZ.PINCODE}.eq.${c.pincode}`),c?.city_slug&&d.push(`${i.cZ.CITY_SLUG}.eq.${c.city_slug}`),d.length>0&&(a=a.or(d.join(",")))}break;case"subscribed":if(e){let{data:t}=await s.from("subscriptions").select("business_profile_id").eq("user_id",e.id),r=t?.map(e=>e.business_profile_id)||[];if(!(r.length>0))return{success:!0,message:"No subscribed businesses found",data:{items:[],totalCount:0,hasMore:!1,hasJustCreatedPost:!1}};a=a.eq("post_source","business").in("author_id",r)}break;case"locality":if(_)a=a.eq("locality_slug",_);else if(e){let[t,r]=await Promise.all([s.from(i.CG.CUSTOMER_PROFILES).select(i.cZ.LOCALITY_SLUG).eq(i.cZ.ID,e.id).single(),s.from(i.CG.BUSINESS_PROFILES).select(i.cZ.LOCALITY_SLUG).eq(i.cZ.ID,e.id).single()]),l=t.data?.locality_slug||r.data?.locality_slug;l&&(a=a.eq("locality_slug",l))}break;case"pincode":if(m)a=a.eq("pincode",m);else if(e){let[t,r]=await Promise.all([s.from(i.CG.CUSTOMER_PROFILES).select(i.cZ.PINCODE).eq(i.cZ.ID,e.id).single(),s.from(i.CG.BUSINESS_PROFILES).select(i.cZ.PINCODE).eq(i.cZ.ID,e.id).single()]),l=t.data?.pincode||r.data?.pincode;l&&(a=a.eq("pincode",l))}break;case"city":if(d)a=a.eq("city_slug",d);else if(e){let[t,r]=await Promise.all([s.from(i.CG.CUSTOMER_PROFILES).select(i.cZ.CITY_SLUG).eq(i.cZ.ID,e.id).single(),s.from(i.CG.BUSINESS_PROFILES).select(i.cZ.CITY_SLUG).eq(i.cZ.ID,e.id).single()]),l=t.data?.city_slug||r.data?.city_slug;l&&(a=a.eq("city_slug",l))}break;case"state":if(u)a=a.eq("state_slug",u);else if(e){let[t,r]=await Promise.all([s.from(i.CG.CUSTOMER_PROFILES).select(i.cZ.STATE_SLUG).eq(i.cZ.ID,e.id).single(),s.from(i.CG.BUSINESS_PROFILES).select(i.cZ.STATE_SLUG).eq(i.cZ.ID,e.id).single()]),l=t.data?.state_slug||r.data?.state_slug;l&&(a=a.eq("state_slug",l))}}let g=(o-1)*c,{data:f,error:S,count:p}=await a.order("created_at",{ascending:!1}).range(g,g+c-1);if(S)return console.error("Error fetching unified feed posts:",S),{success:!1,message:"Failed to fetch posts",error:S.message};let h=f?function(e,t={}){var s,r,a,i;let{enableDiversity:n=!0,maintainChronologicalFlow:o=!0}=t;if(0===e.length)return[];let c=e.filter(e=>"customer"===e.post_source),d=e.filter(e=>"business"===e.post_source),u=(r=(s=c,0===s.length?[]:s.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())),a=function(e){if(0===e.length)return[];let t=new Map;e.forEach(e=>{t.has(e.author_id)||t.set(e.author_id,[]),t.get(e.author_id).push(e)}),t.forEach((e,s)=>{t.set(s,e.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()))});let s=[],r=[];return t.forEach((e,t)=>{e.length>0&&(s.push(e[0]),e.length>1&&r.push(...e.slice(1)))}),[...s.sort((e,t)=>{let s=e.business_plan||"free",r=t.business_plan||"free",a=l[s]||1,i=l[r]||1;return a!==i?i-a:new Date(t.created_at).getTime()-new Date(e.created_at).getTime()}),...r.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())]}(d),i=o,0===r.length?a:0===a.length?r:i?[...r,...a].sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()):[...a,...r]);return n?function(e,t={}){let{maxConsecutiveFromSameAuthor:s=1}=t;if(e.length<=1)return e;let r=[],a=[...e],i=null,l=0;for(;a.length>0;){let e=-1;for(let t=0;t<a.length;t++)if(a[t].author_id!==i){e=t;break}if(-1===e&&l<s&&(e=0),-1===e){for(let t=0;t<a.length;t++)if(a[t].author_id!==i){e=t;break}-1===e&&(e=0)}let t=a.splice(e,1)[0];r.push(t),t.author_id===i?l++:(l=1,i=t.author_id)}return r}(u):u}(f,{enableDiversity:!0,maintainChronologicalFlow:!0}):[],I=p||0,b=h.length===c&&g+c<I;if(!t||!t.justCreatedPostId)return{success:!0,message:"Posts fetched successfully",data:{items:h,totalCount:I,hasMore:b,hasJustCreatedPost:!1}};let E=function(e,t){if(!t.justCreatedPostId)return{posts:e,hasJustCreatedPost:!1};let s=e.find(e=>e.id===t.justCreatedPostId);return s?{posts:[s,...e.filter(e=>e.id!==t.justCreatedPostId)],hasJustCreatedPost:!0,justCreatedPost:s}:{posts:e,hasJustCreatedPost:!1}}(h,t);return{success:!0,message:"Posts fetched successfully",data:{items:E.posts,totalCount:I,hasMore:b,hasJustCreatedPost:E.hasJustCreatedPost,justCreatedPost:E.justCreatedPost,creationState:t}}}catch(e){return console.error("Unexpected error in getUnifiedFeedPosts:",e),{success:!1,message:"An unexpected error occurred",error:e instanceof Error?e.message:"Unknown error"}}}async function c(e,t){return await o(e,t)}},17071:(e,t,s)=>{s.d(t,{A:()=>u});var r=s(60687),a=s(43210),i=s(41862),l=s(97992),n=s(63143);s(38398);var o=s(96241),c=s(24934),d=s(16189);function u({className:e}){let[t,s]=(0,a.useState)(null),[u,_]=(0,a.useState)(!0),[m,g]=(0,a.useState)(null),[f,S]=(0,a.useState)(null),p=(0,d.useRouter)();if(u)return(0,r.jsxs)("div",{className:(0,o.cn)("flex items-center gap-2 text-sm text-muted-foreground",e),children:[(0,r.jsx)(i.A,{className:"h-4 w-4 animate-spin"}),(0,r.jsx)("span",{children:"Loading location..."})]});if(m||!t)return(0,r.jsxs)("div",{className:(0,o.cn)("flex items-center gap-2 text-sm text-muted-foreground",e),children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Location not available"})]});let h=[t.locality,t.city,t.state,t.pincode].filter(Boolean).join(", ");return(0,r.jsxs)("div",{className:(0,o.cn)("flex items-center justify-between gap-2 text-sm text-muted-foreground bg-muted/50 px-3 py-2 rounded-lg border",e),children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 text-[var(--brand-gold)]"}),(0,r.jsxs)("span",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Posting from:"})," ",h]})]}),(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{"business"===f?p.push("/dashboard/business/profile"):"customer"===f&&p.push("/dashboard/customer/profile")},className:"h-6 w-6 p-0 text-muted-foreground hover:text-[var(--brand-gold)]",children:(0,r.jsx)(n.A,{className:"h-3 w-3"})})]})}},30468:(e,t,s)=>{s.d(t,{CG:()=>r,SC:()=>a,cZ:()=>i});let r={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},a={BUSINESS:"business",CUSTOMERS:"customers"},i={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},73066:(e,t,s)=>{s.d(t,{A:()=>f});var r=s(60687),a=s(77882),i=s(96241),l=s(56085),n=s(41312),o=s(97992),c=s(15574),d=s(79410),u=s(11437),_=s(25366);let m=[{value:"smart",label:"Smart Feed",icon:l.A},{value:"subscribed",label:"Following",icon:n.A},{value:"locality",label:"Locality",icon:o.A},{value:"pincode",label:"Pincode",icon:c.A},{value:"city",label:"City",icon:d.A},{value:"state",label:"State",icon:u.A},{value:"all",label:"All Posts",icon:_.A}];function g({activeFilter:e,onFilterChange:t,isLoading:s=!1}){let l=m.find(t=>t.value===e);return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("div",{className:"md:hidden mb-3 px-4",children:(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing: ",(0,r.jsx)("span",{className:"font-medium text-foreground",children:l?.label||"Smart Feed"})]})}),(0,r.jsx)("div",{className:"flex gap-2 overflow-x-auto scrollbar-hide pb-2 md:pb-0 md:justify-center",children:(0,r.jsx)("div",{className:"flex gap-2 md:flex-wrap md:justify-center",children:m.map(l=>{let n=l.icon,o=e===l.value;return(0,r.jsxs)(a.P.button,{onClick:()=>!s&&t(l.value),disabled:s,whileHover:{scale:1.02},whileTap:{scale:.98},className:(0,i.cn)("flex items-center gap-2 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap relative cursor-pointer","border border-neutral-200 dark:border-neutral-700","w-10 h-10 p-0 justify-center md:px-4 md:py-2.5 md:w-auto md:h-auto",o?"bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)] shadow-md":"bg-white dark:bg-black text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-900 hover:border-neutral-300 dark:hover:border-neutral-600",s&&"opacity-50 cursor-not-allowed"),children:[(0,r.jsx)(n,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden md:inline",children:l.label}),o&&(0,r.jsx)(a.P.div,{layoutId:"activeFilter",className:"absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10",initial:!1,transition:{type:"spring",stiffness:300,damping:30}})]},l.value)})})})]})}function f({activeFilter:e,onFilterChange:t,isLoading:s=!1}){return(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-foreground",children:"Your Feed"}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Choose your feed preference"})]}),(0,r.jsx)(g,{activeFilter:e,onFilterChange:t,isLoading:s})]})}}};