"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1994],{38697:(e,s,a)=>{a.d(s,{default:()=>o});var r=a(95155),t=a(12115),l=a(47924),n=a(54416),i=a(89852),d=a(97168),c=a(53999);function o(e){let{onSearch:s,initialSearchTerm:a="",className:o,placeholder:u="Search by name..."}=e,[m,x]=(0,t.useState)(a);return(0,t.useEffect)(()=>{x(a)},[a]),(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),s(m.trim())},className:(0,c.cn)("relative w-full",o),children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-neutral-500 dark:text-neutral-400"}),(0,r.jsx)(i.p,{type:"text",placeholder:u,value:m,onChange:e=>{x(e.target.value)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),s(m.trim()))},className:"pl-10 pr-10 h-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 focus:ring-2 focus:ring-rose-500 dark:focus:ring-rose-600"}),m&&(0,r.jsx)(d.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>{x(""),s("")},className:"absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300",children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})]})})}},41994:(e,s,a)=>{a.d(s,{Ku:()=>r.default,_n:()=>l.default,bJ:()=>t.LikeListSkeleton,o8:()=>i.default,ur:()=>n.default});var r=a(55131),t=a(60482),l=a(38697),n=a(49401),i=a(52729)},49401:(e,s,a)=>{a.d(s,{default:()=>d});var r=a(95155),t=a(97168),l=a(42355),n=a(13052),i=a(53999);function d(e){let{currentPage:s,totalPages:a,onPageChange:d,className:c}=e;if(a<=1)return null;let o=(()=>{let e=[];if(a<=5)for(let s=1;s<=a;s++)e.push(s);else{let r=Math.max(1,s-2),t=Math.min(a,s+2);for(let s=r;s<=t;s++)e.push(s);r>1&&(r>2&&e.unshift("..."),e.unshift(1)),t<a&&(t<a-1&&e.push("..."),e.push(a))}return e})();return(0,r.jsxs)("div",{className:(0,i.cn)("flex items-center justify-center gap-2",c),children:[(0,r.jsx)(t.$,{variant:"outline",size:"sm",onClick:()=>d(s-1),disabled:s<=1,className:"h-8 w-8 p-0",children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}),o.map((e,a)=>(0,r.jsx)("div",{children:"..."===e?(0,r.jsx)("span",{className:"px-2 text-neutral-500 dark:text-neutral-400",children:"..."}):(0,r.jsx)(t.$,{variant:s===e?"default":"outline",size:"sm",onClick:()=>d(e),className:"h-8 w-8 p-0",children:e})},a)),(0,r.jsx)(t.$,{variant:"outline",size:"sm",onClick:()=>d(s+1),disabled:s>=a,className:"h-8 w-8 p-0",children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})]})}},51976:(e,s,a)=>{a.d(s,{A:()=>r});let r=(0,a(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},52729:(e,s,a)=>{a.d(s,{default:()=>u});var r=a(95155),t=a(12115),l=a(6874),n=a.n(l),i=a(97168),d=a(51976),c=a(5456),o=a(55131);function u(e){let{initialLikes:s,onUnlikeSuccess:a,showUnlike:l=!0,variant:u="default",emptyMessage:m="No likes found.",emptyDescription:x="Like profiles to see them here.",showDiscoverButton:h=!1,showVisitButton:f=!0,showAddress:p=!0,showRedirectIcon:b=!1}=e,[g,v]=(0,t.useState)(s);(0,t.useEffect)(()=>{v(s)},[s]);let j=e=>{v(s=>s.filter(s=>s.id!==e)),a&&a(e)};return 0===g.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-20 text-center",children:[(0,r.jsxs)("div",{className:"relative mb-8",children:[(0,r.jsx)("div",{className:"absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"}),(0,r.jsx)("div",{className:"relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg",children:(0,r.jsx)(d.A,{className:"w-10 h-10 text-primary"})})]}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3",children:m}),(0,r.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2",children:x}),(0,r.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-500 max-w-md leading-relaxed mb-8",children:"Discover amazing businesses and show your support by liking them."}),h&&(0,r.jsx)(i.$,{asChild:!0,variant:"outline",className:"gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",children:(0,r.jsxs)(n(),{href:"/businesses",target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),"Discover Businesses"]})})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map((e,s)=>{let a=e.profile;return a?(0,r.jsx)(o.default,{likeId:e.id,profile:a,onUnlikeSuccess:l?j:void 0,showUnlike:l,variant:u,showVisitButton:f,showAddress:p,showRedirectIcon:b},e.id):null})})}},55131:(e,s,a)=>{a.d(s,{default:()=>g});var r=a(95155),t=a(12115),l=a(6874),n=a.n(l),i=a(97168),d=a(69663),c=a(33786),o=a(23227),u=a(71007),m=a(51154),x=a(51976),h=a(60975),f=a(56671),p=a(28695),b=a(53999);function g(e){var s,a;let{likeId:l,profile:g,onUnlikeSuccess:v,showUnlike:j=!0,variant:k="default",showVisitButton:N=!0,showAddress:w=!0,showRedirectIcon:y=!1}=e,[A,C]=(0,t.useState)(!1),E=async()=>{if(j&&v){C(!0);try{let e=await (0,h.J)(g.id);e.success?(f.oR.success("".concat("business"===g.type?"Business":"Profile"," unliked successfully")),v(l)):f.oR.error(e.error||"Failed to unlike ".concat(g.type))}catch(e){console.error("Error unliking:",e),f.oR.error("An unexpected error occurred")}finally{C(!1)}}},S=g.slug?"/".concat(g.slug):"#",z=g.logo_url||g.avatar_url,_=g.name||"Unknown";return(0,r.jsxs)(p.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},whileHover:{y:-5,transition:{duration:.2}},className:(0,b.cn)("rounded-lg border p-0 overflow-hidden transition-all duration-300","bg-white dark:bg-black border-neutral-200 dark:border-neutral-800","shadow-sm hover:shadow-md","compact"===k&&"max-w-sm"),children:[(0,r.jsx)("div",{className:"absolute inset-0 pointer-events-none opacity-5 dark:opacity-10",style:{backgroundImage:'url("/decorative/card-texture.svg")',backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}}),(0,r.jsxs)("div",{className:"relative z-10 p-4",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3",children:["business"===g.type&&g.slug?(0,r.jsx)(n(),{href:S,target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,r.jsxs)(d.eu,{className:"h-12 w-12 border border-neutral-200 dark:border-neutral-800",children:[z?(0,r.jsx)(d.BK,{src:z,alt:_}):null,(0,r.jsx)(d.q5,{className:"bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-300",children:(null==(s=_[0])?void 0:s.toUpperCase())||"?"})]})}):(0,r.jsxs)(d.eu,{className:"h-12 w-12 border border-neutral-200 dark:border-neutral-800",children:[z?(0,r.jsx)(d.BK,{src:z,alt:_}):null,(0,r.jsx)(d.q5,{className:"bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-300",children:(null==(a=_[0])?void 0:a.toUpperCase())||"?"})]}),(0,r.jsx)("div",{className:"flex-1 min-w-0",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2",children:"business"===g.type&&g.slug?(0,r.jsxs)(n(),{href:S,target:"_blank",rel:"noopener noreferrer",className:"group flex items-center gap-1",children:[(0,r.jsx)("h3",{className:"font-medium text-neutral-800 dark:text-neutral-100 truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:_}),y&&(0,r.jsx)(c.A,{className:"w-3 h-3 text-neutral-400 group-hover:text-blue-500 transition-colors flex-shrink-0"})]}):(0,r.jsx)("h3",{className:"font-medium text-neutral-800 dark:text-neutral-100 truncate",children:_})}),(0,r.jsx)("div",{className:"flex items-center gap-1 mt-1",children:"business"===g.type?(0,r.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium",children:[(0,r.jsx)(o.A,{className:"h-3 w-3"}),"Business"]}):(0,r.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium",children:[(0,r.jsx)(u.A,{className:"h-3 w-3"}),"Customer"]})})]})})})]}),w&&(0,r.jsxs)("div",{className:"mt-2 ml-15",children:[" ",(0,r.jsxs)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center",children:[(0,r.jsx)("span",{className:"inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2"}),(e=>{if(!w)return null;let s=[e.locality,e.city,e.state].filter(Boolean);return s.length>0?s.join(", "):"Location not specified"})(g)]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[N&&"business"===g.type&&g.slug?(0,r.jsx)(i.$,{asChild:!0,variant:"outline",size:"sm",className:"text-xs h-8",children:(0,r.jsx)(n(),{href:S,target:"_blank",rel:"noopener noreferrer",children:"Visit Card"})}):(0,r.jsx)("div",{}),j&&v&&(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",className:"text-xs h-8 text-rose-500 hover:text-rose-600 hover:bg-rose-50 dark:hover:bg-rose-900/20",onClick:E,disabled:A,children:[A?(0,r.jsx)(m.A,{className:"h-3.5 w-3.5 mr-1.5 animate-spin"}):(0,r.jsx)(x.A,{className:"h-3.5 w-3.5 mr-1.5 fill-current"}),"Unlike"]})]})]})]})}},60482:(e,s,a)=>{a.d(s,{LikeListSkeleton:()=>d,default:()=>i});var r=a(95155),t=a(28695),l=a(27737),n=a(53999);function i(e){let{index:s=0,variant:a="default"}=e;return(0,r.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.05*s},className:(0,n.cn)("rounded-lg border p-0 overflow-hidden transition-all duration-300","bg-white dark:bg-black border-neutral-200 dark:border-neutral-800","shadow-sm","compact"===a&&"max-w-sm"),children:[(0,r.jsx)("div",{className:"absolute inset-0 pointer-events-none opacity-5 dark:opacity-10",style:{backgroundImage:'url("/decorative/card-texture.svg")',backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}}),(0,r.jsxs)("div",{className:"relative z-10 p-4",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(l.E,{className:"h-12 w-12 rounded-full"}),(0,r.jsx)("div",{className:"flex-1 min-w-0",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)(l.E,{className:"h-5 w-32 mb-2"}),(0,r.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,r.jsx)(l.E,{className:"h-3 w-3 rounded-full"}),(0,r.jsx)(l.E,{className:"h-3 w-16"})]}),(0,r.jsx)(l.E,{className:"h-3 w-24 mt-1"})]})})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,r.jsx)(l.E,{className:"h-8 w-20"}),(0,r.jsx)(l.E,{className:"h-8 w-16"})]})]})]})}function d(e){let{variant:s="default",count:a=6}=e;return(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:a}).map((e,a)=>(0,r.jsx)(i,{index:a,variant:s},a))})}},60975:(e,s,a)=>{a.d(s,{J:()=>t});var r=a(34477);let t=(0,r.createServerReference)("4034994114ec4af82b8beb7b7ae0ca9d7cc384e91d",r.callServer,void 0,r.findSourceMapURL,"unlikeBusiness")}}]);