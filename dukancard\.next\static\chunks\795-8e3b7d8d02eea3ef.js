"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365,795],{23451:(e,t,a)=>{a.d(t,{ki:()=>n,yQ:()=>d.y,i4:()=>l,eF:()=>o,Y6:()=>s,G3:()=>i,Uz:()=>c}),a(71553);var r=a(34477);let s=(0,r.createServerReference)("787e564da2fc6345f07ba27b32c3991bc3e2ebbaad",r.callServer,void 0,r.findSourceMapURL,"getProductServices"),n=(0,r.createServerReference)("4028d9eae4d852411a8378996608e072b40402706b",r.callServer,void 0,r.findSourceMapURL,"addProductService"),i=(0,r.createServerReference)("60628b2893a8cf908260d34968479634cae65b8c58",r.callServer,void 0,r.findSourceMapURL,"updateProductService"),l=(0,r.createServerReference)("4003407ec6313e0e5df396bd13f3f069cdc5666b21",r.callServer,void 0,r.findSourceMapURL,"deleteProductService");var d=a(99249);let c=(0,r.createServerReference)("604381af5c96732d294fa7a1b7bd35fc6aa1a9781b",r.callServer,void 0,r.findSourceMapURL,"updateProductVariant"),o=(0,r.createServerReference)("40f582f4b6156ae356636c0f4aa3c00d2946c8f64b",r.callServer,void 0,r.findSourceMapURL,"deleteProductVariant")},27737:(e,t,a)=>{a.d(t,{E:()=>n});var r=a(95155),s=a(53999);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",t),...a})}},53999:(e,t,a)=>{a.d(t,{M0:()=>o,Yq:()=>u,cn:()=>n,gV:()=>i,gY:()=>c,kY:()=>l,vA:()=>d,vv:()=>x});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function l(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),a=e.substring(e.length-2),r="*".repeat(e.length-4);return"".concat(t).concat(r).concat(a)}function d(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),a=t[0],r=t[1];if(a.length<=2||r.length<=2||!r.includes("."))return"Email Hidden";let s=a.substring(0,2)+"*".repeat(a.length-2),n=r.split("."),i=n[0],l=n.slice(1).join("."),d=i.substring(0,2)+"*".repeat(i.length-2);return"".concat(s,"@").concat(d,".").concat(l)}function c(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),a=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let r=a.length-1;r>=0;r--)if(t>=a[r].value)return(e/a[r].value).toFixed(1).replace(/\.0$/,"")+a[r].symbol;return e.toString()}function o(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let a={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(a.hour="2-digit",a.minute="2-digit",a.hour12=!0),e.toLocaleString("en-IN",a)}function x(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(a){return"".concat(t," ").concat(e.toFixed(2))}}},67133:(e,t,a)=>{a.d(t,{SQ:()=>d,_2:()=>c,lp:()=>o,mB:()=>u,rI:()=>i,ty:()=>l});var r=a(95155);a(12115);var s=a(76215),n=a(53999);function i(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...t})}function l(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...t})}function d(e){let{className:t,sideOffset:a=4,...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...i})})}function c(e){let{className:t,inset:a,variant:i="default",...l}=e;return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":i,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function o(e){let{className:t,inset:a,...i}=e;return(0,r.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...i})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},71553:(e,t,a)=>{a.d(t,{sX:()=>c});var r=a(55594);let s={id:r.Yj().uuid().optional(),business_id:r.Yj().uuid().optional(),product_type:r.k5(["physical","service"]).default("physical"),name:r.Yj().min(1,{message:"Product/Service name is required."}).max(100,{message:"Name cannot exceed 100 characters."}),description:r.Yj().max(500,{message:"Description cannot exceed 500 characters."}).optional().or(r.eu("")),base_price:r.au.number({required_error:"Base price is required.",invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}),discounted_price:r.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:r.zM().default(!0),image_url:r.Yj().url({message:"Invalid image URL format."}).optional().nullable(),images:r.YO(r.Yj()).optional().nullable(),featured_image_index:r.ai().int().min(0).optional().nullable(),slug:r.Yj().optional(),created_at:r.Yj().optional(),updated_at:r.Yj().optional()},n=r.Ik(s);n.omit({id:!0,business_id:!0,created_at:!0,updated_at:!0,image_url:!0}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),n.partial().refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]});let i=r.g1(r.Yj(),r.Yj()).refine(e=>Object.keys(e).length>0,{message:"At least one variant type-value pair is required."}).refine(e=>Object.keys(e).length<=5,{message:"Maximum of 5 variant types allowed per product."}),l={id:r.Yj().uuid().optional(),product_id:r.Yj().uuid(),variant_name:r.Yj().min(1,{message:"Variant name is required."}).max(100,{message:"Variant name cannot exceed 100 characters."}),variant_values:i,base_price:r.au.number({invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}).optional().nullable(),discounted_price:r.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:r.zM().default(!0),images:r.YO(r.Yj().url()).optional().nullable().default([]),featured_image_index:r.ai().int().min(0).optional().nullable(),created_at:r.p6().optional(),updated_at:r.p6().optional()},d=r.Ik(l),c=d.omit({id:!0,created_at:!0,updated_at:!0}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]});d.partial().refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),r.Ik({product_id:r.Yj().uuid(),variant_types_values:r.g1(r.Yj(),r.YO(r.Yj())).refine(e=>Object.keys(e).length>0,{message:"At least one variant type with values is required."}).refine(e=>Object.keys(e).length<=5,{message:"Maximum of 5 variant types allowed per product."})})},75401:(e,t,a)=>{a.d(t,{A:()=>L});var r=a(95155),s=a(12115),n=a(28695),i=a(60760),l=a(37108),d=a(84616),c=a(381),o=a(13052),u=a(5623),x=a(13717),m=a(78749),p=a(92657),h=a(62525),g=a(97168),b=a(88524),v=a(67133),f=a(88145),j=a(66766);let N={formatVariantValues:e=>Object.entries(e).map(e=>{let[t,a]=e;return"".concat(t,": ").concat(a)}).join(", "),getDisplayPrice:e=>{var t,a;return null!=(a=null!=(t=e.discounted_price)?t:e.base_price)?a:null},getProductDisplayPrice:e=>{var t,a;return null!=(a=null!=(t=e.discounted_price)?t:e.base_price)?a:null},hasDiscount:e=>!!(e.base_price&&e.discounted_price&&e.discounted_price<e.base_price),getDiscountPercentage:e=>N.hasDiscount(e)&&e.base_price&&e.discounted_price?Math.round((e.base_price-e.discounted_price)/e.base_price*100):null,getFeaturedImageUrl:e=>{if(!e.images||0===e.images.length)return null;let t=Math.min(e.featured_image_index||0,e.images.length-1);return e.images[t]||e.images[0]||null},getVariantSummary:e=>{if(0===e.length)return"No variants";let t=e.filter(e=>e.is_available).length,a=e.length;return t===a?"".concat(a," variant").concat(1===a?"":"s"):"".concat(t,"/").concat(a," variant").concat(1===a?"":"s"," available")}};var y=a(53999),_=a(55868),k=a(1243),w=a(54416),S=a(51154),A=a(5196),z=a(99840),C=a(95784),P=a(89852),D=a(82714),V=a(95139),F=a(76037),M=a(56671);function R(e){let{variants:t,selectedVariantIds:a,onSelectionChange:l,onBulkUpdate:d,disabled:o=!1,className:u}=e,[x,h]=(0,s.useState)(!1),[b,v]=(0,s.useState)(""),[j,N]=(0,s.useState)(""),[R,Y]=(0,s.useState)(!1),I=t.filter(e=>a.includes(e.id)),L=a.length>0,U=e=>{a.includes(e)?l(a.filter(t=>t!==e)):l([...a,e])},E=async()=>{if(b&&0!==a.length){Y(!0);try{let e;if("update_base_price"===b||"update_discounted_price"===b||"apply_discount"===b){let t=parseFloat(j);if(isNaN(t)||t<0)return void M.oR.error("Please enter a valid positive number");e=t}await d(a,b,e),v(""),N(""),h(!1),l([]),M.oR.success("Successfully updated ".concat(a.length," variant").concat(a.length>1?"s":""))}catch(e){console.error("Bulk operation failed:",e),M.oR.error("Failed to perform bulk operation. Please try again.")}finally{Y(!1)}}},B="update_base_price"===b||"update_discounted_price"===b||"apply_discount"===b;return(0,r.jsxs)("div",{className:(0,y.cn)("space-y-4",u),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(V.S,{checked:a.length===t.length&&t.length>0,onCheckedChange:()=>{a.length===t.length?l([]):l(t.map(e=>e.id))},disabled:o||0===t.length}),(0,r.jsx)("span",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:0===a.length?"Select variants for bulk operations":"".concat(a.length," of ").concat(t.length," selected")})]}),(0,r.jsxs)(z.lG,{open:x,onOpenChange:h,children:[(0,r.jsx)(z.zM,{asChild:!0,children:(0,r.jsxs)(g.$,{variant:"outline",size:"sm",disabled:!L||o,className:"gap-2",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),"Bulk Operations"]})}),(0,r.jsxs)(z.Cf,{className:"max-w-md",children:[(0,r.jsxs)(z.c7,{children:[(0,r.jsx)(z.L3,{children:"Bulk Variant Operations"}),(0,r.jsxs)(z.rr,{children:["Perform operations on ",a.length," selected variant",a.length>1?"s":""]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{className:"text-sm font-medium",children:"Selected Variants"}),(0,r.jsx)("div",{className:"max-h-32 overflow-y-auto space-y-1 p-2 border rounded-md bg-neutral-50 dark:bg-neutral-800",children:I.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsx)("span",{className:"truncate",children:e.variant_name}),(0,r.jsx)(f.E,{variant:e.is_available?"default":"secondary",className:"text-xs",children:e.is_available?"Available":"Unavailable"})]},e.id))})]}),(0,r.jsx)(F.w,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"operation",children:"Operation"}),(0,r.jsxs)(C.l6,{value:b,onValueChange:e=>v(e),children:[(0,r.jsx)(C.bq,{children:(0,r.jsx)(C.yv,{placeholder:"Choose an operation"})}),(0,r.jsxs)(C.gC,{children:[(0,r.jsx)(C.eb,{value:"enable",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),"Enable Variants"]})}),(0,r.jsx)(C.eb,{value:"disable",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),"Disable Variants"]})}),(0,r.jsx)(C.eb,{value:"update_base_price",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(_.A,{className:"h-4 w-4"}),"Update Base Price"]})}),(0,r.jsx)(C.eb,{value:"update_discounted_price",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(_.A,{className:"h-4 w-4"}),"Update Discounted Price"]})}),(0,r.jsx)(C.eb,{value:"apply_discount",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(_.A,{className:"h-4 w-4"}),"Apply Discount"]})})]})]})]}),b&&(0,r.jsx)("div",{className:"p-3 bg-blue-50 dark:bg-blue-950 rounded-md",children:(0,r.jsx)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:(()=>{switch(b){case"enable":return"Make selected variants available for purchase";case"disable":return"Make selected variants unavailable for purchase";case"update_base_price":return"Set a new base price for selected variants";case"update_discounted_price":return"Set a new discounted price for selected variants";case"apply_discount":return"Apply a percentage discount to base prices of selected variants";default:return"Choose an operation to perform on selected variants"}})()})}),(0,r.jsx)(i.N,{children:B&&(0,r.jsxs)(n.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"value",children:(()=>{switch(b){case"update_base_price":return"New Base Price (₹)";case"update_discounted_price":return"New Discounted Price (₹)";case"apply_discount":return"Discount Percentage (%)";default:return"Value"}})()}),(0,r.jsx)(P.p,{id:"value",type:"number",step:"apply_discount"===b?"1":"0.01",min:"0",max:"apply_discount"===b?"100":void 0,placeholder:(()=>{switch(b){case"update_base_price":return"Enter new base price";case"update_discounted_price":return"Enter new discounted price";case"apply_discount":return"Enter discount percentage (e.g., 10 for 10%)";default:return"Enter value"}})(),value:j,onChange:e=>N(e.target.value)})]})}),("disable"===b||"update_base_price"===b||"update_discounted_price"===b)&&(0,r.jsxs)("div",{className:"flex items-start gap-2 p-3 bg-amber-50 dark:bg-amber-950 rounded-md",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("p",{className:"text-sm text-amber-800 dark:text-amber-200",children:["This operation will affect ",a.length," ","variant",a.length>1?"s":"",". Make sure you want to proceed."]})]}),(0,r.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,r.jsxs)(g.$,{variant:"outline",onClick:()=>h(!1),disabled:R,className:"flex-1",children:[(0,r.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,r.jsx)(g.$,{onClick:E,disabled:!b||R||B&&!j,className:"flex-1 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:R?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(S.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"Apply"]})})]})]})]})]})]}),t.length>0&&(0,r.jsx)("div",{className:"space-y-2",children:t.map(e=>(0,r.jsxs)("div",{className:(0,y.cn)("flex items-center gap-3 p-3 border rounded-lg transition-colors",a.includes(e.id)?"bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800":"bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800"),children:[(0,r.jsx)(V.S,{checked:a.includes(e.id),onCheckedChange:()=>U(e.id),disabled:o}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium text-sm text-neutral-900 dark:text-neutral-100 truncate",children:e.variant_name}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsx)(f.E,{variant:e.is_available?"default":"secondary",className:"text-xs",children:e.is_available?"Available":"Unavailable"})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(e.variant_values||{}).map(e=>{let[t,a]=e;return(0,r.jsxs)(f.E,{variant:"outline",className:"text-xs",children:[t,": ",String(a)]},"".concat(t,"-").concat(a))})}),(0,r.jsx)("div",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e.discounted_price?(0,r.jsxs)("span",{className:"text-green-600 dark:text-green-400",children:["₹",e.discounted_price.toLocaleString()]}):e.base_price?(0,r.jsxs)("span",{children:["₹",e.base_price.toLocaleString()]}):(0,r.jsx)("span",{className:"text-neutral-400",children:"No price set"})})]})]})]},e.id))})]})}var Y=a(34477);let I=(0,Y.createServerReference)("70d838791e5769544191e6b0dbbeb36c46196105ea",Y.callServer,void 0,Y.findSourceMapURL,"bulkUpdateVariants");function L(e){let{variants:t,onAddVariant:a,onEditVariant:_,onDeleteVariant:k,onToggleVariantAvailability:w,className:S}=e,[A,z]=(0,s.useState)(new Set),[C,P]=(0,s.useState)([]),[D,V]=(0,s.useState)(!1),F=e=>{let t=new Set(A);t.has(e)?t.delete(e):t.add(e),z(t)},Y=e=>e?e.toLocaleString("en-IN",{style:"currency",currency:"INR"}):"-",L=e=>(0,r.jsx)(f.E,{variant:e?"default":"secondary",className:(0,y.cn)("text-xs font-medium",e?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"),children:e?"Available":"Unavailable"}),U=async(e,t,a)=>{try{let r=await I(e,t,a);r.success?(M.oR.success(r.message),P([])):(M.oR.error(r.message),r.errors&&r.errors.length>0&&r.errors.forEach(e=>M.oR.error(e)))}catch(e){console.error("Bulk update error:",e),M.oR.error("Failed to perform bulk operation. Please try again.")}};return 0===t.length?(0,r.jsx)("div",{className:(0,y.cn)("rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900",S),children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)(l.A,{className:"mx-auto h-12 w-12 text-neutral-400 dark:text-neutral-600 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2",children:"No variants yet"}),(0,r.jsx)("p",{className:"text-neutral-500 dark:text-neutral-400 mb-4",children:"Create variants to offer different options for this product."}),a&&(0,r.jsxs)(g.$,{type:"button",onClick:a,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Add First Variant"]})]})}):(0,r.jsxs)("div",{className:(0,y.cn)("rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 overflow-hidden",S),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-5 w-5 text-neutral-600 dark:text-neutral-400"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-100",children:"Product Variants"}),(0,r.jsx)(f.E,{variant:"secondary",className:"text-xs",children:t.length})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[t.length>1&&(0,r.jsxs)(g.$,{type:"button",onClick:()=>V(!D),variant:"outline",size:"sm",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),D?"Hide":"Bulk Operations"]}),a&&(0,r.jsxs)(g.$,{type:"button",onClick:a,size:"sm",className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Add Variant"]})]})]}),D&&t.length>1&&(0,r.jsx)("div",{className:"p-4 border-b border-neutral-200 dark:border-neutral-700 bg-white dark:bg-black",children:(0,r.jsx)(R,{variants:t,selectedVariantIds:C,onSelectionChange:P,onBulkUpdate:U})}),(0,r.jsxs)(b.XI,{children:[(0,r.jsx)(b.A0,{children:(0,r.jsxs)(b.Hj,{className:"hover:bg-neutral-50 dark:hover:bg-neutral-900",children:[(0,r.jsx)(b.nd,{className:"w-[40px]"}),(0,r.jsx)(b.nd,{className:"text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Variant"}),(0,r.jsx)(b.nd,{className:"text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Properties"}),(0,r.jsx)(b.nd,{className:"text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Base Price"}),(0,r.jsx)(b.nd,{className:"text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Sale Price"}),(0,r.jsx)(b.nd,{className:"text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Status"}),(0,r.jsx)(b.nd,{className:"text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:"Actions"})]})}),(0,r.jsx)(b.BF,{children:t.map(e=>{var t;let a=A.has(e.id),d=N.getDisplayPrice(e),c=N.hasDiscount(e),S=e.images&&e.images.length>0;return(0,r.jsxs)(s.Fragment,{children:[(0,r.jsxs)(n.P.tr,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"hover:bg-neutral-50 dark:hover:bg-neutral-900 transition-colors",children:[(0,r.jsx)(b.nA,{className:"p-2",children:(0,r.jsx)(g.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>F(e.id),className:"h-6 w-6 p-0",children:(0,r.jsx)(n.P.div,{animate:{rotate:90*!!a},transition:{duration:.2},children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})})}),(0,r.jsx)(b.nA,{className:"font-medium text-xs sm:text-sm text-neutral-800 dark:text-neutral-100",children:(0,r.jsx)("div",{className:"max-w-xs truncate",children:e.variant_name})}),(0,r.jsx)(b.nA,{className:"text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(e.variant_values||{}).map(e=>{let[t,a]=e;return(0,r.jsxs)(f.E,{variant:"outline",className:"text-xs",children:[String(t),": ",String(a)]},"".concat(t,"-").concat(a))})})}),(0,r.jsx)(b.nA,{className:"text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400",children:Y(e.base_price)}),(0,r.jsx)(b.nA,{className:"text-right text-xs sm:text-sm",children:c?(0,r.jsxs)("div",{className:"flex flex-col items-end",children:[(0,r.jsx)("span",{className:"text-green-600 dark:text-green-400 font-medium",children:Y(e.discounted_price)}),(0,r.jsx)("span",{className:"text-xs text-neutral-500 line-through",children:Y(e.base_price)})]}):(0,r.jsx)("span",{className:"text-neutral-600 dark:text-neutral-400",children:Y(d)})}),(0,r.jsx)(b.nA,{className:"text-center",children:L(e.is_available)}),(0,r.jsx)(b.nA,{className:"text-right",children:(0,r.jsxs)(v.rI,{children:[(0,r.jsx)(v.ty,{asChild:!0,children:(0,r.jsx)(g.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(v.SQ,{align:"end",children:[_&&(0,r.jsxs)(v._2,{onClick:()=>_(e),children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),w&&(0,r.jsx)(v._2,{onClick:()=>w(e.id,!e.is_available),children:e.is_available?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Make Unavailable"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Make Available"]})}),k&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.mB,{}),(0,r.jsxs)(v._2,{onClick:()=>k(e.id),className:"text-red-600 dark:text-red-400",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]})})]}),(0,r.jsx)(i.N,{children:a&&(0,r.jsx)(n.P.tr,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"bg-neutral-50 dark:bg-neutral-900",children:(0,r.jsx)(b.nA,{colSpan:7,className:"p-0",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-100",children:"Variant Details"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Variant Name"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e.variant_name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Properties"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:Object.entries(e.variant_values||{}).map(e=>{let[t,a]=e;return(0,r.jsxs)(f.E,{variant:"outline",className:"text-xs",children:[(0,r.jsxs)("span",{className:"font-medium",children:[String(t),":"]})," ",String(a)]},"".concat(t,"-").concat(a))})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Base Price"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:Y(e.base_price)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Sale Price"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:Y(e.discounted_price)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Availability"}),(0,r.jsx)("div",{className:"mt-1",children:L(e.is_available)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Created"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:new Date(e.created_at).toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-100",children:"Variant Images"}),S?(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-3",children:null==(t=e.images)?void 0:t.map((t,a)=>(0,r.jsxs)("div",{className:(0,y.cn)("relative aspect-square rounded-lg overflow-hidden border-2",a===e.featured_image_index?"border-[var(--brand-gold)]":"border-neutral-200 dark:border-neutral-700"),children:[(0,r.jsx)(j.default,{src:t,alt:"".concat(e.variant_name," - Image ").concat(a+1),fill:!0,className:"object-cover"}),a===e.featured_image_index&&(0,r.jsx)("div",{className:"absolute top-2 left-2",children:(0,r.jsx)(f.E,{className:"bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] text-xs",children:"Featured"})})]},a))}):(0,r.jsxs)("div",{className:"text-center py-8 border-2 border-dashed border-neutral-200 dark:border-neutral-700 rounded-lg",children:[(0,r.jsx)(l.A,{className:"mx-auto h-8 w-8 text-neutral-400 dark:text-neutral-600 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400",children:"No images for this variant"}),(0,r.jsx)("p",{className:"text-xs text-neutral-400 dark:text-neutral-500 mt-1",children:"Product images will be used as fallback"})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-3 mt-6 pt-4 border-t border-neutral-200 dark:border-neutral-700",children:[_&&(0,r.jsxs)(g.$,{type:"button",onClick:()=>_(e),variant:"outline",size:"sm",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Edit Variant"]}),w&&(0,r.jsx)(g.$,{onClick:()=>w(e.id,!e.is_available),variant:"outline",size:"sm",children:e.is_available?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Make Unavailable"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Make Available"]})}),k&&(0,r.jsxs)(g.$,{onClick:()=>k(e.id),variant:"outline",size:"sm",className:"text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Delete Variant"]})]})]})})},"expanded-".concat(e.id))})]},e.id)})})]})]})}},76037:(e,t,a)=>{a.d(t,{w:()=>i});var r=a(95155);a(12115);var s=a(87489),n=a(53999);function i(e){let{className:t,orientation:a="horizontal",decorative:i=!0,...l}=e;return(0,r.jsx)(s.b,{"data-slot":"separator-root",decorative:i,orientation:a,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...l})}},82714:(e,t,a)=>{a.d(t,{J:()=>i});var r=a(95155);a(12115);var s=a(40968),n=a(53999);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},88145:(e,t,a)=>{a.d(t,{E:()=>d});var r=a(95155);a(12115);var s=a(99708),n=a(74466),i=a(53999);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,asChild:n=!1,...d}=e,c=n?s.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:a}),t),...d})}},88524:(e,t,a)=>{a.d(t,{A0:()=>i,BF:()=>l,Hj:()=>d,XI:()=>n,nA:()=>o,nd:()=>c});var r=a(95155);a(12115);var s=a(53999);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...a})})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},89852:(e,t,a)=>{a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(53999);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},95139:(e,t,a)=>{a.d(t,{S:()=>l});var r=a(95155);a(12115);var s=a(76981),n=a(5196),i=a(53999);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(n.A,{className:"size-3.5"})})})}},95784:(e,t,a)=>{a.d(t,{TR:()=>p,bq:()=>x,eb:()=>h,gC:()=>m,l6:()=>c,s3:()=>o,yv:()=>u});var r=a(95155);a(12115);var s=a(43433),n=a(66474),i=a(5196),l=a(47863),d=a(53999);function c(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function o(e){let{...t}=e;return(0,r.jsx)(s.YJ,{"data-slot":"select-group",...t})}function u(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function x(e){let{className:t,size:a="default",children:i,...l}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[i,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,r.jsx)(g,{}),(0,r.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(b,{})]})})}function p(e){let{className:t,...a}=e;return(0,r.jsx)(s.JU,{"data-slot":"select-label",className:(0,d.cn)("text-muted-foreground px-2 py-1.5 text-xs",t),...a})}function h(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.A,{className:"size-4"})})}function b(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}},97168:(e,t,a)=>{a.d(t,{$:()=>d,r:()=>l});var r=a(95155);a(12115);var s=a(99708),n=a(74466),i=a(53999);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...c}=e,o=d?s.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:t})),...c})}},99249:(e,t,a)=>{a.d(t,{y:()=>s});var r=a(34477);let s=(0,r.createServerReference)("40e0b77a8dc6165a7703d56966ab44b8bb7215bc26",r.callServer,void 0,r.findSourceMapURL,"addProductVariant")},99840:(e,t,a)=>{a.d(t,{Cf:()=>x,Es:()=>p,HM:()=>o,L3:()=>h,c7:()=>m,lG:()=>l,rr:()=>g,zM:()=>d});var r=a(95155);a(12115);var s=a(45821),n=a(54416),i=a(53999);function l(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function o(e){let{...t}=e;return(0,r.jsx)(s.bm,{"data-slot":"dialog-close",...t})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function x(e){let{className:t,children:a,hideClose:l=!1,...d}=e;return(0,r.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...d,children:[a,!l&&(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function p(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}}}]);