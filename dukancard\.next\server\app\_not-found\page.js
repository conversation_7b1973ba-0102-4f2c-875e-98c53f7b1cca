(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35069:(e,t,r)=>{Promise.resolve().then(r.bind(r,97102)),Promise.resolve().then(r.bind(r,88452)),Promise.resolve().then(r.bind(r,80363)),Promise.resolve().then(r.bind(r,23392))},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,generateMetadata:()=>c});var n=r(37413),s=r(59896),o=r.n(s);r(82704);var a=r(23392),i=r(80363),d=r(97102),l=r(88452);async function c(){let e="http://localhost:3000",t="Dukancard - Your Digital Business Card Solution",r="Create a digital business card with Dukancard to showcase your shop, services, or portfolio. Boost your online presence, connect with customers, and grow your business across India.",n=`${e}/opengraph-image.png`;return{metadataBase:new URL(e),title:{default:t,template:"%s - Dukancard"},description:r,keywords:"digital business card, online presence, small business India, shop digital card, freelancer portfolio, Tier 2 cities, Tier 3 cities, Dukancard, local business growth, QR code business card",robots:"index, follow",alternates:{canonical:e},openGraph:{title:t,description:r,url:e,siteName:"Dukancard",type:"website",locale:"en_IN",images:[{url:n,width:1200,height:630,alt:"Dukancard - Digital Business Card Platform"}]},twitter:{card:"summary_large_image",title:t,description:r,image:n}}}function u({children:e}){return(0,n.jsxs)("html",{lang:"en",className:o().variable,suppressHydrationWarning:!0,children:[(0,n.jsxs)("head",{children:[(0,n.jsx)(d.default,{}),(0,n.jsx)(l.default,{})]}),(0,n.jsx)("body",{className:"antialiased flex flex-col min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:(0,n.jsxs)(a.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[e,(0,n.jsx)(i.Toaster,{})]})})]})}},59102:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(60687),s=r(72600);function o(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.default,{id:"meta-pixel",strategy:"beforeInteractive",children:`
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window, document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');
          fbq('init', '700491699058296');
          fbq('track', 'PageView');
        `}),(0,n.jsx)("noscript",{children:(0,n.jsx)("img",{height:"1",width:"1",style:{display:"none"},src:"https://www.facebook.com/tr?id=700491699058296&ev=PageView&noscript=1",alt:"Meta Pixel tracking"})})]})}},62685:(e,t,r)=>{Promise.resolve().then(r.bind(r,70716)),Promise.resolve().then(r.bind(r,59102)),Promise.resolve().then(r.bind(r,94593)),Promise.resolve().then(r.bind(r,10218))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63665:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},70716:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(60687),s=r(72600);function o(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.default,{src:"https://www.googletagmanager.com/gtag/js?id=G-8FDFQL6BX3",strategy:"beforeInteractive"}),(0,n.jsx)(s.default,{id:"google-analytics",strategy:"beforeInteractive",children:`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-8FDFQL6BX3');
        `})]})}},73393:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},80363:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\ui\\sonner.tsx","Toaster")},81143:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var n=r(65239),s=r(48088),o=r(88170),a=r.n(o),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},82704:()=>{},88452:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\MetaPixel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx","default")},94593:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>a});var n=r(60687),s=r(10218),o=r(52581);let a=({...e})=>{let{theme:t="system"}=(0,s.D)();return(0,n.jsx)(o.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},97102:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,6724,2997],()=>r(81143));module.exports=n})();