(()=>{var e={};e.id=8717,e.ids=[5453,8717],e.modules={2331:(e,r,o)=>{"use strict";o.d(r,{HD:()=>n,Ih:()=>a,OB:()=>s,Xz:()=>c,ju:()=>i});var t=o(95453);async function s(e,r){try{let o=(0,t.bG)(),s=await fetch(`${t.ST}/accounts/${e}/webhooks`,{method:"POST",headers:o,body:JSON.stringify(r)}),n=await s.json();if(!s.ok)return console.error("[RAZORPAY] Error creating webhook:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY] Exception creating webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function n(e,r){try{let o=(0,t.bG)(),s="";if(r){let e=new URLSearchParams;void 0!==r.from&&e.append("from",r.from.toString()),void 0!==r.to&&e.append("to",r.to.toString()),void 0!==r.count&&e.append("count",r.count.toString()),void 0!==r.skip&&e.append("skip",r.skip.toString());let o=e.toString();o&&(s=`?${o}`)}let n=await fetch(`${t.ST}/accounts/${e}/webhooks${s}`,{method:"GET",headers:o}),a=await n.json();if(!n.ok)return console.error("[RAZORPAY] Error fetching webhooks:",a),{success:!1,error:a};return{success:!0,data:a.items}}catch(e){return console.error("[RAZORPAY] Exception fetching webhooks:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function a(e,r){try{let o=(0,t.bG)(),s=await fetch(`${t.ST}/accounts/${e}/webhooks/${r}`,{method:"GET",headers:o}),n=await s.json();if(!s.ok)return console.error("[RAZORPAY] Error fetching webhook:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY] Exception fetching webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function c(e,r,o){try{if(!o.url)return{success:!1,error:"URL is required for updating a webhook"};if(!o.events||!Array.isArray(o.events)||0===o.events.length)return{success:!1,error:"Events array is required for updating a webhook"};let s=(0,t.bG)(),n=await fetch(`${t.ST}/accounts/${e}/webhooks/${r}`,{method:"PATCH",headers:s,body:JSON.stringify(o)}),a=await n.json();if(!n.ok)return console.error("[RAZORPAY] Error updating webhook:",a),{success:!1,error:a};return{success:!0,data:a}}catch(e){return console.error("[RAZORPAY] Exception updating webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function i(e,r){try{let o=(0,t.bG)(),s=await fetch(`${t.ST}/accounts/${e}/webhooks/${r}`,{method:"DELETE",headers:o});if(!s.ok){let e;try{e=await s.json()}catch(r){e={error:s.statusText}}return console.error("[RAZORPAY] Error deleting webhook:",e),{success:!1,error:e}}return{success:!0}}catch(e){return console.error("[RAZORPAY] Exception deleting webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72725:(e,r,o)=>{"use strict";o.r(r),o.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var t={};o.r(t),o.d(t,{DELETE:()=>u});var s=o(96559),n=o(48088),a=o(37719),c=o(32190),i=o(2331);async function u(e){try{let r=new URL(e.url),o=r.searchParams.get("account_id"),t=r.searchParams.get("webhook_id");if(!o)return c.NextResponse.json({success:!1,message:"Missing account_id parameter"},{status:400});if(!t)return c.NextResponse.json({success:!1,message:"Missing webhook_id parameter"},{status:400});let s=await (0,i.ju)(o,t);if(!s.success)return c.NextResponse.json({success:!1,error:s.error},{status:400});return c.NextResponse.json({success:!0,message:"Webhook deleted successfully"},{status:200})}catch(e){return console.error("[RAZORPAY_WEBHOOK_DELETE] Error deleting webhook:",e),c.NextResponse.json({success:!1,message:"Error deleting webhook",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/webhooks/razorpay/delete/route",pathname:"/api/webhooks/razorpay/delete",filename:"route",bundlePath:"app/api/webhooks/razorpay/delete/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\delete\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:l}=p;function f(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},78335:()=>{},95453:(e,r,o)=>{"use strict";o.d(r,{ST:()=>n,bG:()=>c,t6:()=>i});var t=o(55511),s=o.n(t);let n="https://api.razorpay.com/v2",a=()=>{let e,r;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",r=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!r)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!r,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:r}},c=()=>{let{keyId:e,keySecret:r}=a(),o=Buffer.from(`${e}:${r}`).toString("base64");return{Authorization:`Basic ${o}`,"Content-Type":"application/json"}},i=(e,r,o)=>{try{let t=s().createHmac("sha256",o).update(e).digest("hex");return s().timingSafeEqual(Buffer.from(r),Buffer.from(t))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[4447,580],()=>o(72725));module.exports=t})();