"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1131],{11131:(e,s,i)=>{i.r(s),i.d(s,{PaymentMethodLimitationsDialog:()=>r});var n=i(95155),l=i(97168),t=i(99840),c=i(85339);function r(e){let{open:s,onOpenChange:i,onContinue:r,paymentMethod:a}=e;return(0,n.jsx)(t.lG,{open:s,onOpenChange:i,children:(0,n.jsxs)(t.Cf,{className:"sm:max-w-md",children:[(0,n.jsxs)(t.c7,{children:[(0,n.jsxs)(t.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(c.A,{className:"h-5 w-5 text-yellow-500"}),"Payment Method Limitation"]}),(0,n.jsxs)(t.rr,{children:["Important: Due to Razorpay and RBI regulations, subscriptions with"," ",a," payment methods require a special process for changes."]})]}),(0,n.jsxs)("div",{className:"space-y-4 py-4 text-sm",children:[(0,n.jsx)("p",{children:'When you click "Continue", the following process will happen:'}),(0,n.jsxs)("ol",{className:"list-decimal pl-5 space-y-2",children:[(0,n.jsx)("li",{children:"We'll create a new subscription with your selected plan"}),(0,n.jsx)("li",{children:"You'll need to complete the payment authorization"}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Only after the new subscription is active"}),", we'll automatically cancel your previous subscription"]})]}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"This ensures you don't lose access to your current subscription if the new payment fails or is abandoned. Your current subscription will remain active until the new one is successfully set up."})]}),(0,n.jsxs)(t.Es,{children:[(0,n.jsx)(l.$,{variant:"outline",onClick:()=>i(!1),children:"Cancel"}),(0,n.jsx)(l.$,{onClick:r,children:"Continue"})]})]})})}},85339:(e,s,i)=>{i.d(s,{A:()=>n});let n=(0,i(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}}]);