"use strict";(()=>{var e={};e.id=4525,e.ids=[4525],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,r,s)=>{s.d(r,{CG:()=>t,SC:()=>i,cZ:()=>o});let t={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},i={BUSINESS:"business",CUSTOMERS:"customers"},o={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},34631:e=>{e.exports=require("tls")},41065:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>E,routeModule:()=>_,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>I});var t={};s.r(t),s.d(t,{GET:()=>S});var i=s(96559),o=s(48088),a=s(37719),n=s(32190),u=s(32032),p=s(8033),c=s(30468);async function S(e,{params:r}){try{let{id:e}=await r,s=await (0,u.createClient)(),{data:{user:t},error:i}=await s.auth.getUser();if(i||!t)return n.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let{data:o,error:a}=await s.from(c.CG.PAYMENT_SUBSCRIPTIONS).select(`${c.cZ.ID}, ${c.cZ.BUSINESS_PROFILE_ID}, ${c.cZ.RAZORPAY_SUBSCRIPTION_ID}`).eq(c.cZ.RAZORPAY_SUBSCRIPTION_ID,e).maybeSingle();if(a)return console.error("Error fetching subscription:",a),n.NextResponse.json({success:!1,error:"Could not fetch subscription details"},{status:500});if(!o){let{data:e}=await s.from(c.CG.BUSINESS_PROFILES).select(c.cZ.ID).eq(c.cZ.ID,t.id).single();if(!e)return n.NextResponse.json({success:!1,error:"Unauthorized to access this subscription"},{status:403})}if(o){let{data:e,error:r}=await s.from(c.CG.BUSINESS_PROFILES).select(c.cZ.ID).eq(c.cZ.ID,o.business_profile_id).single();if(r||!e||e.id!==t.id)return n.NextResponse.json({success:!1,error:"Unauthorized to access this subscription"},{status:403})}let S=await (0,p.$)(e);if(!S.success)return console.error("Error fetching subscription payments:",S.error),n.NextResponse.json({success:!1,error:"Failed to fetch subscription payments"},{status:500});if(!S.data||!S.data.items)return console.error("Invalid response format from Razorpay:",S),n.NextResponse.json({success:!1,error:"Invalid response format from payment provider"},{status:500});let _=S.data.items.map(e=>({id:e.id,amount:e.amount/100,currency:e.currency,status:e.status,date:new Date(1e3*e.created_at).toISOString(),invoiceUrl:e.short_url||null}));return n.NextResponse.json({success:!0,data:_})}catch(e){return console.error("Error in payment history API:",e),n.NextResponse.json({success:!1,error:"An unexpected error occurred"},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/subscription/[id]/payments/route",pathname:"/api/subscription/[id]/payments",filename:"route",bundlePath:"app/api/subscription/[id]/payments/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscription\\[id]\\payments\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:I,serverHooks:l}=_;function E(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:I})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,9398,4386,580,6298],()=>s(41065));module.exports=t})();