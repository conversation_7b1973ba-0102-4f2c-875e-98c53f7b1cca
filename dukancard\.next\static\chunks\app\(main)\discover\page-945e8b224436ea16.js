(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2247],{2914:(e,t,a)=>{Promise.resolve().then(a.bind(a,38617)),Promise.resolve().then(a.bind(a,53687))},29058:(e,t,a)=>{"use strict";a.d(t,{t:()=>s});var r=a(34477);let s=(0,r.createServerReference)("40d32ea8edc6596cf0013772648e4fa734c9679198",r.callServer,void 0,r.findSourceMapURL,"getPincodeDetails")},34424:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(95155),s=a(27737);function l(){return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4",children:Array.from({length:6}).map((e,t)=>(0,r.jsxs)("div",{className:"border border-neutral-200/80 dark:border-neutral-800/80 rounded-xl overflow-hidden bg-white dark:bg-neutral-900 transition-all duration-300 w-full",children:[(0,r.jsx)(s.E,{className:"h-48 w-full"}),(0,r.jsxs)("div",{className:"p-2 sm:p-3 md:p-4 space-y-2 sm:space-y-3",children:[(0,r.jsx)(s.E,{className:"h-4 sm:h-5 w-3/4"}),(0,r.jsx)(s.E,{className:"h-3 sm:h-4 w-1/2"}),(0,r.jsxs)("div",{className:"flex justify-between items-center pt-1 sm:pt-2",children:[(0,r.jsx)(s.E,{className:"h-5 sm:h-6 w-16 sm:w-20"}),(0,r.jsx)(s.E,{className:"h-6 sm:h-8 w-6 sm:w-8 rounded-full"})]})]})]},t))})})}},38617:(e,t,a)=>{"use strict";a.d(t,{default:()=>en});var r=a(95155),s=a(35695),l=a(58880),n=a(94234),i=a(12115),o=a(28695),d=a(60760),c=a(62177),u=a(90221),m=a(57840),x=a(89852),h=a(97168),p=a(4516),b=a(23227),g=a(54416),v=a(51154),f=a(47924),j=a(95784),y=a(44895),N=a(29058),w=a(63162),k=a(27737);function C(){return(0,r.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute z-10 mt-1 w-full bg-white dark:bg-neutral-900 rounded-md border border-neutral-200 dark:border-neutral-800 shadow-lg p-1",children:(0,r.jsx)("div",{className:"py-1 px-2",children:Array.from({length:5}).map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center py-1.5 px-2",children:[(0,r.jsx)(k.E,{className:"h-4 w-4 mr-2 rounded-full"}),(0,r.jsx)(k.E,{className:"h-4 w-full max-w-[180px]"})]},t))})})}var S=a(10081),A=a(5196),P=a(53999),_=a(60823),I=a(88945);function T(e){let{value:t,onValueChange:a,placeholder:s="Select category...",className:l}=e,[n,o]=(0,i.useState)(!1),d=(0,I.bW)(),c=d.find(e=>e.name===t),u=e=>{e===t?a(null):a(e),o(!1)};return(0,r.jsxs)(_.AM,{open:n,onOpenChange:o,children:[(0,r.jsx)(_.Wv,{asChild:!0,children:(0,r.jsxs)(h.$,{variant:"outline",role:"combobox","aria-expanded":n,className:(0,P.cn)("w-full md:w-[200px] h-12 min-h-[48px] justify-between bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 text-base font-normal",!t&&"text-neutral-500 dark:text-neutral-400",l),children:[(0,r.jsx)("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.icon,{className:"h-4 w-4 text-[var(--brand-gold)] flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:c.name})]}):(0,r.jsx)("span",{className:"truncate",children:s})}),(0,r.jsx)("div",{className:"flex items-center gap-1 flex-shrink-0",children:(0,r.jsx)(S.A,{className:"h-4 w-4 text-neutral-400"})})]})}),(0,r.jsx)(_.hl,{className:"w-[300px] p-0",align:"start",children:(0,r.jsxs)(y.uB,{children:[(0,r.jsx)(y.G7,{placeholder:"Search categories..."}),(0,r.jsxs)(y.oI,{children:[(0,r.jsx)(y.xL,{children:"No category found."}),(0,r.jsx)(y.L$,{children:d.map(e=>(0,r.jsxs)(y.h_,{value:e.name,onSelect:()=>u(e.name),className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)(e.icon,{className:"h-4 w-4 text-[var(--brand-gold)]"}),(0,r.jsx)("span",{className:"flex-1",children:e.name}),(0,r.jsx)(A.A,{className:(0,P.cn)("h-4 w-4",t===e.name?"opacity-100":"opacity-0")})]},e.name))})]})]})})]})}function E(e){let{initialValues:t}=e,{performSearch:a,isSearching:s,selectedCategory:n,handleCategoryChange:k}=(0,l.u)(),[S,A]=(0,i.useState)(!1),[P,_]=(0,i.useState)(!1),[I,E]=(0,i.useState)(t.city?"city":"pincode"),[z,L]=(0,i.useState)([]),[F,D]=(0,i.useState)([]),[B,R]=(0,i.useState)(!1),V=(0,c.mN)({resolver:(0,u.u)(m.x4),defaultValues:{businessName:"",pincode:"pincode"===I&&t.pincode||"",city:"city"===I&&t.city||"",locality:"pincode"===I&&t.locality||"_any"},mode:"onChange"}),O=e=>{console.log("Form submitted with data:",e,"Search type:",I),_(!1),R(!1);let t={businessName:e.businessName,category:n};"pincode"===I?(t.pincode=e.pincode,t.locality=e.locality,t.city=null,console.log("Searching by pincode:",t.pincode)):(t.city=e.city,t.pincode=null,t.locality=null,console.log("Searching by city:",t.city)),console.log("Final search data:",t),setTimeout(()=>{a(t)},100)},M=()=>{let e="pincode"===I?"city":"pincode";E(e),V.reset({businessName:"",pincode:"pincode"===e?V.getValues("pincode"):"",city:"city"===e?V.getValues("city"):"",locality:"pincode"===e?"_any":""}),L([]),D([]),R(!1),console.log("Toggled search type to:",e,"Form values:",V.getValues())},Y=(0,i.useCallback)(async e=>{if(e&&6===e.length){console.log("Fetching localities for pincode:",e),A(!0);try{let r=await (0,w.iU)(e);if(r.localities){let e=r.localities.map(e=>e.replace(" B.O","").trim()).filter(Boolean);console.log("Found localities:",e),L(e),1===e.length?V.setValue("locality",e[0]):t.locality&&e.includes(t.locality)&&V.setValue("locality",t.locality)}else{var a;let r=await (0,N.t)(e);if(null==(a=r.data)?void 0:a.localities){let e=r.data.localities.map(e=>e.replace(" B.O","").trim()).filter(Boolean);console.log("Found localities (server):",e),L(e),1===e.length?V.setValue("locality",e[0]):t.locality&&e.includes(t.locality)&&V.setValue("locality",t.locality)}else L([]),V.setValue("locality","_any")}}catch(e){console.error("Error fetching localities:",e),L([])}finally{A(!1)}}},[V,t.locality,A,L]),$=V.watch("pincode");(0,i.useEffect)(()=>{$&&6===$.length&&"pincode"===I?Y($):L([])},[I,$,Y]),(0,i.useEffect)(()=>{t.pincode&&6===t.pincode.length&&"pincode"===I&&(console.log("Initial load with pincode:",t.pincode),Y(t.pincode))},[t.pincode,I,Y]);let U=V.watch("city");(0,i.useEffect)(()=>{if(console.log("City input changed:",U,"Search type:",I),U&&U.length>=2&&"city"===I){_(!0),console.log("Loading cities...");let e=async()=>{try{console.log("Fetching city suggestions for:",U);let e=await (0,w.CH)(U);console.log("City suggestions result:",e),e.cities?(console.log("Setting city suggestions:",e.cities),D(e.cities),R(!0)):(console.log("No city suggestions found"),D([]),R(!1))}catch(e){console.error("Error fetching city suggestions:",e),D([]),R(!1)}finally{_(!1)}},t=setTimeout(()=>{e()},300);return()=>clearTimeout(t)}D([]),R(!1),_(!1)},[I,U]);let H=e=>{V.setValue("city",e),R(!1)};return(0,r.jsx)("div",{className:"w-full py-6 mt-20",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"text-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-neutral-800 dark:text-neutral-100 mb-2",children:"Discover Businesses and Products/Services Across India"}),(0,r.jsx)("p",{className:"text-sm md:text-base text-neutral-600 dark:text-neutral-400",children:"Search through our extensive database of local businesses and products/services"})]}),(0,r.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"max-w-5xl mx-auto",children:(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),console.log("Form submitted directly");let t=V.getValues(),a={businessName:t.businessName,category:n};"pincode"===I?(a.pincode=t.pincode,a.locality=t.locality,a.city=null):(a.city=t.city,a.pincode=null,a.locality=null),O(a)},children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-3 items-center w-full",children:[(0,r.jsx)("div",{className:"w-full md:w-auto",children:(0,r.jsxs)(j.l6,{value:I,onValueChange:e=>{E(e),M()},children:[(0,r.jsx)(j.bq,{className:"w-full md:w-[140px] h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal",children:(0,r.jsx)(j.yv,{placeholder:"Search by",className:"text-base leading-normal"})}),(0,r.jsxs)(j.gC,{children:[(0,r.jsx)(j.eb,{value:"pincode",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 text-[var(--brand-gold)]"}),(0,r.jsx)("span",{children:"Pincode"})]})}),(0,r.jsx)(j.eb,{value:"city",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4 text-[var(--brand-gold)]"}),(0,r.jsx)("span",{children:"City"})]})})]})]})}),(0,r.jsxs)("div",{className:"w-full md:w-auto flex items-center gap-2",children:[(0,r.jsx)(T,{value:n,onValueChange:k,placeholder:"All categories"}),n&&(0,r.jsx)(h.$,{type:"button",variant:"outline",size:"sm",onClick:()=>k(null),className:"h-12 px-3 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200",title:"Clear category filter",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex-1 w-full flex flex-col md:flex-row gap-3 items-center",children:["pincode"===I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"relative w-full flex-1",children:(0,r.jsx)(c.xI,{name:"pincode",control:V.control,render:e=>{let{field:t}=e;return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400"}),(0,r.jsx)(x.p,{...t,placeholder:"Enter 6-digit pincode",className:"pl-12 h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md text-base",maxLength:6,value:t.value||"",type:"tel",inputMode:"numeric",onKeyDown:e=>{/^\d$/.test(e.key)||"Backspace"===e.key||"Delete"===e.key||"Tab"===e.key||"Enter"===e.key||e.key.includes("Arrow")||e.preventDefault()}}),S&&(0,r.jsx)(v.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-neutral-400"})]})}})}),(0,r.jsx)("div",{className:"w-full md:w-[200px]",children:(0,r.jsx)(c.xI,{name:"locality",control:V.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(j.l6,{value:t.value||"_any",onValueChange:t.onChange,disabled:0===z.length&&!S,children:[(0,r.jsx)(j.bq,{className:"w-full h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal",children:(0,r.jsx)(j.yv,{placeholder:"Select locality",className:"text-base leading-normal"})}),(0,r.jsxs)(j.gC,{children:[(0,r.jsx)(j.eb,{value:"_any",children:"Any Locality"}),z.map(e=>(0,r.jsx)(j.eb,{value:e,children:e},e))]})]})}})})]}):(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"relative w-full flex-1",children:[(0,r.jsx)(c.xI,{name:"city",control:V.control,render:e=>{let{field:t}=e;return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(b.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400"}),(0,r.jsx)(x.p,{...t,placeholder:"Enter city name",className:"pl-12 h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md text-base",autoComplete:"off",value:t.value||"",onChange:e=>{t.onChange(e),console.log("City input changed directly:",e.target.value)}}),P&&(0,r.jsx)(v.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-neutral-400"})]})}}),(0,r.jsx)(d.N,{children:P&&V.watch("city")?(0,r.jsx)(C,{}):B&&F.length>0&&(0,r.jsx)(o.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute z-10 mt-1 w-full bg-white dark:bg-neutral-900 rounded-md border border-neutral-200 dark:border-neutral-800 shadow-lg",children:(0,r.jsx)(y.uB,{children:(0,r.jsx)(y.oI,{children:(0,r.jsx)(y.L$,{children:F.map(e=>(0,r.jsxs)(y.h_,{onSelect:()=>H(e),className:"cursor-pointer",children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4 text-neutral-400"}),(0,r.jsx)("span",{children:e})]},e))})})})})})]})}),(0,r.jsxs)(h.$,{type:"submit",disabled:s||("pincode"===I?!V.getValues("pincode"):!V.getValues("city")),className:"h-12 min-h-[48px] bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] w-full md:w-auto px-6 border border-[var(--brand-gold)] rounded-md flex items-center justify-center font-medium text-base",onClick:()=>{console.log("Search button clicked, isSearching:",s)},children:[s?(0,r.jsx)(o.P.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"mr-2",children:(0,r.jsx)(v.A,{className:"h-4 w-4"})}):(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Search"]})]})]})})})]})})}var z=a(76604),L=a(86894);function F(e){let{maxCategories:t=15}=e,{handleCategoryChange:a,selectedCategory:s}=(0,l.u)(),n=(0,i.useRef)(null),d=(0,z.W)(n,{once:!1,amount:.2}),c=(0,I.bW)(t),u=e=>{s===e?a(null):a(e)};return(0,r.jsxs)(o.P.div,{ref:n,initial:{opacity:0,y:20},animate:d?{opacity:1,y:0}:{},transition:{duration:.5,ease:"easeOut"},className:"w-full relative py-6 bg-neutral-50/50 dark:bg-neutral-900/20 border-y border-neutral-200/50 dark:border-neutral-800/50",children:[(0,r.jsx)("div",{className:"container mx-auto px-4 mb-4",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-2",children:[(0,r.jsxs)("div",{className:"mb-2 md:mb-0",children:[(0,r.jsx)(o.P.h2,{className:"text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-1",initial:{opacity:0,y:10},animate:d?{opacity:1,y:0}:{},transition:{duration:.5},children:"Popular Categories"}),(0,r.jsx)(o.P.p,{className:"text-sm text-neutral-600 dark:text-neutral-400",initial:{opacity:0,y:10},animate:d?{opacity:1,y:0}:{},transition:{duration:.5,delay:.1},children:"Explore businesses and products/services by category"})]}),(0,r.jsxs)(o.P.div,{initial:{opacity:0},animate:d?{opacity:1}:{},transition:{duration:.5,delay:.2},className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"hidden md:flex items-center gap-1.5",children:[(0,r.jsx)("div",{className:"w-8 h-1 rounded-full bg-neutral-300 dark:bg-neutral-700"}),(0,r.jsx)("div",{className:"w-3 h-1 rounded-full bg-neutral-200 dark:bg-neutral-800"}),(0,r.jsx)("div",{className:"w-2 h-1 rounded-full bg-neutral-200 dark:bg-neutral-800"})]}),(0,r.jsxs)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400 flex items-center gap-1.5",children:[(0,r.jsx)("span",{className:"hidden md:inline",children:"Drag to scroll"}),(0,r.jsx)("span",{className:"md:hidden",children:"Swipe to explore"}),(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"animate-pulse",children:[(0,r.jsx)("path",{d:"M5 12h14"}),(0,r.jsx)("path",{d:"m12 5 7 7-7 7"})]})]})]})]})}),(0,r.jsxs)(L.FN,{opts:{align:"start",loop:!0,dragFree:!0},className:"w-full",children:[(0,r.jsx)(L.Wk,{className:"-ml-2 md:-ml-4",children:c.map((e,t)=>(0,r.jsx)(L.A7,{className:"pl-2 md:pl-4 basis-1/3 sm:basis-1/4 md:basis-1/5 lg:basis-1/6 py-2",children:(0,r.jsx)(D,{name:e.name,icon:e.icon,index:t,isSelected:s===e.name,onClick:()=>u(e.name)})},t))}),(0,r.jsxs)("div",{className:"hidden sm:block",children:[(0,r.jsx)(L.Q8,{className:"left-2 bg-white/90 dark:bg-neutral-900/90 border-neutral-200 dark:border-neutral-800 shadow-md hover:bg-white dark:hover:bg-neutral-800"}),(0,r.jsx)(L.Oj,{className:"right-2 bg-white/90 dark:bg-neutral-900/90 border-neutral-200 dark:border-neutral-800 shadow-md hover:bg-white dark:hover:bg-neutral-800"})]})]})]})}function D(e){let{name:t,icon:a,index:s,isSelected:l,onClick:n}=e;return(0,r.jsx)(o.P.div,{className:"group cursor-pointer p-1.5",whileHover:{scale:1.02},whileTap:{scale:.97},initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*s},onClick:n,children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2 p-3 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md h-full ".concat(l?"bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 border-[var(--brand-gold)] border-2":"bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)]/30 dark:hover:border-[var(--brand-gold)]/30"),children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full blur-md opacity-30 group-hover:opacity-80 transition-opacity duration-300 scale-150"}),(0,r.jsx)("div",{className:"relative z-10 p-2.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full",children:(0,r.jsx)(a,{className:"h-5 w-5 text-[var(--brand-gold)]"})})]}),(0,r.jsx)("span",{className:"text-xs font-medium text-neutral-700 dark:text-neutral-300 text-center",children:t})]})})}var B=a(49026),R=a(85339);function V(){let{searchError:e,isSearching:t}=(0,l.u)();return(0,r.jsx)(d.N,{mode:"wait",children:!t&&e&&(0,r.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.5},className:"my-12",children:(0,r.jsxs)(B.Fc,{variant:"destructive",className:"max-w-lg mx-auto border-red-200 dark:border-red-900/50 backdrop-blur-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-red-50/50 dark:bg-red-950/20 pointer-events-none"}),(0,r.jsxs)("div",{className:"relative z-10 flex items-start",children:[(0,r.jsx)(o.P.div,{className:"mr-2",initial:{scale:1},animate:{scale:1.1},transition:{duration:.5,repeat:1/0,repeatType:"reverse"},children:(0,r.jsx)(R.A,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(B.XL,{className:"text-lg font-semibold mb-1",children:"Error Occurred"}),(0,r.jsx)(B.TN,{className:"text-red-700 dark:text-red-300",children:e})]})]})]})},"error-state")})}var O=a(37108),M=a(5937),Y=a(53904);function $(e){let{viewType:t,onViewChange:a,disabled:s=!1,hasFilters:l=!1}=e;return(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsxs)(o.P.div,{className:"relative bg-white/90 dark:bg-neutral-800/90 p-1 sm:p-1.5 rounded-xl border border-neutral-200/70 dark:border-neutral-700/70 flex gap-1 backdrop-blur-md overflow-hidden shadow-sm",initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5},whileHover:{boxShadow:"0 8px 30px rgba(0, 0, 0, 0.06)"},children:[(0,r.jsx)("div",{className:"absolute -top-6 -right-6 w-12 h-12 bg-[var(--brand-gold)]/10 blur-xl rounded-full"}),(0,r.jsx)("div",{className:"absolute -bottom-6 -left-6 w-12 h-12 bg-purple-500/10 blur-xl rounded-full"}),(0,r.jsx)(o.P.div,{whileHover:s?void 0:{scale:1.05},whileTap:s?void 0:{scale:.95},className:"relative z-10",children:(0,r.jsxs)(h.$,{variant:"ghost",onClick:()=>a("products"),disabled:s,className:"\n              relative px-3 sm:px-4 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300\n              ".concat("products"===t?"text-black dark:text-white font-medium":"text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300","\n              ").concat(s?"opacity-50 cursor-not-allowed":"cursor-pointer","\n            "),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(O.A,{className:"mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4"})}),(0,r.jsx)("span",{className:"text-xs sm:text-xs md:text-sm font-medium",children:"Products"})]}),"products"===t&&(0,r.jsx)(o.P.div,{layoutId:"activeViewIndicator",className:"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10 shadow-inner",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3}})]})}),(0,r.jsx)(o.P.div,{whileHover:s?void 0:{scale:1.05},whileTap:s?void 0:{scale:.95},className:"relative z-10",children:(0,r.jsxs)(h.$,{variant:"ghost",onClick:()=>a("cards"),disabled:s,className:"\n              relative px-3 sm:px-4 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300\n              ".concat("cards"===t?"text-black dark:text-white font-medium":"text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300","\n              ").concat(s?"opacity-50 cursor-not-allowed":"cursor-pointer","\n            "),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(M.A,{className:"mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4"})}),(0,r.jsx)("span",{className:"text-xs sm:text-xs md:text-sm font-medium",children:"Digital Cards"})]}),"cards"===t&&(0,r.jsx)(o.P.div,{layoutId:"activeViewIndicator",className:"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-xl -z-10",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3}})]})})]}),l&&(0,r.jsx)(o.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.2},whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsxs)(h.$,{onClick:()=>{window.location.href="/discover"},variant:"outline",size:"sm",className:"flex items-center gap-1 sm:gap-2 bg-white dark:bg-neutral-800 border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/30 hover:bg-[var(--brand-gold)]/10 dark:hover:bg-[var(--brand-gold)]/20 text-neutral-700 dark:text-neutral-300 text-xs sm:text-sm shadow-sm transition-all duration-300",children:[(0,r.jsx)(Y.A,{className:"h-3 w-3 sm:h-3.5 sm:w-3.5 text-[var(--brand-gold)]"}),(0,r.jsx)("span",{children:"Reset Filters"})]})})]})}var U=a(21492),H=a(66932),q=a(90497),J=a(60944);function W(e){let{businesses:t,isAuthenticated:a,onSortChange:s,currentSortBy:l,isLoading:n,onSearch:c,initialSearchTerm:u}=e,[m,p]=(0,i.useState)(u||""),[b,v]=(0,i.useState)(u||""),[y,N]=(0,i.useState)(!1),w=(0,i.useRef)(null),k=(0,i.useRef)(!0),C=(0,i.useRef)(m),S=(0,i.useRef)(!!u);(0,i.useEffect)(()=>{if(k.current&&u){k.current=!1,S.current=!0;return}C.current=m},[m,u]),(0,i.useEffect)(()=>{u&&(S.current=!0,k.current=!1)},[u]),(0,i.useEffect)(()=>{(t.length>0||!n)&&(k.current=!1)},[t,n]);let A=()=>{w.current&&(clearTimeout(w.current),w.current=null),p(""),v(""),setTimeout(()=>{c("")},100)};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(o.P.div,{className:"sticky top-[80px] z-30 container mx-auto px-4",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,r.jsxs)("div",{className:"relative overflow-hidden bg-white/80 dark:bg-black/80 p-5 rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80 transition-all duration-300 backdrop-blur-md",children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none opacity-70",children:[(0,r.jsx)("div",{className:"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-2xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10"}),(0,r.jsx)("div",{className:"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-2xl dark:from-purple-500/10 dark:to-blue-500/10"})]}),(0,r.jsxs)("div",{className:"relative z-10 flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-between",children:[(0,r.jsx)("div",{className:"relative w-full sm:w-auto sm:flex-1 max-w-full sm:max-w-md",children:(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),(""===m||m.length>=3)&&(w.current&&(clearTimeout(w.current),w.current=null),v(m),c(m))},children:(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--brand-gold)] transition-all duration-200"}),(0,r.jsx)(x.p,{placeholder:"Search businesses...",value:m,onChange:e=>{let t=e.target.value;if(p(t),w.current&&(clearTimeout(w.current),w.current=null),C.current.length>0&&""===t){v(""),setTimeout(()=>{c("")},100);return}(""===t||t.length>=3)&&(w.current=setTimeout(()=>{v(t),k.current?k.current=!1:c(t)},500))},className:"pl-10 h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm"}),m&&(0,r.jsx)("button",{type:"button",onClick:A,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors duration-200",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{type:"submit",className:"hidden",children:"Search"})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 sm:flex sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto",children:[(0,r.jsxs)(j.l6,{value:l,onValueChange:e=>s(e),disabled:n,children:[(0,r.jsx)(j.bq,{className:"w-full sm:w-[180px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm",children:(0,r.jsxs)("div",{className:"flex items-center overflow-hidden",children:[(0,r.jsx)(U.A,{className:"flex-shrink-0 mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]"}),(0,r.jsx)(j.yv,{placeholder:"Sort by",className:"text-xs sm:text-sm truncate"})]})}),(0,r.jsxs)(j.gC,{children:[(0,r.jsxs)(j.s3,{children:[(0,r.jsx)(j.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5",children:"Date"}),(0,r.jsx)(j.eb,{value:"created_desc",className:"relative pl-8",children:"Newest First"}),(0,r.jsx)(j.eb,{value:"created_asc",className:"relative pl-8",children:"Oldest First"})]}),(0,r.jsxs)(j.s3,{children:[(0,r.jsx)(j.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Name"}),(0,r.jsx)(j.eb,{value:"name_asc",className:"relative pl-8",children:"Name: A to Z"}),(0,r.jsx)(j.eb,{value:"name_desc",className:"relative pl-8",children:"Name: Z to A"})]}),(0,r.jsxs)(j.s3,{children:[(0,r.jsx)(j.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Popularity"}),(0,r.jsx)(j.eb,{value:"likes_desc",className:"relative pl-8",children:"Most Liked"}),(0,r.jsx)(j.eb,{value:"subscriptions_desc",className:"relative pl-8",children:"Most Subscribed"}),(0,r.jsx)(j.eb,{value:"rating_desc",className:"relative pl-8",children:"Highest Rated"})]})]})]}),(0,r.jsx)(h.$,{variant:"outline",onClick:()=>{N(!y)},className:"w-full sm:w-[140px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm overflow-hidden",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(H.A,{className:"flex-shrink-0 mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]"}),(0,r.jsx)("span",{className:"text-xs sm:text-sm truncate",children:"Filters"})]})})]})]}),(0,r.jsx)(d.N,{children:y&&(0,r.jsx)(o.P.div,{className:"mt-4 pt-4 border-t border-neutral-200/50 dark:border-neutral-800/50",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},children:(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4",children:(0,r.jsx)("div",{className:"text-sm text-neutral-500 dark:text-neutral-400",children:"Additional filters coming soon..."})})})})]})}),(0,r.jsxs)(o.P.div,{className:"container mx-auto px-4 flex items-center justify-between",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.2},children:[(0,r.jsx)("div",{className:"text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 truncate",children:b&&(0,r.jsxs)("span",{children:["Showing results for"," ",(0,r.jsxs)("span",{className:"font-medium text-neutral-700 dark:text-neutral-300 max-w-[150px] sm:max-w-none inline-block truncate",children:['"',b,'"']})]})}),b&&(0,r.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:A,className:"text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 h-8 px-2 py-1",children:[(0,r.jsx)(g.A,{className:"h-3.5 w-3.5 mr-1"}),"Clear Search"]})]}),(0,r.jsx)("div",{className:"container mx-auto px-4",children:n||!n&&0===t.length&&k.current?(0,r.jsx)(J.A,{}):t.length>0?(0,r.jsx)(q.A,{businesses:t,isAuthenticated:a}):(0,r.jsx)(o.P.div,{className:"text-center py-12 px-4 bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-sm",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-neutral-900 dark:text-white mb-2",children:"No Businesses Found"}),(0,r.jsxs)("p",{className:"text-neutral-500 dark:text-neutral-400 mb-4",children:["We couldn't find any businesses",b?' with "'.concat(b,'" in their name'):" in this location",". Try adjusting your search criteria."]}),b&&(0,r.jsxs)(h.$,{variant:"outline",className:"mt-2 border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10",onClick:A,children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Clear Search"]})]})})})]})}function X(e){let{businesses:t,isAuthenticated:a,hasMore:s,isLoadingMore:l,onLoadMore:n,onSortChange:d,onSearch:c,currentSortBy:u,isLoading:m,initialSearchTerm:x}=e,h=(0,i.useRef)(null);return(0,i.useEffect)(()=>{let e=new IntersectionObserver(e=>{e[0].isIntersecting&&s&&!l&&n()},{threshold:.1,rootMargin:"100px"}),t=h.current;return t&&e.observe(t),()=>{t&&e.unobserve(t)}},[s,l,n]),(0,r.jsxs)("div",{children:[(0,r.jsx)(W,{businesses:t,isAuthenticated:a,onSortChange:d,onSearch:c,currentSortBy:u,isLoading:m,initialSearchTerm:x}),!m&&(0,r.jsx)("div",{ref:h,className:"flex justify-center items-center py-8",children:l?(0,r.jsxs)(o.P.div,{className:"flex flex-col items-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:[(0,r.jsx)(v.A,{className:"h-6 w-6 animate-spin text-[var(--brand-gold)]"}),(0,r.jsx)("span",{className:"mt-2 text-sm text-neutral-600 dark:text-neutral-400",children:"Loading more businesses..."})]}):s?(0,r.jsx)("span",{className:"text-xs text-neutral-500 dark:text-neutral-500",children:"Scroll to load more"}):t.length>0?(0,r.jsx)("span",{className:"text-xs text-neutral-500 dark:text-neutral-500",children:"You've reached the end"}):null})]})}var Z=a(6874),K=a.n(Z),Q=a(61141),G=a(34424),ee=a(85213);function et(e){let{products:t,onSortChange:a,onFilterChange:s,currentSortBy:l,currentFilterBy:n,isLoading:d,onSearch:c,initialSearchTerm:u}=e,[m,p]=(0,i.useState)(u||""),[b,v]=(0,i.useState)(u||""),y=(0,i.useRef)(null),N=(0,i.useRef)(!0),w=(0,i.useRef)(m),k=(0,i.useRef)(!!u);(0,i.useEffect)(()=>{if(N.current&&u){N.current=!1,k.current=!0;return}w.current=m},[m,u]),(0,i.useEffect)(()=>{u&&(k.current=!0,N.current=!1)},[u]),(0,i.useEffect)(()=>{(t.length>0||!d)&&(N.current=!1)},[t,d]);let C=()=>{y.current&&(clearTimeout(y.current),y.current=null),p(""),v(""),setTimeout(()=>{c("")},100)};return(0,r.jsxs)("div",{className:"space-y-6 container mx-auto px-4",children:[(0,r.jsxs)(o.P.div,{className:"relative overflow-hidden bg-transparent p-5 rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80 transition-all duration-300",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},whileHover:{borderColor:"rgba(var(--brand-gold-rgb), 0.3)"},children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none opacity-70",children:[(0,r.jsx)("div",{className:"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-2xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10"}),(0,r.jsx)("div",{className:"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-2xl dark:from-purple-500/10 dark:to-blue-500/10"})]}),(0,r.jsxs)("div",{className:"relative z-10 flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-between",children:[(0,r.jsx)("div",{className:"relative w-full sm:w-auto sm:flex-1 max-w-full sm:max-w-md",children:(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),(""===m||m.length>=3)&&(y.current&&(clearTimeout(y.current),y.current=null),v(m),c(m))},children:(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--brand-gold)] transition-all duration-200"}),(0,r.jsx)(x.p,{placeholder:"Search products...",value:m,onChange:e=>{let t=e.target.value;if(p(t),y.current&&(clearTimeout(y.current),y.current=null),w.current.length>0&&""===t){v(""),setTimeout(()=>{c("")},100);return}(""===t||t.length>=3)&&(y.current=setTimeout(()=>{v(t),N.current?N.current=!1:c(t)},500))},className:"pl-10 h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm"}),m&&(0,r.jsx)("button",{type:"button",onClick:C,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors duration-200",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{type:"submit",className:"hidden",children:"Search"})]})})}),(0,r.jsxs)("div",{className:"flex flex-row gap-2 sm:gap-3 w-full sm:w-auto",children:[(0,r.jsxs)(j.l6,{value:n,onValueChange:e=>s(e),disabled:d,children:[(0,r.jsxs)(j.bq,{className:"w-full sm:min-w-[140px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm",children:[(0,r.jsx)(H.A,{className:"mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]"}),(0,r.jsx)(j.yv,{placeholder:"Filter",className:"text-xs sm:text-sm"})]}),(0,r.jsx)(j.gC,{children:(0,r.jsxs)(j.s3,{children:[(0,r.jsx)(j.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5",children:"Product Type"}),(0,r.jsx)(j.eb,{value:"all",className:"relative pl-8",children:"All Products"}),(0,r.jsx)(j.eb,{value:"physical",className:"relative pl-8",children:"Physical Items"}),(0,r.jsx)(j.eb,{value:"service",className:"relative pl-8",children:"Services"})]})})]}),(0,r.jsxs)(j.l6,{value:l,onValueChange:e=>a(e),disabled:d,children:[(0,r.jsxs)(j.bq,{className:"w-full sm:min-w-[160px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm",children:[(0,r.jsx)(ee.A,{className:"mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]"}),(0,r.jsx)(j.yv,{placeholder:"Sort by",className:"text-xs sm:text-sm"})]}),(0,r.jsxs)(j.gC,{children:[(0,r.jsxs)(j.s3,{children:[(0,r.jsx)(j.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5",children:"Date"}),(0,r.jsx)(j.eb,{value:"newest",className:"relative pl-8",children:"Newest First"})]}),(0,r.jsxs)(j.s3,{children:[(0,r.jsx)(j.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Price"}),(0,r.jsx)(j.eb,{value:"price_low",className:"relative pl-8",children:"Price: Low to High"}),(0,r.jsx)(j.eb,{value:"price_high",className:"relative pl-8",children:"Price: High to Low"})]}),(0,r.jsxs)(j.s3,{children:[(0,r.jsx)(j.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Name"}),(0,r.jsx)(j.eb,{value:"name_asc",className:"relative pl-8",children:"Name: A to Z"}),(0,r.jsx)(j.eb,{value:"name_desc",className:"relative pl-8",children:"Name: Z to A"})]})]})]})]})]})]}),(0,r.jsxs)(o.P.div,{className:"flex items-center justify-between px-2",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.2},children:[(0,r.jsxs)("div",{className:"text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 truncate",children:[b&&(0,r.jsxs)("span",{children:["Showing results for"," ",(0,r.jsxs)("span",{className:"font-medium text-neutral-700 dark:text-neutral-300 max-w-[150px] sm:max-w-none inline-block truncate",children:['"',b,'"']})]}),"all"!==n&&(0,r.jsxs)("span",{children:[" ","•"," ",(0,r.jsx)("span",{className:"font-medium text-neutral-700 dark:text-neutral-300",children:"physical"===n?"Physical Items":"Services"})]})]}),b&&(0,r.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:C,className:"text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 h-8 px-2 py-1",children:[(0,r.jsx)(g.A,{className:"h-3.5 w-3.5 mr-1"}),"Clear Search"]})]}),d||!d&&0===t.length&&N.current?(0,r.jsx)(G.A,{}):t.length>0?(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4",children:t.map((e,t)=>{let a="product-".concat(e.id);return(0,r.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*t},className:"group",children:e.business_slug?(0,r.jsx)(K(),{href:"/".concat(e.business_slug,"/product/").concat(e.slug||e.id),className:"block h-full",children:(0,r.jsx)("div",{className:"h-full",children:(0,r.jsx)(Q.A,{product:e,isLink:!1})})}):(0,r.jsxs)("div",{className:"relative h-full",children:[(0,r.jsx)(Q.A,{product:e,isLink:!1}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-red-500/80 text-white text-xs py-1 px-2 text-center rounded-b-md",children:"Unable to link to business"})]})},a)})}):(0,r.jsx)(o.P.div,{className:"text-center py-16 px-4 bg-transparent rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2",children:"No Products Found"}),(0,r.jsxs)("p",{className:"text-neutral-500 dark:text-neutral-400 mb-4",children:["We couldn't find any products",b?' with "'.concat(b,'" in the name'):"","all"!==n&&" in the ".concat("physical"===n?"Physical Items":"Services"," category"),". Try adjusting your search criteria or browse all products."]}),b&&(0,r.jsxs)(h.$,{variant:"outline",className:"mt-2 border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10",onClick:C,children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})]})})]})}function ea(e){let{products:t,hasMore:a,isLoadingMore:s,onLoadMore:l,onSortChange:n,onFilterChange:d,onSearch:c,currentSortBy:u,currentFilterBy:m,isLoading:x,initialSearchTerm:h}=e,p=(0,i.useRef)(null);return(0,i.useEffect)(()=>{let e=new IntersectionObserver(e=>{e[0].isIntersecting&&a&&!s&&l()},{threshold:.1,rootMargin:"100px"}),t=p.current;return t&&e.observe(t),()=>{t&&e.unobserve(t)}},[a,s,l]),(0,r.jsxs)("div",{children:[(0,r.jsx)(et,{products:t,onSortChange:n,onFilterChange:d,onSearch:c,currentSortBy:u,currentFilterBy:m,isLoading:x,initialSearchTerm:h}),!x&&(0,r.jsx)("div",{ref:p,className:"flex justify-center items-center py-8",children:s?(0,r.jsxs)(o.P.div,{className:"flex flex-col items-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:[(0,r.jsx)(v.A,{className:"h-6 w-6 animate-spin text-[var(--brand-gold)]"}),(0,r.jsx)("span",{className:"mt-2 text-sm text-neutral-600 dark:text-neutral-400",children:"Loading more products..."}),(0,r.jsx)("span",{className:"text-xs text-neutral-500 dark:text-neutral-500 mt-1",children:"Loading more items..."})]}):a?(0,r.jsx)("div",{className:"h-10 w-full bg-transparent"}):(0,r.jsx)(o.P.div,{className:"text-sm text-neutral-500 dark:text-neutral-400 py-4",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.2},children:t.length>0?"You've reached the end of the list":""})})]})}var er=a(34869);function es(){let e=(0,s.useSearchParams)(),{searchResult:t,viewType:a}=(0,l.u)(),i=e.get(n.lX),d=e.get(n.KC),c=e.get(n.Ie),u="All over India",m=er.A,x="Showing businesses and products from across the country",h="";if(i){var g;u="Pincode: ".concat(i),m=p.A,c&&"_any"!==c&&(h=c,u+=", ".concat(c)),(null==t||null==(g=t.location)?void 0:g.city)?(x="".concat(t.location.city,", ").concat(t.location.state),h||(h=t.location.city)):x="Showing nearby businesses and products"}else d&&(u="".concat(d),m=b.A,h=d,x="Showing businesses and products in this city");return"cards"===a?x=x.replace("businesses and products","businesses"):"products"===a&&(x=x.replace("businesses and products","products/services")),(0,r.jsx)(o.P.div,{className:"container mx-auto px-4 mb-6",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,r.jsxs)("div",{className:"bg-white/80 dark:bg-neutral-900/80 backdrop-blur-md rounded-xl border border-neutral-200/50 dark:border-neutral-800/50 p-4 flex items-center justify-center shadow-sm relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none opacity-50",children:[(0,r.jsx)("div",{className:"absolute -top-12 -right-12 w-24 h-24 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10"}),(0,r.jsx)("div",{className:"absolute -bottom-12 -left-12 w-24 h-24 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-xl dark:from-purple-500/10 dark:to-blue-500/10"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center text-center relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-[var(--brand-gold)]/20 rounded-full blur-sm"}),(0,r.jsx)(m,{className:"h-5 w-5 text-[var(--brand-gold)] relative z-10"})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-200",children:u})]}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:h?x.split(h).map((e,t,a)=>t===a.length-1?(0,r.jsx)("span",{children:e},t):(0,r.jsxs)("span",{children:[e,(0,r.jsx)("span",{className:"font-medium text-[var(--brand-gold)]",children:h})]},t)):x})]})]})})}function el(){let{viewType:e,sortBy:t,isSearching:a,isLoadingMore:i,businesses:d,products:c,hasMore:u,totalCount:m,isAuthenticated:x,productFilterBy:h,productSortBy:p,handleViewChange:b,handleBusinessSortChange:g,handleBusinessSearch:v,handleProductSearch:f,handleProductSortChange:j,handleProductFilterChange:y,loadMore:N}=(0,l.u)(),w=(0,s.useSearchParams)();return(0,r.jsxs)(o.P.div,{className:"w-full pb-16",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[(0,r.jsx)("div",{className:"container mx-auto px-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(o.P.h2,{className:"text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 text-center",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.5},children:"Browse Products/Services & Digital Cards"}),(0,r.jsx)($,{viewType:e,onViewChange:b,hasFilters:!!(w.get(n.ho)||w.get(n.lX)||w.get(n.Ie)||w.get(n.KC))})]})}),(0,r.jsx)(es,{}),(0,r.jsxs)("div",{className:"w-full",children:["cards"===e&&(0,r.jsx)(X,{businesses:d,isAuthenticated:x,totalCount:m,hasMore:u,isLoadingMore:i,onLoadMore:N,onSortChange:g,onSearch:v,currentSortBy:t,isLoading:a,initialSearchTerm:w.get(n.ho)}),"products"===e&&(0,r.jsx)(ea,{products:c,totalCount:m,hasMore:u,isLoadingMore:i,onLoadMore:N,onSortChange:j,onFilterChange:y,onSearch:f,currentSortBy:p,currentFilterBy:h,isLoading:a,initialSearchTerm:w.get(n.u0)})]})]})}function en(){let e=(0,s.useSearchParams)(),t=e.get(n.lX)||"",a=e.get(n.KC)||"",i=e.get(n.Ie)||"";return(0,r.jsx)(l.J,{children:(0,r.jsxs)("div",{className:"relative min-h-screen overflow-hidden bg-white dark:bg-black",children:[(0,r.jsx)(E,{initialValues:{pincode:t,city:a,locality:i}}),(0,r.jsx)(F,{}),(0,r.jsx)("div",{className:"container mx-auto px-4 my-4",children:(0,r.jsx)(V,{})}),(0,r.jsx)(el,{})]})})}},57840:(e,t,a)=>{"use strict";a.d(t,{D$:()=>l,ud:()=>s,x4:()=>i});var r=a(55594);let s=r.Ik({pincode:r.Yj().regex(/^\d{6}$/,{message:"Pincode must be exactly 6 digits."}),locality:r.Yj().optional().nullable()}),l=r.Ik({city:r.Yj().min(2,{message:"City name must be at least 2 characters."}),locality:r.Yj().optional().nullable()}),n=r.Ik({businessName:r.Yj().min(1,{message:"Business name is required."})}),i=r.Ik({businessName:r.Yj().optional().nullable(),pincode:r.Yj().regex(/^\d{6}$/,{message:"Pincode must be exactly 6 digits."}).optional().nullable(),city:r.Yj().optional().nullable(),locality:r.Yj().optional().nullable(),category:r.Yj().optional().nullable()}),o=r.Ik({page:r.ai().int().positive().default(1),limit:r.ai().int().positive().max(50).default(20)}),d=r.Ik({sortBy:r.k5(["name_asc","name_desc","created_asc","created_desc","likes_asc","likes_desc","subscriptions_asc","subscriptions_desc","rating_asc","rating_desc"]).default("created_desc")});s.extend({viewType:r.k5(["cards","products"]),...o.shape,...d.shape}),n.extend({viewType:r.k5(["cards","products"]),...o.shape,...d.shape}),i.extend({viewType:r.k5(["cards","products"]),...o.shape,...d.shape})},60823:(e,t,a)=>{"use strict";a.d(t,{AM:()=>n,Wv:()=>i,hl:()=>o});var r=a(95155);a(12115);var s=a(98176),l=a(53999);function n(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"popover",...t})}function i(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"popover-trigger",...t})}function o(e){let{className:t,align:a="center",sideOffset:n=4,...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"popover-content",align:a,sideOffset:n,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...i})})}},63162:(e,t,a)=>{"use strict";a.d(t,{CH:()=>s,iU:()=>l});var r=a(75168);async function s(e){if(!e||e.length<2)return{error:"Query must be at least 2 characters."};let t=(0,r.U)();try{let{data:a,error:r}=await t.rpc("get_distinct_cities",{search_query:"%".concat(e,"%"),result_limit:5});if(r){console.error("City Suggestions Error:",r);try{let{data:a,error:r}=await t.from("pincodes").select("DivisionName").ilike("DivisionName","%".concat(e,"%")).order("DivisionName").limit(100);if(r)throw r;if(!a||0===a.length)return{cities:[]};return{cities:[...new Set(a.map(e=>e.DivisionName.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase())))].slice(0,5)}}catch(e){return console.error("Fallback City Query Error:",e),{error:"Database error fetching city suggestions."}}}if(!a||0===a.length)return{cities:[]};return{cities:a.map(e=>e.city.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase()))}}catch(e){return console.error("City Suggestions Exception:",e),{error:"An unexpected error occurred during city suggestions."}}}async function l(e){if(!e||!/^\d{6}$/.test(e))return{error:"Invalid Pincode format."};let t=(0,r.U)();try{let{data:a,error:r}=await t.from("pincodes").select("OfficeName, DivisionName, StateName").eq("Pincode",e).order("OfficeName");if(r)return console.error("Pincode Fetch Error:",r),{error:"Database error fetching pincode details."};if(!a||0===a.length)return{error:"Pincode not found."};let s=a[0].StateName,l=a[0].DivisionName.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase()),n=[...new Set(a.map(e=>e.OfficeName.replace(" B.O","").trim()))];return{city:l,state:s,localities:n}}catch(e){return console.error("Pincode Lookup Exception:",e),{error:"An unexpected error occurred during pincode lookup."}}}},75168:(e,t,a)=>{"use strict";a.d(t,{U:()=>s});var r=a(20067);function s(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";return e&&t?(0,r.createBrowserClient)(e,t):(console.error("Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined."),(0,r.createBrowserClient)("",""))}},86894:(e,t,a)=>{"use strict";a.d(t,{A7:()=>h,FN:()=>m,Oj:()=>b,Q8:()=>p,Wk:()=>x});var r=a(95155),s=a(12115),l=a(85005),n=a(35169),i=a(92138),o=a(53999),d=a(97168);let c=s.createContext(null);function u(){let e=s.useContext(c);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}function m(e){let{orientation:t="horizontal",opts:a,setApi:n,plugins:i,className:d,children:u,...m}=e,[x,h]=(0,l.A)({...a,axis:"horizontal"===t?"x":"y"},i),[p,b]=s.useState(!1),[g,v]=s.useState(!1),f=s.useCallback(e=>{e&&(b(e.canScrollPrev()),v(e.canScrollNext()))},[]),j=s.useCallback(()=>{null==h||h.scrollPrev()},[h]),y=s.useCallback(()=>{null==h||h.scrollNext()},[h]),N=s.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),j()):"ArrowRight"===e.key&&(e.preventDefault(),y())},[j,y]);return s.useEffect(()=>{h&&n&&n(h)},[h,n]),s.useEffect(()=>{if(h)return f(h),h.on("reInit",f),h.on("select",f),()=>{null==h||h.off("select",f)}},[h,f]),(0,r.jsx)(c.Provider,{value:{carouselRef:x,api:h,opts:a,orientation:t||((null==a?void 0:a.axis)==="y"?"vertical":"horizontal"),scrollPrev:j,scrollNext:y,canScrollPrev:p,canScrollNext:g},children:(0,r.jsx)("div",{onKeyDownCapture:N,className:(0,o.cn)("relative",d),role:"region","aria-roledescription":"carousel","data-slot":"carousel",...m,children:u})})}function x(e){let{className:t,...a}=e,{carouselRef:s,orientation:l}=u();return(0,r.jsx)("div",{ref:s,className:"overflow-hidden","data-slot":"carousel-content",children:(0,r.jsx)("div",{className:(0,o.cn)("flex","horizontal"===l?"-ml-4":"-mt-4 flex-col",t),...a})})}function h(e){let{className:t,...a}=e,{orientation:s}=u();return(0,r.jsx)("div",{role:"group","aria-roledescription":"slide","data-slot":"carousel-item",className:(0,o.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===s?"pl-4":"pt-4",t),...a})}function p(e){let{className:t,variant:a="outline",size:s="icon",...l}=e,{orientation:i,scrollPrev:c,canScrollPrev:m}=u();return(0,r.jsxs)(d.$,{"data-slot":"carousel-previous",variant:a,size:s,className:(0,o.cn)("absolute size-8 rounded-full","horizontal"===i?"top-1/2 -left-12 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",t),disabled:!m,onClick:c,...l,children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}function b(e){let{className:t,variant:a="outline",size:s="icon",...l}=e,{orientation:n,scrollNext:c,canScrollNext:m}=u();return(0,r.jsxs)(d.$,{"data-slot":"carousel-next",variant:a,size:s,className:(0,o.cn)("absolute size-8 rounded-full","horizontal"===n?"top-1/2 -right-12 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",t),disabled:!m,onClick:c,...l,children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Next slide"})]})}},99840:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>m,Es:()=>h,HM:()=>c,L3:()=>p,c7:()=>x,lG:()=>i,rr:()=>b,zM:()=>o});var r=a(95155);a(12115);var s=a(45821),l=a(54416),n=a(53999);function i(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.bm,{"data-slot":"dialog-close",...t})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function m(e){let{className:t,children:a,hideClose:i=!1,...o}=e;return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[a,!i&&(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function p(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...a})}function b(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,2290,375,5152,7665,1884,67,6766,6199,4577,221,4081,9417,9862,6015,4490,7823,3098,8441,1684,7358],()=>t(2914)),_N_E=e.O()}]);