(()=>{var e={};e.id=5213,e.ids=[5213],e.modules={2052:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\[cardSlug]\\\\product\\\\[productSlug]\\\\ProductDetailClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3589:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3947:(e,t,r)=>{"use strict";r.d(t,{M$:()=>n,Qe:()=>l,cz:()=>o,fy:()=>c,lo:()=>d,y9:()=>i});var a=r(67218);r(79130);var s=r(32032);async function i(e,t){if(!e||!t)return{error:"Product ID and Business ID are required."};try{let r=await (0,s.createClient)(),{data:a,error:i}=await r.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `).eq("id",e).eq("business_id",t).eq("is_available",!0).single();if(i)return console.error("Error fetching product:",i),{error:"Failed to fetch product details"};if(!a)return{error:"Product not found"};return{data:{...a,base_price:a.base_price||0,product_type:a.product_type||"physical",created_at:a.created_at,updated_at:a.updated_at,slug:a.slug||void 0,description:a.description||void 0}}}catch(e){return console.error("Unexpected error in getProductById:",e),{error:"An unexpected error occurred"}}}async function n(e,t){if(!e||!t)return{error:"Product slug and Business ID are required."};try{let r=await (0,s.createClient)(),{data:a,error:i}=await r.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `).eq("slug",e).eq("business_id",t).eq("is_available",!0).single();if(i&&"PGRST116"===i.code)return{data:void 0};if(i)return console.error("Unexpected error fetching product by slug:",i),{error:"Failed to fetch product details"};if(!a)return{data:void 0};return{data:{...a,base_price:a.base_price||0,product_type:a.product_type||"physical",created_at:a.created_at,updated_at:a.updated_at,slug:a.slug||void 0,description:a.description||void 0}}}catch(e){return console.error("Unexpected error in getProductBySlug:",e),{error:"An unexpected error occurred"}}}async function l(e,t){if(!e||!t)return{error:"Product slug and Business ID are required."};try{let r=await (0,s.createClient)(),{data:a,error:i}=await r.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `).eq("slug",e).eq("business_id",t).eq("is_available",!0).single();if(i&&"PGRST116"===i.code)return{data:void 0};if(i)return console.error("Unexpected error fetching product by slug:",i),{error:"Failed to fetch product details"};if(!a)return{data:void 0};let{data:n,error:l}=await r.from("product_variants").select(`
        id,
        product_id,
        variant_name,
        variant_values,
        base_price,
        discounted_price,
        is_available,
        images,
        featured_image_index,
        created_at,
        updated_at
      `).eq("product_id",a.id).order("created_at",{ascending:!0});l&&console.error("Error fetching product variants:",l);let o=(n||[]).map(e=>({...e,variant_values:"string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values,created_at:e.created_at,updated_at:e.updated_at}));return{data:{...a,variant_count:o.length,has_variants:o.length>0,available_variant_count:o.filter(e=>e.is_available).length,variants:o}}}catch(e){return console.error("Unexpected error in getProductWithVariantsBySlug:",e),{error:"An unexpected error occurred"}}}async function o(e,t,r=12){if(!e||!t)return{error:"Business ID and current product ID are required."};try{let a=await (0,s.createClient)(),{data:i,error:n}=await a.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `).eq("business_id",e).eq("is_available",!0).neq("id",t).order("created_at",{ascending:!1}).limit(r);if(n)return console.error("Error fetching more products:",n),{error:"Failed to fetch more products"};return{data:(i||[]).map(e=>({...e,base_price:e.base_price||0,product_type:e.product_type||"physical",created_at:e.created_at,updated_at:e.updated_at,slug:e.slug||void 0,description:e.description||void 0}))}}catch(e){return console.error("Unexpected error in getMoreProductsFromBusiness:",e),{error:"An unexpected error occurred"}}}async function d(e,t=12){if(!e)return{error:"Business ID is required."};try{let r=await (0,s.createClient)(),{data:a,error:i}=await r.from("business_profiles").select("id").neq("id",e).eq("status","online");if(i)return console.error("Error filtering valid businesses:",i),{error:"Failed to filter valid businesses"};if(!a||0===a.length)return{data:[]};let n=a.map(e=>e.id),{data:l,error:o}=await r.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug,
        business_profiles!business_id(business_slug)
      `).in("business_id",n).eq("is_available",!0).order("created_at",{ascending:!1}).limit(t);if(o)return console.error("Error fetching products from other businesses:",o),{error:"Failed to fetch products from other businesses"};return{data:l.map(e=>{let t=null;e.business_profiles&&(t=Array.isArray(e.business_profiles)?e.business_profiles[0]?.business_slug||null:e.business_profiles.business_slug||null);let{...r}=e;return{id:r.id,business_id:r.business_id,name:r.name||"",description:r.description||"",base_price:Number(r.base_price)||0,discounted_price:r.discounted_price?Number(r.discounted_price):void 0,product_type:r.product_type||"physical",is_available:!!r.is_available,image_url:r.image_url,images:r.images||[],featured_image_index:"number"==typeof r.featured_image_index?r.featured_image_index:0,created_at:r.created_at?new Date(r.created_at):void 0,updated_at:r.updated_at?new Date(r.updated_at):void 0,slug:r.slug,business_slug:t}})}}catch(e){return console.error("Unexpected error in getProductsFromOtherBusinesses:",e),{error:"An unexpected error occurred"}}}async function c(e){if(!e)return{error:"Business ID is required."};try{let t=await (0,s.createClient)(),{data:r,error:a}=await t.from("business_profiles").select(`
        business_name,
        whatsapp_number,
        phone,
        business_slug
      `).eq("id",e).single();if(a)return console.error("Error fetching business profile:",a),{error:"Failed to fetch business details"};if(!r)return{error:"Business not found"};return{data:{business_name:r.business_name||"",whatsapp_number:r.whatsapp_number,phone:r.phone,business_slug:r.business_slug||""}}}catch(e){return console.error("Unexpected error in getBusinessWithContactInfo:",e),{error:"An unexpected error occurred"}}}(0,r(17478).D)([i,n,l,o,d,c]),(0,a.A)(i,"60c774c9cd7f805648acc431e17e9b49251f83eadb",null),(0,a.A)(n,"6054e88ba32ae31c6d750ee2a75ec30c873973587b",null),(0,a.A)(l,"603d34febc6e668d1e7ae55a3b2e84815fda4fb08b",null),(0,a.A)(o,"7094369a99c32bd3e8354f2032a6e159d11e10c908",null),(0,a.A)(d,"60a0349f18314ac5e32a4f521bafc546b6fec49407",null),(0,a.A)(c,"40a902b9836da176e1364b93ed50954a45c25e4f54",null)},5767:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(60687);r(43210);let s=e=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"})})},5903:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var a=r(60687),s=r(43210),i=r(30474),n=r(85814),l=r.n(n),o=r(71463),d=r(77882),c=r(88920),u=r(96241);let p=e=>null==e?null:e.toLocaleString("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}),m={hidden:{opacity:0,y:10},show:{opacity:1,y:0}},x={initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1,transition:{duration:.5,type:"spring",stiffness:400,damping:10}},hover:{scale:1.05,rotate:-2,transition:{type:"spring",stiffness:500}}};function b({product:e,isLink:t=!0}){let[r,n]=(0,s.useState)(!1),b=p(e.base_price),h=p(e.discounted_price),f=b,g=null,v=0,y="number"==typeof e.discounted_price&&e.discounted_price>0,_="number"==typeof e.base_price&&e.base_price>0;y&&_&&e.discounted_price<e.base_price?(f=h,g=b,v=Math.round((e.base_price-e.discounted_price)/e.base_price*100)):(f=b,g=null,v=0),f||(f="Price unavailable");let j=v>0,[w,N]=(0,s.useState)(!1),k=!e.is_available,A=(0,a.jsx)(d.P.div,{variants:m,initial:"hidden",animate:"show",className:"w-full overflow-hidden",children:(0,a.jsx)("div",{className:"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg",children:(0,a.jsxs)("div",{className:"relative w-full overflow-hidden rounded-lg",children:[(0,a.jsxs)("div",{className:"relative w-full overflow-hidden rounded-t-xl",children:[(()=>{let t=e.image_url;if(e.images&&Array.isArray(e.images)&&e.images.length>0){let r="number"==typeof e.featured_image_index?Math.min(e.featured_image_index,e.images.length-1):0;t=e.images[r]}return t&&!r?(0,a.jsxs)("div",{className:"overflow-hidden",children:[!w&&(0,a.jsx)(o.E,{className:"absolute inset-0 rounded-t-xl"}),(0,a.jsx)(d.P.div,{className:"w-full",children:(0,a.jsx)(i.default,{src:t,alt:e.name??"Product image",width:500,height:750,className:`w-full aspect-square object-cover ${k?"filter grayscale opacity-70 transition-all duration-500":""} ${w?"opacity-100":"opacity-0"} max-w-full`,loading:"lazy",onError:()=>n(!0),onLoad:()=>N(!0),quality:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",placeholder:"blur",style:{objectFit:"cover"}})})]}):(0,a.jsx)("div",{className:"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl",children:(0,a.jsx)("svg",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})(),k&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40",children:(0,a.jsx)("div",{className:"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground",children:(0,a.jsx)("span",{className:"font-medium tracking-wide uppercase text-xs sm:text-sm",children:"Out of Stock"})})}),j&&(0,a.jsx)(c.N,{children:(0,a.jsx)(d.P.div,{variants:x,initial:"initial",animate:"animate",whileHover:"hover",className:(0,u.cn)("absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg","bg-destructive","text-destructive-foreground border border-destructive-foreground/20","transform-gpu"),children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsx)("span",{className:"text-[7px] sm:text-[9px] md:text-[10px] font-medium",children:"SAVE"}),(0,a.jsxs)("span",{className:"text-[9px] sm:text-xs md:text-sm leading-none",children:[v,"%"]})]})},`discount-badge-${e.id}`)})]}),(0,a.jsxs)("div",{className:"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1",children:[(0,a.jsx)("p",{className:"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden",children:e.name??"Unnamed Product"}),e.description&&(0,a.jsx)("p",{className:"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate",children:e.description}),(0,a.jsx)("div",{className:"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1",children:(0,a.jsxs)("div",{className:"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full",children:[f&&(0,a.jsx)("p",{className:"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full",children:f}),g&&(0,a.jsx)("p",{className:"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500",children:g})]})})]})]})})});return t&&"business_slug"in e&&e.business_slug?(0,a.jsx)(l(),{href:`/${e.business_slug}/product/${e.slug||e.id}`,className:"block h-full",children:A}):A}},8167:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"40274d19c8c06b8c99a2839744992d7bcecb2341c5":()=>a.$,"407031dcd9eee2e858cfbb811d905061dca994df13":()=>a.f,"40a902b9836da176e1364b93ed50954a45c25e4f54":()=>s.fy,"603d34febc6e668d1e7ae55a3b2e84815fda4fb08b":()=>s.Qe,"6054e88ba32ae31c6d750ee2a75ec30c873973587b":()=>s.M$,"60a0349f18314ac5e32a4f521bafc546b6fec49407":()=>s.lo,"60c774c9cd7f805648acc431e17e9b49251f83eadb":()=>s.y9,"7094369a99c32bd3e8354f2032a6e159d11e10c908":()=>s.cz});var a=r(27628),s=r(3947)},9009:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["[cardSlug]",{children:["product",{children:["[productSlug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72978)),"C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,48236)),"C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,36024)),"C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[cardSlug]/product/[productSlug]/page",pathname:"/[cardSlug]/product/[productSlug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17135:(e,t,r)=>{"use strict";r.d(t,{d:()=>l});var a=r(24342),s=r(43210),i=r(32582),n=r(72789);function l(e){let t=(0,n.M)(()=>(0,a.OQ)(e)),{isStatic:r}=(0,s.useContext)(i.Q);if(r){let[,r]=(0,s.useState)(e);(0,s.useEffect)(()=>t.on("change",r),[])}return t}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20247:(e,t,r)=>{Promise.resolve().then(r.bind(r,49156)),Promise.resolve().then(r.bind(r,63538))},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:l="",children:o,iconNode:d,...c},u)=>(0,a.createElement)("svg",{ref:u,...n,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:i("lucide",l),...c},[...d.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(o)?o:[o]])),o=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...n},o)=>(0,a.createElement)(l,{ref:o,iconNode:t,className:i(`lucide-${s(e)}`,r),...n}));return r.displayName=`${e}`,r}},27628:(e,t,r)=>{"use strict";r.d(t,{$:()=>n,f:()=>i});var a=r(67218);r(79130);var s=r(32032);async function i(e){if(!e)return{error:"Business slug is required."};try{let t=await (0,s.createClient)(),{data:r,error:a}=await t.from("business_profiles").select(`
        *,
        payment_subscriptions!business_profile_id (
          plan_id,
          subscription_status
        )
      `).eq("business_slug",e).maybeSingle();if(a)return console.error("Secure Fetch Error:",a),{error:`Failed to fetch business profile: ${a.message}`};if(!r)return{error:"Profile not found."};return{data:{...r,subscription_status:r.payment_subscriptions?.subscription_status||null,plan_id:r.payment_subscriptions?.plan_id||null}}}catch(e){return console.error("Exception in getSecureBusinessProfileBySlug:",e),{error:"An unexpected error occurred."}}}async function n(e){if(!e)return{error:"Business slug is required."};try{let t=await (0,s.createClient)(),{data:r,error:a}=await t.from("business_profiles").select(`
        *,
        products_services (
          id, name, description, base_price, discounted_price, is_available, image_url, created_at, updated_at, product_type
        )
      `).eq("business_slug",e).maybeSingle();if(a)return console.error("Secure Fetch Error:",a),{error:`Failed to fetch business profile: ${a.message}`};if(!r)return{error:"Profile not found."};return{data:{...r,products_services:r.products_services||[]}}}catch(e){return console.error("Exception in getSecureBusinessProfileWithProductsBySlug:",e),{error:"An unexpected error occurred."}}}(0,r(17478).D)([i,n]),(0,a.A)(i,"407031dcd9eee2e858cfbb811d905061dca994df13",null),(0,a.A)(n,"40274d19c8c06b8c99a2839744992d7bcecb2341c5",null)},27910:e=>{"use strict";e.exports=require("stream")},28561:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>s});var a=r(34386);async function s(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let s=null,i=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));s=await e(),i=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||s&&"true"===s.get("x-playwright-testing"))&&s?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),a="customer"===r||"business"===r,s=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,a,s){let i=()=>{var i,n,l,o,d;return i=e,n=t,l=r,o=a,d=s,"customer_profiles"===i?{data:l&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:l&&"business"===n?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===n?{id:"test-subscription-id",plan_id:d,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,r)=>n(e),neq:(t,r)=>n(e),gt:(t,r)=>n(e),gte:(t,r)=>n(e),lt:(t,r)=>n(e),lte:(t,r)=>n(e),like:(t,r)=>n(e),ilike:(t,r)=>n(e),is:(t,r)=>n(e),in:(t,r)=>n(e),contains:(t,r)=>n(e),containedBy:(t,r)=>n(e),rangeGt:(t,r)=>n(e),rangeGte:(t,r)=>n(e),rangeLt:(t,r)=>n(e),rangeLte:(t,r)=>n(e),rangeAdjacent:(t,r)=>n(e),overlaps:(t,r)=>n(e),textSearch:(t,r)=>n(e),match:t=>n(e),not:(t,r,a)=>n(e),or:t=>n(e),filter:(t,r,a)=>n(e),order:(t,r)=>n(e),limit:(t,r)=>n(e),range:(t,r,a)=>n(e),abortSignal:t=>n(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let t=i();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,r,a,s,i)}}(s):i?(0,a.createServerClient)(e,t,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:t,value:r,options:a}of e)await i.set(t,r,a)}catch{}}}}):(0,a.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36024:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(37413),s=r(63420);function i(){return(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(s.A,{className:"h-8 w-8 animate-spin text-[var(--brand-gold)]"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading business card..."})]})})}},38612:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\[cardSlug]\\\\product\\\\components\\\\OfflineProductMessage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx","default")},48236:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(37413);r(61120);var s=r(14890),i=r(60644),n=r(11637),l=r(95006),o=r(92506),d=r(46501),c=r(21886),u=r(23392);function p({children:e}){return(0,a.jsx)(u.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,a.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,a.jsx)(s.default,{}),(0,a.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,a.jsx)(i.default,{}),(0,a.jsx)(l.default,{}),(0,a.jsx)(n.default,{}),(0,a.jsx)(o.default,{}),(0,a.jsx)(d.default,{}),(0,a.jsx)(c.default,{excludePaths:["/dashboard"]})]})})}},48340:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},49156:(e,t,r)=>{"use strict";r.d(t,{default:()=>Z});var a=r(60687),s=r(43210),i=r(30474),n=r(77882),l=r(32192),o=r(62688);let d=(0,o.A)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var c=r(19080),u=r(96882),p=r(81620),m=r(59821),x=r(24934),b=r(71463),h=r(3018),f=r(8730),g=r(14952),v=r(96241);function y({...e}){return(0,a.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function _({className:e,...t}){return(0,a.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,v.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function j({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,v.cn)("inline-flex items-center gap-1.5",e),...t})}function w({asChild:e,className:t,...r}){let s=e?f.DX:"a";return(0,a.jsx)(s,{"data-slot":"breadcrumb-link",className:(0,v.cn)("hover:text-foreground transition-colors",t),...r})}function N({className:e,...t}){return(0,a.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,v.cn)("text-foreground font-normal",e),...t})}function k({children:e,className:t,...r}){return(0,a.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,v.cn)("[&>svg]:size-3.5",t),...r,children:e??(0,a.jsx)(g.A,{})})}var A=r(84404),C=r(5767);function S({whatsappNumber:e,productName:t,businessName:r,productUrl:i}){let[n,l]=(0,s.useState)(!1),o=e.replace(/\D/g,""),d=encodeURIComponent(`Hi ${r}, I'm interested in your product "${t}" that I saw on your Dukancard (${i}). Can you provide more information?`),c=`https://wa.me/${o}?text=${d}`;return(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-green-500/30 to-green-600/30 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium flex items-center justify-center gap-3 py-6 rounded-xl border border-green-500/20 transition-all duration-300",onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),onClick:()=>window.open(c,"_blank"),children:[(0,a.jsx)("div",{className:"absolute inset-0 w-full h-full overflow-hidden rounded-xl",children:(0,a.jsx)("div",{className:"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"})}),(0,a.jsx)(C.A,{className:`w-5 h-5 ${n?"animate-spin-slow":""}`}),(0,a.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"WhatsApp"})]})]})}function P(){return(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-neutral-400/20 to-neutral-600/20 rounded-xl blur-md opacity-50 transition-opacity duration-300"}),(0,a.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-neutral-500 to-neutral-600 text-white font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-80 rounded-xl border border-neutral-500/10 transition-all duration-300",disabled:!0,children:[(0,a.jsx)(C.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"WhatsApp Unavailable"})]})]})}var $=r(48340);function q({phoneNumber:e,_businessName:t}){let[r,i]=(0,s.useState)(!1),n=e.replace(/\D/g,"");return(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-blue-600/30 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium flex items-center justify-center gap-3 py-6 rounded-xl border border-blue-500/20 transition-all duration-300",onMouseEnter:()=>i(!0),onMouseLeave:()=>i(!1),onClick:()=>window.open(`tel:${n}`,"_blank"),children:[(0,a.jsx)("div",{className:"absolute inset-0 w-full h-full overflow-hidden rounded-xl",children:(0,a.jsx)("div",{className:"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"})}),(0,a.jsx)($.A,{className:`w-5 h-5 ${r?"animate-pulse":""}`}),(0,a.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"Call"})]})]})}function E(){return(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-neutral-400/20 to-neutral-600/20 rounded-xl blur-md opacity-50 transition-opacity duration-300"}),(0,a.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-neutral-500 to-neutral-600 text-white font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-80 rounded-xl border border-neutral-500/10 transition-all duration-300",disabled:!0,children:[(0,a.jsx)($.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"Call Unavailable"})]})]})}var z=r(28561);function I(){let[e,t]=(0,s.useState)(!1);return(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/20 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)(x.$,{className:"relative w-full bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] hover:from-[var(--brand-gold-dark)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-90 rounded-xl border border-[var(--brand-gold)]/20 transition-all duration-300",onMouseEnter:()=>t(!0),onMouseLeave:()=>t(!1),disabled:!0,children:[(0,a.jsx)("div",{className:"absolute inset-0 w-full h-full overflow-hidden rounded-xl",children:(0,a.jsx)("div",{className:"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"})}),(0,a.jsx)(z.A,{className:`w-5 h-5 ${e?"animate-pulse":""}`}),(0,a.jsx)("span",{className:"text-base font-semibold tracking-wide",children:"Buy Now"})]})]})}var F=r(11860);let D=(0,o.A)("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var M=r(17135),U=r(88920);function B({isOpen:e,onClose:t,imageUrl:r,altText:l}){let[o,c]=(0,s.useState)(1),[u,p]=(0,s.useState)(!0),m=(0,s.useRef)(null),x=(0,s.useRef)(null),[b,h]=(0,s.useState)({top:0,right:0,bottom:0,left:0}),f=(0,M.d)(0),g=(0,M.d)(0);!function(e,t,r,a={}){let{minScale:i=.5,maxScale:n=3}=a,[,l]=(0,s.useState)([]),[o,d]=(0,s.useState)(null),[c,u]=(0,s.useState)(1),[p,m]=(0,s.useState)(!1),[x,b]=(0,s.useState)(0)}(0,0,0,{minScale:.5,maxScale:3,scaleStep:.1});let v=()=>c(e=>Math.min(e+.25,3)),y=()=>c(e=>Math.max(e-.25,.5));return(0,a.jsx)(U.N,{children:e&&(0,a.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center bg-black p-4",onClick:t,children:[(0,a.jsx)("button",{onClick:t,className:"absolute top-4 right-4 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors","aria-label":"Close",children:(0,a.jsx)(F.A,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 z-10 flex items-center gap-4 bg-black/50 rounded-full p-2",children:[(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),y()},className:"p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors","aria-label":"Zoom out",disabled:o<=.5,children:(0,a.jsx)(D,{className:"w-5 h-5"})}),(0,a.jsxs)("span",{className:"text-white text-sm font-medium",children:[Math.round(100*o),"%"]}),(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),v()},className:"p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors","aria-label":"Zoom in",disabled:o>=3,children:(0,a.jsx)(d,{className:"w-5 h-5"})})]}),(0,a.jsxs)(n.P.div,{ref:m,className:"relative w-full max-w-4xl h-[80vh] overflow-hidden touch-none",onClick:e=>e.stopPropagation(),initial:{scale:.9},animate:{scale:1},exit:{scale:.9},style:{touchAction:"none"},onTouchStart:e=>{o>1&&e.stopPropagation()},children:[u&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-10 h-10 border-4 border-neutral-300 border-t-[var(--brand-gold)] rounded-full animate-spin"})}),(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center overflow-hidden",children:(0,a.jsx)(n.P.div,{ref:x,drag:o>1,dragConstraints:b,dragElastic:0,dragMomentum:!1,dragTransition:{power:.1,timeConstant:200},style:{x:f,y:g,scale:o,touchAction:"none",cursor:o>1?"grab":"default"},className:"relative touch-none will-change-transform",whileDrag:{cursor:"grabbing"},onDoubleClick:e=>{if(o>1)c(1);else{let t=e.currentTarget.getBoundingClientRect(),r=e.clientX-t.left,a=e.clientY-t.top,s=t.width/2,i=t.height/2;c(2),f.set(-((r-s)*.5)),g.set(-((a-i)*.5))}},children:(0,a.jsx)("div",{className:"relative",style:{pointerEvents:"none"},children:(0,a.jsx)(i.default,{src:r,alt:l,width:1200,height:1800,className:"max-w-none object-contain select-none",onLoad:()=>p(!1),priority:!0,draggable:!1,unoptimized:!0,style:{userSelect:"none",WebkitUserSelect:"none"}})})})})]})]})})}var L=r(13964);function O({variants:e,selectedVariant:t,onVariantSelect:r,className:i,disabled:l=!1}){let[o,d]=(0,s.useState)({}),[c,u]=(0,s.useState)({}),p=(e,t)=>{l||u(r=>{let a={...r};return a[e]===t?delete a[e]:a[e]=t,a})},x=(t,r)=>{if(0===Object.keys(c).length)return e.some(e=>e.is_available&&e.variant_values&&e.variant_values[t]===r);let a={...c,[t]:r};return e.some(e=>!!e.is_available&&Object.entries(a).every(([t,r])=>e.variant_values&&e.variant_values[t]===r))};return e&&0!==e.length?(0,a.jsx)("div",{className:(0,v.cn)("space-y-6",i),children:Object.entries(o).map(([e,t])=>(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-base font-semibold text-neutral-900 dark:text-neutral-100 capitalize",children:e}),c[e]&&(0,a.jsx)(m.E,{variant:"secondary",className:"text-xs font-medium",children:t.find(t=>t.value===c[e])?.display_value||c[e]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-3",children:t.map(t=>{let r=c[e]===t.value,s=x(e,t.value),i="color"===e.toLowerCase();return(0,a.jsx)(n.P.div,{whileHover:s&&!l?{scale:1.05}:{},whileTap:s&&!l?{scale:.95}:{},className:"relative",children:i&&t.color_code?(0,a.jsx)("button",{onClick:()=>p(e,t.value),disabled:!s&&!r||l,className:(0,v.cn)("relative w-12 h-12 rounded-full transition-all duration-200 border-2","border-neutral-300 dark:border-neutral-600",r&&"ring-2 ring-[var(--brand-gold)] ring-offset-2 ring-offset-white dark:ring-offset-neutral-900",!s&&!r&&"opacity-60 cursor-not-allowed",(s||r)&&"cursor-pointer hover:scale-105"),style:{backgroundColor:t.color_code},title:r?`${t.display_value} (Click to deselect)`:t.display_value,children:(0,a.jsx)(U.N,{children:r&&(0,a.jsx)(n.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{duration:.2},className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)(L.A,{className:(0,v.cn)("h-5 w-5 drop-shadow-sm","#FFFFFF"===t.color_code||"#FFFF00"===t.color_code||"#FFC0CB"===t.color_code?"text-neutral-800":"text-white")})})})}):(0,a.jsx)("button",{onClick:()=>p(e,t.value),disabled:!s&&!r||l,className:(0,v.cn)("relative px-4 py-2.5 rounded-lg transition-all duration-200 font-medium text-sm border-2","border-neutral-300 dark:border-neutral-600",r&&["bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)]","border-[var(--brand-gold)] ring-2 ring-[var(--brand-gold)]/30"],!s&&!r&&["opacity-60 cursor-not-allowed","text-neutral-400 dark:text-neutral-600"],(s||r)&&!r&&["text-neutral-700 dark:text-neutral-300","hover:scale-105 hover:border-neutral-400 dark:hover:border-neutral-500"],(s||r)&&"cursor-pointer"),children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[t.display_value,(0,a.jsx)(U.N,{children:r&&(0,a.jsx)(n.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{duration:.2},children:(0,a.jsx)(L.A,{className:"h-4 w-4"})})})]})})},`${e}-${t.value}`)})})]},e))}):null}r(38632);var T=r(85814),R=r.n(T);let G=e=>{try{return new URL(e),!0}catch{return!1}};function W({topAdData:e,itemVariants:t,businessCustomAd:r,userPlan:s}){let l=r&&"object"==typeof r&&!0===r.enabled&&r.image_url&&"string"==typeof r.image_url&&""!==r.image_url.trim()&&G(r.image_url),o=r?.link_url&&"string"==typeof r.link_url&&""!==r.link_url.trim()&&G(r.link_url),d=("pro"===s||"enterprise"===s)&&l,c=!d&&e&&e.imageUrl;return(0,a.jsx)(n.P.div,{variants:t,className:"w-full",children:(0,a.jsx)("div",{className:"flex-shrink-0 w-full overflow-hidden",children:d?o?(0,a.jsx)(R(),{href:r.link_url,target:"_blank",rel:"noopener noreferrer",className:"block w-full overflow-hidden",children:(0,a.jsxs)("div",{className:"relative w-full",children:[(0,a.jsx)(i.default,{src:r.image_url,alt:"Business Advertisement",width:1200,height:675,className:"w-full h-auto object-contain max-w-full",unoptimized:!0}),(0,a.jsx)("div",{className:"absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:"Sponsored"})]})}):(0,a.jsx)("div",{className:"block w-full overflow-hidden",children:(0,a.jsxs)("div",{className:"relative w-full",children:[(0,a.jsx)(i.default,{src:r.image_url,alt:"Business Advertisement",width:1200,height:675,className:"w-full h-auto object-contain max-w-full",unoptimized:!0}),(0,a.jsx)("div",{className:"absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:"Sponsored"})]})}):c?(0,a.jsx)(R(),{href:e.linkUrl||"#",target:"_blank",rel:"noopener noreferrer",className:"block w-full overflow-hidden",children:(0,a.jsx)("div",{className:"relative w-full",children:(0,a.jsx)(i.default,{src:e.imageUrl,alt:"Advertisement",width:1200,height:675,className:"w-full h-auto object-contain max-w-full",unoptimized:!0})})}):(0,a.jsx)("div",{className:"border border-dashed rounded-lg p-4 flex items-center justify-center w-full text-neutral-500 bg-neutral-50 dark:bg-neutral-800/50 min-h-[300px]"})})})}function V({product:e,variants:t=[],businessSlug:r,businessName:o,whatsappNumber:f,phoneNumber:g,topAdData:C,businessCustomAd:$,userPlan:z}){let[F,D]=(0,s.useState)({}),[M,U]=(0,s.useState)({}),[L,T]=(0,s.useState)(""),[R,G]=(0,s.useState)(!1),[V,Y]=(0,s.useState)(!1),[Q,Z]=(0,s.useState)(null),[H,J]=(0,s.useState)(),[X,K]=(0,s.useState)(),[ee,et]=(0,s.useState)([]),[er,ea]=(0,s.useState)(0),[es,ei]=(0,s.useState)(null),[en,el]=(0,s.useState)(e),eo=en.base_price?(0,v.vv)(en.base_price):"Price not set",ed=en.discounted_price?(0,v.vv)(en.discounted_price):null,ec=0;en.discounted_price&&en.base_price&&en.discounted_price<en.base_price&&(ec=Math.round((en.base_price-en.discounted_price)/en.base_price*100));let eu={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return(0,a.jsxs)(n.P.div,{className:"w-full mx-auto flex flex-col",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",children:[V&&Q&&(0,a.jsx)(B,{isOpen:V,onClose:()=>Y(!1),imageUrl:Q,altText:e.name||"Product image"}),(0,a.jsx)(n.P.div,{variants:eu,className:"mb-5",children:(0,a.jsx)(y,{children:(0,a.jsxs)(_,{children:[(0,a.jsx)(j,{children:(0,a.jsx)(w,{href:"/",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})}),(0,a.jsx)(k,{}),(0,a.jsx)(j,{children:(0,a.jsx)(w,{href:`/${r}`,children:o})}),(0,a.jsx)(k,{}),(0,a.jsx)(j,{children:(0,a.jsx)(N,{children:e.name})})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 lg:gap-16",children:[(0,a.jsxs)(n.P.div,{variants:{hidden:{opacity:0,x:-30},visible:{opacity:1,x:0,transition:{duration:.7,ease:"easeOut"}}},className:"relative md:sticky md:top-20 self-start md:px-4",children:[(0,a.jsx)(A.FN,{className:"w-full mb-4",opts:{align:"start",loop:!0},setApi:J,children:(0,a.jsx)(A.Wk,{children:ee.length>0?ee.map((t,r)=>(0,a.jsx)(A.A7,{children:(0,a.jsxs)("div",{className:"relative w-full aspect-square overflow-hidden bg-neutral-50 dark:bg-neutral-900 group rounded-xl border border-neutral-200 dark:border-neutral-800 cursor-pointer",onClick:()=>{t&&!M[t]&&Y(!0)},children:[!F[t]&&!M[t]&&(0,a.jsx)(b.E,{className:"absolute inset-0"}),M[t]?(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"w-20 h-20 text-neutral-300 dark:text-neutral-700"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.default,{src:t,alt:e.name,fill:!0,className:`object-cover transition-all duration-500 ${F[t]?"opacity-100":"opacity-0"} group-hover:scale-105`,onLoad:()=>D(e=>({...e,[t]:!0})),onError:()=>U(e=>({...e,[t]:!0})),sizes:"(max-width: 768px) 100vw, 50vw",priority:!0}),F[t]&&(0,a.jsx)("div",{className:"absolute bottom-3 right-3 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer",children:(0,a.jsx)(d,{className:"w-5 h-5"})})]}),ec>0&&(0,a.jsx)(n.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:400,damping:10},className:"absolute top-4 right-4 bg-red-500 text-white px-3 py-1.5 rounded-lg font-bold text-sm shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsx)("span",{className:"text-xs font-medium sm:text-[0.6rem]",children:"SAVE"}),(0,a.jsxs)("span",{className:"text-sm leading-none sm:text-xs",children:[ec,"%"]})]})})]})},`main-${t}-${r}`)):(0,a.jsx)(A.A7,{children:(0,a.jsx)("div",{className:"relative w-full aspect-square overflow-hidden bg-neutral-50 dark:bg-neutral-900 group rounded-xl border border-neutral-200 dark:border-neutral-800 cursor-pointer mb-4 flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"w-20 h-20 text-neutral-300 dark:text-neutral-700"})})})})},`main-carousel-${ee.length}-${ee[0]||"empty"}`),ee.length>1&&(0,a.jsx)(A.FN,{className:"w-full",opts:{align:"start",loop:!0},setApi:K,children:(0,a.jsx)(A.Wk,{className:"justify-center",children:ee.map((t,r)=>(0,a.jsx)(A.A7,{className:"basis-1/4 md:basis-1/5 lg:basis-1/6",children:(0,a.jsx)("div",{className:`relative aspect-square overflow-hidden rounded-md cursor-pointer border-2 ${Q===t?"border-[var(--brand-gold)]":"border-transparent"}`,onClick:()=>ea(r),children:(0,a.jsx)(i.default,{src:t,alt:`${e.name||"Product"} - Image ${r+1}`,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 25vw, 10vw",onLoad:()=>D(e=>({...e,[t]:!0})),onError:()=>U(e=>({...e,[t]:!0}))})})},`thumb-${t}-${r}`))})},`thumbnail-carousel-${ee.length}-${ee[0]||"empty"}`)]}),(0,a.jsxs)(n.P.div,{variants:{hidden:{opacity:0,x:30},visible:{opacity:1,x:0,transition:{duration:.7,ease:"easeOut",delay:.2}}},className:"flex flex-col space-y-5 p-6 md:p-8 lg:p-10 md:sticky md:top-20 self-start md:px-4",children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 leading-tight",children:e.name}),(0,a.jsx)(m.E,{variant:"secondary",className:"w-fit capitalize",children:e.product_type}),(0,a.jsx)("div",{className:"flex items-baseline gap-3",children:ed?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50",children:ed}),(0,a.jsx)("span",{className:"text-sm md:text-base line-through text-neutral-500 dark:text-neutral-400",children:eo})]}):(0,a.jsx)("span",{className:"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50",children:eo})}),(0,a.jsxs)(h.Fc,{variant:"default",className:"mt-4 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)(h.XL,{children:"Important Price Information"}),(0,a.jsx)(h.TN,{children:"Prices are indicative and may vary in-store. Visit us or contact directly for final deals and confirmation."})]}),t&&t.length>0&&(0,a.jsx)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},className:"mt-8",children:(0,a.jsx)(O,{variants:t,selectedVariant:es,onVariantSelect:t=>{ei(t),t?el({...e,base_price:t.base_price||e.base_price,discounted_price:t.discounted_price||e.discounted_price,images:t.images&&t.images.length>0?t.images:e.images,featured_image_index:t.images&&t.images.length>0?t.featured_image_index:e.featured_image_index}):el(e)}})}),(0,a.jsxs)("div",{className:"mt-6 pt-5 border-t border-neutral-200 dark:border-neutral-800 space-y-4",children:[(0,a.jsx)(I,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)("div",{children:f?(0,a.jsx)(S,{whatsappNumber:f,productName:e.name,businessName:o,productUrl:L||`http://localhost:3000/${r}/product/${e.slug||e.id}`}):(0,a.jsx)(P,{})}),(0,a.jsx)("div",{children:g?(0,a.jsx)(q,{phoneNumber:g,_businessName:o}):(0,a.jsx)(E,{})})]})]}),e.description&&(0,a.jsxs)("div",{className:"mt-6 text-neutral-700 dark:text-neutral-300",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Description"}),(0,a.jsx)("p",{className:"whitespace-pre-line leading-relaxed",children:e.description})]}),(0,a.jsxs)("div",{className:"mt-4 relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-neutral-500/20 to-neutral-600/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)(x.$,{variant:"outline",className:"relative w-full flex items-center justify-center gap-3 py-5 border border-neutral-300/50 dark:border-neutral-700/50 hover:border-[var(--brand-gold)]/50 dark:hover:border-[var(--brand-gold)]/50 rounded-xl transition-all duration-300 bg-white/50 dark:bg-black/50 backdrop-blur-sm",onMouseEnter:()=>G(!0),onMouseLeave:()=>G(!1),onClick:()=>{let a=L||`http://localhost:3000/${r}/product/${e.slug||e.id}`,s=`Check out ${e.name} from ${o} on Dukancard`;if(es&&t.length>0){let t=Object.entries(es.variant_values||{}).map(([e,t])=>`${e}: ${t}`).join(", ");s=`Check out ${e.name} (${t}) from ${o} on Dukancard`}navigator.share?navigator.share({title:e.name,text:s,url:a}):(navigator.clipboard.writeText(a),alert("Link copied to clipboard!"))},children:[(0,a.jsx)(p.A,{className:`w-5 h-5 text-[var(--brand-gold)] transition-all duration-300 ${R?"rotate-12":""}`}),(0,a.jsx)("span",{className:"text-base font-medium tracking-wide",children:"Share"})]})]})]})]}),(0,a.jsx)(n.P.div,{variants:eu,className:"mt-8 md:mt-12",children:(0,a.jsx)(W,{topAdData:C,itemVariants:eu,businessCustomAd:$,userPlan:z})})]})}var Y=r(5903);function Q({businessProducts:e,otherBusinessProducts:t,businessSlug:r}){let s={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},i={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return(0,a.jsxs)("div",{className:"mt-8 sm:mt-12 md:mt-16 space-y-8 sm:space-y-12 md:space-y-16 md:px-4",children:[e.length>0&&(0,a.jsxs)(n.P.section,{variants:s,initial:"hidden",animate:"visible",className:"space-y-6",children:[(0,a.jsx)(n.P.div,{variants:i,className:"flex items-center justify-between",children:(0,a.jsxs)("h2",{className:"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50 relative",children:["More from this business",(0,a.jsx)("span",{className:"absolute -bottom-2 left-0 w-16 h-1 bg-primary rounded-full"})]})}),(0,a.jsx)(n.P.div,{variants:i,className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6",children:e.map(e=>(0,a.jsx)(R(),{href:`/${r}/product/${e.slug||e.id}`,className:"block h-full transform transition-transform duration-300 hover:-translate-y-1",children:(0,a.jsx)(Y.A,{product:e,isLink:!1})},e.id))})]}),t.length>0&&(0,a.jsxs)(n.P.section,{variants:s,initial:"hidden",animate:"visible",className:"space-y-6",children:[(0,a.jsx)(n.P.div,{variants:i,className:"flex items-center justify-between",children:(0,a.jsxs)("h2",{className:"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50 relative",children:["You might also like",(0,a.jsx)("span",{className:"absolute -bottom-2 left-0 w-16 h-1 bg-primary rounded-full"})]})}),(0,a.jsx)(n.P.div,{variants:i,className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6",children:t.map(e=>(0,a.jsx)(R(),{href:`/${e.business_slug}/product/${e.slug||e.id}`,className:"block h-full transform transition-transform duration-300 hover:-translate-y-1",children:(0,a.jsx)(Y.A,{product:e,isLink:!1})},e.id))})]})]})}function Z({product:e,variants:t=[],businessSlug:r,businessName:i,whatsappNumber:n,phoneNumber:l,businessProducts:o,otherBusinessProducts:d,topAdData:c,businessCustomAd:u,userPlan:p}){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:"Loading product details..."}),children:(0,a.jsxs)("div",{className:"w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-8 min-h-screen max-w-7xl",children:[(0,a.jsx)(V,{product:e,variants:t,businessSlug:r,businessName:i,whatsappNumber:n,phoneNumber:l,topAdData:c,businessCustomAd:u,userPlan:p}),(o.length>0||d.length>0)&&(0,a.jsx)(Q,{businessProducts:o.slice(0,12),otherBusinessProducts:d.slice(0,12),businessSlug:r})]})})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59999:(e,t,r)=>{Promise.resolve().then(r.bind(r,2052)),Promise.resolve().then(r.bind(r,38612))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63420:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(26373).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},63538:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(60687),s=r(43210),i=r(77882),n=r(19080),l=r(46001),o=r(24934),d=r(16189);function c(){let e=(0,d.useRouter)(),[t,r]=(0,s.useState)(!1);return(0,a.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center p-4 bg-white dark:bg-black",children:(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-md w-full bg-white dark:bg-neutral-900 rounded-xl shadow-lg p-8 border border-neutral-200 dark:border-neutral-800 text-center",children:[(0,a.jsx)("div",{className:"mb-6 flex justify-center",children:(0,a.jsx)("div",{className:"p-4 bg-neutral-100 dark:bg-neutral-800 rounded-full",children:(0,a.jsx)(n.A,{className:"h-12 w-12 text-[var(--brand-gold)]"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold mb-3 text-neutral-900 dark:text-neutral-100",children:"Product Unavailable"}),(0,a.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 mb-6",children:"This product is currently unavailable because the business is offline or in private mode. You cannot view or purchase this product at the moment."}),(0,a.jsxs)("div",{className:"relative group",children:[t&&(0,a.jsx)(i.P.div,{className:"absolute -inset-0.5 rounded-md blur-md",style:{background:"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))"},initial:{opacity:.7},animate:{opacity:[.7,.9,.7],boxShadow:["0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)","0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)","0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)"]},transition:{duration:2,repeat:1/0,repeatType:"reverse"}}),(0,a.jsx)(o.$,{onClick:()=>e.push("/discover"),className:"relative w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-8 py-6 h-12 rounded-md shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer",children:(0,a.jsxs)("span",{className:"relative z-10 flex items-center justify-center gap-2",children:["Discover Products",(0,a.jsx)(l.A,{className:"h-5 w-5"})]})})]})]})})}},71463:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(60687),s=r(96241);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",e),...t})}},72978:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,generateMetadata:()=>m});var a=r(37413),s=r(39916),i=r(27628),n=r(3947),l=r(2052),o=r(32032),d=r(38612);let c=e=>{switch(e.plan_id){case"free":default:return"free";case"growth":return"growth";case"pro":return"pro";case"enterprise":return"enterprise";case"basic":return"basic"}},u=()=>!0;async function p({params:e}){let{cardSlug:t,productSlug:r}=await e,{data:p,error:m}=await (0,i.f)(t);if((m||!p)&&(console.error(`Error fetching profile for slug ${t}:`,m),(0,s.notFound)()),"online"!==p.status)return console.log(`Business profile ${t} is not online (status: ${p.status}).`),(0,a.jsx)(d.default,{});let{data:x}=await (0,n.Qe)(r,p.id);x||(0,s.notFound)();let{data:b}=await (0,n.fy)(p.id),{data:h}=await (0,n.cz)(p.id,x.id),{data:f}=await (0,n.lo)(p.id),g=c(p),v=null;if(u())try{let e=await (0,o.createClient)(),{count:t,error:r}=await e.from("custom_ad_targets").select("*",{count:"exact",head:!0});if(null===t||r)if(p.pincode){let{data:t}=await e.from("custom_ads").select("ad_image_url, ad_link_url").eq("is_active",!0).or(`targeting_locations.eq.'"global"',targeting_locations.cs.'["${p.pincode}"]'`).order("created_at",{ascending:!1}).limit(1).maybeSingle();v=t?{type:"custom",imageUrl:t.ad_image_url,linkUrl:t.ad_link_url}:null}else{let{data:t}=await e.from("custom_ads").select("ad_image_url, ad_link_url").eq("is_active",!0).eq("targeting_locations",'"global"').order("created_at",{ascending:!1}).limit(1).maybeSingle();v=t?{type:"custom",imageUrl:t.ad_image_url,linkUrl:t.ad_link_url}:null}else{let t=p.pincode||"999999",{data:r,error:a}=await e.rpc("get_ad_for_pincode",{target_pincode:t});r&&r.length>0?v={type:"custom",imageUrl:r[0].ad_image_url,linkUrl:r[0].ad_link_url}:(a&&console.error(`Error fetching ad for pincode ${t}:`,a),v=null)}}catch(e){console.error("Error fetching custom ad:",e),v=null}return(0,a.jsx)(l.default,{product:x,variants:x.variants||[],businessSlug:t,businessName:b?.business_name||p.business_name||"",whatsappNumber:b?.whatsapp_number||null,phoneNumber:b?.phone||null,businessProducts:h||[],otherBusinessProducts:f||[],topAdData:v,businessCustomAd:p.custom_ads,userPlan:g})}async function m({params:e}){let{cardSlug:t,productSlug:r}=await e,a=`http://localhost:3000/${t}/product/${r}`,{data:l,error:o}=await (0,i.f)(t);(o||!l)&&(0,s.notFound)();let{data:d}=await (0,n.Qe)(r,l.id),{data:c}=await (0,n.fy)(l.id);d||(0,s.notFound)();let u=c?.business_name||l.business_name||"Business",p=d.name||"Product",m=d.description||`${p} by ${u}`;if(d&&d.variants&&d.variants.length>0){let e=new Set;if(d.variants.forEach(t=>{Object.keys(t.variant_values||{}).forEach(t=>e.add(t))}),e.size>0){let t=Array.from(e).join(", ");m+=` Available in multiple ${t} options.`}}return{title:`${p} | ${u}`,description:m.substring(0,160),openGraph:{title:`${p} | ${u}`,description:m.substring(0,160),url:a,images:d.image_url?[{url:d.image_url,width:1200,height:630,alt:p}]:void 0,type:"website"},twitter:{card:"summary_large_image",title:`${p} | ${u}`,description:m.substring(0,160),images:d.image_url?[d.image_url]:void 0}}}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81620:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},81630:e=>{"use strict";e.exports=require("http")},84404:(e,t,r)=>{"use strict";r.d(t,{A7:()=>x,FN:()=>p,Oj:()=>h,Q8:()=>b,Wk:()=>m});var a=r(60687),s=r(43210),i=r(53562),n=r(28559),l=r(70334),o=r(96241),d=r(24934);let c=s.createContext(null);function u(){let e=s.useContext(c);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}function p({orientation:e="horizontal",opts:t,setApi:r,plugins:n,className:l,children:d,...u}){let[p,m]=(0,i.A)({...t,axis:"horizontal"===e?"x":"y"},n),[x,b]=s.useState(!1),[h,f]=s.useState(!1),g=s.useCallback(e=>{e&&(b(e.canScrollPrev()),f(e.canScrollNext()))},[]),v=s.useCallback(()=>{m?.scrollPrev()},[m]),y=s.useCallback(()=>{m?.scrollNext()},[m]),_=s.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),v()):"ArrowRight"===e.key&&(e.preventDefault(),y())},[v,y]);return s.useEffect(()=>{m&&r&&r(m)},[m,r]),s.useEffect(()=>{if(m)return g(m),m.on("reInit",g),m.on("select",g),()=>{m?.off("select",g)}},[m,g]),(0,a.jsx)(c.Provider,{value:{carouselRef:p,api:m,opts:t,orientation:e||(t?.axis==="y"?"vertical":"horizontal"),scrollPrev:v,scrollNext:y,canScrollPrev:x,canScrollNext:h},children:(0,a.jsx)("div",{onKeyDownCapture:_,className:(0,o.cn)("relative",l),role:"region","aria-roledescription":"carousel","data-slot":"carousel",...u,children:d})})}function m({className:e,...t}){let{carouselRef:r,orientation:s}=u();return(0,a.jsx)("div",{ref:r,className:"overflow-hidden","data-slot":"carousel-content",children:(0,a.jsx)("div",{className:(0,o.cn)("flex","horizontal"===s?"-ml-4":"-mt-4 flex-col",e),...t})})}function x({className:e,...t}){let{orientation:r}=u();return(0,a.jsx)("div",{role:"group","aria-roledescription":"slide","data-slot":"carousel-item",className:(0,o.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===r?"pl-4":"pt-4",e),...t})}function b({className:e,variant:t="outline",size:r="icon",...s}){let{orientation:i,scrollPrev:l,canScrollPrev:c}=u();return(0,a.jsxs)(d.$,{"data-slot":"carousel-previous",variant:t,size:r,className:(0,o.cn)("absolute size-8 rounded-full","horizontal"===i?"top-1/2 -left-12 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:l,...s,children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}function h({className:e,variant:t="outline",size:r="icon",...s}){let{orientation:i,scrollNext:n,canScrollNext:c}=u();return(0,a.jsxs)(d.$,{"data-slot":"carousel-next",variant:t,size:r,className:(0,o.cn)("absolute size-8 rounded-full","horizontal"===i?"top-1/2 -right-12 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:n,...s,children:[(0,a.jsx)(l.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Next slide"})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96882:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,9398,4386,6724,2997,1107,7065,1753,3703,4891,3037,6177,4685],()=>r(9009));module.exports=a})();