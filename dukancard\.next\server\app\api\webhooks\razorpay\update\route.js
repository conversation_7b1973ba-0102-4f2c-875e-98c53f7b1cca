(()=>{var e={};e.id=7267,e.ids=[5453,7267],e.modules={2331:(e,r,s)=>{"use strict";s.d(r,{HD:()=>n,Ih:()=>a,OB:()=>o,Xz:()=>c,ju:()=>i});var t=s(95453);async function o(e,r){try{let s=(0,t.bG)(),o=await fetch(`${t.ST}/accounts/${e}/webhooks`,{method:"POST",headers:s,body:JSON.stringify(r)}),n=await o.json();if(!o.ok)return console.error("[RAZORPAY] Error creating webhook:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY] Exception creating webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function n(e,r){try{let s=(0,t.bG)(),o="";if(r){let e=new URLSearchParams;void 0!==r.from&&e.append("from",r.from.toString()),void 0!==r.to&&e.append("to",r.to.toString()),void 0!==r.count&&e.append("count",r.count.toString()),void 0!==r.skip&&e.append("skip",r.skip.toString());let s=e.toString();s&&(o=`?${s}`)}let n=await fetch(`${t.ST}/accounts/${e}/webhooks${o}`,{method:"GET",headers:s}),a=await n.json();if(!n.ok)return console.error("[RAZORPAY] Error fetching webhooks:",a),{success:!1,error:a};return{success:!0,data:a.items}}catch(e){return console.error("[RAZORPAY] Exception fetching webhooks:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function a(e,r){try{let s=(0,t.bG)(),o=await fetch(`${t.ST}/accounts/${e}/webhooks/${r}`,{method:"GET",headers:s}),n=await o.json();if(!o.ok)return console.error("[RAZORPAY] Error fetching webhook:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY] Exception fetching webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function c(e,r,s){try{if(!s.url)return{success:!1,error:"URL is required for updating a webhook"};if(!s.events||!Array.isArray(s.events)||0===s.events.length)return{success:!1,error:"Events array is required for updating a webhook"};let o=(0,t.bG)(),n=await fetch(`${t.ST}/accounts/${e}/webhooks/${r}`,{method:"PATCH",headers:o,body:JSON.stringify(s)}),a=await n.json();if(!n.ok)return console.error("[RAZORPAY] Error updating webhook:",a),{success:!1,error:a};return{success:!0,data:a}}catch(e){return console.error("[RAZORPAY] Exception updating webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function i(e,r){try{let s=(0,t.bG)(),o=await fetch(`${t.ST}/accounts/${e}/webhooks/${r}`,{method:"DELETE",headers:s});if(!o.ok){let e;try{e=await o.json()}catch(r){e={error:o.statusText}}return console.error("[RAZORPAY] Error deleting webhook:",e),{success:!1,error:e}}return{success:!0}}catch(e){return console.error("[RAZORPAY] Exception deleting webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20147:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var t={};s.r(t),s.d(t,{PATCH:()=>u});var o=s(96559),n=s(48088),a=s(37719),c=s(32190),i=s(2331);async function u(e){try{let{account_id:r,webhook_id:s,url:t,events:o}=await e.json();if(!r)return c.NextResponse.json({success:!1,message:"Missing account_id parameter"},{status:400});if(!s)return c.NextResponse.json({success:!1,message:"Missing webhook_id parameter"},{status:400});if(!t)return c.NextResponse.json({success:!1,message:"Missing url parameter"},{status:400});if(!o||!Array.isArray(o)||0===o.length)return c.NextResponse.json({success:!1,message:"Missing or invalid events parameter"},{status:400});let n=await (0,i.Xz)(r,s,{url:t,events:o});if(!n.success)return c.NextResponse.json({success:!1,error:n.error},{status:400});return c.NextResponse.json({success:!0,data:n.data},{status:200})}catch(e){return console.error("[RAZORPAY_WEBHOOK_UPDATE] Error updating webhook:",e),c.NextResponse.json({success:!1,message:"Error updating webhook",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/webhooks/razorpay/update/route",pathname:"/api/webhooks/razorpay/update",filename:"route",bundlePath:"app/api/webhooks/razorpay/update/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\update\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:f}=p;function g(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},95453:(e,r,s)=>{"use strict";s.d(r,{ST:()=>n,bG:()=>c,t6:()=>i});var t=s(55511),o=s.n(t);let n="https://api.razorpay.com/v2",a=()=>{let e,r;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",r=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!r)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!r,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:r}},c=()=>{let{keyId:e,keySecret:r}=a(),s=Buffer.from(`${e}:${r}`).toString("base64");return{Authorization:`Basic ${s}`,"Content-Type":"application/json"}},i=(e,r,s)=>{try{let t=o().createHmac("sha256",s).update(e).digest("hex");return o().timingSafeEqual(Buffer.from(r),Buffer.from(t))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580],()=>s(20147));module.exports=t})();