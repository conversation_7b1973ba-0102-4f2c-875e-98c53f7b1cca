"use strict";exports.id=5569,exports.ids=[301,5432,5447,5569,7249],exports.modules={301:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{handleSubscriptionCompleted:()=>l});var n=t(32032),r=t(65193),a=t(28485),o=t(94230),c=e([a]);async function l(e,s,t){let i=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription completed: ${c}`);let l=(0,r.extractWebhookTimestamp)(e);i={subscriptionId:c,eventType:"subscription.completed",eventId:t||`completed_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let u=await a.webhookProcessor.processWebhookEvent(i);if(!u.shouldProcess)return{success:u.success,message:u.message};let p=await (0,n.createClient)(),{data:d,error:_}=await p.from("payment_subscriptions").select("subscription_status, plan_id, business_profile_id").eq("razorpay_subscription_id",c).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${c}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!d)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${c}, skipping completion processing`),{success:!0,message:"No subscription found to complete"};console.log(`[RAZORPAY_WEBHOOK] Completing subscription ${c}, downgrading to free plan`);let b=new Date().toISOString(),O=await (0,o.M)({subscription_id:c,business_profile_id:d.business_profile_id,subscription_status:"active",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",subscription_start_date:b,cancelled_at:b,razorpay_subscription_id:null,razorpay_customer_id:null,razorpay_plan_id:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,updated_at:b}});if(O.success)return await a.webhookProcessor.markEventAsSuccess(i.eventId,"Subscription completed and downgraded to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully completed subscription ${c} and downgraded to free plan`),{success:!0,message:"Subscription completed and downgraded to free plan"};return await a.webhookProcessor.markEventAsFailed(i.eventId,O.message),console.error(`[RAZORPAY_WEBHOOK] Failed to complete subscription ${c}:`,O.message),O}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription completed:",e),{success:!1,message:`Error handling subscription completed: ${e instanceof Error?e.message:String(e)}`}}}a=(c.then?(await c)():c)[0],i()}catch(e){i(e)}})},5432:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{handleSubscriptionHalted:()=>l});var n=t(32032),r=t(65193),a=t(28485),o=t(94230),c=e([a]);async function l(e,s,t){let i=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription halted: ${c}`);let l=(0,r.extractWebhookTimestamp)(e);i={subscriptionId:c,eventType:"subscription.halted",eventId:t||`halted_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let u=await a.webhookProcessor.processWebhookEvent(i);if(!u.shouldProcess)return{success:u.success,message:u.message};let p=await (0,n.createClient)(),{data:d,error:_}=await p.from("payment_subscriptions").select("subscription_status, plan_id, plan_cycle, business_profile_id").eq("razorpay_subscription_id",c).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${c}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!d)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${c}, skipping halted processing`),{success:!0,message:"No subscription found to halt"};if("free"===d.plan_id||(0,r.isTerminalStatus)(d.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription ${c} is in terminal state (plan_id: ${d.plan_id}, status: ${d.subscription_status}), skipping halted update`),{success:!0,message:"Subscription is in terminal state, skipping halted update"};console.log(`[RAZORPAY_WEBHOOK] Halting subscription ${c}, preserving original plan ${d.plan_id}/${d.plan_cycle}`);let b=new Date().toISOString(),O=await (0,o.M)({subscription_id:c,business_profile_id:d.business_profile_id,subscription_status:"halted",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",original_plan_id:d.plan_id,original_plan_cycle:d.plan_cycle,subscription_paused_at:b,updated_at:b}});if(O.success)return await a.webhookProcessor.markEventAsSuccess(i.eventId,"Subscription halted and temporarily downgraded to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully halted subscription ${c} with original plan preserved`),{success:!0,message:"Subscription halted and temporarily downgraded to free plan"};return await a.webhookProcessor.markEventAsFailed(i.eventId,O.message),console.error(`[RAZORPAY_WEBHOOK] Failed to halt subscription ${c}:`,O.message),O}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription halted:",e),{success:!1,message:`Error handling subscription halted: ${e instanceof Error?e.message:String(e)}`}}}a=(c.then?(await c)():c)[0],i()}catch(e){i(e)}})},10567:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{h:()=>l});var n=t(11337),r=t(32032),a=t(65193),o=t(28485),c=e([o]);async function l(e,s,i){let c=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let l=s.entity,u=l.id;console.log(`[RAZORPAY_WEBHOOK] Subscription authenticated: ${u}`);let p=(0,a.extractWebhookTimestamp)(e);c={subscriptionId:u,eventType:"subscription.authenticated",eventId:i||`auth_${u}_${Date.now()}`,payload:e,webhookTimestamp:p};let d=await o.webhookProcessor.processWebhookEvent(c);if(!d.shouldProcess)return{success:d.success,message:d.message};let _=await (0,r.createClient)(),{data:b,error:O}=await _.from("payment_subscriptions").select("cancelled_at, plan_id, subscription_status, last_webhook_timestamp").eq("razorpay_subscription_id",u).maybeSingle();if(O)console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${u}:`,O);else if(b){if(b.subscription_status===a.SUBSCRIPTION_STATUS.TRIAL&&"free"!==b.plan_id&&b.cancelled_at){if(console.warn(`[CANCELLATION_CHECK] Subscription ${u} was already cancelled:`),console.warn(`  Current status: ${b.subscription_status} (trial indicates cancellation)`),console.warn(`  Current plan: ${b.plan_id} (preserved from cancellation)`),console.warn(`  Authenticated webhook timestamp: ${p} (${new Date(1e3*p).toISOString()})`),!b.last_webhook_timestamp)return console.warn("  ENTERPRISE DECISION: Rejecting authenticated webhook - subscription is in cancelled state (no previous webhook)"),{success:!0,message:"ENTERPRISE PROTECTION: Authenticated webhook rejected - subscription is in cancelled trial state"};{let e=new Date(b.last_webhook_timestamp).getTime()/1e3,s=p-e;return console.warn(`  Last webhook timestamp: ${e} (${new Date(1e3*e).toISOString()})`),console.warn(`  Time difference: ${s} seconds`),console.warn("  ENTERPRISE DECISION: Rejecting authenticated webhook to preserve cancellation state"),{success:!0,message:`ENTERPRISE PROTECTION: Authenticated webhook rejected - subscription was already cancelled and is in trial state with plan ${b.plan_id}`}}}if("free"===b.plan_id||(0,a.isTerminalStatus)(b.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription ${u} is in terminal state (plan_id: ${b.plan_id}, status: ${b.subscription_status}), skipping authentication`),{success:!0,message:"Subscription is in terminal state, skipping authentication"};b.subscription_status===a.SUBSCRIPTION_STATUS.TRIAL&&"free"===b.plan_id&&console.log(`[RAZORPAY_WEBHOOK] Fresh trial user ${u} authenticating subscription - this is allowed`)}let g=l.notes?.business_profile_id||l.notes?.user_id,m=l.notes?.old_subscription_id;if(m&&console.log(`[RAZORPAY_WEBHOOK] Plan switch detected. Old subscription ${m} will be cancelled when new subscription becomes active`),g){console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Checking for existing authenticated subscriptions for business ${g} before processing Plan B (${u})`);let{data:e,error:s}=await _.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",g).eq("subscription_status",n.SO._AUTHENTICATED).neq("razorpay_subscription_id",u);if(s)console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error finding existing authenticated subscriptions for business ${g}:`,s),console.log("[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite find error");else if(e&&e.length>0)for(let s of(console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Found ${e.length} existing authenticated subscription(s) for business ${g}.`),e)){console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Attempting to cancel previously authenticated Plan A: ${s.razorpay_subscription_id}`);let{cancelSubscription:e}=await Promise.all([t.e(1546),t.e(5453)]).then(t.bind(t,31546)),i=await e(s.razorpay_subscription_id,!1);if(i.success){console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Successfully triggered Razorpay cancellation for Plan A (${s.razorpay_subscription_id}).`);let e=new Date().toISOString(),{error:t}=await _.from("payment_subscriptions").update({subscription_status:a.SUBSCRIPTION_STATUS.CANCELLED,cancellation_requested_at:e,cancelled_at:e,updated_at:e}).eq("razorpay_subscription_id",s.razorpay_subscription_id);t?(console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error updating DB record for cancelled Plan A (${s.razorpay_subscription_id}):`,t),console.log("[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite DB update error for Plan A.")):console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Successfully updated DB record for cancelled Plan A (${s.razorpay_subscription_id}).`)}else console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error cancelling authenticated Plan A (${s.razorpay_subscription_id}) via Razorpay:`,i.error),console.log("[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite Razorpay cancellation error for Plan A.")}else console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] No other existing authenticated subscriptions found for business ${g}.`)}let A=l.start_at?new Date(1e3*l.start_at).toISOString():l.current_start?new Date(1e3*l.current_start).toISOString():null;console.log(`[SUBSCRIPTION_AUTHENTICATED] Using start date: ${A}, start_at: ${l.start_at}, current_start: ${l.current_start}`);let y=null;try{let e=l.notes?.last_payment_id;if(e){let{getPayment:s}=await t.e(4976).then(t.bind(t,94976)),i=await s(e);i.success&&i.data?y=i.data.method:console.error(`[RAZORPAY_WEBHOOK] Error fetching payment ${e}:`,i.error)}}catch(e){console.error("[RAZORPAY_WEBHOOK] Error fetching payment method:",e)}let h=null,{data:R,error:E}=await _.from("payment_subscriptions").select("subscription_status, subscription_expiry_time").eq("razorpay_subscription_id",u).maybeSingle();E?console.error(`[RAZORPAY_WEBHOOK] Error fetching current subscription state for ${u} during trial check:`,E):R&&R.subscription_status===a.SUBSCRIPTION_STATUS.TRIAL&&(h=R.subscription_expiry_time,console.log(`[RAZORPAY_WEBHOOK] Subscription ${u} is currently in trial. Preserving trial expiry: ${h}`));let f={subscription_start_date:A,subscription_expiry_time:h??(l.current_end?new Date(1e3*l.current_end).toISOString():null),subscription_charge_time:l.charge_at?new Date(1e3*l.charge_at).toISOString():null,razorpay_customer_id:l.customer_id||null,cancellation_requested_at:null};y&&(f.last_payment_method=y),console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Updating Plan B subscription ${u} with data:`,f);let S=await o.webhookProcessor.updateSubscriptionStatus(u,a.SUBSCRIPTION_STATUS.AUTHENTICATED,f,p);return console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Result of updating Plan B (${u}) in DB:`,S),S.success?await o.webhookProcessor.markEventAsSuccess(c.eventId,S.message):await o.webhookProcessor.markEventAsFailed(c.eventId,S.message),S}catch(s){console.error("[RAZORPAY_WEBHOOK] Error handling subscription authenticated:",s);let e=`Error handling subscription authenticated: ${s instanceof Error?s.message:String(s)}`;return c&&await o.webhookProcessor.markEventAsFailed(c.eventId,e),{success:!1,message:e}}}o=(c.then?(await c)():c)[0],i()}catch(e){i(e)}})},14055:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{V:()=>l});var n=t(11337),r=t(65193),a=t(32032),o=t(28485),c=e([o]);async function l(e,s,t){let i=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription pending: ${c}`);let l=(0,r.extractWebhookTimestamp)(e);i={subscriptionId:c,eventType:"subscription.pending",eventId:t||`pending_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let u=await o.webhookProcessor.processWebhookEvent(i);if(!u.shouldProcess)return{success:u.success,message:u.message};let p=await (0,a.createClient)(),{data:d,error:_}=await p.from("payment_subscriptions").select("cancelled_at, plan_id, subscription_status").eq("razorpay_subscription_id",c).maybeSingle();if(_)console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${c}:`,_);else if(d){let e="free"===d.plan_id||(0,r.isTerminalStatus)(d.subscription_status),s="authenticated"===d.subscription_status&&d.cancelled_at;if(e&&!s)return console.log(`[RAZORPAY_WEBHOOK] Subscription ${c} is in terminal state (plan_id: ${d.plan_id}, status: ${d.subscription_status}), skipping pending update`),{success:!0,message:"Subscription is in terminal state, skipping pending update"}}return await (0,r.nV)(p,c,n.SO._PENDING,{})}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription pending:",e),{success:!1,message:`Error handling subscription pending: ${e instanceof Error?e.message:String(e)}`}}}o=(c.then?(await c)():c)[0],i()}catch(e){i(e)}})},28539:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{J:()=>c});var n=t(32032),r=t(65193),a=t(28485),o=e([a]);async function c(e,s,i){let o=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=s.entity,l=c.id;console.log(`[RAZORPAY_WEBHOOK] Subscription activated: ${l}`);let u=(0,r.extractWebhookTimestamp)(e);o={subscriptionId:l,eventType:"subscription.activated",eventId:i||`activated_${l}_${Date.now()}`,payload:e,webhookTimestamp:u};let p=await a.webhookProcessor.processWebhookEvent(o);if(!p.shouldProcess)return{success:p.success,message:p.message};let d=(0,n.createClient)(),{isTerminalStatus:_}=await Promise.resolve().then(t.bind(t,65193)),b=await d,{data:O,error:g}=await b.from("payment_subscriptions").select("cancelled_at, plan_id, subscription_status").eq("razorpay_subscription_id",l).maybeSingle();if(g)console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${l}:`,g);else if(O){if("halted"===O.subscription_status)console.log(`[RAZORPAY_WEBHOOK] Halted subscription ${l} can be reactivated - this is allowed`);else if("free"===O.plan_id&&"active"===O.subscription_status||_(O.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription is in terminal state (${O.subscription_status}, plan: ${O.plan_id}), ignoring subscription.activated event`),{success:!0,message:`Subscription is in terminal state (${O.subscription_status}, plan: ${O.plan_id}), ignoring subscription.activated event`};O.subscription_status===r.SUBSCRIPTION_STATUS.TRIAL&&console.log(`[RAZORPAY_WEBHOOK] Trial user ${l} activating subscription - this is allowed even with cancelled_at timestamp`)}let{data:m,error:A}=await b.from("payment_subscriptions").select("original_plan_id, original_plan_cycle, subscription_paused_at, plan_cycle").eq("razorpay_subscription_id",l).maybeSingle();A&&console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for resume check ${l}:`,A);let y=!1,h=null,R=null;m&&m.subscription_paused_at&&m.original_plan_id&&(console.log(`[RAZORPAY_WEBHOOK] Subscription ${l} is being resumed. Original plan: ${m.original_plan_id}, Original cycle: ${m.original_plan_cycle}`),y=!0,h=m.original_plan_id,R=m.original_plan_cycle);let E=c.notes?.is_plan_switch==="true",f=c.notes?.old_subscription_id,S=c.notes?.cancelled_old_subscription==="true";if(console.log("[RAZORPAY_WEBHOOK] Plan switch detection:",{isPlanSwitch:E,oldSubscriptionId:f,oldSubscriptionAlreadyCancelled:S,isResumeFlow:y,subscriptionNotes:c.notes}),E&&!y)if(console.log(`[RAZORPAY_WEBHOOK] ✅ Processing plan switch from subscription ${f} to ${l}`),f&&!S){console.log(`[RAZORPAY_WEBHOOK] Cancelling old subscription ${f}`);let{cancelSubscription:e}=await Promise.all([t.e(1546),t.e(5453)]).then(t.bind(t,31546)),s=await e(f,!1);if(!s.success)return console.error(`[RAZORPAY_WEBHOOK] CRITICAL: Failed to cancel old subscription ${f}:`,s.error),{success:!1,message:`Critical error: Failed to cancel old subscription ${f}. This could result in double billing.`};{console.log(`[RAZORPAY_WEBHOOK] Successfully cancelled old subscription ${f}`),console.log(`[RAZORPAY_WEBHOOK] Updating old subscription record ${f} with new subscription ID ${l}`);let{error:e}=await b.from("payment_subscriptions").update({razorpay_subscription_id:l,updated_at:new Date().toISOString()}).eq("razorpay_subscription_id",f);if(e)return console.error(`[RAZORPAY_WEBHOOK] Error updating old subscription record ${f} with new ID ${l}:`,e),{success:!1,message:"Failed to update old subscription record with new subscription ID"};console.log(`[RAZORPAY_WEBHOOK] Successfully updated old subscription record ${f} with new subscription ID ${l}`)}}else f&&S&&console.log(`[RAZORPAY_WEBHOOK] Old subscription ${f} was already cancelled, skipping cancellation`);let w="card";try{let e=c.notes?.last_payment_id;if(e){let{getPaymentDetails:s}=await t.e(4976).then(t.bind(t,94976)),i=await s(e);i.success&&i.data?w=i.data.method:console.error(`[RAZORPAY_WEBHOOK] Error fetching payment ${e}:`,i.error)}}catch(e){console.error("[RAZORPAY_WEBHOOK] Error fetching payment method:",e)}let P=c.notes?.plan_type,$=c.notes?.plan_cycle,B={subscription_start_date:new Date(1e3*c.current_start).toISOString(),subscription_expiry_time:c.current_end?new Date(1e3*c.current_end).toISOString():null,subscription_charge_time:c.charge_at?new Date(1e3*c.charge_at).toISOString():null,razorpay_customer_id:c.customer_id||null,last_payment_method:w,cancellation_requested_at:null,razorpay_plan_id:c.plan_id};y&&h?(console.log(`[RAZORPAY_WEBHOOK] Restoring original plan ${h} for subscription ${l}`),B.plan_id=h,R?B.plan_cycle=R:(B.plan_cycle=$||"monthly",console.warn(`[RAZORPAY_WEBHOOK] Original plan cycle not available or null for resume of ${l}. Using fallback/notes: ${B.plan_cycle}`)),B.subscription_paused_at=null,B.original_plan_id=null,B.original_plan_cycle=null):(B.plan_id=P,B.plan_cycle=$),console.log(`[RAZORPAY_WEBHOOK] Updating subscription ${l} with data:`,B),console.log(`[RAZORPAY_WEBHOOK] Updating subscription ${l} with status ACTIVE`);let I=await a.webhookProcessor.updateSubscriptionStatus(l,r.SUBSCRIPTION_STATUS.ACTIVE,B,u);return I.success?await a.webhookProcessor.markEventAsSuccess(o.eventId,I.message):await a.webhookProcessor.markEventAsFailed(o.eventId,I.message),I}catch(s){console.error("[RAZORPAY_WEBHOOK] Error handling subscription activated:",s);let e=`Error handling subscription activated: ${s instanceof Error?s.message:String(s)}`;return o&&await a.webhookProcessor.markEventAsFailed(o.eventId,e),{success:!1,message:e}}}a=(o.then?(await o)():o)[0],i()}catch(e){i(e)}})},30260:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{Y:()=>c});var n=t(32032),r=t(65193),a=t(28485),o=e([a]);async function c(e,s,t){let i=null;try{let s=e.payload.payment,o=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let c=s.entity.subscription_id;if(!c)return console.error("[RAZORPAY_WEBHOOK] Subscription ID not found in payment data"),{success:!1,message:"Subscription ID not found in payment data"};let l=null;o&&o.entity&&(l=o.entity),console.log(`[RAZORPAY_WEBHOOK] Subscription charged: ${c}`);let u=(0,r.extractWebhookTimestamp)(e);i={subscriptionId:c,eventType:"subscription.charged",eventId:t||`charged_${c}_${Date.now()}`,payload:e,webhookTimestamp:u};let p=await a.webhookProcessor.processWebhookEvent(i);if(!p.shouldProcess)return{success:p.success,message:p.message};let d=await (0,n.createClient)(),{data:_,error:b}=await d.from("payment_subscriptions").select("cancelled_at, plan_id, subscription_status").eq("razorpay_subscription_id",c).maybeSingle();if(b)console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${c}:`,b);else if(_){if("free"===_.plan_id||(0,r.isTerminalStatus)(_.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription ${c} is in terminal state (plan_id: ${_.plan_id}, status: ${_.subscription_status}), skipping charge processing`),{success:!0,message:"Subscription is in terminal state, skipping charge processing"};_.subscription_status===r.SUBSCRIPTION_STATUS.TRIAL&&console.log(`[RAZORPAY_WEBHOOK] Trial user ${c} processing charge - this is allowed even with cancelled_at timestamp`)}let O=l?.notes?.plan_type||s.entity.notes?.plan_type,g=l?.notes?.plan_cycle||s.entity.notes?.plan_cycle,m=await a.webhookProcessor.updateSubscriptionStatus(c,r.SUBSCRIPTION_STATUS.ACTIVE,{last_payment_id:s.entity.id,last_payment_date:new Date(1e3*s.entity.created_at).toISOString(),last_payment_method:s.entity.method,subscription_start_date:l?.current_start?new Date(1e3*l.current_start).toISOString():null,subscription_expiry_time:l?.current_end?new Date(1e3*l.current_end).toISOString():null,subscription_charge_time:l?.charge_at?new Date(1e3*l.charge_at).toISOString():null,razorpay_customer_id:l?.customer_id||null,cancellation_requested_at:null,plan_id:O,plan_cycle:g,razorpay_plan_id:l?.plan_id},u);return m.success?await a.webhookProcessor.markEventAsSuccess(i.eventId,m.message):await a.webhookProcessor.markEventAsFailed(i.eventId,m.message),m}catch(s){console.error("[RAZORPAY_WEBHOOK] Error handling subscription charged:",s);let e=`Error handling subscription charged: ${s instanceof Error?s.message:String(s)}`;return i&&await a.webhookProcessor.markEventAsFailed(i.eventId,e),{success:!1,message:e}}}a=(o.then?(await o)():o)[0],i()}catch(e){i(e)}})},47249:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{handleSubscriptionExpired:()=>l});var n=t(32032),r=t(65193),a=t(28485),o=t(94230),c=e([a]);async function l(e,s,t){let i=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription expired: ${c}`);let l=(0,r.extractWebhookTimestamp)(e);i={subscriptionId:c,eventType:"subscription.expired",eventId:t||`expired_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let u=await a.webhookProcessor.processWebhookEvent(i);if(!u.shouldProcess)return{success:u.success,message:u.message};let p=await (0,n.createClient)(),{data:d,error:_}=await p.from("payment_subscriptions").select("subscription_status, plan_id, business_profile_id").eq("razorpay_subscription_id",c).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${c}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!d)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${c}, skipping expiry processing`),{success:!0,message:"No subscription found to expire"};console.log(`[RAZORPAY_WEBHOOK] Transitioning expired subscription ${c} to free plan`);let b=new Date().toISOString(),O=await (0,o.M)({subscription_id:c,business_profile_id:d.business_profile_id,subscription_status:"active",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",subscription_start_date:b,subscription_expiry_time:null,subscription_charge_time:null,cancelled_at:b,razorpay_subscription_id:null,razorpay_customer_id:null,razorpay_plan_id:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,updated_at:b}});if(O.success)return await a.webhookProcessor.markEventAsSuccess(i.eventId,"Subscription expired and transitioned to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully transitioned subscription ${c} to free plan`),{success:!0,message:"Subscription expired and transitioned to free plan"};return await a.webhookProcessor.markEventAsFailed(i.eventId,O.message),console.error(`[RAZORPAY_WEBHOOK] Failed to transition subscription ${c} to free plan:`,O.message),O}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription expired:",e),{success:!1,message:`Error handling subscription expired: ${e instanceof Error?e.message:String(e)}`}}}a=(c.then?(await c)():c)[0],i()}catch(e){i(e)}})},75447:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{handleSubscriptionUpdated:()=>o});var n=t(65193),r=t(28485),a=e([r]);async function o(e,s,t){let i=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let a=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription updated: ${a}`);let o=(0,n.extractWebhookTimestamp)(e);i={subscriptionId:a,eventType:"subscription.updated",eventId:t||`updated_${a}_${Date.now()}`,payload:e,webhookTimestamp:o};let c=await r.webhookProcessor.processWebhookEvent(i);if(!c.shouldProcess)return{success:c.success,message:c.message};return{success:!0,message:"Subscription update acknowledged"}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription updated:",e),{success:!1,message:`Error handling subscription updated: ${e instanceof Error?e.message:String(e)}`}}}r=(a.then?(await a)():a)[0],i()}catch(e){i(e)}})}};