"use strict";exports.id=9317,exports.ids=[9317],exports.modules={69317:(r,e,o)=>{o.d(e,{getPostLoginRedirectPath:()=>s});async function s(r,e){try{let[o,s]=await Promise.all([r.from("customer_profiles").select("id").eq("id",e),r.from("business_profiles").select("id, business_slug").eq("id",e)]);if(o.error||s.error){if(console.error("[redirectAfterLogin] Supabase query error:",o.error,s.error),o.error?.code==="PGRST116"||s.error?.code==="PGRST116"||o.error?.message?.toLowerCase().includes("no rows")||s.error?.message?.toLowerCase().includes("no rows"))return"/choose-role";return"/?view=home"}if(o.data&&Array.isArray(o.data)&&o.data.length>0)return"/dashboard/customer";if(s.data&&Array.isArray(s.data)&&s.data.length>0){if(s.data[0].business_slug)return"/dashboard/business";return"/onboarding"}return"/choose-role"}catch(r){return console.error("[redirectAfterLogin] Unexpected error:",r),"/?view=home"}}}};