"use strict";exports.id=1659,exports.ids=[1659],exports.modules={711:(e,r,s)=>{s.d(r,{K:()=>n});var t=s(67218);s(79130);var i=s(5658),a=s(32032);async function n(e,r,s,t=1,o=20,c="created_desc",u){try{let n=await (0,a.createClient)();(0,i.HO)();let l=(t-1)*o,d=`
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year,
      custom_branding, custom_ads
    `,_=n.from("business_profiles").select("id",{count:"exact"}).eq("status","online"),f=n.from("business_profiles").select(d).eq("status","online");if(e){let r=e.trim();r&&(_=_.ilike("business_name",`%${r}%`),f=f.ilike("business_name",`%${r}%`))}r&&(_=_.eq("pincode",r),f=f.eq("pincode",r)),s&&(_=_.eq("locality",s),f=f.eq("locality",s)),u&&u.trim()&&(_=_.eq("business_category",u.trim()),f=f.eq("business_category",u.trim())),f=(f=(0,i.fx)(f,c)).range(l,l+o-1);let[p,b]=await Promise.all([_,f]),{count:g,error:y}=p,{data:m,error:h}=b;if(y)return console.error("Count Error:",y),{error:"Database error counting profiles."};if(h)return console.error("Data Error:",h),{error:"Database error fetching profiles."};if(!g||0===g||!m||0===m.length)return{data:[],count:0};let v=m.map(e=>e.id),{data:A,error:q}=await n.from("payment_subscriptions").select("business_profile_id, subscription_status, plan_id").in("business_profile_id",v).order("created_at",{ascending:!1});q&&console.error("Error fetching subscription data:",q);let w=(0,i.eJ)(A);return{data:m.map(e=>{let r=w.get(e.id)||{subscription_status:null,plan_id:null};return{...e,has_active_subscription:"active"===r.subscription_status,total_visits:0,today_visits:0,yesterday_visits:0,visits_7_days:0,visits_30_days:0,city_slug:null,state_slug:null,locality_slug:null,gallery:null,latitude:null,longitude:null,subscription_status:r.subscription_status,plan_id:r.plan_id}}),count:g}}catch(e){return console.error("Exception in getSecureBusinessProfiles:",e),{error:"An unexpected error occurred."}}}(0,s(17478).D)([n]),(0,t.A)(n,"7f1ff705112688da08e7c134e2df7387375812d40e",null)},5658:(e,r,s)=>{function t(e,r){switch(r){case"name_asc":return e.order("business_name",{ascending:!0});case"name_desc":return e.order("business_name",{ascending:!1});case"created_asc":return e.order("created_at",{ascending:!0});case"created_desc":default:return e.order("created_at",{ascending:!1});case"likes_asc":return e.order("total_likes",{ascending:!0});case"likes_desc":return e.order("total_likes",{ascending:!1});case"subscriptions_asc":return e.order("total_subscriptions",{ascending:!0});case"subscriptions_desc":return e.order("total_subscriptions",{ascending:!1});case"rating_asc":return e.order("average_rating",{ascending:!0});case"rating_desc":return e.order("average_rating",{ascending:!1})}}function i(){return new Date().toISOString()}function a(e){let r=new Map;return e&&e.forEach(e=>{r.has(e.business_profile_id)||r.set(e.business_profile_id,{subscription_status:e.subscription_status,plan_id:e.plan_id})}),r}s.d(r,{HO:()=>i,eJ:()=>a,fx:()=>t})},11597:(e,r,s)=>{s.d(r,{a:()=>a,h:()=>n});var t=s(67218);s(79130);var i=s(32032);async function a(e){try{let r=await (0,i.createClient)(),{data:{user:s},error:t}=await r.auth.getUser();if(t||!s)return{hasAccess:!1,error:"Authentication required"};let{data:a,error:n}=await r.from("business_profiles").select("id").eq("id",e).eq("id",s.id).maybeSingle();if(n)return console.error("Access check error:",n),{hasAccess:!1,error:"Error checking access"};return{hasAccess:!!a}}catch(e){return console.error("Exception in checkBusinessProfileAccess:",e),{hasAccess:!1,error:"An unexpected error occurred"}}}async function n(){try{let e=await (0,i.createClient)(),{data:{user:r},error:s}=await e.auth.getUser();if(s||!r)return{error:"Authentication required"};let{data:t,error:a}=await e.from("business_profiles").select("id").eq("id",r.id).maybeSingle();if(a)return console.error("Profile ID fetch error:",a),{error:"Error fetching profile ID"};if(!t)return{error:"Business profile not found"};return{profileId:t.id}}catch(e){return console.error("Exception in getCurrentUserBusinessProfileId:",e),{error:"An unexpected error occurred"}}}(0,s(17478).D)([a,n]),(0,t.A)(a,"4088dc18235d8e33be19d97fc9c00c650f82e08953",null),(0,t.A)(n,"00a8d295fac0c253c5942cbbd729c108ff5d62d666",null)},11659:(e,r,s)=>{s.d(r,{CU:()=>i.C,fE:()=>t.f});var t=s(27628);s(46317),s(711),s(72116),s(11597);var i=s(66352)},27628:(e,r,s)=>{s.d(r,{$:()=>n,f:()=>a});var t=s(67218);s(79130);var i=s(32032);async function a(e){if(!e)return{error:"Business slug is required."};try{let r=await (0,i.createClient)(),{data:s,error:t}=await r.from("business_profiles").select(`
        *,
        payment_subscriptions!business_profile_id (
          plan_id,
          subscription_status
        )
      `).eq("business_slug",e).maybeSingle();if(t)return console.error("Secure Fetch Error:",t),{error:`Failed to fetch business profile: ${t.message}`};if(!s)return{error:"Profile not found."};return{data:{...s,subscription_status:s.payment_subscriptions?.subscription_status||null,plan_id:s.payment_subscriptions?.plan_id||null}}}catch(e){return console.error("Exception in getSecureBusinessProfileBySlug:",e),{error:"An unexpected error occurred."}}}async function n(e){if(!e)return{error:"Business slug is required."};try{let r=await (0,i.createClient)(),{data:s,error:t}=await r.from("business_profiles").select(`
        *,
        products_services (
          id, name, description, base_price, discounted_price, is_available, image_url, created_at, updated_at, product_type
        )
      `).eq("business_slug",e).maybeSingle();if(t)return console.error("Secure Fetch Error:",t),{error:`Failed to fetch business profile: ${t.message}`};if(!s)return{error:"Profile not found."};return{data:{...s,products_services:s.products_services||[]}}}catch(e){return console.error("Exception in getSecureBusinessProfileWithProductsBySlug:",e),{error:"An unexpected error occurred."}}}(0,s(17478).D)([a,n]),(0,t.A)(a,"407031dcd9eee2e858cfbb811d905061dca994df13",null),(0,t.A)(n,"40274d19c8c06b8c99a2839744992d7bcecb2341c5",null)},46317:(e,r,s)=>{s.d(r,{C:()=>n,Z:()=>o});var t=s(67218);s(79130);var i=s(32032),a=s(5658);async function n(e,r,s=1,t=10,o="created_desc"){if(!e||Array.isArray(e)&&0===e.length)return{error:"At least one pincode is required."};let c=Array.isArray(e)?e:[e];try{let e=await (0,i.createClient)(),n=(s-1)*t,u=e.from("business_profiles").select("id",{count:"exact"}).in("pincode",c).eq("status","online");r&&u.eq("locality",r);let{count:l,error:d}=await u;if(d)return console.error("Count Error:",d),{error:"Database error counting profiles."};if(!l||0===l)return{data:[],count:0};let _=`
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year,
      custom_branding, custom_ads
    `,f=e.from("business_profiles").select(_).in("pincode",c).eq("status","online").range(n,n+t-1);r&&f.eq("locality",r),f=(0,a.fx)(f,o);let{data:p,error:b}=await f;if(b)return console.error("Query Error:",b),{error:"Database error fetching profiles."};return{data:p.map(e=>({...e,has_active_subscription:!1,total_visits:0,today_visits:0,yesterday_visits:0,visits_7_days:0,visits_30_days:0,city_slug:null,state_slug:null,locality_slug:null,gallery:null,latitude:null,longitude:null,subscription_status:null,plan_id:null})),count:l||0}}catch(e){return console.error("Exception in getSecureBusinessProfilesForDiscover:",e),{error:"An unexpected error occurred."}}}async function o(e,r,s="created_desc"){if(!e||Array.isArray(e)&&0===e.length)return{error:"At least one pincode is required."};let t=Array.isArray(e)?e:[e];try{let e=(await (0,i.createClient)()).from("business_profiles").select("id").in("pincode",t).eq("status","online");r&&e.eq("locality",r),e=(0,a.fx)(e,s);let{data:n,error:o}=await e;if(o)return console.error("Query Error:",o),{error:"Database error fetching profile IDs."};return{data:n.map(e=>e.id)}}catch(e){return console.error("Exception in getSecureBusinessProfileIdsForDiscover:",e),{error:"An unexpected error occurred."}}}(0,s(17478).D)([n,o]),(0,t.A)(n,"7c76d89983ca9f1290f11616f3bb631fd67faec14d",null),(0,t.A)(o,"70e4edcef42c86cf3a9ba8c209ba199083b0a30092",null)},66352:(e,r,s)=>{s.d(r,{C:()=>n});var t=s(67218);s(79130);var i=s(32032),a=s(5658);async function n(e,r=1,s=20,t="created_desc"){try{let n=await (0,i.createClient)();(0,a.HO)();let o=(r-1)*s,c=`
      id,
      business_name,
      logo_url,
      member_name,
      title,
      address_line,
      city,
      state,
      pincode,
      locality,
      phone,
      instagram_url,
      facebook_url,
      whatsapp_number,
      about_bio,
      status,
      business_slug,
      theme_color,
      delivery_info,
      business_hours,
      business_category,
      total_likes,
      total_subscriptions,
      average_rating,
      created_at,
      updated_at,
      trial_end_date,
      contact_email,
      established_year,
      custom_branding,
      custom_ads,
      has_active_subscription,
      total_visits,
      today_visits,
      yesterday_visits
    `,u=n.from("business_profiles").select(c,{count:"exact"}).eq("status","online");e.pincode?u=u.eq("pincode",e.pincode):e.city?u=u.eq("city",e.city):e.state&&(u=u.eq("state",e.state)),e.locality&&(u=u.ilike("locality",`%${e.locality}%`)),u=(u="created_desc"===t?u.order("created_at",{ascending:!1}):"created_asc"===t?u.order("created_at",{ascending:!0}):"name_asc"===t?u.order("business_name",{ascending:!0}):"name_desc"===t?u.order("business_name",{ascending:!1}):"rating_desc"===t?u.order("average_rating",{ascending:!1}):"likes_desc"===t?u.order("total_likes",{ascending:!1}):u.order("created_at",{ascending:!1})).range(o,o+s-1);let{data:l,count:d,error:_}=await u;if(_)return console.error("Error fetching businesses by location:",_),{error:"Database error fetching businesses."};return{data:l,count:d||0}}catch(e){return console.error("Unexpected error in getSecureBusinessProfilesByLocation:",e),{error:"An unexpected error occurred."}}}(0,s(17478).D)([n]),(0,t.A)(n,"788721cac1d66dc0019d95bd960521fed5169baa89",null)},72116:(e,r,s)=>{s.d(r,{E:()=>a});var t=s(67218);s(79130);var i=s(32032);async function a(){try{let e=await (0,i.createClient)(),{data:r,error:s}=await e.from("business_profiles").select("business_slug, updated_at").eq("status","online").not("business_slug","is",null);if(s)return{error:"Database error fetching profiles."};if(!r||0===r.length)return{data:[]};let t=new Map;return r.forEach(e=>{e.business_slug&&t.set(e.business_slug,{business_slug:e.business_slug,updated_at:e.updated_at})}),{data:Array.from(t.values())}}catch(e){return{error:"An unexpected error occurred."}}}(0,s(17478).D)([a]),(0,t.A)(a,"0056cc8508beee15907e2741a09bee834aa2799373",null)}};