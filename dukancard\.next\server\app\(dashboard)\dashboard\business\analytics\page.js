(()=>{var t={};t.id=1671,t.ids=[1671],t.modules={22:(t,e,r)=>{var n=r(75254),i=r(20623),o=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?o:"object"==typeof t?a(t)?i(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),i=r(658),o=r(30401),a=r(34772),c=r(17830),s=r(29395),u=r(12290),l="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=u(n),v=u(i),m=u(o),b=u(a),g=u(c),x=s;(n&&x(new n(new ArrayBuffer(1)))!=h||i&&x(new i)!=l||o&&x(o.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=d)&&(x=function(t){var e=s(t),r="[object Object]"==e?t.constructor:void 0,n=r?u(r):"";if(n)switch(n){case y:return h;case v:return l;case m:return f;case b:return p;case g:return d}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),i=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,o=e.length;null!=t&&r<o;)t=t[i(e[r++])];return r&&r==o?t:void 0}},1759:(t,e,r)=>{"use strict";r.d(e,{Fc:()=>u,TN:()=>f,XL:()=>l});var n=r(37413);r(61120);var i=r(75986);let o=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,a=i.$;var c=r(66819);let s=((t,e)=>r=>{var n;if((null==e?void 0:e.variants)==null)return a(t,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:c}=e,s=Object.keys(i).map(t=>{let e=null==r?void 0:r[t],n=null==c?void 0:c[t];if(null===e)return null;let a=o(e)||o(n);return i[t][a]}),u=r&&Object.entries(r).reduce((t,e)=>{let[r,n]=e;return void 0===n||(t[r]=n),t},{});return a(t,s,null==e||null==(n=e.compoundVariants)?void 0:n.reduce((t,e)=>{let{class:r,className:n,...i}=e;return Object.entries(i).every(t=>{let[e,r]=t;return Array.isArray(r)?r.includes({...c,...u}[e]):({...c,...u})[e]===r})?[...t,r,n]:t},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function u({className:t,variant:e,...r}){return(0,n.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,c.cn)(s({variant:e}),t),...r})}function l({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"alert-title",className:(0,c.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...e})}function f({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"alert-description",className:(0,c.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...e})}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),i=r(59467);t.exports=function(t,e){return null!=t&&i(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],c=e(a);if(null!=c&&(void 0===s?c==c&&!n(c):r(c,s)))var s=c,u=a}return u}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3956:(t,e,r)=>{Promise.resolve().then(r.bind(r,64522))},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),i=r(55048);t.exports=function(t){if(!i(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),i=r(34117),o=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=i(e=a(e))?o(e):void 0,c=r?r[0]:e.charAt(0),s=r?n(r,1).join(""):e.slice(1);return c[t]()+s}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},7383:(t,e,r)=>{var n=r(67009),i=r(32269),o=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(i(r)&&o(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),i=r(52931),o=r(32269);t.exports=function(t){return o(t)?n(t):i(t)}},8098:(t,e,r)=>{"use strict";r.r(e),r.d(e,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>n.B});var n=r(64275)},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10090:(t,e,r)=>{var n=r(80458),i=r(89624),o=r(47282),a=o&&o.isTypedArray;t.exports=a?i(a):n},10653:(t,e,r)=>{var n=r(21456),i=r(63979),o=r(7651);t.exports=function(t){return n(t,o,i)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new i(n,o||t,a),s=r?r+e:e;return t._events[s]?t._events[s].fn?t._events[s]=[t._events[s],c]:t._events[s].push(c):(t._events[s]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,i,o,a){var c=r?r+t:t;if(!this._events[c])return!1;var s,u,l=this._events[c],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(t,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,e),!0;case 3:return l.fn.call(l.context,e,n),!0;case 4:return l.fn.call(l.context,e,n,i),!0;case 5:return l.fn.call(l.context,e,n,i,o),!0;case 6:return l.fn.call(l.context,e,n,i,o,a),!0}for(u=1,s=Array(f-1);u<f;u++)s[u-1]=arguments[u];l.fn.apply(l.context,s)}else{var p,d=l.length;for(u=0;u<d;u++)switch(l[u].once&&this.removeListener(t,l[u].fn,void 0,!0),f){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,e);break;case 3:l[u].fn.call(l[u].context,e,n);break;case 4:l[u].fn.call(l[u].context,e,n,i);break;default:if(!s)for(p=1,s=Array(f-1);p<f;p++)s[p-1]=arguments[p];l[u].fn.apply(l[u].context,s)}}return!0},c.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var c=this._events[o];if(c.fn)c.fn!==e||i&&!c.once||n&&c.context!==n||a(this,o);else{for(var s=0,u=[],l=c.length;s<l;s++)(c[s].fn!==e||i&&!c[s].once||n&&c[s].context!==n)&&u.push(c[s]);u.length?this._events[o]=1===u.length?u[0]:u:a(this,o)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),i=r(55048),o=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,u=/^0o[0-7]+$/i,l=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(o(t))return a;if(i(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=i(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=s.test(t);return r||u.test(t)?l(t.slice(2),r?2:8):c.test(t)?a:+t}},11997:t=>{"use strict";t.exports=require("punycode")},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return i(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),i=r(27467);t.exports=function t(e,r,o,a,c){return e===r||(null!=e&&null!=r&&(i(e)||i(r))?n(e,r,o,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),i=r(46063),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},15909:(t,e,r)=>{var n=r(87506),i=r(66930),o=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(o||i),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),i=r(1707),o=r(22),a=r(54765),c=r(43378),s=r(89624),u=r(65727),l=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return i(e,1===t.length?t[0]:t)}:t}):[l];var p=-1;return e=n(e,s(o)),c(a(t,function(t,r,i){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return u(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),i=r(22),o=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var s=null==r?0:o(r);return s<0&&(s=a(c+s,0)),n(t,i(e,3),s)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),i=r.size;return r.set(t,e),this.size+=+(r.size!=i),this}},20540:(t,e,r)=>{var n=r(55048),i=r(70151),o=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var s,u,l,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=s,n=u;return s=u=void 0,h=e,f=t.apply(n,r)}function g(t){var r=t-d,n=t-h;return void 0===d||r>=e||r<0||v&&n>=l}function x(){var t,r,n,o=i();if(g(o))return w(o);p=setTimeout(x,(t=o-d,r=o-h,n=e-t,v?c(n,l-r):n))}function w(t){return(p=void 0,m&&s)?b(t):(s=u=void 0,f)}function O(){var t,r=i(),n=g(r);if(s=arguments,u=this,d=r,n){if(void 0===p)return h=t=d,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(d)}return void 0===p&&(p=setTimeout(x,e)),f}return e=o(e)||0,n(r)&&(y=!!r.leading,l=(v="maxWait"in r)?a(o(r.maxWait)||0,e):l,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,s=d=u=p=void 0},O.flush=function(){return void 0===p?f:w(i())},O}},20623:(t,e,r)=>{var n=r(15871),i=r(40491),o=r(2896),a=r(67619),c=r(34883),s=r(41132),u=r(46436);t.exports=function(t,e){return a(t)&&c(e)?s(u(t),e):function(r){var a=i(r,t);return void 0===a&&a===e?o(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}},21456:(t,e,r)=>{var n=r(41693),i=r(40542);t.exports=function(t,e,r){var o=e(t);return i(t)?o:n(o,r(t))}},21592:(t,e,r)=>{var n=r(42205),i=r(61837);t.exports=function(t,e){return n(i(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,o,a,c){var s=1&r,u=n(t),l=u.length;if(l!=n(e).length&&!s)return!1;for(var f=l;f--;){var p=u[f];if(!(s?p in e:i.call(e,p)))return!1}var d=c.get(t),h=c.get(e);if(d&&h)return d==e&&h==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=s;++f<l;){var m=t[p=u[f]],b=e[p];if(o)var g=s?o(b,m,p,e,t,c):o(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,o,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x!=w&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(t),c.delete(e),y}},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23026:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},23729:(t,e,r)=>{var n=r(22),i=r(32269),o=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!i(e)){var s=n(r,3);e=o(e),r=function(t){return s(c[t],t,c)}}var u=t(e,r,a);return u>-1?c[s?e[u]:u]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},26373:(t,e,r)=>{"use strict";r.d(e,{A:()=>s});var n=r(61120);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...t)=>t.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:c="",children:s,iconNode:u,...l},f)=>(0,n.createElement)("svg",{ref:f,...a,width:e,height:e,stroke:t,strokeWidth:i?24*Number(r)/Number(e):r,className:o("lucide",c),...l},[...u.map(([t,e])=>(0,n.createElement)(t,e)),...Array.isArray(s)?s:[s]])),s=(t,e)=>{let r=(0,n.forwardRef)(({className:r,...a},s)=>(0,n.createElement)(c,{ref:s,iconNode:e,className:o(`lucide-${i(t)}`,r),...a}));return r.displayName=`${t}`,r}},26919:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(26373).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},27006:(t,e,r)=>{var n=r(46328),i=r(99525),o=r(58276);t.exports=function(t,e,r,a,c,s){var u=1&r,l=t.length,f=e.length;if(l!=f&&!(u&&f>l))return!1;var p=s.get(t),d=s.get(e);if(p&&d)return p==e&&d==t;var h=-1,y=!0,v=2&r?new n:void 0;for(s.set(t,e),s.set(e,t);++h<l;){var m=t[h],b=e[h];if(a)var g=u?a(b,m,h,e,t,s):a(m,b,h,t,e,s);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!i(e,function(t,e){if(!o(v,e)&&(m===t||c(m,t,r,a,s)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,s))){y=!1;break}}return s.delete(t),s.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},27910:t=>{"use strict";t.exports=require("stream")},28837:(t,e,r)=>{var n=r(57797),i=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():i.call(e,r,1),--this.size,!0)}},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),i=r(70222),o=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?i(t):o(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},29632:(t,e,r)=>{"use strict";t.exports=r(97668)},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,i){return r=!!e(t,n,i)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30468:(t,e,r)=>{"use strict";r.d(e,{CG:()=>n,SC:()=>i,cZ:()=>o});let n={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},i={BUSINESS:"business",CUSTOMERS:"customers"},o={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},30854:(t,e,r)=>{var n=r(66930),i=r(658),o=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!i||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new o(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),i=r(69619);t.exports=function(t){return null!=t&&i(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34631:t=>{"use strict";t.exports=require("tls")},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),i=r(67619),o=r(73830),a=r(42403);t.exports=function(t,e){return n(t)?t:i(t,e)?[t]:o(a(t))}},35163:(t,e,r)=>{var n=r(15451),i=r(27467),o=Object.prototype,a=o.hasOwnProperty,c=o.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return i(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35233:(t,e,r)=>{"use strict";r.d(e,{Z:()=>o});var n=r(38398);class i{subscribeToTable(t,e,r={},n){let{event:i="*",filter:o,schema:a="public"}=r,c=n||`${t}-${i}-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;this.subscriptions.has(c)&&this.unsubscribe(c);let s=this.supabase.channel(c).on("postgres_changes",{event:i,schema:a,table:t,...o&&{filter:o}},e).subscribe();return this.subscriptions.set(c,s),{unsubscribe:()=>this.unsubscribe(c),channel:s}}subscribeToBusinessActivities(t,e,r){let n=`business-activities-${t}${r?`-${r}`:""}`;return this.subscribeToTable("business_activities",e,{event:"INSERT",filter:`business_profile_id=eq.${t}`},n)}subscribeToBusinessProfile(t,e,r){let n=`business-profile-${t}${r?`-${r}`:""}`;return this.subscribeToTable("business_profiles",e,{event:"*",filter:`id=eq.${t}`},n)}subscribeToMonthlyMetrics(t,e,r){let n=`monthly-metrics-${t}${r?`-${r}`:""}`;return this.subscribeToTable("monthly_visit_metrics",e,{event:"*",filter:`business_profile_id=eq.${t}`},n)}subscribeToPaymentSubscriptions(t,e,r){let n=`payment-subscriptions-${t}${r?`-${r}`:""}`;return this.subscribeToTable("payment_subscriptions",e,{event:"*",filter:`business_profile_id=eq.${t}`},n)}unsubscribe(t){let e=this.subscriptions.get(t);e&&(this.supabase.removeChannel(e),this.subscriptions.delete(t))}unsubscribeAll(){this.subscriptions.forEach((t,e)=>{this.supabase.removeChannel(t)}),this.subscriptions.clear()}getActiveSubscriptionCount(){return this.subscriptions.size}getActiveChannelIds(){return Array.from(this.subscriptions.keys())}constructor(){this.subscriptions=new Map,this.supabase=(0,n.U)()}}let o=new i},35697:(t,e,r)=>{var n=r(79474),i=r(4999),o=r(67009),a=r(27006),c=r(59774),s=r(2408),u=n?n.prototype:void 0,l=u?u.valueOf:void 0;t.exports=function(t,e,r,n,u,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new i(t),new i(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var d=c;case"[object Set]":var h=1&n;if(d||(d=s),t.size!=e.size&&!h)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(d(t),d(e),n,u,f,p);return p.delete(t),v;case"[object Symbol]":if(l)return l.call(t)==l.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),i=r(92662);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),i=r(27006),o=r(35697),a=r(21630),c=r(1566),s=r(40542),u=r(80329),l=r(10090),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=s(t),g=s(e),x=b?p:c(t),w=g?p:c(e);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,S=x==w;if(S&&u(t)){if(!u(e))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||l(t)?i(t,e,r,y,v,m):o(t,e,x,r,y,v,m);if(!(1&r)){var P=O&&h.call(t,"__wrapped__"),A=j&&h.call(e,"__wrapped__");if(P||A){var k=P?t.value():t,_=A?e.value():e;return m||(m=new n),v(k,_,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),i=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(i,""):t}},38404:(t,e,r)=>{var n=r(29395),i=r(65932),o=r(27467),a=Object.prototype,c=Function.prototype.toString,s=a.hasOwnProperty,u=c.call(Object);t.exports=function(t){if(!o(t)||"[object Object]"!=n(t))return!1;var e=i(t);if(null===e)return!0;var r=s.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==u}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40307:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>f,metadata:()=>l});var n=r(37413),i=r(48872),o=r(32032),a=r(1759),c=r(26919),s=r(39916),u=r(73226);let l={title:"Analytics - Dukancard Business",robots:"noindex, nofollow"};async function f(){let t=await (0,o.createClient)(),{data:{user:e}}=await t.auth.getUser();if(!e)return(0,s.redirect)("/login?message=Authentication required");let{data:r,error:l}=await t.from("business_profiles").select("total_likes, total_subscriptions, average_rating, total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days").eq("id",e.id).single();if(l||!r)return(0,s.redirect)("/login?message=Profile fetch error");let{data:f,error:p}=await t.from("payment_subscriptions").select("plan_id").eq("business_profile_id",e.id).order("created_at",{ascending:!1}).limit(1).maybeSingle(),d=f?.plan_id||"free",{data:h,error:y}=await (0,i.i)(d);return y?(0,n.jsxs)(a.Fc,{variant:"destructive",children:[(0,n.jsx)(c.A,{className:"h-4 w-4"}),(0,n.jsx)(a.XL,{children:"Error Fetching Analytics"}),(0,n.jsx)(a.TN,{children:y})]}):h?(0,n.jsx)(u.default,{analyticsData:h,userId:e.id,userPlan:d,initialProfile:{total_likes:r?.total_likes||0,total_subscriptions:r?.total_subscriptions||0,average_rating:r?.average_rating||0,total_visits:r?.total_visits||0,today_visits:r?.today_visits||0,yesterday_visits:r?.yesterday_visits||0,visits_7_days:r?.visits_7_days||0,visits_30_days:r?.visits_30_days||0}}):(0,n.jsx)("p",{children:"No analytics data available."})}},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var i=null==t?void 0:n(t,e);return void 0===i?r:i}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var i=t.length;return r=void 0===r?i:r,!e&&r>=i?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(r=r>i?i:r)<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var o=Array(i);++n<i;)o[n]=t[n+e];return o}},41547:(t,e,r)=>{var n=r(61548),i=r(90851);t.exports=function(t,e){var r=i(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),i=r(85450);t.exports=function t(e,r,o,a,c){var s=-1,u=e.length;for(o||(o=i),c||(c=[]);++s<u;){var l=e[s];r>0&&o(l)?r>1?t(l,r-1,o,a,c):n(c,l):a||(c[c.length]=l)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},44599:(t,e,r)=>{"use strict";r.d(e,{default:()=>h9});var n={};r.r(n),r.d(n,{scaleBand:()=>nw,scaleDiverging:()=>function t(){var e=os(cS()(i0));return e.copy=function(){return cw(e,t())},ny.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=om(cS()).domain([.1,1,10]);return e.copy=function(){return cw(e,t()).base(e.base())},ny.apply(e,arguments)},scaleDivergingPow:()=>cP,scaleDivergingSqrt:()=>cA,scaleDivergingSymlog:()=>function t(){var e=ox(cS());return e.copy=function(){return cw(e,t()).constant(e.constant())},ny.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,iJ),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,iJ):[0,1],os(n)},scaleImplicit:()=>ng,scaleLinear:()=>ou,scaleLog:()=>function t(){let e=om(i4()).domain([1,10]);return e.copy=()=>i3(e,t()).base(e.base()),nh.apply(e,arguments),e},scaleOrdinal:()=>nx,scalePoint:()=>nO,scalePow:()=>oP,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=ia){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[is(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(ie),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nh.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[is(o,t,0,i)]:e}function s(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,s()):[r,n]},c.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,s()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return o.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nh.apply(os(c),arguments)},scaleRadial:()=>function t(){var e,r=i6(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(ok(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,iJ)).map(ok)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},nh.apply(o,arguments),os(o)},scaleSequential:()=>function t(){var e=os(cx()(i0));return e.copy=function(){return cw(e,t())},ny.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=om(cx()).domain([1,10]);return e.copy=function(){return cw(e,t()).base(e.base())},ny.apply(e,arguments)},scaleSequentialPow:()=>cO,scaleSequentialQuantile:()=>function t(){var e=[],r=i0;function n(t){if(null!=t&&!isNaN(t*=1))return r((is(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(ie),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return oE(t);if(e>=1)return o_(t);var n,i=(n-1)*e,o=Math.floor(i),a=o_((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?oM:function(t=ie){if(t===ie)return oM;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,c=r-n+1,s=Math.log(a),u=.5*Math.exp(2*s/3),l=.5*Math.sqrt(s*u*(a-u)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*u/a+l)),p=Math.min(i,Math.floor(r+(a-c)*u/a+l));t(e,r,f,p,o)}let a=e[r],c=n,s=i;for(oT(e,n,r),o(e[i],a)>0&&oT(e,n,i);c<s;){for(oT(e,c,s),++c,--s;0>o(e[c],a);)++c;for(;o(e[s],a)>0;)--s}0===o(e[n],a)?oT(e,n,s):oT(e,++s,i),s<=r&&(n=s+1),r<=s&&(i=s-1)}return e})(t,o).subarray(0,o+1));return a+(oE(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},ny.apply(n,arguments)},scaleSequentialSqrt:()=>cj,scaleSequentialSymlog:()=>function t(){var e=ox(cx());return e.copy=function(){return cw(e,t()).constant(e.constant())},ny.apply(e,arguments)},scaleSqrt:()=>oA,scaleSymlog:()=>function t(){var e=ox(i4());return e.copy=function(){return i3(e,t()).constant(e.constant())},nh.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[is(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},nh.apply(o,arguments)},scaleTime:()=>cb,scaleUtc:()=>cg,tickFormat:()=>oc});var i=r(60687),o=r(43210),a=r.n(o),c=r(77882),s=r(53411);r(35233);var u=r(67760),l=r(23026),f=r(64398),p=r(96241);function d({title:t,value:e,icon:r,description:n,color:o,isUpdated:a=!1,suffix:s,trend:u}){let l={rose:{bgLight:"bg-rose-50",bgDark:"dark:bg-rose-900/20",textLight:"text-rose-600",textDark:"dark:text-rose-400"},blue:{bgLight:"bg-blue-50",bgDark:"dark:bg-blue-900/20",textLight:"text-blue-600",textDark:"dark:text-blue-400"},amber:{bgLight:"bg-amber-50",bgDark:"dark:bg-amber-900/20",textLight:"text-amber-600",textDark:"dark:text-amber-400"},green:{bgLight:"bg-emerald-50",bgDark:"dark:bg-emerald-900/20",textLight:"text-emerald-600",textDark:"dark:text-emerald-400"},purple:{bgLight:"bg-purple-50",bgDark:"dark:bg-purple-900/20",textLight:"text-purple-600",textDark:"dark:text-purple-400"},indigo:{bgLight:"bg-indigo-50",bgDark:"dark:bg-indigo-900/20",textLight:"text-indigo-600",textDark:"dark:text-indigo-400"}}[o];return(0,i.jsx)(c.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},children:(0,i.jsxs)("div",{className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,i.jsxs)("div",{className:"relative z-10",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-neutral-600 dark:text-neutral-400 tracking-wide",children:t}),(0,i.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,i.jsxs)(c.P.h3,{className:"text-3xl font-bold text-neutral-900 dark:text-neutral-100 tracking-tight",variants:{initial:{scale:1},update:{scale:[1,1.1,1],transition:{duration:.3}}},initial:"initial",animate:a?"update":"initial",children:[e,s&&(0,i.jsx)("span",{className:"text-lg text-neutral-500 dark:text-neutral-400 ml-1",children:s})]}),u&&(0,i.jsxs)(c.P.div,{initial:{opacity:0,y:5},animate:{opacity:1,y:0},transition:{delay:.2},className:(0,p.cn)("text-sm font-medium flex items-center",u.isPositive?"text-emerald-600 dark:text-emerald-400":"text-red-600 dark:text-red-400"),children:[u.isPositive?"↗":"↘"," ",Math.abs(u.value),"%"]})]})]}),(0,i.jsx)("div",{className:(0,p.cn)("flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300",l.bgLight,l.bgDark,l.textLight,l.textDark),children:(0,i.jsx)(r,{className:"w-6 h-6"})})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400 leading-relaxed",children:n}),a&&(0,i.jsxs)(c.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"flex items-center gap-2 text-xs font-medium text-primary",children:[(0,i.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary animate-pulse"}),"Updated"]})]})]})]})})}function h({profile:t,initialProfile:e}){return(0,i.jsx)(c.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(c.P.div,{variants:{hidden:{opacity:0,y:-20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,i.jsx)(u.A,{className:"w-5 h-5 text-primary"})}),(0,i.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,i.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"User Engagement"})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Engagement Metrics"}),(0,i.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed",children:"Track how users interact with your business card and measure customer engagement levels."})]})]})}),(0,i.jsxs)("div",{className:"grid gap-6 grid-cols-1 md:grid-cols-3",children:[(0,i.jsx)(d,{title:"Total Likes",value:(0,p.gY)(t.total_likes),icon:u.A,description:"People who liked your card",color:"rose",isUpdated:t.total_likes!==e.total_likes}),(0,i.jsx)(d,{title:"Total Subscribers",value:(0,p.gY)(t.total_subscriptions),icon:l.A,description:"People subscribed to updates",color:"blue",isUpdated:t.total_subscriptions!==e.total_subscriptions}),(0,i.jsx)(d,{title:"Average Rating",value:t.average_rating?.toFixed(1)||"0.0",suffix:"/5.0",icon:f.A,description:"Average customer rating",color:"amber",isUpdated:t.average_rating!==e.average_rating})]})]})})}var y=r(41312);function v({totalUniqueVisits:t,todayUniqueVisits:e,yesterdayUniqueVisits:r,isVisitUpdated:n,currentMonthUniqueVisits:o}){let a=(()=>{if(0===r)return{value:0,isPositive:!0};let t=Math.round((e-r)/r*100);return{value:t,isPositive:t>=0}})();return(0,i.jsx)(c.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(c.P.div,{variants:{hidden:{opacity:0,y:-20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,i.jsx)(y.A,{className:"w-5 h-5 text-primary"})}),(0,i.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,i.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Traffic Analytics"})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Visitor Metrics"}),(0,i.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed",children:"Monitor your business card traffic patterns and visitor engagement over time."})]})]})}),(0,i.jsxs)("div",{className:"grid gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-4",children:[(0,i.jsx)(d,{title:"Total Unique Visits",value:(0,p.gY)(t),icon:y.A,description:"All-time unique visitors",color:"blue",isUpdated:n}),(0,i.jsx)(d,{title:"Today's Visits",value:(0,p.gY)(e),icon:y.A,description:"Unique visitors today",color:"green",isUpdated:n,trend:a}),(0,i.jsx)(d,{title:"Yesterday's Visits",value:(0,p.gY)(r),icon:y.A,description:"Unique visitors yesterday",color:"purple",isUpdated:n}),(0,i.jsx)(d,{title:"This Month",value:(0,p.gY)(o),icon:y.A,description:"Unique visitors this month",color:"indigo",isUpdated:n})]})]})})}var m=r(37456),b=r.n(m),g=r(5231),x=r.n(g),w=r(34990),O=r.n(w),j=r(40491),S=r.n(j),P=r(85938),A=r.n(P),k=r(45603),_=r.n(k),E=r(49384);function M(t,e){if(!t)throw Error("Invariant failed")}var T=r(63866),N=r.n(T),C=r(55048),D=r.n(C),I=r(29632),B=r(77822),L=r.n(B),R=r(93490),U=r.n(R),z=function(t){return 0===t?0:t>0?1:-1},$=function(t){return N()(t)&&t.indexOf("%")===t.length-1},q=function(t){return U()(t)&&!L()(t)},F=function(t){return q(t)||N()(t)},V=0,W=function(t){var e=++V;return"".concat(t||"").concat(e)},G=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!q(t)&&!N()(t))return n;if($(t)){var o=t.indexOf("%");r=e*parseFloat(t.slice(0,o))/100}else r=+t;return L()(r)&&(r=n),i&&r>e&&(r=e),r},Y=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},H=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},X=function(t,e){return q(t)&&q(e)?function(r){return t+r*(e-t)}:function(){return e}};function K(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):S()(t,e))===r}):null}var Z=function(t,e){return q(t)&&q(e)?t-e:N()(t)&&N()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))};function J(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function Q(t){return(Q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tt=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],te=["points","pathLength"],tr={svg:["viewBox","children"],polygon:te,polyline:te},tn=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],ti=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,o.isValidElement)(t)&&(r=t.props),!D()(r))return null;var n={};return Object.keys(r).forEach(function(t){tn.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},to=function(t,e,r){if(!D()(t)||"object"!==Q(t))return null;var n=null;return Object.keys(t).forEach(function(i){var o=t[i];tn.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=function(t){return o(e,r,t),null})}),n},ta=["children"],tc=["children"];function ts(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function tu(t){return(tu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tl={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tf=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tp=null,td=null,th=function t(e){if(e===tp&&Array.isArray(td))return td;var r=[];return o.Children.forEach(e,function(e){b()(e)||((0,I.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),td=r,tp=e,r};function ty(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tf(t)}):[tf(e)],th(t).forEach(function(t){var e=S()(t,"type.displayName")||S()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tv(t,e){var r=ty(t,e);return r&&r[0]}var tm=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!q(r)&&!(r<=0)&&!!q(n)&&!(n<=0)},tb=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tg=function(t,e,r,n){var i,o=null!=(i=null==tr?void 0:tr[n])?i:[];return e.startsWith("data-")||!x()(t)&&(n&&o.includes(e)||tt.includes(e))||r&&tn.includes(e)},tx=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,o.isValidElement)(t)&&(n=t.props),!D()(n))return null;var i={};return Object.keys(n).forEach(function(t){var o;tg(null==(o=n)?void 0:o[t],t,e,r)&&(i[t]=n[t])}),i},tw=function t(e,r){if(e===r)return!0;var n=o.Children.count(e);if(n!==o.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tO(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=e[i],c=r[i];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!tO(a,c))return!1}return!0},tO=function(t,e){if(b()(t)&&b()(e))return!0;if(!b()(t)&&!b()(e)){var r=t.props||{},n=r.children,i=ts(r,ta),o=e.props||{},a=o.children,c=ts(o,tc);if(n&&a)return J(i,c)&&tw(n,a);if(!n&&!a)return J(i,c)}return!1},tj=function(t,e){var r=[],n={};return th(t).forEach(function(t,i){var o;if((o=t)&&o.type&&N()(o.type)&&tb.indexOf(o.type)>=0)r.push(t);else if(t){var a=tf(t.type),c=e[a]||{},s=c.handler,u=c.once;if(s&&(!u||!n[a])){var l=s(t,a,i);r.push(l),n[a]=!0}}}),r},tS=function(t){var e=t&&t.type;return e&&tl[e]?tl[e]:null},tP=["children","width","height","viewBox","className","style","title","desc"];function tA(){return(tA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tk(t){var e=t.children,r=t.width,n=t.height,i=t.viewBox,o=t.className,c=t.style,s=t.title,u=t.desc,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tP),f=i||{width:r,height:n,x:0,y:0},p=(0,E.A)("recharts-surface",o);return a().createElement("svg",tA({},tx(l,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,s),a().createElement("desc",null,u),e)}var t_=["children","className"];function tE(){return(tE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tM=a().forwardRef(function(t,e){var r=t.children,n=t.className,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,t_),o=(0,E.A)("recharts-layer",n);return a().createElement("g",tE({className:o},tx(i,!0),{ref:e}),r)});function tT(t){return(tT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tN(){return(tN=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tC(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tD(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tI(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tD(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tT(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tD(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tB(t){return Array.isArray(t)&&F(t[0])&&F(t[1])?t.join(" ~ "):t}var tL=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,i=t.itemStyle,o=void 0===i?{}:i,c=t.labelStyle,s=t.payload,u=t.formatter,l=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,d=t.label,h=t.labelFormatter,y=t.accessibilityLayer,v=tI({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=tI({margin:0},void 0===c?{}:c),g=!b()(d),x=g?d:"",w=(0,E.A)("recharts-default-tooltip",f),O=(0,E.A)("recharts-tooltip-label",p);return g&&h&&null!=s&&(x=h(d,s)),a().createElement("div",tN({className:w,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:O,style:m},a().isValidElement(x)?x:"".concat(x)),function(){if(s&&s.length){var t=(l?A()(s,l):s).map(function(t,e){if("none"===t.type)return null;var n=tI({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},o),i=t.formatter||u||tB,c=t.value,l=t.name,f=c,p=l;if(i&&null!=f&&null!=p){var d=i(c,l,t,e,s);if(Array.isArray(d)){var h=function(t){if(Array.isArray(t))return t}(d)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{o=(r=r.call(t)).next,!1;for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(d,2)||function(t,e){if(t){if("string"==typeof t)return tC(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tC(t,e)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],p=h[1]}else f=d}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},F(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,F(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tR(t){return(tR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tU(t,e,r){var n;return(n=function(t,e){if("object"!=tR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tR(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tz="recharts-tooltip-wrapper",t$={visibility:"hidden"};function tq(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,o=t.position,a=t.reverseDirection,c=t.tooltipDimension,s=t.viewBox,u=t.viewBoxDimension;if(o&&q(o[n]))return o[n];var l=r[n]-c-i,f=r[n]+i;return e[n]?a[n]?l:f:a[n]?l<s[n]?Math.max(f,s[n]):Math.max(l,s[n]):f+c>s[n]+u?Math.max(l,s[n]):Math.max(f,s[n])}function tF(t){return(tF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tV(Object(r),!0).forEach(function(e){tX(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tG(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tG=function(){return!!t})()}function tY(t){return(tY=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tH(t,e){return(tH=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tX(t,e,r){return(e=tK(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tK(t){var e=function(t,e){if("object"!=tF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tF(e)?e:e+""}var tZ=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=r,n=[].concat(o),e=tY(e),tX(t=function(t,e){if(e&&("object"===tF(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tG()?Reflect.construct(e,n||[],tY(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tX(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,i,o;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(i=null==(o=t.props.coordinate)?void 0:o.y)?i:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tH(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,i,o,c,s,u,l,f,p,d,h,y,v,m,b,g,x=this,w=this.props,O=w.active,j=w.allowEscapeViewBox,S=w.animationDuration,P=w.animationEasing,A=w.children,k=w.coordinate,_=w.hasPayload,M=w.isAnimationActive,T=w.offset,N=w.position,C=w.reverseDirection,D=w.useTranslate3d,I=w.viewBox,B=w.wrapperStyle,L=(p=(t={allowEscapeViewBox:j,coordinate:k,offsetTopLeft:T,position:N,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:D,viewBox:I}).allowEscapeViewBox,d=t.coordinate,h=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,b=t.useTranslate3d,g=t.viewBox,m.height>0&&m.width>0&&d?(r=(e={translateX:l=tq({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=tq({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=e.translateY,u={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):u=t$,{cssProperties:u,cssClasses:(o=(i={translateX:l,translateY:f,coordinate:d}).coordinate,c=i.translateX,s=i.translateY,(0,E.A)(tz,tU(tU(tU(tU({},"".concat(tz,"-right"),q(c)&&o&&q(o.x)&&c>=o.x),"".concat(tz,"-left"),q(c)&&o&&q(o.x)&&c<o.x),"".concat(tz,"-bottom"),q(s)&&o&&q(o.y)&&s>=o.y),"".concat(tz,"-top"),q(s)&&o&&q(o.y)&&s<o.y)))}),R=L.cssClasses,U=L.cssProperties,z=tW(tW({transition:M&&O?"transform ".concat(S,"ms ").concat(P):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&O&&_?"visible":"hidden",position:"absolute",top:0,left:0},B);return a().createElement("div",{tabIndex:-1,className:R,style:z,ref:function(t){x.wrapperNode=t}},A)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tK(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent),tJ={isSsr:!0,get:function(t){return tJ[t]},set:function(t,e){if("string"==typeof t)tJ[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){tJ[e]=t[e]})}}},tQ=r(36315),t0=r.n(tQ);function t1(t,e,r){return!0===e?t0()(t,r):x()(e)?t0()(t,e):t}function t2(t){return(t2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t3(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t5(Object(r),!0).forEach(function(e){t7(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t4(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t4=function(){return!!t})()}function t6(t){return(t6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t8(t,e){return(t8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t7(t,e,r){return(e=t9(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t9(t){var e=function(t,e){if("object"!=t2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t2(e)?e:e+""}function et(t){return t.dataKey}var ee=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=t6(t),function(t,e){if(e&&("object"===t2(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,t4()?Reflect.construct(t,e||[],t6(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&t8(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,i=r.allowEscapeViewBox,o=r.animationDuration,c=r.animationEasing,s=r.content,u=r.coordinate,l=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=d?d:[];l&&x.length&&(x=t1(d.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),h,et));var w=x.length>0;return a().createElement(tZ,{allowEscapeViewBox:i,animationDuration:o,animationEasing:c,isAnimationActive:f,active:n,coordinate:u,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=t3(t3({},this.props),{},{payload:x}),a().isValidElement(s)?a().cloneElement(s,t):"function"==typeof s?a().createElement(s,t):a().createElement(tL,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t9(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);t7(ee,"displayName","Tooltip"),t7(ee,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!tJ.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var er=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]},en=r(69433),ei=r.n(en);let eo=Math.cos,ea=Math.sin,ec=Math.sqrt,es=Math.PI,eu=2*es,el={draw(t,e){let r=ec(e/es);t.moveTo(r,0),t.arc(0,0,r,0,eu)}},ef=ec(1/3),ep=2*ef,ed=ea(es/10)/ea(7*es/10),eh=ea(eu/10)*ed,ey=-eo(eu/10)*ed,ev=ec(3),em=ec(3)/2,eb=1/ec(12),eg=(eb/2+1)*3;function ex(t){return function(){return t}}let ew=Math.PI,eO=2*ew,ej=eO-1e-6;function eS(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eP{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eS:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eS;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,n,i){if(t*=1,e*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,c=r-t,s=n-e,u=o-t,l=a-e,f=u*u+l*l;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(l*c-s*u)>1e-6&&i){let p=r-o,d=n-a,h=c*c+s*s,y=Math.sqrt(h),v=Math.sqrt(f),m=i*Math.tan((ew-Math.acos((h+f-(p*p+d*d))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*u},${e+b*l}`,this._append`A${i},${i},0,0,${+(l*p>u*d)},${this._x1=t+g*c},${this._y1=e+g*s}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,i,o){if(t*=1,e*=1,r*=1,o=!!o,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),s=t+a,u=e+c,l=1^o,f=o?n-i:i-n;null===this._x1?this._append`M${s},${u}`:(Math.abs(this._x1-s)>1e-6||Math.abs(this._y1-u)>1e-6)&&this._append`L${s},${u}`,r&&(f<0&&(f=f%eO+eO),f>ej?this._append`A${r},${r},0,1,${l},${t-a},${e-c}A${r},${r},0,1,${l},${this._x1=s},${this._y1=u}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=ew)},${l},${this._x1=t+r*Math.cos(i)},${this._y1=e+r*Math.sin(i)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eA(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eP(e)}function ek(t){return(ek="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eP.prototype,ec(3),ec(3);var e_=["type","size","sizeType"];function eE(){return(eE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eM(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eM(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=ek(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ek(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ek(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eM(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eN={symbolCircle:el,symbolCross:{draw(t,e){let r=ec(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=ec(e/ep),n=r*ef;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=ec(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=ec(.8908130915292852*e),n=eh*r,i=ey*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){let o=eu*e/5,a=eo(o),c=ea(o);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*i,c*n+a*i)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-ec(e/(3*ev));t.moveTo(0,2*r),t.lineTo(-ev*r,-r),t.lineTo(ev*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=ec(e/eg),n=r/2,i=r*eb,o=r*eb+r,a=-n;t.moveTo(n,i),t.lineTo(n,o),t.lineTo(a,o),t.lineTo(-.5*n-em*i,em*n+-.5*i),t.lineTo(-.5*n-em*o,em*n+-.5*o),t.lineTo(-.5*a-em*o,em*a+-.5*o),t.lineTo(-.5*n+em*i,-.5*i-em*n),t.lineTo(-.5*n+em*o,-.5*o-em*n),t.lineTo(-.5*a+em*o,-.5*o-em*a),t.closePath()}}},eC=Math.PI/180,eD=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eC;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eI=function(t){var e,r=t.type,n=void 0===r?"circle":r,i=t.size,o=void 0===i?64:i,c=t.sizeType,s=void 0===c?"area":c,u=eT(eT({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,e_)),{},{type:n,size:o,sizeType:s}),l=u.className,f=u.cx,p=u.cy,d=tx(u,!0);return f===+f&&p===+p&&o===+o?a().createElement("path",eE({},d,{className:(0,E.A)("recharts-symbols",l),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eN["symbol".concat(ei()(n))]||el,(function(t,e){let r=null,n=eA(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:ex(t||el),e="function"==typeof e?e:ex(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:ex(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:ex(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(e).size(eD(o,s,n))())})):null};function eB(t){return(eB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eL(){return(eL=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eI.registerSymbol=function(t,e){eN["symbol".concat(ei()(t))]=e};function eU(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eU=function(){return!!t})()}function ez(t){return(ez=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e$(t,e){return(e$=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eq(t,e,r){return(e=eF(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eF(t){var e=function(t,e){if("object"!=eB(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eB(e)?e:e+""}var eV=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ez(t),function(t,e){if(e&&("object"===eB(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eU()?Reflect.construct(t,e||[],ez(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&e$(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var o=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eR(Object(r),!0).forEach(function(e){eq(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete o.legendIcon,a().cloneElement(t.legendIcon,o)}return a().createElement(eI,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,i=e.layout,o=e.formatter,c=e.inactiveColor,s={x:0,y:0,width:32,height:32},u={display:"horizontal"===i?"inline-block":"block",marginRight:10},l={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var i=e.formatter||o,f=(0,E.A)(eq(eq({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=x()(e.value)?null:e.value;er(!x()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=e.inactive?c:e.color;return a().createElement("li",eL({className:f,style:u,key:"legend-item-".concat(r)},to(t.props,e,r)),a().createElement(tk,{width:n,height:n,viewBox:s,style:l},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},i?i(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eF(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);function eW(t){return(eW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eq(eV,"displayName","Legend"),eq(eV,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var eG=["ref"];function eY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eH(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eY(Object(r),!0).forEach(function(e){eQ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eX(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e0(n.key),n)}}function eK(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eK=function(){return!!t})()}function eZ(t){return(eZ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eJ(t,e){return(eJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eQ(t,e,r){return(e=e0(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e0(t){var e=function(t,e){if("object"!=eW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eW(e)?e:e+""}function e1(t){return t.value}var e2=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=eZ(e),eQ(t=function(t,e){if(e&&("object"===eW(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eK()?Reflect.construct(e,r||[],eZ(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&eJ(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?eH({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,c=n.margin,s=n.chartWidth,u=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((s||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((u||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),eH(eH({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,i=e.height,o=e.wrapperStyle,c=e.payloadUniqBy,s=e.payload,u=eH(eH({position:"absolute",width:n||"auto",height:i||"auto"},this.getDefaultPosition(o)),o);return a().createElement("div",{className:"recharts-legend-wrapper",style:u,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,eG);return a().createElement(eV,r)}(r,eH(eH({},this.props),{},{payload:t1(s,c,e1)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=eH(eH({},this.defaultProps),t.props).layout;return"vertical"===r&&q(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&eX(n.prototype,e),r&&eX(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function e5(){return(e5=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}eQ(e2,"displayName","Legend"),eQ(e2,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var e3=function(t){var e=t.cx,r=t.cy,n=t.r,i=t.className,o=(0,E.A)("recharts-dot",i);return e===+e&&r===+r&&n===+n?a().createElement("circle",e5({},tx(t,!1),ti(t),{className:o,cx:e,cy:r,r:n})):null},e4=r(87955),e6=r.n(e4),e8=Object.getOwnPropertyNames,e7=Object.getOwnPropertySymbols,e9=Object.prototype.hasOwnProperty;function rt(t,e){return function(r,n,i){return t(r,n,i)&&e(r,n,i)}}function re(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var i=n.cache,o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var c=t(e,r,n);return i.delete(e),i.delete(r),c}}function rr(t){return e8(t).concat(e7(t))}var rn=Object.hasOwn||function(t,e){return e9.call(t,e)};function ri(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var ro=Object.getOwnPropertyDescriptor,ra=Object.keys;function rc(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rs(t,e){return ri(t.getTime(),e.getTime())}function ru(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rl(t,e){return t===e}function rf(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.entries(),s=0;(n=c.next())&&!n.done;){for(var u=e.entries(),l=!1,f=0;(i=u.next())&&!i.done;){if(a[f]){f++;continue}var p=n.value,d=i.value;if(r.equals(p[0],d[0],s,f,t,e,r)&&r.equals(p[1],d[1],p[0],d[0],t,e,r)){l=a[f]=!0;break}f++}if(!l)return!1;s++}return!0}function rp(t,e,r){var n=ra(t),i=n.length;if(ra(e).length!==i)return!1;for(;i-- >0;)if(!rg(t,e,r,n[i]))return!1;return!0}function rd(t,e,r){var n,i,o,a=rr(t),c=a.length;if(rr(e).length!==c)return!1;for(;c-- >0;)if(!rg(t,e,r,n=a[c])||(i=ro(t,n),o=ro(e,n),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable)))return!1;return!0}function rh(t,e){return ri(t.valueOf(),e.valueOf())}function ry(t,e){return t.source===e.source&&t.flags===e.flags}function rv(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.values();(n=c.next())&&!n.done;){for(var s=e.values(),u=!1,l=0;(i=s.next())&&!i.done;){if(!a[l]&&r.equals(n.value,i.value,n.value,i.value,t,e,r)){u=a[l]=!0;break}l++}if(!u)return!1}return!0}function rm(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rb(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rg(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rn(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rx=Array.isArray,rw="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rO=Object.assign,rj=Object.prototype.toString.call.bind(Object.prototype.toString),rS=rP();function rP(t){void 0===t&&(t={});var e,r,n,i,o,a,c,s,u,l,f,p,d,h=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?rd:rc,areDatesEqual:rs,areErrorsEqual:ru,areFunctionsEqual:rl,areMapsEqual:n?rt(rf,rd):rf,areNumbersEqual:ri,areObjectsEqual:n?rd:rp,arePrimitiveWrappersEqual:rh,areRegExpsEqual:ry,areSetsEqual:n?rt(rv,rd):rv,areTypedArraysEqual:n?rd:rm,areUrlsEqual:rb};if(r&&(i=rO({},i,r(i))),e){var o=re(i.areArraysEqual),a=re(i.areMapsEqual),c=re(i.areObjectsEqual),s=re(i.areSetsEqual);i=rO({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:s})}return i}(t)).areArraysEqual,n=e.areDatesEqual,i=e.areErrorsEqual,o=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,s=e.areObjectsEqual,u=e.arePrimitiveWrappersEqual,l=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,d=e.areUrlsEqual,function(t,e,h){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,h):"function"===y&&o(t,e,h);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return s(t,e,h);if(rx(t))return r(t,e,h);if(null!=rw&&rw(t))return p(t,e,h);if(v===Date)return n(t,e,h);if(v===RegExp)return l(t,e,h);if(v===Map)return a(t,e,h);if(v===Set)return f(t,e,h);var m=rj(t);return"[object Date]"===m?n(t,e,h):"[object RegExp]"===m?l(t,e,h):"[object Map]"===m?a(t,e,h):"[object Set]"===m?f(t,e,h):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&s(t,e,h):"[object URL]"===m?d(t,e,h):"[object Error]"===m?i(t,e,h):"[object Arguments]"===m?s(t,e,h):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&u(t,e,h)}),g=y?y(b):function(t,e,r,n,i,o,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,o=t.strict;if(n)return function(t,a){var c=n(),s=c.cache;return r(t,a,{cache:void 0===s?e?new WeakMap:void 0:s,equals:i,meta:c.meta,strict:o})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(t,e){return r(t,e,a)}}({circular:void 0!==h&&h,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rA(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(i){if(r<0&&(r=i),i-r>e)t(i),r=-1;else{var o;o=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(o)}})}function rk(t){return(rk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r_(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rE(t){return(rE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rM(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rM(Object(r),!0).forEach(function(e){rN(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rM(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rN(t,e,r){var n;return(n=function(t,e){if("object"!==rE(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rE(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rP({strict:!0}),rP({circular:!0}),rP({circular:!0,strict:!0}),rP({createInternalComparator:function(){return ri}}),rP({strict:!0,createInternalComparator:function(){return ri}}),rP({circular:!0,createInternalComparator:function(){return ri}}),rP({circular:!0,createInternalComparator:function(){return ri},strict:!0});var rC=function(t){return t},rD=function(t,e){return Object.keys(e).reduce(function(r,n){return rT(rT({},r),{},rN({},n,t(n,e[n])))},{})},rI=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rB=function(t,e,r,n,i,o,a,c){};function rL(t,e){if(t){if("string"==typeof t)return rR(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rR(t,e)}}function rR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rU=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rz=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},r$=function(t,e){return function(r){return rz(rU(t,e),r)}},rq=function(){for(var t,e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n[0],a=n[1],c=n[2],s=n[3];if(1===n.length)switch(n[0]){case"linear":o=0,a=0,c=1,s=1;break;case"ease":o=.25,a=.1,c=.25,s=1;break;case"ease-in":o=.42,a=0,c=1,s=1;break;case"ease-out":o=.42,a=0,c=.58,s=1;break;case"ease-in-out":o=0,a=0,c=.58,s=1;break;default:var u=n[0].split("(");if("cubic-bezier"===u[0]&&4===u[1].split(")")[0].split(",").length){var l,f=function(t){if(Array.isArray(t))return t}(l=u[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{o=(r=r.call(t)).next,!1;for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(l,4)||rL(l,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=f[0],a=f[1],c=f[2],s=f[3]}else rB(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rB([o,c,a,s].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=r$(o,c),d=r$(a,s),h=(t=o,e=c,function(r){var n;return rz([].concat(function(t){if(Array.isArray(t))return rR(t)}(n=rU(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rL(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i,o=p(r)-e,a=h(r);if(1e-4>Math.abs(o-e)||a<1e-4)break;r=(i=r-o/a)>1?1:i<0?0:i}return d(r)};return y.isStepper=!1,y},rF=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,c=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(o)?[e,0]:[c,o]};return c.isStepper=!0,c.dt=a,c},rV=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rq(n);case"spring":return rF();default:if("cubic-bezier"===n.split("(")[0])return rq(n);rB(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rB(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rW(t){return(rW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rG(t){return function(t){if(Array.isArray(t))return rZ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||rK(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rH(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rY(Object(r),!0).forEach(function(e){rX(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rX(t,e,r){var n;return(n=function(t,e){if("object"!==rW(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rW(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rK(t,e){if(t){if("string"==typeof t)return rZ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rZ(t,e)}}function rZ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rJ=function(t,e,r){return t+(e-t)*r},rQ=function(t){return t.from!==t.to},r0=function t(e,r,n){var i=rD(function(t,r){if(rQ(r)){var n,i=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{o=(r=r.call(t)).next,!1;for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(n,2)||rK(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i[1];return rH(rH({},r),{},{from:o,velocity:a})}return r},r);return n<1?rD(function(t,e){return rQ(e)?rH(rH({},e),{},{velocity:rJ(e.velocity,i[t].velocity,n),from:rJ(e.from,i[t].from,n)}):e},r):t(e,i,n-1)};let r1=function(t,e,r,n,i){var o,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),s=c.reduce(function(r,n){return rH(rH({},r),{},rX({},n,[t[n],e[n]]))},{}),u=c.reduce(function(r,n){return rH(rH({},r),{},rX({},n,{from:t[n],velocity:0,to:e[n]}))},{}),l=-1,f=function(){return null};return f=r.isStepper?function(n){o||(o=n);var a=(n-o)/r.dt;u=r0(r,u,a),i(rH(rH(rH({},t),e),rD(function(t,e){return e.from},u))),o=n,Object.values(u).filter(rQ).length&&(l=requestAnimationFrame(f))}:function(o){a||(a=o);var c=(o-a)/n,u=rD(function(t,e){return rJ.apply(void 0,rG(e).concat([r(c)]))},s);if(i(rH(rH(rH({},t),e),u)),c<1)l=requestAnimationFrame(f);else{var p=rD(function(t,e){return rJ.apply(void 0,rG(e).concat([r(1)]))},s);i(rH(rH(rH({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(l)}}};function r2(t){return(r2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r5=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function r3(t){return function(t){if(Array.isArray(t))return r4(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r4(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r4(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r4(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r6(Object(r),!0).forEach(function(e){r7(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r7(t,e,r){return(e=r9(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r9(t){var e=function(t,e){if("object"!==r2(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r2(e)?e:String(e)}function nt(t,e){return(nt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ne(t,e){if(e&&("object"===r2(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nr(t)}function nr(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nn(t){return(nn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var ni=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&nt(i,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nn(i);return t=e?Reflect.construct(r,arguments,nn(this).constructor):r.apply(this,arguments),ne(this,t)});function i(t,e){if(!(this instanceof i))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),o=r.props,a=o.isActive,c=o.attributeName,s=o.from,u=o.to,l=o.steps,f=o.children,p=o.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nr(r)),r.changeStyle=r.changeStyle.bind(nr(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:u}),ne(r);if(l&&l.length)r.state={style:l[0].style};else if(s){if("function"==typeof f)return r.state={style:s},ne(r);r.state={style:c?r7({},c,s):s}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate,a=e.to,c=e.from,s=this.state.style;if(n){if(!r){var u={style:i?r7({},i,a):a};this.state&&s&&(i&&s[i]!==a||!i&&s!==a)&&this.setState(u);return}if(!rS(t.to,a)||!t.canBegin||!t.isActive){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=l||o?c:t.to;if(this.state&&s){var p={style:i?r7({},i,f):f};(i&&s[i]!==f||!i&&s!==f)&&this.setState(p)}this.runAnimation(r8(r8({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,c=t.onAnimationEnd,s=t.onAnimationStart,u=r1(r,n,rV(o),i,this.changeStyle);this.manager.start([s,a,function(){e.stopJSAnimation=u()},i,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,c=o.duration;return this.manager.start([i].concat(r3(r.reduce(function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,c=void 0===a?"ease":a,s=n.style,u=n.properties,l=n.onAnimationEnd,f=i>0?r[i-1]:n,p=u||Object.keys(s);if("function"==typeof c||"spring"===c)return[].concat(r3(t),[e.runJSAnimation.bind(e,{from:f.style,to:s,duration:o,easing:c}),o]);var d=rI(p,o,c),h=r8(r8(r8({},f.style),s),{},{transition:d});return[].concat(r3(t),[h,o,l]).filter(rC)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,i=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var i=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return r_(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r_(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i.slice(1);return"number"==typeof o?void rA(t.bind(null,a),o):(t(o),void rA(t.bind(null,a)))}"object"===rk(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,i(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,i,o=t.begin,a=t.duration,c=t.attributeName,s=t.to,u=t.easing,l=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,d=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof u||"function"==typeof d||"spring"===u)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?r7({},c,s):s,v=rI(Object.keys(y),a,u);h.start([l,o,r8(r8({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,r5)),c=o.Children.count(e),s=this.state.style;if("function"==typeof e)return e(s);if(!n||0===c||r<=0)return e;var u=function(t){var e=t.props,r=e.style,n=e.className;return(0,o.cloneElement)(t,r8(r8({},i),{},{style:r8(r8({},void 0===r?{}:r),s),className:n}))};return 1===c?u(o.Children.only(e)):a().createElement("div",null,o.Children.map(e,function(t){return u(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,r9(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(o.PureComponent);function no(t){return(no="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function na(){return(na=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ns(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ns(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=no(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=no(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==no(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ns(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}ni.displayName="Animate",ni.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ni.propTypes={from:e6().oneOfType([e6().object,e6().string]),to:e6().oneOfType([e6().object,e6().string]),attributeName:e6().string,duration:e6().number,begin:e6().number,easing:e6().oneOfType([e6().string,e6().func]),steps:e6().arrayOf(e6().shape({duration:e6().number.isRequired,style:e6().object.isRequired,easing:e6().oneOfType([e6().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),e6().func]),properties:e6().arrayOf("string"),onAnimationEnd:e6().func})),children:e6().oneOfType([e6().node,e6().func]),isActive:e6().bool,canBegin:e6().bool,onAnimationEnd:e6().func,shouldReAnimate:e6().bool,onAnimationStart:e6().func,onAnimationReStart:e6().func};var nl=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,s=r>=0?1:-1,u=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var l=[0,0,0,0],f=0;f<4;f++)l[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*l[0]),l[0]>0&&(o+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(u,",").concat(t+s*l[0],",").concat(e)),o+="L ".concat(t+r-s*l[1],",").concat(e),l[1]>0&&(o+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(u,",\n        ").concat(t+r,",").concat(e+c*l[1])),o+="L ".concat(t+r,",").concat(e+n-c*l[2]),l[2]>0&&(o+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(u,",\n        ").concat(t+r-s*l[2],",").concat(e+n)),o+="L ".concat(t+s*l[3],",").concat(e+n),l[3]>0&&(o+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(u,",\n        ").concat(t,",").concat(e+n-c*l[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(u,",").concat(t+s*p,",").concat(e,"\n            L ").concat(t+r-s*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(u,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(u,",").concat(t+r-s*p,",").concat(e+n,"\n            L ").concat(t+s*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(u,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},nf=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var s=Math.min(i,i+a),u=Math.max(i,i+a),l=Math.min(o,o+c),f=Math.max(o,o+c);return r>=s&&r<=u&&n>=l&&n<=f}return!1},np={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nd=function(t){var e,r=nu(nu({},np),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{o=(r=r.call(t)).next,!1;for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nc(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nc(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],s=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&s(t)}catch(t){}},[]);var u=r.x,l=r.y,f=r.width,p=r.height,d=r.radius,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(u!==+u||l!==+l||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,E.A)("recharts-rectangle",h);return g?a().createElement(ni,{canBegin:c>0,from:{width:f,height:p,x:u,y:l},to:{width:f,height:p,x:u,y:l},duration:v,animationEasing:y,isActive:g},function(t){var e=t.width,i=t.height,o=t.x,s=t.y;return a().createElement(ni,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},a().createElement("path",na({},tx(r,!0),{className:x,d:nl(o,s,e,i,d),ref:n})))}):a().createElement("path",na({},tx(r,!0),{className:x,d:nl(u,l,f,p,d)}))};function nh(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function ny(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nv extends Map{constructor(t,e=nb){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nm(this,t))}has(t){return super.has(nm(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nm({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nb(t){return null!==t&&"object"==typeof t?t.valueOf():t}let ng=Symbol("implicit");function nx(){var t=new nv,e=[],r=[],n=ng;function i(i){let o=t.get(i);if(void 0===o){if(n!==ng)return n;t.set(i,o=e.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nv,r))t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return nx(e,r).unknown(n)},nh.apply(i,arguments),i}function nw(){var t,e,r=nx().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,c=!1,s=0,u=0,l=.5;function f(){var r=n().length,f=a<o,p=f?a:o,d=f?o:a;t=(d-p)/Math.max(1,r-s+2*u),c&&(t=Math.floor(t)),p+=(d-p-t*(r-s))*l,e=t*(1-s),c&&(p=Math.round(p),e=Math.round(e));var h=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return p+t*e});return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([o,a]=t,o*=1,a*=1,f()):[o,a]},r.rangeRound=function(t){return[o,a]=t,o*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(s=Math.min(1,u=+t),f()):s},r.paddingInner=function(t){return arguments.length?(s=Math.min(1,t),f()):s},r.paddingOuter=function(t){return arguments.length?(u=+t,f()):u},r.align=function(t){return arguments.length?(l=Math.max(0,Math.min(1,t)),f()):l},r.copy=function(){return nw(n(),[o,a]).round(c).paddingInner(s).paddingOuter(u).align(l)},nh.apply(f(),arguments)}function nO(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nw.apply(null,arguments).paddingInner(1))}function nj(t){return(nj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nS(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=nj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nj(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nk={widthCache:{},cacheCount:0},n_={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nE="recharts_measurement_span",nM=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||tJ.isSsr)return{width:0,height:0};var n=(Object.keys(e=nP({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:n});if(nk.widthCache[i])return nk.widthCache[i];try{var o=document.getElementById(nE);o||((o=document.createElement("span")).setAttribute("id",nE),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var a=nP(nP({},n_),n);Object.assign(o.style,a),o.textContent="".concat(t);var c=o.getBoundingClientRect(),s={width:c.width,height:c.height};return nk.widthCache[i]=s,++nk.cacheCount>2e3&&(nk.cacheCount=0,nk.widthCache={}),s}catch(t){return{width:0,height:0}}};function nT(t){return(nT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nN(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nC(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nC(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nC(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nD(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nT(e)?e:e+""}(n.key),n)}}var nI=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nB=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nL=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nR=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nU={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nz=Object.keys(nU),n$=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nL.test(e)||(this.num=NaN,this.unit=""),nz.includes(e)&&(this.num=t*nU[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nN(null!=(e=nR.exec(t))?e:[],3),i=n[1],o=n[2];return new r(parseFloat(i),null!=o?o:"")}}],t&&nD(r.prototype,t),e&&nD(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nq(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nN(null!=(r=nI.exec(e))?r:[],4),i=n[1],o=n[2],a=n[3],c=n$.parse(null!=i?i:""),s=n$.parse(null!=a?a:""),u="*"===o?c.multiply(s):c.divide(s);if(u.isNaN())return"NaN";e=e.replace(nI,u.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var l,f=nN(null!=(l=nB.exec(e))?l:[],4),p=f[1],d=f[2],h=f[3],y=n$.parse(null!=p?p:""),v=n$.parse(null!=h?h:""),m="+"===d?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nB,m.toString())}return e}var nF=/\(([^()]*)\)/;function nV(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nN(nF.exec(e),2)[1];e=e.replace(nF,nq(r))}return e}(e),e=nq(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var nW=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nG=["dx","dy","angle","className","breakAll"];function nY(){return(nY=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nH(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function nX(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nK(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nK(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nK(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nZ=/[ \f\n\r\t\v\u2028\u2029]+/,nJ=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];b()(e)||(i=r?e.toString().split(""):e.toString().split(nZ));var o=i.map(function(t){return{word:t,width:nM(t,n).width}}),a=r?0:nM("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:a}}catch(t){return null}},nQ=function(t,e,r,n,i){var o,a=t.maxLines,c=t.children,s=t.style,u=t.breakAll,l=q(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var o=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||i||c.width+a+r<Number(n))?(c.words.push(o),c.width+=a+r):t.push({words:[o],width:a}),t},[])},p=f(e);if(!l)return p;for(var d=function(t){var e=f(nJ({breakAll:u,style:s,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},h=0,y=c.length-1,v=0;h<=y&&v<=c.length-1;){var m=Math.floor((h+y)/2),b=nX(d(m-1),2),g=b[0],x=b[1],w=nX(d(m),1)[0];if(g||w||(h=m+1),g&&w&&(y=m-1),!g&&w){o=x;break}v++}return o||p},n0=function(t){return[{words:b()(t)?[]:t.toString().split(nZ)}]},n1=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!tJ.isSsr){var c=nJ({breakAll:o,children:n,style:i});if(!c)return n0(n);var s=c.wordsWithComputedWidth,u=c.spaceWidth;return nQ({breakAll:o,children:n,maxLines:a,style:i},s,u,e,r)}return n0(n)},n2="#808080",n5=function(t){var e,r=t.x,n=void 0===r?0:r,i=t.y,c=void 0===i?0:i,s=t.lineHeight,u=void 0===s?"1em":s,l=t.capHeight,f=void 0===l?"0.71em":l,p=t.scaleToFit,d=void 0!==p&&p,h=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?n2:v,b=nH(t,nW),g=(0,o.useMemo)(function(){return n1({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:d,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,d,b.style,b.width]),x=b.dx,w=b.dy,O=b.angle,j=b.className,S=b.breakAll,P=nH(b,nG);if(!F(n)||!F(c))return null;var A=n+(q(x)?x:0),k=c+(q(w)?w:0);switch(void 0===y?"end":y){case"start":e=nV("calc(".concat(f,")"));break;case"middle":e=nV("calc(".concat((g.length-1)/2," * -").concat(u," + (").concat(f," / 2))"));break;default:e=nV("calc(".concat(g.length-1," * -").concat(u,")"))}var _=[];if(d){var M=g[0].width,T=b.width;_.push("scale(".concat((q(T)?T/M:1)/M,")"))}return O&&_.push("rotate(".concat(O,", ").concat(A,", ").concat(k,")")),_.length&&(P.transform=_.join(" ")),a().createElement("text",nY({},tx(P,!0),{x:A,y:k,className:(0,E.A)("recharts-text",j),textAnchor:void 0===h?"start":h,fill:m.includes("url")?n2:m}),g.map(function(t,r){var n=t.words.join(S?"":" ");return a().createElement("tspan",{x:A,dy:0===r?e:u,key:"".concat(n,"-").concat(r)},n)}))};let n3=Math.sqrt(50),n4=Math.sqrt(10),n6=Math.sqrt(2);function n8(t,e,r){let n,i,o,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),s=a/Math.pow(10,c),u=s>=n3?10:s>=n4?5:s>=n6?2:1;return(c<0?(n=Math.round(t*(o=Math.pow(10,-c)/u)),i=Math.round(e*o),n/o<t&&++n,i/o>e&&--i,o=-o):(n=Math.round(t/(o=Math.pow(10,c)*u)),i=Math.round(e/o),n*o<t&&++n,i*o>e&&--i),i<n&&.5<=r&&r<2)?n8(t,e,2*r):[n,i,o]}function n7(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?n8(e,t,r):n8(t,e,r);if(!(o>=i))return[];let c=o-i+1,s=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)s[t]=-((o-t)/a);else for(let t=0;t<c;++t)s[t]=(o-t)*a;else if(a<0)for(let t=0;t<c;++t)s[t]=-((i+t)/a);else for(let t=0;t<c;++t)s[t]=(i+t)*a;return s}function n9(t,e,r){return n8(t*=1,e*=1,r*=1)[2]}function it(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?n9(e,t,r):n9(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function ie(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ir(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ii(t){let e,r,n;function i(t,n,o=0,a=t.length){if(o<a){if(0!==e(n,n))return a;do{let e=o+a>>>1;0>r(t[e],n)?o=e+1:a=e}while(o<a)}return o}return 2!==t.length?(e=ie,r=(e,r)=>ie(t(e),r),n=(e,r)=>t(e)-r):(e=t===ie||t===ir?t:io,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function io(){return 0}function ia(t){return null===t?NaN:+t}let ic=ii(ie),is=ic.right;function iu(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function il(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function ip(){}ic.left,ii(ia).center;var id="\\s*([+-]?\\d+)\\s*",ih="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",iy="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iv=/^#([0-9a-f]{3,8})$/,im=RegExp(`^rgb\\(${id},${id},${id}\\)$`),ib=RegExp(`^rgb\\(${iy},${iy},${iy}\\)$`),ig=RegExp(`^rgba\\(${id},${id},${id},${ih}\\)$`),ix=RegExp(`^rgba\\(${iy},${iy},${iy},${ih}\\)$`),iw=RegExp(`^hsl\\(${ih},${iy},${iy}\\)$`),iO=RegExp(`^hsla\\(${ih},${iy},${iy},${ih}\\)$`),ij={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iS(){return this.rgb().formatHex()}function iP(){return this.rgb().formatRgb()}function iA(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=iv.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?ik(e):3===r?new iM(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?i_(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?i_(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=im.exec(t))?new iM(e[1],e[2],e[3],1):(e=ib.exec(t))?new iM(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=ig.exec(t))?i_(e[1],e[2],e[3],e[4]):(e=ix.exec(t))?i_(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=iw.exec(t))?iB(e[1],e[2]/100,e[3]/100,1):(e=iO.exec(t))?iB(e[1],e[2]/100,e[3]/100,e[4]):ij.hasOwnProperty(t)?ik(ij[t]):"transparent"===t?new iM(NaN,NaN,NaN,0):null}function ik(t){return new iM(t>>16&255,t>>8&255,255&t,1)}function i_(t,e,r,n){return n<=0&&(t=e=r=NaN),new iM(t,e,r,n)}function iE(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof ip||(i=iA(i)),i)?new iM((i=i.rgb()).r,i.g,i.b,i.opacity):new iM:new iM(t,e,r,null==n?1:n)}function iM(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function iT(){return`#${iI(this.r)}${iI(this.g)}${iI(this.b)}`}function iN(){let t=iC(this.opacity);return`${1===t?"rgb(":"rgba("}${iD(this.r)}, ${iD(this.g)}, ${iD(this.b)}${1===t?")":`, ${t})`}`}function iC(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function iD(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function iI(t){return((t=iD(t))<16?"0":"")+t.toString(16)}function iB(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new iR(t,e,r,n)}function iL(t){if(t instanceof iR)return new iR(t.h,t.s,t.l,t.opacity);if(t instanceof ip||(t=iA(t)),!t)return new iR;if(t instanceof iR)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,c=o-i,s=(o+i)/2;return c?(a=e===o?(r-n)/c+(r<n)*6:r===o?(n-e)/c+2:(e-r)/c+4,c/=s<.5?o+i:2-o-i,a*=60):c=s>0&&s<1?0:a,new iR(a,c,s,t.opacity)}function iR(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function iU(t){return(t=(t||0)%360)<0?t+360:t}function iz(t){return Math.max(0,Math.min(1,t||0))}function i$(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function iq(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}iu(ip,iA,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:iS,formatHex:iS,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return iL(this).formatHsl()},formatRgb:iP,toString:iP}),iu(iM,iE,il(ip,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new iM(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new iM(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new iM(iD(this.r),iD(this.g),iD(this.b),iC(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iT,formatHex:iT,formatHex8:function(){return`#${iI(this.r)}${iI(this.g)}${iI(this.b)}${iI((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:iN,toString:iN})),iu(iR,function(t,e,r,n){return 1==arguments.length?iL(t):new iR(t,e,r,null==n?1:n)},il(ip,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new iR(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new iR(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new iM(i$(t>=240?t-240:t+120,i,n),i$(t,i,n),i$(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new iR(iU(this.h),iz(this.s),iz(this.l),iC(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=iC(this.opacity);return`${1===t?"hsl(":"hsla("}${iU(this.h)}, ${100*iz(this.s)}%, ${100*iz(this.l)}%${1===t?")":`, ${t})`}`}}));let iF=t=>()=>t;function iV(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):iF(isNaN(t)?e:t)}let iW=function t(e){var r,n=1==(r=+e)?iV:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):iF(isNaN(t)?e:t)};function i(t,e){var r=n((t=iE(t)).r,(e=iE(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=iV(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function iG(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),c=Array(i);for(r=0;r<i;++r)n=iE(e[r]),o[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return o=t(o),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=c(t),n+""}}}iG(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,c=n<e-1?t[n+2]:2*o-i;return iq((r-n/e)*e,a,i,o,c)}}),iG(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return iq((r-n/e)*e,i,o,a,c)}});function iY(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var iH=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,iX=RegExp(iH.source,"g");function iK(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?iF(e):("number"===i?iY:"string"===i?(n=iA(e))?(e=n,iW):function(t,e){var r,n,i,o,a,c=iH.lastIndex=iX.lastIndex=0,s=-1,u=[],l=[];for(t+="",e+="";(i=iH.exec(t))&&(o=iX.exec(e));)(a=o.index)>c&&(a=e.slice(c,a),u[s]?u[s]+=a:u[++s]=a),(i=i[0])===(o=o[0])?u[s]?u[s]+=o:u[++s]=o:(u[++s]=null,l.push({i:s,x:iY(i,o)})),c=iX.lastIndex;return c<e.length&&(a=e.slice(c),u[s]?u[s]+=a:u[++s]=a),u.length<2?l[0]?(r=l[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=l.length,function(t){for(var r,n=0;n<e;++n)u[(r=l[n]).i]=r.x(t);return u.join("")})}:e instanceof iA?iW:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=iK(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=iK(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:iY:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function iZ(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function iJ(t){return+t}var iQ=[0,1];function i0(t){return t}function i1(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function i2(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=i1(i,n),o=r(a,o)):(n=i1(n,i),o=r(o,a)),function(t){return o(n(t))}}function i5(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=i1(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=is(t,e,1,n)-1;return o[r](i[r](e))}}function i3(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function i4(){var t,e,r,n,i,o,a=iQ,c=iQ,s=iK,u=i0;function l(){var t,e,r,s=Math.min(a.length,c.length);return u!==i0&&(t=a[0],e=a[s-1],t>e&&(r=t,t=e,e=r),u=function(r){return Math.max(t,Math.min(e,r))}),n=s>2?i5:i2,i=o=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(a.map(t),c,s)))(t(u(e)))}return f.invert=function(r){return u(e((o||(o=n(c,a.map(t),iY)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,iJ),l()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),l()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),s=iZ,l()},f.clamp=function(t){return arguments.length?(u=!!t||i0,l()):u!==i0},f.interpolate=function(t){return arguments.length?(s=t,l()):s},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,l()}}function i6(){return i4()(i0,i0)}var i8=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function i7(t){var e;if(!(e=i8.exec(t)))throw Error("invalid format: "+t);return new i9({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function i9(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function ot(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function oe(t){return(t=ot(Math.abs(t)))?t[1]:NaN}function or(t,e){var r=ot(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}i7.prototype=i9.prototype,i9.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let on={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>or(100*t,e),r:or,s:function(t,e){var r=ot(t,e);if(!r)return t+"";var n=r[0],i=r[1],o=i-(cN=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=n.length;return o===a?n:o>a?n+Array(o-a+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+Array(1-o).join("0")+ot(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function oi(t){return t}var oo=Array.prototype.map,oa=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function oc(t,e,r,n){var i,o,a,c=it(t,e,r);switch((n=i7(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(oe(s)/3)))-oe(Math.abs(c))))||(n.precision=a),cI(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,oe(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=c)))-oe(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-oe(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return cD(n)}function os(t){var e=t.domain;return t.ticks=function(t){var r=e();return n7(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return oc(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,c=o.length-1,s=o[a],u=o[c],l=10;for(u<s&&(i=s,s=u,u=i,i=a,a=c,c=i);l-- >0;){if((i=n9(s,u,r))===n)return o[a]=s,o[c]=u,e(o);if(i>0)s=Math.floor(s/i)*i,u=Math.ceil(u/i)*i;else if(i<0)s=Math.ceil(s*i)/i,u=Math.floor(u*i)/i;else break;n=i}return t},t}function ou(){var t=i6();return t.copy=function(){return i3(t,ou())},nh.apply(t,arguments),os(t)}function ol(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function of(t){return Math.log(t)}function op(t){return Math.exp(t)}function od(t){return-Math.log(-t)}function oh(t){return-Math.exp(-t)}function oy(t){return isFinite(t)?+("1e"+t):t<0?0:t}function ov(t){return(e,r)=>-t(-e,r)}function om(t){let e,r,n=t(of,op),i=n.domain,o=10;function a(){var a,c;return e=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=o)?oy:c===Math.E?Math.exp:t=>Math.pow(c,t),i()[0]<0?(e=ov(e),r=ov(r),t(od,oh)):t(of,op),n}return n.base=function(t){return arguments.length?(o=+t,a()):o},n.domain=function(t){return arguments.length?(i(t),a()):i()},n.ticks=t=>{let n,a,c=i(),s=c[0],u=c[c.length-1],l=u<s;l&&([s,u]=[u,s]);let f=e(s),p=e(u),d=null==t?10:+t,h=[];if(!(o%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),s>0){for(;f<=p;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<s)){if(a>u)break;h.push(a)}}else for(;f<=p;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<s)){if(a>u)break;h.push(a)}2*h.length<d&&(h=n7(s,u,d))}else h=n7(f,p,Math.min(p-f,d)).map(r);return l?h.reverse():h},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=i7(i)).precision||(i.trim=!0),i=cD(i)),t===1/0)return i;let a=Math.max(1,o*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*o<o-.5&&(n*=o),n<=a?i(t):""}},n.nice=()=>i(ol(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function ob(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function og(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function ox(t){var e=1,r=t(ob(1),og(e));return r.constant=function(r){return arguments.length?t(ob(e=+r),og(e)):e},os(r)}function ow(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function oO(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function oj(t){return t<0?-t*t:t*t}function oS(t){var e=t(i0,i0),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(i0,i0):.5===r?t(oO,oj):t(ow(r),ow(1/r)):r},os(e)}function oP(){var t=oS(i4());return t.copy=function(){return i3(t,oP()).exponent(t.exponent())},nh.apply(t,arguments),t}function oA(){return oP.apply(null,arguments).exponent(.5)}function ok(t){return Math.sign(t)*t*t}function o_(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function oE(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}cD=(cC=function(t){var e,r,n,i=void 0===t.grouping||void 0===t.thousands?oi:(e=oo.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,c=e[0],s=0;i>0&&c>0&&(s+c+1>n&&(c=Math.max(1,n-s)),o.push(t.substring(i-=c,i+c)),!((s+=c+1)>n));)c=e[a=(a+1)%e.length];return o.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",s=void 0===t.numerals?oi:(n=oo.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),u=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=i7(t)).fill,r=t.align,n=t.sign,p=t.symbol,d=t.zero,h=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):on[b]||(void 0===v&&(v=12),m=!0,b="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?u:"",w=on[b],O=/[defgprs%]/.test(b);function j(t){var o,a,u,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:l:"-"===n||"("===n?"":n)+p,j=("s"===b?oa[8+cN/3]:"")+j+(S&&"("===n?")":""),O){for(o=-1,a=t.length;++o<a;)if(48>(u=t.charCodeAt(o))||u>57){j=(46===u?c+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}}y&&!d&&(t=i(t,1/0));var P=p.length+t.length+j.length,A=P<h?Array(h-P+1).join(e):"";switch(y&&d&&(t=i(A+t,A.length?h-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return s(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=i7(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(oe(e)/3))),i=Math.pow(10,-n),o=oa[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cI=cC.formatPrefix;function oM(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function oT(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let oN=new Date,oC=new Date;function oD(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a,c=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return c;do c.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return c},i.filter=r=>oD(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(oN.setTime(+e),oC.setTime(+n),t(oN),t(oC),Math.floor(r(oN,oC))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let oI=oD(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);oI.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?oD(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):oI:null,oI.range;let oB=oD(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());oB.range;let oL=oD(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());oL.range;let oR=oD(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());oR.range;let oU=oD(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());oU.range;let oz=oD(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());oz.range;let o$=oD(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);o$.range;let oq=oD(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);oq.range;let oF=oD(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function oV(t){return oD(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}oF.range;let oW=oV(0),oG=oV(1),oY=oV(2),oH=oV(3),oX=oV(4),oK=oV(5),oZ=oV(6);function oJ(t){return oD(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}oW.range,oG.range,oY.range,oH.range,oX.range,oK.range,oZ.range;let oQ=oJ(0),o0=oJ(1),o1=oJ(2),o2=oJ(3),o5=oJ(4),o3=oJ(5),o4=oJ(6);oQ.range,o0.range,o1.range,o2.range,o5.range,o3.range,o4.range;let o6=oD(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());o6.range;let o8=oD(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());o8.range;let o7=oD(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());o7.every=t=>isFinite(t=Math.floor(t))&&t>0?oD(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,o7.range;let o9=oD(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function at(t,e,r,n,i,o){let a=[[oB,1,1e3],[oB,5,5e3],[oB,15,15e3],[oB,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let i=Math.abs(r-e)/n,o=ii(([,,t])=>t).right(a,i);if(o===a.length)return t.every(it(e/31536e6,r/31536e6,n));if(0===o)return oI.every(Math.max(it(e,r,n),1));let[c,s]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return c.every(s)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:c(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},c]}o9.every=t=>isFinite(t=Math.floor(t))&&t>0?oD(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,o9.range;let[ae,ar]=at(o9,o8,oQ,oF,oz,oR),[an,ai]=at(o7,o6,oW,o$,oU,oL);function ao(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function aa(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ac(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var as={"-":"",_:" ",0:"0"},au=/^\s*\d+/,al=/^%/,af=/[\\^$*+?|[\]().{}]/g;function ap(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function ad(t){return t.replace(af,"\\$&")}function ah(t){return RegExp("^(?:"+t.map(ad).join("|")+")","i")}function ay(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function av(t,e,r){var n=au.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function am(t,e,r){var n=au.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function ab(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function ag(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function ax(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function aw(t,e,r){var n=au.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aO(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aj(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aS(t,e,r){var n=au.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aP(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aA(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function ak(t,e,r){var n=au.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function a_(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function aE(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aM(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aT(t,e,r){var n=au.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aN(t,e,r){var n=au.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aC(t,e,r){var n=al.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aD(t,e,r){var n=au.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=au.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aB(t,e){return ap(t.getDate(),e,2)}function aL(t,e){return ap(t.getHours(),e,2)}function aR(t,e){return ap(t.getHours()%12||12,e,2)}function aU(t,e){return ap(1+o$.count(o7(t),t),e,3)}function az(t,e){return ap(t.getMilliseconds(),e,3)}function a$(t,e){return az(t,e)+"000"}function aq(t,e){return ap(t.getMonth()+1,e,2)}function aF(t,e){return ap(t.getMinutes(),e,2)}function aV(t,e){return ap(t.getSeconds(),e,2)}function aW(t){var e=t.getDay();return 0===e?7:e}function aG(t,e){return ap(oW.count(o7(t)-1,t),e,2)}function aY(t){var e=t.getDay();return e>=4||0===e?oX(t):oX.ceil(t)}function aH(t,e){return t=aY(t),ap(oX.count(o7(t),t)+(4===o7(t).getDay()),e,2)}function aX(t){return t.getDay()}function aK(t,e){return ap(oG.count(o7(t)-1,t),e,2)}function aZ(t,e){return ap(t.getFullYear()%100,e,2)}function aJ(t,e){return ap((t=aY(t)).getFullYear()%100,e,2)}function aQ(t,e){return ap(t.getFullYear()%1e4,e,4)}function a0(t,e){var r=t.getDay();return ap((t=r>=4||0===r?oX(t):oX.ceil(t)).getFullYear()%1e4,e,4)}function a1(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ap(e/60|0,"0",2)+ap(e%60,"0",2)}function a2(t,e){return ap(t.getUTCDate(),e,2)}function a5(t,e){return ap(t.getUTCHours(),e,2)}function a3(t,e){return ap(t.getUTCHours()%12||12,e,2)}function a4(t,e){return ap(1+oq.count(o9(t),t),e,3)}function a6(t,e){return ap(t.getUTCMilliseconds(),e,3)}function a8(t,e){return a6(t,e)+"000"}function a7(t,e){return ap(t.getUTCMonth()+1,e,2)}function a9(t,e){return ap(t.getUTCMinutes(),e,2)}function ct(t,e){return ap(t.getUTCSeconds(),e,2)}function ce(t){var e=t.getUTCDay();return 0===e?7:e}function cr(t,e){return ap(oQ.count(o9(t)-1,t),e,2)}function cn(t){var e=t.getUTCDay();return e>=4||0===e?o5(t):o5.ceil(t)}function ci(t,e){return t=cn(t),ap(o5.count(o9(t),t)+(4===o9(t).getUTCDay()),e,2)}function co(t){return t.getUTCDay()}function ca(t,e){return ap(o0.count(o9(t)-1,t),e,2)}function cc(t,e){return ap(t.getUTCFullYear()%100,e,2)}function cs(t,e){return ap((t=cn(t)).getUTCFullYear()%100,e,2)}function cu(t,e){return ap(t.getUTCFullYear()%1e4,e,4)}function cl(t,e){var r=t.getUTCDay();return ap((t=r>=4||0===r?o5(t):o5.ceil(t)).getUTCFullYear()%1e4,e,4)}function cf(){return"+0000"}function cp(){return"%"}function cd(t){return+t}function ch(t){return Math.floor(t/1e3)}function cy(t){return new Date(t)}function cv(t){return t instanceof Date?+t:+new Date(+t)}function cm(t,e,r,n,i,o,a,c,s,u){var l=i6(),f=l.invert,p=l.domain,d=u(".%L"),h=u(":%S"),y=u("%I:%M"),v=u("%I %p"),m=u("%a %d"),b=u("%b %d"),g=u("%B"),x=u("%Y");function w(t){return(s(t)<t?d:c(t)<t?h:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:b:r(t)<t?g:x)(t)}return l.invert=function(t){return new Date(f(t))},l.domain=function(t){return arguments.length?p(Array.from(t,cv)):p().map(cy)},l.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},l.tickFormat=function(t,e){return null==e?w:u(e)},l.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(ol(r,t)):l},l.copy=function(){return i3(l,cm(t,e,r,n,i,o,a,c,s,u))},l}function cb(){return nh.apply(cm(an,ai,o7,o6,oW,o$,oU,oL,oB,cL).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cg(){return nh.apply(cm(ae,ar,o9,o8,oQ,oq,oz,oR,oB,cR).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cx(){var t,e,r,n,i,o=0,a=1,c=i0,s=!1;function u(e){return null==e||isNaN(e*=1)?i:c(0===r?.5:(e=(n(e)-t)*r,s?Math.max(0,Math.min(1,e)):e))}function l(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),u):[c(0),c(1)]}}return u.domain=function(i){return arguments.length?([o,a]=i,t=n(o*=1),e=n(a*=1),r=t===e?0:1/(e-t),u):[o,a]},u.clamp=function(t){return arguments.length?(s=!!t,u):s},u.interpolator=function(t){return arguments.length?(c=t,u):c},u.range=l(iK),u.rangeRound=l(iZ),u.unknown=function(t){return arguments.length?(i=t,u):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),u}}function cw(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cO(){var t=oS(cx());return t.copy=function(){return cw(t,cO()).exponent(t.exponent())},ny.apply(t,arguments)}function cj(){return cO.apply(null,arguments).exponent(.5)}function cS(){var t,e,r,n,i,o,a,c=0,s=.5,u=1,l=1,f=i0,p=!1;function d(t){return isNaN(t*=1)?a:(t=.5+((t=+o(t))-e)*(l*t<l*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function h(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=iK);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([c,s,u]=a,t=o(c*=1),e=o(s*=1),r=o(u*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),l=e<t?-1:1,d):[c,s,u]},d.clamp=function(t){return arguments.length?(p=!!t,d):p},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=h(iK),d.rangeRound=h(iZ),d.unknown=function(t){return arguments.length?(a=t,d):a},function(a){return o=a,t=a(c),e=a(s),r=a(u),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),l=e<t?-1:1,d}}function cP(){var t=oS(cS());return t.copy=function(){return cw(t,cP()).exponent(t.exponent())},ny.apply(t,arguments)}function cA(){return cP.apply(null,arguments).exponent(.5)}function ck(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],c=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function c_(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function cE(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cM(t,e){return t[e]}function cT(t){let e=[];return e.key=t,e}cL=(cB=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,c=t.months,s=t.shortMonths,u=ah(i),l=ay(i),f=ah(o),p=ay(o),d=ah(a),h=ay(a),y=ah(c),v=ay(c),m=ah(s),b=ay(s),g={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return s[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aB,e:aB,f:a$,g:aJ,G:a0,H:aL,I:aR,j:aU,L:az,m:aq,M:aF,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cd,s:ch,S:aV,u:aW,U:aG,V:aH,w:aX,W:aK,x:null,X:null,y:aZ,Y:aQ,Z:a1,"%":cp},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return s[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:a2,e:a2,f:a8,g:cs,G:cl,H:a5,I:a3,j:a4,L:a6,m:a7,M:a9,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cd,s:ch,S:ct,u:ce,U:cr,V:ci,w:co,W:ca,x:null,X:null,y:cc,Y:cu,Z:cf,"%":cp},w={a:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aA,e:aA,f:aN,g:aO,G:aw,H:a_,I:a_,j:ak,L:aT,m:aP,M:aE,p:function(t,e,r){var n=u.exec(e.slice(r));return n?(t.p=l.get(n[0].toLowerCase()),r+n[0].length):-1},q:aS,Q:aD,s:aI,S:aM,u:am,U:ab,V:ag,w:av,W:ax,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aO,Y:aw,Z:aj,"%":aC};function O(t,e){return function(r){var n,i,o,a=[],c=-1,s=0,u=t.length;for(r instanceof Date||(r=new Date(+r));++c<u;)37===t.charCodeAt(c)&&(a.push(t.slice(s,c)),null!=(i=as[n=t.charAt(++c)])?n=t.charAt(++c):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),s=c+1);return a.push(t.slice(s,c)),a.join("")}}function j(t,e){return function(r){var n,i,o=ac(1900,void 0,1);if(S(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=aa(ac(o.y,0,1))).getUTCDay())>4||0===i?o0.ceil(n):o0(n),n=oq.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=ao(ac(o.y,0,1))).getDay())>4||0===i?oG.ceil(n):oG(n),n=o$.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?aa(ac(o.y,0,1)).getUTCDay():ao(ac(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,aa(o)):ao(o)}}function S(t,e,r,n){for(var i,o,a=0,c=e.length,s=r.length;a<c;){if(n>=s)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=w[(i=e.charAt(a++))in as?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cB.parse,cR=cB.utcFormat,cB.utcParse,Array.prototype.slice;var cN,cC,cD,cI,cB,cL,cR,cU,cz,c$=r(90453),cq=r.n(c$),cF=r(15883),cV=r.n(cF),cW=r(21592),cG=r.n(cW),cY=r(71967),cH=r.n(cY),cX=!0,cK="[DecimalError] ",cZ=cK+"Invalid argument: ",cJ=cK+"Exponent out of range: ",cQ=Math.floor,c0=Math.pow,c1=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,c2=cQ(1286742750677284.5),c5={};function c3(t,e){var r,n,i,o,a,c,s,u,l=t.constructor,f=l.precision;if(!t.s||!e.s)return e.s||(e=new l(t)),cX?si(e,f):e;if(s=t.d,u=e.d,a=t.e,i=e.e,s=s.slice(),o=a-i){for(o<0?(n=s,o=-o,c=u.length):(n=u,i=a,c=s.length),o>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=s.length)-(o=u.length)<0&&(o=c,n=u,u=s,s=n),r=0;o;)r=(s[--o]=s[o]+u[o]+r)/1e7|0,s[o]%=1e7;for(r&&(s.unshift(r),++i),c=s.length;0==s[--c];)s.pop();return e.d=s,e.e=i,cX?si(e,f):e}function c4(t,e,r){if(t!==~~t||t<e||t>r)throw Error(cZ+t)}function c6(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=se(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=se(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}c5.absoluteValue=c5.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},c5.comparedTo=c5.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},c5.decimalPlaces=c5.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},c5.dividedBy=c5.div=function(t){return c8(this,new this.constructor(t))},c5.dividedToIntegerBy=c5.idiv=function(t){var e=this.constructor;return si(c8(this,new e(t),0,1),e.precision)},c5.equals=c5.eq=function(t){return!this.cmp(t)},c5.exponent=function(){return c9(this)},c5.greaterThan=c5.gt=function(t){return this.cmp(t)>0},c5.greaterThanOrEqualTo=c5.gte=function(t){return this.cmp(t)>=0},c5.isInteger=c5.isint=function(){return this.e>this.d.length-2},c5.isNegative=c5.isneg=function(){return this.s<0},c5.isPositive=c5.ispos=function(){return this.s>0},c5.isZero=function(){return 0===this.s},c5.lessThan=c5.lt=function(t){return 0>this.cmp(t)},c5.lessThanOrEqualTo=c5.lte=function(t){return 1>this.cmp(t)},c5.logarithm=c5.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(cz))throw Error(cK+"NaN");if(this.s<1)throw Error(cK+(this.s?"NaN":"-Infinity"));return this.eq(cz)?new r(0):(cX=!1,e=c8(sr(this,i),sr(t,i),i),cX=!0,si(e,n))},c5.minus=c5.sub=function(t){return t=new this.constructor(t),this.s==t.s?so(this,t):c3(this,(t.s=-t.s,t))},c5.modulo=c5.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(cK+"NaN");return this.s?(cX=!1,e=c8(this,t,0,1).times(t),cX=!0,this.minus(e)):si(new r(this),n)},c5.naturalExponential=c5.exp=function(){return c7(this)},c5.naturalLogarithm=c5.ln=function(){return sr(this)},c5.negated=c5.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},c5.plus=c5.add=function(t){return t=new this.constructor(t),this.s==t.s?c3(this,t):so(this,(t.s=-t.s,t))},c5.precision=c5.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(cZ+t);if(e=c9(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},c5.squareRoot=c5.sqrt=function(){var t,e,r,n,i,o,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(cK+"NaN")}for(t=c9(this),cX=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=c6(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=cQ((t+1)/2)-(t<0||t%2),n=new c(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(i.toString()),i=a=(r=c.precision)+3;;)if(n=(o=n).plus(c8(this,o,a+2)).times(.5),c6(o.d).slice(0,a)===(e=c6(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(si(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return cX=!0,si(n,r)},c5.times=c5.mul=function(t){var e,r,n,i,o,a,c,s,u,l=this.constructor,f=this.d,p=(t=new l(t)).d;if(!this.s||!t.s)return new l(0);for(t.s*=this.s,r=this.e+t.e,(s=f.length)<(u=p.length)&&(o=f,f=p,p=o,a=s,s=u,u=a),o=[],n=a=s+u;n--;)o.push(0);for(n=u;--n>=0;){for(e=0,i=s+n;i>n;)c=o[i]+p[n]*f[i-n-1]+e,o[i--]=c%1e7|0,e=c/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,cX?si(t,l.precision):t},c5.toDecimalPlaces=c5.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(c4(t,0,1e9),void 0===e?e=n.rounding:c4(e,0,8),si(r,t+c9(r)+1,e))},c5.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=sa(n,!0):(c4(t,0,1e9),void 0===e?e=i.rounding:c4(e,0,8),r=sa(n=si(new i(n),t+1,e),!0,t+1)),r},c5.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?sa(this):(c4(t,0,1e9),void 0===e?e=i.rounding:c4(e,0,8),r=sa((n=si(new i(this),t+c9(this)+1,e)).abs(),!1,t+c9(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},c5.toInteger=c5.toint=function(){var t=this.constructor;return si(new t(this),c9(this)+1,t.rounding)},c5.toNumber=function(){return+this},c5.toPower=c5.pow=function(t){var e,r,n,i,o,a,c=this,s=c.constructor,u=+(t=new s(t));if(!t.s)return new s(cz);if(!(c=new s(c)).s){if(t.s<1)throw Error(cK+"Infinity");return c}if(c.eq(cz))return c;if(n=s.precision,t.eq(cz))return si(c,n);if(a=(e=t.e)>=(r=t.d.length-1),o=c.s,a){if((r=u<0?-u:u)<=0x1fffffffffffff){for(i=new s(cz),e=Math.ceil(n/7+4),cX=!1;r%2&&sc((i=i.times(c)).d,e),0!==(r=cQ(r/2));)sc((c=c.times(c)).d,e);return cX=!0,t.s<0?new s(cz).div(i):si(i,n)}}else if(o<0)throw Error(cK+"NaN");return o=o<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,cX=!1,i=t.times(sr(c,n+12)),cX=!0,(i=c7(i)).s=o,i},c5.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=c9(i),n=sa(i,r<=o.toExpNeg||r>=o.toExpPos)):(c4(t,1,1e9),void 0===e?e=o.rounding:c4(e,0,8),r=c9(i=si(new o(i),t,e)),n=sa(i,t<=r||r<=o.toExpNeg,t)),n},c5.toSignificantDigits=c5.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(c4(t,1,1e9),void 0===e?e=r.rounding:c4(e,0,8)),si(new r(this),t,e)},c5.toString=c5.valueOf=c5.val=c5.toJSON=c5[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=c9(this),e=this.constructor;return sa(this,t<=e.toExpNeg||t>=e.toExpPos)};var c8=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var c,s,u,l,f,p,d,h,y,v,m,b,g,x,w,O,j,S,P=n.constructor,A=n.s==i.s?1:-1,k=n.d,_=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(cK+"Division by zero");for(u=0,s=n.e-i.e,j=_.length,w=k.length,h=(d=new P(A)).d=[];_[u]==(k[u]||0);)++u;if(_[u]>(k[u]||0)&&--s,(b=null==o?o=P.precision:a?o+(c9(n)-c9(i))+1:o)<0)return new P(0);if(b=b/7+2|0,u=0,1==j)for(l=0,_=_[0],b++;(u<w||l)&&b--;u++)g=1e7*l+(k[u]||0),h[u]=g/_|0,l=g%_|0;else{for((l=1e7/(_[0]+1)|0)>1&&(_=t(_,l),k=t(k,l),j=_.length,w=k.length),x=j,v=(y=k.slice(0,j)).length;v<j;)y[v++]=0;(S=_.slice()).unshift(0),O=_[0],_[1]>=1e7/2&&++O;do l=0,(c=e(_,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(l=m/O|0)>1?(l>=1e7&&(l=1e7-1),p=(f=t(_,l)).length,v=y.length,1==(c=e(f,y,p,v))&&(l--,r(f,j<p?S:_,p))):(0==l&&(c=l=1),f=_.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(_,y,j,v))<1&&(l++,r(y,j<v?S:_,v))),v=y.length):0===c&&(l++,y=[0]),h[u++]=l,c&&y[0]?y[v++]=k[x]||0:(y=[k[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return h[0]||h.shift(),d.e=s,si(d,a?o+c9(d)+1:o)}}();function c7(t,e){var r,n,i,o,a,c=0,s=0,u=t.constructor,l=u.precision;if(c9(t)>16)throw Error(cJ+c9(t));if(!t.s)return new u(cz);for(null==e?(cX=!1,a=l):a=e,o=new u(.03125);t.abs().gte(.1);)t=t.times(o),s+=5;for(a+=Math.log(c0(2,s))/Math.LN10*2+5|0,r=n=i=new u(cz),u.precision=a;;){if(n=si(n.times(t),a),r=r.times(++c),c6((o=i.plus(c8(n,r,a))).d).slice(0,a)===c6(i.d).slice(0,a)){for(;s--;)i=si(i.times(i),a);return u.precision=l,null==e?(cX=!0,si(i,l)):i}i=o}}function c9(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function st(t,e,r){if(e>t.LN10.sd())throw cX=!0,r&&(t.precision=r),Error(cK+"LN10 precision limit exceeded");return si(new t(t.LN10),e)}function se(t){for(var e="";t--;)e+="0";return e}function sr(t,e){var r,n,i,o,a,c,s,u,l,f=1,p=t,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(cK+(p.s?"NaN":"-Infinity"));if(p.eq(cz))return new h(0);if(null==e?(cX=!1,u=y):u=e,p.eq(10))return null==e&&(cX=!0),st(h,u);if(h.precision=u+=10,n=(r=c6(d)).charAt(0),!(15e14>Math.abs(o=c9(p))))return s=st(h,u+2,y).times(o+""),p=sr(new h(n+"."+r.slice(1)),u-10).plus(s),h.precision=y,null==e?(cX=!0,si(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=c6((p=p.times(t)).d)).charAt(0),f++;for(o=c9(p),n>1?(p=new h("0."+r),o++):p=new h(n+"."+r.slice(1)),c=a=p=c8(p.minus(cz),p.plus(cz),u),l=si(p.times(p),u),i=3;;){if(a=si(a.times(l),u),c6((s=c.plus(c8(a,new h(i),u))).d).slice(0,u)===c6(c.d).slice(0,u))return c=c.times(2),0!==o&&(c=c.plus(st(h,u+2,y).times(o+""))),c=c8(c,new h(f),u),h.precision=y,null==e?(cX=!0,si(c,y)):c;c=s,i+=2}}function sn(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=cQ((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),cX&&(t.e>c2||t.e<-c2))throw Error(cJ+r)}else t.s=0,t.e=0,t.d=[0];return t}function si(t,e,r){var n,i,o,a,c,s,u,l,f=t.d;for(a=1,o=f[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,u=f[l=0];else{if((l=Math.ceil((n+1)/7))>=(o=f.length))return t;for(a=1,u=o=f[l];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(c=u/(o=c0(10,a-i-1))%10|0,s=e<0||void 0!==f[l+1]||u%o,s=r<4?(c||s)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||s||6==r&&(n>0?i>0?u/c0(10,a-i):0:f[l-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return s?(o=c9(t),f.length=1,e=e-o-1,f[0]=c0(10,(7-e%7)%7),t.e=cQ(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=l,o=1,l--):(f.length=l+1,o=c0(10,7-n),f[l]=i>0?(u/c0(10,a-i)%c0(10,i)|0)*o:0),s)for(;;)if(0==l){1e7==(f[0]+=o)&&(f[0]=1,++t.e);break}else{if(f[l]+=o,1e7!=f[l])break;f[l--]=0,o=1}for(n=f.length;0===f[--n];)f.pop();if(cX&&(t.e>c2||t.e<-c2))throw Error(cJ+c9(t));return t}function so(t,e){var r,n,i,o,a,c,s,u,l,f,p=t.constructor,d=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),cX?si(e,d):e;if(s=t.d,f=e.d,n=e.e,u=t.e,s=s.slice(),a=u-n){for((l=a<0)?(r=s,a=-a,c=f.length):(r=f,n=u,c=s.length),a>(i=Math.max(Math.ceil(d/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((l=(i=s.length)<(c=f.length))&&(c=i),i=0;i<c;i++)if(s[i]!=f[i]){l=s[i]<f[i];break}a=0}for(l&&(r=s,s=f,f=r,e.s=-e.s),c=s.length,i=f.length-c;i>0;--i)s[c++]=0;for(i=f.length;i>a;){if(s[--i]<f[i]){for(o=i;o&&0===s[--o];)s[o]=1e7-1;--s[o],s[i]+=1e7}s[i]-=f[i]}for(;0===s[--c];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(e.d=s,e.e=n,cX?si(e,d):e):new p(0)}function sa(t,e,r){var n,i=c9(t),o=c6(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+se(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+se(-i-1)+o,r&&(n=r-a)>0&&(o+=se(n))):i>=a?(o+=se(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+se(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=se(n))),t.s<0?"-"+o:o}function sc(t,e){if(t.length>e)return t.length=e,!0}function ss(t){if(!t||"object"!=typeof t)throw Error(cK+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(cQ(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(cZ+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(cZ+r+": "+n);return this}var cU=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(cZ+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return sn(this,t.toString())}if("string"!=typeof t)throw Error(cZ+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,c1.test(t))sn(this,t);else throw Error(cZ+t)}if(o.prototype=c5,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=ss,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cz=new cU(1);let su=cU;function sl(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var sf=function(t){return t},sp={},sd=function(t){return t===sp},sh=function(t){return function e(){return 0==arguments.length||1==arguments.length&&sd(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},sy=function(t){return function t(e,r){return 1===e?r:sh(function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter(function(t){return t!==sp}).length;return a>=e?r.apply(void 0,i):t(e-a,sh(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map(function(t){return sd(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return sl(t)})(o)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sl(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sl(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},sv=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},sm=sy(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),sb=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return sf;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce(function(t,e){return e(t)},i.apply(void 0,arguments))}},sg=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},sx=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every(function(t,r){return t===e[r]})?r:(e=i,r=t.apply(void 0,i))}};sy(function(t,e,r){var n=+t;return n+r*(e-n)}),sy(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),sy(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let sw={rangeStep:function(t,e,r){for(var n=new su(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new su(t).abs().log(10).toNumber())+1}};function sO(t){return function(t){if(Array.isArray(t))return sP(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||sS(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sj(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw o}}return r}}(t,e)||sS(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sS(t,e){if(t){if("string"==typeof t)return sP(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sP(t,e)}}function sP(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sA(t){var e=sj(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function sk(t,e,r){if(t.lte(0))return new su(0);var n=sw.getDigitCount(t.toNumber()),i=new su(10).pow(n),o=t.div(i),a=1!==n?.05:.1,c=new su(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?c:new su(Math.ceil(c))}function s_(t,e,r){var n=1,i=new su(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new su(10).pow(sw.getDigitCount(t)-1),i=new su(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new su(Math.floor(t)))}else 0===t?i=new su(Math.floor((e-1)/2)):r||(i=new su(Math.floor(t)));var a=Math.floor((e-1)/2);return sb(sm(function(t){return i.add(new su(t-a).mul(n)).toNumber()}),sv)(0,e)}var sE=sx(function(t){var e=sj(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=sj(sA([r,n]),2),s=c[0],u=c[1];if(s===-1/0||u===1/0){var l=u===1/0?[s].concat(sO(sv(0,i-1).map(function(){return 1/0}))):[].concat(sO(sv(0,i-1).map(function(){return-1/0})),[u]);return r>n?sg(l):l}if(s===u)return s_(s,i,o);var f=function t(e,r,n,i){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new su(0),tickMin:new su(0),tickMax:new su(0)};var c=sk(new su(r).sub(e).div(n-1),i,a),s=Math.ceil((o=e<=0&&r>=0?new su(0):(o=new su(e).add(r).div(2)).sub(new su(o).mod(c))).sub(e).div(c).toNumber()),u=Math.ceil(new su(r).sub(o).div(c).toNumber()),l=s+u+1;return l>n?t(e,r,n,i,a+1):(l<n&&(u=r>0?u+(n-l):u,s=r>0?s:s+(n-l)),{step:c,tickMin:o.sub(new su(s).mul(c)),tickMax:o.add(new su(u).mul(c))})}(s,u,a,o),p=f.step,d=f.tickMin,h=f.tickMax,y=sw.rangeStep(d,h.add(new su(.1).mul(p)),p);return r>n?sg(y):y});sx(function(t){var e=sj(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=sj(sA([r,n]),2),s=c[0],u=c[1];if(s===-1/0||u===1/0)return[r,n];if(s===u)return s_(s,i,o);var l=sk(new su(u).sub(s).div(a-1),o,0),f=sb(sm(function(t){return new su(s).add(new su(t).mul(l)).toNumber()}),sv)(0,a).filter(function(t){return t>=s&&t<=u});return r>n?sg(f):f});var sM=sx(function(t,e){var r=sj(t,2),n=r[0],i=r[1],o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=sj(sA([n,i]),2),c=a[0],s=a[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var u=Math.max(e,2),l=sk(new su(s).sub(c).div(u-1),o,0),f=[].concat(sO(sw.rangeStep(new su(c),new su(s).sub(new su(.99).mul(l)),l)),[s]);return n>i?sg(f):f}),sT=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function sN(t){return(sN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sC(){return(sC=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sD(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sI(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(sI=function(){return!!t})()}function sB(t){return(sB=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function sL(t,e){return(sL=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function sR(t,e,r){return(e=sU(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sU(t){var e=function(t,e){if("object"!=sN(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sN(e)?e:e+""}var sz=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=sB(t),function(t,e){if(e&&("object"===sN(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,sI()?Reflect.construct(t,e||[],sB(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&sL(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,i=t.dataKey,o=t.data,c=t.dataPointFormatter,s=t.xAxis,u=t.yAxis,l=tx(function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,sT),!1);"x"===this.props.direction&&"number"!==s.type&&M(!1);var f=o.map(function(t){var o,f,p=c(t,i),d=p.x,h=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{o=(r=r.call(t)).next,!1;for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return sD(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sD(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=b[0],f=b[1]}else o=f=v;if("vertical"===r){var g=s.scale,x=h+e,w=x+n,O=x-n,j=g(y-o),S=g(y+f);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var P=u.scale,A=d+e,k=A-n,_=A+n,E=P(y-o),M=P(y+f);m.push({x1:k,y1:M,x2:_,y2:M}),m.push({x1:A,y1:E,x2:A,y2:M}),m.push({x1:k,y1:E,x2:_,y2:E})}return a().createElement(tM,sC({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},l),m.map(function(t){return a().createElement("line",sC({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tM,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,sU(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function s$(t){return(s$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sF(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sq(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=s$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s$(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}sR(sz,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),sR(sz,"displayName","ErrorBar");var sV=function(t){var e,r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,o=t.legendContent,a=tv(r,e2);if(!a)return null;var c=e2.defaultProps,s=void 0!==c?sF(sF({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===o?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?sF(sF({},r),e.props):{},i=n.dataKey,o=n.name,a=n.legendType;return{inactive:n.hide,dataKey:i,type:s.iconType||a||"square",color:s0(e),value:o||i,payload:n}}),sF(sF(sF({},s),e2.getWithHeight(a,i)),{},{payload:e,item:a})};function sW(t){return(sW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sG(t){return function(t){if(Array.isArray(t))return sY(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return sY(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sY(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sY(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sX(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sH(Object(r),!0).forEach(function(e){sK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sK(t,e,r){var n;return(n=function(t,e){if("object"!=sW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==sW(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sZ(t,e,r){return b()(t)||b()(e)?r:F(e)?S()(t,e,r):x()(e)?e(t):r}function sJ(t,e,r,n){var i=cG()(t,function(t){return sZ(t,e)});if("number"===r){var o=i.filter(function(t){return q(t)||parseFloat(t)});return o.length?[cV()(o),cq()(o)]:[1/0,-1/0]}return(n?i.filter(function(t){return!b()(t)}):i).map(function(t){return F(t)||t instanceof Date?t:""})}var sQ=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var c=i.range,s=0;s<a;s++){var u=s>0?n[s-1].coordinate:n[a-1].coordinate,l=n[s].coordinate,f=s>=a-1?n[0].coordinate:n[s+1].coordinate,p=void 0;if(z(l-u)!==z(f-l)){var d=[];if(z(f-l)===z(c[1]-c[0])){p=f;var h=l+c[1]-c[0];d[0]=Math.min(h,(h+u)/2),d[1]=Math.max(h,(h+u)/2)}else{p=u;var y=f+c[1]-c[0];d[0]=Math.min(l,(y+l)/2),d[1]=Math.max(l,(y+l)/2)}var v=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>v[0]&&t<=v[1]||t>=d[0]&&t<=d[1]){o=n[s].index;break}}else{var m=Math.min(u,f),b=Math.max(u,f);if(t>(m+l)/2&&t<=(b+l)/2){o=n[s].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},s0=function(t){var e,r,n=t.type.displayName,i=null!=(e=t.type)&&e.defaultProps?sX(sX({},t.type.defaultProps),t.props):t.props,o=i.stroke,a=i.fill;switch(n){case"Line":r=o;break;case"Area":case"Radar":r=o&&"none"!==o?o:a;break;default:r=a}return r},s1=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),c=0,s=a.length;c<s;c++)for(var u=i[a[c]].stackGroups,l=Object.keys(u),f=0,p=l.length;f<p;f++){var d=u[l[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(t){return tf(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,g=void 0!==m?sX(sX({},m),v[0].props):v[0].props,x=g.barSize,w=g[y];o[w]||(o[w]=[]);var O=b()(x)?e:x;o[w].push({item:v[0],stackList:v.slice(1),barSize:b()(O)?void 0:G(O,r,0)})}}return o},s2=function(t){var e,r=t.barGap,n=t.barCategoryGap,i=t.bandSize,o=t.sizeList,a=void 0===o?[]:o,c=t.maxBarSize,s=a.length;if(s<1)return null;var u=G(r,i,0,!0),l=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=i/s,d=a.reduce(function(t,e){return t+e.barSize||0},0);(d+=(s-1)*u)>=i&&(d-=(s-1)*u,u=0),d>=i&&p>0&&(f=!0,p*=.9,d=s*p);var h={offset:((i-d)/2|0)-u,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:h.offset+h.size+u,size:f?p:e.barSize}},n=[].concat(sG(t),[r]);return h=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:h})}),n},l)}else{var y=G(n,i,0,!0);i-2*y-(s-1)*u<=0&&(u=0);var v=(i-2*y-(s-1)*u)/s;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(sG(t),[{item:e.item,position:{offset:y+(v+u)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},l)}return e},s5=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=sV({children:i,legendWidth:o-(a.left||0)-(a.right||0)});if(c){var s=n||{},u=s.width,l=s.height,f=c.align,p=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&q(t[f]))return sX(sX({},t),{},sK({},f,t[f]+(u||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&q(t[p]))return sX(sX({},t),{},sK({},p,t[p]+(l||0)))}return t},s3=function(t,e,r,n,i){var o=ty(e.props.children,sz).filter(function(t){var e;return e=t.props.direction,!!b()(i)||("horizontal"===n?"yAxis"===i:"vertical"===n||"x"===e?"xAxis"===i:"y"!==e||"yAxis"===i)});if(o&&o.length){var a=o.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=sZ(e,r);if(b()(n))return t;var i=Array.isArray(n)?[cV()(n),cq()(n)]:[n,n],o=a.reduce(function(t,r){var n=sZ(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]},[1/0,-1/0])}return null},s4=function(t,e,r,n,i){var o=e.map(function(e){return s3(t,e,r,i,n)}).filter(function(t){return!b()(t)});return o&&o.length?o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},s6=function(t,e,r,n,i){var o=e.map(function(e){var o=e.props.dataKey;return"number"===r&&o&&s3(t,e,o,n)||sJ(t,o,r,i)});if("number"===r)return o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return o.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},s8=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},s7=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var i,o,a=t.map(function(t){return t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate});return i||a.push(e),o||a.push(r),a},s9=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,s=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return(s="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*z(a[0]-a[1])*s:s,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(i?i.indexOf(t):t)+s,value:t,offset:s}}).filter(function(t){return!L()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+s,value:t,index:e,offset:s}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+s,value:t,offset:s}}):n.domain().map(function(t,e){return{coordinate:n(t)+s,value:i?i[t]:t,index:e,offset:s}})},ut=new WeakMap,ue=function(t,e){if("function"!=typeof e)return t;ut.has(t)||ut.set(t,new WeakMap);var r=ut.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},ur=function(t,e,r){var i=t.scale,o=t.type,a=t.layout,c=t.axisType;if("auto"===i)return"radial"===a&&"radiusAxis"===c?{scale:nw(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:ou(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nO(),realScaleType:"point"}:"category"===o?{scale:nw(),realScaleType:"band"}:{scale:ou(),realScaleType:"linear"};if(N()(i)){var s="scale".concat(ei()(i));return{scale:(n[s]||nO)(),realScaleType:n[s]?s:"point"}}return x()(i)?{scale:i}:{scale:nO(),realScaleType:"point"}},un=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},ui=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},uo=function(t,e){if(!e||2!==e.length||!q(e[0])||!q(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!q(t[0])||t[0]<r)&&(i[0]=r),(!q(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},ua={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=L()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}ck(t,e)}},none:ck,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}ck(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,s=0,u=0;c<i;++c){for(var l=t[e[c]],f=l[a][1]||0,p=(f-(l[a-1][1]||0))/2,d=0;d<c;++d){var h=t[e[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}s+=f,u+=p*f}r[a-1][1]+=r[a-1][0]=o,s&&(o-=u/s)}r[a-1][1]+=r[a-1][0]=o,ck(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=L()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},uc=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),i=ua[r];return(function(){var t=ex([]),e=cE,r=ck,n=cM;function i(i){var o,a,c=Array.from(t.apply(this,arguments),cT),s=c.length,u=-1;for(let t of i)for(o=0,++u;o<s;++o)(c[o][u]=[0,+n(t,c[o].key,u,i)]).data=t;for(o=0,a=c_(e(c));o<s;++o)c[a[o]].index=o;return r(c,a),c}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:ex(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:ex(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?cE:"function"==typeof t?t:ex(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?ck:t,i):r},i})().keys(n).value(function(t,e){return+sZ(t,e,0)}).order(cE).offset(i)(t)},us=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce(function(t,e){var i,o=null!=(i=e.type)&&i.defaultProps?sX(sX({},e.type.defaultProps),e.props):e.props,a=o.stackId;if(o.hide)return t;var c=o[r],s=t[c]||{hasStack:!1,stackGroups:{}};if(F(a)){var u=s.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};u.items.push(e),s.hasStack=!0,s.stackGroups[a]=u}else s.stackGroups[W("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return sX(sX({},t),{},sK({},c,s))},{});return Object.keys(a).reduce(function(e,o){var c=a[o];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,o){var a=c.stackGroups[o];return sX(sX({},e),{},sK({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:uc(t,a.items,i)}))},{})),sX(sX({},e),{},sK({},o,c))},{})},uu=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var s=t.domain();if(!s.length)return null;var u=sE(s,i,a);return t.domain([cV()(u),cq()(u)]),{niceTicks:u}}return i&&"number"===n?{niceTicks:sM(t.domain(),i,a)}:null};function ul(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,o=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!b()(i[e.dataKey])){var c=K(r,"value",i[e.dataKey]);if(c)return c.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var s=sZ(i,b()(a)?e.dataKey:a);return b()(s)?null:e.scale(s)}var uf=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=sZ(o,e.dataKey,e.domain[a]);return b()(c)?null:e.scale(c)-i/2+n},up=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},ud=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?sX(sX({},t.type.defaultProps),t.props):t.props).stackId;if(F(n)){var i=e[n];if(i){var o=i.items.indexOf(t);return o>=0?i.stackedData[o]:null}}return null},uh=function(t,e,r){return Object.keys(t).reduce(function(n,i){var o=t[i].stackedData.reduce(function(t,n){var i=n.slice(e,r+1).reduce(function(t,e){return[cV()(e.concat([t[0]]).filter(q)),cq()(e.concat([t[1]]).filter(q))]},[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},uy=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,uv=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,um=function(t,e,r){if(x()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(q(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(uy.test(t[0])){var i=+uy.exec(t[0])[1];n[0]=e[0]-i}else x()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(q(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(uv.test(t[1])){var o=+uv.exec(t[1])[1];n[1]=e[1]+o}else x()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},ub=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=A()(e,function(t){return t.coordinate}),o=1/0,a=1,c=i.length;a<c;a++){var s=i[a],u=i[a-1];o=Math.min((s.coordinate||0)-(u.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},ug=function(t,e,r){return!t||!t.length||cH()(t,S()(r,"type.defaultProps.domain"))?e:t},ux=function(t,e){var r=t.type.defaultProps?sX(sX({},t.type.defaultProps),t.props):t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,s=r.chartType,u=r.hide;return sX(sX({},tx(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:s0(t),value:sZ(e,n),type:c,payload:e,chartType:s,hide:u})};function uw(t){return(uw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uO(Object(r),!0).forEach(function(e){uS(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function uS(t,e,r){var n;return(n=function(t,e){if("object"!=uw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uw(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var uP=["Webkit","Moz","O","ms"],uA=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=uP.reduce(function(t,n){return uj(uj({},t),{},uS({},n+r,e))},{});return n[t]=e,n};function uk(t){return(uk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u_(){return(u_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uM(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uE(Object(r),!0).forEach(function(e){uI(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function uT(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uB(n.key),n)}}function uN(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uN=function(){return!!t})()}function uC(t){return(uC=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uD(t,e){return(uD=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uI(t,e,r){return(e=uB(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uB(t){var e=function(t,e){if("object"!=uk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uk(e)?e:e+""}var uL=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,s=nO().domain(O()(0,c)).range([i,i+o-a]),u=s.domain().map(function(t){return s(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(r),endX:s(n),scale:s,scaleValues:u}},uR=function(t){return t.changedTouches&&!!t.changedTouches.length},uU=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=uC(r),uI(e=function(t,e){if(e&&("object"===uk(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uN()?Reflect.construct(r,i||[],uC(this).constructor):r.apply(this,i)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),uI(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),uI(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,i=t.startIndex;null==n||n({endIndex:r,startIndex:i})}),e.detachDragEndListener()}),uI(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),uI(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),uI(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),uI(e,"handleSlideDragStart",function(t){var r=uR(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&uD(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,i=this.state.scaleValues,o=this.props,a=o.gap,c=o.data.length-1,s=Math.min(e,r),u=Math.max(e,r),l=n.getIndexInRange(i,s),f=n.getIndexInRange(i,u);return{startIndex:l-l%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=sZ(r[t],i,t);return x()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,s=o.travellerWidth,u=o.startIndex,l=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-s-i,a+c-s-n):p<0&&(p=Math.max(p,a-n,a-i));var d=this.getIndex({startX:n+p,endX:i+p});(d.startIndex!==u||d.endIndex!==l)&&f&&f(d),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=uR(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],c=this.props,s=c.x,u=c.width,l=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,h={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,s+u-l-a):y<0&&(y=Math.max(y,s-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,b=v.endIndex,g=function(){var t=d.length-1;return"startX"===n&&(i>o?m%p==0:b%p==0)||!!(i<o)&&b===t||"endX"===n&&(i>o?b%p==0:m%p==0)||!!(i>o)&&b===t};this.setState(uI(uI({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,c=this.state[e],s=i.indexOf(c);if(-1!==s){var u=s+t;if(-1!==u&&!(u>=i.length)){var l=i[u];"startX"===e&&l>=a||"endX"===e&&l<=o||this.setState(uI({},e,l),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:o,x:e,y:r,width:n,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,c=t.data,s=t.children,u=t.padding,l=o.Children.only(s);return l?a().cloneElement(l,{x:e,y:r,width:n,height:i,margin:u,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,i,o=this,c=this.props,s=c.y,u=c.travellerWidth,l=c.height,f=c.traveller,p=c.ariaLabel,d=c.data,h=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=uM(uM({},tx(this.props,!1)),{},{x:v,y:s,width:u,height:l}),b=p||"Min value: ".concat(null==(r=d[h])?void 0:r.name,", Max value: ").concat(null==(i=d[y])?void 0:i.name);return a().createElement(tM,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),o.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){o.setState({isTravellerFocused:!0})},onBlur:function(){o.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,i=r.height,o=r.stroke,c=r.travellerWidth,s=Math.min(t,e)+c,u=Math.max(Math.abs(e-t)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:o,fillOpacity:.2,x:s,y:n,width:u,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,i=t.height,o=t.travellerWidth,c=t.stroke,s=this.state,u=s.startX,l=s.endX,f={pointerEvents:"none",fill:c};return a().createElement(tM,{className:"recharts-brush-texts"},a().createElement(n5,u_({textAnchor:"end",verticalAnchor:"middle",x:Math.min(u,l)-5,y:n+i/2},f),this.getTextOfTick(e)),a().createElement(n5,u_({textAnchor:"start",verticalAnchor:"middle",x:Math.max(u,l)+o+5,y:n+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,i=t.x,o=t.y,c=t.width,s=t.height,u=t.alwaysShowText,l=this.state,f=l.startX,p=l.endX,d=l.isTextActive,h=l.isSlideMoving,y=l.isTravellerMoving,v=l.isTravellerFocused;if(!e||!e.length||!q(i)||!q(o)||!q(c)||!q(s)||c<=0||s<=0)return null;var m=(0,E.A)("recharts-brush",r),b=1===a().Children.count(n),g=uA("userSelect","none");return a().createElement(tM,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||v||u)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,i=t.height,o=t.stroke,c=Math.floor(r+i/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:i,fill:o,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):x()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,c=t.startIndex,s=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return uM({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?uL({data:r,width:n,x:i,travellerWidth:o,startIndex:c,endIndex:s}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var u=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:u}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,i=r-1;i-n>1;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o}return e>=t[i]?i:n}}],e&&uT(n.prototype,e),r&&uT(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function uz(t){return(uz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u$(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uq(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u$(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=uz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uz(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u$(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uI(uU,"displayName","Brush"),uI(uU,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var uF=Math.PI/180,uV=function(t,e,r,n){return{x:t+Math.cos(-uF*n)*r,y:e+Math.sin(-uF*n)*r}},uW=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},uG=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=uW({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=Math.acos((r-i)/a);return n>o&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},uY=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},uH=function(t,e){var r,n=uG({x:t.x,y:t.y},e),i=n.radius,o=n.angle,a=e.innerRadius,c=e.outerRadius;if(i<a||i>c)return!1;if(0===i)return!0;var s=uY(e),u=s.startAngle,l=s.endAngle,f=o;if(u<=l){for(;f>l;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=l}else{for(;f>u;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=u}return r?uq(uq({},e),{},{radius:i,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function uX(t){return(uX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var uK=["offset"];function uZ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uQ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uJ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=uX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uX(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u0(){return(u0=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var u1=function(t){var e=t.value,r=t.formatter,n=b()(t.children)?e:t.children;return x()(r)?r(n):n},u2=function(t,e,r){var n,i,o=t.position,c=t.viewBox,s=t.offset,u=t.className,l=c.cx,f=c.cy,p=c.innerRadius,d=c.outerRadius,h=c.startAngle,y=c.endAngle,v=c.clockWise,m=(p+d)/2,g=z(y-h)*Math.min(Math.abs(y-h),360),x=g>=0?1:-1;"insideStart"===o?(n=h+x*s,i=v):"insideEnd"===o?(n=y-x*s,i=!v):"end"===o&&(n=y+x*s,i=v),i=g<=0?i:!i;var w=uV(l,f,m,n),O=uV(l,f,m,n+(i?1:-1)*359),j="M".concat(w.x,",").concat(w.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!i,",\n    ").concat(O.x,",").concat(O.y),S=b()(t.id)?W("recharts-radial-line-"):t.id;return a().createElement("text",u0({},r,{dominantBaseline:"central",className:(0,E.A)("recharts-radial-bar-label",u)}),a().createElement("defs",null,a().createElement("path",{id:S,d:j})),a().createElement("textPath",{xlinkHref:"#".concat(S)},e))},u5=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e.cx,o=e.cy,a=e.innerRadius,c=e.outerRadius,s=(e.startAngle+e.endAngle)/2;if("outside"===n){var u=uV(i,o,c+r,s),l=u.x;return{x:l,y:u.y,textAnchor:l>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var f=uV(i,o,(a+c)/2,s);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},u3=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e.x,a=e.y,c=e.width,s=e.height,u=s>=0?1:-1,l=u*n,f=u>0?"end":"start",p=u>0?"start":"end",d=c>=0?1:-1,h=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===i)return uQ(uQ({},{x:o+c/2,y:a-u*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===i)return uQ(uQ({},{x:o+c/2,y:a+s+l,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+s),0),width:c}:{});if("left"===i){var m={x:o-h,y:a+s/2,textAnchor:y,verticalAnchor:"middle"};return uQ(uQ({},m),r?{width:Math.max(m.x-r.x,0),height:s}:{})}if("right"===i){var b={x:o+c+h,y:a+s/2,textAnchor:v,verticalAnchor:"middle"};return uQ(uQ({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:s}:{})}var g=r?{width:c,height:s}:{};return"insideLeft"===i?uQ({x:o+h,y:a+s/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===i?uQ({x:o+c-h,y:a+s/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===i?uQ({x:o+c/2,y:a+l,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===i?uQ({x:o+c/2,y:a+s-l,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===i?uQ({x:o+h,y:a+l,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===i?uQ({x:o+c-h,y:a+l,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===i?uQ({x:o+h,y:a+s-l,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===i?uQ({x:o+c-h,y:a+s-l,textAnchor:y,verticalAnchor:f},g):D()(i)&&(q(i.x)||$(i.x))&&(q(i.y)||$(i.y))?uQ({x:o+G(i.x,c),y:a+G(i.y,s),textAnchor:"end",verticalAnchor:"end"},g):uQ({x:o+c/2,y:a+s/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function u4(t){var e,r=t.offset,n=uQ({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,uK)),i=n.viewBox,c=n.position,s=n.value,u=n.children,l=n.content,f=n.className,p=n.textBreakAll;if(!i||b()(s)&&b()(u)&&!(0,o.isValidElement)(l)&&!x()(l))return null;if((0,o.isValidElement)(l))return(0,o.cloneElement)(l,n);if(x()(l)){if(e=(0,o.createElement)(l,n),(0,o.isValidElement)(e))return e}else e=u1(n);var d="cx"in i&&q(i.cx),h=tx(n,!0);if(d&&("insideStart"===c||"insideEnd"===c||"end"===c))return u2(n,e,h);var y=d?u5(n):u3(n);return a().createElement(n5,u0({className:(0,E.A)("recharts-label",void 0===f?"":f)},h,y,{breakAll:p}),e)}u4.displayName="Label";var u6=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,s=t.innerRadius,u=t.outerRadius,l=t.x,f=t.y,p=t.top,d=t.left,h=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(q(h)&&q(y)){if(q(l)&&q(f))return{x:l,y:f,width:h,height:y};if(q(p)&&q(d))return{x:p,y:d,width:h,height:y}}return q(l)&&q(f)?{x:l,y:f,width:0,height:0}:q(e)&&q(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:s||0,outerRadius:u||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};u4.parseViewBox=u6,u4.renderCallByParent=function(t,e){var r,n,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var c=t.children,s=u6(t),u=ty(c,u4).map(function(t,r){return(0,o.cloneElement)(t,{viewBox:e||s,key:"label-".concat(r)})});if(!i)return u;return[(r=t.label,n=e||s,!r?null:!0===r?a().createElement(u4,{key:"label-implicit",viewBox:n}):F(r)?a().createElement(u4,{key:"label-implicit",viewBox:n,value:r}):(0,o.isValidElement)(r)?r.type===u4?(0,o.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(u4,{key:"label-implicit",content:r,viewBox:n}):x()(r)?a().createElement(u4,{key:"label-implicit",content:r,viewBox:n}):D()(r)?a().createElement(u4,u0({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return uZ(t)}(u)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(u)||function(t,e){if(t){if("string"==typeof t)return uZ(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uZ(t,e)}}(u)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var u8=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},u7=r(69691),u9=r.n(u7),lt=r(47212),le=r.n(lt),lr=function(t){return null};lr.displayName="Cell";var ln=r(5359),li=r.n(ln);function lo(t){return(lo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var la=["valueAccessor"],lc=["data","dataKey","clockWise","id","textBreakAll"];function ls(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function lu(){return(lu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ll(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ll(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lo(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lo(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lo(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ll(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lp(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var ld=function(t){return Array.isArray(t.value)?li()(t.value):t.value};function lh(t){var e=t.valueAccessor,r=void 0===e?ld:e,n=lp(t,la),i=n.data,o=n.dataKey,c=n.clockWise,s=n.id,u=n.textBreakAll,l=lp(n,lc);return i&&i.length?a().createElement(tM,{className:"recharts-label-list"},i.map(function(t,e){var n=b()(o)?r(t,e):sZ(t&&t.payload,o),i=b()(s)?{}:{id:"".concat(s,"-").concat(e)};return a().createElement(u4,lu({},tx(t,!0),l,i,{parentViewBox:t.parentViewBox,value:n,textBreakAll:u,viewBox:u4.parseViewBox(b()(c)?t:lf(lf({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}lh.displayName="LabelList",lh.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=ty(t.children,lh).map(function(t,r){return(0,o.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?a().createElement(lh,{key:"labelList-implicit",data:e}):a().isValidElement(r)||x()(r)?a().createElement(lh,{key:"labelList-implicit",data:e,content:r}):D()(r)?a().createElement(lh,lu({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return ls(t)}(i)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return ls(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ls(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):i};var ly=r(38404),lv=r.n(ly),lm=r(98451),lb=r.n(lm);function lg(t){return(lg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lx(){return(lx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function lO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lO(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lg(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var lS=function(t,e,r,n,i){var o,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+i)+"L ".concat(t+r-a/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},lP={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},lA=function(t){var e,r=lj(lj({},lP),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{o=(r=r.call(t)).next,!1;for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return lw(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lw(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],s=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&s(t)}catch(t){}},[]);var u=r.x,l=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(u!==+u||l!==+l||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var g=(0,E.A)("recharts-trapezoid",h);return b?a().createElement(ni,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:d,x:u,y:l},to:{upperWidth:f,lowerWidth:p,height:d,x:u,y:l},duration:v,animationEasing:y,isActive:b},function(t){var e=t.upperWidth,i=t.lowerWidth,o=t.height,s=t.x,u=t.y;return a().createElement(ni,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},a().createElement("path",lx({},tx(r,!0),{className:g,d:lS(s,u,e,i,o),ref:n})))}):a().createElement("g",null,a().createElement("path",lx({},tx(r,!0),{className:g,d:lS(u,l,f,p,d)})))};function lk(t){return(lk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l_(){return(l_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lM(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lE(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lk(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var lT=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,a=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,u=c*(a?1:-1)+n,l=Math.asin(c/u)/uF,f=s?i:i+o*l;return{center:uV(e,r,u,f),circleTangency:uV(e,r,n,f),lineTangency:uV(e,r,u*Math.cos(l*uF),s?i-o*l:i),theta:l}},lN=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,a=t.endAngle,c=z(a-o)*Math.min(Math.abs(a-o),359.999),s=o+c,u=uV(e,r,i,o),l=uV(e,r,i,s),f="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(o>s),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(n>0){var p=uV(e,r,n,o),d=uV(e,r,n,s);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(o<=s),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},lC=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,u=t.endAngle,l=z(u-s),f=lT({cx:e,cy:r,radius:i,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=lT({cx:e,cy:r,radius:i,angle:u,sign:-l,cornerRadius:o,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(s-u):Math.abs(s-u)-h-b;if(g<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):lN({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:s,endAngle:u});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(l<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=lT({cx:e,cy:r,radius:n,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,S=w.theta,P=lT({cx:e,cy:r,radius:n,angle:u,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=P.circleTangency,k=P.lineTangency,_=P.theta,E=c?Math.abs(s-u):Math.abs(s-u)-S-_;if(E<0&&0===o)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(E>180),",").concat(+(l>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},lD={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},lI=function(t){var e,r=lM(lM({},lD),t),n=r.cx,i=r.cy,o=r.innerRadius,c=r.outerRadius,s=r.cornerRadius,u=r.forceCornerRadius,l=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(c<o||f===p)return null;var h=(0,E.A)("recharts-sector",d),y=c-o,v=G(s,y,0,!0);return e=v>0&&360>Math.abs(f-p)?lC({cx:n,cy:i,innerRadius:o,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:u,cornerIsExternal:l,startAngle:f,endAngle:p}):lN({cx:n,cy:i,innerRadius:o,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",l_({},tx(r,!0),{className:h,d:e,role:"img"}))},lB=["option","shapeType","propTransformer","activeClassName","isActive"];function lL(t){return(lL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lU(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lR(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lL(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lz(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(nd,r);case"trapezoid":return a().createElement(lA,r);case"sector":return a().createElement(lI,r);case"symbols":if("symbols"===e)return a().createElement(eI,r);break;default:return null}}function l$(t){var e,r=t.option,n=t.shapeType,i=t.propTransformer,c=t.activeClassName,s=t.isActive,u=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,lB);if((0,o.isValidElement)(r))e=(0,o.cloneElement)(r,lU(lU({},u),(0,o.isValidElement)(r)?r.props:r));else if(x()(r))e=r(u);else if(lv()(r)&&!lb()(r)){var l=(void 0===i?function(t,e){return lU(lU({},e),t)}:i)(r,u);e=a().createElement(lz,{shapeType:n,elementProps:l})}else e=a().createElement(lz,{shapeType:n,elementProps:u});return s?a().createElement(tM,{className:void 0===c?"recharts-active-shape":c},e):e}function lq(t,e){return null!=e&&"trapezoids"in t.props}function lF(t,e){return null!=e&&"sectors"in t.props}function lV(t,e){return null!=e&&"points"in t.props}function lW(t,e){var r,n,i=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,o=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return i&&o}function lG(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function lY(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}var lH=["x","y"];function lX(t){return(lX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lK(){return(lK=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lZ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lZ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lX(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lZ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lQ(t,e){var r=t.x,n=t.y,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,lH),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||i.height),10),s=parseInt("".concat(e.width||i.width),10);return lJ(lJ(lJ(lJ(lJ({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:c,width:s,name:e.name,radius:e.radius})}function l0(t){return a().createElement(l$,lK({shapeType:"rectangle",propTransformer:lQ,activeClassName:"recharts-active-bar"},t))}var l1=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var i="number"==typeof r;return i?t(r,n):(i||M(!1),e)}},l2=["value","background"];function l5(t){return(l5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l3(){return(l3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l4(Object(r),!0).forEach(function(e){fe(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l8(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fr(n.key),n)}}function l7(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(l7=function(){return!!t})()}function l9(t){return(l9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ft(t,e){return(ft=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fe(t,e,r){return(e=fr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fr(t){var e=function(t,e){if("object"!=l5(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l5(e)?e:e+""}var fn=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=l9(e),fe(t=function(t,e){if(e&&("object"===l5(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,l7()?Reflect.construct(e,r||[],l9(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fe(t,"id",W("recharts-bar-")),fe(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fe(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&ft(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.dataKey,o=r.activeIndex,c=r.activeBar,s=tx(this.props,!1);return t&&t.map(function(t,r){var u=r===o,l=l6(l6(l6({},s),t),{},{isActive:u,option:u?c:n,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tM,l3({className:"recharts-bar-rectangle"},to(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),a().createElement(l0,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,i=e.isAnimationActive,o=e.animationBegin,c=e.animationDuration,s=e.animationEasing,u=e.animationId,l=this.state.prevData;return a().createElement(ni,{begin:o,duration:c,isActive:i,easing:s,from:{t:0},to:{t:1},key:"bar-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,o=r.map(function(t,e){var r=l&&l[e];if(r){var o=X(r.x,t.x),a=X(r.y,t.y),c=X(r.width,t.width),s=X(r.height,t.height);return l6(l6({},t),{},{x:o(i),y:a(i),width:c(i),height:s(i)})}if("horizontal"===n){var u=X(0,t.height)(i);return l6(l6({},t),{},{y:t.y+t.height-u,height:u})}var f=X(0,t.width)(i);return l6(l6({},t),{},{width:f})});return a().createElement(tM,null,t.renderRectanglesStatically(o))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!cH()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,i=e.activeIndex,o=tx(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,s=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,l2);if(!c)return null;var u=l6(l6(l6(l6(l6({},s),{},{fill:"#eee"},c),o),to(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(l0,l3({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},u))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,i=r.xAxis,o=r.yAxis,c=r.layout,s=ty(r.children,sz);if(!s)return null;var u="vertical"===c?n[0].height/2:n[0].width/2,l=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:sZ(t,e)}};return a().createElement(tM,{clipPath:t?"url(#clipPath-".concat(e,")"):null},s.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:i,yAxis:o,layout:c,offset:u,dataPointFormatter:l})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,i=t.xAxis,o=t.yAxis,c=t.left,s=t.top,u=t.width,l=t.height,f=t.isAnimationActive,p=t.background,d=t.id;if(e||!r||!r.length)return null;var h=this.state.isAnimationFinished,y=(0,E.A)("recharts-bar",n),v=i&&i.allowDataOverflow,m=o&&o.allowDataOverflow,g=v||m,x=b()(d)?this.id:d;return a().createElement(tM,{className:y},v||m?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(x)},a().createElement("rect",{x:v?c:c-u/2,y:m?s:s-l/2,width:v?u:2*u,height:m?l:2*l}))):null,a().createElement(tM,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(x,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,x),(!f||h)&&lh.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&l8(n.prototype,e),r&&l8(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function fi(t){return(fi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fu(n.key),n)}}function fa(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fa(Object(r),!0).forEach(function(e){fs(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fa(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fs(t,e,r){return(e=fu(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fu(t){var e=function(t,e){if("object"!=fi(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fi(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fi(e)?e:e+""}fe(fn,"displayName","Bar"),fe(fn,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!tJ.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fe(fn,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,c=t.xAxisTicks,s=t.yAxisTicks,u=t.stackedData,l=t.dataStartIndex,f=t.displayedData,p=t.offset,d=ui(n,r);if(!d)return null;var h=e.layout,y=r.type.defaultProps,v=void 0!==y?l6(l6({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===h?a:o,w=u?x.scale.domain():null,O=up({numericAxis:x}),j=ty(b,lr),S=f.map(function(t,e){u?f=uo(u[l+e],w):Array.isArray(f=sZ(t,m))||(f=[O,f]);var n=l1(g,fn.defaultProps.minPointSize)(f[1],e);if("horizontal"===h){var f,p,y,v,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],A=P[0],k=P[1];p=uf({axis:o,ticks:c,bandSize:i,offset:d.offset,entry:t,index:e}),y=null!=(S=null!=k?k:A)?S:void 0,v=d.size;var _=A-k;if(b=Number.isNaN(_)?0:_,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var E=z(b||n)*(Math.abs(n)-Math.abs(b));y-=E,b+=E}}else{var M=[o.scale(f[0]),o.scale(f[1])],T=M[0],N=M[1];if(p=T,y=uf({axis:a,ticks:s,bandSize:i,offset:d.offset,entry:t,index:e}),v=N-T,b=d.size,x={x:o.x,y:y,width:o.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var C=z(v||n)*(Math.abs(n)-Math.abs(v));v+=C}}return l6(l6(l6({},t),{},{x:p,y:y,width:v,height:b,value:u?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[ux(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return l6({data:S,layout:h},p)});var fl=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},ff=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fo(r.prototype,t),e&&fo(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fs(ff,"EPS",1e-4);var fp=function(t){var e=Object.keys(t).reduce(function(e,r){return fc(fc({},e),{},fs({},r,ff.create(t[r])))},{});return fc(fc({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return u9()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return le()(t,function(t,r){return e[r].isInRange(t)})}})},fd=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))};function fh(){return(fh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fy(t){return(fy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fv(Object(r),!0).forEach(function(e){fw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fb=function(){return!!t})()}function fg(t){return(fg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fx(t,e){return(fx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fw(t,e,r){return(e=fO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fO(t){var e=function(t,e){if("object"!=fy(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fy(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fy(e)?e:e+""}var fj=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=fp({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return u8(t,"discard")&&!o.isInRange(a)?null:a},fS=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fg(t),function(t,e){if(e&&("object"===fy(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fb()?Reflect.construct(t,e||[],fg(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fx(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,i=t.r,o=t.alwaysShow,c=t.clipPathId,s=F(e),u=F(n);if(er(void 0===o,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!u)return null;var l=fj(this.props);if(!l)return null;var f=l.x,p=l.y,d=this.props,h=d.shape,y=d.className,v=fm(fm({clipPath:u8(this.props,"hidden")?"url(#".concat(c,")"):void 0},tx(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tM,{className:(0,E.A)("recharts-reference-dot",y)},r.renderDot(h,v),u4.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fO(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fw(fS,"displayName","ReferenceDot"),fw(fS,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fw(fS,"renderDot",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):x()(t)?t(e):a().createElement(e3,fh({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fP=r(67367),fA=r.n(fP),fk=r(22964),f_=r.n(fk),fE=r(86451),fM=r.n(fE)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fT=(0,o.createContext)(void 0),fN=(0,o.createContext)(void 0),fC=(0,o.createContext)(void 0),fD=(0,o.createContext)({}),fI=(0,o.createContext)(void 0),fB=(0,o.createContext)(0),fL=(0,o.createContext)(0),fR=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,i=e.offset,o=t.clipPathId,c=t.children,s=t.width,u=t.height,l=fM(i);return a().createElement(fT.Provider,{value:r},a().createElement(fN.Provider,{value:n},a().createElement(fD.Provider,{value:i},a().createElement(fC.Provider,{value:l},a().createElement(fI.Provider,{value:o},a().createElement(fB.Provider,{value:u},a().createElement(fL.Provider,{value:s},c)))))))},fU=function(t){var e=(0,o.useContext)(fT);null==e&&M(!1);var r=e[t];return null==r&&M(!1),r},fz=function(){var t=(0,o.useContext)(fN);return f_()(t,function(t){return le()(t.domain,Number.isFinite)})||Y(t)},f$=function(t){var e=(0,o.useContext)(fN);null==e&&M(!1);var r=e[t];return null==r&&M(!1),r},fq=function(){return(0,o.useContext)(fL)},fF=function(){return(0,o.useContext)(fB)};function fV(t){return(fV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fW(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fW=function(){return!!t})()}function fG(t){return(fG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fY(t,e){return(fY=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fX(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fH(Object(r),!0).forEach(function(e){fK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fK(t,e,r){return(e=fZ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fZ(t){var e=function(t,e){if("object"!=fV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fV(e)?e:e+""}function fJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function fQ(){return(fQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var f0=function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):x()(t)?t(e):a().createElement("line",fQ({},e,{className:"recharts-reference-line-line"}))},f1=function(t,e,r,n,i,o,a,c,s){var u=i.x,l=i.y,f=i.width,p=i.height;if(r){var d=s.y,h=t.y.apply(d,{position:o});if(u8(s,"discard")&&!t.y.isInRange(h))return null;var y=[{x:u+f,y:h},{x:u,y:h}];return"left"===c?y.reverse():y}if(e){var v=s.x,m=t.x.apply(v,{position:o});if(u8(s,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:l+p},{x:m,y:l}];return"top"===a?b.reverse():b}if(n){var g=s.segment.map(function(e){return t.apply(e,{position:o})});return u8(s,"discard")&&fA()(g,function(e){return!t.isInRange(e)})?null:g}return null};function f2(t){var e,r=t.x,n=t.y,i=t.segment,c=t.xAxisId,s=t.yAxisId,u=t.shape,l=t.className,f=t.alwaysShow,p=(0,o.useContext)(fI),d=fU(c),h=f$(s),y=(0,o.useContext)(fC);if(!p||!y)return null;er(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=f1(fp({x:d.scale,y:h.scale}),F(r),F(n),i&&2===i.length,y,t.position,d.orientation,h.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{o=(r=r.call(t)).next,!1;for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return fJ(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fJ(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,x=b.y,w=m[1],O=w.x,j=w.y,S=fX(fX({clipPath:u8(t,"hidden")?"url(#".concat(p,")"):void 0},tx(t,!0)),{},{x1:g,y1:x,x2:O,y2:j});return a().createElement(tM,{className:(0,E.A)("recharts-reference-line",l)},f0(u,S),u4.renderCallByParent(t,fl({x:(e={x1:g,y1:x,x2:O,y2:j}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var f5=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fG(t),function(t,e){if(e&&("object"===fV(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fW()?Reflect.construct(t,e||[],fG(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fY(r,t),e=[{key:"render",value:function(){return a().createElement(f2,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fZ(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function f3(){return(f3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f4(t){return(f4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f6(Object(r),!0).forEach(function(e){pe(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}fK(f5,"displayName","ReferenceLine"),fK(f5,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function f7(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f7=function(){return!!t})()}function f9(t){return(f9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pt(t,e){return(pt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pe(t,e,r){return(e=pr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pr(t){var e=function(t,e){if("object"!=f4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f4(e)?e:e+""}var pn=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,s=i.y2,u=i.xAxis,l=i.yAxis;if(!u||!l)return null;var f=fp({x:u.scale,y:l.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},d={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(s,{position:"end"}):f.y.rangeMax};return!u8(i,"discard")||f.isInRange(p)&&f.isInRange(d)?fl(p,d):null},pi=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=f9(t),function(t,e){if(e&&("object"===f4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f7()?Reflect.construct(t,e||[],f9(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&pt(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,i=t.y1,o=t.y2,c=t.className,s=t.alwaysShow,u=t.clipPathId;er(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=F(e),f=F(n),p=F(i),d=F(o),h=this.props.shape;if(!l&&!f&&!p&&!d&&!h)return null;var y=pn(l,f,p,d,this.props);if(!y&&!h)return null;var v=u8(this.props,"hidden")?"url(#".concat(u,")"):void 0;return a().createElement(tM,{className:(0,E.A)("recharts-reference-area",c)},r.renderRect(h,f8(f8({clipPath:v},tx(this.props,!0)),y)),u4.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pr(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function po(t){return function(t){if(Array.isArray(t))return pa(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pa(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pa(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pa(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pe(pi,"displayName","ReferenceArea"),pe(pi,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pe(pi,"renderRect",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):x()(t)?t(e):a().createElement(nd,f3({},e,{className:"recharts-reference-area-rect"}))});var pc=function(t,e,r,n,i){var o=ty(t,f5),a=ty(t,fS),c=[].concat(po(o),po(a)),s=ty(t,pi),u="".concat(n,"Id"),l=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[u]===r&&u8(e.props,"extendDomain")&&q(e.props[l])){var n=e.props[l];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),s.length){var p="".concat(l,"1"),d="".concat(l,"2");f=s.reduce(function(t,e){if(e.props[u]===r&&u8(e.props,"extendDomain")&&q(e.props[p])&&q(e.props[d])){var n=e.props[p],i=e.props[d];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t},f)}return i&&i.length&&(f=i.reduce(function(t,e){return q(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},ps=r(11117),pu=new(r.n(ps)()),pl="recharts.syncMouseEvents";function pf(t){return(pf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pp(t,e,r){return(e=pd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pd(t){var e=function(t,e){if("object"!=pf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pf(e)?e:e+""}var ph=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");pp(this,"activeIndex",0),pp(this,"coordinateList",[]),pp(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,c=void 0===a?null:a,s=t.offset,u=void 0===s?null:s,l=t.mouseHandlerCallback,f=void 0===l?null:l;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=o?o:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=u?u:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,s=(null==(e=window)?void 0:e.scrollY)||0,u=i+this.offset.top+o/2+s;this.mouseHandlerCallback({pageX:n+a+c,pageY:u})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pd(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function py(){}function pv(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pm(t){this._context=t}function pb(t){this._context=t}function pg(t){this._context=t}pm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pv(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pv(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pb.prototype={areaStart:py,areaEnd:py,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pv(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pv(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class px{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pw(t){this._context=t}function pO(t){this._context=t}function pj(t){return new pO(t)}pw.prototype={areaStart:py,areaEnd:py,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pS(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function pP(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pA(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,c=(o-n)/3;t._context.bezierCurveTo(n+c,i+c*e,o-c,a-c*r,o,a)}function pk(t){this._context=t}function p_(t){this._context=new pE(t)}function pE(t){this._context=t}function pM(t){this._context=t}function pT(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function pN(t,e){this._context=t,this._t=e}function pC(t){return t[0]}function pD(t){return t[1]}function pI(t,e){var r=ex(!0),n=null,i=pj,o=null,a=eA(c);function c(c){var s,u,l,f=(c=c_(c)).length,p=!1;for(null==n&&(o=i(l=a())),s=0;s<=f;++s)!(s<f&&r(u=c[s],s,c))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(u,s,c),+e(u,s,c));if(l)return o=null,l+""||null}return t="function"==typeof t?t:void 0===t?pC:ex(t),e="function"==typeof e?e:void 0===e?pD:ex(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:ex(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:ex(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:ex(!!t),c):r},c.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),c):i},c.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),c):n},c}function pB(t,e,r){var n=null,i=ex(!0),o=null,a=pj,c=null,s=eA(u);function u(u){var l,f,p,d,h,y=(u=c_(u)).length,v=!1,m=Array(y),b=Array(y);for(null==o&&(c=a(h=s())),l=0;l<=y;++l){if(!(l<y&&i(d=u[l],l,u))===v)if(v=!v)f=l,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=l-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[l]=+t(d,l,u),b[l]=+e(d,l,u),c.point(n?+n(d,l,u):m[l],r?+r(d,l,u):b[l]))}if(h)return c=null,h+""||null}function l(){return pI().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?pC:ex(+t),e="function"==typeof e?e:void 0===e?ex(0):ex(+e),r="function"==typeof r?r:void 0===r?pD:ex(+r),u.x=function(e){return arguments.length?(t="function"==typeof e?e:ex(+e),n=null,u):t},u.x0=function(e){return arguments.length?(t="function"==typeof e?e:ex(+e),u):t},u.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:ex(+t),u):n},u.y=function(t){return arguments.length?(e="function"==typeof t?t:ex(+t),r=null,u):e},u.y0=function(t){return arguments.length?(e="function"==typeof t?t:ex(+t),u):e},u.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:ex(+t),u):r},u.lineX0=u.lineY0=function(){return l().x(t).y(e)},u.lineY1=function(){return l().x(t).y(r)},u.lineX1=function(){return l().x(n).y(e)},u.defined=function(t){return arguments.length?(i="function"==typeof t?t:ex(!!t),u):i},u.curve=function(t){return arguments.length?(a=t,null!=o&&(c=a(o)),u):a},u.context=function(t){return arguments.length?(null==t?o=c=null:c=a(o=t),u):o},u}function pL(t){return(pL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pR(){return(pR=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pz(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pU(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=pL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pL(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pO.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pk.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pA(this,this._t0,pP(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pA(this,pP(this,r=pS(this,t,e)),r);break;default:pA(this,this._t0,r=pS(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(p_.prototype=Object.create(pk.prototype)).point=function(t,e){pk.prototype.point.call(this,e,t)},pE.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},pM.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pT(t),i=pT(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pN.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var p$={curveBasisClosed:function(t){return new pb(t)},curveBasisOpen:function(t){return new pg(t)},curveBasis:function(t){return new pm(t)},curveBumpX:function(t){return new px(t,!0)},curveBumpY:function(t){return new px(t,!1)},curveLinearClosed:function(t){return new pw(t)},curveLinear:pj,curveMonotoneX:function(t){return new pk(t)},curveMonotoneY:function(t){return new p_(t)},curveNatural:function(t){return new pM(t)},curveStep:function(t){return new pN(t,.5)},curveStepAfter:function(t){return new pN(t,1)},curveStepBefore:function(t){return new pN(t,0)}},pq=function(t){return t.x===+t.x&&t.y===+t.y},pF=function(t){return t.x},pV=function(t){return t.y},pW=function(t,e){if(x()(t))return t;var r="curve".concat(ei()(t));return("curveMonotone"===r||"curveBump"===r)&&e?p$["".concat(r).concat("vertical"===e?"Y":"X")]:p$[r]||pj},pG=function(t){var e,r=t.type,n=t.points,i=void 0===n?[]:n,o=t.baseLine,a=t.layout,c=t.connectNulls,s=void 0!==c&&c,u=pW(void 0===r?"linear":r,a),l=s?i.filter(function(t){return pq(t)}):i;if(Array.isArray(o)){var f=s?o.filter(function(t){return pq(t)}):o,p=l.map(function(t,e){return pz(pz({},t),{},{base:f[e]})});return(e="vertical"===a?pB().y(pV).x1(pF).x0(function(t){return t.base.x}):pB().x(pF).y1(pV).y0(function(t){return t.base.y})).defined(pq).curve(u),e(p)}return(e="vertical"===a&&q(o)?pB().y(pV).x1(pF).x0(o):q(o)?pB().x(pF).y1(pV).y0(o):pI().x(pF).y(pV)).defined(pq).curve(u),e(l)},pY=function(t){var e=t.className,r=t.points,n=t.path,i=t.pathRef;if((!r||!r.length)&&!n)return null;var o=r&&r.length?pG(t):n;return a().createElement("path",pR({},tx(t,!1),ti(t),{className:(0,E.A)("recharts-curve",e),d:o,ref:i}))};function pH(t){return(pH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var pX=["x","y","top","left","width","height","className"];function pK(){return(pK=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pZ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var pJ=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,i=void 0===n?0:n,o=t.top,c=void 0===o?0:o,s=t.left,u=void 0===s?0:s,l=t.width,f=void 0===l?0:l,p=t.height,d=void 0===p?0:p,h=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pZ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=pH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pH(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pZ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:u,width:f,height:d},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,pX));return q(r)&&q(i)&&q(f)&&q(d)&&q(c)&&q(u)?a().createElement("path",pK({},tx(y,!0),{className:(0,E.A)("recharts-cross",h),d:"M".concat(r,",").concat(c,"v").concat(d,"M").concat(u,",").concat(i,"h").concat(f)})):null};function pQ(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[uV(e,r,n,i),uV(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}function p0(t){return(p0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p1(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=p0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p0(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p5(t){var e,r,n,i,a=t.element,c=t.tooltipEventType,s=t.isActive,u=t.activeCoordinate,l=t.activePayload,f=t.offset,p=t.activeTooltipIndex,d=t.tooltipAxisBandSize,h=t.layout,y=t.chartName,v=null!=(r=a.props.cursor)?r:null==(n=a.type.defaultProps)?void 0:n.cursor;if(!a||!v||!s||!u||"ScatterChart"!==y&&"axis"!==c)return null;var m=pY;if("ScatterChart"===y)i=u,m=pJ;else if("BarChart"===y)e=d/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===h?u.x-e:f.left+.5,y:"horizontal"===h?f.top+.5:u.y-e,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},m=nd;else if("radial"===h){var b=pQ(u),g=b.cx,x=b.cy,w=b.radius;i={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=lI}else i={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return pQ(e);else{var c=e.cx,s=e.cy,u=e.innerRadius,l=e.outerRadius,f=e.angle,p=uV(c,s,u,f),d=uV(c,s,l,f);n=p.x,i=p.y,o=d.x,a=d.y}return[{x:n,y:i},{x:o,y:a}]}(h,u,f)},m=pY;var O=p2(p2(p2(p2({stroke:"#ccc",pointerEvents:"none"},f),i),tx(v,!1)),{},{payload:l,payloadIndex:p,className:(0,E.A)("recharts-tooltip-cursor",v.className)});return(0,o.isValidElement)(v)?(0,o.cloneElement)(v,O):(0,o.createElement)(m,O)}var p3=["item"],p4=["children","className","width","height","style","compact","title","desc"];function p6(t){return(p6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p8(){return(p8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p7(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(t,e)||di(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p9(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function dt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dt=function(){return!!t})()}function de(t){return(de=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dr(t,e){return(dr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dn(t){return function(t){if(Array.isArray(t))return da(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||di(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function di(t,e){if(t){if("string"==typeof t)return da(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return da(t,e)}}function da(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function dc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ds(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dc(Object(r),!0).forEach(function(e){du(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dc(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function du(t,e,r){return(e=dl(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dl(t){var e=function(t,e){if("object"!=p6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p6(e)?e:e+""}var df={xAxis:["bottom","top"],yAxis:["left","right"]},dp={width:"100%",height:"100%"},dd={x:0,y:0};function dh(t){return t}var dy=function(t,e,r,n){var i=e.find(function(t){return t&&t.index===r});if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return ds(ds(ds({},n),uV(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,s=n.angle;return ds(ds(ds({},n),uV(n.cx,n.cy,c,s)),{},{angle:s,radius:c})}return dd},dv=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(dn(t),dn(r)):t},[]);return o.length>0?o:t&&t.length&&q(n)&&q(i)?t.slice(n,i+1):[]};function dm(t){return"number"===t?[0,"auto"]:void 0}var db=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=dv(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce(function(i,c){var s,u,l=null!=(s=c.props.data)?s:e;return(l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1)),u=o.dataKey&&!o.allowDuplicatedCategory?K(void 0===l?a:l,o.dataKey,n):l&&l[r]||a[r])?[].concat(dn(i),[ux(c,u)]):i},[])},dg=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o="horizontal"===r?i.x:"vertical"===r?i.y:"centric"===r?i.angle:i.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,u=sQ(o,a,s,c);if(u>=0&&s){var l=s[u]&&s[u].value,f=db(t,e,u,l),p=dy(r,a,u,i);return{activeTooltipIndex:u,activeLabel:l,activePayload:f,activeCoordinate:p}}return null},dx=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,s=e.dataEndIndex,u=t.layout,l=t.children,f=t.stackOffset,p=s8(u,i);return r.reduce(function(e,r){var d=void 0!==r.type.defaultProps?ds(ds({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,v=d.allowDataOverflow,m=d.allowDuplicatedCategory,g=d.scale,x=d.ticks,w=d.includeHidden,j=d[o];if(e[j])return e;var S=dv(t.data,{graphicalItems:n.filter(function(t){var e;return(o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o])===j}),dataStartIndex:c,dataEndIndex:s}),P=S.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&q(n)&&q(i))return!0}return!1})(d.domain,v,h)&&(_=um(d.domain,null,v),p&&("number"===h||"auto"!==g)&&(M=sJ(S,y,"category")));var A=dm(h);if(!_||0===_.length){var k,_,E,M,T,N=null!=(T=d.domain)?T:A;if(y){if(_=sJ(S,y,h),"category"===h&&p){var C=H(_);m&&C?(E=_,_=O()(0,P)):m||(_=ug(N,_,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(dn(t),[e])},[]))}else if("category"===h)_=m?_.filter(function(t){return""!==t&&!b()(t)}):ug(N,_,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||b()(e)?t:[].concat(dn(t),[e])},[]);else if("number"===h){var D=s4(S,n.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===j&&(w||!i)}),y,i,u);D&&(_=D)}p&&("number"===h||"auto"!==g)&&(M=sJ(S,y,"category"))}else _=p?O()(0,P):a&&a[j]&&a[j].hasStack&&"number"===h?"expand"===f?[0,1]:uh(a[j].stackGroups,c,s):s6(S,n.filter(function(t){var e=o in t.props?t.props[o]:t.type.defaultProps[o],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===j&&(w||!r)}),h,u,!0);"number"===h?(_=pc(l,_,j,i,x),N&&(_=um(N,_,v))):"category"===h&&N&&_.every(function(t){return N.indexOf(t)>=0})&&(_=N)}return ds(ds({},e),{},du({},j,ds(ds({},d),{},{axisType:i,domain:_,categoricalDomain:M,duplicateDomain:E,originalDomain:null!=(k=d.domain)?k:A,isCategorical:p,layout:u})))},{})},dw=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,s=e.dataEndIndex,u=t.layout,l=t.children,f=dv(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:s}),p=f.length,d=s8(u,i),h=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?ds(ds({},e.type.defaultProps),e.props):e.props)[o],m=dm("number");return t[v]?t:(h++,y=d?O()(0,p):a&&a[v]&&a[v].hasStack?pc(l,y=uh(a[v].stackGroups,c,s),v,i):pc(l,y=um(m,s6(f,r.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!i}),"number",u),n.defaultProps.allowDataOverflow),v,i),ds(ds({},t),{},du({},v,ds(ds({axisType:i},n.defaultProps),{},{hide:!0,orientation:S()(df,"".concat(i,".").concat(h%2),null),domain:y,originalDomain:m,isCategorical:d,layout:u}))))},{})},dO=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,s=e.dataEndIndex,u=t.children,l="".concat(n,"Id"),f=ty(u,i),p={};return f&&f.length?p=dx(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(p=dw(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:c,dataEndIndex:s})),p},dj=function(t){var e=Y(t),r=s9(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:A()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:ub(e,r)}},dS=function(t){var e=t.children,r=t.defaultShowTooltip,n=tv(e,uU),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},dP=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},dA=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,c=void 0===a?{}:a,s=r.width,u=r.height,l=r.children,f=r.margin||{},p=tv(l,uU),d=tv(l,e2),h=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:ds(ds({},t),{},du({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:ds(ds({},t),{},du({},n,S()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=ds(ds({},y),h),m=v.bottom;p&&(v.bottom+=p.props.height||uU.defaultProps.height),d&&e&&(v=s5(v,n,r,e));var b=s-v.left-v.right,g=u-v.top-v.bottom;return ds(ds({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},dk=["layout","type","stroke","connectNulls","isRange","ref"],d_=["key"];function dE(t){return(dE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dM(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function dT(){return(dT=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dN(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dN(Object(r),!0).forEach(function(e){dR(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dN(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dD(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dU(n.key),n)}}function dI(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dI=function(){return!!t})()}function dB(t){return(dB=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dL(t,e){return(dL=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dR(t,e,r){return(e=dU(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dU(t){var e=function(t,e){if("object"!=dE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dE(e)?e:e+""}var dz=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=dB(e),dR(t=function(t,e){if(e&&("object"===dE(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dI()?Reflect.construct(e,r||[],dB(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0}),dR(t,"id",W("recharts-area-")),dR(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),x()(e)&&e()}),dR(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),x()(e)&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&dL(n,t),e=[{key:"renderDots",value:function(t,e,r){var i=this.props.isAnimationActive,o=this.state.isAnimationFinished;if(i&&!o)return null;var c=this.props,s=c.dot,u=c.points,l=c.dataKey,f=tx(this.props,!1),p=tx(s,!0),d=u.map(function(t,e){var r=dC(dC(dC({key:"dot-".concat(e),r:3},f),p),{},{index:e,cx:t.x,cy:t.y,dataKey:l,value:t.value,payload:t.payload,points:u});return n.renderDotItem(s,r)}),h={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return a().createElement(tM,dT({className:"recharts-area-dots"},h),d)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,i=e.strokeWidth,o=n[0].x,c=n[n.length-1].x,s=t*Math.abs(o-c),u=cq()(n.map(function(t){return t.y||0}));return(q(r)&&"number"==typeof r?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(cq()(r.map(function(t){return t.y||0})),u)),q(u))?a().createElement("rect",{x:o<c?o:o-s,y:0,width:s,height:Math.floor(u+(i?parseInt("".concat(i),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,i=e.strokeWidth,o=n[0].y,c=n[n.length-1].y,s=t*Math.abs(o-c),u=cq()(n.map(function(t){return t.x||0}));return(q(r)&&"number"==typeof r?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(cq()(r.map(function(t){return t.x||0})),u)),q(u))?a().createElement("rect",{x:0,y:o<c?o:o-s,width:u+(i?parseInt("".concat(i),10):1),height:Math.floor(s)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,r,n){var i=this.props,o=i.layout,c=i.type,s=i.stroke,u=i.connectNulls,l=i.isRange,f=(i.ref,dM(i,dk));return a().createElement(tM,{clipPath:r?"url(#clipPath-".concat(n,")"):null},a().createElement(pY,dT({},tx(f,!0),{points:t,connectNulls:u,type:c,baseLine:e,layout:o,stroke:"none",className:"recharts-area-area"})),"none"!==s&&a().createElement(pY,dT({},tx(this.props,!1),{className:"recharts-area-curve",layout:o,type:c,connectNulls:u,fill:"none",points:t})),"none"!==s&&l&&a().createElement(pY,dT({},tx(this.props,!1),{className:"recharts-area-curve",layout:o,type:c,connectNulls:u,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var r=this,n=this.props,i=n.points,o=n.baseLine,c=n.isAnimationActive,s=n.animationBegin,u=n.animationDuration,l=n.animationEasing,f=n.animationId,p=this.state,d=p.prevPoints,h=p.prevBaseLine;return a().createElement(ni,{begin:s,duration:u,isActive:c,easing:l,from:{t:0},to:{t:1},key:"area-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var c=n.t;if(d){var s,u=d.length/i.length,l=i.map(function(t,e){var r=Math.floor(e*u);if(d[r]){var n=d[r],i=X(n.x,t.x),o=X(n.y,t.y);return dC(dC({},t),{},{x:i(c),y:o(c)})}return t});return s=q(o)&&"number"==typeof o?X(h,o)(c):b()(o)||L()(o)?X(h,0)(c):o.map(function(t,e){var r=Math.floor(e*u);if(h[r]){var n=h[r],i=X(n.x,t.x),o=X(n.y,t.y);return dC(dC({},t),{},{x:i(c),y:o(c)})}return t}),r.renderAreaStatically(l,s,t,e)}return a().createElement(tM,null,a().createElement("defs",null,a().createElement("clipPath",{id:"animationClipPath-".concat(e)},r.renderClipRect(c))),a().createElement(tM,{clipPath:"url(#animationClipPath-".concat(e,")")},r.renderAreaStatically(i,o,t,e)))})}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,i=r.baseLine,o=r.isAnimationActive,a=this.state,c=a.prevPoints,s=a.prevBaseLine,u=a.totalLength;return o&&n&&n.length&&(!c&&u>0||!cH()(c,n)||!cH()(s,i))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,i,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,i=e.points,o=e.className,c=e.top,s=e.left,u=e.xAxis,l=e.yAxis,f=e.width,p=e.height,d=e.isAnimationActive,h=e.id;if(r||!i||!i.length)return null;var y=this.state.isAnimationFinished,v=1===i.length,m=(0,E.A)("recharts-area",o),g=u&&u.allowDataOverflow,x=l&&l.allowDataOverflow,w=g||x,O=b()(h)?this.id:h,j=null!=(t=tx(n,!1))?t:{r:3,strokeWidth:2},S=j.r,P=j.strokeWidth,A=(n&&"object"===tu(n)&&"clipDot"in n?n:{}).clipDot,k=void 0===A||A,_=2*(void 0===S?3:S)+(void 0===P?2:P);return a().createElement(tM,{className:m},g||x?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(O)},a().createElement("rect",{x:g?s:s-f/2,y:x?c:c-p/2,width:g?f:2*f,height:x?p:2*p})),!k&&a().createElement("clipPath",{id:"clipPath-dots-".concat(O)},a().createElement("rect",{x:s-_/2,y:c-_/2,width:f+_,height:p+_}))):null,v?null:this.renderArea(w,O),(n||v)&&this.renderDots(w,k,O),(!d||y)&&lh.renderCallByParent(this.props,i))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],e&&dD(n.prototype,e),r&&dD(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function d$(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function dq(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function dF(t){return(dF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dV(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=dF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dF(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dG(t,e,r){var n,i,o,a,c,s=t.tick,u=t.ticks,l=t.viewBox,f=t.minTickGap,p=t.orientation,d=t.interval,h=t.tickFormatter,y=t.unit,v=t.angle;if(!u||!u.length||!s)return[];if(q(d)||tJ.isSsr)return d$(u,("number"==typeof d&&q(d)?d:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nM(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},w=function(t,n){var i,o=x()(h)?h(t.value,n):t.value;return"width"===b?(i=nM(o,{fontSize:e,letterSpacing:r}),fd({width:i.width+g.width,height:i.height+g.height},v)):nM(o,{fontSize:e,letterSpacing:r})[b]},O=u.length>=2?z(u[1].coordinate-u[0].coordinate):1,j=(n="width"===b,i=l.x,o=l.y,a=l.width,c=l.height,1===O?{start:n?i:o,end:n?i+a:o+c}:{start:n?i+a:o+c,end:n?i:o});return"equidistantPreserveStart"===d?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,s=e.end,u=0,l=1,f=c;l<=a.length;)if(o=function(){var e,o=null==n?void 0:n[u];if(void 0===o)return{v:d$(n,l)};var a=u,p=function(){return void 0===e&&(e=r(o,a)),e},d=o.coordinate,h=0===u||dq(t,d,p,f,s);h||(u=0,f=c,l+=1),h&&(f=d+t*(p()/2+i),u+=l)}())return o.v;return[]}(O,j,w,u,f):("preserveStart"===d||"preserveStartEnd"===d?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,s=e.start,u=e.end;if(o){var l=n[c-1],f=r(l,c-1),p=t*(l.coordinate+t*f/2-u);a[c-1]=l=dW(dW({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate}),dq(t,l.tickCoord,function(){return f},s,u)&&(u=l.tickCoord-t*(f/2+i),a[c-1]=dW(dW({},l),{},{isShow:!0}))}for(var d=o?c-1:c,h=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var l=t*(o.coordinate-t*c()/2-s);a[e]=o=dW(dW({},o),{},{tickCoord:l<0?o.coordinate-l*t:o.coordinate})}else a[e]=o=dW(dW({},o),{},{tickCoord:o.coordinate});dq(t,o.tickCoord,c,s,u)&&(s=o.tickCoord+t*(c()/2+i),a[e]=dW(dW({},o),{},{isShow:!0}))},y=0;y<d;y++)h(y);return a}(O,j,w,u,f,"preserveStartEnd"===d):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,s=e.end,u=function(e){var n,u=o[e],l=function(){return void 0===n&&(n=r(u,e)),n};if(e===a-1){var f=t*(u.coordinate+t*l()/2-s);o[e]=u=dW(dW({},u),{},{tickCoord:f>0?u.coordinate-f*t:u.coordinate})}else o[e]=u=dW(dW({},u),{},{tickCoord:u.coordinate});dq(t,u.tickCoord,l,c,s)&&(s=u.tickCoord-t*(l()/2+i),o[e]=dW(dW({},u),{},{isShow:!0}))},l=a-1;l>=0;l--)u(l);return o}(O,j,w,u,f)).filter(function(t){return t.isShow})}dR(dz,"displayName","Area"),dR(dz,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!tJ.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),dR(dz,"getBaseValue",function(t,e,r,n){var i=t.layout,o=t.baseValue,a=e.props.baseValue,c=null!=a?a:o;if(q(c)&&"number"==typeof c)return c;var s="horizontal"===i?n:r,u=s.scale.domain();if("number"===s.type){var l=Math.max(u[0],u[1]),f=Math.min(u[0],u[1]);return"dataMin"===c?f:"dataMax"===c||l<0?l:Math.max(Math.min(u[0],u[1]),0)}return"dataMin"===c?u[0]:"dataMax"===c?u[1]:u[0]}),dR(dz,"getComposedData",function(t){var e,r=t.props,n=t.item,i=t.xAxis,o=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,s=t.bandSize,u=t.dataKey,l=t.stackedData,f=t.dataStartIndex,p=t.displayedData,d=t.offset,h=r.layout,y=l&&l.length,v=dz.getBaseValue(r,n,i,o),m="horizontal"===h,b=!1,g=p.map(function(t,e){y?r=l[f+e]:Array.isArray(r=sZ(t,u))?b=!0:r=[v,r];var r,n=null==r[1]||y&&null==sZ(t,u);return m?{x:ul({axis:i,ticks:a,bandSize:s,entry:t,index:e}),y:n?null:o.scale(r[1]),value:r,payload:t}:{x:n?null:i.scale(r[1]),y:ul({axis:o,ticks:c,bandSize:s,entry:t,index:e}),value:r,payload:t}});return e=y||b?g.map(function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?o.scale(e):null}:{x:null!=e?i.scale(e):null,y:t.y}}):m?o.scale(v):i.scale(v),dC({points:g,baseLine:e,layout:h,isRange:b},d)}),dR(dz,"renderDotItem",function(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(x()(t))r=t(e);else{var n=(0,E.A)("recharts-area-dot","boolean"!=typeof t?t.className:""),i=e.key,o=dM(e,d_);r=a().createElement(e3,dT({},o,{key:i,className:n}))}return r});var dY=["viewBox"],dH=["viewBox"],dX=["ticks"];function dK(t){return(dK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dZ(){return(dZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dQ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dJ(Object(r),!0).forEach(function(e){d4(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d0(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function d1(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d6(n.key),n)}}function d2(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d2=function(){return!!t})()}function d5(t){return(d5=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function d3(t,e){return(d3=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d4(t,e,r){return(e=d6(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d6(t){var e=function(t,e){if("object"!=dK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dK(e)?e:e+""}var d8=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=d5(r),(e=function(t,e){if(e&&("object"===dK(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,d2()?Reflect.construct(r,i||[],d5(this).constructor):r.apply(this,i))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&d3(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=d0(t,dY),i=this.props,o=i.viewBox,a=d0(i,dH);return!J(r,o)||!J(n,a)||!J(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,s=c.x,u=c.y,l=c.width,f=c.height,p=c.orientation,d=c.tickSize,h=c.mirror,y=c.tickMargin,v=h?-1:1,m=t.tickSize||d,b=q(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=u+!h*f)-v*m)-v*y,o=b;break;case"left":n=i=t.coordinate,o=(e=(r=s+!h*l)-v*m)-v*y,a=b;break;case"right":n=i=t.coordinate,o=(e=(r=s+h*l)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(i=u+h*f)+v*m)+v*y,o=b}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.orientation,c=t.mirror,s=t.axisLine,u=dQ(dQ(dQ({},tx(this.props,!1)),tx(s,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var l=+("top"===o&&!c||"bottom"===o&&c);u=dQ(dQ({},u),{},{x1:e,y1:r+l*i,x2:e+n,y2:r+l*i})}else{var f=+("left"===o&&!c||"right"===o&&c);u=dQ(dQ({},u),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+i})}return a().createElement("line",dZ({},u,{className:(0,E.A)("recharts-cartesian-axis-line",S()(s,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var i=this,o=this.props,c=o.tickLine,s=o.stroke,u=o.tick,l=o.tickFormatter,f=o.unit,p=dG(dQ(dQ({},this.props),{},{ticks:t}),e,r),d=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),y=tx(this.props,!1),v=tx(u,!1),m=dQ(dQ({},y),{},{fill:"none"},tx(c,!1)),b=p.map(function(t,e){var r=i.getTickLineCoord(t),o=r.line,b=r.tick,g=dQ(dQ(dQ(dQ({textAnchor:d,verticalAnchor:h},y),{},{stroke:"none",fill:s},v),b),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:l});return a().createElement(tM,dZ({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},to(i.props,t,e)),c&&a().createElement("line",dZ({},m,o,{className:(0,E.A)("recharts-cartesian-axis-tick-line",S()(c,"className"))})),u&&n.renderTickItem(u,g,"".concat(x()(l)?l(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,i=e.height,o=e.ticksGenerator,c=e.className;if(e.hide)return null;var s=this.props,u=s.ticks,l=d0(s,dX),f=u;return(x()(o)&&(f=o(u&&u.length>0?this.props:l)),n<=0||i<=0||!f||!f.length)?null:a().createElement(tM,{className:(0,E.A)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),u4.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):x()(t)?t(e):a().createElement(n5,dZ({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&d1(n.prototype,e),r&&d1(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);function d7(t){return(d7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}d4(d8,"displayName","CartesianAxis"),d4(d8,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function d9(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d9=function(){return!!t})()}function ht(t){return(ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function he(t,e){return(he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hr(t,e,r){return(e=hn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hn(t){var e=function(t,e){if("object"!=d7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d7(e)?e:e+""}function hi(){return(hi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ho(t){var e=t.xAxisId,r=fq(),n=fF(),i=fU(e);return null==i?null:a().createElement(d8,hi({},i,{className:(0,E.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return s9(t,!0)}}))}var ha=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ht(t),function(t,e){if(e&&("object"===d7(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,d9()?Reflect.construct(t,e||[],ht(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&he(r,t),e=[{key:"render",value:function(){return a().createElement(ho,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hn(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function hc(t){return(hc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}hr(ha,"displayName","XAxis"),hr(ha,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function hs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hs=function(){return!!t})()}function hu(t){return(hu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hl(t,e){return(hl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hf(t,e,r){return(e=hp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hp(t){var e=function(t,e){if("object"!=hc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hc(e)?e:e+""}function hd(){return(hd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var hh=function(t){var e=t.yAxisId,r=fq(),n=fF(),i=f$(e);return null==i?null:a().createElement(d8,hd({},i,{className:(0,E.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return s9(t,!0)}}))},hy=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=hu(t),function(t,e){if(e&&("object"===hc(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hs()?Reflect.construct(t,e||[],hu(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&hl(r,t),e=[{key:"render",value:function(){return a().createElement(hh,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hp(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);hf(hy,"displayName","YAxis"),hf(hy,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var hv=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,i=void 0===n?"axis":n,c=t.validateTooltipEventTypes,s=void 0===c?["axis"]:c,u=t.axisComponents,l=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,d=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,o=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,s=t.barSize,l=t.layout,f=t.barGap,p=t.barCategoryGap,d=t.maxBarSize,h=dP(l),y=h.numericAxisName,v=h.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tf(t&&t.type);return e&&e.indexOf("Bar")>=0}),g=[];return r.forEach(function(r,h){var x=dv(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),w=void 0!==r.type.defaultProps?ds(ds({},r.type.defaultProps),r.props):r.props,O=w.dataKey,j=w.maxBarSize,S=w["".concat(y,"Id")],P=w["".concat(v,"Id")],A=u.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],i=w["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||M(!1);var o=n[i];return ds(ds({},t),{},du(du({},r.axisType,o),"".concat(r.axisType,"Ticks"),s9(o)))},{}),k=A[v],_=A["".concat(v,"Ticks")],E=n&&n[S]&&n[S].hasStack&&ud(r,n[S].stackGroups),T=tf(r.type).indexOf("Bar")>=0,N=ub(k,_),C=[],D=m&&s1({barSize:s,stackGroups:n,totalSize:"xAxis"===v?A[v].width:"yAxis"===v?A[v].height:void 0});if(T){var I,B,L=b()(j)?d:j,R=null!=(I=null!=(B=ub(k,_,!0))?B:L)?I:0;C=s2({barGap:f,barCategoryGap:p,bandSize:R!==N?R:N,sizeList:D[P],maxBarSize:L}),R!==N&&(C=C.map(function(t){return ds(ds({},t),{},{position:ds(ds({},t.position),{},{offset:t.position.offset-R/2})})}))}var U=r&&r.type&&r.type.getComposedData;U&&g.push({props:ds(ds({},U(ds(ds({},A),{},{displayedData:x,props:t,dataKey:O,item:r,bandSize:N,barPosition:C,offset:i,stackedData:E,layout:l,dataStartIndex:a,dataEndIndex:c}))),{},du(du(du({key:r.key||"item-".concat(h)},y,A[y]),v,A[v]),"animationId",o)),childIndex:th(t.children).indexOf(r),item:r})}),g},h=function(t,n){var i=t.props,o=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!tm({props:i}))return null;var s=i.children,l=i.layout,p=i.stackOffset,h=i.data,y=i.reverseStackOrder,v=dP(l),m=v.numericAxisName,b=v.cateAxisName,g=ty(s,r),x=us(h,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),w=u.reduce(function(t,e){var r="".concat(e.axisType,"Map");return ds(ds({},t),{},du({},r,dO(i,ds(ds({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:o,dataEndIndex:a}))))},{}),O=dA(ds(ds({},w),{},{props:i,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(t){w[t]=f(i,w[t],O,t.replace("Map",""),e)});var j=dj(w["".concat(b,"Map")]),S=d(i,ds(ds({},w),{},{dataStartIndex:o,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return ds(ds({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},y=function(t){var r;function n(t){var r,i,c,s,u;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return s=n,u=[t],s=de(s),du(c=function(t,e){if(e&&("object"===p6(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dt()?Reflect.construct(s,u||[],de(this).constructor):s.apply(this,u)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),du(c,"accessibilityManager",new ph),du(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;c.setState(ds({legendBBox:t},h({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:i},ds(ds({},c.state),{},{legendBBox:t}))))}}),du(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),du(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return ds({dataStartIndex:e,dataEndIndex:r},h({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),du(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=ds(ds({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;x()(n)&&n(r,t)}}),du(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?ds(ds({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;x()(n)&&n(r,t)}),du(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),du(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),du(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),du(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;x()(r)&&r(e,t)}),du(c,"handleOuterEvent",function(t){var e,r,n=tS(t),i=S()(c.props,"".concat(n));n&&x()(i)&&i(null!=(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))?e:{},t)}),du(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=ds(ds({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;x()(n)&&n(r,t)}}),du(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;x()(e)&&e(c.getMouseInfo(t),t)}),du(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;x()(e)&&e(c.getMouseInfo(t),t)}),du(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),du(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),du(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),du(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;x()(e)&&e(c.getMouseInfo(t),t)}),du(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;x()(e)&&e(c.getMouseInfo(t),t)}),du(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&pu.emit(pl,c.props.syncId,t,c.eventEmitterSymbol)}),du(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,i=c.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(ds({dataStartIndex:o,dataEndIndex:a},h({props:c.props,dataStartIndex:o,dataEndIndex:a,updateId:i},c.state)));else if(void 0!==t.activeTooltipIndex){var s=t.chartX,u=t.chartY,l=t.activeTooltipIndex,f=c.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)l=n(d,t);else if("value"===n){l=-1;for(var y=0;y<d.length;y++)if(d[y].value===t.activeLabel){l=y;break}}var v=ds(ds({},p),{},{x:p.left,y:p.top}),m=Math.min(s,v.x+v.width),b=Math.min(u,v.y+v.height),g=d[l]&&d[l].value,x=db(c.state,c.props.data,l),w=d[l]?{x:"horizontal"===r?d[l].coordinate:m,y:"horizontal"===r?b:d[l].coordinate}:dd;c.setState(ds(ds({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:l}))}else c.setState(t)}),du(c,"renderCursor",function(t){var r,n=c.state,i=n.isTooltipActive,o=n.activeCoordinate,s=n.activePayload,u=n.offset,l=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),d=null!=(r=t.props.active)?r:i,h=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(p5,{key:y,activeCoordinate:o,activePayload:s,activeTooltipIndex:l,chartName:e,element:t,isActive:d,layout:h,offset:u,tooltipAxisBandSize:f,tooltipEventType:p})}),du(c,"renderPolarAxis",function(t,e,r){var n=S()(t,"type.axisType"),i=S()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,s=void 0!==a?ds(ds({},a),t.props):t.props,u=i&&i[s["".concat(n,"Id")]];return(0,o.cloneElement)(t,ds(ds({},u),{},{className:(0,E.A)(n,u.className),key:t.key||"".concat(e,"-").concat(r),ticks:s9(u,!0)}))}),du(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,i=e.polarRadius,a=c.state,s=a.radiusAxisMap,u=a.angleAxisMap,l=Y(s),f=Y(u),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,o.cloneElement)(t,{polarAngles:Array.isArray(n)?n:s9(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:s9(l,!0).map(function(t){return t.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),du(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,i=e.height,a=c.props.margin||{},s=sV({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:l});if(!s)return null;var u=s.item,f=p9(s,p3);return(0,o.cloneElement)(u,ds(ds({},f),{},{chartWidth:n,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),du(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,i=tv(r,ee);if(!i)return null;var a=c.state,s=a.isTooltipActive,u=a.activeCoordinate,l=a.activePayload,f=a.activeLabel,p=a.offset,d=null!=(t=i.props.active)?t:s;return(0,o.cloneElement)(i,{viewBox:ds(ds({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?l:[],coordinate:u,accessibilityLayer:n})}),du(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,i=c.state,a=i.offset,s=i.dataStartIndex,u=i.dataEndIndex,l=i.updateId;return(0,o.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:ue(c.handleBrushChange,t.props.onChange),data:n,x:q(t.props.x)?t.props.x:a.left,y:q(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:q(t.props.width)?t.props.width:a.width,startIndex:s,endIndex:u,updateId:"brush-".concat(l)})}),du(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,i=c.state,a=i.xAxisMap,s=i.yAxisMap,u=i.offset,l=t.type.defaultProps||{},f=t.props,p=f.xAxisId,d=void 0===p?l.xAxisId:p,h=f.yAxisId,y=void 0===h?l.yAxisId:h;return(0,o.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[d],yAxis:s[y],viewBox:{x:u.left,y:u.top,width:u.width,height:u.height},clipPathId:n})}),du(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,i=t.basePoint,o=t.childIndex,a=t.isRange,c=[],s=e.props.key,u=void 0!==e.item.type.defaultProps?ds(ds({},e.item.type.defaultProps),e.item.props):e.item.props,l=u.activeDot,f=ds(ds({index:o,dataKey:u.dataKey,cx:r.x,cy:r.y,r:4,fill:s0(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tx(l,!1)),ti(l));return c.push(n.renderActiveDot(l,f,"".concat(s,"-activePoint-").concat(o))),i?c.push(n.renderActiveDot(l,ds(ds({},f),{},{cx:i.x,cy:i.y}),"".concat(s,"-basePoint-").concat(o))):a&&c.push(null),c}),du(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var i=c.getTooltipEventType(),a=c.state,s=a.isTooltipActive,u=a.tooltipAxis,l=a.activeTooltipIndex,f=a.activeLabel,p=tv(c.props.children,ee),d=n.props,h=d.points,y=d.isRange,v=d.baseLine,m=void 0!==n.item.type.defaultProps?ds(ds({},n.item.type.defaultProps),n.item.props):n.item.props,g=m.activeDot,x=m.hide,w=m.activeBar,O=m.activeShape,j=!!(!x&&s&&p&&(g||w||O)),S={};"axis"!==i&&p&&"click"===p.props.trigger?S={onClick:ue(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(S={onMouseLeave:ue(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:ue(c.handleItemMouseEnter,t.props.onMouseEnter)});var P=(0,o.cloneElement)(t,ds(ds({},n.props),S));if(j)if(l>=0){if(u.dataKey&&!u.allowDuplicatedCategory){var A="function"==typeof u.dataKey?function(t){return"function"==typeof u.dataKey?u.dataKey(t.payload):null}:"payload.".concat(u.dataKey.toString());_=K(h,A,f),E=y&&v&&K(v,A,f)}else _=null==h?void 0:h[l],E=y&&v&&v[l];if(O||w){var k=void 0!==t.props.activeIndex?t.props.activeIndex:l;return[(0,o.cloneElement)(t,ds(ds(ds({},n.props),S),{},{activeIndex:k})),null,null]}if(!b()(_))return[P].concat(dn(c.renderActivePoints({item:n,activePoint:_,basePoint:E,childIndex:l,isRange:y})))}else{var _,E,M,T=(null!=(M=c.getItemByXY(c.state.activeCoordinate))?M:{graphicalItem:P}).graphicalItem,N=T.item,C=void 0===N?t:N,D=T.childIndex,I=ds(ds(ds({},n.props),S),{},{activeIndex:D});return[(0,o.cloneElement)(C,I),null,null]}return y?[P,null,null]:[P,null]}),du(c,"renderCustomized",function(t,e,r){return(0,o.cloneElement)(t,ds(ds({key:"recharts-customized-".concat(r)},c.props),c.state))}),du(c,"renderMap",{CartesianGrid:{handler:dh,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:dh},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:dh},YAxis:{handler:dh},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=t.id)?r:W("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=_()(c.triggeredAfterMouseMove,null!=(i=t.throttleDelay)?i:1e3/60),c.state={},c}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&dr(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=tv(e,ee);if(o){var a=o.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,s=db(this.state,r,a,c),u=this.state.tooltipTicks[a].coordinate,l=(this.state.offset.top+n)/2,f="horizontal"===i?{x:u,y:l}:{y:u,x:l},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=ds(ds({},f),p.props.points[a].tooltipPosition),s=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:s,activeCoordinate:f};this.setState(d),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tw([tv(t.children,ee)],[tv(this.props.children,ee)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tv(this.props.children,ee);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return s.indexOf(e)>=0?e:i}return i}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var c=this.state,s=c.xAxisMap,u=c.yAxisMap,l=this.getTooltipEventType(),f=dg(this.state,this.props.data,this.props.layout,a);if("axis"!==l&&s&&u){var p=Y(s).scale,d=Y(u).scale,h=p&&p.invert?p.invert(i.chartX):null,y=d&&d.invert?d.invert(i.chartY):null;return ds(ds({},i),{},{xValue:h,yValue:y},f)}return f?ds(ds({},i),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var c=this.state,s=c.angleAxisMap,u=c.radiusAxisMap;return s&&u?uH({x:i,y:o},Y(s)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tv(t,ee),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),ds(ds({},ti(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pu.on(pl,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pu.removeListener(pl,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===tf(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,i=e.height,o=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:i,width:o})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=p7(e,2),n=r[0],i=r[1];return ds(ds({},t),{},du({},n,i.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=p7(e,2),n=r[0],i=r[1];return ds(ds({},t),{},du({},n,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],c=a.props,s=a.item,u=void 0!==s.type.defaultProps?ds(ds({},s.type.defaultProps),s.props):s.props,l=tf(s.type);if("Bar"===l){var f=(c.data||[]).find(function(e){return nf(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===l){var p=(c.data||[]).find(function(e){return uH(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(lq(a,n)||lF(a,n)||lV(a,n)){var d=function(t){var e,r,n,i=t.activeTooltipItem,o=t.graphicalItem,a=t.itemData,c=(lq(o,i)?e="trapezoids":lF(o,i)?e="sectors":lV(o,i)&&(e="points"),e),s=lq(o,i)?null==(r=i.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:lF(o,i)?null==(n=i.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:lV(o,i)?i.payload:{},u=a.filter(function(t,e){var r=cH()(s,t),n=o.props[c].filter(function(t){var e;return(lq(o,i)?e=lW:lF(o,i)?e=lG:lV(o,i)&&(e=lY),e)(t,i)}),a=o.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(u[u.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:u.data}),h=void 0===u.activeIndex?d:u.activeIndex;return{graphicalItem:ds(ds({},a),{},{childIndex:h}),payload:lV(a,n)?u.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tm(this))return null;var n=this.props,i=n.children,o=n.className,c=n.width,s=n.height,u=n.style,l=n.compact,f=n.title,p=n.desc,d=tx(p9(n,p4),!1);if(l)return a().createElement(fR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tk,p8({},d,{width:c,height:s,title:f,desc:p}),this.renderClipPath(),tj(i,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!=(t=this.props.tabIndex)?t:0,d.role=null!=(e=this.props.role)?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return a().createElement(fR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",p8({className:(0,E.A)("recharts-wrapper",o),style:ds({position:"relative",cursor:"default",width:c,height:s},u)},h,{ref:function(t){r.container=t}}),a().createElement(tk,p8({},d,{width:c,height:s,title:f,desc:p,style:dp}),this.renderClipPath(),tj(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dl(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);du(y,"displayName",e),du(y,"defaultProps",ds({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),du(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,i=t.children,o=t.width,a=t.height,c=t.layout,s=t.stackOffset,u=t.margin,l=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=dS(t);return ds(ds(ds({},p),{},{updateId:0},h(ds(ds({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:s,prevMargin:u,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||o!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||s!==e.prevStackOffset||!J(u,e.prevMargin)){var d=dS(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=ds(ds({},dg(e,n,c)),{},{updateId:e.updateId+1}),m=ds(ds(ds({},d),y),v);return ds(ds(ds({},m),h(ds({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:s,prevMargin:u,prevChildren:i})}if(!tw(i,e.prevChildren)){var g,x,w,O,j=tv(i,uU),S=j&&null!=(g=null==(x=j.props)?void 0:x.startIndex)?g:l,P=j&&null!=(w=null==(O=j.props)?void 0:O.endIndex)?w:f,A=b()(n)||S!==l||P!==f?e.updateId+1:e.updateId;return ds(ds({updateId:A},h(ds(ds({props:t},e),{},{updateId:A,dataStartIndex:S,dataEndIndex:P}),e)),{},{prevChildren:i,dataStartIndex:S,dataEndIndex:P})}return null}),du(y,"renderActiveDot",function(t,e,r){var n;return n=(0,o.isValidElement)(t)?(0,o.cloneElement)(t,e):x()(t)?t(e):a().createElement(e3,e),a().createElement(tM,{className:"recharts-active-dot",key:r},n)});var v=(0,o.forwardRef)(function(t,e){return a().createElement(y,p8({},t,{ref:e}))});return v.displayName=y.displayName,v}({chartName:"AreaChart",GraphicalChild:dz,axisComponents:[{axisType:"xAxis",AxisComp:ha},{axisType:"yAxis",AxisComp:hy}],formatAxisMap:function(t,e,r,n,i){var o=t.width,a=t.height,c=t.layout,s=t.children,u=Object.keys(e),l={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tv(s,fn);return u.reduce(function(o,a){var s,u,p,d,h,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort(Z);if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,k="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(s=A*k/2),"no-gap"===y.padding){var _=G(t.barCategoryGap,A*k),E=A*k/2;s=E-_-(E-_)/k*_}}}u="xAxis"===n?[r.left+(g.left||0)+(s||0),r.left+r.width-(g.right||0)-(s||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(s||0),r.top+r.height-(g.bottom||0)-(s||0)]:y.range,w&&(u=[u[1],u[0]]);var M=ur(y,i,f),T=M.scale,N=M.realScaleType;T.domain(m).range(u),un(T);var C=uu(T,fc(fc({},y),{},{realScaleType:N}));"xAxis"===n?(h="top"===v&&!x||"bottom"===v&&x,p=r.left,d=l[O]-h*y.height):"yAxis"===n&&(h="left"===v&&!x||"right"===v&&x,p=l[O]-h*y.width,d=r.top);var D=fc(fc(fc({},y),C),{},{realScaleType:N,x:p,y:d,scale:T,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=ub(D,C),y.hide||"xAxis"!==n?y.hide||(l[O]+=(h?-1:1)*D.width):l[O]+=(h?-1:1)*D.height,fc(fc({},o),{},fs({},a,D))},{})}}),hm=["x1","y1","x2","y2","key"],hb=["offset"];function hg(t){return(hg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hx(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=hg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hg(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hO(){return(hO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hj(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var hS=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,s=t.ry;return a().createElement("rect",{x:n,y:i,ry:s,width:o,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function hP(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(x()(t))r=t(e);else{var n=e.x1,i=e.y1,o=e.x2,c=e.y2,s=e.key,u=tx(hj(e,hm),!1),l=(u.offset,hj(u,hb));r=a().createElement("line",hO({},l,{x1:n,y1:i,x2:o,y2:c,fill:"none",key:s}))}return r}function hA(t){var e=t.x,r=t.width,n=t.horizontal,i=void 0===n||n,o=t.horizontalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return hP(i,hw(hw({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function hk(t){var e=t.y,r=t.height,n=t.vertical,i=void 0===n||n,o=t.verticalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return hP(i,hw(hw({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function h_(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,s=t.horizontalPoints,u=t.horizontal;if(!(void 0===u||u)||!e||!e.length)return null;var l=s.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==l[0]&&l.unshift(0);var f=l.map(function(t,s){var u=l[s+1]?l[s+1]-t:i+c-t;if(u<=0)return null;var f=s%e.length;return a().createElement("rect",{key:"react-".concat(s),y:t,x:n,height:u,width:o,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function hE(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,i=t.x,o=t.y,c=t.width,s=t.height,u=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var l=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==l[0]&&l.unshift(0);var f=l.map(function(t,e){var u=l[e+1]?l[e+1]-t:i+c-t;if(u<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:o,width:u,height:s,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var hM=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return s7(dG(hw(hw(hw({},d8.defaultProps),r),{},{ticks:s9(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},hT=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return s7(dG(hw(hw(hw({},d8.defaultProps),r),{},{ticks:s9(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},hN={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function hC(t){var e,r,n,i,c,s,u=fq(),l=fF(),f=(0,o.useContext)(fD),p=hw(hw({},t),{},{stroke:null!=(e=t.stroke)?e:hN.stroke,fill:null!=(r=t.fill)?r:hN.fill,horizontal:null!=(n=t.horizontal)?n:hN.horizontal,horizontalFill:null!=(i=t.horizontalFill)?i:hN.horizontalFill,vertical:null!=(c=t.vertical)?c:hN.vertical,verticalFill:null!=(s=t.verticalFill)?s:hN.verticalFill,x:q(t.x)?t.x:f.left,y:q(t.y)?t.y:f.top,width:q(t.width)?t.width:f.width,height:q(t.height)?t.height:f.height}),d=p.x,h=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,w=Y((0,o.useContext)(fT)),O=fz();if(!q(y)||y<=0||!q(v)||v<=0||!q(d)||d!==+d||!q(h)||h!==+h)return null;var j=p.verticalCoordinatesGenerator||hM,S=p.horizontalCoordinatesGenerator||hT,P=p.horizontalPoints,A=p.verticalPoints;if((!P||!P.length)&&x()(S)){var k=b&&b.length,_=S({yAxis:O?hw(hw({},O),{},{ticks:k?b:O.ticks}):void 0,width:u,height:l,offset:f},!!k||m);er(Array.isArray(_),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(hg(_),"]")),Array.isArray(_)&&(P=_)}if((!A||!A.length)&&x()(j)){var E=g&&g.length,M=j({xAxis:w?hw(hw({},w),{},{ticks:E?g:w.ticks}):void 0,width:u,height:l,offset:f},!!E||m);er(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(hg(M),"]")),Array.isArray(M)&&(A=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(hS,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(hA,hO({},p,{offset:f,horizontalPoints:P,xAxis:w,yAxis:O})),a().createElement(hk,hO({},p,{offset:f,verticalPoints:A,xAxis:w,yAxis:O})),a().createElement(h_,hO({},p,{horizontalPoints:P})),a().createElement(hE,hO({},p,{verticalPoints:A})))}hC.displayName="CartesianGrid";var hD=r(63974),hI=r(15251),hB=r(64021),hL=r(56085),hR=r(70334),hU=r(24934),hz=r(85814),h$=r.n(hz);function hq({title:t,description:e="Upgrade to Basic plan or higher to unlock this premium analytics feature.",height:r="h-[350px]"}){return(0,i.jsxs)("div",{className:`w-full ${r} flex items-center justify-center rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 overflow-hidden relative`,children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-transparent to-amber-50 dark:to-amber-950/20 opacity-50"}),(0,i.jsx)("div",{className:"absolute opacity-10 text-amber-500 dark:text-amber-400",children:(0,i.jsx)(hB.A,{className:"w-32 h-32"})}),(0,i.jsxs)("div",{className:"z-10 text-center max-w-md px-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,i.jsx)(hL.A,{className:"h-5 w-5 text-amber-500 dark:text-amber-400"}),(0,i.jsxs)("h3",{className:"text-lg font-medium text-amber-700 dark:text-amber-300",children:["Premium Feature: ",t]})]}),(0,i.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 mb-6",children:e}),(0,i.jsx)("div",{children:(0,i.jsx)(hU.$,{asChild:!0,className:"bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white",children:(0,i.jsxs)(h$(),{href:"/dashboard/business/plan",className:"flex items-center gap-2",children:["Upgrade Now",(0,i.jsx)(hR.A,{className:"h-4 w-4"})]})})})]})]})}function hF(t){return(hF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hV(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=hF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hF(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hG(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var hY=(0,o.forwardRef)(function(t,e){var r,n=t.aspect,i=t.initialDimension,c=void 0===i?{width:-1,height:-1}:i,s=t.width,u=void 0===s?"100%":s,l=t.height,f=void 0===l?"100%":l,p=t.minWidth,d=void 0===p?0:p,h=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,b=void 0===m?0:m,g=t.id,x=t.className,w=t.onResize,O=t.style,j=(0,o.useRef)(null),S=(0,o.useRef)();S.current=w,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var P=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],s=!0,u=!1;try{o=(r=r.call(t)).next,!1;for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return hG(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hG(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),A=P[0],k=P[1],M=(0,o.useCallback)(function(t,e){k(function(r){var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,i=r.height;M(n,i),null==(e=S.current)||e.call(S,n,i)};b>0&&(t=_()(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=j.current.getBoundingClientRect();return M(r.width,r.height),e.observe(j.current),function(){e.disconnect()}},[M,b]);var T=(0,o.useMemo)(function(){var t=A.containerWidth,e=A.containerHeight;if(t<0||e<0)return null;er($(u)||$(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),er(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=$(u)?t:u,i=$(f)?e:f;n&&n>0&&(r?i=r/n:i&&(r=i*n),y&&i>y&&(i=y)),er(r>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,i,u,f,d,h,n);var c=!Array.isArray(v)&&tf(v.type).endsWith("Chart");return a().Children.map(v,function(t){return a().isValidElement(t)?(0,o.cloneElement)(t,hW({width:r,height:i},c?{style:hW({height:"100%",width:"100%",maxHeight:i,maxWidth:r},t.props.style)}:{})):t})},[n,v,f,y,h,d,A,u]);return a().createElement("div",{id:g?"".concat(g):void 0,className:(0,E.A)("recharts-responsive-container",x),style:hW(hW({},void 0===O?{}:O),{},{width:u,height:f,minWidth:d,minHeight:h,maxHeight:y}),ref:j},T)});let hH={light:"",dark:".dark"},hX=o.createContext(null);function hK({id:t,className:e,children:r,config:n,...a}){let c=o.useId(),s=`chart-${t||c.replace(/:/g,"")}`;return(0,i.jsx)(hX.Provider,{value:{config:n},children:(0,i.jsxs)("div",{"data-slot":"chart","data-chart":s,className:(0,p.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",e),...a,children:[(0,i.jsx)(hZ,{id:s,config:n}),(0,i.jsx)(hY,{children:r})]})})}let hZ=({id:t,config:e})=>{let r=Object.entries(e).filter(([,t])=>t.theme||t.color);return r.length?(0,i.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(hH).map(([e,n])=>`
${n} [data-chart=${t}] {
${r.map(([t,r])=>{let n=r.theme?.[e]||r.color;return n?`  --color-${t}: ${n};`:null}).join("\n")}
}
`).join("\n")}}):null};function hJ({active:t,payload:e,className:r,indicator:n="dot",hideLabel:a=!1,hideIndicator:c=!1,label:s,labelFormatter:u,labelClassName:l,formatter:f,color:d,nameKey:h,labelKey:y}){let{config:v}=function(){let t=o.useContext(hX);if(!t)throw Error("useChart must be used within a <ChartContainer />");return t}(),m=o.useMemo(()=>{if(a||!e?.length)return null;let[t]=e,r=`${y||t?.dataKey||t?.name||"value"}`,n=hQ(v,t,r),o=y||"string"!=typeof s?n?.label:v[s]?.label||s;return u?(0,i.jsx)("div",{className:(0,p.cn)("font-medium",l),children:u(o,e)}):o?(0,i.jsx)("div",{className:(0,p.cn)("font-medium",l),children:o}):null},[s,u,e,a,l,v,y]);if(!t||!e?.length)return null;let b=1===e.length&&"dot"!==n;return(0,i.jsxs)("div",{className:(0,p.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",r),children:[b?null:m,(0,i.jsx)("div",{className:"grid gap-1.5",children:e.map((t,e)=>{let r=`${h||t.name||t.dataKey||"value"}`,o=hQ(v,t,r),a=d||t.payload.fill||t.color;return(0,i.jsx)("div",{className:(0,p.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===n&&"items-center"),children:f&&t?.value!==void 0&&t.name?f(t.value,t.name,t,e,t.payload):(0,i.jsxs)(i.Fragment,{children:[o?.icon?(0,i.jsx)(o.icon,{}):!c&&(0,i.jsx)("div",{className:(0,p.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":b&&"dashed"===n}),style:{"--color-bg":a,"--color-border":a}}),(0,i.jsxs)("div",{className:(0,p.cn)("flex flex-1 justify-between leading-none",b?"items-end":"items-center"),children:[(0,i.jsxs)("div",{className:"grid gap-1.5",children:[b?m:null,(0,i.jsx)("span",{className:"text-muted-foreground",children:o?.label||t.name})]}),t.value&&(0,i.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:t.value.toLocaleString()})]})]})},t.dataKey)})})]})}function hQ(t,e,r){if("object"!=typeof e||null===e)return;let n="payload"in e&&"object"==typeof e.payload&&null!==e.payload?e.payload:void 0,i=r;return r in e&&"string"==typeof e[r]?i=e[r]:n&&r in n&&"string"==typeof n[r]&&(i=n[r]),i in t?t[i]:t[r]}let h0={visits:{label:"Visits",color:"var(--chart-1)"}};function h1({trend7Days:t,trend30Days:e,userPlan:r}){let n=(0,hI.a)(),[a,u]=(0,o.useState)("7days"),l=t=>new Date(t).toLocaleDateString("en-IN",{day:"2-digit",month:"short"}),f=(t=>{let e=[],r=new Date;for(let n=t-1;n>=0;n--){let t=new Date;t.setDate(r.getDate()-n);let i=t.toISOString().split("T")[0];e.push(i)}return e})("7days"===a?7:30),d=new Map;"7days"===a?t.forEach(t=>{d.set(t.date,t.visits)}):e.forEach(t=>{d.set(t.date,t.visits)});let h=f.map(t=>({date:t,visits:d.has(t)?d.get(t):0,formattedDate:l(t)})),y=Math.max(...h.map(t=>t.visits)),v=y<=0?5:y<=5?Math.ceil(1.2*y):y<=10?Math.ceil(1.1*y):Math.ceil(1.05*y);return"growth"!==r&&"pro"!==r&&"enterprise"!==r?(0,i.jsx)(hq,{title:"Daily Visit Trend",description:"Upgrade to Growth plan or higher to see detailed daily visit trends for your business."}):(0,i.jsxs)(c.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.5}}},initial:"hidden",animate:"visible",className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,i.jsxs)("div",{className:"relative z-10",children:[(0,i.jsx)("div",{className:"flex items-start justify-between mb-6",children:(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,i.jsx)(s.A,{className:"w-5 h-5 text-primary"})}),(0,i.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,i.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Trend Analysis"})]}),(0,i.jsx)("h3",{className:"text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Daily Visit Trend"}),(0,i.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Track unique visitors over time"})]})}),(0,i.jsx)("div",{className:"mb-6 flex items-center justify-end",children:(0,i.jsxs)(hD.l6,{value:a,onValueChange:t=>{u(t)},children:[(0,i.jsx)(hD.bq,{className:"w-[160px] border-neutral-200 dark:border-neutral-700",children:(0,i.jsx)(hD.yv,{placeholder:"Select Range"})}),(0,i.jsxs)(hD.gC,{children:[(0,i.jsx)(hD.eb,{value:"7days",children:"Last 7 Days"}),(0,i.jsx)(hD.eb,{value:"30days",children:"Last 30 Days"})]})]})}),(0,i.jsx)("div",{className:"h-[280px] w-full",children:(0,i.jsx)(hK,{config:h0,className:"h-full w-full",children:(0,i.jsxs)(hv,{data:h,margin:n?{top:5,right:5,left:0,bottom:5}:{top:10,right:10,left:0,bottom:5},children:[(0,i.jsx)("defs",{children:(0,i.jsxs)("linearGradient",{id:"fillVisits",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,i.jsx)("stop",{offset:"5%",stopColor:"var(--color-visits)",stopOpacity:.8}),(0,i.jsx)("stop",{offset:"95%",stopColor:"var(--color-visits)",stopOpacity:.1})]})}),(0,i.jsx)(hC,{strokeDasharray:"3 3",vertical:!1}),(0,i.jsx)(ha,{dataKey:"formattedDate",axisLine:!1,tickLine:!1,tickMargin:8,minTickGap:32,tickFormatter:t=>n?new Date(t).getDate().toString():t}),(0,i.jsx)(hy,{axisLine:!1,tickLine:!1,domain:[0,v],allowDecimals:!1,tickFormatter:t=>(0,p.gY)(Math.floor(t))}),(0,i.jsx)(ee,{content:(0,i.jsx)(hJ,{labelFormatter:t=>`Date: ${t}`,formatter:t=>[(0,p.gY)(Number(t))," Visits"]})}),(0,i.jsx)(dz,{dataKey:"visits",type:"natural",fill:"url(#fillVisits)",stroke:"var(--color-visits)",strokeWidth:n?1.5:2})]})})})]})]})}var h2=r(48730);let h5={visits:{label:"Visits",color:"var(--chart-2)"}};function h3({data:t,userPlan:e}){let r=(0,hI.a)(),n=t=>0===t?"12 AM":12===t?"12 PM":t<12?`${t} AM`:`${t-12} PM`,o=(()=>{let t=[];for(let e=0;e<24;e++)t.push(e);return t})(),a=new Map;t.forEach(t=>{a.set(t.hour,t.visits)});let s=o.map(t=>({hour:t,visits:a.has(t)?a.get(t):0,formattedHour:n(t)})),u=Math.max(...s.map(t=>t.visits)),l=u<=0?5:u<=5?Math.ceil(1.2*u):u<=10?Math.ceil(1.1*u):Math.ceil(1.05*u);return"growth"!==e&&"pro"!==e&&"enterprise"!==e?(0,i.jsx)(hq,{title:"Hourly Visit Trend",description:"Upgrade to Growth plan or higher to see detailed hourly visit trends for your business."}):(0,i.jsxs)(c.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.5}}},initial:"hidden",animate:"visible",className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,i.jsxs)("div",{className:"relative z-10",children:[(0,i.jsx)("div",{className:"flex items-start justify-between mb-6",children:(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,i.jsx)(h2.A,{className:"w-5 h-5 text-primary"})}),(0,i.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,i.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Hourly Breakdown"})]}),(0,i.jsx)("h3",{className:"text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Hourly Visit Trend"}),(0,i.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Today's visitor activity by hour"})]})}),(0,i.jsx)("div",{className:"h-[280px] w-full",children:(0,i.jsx)(hK,{config:h5,className:"h-full w-full",children:(0,i.jsxs)(hv,{data:s,margin:r?{top:5,right:5,left:0,bottom:5}:{top:10,right:10,left:0,bottom:5},children:[(0,i.jsx)("defs",{children:(0,i.jsxs)("linearGradient",{id:"fillVisits",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,i.jsx)("stop",{offset:"5%",stopColor:"var(--color-visits)",stopOpacity:.8}),(0,i.jsx)("stop",{offset:"95%",stopColor:"var(--color-visits)",stopOpacity:.1})]})}),(0,i.jsx)(hC,{strokeDasharray:"3 3",vertical:!1}),(0,i.jsx)(ha,{dataKey:"formattedHour",axisLine:!1,tickLine:!1,tickMargin:8,minTickGap:32,tickFormatter:t=>{if(r){let e=t.match(/(\d+)/);if(e)return e[1]}return t}}),(0,i.jsx)(hy,{axisLine:!1,tickLine:!1,domain:[0,l],allowDecimals:!1,tickFormatter:t=>(0,p.gY)(Math.floor(t))}),(0,i.jsx)(ee,{content:(0,i.jsx)(hJ,{labelFormatter:t=>`Time: ${t}`,formatter:t=>[(0,p.gY)(Number(t))," Visits"]})}),(0,i.jsx)(dz,{dataKey:"visits",type:"natural",fill:"url(#fillVisits)",stroke:"var(--color-visits)",strokeWidth:r?1.5:2})]})})})]})]})}var h4=r(47033),h6=r(14952);let h8={visits:{label:"Visits",color:"var(--chart-3)"}};function h7({monthlyTrend:t,availableYears:e,userPlan:r}){let n=(0,hI.a)(),[a,u]=(0,o.useState)(new Date().getFullYear()),l=t.filter(t=>t.year===a),f=t=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t-1],d=(()=>{let t=[];for(let e=1;e<=12;e++)t.push(e);return t})(),h=new Map;l.forEach(t=>{h.set(t.month,t.visits)});let y=d.map(t=>({month:f(t),visits:h.has(t)?h.get(t):0,monthNum:t})),v=Math.max(...y.map(t=>t.visits)),m=v<=0?5:v<=5?Math.ceil(1.2*v):v<=10?Math.ceil(1.1*v):Math.ceil(1.05*v);return"growth"!==r&&"pro"!==r&&"enterprise"!==r?(0,i.jsx)(hq,{title:"Monthly Visit Trend",description:"Upgrade to Growth plan or higher to see detailed monthly visit trends for your business."}):(0,i.jsxs)(c.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.5}}},initial:"hidden",animate:"visible",className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,i.jsxs)("div",{className:"relative z-10",children:[(0,i.jsx)("div",{className:"flex items-start justify-between mb-6",children:(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,i.jsx)(s.A,{className:"w-5 h-5 text-primary"})}),(0,i.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,i.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Long-term Analysis"})]}),(0,i.jsx)("h3",{className:"text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Monthly Visit Trend"}),(0,i.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Track visitor patterns over months"})]})}),(0,i.jsxs)("div",{className:"mb-6 flex items-center justify-end gap-3",children:[(0,i.jsx)(hU.$,{variant:"outline",size:"sm",onClick:()=>{let t=[...e].sort((t,e)=>t-e),r=t.indexOf(a);r>0&&u(t[r-1])},disabled:0===e.indexOf(a),className:"border-neutral-200 dark:border-neutral-700",children:(0,i.jsx)(h4.A,{className:"h-4 w-4"})}),(0,i.jsxs)(hD.l6,{value:a.toString(),onValueChange:t=>{u(parseInt(t))},children:[(0,i.jsx)(hD.bq,{className:"w-[120px] border-neutral-200 dark:border-neutral-700",children:(0,i.jsx)(hD.yv,{placeholder:"Select Year"})}),(0,i.jsx)(hD.gC,{children:e.map(t=>(0,i.jsx)(hD.eb,{value:t.toString(),children:t},t))})]}),(0,i.jsx)(hU.$,{variant:"outline",size:"sm",onClick:()=>{let t=[...e].sort((t,e)=>t-e),r=t.indexOf(a);r<t.length-1&&u(t[r+1])},disabled:e.indexOf(a)===e.length-1,className:"border-neutral-200 dark:border-neutral-700",children:(0,i.jsx)(h6.A,{className:"h-4 w-4"})})]}),(0,i.jsx)("div",{className:"h-[320px] w-full",children:(0,i.jsx)(hK,{config:h8,className:"h-full w-full",children:(0,i.jsxs)(hv,{data:y,margin:n?{top:5,right:5,left:0,bottom:5}:{top:10,right:10,left:0,bottom:5},children:[(0,i.jsx)("defs",{children:(0,i.jsxs)("linearGradient",{id:"fillVisits",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,i.jsx)("stop",{offset:"5%",stopColor:"var(--color-visits)",stopOpacity:.8}),(0,i.jsx)("stop",{offset:"95%",stopColor:"var(--color-visits)",stopOpacity:.1})]})}),(0,i.jsx)(hC,{strokeDasharray:"3 3",vertical:!1}),(0,i.jsx)(ha,{dataKey:"month",axisLine:!1,tickLine:!1,tickMargin:8,minTickGap:32}),(0,i.jsx)(hy,{axisLine:!1,tickLine:!1,domain:[0,m],allowDecimals:!1,tickFormatter:t=>(0,p.gY)(Math.floor(t))}),(0,i.jsx)(ee,{content:(0,i.jsx)(hJ,{labelFormatter:t=>`Month: ${t}`,formatter:t=>[(0,p.gY)(Number(t))," Visits"]})}),(0,i.jsx)(dz,{dataKey:"visits",type:"natural",fill:"url(#fillVisits)",stroke:"var(--color-visits)",strokeWidth:n?1.5:2})]})})})]})]})}function h9({analyticsData:t,userId:e,initialProfile:r,userPlan:n}){let[a,u]=(0,o.useState)(r);(0,o.useRef)(r);let[l,f]=(0,o.useState)(!1),p={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}};return(0,i.jsxs)(c.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-10",children:[(0,i.jsx)("div",{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,i.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,i.jsx)(s.A,{className:"w-5 h-5 text-primary"})}),(0,i.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,i.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Performance Insights"})]}),(0,i.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Business Analytics"}),(0,i.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Comprehensive insights into your business card performance, visitor engagement, and growth trends with real-time data."})]})}),(0,i.jsxs)(c.P.div,{variants:p,className:"space-y-8",children:[(0,i.jsx)(h,{profile:a,initialProfile:r}),(0,i.jsx)(v,{totalUniqueVisits:a.total_visits,todayUniqueVisits:a.today_visits,yesterdayUniqueVisits:a.yesterday_visits,currentMonthUniqueVisits:t.currentMonthUniqueVisits,isVisitUpdated:l})]}),(0,i.jsxs)(c.P.div,{variants:p,className:"space-y-8",children:[(0,i.jsxs)("div",{className:"grid gap-6 grid-cols-1 xl:grid-cols-2",children:[(0,i.jsx)(h1,{trend7Days:t.dailyTrend7Days||[],trend30Days:t.dailyTrend30Days||[],userPlan:n}),(0,i.jsx)(h3,{data:t.hourlyTrendToday||[],userPlan:n})]}),(0,i.jsx)(h7,{monthlyTrend:t.monthlyTrend||[],availableYears:t.availableYears||[],userPlan:n})]})]})}},45058:(t,e,r)=>{var n=r(42082),i=r(8852),o=r(67619),a=r(46436);t.exports=function(t){return o(t)?n(a(t)):i(t)}},45488:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>s});var n=r(37413);r(61120);var i=r(32032),o=r(64522);let a=["member_name","title","business_name","business_category","contact_email","phone","address_line","pincode","city","state","locality"];var c=r(39916);async function s({children:t}){let e=await (0,i.createClient)(),r=null,s=null,u=null,l=null,{data:{user:f}}=await e.auth.getUser();if(f){let{data:t,error:n}=await e.from("business_profiles").select(`
        business_name,
        logo_url,
        member_name,
        title,
        business_category,
        contact_email,
        phone,
        address_line,
        pincode,
        city,
        state,
        locality
      `).eq("id",f.id).single(),{data:i}=await e.from("payment_subscriptions").select("plan_id").eq("business_profile_id",f.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(n)console.error("Error fetching business profile in layout:",n.message);else if(t){r=t.business_name,s=t.logo_url,u=t.member_name,l=i?.plan_id||"free";let e=function(t){if(!t)return{isComplete:!1,missingFields:[...a],missingFieldLabels:["Your name","Your title","Business name","Business category","Contact email","Primary phone","Address line","Pincode","City","State","Locality/area"]};let e=[],r=[],n={member_name:"Your name",title:"Your title",business_name:"Business name",business_category:"Business category",contact_email:"Contact email",phone:"Primary phone",address_line:"Address line",pincode:"Pincode",city:"City",state:"State",locality:"Locality/area"};return a.forEach(i=>{let o=t[i];o&&""!==String(o).trim()||(e.push(i),r.push(n[i]))}),{isComplete:0===e.length,missingFields:e,missingFieldLabels:r}}(t);if(!e.isComplete){let t=function(t){if(0===t.length)return"";if(1===t.length)return`Please complete your ${t[0].toLowerCase()} to access the dashboard.`;if(2===t.length)return`Please complete your ${t[0].toLowerCase()} and ${t[1].toLowerCase()} to access the dashboard.`;let e=t[t.length-1],r=t.slice(0,-1);return`Please complete your ${r.map(t=>t.toLowerCase()).join(", ")}, and ${e.toLowerCase()} to access the dashboard.`}(e.missingFieldLabels);(0,c.redirect)(`/onboarding?message=${encodeURIComponent(t)}`)}}}else console.warn("No user found in business dashboard layout.");return(0,n.jsx)(o.default,{businessName:r,logoUrl:s,memberName:u,userPlan:l,children:t})}},45603:(t,e,r)=>{var n=r(20540),i=r(55048);t.exports=function(t,e,r){var o=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:o,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),i=r(66354),o=r(11424);t.exports=function(t,e){return o(i(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),i=r(89185),o=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=i,a.prototype.has=o,t.exports=a},46436:(t,e,r)=>{var n=r(49227),i=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-i?"-0":e}},47033:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47212:(t,e,r)=>{var n=r(87270),i=r(30316),o=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var s=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),s(t,o(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,a=o&&o.exports===i&&n.process,c=function(){try{var t=o&&o.require&&o.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),i=r(91928),o=r(48169);t.exports=i?function(t,e){return i(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:o},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+e+"]",o="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",s="[\\ufe0e\\ufe0f]?",u="(?:\\u200d(?:"+[i,o,a].join("|")+")"+s+c+")*",l=RegExp(n+"(?="+n+")|"+("(?:"+[i+r+"?",r,o,a,"["+e+"]"].join("|"))+")"+(s+c+u),"g");t.exports=function(t){return t.match(l)||[]}},48730:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},48872:(t,e,r)=>{"use strict";r.d(e,{i:()=>m});var n=r(67218);r(79130);var i=r(62351),o=r(32032),a=r(30468),c=r(17478);async function s(){let t=await (0,o.createClient)();try{let{data:{user:e},error:r}=await t.auth.getUser();if(r)return console.error("Error fetching authenticated user:",r.message),{user:null,error:"User not found or authentication error."};return{user:e,error:null}}catch(t){return console.error("Unexpected error fetching authenticated user:",t),{user:null,error:"An unexpected error occurred."}}}async function u(t){let e=await (0,o.createClient)();try{let{data:r,error:n}=await e.from(a.CG.BUSINESS_PROFILES).select("total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days").eq(a.cZ.ID,t).single();if(n)return console.error("Error fetching business profile analytics data:",n.message),{data:null,error:n.message};return{data:r,error:null}}catch(t){return console.error("Unexpected error fetching business profile analytics data:",t),{data:null,error:"An unexpected error occurred."}}}async function l(t,e,r){let n=await (0,o.createClient)();try{let{data:i,error:o}=await n.rpc("get_daily_unique_visit_trend",{business_id:t,start_date:e,end_date:r});if(o)return console.error("Error fetching daily unique visit trend:",o.message),{data:null,error:o.message};return{data:i,error:null}}catch(t){return console.error("Unexpected error fetching daily unique visit trend:",t),{data:null,error:"An unexpected error occurred."}}}async function f(t,e){let r=await (0,o.createClient)();try{let{data:n,error:i}=await r.rpc("get_hourly_unique_visit_trend",{business_id:t,target_date:e});if(i)return console.error("Error fetching hourly unique visit trend:",i.message),{data:null,error:i.message};return{data:n,error:null}}catch(t){return console.error("Unexpected error fetching hourly unique visit trend:",t),{data:null,error:"An unexpected error occurred."}}}async function p(t,e,r){let n=await (0,o.createClient)();try{let{data:i,error:o}=await n.rpc("get_monthly_unique_visits",{business_id:t,target_year:e,target_month:r});if(o)return console.error("Error fetching monthly unique visits:",o.message),{data:null,error:o.message};return{data:i,error:null}}catch(t){return console.error("Unexpected error fetching monthly unique visits:",t),{data:null,error:"An unexpected error occurred."}}}async function d(t,e,r,n,i){let a=await (0,o.createClient)();try{let{data:o,error:c}=await a.rpc("get_monthly_unique_visit_trend",{business_id:t,start_year:e,start_month:r,end_year:n,end_month:i});if(c)return console.error("Error fetching monthly unique visit trend:",c.message),{data:null,error:c.message};return{data:o,error:null}}catch(t){return console.error("Unexpected error fetching monthly unique visit trend:",t),{data:null,error:"An unexpected error occurred."}}}async function h(t){let e=await (0,o.createClient)();try{let{data:r,error:n}=await e.rpc("get_available_years_for_monthly_metrics",{business_id:t});if(n)return console.error("Error fetching available years for monthly metrics:",n.message),{data:null,error:n.message};return{data:r,error:null}}catch(t){return console.error("Unexpected error fetching available years for monthly metrics:",t),{data:null,error:"An unexpected error occurred."}}}async function y(t){let e=await (0,o.createClient)();try{let{data:r,error:n}=await e.rpc("get_total_unique_visits",{business_id:t});if(n)return console.error("Error fetching total unique visits:",n.message),{data:null,error:n.message};return{data:r,error:null}}catch(t){return console.error("Unexpected error fetching total unique visits:",t),{data:null,error:"An unexpected error occurred."}}}let v=(t=0)=>{let e=new Date(new Date().getTime()+198e5);e.setUTCDate(e.getUTCDate()-t);let r=e.getUTCFullYear(),n=String(e.getUTCMonth()+1).padStart(2,"0"),i=String(e.getUTCDate()).padStart(2,"0");return`${r}-${n}-${i}`};async function m(t){let e="growth"===t||"pro"===t||"enterprise"===t;(0,i.unstable_noStore)();let{user:r,error:n}=await s();if(n||!r)return{error:"Authentication required."};let o=r.id;try{let t,r,n,i,a,{data:c,error:s}=await u(o);if(s||!c)throw Error(`Failed to fetch business profile analytics data: ${s||"Profile data is null"}`);let m=v(0),b=v(1),g=v(7),x=v(30),w=new Date,O=new Date(w.getTime()+198e5),j=O.getUTCFullYear(),S=O.getUTCMonth()+1,P=S-1,A=j;0===P&&(P=12,A=j-1);let k=[],_=[],E=[];if(e){let{data:t,error:e}=await l(o,g,m);if(e)throw Error(`Failed to fetch 7-day trend: ${e||"Unknown error"}`);k=t??[];let{data:r,error:n}=await l(o,x,m);if(n)throw Error(`Failed to fetch 30-day trend: ${n||"Unknown error"}`);_=r??[];let{data:i,error:a}=await f(o,m);if(a)throw Error(`Failed to fetch hourly trend: ${a||"Unknown error"}`);E=i??[]}let{data:M,error:T}=await p(o,j,S);if(T)throw Error(`Failed to fetch current month visits: ${T||"Unknown error"}`);let{data:N,error:C}=await p(o,A,P);if(C)throw Error(`Failed to fetch previous month visits: ${C||"Unknown error"}`);let D=[j],I=[];if(e){let{data:t,error:e}=await h(o);if(e)throw Error(`Failed to fetch available years: ${e||"Unknown error"}`);D=t&&Array.isArray(t)&&t.length>0?t.map(t=>t.year):[j];let{data:r,error:n}=await d(o,Math.min(...D),1,j,S);if(n)throw Error(`Failed to fetch monthly trend: ${n||"Unknown error"}`);I=r??[]}if(c)t=c.total_visits??0,r=c.today_visits??0,n=c.yesterday_visits??0,i=c.visits_7_days??0,a=c.visits_30_days??0;else{console.warn("Using calculated visit metrics due to missing pre-aggregated data"),r=k?.find(t=>t.date===m)?.visits??0,n=k?.find(t=>t.date===b)?.visits??0;let{data:e,error:c}=await y(o);if(c)throw Error(`Failed to fetch total visits: ${c||"Unknown error"}`);t=e??0,i=k?.reduce((t,e)=>t+e.visits,0)??0,a=_?.reduce((t,e)=>t+e.visits,0)??0}return{data:{totalUniqueVisits:Number(t),todayUniqueVisits:Number(r),yesterdayUniqueVisits:Number(n),visits7Days:Number(i),visits30Days:Number(a),currentMonthUniqueVisits:Number(M||0),previousMonthUniqueVisits:Number(N||0),currentYear:j,currentMonth:S,dailyTrend7Days:k??[],dailyTrend30Days:_??[],hourlyTrendToday:E??[],monthlyTrend:I??[],availableYears:D}}}catch(t){return console.error("getVisitAnalytics Error:",t),{error:t instanceof Error?t.message:"An unknown error occurred."}}}(0,c.D)([m]),(0,n.A)(m,"4065fb9b6fea3e2af3f93558f66e7a6f0552d2b9bc",null)},49227:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"symbol"==typeof t||i(t)&&"[object Symbol]"==n(t)}},50465:(t,e,r)=>{"use strict";r.r(e),r.d(e,{"4065fb9b6fea3e2af3f93558f66e7a6f0552d2b9bc":()=>n.i});var n=r(48872)},51449:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>l,routeModule:()=>p,tree:()=>u});var n=r(65239),i=r(48088),o=r(88170),a=r.n(o),c=r(30893),s={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(s[t]=()=>c[t]);r.d(e,s);let u={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40307)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,46055))).default(t)],apple:[],openGraph:[async t=>(await Promise.resolve().then(r.bind(r,90253))).default(t)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,46055))).default(t)],apple:[],openGraph:[async t=>(await Promise.resolve().then(r.bind(r,90253))).default(t)],twitter:[],manifest:void 0}}]}.children,l=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/analytics/page",pathname:"/dashboard/business/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=0,o=[];++r<n;){var a=t[r];e(a,r,t)&&(o[i++]=a)}return o}},52823:(t,e,r)=>{var n=r(85406),i=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!i&&i in t}},52931:(t,e,r)=>{var n=r(77834),i=r(89605),o=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return i(t);var e=[];for(var r in Object(t))o.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54765:(t,e,r)=>{var n=r(67554),i=r(32269);t.exports=function(t,e){var r=-1,o=i(t)?Array(t.length):[];return n(t,function(t,n,i){o[++r]=e(t,n,i)}),o}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},56085:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,i){if(null==r)return r;if(!n(r))return t(r,i);for(var o=r.length,a=e?o:-1,c=Object(r);(e?a--:++a<o)&&!1!==i(c[a],a,c););return r}}},57202:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,i=n(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),i=r(35163),o=r(40542),a=r(38428),c=r(69619),s=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var u=-1,l=e.length,f=!1;++u<l;){var p=s(e[u]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++u!=l?f:!!(l=null==t?0:t.length)&&c(l)&&a(p,l)&&(o(t)||i(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61192:(t,e,r)=>{"use strict";r.d(e,{default:()=>f});var n=r(60687);r(43210);var i=r(27625),o=r(41956),a=r(38606),c=r(24861),s=r(21121),u=r(96241),l=r(52529);function f({children:t,businessName:e,logoUrl:r,memberName:f,userPlan:p}){return(0,n.jsx)(l.Q,{children:(0,n.jsxs)(c.GB,{children:[(0,n.jsx)(s.s,{businessName:e,logoUrl:r,memberName:f,userPlan:p}),(0,n.jsxs)(c.sF,{children:[(0,n.jsxs)(i.default,{businessName:e,logoUrl:r,userName:f,children:[(0,n.jsx)(c.x2,{className:"ml-auto md:ml-0"})," ",(0,n.jsx)(o.ThemeToggle,{variant:"dashboard"})]}),(0,n.jsx)("main",{className:(0,u.cn)("flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto","pb-20 sm:pb-18 md:pb-6","bg-white dark:bg-black"),children:t}),(0,n.jsx)(a.default,{})]})]})})}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),i=r(52823),o=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,s=Object.prototype,u=Function.prototype.toString,l=s.hasOwnProperty,f=RegExp("^"+u.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!o(t)||i(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),i=r(22),o=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),i=r(40542),o=r(27467);t.exports=function(t){return"string"==typeof t||!i(t)&&o(t)&&"[object String]"==n(t)}},63974:(t,e,r)=>{"use strict";r.d(e,{TR:()=>h,bq:()=>p,eb:()=>y,gC:()=>d,l6:()=>u,s3:()=>l,yv:()=>f});var n=r(60687);r(43210);var i=r(28695),o=r(78272),a=r(13964),c=r(3589),s=r(96241);function u({...t}){return(0,n.jsx)(i.bL,{"data-slot":"select",...t})}function l({...t}){return(0,n.jsx)(i.YJ,{"data-slot":"select-group",...t})}function f({...t}){return(0,n.jsx)(i.WT,{"data-slot":"select-value",...t})}function p({className:t,size:e="default",children:r,...a}){return(0,n.jsxs)(i.l9,{"data-slot":"select-trigger","data-size":e,className:(0,s.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a,children:[r,(0,n.jsx)(i.In,{asChild:!0,children:(0,n.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function d({className:t,children:e,position:r="popper",...o}){return(0,n.jsx)(i.ZL,{children:(0,n.jsxs)(i.UC,{"data-slot":"select-content",className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...o,children:[(0,n.jsx)(v,{}),(0,n.jsx)(i.LM,{className:(0,s.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:e}),(0,n.jsx)(m,{})]})})}function h({className:t,...e}){return(0,n.jsx)(i.JU,{"data-slot":"select-label",className:(0,s.cn)("text-muted-foreground px-2 py-1.5 text-xs",t),...e})}function y({className:t,children:e,...r}){return(0,n.jsxs)(i.q7,{"data-slot":"select-item",className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(i.VF,{children:(0,n.jsx)(a.A,{className:"size-4"})})}),(0,n.jsx)(i.p4,{children:e})]})}function v({className:t,...e}){return(0,n.jsx)(i.PP,{"data-slot":"select-scroll-up-button",className:(0,s.cn)("flex cursor-default items-center justify-center py-1",t),...e,children:(0,n.jsx)(c.A,{className:"size-4"})})}function m({className:t,...e}){return(0,n.jsx)(i.wn,{"data-slot":"select-scroll-down-button",className:(0,s.cn)("flex cursor-default items-center justify-center py-1",t),...e,children:(0,n.jsx)(o.A,{className:"size-4"})})}},63979:(t,e,r)=>{var n=r(52599),i=r(6330),o=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return o.call(t,e)})}:i},64021:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},64522:(t,e,r)=>{"use strict";r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\components\\\\BusinessDashboardClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx","default")},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var i=-1,o=t.criteria,a=e.criteria,c=o.length,s=r.length;++i<c;){var u=n(o[i],a[i]);if(u){if(i>=s)return u;return u*("desc"==r[i]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var i=-1,o=Object(e),a=n(e),c=a.length;c--;){var s=a[t?c:++i];if(!1===r(o[s],s,o))break}return e}}},66354:(t,e,r)=>{var n=r(85244),i=Math.max;t.exports=function(t,e,r){return e=i(void 0===e?t.length-1:e,0),function(){for(var o=arguments,a=-1,c=i(o.length-e,0),s=Array(c);++a<c;)s[a]=o[e+a];a=-1;for(var u=Array(e+1);++a<e;)u[a]=o[a];return u[e]=r(s),n(t,this,u)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var i=e(),o=16-(i-n);if(n=i,o>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),i=r(34117),o=r(48385);t.exports=function(t){return i(t)?o(t):n(t)}},66819:(t,e,r)=>{"use strict";r.d(e,{cn:()=>o,gV:()=>a});var n=r(75986),i=r(8974);function o(...t){return(0,i.QP)((0,n.$)(t))}function a(t){if(!t)return null;let e=t.trim();return(e.startsWith("+91")?e=e.substring(3):12===e.length&&e.startsWith("91")&&(e=e.substring(2)),/^\d{10}$/.test(e))?e:null}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),i=r(28837),o=r(94388),a=r(35800),c=r(58744);function s(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=i,s.prototype.get=o,s.prototype.has=a,s.prototype.set=c,t.exports=s},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),i=r(37575),o=r(75411),a=r(34746),c=r(25118),s=r(30854);function u(t){var e=this.__data__=new n(t);this.size=e.size}u.prototype.clear=i,u.prototype.delete=o,u.prototype.get=a,u.prototype.has=c,u.prototype.set=s,t.exports=u},67367:(t,e,r)=>{var n=r(99525),i=r(22),o=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var s=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),s(t,i(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),i=r(49227),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||i(t))||a.test(t)||!o.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),i=r(99114),o=r(22);t.exports=function(t,e){var r={};return e=o(e,3),i(t,function(t,i,o){n(r,i,e(t,i,o))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),i=Object.prototype,o=i.hasOwnProperty,a=i.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=o.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var i=a.call(t);return n&&(e?t[c]=r:delete t[c]),i}},70334:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71960:t=>{t.exports=function(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},73226:(t,e,r)=>{"use strict";r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\analytics\\\\components\\\\EnhancedAnalyticsPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedAnalyticsPageClient.tsx","default")},73739:(t,e,r)=>{var n=r(11539),i=1/0;t.exports=function(t){return t?(t=n(t))===i||t===-i?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},73830:(t,e,r)=>{var n=r(85745),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,o=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(i,function(t,r,n,i){e.push(n?i.replace(o,"$1"):r||t)}),e})},74075:t=>{"use strict";t.exports=require("zlib")},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),i=r(93311),o=r(41132);t.exports=function(t){var e=i(t);return 1==e.length&&e[0][2]?o(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,i){return!(r=e(t,n,i))}),!!r}},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),i=r(15871);t.exports=function(t,e,r,o){var a=r.length,c=a,s=!o;if(null==t)return!c;for(t=Object(t);a--;){var u=r[a];if(s&&u[2]?u[1]!==t[u[0]]:!(u[0]in t))return!1}for(;++a<c;){var l=(u=r[a])[0],f=t[l],p=u[1];if(s&&u[2]){if(void 0===f&&!(l in t))return!1}else{var d=new n;if(o)var h=o(f,p,l,t,e,d);if(!(void 0===h?i(p,f,3,o,d):h))return!1}}return!0}},79428:t=>{"use strict";t.exports=require("buffer")},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},79988:(t,e,r)=>{Promise.resolve().then(r.bind(r,61192))},80195:(t,e,r)=>{var n=r(79474),i=r(21367),o=r(40542),a=r(49227),c=1/0,s=n?n.prototype:void 0,u=s?s.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(o(e))return i(e,t)+"";if(a(e))return u?u.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),i=r(1944),o=e&&!e.nodeType&&e,a=o&&t&&!t.nodeType&&t,c=a&&a.exports===o?n.Buffer:void 0,s=c?c.isBuffer:void 0;t.exports=s||i},80458:(t,e,r)=>{var n=r(29395),i=r(69619),o=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return o(t)&&i(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81630:t=>{"use strict";t.exports=require("http")},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,i=null===t,o=t==t,a=n(t),c=void 0!==e,s=null===e,u=e==e,l=n(e);if(!s&&!l&&!a&&t>e||a&&c&&u&&!s&&!l||i&&c&&u||!r&&u||!o)return 1;if(!i&&!a&&!l&&t<e||l&&r&&o&&!i&&!a||s&&r&&o||!c&&o||!u)return -1}return 0}},82038:(t,e,r)=>{var n=r(57202),i=r(35163),o=r(40542),a=r(80329),c=r(38428),s=r(10090),u=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=o(t),l=!r&&i(t),f=!r&&!l&&a(t),p=!r&&!l&&!f&&s(t),d=r||l||f||p,h=d?n(t.length,String):[],y=h.length;for(var v in t)(e||u.call(t,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&h.push(v);return h}},84031:(t,e,r)=>{"use strict";var n=r(34452);function i(){}function o(){}o.resetWarningCache=i,t.exports=function(){function t(t,e,r,i,o,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(73739);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),i=r(35163),o=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return o(t)||i(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),i="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||i||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),i=r(17518),o=r(46229),a=r(7383);t.exports=o(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),i(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function i(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,i=e?e.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var a=t.apply(this,n);return r.cache=o.set(i,a)||o,a};return r.cache=new(i.Cache||n),r}i.Cache=n,t.exports=i},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),i=r(7383),o=r(73739);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&i(e,r,a)&&(r=a=void 0),e=o(e),void 0===r?(r=e,e=0):r=o(r),a=void 0===a?e<r?1:-1:o(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),i=r(84261),o=r(89492),a=r(90200),c=r(39672);function s(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=i,s.prototype.get=o,s.prototype.has=a,s.prototype.set=c,t.exports=s},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return i.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},89963:(t,e,r)=>{Promise.resolve().then(r.bind(r,44599))},90200:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:i.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),i=r(99180),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var i=t.length,o=r+(n?1:-1);n?o--:++o<i;)if(e(t[o],o,t))return o;return -1}},91645:t=>{"use strict";t.exports=require("net")},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92662:(t,e,r)=>{var n=r(46328),i=r(80704),o=r(71960),a=r(58276),c=r(95308),s=r(2408);t.exports=function(t,e,r){var u=-1,l=i,f=t.length,p=!0,d=[],h=d;if(r)p=!1,l=o;else if(f>=200){var y=e?null:c(t);if(y)return s(y);p=!1,l=a,h=new n}else h=e?[]:d;e:for(;++u<f;){var v=t[u],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=h.length;b--;)if(h[b]===m)continue e;e&&h.push(m),d.push(v)}else l(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},93311:(t,e,r)=>{var n=r(34883),i=r(7651);t.exports=function(t){for(var e=i(t),r=e.length;r--;){var o=e[r],a=t[o];e[r]=[o,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"number"==typeof t||i(t)&&"[object Number]"==n(t)}},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},94735:t=>{"use strict";t.exports=require("events")},95308:(t,e,r)=>{var n=r(34772),i=r(36959),o=r(2408);t.exports=n&&1/o(new n([,-0]))[1]==1/0?function(t){return new n(t)}:i},95539:(t,e,r)=>{Promise.resolve().then(r.bind(r,73226))},95746:(t,e,r)=>{var n=r(15909),i=r(29205),o=r(29508),a=r(61320),c=r(19976);function s(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=i,s.prototype.get=o,s.prototype.has=a,s.prototype.set=c,t.exports=s},96678:(t,e,r)=>{var n=r(91290),i=r(39774),o=r(74610);t.exports=function(t,e,r){return e==e?o(t,e,r):n(t,i,r)}},97668:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),u=Symbol.for("react.context"),l=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case o:case c:case a:case p:case d:return t;default:switch(t=t&&t.$$typeof){case l:case u:case f:case y:case h:case s:return t;default:return e}}case i:return e}}}(t)===o}},98451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,i,o){for(var a=-1,c=r(e((n-t)/(i||1)),0),s=Array(c);c--;)s[o?c:++a]=t,t+=i;return s}},99114:(t,e,r)=>{var n=r(12344),i=r(7651);t.exports=function(t,e){return t&&n(t,e,i)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,6380,4851,4017,2186,3037,3739,9538,5265],()=>r(51449));module.exports=n})();