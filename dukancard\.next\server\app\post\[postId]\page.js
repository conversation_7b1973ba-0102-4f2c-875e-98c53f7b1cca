(()=>{var e={};e.id=559,e.ids=[559],e.modules={500:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},1366:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\components\\\\post\\\\ConditionalPostLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("C:\\web-app\\dukancard\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10959:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(37413),a=r(4536),n=r.n(a),o=r(26373);let l=(0,o.A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),i=(0,o.A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),d=(0,o.A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function c(){return(0,s.jsx)("div",{className:"min-h-screen bg-white dark:bg-black flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)(l,{className:"w-16 h-16 text-gray-400 dark:text-gray-600 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Post Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"The post you're looking for doesn't exist or has been removed."})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(n(),{href:"/",className:"inline-flex items-center justify-center w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors",children:[(0,s.jsx)(i,{className:"w-4 h-4 mr-2"}),"Go to Homepage"]}),(0,s.jsxs)("button",{onClick:()=>window.history.back(),className:"inline-flex items-center justify-center w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,s.jsx)(d,{className:"w-4 h-4 mr-2"}),"Go Back"]})]})]})})}},11997:e=>{"use strict";e.exports=require("punycode")},13889:(e,t,r)=>{Promise.resolve().then(r.bind(r,72945))},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},17886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>s.B,"4009d2933c186901a1403c78560cd329314f35e005":()=>n.ys,"401bed28fefc4fa316c9657ce0fb9ce951b2f6cefe":()=>o.E,"4034b523293d3ffd37df154f2b315894750110b2c1":()=>o.H,"404b1d9bf7310a5b14e59bf18e153b291565613a40":()=>a.w,"4078b27f89338bdea95f74b22fd4d22c4710876eab":()=>n.pD,"60183dfe46fbe995d7961a9cf8fe9545820e57eb60":()=>n.Rl,"60e85a41c7f76e7af6443414547e080e8dfaf27c80":()=>n.yf,"60fef8195d22c743fbc958b9c6e7229ba7a09ebfb2":()=>n.gg});var s=r(64275),a=r(16111),n=r(33635),o=r(26471)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:d,...c},u)=>(0,s.createElement)("svg",{ref:u,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",l),...c},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),i=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...o},i)=>(0,s.createElement)(l,{ref:i,iconNode:t,className:n(`lucide-${a(e)}`,r),...o}));return r.displayName=`${e}`,r}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30395:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),a=r(51536);function n({post:e}){return(0,s.jsx)(a.A,{post:e,index:0,showActualAspectRatio:!0,disablePostClick:!0,enableImageFullscreen:!0})}},33872:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37250:(e,t,r)=>{Promise.resolve().then(r.bind(r,69153))},48340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},49041:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),a=r(16189);function n(){let e=(0,a.useRouter)();return(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("button",{onClick:()=>{window.history.length>1?e.back():e.push("/")},className:"inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors cursor-pointer",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Feed"]})})}},49929:(e,t,r)=>{Promise.resolve().then(r.bind(r,72945)),Promise.resolve().then(r.bind(r,49041)),Promise.resolve().then(r.bind(r,96324)),Promise.resolve().then(r.bind(r,30395))},53236:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413),a=r(62555);function n(){return(0,s.jsx)("div",{className:"min-h-screen bg-white dark:bg-black",children:(0,s.jsx)("div",{className:"max-w-2xl mx-auto py-4 px-4",children:(0,s.jsx)(a.default,{showImage:!0,showProducts:!0,index:0})})})}},62555:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\components\\\\feed\\\\shared\\\\PostCardSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65679:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,dynamic:()=>h,generateMetadata:()=>p,generateStaticParams:()=>f,revalidate:()=>x});var s=r(37413),a=r(39916),n=r(61120),o=r(32032);async function l(e,t=0){try{if(!e||"string"!=typeof e)return{success:!1,message:"Invalid post ID provided",error:"INVALID_POST_ID"};let t=await (0,o.createClient)(),{data:r,error:s}=await t.from("unified_posts").select("*").eq("id",e).single();if(s){if(console.error("Error fetching single post:",s),"PGRST116"===s.code)return{success:!1,message:"Post not found",error:"POST_NOT_FOUND"};return{success:!1,message:"Failed to fetch post",error:s.message}}if(!r)return{success:!1,message:"Post not found",error:"POST_NOT_FOUND"};let a={id:r.id,post_source:r.post_source,author_id:r.author_id,content:r.content||"",image_url:r.image_url,created_at:r.created_at,updated_at:r.updated_at,city_slug:r.city_slug,state_slug:r.state_slug,locality_slug:r.locality_slug,pincode:r.pincode,product_ids:r.product_ids||[],mentioned_business_ids:r.mentioned_business_ids||[],author_name:r.author_name,author_avatar:r.author_avatar,business_slug:r.business_slug,phone:r.phone,whatsapp_number:r.whatsapp_number,business_plan:r.business_plan};return{success:!0,message:"Post fetched successfully",data:a}}catch(r){if(console.error("Unexpected error in fetchSinglePost:",r),t<3&&r instanceof Error&&(r.message.includes("network")||r.message.includes("timeout")))return console.log(`Retrying fetchSinglePost, attempt ${t+1}/3`),await new Promise(e=>setTimeout(e,1e3*(t+1))),l(e,t+1);return{success:!1,message:"An unexpected error occurred",error:r instanceof Error?r.message:"UNKNOWN_ERROR"}}}var i=r(81733),d=r(62555),c=r(84599),u=r(1366);async function m({params:e}){let{postId:t}=await e;t&&"string"==typeof t||(0,a.notFound)();let r=await l(t);return r.success&&r.data||(0,a.notFound)(),(0,s.jsxs)(u.default,{children:[(0,s.jsx)(c.default,{}),(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)(d.default,{showImage:!0,showProducts:!0}),children:(0,s.jsx)(i.default,{post:r.data})})]})}async function p({params:e}){let{postId:t}=await e,r=`http://localhost:3000/post/${t}`,s=await l(t);if(!s.success||!s.data)return{title:"Post Not Found - Dukancard",description:"The requested post could not be found."};let a=s.data,n=`${a.author_name||"Post"} - Dukancard`,o=a.content.length>160?`${a.content.substring(0,157)}...`:a.content;return{title:n,description:o,openGraph:{title:n,description:o,url:r,type:"article",images:a.image_url?[{url:a.image_url,width:1200,height:630,alt:`Post by ${a.author_name}`}]:[],publishedTime:a.created_at,authors:a.author_name?[a.author_name]:[]},twitter:{card:"summary_large_image",title:n,description:o,images:a.image_url?[a.image_url]:[]},alternates:{canonical:r}}}let h="force-dynamic",x=3600,f=async()=>[]},69153:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687);r(43210);var a=r(85814),n=r.n(a),o=r(93613),l=r(78122),i=r(32192);function d({error:e,reset:t}){return(0,s.jsx)("div",{className:"min-h-screen bg-white dark:bg-black flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)(o.A,{className:"w-16 h-16 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Something went wrong"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"We encountered an error while loading this post. This might be a temporary issue."}),!1]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:t,className:"inline-flex items-center justify-center w-full px-4 py-2 bg-[var(--brand-gold)] text-white font-medium rounded-lg transition-colors",children:[(0,s.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Try Again"]}),(0,s.jsxs)(n(),{href:"/",className:"inline-flex items-center justify-center w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,s.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Go to Homepage"]})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},75034:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},77441:(e,t,r)=>{Promise.resolve().then(r.bind(r,62555))},78122:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81620:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},81630:e=>{"use strict";e.exports=require("http")},81733:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\components\\\\post\\\\SinglePostView.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx","default")},81904:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},83139:(e,t,r)=>{"use strict";r.d(t,{O:()=>j});var s=r(60687),a=r(43210),n=r(85814),o=r.n(n),l=r(38782),i=r(24861),d=r(47696),c=r(17971),u=r(40083),m=r(42154),p=r(70373),h=r(55629),x=r(24934),f=r(96241);let b=e=>{if(!e)return"?";let t=e.trim().split(/\s+/);return 1===t.length&&t[0]?t[0].charAt(0).toUpperCase():t.length>1&&t[0]&&t[t.length-1]?t[0].charAt(0).toUpperCase()+t[t.length-1].charAt(0).toUpperCase():"?"};function g({user:e}){let{isMobile:t}=(0,i.cL)(),r=b(e.name),n=e.name||"User",[o,l]=(0,a.useState)(!1);return(0,s.jsx)(i.wZ,{className:(0,f.cn)(t?"pb-16":o?"pb-14":""),children:(0,s.jsx)(i.FX,{children:(0,s.jsxs)(h.rI,{children:[(0,s.jsx)(h.ty,{asChild:!0,children:(0,s.jsxs)(i.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,s.jsxs)(p.eu,{className:"h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30",children:[e.avatar?(0,s.jsx)(p.BK,{src:e.avatar,alt:n}):null,(0,s.jsx)(p.q5,{className:"rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 text-blue-800 dark:text-blue-200 text-xs border border-[var(--brand-gold)]/30",children:r})]}),(0,s.jsx)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:(0,s.jsx)("span",{className:"truncate font-semibold",children:n})}),(0,s.jsx)(c.A,{className:"ml-auto size-4"})]})}),(0,s.jsxs)(h.SQ,{className:"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg",side:t?"bottom":"right",align:"end",sideOffset:4,children:[(0,s.jsx)(h.lp,{className:"p-0 font-normal",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,s.jsxs)(p.eu,{className:"h-8 w-8 rounded-lg",children:[e.avatar?(0,s.jsx)(p.BK,{src:e.avatar,alt:n}):null,(0,s.jsx)(p.q5,{className:"rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 text-blue-800 dark:text-blue-200 text-xs",children:r})]}),(0,s.jsx)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:(0,s.jsx)("span",{className:"truncate font-semibold",children:n})})]})}),(0,s.jsx)(h.mB,{}),(0,s.jsx)("form",{action:m.B,className:"w-full px-2 py-1.5",children:(0,s.jsxs)(x.$,{variant:"ghost",type:"submit",className:"w-full justify-start p-0 h-auto font-normal cursor-pointer",children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Log out"]})})]})]})})})}var y=r(14952),v=r(8936);function j({userName:e,userAvatarUrl:t,...r}){return(0,s.jsxs)(i.Bx,{collapsible:"icon",...r,children:[(0,s.jsx)(i.Gh,{className:"border-b border-border/50",children:(0,s.jsx)("div",{className:"flex items-center px-2 py-4",children:(0,s.jsxs)(o(),{href:"/?view=home",className:"flex flex-col group transition-all duration-200 hover:opacity-80",children:[(0,s.jsxs)("span",{className:"font-bold text-lg text-[var(--brand-gold)]",children:["Dukan",(0,s.jsx)("span",{className:"text-foreground",children:"card"})]}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Customer Portal"})]})})}),(0,s.jsx)(i.Yv,{className:"px-2",children:(0,s.jsx)(i.Cn,{children:(0,s.jsx)(i.wZ,{className:"space-y-1",children:[{title:"Feed",icon:"LayoutList",url:"/dashboard/customer",items:[]},{title:"Overview",icon:"LayoutDashboard",url:"/dashboard/customer/overview",items:[]},{title:"Social",icon:"Users",items:[{title:"Subscriptions",icon:"Bell",url:"/dashboard/customer/subscriptions"},{title:"My Likes",icon:"Heart",url:"/dashboard/customer/likes"},{title:"My Reviews",icon:"Star",url:"/dashboard/customer/reviews"}]},{title:"Account",icon:"User",items:[{title:"Profile",icon:"User",url:"/dashboard/customer/profile"},{title:"Settings",icon:"Settings",url:"/dashboard/customer/settings"}]}].map((e,t)=>0===t||1===t?(0,s.jsx)(i.FX,{children:(0,s.jsx)(i.Uj,{asChild:!0,tooltip:e.title,className:"h-10 rounded-lg",children:(0,s.jsxs)(l.d,{href:e.url||"#",className:"flex items-center gap-3",children:[e.icon&&v.K[e.icon]&&a.createElement(v.K[e.icon],{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"font-medium",children:e.title})]})})},e.title):(0,s.jsx)(d.Nt,{defaultOpen:2===t,className:"group/collapsible",children:(0,s.jsxs)(i.FX,{children:[(0,s.jsx)(d.R6,{asChild:!0,children:(0,s.jsxs)(i.Uj,{className:"h-10 rounded-lg",children:[e.icon&&v.K[e.icon]&&a.createElement(v.K[e.icon],{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"font-medium",children:e.title}),(0,s.jsx)(y.A,{className:"ml-auto h-4 w-4 text-muted-foreground group-data-[state=open]/collapsible:rotate-90"})]})}),(0,s.jsx)(d.Ke,{className:"transition-all duration-200",children:(0,s.jsx)(i.q9,{className:"ml-4 mt-1 space-y-1 border-l border-border/30 pl-4",children:e.items.map(e=>(0,s.jsx)(i.Fg,{children:(0,s.jsx)(i.Cp,{asChild:!0,className:"h-9 rounded-md",children:(0,s.jsxs)(l.d,{href:e.url||"#",className:"flex items-center gap-3",children:[e.icon&&v.K[e.icon]&&a.createElement(v.K[e.icon],{className:"h-3.5 w-3.5 text-muted-foreground"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:e.title})]})})},e.title))})})]})},e.title))})})}),(0,s.jsx)(i.CG,{className:"border-t border-border/50 p-2",children:(0,s.jsx)(g,{user:{name:e,avatar:t}})}),(0,s.jsx)(i.jM,{})]})}},84517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["post",{children:["[postId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65679)),"C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx"]}]},{error:[()=>Promise.resolve().then(r.bind(r,96963)),"C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,62493)),"C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,10959)),"C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/post/[postId]/page",pathname:"/post/[postId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},84599:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\components\\\\post\\\\BackNavigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx","default")},84777:(e,t,r)=>{Promise.resolve().then(r.bind(r,62555)),Promise.resolve().then(r.bind(r,84599)),Promise.resolve().then(r.bind(r,1366)),Promise.resolve().then(r.bind(r,81733))},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96324:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(60687),a=r(43210),n=r(38398),o=r(24861),l=r(21121),i=r(83139),d=r(27625),c=r(41956),u=r(38606),m=r(96241),p=r(52529);function h({children:e}){let[t,r]=(0,a.useState)(null),[h,x]=(0,a.useState)(null),[f,b]=(0,a.useState)(null),[g,y]=(0,a.useState)(!0);return((0,n.U)(),g)?(0,s.jsx)("div",{className:"min-h-screen bg-white dark:bg-black",children:(0,s.jsx)("div",{className:"max-w-2xl mx-auto py-4 px-4",children:e})}):t?"business"===f&&h?(0,s.jsx)(p.Q,{children:(0,s.jsxs)(o.GB,{children:[(0,s.jsx)(l.s,{businessName:h.business_name||null,logoUrl:h.logo_url||null,memberName:t.user_metadata?.full_name||t.user_metadata?.name||null,userPlan:"free"}),(0,s.jsxs)(o.sF,{children:[(0,s.jsxs)(d.default,{businessName:h.business_name||null,logoUrl:h.logo_url||null,userName:t.user_metadata?.full_name||t.user_metadata?.name||null,children:[(0,s.jsx)(o.x2,{className:"ml-auto md:ml-0"}),(0,s.jsx)(c.ThemeToggle,{variant:"dashboard"})]}),(0,s.jsx)("main",{className:(0,m.cn)("flex-grow p-3 sm:p-4 md:p-5 pb-16 md:pb-6","bg-white dark:bg-black"),children:e}),(0,s.jsx)(u.default,{})]})]})}):"customer"===f?(0,s.jsxs)(o.GB,{children:[(0,s.jsx)(i.O,{userName:t.user_metadata?.full_name||t.user_metadata?.name||null,userAvatarUrl:h?.avatar_url||null}),(0,s.jsxs)(o.sF,{children:[(0,s.jsxs)(d.default,{userName:t.user_metadata?.full_name||t.user_metadata?.name||null,businessName:null,logoUrl:h?.avatar_url||null,children:[(0,s.jsx)(o.x2,{className:"ml-auto md:ml-0"}),(0,s.jsx)(c.ThemeToggle,{variant:"dashboard"})]}),(0,s.jsx)("main",{className:(0,m.cn)("flex-grow p-3 sm:p-4 md:p-5 pb-16 md:pb-6","bg-white dark:bg-black"),children:e}),(0,s.jsx)(u.default,{})]})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black",children:[(0,s.jsx)("div",{className:"max-w-2xl mx-auto py-4 px-4 pb-16 md:pb-6",children:e}),(0,s.jsx)(u.default,{})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black",children:[(0,s.jsx)("div",{className:"max-w-2xl mx-auto py-4 px-4 pb-16 md:pb-6",children:e}),(0,s.jsx)(u.default,{})]})}},96963:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\post\\\\[postId]\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx","default")},97082:(e,t,r)=>{Promise.resolve().then(r.bind(r,96963))},97992:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,1753,6380,399,2836,4212,1606,3037,3739,9538,5265,4308],()=>r(84517));module.exports=s})();