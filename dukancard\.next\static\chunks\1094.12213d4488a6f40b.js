"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1094],{13468:(e,t,r)=>{r.d(t,{U:()=>n});var s=r(20067),a=r(49509);async function n(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let n=null,o=null;try{let{headers:e,cookies:t}=await r.e(8974).then(r.bind(r,66593));n=await e(),o=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===a.env.PLAYWRIGHT_TESTING||n&&"true"===n.get("x-playwright-testing"))&&n?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,a=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,a){let n=()=>{var n,o,l,i,c;return n=e,o=t,l=r,i=s,c=a,"customer_profiles"===n?{data:l&&"customer"===o?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:l&&"business"===o?{id:"test-user-id",business_slug:i||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===o?{id:"test-subscription-id",plan_id:c,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===o?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},o=e=>({select:t=>o(e),eq:(t,r)=>o(e),neq:(t,r)=>o(e),gt:(t,r)=>o(e),gte:(t,r)=>o(e),lt:(t,r)=>o(e),lte:(t,r)=>o(e),like:(t,r)=>o(e),ilike:(t,r)=>o(e),is:(t,r)=>o(e),in:(t,r)=>o(e),contains:(t,r)=>o(e),containedBy:(t,r)=>o(e),rangeGt:(t,r)=>o(e),rangeGte:(t,r)=>o(e),rangeLt:(t,r)=>o(e),rangeLte:(t,r)=>o(e),rangeAdjacent:(t,r)=>o(e),overlaps:(t,r)=>o(e),textSearch:(t,r)=>o(e),match:t=>o(e),not:(t,r,s)=>o(e),or:t=>o(e),filter:(t,r,s)=>o(e),order:(t,r)=>o(e),limit:(t,r)=>o(e),range:(t,r,s)=>o(e),abortSignal:t=>o(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>o(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>o(e),upsert:e=>o(e),delete:()=>o(),rpc:(e,t)=>o()}})(e,r,s,a,n)}}(n):o?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await o.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await o.set(t,r,s)}catch(e){}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},16640:(e,t,r)=>{function s(e){if(!e||"string"!=typeof e)throw Error("Invalid userId: expected string, got ".concat(typeof e,". Value: ").concat(e));if(e.length<4)throw Error("Invalid userId: must be at least 4 characters long. Got: ".concat(e));let t=e.substring(0,2).toLowerCase(),r=e.substring(2,4).toLowerCase();return"users/".concat(t,"/").concat(r,"/").concat(e)}function a(e,t,r,a,n){let o=s(e),l=n?new Date(n):new Date,i=l.getFullYear(),c=String(l.getMonth()+1).padStart(2,"0");return"".concat(o,"/posts/").concat(i,"/").concat(c,"/").concat(t,"/image_").concat(r,"_").concat(a,".webp")}function n(e,t,r){let a=s(e),n=new Date(r),o=n.getFullYear(),l=String(n.getMonth()+1).padStart(2,"0");return"".concat(a,"/posts/").concat(o,"/").concat(l,"/").concat(t)}r.d(t,{EK:()=>n,RE:()=>a})},41094:(e,t,r)=>{r.d(t,{deleteCustomerPostMedia:()=>o});var s=r(13468),a=r(16640),n=r(47186);async function o(e,t,r){let o=await (0,s.U)();try{let s=n.SC.CUSTOMERS,l=(0,a.EK)(e,t,r),{data:i,error:c}=await o.storage.from(s).list(l,{limit:1e3,sortBy:{column:"name",order:"asc"}});if(c)return console.error("Error listing customer post folder contents:",c),{success:!1,error:"Failed to list customer post folder: ".concat(c.message)};if(!i||0===i.length)return{success:!0};let u=i.map(e=>"".concat(l,"/").concat(e.name)),{error:d}=await o.storage.from(s).remove(u);if(d)return console.error("Error deleting customer post folder contents:",d),{success:!1,error:"Failed to delete customer post folder: ".concat(d.message)};return{success:!0}}catch(e){return console.error("Error in deleteCustomerPostMedia:",e),{success:!1,error:"An unexpected error occurred while deleting customer post folder."}}}}}]);