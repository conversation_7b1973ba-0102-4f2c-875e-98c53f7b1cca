"use strict";(()=>{var e={};e.id=3365,e.ids=[3365],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,r,s)=>{s.d(r,{CG:()=>t,SC:()=>i,cZ:()=>o});let t={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},i={BUSINESS:"business",CUSTOMERS:"customers"},o={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68054:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>l,routeModule:()=>_,serverHooks:()=>I,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>R});var t={};s.r(t),s.d(t,{GET:()=>S});var i=s(96559),o=s(48088),n=s(37719),a=s(32190),u=s(32032),c=s(8033),p=s(30468);async function S(e,{params:r}){try{let{id:s}=await r,t=e.nextUrl.searchParams,i=parseInt(t.get("page")||"1",10),o=parseInt(t.get("count")||"10",10),n=await (0,u.createClient)(),{data:{user:S},error:_}=await n.auth.getUser();if(_||!S)return a.NextResponse.json({error:"Authentication required"},{status:401});let{data:d,error:R}=await n.from(p.CG.PAYMENT_SUBSCRIPTIONS).select("*").eq(p.cZ.RAZORPAY_SUBSCRIPTION_ID,s).maybeSingle();if(R)return console.error("[RAZORPAY_ERROR] Error fetching subscription:",R),a.NextResponse.json({success:!1,error:"Error fetching subscription"},{status:500});if(d){if(d.business_profile_id!==S.id)return a.NextResponse.json({success:!1,error:"Unauthorized to access this subscription"},{status:403})}else{console.log(`[SUBSCRIPTION_INVOICES] No active subscription found for ID: ${s}, checking user authorization`);let{data:e}=await n.from(p.CG.BUSINESS_PROFILES).select(p.cZ.ID).eq(p.cZ.ID,S.id).single();if(!e)return a.NextResponse.json({success:!1,error:"Unauthorized to access this subscription"},{status:403})}let I=await (0,c.$)(s,i,o);if(!I.success)return console.error("[RAZORPAY_ERROR] Error fetching subscription invoices:",I.error),a.NextResponse.json({success:!1,error:"Failed to fetch subscription invoices"},{status:500});return a.NextResponse.json({success:!0,invoices:I.data,pagination:{page:i,count:o,totalCount:I.data?.count||0,totalPages:Math.ceil((I.data?.count||0)/o)}})}catch(e){return console.error("[RAZORPAY_ERROR] Unexpected error in invoices API:",e),a.NextResponse.json({success:!1,error:e instanceof Error?e.message:"An unexpected error occurred"},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/invoices/route",pathname:"/api/subscriptions/[id]/invoices",filename:"route",bundlePath:"app/api/subscriptions/[id]/invoices/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\invoices\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:R,serverHooks:I}=_;function l(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:R})}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,9398,4386,580,6298],()=>s(68054));module.exports=t})();