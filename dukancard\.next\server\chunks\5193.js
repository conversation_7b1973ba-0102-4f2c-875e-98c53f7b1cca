"use strict";exports.id=5193,exports.ids=[33,5193],exports.modules={10033:(t,e,s)=>{s.d(e,{SubscriptionStateManager:()=>r,TQ:()=>a});var i=s(80223);class r{static shouldHaveActiveSubscription(t,e=i.v.FREE){return e!==i.v.FREE&&t!==i.d.TRIAL&&[i.d.ACTIVE].includes(t)}static isTerminalStatus(t){return[i.d.CANCELLED,i.d.EXPIRED,i.d.COMPLETED].includes(t)}static isTrialStatus(t){return t===i.d.TRIAL}static isFreeStatus(t,e){return e===i.v.FREE||"free"===t}static getAccessLevel(t,e=i.v.FREE){return e===i.v.FREE?"free":t===i.d.TRIAL?"trial":this.shouldHaveActiveSubscription(t,e)?"paid":"free"}static isActivePaidSubscription(t,e=i.v.FREE){return this.shouldHaveActiveSubscription(t,e)}static isValidStatusTransition(t,e){return!this.isTerminalStatus(t)||!!this.isTerminalStatus(e)}}function a(t){return r.isTerminalStatus(t)}},11337:(t,e,s)=>{s.d(e,{SO:()=>r,mz:()=>i});var i=function(t){return t._SUBSCRIPTION_AUTHENTICATED="subscription.authenticated",t._SUBSCRIPTION_ACTIVATED="subscription.activated",t._SUBSCRIPTION_CHARGED="subscription.charged",t._SUBSCRIPTION_PENDING="subscription.pending",t._SUBSCRIPTION_HALTED="subscription.halted",t._SUBSCRIPTION_CANCELLED="subscription.cancelled",t._SUBSCRIPTION_COMPLETED="subscription.completed",t._SUBSCRIPTION_EXPIRED="subscription.expired",t._SUBSCRIPTION_UPDATED="subscription.updated",t._PAYMENT_AUTHORIZED="payment.authorized",t._PAYMENT_CAPTURED="payment.captured",t._PAYMENT_FAILED="payment.failed",t._INVOICE_PAID="invoice.paid",t._REFUND_CREATED="refund.created",t._REFUND_PROCESSED="refund.processed",t._REFUND_FAILED="refund.failed",t}({}),r=function(t){return t._ACTIVE="active",t._PENDING="pending",t._HALTED="halted",t._CANCELLED="cancelled",t._COMPLETED="completed",t._EXPIRED="expired",t._PAYMENT_FAILED="payment_failed",t._AUTHENTICATED="authenticated",t}({})},65193:(t,e,s)=>{s.d(e,{SUBSCRIPTION_STATUS:()=>i.d,SubscriptionStateManager:()=>r.SubscriptionStateManager,extractWebhookTimestamp:()=>n,isTerminalStatus:()=>r.TQ,nV:()=>a.n});var i=s(80223),r=s(10033);s(32032);var a=s(94230);function n(t){try{for(let e of[()=>t.created_at,()=>t.payload?.subscription?.entity?.created_at,()=>t.payload?.subscription?.entity?.updated_at,()=>t.payload?.payment?.entity?.created_at,()=>t.payload?.payment?.entity?.updated_at,()=>t.payload?.invoice?.entity?.created_at,()=>t.payload?.invoice?.entity?.updated_at,()=>t.payload?.entity?.created_at,()=>t.payload?.entity?.updated_at,()=>t.event_timestamp,()=>t.timestamp])try{let t=e();if(t&&"number"==typeof t&&t>0){let e=Math.floor(Date.now()/1e3);if(t>=e-86400&&t<=e+300)return t;console.warn(`[WEBHOOK_TIMESTAMP] Timestamp ${t} outside reasonable range, trying next source`)}}catch(t){continue}return console.warn("[WEBHOOK_TIMESTAMP] Could not extract valid timestamp from payload, using current time"),console.warn("[WEBHOOK_TIMESTAMP] Payload structure:",JSON.stringify(t,null,2)),Math.floor(Date.now()/1e3)}catch(t){return console.error("[WEBHOOK_TIMESTAMP] Error extracting timestamp from payload:",t),Math.floor(Date.now()/1e3)}}},80223:(t,e,s)=>{s.d(e,{d:()=>i,v:()=>r});let i={ACTIVE:"active",AUTHENTICATED:"authenticated",TRIAL:"trial",PENDING:"pending",HALTED:"halted",CANCELLED:"cancelled",EXPIRED:"expired",COMPLETED:"completed",PAYMENT_FAILED:"payment_failed",CANCELLATION_SCHEDULED:"cancellation_scheduled"},r={FREE:"free",BASIC:"basic",GROWTH:"growth",PRO:"pro",ENTERPRISE:"enterprise"}},94230:(t,e,s)=>{s.d(e,{M:()=>c,n:()=>n});var i=s(11337),r=s(32032),a=s(10033);async function n(t,e,i,a={}){try{let t=(0,r.createClient)(),{getSubscription:n}=await Promise.all([s.e(1546),s.e(5453)]).then(s.bind(s,31546)),c=await n(e);if(!c.success||!c.data){console.error(`[RAZORPAY_WEBHOOK] Failed to get subscription details from Razorpay for ${e}`);let s=await t,{data:r,error:n}=await s.from("payment_subscriptions").select("business_profile_id, plan_id, plan_cycle").eq("razorpay_subscription_id",e).maybeSingle();if(n||!r)return console.error(`[RAZORPAY_WEBHOOK] Also failed to get subscription from local database for ${e}:`,n),{success:!1,message:"Failed to get subscription details from both Razorpay and local database"};console.log(`[RAZORPAY_WEBHOOK] Using local subscription data for ${e} since Razorpay API failed`);let c={success:!0,data:{id:e,plan_id:`${r.plan_id}_${r.plan_cycle}`,customer_id:null,current_start:null,current_end:null,charge_at:null,start_at:null,notes:{business_profile_id:r.business_profile_id,plan_type:r.plan_id,plan_cycle:r.plan_cycle}}};return await o(s,e,i,a,c.data)}let u=await t;return await o(u,e,i,a,c.data)}catch(t){return console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${e}:`,t),{success:!1,message:`Exception updating subscription: ${t instanceof Error?t.message:String(t)}`}}}async function o(t,e,r,n,o){try{let u=o.notes?.business_profile_id||o.notes?.user_id;if(!u)return console.error(`[RAZORPAY_WEBHOOK] No business_profile_id found in subscription notes for ${e}`),{success:!1,message:"No business_profile_id found in subscription notes"};let d=o.notes?.plan_type,l=o.notes?.plan_cycle;if(!d||!l){console.log(`[RAZORPAY_WEBHOOK] Plan type or cycle not found in notes, determining from plan_id: ${o.plan_id}`);let{getPlanByRazorpayPlanId:t}=await s.e(9209).then(s.bind(s,79209)),e=t(o.plan_id);e?(d=e.id,l=e.razorpayPlanIds.monthly===o.plan_id?"monthly":"yearly",console.log(`[RAZORPAY_WEBHOOK] Determined plan type: ${d}, cycle: ${l} from plan_id using centralized config`)):(d="basic",l="monthly",console.log(`[RAZORPAY_WEBHOOK] Could not determine plan type and cycle from plan_id: ${o.plan_id}, defaulting to basic monthly`))}let _=a.SubscriptionStateManager.shouldHaveActiveSubscription(r,d||"free"),p={...n};"has_active_subscription"in p&&(console.log(`[RAZORPAY_WEBHOOK] Removing has_active_subscription from additionalData for subscription with status ${r}`),delete p.has_active_subscription);let b=o.current_start?new Date(1e3*o.current_start).toISOString():null,E=o.current_end?new Date(1e3*o.current_end).toISOString():null,O=o.charge_at?new Date(1e3*o.charge_at).toISOString():null;if(r!==i.SO._AUTHENTICATED&&r!==i.SO._ACTIVE)return console.log(`[RAZORPAY_WEBHOOK] Skipping creation/update of subscription record for ${e} with status ${r} - only handling authenticated or active statuses`),{success:!0,message:`Skipped creation/update of subscription record with status ${r}`};if(r===i.SO._AUTHENTICATED&&(console.log("[RAZORPAY_WEBHOOK] Authenticated subscription detected, ensuring dates are set correctly"),o.start_at)){let t=new Date(1e3*o.start_at).toISOString();console.log(`[RAZORPAY_WEBHOOK] Using start_at (${t}) for subscription_start_date`),b=t}let{data:A,error:g}=await t.from("payment_subscriptions").select("id, business_profile_id, razorpay_subscription_id").eq("razorpay_subscription_id",e).maybeSingle();if(g)return console.error(`[RAZORPAY_WEBHOOK] Error finding subscription ${e}:`,g),{success:!1,message:`Error finding subscription: ${g.message}`};if(!A){console.log(`[RAZORPAY_WEBHOOK] No subscription found with ID ${e}, checking for existing subscription for business ${u}`);let{data:s,error:i}=await t.from("payment_subscriptions").select("id, razorpay_subscription_id, subscription_status").eq("business_profile_id",u);if(i)return console.error(`[RAZORPAY_WEBHOOK] Error checking for existing subscriptions for business ${u}:`,i),{success:!1,message:`Error checking for existing subscriptions: ${i.message}`};if(s&&s.length>0){console.log(`[RAZORPAY_WEBHOOK] Found existing subscription for business ${u}, updating instead of creating new one`);let i=s[0],a={razorpay_subscription_id:e,razorpay_customer_id:o.customer_id||null,subscription_status:r,plan_id:d,plan_cycle:l,subscription_start_date:b,subscription_expiry_time:E,subscription_charge_time:O,updated_at:new Date().toISOString(),...p},{error:n}=await t.from("payment_subscriptions").update(a).eq("id",i.id);if(n)return console.error(`[RAZORPAY_WEBHOOK] Error updating existing subscription ${i.id}:`,n),{success:!1,message:`Error updating existing subscription: ${n.message}`};console.log(`[RAZORPAY_WEBHOOK] Updated existing subscription ${i.id} with new Razorpay ID ${e} and status ${r}`);let A=await c({subscription_id:e,business_profile_id:u,subscription_status:r,has_active_subscription:_,additional_data:a});if(!A.success)return console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${e}:`,A.message),{success:!1,message:`Transaction failed: ${A.message}`};return{success:!0,message:`Updated existing subscription with new Razorpay ID and status ${r}`}}console.log(`[RAZORPAY_WEBHOOK] No existing subscription found for business ${u}, creating new one`);let a={business_profile_id:u,razorpay_subscription_id:e,razorpay_customer_id:o.customer_id||null,subscription_status:r,plan_id:d,plan_cycle:l,subscription_start_date:b,subscription_expiry_time:E,subscription_charge_time:O,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),...p},{data:n,error:A}=await t.from("payment_subscriptions").insert(a).select("id").single();if(A)return console.error(`[RAZORPAY_WEBHOOK] Error creating subscription record for ${e}:`,A),{success:!1,message:`Error creating subscription record: ${A.message}`};console.log(`[RAZORPAY_WEBHOOK] Created new subscription record for ${e}`);let g=await c({subscription_id:e,business_profile_id:u,subscription_status:r,has_active_subscription:_,additional_data:a});if(!g.success)return console.error(`[RAZORPAY_WEBHOOK] Transaction failed for new subscription ${e}:`,g.message),{success:!1,message:`Transaction failed: ${g.message}`};return console.log(`[RAZORPAY_WEBHOOK] Created subscription for ${e} with status ${r}`),{success:!0,message:`Created subscription with status ${r}`}}let f={subscription_status:r,subscription_start_date:b,subscription_expiry_time:E,subscription_charge_time:O,updated_at:new Date().toISOString(),...p},m=await c({subscription_id:e,business_profile_id:u,subscription_status:r,has_active_subscription:_,additional_data:f});if(!m.success)return console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${e}:`,m.message),{success:!1,message:`Transaction failed: ${m.message}`};return console.log(`[RAZORPAY_WEBHOOK] Updated subscription ${A.id} with status ${r}`),{success:!0,message:`Updated subscription with status ${r}`}}catch(t){return console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${e}:`,t),{success:!1,message:`Exception updating subscription: ${t instanceof Error?t.message:String(t)}`}}}async function c(t){try{let e=await (0,r.createClient)();console.log(`[ATOMIC_TRANSACTION] Using atomic RPC for subscription ${t.subscription_id}`);let{data:s,error:i}=await e.rpc("update_subscription_atomic",{p_subscription_id:t.subscription_id,p_new_status:t.subscription_status,p_business_profile_id:t.business_profile_id,p_has_active_subscription:t.has_active_subscription,p_additional_data:t.additional_data||{},p_webhook_timestamp:t.additional_data?.last_webhook_timestamp||void 0});if(i)return console.error(`[ATOMIC_TRANSACTION] RPC error for ${t.subscription_id}:`,i),{success:!1,message:`RPC error: ${i.message}`};if(!s?.success)return console.error(`[ATOMIC_TRANSACTION] RPC function returned error for ${t.subscription_id}:`,s),{success:!1,message:s?.error||"Unknown RPC error"};return console.log(`[ATOMIC_TRANSACTION] Successfully updated subscription ${t.subscription_id} atomically`),{success:!0,message:"Atomic transaction completed successfully via RPC"}}catch(t){return console.error("[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:",t),{success:!1,message:`Atomic transaction exception: ${t instanceof Error?t.message:String(t)}`}}}}};