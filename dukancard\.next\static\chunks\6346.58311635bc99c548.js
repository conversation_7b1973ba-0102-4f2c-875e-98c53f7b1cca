"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6346],{13468:(e,t,r)=>{r.d(t,{U:()=>n});var s=r(20067),a=r(49509);async function n(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let n=null,i=null;try{let{headers:e,cookies:t}=await r.e(8974).then(r.bind(r,66593));n=await e(),i=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===a.env.PLAYWRIGHT_TESTING||n&&"true"===n.get("x-playwright-testing"))&&n?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,a=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,a){let n=()=>{var n,i,l,o,u;return n=e,i=t,l=r,o=s,u=a,"customer_profiles"===n?{data:l&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:l&&"business"===i?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===i?{id:"test-subscription-id",plan_id:u,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,r)=>i(e),neq:(t,r)=>i(e),gt:(t,r)=>i(e),gte:(t,r)=>i(e),lt:(t,r)=>i(e),lte:(t,r)=>i(e),like:(t,r)=>i(e),ilike:(t,r)=>i(e),is:(t,r)=>i(e),in:(t,r)=>i(e),contains:(t,r)=>i(e),containedBy:(t,r)=>i(e),rangeGt:(t,r)=>i(e),rangeGte:(t,r)=>i(e),rangeLt:(t,r)=>i(e),rangeLte:(t,r)=>i(e),rangeAdjacent:(t,r)=>i(e),overlaps:(t,r)=>i(e),textSearch:(t,r)=>i(e),match:t=>i(e),not:(t,r,s)=>i(e),or:t=>i(e),filter:(t,r,s)=>i(e),order:(t,r)=>i(e),limit:(t,r)=>i(e),range:(t,r,s)=>i(e),abortSignal:t=>i(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,r,s,a,n)}}(n):i?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await i.set(t,r,s)}catch(e){}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},16640:(e,t,r)=>{function s(e){if(!e||"string"!=typeof e)throw Error("Invalid userId: expected string, got ".concat(typeof e,". Value: ").concat(e));if(e.length<4)throw Error("Invalid userId: must be at least 4 characters long. Got: ".concat(e));let t=e.substring(0,2).toLowerCase(),r=e.substring(2,4).toLowerCase();return"users/".concat(t,"/").concat(r,"/").concat(e)}function a(e,t,r,a,n){let i=s(e),l=n?new Date(n):new Date,o=l.getFullYear(),u=String(l.getMonth()+1).padStart(2,"0");return"".concat(i,"/posts/").concat(o,"/").concat(u,"/").concat(t,"/image_").concat(r,"_").concat(a,".webp")}function n(e,t,r){let a=s(e),n=new Date(r),i=n.getFullYear(),l=String(n.getMonth()+1).padStart(2,"0");return"".concat(a,"/posts/").concat(i,"/").concat(l,"/").concat(t)}r.d(t,{EK:()=>n,RE:()=>a})},86346:(e,t,r)=>{r.d(t,{uploadBusinessPostImage:()=>i});var s=r(13468),a=r(16640),n=r(49641).Buffer;async function i(e,t,r){let i=await (0,s.U)(),{data:{user:l},error:o}=await i.auth.getUser();if(o||!l)return{success:!1,error:"User not authenticated."};let u=l.id,c=e.get("imageFile");if(!c)return{success:!1,error:"No image file provided."};if(c.size>0xf00000){let e=(c.size/1048576).toFixed(1);return{success:!1,error:"File size (".concat(e,"MB) exceeds the 15MB limit.")}}if(!["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(c.type))return{success:!1,error:"Invalid file type. Please select JPG, PNG, WebP, or GIF images."};if(!c.name||""===c.name.trim())return{success:!1,error:"Invalid file name."};let{data:d,error:g}=await i.from("business_profiles").select("id").eq("id",u).single();if(g||!d)return{success:!1,error:"Business profile not found. Please complete your business profile first."};try{let e=Date.now()+Math.floor(1e3*Math.random()),i="business",l=(0,a.RE)(u,t,0,e,r),o=n.from(await c.arrayBuffer()),d=await (0,s.U)(),{error:g}=await d.storage.from(i).upload(l,o,{contentType:c.type,upsert:!0});if(g)return console.error("Business Post Image Upload Error:",g),{success:!1,error:"Failed to upload image: ".concat(g.message)};let{data:p}=d.storage.from(i).getPublicUrl(l);if(!(null==p?void 0:p.publicUrl))return{success:!1,error:"Could not retrieve public URL after upload."};return{success:!0,url:p.publicUrl}}catch(e){return console.error("Error processing business post image:",e),{success:!1,error:"Failed to process image. Please try a different image."}}}}}]);