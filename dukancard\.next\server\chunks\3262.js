"use strict";exports.id=3262,exports.ids=[3262],exports.modules={19164:(e,t,a)=>{a.d(t,{Nh:()=>i,kn:()=>s});var r=a(73511);let i=e=>r.NB.map(t=>(function(e,t){let a="enterprise"===e.id,r=e.pricing.monthly,i=e.pricing.yearly;return{id:e.id,name:`${e.name} Plan`,razorpayPlanIds:{monthly:e.razorpayPlanIds.monthly||null,yearly:e.razorpayPlanIds.yearly||null},price:a?"Contact Sales":"monthly"===t?`₹${e.pricing.monthly.toLocaleString("en-IN")}`:`₹${e.pricing.yearly.toLocaleString("en-IN")}`,yearlyPrice:a?"Contact Sales":`₹${e.pricing.yearly.toLocaleString("en-IN")}`,period:a?"":"monthly"===t?"/month":"/year",savings:a?void 0:"yearly"===t?`Save ₹${(a?0:12*r-i).toLocaleString("en-IN")}`:void 0,description:e.description,features:e.features.map(e=>{if("Product Listings"===e.name&&e.included){let t="unlimited"===e.limit?"Unlimited":e.limit;return`Product/Service Listings (up to ${t})`}return e.included?e.name:`❌ ${e.name}`}),button:a?"Contact Sales":"Subscribe Now",available:!0,featured:"free"===e.id||"basic"===e.id||"growth"===e.id||"pro"===e.id,recommended:e.recommended||!1,mostPopular:e.recommended||!1}})(t,e)),s=i("monthly")},19559:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var r=a(37413);a(61120);var i=a(14890),s=a(60644),o=a(11637),n=a(95006),l=a(92506),d=a(46501),c=a(21886),m=a(23392);function u({children:e}){return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(m.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,r.jsx)(s.default,{}),(0,r.jsx)(n.default,{}),(0,r.jsx)(o.default,{}),(0,r.jsx)(l.default,{}),(0,r.jsx)(d.default,{}),(0,r.jsx)(c.default,{excludePaths:["/dashboard"]})]})})})}},27908:(e,t,a)=>{a.d(t,{A:()=>s});var r=a(60687),i=a(77882);function s({variant:e="gold",className:t=""}){return(0,r.jsxs)("div",{className:`w-full h-12 md:h-16 relative overflow-hidden ${t}`,children:[(0,r.jsx)("div",{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-7xl px-4 mx-auto",children:(0,r.jsx)(i.P.div,{className:"w-full h-px bg-gradient-to-r from-transparent via-neutral-400 to-transparent dark:via-neutral-600",initial:{width:"0%",left:"50%"},whileInView:{width:"100%",left:"0%"},viewport:{once:!0},transition:{duration:1.2}})}),(0,r.jsx)(i.P.div,{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-neutral-400 dark:bg-neutral-600",initial:{scale:0,opacity:0},whileInView:{scale:1,opacity:.7},viewport:{once:!0},transition:{duration:.5,delay:.7}}),(0,r.jsx)(i.P.div,{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full border-2 border-neutral-300 dark:border-neutral-700",initial:{scale:0,opacity:0},whileInView:{scale:1,opacity:.5},viewport:{once:!0},transition:{duration:.5,delay:.8}})]})}},56085:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},64398:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65668:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},77632:(e,t,a)=>{a.d(t,{A:()=>x});var r=a(60687),i=a(43210),s=a(77882),o=a(5336);let n=(0,a(62688).A)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var l=a(65668),d=a(80189),c=a(96241);let m=[{id:"core",name:"Core Features"},{id:"analytics",name:"Analytics & Insights"},{id:"customization",name:"Customization"},{id:"management",name:"Management"},{id:"support",name:"Support"}],u=[{id:"digital-card",name:"Digital Business Card",category:"core",tooltip:"Create a digital business card with your contact information"},{id:"qr-code",name:"QR Code Generation",category:"core",tooltip:"Generate a QR code that links to your digital card"},{id:"social-links",name:"Social Media Links",category:"core",tooltip:"Add links to your social media profiles"},{id:"digital-storefront",name:"Digital Storefront",category:"core",tooltip:"Get a shareable dukancard.in/[your-slug] web presence"},{id:"product-listings",name:"Product Listings",category:"core",tooltip:"Showcase your products or services with details and images"},{id:"customer-subscriptions",name:"Customer Subscriptions",category:"core",tooltip:"Allow customers to subscribe to your business"},{id:"business-subscriptions",name:"Business Subscriptions",category:"core",tooltip:"Subscribe to other businesses and let them subscribe to you"},{id:"ratings-reviews",name:"Ratings & Reviews",category:"core",tooltip:"Collect and display customer reviews"},{id:"like-feature",name:"Like Feature",category:"core",tooltip:"Let customers like your business card"},{id:"basic-analytics",name:"Basic Analytics (Views/Clicks)",category:"analytics",tooltip:"Track views and clicks on your digital card"},{id:"product-views",name:"Product Views (Coming Soon)",category:"analytics",tooltip:"Track views and engagement for individual products"},{id:"advanced-analytics",name:"Advanced Analytics (Coming Soon)",category:"analytics",tooltip:"Comprehensive analytics with custom reports and insights"},{id:"photo-gallery",name:"Photo Gallery",category:"core",tooltip:"Upload and display images in a gallery"},{id:"custom-themes",name:"Custom Theme & Colors",category:"customization",tooltip:"Customize the theme and colors of your digital card"},{id:"custom-branding",name:"Custom Branding",category:"customization",tooltip:"Remove Dukancard branding from your digital card"},{id:"no-ads",name:"No Ads",category:"customization",tooltip:"No advertisements displayed on your digital card"},{id:"order-management",name:"Order Management (Coming Soon)",category:"management",tooltip:"Accept and manage customer orders with delivery tracking"},{id:"custom-domain",name:"Custom Domain (Coming Soon)",category:"management",tooltip:"Use your own domain for your digital business card"},{id:"priority-support",name:"Priority Support",category:"support",tooltip:"Get priority email and chat support"},{id:"api-access",name:"API Access",category:"support",tooltip:"Integrate Dukancard with your existing business systems"}];function x({plans:e,title:t="Feature Comparison",subtitle:a="Compare features across plans to find the perfect fit for your business needs.",className:x=""}){let[p,g]=(0,i.useState)(null),h=p?u.filter(e=>e.category===p):u,y=(e,t)=>["Digital Business Card","QR Code Generation","Social Media Links","Digital Storefront","Customer Subscriptions","Business Subscriptions","Ratings & Reviews","Like Feature"].includes(e)?(0,r.jsx)(o.A,{className:"w-5 h-5 text-green-500 mx-auto"}):"Product Listings"===e?"free"===t.id?"5 Products":"basic"===t.id?"15 Products":"growth"===t.id?"50 Products":"pro"===t.id||"enterprise"===t.id?"Unlimited":(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):"Basic Analytics (Views/Clicks)"===e?"free"===t.id?(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):(0,r.jsx)(o.A,{className:"w-5 h-5 text-green-500 mx-auto"}):"Product Views (Coming Soon)"===e?"free"===t.id||"basic"===t.id?(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):"Coming Soon":"Advanced Analytics (Coming Soon)"===e?"free"===t.id||"basic"===t.id||"growth"===t.id?(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):"Coming Soon":"Photo Gallery"===e?"free"===t.id?"1 Image":"basic"===t.id?"3 Images":"growth"===t.id?"10 Images":"pro"===t.id?"50 Images":"enterprise"===t.id?"100 Images":(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):["Custom Theme & Colors","Custom Branding","No Ads"].includes(e)?"pro"===t.id||"enterprise"===t.id?(0,r.jsx)(o.A,{className:"w-5 h-5 text-green-500 mx-auto"}):(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):"Order Management (Coming Soon)"===e?"free"===t.id||"basic"===t.id?(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):(0,r.jsx)(o.A,{className:"w-5 h-5 text-green-500 mx-auto"}):"Custom Domain (Coming Soon)"===e?"free"===t.id||"basic"===t.id||"growth"===t.id?(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):"Coming Soon":"Priority Support"===e?"pro"===t.id||"enterprise"===t.id?(0,r.jsx)(o.A,{className:"w-5 h-5 text-green-500 mx-auto"}):(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"}):"API Access"===e&&"enterprise"===t.id?"(Potential)":(0,r.jsx)(n,{className:"w-5 h-5 text-neutral-400 mx-auto"});return(0,r.jsxs)("div",{className:(0,c.cn)("w-full",x),children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-4",children:t.split(" ").map((e,a)=>(0,r.jsxs)("span",{children:["Comparison"===e?(0,r.jsxs)("span",{className:"text-[var(--brand-gold)] relative",children:[e,(0,r.jsx)(s.P.div,{className:"absolute -bottom-1 left-0 h-1 bg-[var(--brand-gold)]/30 rounded-full",initial:{width:0},animate:{width:"100%"},transition:{duration:.7,delay:.3}})]}):e,a<t.split(" ").length-1&&" "]},a))}),(0,r.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:a})]}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-2 mb-8",children:[(0,r.jsx)("button",{onClick:()=>g(null),className:(0,c.cn)("px-4 py-2 rounded-full text-sm font-medium transition-colors",null===p?"bg-[var(--brand-gold)] text-black dark:text-neutral-900":"bg-muted text-muted-foreground hover:text-foreground"),children:"All Features"}),m.map(e=>(0,r.jsx)("button",{onClick:()=>g(e.id),className:(0,c.cn)("px-4 py-2 rounded-full text-sm font-medium transition-colors",p===e.id?"bg-[var(--brand-gold)] text-black dark:text-neutral-900":"bg-muted text-muted-foreground hover:text-foreground"),children:e.name},e.id))]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full border-collapse border border-neutral-200 dark:border-neutral-800",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"bg-neutral-100 dark:bg-neutral-800",children:[(0,r.jsx)("th",{className:"p-4 text-left font-semibold text-foreground border-b border-neutral-200 dark:border-neutral-800",children:"Feature"}),e.map(e=>(0,r.jsx)("th",{className:"p-4 text-center font-semibold text-foreground border-b border-neutral-200 dark:border-neutral-800",children:(0,r.jsxs)("div",{className:(0,c.cn)(e.featured?"text-[var(--brand-gold)]":"","flex flex-col items-center"),children:[(0,r.jsx)("span",{children:e.name.replace(" Plan","")}),e.mostPopular&&(0,r.jsx)("span",{className:"text-xs mt-1 bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] px-2 py-0.5 rounded-full",children:"Most Popular"})]})},e.id))]})}),(0,r.jsx)("tbody",{children:h.map(t=>(0,r.jsxs)("tr",{className:"border-b border-neutral-200 dark:border-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-900",children:[(0,r.jsx)("td",{className:"p-4 text-left text-foreground font-medium",children:(0,r.jsxs)("div",{className:"flex items-center",children:[t.name,t.tooltip&&(0,r.jsx)(d.Bc,{children:(0,r.jsxs)(d.m_,{children:[(0,r.jsx)(d.k$,{asChild:!0,children:(0,r.jsx)(l.A,{className:"ml-2 h-4 w-4 text-neutral-400 cursor-help"})}),(0,r.jsx)(d.ZI,{children:(0,r.jsx)("p",{className:"max-w-xs",children:t.tooltip})})]})})]})}),e.map(e=>(0,r.jsx)("td",{className:"p-4 text-center",children:(0,r.jsx)("div",{className:"flex justify-center",children:y(t.name,e)})},e.id))]},t.id))})]})})]})}},85778:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])}};