"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7793],{1184:(e,t,n)=>{n.d(t,{dK:()=>m,wE:()=>h});var r=n(12115),l=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,c=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&c(e,n,t[n]);if(i)for(var n of i(t))s.call(t,n)&&c(e,n,t[n]);return e},p=(e,t)=>a(e,o(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&s.call(e,r)&&(n[r]=e[r]);return n},m=r.createContext({}),h=r.forwardRef((e,t)=>{var n,l,a,o,i,{value:u,onChange:s,maxLength:c,textAlign:h="left",pattern:y,placeholder:b,inputMode:w="numeric",onComplete:E,pushPasswordManagerStrategy:S="increase-width",pasteTransformer:C,containerClassName:P,noScriptCSSFallback:x=g,render:k,children:R}=e,M=f(e,["value","onChange","maxLength","textAlign","pattern","placeholder","inputMode","onComplete","pushPasswordManagerStrategy","pasteTransformer","containerClassName","noScriptCSSFallback","render","children"]);let[j,A]=r.useState("string"==typeof M.defaultValue?M.defaultValue:""),O=null!=u?u:j,D=function(e){let t=r.useRef();return r.useEffect(()=>{t.current=e}),t.current}(O),W=r.useCallback(e=>{null==s||s(e),A(e)},[s]),_=r.useMemo(()=>y?"string"==typeof y?new RegExp(y):y:null,[y]),N=r.useRef(null),B=r.useRef(null),T=r.useRef({value:O,onChange:W,isIOS:"undefined"!=typeof window&&(null==(l=null==(n=null==window?void 0:window.CSS)?void 0:n.supports)?void 0:l.call(n,"-webkit-touch-callout","none"))}),I=r.useRef({prev:[null==(a=N.current)?void 0:a.selectionStart,null==(o=N.current)?void 0:o.selectionEnd,null==(i=N.current)?void 0:i.selectionDirection]});r.useImperativeHandle(t,()=>N.current,[]),r.useEffect(()=>{let e=N.current,t=B.current;if(!e||!t)return;function n(){if(document.activeElement!==e){G(null),U(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,l=e.maxLength,a=e.value,o=I.current.prev,i=-1,u=-1,s;if(0!==a.length&&null!==t&&null!==n){let e=t===n,r=t===a.length&&a.length<l;if(e&&!r){if(0===t)i=0,u=1,s="forward";else if(t===l)i=t-1,u=t,s="backward";else if(l>1&&a.length>1){let e=0;if(null!==o[0]&&null!==o[1]){s=t<o[1]?"backward":"forward";let n=o[0]===o[1]&&o[0]<l;"backward"!==s||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&N.current.setSelectionRange(i,u,s)}let c=-1!==i?i:t,d=-1!==u?u:n,p=null!=s?s:r;G(c),U(d),I.current.prev=[c,d,p]}if(T.current.value!==e.value&&T.current.onChange(e.value),I.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&L(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";v(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),v(e.sheet,`[data-input-otp]:autofill { ${t} }`),v(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),v(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),v(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let l=new ResizeObserver(r);return l.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),l.disconnect()}},[]);let[$,V]=r.useState(!1),[F,L]=r.useState(!1),[H,G]=r.useState(null),[z,U]=r.useState(null);r.useEffect(()=>{!function(e){setTimeout(e,0),setTimeout(e,10),setTimeout(e,50)}(()=>{var e,t,n,r;null==(e=N.current)||e.dispatchEvent(new Event("input"));let l=null==(t=N.current)?void 0:t.selectionStart,a=null==(n=N.current)?void 0:n.selectionEnd,o=null==(r=N.current)?void 0:r.selectionDirection;null!==l&&null!==a&&(G(l),U(a),I.current.prev=[l,a,o])})},[O,F]),r.useEffect(()=>{void 0!==D&&O!==D&&D.length<c&&O.length===c&&(null==E||E(O))},[c,E,D,O]);let Z=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:l}){let[a,o]=r.useState(!1),[i,u]=r.useState(!1),[s,c]=r.useState(!1),d=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&a&&i,[a,i,n]),p=r.useCallback(()=>{let r=e.current,l=t.current;if(!r||!l||s||"none"===n)return;let a=r.getBoundingClientRect().left+r.offsetWidth,i=r.getBoundingClientRect().top+r.offsetHeight/2;0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(a-18,i)===r||(o(!0),c(!0))},[e,t,s,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){u(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let l=setInterval(r,1e3);return()=>{clearInterval(l)}},[e,n]),r.useEffect(()=>{let e=l||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(p,0),a=setTimeout(p,2e3),o=setTimeout(p,5e3),i=setTimeout(()=>{c(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(a),clearTimeout(o),clearTimeout(i)}},[t,l,n,p]),{hasPWMBadge:a,willPushPWMBadge:d,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:B,inputRef:N,pushPasswordManagerStrategy:S,isFocused:F}),q=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,c);if(t.length>0&&_&&!_.test(t))return void e.preventDefault();"string"==typeof D&&t.length<D.length&&document.dispatchEvent(new Event("selectionchange")),W(t)},[c,W,D,_]),K=r.useCallback(()=>{var e;if(N.current){let t=Math.min(N.current.value.length,c-1),n=N.current.value.length;null==(e=N.current)||e.setSelectionRange(t,n),G(t),U(n)}L(!0)},[c]),X=r.useCallback(e=>{var t,n;let r=N.current;if(!C&&(!T.current.isIOS||!e.clipboardData||!r))return;let l=e.clipboardData.getData("text/plain"),a=C?C(l):l;e.preventDefault();let o=null==(t=N.current)?void 0:t.selectionStart,i=null==(n=N.current)?void 0:n.selectionEnd,u=(o!==i?O.slice(0,o)+a+O.slice(i):O.slice(0,o)+a+O.slice(o)).slice(0,c);if(u.length>0&&_&&!_.test(u))return;r.value=u,W(u);let s=Math.min(u.length,c-1),d=u.length;r.setSelectionRange(s,d),G(s),U(d)},[c,W,_,O]),J=r.useMemo(()=>({position:"relative",cursor:M.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[M.disabled]),Q=r.useMemo(()=>({position:"absolute",inset:0,width:Z.willPushPWMBadge?`calc(100% + ${Z.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:Z.willPushPWMBadge?`inset(0 ${Z.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:h,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[Z.PWM_BADGE_SPACE_WIDTH,Z.willPushPWMBadge,h]),Y=r.useMemo(()=>r.createElement("input",p(d({autoComplete:M.autoComplete||"one-time-code"},M),{"data-input-otp":!0,"data-input-otp-placeholder-shown":0===O.length||void 0,"data-input-otp-mss":H,"data-input-otp-mse":z,inputMode:w,pattern:null==_?void 0:_.source,"aria-placeholder":b,style:Q,maxLength:c,value:O,ref:N,onPaste:e=>{var t;X(e),null==(t=M.onPaste)||t.call(M,e)},onChange:q,onMouseOver:e=>{var t;V(!0),null==(t=M.onMouseOver)||t.call(M,e)},onMouseLeave:e=>{var t;V(!1),null==(t=M.onMouseLeave)||t.call(M,e)},onFocus:e=>{var t;K(),null==(t=M.onFocus)||t.call(M,e)},onBlur:e=>{var t;L(!1),null==(t=M.onBlur)||t.call(M,e)}})),[q,K,X,w,Q,c,z,H,M,null==_?void 0:_.source,O]),ee=r.useMemo(()=>({slots:Array.from({length:c}).map((e,t)=>{var n;let r=F&&null!==H&&null!==z&&(H===z&&t===H||t>=H&&t<z),l=void 0!==O[t]?O[t]:null;return{char:l,placeholderChar:void 0!==O[0]?null:null!=(n=null==b?void 0:b[t])?n:null,isActive:r,hasFakeCaret:r&&null===l}}),isFocused:F,isHovering:!M.disabled&&$}),[F,$,c,z,H,M.disabled,O]),et=r.useMemo(()=>k?k(ee):r.createElement(m.Provider,{value:ee},R),[R,ee,k]);return r.createElement(r.Fragment,null,null!==x&&r.createElement("noscript",null,r.createElement("style",null,x)),r.createElement("div",{ref:B,"data-input-otp-container":!0,style:J,className:P},et,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},Y)))});function v(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}h.displayName="Input";var g=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`},6101:(e,t,n)=>{n.d(t,{s:()=>o,t:()=>a});var r=n(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function o(...e){return r.useCallback(a(...e),e)}},19946:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:d,...p}=e;return(0,r.createElement)("svg",{ref:t,...o,width:l,height:l,stroke:n,strokeWidth:u?24*Number(i)/Number(l):i,className:a("lucide",s),...p},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:u,...s}=n;return(0,r.createElement)(i,{ref:o,iconNode:t,className:a("lucide-".concat(l(e)),u),...s})});return n.displayName="".concat(e),n}},35695:(e,t,n)=>{var r=n(18999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},40968:(e,t,n)=>{n.d(t,{b:()=>i});var r=n(12115),l=n(63540),a=n(95155),o=r.forwardRef((e,t)=>(0,a.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},46896:(e,t,n)=>{n.d(t,{s:()=>c});var r=n(54542),l=n(18802),a=n(85982);function o(e,t){[...t].reverse().forEach(n=>{let r=e.getVariant(n);r&&(0,l.U)(e,r),e.variantChildren&&e.variantChildren.forEach(e=>{o(e,t)})})}function i(){let e=!1,t=new Set,n={subscribe:e=>(t.add(e),()=>void t.delete(e)),start(n,l){(0,r.V)(e,"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.");let o=[];return t.forEach(e=>{o.push((0,a._)(e,n,{transitionOverride:l}))}),Promise.all(o)},set:n=>((0,r.V)(e,"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook."),t.forEach(e=>{var t,r;t=e,Array.isArray(r=n)?o(t,r):"string"==typeof r?o(t,[r]):(0,l.U)(t,r)})),stop(){t.forEach(e=>{e.values.forEach(e=>e.stop())})},mount:()=>(e=!0,()=>{e=!1,n.stop()})};return n}var u=n(82885),s=n(97494);let c=function(){let e=(0,u.M)(i);return(0,s.E)(e.mount,[]),e}},63540:(e,t,n)=>{n.d(t,{sG:()=>s,hO:()=>c});var r=n(12115),l=n(47650),a=n(6101),o=n(95155),i=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...l}=e;if(r.isValidElement(n)){var o;let e,i,u=(o=n,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let n={...t};for(let r in t){let l=e[r],a=t[r];/^on[A-Z]/.test(r)?l&&a?n[r]=(...e)=>{a(...e),l(...e)}:l&&(n[r]=l):"style"===r?n[r]={...l,...a}:"className"===r&&(n[r]=[l,a].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,a.t)(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...a}=e,i=r.Children.toArray(l),s=i.find(u);if(s){let e=s.props.children,l=i.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...a,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:l,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?n:t,{...a,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function c(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},74466:(e,t,n)=>{n.d(t,{F:()=>o});var r=n(52596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,o=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:i}=t,u=Object.keys(o).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let a=l(t)||l(r);return o[e][a]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return a(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...s}[t]):({...i,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},92138:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},99708:(e,t,n)=>{n.d(t,{DX:()=>o});var r=n(12115),l=n(6101),a=n(95155),o=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...a}=e;if(r.isValidElement(n)){var o;let e,i,u=(o=n,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let n={...t};for(let r in t){let l=e[r],a=t[r];/^on[A-Z]/.test(r)?l&&a?n[r]=(...e)=>{let t=a(...e);return l(...e),t}:l&&(n[r]=l):"style"===r?n[r]={...l,...a}:"className"===r&&(n[r]=[l,a].filter(Boolean).join(" "))}return{...e,...n}}(a,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,l.t)(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...o}=e,i=r.Children.toArray(l),s=i.find(u);if(s){let e=s.props.children,l=i.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,a.jsx)(t,{...o,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}("Slot"),i=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}}]);