"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4092],{1117:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(95155),s=r(12115),n=r(52479),i=r(26661),l=r(37777);let o=e=>{let{endDate:t,label:r="Trial ends in:",tooltipText:o="Your trial will expire soon. Upgrade to continue using all features.",className:d}=e,[u,c]=(0,s.useState)(new Date),[b,g]=(0,s.useState)([]),m=(0,s.useMemo)(()=>{let e;return isNaN((e="string"==typeof t?new Date(t):t).getTime())?null:e},[t]);if((0,s.useEffect)(()=>{if(!m)return;let e=()=>{let e=new Date;if(c(e),m&&e<m){let t=(0,n.F)({start:e,end:m}),r=[],a=m.getTime()-e.getTime(),s=Math.floor(a/315576e5),i=a%315576e5,l=Math.floor(i/2630016e3),o=Math.floor((i%=2630016e3)/864e5);i%=864e5;let d=t.hours||0,u=t.minutes||0,c=t.seconds||0,b=[];s>0&&b.push({label:"years",value:s}),l>0&&b.push({label:"months",value:l}),o>0&&b.push({label:"days",value:o}),b.push({label:"hrs",value:d},{label:"min",value:u},{label:"sec",value:c});let p=b.pop();r.push(...b.slice(0,5)),p&&r.push(p),g(r)}};e();let t=setInterval(e,1e3);return()=>clearInterval(t)},[m]),!m||u>=m)return null;let p=b.some(e=>"months"===e.label||"years"===e.label)?{gradient:"from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-900/20",border:"border-blue-200 dark:border-blue-800/50",text:"text-blue-600 dark:text-blue-400",shadow:"shadow-blue-200/20 dark:shadow-blue-900/20",glow:"after:bg-blue-500/10 dark:after:bg-blue-400/10"}:b.some(e=>"days"===e.label)?{gradient:"from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/5 dark:from-[var(--brand-gold)]/30 dark:to-[var(--brand-gold)]/10",border:"border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/20",text:"text-[var(--brand-gold)] dark:text-[var(--brand-gold)]",shadow:"shadow-[var(--brand-gold)]/10 dark:shadow-[var(--brand-gold)]/10",glow:"after:bg-[var(--brand-gold)]/10 dark:after:bg-[var(--brand-gold)]/10"}:{gradient:"from-amber-100 to-amber-50 dark:from-amber-900/40 dark:to-amber-900/20",border:"border-amber-200 dark:border-amber-800/50",text:"text-amber-600 dark:text-amber-400",shadow:"shadow-amber-200/20 dark:shadow-amber-900/20",glow:"after:bg-amber-500/10 dark:after:bg-amber-400/10"};return(0,a.jsxs)("div",{className:"flex flex-col items-center w-full ".concat(d||""),children:[(0,a.jsx)(l.Bc,{delayDuration:100,children:(0,a.jsxs)(l.m_,{children:[(0,a.jsx)(l.k$,{asChild:!0,children:(0,a.jsxs)("div",{className:"flex items-center gap-1.5 mb-1.5",children:[(0,a.jsx)("div",{className:"transition-transform hover:scale-110",children:(0,a.jsx)(i.A,{className:"w-4 h-4 ".concat(p.text)})}),(0,a.jsx)("span",{className:"text-xs font-medium text-muted-foreground",children:r})]})}),(0,a.jsx)(l.ZI,{side:"bottom",className:"bg-neutral-800/95 dark:bg-neutral-950/95 backdrop-blur-sm border border-neutral-700/50 dark:border-[var(--brand-gold)]/20 text-white text-xs p-3 rounded-lg shadow-lg",children:(0,a.jsx)("p",{children:o})})]})}),(0,a.jsx)("div",{className:"flex gap-1 xs:gap-1.5 sm:gap-2 flex-wrap justify-center",children:b.map((e,t)=>(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"relative w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 md:w-11 md:h-11 overflow-hidden rounded-md ".concat(p.shadow," shadow-md after:absolute after:inset-0 after:rounded-md after:opacity-30 after:blur-xl ").concat(p.glow),children:[(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0 h-[1px] bg-white/40 dark:bg-white/10 z-10"}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-[1px] bg-black/10 dark:bg-black/20 z-10"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-0 right-0 h-[1px] bg-black/10 dark:bg-white/10 z-10"}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gradient-to-b ".concat(p.gradient," rounded-md border ").concat(p.border),children:(0,a.jsx)("span",{className:"text-xs xs:text-sm sm:text-base md:text-lg font-mono font-bold text-neutral-800 dark:text-neutral-200",children:e.value.toString().padStart(2,"0")})},"".concat(e.label,"-").concat(e.value))]}),(0,a.jsx)("span",{className:"text-[7px] xs:text-[8px] sm:text-[9px] font-medium text-muted-foreground mt-0.5",children:e.label})]},t))})]})}},13468:(e,t,r)=>{r.d(t,{U:()=>n});var a=r(20067),s=r(49509);async function n(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let n=null,i=null;try{let{headers:e,cookies:t}=await r.e(8974).then(r.bind(r,66593));n=await e(),i=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===s.env.PLAYWRIGHT_TESTING||n&&"true"===n.get("x-playwright-testing"))&&n?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),a="customer"===r||"business"===r,s=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,a,s){let n=()=>{var n,i,l,o,d;return n=e,i=t,l=r,o=a,d=s,"customer_profiles"===n?{data:l&&"customer"===i?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:l&&"business"===i?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===i?{id:"test-subscription-id",plan_id:d,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===i?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},i=e=>({select:t=>i(e),eq:(t,r)=>i(e),neq:(t,r)=>i(e),gt:(t,r)=>i(e),gte:(t,r)=>i(e),lt:(t,r)=>i(e),lte:(t,r)=>i(e),like:(t,r)=>i(e),ilike:(t,r)=>i(e),is:(t,r)=>i(e),in:(t,r)=>i(e),contains:(t,r)=>i(e),containedBy:(t,r)=>i(e),rangeGt:(t,r)=>i(e),rangeGte:(t,r)=>i(e),rangeLt:(t,r)=>i(e),rangeLte:(t,r)=>i(e),rangeAdjacent:(t,r)=>i(e),overlaps:(t,r)=>i(e),textSearch:(t,r)=>i(e),match:t=>i(e),not:(t,r,a)=>i(e),or:t=>i(e),filter:(t,r,a)=>i(e),order:(t,r)=>i(e),limit:(t,r)=>i(e),range:(t,r,a)=>i(e),abortSignal:t=>i(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>i(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>i(e),upsert:e=>i(e),delete:()=>i(),rpc:(e,t)=>i()}})(e,r,a,s,n)}}(n):i?(0,a.createServerClient)(e,t,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:t,value:r,options:a}of e)await i.set(t,r,a)}catch(e){}}}}):(0,a.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},30529:(e,t,r)=>{r.d(t,{dH:()=>a,SubscriptionStateManager:()=>n});let a={ACTIVE:"active",AUTHENTICATED:"authenticated",TRIAL:"trial",PENDING:"pending",HALTED:"halted",CANCELLED:"cancelled",EXPIRED:"expired",COMPLETED:"completed",PAYMENT_FAILED:"payment_failed",CANCELLATION_SCHEDULED:"cancellation_scheduled"},s={FREE:"free"};class n{static shouldHaveActiveSubscription(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.FREE;return t!==s.FREE&&e!==a.TRIAL&&[a.ACTIVE].includes(e)}static isTerminalStatus(e){return[a.CANCELLED,a.EXPIRED,a.COMPLETED].includes(e)}static isTrialStatus(e){return e===a.TRIAL}static isFreeStatus(e,t){return t===s.FREE||"free"===e}static getAccessLevel(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.FREE;return t===s.FREE?"free":e===a.TRIAL?"trial":this.shouldHaveActiveSubscription(e,t)?"paid":"free"}static isActivePaidSubscription(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.FREE;return this.shouldHaveActiveSubscription(e,t)}static isValidStatusTransition(e,t){return!this.isTerminalStatus(e)||!!this.isTerminalStatus(t)}}r(13468);async function i(e){try{var t;let r=await createClient();console.log("[ATOMIC_TRANSACTION] Using atomic RPC for subscription ".concat(e.subscription_id));let{data:a,error:s}=await r.rpc("update_subscription_atomic",{p_subscription_id:e.subscription_id,p_new_status:e.subscription_status,p_business_profile_id:e.business_profile_id,p_has_active_subscription:e.has_active_subscription,p_additional_data:e.additional_data||{},p_webhook_timestamp:(null==(t=e.additional_data)?void 0:t.last_webhook_timestamp)||void 0});if(s)return console.error("[ATOMIC_TRANSACTION] RPC error for ".concat(e.subscription_id,":"),s),{success:!1,message:"RPC error: ".concat(s.message)};if(!(null==a?void 0:a.success))return console.error("[ATOMIC_TRANSACTION] RPC function returned error for ".concat(e.subscription_id,":"),a),{success:!1,message:(null==a?void 0:a.error)||"Unknown RPC error"};return console.log("[ATOMIC_TRANSACTION] Successfully updated subscription ".concat(e.subscription_id," atomically")),{success:!0,message:"Atomic transaction completed successfully via RPC"}}catch(e){return console.error("[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:",e),{success:!1,message:"Atomic transaction exception: ".concat(e instanceof Error?e.message:String(e))}}}},37777:(e,t,r)=>{r.d(t,{Bc:()=>i,ZI:()=>d,k$:()=>o,m_:()=>l});var a=r(95155);r(12115);var s=r(78082),n=r(53999);function i(e){let{delayDuration:t=0,...r}=e;return(0,a.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...r})}function l(e){let{...t}=e;return(0,a.jsx)(i,{children:(0,a.jsx)(s.bL,{"data-slot":"tooltip",...t})})}function o(e){let{...t}=e;return(0,a.jsx)(s.l9,{"data-slot":"tooltip-trigger",...t})}function d(e){let{className:t,sideOffset:r=0,children:i,...l}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:r,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...l,children:[i,(0,a.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},49026:(e,t,r)=>{r.d(t,{Fc:()=>l,TN:()=>d,XL:()=>o});var a=r(95155);r(12115);var s=r(74466),n=r(53999);let i=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),t),...s})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},53999:(e,t,r)=>{r.d(t,{M0:()=>u,Yq:()=>c,cn:()=>n,gV:()=>i,gY:()=>d,kY:()=>l,vA:()=>o,vv:()=>b});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function i(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function l(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),r=e.substring(e.length-2),a="*".repeat(e.length-4);return"".concat(t).concat(a).concat(r)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),r=t[0],a=t[1];if(r.length<=2||a.length<=2||!a.includes("."))return"Email Hidden";let s=r.substring(0,2)+"*".repeat(r.length-2),n=a.split("."),i=n[0],l=n.slice(1).join("."),o=i.substring(0,2)+"*".repeat(i.length-2);return"".concat(s,"@").concat(o,".").concat(l)}function d(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),r=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let a=r.length-1;a>=0;a--)if(t>=r[a].value)return(e/r[a].value).toFixed(1).replace(/\.0$/,"")+r[a].symbol;return e.toString()}function u(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let r={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(r.hour="2-digit",r.minute="2-digit",r.hour12=!0),e.toLocaleString("en-IN",r)}function b(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(r){return"".concat(t," ").concat(e.toFixed(2))}}},75168:(e,t,r)=>{r.d(t,{U:()=>s});var a=r(20067);function s(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";return e&&t?(0,a.createBrowserClient)(e,t):(console.error("Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined."),(0,a.createBrowserClient)("",""))}},88145:(e,t,r)=>{r.d(t,{E:()=>o});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(53999);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:n=!1,...o}=e,d=n?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(l({variant:r}),t),...o})}},97168:(e,t,r)=>{r.d(t,{$:()=>o,r:()=>l});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(53999);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:n,asChild:o=!1,...d}=e,u=o?s.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:n,className:t})),...d})}}}]);