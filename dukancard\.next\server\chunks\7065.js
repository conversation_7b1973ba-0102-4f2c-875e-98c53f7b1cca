"use strict";exports.id=7065,exports.ids=[7065],exports.modules={41312:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},58730:(e,t,r)=>{r.d(t,{i3:()=>K,UC:()=>Z,ZL:()=>Y,Kq:()=>G,bL:()=>W,l9:()=>X});var n=r(43210),o=r(70569),i=r(98599),l=r(11273),a=r(31355),s=r(19344),u=r(55509),c=r(25028),d=r(46059),p=r(3416),f=r(60687),x=Symbol("radix.slottable"),h=r(65551),y=r(69024),[g,v]=(0,l.A)("Tooltip",[u.Bk]),m=(0,u.Bk)(),b="TooltipProvider",w="tooltip.open",[C,T]=g(b),k=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,f.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};k.displayName=b;var E="Tooltip",[j,L]=g(E),R=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=T(E,e.__scopeTooltip),p=m(t),[x,y]=n.useState(null),g=(0,s.B)(),v=n.useRef(0),b=a??d.disableHoverableContent,C=c??d.delayDuration,k=n.useRef(!1),[L,R]=(0,h.i)({prop:o,defaultProp:i??!1,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),l?.(e)},caller:E}),M=n.useMemo(()=>L?k.current?"delayed-open":"instant-open":"closed",[L]),P=n.useCallback(()=>{window.clearTimeout(v.current),v.current=0,k.current=!1,R(!0)},[R]),_=n.useCallback(()=>{window.clearTimeout(v.current),v.current=0,R(!1)},[R]),O=n.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{k.current=!0,R(!0),v.current=0},C)},[C,R]);return n.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),(0,f.jsx)(u.bL,{...p,children:(0,f.jsx)(j,{scope:t,contentId:g,open:L,stateAttribute:M,trigger:x,onTriggerChange:y,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?O():P()},[d.isOpenDelayedRef,O,P]),onTriggerLeave:n.useCallback(()=>{b?_():(window.clearTimeout(v.current),v.current=0)},[_,b]),onOpen:P,onClose:_,disableHoverableContent:b,children:r})})};R.displayName=E;var M="TooltipTrigger",P=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=L(M,r),s=T(M,r),c=m(r),d=n.useRef(null),x=(0,i.s)(t,d,a.onTriggerChange),h=n.useRef(!1),y=n.useRef(!1),g=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,f.jsx)(u.Mz,{asChild:!0,...c,children:(0,f.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:x,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});P.displayName=M;var _="TooltipPortal",[O,D]=g(_,{forceMount:void 0}),I=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,i=L(_,t);return(0,f.jsx)(O,{scope:t,forceMount:r,children:(0,f.jsx)(d.C,{present:r||i.open,children:(0,f.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};I.displayName=_;var N="TooltipContent",B=n.forwardRef((e,t)=>{let r=D(N,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=L(N,e.__scopeTooltip);return(0,f.jsx)(d.C,{present:n||l.open,children:l.disableHoverableContent?(0,f.jsx)(q,{side:o,...i,ref:t}):(0,f.jsx)(A,{side:o,...i,ref:t})})}),A=n.forwardRef((e,t)=>{let r=L(N,e.__scopeTooltip),o=T(N,e.__scopeTooltip),l=n.useRef(null),a=(0,i.s)(t,l),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=l.current,{onPointerInTransitChange:x}=o,h=n.useCallback(()=>{u(null),x(!1)},[x]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),x(!0)},[x]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(c&&p){let e=e=>y(e,p),t=e=>y(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,y,h]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||p?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,h]),(0,f.jsx)(q,{...e,ref:a})}),[H,S]=g(E,{isInside:!1}),F=function(e){let t=({children:e})=>(0,f.jsx)(f.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=x,t}("TooltipContent"),q=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,d=L(N,r),p=m(r),{onClose:x}=d;return n.useEffect(()=>(document.addEventListener(w,x),()=>document.removeEventListener(w,x)),[x]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&x()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,x]),(0,f.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:x,children:(0,f.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,f.jsx)(F,{children:o}),(0,f.jsx)(H,{scope:r,isInside:!0,children:(0,f.jsx)(y.bL,{id:d.contentId,role:"tooltip",children:i||o})})]})})});B.displayName=N;var U="TooltipArrow",z=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=m(r);return S(U,r).isInside?null:(0,f.jsx)(u.i3,{...o,...n,ref:t})});z.displayName=U;var G=k,W=R,X=P,Y=I,Z=B,K=z},69024:(e,t,r)=>{r.d(t,{Qg:()=>l,bL:()=>s});var n=r(43210),o=r(3416),i=r(60687),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var s=a}};