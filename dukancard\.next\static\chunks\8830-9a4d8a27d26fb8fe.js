"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8830],{8830:(t,e,n)=>{n.d(e,{m:()=>h});var r=n(92084),a=n(35339),u=n(41876),s=n(51727),o=n(25900),i=n(35476),l=n(53072),f=n(36199),c=n(43461);function h(t,e){return function(t,e,n){var r,h;let d,m,M,D=(0,f.q)(),g=null!=(h=null!=(r=null==n?void 0:n.locale)?r:D.locale)?h:l.c,v=(0,a.z)(t,e);if(isNaN(v))throw RangeError("Invalid time value");let b=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:v});v>0?(d=(0,i.a)(e),m=(0,i.a)(t)):(d=(0,i.a)(t),m=(0,i.a)(e));let X=(0,o.O)(m,d),w=Math.round((X-((0,c.G)(m)-(0,c.G)(d))/1e3)/60);if(w<2)if(null==n?void 0:n.includeSeconds)if(X<5)return g.formatDistance("lessThanXSeconds",5,b);else if(X<10)return g.formatDistance("lessThanXSeconds",10,b);else if(X<20)return g.formatDistance("lessThanXSeconds",20,b);else if(X<40)return g.formatDistance("halfAMinute",0,b);else if(X<60)return g.formatDistance("lessThanXMinutes",1,b);else return g.formatDistance("xMinutes",1,b);else if(0===w)return g.formatDistance("lessThanXMinutes",1,b);else return g.formatDistance("xMinutes",w,b);if(w<45)return g.formatDistance("xMinutes",w,b);if(w<90)return g.formatDistance("aboutXHours",1,b);if(w<u.F6){let t=Math.round(w/60);return g.formatDistance("aboutXHours",t,b)}if(w<2520)return g.formatDistance("xDays",1,b);else if(w<u.Nw){let t=Math.round(w/u.F6);return g.formatDistance("xDays",t,b)}else if(w<2*u.Nw)return M=Math.round(w/u.Nw),g.formatDistance("aboutXMonths",M,b);if((M=(0,s.W)(m,d))<12){let t=Math.round(w/u.Nw);return g.formatDistance("xMonths",t,b)}{let t=M%12,e=Math.trunc(M/12);return t<3?g.formatDistance("aboutXYears",e,b):t<9?g.formatDistance("overXYears",e,b):g.formatDistance("almostXYears",e+1,b)}}(t,(0,r.w)(t,Date.now()),e)}},24937:(t,e,n)=>{n.d(e,{u:()=>r});function r(t){return e=>{let n=(t?Math[t]:Math.trunc)(e);return 0===n?0:n}}},25900:(t,e,n)=>{n.d(e,{O:()=>u});var r=n(24937),a=n(50657);function u(t,e,n){let u=(0,a.b)(t,e)/1e3;return(0,r.u)(null==n?void 0:n.roundingMethod)(u)}},35339:(t,e,n)=>{n.d(e,{z:()=>a});var r=n(35476);function a(t,e){let n=(0,r.a)(t),a=(0,r.a)(e),u=n.getTime()-a.getTime();return u<0?-1:u>0?1:u}},50657:(t,e,n)=>{n.d(e,{b:()=>a});var r=n(35476);function a(t,e){return(0,r.a)(t)-(0,r.a)(e)}},51727:(t,e,n)=>{n.d(e,{W:()=>u});var r=n(35339),a=n(35476);function u(t,e){let n,u=(0,a.a)(t),s=(0,a.a)(e),o=(0,r.z)(u,s),i=Math.abs(function(t,e){let n=(0,a.a)(t),r=(0,a.a)(e);return 12*(n.getFullYear()-r.getFullYear())+(n.getMonth()-r.getMonth())}(u,s));if(i<1)n=0;else{1===u.getMonth()&&u.getDate()>27&&u.setDate(30),u.setMonth(u.getMonth()-o*i);let e=(0,r.z)(u,s)===-o;(function(t){let e=(0,a.a)(t);return+function(t){let e=(0,a.a)(t);return e.setHours(23,59,59,999),e}(e)==+function(t){let e=(0,a.a)(t),n=e.getMonth();return e.setFullYear(e.getFullYear(),n+1,0),e.setHours(23,59,59,999),e}(e)})((0,a.a)(t))&&1===i&&1===(0,r.z)(t,s)&&(e=!1),n=o*(i-Number(e))}return 0===n?0:n}}}]);