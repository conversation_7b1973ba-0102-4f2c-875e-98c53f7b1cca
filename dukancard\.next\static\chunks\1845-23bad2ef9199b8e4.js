"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196,1845],{381:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2219:(e,a,t)=>{t.d(a,{A:()=>n});var r=t(95155),s=t(12115),l=t(54073),i=t(53999);function n(e){let{className:a,defaultValue:t,value:n,min:d=0,max:o=100,...c}=e,u=s.useMemo(()=>Array.isArray(n)?n:Array.isArray(t)?t:[d,o],[n,t,d,o]);return(0,r.jsxs)(l.bL,{"data-slot":"slider",defaultValue:t,value:n,min:d,max:o,className:(0,i.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",a),...c,children:[(0,r.jsx)(l.CC,{"data-slot":"slider-track",className:(0,i.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,r.jsx)(l.Q6,{"data-slot":"slider-range",className:(0,i.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:u.length},(e,a)=>(0,r.jsx)(l.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},a))]})}},2564:(e,a,t)=>{t.d(a,{Qg:()=>i,bL:()=>d});var r=t(12115),s=t(63540),l=t(95155),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),n=r.forwardRef((e,a)=>(0,l.jsx)(s.sG.span,{...e,ref:a,style:{...i,...e.style}}));n.displayName="VisuallyHidden";var d=n},5623:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},8174:(e,a,t)=>{t.d(a,{A:()=>Q});var r=t(95155),s=t(62177),l=t(90221),i=t(55594),n=t(28695),d=t(37108),o=t(51154),c=t(97168),u=t(30070),m=t(89852),h=t(99474),p=t(90088),x=t(95784),g=t(12115),v=t(56671),b=t(90196);function f(){let{initialImageUrls:e=null,initialFeaturedIndex:a=0,maxImages:t=5}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[r,s]=(0,g.useState)("idle"),[l,i]=(0,g.useState)(null),[n,d]=(0,g.useState)([]),o=(null==e?void 0:e.map((e,a)=>({id:"existing-".concat(a),file:null,previewUrl:e,isUploading:!1,isNew:!1,originalIndex:a})))||[{id:"featured-".concat(Date.now()),file:null,previewUrl:"",isUploading:!1,isNew:!0}],[c,u]=(0,g.useState)(o),[m,h]=(0,g.useState)(a<o.length?a:0),[p,x]=(0,g.useState)(null),f=async e=>{if(!p)return void x(null);let{targetIndex:a,originalFile:t}=p;if(x(null),e)try{let r=(null==t?void 0:t.name)||"product-image-".concat(Date.now(),".jpg"),l=new File([e],r,{type:"image/png",lastModified:Date.now()}),i=await (0,b.compressImageUltraAggressiveClient)(l,{maxDimension:800,targetSizeKB:100}),n=new File([i.blob],r,{type:i.blob.type}),d=URL.createObjectURL(n);u(e=>{var t;let r=[...e];return(null==(t=r[a])?void 0:t.previewUrl)&&r[a].isNew&&URL.revokeObjectURL(r[a].previewUrl),r[a]={...r[a],file:n,previewUrl:d,isNew:!0},r}),s("success")}catch(e){console.error("Error processing cropped image:",e),v.oR.error("Failed to process cropped image"),s("error"),i("Failed to process cropped image")}};return{images:c,featuredImageIndex:m,removedOriginalIndices:n,imageToCrop:p,imageUploadStatus:r,imageUploadError:l,imageErrorDisplay:"error"===r&&l?l:null,addImageSlot:()=>{if(c.length>=t)return void v.oR.error("Maximum of ".concat(t," images allowed"));u(e=>[...e,{id:"new-".concat(Date.now()),file:null,previewUrl:"",isUploading:!1,isNew:!0}])},removeImage:e=>{u(a=>{let t=[...a],r=t[e];if((null==r?void 0:r.originalIndex)!==void 0&&d(e=>[...e,r.originalIndex]),t.length<=1){var s;return(null==(s=t[0])?void 0:s.previewUrl)&&(t[0].previewUrl&&t[0].isNew&&URL.revokeObjectURL(t[0].previewUrl),t[0]={id:"featured-".concat(Date.now()),file:null,previewUrl:"",isUploading:!1,isNew:!0},h(0)),t}return e===m?t.length>1&&h(0):e<m&&h(m-1),t[e].previewUrl&&t[e].isNew&&URL.revokeObjectURL(t[e].previewUrl),t.splice(e,1),t})},setAsFeatured:e=>{e>=0&&e<c.length&&h(e)},handleFileSelect:async(e,a)=>{try{if(!["image/jpeg","image/png","image/webp","image/gif"].includes(e.type)){v.oR.error("Please select a valid image file (JPG, PNG, WebP, or GIF)."),s("error"),i("Please select a valid image file (JPG, PNG, WebP, or GIF).");return}if(e.size>0xf00000){v.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}),s("error"),i("Image is too large. Please select a smaller image (max 15MB).");return}x({dataUrl:URL.createObjectURL(e),originalFile:e,targetIndex:a})}catch(e){console.error("Error processing image:",e),v.oR.error("Failed to process image. Please try again."),s("error"),i("Failed to process image. Please try again.")}},handleCropComplete:f,handleCropDialogClose:()=>{(null==p?void 0:p.dataUrl)&&URL.revokeObjectURL(p.dataUrl),x(null)}}}var j=t(3159),y=t(99840),N=t(2219);let w=e=>new Promise((a,t)=>{let r=new Image;r.addEventListener("load",()=>a(r)),r.addEventListener("error",e=>t(e)),r.setAttribute("crossOrigin","anonymous"),r.src=e});async function k(e,a){let t=await w(e),r=document.createElement("canvas"),s=r.getContext("2d");if(!s)return null;let l=t.naturalWidth/t.width,i=t.naturalHeight/t.height,n=window.devicePixelRatio||1;return r.width=a.width*n*l,r.height=a.height*n*i,s.setTransform(n,0,0,n,0,0),s.imageSmoothingQuality="high",s.drawImage(t,a.x*l,a.y*i,a.width*l,a.height*i,0,0,a.width*l,a.height*i),new Promise(e=>{r.toBlob(e,"image/png")})}function _(e){let{imgSrc:a,onCropComplete:t,onClose:s,isOpen:l}=e,[i,n]=(0,g.useState)({x:0,y:0}),[d,u]=(0,g.useState)(1),[m,h]=(0,g.useState)(null),[p,x]=(0,g.useState)(!1),v=(0,g.useCallback)((e,a)=>{h(a)},[]),b=async()=>{if(!a||!m){console.warn("Image source or crop area not available."),t(null);return}x(!0);try{let e=await k(a,m);t(e)}catch(e){console.error("Error cropping image:",e),t(null)}finally{x(!1)}};return(0,g.useEffect)(()=>{l&&u(1)},[l]),(0,r.jsx)(y.lG,{open:l,onOpenChange:e=>!e&&s(),children:(0,r.jsxs)(y.Cf,{className:"sm:max-w-[600px]",children:[(0,r.jsx)(y.c7,{children:(0,r.jsx)(y.L3,{children:"Crop Your Product Image"})}),(0,r.jsx)("div",{className:"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800",children:a?(0,r.jsx)(j.Ay,{image:a,crop:i,zoom:d,aspect:1,cropShape:"rect",showGrid:!0,onCropChange:n,onZoomChange:u,onCropComplete:v}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("p",{children:"Loading image..."})})}),(0,r.jsx)("div",{className:"px-4 pb-4",children:(0,r.jsx)(N.A,{min:1,max:3,step:.1,value:[d],onValueChange:e=>u(e[0]),className:"w-full","aria-label":"Zoom slider"})}),(0,r.jsxs)(y.Es,{children:[(0,r.jsx)(c.$,{variant:"outline",onClick:s,disabled:p,children:"Cancel"}),(0,r.jsxs)(c.$,{onClick:b,disabled:p,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[p?(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Crop Image"]})]})]})})}var C=t(66766),P=t(84616),A=t(27213),I=t(38564),S=t(62525),E=t(53999),R=t(60760);function z(e){let{images:a,featuredImageIndex:t,disabled:s=!1,maxImages:l=5,onAddImage:i,onRemoveImage:d,onSetFeatured:o,onFileSelect:h,errorDisplay:p}=e,[x,b]=(0,g.useState)(null),[f,j]=(0,g.useState)(!1),[y,N]=(0,g.useState)(null),w=(e,a)=>{var t;let r=null==(t=e.target.files)?void 0:t[0];r&&h(r,a),e.target.value=""},k=e=>{if(s)return;let a=document.getElementById("product-image-".concat(e));a&&a.click()},_=(e,a)=>{e.preventDefault(),e.stopPropagation(),j(!0),"number"==typeof a&&N(a)},z=e=>{e.preventDefault(),e.stopPropagation(),e.currentTarget.contains(e.relatedTarget)||(j(!1),N(null))},U=(e,a)=>{e.preventDefault(),e.stopPropagation(),f||j(!0),"number"==typeof a&&N(a)},L=(e,a)=>{if(e.preventDefault(),e.stopPropagation(),j(!1),N(null),s)return;let t=e.dataTransfer.files;if(t.length>0){let e=t[0];if(!["image/jpeg","image/png","image/webp","image/gif"].includes(e.type))return void v.oR.error("Please drop a valid image file (JPG, PNG, WebP, or GIF).");if(e.size>0xf00000)return void v.oR.error("Image too large",{description:"Please select an image smaller than 15MB"});h(e,a)}},V={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}},exit:{opacity:0,scale:.8,transition:{duration:.2}}},D={rest:{scale:1},hover:{scale:1.1},tap:{scale:.95}};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 flex justify-between items-center",children:[(0,r.jsxs)("span",{children:["Product Images (",a.length,"/",l,")"]}),(0,r.jsx)("div",{className:"flex gap-2",children:a.length<l&&(0,r.jsx)(n.P.div,{initial:"rest",whileHover:"hover",whileTap:"tap",variants:D,children:(0,r.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:i,disabled:s||a.length>=l,className:"h-8 px-2 text-xs",children:[(0,r.jsx)(P.A,{className:"h-3.5 w-3.5 mr-1"}),"Add Image"]})})})]}),(0,r.jsx)(n.P.div,{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 md:gap-4",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.08,delayChildren:.1}}},initial:"hidden",animate:"visible",children:(0,r.jsxs)(R.N,{mode:"popLayout",children:[a.map((e,l)=>{let i=l===t,u=x===l,h=y===l;return(0,r.jsx)(n.P.div,{layout:!0,variants:V,exit:"exit",onHoverStart:()=>b(l),onHoverEnd:()=>b(null),onDragEnter:e=>_(e,l),onDragLeave:z,onDragOver:e=>U(e,l),onDrop:e=>L(e,l),onClick:()=>k(l),className:(0,E.cn)("relative border rounded-lg overflow-hidden group transition-all duration-200 cursor-pointer",i?"border-amber-500 dark:border-amber-400 border-2 col-span-2 sm:col-span-1 md:col-span-1 order-first":"border-neutral-200 dark:border-neutral-700",h&&"border-amber-500 dark:border-amber-400 border-2 bg-amber-50 dark:bg-amber-950/20",!s&&"hover:border-amber-400 dark:hover:border-amber-500"),children:(0,r.jsxs)("div",{className:(0,E.cn)("aspect-square bg-neutral-100 dark:bg-neutral-800 relative transition-all duration-200",h&&"bg-amber-50 dark:bg-amber-950/20"),children:[e.previewUrl?(0,r.jsx)("div",{className:"w-full h-full",children:(0,r.jsx)(C.default,{src:e.previewUrl,alt:"Product image ".concat(l+1),fill:!0,className:"object-cover"})}):(0,r.jsxs)("div",{className:"w-full h-full flex flex-col items-center justify-center text-neutral-400 dark:text-neutral-500 p-2",children:[(0,r.jsx)(n.P.div,{animate:{rotate:10*!!u,scale:h?1.1:1},transition:{duration:.3},children:(0,r.jsx)(A.A,{className:(0,E.cn)("mb-1 transition-colors duration-200",i?"h-10 w-10":"h-8 w-8",h&&"text-amber-500")})}),(0,r.jsx)("span",{className:(0,E.cn)("text-center transition-colors duration-200",i?"text-sm":"text-xs",h&&"text-amber-500"),children:h?"Drop image here":i?"Featured Image":"Add image ".concat(l+1)}),!h&&(0,r.jsx)("span",{className:(0,E.cn)("text-center mt-1 opacity-60",i?"text-xs":"text-[10px]"),children:"Click or drag & drop"})]}),i&&(0,r.jsx)(n.P.div,{className:"absolute top-2 left-2 bg-amber-500 text-white rounded-full p-1.5",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:500,damping:15},children:(0,r.jsx)(I.A,{className:"h-4 w-4"})}),(e.previewUrl||a.length>1)&&(0,r.jsx)(n.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:+!!u,scale:u?1:.8},transition:{duration:.2},className:"absolute top-2 right-2 z-10 md:opacity-0 md:group-hover:opacity-100",children:(0,r.jsx)(n.P.div,{variants:D,initial:"rest",whileHover:"hover",whileTap:"tap",children:(0,r.jsx)(c.$,{type:"button",variant:"destructive",size:"icon",onClick:e=>{e.stopPropagation(),d(l)},disabled:s,className:(0,E.cn)("rounded-full",i?"h-7 w-7":"h-6 w-6"),children:(0,r.jsx)(S.A,{className:i?"h-3.5 w-3.5":"h-3 w-3"})})})}),!i&&e.previewUrl&&(0,r.jsx)(n.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:+!!u,scale:u?1:.8},transition:{duration:.2},className:"absolute top-2 left-2 z-10 md:opacity-0 md:group-hover:opacity-100",children:(0,r.jsx)(n.P.div,{variants:D,initial:"rest",whileHover:"hover",whileTap:"tap",children:(0,r.jsx)(c.$,{type:"button",variant:"outline",size:"icon",onClick:e=>{e.stopPropagation(),o(l)},disabled:s,className:"h-6 w-6 rounded-full bg-amber-500 text-white hover:bg-amber-600 border-none",children:(0,r.jsx)(I.A,{className:"h-3 w-3"})})})}),(0,r.jsx)(m.p,{id:"product-image-".concat(l),type:"file",accept:"image/jpeg,image/png,image/webp,image/gif",onChange:e=>w(e,l),className:"hidden",disabled:s})]})},e.id)}),a.length<l&&a.some(e=>e.previewUrl)&&(0,r.jsxs)(n.P.button,{variants:V,whileHover:{scale:1.05,borderColor:"var(--amber-500)"},whileTap:{scale:.95},type:"button",onClick:i,disabled:s,className:(0,E.cn)("aspect-square border-2 border-dashed rounded-lg flex flex-col items-center justify-center transition-colors","border-neutral-200 dark:border-neutral-700 text-neutral-400 dark:text-neutral-500","hover:border-amber-500 dark:hover:border-amber-500"),children:[(0,r.jsx)(n.P.div,{animate:{y:[0,-5,0]},transition:{repeat:1/0,repeatType:"loop",duration:2,repeatDelay:1},children:(0,r.jsx)(P.A,{className:"h-8 w-8 mb-1"})}),(0,r.jsx)("span",{className:"text-xs",children:"Add Image"})]},"add-button")]})})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,r.jsxs)(u.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400",children:["Upload up to ",l," images (JPG, PNG, WebP, or GIF, max 15MB each). Click on any image card or drag & drop files to upload. Images will be automatically cropped to square (1:1) aspect ratio. Select one image as featured to display in listings."]}),p&&(0,r.jsx)(n.P.p,{initial:{opacity:0},animate:{opacity:1},className:"text-xs text-red-500 dark:text-red-400 mt-1",children:p})]})]})}var U=t(75401),L=t(82714),V=t(49026),D=t(1243),M=t(43332),F=t(54416),B=t(27737),O=t(71553),q=t(44895),G=t(60823),T=t(10081),J=t(5196),$=t(34704);function H(e){var a;let{productId:t,initialData:i,onSubmit:u,isSubmitting:h,onCancel:x}=e,[b,j]=(0,g.useState)(!0),[y,N]=(0,g.useState)((null==i?void 0:i.variant_values)||{}),[w,k]=(0,g.useState)([]),[C,A]=(0,g.useState)(!1),I=!!i,[S,U]=(0,g.useState)(null),[B,H]=(0,g.useState)(null),{images:K,featuredImageIndex:Y,removedOriginalIndices:Q,imageToCrop:Z,imageErrorDisplay:X,addImageSlot:ee,removeImage:ea,setAsFeatured:et,handleFileSelect:er,handleCropComplete:es,handleCropDialogClose:el}=f({initialImageUrls:(null==i?void 0:i.images)||null,initialFeaturedIndex:(null==i?void 0:i.featured_image_index)||0,maxImages:5}),ei=(0,s.mN)({resolver:(0,l.u)(O.sX),defaultValues:{product_id:t,variant_name:(null==i?void 0:i.variant_name)||"",variant_values:(null==i?void 0:i.variant_values)||{},base_price:(null==i?void 0:i.base_price)||void 0,discounted_price:(null==i?void 0:i.discounted_price)||void 0,is_available:null==(a=null==i?void 0:i.is_available)||a,images:(null==i?void 0:i.images)||[],featured_image_index:(null==i?void 0:i.featured_image_index)||0}});(0,g.useEffect)(()=>{let e=setTimeout(()=>j(!1),500);return()=>clearTimeout(e)},[]);let en=e=>{let a=[];Object.entries(e).forEach(e=>{let[t,r]=e;t.trim()||a.push("Variant type names cannot be empty"),r.trim()||a.push("Variant values cannot be empty"),t.trim().length>50&&a.push("Variant type names cannot exceed 50 characters"),r.trim().length>50&&a.push("Variant values cannot exceed 50 characters")});let t=Object.keys(e).map(e=>e.trim().toLowerCase()),r=new Set(t);return t.length!==r.size&&a.push("Duplicate variant type names are not allowed"),0===Object.keys(e).length&&a.push("At least one variant property is required"),[...new Set(a)]},ed=(e,a)=>{let t=[];return void 0!==e&&e<=0&&t.push("Base price must be greater than 0"),void 0!==a&&a<=0&&t.push("Discounted price must be greater than 0"),e&&a&&a>=e&&t.push("Discounted price must be less than base price"),t};(0,g.useEffect)(()=>{ei.setValue("variant_values",y),A(!0),k(en(y)),A(!1)},[y,ei]);let eo=async e=>{try{A(!0);let a=en(e.variant_values||{}),t=ed(e.base_price||void 0,e.discounted_price||void 0),r=[...a,...t];if(r.length>0){k(r),A(!1),v.oR.error("Please fix the validation errors before submitting");return}k([]);let s=K.map(e=>e.file),l=I&&(null==i?void 0:i.id)?{...e,id:i.id}:e;await u(l,s,Y,Q,K),k([]),A(!1)}catch(e){console.error("Error submitting variant form:",e),A(!1),v.oR.error("Failed to save variant. Please try again.")}},ec=e=>{N(a=>{let t={...a};return delete t[e],t})},eu=(e,a)=>{if(e===a)return;let t=a.trim();return t.length>50?void v.oR.error("Variant type name cannot exceed 50 characters"):Object.keys(y).filter(a=>a!==e).map(e=>e.trim().toLowerCase()).includes(t.toLowerCase())?void v.oR.error("Variant type name already exists"):void N(a=>{let r={...a},s=r[e];return delete r[e],r[t]=s,r})},em=(e,a)=>{let t=a.trim();if(t.length>50)return void v.oR.error("Variant value cannot exceed 50 characters");N(a=>({...a,[e]:t}))};return b?(0,r.jsx)(W,{}):(0,r.jsxs)("div",{className:"space-y-6",children:[w.length>0&&(0,r.jsxs)(V.Fc,{variant:"destructive",children:[(0,r.jsx)(D.A,{className:"h-4 w-4"}),(0,r.jsx)(V.TN,{children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"font-medium",children:"Please fix the following errors:"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-1",children:w.map((e,a)=>(0,r.jsx)("li",{className:"text-sm",children:e},a))})]})})]}),(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(R.N,{mode:"wait",children:[(0,r.jsx)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(L.J,{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Variant Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(M.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400"}),(0,r.jsx)(m.p,{placeholder:"e.g., Red Large, 64GB Blue, Cotton Medium",className:"pl-10",value:ei.watch("variant_name")||"",onChange:e=>ei.setValue("variant_name",e.target.value)})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"A descriptive name for this variant combination"})]})},"variant-name"),(0,r.jsx)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.2},children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Variant Properties"}),(0,r.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{if(Object.keys(y).length>=5)return void v.oR.error("Maximum of 5 variant types allowed per product");let e=Object.keys(y).map(e=>e.toLowerCase()),a=(0,$.MJ)().find(a=>!e.includes(a.name.toLowerCase())),t=a?a.name:"type_".concat(Object.keys(y).length+1);N(e=>({...e,[t]:""}))},disabled:Object.keys(y).length>=5,className:"h-8 px-2 text-xs",children:[(0,r.jsx)(P.A,{className:"h-3.5 w-3.5 mr-1"}),"Add Property"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[Object.entries(y).map(e=>{var a,t;let[s,l]=e,i=(0,$.Ou)(s);return(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"flex gap-2 items-center",children:[(0,r.jsxs)(G.AM,{open:S===s,onOpenChange:e=>U(e?s:null),children:[(0,r.jsx)(G.Wv,{asChild:!0,children:(0,r.jsxs)(c.$,{variant:"outline",role:"combobox","aria-expanded":S===s,className:"flex-1 justify-between bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900",children:[s?(null==(a=(0,$.MJ)().find(e=>e.name===s))?void 0:a.display_name)||s:"Select type...",(0,r.jsx)(T.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,r.jsx)(G.hl,{className:"w-[300px] p-0 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700",children:(0,r.jsxs)(q.uB,{className:"bg-white dark:bg-black",children:[(0,r.jsx)(q.G7,{placeholder:"Search variant type...",className:"h-9 bg-white dark:bg-black border-none focus:ring-0 focus:ring-offset-0 focus:border-transparent focus:outline-none"}),(0,r.jsxs)(q.oI,{className:"max-h-[200px] bg-white dark:bg-black",children:[(0,r.jsx)(q.xL,{className:"bg-white dark:bg-black",children:"No variant type found."}),(0,r.jsx)(q.L$,{className:"bg-white dark:bg-black",children:(0,$.MJ)().map(e=>(0,r.jsxs)(q.h_,{value:e.name,onSelect:e=>{eu(s,e),U(null)},className:"bg-white dark:bg-black hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer",children:[(0,r.jsxs)("div",{className:"flex flex-col flex-1",children:[(0,r.jsx)("span",{className:"font-medium",children:e.display_name}),e.description&&(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,r.jsx)(J.A,{className:(0,E.cn)("ml-auto h-4 w-4",s===e.name?"opacity-100":"opacity-0")})]},e.name))})]})]})})]}),"color"===s.toLowerCase()&&i.length>0?(0,r.jsxs)(G.AM,{open:B===s,onOpenChange:e=>H(e?s:null),children:[(0,r.jsx)(G.Wv,{asChild:!0,children:(0,r.jsxs)(c.$,{variant:"outline",role:"combobox","aria-expanded":B===s,className:"flex-1 justify-between bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900",children:[l?(null==(t=i.find(e=>e.value===l))?void 0:t.display_value)||l:"Select color...",(0,r.jsx)(T.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,r.jsx)(G.hl,{className:"w-[300px] p-0 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700",children:(0,r.jsxs)(q.uB,{className:"bg-white dark:bg-black",children:[(0,r.jsx)(q.G7,{placeholder:"Search color...",className:"h-9 bg-white dark:bg-black border-none focus:ring-0 focus:ring-offset-0 focus:border-transparent focus:outline-none"}),(0,r.jsxs)(q.oI,{className:"max-h-[200px] bg-white dark:bg-black",children:[(0,r.jsx)(q.xL,{className:"bg-white dark:bg-black",children:"No color found."}),(0,r.jsx)(q.L$,{className:"bg-white dark:bg-black",children:i.map(e=>(0,r.jsxs)(q.h_,{value:e.value,onSelect:e=>{em(s,e),H(null)},className:"bg-white dark:bg-black hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[e.color_code&&(0,r.jsx)("div",{className:"w-4 h-4 rounded-full border border-neutral-300",style:{backgroundColor:e.color_code}}),(0,r.jsx)("span",{children:e.display_value})]}),(0,r.jsx)(J.A,{className:(0,E.cn)("ml-auto h-4 w-4",l===e.value?"opacity-100":"opacity-0")})]},e.value))})]})]})})]}):(0,r.jsx)(m.p,{type:"text",placeholder:"Enter ".concat(s.toLowerCase(),"..."),value:l||"",onChange:e=>em(s,e.target.value),className:"flex-1 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700"}),(0,r.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>ec(s),className:"h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950",children:(0,r.jsx)(F.A,{className:"h-4 w-4"})})]},s)}),0===Object.keys(y).length&&(0,r.jsx)("div",{className:"text-sm text-neutral-500 text-center py-4 border-2 border-dashed border-neutral-200 dark:border-neutral-700 rounded-lg",children:'No variant properties added yet. Click "Add Property" to start.'})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add up to 5 variant properties (e.g., color: red, size: large)"})]})},"variant-values"),(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3},className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(L.J,{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Base Price (₹)"}),(0,r.jsx)(m.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",value:ei.watch("base_price")||"",onChange:e=>ei.setValue("base_price",e.target.value?parseFloat(e.target.value):void 0)}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Leave empty to use product's base price"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(L.J,{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Discounted Price (₹)"}),(0,r.jsx)(m.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",value:ei.watch("discounted_price")||"",onChange:e=>ei.setValue("discounted_price",e.target.value?parseFloat(e.target.value):void 0)}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Optional discounted price for this variant"})]})]},"pricing"),(0,r.jsx)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},children:(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(L.J,{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Available for Purchase"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Toggle to make this variant available to customers"})]}),(0,r.jsx)(p.d,{checked:ei.watch("is_available")||!1,onCheckedChange:e=>ei.setValue("is_available",e),className:"data-[state=checked]:bg-primary"})]})},"availability"),(0,r.jsx)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.5},children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Variant Images"}),(0,r.jsx)(z,{images:K,featuredImageIndex:Y,disabled:h,maxImages:5,onAddImage:ee,onRemoveImage:ea,onSetFeatured:et,onFileSelect:er,errorDisplay:X}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add images specific to this variant. If no images are added, the product's main images will be used."})]})},"images")]}),(0,r.jsxs)("div",{className:"flex gap-3 pt-4 border-t",children:[x&&(0,r.jsx)(c.$,{type:"button",variant:"outline",onClick:x,disabled:h,className:"flex-1",children:"Cancel"}),(0,r.jsx)(c.$,{type:"button",disabled:h||C||0===Object.keys(y).length||w.length>0,className:"flex-1 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",onClick:()=>{eo(ei.getValues())},children:h||C?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),C?"Validating...":I?"Updating...":"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),I?"Update Variant":"Create Variant"]})})]})]})}),(0,r.jsx)(_,{imgSrc:(null==Z?void 0:Z.dataUrl)||null,onCropComplete:es,onClose:el,isOpen:!!Z})]})}function W(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(B.E,{className:"h-5 w-24"}),(0,r.jsx)(B.E,{className:"h-10 w-full rounded-md"}),(0,r.jsx)(B.E,{className:"h-3 w-48"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(B.E,{className:"h-5 w-32"}),(0,r.jsx)(B.E,{className:"h-8 w-24 rounded-md"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(B.E,{className:"h-10 flex-1 rounded-md"}),(0,r.jsx)(B.E,{className:"h-10 flex-1 rounded-md"}),(0,r.jsx)(B.E,{className:"h-8 w-8 rounded-md"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(B.E,{className:"h-10 flex-1 rounded-md"}),(0,r.jsx)(B.E,{className:"h-10 flex-1 rounded-md"}),(0,r.jsx)(B.E,{className:"h-8 w-8 rounded-md"})]})]}),(0,r.jsx)(B.E,{className:"h-3 w-64"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(B.E,{className:"h-5 w-20"}),(0,r.jsx)(B.E,{className:"h-10 w-full rounded-md"}),(0,r.jsx)(B.E,{className:"h-3 w-40"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(B.E,{className:"h-5 w-28"}),(0,r.jsx)(B.E,{className:"h-10 w-full rounded-md"}),(0,r.jsx)(B.E,{className:"h-3 w-44"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(B.E,{className:"h-5 w-32"}),(0,r.jsx)(B.E,{className:"h-3 w-56"})]}),(0,r.jsx)(B.E,{className:"h-6 w-12 rounded-full"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(B.E,{className:"h-5 w-24"}),(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[(0,r.jsx)(B.E,{className:"h-32 w-32 rounded-lg"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(B.E,{className:"h-10 w-full rounded-md"}),(0,r.jsx)(B.E,{className:"h-3 w-full mt-2"})]})]}),(0,r.jsx)(B.E,{className:"h-3 w-80"})]}),(0,r.jsxs)("div",{className:"flex gap-3 pt-4 border-t",children:[(0,r.jsx)(B.E,{className:"h-10 flex-1 rounded-md"}),(0,r.jsx)(B.E,{className:"h-10 flex-1 rounded-md"})]})]})}var K=t(381);let Y=i.Ik({product_type:i.k5(["physical","service"],{required_error:"Please select a product type."}).default("physical"),name:i.Yj().min(1,{message:"Product/Service name is required."}).max(100,{message:"Name cannot exceed 100 characters."}),description:i.Yj().max(500,{message:"Description cannot exceed 500 characters."}).optional().or(i.eu("")),base_price:i.au.number({required_error:"Base price is required.",invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}),discounted_price:i.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:i.zM().default(!0)}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]});function Q(e){var a,t,i,b;let{initialData:j,initialVariants:y,onSubmit:N,isSubmitting:w,isEditing:k,planLimit:C,currentAvailableCount:A}=e,[I,S]=(0,g.useState)(y||[]),[E,R]=(0,g.useState)(!1),[L,V]=(0,g.useState)(null),[D,M]=(0,g.useState)(!1),B={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},{images:O,featuredImageIndex:q,imageToCrop:G,imageErrorDisplay:T,addImageSlot:J,removeImage:$,setAsFeatured:W,handleFileSelect:Q,handleCropComplete:Z,handleCropDialogClose:X}=f({initialImageUrls:(null==j?void 0:j.images)||((null==j?void 0:j.image_url)?[j.image_url]:null),initialFeaturedIndex:(null==j?void 0:j.featured_image_index)||0,maxImages:5}),ee=(0,s.mN)({resolver:(0,l.u)(Y),mode:"onChange",defaultValues:{product_type:null!=(a=null==j?void 0:j.product_type)?a:"physical",name:null!=(t=null==j?void 0:j.name)?t:"",description:null!=(i=null==j?void 0:j.description)?i:"",base_price:(null==j?void 0:j.base_price)!==void 0?j.base_price:void 0,discounted_price:(null==j?void 0:j.discounted_price)!==void 0?j.discounted_price:null,is_available:null==(b=null==j?void 0:j.is_available)||b}}),ea=()=>{V(null),R(!0)},et=()=>{R(!1),V(null)},er=async(e,a,t,r,s)=>{try{let d=(null==a?void 0:a.filter(e=>null!==e))||[];if(L){let a=[];if(s&&s.length>0)a=s.map(e=>e.previewUrl).filter(e=>""!==e);else{let e=L.images||[];if(a=r&&r.length>0?e.filter((e,a)=>!r.includes(a)):[...e],d.length>0){let e=d.map(e=>URL.createObjectURL(e));a.push(...e)}}S(s=>s.map(s=>{var l,i,n;return s.id===L.id?{...s,variant_name:e.variant_name||"",variant_values:e.variant_values||{},base_price:null!=(l=e.base_price)?l:null,discounted_price:null!=(i=e.discounted_price)?i:null,is_available:null==(n=e.is_available)||n,images:a,featured_image_index:Math.min(t||0,Math.max(0,a.length-1)),updated_at:new Date().toISOString(),_imageFiles:d.length>0?d:s._imageFiles,_removedImageIndices:r&&r.length>0?r:s._removedImageIndices}:s})),v.oR.success("Variant updated successfully")}else{var l,i,n;let a=d.length>0?d.map(e=>URL.createObjectURL(e)):[],r={id:"temp-".concat(Date.now()),product_id:(null==j?void 0:j.id)||"",variant_name:e.variant_name||"",variant_values:e.variant_values||{},base_price:null!=(l=e.base_price)?l:null,discounted_price:null!=(i=e.discounted_price)?i:null,is_available:null==(n=e.is_available)||n,images:a,featured_image_index:t||0,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),_imageFiles:d};S(e=>[...e,r]),v.oR.success("Variant added successfully")}R(!1),V(null)}catch(e){console.error("Error saving variant:",e),v.oR.error("Failed to save variant")}};return(0,r.jsxs)(n.P.div,{className:"space-y-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.05}}},initial:"hidden",animate:"visible",children:[(0,r.jsx)(n.P.div,{variants:B,className:"py-6 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-50",children:k?"Product Details":"Product Information"}),(0,r.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 leading-relaxed",children:k?"Update the details for this item. All fields marked with * are required.":"Fill in the details for the new product or service. All fields marked with * are required."})]})}),(0,r.jsx)(n.P.div,{variants:B,children:(0,r.jsx)(u.lV,{...ee,children:(0,r.jsxs)("form",{onSubmit:ee.handleSubmit(e=>{var a;let t=O.map(e=>e.file),r=[],s=(null==j||null==(a=j.images)?void 0:a.length)||+(null!=j&&!!j.image_url);if(s>0){let e=new Set(O.filter(e=>void 0!==e.originalIndex).map(e=>e.originalIndex));for(let a=0;a<s;a++)e.has(a)||r.push(a)}console.log("Initial image count:",s),console.log("Current images:",O),console.log("Removed image indices:",r),console.log("Variants to be created:",I),N({...e,variants:I},t,q,r)}),className:"space-y-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"pb-4 border-b border-neutral-200/60 dark:border-neutral-800/60",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-50",children:"Basic Information"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mt-1",children:"Essential details about your product or service"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(n.P.div,{variants:B,className:"md:col-span-2",children:(0,r.jsx)(u.zB,{control:ee.control,name:"product_type",render:e=>{let{field:a}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Product Type *"}),(0,r.jsxs)(x.l6,{disabled:w,onValueChange:a.onChange,defaultValue:a.value,children:[(0,r.jsx)(u.MJ,{children:(0,r.jsx)(x.bq,{className:"w-full",children:(0,r.jsx)(x.yv,{placeholder:"Select product type"})})}),(0,r.jsxs)(x.gC,{children:[(0,r.jsx)(x.eb,{value:"physical",children:"Physical Product"}),(0,r.jsx)(x.eb,{value:"service",children:"Service"})]})]}),(0,r.jsx)(u.Rr,{children:"Choose whether this is a physical product or a service."}),(0,r.jsx)(u.C5,{})]})}})}),(0,r.jsx)(n.P.div,{variants:B,className:"md:col-span-2",children:(0,r.jsx)(u.zB,{control:ee.control,name:"name",render:e=>{let{field:a}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Name *"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(m.p,{placeholder:"Enter product/service name",...a,disabled:w,className:"h-10"})}),(0,r.jsx)(u.Rr,{children:"The name of your product or service (max 100 characters)."}),(0,r.jsx)(u.C5,{})]})}})}),(0,r.jsx)(n.P.div,{variants:B,className:"md:col-span-2",children:(0,r.jsx)(u.zB,{control:ee.control,name:"description",render:e=>{let{field:a}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Description"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(h.T,{placeholder:"Enter product/service description",className:"resize-none min-h-[120px]",...a,disabled:w})}),(0,r.jsx)(u.Rr,{children:"Describe your product or service (max 500 characters)."}),(0,r.jsx)(u.C5,{})]})}})}),(0,r.jsx)(n.P.div,{variants:B,children:(0,r.jsx)(u.zB,{control:ee.control,name:"base_price",render:e=>{let{field:{value:a,onChange:t,...s}}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Base Price (₹) *"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(m.p,{type:"number",placeholder:"Enter base price",value:void 0===a?"":a,onChange:e=>{let a=e.target.value;t(""===a?void 0:Number(a))},...s,disabled:w,className:"h-10"})}),(0,r.jsx)(u.Rr,{children:"The regular price of your product or service."}),(0,r.jsx)(u.C5,{})]})}})}),(0,r.jsx)(n.P.div,{variants:B,children:(0,r.jsx)(u.zB,{control:ee.control,name:"discounted_price",render:e=>{let{field:{value:a,onChange:t,...s}}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Discounted Price (₹)"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(m.p,{type:"number",placeholder:"Enter discounted price (optional)",value:null===a?"":a,onChange:e=>{let a=e.target.value;t(""===a?null:Number(a))},...s,disabled:w,className:"h-10"})}),(0,r.jsx)(u.Rr,{children:"Optional: A special or sale price (must be less than base price)."}),(0,r.jsx)(u.C5,{})]})}})}),(0,r.jsx)(n.P.div,{variants:B,className:"md:col-span-2",children:(0,r.jsx)(u.zB,{control:ee.control,name:"is_available",render:e=>{let{field:a}=e,t=void 0!==C&&void 0!==A&&A>=C&&!a.value&&!(null==j?void 0:j.is_available);return(0,r.jsxs)(u.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(u.lR,{className:"text-base",children:"Available"}),(0,r.jsx)(u.Rr,{children:t?"You've reached your plan limit of ".concat(C," available products."):"Toggle whether this product/service is currently available."})]}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(p.d,{checked:a.value,onCheckedChange:a.onChange,disabled:w||t})})]})}})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"pb-4 border-b border-neutral-200/60 dark:border-neutral-800/60",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-50",children:"Product Images"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mt-1",children:"Upload high-quality images to showcase your product"})]}),(0,r.jsx)(n.P.div,{variants:B,children:(0,r.jsx)(z,{images:O,featuredImageIndex:q,onAddImage:J,onRemoveImage:$,onSetFeatured:W,onFileSelect:Q,disabled:w,errorDisplay:T})})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"pb-4 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-neutral-900 dark:text-neutral-50",children:"Product Variants"}),(0,r.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mt-1",children:k?"Manage different variations of this product (e.g., size, color, style)":"Add different variations of this product (e.g., size, color, style). Variants will be saved when you create the product."})]}),(0,r.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>M(!D),className:"flex items-center gap-2",children:[(0,r.jsx)(K.A,{className:"h-4 w-4"}),D?"Hide":"Show"," Variants"]})]})}),(0,r.jsx)(n.P.div,{variants:B,children:D&&(0,r.jsxs)("div",{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 bg-white dark:bg-black",children:[(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-2 mb-4",children:(0,r.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:ea,className:"flex items-center gap-2",disabled:E,children:[(0,r.jsx)(P.A,{className:"h-4 w-4"}),"Add Variant"]})}),E&&(0,r.jsxs)("div",{className:"mb-6 p-4 border border-neutral-200 dark:border-neutral-700 rounded-lg bg-white dark:bg-black",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:L?"Edit Variant":"Add New Variant"}),(0,r.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",onClick:et,className:"h-8 w-8 p-0",children:(0,r.jsx)(F.A,{className:"h-4 w-4"})})]}),(0,r.jsx)(H,{productId:(null==j?void 0:j.id)||"draft",initialData:L,onSubmit:er,isSubmitting:!1,onCancel:et})]}),I.length>0?(0,r.jsx)(U.A,{productId:(null==j?void 0:j.id)||"draft",variants:I,onAddVariant:ea,onEditVariant:e=>{V(e),R(!0)},onDeleteVariant:e=>{S(a=>a.filter(a=>a.id!==e)),v.oR.success("Variant deleted successfully")},onToggleVariantAvailability:(e,a)=>{S(t=>t.map(t=>t.id===e?{...t,is_available:a}:t)),v.oR.success("Variant ".concat(a?"enabled":"disabled"," successfully"))}}):(0,r.jsxs)("div",{className:"text-center py-8 text-neutral-500 dark:text-neutral-400",children:[(0,r.jsx)(d.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{className:"text-sm",children:"No variants created yet"}),(0,r.jsx)("p",{className:"text-xs mt-1",children:"Add variants to offer different options for this product"})]})]})})]}),(0,r.jsx)("div",{className:"pt-8 border-t border-neutral-200/60 dark:border-neutral-800/60",children:(0,r.jsxs)(n.P.div,{variants:B,className:"flex flex-col sm:flex-row gap-3 justify-end",children:[(0,r.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>window.history.back(),disabled:w,className:"order-2 sm:order-1",children:"Cancel"}),(0,r.jsx)(c.$,{type:"submit",disabled:w,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium order-1 sm:order-2",size:"lg",children:w?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,r.jsx)("span",{children:k?"Updating...":"Creating..."})]}):(0,r.jsx)(r.Fragment,{children:k?"Update Product":"Create Product"})})]})})]})})}),(0,r.jsx)(_,{isOpen:!!G,imgSrc:(null==G?void 0:G.dataUrl)||null,onCropComplete:Z,onClose:X})]})}},13052:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13717:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},30070:(e,a,t)=>{t.d(a,{C5:()=>b,MJ:()=>g,Rr:()=>v,eI:()=>p,lR:()=>x,lV:()=>o,zB:()=>u});var r=t(95155),s=t(12115),l=t(99708),i=t(62177),n=t(53999),d=t(82714);let o=i.Op,c=s.createContext({}),u=e=>{let{...a}=e;return(0,r.jsx)(c.Provider,{value:{name:a.name},children:(0,r.jsx)(i.xI,{...a})})},m=()=>{let e=s.useContext(c),a=s.useContext(h),{getFieldState:t}=(0,i.xW)(),r=(0,i.lN)({name:e.name}),l=t(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=a;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},h=s.createContext({});function p(e){let{className:a,...t}=e,l=s.useId();return(0,r.jsx)(h.Provider,{value:{id:l},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,n.cn)("grid gap-2",a),...t})})}function x(e){let{className:a,...t}=e,{error:s,formItemId:l}=m();return(0,r.jsx)(d.J,{"data-slot":"form-label","data-error":!!s,className:(0,n.cn)("data-[error=true]:text-destructive",a),htmlFor:l,...t})}function g(e){let{...a}=e,{error:t,formItemId:s,formDescriptionId:i,formMessageId:n}=m();return(0,r.jsx)(l.DX,{"data-slot":"form-control",id:s,"aria-describedby":t?"".concat(i," ").concat(n):"".concat(i),"aria-invalid":!!t,...a})}function v(e){let{className:a,...t}=e,{formDescriptionId:s}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:s,className:(0,n.cn)("text-muted-foreground text-sm",a),...t})}function b(e){var a;let{className:t,...s}=e,{error:l,formMessageId:i}=m(),d=l?String(null!=(a=null==l?void 0:l.message)?a:""):s.children;return d?(0,r.jsx)("p",{"data-slot":"form-message",id:i,className:(0,n.cn)("text-destructive text-sm",t),...s,children:d}):null}},35169:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,a,t)=>{var r=t(18999);t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(a,{useSearchParams:function(){return r.useSearchParams}})},37108:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},38564:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},44895:(e,a,t)=>{t.d(a,{G7:()=>d,L$:()=>u,h_:()=>m,oI:()=>o,uB:()=>n,xL:()=>c});var r=t(95155);t(12115);var s=t(77740),l=t(47924),i=t(53999);function n(e){let{className:a,...t}=e;return(0,r.jsx)(s.uB,{"data-slot":"command",className:(0,i.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",a),...t})}function d(e){let{className:a,...t}=e;return(0,r.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,r.jsx)(l.A,{className:"size-4 shrink-0 opacity-50"}),(0,r.jsx)(s.uB.Input,{"data-slot":"command-input",className:(0,i.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",a),...t})]})}function o(e){let{className:a,...t}=e;return(0,r.jsx)(s.uB.List,{"data-slot":"command-list",className:(0,i.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",a),...t})}function c(e){let{...a}=e;return(0,r.jsx)(s.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...a})}function u(e){let{className:a,...t}=e;return(0,r.jsx)(s.uB.Group,{"data-slot":"command-group",className:(0,i.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",a),...t})}function m(e){let{className:a,...t}=e;return(0,r.jsx)(s.uB.Item,{"data-slot":"command-item",className:(0,i.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...t})}t(99840)},55868:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},60823:(e,a,t)=>{t.d(a,{AM:()=>i,Wv:()=>n,hl:()=>d});var r=t(95155);t(12115);var s=t(98176),l=t(53999);function i(e){let{...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"popover",...a})}function n(e){let{...a}=e;return(0,r.jsx)(s.l9,{"data-slot":"popover-trigger",...a})}function d(e){let{className:a,align:t="center",sideOffset:i=4,...n}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"popover-content",align:t,sideOffset:i,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",a),...n})})}},84616:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},90088:(e,a,t)=>{t.d(a,{d:()=>i});var r=t(95155);t(12115);var s=t(4884),l=t(53999);function i(e){let{className:a,...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,r.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},90196:(e,a,t)=>{async function r(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{format:t="webp",targetSizeKB:r=100,maxDimension:s=800,quality:l=.8}=a;return new Promise((a,i)=>{let n=new Image;n.onload=()=>{try{let d=document.createElement("canvas"),o=d.getContext("2d");if(!o)return void i(Error("Could not get canvas context"));let{width:c,height:u}=n;(c>s||u>s)&&(c>u?(u=u*s/c,c=s):(c=c*s/u,u=s)),d.width=c,d.height=u,o.drawImage(n,0,0,c,u);let m=l,h=0,p=()=>{d.toBlob(t=>{if(!t)return void i(Error("Failed to create blob"));let s=t.size/1024;if(s<=r||h>=5||m<=.1){let r=e.size/t.size;a({blob:t,finalSizeKB:Math.round(100*s)/100,compressionRatio:Math.round(100*r)/100,dimensions:{width:c,height:u}})}else h++,m=Math.max(.1,m-.15),p()},"image/".concat(t),m)};p()}catch(e){i(e)}},n.onerror=()=>i(Error("Failed to load image")),n.src=URL.createObjectURL(e)})}async function s(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.size/1048576,s=100,l=800,i=.7;return t<=2?(i=.7,l=800,s=90):t<=5?(i=.55,l=700,s=80):t<=10?(i=.45,l=600,s=70):(i=.35,l=550,s=60),r(e,{...a,targetSizeKB:a.targetSizeKB||s,maxDimension:a.maxDimension||l,quality:a.quality||i})}async function l(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return r(e,{targetSizeKB:50,maxDimension:400,quality:.7,...a})}t.d(a,{compressImageUltraAggressiveClient:()=>s,q:()=>l})},99474:(e,a,t)=>{t.d(a,{T:()=>l});var r=t(95155);t(12115);var s=t(53999);function l(e){let{className:a,...t}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...t})}}}]);