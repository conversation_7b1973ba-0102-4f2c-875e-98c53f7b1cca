(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2017],{29058:(e,r,t)=>{"use strict";t.d(r,{t:()=>s});var a=t(34477);let s=(0,a.createServerReference)("40d32ea8edc6596cf0013772648e4fa734c9679198",a.callServer,void 0,a.findSourceMapURL,"getPincodeDetails")},30070:(e,r,t)=>{"use strict";t.d(r,{C5:()=>f,MJ:()=>p,Rr:()=>h,eI:()=>x,lR:()=>b,lV:()=>d,zB:()=>u});var a=t(95155),s=t(12115),n=t(99708),l=t(62177),i=t(53999),o=t(82714);let d=l.Op,c=s.createContext({}),u=e=>{let{...r}=e;return(0,a.jsx)(c.Provider,{value:{name:r.name},children:(0,a.jsx)(l.xI,{...r})})},m=()=>{let e=s.useContext(c),r=s.useContext(g),{getFieldState:t}=(0,l.xW)(),a=(0,l.lN)({name:e.name}),n=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},g=s.createContext({});function x(e){let{className:r,...t}=e,n=s.useId();return(0,a.jsx)(g.Provider,{value:{id:n},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",r),...t})})}function b(e){let{className:r,...t}=e,{error:s,formItemId:n}=m();return(0,a.jsx)(o.J,{"data-slot":"form-label","data-error":!!s,className:(0,i.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...t})}function p(e){let{...r}=e,{error:t,formItemId:s,formDescriptionId:l,formMessageId:i}=m();return(0,a.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":t?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!t,...r})}function h(e){let{className:r,...t}=e,{formDescriptionId:s}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,i.cn)("text-muted-foreground text-sm",r),...t})}function f(e){var r;let{className:t,...s}=e,{error:n,formMessageId:l}=m(),o=n?String(null!=(r=null==n?void 0:n.message)?r:""):s.children;return o?(0,a.jsx)("p",{"data-slot":"form-message",id:l,className:(0,i.cn)("text-destructive text-sm",t),...s,children:o}):null}},43026:(e,r,t)=>{"use strict";t.d(r,{default:()=>ea});var a=t(95155),s=t(12115),n=t(88482),l=t(76037),i=t(30070),o=t(35695),d=t(75168),c=t(56671),u=t(49509),m=t(55860),g=t(34477);let x=(0,g.createServerReference)("00c19fcd271ff4dd7deb52f7fbab320718d86640ae",g.callServer,void 0,g.findSourceMapURL,"getExistingBusinessProfileData");var b=t(62177),p=t(90221);let h=(0,g.createServerReference)("40f3fc57f11db1570845526aba403b36381f06977d",g.callServer,void 0,g.findSourceMapURL,"createBusinessProfile");var f=t(55594),v=t(48347);let j=f.z.object({businessName:f.z.string().min(2,{message:"Business name must be at least 2 characters."}),email:f.z.string().email({message:"Please enter a valid email."}),memberName:f.z.string().min(2,{message:"Your name is required."}),title:f.z.string().min(2,{message:"Your title/designation is required."}),phone:v.LE,businessCategory:f.z.string().min(1,{message:"Business category is required."}),businessSlug:f.z.string().min(3,{message:"URL slug must be at least 3 characters."}).regex(/^[a-z0-9-]+$/,{message:"URL slug can only contain lowercase letters, numbers, and hyphens."}),addressLine:f.z.string().min(1,{message:"Address line is required."}),pincode:f.z.string().min(6,{message:"Pincode must be 6 digits."}).max(6,{message:"Pincode must be 6 digits."}).regex(/^\d+$/,{message:"Pincode must contain only digits."}),city:f.z.string().min(1,{message:"City is required."}),state:f.z.string().min(1,{message:"State is required."}),locality:f.z.string().min(1,{message:"Locality/area is required."}),businessStatus:f.z.enum(["online","offline"]).default("online"),planId:f.z.string().min(1,{message:"Please select a plan."})}),y=[["businessName","email"],["memberName","title","phone","businessCategory","businessSlug"],["addressLine","pincode","city","state","locality","businessStatus"],["planId"]],k=["Business Details","Card Information","Address & Status","Choose Your Plan"],N=["Let's set up your business profile","Let's create your unique digital business card","Complete your business address and status","Select the perfect plan for your business"],w=(e,r)=>3===e&&r?"Review your current plan and complete setup":N[e]||"";var S=t(53999),C=t(45964),A=t.n(C);let z=(0,g.createServerReference)("40058db62dc30578fcbfd58f8274f8dc59046ae95e",g.callServer,void 0,g.findSourceMapURL,"checkSlugAvailability");var I=t(29058),P=t(5196);function E(e){let{currentStep:r,existingData:t,_isLoadingExistingData:s}=e;return(0,a.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,a.jsx)("div",{className:"flex justify-center items-center mb-4",children:(0,a.jsx)("div",{className:"flex items-center space-x-2",children:[1,2,3,4].map(e=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center transition-colors ".concat(e<r?"bg-primary dark:bg-[var(--brand-gold)] text-white dark:text-black":e===r?"bg-primary/20 dark:bg-[var(--brand-gold)]/20 text-primary dark:text-[var(--brand-gold)] border border-primary dark:border-[var(--brand-gold)]":"bg-muted dark:bg-neutral-800 text-muted-foreground dark:text-neutral-500"),children:e<r?(0,a.jsx)(P.A,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-label":"Check"}):(0,a.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:e})}),e<4&&(0,a.jsx)("div",{"data-testid":"progress-line",className:"w-8 sm:w-12 h-0.5 mx-1 transition-colors ".concat(e<r?"bg-primary dark:bg-[var(--brand-gold)]":"bg-muted dark:bg-neutral-700")})]},e))})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-xs sm:text-sm text-muted-foreground dark:text-neutral-400 mb-1",children:["Step ",r," of 4"]}),(0,a.jsx)("h3",{className:"text-sm sm:text-base font-medium text-primary dark:text-[var(--brand-gold)]",children:k[r-1]})]}),(0,a.jsx)("div",{className:"text-center mt-6 sm:mt-8",children:(0,a.jsx)("p",{className:"text-sm sm:text-base text-muted-foreground dark:text-neutral-400",children:w(r-1,null==t?void 0:t.hasExistingSubscription)},"desc-".concat(r))})]})}var R=t(97168),L=t(35169),V=t(92138),_=t(51154);function M(e){let{currentStep:r,isSubmitting:t,isCheckingSlug:s,slugAvailable:n,selectedPlan:l,existingData:i,onNextStep:d,onPreviousStep:c,onSubmitIntended:u}=e,m=(0,o.useRouter)();return 1===r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(R.$,{type:"button",variant:"outline",onClick:()=>m.push("/choose-role"),className:"cursor-pointer border-border text-foreground hover:bg-muted dark:border-neutral-700 dark:hover:bg-neutral-800 dark:text-neutral-200 group transition-all text-xs sm:text-sm h-9 sm:h-10",children:[(0,a.jsx)(L.A,{className:"mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:-translate-x-1 transition-transform"}),"Back"]}),(0,a.jsxs)(R.$,{type:"button",onClick:d,disabled:t,className:"cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black group transition-all text-xs sm:text-sm h-9 sm:h-10",children:["Next",(0,a.jsx)(V.A,{className:"ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:translate-x-1 transition-transform"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(R.$,{type:"button",variant:"outline",onClick:c,disabled:t,className:"cursor-pointer border-border text-foreground hover:bg-muted dark:border-neutral-700 dark:hover:bg-neutral-800 dark:text-neutral-200 group transition-all text-xs sm:text-sm h-9 sm:h-10",children:[(0,a.jsx)(L.A,{className:"mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:-translate-x-1 transition-transform"}),"Back"]}),r<4?(0,a.jsxs)(R.$,{type:"button",onClick:d,disabled:t||2===r&&(s||!0!==n),className:"cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black group transition-all text-xs sm:text-sm h-9 sm:h-10",children:["Next",(0,a.jsx)(V.A,{className:"ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:translate-x-1 transition-transform"})]}):(0,a.jsxs)(R.$,{type:"submit",className:"cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground dark:bg-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/90 dark:text-black relative overflow-hidden group transition-all text-xs sm:text-sm h-9 sm:h-10",disabled:t||!l,onClick:u,children:[(0,a.jsx)("span",{className:"relative z-10 flex items-center",children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 animate-spin"}),(null==i?void 0:i.hasExistingSubscription)?"Updating Profile...":(null==l?void 0:l.id)==="free"?"Creating Account...":"Starting Trial..."]}):(0,a.jsxs)(a.Fragment,{children:[(null==i?void 0:i.hasExistingSubscription)?"Complete Profile Setup":(null==l?void 0:l.id)==="free"?"Get Started for FREE":"Finish Setup & Start Trial"," ",(0,a.jsx)(V.A,{className:"w-4 h-4 sm:w-5 sm:h-5 ml-1 sm:ml-2 group-hover:translate-x-1 transition-transform"})]})}),(0,a.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-primary/0 via-white/10 to-primary/0 dark:from-[var(--brand-gold)]/0 dark:via-white/20 dark:to-[var(--brand-gold)]/0 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-in-out"})]})]})}function B(e){let{isLoading:r,message:t="Loading your profile data..."}=e;return r?(0,a.jsx)("div",{className:"absolute inset-0 bg-background/80 dark:bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,a.jsx)(_.A,{className:"w-8 h-8 animate-spin text-primary dark:text-[var(--brand-gold)]",role:"status","aria-label":"Loading"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground dark:text-neutral-400",children:t})]})}):null}var D=t(89852),F=t(23227),T=t(28883);function J(e){var r,t,s;let{form:n,isSubmitting:l,user:o}=e,d=(null==o||null==(r=o.app_metadata)?void 0:r.provider)==="google",c=(null==o||null==(t=o.app_metadata)?void 0:t.provider)==="email",u=(null==o||null==(s=o.app_metadata)?void 0:s.provider)==="phone";return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.zB,{control:n.control,name:"businessName",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(F.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Business Name"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(D.p,{placeholder:"My Awesome Business",...r,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:l}),(0,a.jsx)(F.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsx)(i.zB,{control:n.control,name:"email",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(T.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Contact Email",d&&(0,a.jsx)("span",{className:"text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full",children:"Google Account"}),c&&(0,a.jsx)("span",{className:"text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full",children:"Email Account"}),u&&(0,a.jsx)("span",{className:"text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full",children:"Mobile Account"})]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(D.p,{placeholder:"<EMAIL>",type:"email",...r,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:l}),(0,a.jsx)(T.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(i.C5,{})]})}})]})}var O=t(99828),U=t(71007),Y=t(17576),$=t(19420),q=t(38164),Z=t(43453),W=t(85339);function G(e){let{form:r,isSubmitting:t,slugAvailable:s,isCheckingSlug:n,setSlugToCheck:l,setSlugAvailable:o,user:d}=e,c=!!((null==d?void 0:d.phone)&&(null==d?void 0:d.phone.trim())!=="");return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.zB,{control:r.control,name:"memberName",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(U.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Your Name"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(D.p,{placeholder:"e.g., John Doe",...r,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:t}),(0,a.jsx)(U.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsx)(i.zB,{control:r.control,name:"title",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(Y.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Your Title/Designation"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(D.p,{placeholder:"e.g., Founder, Manager",...r,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:t}),(0,a.jsx)(Y.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsx)(i.zB,{control:r.control,name:"phone",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)($.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Primary Phone Number",c&&(0,a.jsx)("span",{className:"text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full",children:"Mobile Account"})]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(D.p,{placeholder:"e.g., **********",type:"tel",pattern:"[0-9]*",inputMode:"numeric",...r,onChange:e=>{let t=e.target.value.replace(/^\+91/,"");(t=t.replace(/\D/g,"")).length>10&&(t=t.slice(0,10)),r.onChange(t)},onKeyDown:e=>{let r=/^[0-9]$/.test(e.key),t=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);r||t||e.preventDefault()},className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:t}),(0,a.jsx)($.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsx)(i.zB,{control:r.control,name:"businessCategory",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(Y.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Business Category"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsx)(O.A,{value:r.value,onChange:r.onChange,placeholder:"Select a business category",disabled:t,className:"w-full bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 text-foreground transition-all rounded-lg"})}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsx)(i.zB,{control:r.control,name:"businessSlug",render:e=>{let{field:d}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Desired Card URL"]}),(0,a.jsx)("div",{className:"flex flex-col space-y-2",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center rounded-lg overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-muted dark:bg-neutral-800 px-3 py-3 flex items-center border-y border-l border-border dark:border-neutral-700 rounded-l-lg",children:[(0,a.jsx)(q.A,{className:"w-4 h-4 text-muted-foreground dark:text-neutral-400 mr-1"}),(0,a.jsx)("span",{className:"text-muted-foreground dark:text-neutral-400 text-sm whitespace-nowrap",children:"dukancard.in/"})]}),(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(i.MJ,{children:(0,a.jsx)(D.p,{placeholder:"your-business-name",...d,onChange:e=>{let r=e.target.value.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,"");d.onChange(r),r.length>=3&&/^[a-z0-9-]+$/.test(r)&&l&&o?(console.log("Client: Setting slug to check:",r),l(r),o(null)):o&&o(null)},className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 h-12 text-foreground transition-all rounded-none rounded-r-lg border-l-0 pl-3 pr-10",disabled:t})}),(0,a.jsxs)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center w-5 h-5",children:[n&&(0,a.jsx)(_.A,{className:"w-5 h-5 text-muted-foreground animate-spin"}),!n&&!0===s&&(0,a.jsx)(Z.A,{className:"w-5 h-5 text-green-500"}),!n&&!1===s&&(0,a.jsx)(W.A,{className:"w-5 h-5 text-red-500"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center h-5 px-1",children:[n&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Checking availability..."}),!n&&!0===s&&!r.formState.errors.businessSlug&&(0,a.jsxs)("p",{className:"text-xs text-green-500 flex items-center gap-1",children:[(0,a.jsx)(P.A,{className:"w-3 h-3"})," URL is available!"]})]})]})}),(0,a.jsx)(i.C5,{})]})}})]})}var X=t(95784),K=t(57340),H=t(4516);function Q(e){let{form:r,isSubmitting:t,availableLocalities:s=[],isPincodeLoading:n=!1,handlePincodeChange:l}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.zB,{control:r.control,name:"addressLine",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(K.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Address Line"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(D.p,{placeholder:"e.g., 123 Main Street, Building A",...r,className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg",disabled:t}),(0,a.jsx)(K.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"})]})}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsx)(i.zB,{control:r.control,name:"pincode",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Pincode"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(D.p,{placeholder:"e.g., 110001",type:"tel",pattern:"[0-9]*",inputMode:"numeric",...r,onChange:e=>{let t=e.target.value.replace(/\D/g,"");r.onChange(t),6===t.length&&l&&l(t)},onKeyDown:e=>{let r=/^[0-9]$/.test(e.key),t=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);r||t||e.preventDefault()},className:"bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 pr-10 h-12 text-foreground transition-all rounded-lg",disabled:t}),(0,a.jsx)(H.A,{className:"absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400"}),n&&(0,a.jsx)(_.A,{className:"absolute right-3 top-3.5 w-5 h-5 text-muted-foreground animate-spin"})]})}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(i.zB,{control:r.control,name:"city",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"City"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsx)(D.p,{placeholder:"Auto-filled from pincode",...r,className:"bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 h-12 transition-all rounded-lg cursor-not-allowed",disabled:!0,readOnly:!0})}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsx)(i.zB,{control:r.control,name:"state",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"State"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsx)(D.p,{placeholder:"Auto-filled from pincode",...r,className:"bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 h-12 transition-all rounded-lg cursor-not-allowed",disabled:!0,readOnly:!0})}),(0,a.jsx)(i.C5,{})]})}})]}),(0,a.jsx)(i.zB,{control:r.control,name:"locality",render:e=>{var r;let{field:n}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Locality/Area"]}),(0,a.jsxs)(X.l6,{onValueChange:n.onChange,value:null!=(r=n.value)?r:"",disabled:0===s.length||t,children:[(0,a.jsx)(i.MJ,{children:(0,a.jsx)(X.bq,{className:"w-full bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 h-12 text-foreground transition-all rounded-lg",children:(0,a.jsx)(X.yv,{placeholder:0===s.length?"Enter Pincode first":"Select your locality"})})}),(0,a.jsx)(X.gC,{className:"w-full border border-border dark:border-neutral-700 rounded-lg shadow-lg",children:s.map(e=>(0,a.jsx)(X.eb,{value:e,className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20",children:e},e))})]}),(0,a.jsx)(i.C5,{})]})}}),(0,a.jsx)(i.zB,{control:r.control,name:"businessStatus",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(F.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Business Status"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>r.onChange("online"),className:"flex-1 p-3 rounded-lg border transition-all ".concat("online"===r.value?"bg-primary/10 border-primary text-primary dark:bg-[var(--brand-gold)]/10 dark:border-[var(--brand-gold)] dark:text-[var(--brand-gold)]":"bg-background border-border text-muted-foreground hover:border-primary/50 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)]/50"),disabled:t,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Online"}),(0,a.jsx)("div",{className:"text-xs opacity-75",children:"Ready to serve customers"})]})}),(0,a.jsx)("button",{type:"button",onClick:()=>r.onChange("offline"),className:"flex-1 p-3 rounded-lg border transition-all ".concat("offline"===r.value?"bg-primary/10 border-primary text-primary dark:bg-[var(--brand-gold)]/10 dark:border-[var(--brand-gold)] dark:text-[var(--brand-gold)]":"bg-background border-border text-muted-foreground hover:border-primary/50 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)]/50"),disabled:t,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Offline"}),(0,a.jsx)("div",{className:"text-xs opacity-75",children:"Setting up business"})]})})]})}),(0,a.jsx)(i.C5,{})]})}})]})}var ee=t(81586),er=t(66474);function et(e){let{form:r,isSubmitting:t,existingData:n,selectedPlan:o,setSelectedPlan:d,showPlans:c,setShowPlans:u}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.zB,{control:r.control,name:"planId",render:e=>{let{field:r}=e;return(0,a.jsxs)(i.eI,{children:[(0,a.jsxs)(i.lR,{className:"text-foreground flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"w-4 h-4 text-primary dark:text-[var(--brand-gold)]"}),"Select Plan"]}),(0,a.jsx)(i.MJ,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{t||(null==n?void 0:n.hasExistingSubscription)||!u||u(!c)},className:"flex items-center justify-between w-full border rounded-lg px-4 py-3 text-left h-12 transition-all ".concat((null==n?void 0:n.hasExistingSubscription)?"bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 cursor-not-allowed opacity-75":"cursor-pointer bg-background border-border hover:border-primary hover:bg-background/80 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)] dark:hover:bg-neutral-800/80 text-foreground dark:text-white"," ").concat(t?"opacity-50":""),disabled:t||(null==n?void 0:n.hasExistingSubscription),children:[(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"w-5 h-5 text-muted-foreground dark:text-neutral-400"}),o?o.name:"Select a plan",(null==n?void 0:n.hasExistingSubscription)&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground dark:text-neutral-400",children:"(Current Plan)"})]}),!(null==n?void 0:n.hasExistingSubscription)&&(0,a.jsx)(er.A,{className:"w-5 h-5 text-muted-foreground dark:text-neutral-400 transition-transform ".concat(c?"rotate-180":"")})]}),(0,a.jsx)("input",{type:"hidden",...r,value:(null==o?void 0:o.id)||""})]})}),(0,a.jsx)(i.C5,{})]})}}),c&&!(null==n?void 0:n.hasExistingSubscription)&&(0,a.jsx)("div",{className:"bg-popover border border-border dark:bg-neutral-900 dark:border-[var(--brand-gold)]/30 rounded-lg shadow-lg overflow-hidden",children:m.kn.map((e,t,n)=>{let i=!e.available;return(0,a.jsxs)(s.Fragment,{children:[(0,a.jsx)("div",{"data-testid":"plan-item-".concat(e.id),onClick:()=>{!i&&d&&u&&(d(e),r.setValue("planId",e.id,{shouldValidate:!0}),u(!1))},"aria-disabled":i,tabIndex:i?-1:0,className:"p-4 transition-colors flex flex-col gap-1\n                    ".concat(i?"opacity-60 cursor-not-allowed bg-muted dark:bg-neutral-800":"cursor-pointer hover:bg-muted dark:hover:bg-neutral-800","\n                    ").concat(e.recommended?"border-l-4 border-primary dark:border-[var(--brand-gold)]":"","\n                    ").concat((null==o?void 0:o.id)===e.id?"bg-muted dark:bg-neutral-800":"","\n                  "),title:i?"This plan is coming soon and cannot be selected.":"",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-medium text-popover-foreground dark:text-white",children:[e.name,i&&(0,a.jsx)("span",{className:"ml-2 text-xs text-muted-foreground dark:text-neutral-400 font-semibold",children:"(Coming Soon)"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground dark:text-neutral-400",children:e.price})]}),(null==o?void 0:o.id)===e.id&&!i&&(0,a.jsx)("div",{className:"w-6 h-6 rounded-full bg-primary dark:bg-[var(--brand-gold)] flex items-center justify-center",children:(0,a.jsx)(P.A,{className:"w-4 h-4 text-white dark:text-black"})}),e.recommended&&(0,a.jsx)("span",{className:"text-xs bg-primary/10 text-primary dark:bg-[var(--brand-gold)]/20 dark:text-[var(--brand-gold)] px-3 py-1 rounded-full font-medium",children:"Recommended"})]})}),t<n.length-1&&(0,a.jsx)(l.w,{className:"bg-border dark:bg-[var(--brand-gold)]/20"})]},e.id)})}),(null==n?void 0:n.hasExistingSubscription)&&o&&(0,a.jsx)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center",children:(0,a.jsx)(Z.A,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:["You're already subscribed to ",o.name]}),(0,a.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-300",children:"Your current plan will continue as usual. You can manage your subscription from the dashboard."})]})]})}),(0,a.jsx)("div",{className:"p-5 bg-muted/50 border border-border dark:bg-neutral-900/50 dark:border-[var(--brand-gold)]/20 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 dark:bg-[var(--brand-gold)]/10 flex items-center justify-center",children:(0,a.jsx)(P.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-foreground dark:text-white text-lg",children:(null==n?void 0:n.hasExistingSubscription)?"Continue with Current Plan":(null==o?void 0:o.id)==="free"?"Free Forever Plan":"1 Month Free Trial"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground dark:text-neutral-400",children:(null==n?void 0:n.hasExistingSubscription)?"Complete your profile setup to access all features of your current plan.":(null==o?void 0:o.id)==="free"?"Get your business online instantly with our free plan. Upgrade anytime.":"Start your 30-day free trial of premium features by completing the setup."})]})]})})]})}function ea(){let{redirectSlug:e,message:r}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,g]=(0,s.useState)(!1),[f,v]=(0,s.useState)(null),{user:k}=function(){let e=(0,o.useRouter)(),r=(0,d.U)(),[t,a]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{var t;if("true"===u.env.PLAYWRIGHT_TESTING||window.navigator.userAgent.includes("Playwright"))return a({id:"test-user-id",email:"<EMAIL>",user_metadata:{name:"Test User"},created_at:"2024-01-01T00:00:00Z"});let s=await r.auth.getSession(),n=null==s||null==(t=s.data)?void 0:t.session;if((null==s?void 0:s.error)||!(null==n?void 0:n.user)){c.oR.error("Authentication error. Redirecting to login."),e.push("/login");return}a(n.user)})()},[e,r.auth]),{user:t}}(),{isLoadingExistingData:N,existingData:w}=function(e){let{user:r,form:t,setSelectedPlan:a}=e,[n,l]=(0,s.useState)(!0),[i,o]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{if(r){l(!0);try{let r=await x();if(r.error)console.error("Error fetching existing business profile data:",r.error),c.oR.error("Failed to load existing profile data");else if(r.data){var e,s,n,i;if(o(r.data),t){if(Object.entries(r.data).forEach(e=>{let[r,a]=e;"hasExistingSubscription"!==r&&"string"==typeof a&&""!==a.trim()&&t.setValue(r,a,{shouldValidate:!1})}),(null==(e=r.data)?void 0:e.hasExistingSubscription)&&(null==(s=r.data)?void 0:s.planId)){let e=m.kn.find(e=>{var t;return e.id===(null==(t=r.data)?void 0:t.planId)});e&&(a(e),t.setValue("planId",e.id,{shouldValidate:!1}))}}else if((null==(n=r.data)?void 0:n.hasExistingSubscription)&&(null==(i=r.data)?void 0:i.planId)){let e=m.kn.find(e=>{var t;return e.id===(null==(t=r.data)?void 0:t.planId)});e&&a(e)}}}catch(e){console.error("Unexpected error fetching existing data:",e),c.oR.error("Failed to load existing profile data")}finally{l(!1)}}})()},[r,t,a]),{isLoadingExistingData:n,existingData:i}}({user:k,form:null,setSelectedPlan:v}),[C,P]=(0,s.useState)(null),[R,L]=(0,s.useState)(""),{form:V,isSubmitting:_,currentStep:D,handleNextStep:F,handlePreviousStep:T,onSubmitHandler:O,setIsSubmitIntended:U}=function(e){let{redirectSlug:r,message:t,user:a,existingData:n,slugAvailable:l,selectedPlan:i}=e,d=(0,o.useSearchParams)(),[u,m]=(0,s.useTransition)(),[g,x]=(0,s.useState)(1),[f,v]=(0,s.useState)(r||null),[k,N]=(0,s.useState)(t||null),[w,C]=(0,s.useState)(!1),A=(0,b.mN)({resolver:(0,p.u)(j),mode:"onChange",defaultValues:{businessName:"",email:"",memberName:"",title:"",phone:"",businessCategory:"",businessSlug:"",addressLine:"",pincode:"",city:"",state:"",locality:"",businessStatus:"online",planId:""}});(0,s.useEffect)(()=>{if(!f){let e=d.get("redirect");if(e)v(e);else if(1){let e=localStorage.getItem("postOnboardingRedirect");e&&(v(e),localStorage.removeItem("postOnboardingRedirect"))}}if(!k){let e=d.get("message");if(e)N(e);else if(1){let e=localStorage.getItem("postOnboardingMessage");e&&(N(e),localStorage.removeItem("postOnboardingMessage"))}}},[d,f,k]),(0,s.useEffect)(()=>{(null==a?void 0:a.email)&&!A.getValues("email")&&A.setValue("email",a.email,{shouldValidate:!0})},[a,A]),(0,s.useEffect)(()=>{if(a&&!A.getValues("memberName")){var e,r;let t=(null==(e=a.user_metadata)?void 0:e.full_name)||(null==(r=a.user_metadata)?void 0:r.name)||"";t&&A.setValue("memberName",t,{shouldValidate:!0})}},[a,A]),(0,s.useEffect)(()=>{if((null==a?void 0:a.phone)&&!A.getValues("phone")){let e=(0,S.gV)(a.phone);e&&A.setValue("phone",e,{shouldValidate:!0})}},[a,A]),(0,s.useEffect)(()=>{var e;(null==a||null==(e=a.user_metadata)?void 0:e.display_name)&&!A.getValues("memberName")&&A.setValue("memberName",a.user_metadata.display_name,{shouldValidate:!0})},[a,A]);let z=(0,s.useCallback)(async()=>{let e=y[g-1],r=await A.trigger(e);if(2===g){if(!r)return;if(!0!==l)return void A.setError("businessSlug",{type:"manual",message:!1===l?"This URL slug is already taken.":"Please enter a valid slug and wait for check."})}else if(!r)return;C(!1),g<4&&x(g+1)},[g,A,l]),I=(0,s.useCallback)(()=>{C(!1),g>1&&x(g-1)},[g]),P=(0,s.useCallback)(e=>{if(!w)return;if(!i)return void c.oR.error("Please select a plan.");let r=new FormData;Object.entries(e).forEach(e=>{let[t,a]=e;null!=a&&r.append(t,a)}),r.set("planId",i.id),m(async()=>{let e=await h({formData:r,planId:i.id,redirectSlug:f,message:k});if(null==e?void 0:e.error){var t;c.oR.error("Onboarding failed: ".concat(e.error)),Object.entries(e.fieldErrors||{}).forEach(e=>{let[r,t]=e;t&&t.length>0&&A.setError(r,{message:t[0]})}),(null==(t=e.fieldErrors)?void 0:t.businessSlug)&&x(2)}else c.oR.success("Onboarding complete! Welcome aboard.")})},[w,i,f,k,A]);return{form:A,isSubmitting:u,currentStep:g,handleNextStep:z,handlePreviousStep:I,onSubmitHandler:P,setIsSubmitIntended:C}}({redirectSlug:e,message:r,user:k,existingData:w,slugAvailable:C,selectedPlan:f}),{isCheckingSlug:Y}=function(e){let{form:r,slugToCheck:t,setSlugAvailable:a}=e,[n,l]=(0,s.useTransition)(),i=(0,s.useCallback)(async e=>{if(console.log("Client: Performing slug check for:",e),!e||e.length<3||!/^[a-z0-9-]+$/.test(e)){console.log("Client: Slug failed basic validation:",e),a(null);return}l(async()=>{try{console.log("Client: Calling checkSlugAvailability for:",e);let{available:t}=await z(e);console.log("Client: Slug availability result:",{slug:e,available:t}),a(t),t?(console.log("Client: Clearing errors for available slug:",e),r.clearErrors("businessSlug")):(console.log("Client: Setting error for unavailable slug:",e),r.setError("businessSlug",{type:"manual",message:"This URL slug is already taken."}))}catch(e){console.error("Client: Error checking slug availability:",e),a(!1),r.setError("businessSlug",{type:"manual",message:"Error checking slug availability. Please try again."})}})},[r,a,l]),o=(0,s.useMemo)(()=>A()(e=>i(e),500),[i]);return(0,s.useEffect)(()=>(t&&(console.log("useSlugAvailability: Triggering check for:",t),o(t)),()=>{o.cancel()}),[t,o]),{isCheckingSlug:n}}({form:V,slugToCheck:R,setSlugAvailable:P}),{isPincodeLoading:$,availableLocalities:q,handlePincodeChange:Z}=function(e){let{form:r,initialPincode:t,initialLocality:a}=e,[n,l]=(0,s.useState)(!1),[i,o]=(0,s.useState)([]),d=(0,s.useCallback)(async e=>{if(6!==e.length)return;l(!0),o([]),r.setValue("locality",""),r.setValue("city",""),r.setValue("state","");let t=await (0,I.t)(e);l(!1),t.error?c.oR.error(t.error):t.city&&t.state&&t.localities&&t.localities.length>0&&(r.setValue("city",t.city,{shouldValidate:!0}),r.setValue("state",t.state,{shouldValidate:!0}),o(t.localities),1===t.localities.length&&r.setValue("locality",t.localities[0],{shouldValidate:!0,shouldDirty:!0}),c.oR.success("City and State auto-filled. Please select your locality."))},[r]);return(0,s.useEffect)(()=>{t&&6===t.length&&(async e=>{l(!0),o([]);try{let t=await (0,I.t)(e);t.error?(c.oR.error("Failed to fetch details for pincode ".concat(e,": ").concat(t.error)),o([])):t.city&&t.state&&t.localities&&t.localities.length>0?(r.setValue("city",t.city,{shouldValidate:!0}),r.setValue("state",t.state,{shouldValidate:!0}),o(t.localities),a&&(t.localities.some(e=>e.toLowerCase()===a.toLowerCase())?r.setValue("locality",a,{shouldValidate:!0,shouldDirty:!0}):(r.setValue("locality","",{shouldValidate:!0,shouldDirty:!0}),c.oR.warning('The locality "'.concat(a,'" is not available for pincode ').concat(e,". Please select a valid locality."))))):(o([]),c.oR.warning("No localities found for pincode ".concat(e,".")),a&&r.setValue("locality","",{shouldValidate:!0,shouldDirty:!0}))}catch(e){console.error("Error fetching pincode details:",e),c.oR.error("An unexpected error occurred while fetching pincode details."),o([]),a&&r.setValue("locality","",{shouldValidate:!0,shouldDirty:!0})}finally{l(!1)}})(t)},[t,a,r]),{isPincodeLoading:n,availableLocalities:i,handlePincodeChange:d}}({form:V,initialPincode:null==w?void 0:w.pincode,initialLocality:null==w?void 0:w.locality});return(0,s.useEffect)(()=>{(async()=>{if(2===D&&!N&&(null==w?void 0:w.businessSlug)){let e=w.businessSlug.trim();e.length>=3&&/^[a-z0-9-]+$/.test(e)&&(console.log("Auto-checking pre-filled slug:",e),L(e))}})()},[D,N,w]),(0,a.jsxs)("div",{className:"w-full flex items-center justify-center relative",children:[(0,a.jsx)("div",{className:"fixed -top-24 -left-24 w-64 h-64 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl pointer-events-none"}),(0,a.jsx)("div",{className:"fixed -bottom-32 -right-32 w-96 h-96 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl pointer-events-none"}),(0,a.jsx)("div",{className:"w-full max-w-[90%] sm:max-w-md md:max-w-lg",children:(0,a.jsxs)(n.Zp,{className:"w-full border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl shadow-lg relative overflow-hidden backdrop-blur-sm",children:[(0,a.jsx)(B,{isLoading:N}),(0,a.jsx)(E,{currentStep:D,existingData:w,_isLoadingExistingData:N}),(0,a.jsx)(n.Wu,{className:"p-0",children:(0,a.jsx)(i.lV,{...V,children:(0,a.jsxs)("form",{onSubmit:V.handleSubmit(O),className:"space-y-6",children:[(0,a.jsx)("div",{className:"space-y-6",children:(()=>{switch(D){case 1:return(0,a.jsx)(J,{form:V,isSubmitting:_,user:k,existingData:w});case 2:return(0,a.jsx)(G,{form:V,isSubmitting:_,user:k,existingData:w,slugAvailable:C,isCheckingSlug:Y,setSlugToCheck:L,setSlugAvailable:P});case 3:return(0,a.jsx)(Q,{form:V,isSubmitting:_,user:k,existingData:w,availableLocalities:q,isPincodeLoading:$,handlePincodeChange:Z});case 4:return(0,a.jsx)(et,{form:V,isSubmitting:_,user:k,existingData:w,selectedPlan:f,setSelectedPlan:v,showPlans:t,setShowPlans:g});default:return null}})()},D),(0,a.jsx)(l.w,{className:"bg-border/50 dark:bg-neutral-700/50 my-6 sm:my-8"}),(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsx)(M,{currentStep:D,isSubmitting:_,isCheckingSlug:Y,slugAvailable:C,selectedPlan:f,existingData:w,onNextStep:F,onPreviousStep:T,onSubmitIntended:()=>U(!0)})})]})})})]})})]})}},48347:(e,r,t)=>{"use strict";t.d(r,{LE:()=>s,oX:()=>n});var a=t(55594);let s=a.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:a.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),a.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character");let n=a.z.object({mobile:s,password:a.z.string().trim().min(1,{message:"Password is required"})})},53999:(e,r,t)=>{"use strict";t.d(r,{M0:()=>c,Yq:()=>u,cn:()=>n,gV:()=>l,gY:()=>d,kY:()=>i,vA:()=>o,vv:()=>m});var a=t(52596),s=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}function l(e){if(!e)return null;let r=e.trim();return(r.startsWith("+91")?r=r.substring(3):12===r.length&&r.startsWith("91")&&(r=r.substring(2)),/^\d{10}$/.test(r))?r:null}function i(e){if(!e||e.length<4)return"Invalid Phone";let r=e.substring(0,2),t=e.substring(e.length-2),a="*".repeat(e.length-4);return"".concat(r).concat(a).concat(t)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let r=e.split("@"),t=r[0],a=r[1];if(t.length<=2||a.length<=2||!a.includes("."))return"Email Hidden";let s=t.substring(0,2)+"*".repeat(t.length-2),n=a.split("."),l=n[0],i=n.slice(1).join("."),o=l.substring(0,2)+"*".repeat(l.length-2);return"".concat(s,"@").concat(o,".").concat(i)}function d(e){if(null==e||isNaN(e))return"0";let r=Math.abs(e),t=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(r<1e5)return r>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let a=t.length-1;a>=0;a--)if(r>=t[a].value)return(e/t[a].value).toFixed(1).replace(/\.0$/,"")+t[a].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let t={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return r&&(t.hour="2-digit",t.minute="2-digit",t.hour12=!0),e.toLocaleString("en-IN",t)}function m(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:r,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(t){return"".concat(r," ").concat(e.toFixed(2))}}},55860:(e,r,t)=>{"use strict";t.d(r,{Nh:()=>s,kn:()=>n});var a=t(4327);let s=e=>a.NB.map(r=>(function(e,r){let t="enterprise"===e.id,a=e.pricing.monthly,s=e.pricing.yearly;return{id:e.id,name:"".concat(e.name," Plan"),razorpayPlanIds:{monthly:e.razorpayPlanIds.monthly||null,yearly:e.razorpayPlanIds.yearly||null},price:t?"Contact Sales":"monthly"===r?"₹".concat(e.pricing.monthly.toLocaleString("en-IN")):"₹".concat(e.pricing.yearly.toLocaleString("en-IN")),yearlyPrice:t?"Contact Sales":"₹".concat(e.pricing.yearly.toLocaleString("en-IN")),period:t?"":"monthly"===r?"/month":"/year",savings:t?void 0:"yearly"===r?"Save ₹".concat((t?0:12*a-s).toLocaleString("en-IN")):void 0,description:e.description,features:e.features.map(e=>{if("Product Listings"===e.name&&e.included){let r="unlimited"===e.limit?"Unlimited":e.limit;return"Product/Service Listings (up to ".concat(r,")")}return e.included?e.name:"❌ ".concat(e.name)}),button:t?"Contact Sales":"Subscribe Now",available:!0,featured:"free"===e.id||"basic"===e.id||"growth"===e.id||"pro"===e.id,recommended:e.recommended||!1,mostPopular:e.recommended||!1}})(r,e)),n=s("monthly")},60823:(e,r,t)=>{"use strict";t.d(r,{AM:()=>l,Wv:()=>i,hl:()=>o});var a=t(95155);t(12115);var s=t(98176),n=t(53999);function l(e){let{...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"popover",...r})}function i(e){let{...r}=e;return(0,a.jsx)(s.l9,{"data-slot":"popover-trigger",...r})}function o(e){let{className:r,align:t="center",sideOffset:l=4,...i}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsx)(s.UC,{"data-slot":"popover-content",align:t,sideOffset:l,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",r),...i})})}},75168:(e,r,t)=>{"use strict";t.d(r,{U:()=>s});var a=t(20067);function s(){let e="https://rnjolcoecogzgglnblqn.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";return e&&r?(0,a.createBrowserClient)(e,r):(console.error("Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined."),(0,a.createBrowserClient)("",""))}},76037:(e,r,t)=>{"use strict";t.d(r,{w:()=>l});var a=t(95155);t(12115);var s=t(87489),n=t(53999);function l(e){let{className:r,orientation:t="horizontal",decorative:l=!0,...i}=e;return(0,a.jsx)(s.b,{"data-slot":"separator-root",decorative:l,orientation:t,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",r),...i})}},82714:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var a=t(95155);t(12115);var s=t(40968),n=t(53999);function l(e){let{className:r,...t}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},85433:(e,r,t)=>{Promise.resolve().then(t.bind(t,43026))},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>c});var a=t(95155);t(12115);var s=t(53999);function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},95784:(e,r,t)=>{"use strict";t.d(r,{TR:()=>x,bq:()=>m,eb:()=>b,gC:()=>g,l6:()=>d,s3:()=>c,yv:()=>u});var a=t(95155);t(12115);var s=t(43433),n=t(66474),l=t(5196),i=t(47863),o=t(53999);function d(e){let{...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"select",...r})}function c(e){let{...r}=e;return(0,a.jsx)(s.YJ,{"data-slot":"select-group",...r})}function u(e){let{...r}=e;return(0,a.jsx)(s.WT,{"data-slot":"select-value",...r})}function m(e){let{className:r,size:t="default",children:l,...i}=e;return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",r),...i,children:[l,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function g(e){let{className:r,children:t,position:n="popper",...l}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:n,...l,children:[(0,a.jsx)(p,{}),(0,a.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function x(e){let{className:r,...t}=e;return(0,a.jsx)(s.JU,{"data-slot":"select-label",className:(0,o.cn)("text-muted-foreground px-2 py-1.5 text-xs",r),...t})}function b(e){let{className:r,children:t,...n}=e;return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",r),...n,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(l.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:t})]})}function p(e){let{className:r,...t}=e;return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}function h(e){let{className:r,...t}=e;return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>o,r:()=>i});var a=t(95155);t(12115);var s=t(99708),n=t(74466),l=t(53999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:n,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,l.cn)(i({variant:t,size:n,className:r})),...d})}},99828:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var a=t(95155),s=t(12115),n=t(10081),l=t(5196),i=t(53999),o=t(97168),d=t(44895),c=t(60823),u=t(88945);function m(e){var r;let{value:t,onChange:m,placeholder:g="Select a category...",className:x,disabled:b=!1}=e,[p,h]=s.useState(!1);return(0,a.jsxs)(c.AM,{open:p,onOpenChange:h,children:[(0,a.jsx)(c.Wv,{asChild:!0,children:(0,a.jsxs)(o.$,{variant:"outline",role:"combobox","aria-expanded":p,className:(0,i.cn)("w-full justify-between h-12 text-sm",x),disabled:b,children:[t?null==(r=u.qW.find(e=>e.name===t))?void 0:r.name:g,(0,a.jsx)(n.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,a.jsx)(c.hl,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:(0,a.jsxs)(d.uB,{children:[(0,a.jsx)(d.G7,{placeholder:"Search category...",className:"h-9 border-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"}),(0,a.jsxs)(d.oI,{className:"max-h-[300px]",children:[(0,a.jsx)(d.xL,{children:"No category found."}),(0,a.jsx)(d.L$,{children:u.qW.map(e=>(0,a.jsxs)(d.h_,{value:e.name,onSelect:e=>{m(e===t?"":e),h(!1)},children:[(0,a.jsx)(e.icon,{className:"mr-2 h-4 w-4"}),e.name,(0,a.jsx)(l.A,{className:(0,i.cn)("ml-auto h-4 w-4",t===e.name?"opacity-100":"opacity-0")})]},e.slug))})]})]})})]})}},99840:(e,r,t)=>{"use strict";t.d(r,{Cf:()=>m,Es:()=>x,HM:()=>c,L3:()=>b,c7:()=>g,lG:()=>i,rr:()=>p,zM:()=>o});var a=t(95155);t(12115);var s=t(45821),n=t(54416),l=t(53999);function i(e){let{...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"dialog",...r})}function o(e){let{...r}=e;return(0,a.jsx)(s.l9,{"data-slot":"dialog-trigger",...r})}function d(e){let{...r}=e;return(0,a.jsx)(s.ZL,{"data-slot":"dialog-portal",...r})}function c(e){let{...r}=e;return(0,a.jsx)(s.bm,{"data-slot":"dialog-close",...r})}function u(e){let{className:r,...t}=e;return(0,a.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",r),...t})}function m(e){let{className:r,children:t,hideClose:i=!1,...o}=e;return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(u,{}),(0,a.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",r),...o,children:[t,!i&&(0,a.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function g(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",r),...t})}function x(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",r),...t})}function b(e){let{className:r,...t}=e;return(0,a.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",r),...t})}function p(e){let{className:r,...t}=e;return(0,a.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",r),...t})}}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,2290,6671,375,5152,7665,1884,67,6199,4577,221,4081,9417,5864,4327,4490,8441,1684,7358],()=>r(85433)),_N_E=e.O()}]);