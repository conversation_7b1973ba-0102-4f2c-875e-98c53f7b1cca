"use strict";exports.id=7249,exports.ids=[7249],exports.modules={47249:(e,s,i)=>{i.a(e,async(e,r)=>{try{i.d(s,{handleSubscriptionExpired:()=>l});var t=i(32032),n=i(65193),o=i(28485),a=i(94230),c=e([o]);async function l(e,s,i){let r=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription expired: ${c}`);let l=(0,n.extractWebhookTimestamp)(e);r={subscriptionId:c,eventType:"subscription.expired",eventId:i||`expired_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let p=await o.webhookProcessor.processWebhookEvent(r);if(!p.shouldProcess)return{success:p.success,message:p.message};let u=await (0,t.createClient)(),{data:d,error:_}=await u.from("payment_subscriptions").select("subscription_status, plan_id, business_profile_id").eq("razorpay_subscription_id",c).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${c}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!d)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${c}, skipping expiry processing`),{success:!0,message:"No subscription found to expire"};console.log(`[RAZORPAY_WEBHOOK] Transitioning expired subscription ${c} to free plan`);let b=new Date().toISOString(),m=await (0,a.M)({subscription_id:c,business_profile_id:d.business_profile_id,subscription_status:"active",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",subscription_start_date:b,subscription_expiry_time:null,subscription_charge_time:null,cancelled_at:b,razorpay_subscription_id:null,razorpay_customer_id:null,razorpay_plan_id:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,updated_at:b}});if(m.success)return await o.webhookProcessor.markEventAsSuccess(r.eventId,"Subscription expired and transitioned to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully transitioned subscription ${c} to free plan`),{success:!0,message:"Subscription expired and transitioned to free plan"};return await o.webhookProcessor.markEventAsFailed(r.eventId,m.message),console.error(`[RAZORPAY_WEBHOOK] Failed to transition subscription ${c} to free plan:`,m.message),m}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription expired:",e),{success:!1,message:`Error handling subscription expired: ${e instanceof Error?e.message:String(e)}`}}}o=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})}};