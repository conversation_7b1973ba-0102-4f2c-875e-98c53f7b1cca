"use strict";exports.id=3106,exports.ids=[3106],exports.modules={1119:(e,t,a)=>{a.d(t,{default:()=>n});var s=a(60687),r=a(71463),l=a(9904);function n(){return(0,s.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black",children:[(0,s.jsx)("div",{className:"w-full py-6 mt-6",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-3 items-start",children:[(0,s.jsx)(r.<PERSON>,{className:"h-12 w-full md:w-[140px]"}),(0,s.jsxs)("div",{className:"flex-1 w-full flex flex-col md:flex-row gap-3",children:[(0,s.jsx)(r.E,{className:"h-12 flex-1"}),(0,s.jsx)(r.E,{className:"h-12 w-full md:w-[200px]"}),(0,s.jsx)(r.E,{className:"h-12 w-full md:w-[120px]"})]})]})})})}),(0,s.jsxs)("div",{className:"mb-6 container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)(r.E,{className:"h-5 w-36"}),(0,s.jsx)(r.E,{className:"h-4 w-24"})]}),(0,s.jsx)("div",{className:"flex gap-2 overflow-hidden",children:Array.from({length:5}).map((e,t)=>(0,s.jsx)(r.E,{className:"h-20 w-20 flex-shrink-0 rounded-xl"},t))})]}),(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)(r.E,{className:"h-10 w-64 rounded-xl"})}),(0,s.jsx)("div",{className:"container mx-auto px-4 mb-4",children:(0,s.jsx)(r.E,{className:"h-12 w-full rounded-lg"})}),(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsx)(l.A,{})})]})}},3018:(e,t,a)=>{a.d(t,{Fc:()=>d,TN:()=>o,XL:()=>i});var s=a(60687);a(43210);var r=a(24224),l=a(96241);let n=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...a}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(n({variant:t}),e),...a})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,l.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},15879:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]])},23026:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},23608:(e,t,a)=>{a.d(t,{tV:()=>s});function s(e){switch(e){case"newest":default:return"created_desc";case"name_asc":return"name_asc";case"name_desc":return"name_desc";case"price_low":return"price_asc";case"price_high":return"price_desc"}}},24232:(e,t,a)=>{a.d(t,{Ie:()=>c,KC:()=>o,OL:()=>r,b2:()=>u,dv:()=>p,ho:()=>s,lX:()=>i,ry:()=>d,u0:()=>l,v0:()=>n});let s="businessName",r="businessSort",l="productName",n="productSort",d="productType",i="pincode",o="city",c="locality",u="view",p="category"},37857:(e,t,a)=>{a.d(t,{A:()=>d});var s=a(60687),r=a(43210),l=a(77882),n=a(70319);function d({businesses:e}){let t=(0,r.useRef)(null);return(0,s.jsx)(l.P.div,{ref:t,className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:e.map((e,t)=>(0,s.jsx)(n.A,{business:e,index:t},e.id))})}},57637:(e,t,a)=>{a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\discover\\\\ModernResultsSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx","default")},63974:(e,t,a)=>{a.d(t,{TR:()=>f,bq:()=>p,eb:()=>g,gC:()=>m,l6:()=>o,s3:()=>c,yv:()=>u});var s=a(60687);a(43210);var r=a(28695),l=a(78272),n=a(13964),d=a(3589),i=a(96241);function o({...e}){return(0,s.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(r.YJ,{"data-slot":"select-group",...e})}function u({...e}){return(0,s.jsx)(r.WT,{"data-slot":"select-value",...e})}function p({className:e,size:t="default",children:a,...n}){return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[a,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:a="popper",...l}){return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...l,children:[(0,s.jsx)(h,{}),(0,s.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(x,{})]})})}function f({className:e,...t}){return(0,s.jsx)(r.JU,{"data-slot":"select-label",className:(0,i.cn)("text-muted-foreground px-2 py-1.5 text-xs",e),...t})}function g({className:e,children:t,...a}){return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function h({className:e,...t}){return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(d.A,{className:"size-4"})})}function x({className:e,...t}){return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}},64398:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},67760:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},72185:(e,t,a)=>{a.d(t,{J:()=>m,u:()=>f});var s=a(60687),r=a(43210),l=a(16189),n=a(6475);let d=(0,n.createServerReference)("40edb48823df0fd876d80ab1b1ef92ac50df45d091",n.callServer,void 0,n.findSourceMapURL,"searchDiscoverCombined");var i=a(23608),o=a(24232);let c=(0,n.createServerReference)("4027ebed4ed3330c943c58b278d97ba413e3a31d66",n.callServer,void 0,n.findSourceMapURL,"fetchMoreBusinessCardsCombined"),u=(0,n.createServerReference)("4018b43028809df977ce7bb88f96c0d45dd8e2c788",n.callServer,void 0,n.findSourceMapURL,"fetchMoreProductsCombined"),p=(0,r.createContext)(void 0);function m({children:e}){let t=(0,l.useSearchParams)();t.get(o.ho),t.get(o.lX),t.get(o.KC),t.get(o.Ie);let a=t.get(o.dv)||null,n=t.get(o.b2)||"products",m=t.get(o.OL)||"created_desc",f=t.get(o.v0)||"newest",g=t.get("sortBy");g&&("cards"===n?m=g:"products"===n&&(f=g));let h=t.get(o.ry)||"all",[x,v]=(0,r.useState)(n),[y,b]=(0,r.useState)(m),[w,j]=(0,r.useState)(a),[N,S]=(0,r.useState)(!1),[C,A]=(0,r.useState)(!1),[_,M]=(0,r.useState)(null),[k,P]=(0,r.useState)(h),[T,z]=(0,r.useState)(f),[I,E]=(0,r.useState)(null),[X,F]=(0,r.useState)([]),[L,R]=(0,r.useState)([]),[V,U]=(0,r.useState)(1),[B,q]=(0,r.useState)(!1),[K,D]=(0,r.useState)(0),[J,H]=(0,r.useState)(!1),{handleBusinessSortChange:O,handleBusinessSearch:W,loadMoreBusinesses:Z}=function(e,t,a,s,n,i,u,p,m,f,g,h){let x=(0,l.useSearchParams)(),[,v]=(0,r.useTransition)();return{handleBusinessSortChange:r=>{if(r!==h&&(m(r),t(!0),"cards"===e)){let l=x.get(o.ho)||null,c=x.get(o.lX)||null,m=x.get(o.Ie)||null;"_any"===m&&(m=""),v(async()=>{let o=await d({businessName:l,pincode:c,locality:m,viewType:e,page:1,limit:20,businessSort:r});o.data?(a(o.data),s(o.data.isAuthenticated),o.data.businesses&&n(o.data.businesses),i(o.data.hasMore),u(o.data.totalCount),p(1)):f(o.error||"Failed to fetch results."),t(!1)})}},handleBusinessSearch:r=>{if("cards"!==e)return;if(t(!0),!r){let r=x.get(o.lX)||null,l=x.get(o.Ie)||null;"_any"===l&&(l=""),v(async()=>{await new Promise(e=>setTimeout(e,100));let o=await d({businessName:null,pincode:r,locality:l,viewType:e,page:1,limit:20,businessSort:h});o.data?(a(o.data),s(o.data.isAuthenticated),o.data.businesses&&n(o.data.businesses),i(o.data.hasMore),u(o.data.totalCount),p(1)):f(o.error||"Failed to fetch results."),t(!1)});return}let l=x.get(o.lX)||null,c=x.get(o.Ie)||null;"_any"===c&&(c=""),f(null),n([]),p(1),i(!1),u(0),v(async()=>{await new Promise(e=>setTimeout(e,100));let o=await d({businessName:r,pincode:l,locality:c,viewType:e,page:1,limit:20,businessSort:h});o.data?(a(o.data),s(o.data.isAuthenticated),o.data.businesses&&n(o.data.businesses),i(o.data.hasMore),u(o.data.totalCount),p(1)):f(o.error||"Failed to fetch results."),t(!1)})},loadMoreBusinesses:async(e,t,a)=>{if(t)return;let s=x.get(o.ho)||null,r=x.get(o.lX)||null,l=x.get(o.Ie)||null;"_any"===l&&(l=""),a(!0);try{await new Promise(e=>setTimeout(e,300));let t=await c({businessName:s,pincode:r,locality:l,page:e,limit:20,sortBy:h});if(!t.data||!t.data.businesses)return i(!1),!1;if(!(t.data.businesses.length>0))return i(!1),!1;{let a=t.data.businesses,s=new Set(g.map(e=>e.id)),r=a.filter(e=>!s.has(e.id));if(!(r.length>0))return console.warn("All new businesses were duplicates, stopping infinite scroll"),i(!1),!1;{let a=[...g,...r];return n(a),i(t.data.hasMore),p(e),!0}}}catch(e){return console.error("Error loading more businesses:",e),i(!1),!1}finally{a(!1)}}}}(x,S,E,H,F,q,D,U,b,M,X,y),{handleProductSortChange:Y,handleProductSearch:G,handleProductFilterChange:Q,loadMoreProducts:$}=function(e,t,a,s,n,c,p,m,f,g,h,x,v,y,b){let w=(0,l.useSearchParams)(),[,j]=(0,r.useTransition)();return{handleProductSortChange:r=>{if(r!==y){f(r),t(!0);let l=(0,i.tV)(r);if("products"===e){let r=w.get(o.u0)||null,i=w.get(o.lX)||null,u=w.get(o.Ie)||null;"_any"===u&&(u=""),j(async()=>{let o=await d({businessName:null,productName:r,pincode:i,locality:u,viewType:e,page:1,limit:20,productSort:l,productType:"all"===b?null:b});o.data?(a(o.data),s(o.data.isAuthenticated),"products"===e&&o.data.products&&n(o.data.products),c(o.data.hasMore),p(o.data.totalCount),m(1)):h(o.error||"Failed to fetch results."),t(!1)})}}},handleProductSearch:r=>{if("products"!==e)return;if(t(!0),!r){let r=w.get(o.lX)||null,l=w.get(o.Ie)||null;"_any"===l&&(l=""),j(async()=>{await new Promise(e=>setTimeout(e,100));let o=(0,i.tV)(y),u=await d({businessName:null,productName:null,pincode:r,locality:l,viewType:e,page:1,limit:20,productSort:o,productType:"all"===b?null:b});u.data?(a(u.data),s(u.data.isAuthenticated),"products"===e&&u.data.products&&n(u.data.products),c(u.data.hasMore),p(u.data.totalCount),m(1)):h(u.error||"Failed to fetch results."),t(!1)});return}let l=w.get(o.lX)||null,u=w.get(o.Ie)||null;"_any"===u&&(u=""),h(null),n([]),m(1),c(!1),p(0),j(async()=>{await new Promise(e=>setTimeout(e,100));let o=(0,i.tV)(y),f=await d({businessName:null,productName:r,pincode:l,locality:u,viewType:e,page:1,limit:20,productSort:o,productType:"all"===b?null:b});f.data?(a(f.data),s(f.data.isAuthenticated),"products"===e&&f.data.products&&n(f.data.products),c(f.data.hasMore),p(f.data.totalCount),m(1)):h(f.error||"Failed to fetch results."),t(!1)})},handleProductFilterChange:r=>{if(r!==b&&(g(r),t(!0),"products"===e)){let l=w.get(o.u0)||null,u=w.get(o.lX)||null,f=w.get(o.Ie)||null;"_any"===f&&(f=""),j(async()=>{let o=(0,i.tV)(y),g=await d({businessName:null,productName:l,pincode:u,locality:f,viewType:e,page:1,limit:20,productSort:o,productType:"all"===r?null:r});g.data?(a(g.data),s(g.data.isAuthenticated),"products"===e&&g.data.products&&n(g.data.products),c(g.data.hasMore),p(g.data.totalCount),m(1)):h(g.error||"Failed to fetch results."),t(!1)})}},loadMoreProducts:async(e,t,a)=>{if(t)return;let s=w.get(o.u0)||null,r=w.get(o.lX)||null,l=w.get(o.Ie)||null;"_any"===l&&(l=""),a(!0);try{await new Promise(e=>setTimeout(e,300));let t=(0,i.tV)(y),a=await u({businessName:null,productName:s,pincode:r,locality:l,page:e,limit:20,productSort:t,productType:"all"===b?null:b});if(!a.data||!a.data.products)return c(!1),!1;if(!(a.data.products.length>0))return c(!1),!1;{let t=a.data.products,s=new Set(x.map(e=>e.id)),r=t.filter(e=>!s.has(e.id));if(!(r.length>0))return console.warn("All new products were duplicates, stopping infinite scroll"),c(!1),!1;{let t=[...x,...r];return n(t),c(a.data.hasMore),m(e),!0}}}catch(e){return console.error("Error loading more products:",e),c(!1),!1}finally{a(!1)}}}}(x,S,E,H,R,q,D,U,z,P,M,L,0,T,k),{isPending:ee,handleViewChange:et,performSearch:ea,loadMore:es}=function(e,t,a,s,n,i,c,u,p,m,f,g,h,x,v){let y=(0,l.useSearchParams)(),[b,w]=(0,r.useTransition)(),j=async(t,a,s,r,l)=>{if(r||b)return;let n=s+1;"cards"===e?await t(n,r,l):await a(n,r,l)};return{isPending:b,handleViewChange:r=>{if(r!==e){t(r),a(!0),"products"===r?c([]):i([]);let e=y.get(o.ho)||null,l=y.get(o.u0)||null,b=y.get(o.lX)||null,j=y.get(o.KC)||null,N=y.get(o.Ie)||null;"_any"===N&&(N=""),w(async()=>{try{await new Promise(e=>setTimeout(e,100)),"products"===r?i([]):c([]);let t=await d({businessName:"cards"===r?e:null,productName:"products"===r?l:null,pincode:b,city:j,locality:N,viewType:r,page:1,limit:20,businessSort:g,productSort:h,productType:"products"===r&&"all"!==x?x:null,category:v});t.data?(s(t.data),n(t.data.isAuthenticated),"cards"===r&&t.data.businesses?i(t.data.businesses):"products"===r&&t.data.products&&c(t.data.products),u(t.data.hasMore),p(t.data.totalCount),m(1)):f(t.error||"Failed to fetch results.")}catch(e){console.error("Error changing view:",e),f("An unexpected error occurred.")}finally{a(!1)}})}},performSearch:t=>{a(!0),f(null),"cards"===e?i([]):c([]),m(1),u(!1),p(0);let{businessName:r,pincode:l,city:o,locality:y,category:b}=t;w(async()=>{try{await new Promise(e=>setTimeout(e,100));let t=await d({businessName:"cards"===e?r:null,productName:"products"===e?r:null,pincode:l,city:o,locality:"_any"===y?"":y,category:void 0!==b?b:v,viewType:e,page:1,limit:20,businessSort:g,productSort:h,productType:"products"===e&&"all"!==x?x:null});t.data?(s(t.data),n(t.data.isAuthenticated),"cards"===e&&t.data.businesses?i(t.data.businesses):"products"===e&&t.data.products&&c(t.data.products),u(t.data.hasMore),p(t.data.totalCount)):f(t.error||"Failed to fetch results.")}catch(e){console.error("Unexpected error in performSearch:",e),f("An unexpected error occurred. Please try again.")}finally{a(!1)}})},loadMore:j}}(x,v,S,E,H,F,R,q,D,U,M,y,T,k,w),er=async()=>{await es(Z,$,V,C,A)};return(0,s.jsx)(p.Provider,{value:{viewType:x,sortBy:y,selectedCategory:w,isSearching:N,isPending:ee,isLoadingMore:C,searchError:_,productFilterBy:k,productSortBy:T,searchResult:I,businesses:X,products:L,currentPage:V,hasMore:B,totalCount:K,isAuthenticated:J,performSearch:ea,handleViewChange:et,handleBusinessSortChange:O,handleBusinessSearch:W,handleProductSearch:G,handleProductSortChange:Y,handleProductFilterChange:Q,handleCategoryChange:e=>{j(e),U(1),q(!1);let a=t.get(o.ho),s=t.get(o.lX);ea({businessName:a,pincode:s,city:t.get(o.KC),locality:t.get(o.Ie),category:e})},loadMore:er},children:e})}function f(){let e=(0,r.useContext)(p);if(void 0===e)throw Error("useDiscoverContext must be used within a DiscoverProvider");return e}}};