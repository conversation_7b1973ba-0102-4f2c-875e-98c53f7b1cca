(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[864],{3159:(e,t,o)=>{"use strict";o.d(t,{Ay:()=>g});var r=o(39249),n=o(12115),i=o(76879),a=o.n(i);function s(e,t,o,r,n){void 0===n&&(n=0);var i=f(t.width,t.height,n),a=i.width,s=i.height;return{x:c(e.x,a,o.width,r),y:c(e.y,s,o.height,r)}}function c(e,t,o,r){var n=t*r/2-o/2;return m(e,-n,n)}function p(e,t){return Math.sqrt(Math.pow(e.y-t.y,2)+Math.pow(e.x-t.x,2))}function u(e,t){return 180*Math.atan2(t.y-e.y,t.x-e.x)/Math.PI}function l(e,t){return Math.min(e,Math.max(0,t))}function h(e,t){return t}function d(e,t){return{x:(t.x+e.x)/2,y:(t.y+e.y)/2}}function f(e,t,o){var r=o*Math.PI/180;return{width:Math.abs(Math.cos(r)*e)+Math.abs(Math.sin(r)*t),height:Math.abs(Math.sin(r)*e)+Math.abs(Math.cos(r)*t)}}function m(e,t,o){return Math.min(Math.max(e,t),o)}function v(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.filter(function(e){return"string"==typeof e&&!!(e.length>0)}).join(" ").trim()}var g=function(e){function t(){var o=null!==e&&e.apply(this,arguments)||this;return o.cropperRef=n.createRef(),o.imageRef=n.createRef(),o.videoRef=n.createRef(),o.containerPosition={x:0,y:0},o.containerRef=null,o.styleRef=null,o.containerRect=null,o.mediaSize={width:0,height:0,naturalWidth:0,naturalHeight:0},o.dragStartPosition={x:0,y:0},o.dragStartCrop={x:0,y:0},o.gestureZoomStart=0,o.gestureRotationStart=0,o.isTouching=!1,o.lastPinchDistance=0,o.lastPinchRotation=0,o.rafDragTimeout=null,o.rafPinchTimeout=null,o.wheelTimer=null,o.currentDoc="undefined"!=typeof document?document:null,o.currentWindow="undefined"!=typeof window?window:null,o.resizeObserver=null,o.state={cropSize:null,hasWheelJustStarted:!1,mediaObjectFit:void 0},o.initResizeObserver=function(){if(void 0!==window.ResizeObserver&&o.containerRef){var e=!0;o.resizeObserver=new window.ResizeObserver(function(t){if(e){e=!1;return}o.computeSizes()}),o.resizeObserver.observe(o.containerRef)}},o.preventZoomSafari=function(e){return e.preventDefault()},o.cleanEvents=function(){o.currentDoc&&(o.currentDoc.removeEventListener("mousemove",o.onMouseMove),o.currentDoc.removeEventListener("mouseup",o.onDragStopped),o.currentDoc.removeEventListener("touchmove",o.onTouchMove),o.currentDoc.removeEventListener("touchend",o.onDragStopped),o.currentDoc.removeEventListener("gesturemove",o.onGestureMove),o.currentDoc.removeEventListener("gestureend",o.onGestureEnd),o.currentDoc.removeEventListener("scroll",o.onScroll))},o.clearScrollEvent=function(){o.containerRef&&o.containerRef.removeEventListener("wheel",o.onWheel),o.wheelTimer&&clearTimeout(o.wheelTimer)},o.onMediaLoad=function(){var e=o.computeSizes();e&&(o.emitCropData(),o.setInitialCrop(e)),o.props.onMediaLoaded&&o.props.onMediaLoaded(o.mediaSize)},o.setInitialCrop=function(e){if(o.props.initialCroppedAreaPercentages){var t,r,n,i,a,s,c,p=(t=o.props.initialCroppedAreaPercentages,r=o.mediaSize,n=o.props.rotation,i=o.props.minZoom,a=o.props.maxZoom,s=f(r.width,r.height,n),{crop:{x:(c=m(e.width/s.width*(100/t.width),i,a))*s.width/2-e.width/2-s.width*c*(t.x/100),y:c*s.height/2-e.height/2-s.height*c*(t.y/100)},zoom:c}),u=p.crop,l=p.zoom;o.props.onCropChange(u),o.props.onZoomChange&&o.props.onZoomChange(l)}else if(o.props.initialCroppedAreaPixels){var h,d,v,g,w,S,y,C,R,x=(h=o.props.initialCroppedAreaPixels,d=o.mediaSize,v=o.props.rotation,g=o.props.minZoom,w=o.props.maxZoom,void 0===v&&(v=0),S=f(d.naturalWidth,d.naturalHeight,v),C=m((y=d.width>d.height?d.width/d.naturalWidth:d.height/d.naturalHeight,e.height>e.width?e.height/(h.height*y):e.width/(h.width*y)),g,w),R=e.height>e.width?e.height/h.height:e.width/h.width,{crop:{x:((S.width-h.width)/2-h.x)*R,y:((S.height-h.height)/2-h.y)*R},zoom:C}),u=x.crop,l=x.zoom;o.props.onCropChange(u),o.props.onZoomChange&&o.props.onZoomChange(l)}},o.computeSizes=function(){var e,t,n,i,a,s,c=o.imageRef.current||o.videoRef.current;if(c&&o.containerRef){o.containerRect=o.containerRef.getBoundingClientRect(),o.saveContainerPosition();var p,u,l,h,d,m,v,g,w,S,y,C=o.containerRect.width/o.containerRect.height,R=(null==(e=o.imageRef.current)?void 0:e.naturalWidth)||(null==(t=o.videoRef.current)?void 0:t.videoWidth)||0,x=(null==(n=o.imageRef.current)?void 0:n.naturalHeight)||(null==(i=o.videoRef.current)?void 0:i.videoHeight)||0,b=c.offsetWidth<R||c.offsetHeight<x,D=R/x,z=void 0;if(b)switch(o.state.mediaObjectFit){default:case"contain":z=C>D?{width:o.containerRect.height*D,height:o.containerRect.height}:{width:o.containerRect.width,height:o.containerRect.width/D};break;case"horizontal-cover":z={width:o.containerRect.width,height:o.containerRect.width/D};break;case"vertical-cover":z={width:o.containerRect.height*D,height:o.containerRect.height}}else z={width:c.offsetWidth,height:c.offsetHeight};o.mediaSize=(0,r.Cl)((0,r.Cl)({},z),{naturalWidth:R,naturalHeight:x}),o.props.setMediaSize&&o.props.setMediaSize(o.mediaSize);var P=o.props.cropSize?o.props.cropSize:(p=o.mediaSize.width,u=o.mediaSize.height,l=o.containerRect.width,h=o.containerRect.height,d=o.props.aspect,void 0===(m=o.props.rotation)&&(m=0),g=(v=f(p,u,m)).width,w=v.height,(S=Math.min(g,l))>(y=Math.min(w,h))*d?{width:y*d,height:y}:{width:S,height:S/d});return((null==(a=o.state.cropSize)?void 0:a.height)!==P.height||(null==(s=o.state.cropSize)?void 0:s.width)!==P.width)&&o.props.onCropSizeChange&&o.props.onCropSizeChange(P),o.setState({cropSize:P},o.recomputeCropPosition),o.props.setCropSize&&o.props.setCropSize(P),P}},o.saveContainerPosition=function(){if(o.containerRef){var e=o.containerRef.getBoundingClientRect();o.containerPosition={x:e.left,y:e.top}}},o.onMouseDown=function(e){o.currentDoc&&(e.preventDefault(),o.currentDoc.addEventListener("mousemove",o.onMouseMove),o.currentDoc.addEventListener("mouseup",o.onDragStopped),o.saveContainerPosition(),o.onDragStart(t.getMousePoint(e)))},o.onMouseMove=function(e){return o.onDrag(t.getMousePoint(e))},o.onScroll=function(e){o.currentDoc&&(e.preventDefault(),o.saveContainerPosition())},o.onTouchStart=function(e){o.currentDoc&&(o.isTouching=!0,(!o.props.onTouchRequest||o.props.onTouchRequest(e))&&(o.currentDoc.addEventListener("touchmove",o.onTouchMove,{passive:!1}),o.currentDoc.addEventListener("touchend",o.onDragStopped),o.saveContainerPosition(),2===e.touches.length?o.onPinchStart(e):1===e.touches.length&&o.onDragStart(t.getTouchPoint(e.touches[0]))))},o.onTouchMove=function(e){e.preventDefault(),2===e.touches.length?o.onPinchMove(e):1===e.touches.length&&o.onDrag(t.getTouchPoint(e.touches[0]))},o.onGestureStart=function(e){o.currentDoc&&(e.preventDefault(),o.currentDoc.addEventListener("gesturechange",o.onGestureMove),o.currentDoc.addEventListener("gestureend",o.onGestureEnd),o.gestureZoomStart=o.props.zoom,o.gestureRotationStart=o.props.rotation)},o.onGestureMove=function(e){if(e.preventDefault(),!o.isTouching){var r=t.getMousePoint(e),n=o.gestureZoomStart-1+e.scale;if(o.setNewZoom(n,r,{shouldUpdatePosition:!0}),o.props.onRotationChange){var i=o.gestureRotationStart+e.rotation;o.props.onRotationChange(i)}}},o.onGestureEnd=function(e){o.cleanEvents()},o.onDragStart=function(e){var t,n;o.dragStartPosition={x:e.x,y:e.y},o.dragStartCrop=(0,r.Cl)({},o.props.crop),null==(n=(t=o.props).onInteractionStart)||n.call(t)},o.onDrag=function(e){var t=e.x,r=e.y;o.currentWindow&&(o.rafDragTimeout&&o.currentWindow.cancelAnimationFrame(o.rafDragTimeout),o.rafDragTimeout=o.currentWindow.requestAnimationFrame(function(){if(o.state.cropSize&&void 0!==t&&void 0!==r){var e=t-o.dragStartPosition.x,n=r-o.dragStartPosition.y,i={x:o.dragStartCrop.x+e,y:o.dragStartCrop.y+n},a=o.props.restrictPosition?s(i,o.mediaSize,o.state.cropSize,o.props.zoom,o.props.rotation):i;o.props.onCropChange(a)}}))},o.onDragStopped=function(){var e,t;o.isTouching=!1,o.cleanEvents(),o.emitCropData(),null==(t=(e=o.props).onInteractionEnd)||t.call(e)},o.onWheel=function(e){if(o.currentWindow&&(!o.props.onWheelRequest||o.props.onWheelRequest(e))){e.preventDefault();var r=t.getMousePoint(e),n=a()(e).pixelY,i=o.props.zoom-n*o.props.zoomSpeed/200;o.setNewZoom(i,r,{shouldUpdatePosition:!0}),o.state.hasWheelJustStarted||o.setState({hasWheelJustStarted:!0},function(){var e,t;return null==(t=(e=o.props).onInteractionStart)?void 0:t.call(e)}),o.wheelTimer&&clearTimeout(o.wheelTimer),o.wheelTimer=o.currentWindow.setTimeout(function(){return o.setState({hasWheelJustStarted:!1},function(){var e,t;return null==(t=(e=o.props).onInteractionEnd)?void 0:t.call(e)})},250)}},o.getPointOnContainer=function(e,t){var r=e.x,n=e.y;if(!o.containerRect)throw Error("The Cropper is not mounted");return{x:o.containerRect.width/2-(r-t.x),y:o.containerRect.height/2-(n-t.y)}},o.getPointOnMedia=function(e){var t=e.x,r=e.y,n=o.props,i=n.crop,a=n.zoom;return{x:(t+i.x)/a,y:(r+i.y)/a}},o.setNewZoom=function(e,t,r){var n=(void 0===r?{}:r).shouldUpdatePosition;if(o.state.cropSize&&o.props.onZoomChange){var i=m(e,o.props.minZoom,o.props.maxZoom);if(void 0===n||n){var a=o.getPointOnContainer(t,o.containerPosition),c=o.getPointOnMedia(a),p={x:c.x*i-a.x,y:c.y*i-a.y},u=o.props.restrictPosition?s(p,o.mediaSize,o.state.cropSize,i,o.props.rotation):p;o.props.onCropChange(u)}o.props.onZoomChange(i)}},o.getCropData=function(){var e,t,n,i,a,c,p,u,d,m,v,g,w,S,y;return o.state.cropSize?(e=o.props.restrictPosition?s(o.props.crop,o.mediaSize,o.state.cropSize,o.props.zoom,o.props.rotation):o.props.crop,t=o.mediaSize,n=o.state.cropSize,i=o.getAspect(),a=o.props.zoom,c=o.props.rotation,p=o.props.restrictPosition,void 0===c&&(c=0),void 0===p&&(p=!0),u=p?l:h,d=f(t.width,t.height,c),m=f(t.naturalWidth,t.naturalHeight,c),v={x:u(100,((d.width-n.width/a)/2-e.x/a)/d.width*100),y:u(100,((d.height-n.height/a)/2-e.y/a)/d.height*100),width:u(100,n.width/d.width*100/a),height:u(100,n.height/d.height*100/a)},g=Math.round(u(m.width,v.width*m.width/100)),w=Math.round(u(m.height,v.height*m.height/100)),S=m.width>=m.height*i?{width:Math.round(w*i),height:w}:{width:g,height:Math.round(g/i)},y=(0,r.Cl)((0,r.Cl)({},S),{x:Math.round(u(m.width-S.width,v.x*m.width/100)),y:Math.round(u(m.height-S.height,v.y*m.height/100))}),{croppedAreaPercentages:v,croppedAreaPixels:y}):null},o.emitCropData=function(){var e=o.getCropData();if(e){var t=e.croppedAreaPercentages,r=e.croppedAreaPixels;o.props.onCropComplete&&o.props.onCropComplete(t,r),o.props.onCropAreaChange&&o.props.onCropAreaChange(t,r)}},o.emitCropAreaChange=function(){var e=o.getCropData();if(e){var t=e.croppedAreaPercentages,r=e.croppedAreaPixels;o.props.onCropAreaChange&&o.props.onCropAreaChange(t,r)}},o.recomputeCropPosition=function(){if(o.state.cropSize){var e=o.props.restrictPosition?s(o.props.crop,o.mediaSize,o.state.cropSize,o.props.zoom,o.props.rotation):o.props.crop;o.props.onCropChange(e),o.emitCropData()}},o.onKeyDown=function(e){var t,n,i=o.props,a=i.crop,c=i.onCropChange,p=i.keyboardStep,u=i.zoom,l=i.rotation,h=p;if(o.state.cropSize){e.shiftKey&&(h*=.2);var d=(0,r.Cl)({},a);switch(e.key){case"ArrowUp":d.y-=h,e.preventDefault();break;case"ArrowDown":d.y+=h,e.preventDefault();break;case"ArrowLeft":d.x-=h,e.preventDefault();break;case"ArrowRight":d.x+=h,e.preventDefault();break;default:return}o.props.restrictPosition&&(d=s(d,o.mediaSize,o.state.cropSize,u,l)),e.repeat||null==(n=(t=o.props).onInteractionStart)||n.call(t),c(d)}},o.onKeyUp=function(e){var t,r;switch(e.key){case"ArrowUp":case"ArrowDown":case"ArrowLeft":case"ArrowRight":e.preventDefault();break;default:return}o.emitCropData(),null==(r=(t=o.props).onInteractionEnd)||r.call(t)},o}return(0,r.C6)(t,e),t.prototype.componentDidMount=function(){this.currentDoc&&this.currentWindow&&(this.containerRef&&(this.containerRef.ownerDocument&&(this.currentDoc=this.containerRef.ownerDocument),this.currentDoc.defaultView&&(this.currentWindow=this.currentDoc.defaultView),this.initResizeObserver(),void 0===window.ResizeObserver&&this.currentWindow.addEventListener("resize",this.computeSizes),this.props.zoomWithScroll&&this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}),this.containerRef.addEventListener("gesturestart",this.onGestureStart)),this.currentDoc.addEventListener("scroll",this.onScroll),this.props.disableAutomaticStylesInjection||(this.styleRef=this.currentDoc.createElement("style"),this.styleRef.setAttribute("type","text/css"),this.props.nonce&&this.styleRef.setAttribute("nonce",this.props.nonce),this.styleRef.innerHTML=".reactEasyCrop_Container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: hidden;\n  user-select: none;\n  touch-action: none;\n  cursor: move;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.reactEasyCrop_Image,\n.reactEasyCrop_Video {\n  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */\n}\n\n.reactEasyCrop_Contain {\n  max-width: 100%;\n  max-height: 100%;\n  margin: auto;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n.reactEasyCrop_Cover_Horizontal {\n  width: 100%;\n  height: auto;\n}\n.reactEasyCrop_Cover_Vertical {\n  width: auto;\n  height: 100%;\n}\n\n.reactEasyCrop_CropArea {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  box-sizing: border-box;\n  box-shadow: 0 0 0 9999em;\n  color: rgba(0, 0, 0, 0.5);\n  overflow: hidden;\n}\n\n.reactEasyCrop_CropAreaRound {\n  border-radius: 50%;\n}\n\n.reactEasyCrop_CropAreaGrid::before {\n  content: ' ';\n  box-sizing: border-box;\n  position: absolute;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  top: 0;\n  bottom: 0;\n  left: 33.33%;\n  right: 33.33%;\n  border-top: 0;\n  border-bottom: 0;\n}\n\n.reactEasyCrop_CropAreaGrid::after {\n  content: ' ';\n  box-sizing: border-box;\n  position: absolute;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  top: 33.33%;\n  bottom: 33.33%;\n  left: 0;\n  right: 0;\n  border-left: 0;\n  border-right: 0;\n}\n",this.currentDoc.head.appendChild(this.styleRef)),this.imageRef.current&&this.imageRef.current.complete&&this.onMediaLoad(),this.props.setImageRef&&this.props.setImageRef(this.imageRef),this.props.setVideoRef&&this.props.setVideoRef(this.videoRef),this.props.setCropperRef&&this.props.setCropperRef(this.cropperRef))},t.prototype.componentWillUnmount=function(){var e,t;this.currentDoc&&this.currentWindow&&(void 0===window.ResizeObserver&&this.currentWindow.removeEventListener("resize",this.computeSizes),null==(e=this.resizeObserver)||e.disconnect(),this.containerRef&&this.containerRef.removeEventListener("gesturestart",this.preventZoomSafari),this.styleRef&&(null==(t=this.styleRef.parentNode)||t.removeChild(this.styleRef)),this.cleanEvents(),this.props.zoomWithScroll&&this.clearScrollEvent())},t.prototype.componentDidUpdate=function(e){e.rotation!==this.props.rotation?(this.computeSizes(),this.recomputeCropPosition()):e.aspect!==this.props.aspect||e.objectFit!==this.props.objectFit?this.computeSizes():e.zoom!==this.props.zoom?this.recomputeCropPosition():(null==(t=e.cropSize)?void 0:t.height)!==(null==(o=this.props.cropSize)?void 0:o.height)||(null==(r=e.cropSize)?void 0:r.width)!==(null==(n=this.props.cropSize)?void 0:n.width)?this.computeSizes():((null==(i=e.crop)?void 0:i.x)!==(null==(a=this.props.crop)?void 0:a.x)||(null==(s=e.crop)?void 0:s.y)!==(null==(c=this.props.crop)?void 0:c.y))&&this.emitCropAreaChange(),e.zoomWithScroll!==this.props.zoomWithScroll&&this.containerRef&&(this.props.zoomWithScroll?this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}):this.clearScrollEvent()),e.video!==this.props.video&&(null==(p=this.videoRef.current)||p.load());var t,o,r,n,i,a,s,c,p,u=this.getObjectFit();u!==this.state.mediaObjectFit&&this.setState({mediaObjectFit:u},this.computeSizes)},t.prototype.getAspect=function(){var e=this.props,t=e.cropSize,o=e.aspect;return t?t.width/t.height:o},t.prototype.getObjectFit=function(){var e,t,o,r;if("cover"===this.props.objectFit){if((this.imageRef.current||this.videoRef.current)&&this.containerRef){this.containerRect=this.containerRef.getBoundingClientRect();var n=this.containerRect.width/this.containerRect.height;return((null==(e=this.imageRef.current)?void 0:e.naturalWidth)||(null==(t=this.videoRef.current)?void 0:t.videoWidth)||0)/((null==(o=this.imageRef.current)?void 0:o.naturalHeight)||(null==(r=this.videoRef.current)?void 0:r.videoHeight)||0)<n?"horizontal-cover":"vertical-cover"}return"horizontal-cover"}return this.props.objectFit},t.prototype.onPinchStart=function(e){var o=t.getTouchPoint(e.touches[0]),r=t.getTouchPoint(e.touches[1]);this.lastPinchDistance=p(o,r),this.lastPinchRotation=u(o,r),this.onDragStart(d(o,r))},t.prototype.onPinchMove=function(e){var o=this;if(this.currentDoc&&this.currentWindow){var r=t.getTouchPoint(e.touches[0]),n=t.getTouchPoint(e.touches[1]),i=d(r,n);this.onDrag(i),this.rafPinchTimeout&&this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout),this.rafPinchTimeout=this.currentWindow.requestAnimationFrame(function(){var e=p(r,n),t=o.props.zoom*(e/o.lastPinchDistance);o.setNewZoom(t,i,{shouldUpdatePosition:!1}),o.lastPinchDistance=e;var a=u(r,n),s=o.props.rotation+(a-o.lastPinchRotation);o.props.onRotationChange&&o.props.onRotationChange(s),o.lastPinchRotation=a})}},t.prototype.render=function(){var e,t=this,o=this.props,i=o.image,a=o.video,s=o.mediaProps,c=o.cropperProps,p=o.transform,u=o.crop,l=u.x,h=u.y,d=o.rotation,f=o.zoom,m=o.cropShape,g=o.showGrid,w=o.style,S=w.containerStyle,y=w.cropAreaStyle,C=w.mediaStyle,R=o.classes,x=R.containerClassName,b=R.cropAreaClassName,D=R.mediaClassName,z=null!=(e=this.state.mediaObjectFit)?e:this.getObjectFit();return n.createElement("div",{onMouseDown:this.onMouseDown,onTouchStart:this.onTouchStart,ref:function(e){return t.containerRef=e},"data-testid":"container",style:S,className:v("reactEasyCrop_Container",x)},i?n.createElement("img",(0,r.Cl)({alt:"",className:v("reactEasyCrop_Image","contain"===z&&"reactEasyCrop_Contain","horizontal-cover"===z&&"reactEasyCrop_Cover_Horizontal","vertical-cover"===z&&"reactEasyCrop_Cover_Vertical",D)},s,{src:i,ref:this.imageRef,style:(0,r.Cl)((0,r.Cl)({},C),{transform:p||"translate(".concat(l,"px, ").concat(h,"px) rotate(").concat(d,"deg) scale(").concat(f,")")}),onLoad:this.onMediaLoad})):a&&n.createElement("video",(0,r.Cl)({autoPlay:!0,playsInline:!0,loop:!0,muted:!0,className:v("reactEasyCrop_Video","contain"===z&&"reactEasyCrop_Contain","horizontal-cover"===z&&"reactEasyCrop_Cover_Horizontal","vertical-cover"===z&&"reactEasyCrop_Cover_Vertical",D)},s,{ref:this.videoRef,onLoadedMetadata:this.onMediaLoad,style:(0,r.Cl)((0,r.Cl)({},C),{transform:p||"translate(".concat(l,"px, ").concat(h,"px) rotate(").concat(d,"deg) scale(").concat(f,")")}),controls:!1}),(Array.isArray(a)?a:[{src:a}]).map(function(e){return n.createElement("source",(0,r.Cl)({key:e.src},e))})),this.state.cropSize&&n.createElement("div",(0,r.Cl)({ref:this.cropperRef,style:(0,r.Cl)((0,r.Cl)({},y),{width:this.state.cropSize.width,height:this.state.cropSize.height}),tabIndex:0,onKeyDown:this.onKeyDown,onKeyUp:this.onKeyUp,"data-testid":"cropper",className:v("reactEasyCrop_CropArea","round"===m&&"reactEasyCrop_CropAreaRound",g&&"reactEasyCrop_CropAreaGrid",b)},c)))},t.defaultProps={zoom:1,rotation:0,aspect:4/3,maxZoom:3,minZoom:1,cropShape:"rect",objectFit:"contain",showGrid:!0,style:{},classes:{},mediaProps:{},cropperProps:{},zoomSpeed:1,restrictPosition:!0,zoomWithScroll:!0,keyboardStep:1},t.getMousePoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},t.getTouchPoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},t}(n.Component)},9125:e=>{"use strict";var t=!!("undefined"!=typeof window&&window.document&&window.document.createElement);e.exports={canUseDOM:t,canUseWorkers:"undefined"!=typeof Worker,canUseEventListeners:t&&!!(window.addEventListener||window.attachEvent),canUseViewport:t&&!!window.screen,isInWorker:!t}},40968:(e,t,o)=>{"use strict";o.d(t,{b:()=>s});var r=o(12115),n=o(63540),i=o(95155),a=r.forwardRef((e,t)=>(0,i.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var o;t.target.closest("button, input, select, textarea")||(null==(o=e.onMouseDown)||o.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var s=a},54073:(e,t,o)=>{"use strict";o.d(t,{CC:()=>U,Q6:()=>G,bL:()=>H,zi:()=>K});var r=o(12115),n=o(89367),i=o(85185),a=o(6101),s=o(46081),c=o(5845),p=o(94315),u=o(45503),l=o(11275),h=o(63540),d=o(57683),f=o(95155),m=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],g={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},w="Slider",[S,y,C]=(0,d.N)(w),[R,x]=(0,s.A)(w,[C]),[b,D]=R(w),z=r.forwardRef((e,t)=>{let{name:o,min:a=0,max:s=100,step:p=1,orientation:u="horizontal",disabled:l=!1,minStepsBetweenThumbs:h=0,defaultValue:d=[a],value:g,onValueChange:w=()=>{},onValueCommit:y=()=>{},inverted:C=!1,form:R,...x}=e,D=r.useRef(new Set),z=r.useRef(0),P="horizontal"===u,[M=[],_]=(0,c.i)({prop:g,defaultProp:d,onChange:e=>{var t;null==(t=[...D.current][z.current])||t.focus(),w(e)}}),W=r.useRef(M);function T(e,t){let{commit:o}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1};let r=(String(p).split(".")[1]||"").length,i=function(e,t){let o=Math.pow(10,t);return Math.round(e*o)/o}(Math.round((e-a)/p)*p+a,r),c=(0,n.q)(i,[a,s]);_(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0,r=[...e];return r[o]=t,r.sort((e,t)=>e-t)}(e,c,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,o)=>e[o+1]-t))>=t;return!0}(r,h*p))return e;{z.current=r.indexOf(c);let t=String(r)!==String(e);return t&&o&&y(r),t?r:e}})}return(0,f.jsx)(b,{scope:e.__scopeSlider,name:o,disabled:l,min:a,max:s,valueIndexToChangeRef:z,thumbs:D.current,values:M,orientation:u,form:R,children:(0,f.jsx)(S.Provider,{scope:e.__scopeSlider,children:(0,f.jsx)(S.Slot,{scope:e.__scopeSlider,children:(0,f.jsx)(P?E:A,{"aria-disabled":l,"data-disabled":l?"":void 0,...x,ref:t,onPointerDown:(0,i.m)(x.onPointerDown,()=>{l||(W.current=M)}),min:a,max:s,inverted:C,onSlideStart:l?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let o=e.map(e=>Math.abs(e-t)),r=Math.min(...o);return o.indexOf(r)}(M,e);T(e,t)},onSlideMove:l?void 0:function(e){T(e,z.current)},onSlideEnd:l?void 0:function(){let e=W.current[z.current];M[z.current]!==e&&y(M)},onHomeKeyDown:()=>!l&&T(a,0,{commit:!0}),onEndKeyDown:()=>!l&&T(s,M.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:o}=e;if(!l){let e=m.includes(t.key)||t.shiftKey&&v.includes(t.key),r=z.current;T(M[r]+p*(e?10:1)*o,r,{commit:!0})}}})})})})});z.displayName=w;var[P,M]=R(w,{startEdge:"left",endEdge:"right",size:"width",direction:1}),E=r.forwardRef((e,t)=>{let{min:o,max:n,dir:i,inverted:s,onSlideStart:c,onSlideMove:u,onSlideEnd:l,onStepKeyDown:h,...d}=e,[m,v]=r.useState(null),w=(0,a.s)(t,e=>v(e)),S=r.useRef(void 0),y=(0,p.jH)(i),C="ltr"===y,R=C&&!s||!C&&s;function x(e){let t=S.current||m.getBoundingClientRect(),r=Z([0,t.width],R?[o,n]:[n,o]);return S.current=t,r(e-t.left)}return(0,f.jsx)(P,{scope:e.__scopeSlider,startEdge:R?"left":"right",endEdge:R?"right":"left",direction:R?1:-1,size:"width",children:(0,f.jsx)(_,{dir:y,"data-orientation":"horizontal",...d,ref:w,style:{...d.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=x(e.clientX);null==c||c(t)},onSlideMove:e=>{let t=x(e.clientX);null==u||u(t)},onSlideEnd:()=>{S.current=void 0,null==l||l()},onStepKeyDown:e=>{let t=g[R?"from-left":"from-right"].includes(e.key);null==h||h({event:e,direction:t?-1:1})}})})}),A=r.forwardRef((e,t)=>{let{min:o,max:n,inverted:i,onSlideStart:s,onSlideMove:c,onSlideEnd:p,onStepKeyDown:u,...l}=e,h=r.useRef(null),d=(0,a.s)(t,h),m=r.useRef(void 0),v=!i;function w(e){let t=m.current||h.current.getBoundingClientRect(),r=Z([0,t.height],v?[n,o]:[o,n]);return m.current=t,r(e-t.top)}return(0,f.jsx)(P,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,f.jsx)(_,{"data-orientation":"vertical",...l,ref:d,style:{...l.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=w(e.clientY);null==s||s(t)},onSlideMove:e=>{let t=w(e.clientY);null==c||c(t)},onSlideEnd:()=>{m.current=void 0,null==p||p()},onStepKeyDown:e=>{let t=g[v?"from-bottom":"from-top"].includes(e.key);null==u||u({event:e,direction:t?-1:1})}})})}),_=r.forwardRef((e,t)=>{let{__scopeSlider:o,onSlideStart:r,onSlideMove:n,onSlideEnd:a,onHomeKeyDown:s,onEndKeyDown:c,onStepKeyDown:p,...u}=e,l=D(w,o);return(0,f.jsx)(h.sG.span,{...u,ref:t,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Home"===e.key?(s(e),e.preventDefault()):"End"===e.key?(c(e),e.preventDefault()):m.concat(v).includes(e.key)&&(p(e),e.preventDefault())}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),l.thumbs.has(t)?t.focus():r(e)}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&n(e)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),a(e))})})}),W="SliderTrack",T=r.forwardRef((e,t)=>{let{__scopeSlider:o,...r}=e,n=D(W,o);return(0,f.jsx)(h.sG.span,{"data-disabled":n.disabled?"":void 0,"data-orientation":n.orientation,...r,ref:t})});T.displayName=W;var L="SliderRange",N=r.forwardRef((e,t)=>{let{__scopeSlider:o,...n}=e,i=D(L,o),s=M(L,o),c=r.useRef(null),p=(0,a.s)(t,c),u=i.values.length,l=i.values.map(e=>k(e,i.min,i.max)),d=u>1?Math.min(...l):0,m=100-Math.max(...l);return(0,f.jsx)(h.sG.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...n,ref:p,style:{...e.style,[s.startEdge]:d+"%",[s.endEdge]:m+"%"}})});N.displayName=L;var j="SliderThumb",O=r.forwardRef((e,t)=>{let o=y(e.__scopeSlider),[n,i]=r.useState(null),s=(0,a.s)(t,e=>i(e)),c=r.useMemo(()=>n?o().findIndex(e=>e.ref.current===n):-1,[o,n]);return(0,f.jsx)(F,{...e,ref:s,index:c})}),F=r.forwardRef((e,t)=>{let{__scopeSlider:o,index:n,name:s,...c}=e,p=D(j,o),u=M(j,o),[d,m]=r.useState(null),v=(0,a.s)(t,e=>m(e)),g=!d||p.form||!!d.closest("form"),w=(0,l.X)(d),y=p.values[n],C=void 0===y?0:k(y,p.min,p.max),R=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(n,p.values.length),x=null==w?void 0:w[u.size],b=x?function(e,t,o){let r=e/2,n=Z([0,50],[0,r]);return(r-n(t)*o)*o}(x,C,u.direction):0;return r.useEffect(()=>{if(d)return p.thumbs.add(d),()=>{p.thumbs.delete(d)}},[d,p.thumbs]),(0,f.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[u.startEdge]:"calc(".concat(C,"% + ").concat(b,"px)")},children:[(0,f.jsx)(S.ItemSlot,{scope:e.__scopeSlider,children:(0,f.jsx)(h.sG.span,{role:"slider","aria-label":e["aria-label"]||R,"aria-valuemin":p.min,"aria-valuenow":y,"aria-valuemax":p.max,"aria-orientation":p.orientation,"data-orientation":p.orientation,"data-disabled":p.disabled?"":void 0,tabIndex:p.disabled?void 0:0,...c,ref:v,style:void 0===y?{display:"none"}:e.style,onFocus:(0,i.m)(e.onFocus,()=>{p.valueIndexToChangeRef.current=n})})}),g&&(0,f.jsx)(I,{name:null!=s?s:p.name?p.name+(p.values.length>1?"[]":""):void 0,form:p.form,value:y},n)]})});O.displayName=j;var I=r.forwardRef((e,t)=>{let{__scopeSlider:o,value:n,...i}=e,s=r.useRef(null),c=(0,a.s)(s,t),p=(0,u.Z)(n);return r.useEffect(()=>{let e=s.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(p!==n&&t){let o=new Event("input",{bubbles:!0});t.call(e,n),e.dispatchEvent(o)}},[p,n]),(0,f.jsx)(h.sG.input,{style:{display:"none"},...i,ref:c,defaultValue:n})});function k(e,t,o){return(0,n.q)(100/(o-t)*(e-t),[0,100])}function Z(e,t){return o=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(o-e[0])}}I.displayName="RadioBubbleInput";var H=z,U=T,G=N,K=O},57988:(e,t,o)=>{"use strict";var r,n=o(9125);n.canUseDOM&&(r=document.implementation&&document.implementation.hasFeature&&!0!==document.implementation.hasFeature("","")),e.exports=function(e,t){if(!n.canUseDOM||t&&!("addEventListener"in document))return!1;var o="on"+e,i=o in document;if(!i){var a=document.createElement("div");a.setAttribute(o,"return;"),i="function"==typeof a[o]}return!i&&r&&"wheel"===e&&(i=document.implementation.hasFeature("Events.wheel","3.0")),i}},66318:e=>{var t,o,r,n,i,a,s,c,p,u,l,h,d,f,m,v=!1;function g(){if(!v){v=!0;var e=navigator.userAgent,g=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e),w=/(Mac OS X)|(Windows)|(Linux)/.exec(e);if(h=/\b(iPhone|iP[ao]d)/.exec(e),d=/\b(iP[ao]d)/.exec(e),u=/Android/i.exec(e),f=/FBAN\/\w+;/i.exec(e),m=/Mobile/i.exec(e),l=!!/Win64/.exec(e),g){(t=g[1]?parseFloat(g[1]):g[5]?parseFloat(g[5]):NaN)&&document&&document.documentMode&&(t=document.documentMode);var S=/(?:Trident\/(\d+.\d+))/.exec(e);a=S?parseFloat(S[1])+4:t,o=g[2]?parseFloat(g[2]):NaN,r=g[3]?parseFloat(g[3]):NaN,i=(n=g[4]?parseFloat(g[4]):NaN)&&(g=/(?:Chrome\/(\d+\.\d+))/.exec(e))&&g[1]?parseFloat(g[1]):NaN}else t=o=r=i=n=NaN;if(w){if(w[1]){var y=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);s=!y||parseFloat(y[1].replace("_","."))}else s=!1;c=!!w[2],p=!!w[3]}else s=c=p=!1}}var w={ie:function(){return g()||t},ieCompatibilityMode:function(){return g()||a>t},ie64:function(){return w.ie()&&l},firefox:function(){return g()||o},opera:function(){return g()||r},webkit:function(){return g()||n},safari:function(){return w.webkit()},chrome:function(){return g()||i},windows:function(){return g()||c},osx:function(){return g()||s},linux:function(){return g()||p},iphone:function(){return g()||h},mobile:function(){return g()||h||d||u||m},nativeApp:function(){return g()||f},android:function(){return g()||u},ipad:function(){return g()||d}};e.exports=w},76879:(e,t,o)=>{e.exports=o(92824)},92824:(e,t,o)=>{"use strict";var r=o(66318),n=o(57988);function i(e){var t=0,o=0,r=0,n=0;return"detail"in e&&(o=e.detail),"wheelDelta"in e&&(o=-e.wheelDelta/120),"wheelDeltaY"in e&&(o=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=o,o=0),r=10*t,n=10*o,"deltaY"in e&&(n=e.deltaY),"deltaX"in e&&(r=e.deltaX),(r||n)&&e.deltaMode&&(1==e.deltaMode?(r*=40,n*=40):(r*=800,n*=800)),r&&!t&&(t=r<1?-1:1),n&&!o&&(o=n<1?-1:1),{spinX:t,spinY:o,pixelX:r,pixelY:n}}i.getEventType=function(){return r.firefox()?"DOMMouseScroll":n("wheel")?"wheel":"mousewheel"},e.exports=i}}]);