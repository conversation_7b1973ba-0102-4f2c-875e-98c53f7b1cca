"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8695],{1335:(t,e,i)=>{i.d(e,{u:()=>n});var s=i(9064);let n={test:(0,i(55920).$)("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:s.B.transform}},2735:(t,e,i)=>{function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function n(t,e,i,n){if("function"==typeof e){let[r,a]=s(n);e=e(void 0!==i?i:t.custom,r,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,a]=s(n);e=e(void 0!==i?i:t.custom,r,a)}return e}i.d(e,{a:()=>n})},4272:(t,e,i)=>{i.d(e,{y:()=>a});var s=i(1335),n=i(18476),r=i(9064);let a={test:t=>r.B.test(t)||s.u.test(t)||n.V.test(t),parse:t=>r.B.test(t)?r.B.parse(t):n.V.test(t)?n.V.parse(t):s.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?r.B.transform(t):n.V.transform(t)}},5910:(t,e,i)=>{i.d(e,{p:()=>s});let s=t=>Array.isArray(t)},6775:(t,e,i)=>{i.d(e,{G:()=>u});var s=i(23387),n=i(19827),r=i(53191),a=i(54542),o=i(45818),l=i(53678),h=i(26087);function u(t,e,{clamp:i=!0,ease:d,mixer:c}={}){let p=t.length;if((0,a.V)(p===e.length,"Both input and output ranges must be the same length"),1===p)return()=>e[0];if(2===p&&e[0]===e[1])return()=>e[1];let m=t[0]===t[1];t[0]>t[p-1]&&(t=[...t].reverse(),e=[...e].reverse());let f=function(t,e,i){let a=[],o=i||s.W.mix||h.j,l=t.length-1;for(let i=0;i<l;i++){let s=o(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||n.l:e;s=(0,r.F)(t,s)}a.push(s)}return a}(e,d,c),v=f.length,y=i=>{if(m&&i<t[0])return e[0];let s=0;if(v>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=(0,o.q)(t[s],t[s+1],i);return f[s](n)};return i?e=>y((0,l.q)(t[0],t[p-1],e)):y}},7322:(t,e,i)=>{i.d(e,{h:()=>c,q:()=>d});var s=i(60280),n=i(69515);let r=new Set,a=!1,o=!1,l=!1;function h(){if(o){let t=Array.from(r).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=(0,s.W9)(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}o=!1,a=!1,r.forEach(t=>t.complete(l)),r.clear()}function u(){r.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(o=!0)})}function d(){l=!0,u(),h(),l=!1}class c{constructor(t,e,i,s,n,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(r.add(this),a||(a=!0,n.Gt.read(u),n.Gt.resolveKeyframes(h))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),r.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,r.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}},7712:(t,e,i)=>{i.d(e,{po:()=>r,tn:()=>o,yT:()=>a});var s=i(91765),n=i(54180);let r=t=>1-Math.sin(Math.acos(t)),a=(0,n.G)(r),o=(0,s.V)(r)},9064:(t,e,i)=>{i.d(e,{B:()=>h});var s=i(53678),n=i(57887),r=i(11557),a=i(55920);let o=t=>(0,s.q)(0,255,t),l={...n.ai,transform:t=>Math.round(o(t))},h={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,r.a)(n.X4.transform(s))+")"}},11557:(t,e,i)=>{i.d(e,{a:()=>s});let s=t=>Math.round(1e5*t)/1e5},14570:(t,e,i)=>{i.d(e,{S:()=>s});let s=t=>!!(t&&t.getVelocity)},18476:(t,e,i)=>{i.d(e,{V:()=>o});var s=i(57887),n=i(34158),r=i(11557),a=i(55920);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:a=1})=>"hsla("+Math.round(t)+", "+n.KN.transform((0,r.a)(e))+", "+n.KN.transform((0,r.a)(i))+", "+(0,r.a)(s.X4.transform(a))+")"}},18802:(t,e,i)=>{i.d(e,{U:()=>a});var s=i(60098),n=i(5910),r=i(20419);function a(t,e){let{transitionEnd:i={},transition:a={},...o}=(0,r.K)(t,e)||{};for(let e in o={...o,...i}){var l;let i=(l=o[e],(0,n.p)(l)?l[l.length-1]||0:l);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,s.OQ)(i))}}},19827:(t,e,i)=>{i.d(e,{l:()=>s});let s=t=>t},20419:(t,e,i)=>{i.d(e,{K:()=>n});var s=i(2735);function n(t,e,i){let n=t.getProps();return(0,s.a)(n,e,void 0!==i?i:n.custom,t)}},23387:(t,e,i)=>{i.d(e,{W:()=>s});let s={}},24744:(t,e,i)=>{i.d(e,{Q:()=>s});let s={value:null,addProjectionMetrics:null}},26087:(t,e,i)=>{i.d(e,{j:()=>A});var s=i(53191),n=i(54542),r=i(78606),a=i(4272),o=i(60010),l=i(1335),h=i(18476);function u(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var d=i(9064);function c(t,e){return i=>i>0?e:t}var p=i(33210);let m=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},f=[l.u,d.B,h.V],v=t=>f.find(e=>e.test(t));function y(t){let e=v(t);if((0,n.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===h.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,o=2*i-s;n=u(o,s,t+1/3),r=u(o,s,t),a=u(o,s,t-1/3)}else n=r=a=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}(i)),i}let g=(t,e)=>{let i=y(t),s=y(e);if(!i||!s)return c(t,e);let n={...i};return t=>(n.red=m(i.red,s.red,t),n.green=m(i.green,s.green,t),n.blue=m(i.blue,s.blue,t),n.alpha=(0,p.k)(i.alpha,s.alpha,t),d.B.transform(n))},x=new Set(["none","hidden"]);function T(t,e){return i=>(0,p.k)(t,e,i)}function w(t){return"number"==typeof t?T:"string"==typeof t?(0,r.p)(t)?c:a.y.test(t)?g:b:Array.isArray(t)?P:"object"==typeof t?a.y.test(t)?g:S:c}function P(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>w(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function S(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=w(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let b=(t,e)=>{let i=o.f.createTransformer(e),r=(0,o.V)(t),a=(0,o.V)(e);return r.indexes.var.length===a.indexes.var.length&&r.indexes.color.length===a.indexes.color.length&&r.indexes.number.length>=a.indexes.number.length?x.has(t)&&!a.values.length||x.has(e)&&!r.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,s.F)(P(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],a=t.indexes[r][s[r]],o=t.values[a]??0;i[n]=o,s[r]++}return i}(r,a),a.values),i):((0,n.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),c(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,p.k)(t,e,i):w(t)(t,e)}},28695:(t,e,i)=>{function s(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{P:()=>sv});var n,r,a=i(85982),o=i(5910);function l(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function h(t){return"string"==typeof t||Array.isArray(t)}let u=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],d=["initial",...u],c=d.length;var p=i(20419);let m=[...u].reverse(),f=u.length;function v(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function y(){return{animate:v(!0),whileInView:v(),whileHover:v(),whileTap:v(),whileDrag:v(),whileFocus:v(),exit:v()}}class g{constructor(t){this.isMounted=!1,this.node=t}update(){}}class x extends g{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(0,a._)(t,e,i))),i=y(),n=!0,r=e=>(i,s)=>{let n=(0,p.K)(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function u(a){let{props:u}=t,v=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<c;t++){let s=d[t],n=e.props[s];(h(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},y=[],g=new Set,x={},T=1/0;for(let e=0;e<f;e++){var w,P;let d=m[e],c=i[d],p=void 0!==u[d]?u[d]:v[d],f=h(p),S=d===a?c.isActive:null;!1===S&&(T=e);let b=p===v[d]&&p!==u[d]&&f;if(b&&n&&t.manuallyAnimateOnMount&&(b=!1),c.protectedKeys={...x},!c.isActive&&null===S||!p&&!c.prevProp||s(p)||"boolean"==typeof p)continue;let A=(w=c.prevProp,"string"==typeof(P=p)?P!==w:!!Array.isArray(P)&&!l(P,w)),V=A||d===a&&c.isActive&&!b&&f||e>T&&f,M=!1,k=Array.isArray(p)?p:[p],E=k.reduce(r(d),{});!1===S&&(E={});let{prevResolvedValues:D={}}=c,C={...D,...E},R=e=>{V=!0,g.has(e)&&(M=!0,g.delete(e)),c.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in C){let e=E[t],i=D[t];if(x.hasOwnProperty(t))continue;let s=!1;((0,o.p)(e)&&(0,o.p)(i)?l(e,i):e===i)?void 0!==e&&g.has(t)?R(t):c.protectedKeys[t]=!0:null!=e?R(t):g.add(t)}c.prevProp=p,c.prevResolvedValues=E,c.isActive&&(x={...x,...E}),n&&t.blockInitialAnimation&&(V=!1);let j=!(b&&A)||M;V&&j&&y.push(...k.map(t=>({animation:t,options:{type:d}})))}if(g.size){let e={};if("boolean"!=typeof u.initial){let i=(0,p.K)(t,Array.isArray(u.initial)?u.initial[0]:u.initial);i&&i.transition&&(e.transition=i.transition)}g.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),y.push({animation:e})}let S=!!y.length;return n&&(!1===u.initial||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(S=!1),n=!1,S?e(y):Promise.resolve()}return{animateChanges:u,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=u(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=y(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let T=0;class w extends g{constructor(){super(...arguments),this.id=T++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}var P=i(19827);let S={x:!1,y:!1};var b=i(34158),A=i(69515),V=i(33210),M=i(54542),k=i(98047);function E(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let D=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function C(t){return{point:{x:t.pageX,y:t.pageY}}}let R=t=>e=>D(e)&&t(e,C(e));function j(t,e,i,s){return E(t,e,R(i),s)}function L({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function F(t){return t.max-t.min}function B(t,e,i,s=.5){t.origin=s,t.originPoint=(0,V.k)(e.min,e.max,t.origin),t.scale=F(i)/F(e),t.translate=(0,V.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function O(t,e,i,s){B(t.x,e.x,i.x,s?s.originX:void 0),B(t.y,e.y,i.y,s?s.originY:void 0)}function I(t,e,i){t.min=i.min+e.min,t.max=t.min+F(e)}function U(t,e,i){t.min=e.min-i.min,t.max=t.min+F(e)}function W(t,e,i){U(t.x,e.x,i.x),U(t.y,e.y,i.y)}let $=()=>({translate:0,scale:1,origin:0,originPoint:0}),N=()=>({x:$(),y:$()}),q=()=>({min:0,max:0}),G=()=>({x:q(),y:q()});function X(t){return[t("x"),t("y")]}function K(t){return void 0===t||1===t}function Y({scale:t,scaleX:e,scaleY:i}){return!K(t)||!K(e)||!K(i)}function H(t){return Y(t)||z(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function z(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function Q(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function _(t,e=0,i=1,s,n){t.min=Q(t.min,e,i,s,n),t.max=Q(t.max,e,i,s,n)}function Z(t,{x:e,y:i}){_(t.x,e.translate,e.scale,e.originPoint),_(t.y,i.translate,i.scale,i.originPoint)}function J(t,e){t.min=t.min+e,t.max=t.max+e}function tt(t,e,i,s,n=.5){let r=(0,V.k)(t.min,t.max,n);_(t,e,i,r,s)}function te(t,e){tt(t.x,e.x,e.scaleX,e.scale,e.originX),tt(t.y,e.y,e.scaleY,e.scale,e.originY)}function ti(t,e){return L(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let ts=({current:t})=>t?t.ownerDocument.defaultView:null;function tn(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var tr=i(76333),ta=i(53191),to=i(47215);let tl=(t,e)=>Math.abs(t-e);class th{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=tc(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(tl(t.x,e.x)**2+tl(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=A.uv;this.history.push({...s,timestamp:n});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=tu(e,this.transformPagePoint),A.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=tc("pointercancel"===t.type?this.lastMoveEventInfo:tu(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!D(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=tu(C(t),this.transformPagePoint),{point:a}=r,{timestamp:o}=A.uv;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,tc(r,this.history)),this.removeListeners=(0,ta.F)(j(this.contextWindow,"pointermove",this.handlePointerMove),j(this.contextWindow,"pointerup",this.handlePointerUp),j(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,A.WG)(this.updatePoint)}}function tu(t,e){return e?{point:e(t.point)}:t}function td(t,e){return{x:t.x-e.x,y:t.y-e.y}}function tc({point:t},e){return{point:t,delta:td(t,tp(e)),offset:td(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=tp(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>(0,to.f)(.1)));)i--;if(!s)return{x:0,y:0};let r=(0,to.X)(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let a={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function tp(t){return t[t.length-1]}var tm=i(45818),tf=i(53678);function tv(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function ty(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function tg(t,e,i){return{min:tx(t,e),max:tx(t,i)}}function tx(t,e){return"number"==typeof t?t:t[e]||0}let tT=new WeakMap;class tw{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=G(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new th(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(C(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(S[t])return null;else return S[t]=!0,()=>{S[t]=!1};return S.x||S.y?null:(S.x=S.y=!0,()=>{S.x=S.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),X(t=>{let e=this.getAxisMotionValue(t).get()||0;if(b.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=F(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&A.Gt.postRender(()=>n(t,e)),(0,tr.g)(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>X(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:ts(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&A.Gt.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!tP(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,V.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,V.k)(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&tn(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:tv(t.x,i,n),y:tv(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:tg(t,"left","right"),y:tg(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&X(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!tn(e))return!1;let s=e.current;(0,M.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=ti(t,i),{scroll:n}=e;return n&&(J(s.x,n.offset.x),J(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),a=(t=n.layout.layoutBox,{x:ty(t.x,r.x),y:ty(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=L(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(X(a=>{if(!tP(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return(0,tr.g)(this.visualElement,t),i.start((0,k.f)(t,i,0,e,this.visualElement,!1))}stopAnimation(){X(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){X(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){X(e=>{let{drag:i}=this.getProps();if(!tP(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-(0,V.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!tn(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};X(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=F(t),n=F(e);return n>s?i=(0,tm.q)(e.min,e.max-s,t.min):s>n&&(i=(0,tm.q)(t.min,t.max-n,e.min)),(0,tf.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),X(e=>{if(!tP(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set((0,V.k)(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;tT.set(this.visualElement,this);let t=j(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();tn(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),A.Gt.read(e);let n=E(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(X(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:a}}}function tP(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class tS extends g{constructor(t){super(t),this.removeGroupControls=P.l,this.removeListeners=P.l,this.controls=new tw(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||P.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let tb=t=>(e,i)=>{t&&A.Gt.postRender(()=>t(e,i))};class tA extends g{constructor(){super(...arguments),this.removePointerDownListener=P.l}onPointerDown(t){this.session=new th(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ts(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:tb(t),onStart:tb(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&A.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=j(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var tV=i(95155);let{schedule:tM}=(0,i(58437).I)(queueMicrotask,!1);var tk=i(12115),tE=i(32082),tD=i(90869);let tC=(0,tk.createContext)({}),tR={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function tj(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let tL={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!b.px.test(t))return t;else t=parseFloat(t);let i=tj(t,e.target.x),s=tj(t,e.target.y);return`${i}% ${s}%`}};var tF=i(60010),tB=i(78606);let tO={};class tI extends tk.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in tW)tO[t]=tW[t],(0,tB.j)(t)&&(tO[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),tR.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,r=i.projection;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||A.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),tM.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function tU(t){let[e,i]=(0,tE.xQ)(),s=(0,tk.useContext)(tD.L);return(0,tV.jsx)(tI,{...t,layoutGroup:s,switchLayoutGroup:(0,tk.useContext)(tC),isPresent:e,safeToRemove:i})}let tW={borderRadius:{...tL,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:tL,borderTopRightRadius:tL,borderBottomLeftRadius:tL,borderBottomRightRadius:tL,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tF.f.parse(t);if(s.length>5)return t;let n=tF.f.createTransformer(t),r=+("number"!=typeof s[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;s[0+r]/=a,s[1+r]/=o;let l=(0,V.k)(a,o,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};var t$=i(24744),tN=i(78777),tq=i(74261),tG=i(63704),tX=i(75626),tK=i(60098),tY=i(14570),tH=i(46926),tz=i(56668);let tQ=(t,e)=>t.depth-e.depth;class t_{constructor(){this.children=[],this.isDirty=!1}add(t){(0,tz.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,tz.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(tQ),this.isDirty=!1,this.children.forEach(t)}}function tZ(t){return(0,tY.S)(t)?t.get():t}var tJ=i(7712);let t0=["TopLeft","TopRight","BottomLeft","BottomRight"],t1=t0.length,t5=t=>"string"==typeof t?parseFloat(t):t,t2=t=>"number"==typeof t||b.px.test(t);function t8(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let t3=t4(0,.5,tJ.yT),t7=t4(.5,.95,P.l);function t4(t,e,i){return s=>s<t?0:s>e?1:i((0,tm.q)(t,e,s))}function t6(t,e){t.min=e.min,t.max=e.max}function t9(t,e){t6(t.x,e.x),t6(t.y,e.y)}function et(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ee(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function ei(t,e,[i,s,n],r,a){!function(t,e=0,i=1,s=.5,n,r=t,a=t){if(b.KN.test(e)&&(e=parseFloat(e),e=(0,V.k)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=(0,V.k)(r.min,r.max,s);t===r&&(o-=e),t.min=ee(t.min,e,i,o,n),t.max=ee(t.max,e,i,o,n)}(t,e[i],e[s],e[n],e.scale,r,a)}let es=["x","scaleX","originX"],en=["y","scaleY","originY"];function er(t,e,i,s){ei(t.x,e,es,i?i.x:void 0,s?s.x:void 0),ei(t.y,e,en,i?i.y:void 0,s?s.y:void 0)}function ea(t){return 0===t.translate&&1===t.scale}function eo(t){return ea(t.x)&&ea(t.y)}function el(t,e){return t.min===e.min&&t.max===e.max}function eh(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function eu(t,e){return eh(t.x,e.x)&&eh(t.y,e.y)}function ed(t){return F(t.x)/F(t.y)}function ec(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class ep{constructor(){this.members=[]}add(t){(0,tz.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,tz.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let em={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ef=["","X","Y","Z"],ev={visibility:"hidden"},ey=0;function eg(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function ex({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=ey++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,t$.Q.value&&(em.nodes=em.calculatedTargetDeltas=em.calculatedProjections=0),this.nodes.forEach(eP),this.nodes.forEach(eE),this.nodes.forEach(eD),this.nodes.forEach(eS),t$.Q.addProjectionMetrics&&t$.Q.addProjectionMetrics(em)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new t_)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tX.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||s)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tq.k.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&((0,A.WG)(s),t(r-e))};return A.Gt.setup(s,!0),()=>(0,A.WG)(s)}(s,250),tR.hasAnimatedSinceResize&&(tR.hasAnimatedSinceResize=!1,this.nodes.forEach(ek))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||eB,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),l=!this.targetLayout||!eu(this.targetLayout,s),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...(0,tN.r)(n,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ek(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,A.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eC),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=(0,tH.P)(i);if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",A.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eA);return}this.isUpdating||this.nodes.forEach(eV),this.isUpdating=!1,this.nodes.forEach(eM),this.nodes.forEach(eT),this.nodes.forEach(ew),this.clearAllSnapshots();let t=tq.k.now();A.uv.delta=(0,tf.q)(0,1e3/60,t-A.uv.timestamp),A.uv.timestamp=t,A.uv.isProcessing=!0,A.PP.update.process(A.uv),A.PP.preRender.process(A.uv),A.PP.render.process(A.uv),A.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tM.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eb),this.sharedNodes.forEach(eR)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||F(this.snapshot.measuredBox.x)||F(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=G(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!eo(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||H(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),eU((e=s).x),eU(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return G();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(e$))){let{scroll:t}=this.root;t&&(J(e.x,t.offset.x),J(e.y,t.offset.y))}return e}removeElementScroll(t){let e=G();if(t9(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&t9(e,t),J(e.x,n.offset.x),J(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=G();t9(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&te(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),H(s.latestValues)&&te(i,s.latestValues)}return H(this.latestValues)&&te(i,this.latestValues),i}removeTransform(t){let e=G();t9(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!H(i.latestValues))continue;Y(i.latestValues)&&i.updateSnapshot();let s=G();t9(s,i.measurePageBox()),er(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return H(this.latestValues)&&er(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==A.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=A.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=G(),this.relativeTargetOrigin=G(),W(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),t9(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=G(),this.targetWithTransforms=G()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,I(r.x,a.x,o.x),I(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):t9(this.target,this.layout.layoutBox),Z(this.target,this.targetDelta)):t9(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=G(),this.relativeTargetOrigin=G(),W(this.relativeTargetOrigin,this.target,t.target),t9(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}t$.Q.value&&em.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Y(this.parent.latestValues)||z(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===A.uv.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;t9(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&te(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Z(t,r)),s&&H(n.latestValues)&&te(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=G());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(et(this.prevProjectionDelta.x,this.projectionDelta.x),et(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),O(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&ec(this.projectionDelta.x,this.prevProjectionDelta.x)&&ec(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),t$.Q.value&&em.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=N(),this.projectionDelta=N(),this.projectionDeltaWithTransform=N()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},a=N();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=G(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(eF));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(ej(a.x,t.x,s),ej(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,v;W(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,v=s,eL(p.x,m.x,f.x,v),eL(p.y,m.y,f.y,v),i&&(h=this.relativeTarget,c=i,el(h.x,c.x)&&el(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=G()),t9(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=(0,V.k)(0,i.opacity??1,t3(s)),t.opacityExit=(0,V.k)(e.opacity??1,0,t7(s))):r&&(t.opacity=(0,V.k)(e.opacity??1,i.opacity??1,s));for(let n=0;n<t1;n++){let r=`border${t0[n]}Radius`,a=t8(e,r),o=t8(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||t2(a)===t2(o)?(t[r]=Math.max((0,V.k)(t5(a),t5(o),s),0),(b.KN.test(o)||b.KN.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=(0,V.k)(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,A.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.Gt.update(()=>{tR.hasAnimatedSinceResize=!0,tG.q.layout++,this.currentAnimation=function(t,e,i){let s=(0,tY.S)(0)?0:(0,tK.OQ)(t);return s.start((0,k.f)("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{tG.q.layout--},onComplete:()=>{tG.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&eW(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||G();let e=F(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=F(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}t9(e,i),te(e,n),O(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new ep),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&eg("z",t,s,this.animationValues);for(let e=0;e<ef.length;e++)eg(`rotate${ef[e]}`,t,s,this.animationValues),eg(`skew${ef[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ev;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=tZ(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tZ(t?.pointerEvents)||""),this.hasProjected&&!H(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((n||r||a)&&(s=`translate3d(${n}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:a,skewY:o}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:r,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,tO){if(void 0===n[t])continue;let{correct:i,applyTo:r,isCSSVariable:a}=tO[t],o="none"===e.transform?n[t]:i(n[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=s===this?tZ(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(eA),this.root.sharedNodes.clear()}}}function eT(t){t.updateLayout()}function ew(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?X(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=F(s);s.min=i[t].min,s.max=s.min+n}):eW(n,e.layoutBox,i)&&X(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],a=F(i[s]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+a)});let a=N();O(a,i,e.layoutBox);let o=N();r?O(o,t.applyTransform(s,!0),e.measuredBox):O(o,i,e.layoutBox);let l=!eo(a),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let a=G();W(a,e.layoutBox,n.layoutBox);let o=G();W(o,i,r.layoutBox),eu(a,o)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function eP(t){t$.Q.value&&em.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function eS(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function eb(t){t.clearSnapshot()}function eA(t){t.clearMeasurements()}function eV(t){t.isLayoutDirty=!1}function eM(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ek(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function eE(t){t.resolveTargetDelta()}function eD(t){t.calcProjection()}function eC(t){t.resetSkewAndRotation()}function eR(t){t.removeLeadSnapshot()}function ej(t,e,i){t.translate=(0,V.k)(e.translate,0,i),t.scale=(0,V.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function eL(t,e,i,s){t.min=(0,V.k)(e.min,i.min,s),t.max=(0,V.k)(e.max,i.max,s)}function eF(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let eB={duration:.45,ease:[.4,0,.1,1]},eO=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),eI=eO("applewebkit/")&&!eO("chrome/")?Math.round:P.l;function eU(t){t.min=eI(t.min),t.max=eI(t.max)}function eW(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(ed(e)-ed(i)))}function e$(t){return t!==t.root&&t.scroll?.wasRoot}let eN=ex({attachResizeListener:(t,e)=>E(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),eq={current:void 0},eG=ex({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!eq.current){let t=new eN({});t.mount(window),t.setOptions({layoutScroll:!0}),eq.current=t}return eq.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var eX=i(42198);function eK(t,e){let i=(0,eX.K)(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function eY(t){return!("touch"===t.pointerType||S.x||S.y)}function eH(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&A.Gt.postRender(()=>n(e,C(e)))}class ez extends g{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=eK(t,i),a=t=>{if(!eY(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{eY(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",a,n)}),r}(t,(t,e)=>(eH(this.node,e,"Start"),t=>eH(this.node,t,"End"))))}unmount(){}}class eQ extends g{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,ta.F)(E(this.node.current,"focus",()=>this.onFocus()),E(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let e_=(t,e)=>!!e&&(t===e||e_(t,e.parentElement)),eZ=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),eJ=new WeakSet;function e0(t){return e=>{"Enter"===e.key&&t(e)}}function e1(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let e5=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=e0(()=>{if(eJ.has(i))return;e1(i,"down");let t=e0(()=>{e1(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>e1(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function e2(t){return D(t)&&!(S.x||S.y)}function e8(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&A.Gt.postRender(()=>n(e,C(e)))}class e3 extends g{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=eK(t,i),a=t=>{let s=t.currentTarget;if(!e2(t)||eJ.has(s))return;eJ.add(s);let r=e(s,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),e2(t)&&eJ.has(s)&&(eJ.delete(s),"function"==typeof r&&r(t,{success:e}))},o=t=>{a(t,s===window||s===document||i.useGlobalTarget||e_(s,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),t instanceof HTMLElement)&&(t.addEventListener("focus",t=>e5(t,n)),eZ.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(e8(this.node,e,"Start"),(t,{success:e})=>e8(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let e7=new WeakMap,e4=new WeakMap,e6=t=>{let e=e7.get(t.target);e&&e(t)},e9=t=>{t.forEach(e6)},it={some:0,all:1};class ie extends g{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:it[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;e4.has(i)||e4.set(i,{});let s=e4.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(e9,{root:t,...e})),s[n]}(e);return e7.set(t,i),s.observe(t),()=>{e7.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let ii=(0,tk.createContext)({strict:!1});var is=i(51508);let ir=(0,tk.createContext)({});function ia(t){return s(t.animate)||d.some(e=>h(t[e]))}function io(t){return!!(ia(t)||t.variants)}function il(t){return Array.isArray(t)?t.join(" "):t}var ih=i(68972);let iu={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},id={};for(let t in iu)id[t]={isEnabled:e=>iu[t].some(t=>!!e[t])};let ic=Symbol.for("motionComponentSymbol");var ip=i(31788),im=i(80845),iv=i(97494),iy=i(60018);function ig(t,{layout:e,layoutId:i}){return iy.f.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!tO[t]||"opacity"===t)}let ix=(t,e)=>e&&"number"==typeof t?e.transform(t):t;var iT=i(57887);let iw={...iT.ai,transform:Math.round},iP={rotate:b.uj,rotateX:b.uj,rotateY:b.uj,rotateZ:b.uj,scale:iT.hs,scaleX:iT.hs,scaleY:iT.hs,scaleZ:iT.hs,skew:b.uj,skewX:b.uj,skewY:b.uj,distance:b.px,translateX:b.px,translateY:b.px,translateZ:b.px,x:b.px,y:b.px,z:b.px,perspective:b.px,transformPerspective:b.px,opacity:iT.X4,originX:b.gQ,originY:b.gQ,originZ:b.px},iS={borderWidth:b.px,borderTopWidth:b.px,borderRightWidth:b.px,borderBottomWidth:b.px,borderLeftWidth:b.px,borderRadius:b.px,radius:b.px,borderTopLeftRadius:b.px,borderTopRightRadius:b.px,borderBottomRightRadius:b.px,borderBottomLeftRadius:b.px,width:b.px,maxWidth:b.px,height:b.px,maxHeight:b.px,top:b.px,right:b.px,bottom:b.px,left:b.px,padding:b.px,paddingTop:b.px,paddingRight:b.px,paddingBottom:b.px,paddingLeft:b.px,margin:b.px,marginTop:b.px,marginRight:b.px,marginBottom:b.px,marginLeft:b.px,backgroundPositionX:b.px,backgroundPositionY:b.px,...iP,zIndex:iw,fillOpacity:iT.X4,strokeOpacity:iT.X4,numOctaves:iw},ib={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iA=iy.U.length;function iV(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(iy.f.has(t)){a=!0;continue}if((0,tB.j)(t)){n[t]=i;continue}{let e=ix(i,iS[t]);t.startsWith("origin")?(o=!0,r[t]=e):s[t]=e}}if(!e.transform&&(a||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<iA;r++){let a=iy.U[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=ix(o,iS[a]);if(!l){n=!1;let e=ib[a]||a;s+=`${e}(${t}) `}i&&(e[a]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let iM=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ik(t,e,i){for(let s in e)(0,tY.S)(e[s])||ig(s,i)||(t[s]=e[s])}let iE=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function iD(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||iE.has(t)}let iC=t=>!iD(t);try{!function(t){t&&(iC=e=>e.startsWith("on")?!iD(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let iR=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ij(t){if("string"!=typeof t||t.includes("-"));else if(iR.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let iL={offset:"stroke-dashoffset",array:"stroke-dasharray"},iF={offset:"strokeDashoffset",array:"strokeDasharray"};function iB(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:a=0,...o},l,h){if(iV(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:u,style:d}=t;u.transform&&(d.transform=u.transform,delete u.transform),(d.transform||u.transformOrigin)&&(d.transformOrigin=u.transformOrigin??"50% 50%",delete u.transformOrigin),d.transform&&(d.transformBox="fill-box",delete u.transformBox),void 0!==e&&(u.x=e),void 0!==i&&(u.y=i),void 0!==s&&(u.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?iL:iF;t[r.offset]=b.px.transform(-s);let a=b.px.transform(e),o=b.px.transform(i);t[r.array]=`${a} ${o}`}(u,n,r,a,!1)}let iO=()=>({...iM(),attrs:{}}),iI=t=>"string"==typeof t&&"svg"===t.toLowerCase();var iU=i(2735),iW=i(82885);let i$=t=>(e,i)=>{let n=(0,tk.useContext)(ir),r=(0,tk.useContext)(im.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,r){return{latestValues:function(t,e,i,n){let r={},a=n(t,{});for(let t in a)r[t]=tZ(a[t]);let{initial:o,animate:l}=t,h=ia(t),u=io(t);e&&u&&!h&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===l&&(l=e.animate));let d=!!i&&!1===i.initial,c=(d=d||!1===o)?l:o;if(c&&"boolean"!=typeof c&&!s(c)){let e=Array.isArray(c)?c:[c];for(let i=0;i<e.length;i++){let s=(0,iU.a)(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,n,r,t),renderState:e()}})(t,e,n,r);return i?a():(0,iW.M)(a)};function iN(t,e,i){let{style:s}=t,n={};for(let r in s)((0,tY.S)(s[r])||e.style&&(0,tY.S)(e.style[r])||ig(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let iq={useVisualState:i$({scrapeMotionValuesFromProps:iN,createRenderState:iM})};function iG(t,e,i){let s=iN(t,e,i);for(let i in t)((0,tY.S)(t[i])||(0,tY.S)(e[i]))&&(s[-1!==iy.U.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let iX={useVisualState:i$({scrapeMotionValuesFromProps:iG,createRenderState:iO})};var iK=i(84160),iY=i(58109);let iH=t=>e=>e.test(t),iz=[iT.ai,b.px,b.KN,b.uj,b.vw,b.vh,{test:t=>"auto"===t,parse:t=>t}],iQ=t=>iz.find(iH(t)),i_=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),iZ=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var iJ=i(7322);let i0=t=>/^0[^.\s]+$/u.test(t);var i1=i(30614);let i5=new Set(["brightness","contrast","saturate","opacity"]);function i2(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(i1.S)||[];if(!s)return t;let n=i.replace(s,""),r=+!!i5.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let i8=/\b([a-z-]*)\(.*?\)/gu,i3={...tF.f,getAnimatableNone:t=>{let e=t.match(i8);return e?e.map(i2).join(" "):t}};var i7=i(4272);let i4={...iS,color:i7.y,backgroundColor:i7.y,outlineColor:i7.y,fill:i7.y,stroke:i7.y,borderColor:i7.y,borderTopColor:i7.y,borderRightColor:i7.y,borderBottomColor:i7.y,borderLeftColor:i7.y,filter:i3,WebkitFilter:i3},i6=t=>i4[t];function i9(t,e){let i=i6(t);return i!==i3&&(i=tF.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let st=new Set(["auto","none","0"]);var se=i(60280);class si extends iJ.h{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&(s=s.trim(),(0,tB.p)(s))){let n=function t(e,i,s=1){(0,M.V)(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=iZ.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return i_(t)?parseFloat(t):t}return(0,tB.p)(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!iY.$.has(i)||2!==t.length)return;let[s,n]=t,r=iQ(s),a=iQ(n);if(r!==a)if((0,se.E4)(r)&&(0,se.E4)(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||i0(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!st.has(e)&&(0,tF.V)(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=i9(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=se.Hr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=se.Hr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let ss=[...iz,i7.y,tF.f],sn=t=>ss.find(iH(t)),sr={current:null},sa={current:!1},so=new WeakMap,sl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sh{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=iJ.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tq.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,A.Gt.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=ia(e),this.isVariantNode=io(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==o[t]&&(0,tY.S)(e)&&e.set(o[t],!1)}}mount(t){this.current=t,so.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sa.current||function(){if(sa.current=!0,ih.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sr.current=t.matches;t.addListener(e),e()}else sr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,A.WG)(this.notifyUpdate),(0,A.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=iy.f.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&A.Gt.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in id){let e=id[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):G()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sl.length;e++){let i=sl[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if((0,tY.S)(n))t.addValue(s,n);else if((0,tY.S)(r))t.addValue(s,(0,tK.OQ)(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,(0,tK.OQ)(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,tK.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(i_(i)||i0(i))?i=parseFloat(i):!sn(i)&&tF.f.test(e)&&(i=i9(t,e)),this.setBaseTarget(t,(0,tY.S)(i)?i.get():i)),(0,tY.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=(0,iU.a)(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||(0,tY.S)(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new tX.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class su extends sh{constructor(){super(...arguments),this.KeyframeResolver=si}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,tY.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sd(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}class sc extends su{constructor(){super(...arguments),this.type="html",this.renderInstance=sd}readValueFromInstance(t,e){if(iy.f.has(e))return(0,iK.I)(t,e);{let i=window.getComputedStyle(t),s=((0,tB.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ti(t,e)}build(t,e,i){iV(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return iN(t,e,i)}}var sp=i(78450);let sm=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sf extends su{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=G}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(iy.f.has(e)){let t=i6(e);return t&&t.default||0}return e=sm.has(e)?e:(0,sp.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return iG(t,e,i)}build(t,e,i){iB(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){for(let i in sd(t,e,void 0,s),e.attrs)t.setAttribute(sm.has(i)?i:(0,sp.I)(i),e.attrs[i])}mount(t){this.isSVGTag=iI(t.tagName),super.mount(t)}}let sv=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((n={animation:{Feature:x},exit:{Feature:w},inView:{Feature:ie},tap:{Feature:e3},focus:{Feature:eQ},hover:{Feature:ez},pan:{Feature:tA},drag:{Feature:tS,ProjectionNode:eG,MeasureLayout:tU},layout:{ProjectionNode:eG,MeasureLayout:tU}},r=(t,e)=>ij(t)?new sf(e):new sc(e,{allowProjection:t!==tk.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:a,Component:o}=t;function l(t,e){var i,s,l;let u,d={...(0,tk.useContext)(is.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,tk.useContext)(tD.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(ia(t)){let{initial:e,animate:i}=t;return{initial:!1===e||h(e)?e:void 0,animate:h(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,tk.useContext)(ir));return(0,tk.useMemo)(()=>({initial:e,animate:i}),[il(e),il(i)])}(t),m=a(t,c);if(!c&&ih.B){s=0,l=0,(0,tk.useContext)(ii).strict;let t=function(t){let{drag:e,layout:i}=id;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(d);u=t.MeasureLayout,p.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,tk.useContext)(ir),a=(0,tk.useContext)(ii),o=(0,tk.useContext)(im.t),l=(0,tk.useContext)(is.Q).reducedMotion,h=(0,tk.useRef)(null);s=s||a.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:r,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let u=h.current,d=(0,tk.useContext)(tC);u&&!u.projection&&n&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!a||o&&tn(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,n,d);let c=(0,tk.useRef)(!1);(0,tk.useInsertionEffect)(()=>{u&&c.current&&u.update(i,o)});let p=i[ip.n],m=(0,tk.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,iv.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),tM.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,tk.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(o,m,d,n,t.ProjectionNode)}return(0,tV.jsxs)(ir.Provider,{value:p,children:[u&&p.visualElement?(0,tV.jsx)(u,{visualElement:p.visualElement,...d}):null,r(o,t,(i=p.visualElement,(0,tk.useCallback)(t=>{t&&m.onMount&&m.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):tn(e)&&(e.current=t))},[i])),m,c,p.visualElement)]})}s&&function(t){for(let e in t)id[e]={...id[e],...t[e]}}(s),l.displayName="motion.".concat("string"==typeof o?o:"create(".concat(null!=(i=null!=(e=o.displayName)?e:o.name)?i:"",")"));let u=(0,tk.forwardRef)(l);return u[ic]=o,u}({...ij(t)?iX:iq,preloadedFeatures:n,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let a=(ij(e)?function(t,e,i,s){let n=(0,tk.useMemo)(()=>{let i=iO();return iB(i,e,iI(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ik(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return ik(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,tk.useMemo)(()=>{let i=iM();return iV(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),o=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(iC(n)||!0===i&&iD(n)||!e&&!iD(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==tk.Fragment?{...o,...a,ref:s}:{},{children:h}=i,u=(0,tk.useMemo)(()=>(0,tY.S)(h)?h.get():h,[h]);return(0,tk.createElement)(e,{...l,children:u})}}(e),createVisualElement:r,Component:t})}))},30614:(t,e,i)=>{i.d(e,{S:()=>s});let s=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},31788:(t,e,i)=>{i.d(e,{n:()=>s});let s="data-"+(0,i(78450).I)("framerAppearId")},32082:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(12115),n=i(80845);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return o(l)},[t]);let h=(0,s.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,h]:[!0]}},33210:(t,e,i)=>{i.d(e,{k:()=>s});let s=(t,e,i)=>t+(e-t)*i},34158:(t,e,i)=>{i.d(e,{KN:()=>r,gQ:()=>h,px:()=>a,uj:()=>n,vh:()=>o,vw:()=>l});let s=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),n=s("deg"),r=s("%"),a=s("px"),o=s("vh"),l=s("vw"),h={...r,parse:t=>r.parse(t)/100,transform:t=>r.transform(100*t)}},41917:(t,e,i)=>{i.d(e,{p:()=>s});function s(t){let e;return()=>(void 0===e&&(e=t()),e)}},42198:(t,e,i)=>{i.d(e,{K:()=>s});function s(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);let n=i?.[t]??s.querySelectorAll(t);return n?Array.from(n):[]}return Array.from(t)}},45818:(t,e,i)=>{i.d(e,{q:()=>s});let s=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s}},46926:(t,e,i)=>{i.d(e,{P:()=>n});var s=i(31788);function n(t){return t.props[s.n]}},47215:(t,e,i)=>{i.d(e,{X:()=>n,f:()=>s});let s=t=>1e3*t,n=t=>t/1e3},51508:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},53191:(t,e,i)=>{i.d(e,{F:()=>n});let s=(t,e)=>i=>e(t(i)),n=(...t)=>t.reduce(s)},53678:(t,e,i)=>{i.d(e,{q:()=>s});let s=(t,e,i)=>i>e?e:i<t?t:i},54180:(t,e,i)=>{i.d(e,{G:()=>s});let s=t=>e=>1-t(1-e)},54542:(t,e,i)=>{i.d(e,{$:()=>s,V:()=>n});let s=()=>{},n=()=>{}},55920:(t,e,i)=>{i.d(e,{$:()=>r,q:()=>a});var s=i(30614);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,r=(t,e)=>i=>!!("string"==typeof i&&n.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),a=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,a,o,l]=n.match(s.S);return{[t]:parseFloat(r),[e]:parseFloat(a),[i]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},56668:(t,e,i)=>{function s(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>n,Kq:()=>s})},57887:(t,e,i)=>{i.d(e,{X4:()=>r,ai:()=>n,hs:()=>a});var s=i(53678);let n={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},r={...n,transform:t=>(0,s.q)(0,1,t)},a={...n,default:1}},58109:(t,e,i)=>{i.d(e,{$:()=>s});let s=new Set(["width","height","top","left","right","bottom",...i(60018).U])},58437:(t,e,i)=>{i.d(e,{I:()=>a});var s=i(23387);let n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var r=i(24744);function a(t,e){let i=!1,a=!0,o={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,h=n.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,a=!1,o=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},h=0;function u(e){o.has(e)&&(d.schedule(e),t()),h++,e(l)}let d={schedule:(t,e=!1,r=!1)=>{let a=r&&n?i:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(l=t,n){a=!0;return}n=!0,[i,s]=[s,i],i.forEach(u),e&&r.Q.value&&r.Q.value.frameloop[e].push(h),h=0,i.clear(),n=!1,a&&(a=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:u,read:d,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:v,postRender:y}=h,g=()=>{let n=s.W.useManualTiming?o.timestamp:performance.now();i=!1,s.W.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,u.process(o),d.process(o),c.process(o),p.process(o),m.process(o),f.process(o),v.process(o),y.process(o),o.isProcessing=!1,i&&e&&(a=!1,t(g))},x=()=>{i=!0,a=!0,o.isProcessing||t(g)};return{schedule:n.reduce((t,e)=>{let s=h[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<n.length;e++)h[n[e]].cancel(t)},state:o,steps:h}}},60010:(t,e,i)=>{i.d(e,{V:()=>u,f:()=>m});var s=i(4272);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var r=i(30614),a=i(11557);let o="number",l="color",h=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function u(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],a=0,u=e.replace(h,t=>(s.y.test(t)?(n.color.push(a),r.push(l),i.push(s.y.parse(t))):t.startsWith("var(")?(n.var.push(a),r.push("var"),i.push(t)):(n.number.push(a),r.push(o),i.push(parseFloat(t))),++a,"${}")).split("${}");return{values:i,split:u,indexes:n,types:r}}function d(t){return u(t).values}function c(t){let{split:e,types:i}=u(t),n=e.length;return t=>{let r="";for(let h=0;h<n;h++)if(r+=e[h],void 0!==t[h]){let e=i[h];e===o?r+=(0,a.a)(t[h]):e===l?r+=s.y.transform(t[h]):r+=t[h]}return r}}let p=t=>"number"==typeof t?0:t,m={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(r.S)?.length||0)+(t.match(n)?.length||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},60018:(t,e,i)=>{i.d(e,{U:()=>s,f:()=>n});let s=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],n=new Set(s)},60098:(t,e,i)=>{i.d(e,{OQ:()=>u,bt:()=>l});var s=i(75626),n=i(62923),r=i(74261),a=i(69515);let o=t=>!isNaN(parseFloat(t)),l={current:void 0};class h{constructor(t,e={}){this.version="12.9.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=r.k.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change?.notify(this.current),e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=r.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new s.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=r.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,n.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function u(t,e){return new h(t,e)}},60280:(t,e,i)=>{i.d(e,{E4:()=>o,Hr:()=>d,W9:()=>u});var s=i(84160),n=i(60018),r=i(57887),a=i(34158);let o=t=>t===r.ai||t===a.px,l=new Set(["x","y","z"]),h=n.U.filter(t=>!l.has(t));function u(t){let e=[];return h.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}let d={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>(0,s.r)(e,"x"),y:(t,{transform:e})=>(0,s.r)(e,"y")};d.translateX=d.x,d.translateY=d.y},62923:(t,e,i)=>{i.d(e,{f:()=>s});function s(t,e){return e?1e3/e*t:0}},63704:(t,e,i)=>{i.d(e,{q:()=>s});let s={layout:0,mainThread:0,waapi:0}},68972:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},69515:(t,e,i)=>{i.d(e,{Gt:()=>n,PP:()=>o,WG:()=>r,uv:()=>a});var s=i(19827);let{schedule:n,cancel:r,state:a,steps:o}=(0,i(58437).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:s.l,!0)},71784:(t,e,i)=>{i.d(e,{Z:()=>r});var s=i(45818),n=i(33210);function r(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let a=(0,s.q)(0,e,r);t.push((0,n.k)(i,1,a))}}(e,t.length-1),e}},74261:(t,e,i)=>{let s;i.d(e,{k:()=>o});var n=i(23387),r=i(69515);function a(){s=void 0}let o={now:()=>(void 0===s&&o.set(r.uv.isProcessing||n.W.useManualTiming?r.uv.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(a)}}},75626:(t,e,i)=>{i.d(e,{v:()=>n});var s=i(56668);class n{constructor(){this.subscriptions=[]}add(t){return(0,s.Kq)(this.subscriptions,t),()=>(0,s.Ai)(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},76333:(t,e,i)=>{i.d(e,{g:()=>r});var s=i(23387),n=i(14570);function r(t,e){let i=t.getValue("willChange");if((0,n.S)(i)&&i.add)return i.add(e);if(!i&&s.W.WillChange){let i=new s.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}},78450:(t,e,i)=>{i.d(e,{I:()=>s});let s=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},78606:(t,e,i)=>{i.d(e,{j:()=>n,p:()=>a});let s=t=>e=>"string"==typeof e&&e.startsWith(t),n=s("--"),r=s("var(--"),a=t=>!!r(t)&&o.test(t.split("/*")[0].trim()),o=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},78777:(t,e,i)=>{i.d(e,{r:()=>s});function s(t,e){return t?.[e]??t?.default??t}},80845:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(12115).createContext)(null)},82885:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(12115);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},84160:(t,e,i)=>{i.d(e,{I:()=>c,r:()=>d});let s=t=>180*t/Math.PI,n=t=>a(s(Math.atan2(t[1],t[0]))),r={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:n,rotateZ:n,skewX:t=>s(Math.atan(t[1])),skewY:t=>s(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},a=t=>((t%=360)<0&&(t+=360),t),o=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),l=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),h={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:o,scaleY:l,scale:t=>(o(t)+l(t))/2,rotateX:t=>a(s(Math.atan2(t[6],t[5]))),rotateY:t=>a(s(Math.atan2(-t[2],t[0]))),rotateZ:n,rotate:n,skewX:t=>s(Math.atan(t[4])),skewY:t=>s(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function u(t){return+!!t.includes("scale")}function d(t,e){let i,s;if(!t||"none"===t)return u(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=h,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=r,s=e}if(!s)return u(e);let a=i[e],o=s[1].split(",").map(p);return"function"==typeof a?a(o):o[a]}let c=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return d(i,e)};function p(t){return parseFloat(t.trim())}},85982:(t,e,i)=>{i.d(e,{_:()=>m});var s=i(20419),n=i(78777),r=i(69515),a=i(58109),o=i(18802),l=i(76333),h=i(46926),u=i(98047);function d(t,e,{delay:i=0,transitionOverride:s,type:c}={}){let{transition:p=t.getDefaultTransition(),transitionEnd:m,...f}=e;s&&(p=s);let v=[],y=c&&t.animationState&&t.animationState.getState()[c];for(let e in f){let s=t.getValue(e,t.latestValues[e]??null),o=f[e];if(void 0===o||y&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(y,e))continue;let d={delay:i,...(0,n.r)(p||{},e)},c=s.get();if(void 0!==c&&!s.isAnimating&&!Array.isArray(o)&&o===c&&!d.velocity)continue;let m=!1;if(window.MotionHandoffAnimation){let i=(0,h.P)(t);if(i){let t=window.MotionHandoffAnimation(i,e,r.Gt);null!==t&&(d.startTime=t,m=!0)}}(0,l.g)(t,e),s.start((0,u.f)(e,s,o,t.shouldReduceMotion&&a.$.has(e)?{type:!1}:d,t,m));let g=s.animation;g&&v.push(g)}return m&&Promise.all(v).then(()=>{r.Gt.update(()=>{m&&(0,o.U)(t,m)})}),v}function c(t,e,i={}){let n=(0,s.K)(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let a=n?()=>Promise.all(d(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=r;return function(t,e,i=0,s=0,n=1,r){let a=[],o=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>o-t*s;return Array.from(t.variantChildren).sort(p).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(c(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,n+s,a,o,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([a(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[a,o]:[o,a];return t().then(()=>e())}}function p(t,e){return t.sortNodePosition(e)}function m(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>c(t,e,i)));else if("string"==typeof e)n=c(t,e,i);else{let r="function"==typeof e?(0,s.K)(t,e,i.custom):e;n=Promise.all(d(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})}},90869:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(12115).createContext)({})},91116:(t,e,i)=>{i.d(e,{J:()=>s});let s=(0,i(41917).p)(()=>void 0!==window.ScrollTimeline)},91765:(t,e,i)=>{i.d(e,{V:()=>s});let s=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},97494:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(12115);let n=i(68972).B?s.useLayoutEffect:s.useEffect},98047:(t,e,i)=>{i.d(e,{f:()=>tb});var s=i(78777),n=i(69515),r=i(23387),a=i(19827),o=i(74261),l=i(53191),h=i(53678),u=i(47215),d=i(63704),c=i(26087);let p=t=>{let e=({timestamp:e})=>t(e);return{start:()=>n.Gt.update(e,!0),stop:()=>(0,n.WG)(e),now:()=>n.uv.isProcessing?n.uv.timestamp:o.k.now()}},m=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(e/(n-1))+", ";return`linear(${s.substring(0,s.length-2)})`};function f(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}var v=i(62923);function y(t,e,i){let s=Math.max(e-5,0);return(0,v.f)(i-t(s),e-s)}let g={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var x=i(54542);function T(t,e){return t*Math.sqrt(1-e*e)}let w=["duration","bounce"],P=["stiffness","damping","mass"];function S(t,e){return e.some(e=>void 0!==t[e])}function b(t=g.visualDuration,e=g.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:d,damping:c,mass:p,duration:v,velocity:A,isResolvedFromDuration:V}=function(t){let e={velocity:g.velocity,stiffness:g.stiffness,damping:g.damping,mass:g.mass,isResolvedFromDuration:!1,...t};if(!S(t,P)&&S(t,w))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*(0,h.q)(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:g.mass,stiffness:s,damping:n}}else{let i=function({duration:t=g.duration,bounce:e=g.bounce,velocity:i=g.velocity,mass:s=g.mass}){let n,r;(0,x.$)(t<=(0,u.f)(g.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=(0,h.q)(g.minDamping,g.maxDamping,a),t=(0,h.q)(g.minDuration,g.maxDuration,(0,u.X)(t)),a<1?(n=e=>{let s=e*a,n=s*t;return .001-(s-i)/T(e,a)*Math.exp(-n)},r=e=>{let s=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-s),l=T(Math.pow(e,2),a);return(s*i+i-r)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=(0,u.f)(t),isNaN(o))return{stiffness:g.stiffness,damping:g.damping,duration:t};{let e=Math.pow(o,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:g.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-(0,u.X)(s.velocity||0)}),M=A||0,k=c/(2*Math.sqrt(d*p)),E=o-a,D=(0,u.X)(Math.sqrt(d/p)),C=5>Math.abs(E);if(n||(n=C?g.restSpeed.granular:g.restSpeed.default),r||(r=C?g.restDelta.granular:g.restDelta.default),k<1){let t=T(D,k);i=e=>o-Math.exp(-k*D*e)*((M+k*D*E)/t*Math.sin(t*e)+E*Math.cos(t*e))}else if(1===k)i=t=>o-Math.exp(-D*t)*(E+(M+D*E)*t);else{let t=D*Math.sqrt(k*k-1);i=e=>{let i=Math.exp(-k*D*e),s=Math.min(t*e,300);return o-i*((M+k*D*E)*Math.sinh(s)+t*E*Math.cosh(s))/t}}let R={calculatedDuration:V&&v||null,next:t=>{let e=i(t);if(V)l.done=t>=v;else{let s=0===t?M:0;k<1&&(s=0===t?(0,u.f)(M):y(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(s)<=n&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(f(R),2e4),e=m(e=>R.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return R}function A({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,v=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,g=i*e,x=p+g,T=void 0===a?x:a(x);T!==x&&(g=T-p);let w=t=>-g*Math.exp(-t/s),P=t=>T+w(t),S=t=>{let e=w(t),i=P(t);m.done=Math.abs(e)<=h,m.value=m.done?T:i},A=t=>{f(m.value)&&(d=t,c=b({keyframes:[m.value,v(m.value)],velocity:y(P,t,m.value),damping:n,stiffness:r,restDelta:h,restSpeed:u}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,S(t),A(t)),void 0!==d&&t>=d)?c.next(t-d):(e||S(t),m)}}}b.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(f(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:(0,u.X)(n)}}(t,100,b);return t.ease=e.ease,t.duration=(0,u.f)(e.duration),t.type="keyframes",t};let V=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function M(t,e,i,s){if(t===e&&i===s)return a.l;let n=e=>(function(t,e,i,s,n){let r,a,o=0;do(r=V(a=e+(i-e)/2,s,n)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:V(n(t),e,s)}let k=M(.42,0,1,1),E=M(0,0,.58,1),D=M(.42,0,.58,1),C=t=>Array.isArray(t)&&"number"!=typeof t[0];var R=i(91765),j=i(54180);let L=M(.33,1.53,.69,.99),F=(0,j.G)(L),B=(0,R.V)(F),O=t=>(t*=2)<1?.5*F(t):.5*(2-Math.pow(2,-10*(t-1)));var I=i(7712);let U=t=>Array.isArray(t)&&"number"==typeof t[0],W={linear:a.l,easeIn:k,easeInOut:D,easeOut:E,circIn:I.po,circInOut:I.tn,circOut:I.yT,backIn:F,backInOut:B,backOut:L,anticipate:O},$=t=>"string"==typeof t,N=t=>{if(U(t)){(0,x.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return M(e,i,s,n)}return $(t)?((0,x.V)(void 0!==W[t],`Invalid easing type '${t}'`),W[t]):t};var q=i(6775),G=i(71784);function X({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=C(s)?s.map(N):N(s),a={done:!1,value:e[0]},o=(n=i&&i.length===e.length?i:(0,G.Z)(e),n.map(e=>e*t)),l=(0,q.G)(o,e,{ease:Array.isArray(r)?r:e.map(()=>r||D).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=l(e),a.done=e>=t,a)}}let K=t=>null!==t;function Y(t,{repeat:e,repeatType:i="loop"},s,n=1){let r=t.filter(K),a=n<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}let H={decay:A,inertia:A,tween:X,keyframes:X,spring:b};function z(t){"string"==typeof t.type&&(t.type=H[t.type])}class Q{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let _=t=>t/100;class Z extends Q{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;if(t&&t.updatedAt!==o.k.now()&&this.tick(o.k.now()),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()},d.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;z(t);let{type:e=X,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:r=0}=t,{keyframes:a}=t,o=e||X;o!==X&&"number"!=typeof a[0]&&(this.mixKeyframes=(0,l.F)(_,(0,c.j)(a[0],a[1])),a=[0,100]);let h=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===h.calculatedDuration&&(h.calculatedDuration=f(h));let{calculatedDuration:u}=h;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=h}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:d,repeatType:c,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>s;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let x=this.currentTime,T=i;if(d){let t=Math.min(this.currentTime,s)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===c?(i=1-i,p&&(i-=p/a)):"mirror"===c&&(T=r)),x=(0,h.q)(0,1,i)*a}let w=g?{done:!1,value:u[0]}:T.next(x);n&&(w.value=n(w.value));let{done:P}=w;g||null===o||(P=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&m!==A&&(w.value=Y(u,this.options,v,this.speed)),f&&f(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return(0,u.X)(this.calculatedDuration)}get time(){return(0,u.X)(this.currentTime)}set time(t){t=(0,u.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(o.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,u.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=p,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=i??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(o.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,d.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),t.observe(this)}}var J=i(7322);let tt=t=>t.startsWith("--");var te=i(91116),ti=i(24744),ts=i(41917);let tn={},tr=function(t,e){let i=(0,ts.p)(t);return()=>tn[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),ta=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,to={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ta([0,.65,.55,1]),circOut:ta([.55,0,1,.45]),backIn:ta([.31,.01,.66,-.59]),backOut:ta([.33,1.53,.69,.99])};function tl(t){return"function"==typeof t&&"applyToOptions"in t}class th extends Q{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,(0,x.V)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return tl(t)&&tr()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?tr()?m(e,i):"ease-out":U(e)?ta(e):Array.isArray(e)?e.map(e=>t(e,i)||to.easeOut):to[e]}(o,n);Array.isArray(c)&&(u.easing=c),ti.Q.value&&d.q.waapi++;let p={delay:s,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(p.pseudoElement=h);let f=t.animate(u,p);return ti.Q.value&&f.finished.finally(()=>{d.q.waapi--}),f}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=Y(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){tt(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,u.X)(Number(t))}get time(){return(0,u.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,u.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&(0,te.J)())?(this.animation.timeline=t,a.l):e(this)}}let tu={anticipate:O,backInOut:B,circInOut:I.tn};class td extends th{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in tu&&(t.ease=tu[t.ease])}(t),z(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new Z({...r,autoplay:!1}),o=(0,u.f)(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}var tc=i(60010);let tp=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tc.f.test(t)||"0"===t)&&!t.startsWith("url(")),tm=new Set(["opacity","clipPath","filter","transform"]),tf=(0,ts.p)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tv extends Q{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:a,name:l,motionValue:h,element:u,...d}){super(),this.stop=()=>{this._animation?(this._animation.stop(),this.stopTimeline?.()):this.keyframeResolver?.cancel()},this.createdAt=o.k.now();let c={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:l,motionValue:h,element:u,...d},p=u?.KeyframeResolver||J.h;this.keyframeResolver=new p(a,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),l,h,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:l,velocity:h,delay:u,isHandoff:d,onUpdate:c}=i;this.resolvedAt=o.k.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=tp(n,e),o=tp(r,e);return(0,x.$)(a===o,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||tl(i))&&s)}(t,n,l,h)&&((r.W.instantAnimations||!u)&&c?.(Y(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let p={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},m=!d&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:a}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return tf()&&i&&tm.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==n&&0!==r&&"inertia"!==a}(p)?new td({...p,element:p.motionValue.owner.current}):new Z(p);m.finished.then(()=>this.notifyFinished()).catch(a.l),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(0,J.q)(),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this.animation.cancel()}}let ty=t=>null!==t;var tg=i(60018);let tx={type:"spring",stiffness:500,damping:25,restSpeed:10},tT=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),tw={type:"keyframes",duration:.8},tP={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tS=(t,{keyframes:e})=>e.length>2?tw:tg.f.has(t)?t.startsWith("scale")?tT(e[1]):tx:tP,tb=(t,e,i,a={},o,l)=>h=>{let d=(0,s.r)(a,t)||{},c=d.delay||a.delay||0,{elapsed:p=0}=a;p-=(0,u.f)(c);let m={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...d,delay:-p,onUpdate:t=>{e.set(t),d.onUpdate&&d.onUpdate(t)},onComplete:()=>{h(),d.onComplete&&d.onComplete()},name:t,motionValue:e,element:l?void 0:o};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(d)&&Object.assign(m,tS(t,m)),m.duration&&(m.duration=(0,u.f)(m.duration)),m.repeatDelay&&(m.repeatDelay=(0,u.f)(m.repeatDelay)),void 0!==m.from&&(m.keyframes[0]=m.from);let f=!1;if(!1!==m.type&&(0!==m.duration||m.repeatDelay)||(m.duration=0,0===m.delay&&(f=!0)),(r.W.instantAnimations||r.W.skipAnimations)&&(f=!0,m.duration=0,m.delay=0),m.allowFlatten=!d.type&&!d.ease,f&&!l&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(ty),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(m.keyframes,d);if(void 0!==t)return void n.Gt.update(()=>{m.onUpdate(t),m.onComplete()})}return new tv(m)}}}]);