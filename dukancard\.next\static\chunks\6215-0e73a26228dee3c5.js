"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6215],{76215:(e,r,n)=>{n.d(r,{UC:()=>ez,q7:()=>e$,JU:()=>eY,ZL:()=>eZ,bL:()=>eX,wv:()=>eJ,l9:()=>eH});var t=n(12115),o=n(85185),a=n(6101),l=n(46081),u=n(5845),i=n(63540),s=n(57683),d=n(94315),c=n(19178),f=n(92293),p=n(25519),m=n(61285),v=n(35152),h=n(34378),w=n(28905),g=n(89196),x=n(95155),y=Symbol("radix.slottable");function b(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}var C=n(39033),R=n(38168),j=n(31114),M=["Enter"," "],D=["ArrowUp","PageDown","End"],_=["ArrowDown","PageUp","Home",...D],k={ltr:[...M,"ArrowRight"],rtl:[...M,"ArrowLeft"]},E={ltr:["ArrowLeft"],rtl:["ArrowRight"]},I="Menu",[P,T,S]=(0,s.N)(I),[N,A]=(0,l.A)(I,[S,v.Bk,g.RG]),F=(0,v.Bk)(),O=(0,g.RG)(),[L,K]=N(I),[G,B]=N(I),U=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=F(r),[s,c]=t.useState(null),f=t.useRef(!1),p=(0,C.c)(l),m=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,x.jsx)(v.bL,{...i,children:(0,x.jsx)(L,{scope:r,open:n,onOpenChange:p,content:s,onContentChange:c,children:(0,x.jsx)(G,{scope:r,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:u,children:o})})})};U.displayName=I;var V=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=F(n);return(0,x.jsx)(v.Mz,{...o,...t,ref:r})});V.displayName="MenuAnchor";var W="MenuPortal",[q,X]=N(W,{forceMount:void 0}),H=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=K(W,r);return(0,x.jsx)(q,{scope:r,forceMount:n,children:(0,x.jsx)(w.C,{present:n||a.open,children:(0,x.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};H.displayName=W;var Z="MenuContent",[z,Y]=N(Z),$=t.forwardRef((e,r)=>{let n=X(Z,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=K(Z,e.__scopeMenu),l=B(Z,e.__scopeMenu);return(0,x.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(w.C,{present:t||a.open,children:(0,x.jsx)(P.Slot,{scope:e.__scopeMenu,children:l.modal?(0,x.jsx)(J,{...o,ref:r}):(0,x.jsx)(Q,{...o,ref:r})})})})}),J=t.forwardRef((e,r)=>{let n=K(Z,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,R.Eq)(e)},[]),(0,x.jsx)(er,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=K(Z,e.__scopeMenu);return(0,x.jsx)(er,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),ee=function(e){let r=function(e){let r=t.forwardRef((e,r)=>{let{children:n,...o}=e;if(t.isValidElement(n)){var l;let e,u,i=(l=n,(u=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(u=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,r){let n={...r};for(let t in r){let o=e[t],a=r[t];/^on[A-Z]/.test(t)?o&&a?n[t]=(...e)=>{a(...e),o(...e)}:o&&(n[t]=o):"style"===t?n[t]={...o,...a}:"className"===t&&(n[t]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==t.Fragment&&(s.ref=r?(0,a.t)(r,i):i),t.cloneElement(n,s)}return t.Children.count(n)>1?t.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),n=t.forwardRef((e,n)=>{let{children:o,...a}=e,l=t.Children.toArray(o),u=l.find(b);if(u){let e=u.props.children,o=l.map(r=>r!==u?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,x.jsx)(r,{...a,ref:n,children:t.isValidElement(e)?t.cloneElement(e,void 0,o):null})}return(0,x.jsx)(r,{...a,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}("MenuContent.ScrollLock"),er=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:y,onInteractOutside:b,onDismiss:C,disableOutsideScroll:R,...M}=e,k=K(Z,n),E=B(Z,n),I=F(n),P=O(n),S=T(n),[N,A]=t.useState(null),L=t.useRef(null),G=(0,a.s)(r,L,k.onContentChange),U=t.useRef(0),V=t.useRef(""),W=t.useRef(0),q=t.useRef(null),X=t.useRef("right"),H=t.useRef(0),Y=R?j.A:t.Fragment,$=e=>{var r,n;let t=V.current+e,o=S().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,l=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,l),i=null==(n=o.find(e=>e.textValue===u))?void 0:n.ref.current;!function e(r){V.current=r,window.clearTimeout(U.current),""!==r&&(U.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(U.current),[]),(0,f.Oh)();let J=t.useCallback(e=>{var r,n;return X.current===(null==(r=q.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,s=l.y,d=u.x,c=u.y;s>t!=c>t&&n<(d-i)*(t-s)/(c-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(n=q.current)?void 0:n.area)},[]);return(0,x.jsx)(z,{scope:n,searchRef:V,onItemEnter:t.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:t.useCallback(e=>{var r;J(e)||(null==(r=L.current)||r.focus(),A(null))},[J]),onTriggerLeave:t.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:W,onPointerGraceIntentChange:t.useCallback(e=>{q.current=e},[]),children:(0,x.jsx)(Y,{...R?{as:ee,allowPinchZoom:!0}:void 0,children:(0,x.jsx)(p.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=L.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,x.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:y,onInteractOutside:b,onDismiss:C,children:(0,x.jsx)(g.bL,{asChild:!0,...P,dir:E.dir,orientation:"vertical",loop:l,currentTabStopId:N,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.m)(m,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":e_(k.open),"data-radix-menu-content":"",dir:E.dir,...I,...M,ref:G,style:{outline:"none",...M.style},onKeyDown:(0,o.m)(M.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&$(e.key));let o=L.current;if(e.target!==o||!_.includes(e.key))return;e.preventDefault();let a=S().filter(e=>!e.disabled).map(e=>e.ref.current);D.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(U.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eI(e=>{let r=e.target,n=H.current!==e.clientX;e.currentTarget.contains(r)&&n&&(X.current=e.clientX>H.current?"right":"left",H.current=e.clientX)}))})})})})})})});$.displayName=Z;var en=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.sG.div,{role:"group",...t,ref:r})});en.displayName="MenuGroup";var et=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.sG.div,{...t,ref:r})});et.displayName="MenuLabel";var eo="MenuItem",ea="menu.itemSelect",el=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...u}=e,s=t.useRef(null),d=B(eo,e.__scopeMenu),c=Y(eo,e.__scopeMenu),f=(0,a.s)(r,s),p=t.useRef(!1);return(0,x.jsx)(eu,{...u,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let r=new CustomEvent(ea,{bubbles:!0,cancelable:!0});e.addEventListener(ea,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:r=>{var n;null==(n=e.onPointerDown)||n.call(e,r),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;p.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;n||r&&" "===e.key||M.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});el.displayName=eo;var eu=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:u,...s}=e,d=Y(eo,n),c=O(n),f=t.useRef(null),p=(0,a.s)(r,f),[m,v]=t.useState(!1),[h,w]=t.useState("");return t.useEffect(()=>{let e=f.current;if(e){var r;w((null!=(r=e.textContent)?r:"").trim())}},[s.children]),(0,x.jsx)(P.ItemSlot,{scope:n,disabled:l,textValue:null!=u?u:h,children:(0,x.jsx)(g.q7,{asChild:!0,...c,focusable:!l,children:(0,x.jsx)(i.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eI(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eI(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),ei=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,x.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,x.jsx)(el,{role:"menuitemcheckbox","aria-checked":ek(n)?"mixed":n,...a,ref:r,"data-state":eE(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!ek(n)||!n),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var es="MenuRadioGroup",[ed,ec]=N(es,{value:void 0,onValueChange:()=>{}}),ef=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,C.c)(t);return(0,x.jsx)(ed,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,x.jsx)(en,{...o,ref:r})})});ef.displayName=es;var ep="MenuRadioItem",em=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=ec(ep,e.__scopeMenu),l=n===a.value;return(0,x.jsx)(eh,{scope:e.__scopeMenu,checked:l,children:(0,x.jsx)(el,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":eE(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});em.displayName=ep;var ev="MenuItemIndicator",[eh,ew]=N(ev,{checked:!1}),eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=ew(ev,n);return(0,x.jsx)(w.C,{present:t||ek(a.checked)||!0===a.checked,children:(0,x.jsx)(i.sG.span,{...o,ref:r,"data-state":eE(a.checked)})})});eg.displayName=ev;var ex=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ex.displayName="MenuSeparator";var ey=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=F(n);return(0,x.jsx)(v.i3,{...o,...t,ref:r})});ey.displayName="MenuArrow";var[eb,eC]=N("MenuSub"),eR="MenuSubTrigger",ej=t.forwardRef((e,r)=>{let n=K(eR,e.__scopeMenu),l=B(eR,e.__scopeMenu),u=eC(eR,e.__scopeMenu),i=Y(eR,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=i,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,x.jsx)(V,{asChild:!0,...f,children:(0,x.jsx)(eu,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":e_(n.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var t;null==(t=e.onClick)||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eI(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eI(e=>{var r,t;p();let o=null==(r=n.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(t=n.content)?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&k[l.dir].includes(r.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),r.preventDefault()}})})})});ej.displayName=eR;var eM="MenuSubContent",eD=t.forwardRef((e,r)=>{let n=X(Z,e.__scopeMenu),{forceMount:l=n.forceMount,...u}=e,i=K(Z,e.__scopeMenu),s=B(Z,e.__scopeMenu),d=eC(eM,e.__scopeMenu),c=t.useRef(null),f=(0,a.s)(r,c);return(0,x.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(w.C,{present:l||i.open,children:(0,x.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,x.jsx)(er,{id:d.contentId,"aria-labelledby":d.triggerId,...u,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;s.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=E[s.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null==(t=d.trigger)||t.focus(),e.preventDefault()}})})})})})});function e_(e){return e?"open":"closed"}function ek(e){return"indeterminate"===e}function eE(e){return ek(e)?"indeterminate":e?"checked":"unchecked"}function eI(e){return r=>"mouse"===r.pointerType?e(r):void 0}eD.displayName=eM;var eP="DropdownMenu",[eT,eS]=(0,l.A)(eP,[A]),eN=A(),[eA,eF]=eT(eP),eO=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,d=eN(r),c=t.useRef(null),[f,p]=(0,u.i)({prop:a,defaultProp:null!=l&&l,onChange:i,caller:eP});return(0,x.jsx)(eA,{scope:r,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,x.jsx)(U,{...d,open:f,onOpenChange:p,dir:o,modal:s,children:n})})};eO.displayName=eP;var eL="DropdownMenuTrigger",eK=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,u=eF(eL,n),s=eN(n);return(0,x.jsx)(V,{asChild:!0,...s,children:(0,x.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eK.displayName=eL;var eG=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eN(r);return(0,x.jsx)(H,{...t,...n})};eG.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eF(eB,n),u=eN(n),i=t.useRef(!1);return(0,x.jsx)($,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eU.displayName=eB,t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(en,{...o,...t,ref:r})}).displayName="DropdownMenuGroup";var eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(et,{...o,...t,ref:r})});eV.displayName="DropdownMenuLabel";var eW=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(el,{...o,...t,ref:r})});eW.displayName="DropdownMenuItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(ei,{...o,...t,ref:r})}).displayName="DropdownMenuCheckboxItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(ef,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(em,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(eg,{...o,...t,ref:r})}).displayName="DropdownMenuItemIndicator";var eq=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(ex,{...o,...t,ref:r})});eq.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(ey,{...o,...t,ref:r})}).displayName="DropdownMenuArrow",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(ej,{...o,...t,ref:r})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,x.jsx)(eD,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eX=eO,eH=eK,eZ=eG,ez=eU,eY=eV,e$=eW,eJ=eq},89196:(e,r,n)=>{n.d(r,{RG:()=>b,bL:()=>I,q7:()=>P});var t=n(12115),o=n(85185),a=n(57683),l=n(6101),u=n(46081),i=n(61285),s=n(63540),d=n(39033),c=n(5845),f=n(94315),p=n(95155),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,g,x]=(0,a.N)(h),[y,b]=(0,u.A)(h,[x]),[C,R]=y(h),j=t.forwardRef((e,r)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(M,{...e,ref:r})})}));j.displayName=h;var M=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:i,currentTabStopId:w,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:y,onEntryFocus:b,preventScrollOnEntryFocus:R=!1,...j}=e,M=t.useRef(null),D=(0,l.s)(r,M),_=(0,f.jH)(i),[k,I]=(0,c.i)({prop:w,defaultProp:null!=x?x:null,onChange:y,caller:h}),[P,T]=t.useState(!1),S=(0,d.c)(b),N=g(n),A=t.useRef(!1),[F,O]=t.useState(0);return t.useEffect(()=>{let e=M.current;if(e)return e.addEventListener(m,S),()=>e.removeEventListener(m,S)},[S]),(0,p.jsx)(C,{scope:n,orientation:a,dir:_,loop:u,currentTabStopId:k,onItemFocus:t.useCallback(e=>I(e),[I]),onItemShiftTab:t.useCallback(()=>T(!0),[]),onFocusableItemAdd:t.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>O(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:P||0===F?-1:0,"data-orientation":a,...j,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{A.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!A.current;if(e.target===e.currentTarget&&r&&!P){let r=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=N().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),R)}}A.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),D="RovingFocusGroupItem",_=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:l=!1,tabStopId:u,children:d,...c}=e,f=(0,i.B)(),m=u||f,v=R(D,n),h=v.currentTabStopId===m,x=g(n),{onFocusableItemAdd:y,onFocusableItemRemove:b,currentTabStopId:C}=v;return t.useEffect(()=>{if(a)return y(),()=>b()},[a,y,b]),(0,p.jsx)(w.ItemSlot,{scope:n,id:m,focusable:a,active:l,children:(0,p.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...c,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return k[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)n.reverse();else if("prev"===r||"next"===r){"prev"===r&&n.reverse();let t=n.indexOf(e.currentTarget);n=v.loop?function(e,r){return e.map((n,t)=>e[(r+t)%e.length])}(n,t+1):n.slice(t+1)}setTimeout(()=>E(n))}}),children:"function"==typeof d?d({isCurrentTabStop:h,hasTabStop:null!=C}):d})})});_.displayName=D;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var I=j,P=_}}]);