"use strict";exports.id=8235,exports.ids=[8235],exports.modules={8235:(e,r,s)=>{s.d(r,{createCustomer:()=>c,findCustomerByEmail:()=>n});var o=s(95453);async function c(e){try{let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/customers`,{method:"POST",headers:{...r,"Content-Type":"application/json"},body:JSON.stringify(e)}),c=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error creating customer:",c),{success:!1,error:c};return{success:!0,data:c}}catch(e){return console.error("[RAZORPAY_ERROR] Exception creating customer:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function t(e={}){try{let r=(0,o.bG)(),s=Object.entries(e).map(([e,r])=>`${e}=${encodeURIComponent(r)}`).join("&"),c=`${o.ST.replace("/v2","/v1")}/customers${s?`?${s}`:""}`,t=await fetch(c,{method:"GET",headers:r}),n=await t.json();if(!t.ok)return console.error("[RAZORPAY_ERROR] Error fetching customers:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY_ERROR] Exception fetching customers:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function n(e){try{let r=await t({count:100});if(!r.success)return{success:!1,error:r.error};if(!r.data)return{success:!1,error:{message:"Invalid response format from Razorpay",code:"INVALID_RESPONSE",type:"ERROR"}};let s=r.data.items.find(r=>r.email.toLowerCase()===e.toLowerCase());if(s)return{success:!0,data:s};return{success:!1,error:{message:`No customer found with email: ${e}`,code:"CUSTOMER_NOT_FOUND",type:"NOT_FOUND"}}}catch(e){return console.error("[RAZORPAY_ERROR] Exception finding customer by email:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}}};