(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5604],{8542:function(e,t,r){(function(e){"use strict";let t;var n,i,s,o,a,l,h,u,d,c,g,f,w,A,C,E,m,_,I,S,p,T,R=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},N=function(e){function t(){this.constructor=r}function r(t){var r,n,i,s,o=this.constructor,a=e.call(this,t)||this;return Object.defineProperty(a,"name",{value:o.name,enumerable:!1}),r=o.prototype,(n=Object.setPrototypeOf)?n(a,r):a.__proto__=r,void 0===i&&(i=a.constructor),(s=Error.captureStackTrace)&&s(a,i),a}return R(r,e),r.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t),r}(Error);class D extends N{constructor(e){super(e),this.message=e}getKind(){return this.constructor.kind}}D.kind="Exception";class y extends D{}y.kind="ArgumentException";class O extends D{}O.kind="IllegalArgumentException";class M{constructor(e){if(this.binarizer=e,null===e)throw new O("Binarizer must be non-null.")}getWidth(){return this.binarizer.getWidth()}getHeight(){return this.binarizer.getHeight()}getBlackRow(e,t){return this.binarizer.getBlackRow(e,t)}getBlackMatrix(){return(null===this.matrix||void 0===this.matrix)&&(this.matrix=this.binarizer.getBlackMatrix()),this.matrix}isCropSupported(){return this.binarizer.getLuminanceSource().isCropSupported()}crop(e,t,r,n){let i=this.binarizer.getLuminanceSource().crop(e,t,r,n);return new M(this.binarizer.createBinarizer(i))}isRotateSupported(){return this.binarizer.getLuminanceSource().isRotateSupported()}rotateCounterClockwise(){let e=this.binarizer.getLuminanceSource().rotateCounterClockwise();return new M(this.binarizer.createBinarizer(e))}rotateCounterClockwise45(){let e=this.binarizer.getLuminanceSource().rotateCounterClockwise45();return new M(this.binarizer.createBinarizer(e))}toString(){try{return this.getBlackMatrix().toString()}catch(e){return""}}}class B extends D{static getChecksumInstance(){return new B}}B.kind="ChecksumException";class b{constructor(e){this.source=e}getLuminanceSource(){return this.source}getWidth(){return this.source.getWidth()}getHeight(){return this.source.getHeight()}}class P{static arraycopy(e,t,r,n,i){for(;i--;)r[n++]=e[t++]}static currentTimeMillis(){return Date.now()}}class L extends D{}L.kind="IndexOutOfBoundsException";class F extends L{constructor(e,t){super(t),this.index=e,this.message=t}}F.kind="ArrayIndexOutOfBoundsException";class v{static fill(e,t){for(let r=0,n=e.length;r<n;r++)e[r]=t}static fillWithin(e,t,r,n){v.rangeCheck(e.length,t,r);for(let i=t;i<r;i++)e[i]=n}static rangeCheck(e,t,r){if(t>r)throw new O("fromIndex("+t+") > toIndex("+r+")");if(t<0)throw new F(t);if(r>e)throw new F(r)}static asList(...e){return e}static create(e,t,r){return Array.from({length:e}).map(e=>Array.from({length:t}).fill(r))}static createInt32Array(e,t,r){return Array.from({length:e}).map(e=>Int32Array.from({length:t}).fill(r))}static equals(e,t){if(!e||!t||!e.length||!t.length||e.length!==t.length)return!1;for(let r=0,n=e.length;r<n;r++)if(e[r]!==t[r])return!1;return!0}static hashCode(e){if(null===e)return 0;let t=1;for(let r of e)t=31*t+r;return t}static fillUint8Array(e,t){for(let r=0;r!==e.length;r++)e[r]=t}static copyOf(e,t){return e.slice(0,t)}static copyOfUint8Array(e,t){if(e.length<=t){let r=new Uint8Array(t);return r.set(e),r}return e.slice(0,t)}static copyOfRange(e,t,r){let n=r-t,i=new Int32Array(n);return P.arraycopy(e,t,i,0,n),i}static binarySearch(e,t,r){void 0===r&&(r=v.numberComparator);let n=0,i=e.length-1;for(;n<=i;){let s=i+n>>1,o=r(t,e[s]);if(o>0)n=s+1;else{if(!(o<0))return s;i=s-1}}return-n-1}static numberComparator(e,t){return e-t}}class k{static numberOfTrailingZeros(e){let t;if(0===e)return 32;let r=31;return 0!=(t=e<<16)&&(r-=16,e=t),0!=(t=e<<8)&&(r-=8,e=t),0!=(t=e<<4)&&(r-=4,e=t),0!=(t=e<<2)&&(r-=2,e=t),r-(e<<1>>>31)}static numberOfLeadingZeros(e){if(0===e)return 32;let t=1;return e>>>16==0&&(t+=16,e<<=16),e>>>24==0&&(t+=8,e<<=8),e>>>28==0&&(t+=4,e<<=4),e>>>30==0&&(t+=2,e<<=2),t-=e>>>31}static toHexString(e){return e.toString(16)}static toBinaryString(e){return String(parseInt(String(e),2))}static bitCount(e){return e-=e>>>1&0x55555555,e=(e=(0x33333333&e)+(e>>>2&0x33333333))+(e>>>4)&0xf0f0f0f,e+=e>>>8,63&(e+=e>>>16)}static truncDivision(e,t){return Math.trunc(e/t)}static parseInt(e,t){return parseInt(e,t)}}k.MIN_VALUE_32_BITS=-0x80000000,k.MAX_VALUE=Number.MAX_SAFE_INTEGER;class x{constructor(e,t){void 0===e?(this.size=0,this.bits=new Int32Array(1)):(this.size=e,null==t?this.bits=x.makeArray(e):this.bits=t)}getSize(){return this.size}getSizeInBytes(){return Math.floor((this.size+7)/8)}ensureCapacity(e){if(e>32*this.bits.length){let t=x.makeArray(e);P.arraycopy(this.bits,0,t,0,this.bits.length),this.bits=t}}get(e){return(this.bits[Math.floor(e/32)]&1<<(31&e))!=0}set(e){this.bits[Math.floor(e/32)]|=1<<(31&e)}flip(e){this.bits[Math.floor(e/32)]^=1<<(31&e)}getNextSet(e){let t=this.size;if(e>=t)return t;let r=this.bits,n=Math.floor(e/32),i=r[n];i&=~((1<<(31&e))-1);let s=r.length;for(;0===i;){if(++n===s)return t;i=r[n]}let o=32*n+k.numberOfTrailingZeros(i);return o>t?t:o}getNextUnset(e){let t=this.size;if(e>=t)return t;let r=this.bits,n=Math.floor(e/32),i=~r[n];i&=~((1<<(31&e))-1);let s=r.length;for(;0===i;){if(++n===s)return t;i=~r[n]}let o=32*n+k.numberOfTrailingZeros(i);return o>t?t:o}setBulk(e,t){this.bits[Math.floor(e/32)]=t}setRange(e,t){if(t<e||e<0||t>this.size)throw new O;if(t===e)return;let r=Math.floor(e/32),n=Math.floor(--t/32),i=this.bits;for(let s=r;s<=n;s++){let o=s>r?0:31&e,a=(2<<(s<n?31:31&t))-(1<<o);i[s]|=a}}clear(){let e=this.bits.length,t=this.bits;for(let r=0;r<e;r++)t[r]=0}isRange(e,t,r){if(t<e||e<0||t>this.size)throw new O;if(t===e)return!0;let n=Math.floor(e/32),i=Math.floor(--t/32),s=this.bits;for(let o=n;o<=i;o++){let a=o>n?0:31&e,l=(2<<(o<i?31:31&t))-(1<<a)|0;if((s[o]&l)!==(r?l:0))return!1}return!0}appendBit(e){this.ensureCapacity(this.size+1),e&&(this.bits[Math.floor(this.size/32)]|=1<<(31&this.size)),this.size++}appendBits(e,t){if(t<0||t>32)throw new O("Num bits must be between 0 and 32");this.ensureCapacity(this.size+t);for(let r=t;r>0;r--)this.appendBit((e>>r-1&1)==1)}appendBitArray(e){let t=e.size;this.ensureCapacity(this.size+t);for(let r=0;r<t;r++)this.appendBit(e.get(r))}xor(e){if(this.size!==e.size)throw new O("Sizes don't match");let t=this.bits;for(let r=0,n=t.length;r<n;r++)t[r]^=e.bits[r]}toBytes(e,t,r,n){for(let i=0;i<n;i++){let n=0;for(let t=0;t<8;t++)this.get(e)&&(n|=1<<7-t),e++;t[r+i]=n}}getBitArray(){return this.bits}reverse(){let e=new Int32Array(this.bits.length),t=Math.floor((this.size-1)/32),r=t+1,n=this.bits;for(let i=0;i<r;i++){let r=n[i];r=(r=(r=(r=(r=r>>1&0x55555555|(0x55555555&r)<<1)>>2&0x33333333|(0x33333333&r)<<2)>>4&0xf0f0f0f|(0xf0f0f0f&r)<<4)>>8&0xff00ff|(0xff00ff&r)<<8)>>16&65535|(65535&r)<<16,e[t-i]=r}if(this.size!==32*r){let t=32*r-this.size,n=e[0]>>>t;for(let i=1;i<r;i++){let r=e[i];n|=r<<32-t,e[i-1]=n,n=r>>>t}e[r-1]=n}this.bits=e}static makeArray(e){return new Int32Array(Math.floor((e+31)/32))}equals(e){return e instanceof x&&this.size===e.size&&v.equals(this.bits,e.bits)}hashCode(){return 31*this.size+v.hashCode(this.bits)}toString(){let e="";for(let t=0,r=this.size;t<r;t++)(7&t)==0&&(e+=" "),e+=this.get(t)?"X":".";return e}clone(){return new x(this.size,this.bits.slice())}}(n=f||(f={}))[n.OTHER=0]="OTHER",n[n.PURE_BARCODE=1]="PURE_BARCODE",n[n.POSSIBLE_FORMATS=2]="POSSIBLE_FORMATS",n[n.TRY_HARDER=3]="TRY_HARDER",n[n.CHARACTER_SET=4]="CHARACTER_SET",n[n.ALLOWED_LENGTHS=5]="ALLOWED_LENGTHS",n[n.ASSUME_CODE_39_CHECK_DIGIT=6]="ASSUME_CODE_39_CHECK_DIGIT",n[n.ASSUME_GS1=7]="ASSUME_GS1",n[n.RETURN_CODABAR_START_END=8]="RETURN_CODABAR_START_END",n[n.NEED_RESULT_POINT_CALLBACK=9]="NEED_RESULT_POINT_CALLBACK",n[n.ALLOWED_EAN_EXTENSIONS=10]="ALLOWED_EAN_EXTENSIONS";var V=f;class U extends D{static getFormatInstance(){return new U}}U.kind="FormatException",(i=w||(w={}))[i.Cp437=0]="Cp437",i[i.ISO8859_1=1]="ISO8859_1",i[i.ISO8859_2=2]="ISO8859_2",i[i.ISO8859_3=3]="ISO8859_3",i[i.ISO8859_4=4]="ISO8859_4",i[i.ISO8859_5=5]="ISO8859_5",i[i.ISO8859_6=6]="ISO8859_6",i[i.ISO8859_7=7]="ISO8859_7",i[i.ISO8859_8=8]="ISO8859_8",i[i.ISO8859_9=9]="ISO8859_9",i[i.ISO8859_10=10]="ISO8859_10",i[i.ISO8859_11=11]="ISO8859_11",i[i.ISO8859_13=12]="ISO8859_13",i[i.ISO8859_14=13]="ISO8859_14",i[i.ISO8859_15=14]="ISO8859_15",i[i.ISO8859_16=15]="ISO8859_16",i[i.SJIS=16]="SJIS",i[i.Cp1250=17]="Cp1250",i[i.Cp1251=18]="Cp1251",i[i.Cp1252=19]="Cp1252",i[i.Cp1256=20]="Cp1256",i[i.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",i[i.UTF8=22]="UTF8",i[i.ASCII=23]="ASCII",i[i.Big5=24]="Big5",i[i.GB18030=25]="GB18030",i[i.EUC_KR=26]="EUC_KR";class H{constructor(e,t,r,...n){this.valueIdentifier=e,this.name=r,"number"==typeof t?this.values=Int32Array.from([t]):this.values=t,this.otherEncodingNames=n,H.VALUE_IDENTIFIER_TO_ECI.set(e,this),H.NAME_TO_ECI.set(r,this);let i=this.values;for(let e=0,t=i.length;e!==t;e++){let t=i[e];H.VALUES_TO_ECI.set(t,this)}for(let e of n)H.NAME_TO_ECI.set(e,this)}getValueIdentifier(){return this.valueIdentifier}getName(){return this.name}getValue(){return this.values[0]}static getCharacterSetECIByValue(e){if(e<0||e>=900)throw new U("incorect value");let t=H.VALUES_TO_ECI.get(e);if(void 0===t)throw new U("incorect value");return t}static getCharacterSetECIByName(e){let t=H.NAME_TO_ECI.get(e);if(void 0===t)throw new U("incorect value");return t}equals(e){return e instanceof H&&this.getName()===e.getName()}}H.VALUE_IDENTIFIER_TO_ECI=new Map,H.VALUES_TO_ECI=new Map,H.NAME_TO_ECI=new Map,H.Cp437=new H(w.Cp437,Int32Array.from([0,2]),"Cp437"),H.ISO8859_1=new H(w.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),H.ISO8859_2=new H(w.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),H.ISO8859_3=new H(w.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),H.ISO8859_4=new H(w.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),H.ISO8859_5=new H(w.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),H.ISO8859_6=new H(w.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),H.ISO8859_7=new H(w.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),H.ISO8859_8=new H(w.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),H.ISO8859_9=new H(w.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),H.ISO8859_10=new H(w.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),H.ISO8859_11=new H(w.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),H.ISO8859_13=new H(w.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),H.ISO8859_14=new H(w.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),H.ISO8859_15=new H(w.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),H.ISO8859_16=new H(w.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),H.SJIS=new H(w.SJIS,20,"SJIS","Shift_JIS"),H.Cp1250=new H(w.Cp1250,21,"Cp1250","windows-1250"),H.Cp1251=new H(w.Cp1251,22,"Cp1251","windows-1251"),H.Cp1252=new H(w.Cp1252,23,"Cp1252","windows-1252"),H.Cp1256=new H(w.Cp1256,24,"Cp1256","windows-1256"),H.UnicodeBigUnmarked=new H(w.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),H.UTF8=new H(w.UTF8,26,"UTF8","UTF-8"),H.ASCII=new H(w.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),H.Big5=new H(w.Big5,28,"Big5"),H.GB18030=new H(w.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),H.EUC_KR=new H(w.EUC_KR,30,"EUC_KR","EUC-KR");class G extends D{}G.kind="UnsupportedOperationException";class X{static decode(e,t){let r=this.encodingName(t);return this.customDecoder?this.customDecoder(e,r):"undefined"==typeof TextDecoder||this.shouldDecodeOnFallback(r)?this.decodeFallback(e,r):new TextDecoder(r).decode(e)}static shouldDecodeOnFallback(e){return!X.isBrowser()&&"ISO-8859-1"===e}static encode(e,t){let r=this.encodingName(t);return this.customEncoder?this.customEncoder(e,r):"undefined"==typeof TextEncoder?this.encodeFallback(e):new TextEncoder().encode(e)}static isBrowser(){return"undefined"!=typeof window&&"[object Window]"===({}).toString.call(window)}static encodingName(e){return"string"==typeof e?e:e.getName()}static encodingCharacterSet(e){return e instanceof H?e:H.getCharacterSetECIByName(e)}static decodeFallback(e,t){let r=this.encodingCharacterSet(t);if(X.isDecodeFallbackSupported(r)){let t="";for(let r=0,n=e.length;r<n;r++){let n=e[r].toString(16);n.length<2&&(n="0"+n),t+="%"+n}return decodeURIComponent(t)}if(r.equals(H.UnicodeBigUnmarked))return String.fromCharCode.apply(null,new Uint16Array(e.buffer));throw new G(`Encoding ${this.encodingName(t)} not supported by fallback.`)}static isDecodeFallbackSupported(e){return e.equals(H.UTF8)||e.equals(H.ISO8859_1)||e.equals(H.ASCII)}static encodeFallback(e){let t=btoa(unescape(encodeURIComponent(e))).split(""),r=[];for(let e=0;e<t.length;e++)r.push(t[e].charCodeAt(0));return new Uint8Array(r)}}class W{static castAsNonUtf8Char(e,t=null){let r=t?t.getName():this.ISO88591;return X.decode(new Uint8Array([e]),r)}static guessEncoding(e,t){if(null!=t&&void 0!==t.get(V.CHARACTER_SET))return t.get(V.CHARACTER_SET).toString();let r=e.length,n=!0,i=!0,s=!0,o=0,a=0,l=0,h=0,u=0,d=0,c=0,g=0,f=0,w=0,A=0,C=e.length>3&&239===e[0]&&187===e[1]&&191===e[2];for(let t=0;t<r&&(n||i||s);t++){let r=255&e[t];s&&(o>0?(128&r)==0?s=!1:o--:(128&r)!=0&&((64&r)==0?s=!1:(o++,(32&r)==0?a++:(o++,(16&r)==0?l++:(o++,(8&r)==0?h++:s=!1))))),n&&(r>127&&r<160?n=!1:r>159&&(r<192||215===r||247===r)&&A++),i&&(u>0?r<64||127===r||r>252?i=!1:u--:128===r||160===r||r>239?i=!1:r>160&&r<224?(d++,g=0,++c>f&&(f=c)):r>127?(u++,c=0,++g>w&&(w=g)):(c=0,g=0))}return(s&&o>0&&(s=!1),i&&u>0&&(i=!1),s&&(C||a+l+h>0))?W.UTF8:i&&(W.ASSUME_SHIFT_JIS||f>=3||w>=3)?W.SHIFT_JIS:n&&i?2===f&&2===d||10*A>=r?W.SHIFT_JIS:W.ISO88591:n?W.ISO88591:i?W.SHIFT_JIS:s?W.UTF8:W.PLATFORM_DEFAULT_ENCODING}static format(e,...t){let r=-1;return e.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function(e,n,i,s,o,a){let l;if("%%"===e)return"%";if(void 0===t[++r])return;e=s?parseInt(s.substr(1)):void 0;let h=o?parseInt(o.substr(1)):void 0;switch(a){case"s":l=t[r];break;case"c":l=t[r][0];break;case"f":l=parseFloat(t[r]).toFixed(e);break;case"p":l=parseFloat(t[r]).toPrecision(e);break;case"e":l=parseFloat(t[r]).toExponential(e);break;case"x":l=parseInt(t[r]).toString(h||16);break;case"d":l=parseFloat(parseInt(t[r],h||10).toPrecision(e)).toFixed(0)}l="object"==typeof l?JSON.stringify(l):(+l).toString(h);let u=parseInt(i),d=i&&i[0]+""=="0"?"0":" ";for(;l.length<u;)l=void 0!==n?l+d:d+l;return l})}static getBytes(e,t){return X.encode(e,t)}static getCharCode(e,t=0){return e.charCodeAt(t)}static getCharAt(e){return String.fromCharCode(e)}}W.SHIFT_JIS=H.SJIS.getName(),W.GB2312="GB2312",W.ISO88591=H.ISO8859_1.getName(),W.EUC_JP="EUC_JP",W.UTF8=H.UTF8.getName(),W.PLATFORM_DEFAULT_ENCODING=W.UTF8,W.ASSUME_SHIFT_JIS=!1;class z{constructor(e=""){this.value=e}enableDecoding(e){return this.encoding=e,this}append(e){return"string"==typeof e?this.value+=e.toString():this.encoding?this.value+=W.castAsNonUtf8Char(e,this.encoding):this.value+=String.fromCharCode(e),this}appendChars(e,t,r){for(let n=t;t<t+r;n++)this.append(e[n]);return this}length(){return this.value.length}charAt(e){return this.value.charAt(e)}deleteCharAt(e){this.value=this.value.substr(0,e)+this.value.substring(e+1)}setCharAt(e,t){this.value=this.value.substr(0,e)+t+this.value.substr(e+1)}substring(e,t){return this.value.substring(e,t)}setLengthToZero(){this.value=""}toString(){return this.value}insert(e,t){this.value=this.value.substr(0,e)+t+this.value.substr(e+t.length)}}class Y{constructor(e,t,r,n){if(this.width=e,this.height=t,this.rowSize=r,this.bits=n,null==t&&(t=e),this.height=t,e<1||t<1)throw new O("Both dimensions must be greater than 0");null==r&&(r=Math.floor((e+31)/32)),this.rowSize=r,null==n&&(this.bits=new Int32Array(this.rowSize*this.height))}static parseFromBooleanArray(e){let t=e.length,r=e[0].length,n=new Y(r,t);for(let i=0;i<t;i++){let t=e[i];for(let e=0;e<r;e++)t[e]&&n.set(e,i)}return n}static parseFromString(e,t,r){if(null===e)throw new O("stringRepresentation cannot be null");let n=Array(e.length),i=0,s=0,o=-1,a=0,l=0;for(;l<e.length;)if("\n"===e.charAt(l)||"\r"===e.charAt(l)){if(i>s){if(-1===o)o=i-s;else if(i-s!==o)throw new O("row lengths do not match");s=i,a++}l++}else if(e.substring(l,l+t.length)===t)l+=t.length,n[i]=!0,i++;else if(e.substring(l,l+r.length)===r)l+=r.length,n[i]=!1,i++;else throw new O("illegal character encountered: "+e.substring(l));if(i>s){if(-1===o)o=i-s;else if(i-s!==o)throw new O("row lengths do not match");a++}let h=new Y(o,a);for(let e=0;e<i;e++)n[e]&&h.set(Math.floor(e%o),Math.floor(e/o));return h}get(e,t){let r=t*this.rowSize+Math.floor(e/32);return(this.bits[r]>>>(31&e)&1)!=0}set(e,t){let r=t*this.rowSize+Math.floor(e/32);this.bits[r]|=1<<(31&e)}unset(e,t){let r=t*this.rowSize+Math.floor(e/32);this.bits[r]&=~(1<<(31&e))}flip(e,t){let r=t*this.rowSize+Math.floor(e/32);this.bits[r]^=1<<(31&e)}xor(e){if(this.width!==e.getWidth()||this.height!==e.getHeight()||this.rowSize!==e.getRowSize())throw new O("input matrix dimensions do not match");let t=new x(Math.floor(this.width/32)+1),r=this.rowSize,n=this.bits;for(let i=0,s=this.height;i<s;i++){let s=i*r,o=e.getRow(i,t).getBitArray();for(let e=0;e<r;e++)n[s+e]^=o[e]}}clear(){let e=this.bits,t=e.length;for(let r=0;r<t;r++)e[r]=0}setRegion(e,t,r,n){if(t<0||e<0)throw new O("Left and top must be nonnegative");if(n<1||r<1)throw new O("Height and width must be at least 1");let i=e+r,s=t+n;if(s>this.height||i>this.width)throw new O("The region must fit inside the matrix");let o=this.rowSize,a=this.bits;for(let r=t;r<s;r++){let t=r*o;for(let r=e;r<i;r++)a[t+Math.floor(r/32)]|=1<<(31&r)}}getRow(e,t){null==t||t.getSize()<this.width?t=new x(this.width):t.clear();let r=this.rowSize,n=this.bits,i=e*r;for(let e=0;e<r;e++)t.setBulk(32*e,n[i+e]);return t}setRow(e,t){P.arraycopy(t.getBitArray(),0,this.bits,e*this.rowSize,this.rowSize)}rotate180(){let e=this.getWidth(),t=this.getHeight(),r=new x(e),n=new x(e);for(let e=0,i=Math.floor((t+1)/2);e<i;e++)r=this.getRow(e,r),n=this.getRow(t-1-e,n),r.reverse(),n.reverse(),this.setRow(e,n),this.setRow(t-1-e,r)}getEnclosingRectangle(){let e=this.width,t=this.height,r=this.rowSize,n=this.bits,i=e,s=t,o=-1,a=-1;for(let e=0;e<t;e++)for(let t=0;t<r;t++){let l=n[e*r+t];if(0!==l){if(e<s&&(s=e),e>a&&(a=e),32*t<i){let e=0;for(;l<<31-e==0;)e++;32*t+e<i&&(i=32*t+e)}if(32*t+31>o){let e=31;for(;l>>>e==0;)e--;32*t+e>o&&(o=32*t+e)}}}return o<i||a<s?null:Int32Array.from([i,s,o-i+1,a-s+1])}getTopLeftOnBit(){let e=this.rowSize,t=this.bits,r=0;for(;r<t.length&&0===t[r];)r++;if(r===t.length)return null;let n=r/e,i=r%e*32,s=t[r],o=0;for(;s<<31-o==0;)o++;return i+=o,Int32Array.from([i,n])}getBottomRightOnBit(){let e=this.rowSize,t=this.bits,r=t.length-1;for(;r>=0&&0===t[r];)r--;if(r<0)return null;let n=Math.floor(r/e),i=32*Math.floor(r%e),s=t[r],o=31;for(;s>>>o==0;)o--;return i+=o,Int32Array.from([i,n])}getWidth(){return this.width}getHeight(){return this.height}getRowSize(){return this.rowSize}equals(e){return e instanceof Y&&this.width===e.width&&this.height===e.height&&this.rowSize===e.rowSize&&v.equals(this.bits,e.bits)}hashCode(){let e=this.width;return 31*(e=31*(e=31*(e=31*e+this.width)+this.height)+this.rowSize)+v.hashCode(this.bits)}toString(e="X ",t="  ",r="\n"){return this.buildToString(e,t,r)}buildToString(e,t,r){let n=new z;for(let i=0,s=this.height;i<s;i++){for(let r=0,s=this.width;r<s;r++)n.append(this.get(r,i)?e:t);n.append(r)}return n.toString()}clone(){return new Y(this.width,this.height,this.rowSize,this.bits.slice())}}class Z extends D{static getNotFoundInstance(){return new Z}}Z.kind="NotFoundException";class K extends b{constructor(e){super(e),this.luminances=K.EMPTY,this.buckets=new Int32Array(K.LUMINANCE_BUCKETS)}getBlackRow(e,t){let r=this.getLuminanceSource(),n=r.getWidth();null==t||t.getSize()<n?t=new x(n):t.clear(),this.initArrays(n);let i=r.getRow(e,this.luminances),s=this.buckets;for(let e=0;e<n;e++)s[(255&i[e])>>K.LUMINANCE_SHIFT]++;let o=K.estimateBlackPoint(s);if(n<3)for(let e=0;e<n;e++)(255&i[e])<o&&t.set(e);else{let e=255&i[0],r=255&i[1];for(let s=1;s<n-1;s++){let n=255&i[s+1];(4*r-e-n)/2<o&&t.set(s),e=r,r=n}}return t}getBlackMatrix(){let e=this.getLuminanceSource(),t=e.getWidth(),r=e.getHeight(),n=new Y(t,r);this.initArrays(t);let i=this.buckets;for(let n=1;n<5;n++){let s=Math.floor(r*n/5),o=e.getRow(s,this.luminances),a=Math.floor(4*t/5);for(let e=Math.floor(t/5);e<a;e++){let t=255&o[e];i[t>>K.LUMINANCE_SHIFT]++}}let s=K.estimateBlackPoint(i),o=e.getMatrix();for(let e=0;e<r;e++){let r=e*t;for(let i=0;i<t;i++)(255&o[r+i])<s&&n.set(i,e)}return n}createBinarizer(e){return new K(e)}initArrays(e){this.luminances.length<e&&(this.luminances=new Uint8ClampedArray(e));let t=this.buckets;for(let e=0;e<K.LUMINANCE_BUCKETS;e++)t[e]=0}static estimateBlackPoint(e){let t=e.length,r=0,n=0,i=0;for(let s=0;s<t;s++)e[s]>i&&(n=s,i=e[s]),e[s]>r&&(r=e[s]);let s=0,o=0;for(let r=0;r<t;r++){let t=r-n,i=e[r]*t*t;i>o&&(s=r,o=i)}if(n>s){let e=n;n=s,s=e}if(s-n<=t/16)throw new Z;let a=s-1,l=-1;for(let t=s-1;t>n;t--){let i=t-n,o=i*i*(s-t)*(r-e[t]);o>l&&(a=t,l=o)}return a<<K.LUMINANCE_SHIFT}}K.LUMINANCE_BITS=5,K.LUMINANCE_SHIFT=8-K.LUMINANCE_BITS,K.LUMINANCE_BUCKETS=1<<K.LUMINANCE_BITS,K.EMPTY=Uint8ClampedArray.from([0]);class q extends K{constructor(e){super(e),this.matrix=null}getBlackMatrix(){if(null!==this.matrix)return this.matrix;let e=this.getLuminanceSource(),t=e.getWidth(),r=e.getHeight();if(t>=q.MINIMUM_DIMENSION&&r>=q.MINIMUM_DIMENSION){let n=e.getMatrix(),i=t>>q.BLOCK_SIZE_POWER;(t&q.BLOCK_SIZE_MASK)!=0&&i++;let s=r>>q.BLOCK_SIZE_POWER;(r&q.BLOCK_SIZE_MASK)!=0&&s++;let o=q.calculateBlackPoints(n,i,s,t,r),a=new Y(t,r);q.calculateThresholdForBlock(n,i,s,t,r,o,a),this.matrix=a}else this.matrix=super.getBlackMatrix();return this.matrix}createBinarizer(e){return new q(e)}static calculateThresholdForBlock(e,t,r,n,i,s,o){let a=i-q.BLOCK_SIZE,l=n-q.BLOCK_SIZE;for(let i=0;i<r;i++){let h=i<<q.BLOCK_SIZE_POWER;h>a&&(h=a);let u=q.cap(i,2,r-3);for(let r=0;r<t;r++){let i=r<<q.BLOCK_SIZE_POWER;i>l&&(i=l);let a=q.cap(r,2,t-3),d=0;for(let e=-2;e<=2;e++){let t=s[u+e];d+=t[a-2]+t[a-1]+t[a]+t[a+1]+t[a+2]}let c=d/25;q.thresholdBlock(e,i,h,c,n,o)}}}static cap(e,t,r){return e<t?t:e>r?r:e}static thresholdBlock(e,t,r,n,i,s){for(let o=0,a=r*i+t;o<q.BLOCK_SIZE;o++,a+=i)for(let i=0;i<q.BLOCK_SIZE;i++)(255&e[a+i])<=n&&s.set(t+i,r+o)}static calculateBlackPoints(e,t,r,n,i){let s=i-q.BLOCK_SIZE,o=n-q.BLOCK_SIZE,a=Array(r);for(let i=0;i<r;i++){a[i]=new Int32Array(t);let r=i<<q.BLOCK_SIZE_POWER;r>s&&(r=s);for(let s=0;s<t;s++){let t=s<<q.BLOCK_SIZE_POWER;t>o&&(t=o);let l=0,h=255,u=0;for(let i=0,s=r*n+t;i<q.BLOCK_SIZE;i++,s+=n){for(let t=0;t<q.BLOCK_SIZE;t++){let r=255&e[s+t];l+=r,r<h&&(h=r),r>u&&(u=r)}if(u-h>q.MIN_DYNAMIC_RANGE)for(i++,s+=n;i<q.BLOCK_SIZE;i++,s+=n)for(let t=0;t<q.BLOCK_SIZE;t++)l+=255&e[s+t]}let d=l>>2*q.BLOCK_SIZE_POWER;if(u-h<=q.MIN_DYNAMIC_RANGE&&(d=h/2,i>0&&s>0)){let e=(a[i-1][s]+2*a[i][s-1]+a[i-1][s-1])/4;h<e&&(d=e)}a[i][s]=d}}return a}}q.BLOCK_SIZE_POWER=3,q.BLOCK_SIZE=1<<q.BLOCK_SIZE_POWER,q.BLOCK_SIZE_MASK=q.BLOCK_SIZE-1,q.MINIMUM_DIMENSION=5*q.BLOCK_SIZE,q.MIN_DYNAMIC_RANGE=24;class Q{constructor(e,t){this.width=e,this.height=t}getWidth(){return this.width}getHeight(){return this.height}isCropSupported(){return!1}crop(e,t,r,n){throw new G("This luminance source does not support cropping.")}isRotateSupported(){return!1}rotateCounterClockwise(){throw new G("This luminance source does not support rotation by 90 degrees.")}rotateCounterClockwise45(){throw new G("This luminance source does not support rotation by 45 degrees.")}toString(){let e=new Uint8ClampedArray(this.width),t=new z;for(let r=0;r<this.height;r++){let n=this.getRow(r,e);for(let e=0;e<this.width;e++){let r,i=255&n[e];r=i<64?"#":i<128?"+":i<192?".":" ",t.append(r)}t.append("\n")}return t.toString()}}class j extends Q{constructor(e){super(e.getWidth(),e.getHeight()),this.delegate=e}getRow(e,t){let r=this.delegate.getRow(e,t),n=this.getWidth();for(let e=0;e<n;e++)r[e]=255-(255&r[e]);return r}getMatrix(){let e=this.delegate.getMatrix(),t=this.getWidth()*this.getHeight(),r=new Uint8ClampedArray(t);for(let n=0;n<t;n++)r[n]=255-(255&e[n]);return r}isCropSupported(){return this.delegate.isCropSupported()}crop(e,t,r,n){return new j(this.delegate.crop(e,t,r,n))}isRotateSupported(){return this.delegate.isRotateSupported()}invert(){return this.delegate}rotateCounterClockwise(){return new j(this.delegate.rotateCounterClockwise())}rotateCounterClockwise45(){return new j(this.delegate.rotateCounterClockwise45())}}class J extends Q{constructor(e){super(e.width,e.height),this.canvas=e,this.tempCanvasElement=null,this.buffer=J.makeBufferFromCanvasImageData(e)}static makeBufferFromCanvasImageData(e){let t=e.getContext("2d").getImageData(0,0,e.width,e.height);return J.toGrayscaleBuffer(t.data,e.width,e.height)}static toGrayscaleBuffer(e,t,r){let n=new Uint8ClampedArray(t*r);for(let t=0,r=0,i=e.length;t<i;t+=4,r++){let i;if(0===e[t+3])i=255;else{let r=e[t];i=306*r+601*e[t+1]+117*e[t+2]+512>>10}n[r]=i}return n}getRow(e,t){if(e<0||e>=this.getHeight())throw new O("Requested row is outside the image: "+e);let r=this.getWidth(),n=e*r;return null===t?t=this.buffer.slice(n,n+r):(t.length<r&&(t=new Uint8ClampedArray(r)),t.set(this.buffer.slice(n,n+r))),t}getMatrix(){return this.buffer}isCropSupported(){return!0}crop(e,t,r,n){return super.crop(e,t,r,n),this}isRotateSupported(){return!0}rotateCounterClockwise(){return this.rotate(-90),this}rotateCounterClockwise45(){return this.rotate(-45),this}getTempCanvasElement(){if(null===this.tempCanvasElement){let e=this.canvas.ownerDocument.createElement("canvas");e.width=this.canvas.width,e.height=this.canvas.height,this.tempCanvasElement=e}return this.tempCanvasElement}rotate(e){let t=this.getTempCanvasElement(),r=t.getContext("2d"),n=e*J.DEGREE_TO_RADIANS,i=this.canvas.width,s=this.canvas.height,o=Math.ceil(Math.abs(Math.cos(n))*i+Math.abs(Math.sin(n))*s),a=Math.ceil(Math.abs(Math.sin(n))*i+Math.abs(Math.cos(n))*s);return t.width=o,t.height=a,r.translate(o/2,a/2),r.rotate(n),r.drawImage(this.canvas,-(i/2),-(s/2)),this.buffer=J.makeBufferFromCanvasImageData(t),this}invert(){return new j(this)}}J.DEGREE_TO_RADIANS=Math.PI/180;class ${constructor(e,t,r){this.deviceId=e,this.label=t,this.kind="videoinput",this.groupId=r||void 0}toJSON(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}}}var ee=(globalThis||r.g||self||window||void 0)&&(globalThis||r.g||self||window||void 0).__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})};class et{constructor(e,t=500,r){this.reader=e,this.timeBetweenScansMillis=t,this._hints=r,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}get hasNavigator(){return"undefined"!=typeof navigator}get isMediaDevicesSuported(){return this.hasNavigator&&!!navigator.mediaDevices}get canEnumerateDevices(){return!!(this.isMediaDevicesSuported&&navigator.mediaDevices.enumerateDevices)}get timeBetweenDecodingAttempts(){return this._timeBetweenDecodingAttempts}set timeBetweenDecodingAttempts(e){this._timeBetweenDecodingAttempts=e<0?0:e}set hints(e){this._hints=e||null}get hints(){return this._hints}listVideoInputDevices(){return ee(this,void 0,void 0,function*(){if(!this.hasNavigator)throw Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw Error("Can't enumerate devices, method not supported.");let e=yield navigator.mediaDevices.enumerateDevices(),t=[];for(let r of e){let e="video"===r.kind?"videoinput":r.kind;if("videoinput"!==e)continue;let n=r.deviceId||r.id,i={deviceId:n,label:r.label||`Video device ${t.length+1}`,kind:e,groupId:r.groupId};t.push(i)}return t})}getVideoInputDevices(){return ee(this,void 0,void 0,function*(){return(yield this.listVideoInputDevices()).map(e=>new $(e.deviceId,e.label))})}findDeviceById(e){return ee(this,void 0,void 0,function*(){let t=yield this.listVideoInputDevices();return t?t.find(t=>t.deviceId===e):null})}decodeFromInputVideoDevice(e,t){return ee(this,void 0,void 0,function*(){return yield this.decodeOnceFromVideoDevice(e,t)})}decodeOnceFromVideoDevice(e,t){return ee(this,void 0,void 0,function*(){return this.reset(),yield this.decodeOnceFromConstraints({video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},t)})}decodeOnceFromConstraints(e,t){return ee(this,void 0,void 0,function*(){let r=yield navigator.mediaDevices.getUserMedia(e);return yield this.decodeOnceFromStream(r,t)})}decodeOnceFromStream(e,t){return ee(this,void 0,void 0,function*(){this.reset();let r=yield this.attachStreamToVideo(e,t);return yield this.decodeOnce(r)})}decodeFromInputVideoDeviceContinuously(e,t,r){return ee(this,void 0,void 0,function*(){return yield this.decodeFromVideoDevice(e,t,r)})}decodeFromVideoDevice(e,t,r){return ee(this,void 0,void 0,function*(){return yield this.decodeFromConstraints({video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},t,r)})}decodeFromConstraints(e,t,r){return ee(this,void 0,void 0,function*(){let n=yield navigator.mediaDevices.getUserMedia(e);return yield this.decodeFromStream(n,t,r)})}decodeFromStream(e,t,r){return ee(this,void 0,void 0,function*(){this.reset();let n=yield this.attachStreamToVideo(e,t);return yield this.decodeContinuously(n,r)})}stopAsyncDecode(){this._stopAsyncDecode=!0}stopContinuousDecode(){this._stopContinuousDecode=!0}attachStreamToVideo(e,t){return ee(this,void 0,void 0,function*(){let r=this.prepareVideoElement(t);return this.addVideoSource(r,e),this.videoElement=r,this.stream=e,yield this.playVideoOnLoadAsync(r),r})}playVideoOnLoadAsync(e){return new Promise((t,r)=>this.playVideoOnLoad(e,()=>t()))}playVideoOnLoad(e,t){this.videoEndedListener=()=>this.stopStreams(),this.videoCanPlayListener=()=>this.tryPlayVideo(e),e.addEventListener("ended",this.videoEndedListener),e.addEventListener("canplay",this.videoCanPlayListener),e.addEventListener("playing",t),this.tryPlayVideo(e)}isVideoPlaying(e){return e.currentTime>0&&!e.paused&&!e.ended&&e.readyState>2}tryPlayVideo(e){return ee(this,void 0,void 0,function*(){if(this.isVideoPlaying(e))return void console.warn("Trying to play video that is already playing.");try{yield e.play()}catch(e){console.warn("It was not possible to play the video.")}})}getMediaElement(e,t){let r=document.getElementById(e);if(!r)throw new y(`element with id '${e}' not found`);if(r.nodeName.toLowerCase()!==t.toLowerCase())throw new y(`element with id '${e}' must be an ${t} element`);return r}decodeFromImage(e,t){if(!e&&!t)throw new y("either imageElement with a src set or an url must be provided");return t&&!e?this.decodeFromImageUrl(t):this.decodeFromImageElement(e)}decodeFromVideo(e,t){if(!e&&!t)throw new y("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrl(t):this.decodeFromVideoElement(e)}decodeFromVideoContinuously(e,t,r){if(void 0===e&&void 0===t)throw new y("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrlContinuously(t,r):this.decodeFromVideoElementContinuously(e,r)}decodeFromImageElement(e){let t;if(!e)throw new y("An image element must be provided.");this.reset();let r=this.prepareImageElement(e);return this.imageElement=r,this.isImageLoaded(r)?this.decodeOnce(r,!1,!0):this._decodeOnLoadImage(r)}decodeFromVideoElement(e){let t=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideo(t)}decodeFromVideoElementContinuously(e,t){let r=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideoContinuously(r,t)}_decodeFromVideoElementSetup(e){if(!e)throw new y("A video element must be provided.");this.reset();let t=this.prepareVideoElement(e);return this.videoElement=t,t}decodeFromImageUrl(e){if(!e)throw new y("An URL must be provided.");this.reset();let t=this.prepareImageElement();this.imageElement=t;let r=this._decodeOnLoadImage(t);return t.src=e,r}decodeFromVideoUrl(e){if(!e)throw new y("An URL must be provided.");this.reset();let t=this.prepareVideoElement(),r=this.decodeFromVideoElement(t);return t.src=e,r}decodeFromVideoUrlContinuously(e,t){if(!e)throw new y("An URL must be provided.");this.reset();let r=this.prepareVideoElement(),n=this.decodeFromVideoElementContinuously(r,t);return r.src=e,n}_decodeOnLoadImage(e){return new Promise((t,r)=>{this.imageLoadedListener=()=>this.decodeOnce(e,!1,!0).then(t,r),e.addEventListener("load",this.imageLoadedListener)})}_decodeOnLoadVideo(e){return ee(this,void 0,void 0,function*(){return yield this.playVideoOnLoadAsync(e),yield this.decodeOnce(e)})}_decodeOnLoadVideoContinuously(e,t){return ee(this,void 0,void 0,function*(){yield this.playVideoOnLoadAsync(e),this.decodeContinuously(e,t)})}isImageLoaded(e){return!!e.complete&&0!==e.naturalWidth}prepareImageElement(e){let t;return void 0===e&&((t=document.createElement("img")).width=200,t.height=200),"string"==typeof e&&(t=this.getMediaElement(e,"img")),e instanceof HTMLImageElement&&(t=e),t}prepareVideoElement(e){let t;return e||"undefined"==typeof document||((t=document.createElement("video")).width=200,t.height=200),"string"==typeof e&&(t=this.getMediaElement(e,"video")),e instanceof HTMLVideoElement&&(t=e),t.setAttribute("autoplay","true"),t.setAttribute("muted","true"),t.setAttribute("playsinline","true"),t}decodeOnce(e,t=!0,r=!0){this._stopAsyncDecode=!1;let n=(i,s)=>{if(this._stopAsyncDecode){s(new Z("Video stream has ended before any code could be detected.")),this._stopAsyncDecode=void 0;return}try{let t=this.decode(e);i(t)}catch(a){let e=t&&a instanceof Z,o=a instanceof B||a instanceof U;if(e||o&&r)return setTimeout(n,this._timeBetweenDecodingAttempts,i,s);s(a)}};return new Promise((e,t)=>n(e,t))}decodeContinuously(e,t){this._stopContinuousDecode=!1;let r=()=>{if(this._stopContinuousDecode){this._stopContinuousDecode=void 0;return}try{let n=this.decode(e);t(n,null),setTimeout(r,this.timeBetweenScansMillis)}catch(i){t(null,i);let e=i instanceof B||i instanceof U,n=i instanceof Z;(e||n)&&setTimeout(r,this._timeBetweenDecodingAttempts)}};r()}decode(e){let t=this.createBinaryBitmap(e);return this.decodeBitmap(t)}_isHTMLVideoElement(e){return 0!==e.videoWidth}drawFrameOnCanvas(e,t,r){t||(t={sx:0,sy:0,sWidth:e.videoWidth,sHeight:e.videoHeight,dx:0,dy:0,dWidth:e.videoWidth,dHeight:e.videoHeight}),r||(r=this.captureCanvasContext),r.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)}drawImageOnCanvas(e,t,r=this.captureCanvasContext){t||(t={sx:0,sy:0,sWidth:e.naturalWidth,sHeight:e.naturalHeight,dx:0,dy:0,dWidth:e.naturalWidth,dHeight:e.naturalHeight}),r||(r=this.captureCanvasContext),r.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)}createBinaryBitmap(e){return this.getCaptureCanvasContext(e),this._isHTMLVideoElement(e)?this.drawFrameOnCanvas(e):this.drawImageOnCanvas(e),new M(new q(new J(this.getCaptureCanvas(e))))}getCaptureCanvasContext(e){if(!this.captureCanvasContext){let t=this.getCaptureCanvas(e).getContext("2d");this.captureCanvasContext=t}return this.captureCanvasContext}getCaptureCanvas(e){if(!this.captureCanvas){let t=this.createCaptureCanvas(e);this.captureCanvas=t}return this.captureCanvas}decodeBitmap(e){return this.reader.decode(e,this._hints)}createCaptureCanvas(e){let t,r;if("undefined"==typeof document)return this._destroyCaptureCanvas(),null;let n=document.createElement("canvas");return void 0!==e&&(e instanceof HTMLVideoElement?(t=e.videoWidth,r=e.videoHeight):e instanceof HTMLImageElement&&(t=e.naturalWidth||e.width,r=e.naturalHeight||e.height)),n.style.width=t+"px",n.style.height=r+"px",n.width=t,n.height=r,n}stopStreams(){this.stream&&(this.stream.getVideoTracks().forEach(e=>e.stop()),this.stream=void 0),!1===this._stopAsyncDecode&&this.stopAsyncDecode(),!1===this._stopContinuousDecode&&this.stopContinuousDecode()}reset(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()}_destroyVideoElement(){this.videoElement&&(void 0!==this.videoEndedListener&&this.videoElement.removeEventListener("ended",this.videoEndedListener),void 0!==this.videoPlayingEventListener&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),void 0!==this.videoCanPlayListener&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)}_destroyImageElement(){this.imageElement&&(void 0!==this.imageLoadedListener&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)}_destroyCaptureCanvas(){this.captureCanvasContext=void 0,this.captureCanvas=void 0}addVideoSource(e,t){try{e.srcObject=t}catch(r){e.src=URL.createObjectURL(t)}}cleanVideoSource(e){try{e.srcObject=null}catch(t){e.src=""}this.videoElement.removeAttribute("src")}}class er{constructor(e,t,r=null==t?0:8*t.length,n,i,s=P.currentTimeMillis()){this.text=e,this.rawBytes=t,this.numBits=r,this.resultPoints=n,this.format=i,this.timestamp=s,this.text=e,this.rawBytes=t,null==r?this.numBits=null==t?0:8*t.length:this.numBits=r,this.resultPoints=n,this.format=i,this.resultMetadata=null,null==s?this.timestamp=P.currentTimeMillis():this.timestamp=s}getText(){return this.text}getRawBytes(){return this.rawBytes}getNumBits(){return this.numBits}getResultPoints(){return this.resultPoints}getBarcodeFormat(){return this.format}getResultMetadata(){return this.resultMetadata}putMetadata(e,t){null===this.resultMetadata&&(this.resultMetadata=new Map),this.resultMetadata.set(e,t)}putAllMetadata(e){null!==e&&(null===this.resultMetadata?this.resultMetadata=e:this.resultMetadata=new Map(e))}addResultPoints(e){let t=this.resultPoints;if(null===t)this.resultPoints=e;else if(null!==e&&e.length>0){let r=Array(t.length+e.length);P.arraycopy(t,0,r,0,t.length),P.arraycopy(e,0,r,t.length,e.length),this.resultPoints=r}}getTimestamp(){return this.timestamp}toString(){return this.text}}(s=A||(A={}))[s.AZTEC=0]="AZTEC",s[s.CODABAR=1]="CODABAR",s[s.CODE_39=2]="CODE_39",s[s.CODE_93=3]="CODE_93",s[s.CODE_128=4]="CODE_128",s[s.DATA_MATRIX=5]="DATA_MATRIX",s[s.EAN_8=6]="EAN_8",s[s.EAN_13=7]="EAN_13",s[s.ITF=8]="ITF",s[s.MAXICODE=9]="MAXICODE",s[s.PDF_417=10]="PDF_417",s[s.QR_CODE=11]="QR_CODE",s[s.RSS_14=12]="RSS_14",s[s.RSS_EXPANDED=13]="RSS_EXPANDED",s[s.UPC_A=14]="UPC_A",s[s.UPC_E=15]="UPC_E",s[s.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION";var en=A;(o=C||(C={}))[o.OTHER=0]="OTHER",o[o.ORIENTATION=1]="ORIENTATION",o[o.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",o[o.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",o[o.ISSUE_NUMBER=4]="ISSUE_NUMBER",o[o.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",o[o.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",o[o.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",o[o.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",o[o.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",o[o.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY";var ei=C;class es{constructor(e,t,r,n,i=-1,s=-1){this.rawBytes=e,this.text=t,this.byteSegments=r,this.ecLevel=n,this.structuredAppendSequenceNumber=i,this.structuredAppendParity=s,this.numBits=null==e?0:8*e.length}getRawBytes(){return this.rawBytes}getNumBits(){return this.numBits}setNumBits(e){this.numBits=e}getText(){return this.text}getByteSegments(){return this.byteSegments}getECLevel(){return this.ecLevel}getErrorsCorrected(){return this.errorsCorrected}setErrorsCorrected(e){this.errorsCorrected=e}getErasures(){return this.erasures}setErasures(e){this.erasures=e}getOther(){return this.other}setOther(e){this.other=e}hasStructuredAppend(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0}getStructuredAppendParity(){return this.structuredAppendParity}getStructuredAppendSequenceNumber(){return this.structuredAppendSequenceNumber}}class eo{exp(e){return this.expTable[e]}log(e){if(0===e)throw new O;return this.logTable[e]}static addOrSubtract(e,t){return e^t}}class ea{constructor(e,t){if(0===t.length)throw new O;this.field=e;let r=t.length;if(r>1&&0===t[0]){let e=1;for(;e<r&&0===t[e];)e++;e===r?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(r-e),P.arraycopy(t,e,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}getCoefficients(){return this.coefficients}getDegree(){return this.coefficients.length-1}isZero(){return 0===this.coefficients[0]}getCoefficient(e){return this.coefficients[this.coefficients.length-1-e]}evaluateAt(e){let t;if(0===e)return this.getCoefficient(0);let r=this.coefficients;if(1===e){t=0;for(let e=0,n=r.length;e!==n;e++){let n=r[e];t=eo.addOrSubtract(t,n)}return t}t=r[0];let n=r.length,i=this.field;for(let s=1;s<n;s++)t=eo.addOrSubtract(i.multiply(e,t),r[s]);return t}addOrSubtract(e){if(!this.field.equals(e.field))throw new O("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;let t=this.coefficients,r=e.coefficients;if(t.length>r.length){let e=t;t=r,r=e}let n=new Int32Array(r.length),i=r.length-t.length;P.arraycopy(r,0,n,0,i);for(let e=i;e<r.length;e++)n[e]=eo.addOrSubtract(t[e-i],r[e]);return new ea(this.field,n)}multiply(e){if(!this.field.equals(e.field))throw new O("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();let t=this.coefficients,r=t.length,n=e.coefficients,i=n.length,s=new Int32Array(r+i-1),o=this.field;for(let e=0;e<r;e++){let r=t[e];for(let t=0;t<i;t++)s[e+t]=eo.addOrSubtract(s[e+t],o.multiply(r,n[t]))}return new ea(o,s)}multiplyScalar(e){if(0===e)return this.field.getZero();if(1===e)return this;let t=this.coefficients.length,r=this.field,n=new Int32Array(t),i=this.coefficients;for(let s=0;s<t;s++)n[s]=r.multiply(i[s],e);return new ea(r,n)}multiplyByMonomial(e,t){if(e<0)throw new O;if(0===t)return this.field.getZero();let r=this.coefficients,n=r.length,i=new Int32Array(n+e),s=this.field;for(let e=0;e<n;e++)i[e]=s.multiply(r[e],t);return new ea(s,i)}divide(e){if(!this.field.equals(e.field))throw new O("GenericGFPolys do not have same GenericGF field");if(e.isZero())throw new O("Divide by 0");let t=this.field,r=t.getZero(),n=this,i=e.getCoefficient(e.getDegree()),s=t.inverse(i);for(;n.getDegree()>=e.getDegree()&&!n.isZero();){let i=n.getDegree()-e.getDegree(),o=t.multiply(n.getCoefficient(n.getDegree()),s),a=e.multiplyByMonomial(i,o),l=t.buildMonomial(i,o);r=r.addOrSubtract(l),n=n.addOrSubtract(a)}return[r,n]}toString(){let e="";for(let t=this.getDegree();t>=0;t--){let r=this.getCoefficient(t);if(0!==r){if(r<0?(e+=" - ",r=-r):e.length>0&&(e+=" + "),0===t||1!==r){let t=this.field.log(r);0===t?e+="1":1===t?e+="a":(e+="a^",e+=t)}0!==t&&(1===t?e+="x":(e+="x^",e+=t))}}return e}}class el extends D{}el.kind="ArithmeticException";class eh extends eo{constructor(e,t,r){super(),this.primitive=e,this.size=t,this.generatorBase=r;let n=new Int32Array(t),i=1;for(let r=0;r<t;r++)n[r]=i,(i*=2)>=t&&(i^=e,i&=t-1);this.expTable=n;let s=new Int32Array(t);for(let e=0;e<t-1;e++)s[n[e]]=e;this.logTable=s,this.zero=new ea(this,Int32Array.from([0])),this.one=new ea(this,Int32Array.from([1]))}getZero(){return this.zero}getOne(){return this.one}buildMonomial(e,t){if(e<0)throw new O;if(0===t)return this.zero;let r=new Int32Array(e+1);return r[0]=t,new ea(this,r)}inverse(e){if(0===e)throw new el;return this.expTable[this.size-this.logTable[e]-1]}multiply(e,t){return 0===e||0===t?0:this.expTable[(this.logTable[e]+this.logTable[t])%(this.size-1)]}getSize(){return this.size}getGeneratorBase(){return this.generatorBase}toString(){return"GF(0x"+k.toHexString(this.primitive)+","+this.size+")"}equals(e){return e===this}}eh.AZTEC_DATA_12=new eh(4201,4096,1),eh.AZTEC_DATA_10=new eh(1033,1024,1),eh.AZTEC_DATA_6=new eh(67,64,1),eh.AZTEC_PARAM=new eh(19,16,1),eh.QR_CODE_FIELD_256=new eh(285,256,0),eh.DATA_MATRIX_FIELD_256=new eh(301,256,1),eh.AZTEC_DATA_8=eh.DATA_MATRIX_FIELD_256,eh.MAXICODE_FIELD_64=eh.AZTEC_DATA_6;class eu extends D{}eu.kind="ReedSolomonException";class ed extends D{}ed.kind="IllegalStateException";class ec{constructor(e){this.field=e}decode(e,t){let r=this.field,n=new ea(r,e),i=new Int32Array(t),s=!0;for(let e=0;e<t;e++){let t=n.evaluateAt(r.exp(e+r.getGeneratorBase()));i[i.length-1-e]=t,0!==t&&(s=!1)}if(s)return;let o=new ea(r,i),a=this.runEuclideanAlgorithm(r.buildMonomial(t,1),o,t),l=a[0],h=a[1],u=this.findErrorLocations(l),d=this.findErrorMagnitudes(h,u);for(let t=0;t<u.length;t++){let n=e.length-1-r.log(u[t]);if(n<0)throw new eu("Bad error location");e[n]=eh.addOrSubtract(e[n],d[t])}}runEuclideanAlgorithm(e,t,r){if(e.getDegree()<t.getDegree()){let r=e;e=t,t=r}let n=this.field,i=e,s=t,o=n.getZero(),a=n.getOne();for(;s.getDegree()>=(r/2|0);){let e=i,t=o;if(i=s,o=a,i.isZero())throw new eu("r_{i-1} was zero");s=e;let r=n.getZero(),l=i.getCoefficient(i.getDegree()),h=n.inverse(l);for(;s.getDegree()>=i.getDegree()&&!s.isZero();){let e=s.getDegree()-i.getDegree(),t=n.multiply(s.getCoefficient(s.getDegree()),h);r=r.addOrSubtract(n.buildMonomial(e,t)),s=s.addOrSubtract(i.multiplyByMonomial(e,t))}if(a=r.multiply(o).addOrSubtract(t),s.getDegree()>=i.getDegree())throw new ed("Division algorithm failed to reduce polynomial?")}let l=a.getCoefficient(0);if(0===l)throw new eu("sigmaTilde(0) was zero");let h=n.inverse(l);return[a.multiplyScalar(h),s.multiplyScalar(h)]}findErrorLocations(e){let t=e.getDegree();if(1===t)return Int32Array.from([e.getCoefficient(1)]);let r=new Int32Array(t),n=0,i=this.field;for(let s=1;s<i.getSize()&&n<t;s++)0===e.evaluateAt(s)&&(r[n]=i.inverse(s),n++);if(n!==t)throw new eu("Error locator degree does not match number of roots");return r}findErrorMagnitudes(e,t){let r=t.length,n=new Int32Array(r),i=this.field;for(let s=0;s<r;s++){let o=i.inverse(t[s]),a=1;for(let e=0;e<r;e++)if(s!==e){let r=i.multiply(t[e],o),n=(1&r)==0?1|r:-2&r;a=i.multiply(a,n)}n[s]=i.multiply(e.evaluateAt(o),i.inverse(a)),0!==i.getGeneratorBase()&&(n[s]=i.multiply(n[s],o))}return n}}(a=E||(E={}))[a.UPPER=0]="UPPER",a[a.LOWER=1]="LOWER",a[a.MIXED=2]="MIXED",a[a.DIGIT=3]="DIGIT",a[a.PUNCT=4]="PUNCT",a[a.BINARY=5]="BINARY";class eg{decode(e){this.ddata=e;let t=e.getBits(),r=this.extractBits(t),n=this.correctBits(r),i=new es(eg.convertBoolArrayToByteArray(n),eg.getEncodedData(n),null,null);return i.setNumBits(n.length),i}static highLevelDecode(e){return this.getEncodedData(e)}static getEncodedData(e){let t=e.length,r=E.UPPER,n=E.UPPER,i="",s=0;for(;s<t;)if(n===E.BINARY){if(t-s<5)break;let o=eg.readCode(e,s,5);if(s+=5,0===o){if(t-s<11)break;o=eg.readCode(e,s,11)+31,s+=11}for(let r=0;r<o;r++){if(t-s<8){s=t;break}let r=eg.readCode(e,s,8);i+=W.castAsNonUtf8Char(r),s+=8}n=r}else{let o=n===E.DIGIT?4:5;if(t-s<o)break;let a=eg.readCode(e,s,o);s+=o;let l=eg.getCharacter(n,a);l.startsWith("CTRL_")?(r=n,n=eg.getTable(l.charAt(5)),"L"===l.charAt(6)&&(r=n)):(i+=l,n=r)}return i}static getTable(e){switch(e){case"L":return E.LOWER;case"P":return E.PUNCT;case"M":return E.MIXED;case"D":return E.DIGIT;case"B":return E.BINARY;default:return E.UPPER}}static getCharacter(e,t){switch(e){case E.UPPER:return eg.UPPER_TABLE[t];case E.LOWER:return eg.LOWER_TABLE[t];case E.MIXED:return eg.MIXED_TABLE[t];case E.PUNCT:return eg.PUNCT_TABLE[t];case E.DIGIT:return eg.DIGIT_TABLE[t];default:throw new ed("Bad table")}}correctBits(e){let t,r;2>=this.ddata.getNbLayers()?(r=6,t=eh.AZTEC_DATA_6):8>=this.ddata.getNbLayers()?(r=8,t=eh.AZTEC_DATA_8):22>=this.ddata.getNbLayers()?(r=10,t=eh.AZTEC_DATA_10):(r=12,t=eh.AZTEC_DATA_12);let n=this.ddata.getNbDatablocks(),i=e.length/r;if(i<n)throw new U;let s=e.length%r,o=new Int32Array(i);for(let t=0;t<i;t++,s+=r)o[t]=eg.readCode(e,s,r);try{new ec(t).decode(o,i-n)}catch(e){throw new U(e)}let a=(1<<r)-1,l=0;for(let e=0;e<n;e++){let t=o[e];if(0===t||t===a)throw new U;(1===t||t===a-1)&&l++}let h=Array(n*r-l),u=0;for(let e=0;e<n;e++){let t=o[e];if(1===t||t===a-1)h.fill(t>1,u,u+r-1),u+=r-1;else for(let e=r-1;e>=0;--e)h[u++]=(t&1<<e)!=0}return h}extractBits(e){let t=this.ddata.isCompact(),r=this.ddata.getNbLayers(),n=(t?11:14)+4*r,i=new Int32Array(n),s=Array(this.totalBitsInLayer(r,t));if(t)for(let e=0;e<i.length;e++)i[e]=e;else{let e=n+1+2*k.truncDivision(k.truncDivision(n,2)-1,15),t=n/2,r=k.truncDivision(e,2);for(let e=0;e<t;e++){let n=e+k.truncDivision(e,15);i[t-e-1]=r-n-1,i[t+e]=r+n+1}}for(let o=0,a=0;o<r;o++){let l=(r-o)*4+(t?9:12),h=2*o,u=n-1-h;for(let t=0;t<l;t++){let r=2*t;for(let n=0;n<2;n++)s[a+r+n]=e.get(i[h+n],i[h+t]),s[a+2*l+r+n]=e.get(i[h+t],i[u-n]),s[a+4*l+r+n]=e.get(i[u-n],i[u-t]),s[a+6*l+r+n]=e.get(i[u-t],i[h+n])}a+=8*l}return s}static readCode(e,t,r){let n=0;for(let i=t;i<t+r;i++)n<<=1,e[i]&&(n|=1);return n}static readByte(e,t){let r=e.length-t;return r>=8?eg.readCode(e,t,8):eg.readCode(e,t,r)<<8-r}static convertBoolArrayToByteArray(e){let t=new Uint8Array((e.length+7)/8);for(let r=0;r<t.length;r++)t[r]=eg.readByte(e,8*r);return t}totalBitsInLayer(e,t){return((t?88:112)+16*e)*e}}eg.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],eg.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],eg.MIXED_TABLE=["CTRL_PS"," ","\\1","\\2","\\3","\\4","\\5","\\6","\\7","\b","	","\n","\\13","\f","\r","\\33","\\34","\\35","\\36","\\37","@","\\","^","_","`","|","~","\\177","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],eg.PUNCT_TABLE=["","\r","\r\n",". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],eg.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"];class ef{constructor(){}static round(e){return e<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:e>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:e+(e<0?-.5:.5)|0}static distance(e,t,r,n){let i=e-r,s=t-n;return Math.sqrt(i*i+s*s)}static sum(e){let t=0;for(let r=0,n=e.length;r!==n;r++)t+=e[r];return t}}class ew{static floatToIntBits(e){return e}}ew.MAX_VALUE=Number.MAX_SAFE_INTEGER;class eA{constructor(e,t){this.x=e,this.y=t}getX(){return this.x}getY(){return this.y}equals(e){return e instanceof eA&&this.x===e.x&&this.y===e.y}hashCode(){return 31*ew.floatToIntBits(this.x)+ew.floatToIntBits(this.y)}toString(){return"("+this.x+","+this.y+")"}static orderBestPatterns(e){let t,r,n,i=this.distance(e[0],e[1]),s=this.distance(e[1],e[2]),o=this.distance(e[0],e[2]);if(s>=i&&s>=o?(r=e[0],t=e[1],n=e[2]):o>=s&&o>=i?(r=e[1],t=e[0],n=e[2]):(r=e[2],t=e[0],n=e[1]),0>this.crossProductZ(t,r,n)){let e=t;t=n,n=e}e[0]=t,e[1]=r,e[2]=n}static distance(e,t){return ef.distance(e.x,e.y,t.x,t.y)}static crossProductZ(e,t,r){let n=t.x,i=t.y;return(r.x-n)*(e.y-i)-(r.y-i)*(e.x-n)}}class eC{constructor(e,t){this.bits=e,this.points=t}getBits(){return this.bits}getPoints(){return this.points}}class eE extends eC{constructor(e,t,r,n,i){super(e,t),this.compact=r,this.nbDatablocks=n,this.nbLayers=i}getNbLayers(){return this.nbLayers}getNbDatablocks(){return this.nbDatablocks}isCompact(){return this.compact}}class em{constructor(e,t,r,n){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),null==t&&(t=em.INIT_SIZE),null==r&&(r=e.getWidth()/2|0),null==n&&(n=e.getHeight()/2|0);let i=t/2|0;if(this.leftInit=r-i,this.rightInit=r+i,this.upInit=n-i,this.downInit=n+i,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new Z}detect(){let e=this.leftInit,t=this.rightInit,r=this.upInit,n=this.downInit,i=!1,s=!0,o=!1,a=!1,l=!1,h=!1,u=!1,d=this.width,c=this.height;for(;s;){s=!1;let g=!0;for(;(g||!a)&&t<d;)(g=this.containsBlackPoint(r,n,t,!1))?(t++,s=!0,a=!0):!a&&t++;if(t>=d){i=!0;break}let f=!0;for(;(f||!l)&&n<c;)(f=this.containsBlackPoint(e,t,n,!0))?(n++,s=!0,l=!0):!l&&n++;if(n>=c){i=!0;break}let w=!0;for(;(w||!h)&&e>=0;)(w=this.containsBlackPoint(r,n,e,!1))?(e--,s=!0,h=!0):!h&&e--;if(e<0){i=!0;break}let A=!0;for(;(A||!u)&&r>=0;)(A=this.containsBlackPoint(e,t,r,!0))?(r--,s=!0,u=!0):!u&&r--;if(r<0){i=!0;break}s&&(o=!0)}if(!i&&o){let i=t-e,s=null;for(let t=1;null===s&&t<i;t++)s=this.getBlackPointOnSegment(e,n-t,e+t,n);if(null==s)throw new Z;let o=null;for(let t=1;null===o&&t<i;t++)o=this.getBlackPointOnSegment(e,r+t,e+t,r);if(null==o)throw new Z;let a=null;for(let e=1;null===a&&e<i;e++)a=this.getBlackPointOnSegment(t,r+e,t-e,r);if(null==a)throw new Z;let l=null;for(let e=1;null===l&&e<i;e++)l=this.getBlackPointOnSegment(t,n-e,t-e,n);if(null==l)throw new Z;return this.centerEdges(l,s,a,o)}throw new Z}getBlackPointOnSegment(e,t,r,n){let i=ef.round(ef.distance(e,t,r,n)),s=(r-e)/i,o=(n-t)/i,a=this.image;for(let r=0;r<i;r++){let n=ef.round(e+r*s),i=ef.round(t+r*o);if(a.get(n,i))return new eA(n,i)}return null}centerEdges(e,t,r,n){let i=e.getX(),s=e.getY(),o=t.getX(),a=t.getY(),l=r.getX(),h=r.getY(),u=n.getX(),d=n.getY(),c=em.CORR;return i<this.width/2?[new eA(u-c,d+c),new eA(o+c,a+c),new eA(l-c,h-c),new eA(i+c,s-c)]:[new eA(u+c,d+c),new eA(o+c,a-c),new eA(l-c,h+c),new eA(i-c,s-c)]}containsBlackPoint(e,t,r,n){let i=this.image;if(n){for(let n=e;n<=t;n++)if(i.get(n,r))return!0}else for(let n=e;n<=t;n++)if(i.get(r,n))return!0;return!1}}em.INIT_SIZE=10,em.CORR=1;class e_{static checkAndNudgePoints(e,t){let r=e.getWidth(),n=e.getHeight(),i=!0;for(let e=0;e<t.length&&i;e+=2){let s=Math.floor(t[e]),o=Math.floor(t[e+1]);if(s<-1||s>r||o<-1||o>n)throw new Z;i=!1,-1===s?(t[e]=0,i=!0):s===r&&(t[e]=r-1,i=!0),-1===o?(t[e+1]=0,i=!0):o===n&&(t[e+1]=n-1,i=!0)}i=!0;for(let e=t.length-2;e>=0&&i;e-=2){let s=Math.floor(t[e]),o=Math.floor(t[e+1]);if(s<-1||s>r||o<-1||o>n)throw new Z;i=!1,-1===s?(t[e]=0,i=!0):s===r&&(t[e]=r-1,i=!0),-1===o?(t[e+1]=0,i=!0):o===n&&(t[e+1]=n-1,i=!0)}}}class eI{constructor(e,t,r,n,i,s,o,a,l){this.a11=e,this.a21=t,this.a31=r,this.a12=n,this.a22=i,this.a32=s,this.a13=o,this.a23=a,this.a33=l}static quadrilateralToQuadrilateral(e,t,r,n,i,s,o,a,l,h,u,d,c,g,f,w){let A=eI.quadrilateralToSquare(e,t,r,n,i,s,o,a);return eI.squareToQuadrilateral(l,h,u,d,c,g,f,w).times(A)}transformPoints(e){let t=e.length,r=this.a11,n=this.a12,i=this.a13,s=this.a21,o=this.a22,a=this.a23,l=this.a31,h=this.a32,u=this.a33;for(let d=0;d<t;d+=2){let t=e[d],c=e[d+1],g=i*t+a*c+u;e[d]=(r*t+s*c+l)/g,e[d+1]=(n*t+o*c+h)/g}}transformPointsWithValues(e,t){let r=this.a11,n=this.a12,i=this.a13,s=this.a21,o=this.a22,a=this.a23,l=this.a31,h=this.a32,u=this.a33,d=e.length;for(let c=0;c<d;c++){let d=e[c],g=t[c],f=i*d+a*g+u;e[c]=(r*d+s*g+l)/f,t[c]=(n*d+o*g+h)/f}}static squareToQuadrilateral(e,t,r,n,i,s,o,a){let l=e-r+i-o,h=t-n+s-a;if(0===l&&0===h)return new eI(r-e,i-r,e,n-t,s-n,t,0,0,1);{let u=r-i,d=o-i,c=n-s,g=a-s,f=u*g-d*c,w=(l*g-d*h)/f,A=(u*h-l*c)/f;return new eI(r-e+w*r,o-e+A*o,e,n-t+w*n,a-t+A*a,t,w,A,1)}}static quadrilateralToSquare(e,t,r,n,i,s,o,a){return eI.squareToQuadrilateral(e,t,r,n,i,s,o,a).buildAdjoint()}buildAdjoint(){return new eI(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)}times(e){return new eI(this.a11*e.a11+this.a21*e.a12+this.a31*e.a13,this.a11*e.a21+this.a21*e.a22+this.a31*e.a23,this.a11*e.a31+this.a21*e.a32+this.a31*e.a33,this.a12*e.a11+this.a22*e.a12+this.a32*e.a13,this.a12*e.a21+this.a22*e.a22+this.a32*e.a23,this.a12*e.a31+this.a22*e.a32+this.a32*e.a33,this.a13*e.a11+this.a23*e.a12+this.a33*e.a13,this.a13*e.a21+this.a23*e.a22+this.a33*e.a23,this.a13*e.a31+this.a23*e.a32+this.a33*e.a33)}}class eS extends e_{sampleGrid(e,t,r,n,i,s,o,a,l,h,u,d,c,g,f,w,A,C,E){let m=eI.quadrilateralToQuadrilateral(n,i,s,o,a,l,h,u,d,c,g,f,w,A,C,E);return this.sampleGridWithTransform(e,t,r,m)}sampleGridWithTransform(e,t,r,n){if(t<=0||r<=0)throw new Z;let i=new Y(t,r),s=new Float32Array(2*t);for(let t=0;t<r;t++){let r=s.length,o=t+.5;for(let e=0;e<r;e+=2)s[e]=e/2+.5,s[e+1]=o;n.transformPoints(s),e_.checkAndNudgePoints(e,s);try{for(let n=0;n<r;n+=2)e.get(Math.floor(s[n]),Math.floor(s[n+1]))&&i.set(n/2,t)}catch(e){throw new Z}}return i}}class ep{static setGridSampler(e){ep.gridSampler=e}static getInstance(){return ep.gridSampler}}ep.gridSampler=new eS;class eT{constructor(e,t){this.x=e,this.y=t}toResultPoint(){return new eA(this.getX(),this.getY())}getX(){return this.x}getY(){return this.y}}class eR{constructor(e){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=e}detect(){return this.detectMirror(!1)}detectMirror(e){let t=this.getMatrixCenter(),r=this.getBullsEyeCorners(t);if(e){let e=r[0];r[0]=r[2],r[2]=e}return this.extractParameters(r),new eE(this.sampleGrid(this.image,r[this.shift%4],r[(this.shift+1)%4],r[(this.shift+2)%4],r[(this.shift+3)%4]),this.getMatrixCornerPoints(r),this.compact,this.nbDataBlocks,this.nbLayers)}extractParameters(e){if(!this.isValidPoint(e[0])||!this.isValidPoint(e[1])||!this.isValidPoint(e[2])||!this.isValidPoint(e[3]))throw new Z;let t=2*this.nbCenterLayers,r=new Int32Array([this.sampleLine(e[0],e[1],t),this.sampleLine(e[1],e[2],t),this.sampleLine(e[2],e[3],t),this.sampleLine(e[3],e[0],t)]);this.shift=this.getRotation(r,t);let n=0;for(let e=0;e<4;e++){let t=r[(this.shift+e)%4];this.compact?(n<<=7,n+=t>>1&127):(n<<=10,n+=(t>>2&992)+(t>>1&31))}let i=this.getCorrectedParameterData(n,this.compact);this.compact?(this.nbLayers=(i>>6)+1,this.nbDataBlocks=(63&i)+1):(this.nbLayers=(i>>11)+1,this.nbDataBlocks=(2047&i)+1)}getRotation(e,t){let r=0;e.forEach((e,n,i)=>{r=(r<<3)+((e>>t-2<<1)+(1&e))}),r=((1&r)<<11)+(r>>1);for(let e=0;e<4;e++)if(2>=k.bitCount(r^this.EXPECTED_CORNER_BITS[e]))return e;throw new Z}getCorrectedParameterData(e,t){let r,n;t?(r=7,n=2):(r=10,n=4);let i=r-n,s=new Int32Array(r);for(let t=r-1;t>=0;--t)s[t]=15&e,e>>=4;try{new ec(eh.AZTEC_PARAM).decode(s,i)}catch(e){throw new Z}let o=0;for(let e=0;e<n;e++)o=(o<<4)+s[e];return o}getBullsEyeCorners(e){let t=e,r=e,n=e,i=e,s=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){let e=this.getFirstDifferent(t,s,1,-1),o=this.getFirstDifferent(r,s,1,1),a=this.getFirstDifferent(n,s,-1,1),l=this.getFirstDifferent(i,s,-1,-1);if(this.nbCenterLayers>2){let r=this.distancePoint(l,e)*this.nbCenterLayers/(this.distancePoint(i,t)*(this.nbCenterLayers+2));if(r<.75||r>1.25||!this.isWhiteOrBlackRectangle(e,o,a,l))break}t=e,r=o,n=a,i=l,s=!s}if(5!==this.nbCenterLayers&&7!==this.nbCenterLayers)throw new Z;this.compact=5===this.nbCenterLayers;let o=new eA(t.getX()+.5,t.getY()-.5),a=new eA(r.getX()+.5,r.getY()+.5),l=new eA(n.getX()-.5,n.getY()+.5),h=new eA(i.getX()-.5,i.getY()-.5);return this.expandSquare([o,a,l,h],2*this.nbCenterLayers-3,2*this.nbCenterLayers)}getMatrixCenter(){let e,t,r,n;try{let i=new em(this.image).detect();e=i[0],t=i[1],r=i[2],n=i[3]}catch(o){let i=this.image.getWidth()/2,s=this.image.getHeight()/2;e=this.getFirstDifferent(new eT(i+7,s-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new eT(i+7,s+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new eT(i-7,s+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new eT(i-7,s-7),!1,-1,-1).toResultPoint()}let i=ef.round((e.getX()+n.getX()+t.getX()+r.getX())/4),s=ef.round((e.getY()+n.getY()+t.getY()+r.getY())/4);try{let o=new em(this.image,15,i,s).detect();e=o[0],t=o[1],r=o[2],n=o[3]}catch(o){e=this.getFirstDifferent(new eT(i+7,s-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new eT(i+7,s+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new eT(i-7,s+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new eT(i-7,s-7),!1,-1,-1).toResultPoint()}return new eT(i=ef.round((e.getX()+n.getX()+t.getX()+r.getX())/4),s=ef.round((e.getY()+n.getY()+t.getY()+r.getY())/4))}getMatrixCornerPoints(e){return this.expandSquare(e,2*this.nbCenterLayers,this.getDimension())}sampleGrid(e,t,r,n,i){let s=ep.getInstance(),o=this.getDimension(),a=o/2-this.nbCenterLayers,l=o/2+this.nbCenterLayers;return s.sampleGrid(e,o,o,a,a,l,a,l,l,a,l,t.getX(),t.getY(),r.getX(),r.getY(),n.getX(),n.getY(),i.getX(),i.getY())}sampleLine(e,t,r){let n=0,i=this.distanceResultPoint(e,t),s=i/r,o=e.getX(),a=e.getY(),l=s*(t.getX()-e.getX())/i,h=s*(t.getY()-e.getY())/i;for(let e=0;e<r;e++)this.image.get(ef.round(o+e*l),ef.round(a+e*h))&&(n|=1<<r-e-1);return n}isWhiteOrBlackRectangle(e,t,r,n){e=new eT(e.getX()-3,e.getY()+3),t=new eT(t.getX()-3,t.getY()-3),r=new eT(r.getX()+3,r.getY()-3),n=new eT(n.getX()+3,n.getY()+3);let i=this.getColor(n,e);if(0===i)return!1;let s=this.getColor(e,t);return s===i&&(s=this.getColor(t,r))===i&&(s=this.getColor(r,n))===i}getColor(e,t){let r=this.distancePoint(e,t),n=(t.getX()-e.getX())/r,i=(t.getY()-e.getY())/r,s=0,o=e.getX(),a=e.getY(),l=this.image.get(e.getX(),e.getY()),h=Math.ceil(r);for(let e=0;e<h;e++)o+=n,a+=i,this.image.get(ef.round(o),ef.round(a))!==l&&s++;let u=s/r;return u>.1&&u<.9?0:u<=.1===l?1:-1}getFirstDifferent(e,t,r,n){let i=e.getX()+r,s=e.getY()+n;for(;this.isValid(i,s)&&this.image.get(i,s)===t;)i+=r,s+=n;for(i-=r,s-=n;this.isValid(i,s)&&this.image.get(i,s)===t;)i+=r;for(i-=r;this.isValid(i,s)&&this.image.get(i,s)===t;)s+=n;return new eT(i,s-=n)}expandSquare(e,t,r){let n=r/(2*t),i=e[0].getX()-e[2].getX(),s=e[0].getY()-e[2].getY(),o=(e[0].getX()+e[2].getX())/2,a=(e[0].getY()+e[2].getY())/2,l=new eA(o+n*i,a+n*s),h=new eA(o-n*i,a-n*s);return i=e[1].getX()-e[3].getX(),s=e[1].getY()-e[3].getY(),[l,new eA((o=(e[1].getX()+e[3].getX())/2)+n*i,(a=(e[1].getY()+e[3].getY())/2)+n*s),h,new eA(o-n*i,a-n*s)]}isValid(e,t){return e>=0&&e<this.image.getWidth()&&t>0&&t<this.image.getHeight()}isValidPoint(e){let t=ef.round(e.getX()),r=ef.round(e.getY());return this.isValid(t,r)}distancePoint(e,t){return ef.distance(e.getX(),e.getY(),t.getX(),t.getY())}distanceResultPoint(e,t){return ef.distance(e.getX(),e.getY(),t.getX(),t.getY())}getDimension(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(k.truncDivision(this.nbLayers-4,8)+1)+15}}class eN{decode(e,t=null){let r=null,n=new eR(e.getBlackMatrix()),i=null,s=null;try{let e=n.detectMirror(!1);i=e.getPoints(),this.reportFoundResultPoints(t,i),s=new eg().decode(e)}catch(e){r=e}if(null==s)try{let e=n.detectMirror(!0);i=e.getPoints(),this.reportFoundResultPoints(t,i),s=new eg().decode(e)}catch(e){if(null!=r)throw r;throw e}let o=new er(s.getText(),s.getRawBytes(),s.getNumBits(),i,en.AZTEC,P.currentTimeMillis()),a=s.getByteSegments();null!=a&&o.putMetadata(ei.BYTE_SEGMENTS,a);let l=s.getECLevel();return null!=l&&o.putMetadata(ei.ERROR_CORRECTION_LEVEL,l),o}reportFoundResultPoints(e,t){if(null!=e){let r=e.get(V.NEED_RESULT_POINT_CALLBACK);null!=r&&t.forEach((e,t,n)=>{r.foundPossibleResultPoint(e)})}}reset(){}}class eD extends et{constructor(e=500){super(new eN,e)}}class ey{decode(e,t){try{return this.doDecode(e,t)}catch(r){if(t&&!0===t.get(V.TRY_HARDER)&&e.isRotateSupported()){let r=e.rotateCounterClockwise(),n=this.doDecode(r,t),i=n.getResultMetadata(),s=270;null!==i&&!0===i.get(ei.ORIENTATION)&&(s+=i.get(ei.ORIENTATION)%360),n.putMetadata(ei.ORIENTATION,s);let o=n.getResultPoints();if(null!==o){let e=r.getHeight();for(let t=0;t<o.length;t++)o[t]=new eA(e-o[t].getY()-1,o[t].getX())}return n}throw new Z}}reset(){}doDecode(e,t){let r,n=e.getWidth(),i=e.getHeight(),s=new x(n),o=t&&!0===t.get(V.TRY_HARDER),a=Math.max(1,i>>(o?8:5));r=o?i:15;let l=Math.trunc(i/2);for(let o=0;o<r;o++){let r=Math.trunc((o+1)/2),h=l+a*((1&o)==0?r:-r);if(h<0||h>=i)break;try{s=e.getBlackRow(h,s)}catch(e){continue}for(let e=0;e<2;e++){if(1===e&&(s.reverse(),t&&!0===t.get(V.NEED_RESULT_POINT_CALLBACK))){let e=new Map;t.forEach((t,r)=>e.set(r,t)),e.delete(V.NEED_RESULT_POINT_CALLBACK),t=e}try{let r=this.decodeRow(h,s,t);if(1===e){r.putMetadata(ei.ORIENTATION,180);let e=r.getResultPoints();null!==e&&(e[0]=new eA(n-e[0].getX()-1,e[0].getY()),e[1]=new eA(n-e[1].getX()-1,e[1].getY()))}return r}catch(e){}}}throw new Z}static recordPattern(e,t,r){let n=r.length;for(let e=0;e<n;e++)r[e]=0;let i=e.getSize();if(t>=i)throw new Z;let s=!e.get(t),o=0,a=t;for(;a<i;){if(e.get(a)!==s)r[o]++;else if(++o===n)break;else r[o]=1,s=!s;a++}if(o!==n&&(o!==n-1||a!==i))throw new Z}static recordPatternInReverse(e,t,r){let n=r.length,i=e.get(t);for(;t>0&&n>=0;)e.get(--t)!==i&&(n--,i=!i);if(n>=0)throw new Z;ey.recordPattern(e,t+1,r)}static patternMatchVariance(e,t,r){let n=e.length,i=0,s=0;for(let r=0;r<n;r++)i+=e[r],s+=t[r];if(i<s)return Number.POSITIVE_INFINITY;let o=i/s;r*=o;let a=0;for(let i=0;i<n;i++){let n=e[i],s=t[i]*o,l=n>s?n-s:s-n;if(l>r)return Number.POSITIVE_INFINITY;a+=l}return a/i}}class eO extends ey{static findStartPattern(e){let t=e.getSize(),r=e.getNextSet(0),n=0,i=Int32Array.from([0,0,0,0,0,0]),s=r,o=!1;for(let a=r;a<t;a++)if(e.get(a)!==o)i[n]++;else{if(5===n){let t=eO.MAX_AVG_VARIANCE,r=-1;for(let e=eO.CODE_START_A;e<=eO.CODE_START_C;e++){let n=ey.patternMatchVariance(i,eO.CODE_PATTERNS[e],eO.MAX_INDIVIDUAL_VARIANCE);n<t&&(t=n,r=e)}if(r>=0&&e.isRange(Math.max(0,s-(a-s)/2),s,!1))return Int32Array.from([s,a,r]);s+=i[0]+i[1],(i=i.slice(2,i.length-1))[n-1]=0,i[n]=0,n--}else n++;i[n]=1,o=!o}throw new Z}static decodeCode(e,t,r){ey.recordPattern(e,r,t);let n=eO.MAX_AVG_VARIANCE,i=-1;for(let e=0;e<eO.CODE_PATTERNS.length;e++){let r=eO.CODE_PATTERNS[e],s=this.patternMatchVariance(t,r,eO.MAX_INDIVIDUAL_VARIANCE);s<n&&(n=s,i=e)}if(i>=0)return i;throw new Z}decodeRow(e,t,r){let n,i=r&&!0===r.get(V.ASSUME_GS1),s=eO.findStartPattern(t),o=s[2],a=0,l=new Uint8Array(20);switch(l[a++]=o,o){case eO.CODE_START_A:n=eO.CODE_CODE_A;break;case eO.CODE_START_B:n=eO.CODE_CODE_B;break;case eO.CODE_START_C:n=eO.CODE_CODE_C;break;default:throw new U}let h=!1,u=!1,d="",c=s[0],g=s[1],f=Int32Array.from([0,0,0,0,0,0]),w=0,A=0,C=o,E=0,m=!0,_=!1,I=!1;for(;!h;){let e=u;switch(u=!1,w=A,A=eO.decodeCode(t,f,g),l[a++]=A,A!==eO.CODE_STOP&&(m=!0),A!==eO.CODE_STOP&&(C+=++E*A),c=g,g+=f.reduce((e,t)=>e+t,0),A){case eO.CODE_START_A:case eO.CODE_START_B:case eO.CODE_START_C:throw new U}switch(n){case eO.CODE_CODE_A:if(A<64)I===_?d+=String.fromCharCode(32+A):d+=String.fromCharCode(32+A+128),I=!1;else if(A<96)I===_?d+=String.fromCharCode(A-64):d+=String.fromCharCode(A+64),I=!1;else switch(A!==eO.CODE_STOP&&(m=!1),A){case eO.CODE_FNC_1:i&&(0===d.length?d+="]C1":d+="\x1d");break;case eO.CODE_FNC_2:case eO.CODE_FNC_3:break;case eO.CODE_FNC_4_A:!_&&I?(_=!0,I=!1):_&&I?(_=!1,I=!1):I=!0;break;case eO.CODE_SHIFT:u=!0,n=eO.CODE_CODE_B;break;case eO.CODE_CODE_B:n=eO.CODE_CODE_B;break;case eO.CODE_CODE_C:n=eO.CODE_CODE_C;break;case eO.CODE_STOP:h=!0}break;case eO.CODE_CODE_B:if(A<96)I===_?d+=String.fromCharCode(32+A):d+=String.fromCharCode(32+A+128),I=!1;else switch(A!==eO.CODE_STOP&&(m=!1),A){case eO.CODE_FNC_1:i&&(0===d.length?d+="]C1":d+="\x1d");break;case eO.CODE_FNC_2:case eO.CODE_FNC_3:break;case eO.CODE_FNC_4_B:!_&&I?(_=!0,I=!1):_&&I?(_=!1,I=!1):I=!0;break;case eO.CODE_SHIFT:u=!0,n=eO.CODE_CODE_A;break;case eO.CODE_CODE_A:n=eO.CODE_CODE_A;break;case eO.CODE_CODE_C:n=eO.CODE_CODE_C;break;case eO.CODE_STOP:h=!0}break;case eO.CODE_CODE_C:if(A<100)A<10&&(d+="0"),d+=A;else switch(A!==eO.CODE_STOP&&(m=!1),A){case eO.CODE_FNC_1:i&&(0===d.length?d+="]C1":d+="\x1d");break;case eO.CODE_CODE_A:n=eO.CODE_CODE_A;break;case eO.CODE_CODE_B:n=eO.CODE_CODE_B;break;case eO.CODE_STOP:h=!0}}e&&(n=n===eO.CODE_CODE_A?eO.CODE_CODE_B:eO.CODE_CODE_A)}let S=g-c;if(g=t.getNextUnset(g),!t.isRange(g,Math.min(t.getSize(),g+(g-c)/2),!1))throw new Z;if((C-=E*w)%103!==w)throw new B;let p=d.length;if(0===p)throw new Z;p>0&&m&&(d=n===eO.CODE_CODE_C?d.substring(0,p-2):d.substring(0,p-1));let T=(s[1]+s[0])/2,R=c+S/2,N=l.length,D=new Uint8Array(N);for(let e=0;e<N;e++)D[e]=l[e];return new er(d,D,0,[new eA(T,e),new eA(R,e)],en.CODE_128,new Date().getTime())}}eO.CODE_PATTERNS=[Int32Array.from([2,1,2,2,2,2]),Int32Array.from([2,2,2,1,2,2]),Int32Array.from([2,2,2,2,2,1]),Int32Array.from([1,2,1,2,2,3]),Int32Array.from([1,2,1,3,2,2]),Int32Array.from([1,3,1,2,2,2]),Int32Array.from([1,2,2,2,1,3]),Int32Array.from([1,2,2,3,1,2]),Int32Array.from([1,3,2,2,1,2]),Int32Array.from([2,2,1,2,1,3]),Int32Array.from([2,2,1,3,1,2]),Int32Array.from([2,3,1,2,1,2]),Int32Array.from([1,1,2,2,3,2]),Int32Array.from([1,2,2,1,3,2]),Int32Array.from([1,2,2,2,3,1]),Int32Array.from([1,1,3,2,2,2]),Int32Array.from([1,2,3,1,2,2]),Int32Array.from([1,2,3,2,2,1]),Int32Array.from([2,2,3,2,1,1]),Int32Array.from([2,2,1,1,3,2]),Int32Array.from([2,2,1,2,3,1]),Int32Array.from([2,1,3,2,1,2]),Int32Array.from([2,2,3,1,1,2]),Int32Array.from([3,1,2,1,3,1]),Int32Array.from([3,1,1,2,2,2]),Int32Array.from([3,2,1,1,2,2]),Int32Array.from([3,2,1,2,2,1]),Int32Array.from([3,1,2,2,1,2]),Int32Array.from([3,2,2,1,1,2]),Int32Array.from([3,2,2,2,1,1]),Int32Array.from([2,1,2,1,2,3]),Int32Array.from([2,1,2,3,2,1]),Int32Array.from([2,3,2,1,2,1]),Int32Array.from([1,1,1,3,2,3]),Int32Array.from([1,3,1,1,2,3]),Int32Array.from([1,3,1,3,2,1]),Int32Array.from([1,1,2,3,1,3]),Int32Array.from([1,3,2,1,1,3]),Int32Array.from([1,3,2,3,1,1]),Int32Array.from([2,1,1,3,1,3]),Int32Array.from([2,3,1,1,1,3]),Int32Array.from([2,3,1,3,1,1]),Int32Array.from([1,1,2,1,3,3]),Int32Array.from([1,1,2,3,3,1]),Int32Array.from([1,3,2,1,3,1]),Int32Array.from([1,1,3,1,2,3]),Int32Array.from([1,1,3,3,2,1]),Int32Array.from([1,3,3,1,2,1]),Int32Array.from([3,1,3,1,2,1]),Int32Array.from([2,1,1,3,3,1]),Int32Array.from([2,3,1,1,3,1]),Int32Array.from([2,1,3,1,1,3]),Int32Array.from([2,1,3,3,1,1]),Int32Array.from([2,1,3,1,3,1]),Int32Array.from([3,1,1,1,2,3]),Int32Array.from([3,1,1,3,2,1]),Int32Array.from([3,3,1,1,2,1]),Int32Array.from([3,1,2,1,1,3]),Int32Array.from([3,1,2,3,1,1]),Int32Array.from([3,3,2,1,1,1]),Int32Array.from([3,1,4,1,1,1]),Int32Array.from([2,2,1,4,1,1]),Int32Array.from([4,3,1,1,1,1]),Int32Array.from([1,1,1,2,2,4]),Int32Array.from([1,1,1,4,2,2]),Int32Array.from([1,2,1,1,2,4]),Int32Array.from([1,2,1,4,2,1]),Int32Array.from([1,4,1,1,2,2]),Int32Array.from([1,4,1,2,2,1]),Int32Array.from([1,1,2,2,1,4]),Int32Array.from([1,1,2,4,1,2]),Int32Array.from([1,2,2,1,1,4]),Int32Array.from([1,2,2,4,1,1]),Int32Array.from([1,4,2,1,1,2]),Int32Array.from([1,4,2,2,1,1]),Int32Array.from([2,4,1,2,1,1]),Int32Array.from([2,2,1,1,1,4]),Int32Array.from([4,1,3,1,1,1]),Int32Array.from([2,4,1,1,1,2]),Int32Array.from([1,3,4,1,1,1]),Int32Array.from([1,1,1,2,4,2]),Int32Array.from([1,2,1,1,4,2]),Int32Array.from([1,2,1,2,4,1]),Int32Array.from([1,1,4,2,1,2]),Int32Array.from([1,2,4,1,1,2]),Int32Array.from([1,2,4,2,1,1]),Int32Array.from([4,1,1,2,1,2]),Int32Array.from([4,2,1,1,1,2]),Int32Array.from([4,2,1,2,1,1]),Int32Array.from([2,1,2,1,4,1]),Int32Array.from([2,1,4,1,2,1]),Int32Array.from([4,1,2,1,2,1]),Int32Array.from([1,1,1,1,4,3]),Int32Array.from([1,1,1,3,4,1]),Int32Array.from([1,3,1,1,4,1]),Int32Array.from([1,1,4,1,1,3]),Int32Array.from([1,1,4,3,1,1]),Int32Array.from([4,1,1,1,1,3]),Int32Array.from([4,1,1,3,1,1]),Int32Array.from([1,1,3,1,4,1]),Int32Array.from([1,1,4,1,3,1]),Int32Array.from([3,1,1,1,4,1]),Int32Array.from([4,1,1,1,3,1]),Int32Array.from([2,1,1,4,1,2]),Int32Array.from([2,1,1,2,1,4]),Int32Array.from([2,1,1,2,3,2]),Int32Array.from([2,3,3,1,1,1,2])],eO.MAX_AVG_VARIANCE=.25,eO.MAX_INDIVIDUAL_VARIANCE=.7,eO.CODE_SHIFT=98,eO.CODE_CODE_C=99,eO.CODE_CODE_B=100,eO.CODE_CODE_A=101,eO.CODE_FNC_1=102,eO.CODE_FNC_2=97,eO.CODE_FNC_3=96,eO.CODE_FNC_4_A=101,eO.CODE_FNC_4_B=100,eO.CODE_START_A=103,eO.CODE_START_B=104,eO.CODE_START_C=105,eO.CODE_STOP=106;class eM extends ey{constructor(e=!1,t=!1){super(),this.usingCheckDigit=e,this.extendedMode=t,this.decodeRowResult="",this.counters=new Int32Array(9)}decodeRow(e,t,r){let n,i,s,o=this.counters;o.fill(0),this.decodeRowResult="";let a=eM.findAsteriskPattern(t,o),l=t.getNextSet(a[1]),h=t.getSize();do{eM.recordPattern(t,l,o);let e=eM.toNarrowWidePattern(o);if(e<0)throw new Z;for(let t of(n=eM.patternToChar(e),this.decodeRowResult+=n,i=l,o))l+=t;l=t.getNextSet(l)}while("*"!==n);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);let u=0;for(let e of o)u+=e;let d=l-i-u;if(l!==h&&2*d<u)throw new Z;if(this.usingCheckDigit){let e=this.decodeRowResult.length-1,t=0;for(let r=0;r<e;r++)t+=eM.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(r));if(this.decodeRowResult.charAt(e)!==eM.ALPHABET_STRING.charAt(t%43))throw new B;this.decodeRowResult=this.decodeRowResult.substring(0,e)}if(0===this.decodeRowResult.length)throw new Z;s=this.extendedMode?eM.decodeExtended(this.decodeRowResult):this.decodeRowResult;let c=(a[1]+a[0])/2,g=i+u/2;return new er(s,null,0,[new eA(c,e),new eA(g,e)],en.CODE_39,new Date().getTime())}static findAsteriskPattern(e,t){let r=e.getSize(),n=e.getNextSet(0),i=0,s=n,o=!1,a=t.length;for(let l=n;l<r;l++)if(e.get(l)!==o)t[i]++;else{if(i===a-1){if(this.toNarrowWidePattern(t)===eM.ASTERISK_ENCODING&&e.isRange(Math.max(0,s-Math.floor((l-s)/2)),s,!1))return[s,l];s+=t[0]+t[1],t.copyWithin(0,2,2+i-1),t[i-1]=0,t[i]=0,i--}else i++;t[i]=1,o=!o}throw new Z}static toNarrowWidePattern(e){let t,r=e.length,n=0;do{let i=0x7fffffff;for(let t of e)t<i&&t>n&&(i=t);n=i,t=0;let s=0,o=0;for(let i=0;i<r;i++){let a=e[i];a>n&&(o|=1<<r-1-i,t++,s+=a)}if(3===t){for(let i=0;i<r&&t>0;i++){let r=e[i];if(r>n&&(t--,2*r>=s))return -1}return o}}while(t>3);return -1}static patternToChar(e){for(let t=0;t<eM.CHARACTER_ENCODINGS.length;t++)if(eM.CHARACTER_ENCODINGS[t]===e)return eM.ALPHABET_STRING.charAt(t);if(e===eM.ASTERISK_ENCODING)return"*";throw new Z}static decodeExtended(e){let t=e.length,r="";for(let n=0;n<t;n++){let t=e.charAt(n);if("+"===t||"$"===t||"%"===t||"/"===t){let i=e.charAt(n+1),s="\0";switch(t){case"+":if(i>="A"&&i<="Z")s=String.fromCharCode(i.charCodeAt(0)+32);else throw new U;break;case"$":if(i>="A"&&i<="Z")s=String.fromCharCode(i.charCodeAt(0)-64);else throw new U;break;case"%":if(i>="A"&&i<="E")s=String.fromCharCode(i.charCodeAt(0)-38);else if(i>="F"&&i<="J")s=String.fromCharCode(i.charCodeAt(0)-11);else if(i>="K"&&i<="O")s=String.fromCharCode(i.charCodeAt(0)+16);else if(i>="P"&&i<="T")s=String.fromCharCode(i.charCodeAt(0)+43);else if("U"===i)s="\0";else if("V"===i)s="@";else if("W"===i)s="`";else if("X"===i||"Y"===i||"Z"===i)s="";else throw new U;break;case"/":if(i>="A"&&i<="O")s=String.fromCharCode(i.charCodeAt(0)-32);else if("Z"===i)s=":";else throw new U}r+=s,n++}else r+=t}return r}}eM.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",eM.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],eM.ASTERISK_ENCODING=148;class eB extends ey{constructor(){super(...arguments),this.narrowLineWidth=-1}decodeRow(e,t,r){let n=this.decodeStart(t),i=this.decodeEnd(t),s=new z;eB.decodeMiddle(t,n[1],i[0],s);let o=s.toString(),a=null;null!=r&&(a=r.get(V.ALLOWED_LENGTHS)),null==a&&(a=eB.DEFAULT_ALLOWED_LENGTHS);let l=o.length,h=!1,u=0;for(let e of a){if(l===e){h=!0;break}e>u&&(u=e)}if(!h&&l>u&&(h=!0),!h)throw new U;return new er(o,null,0,[new eA(n[1],e),new eA(i[0],e)],en.ITF,new Date().getTime())}static decodeMiddle(e,t,r,n){let i=new Int32Array(10),s=new Int32Array(5),o=new Int32Array(5);for(i.fill(0),s.fill(0),o.fill(0);t<r;){ey.recordPattern(e,t,i);for(let e=0;e<5;e++){let t=2*e;s[e]=i[t],o[e]=i[t+1]}let r=eB.decodeDigit(s);n.append(r.toString()),r=this.decodeDigit(o),n.append(r.toString()),i.forEach(function(e){t+=e})}}decodeStart(e){let t=eB.skipWhiteSpace(e),r=eB.findGuardPattern(e,t,eB.START_PATTERN);return this.narrowLineWidth=(r[1]-r[0])/4,this.validateQuietZone(e,r[0]),r}validateQuietZone(e,t){let r=10*this.narrowLineWidth;r=r<t?r:t;for(let n=t-1;r>0&&n>=0&&!e.get(n);n--)r--;if(0!==r)throw new Z}static skipWhiteSpace(e){let t=e.getSize(),r=e.getNextSet(0);if(r===t)throw new Z;return r}decodeEnd(e){e.reverse();try{let t,r=eB.skipWhiteSpace(e);try{t=eB.findGuardPattern(e,r,eB.END_PATTERN_REVERSED[0])}catch(n){n instanceof Z&&(t=eB.findGuardPattern(e,r,eB.END_PATTERN_REVERSED[1]))}this.validateQuietZone(e,t[0]);let n=t[0];return t[0]=e.getSize()-t[1],t[1]=e.getSize()-n,t}finally{e.reverse()}}static findGuardPattern(e,t,r){let n=r.length,i=new Int32Array(n),s=e.getSize(),o=!1,a=0,l=t;i.fill(0);for(let h=t;h<s;h++)if(e.get(h)!==o)i[a]++;else{if(a===n-1){if(ey.patternMatchVariance(i,r,eB.MAX_INDIVIDUAL_VARIANCE)<eB.MAX_AVG_VARIANCE)return[l,h];l+=i[0]+i[1],P.arraycopy(i,2,i,0,a-1),i[a-1]=0,i[a]=0,a--}else a++;i[a]=1,o=!o}throw new Z}static decodeDigit(e){let t=eB.MAX_AVG_VARIANCE,r=-1,n=eB.PATTERNS.length;for(let i=0;i<n;i++){let n=eB.PATTERNS[i],s=ey.patternMatchVariance(e,n,eB.MAX_INDIVIDUAL_VARIANCE);s<t?(t=s,r=i):s===t&&(r=-1)}if(r>=0)return r%10;throw new Z}}eB.PATTERNS=[Int32Array.from([1,1,2,2,1]),Int32Array.from([2,1,1,1,2]),Int32Array.from([1,2,1,1,2]),Int32Array.from([2,2,1,1,1]),Int32Array.from([1,1,2,1,2]),Int32Array.from([2,1,2,1,1]),Int32Array.from([1,2,2,1,1]),Int32Array.from([1,1,1,2,2]),Int32Array.from([2,1,1,2,1]),Int32Array.from([1,2,1,2,1]),Int32Array.from([1,1,3,3,1]),Int32Array.from([3,1,1,1,3]),Int32Array.from([1,3,1,1,3]),Int32Array.from([3,3,1,1,1]),Int32Array.from([1,1,3,1,3]),Int32Array.from([3,1,3,1,1]),Int32Array.from([1,3,3,1,1]),Int32Array.from([1,1,1,3,3]),Int32Array.from([3,1,1,3,1]),Int32Array.from([1,3,1,3,1])],eB.MAX_AVG_VARIANCE=.38,eB.MAX_INDIVIDUAL_VARIANCE=.5,eB.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],eB.START_PATTERN=Int32Array.from([1,1,1,1]),eB.END_PATTERN_REVERSED=[Int32Array.from([1,1,2]),Int32Array.from([1,1,3])];class eb extends ey{constructor(){super(...arguments),this.decodeRowStringBuffer=""}static findStartGuardPattern(e){let t,r=!1,n=0,i=Int32Array.from([0,0,0]);for(;!r;){i=Int32Array.from([0,0,0]);let s=(t=eb.findGuardPattern(e,n,!1,this.START_END_PATTERN,i))[0],o=s-((n=t[1])-s);o>=0&&(r=e.isRange(o,s,!1))}return t}static checkChecksum(e){return eb.checkStandardUPCEANChecksum(e)}static checkStandardUPCEANChecksum(e){let t=e.length;if(0===t)return!1;let r=parseInt(e.charAt(t-1),10);return eb.getStandardUPCEANChecksum(e.substring(0,t-1))===r}static getStandardUPCEANChecksum(e){let t=e.length,r=0;for(let n=t-1;n>=0;n-=2){let t=e.charAt(n).charCodeAt(0)-48;if(t<0||t>9)throw new U;r+=t}r*=3;for(let n=t-2;n>=0;n-=2){let t=e.charAt(n).charCodeAt(0)-48;if(t<0||t>9)throw new U;r+=t}return(1e3-r)%10}static decodeEnd(e,t){return eb.findGuardPattern(e,t,!1,eb.START_END_PATTERN,new Int32Array(eb.START_END_PATTERN.length).fill(0))}static findGuardPatternWithoutCounters(e,t,r,n){return this.findGuardPattern(e,t,r,n,new Int32Array(n.length))}static findGuardPattern(e,t,r,n,i){let s=e.getSize();t=r?e.getNextUnset(t):e.getNextSet(t);let o=0,a=t,l=n.length,h=r;for(let r=t;r<s;r++)if(e.get(r)!==h)i[o]++;else{if(o===l-1){if(ey.patternMatchVariance(i,n,eb.MAX_INDIVIDUAL_VARIANCE)<eb.MAX_AVG_VARIANCE)return Int32Array.from([a,r]);a+=i[0]+i[1];let e=i.slice(2,i.length-1);for(let t=0;t<o-1;t++)i[t]=e[t];i[o-1]=0,i[o]=0,o--}else o++;i[o]=1,h=!h}throw new Z}static decodeDigit(e,t,r,n){this.recordPattern(e,r,t);let i=this.MAX_AVG_VARIANCE,s=-1,o=n.length;for(let e=0;e<o;e++){let r=n[e],o=ey.patternMatchVariance(t,r,eb.MAX_INDIVIDUAL_VARIANCE);o<i&&(i=o,s=e)}if(s>=0)return s;throw new Z}}eb.MAX_AVG_VARIANCE=.48,eb.MAX_INDIVIDUAL_VARIANCE=.7,eb.START_END_PATTERN=Int32Array.from([1,1,1]),eb.MIDDLE_PATTERN=Int32Array.from([1,1,1,1,1]),eb.END_PATTERN=Int32Array.from([1,1,1,1,1,1]),eb.L_PATTERNS=[Int32Array.from([3,2,1,1]),Int32Array.from([2,2,2,1]),Int32Array.from([2,1,2,2]),Int32Array.from([1,4,1,1]),Int32Array.from([1,1,3,2]),Int32Array.from([1,2,3,1]),Int32Array.from([1,1,1,4]),Int32Array.from([1,3,1,2]),Int32Array.from([1,2,1,3]),Int32Array.from([3,1,1,2])];class eP{constructor(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}decodeRow(e,t,r){let n=this.decodeRowStringBuffer,i=this.decodeMiddle(t,r,n),s=n.toString(),o=eP.parseExtensionString(s),a=new er(s,null,0,[new eA((r[0]+r[1])/2,e),new eA(i,e)],en.UPC_EAN_EXTENSION,new Date().getTime());return null!=o&&a.putAllMetadata(o),a}decodeMiddle(e,t,r){let n=this.decodeMiddleCounters;n[0]=0,n[1]=0,n[2]=0,n[3]=0;let i=e.getSize(),s=t[1],o=0;for(let t=0;t<5&&s<i;t++){let i=eb.decodeDigit(e,n,s,eb.L_AND_G_PATTERNS);for(let e of(r+=String.fromCharCode(48+i%10),n))s+=e;i>=10&&(o|=1<<4-t),4!==t&&(s=e.getNextSet(s),s=e.getNextUnset(s))}if(5!==r.length)throw new Z;let a=this.determineCheckDigit(o);if(eP.extensionChecksum(r.toString())!==a)throw new Z;return s}static extensionChecksum(e){let t=e.length,r=0;for(let n=t-2;n>=0;n-=2)r+=e.charAt(n).charCodeAt(0)-48;r*=3;for(let n=t-1;n>=0;n-=2)r+=e.charAt(n).charCodeAt(0)-48;return(r*=3)%10}determineCheckDigit(e){for(let t=0;t<10;t++)if(e===this.CHECK_DIGIT_ENCODINGS[t])return t;throw new Z}static parseExtensionString(e){if(5!==e.length)return null;let t=eP.parseExtension5String(e);return null==t?null:new Map([[ei.SUGGESTED_PRICE,t]])}static parseExtension5String(e){let t;switch(e.charAt(0)){case"0":t="\xa3";break;case"5":t="$";break;case"9":switch(e){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}t="";break;default:t=""}let r=parseInt(e.substring(1)),n=(r/100).toString(),i=r%100;return t+n+"."+(i<10?"0"+i:i.toString())}}class eL{constructor(){this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}decodeRow(e,t,r){let n=this.decodeRowStringBuffer,i=this.decodeMiddle(t,r,n),s=n.toString(),o=eL.parseExtensionString(s),a=new er(s,null,0,[new eA((r[0]+r[1])/2,e),new eA(i,e)],en.UPC_EAN_EXTENSION,new Date().getTime());return null!=o&&a.putAllMetadata(o),a}decodeMiddle(e,t,r){let n=this.decodeMiddleCounters;n[0]=0,n[1]=0,n[2]=0,n[3]=0;let i=e.getSize(),s=t[1],o=0;for(let t=0;t<2&&s<i;t++){let i=eb.decodeDigit(e,n,s,eb.L_AND_G_PATTERNS);for(let e of(r+=String.fromCharCode(48+i%10),n))s+=e;i>=10&&(o|=1<<1-t),1!==t&&(s=e.getNextSet(s),s=e.getNextUnset(s))}if(2!==r.length||parseInt(r.toString())%4!==o)throw new Z;return s}static parseExtensionString(e){return 2!==e.length?null:new Map([[ei.ISSUE_NUMBER,parseInt(e)]])}}class eF{static decodeRow(e,t,r){let n=eb.findGuardPattern(t,r,!1,this.EXTENSION_START_PATTERN,new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));try{return new eP().decodeRow(e,t,n)}catch(r){return new eL().decodeRow(e,t,n)}}}eF.EXTENSION_START_PATTERN=Int32Array.from([1,1,2]);class ev extends eb{constructor(){super(),this.decodeRowStringBuffer="",ev.L_AND_G_PATTERNS=ev.L_PATTERNS.map(e=>Int32Array.from(e));for(let e=10;e<20;e++){let t=ev.L_PATTERNS[e-10],r=new Int32Array(t.length);for(let e=0;e<t.length;e++)r[e]=t[t.length-e-1];ev.L_AND_G_PATTERNS[e]=r}}decodeRow(e,t,r){let n=ev.findStartGuardPattern(t),i=null==r?null:r.get(V.NEED_RESULT_POINT_CALLBACK);if(null!=i){let t=new eA((n[0]+n[1])/2,e);i.foundPossibleResultPoint(t)}let s=this.decodeMiddle(t,n,this.decodeRowStringBuffer),o=s.rowOffset,a=s.resultString;if(null!=i){let t=new eA(o,e);i.foundPossibleResultPoint(t)}let l=this.decodeEnd(t,o);if(null!=i){let t=new eA((l[0]+l[1])/2,e);i.foundPossibleResultPoint(t)}let h=l[1],u=h+(h-l[0]);if(u>=t.getSize()||!t.isRange(h,u,!1))throw new Z;let d=a.toString();if(d.length<8)throw new U;if(!ev.checkChecksum(d))throw new B;let c=(n[1]+n[0])/2,g=(l[1]+l[0])/2,f=this.getBarcodeFormat(),w=new er(d,null,0,[new eA(c,e),new eA(g,e)],f,new Date().getTime()),A=0;try{let r=eF.decodeRow(e,t,l[1]);w.putMetadata(ei.UPC_EAN_EXTENSION,r.getText()),w.putAllMetadata(r.getResultMetadata()),w.addResultPoints(r.getResultPoints()),A=r.getText().length}catch(e){}let C=null==r?null:r.get(V.ALLOWED_EAN_EXTENSIONS);if(null!=C){let e=!1;for(let t in C)if(A.toString()===t){e=!0;break}if(!e)throw new Z}return w}decodeEnd(e,t){return ev.findGuardPattern(e,t,!1,ev.START_END_PATTERN,new Int32Array(ev.START_END_PATTERN.length).fill(0))}static checkChecksum(e){return ev.checkStandardUPCEANChecksum(e)}static checkStandardUPCEANChecksum(e){let t=e.length;if(0===t)return!1;let r=parseInt(e.charAt(t-1),10);return ev.getStandardUPCEANChecksum(e.substring(0,t-1))===r}static getStandardUPCEANChecksum(e){let t=e.length,r=0;for(let n=t-1;n>=0;n-=2){let t=e.charAt(n).charCodeAt(0)-48;if(t<0||t>9)throw new U;r+=t}r*=3;for(let n=t-2;n>=0;n-=2){let t=e.charAt(n).charCodeAt(0)-48;if(t<0||t>9)throw new U;r+=t}return(1e3-r)%10}}class ek extends ev{constructor(){super(),this.decodeMiddleCounters=Int32Array.from([0,0,0,0])}decodeMiddle(e,t,r){let n=this.decodeMiddleCounters;n[0]=0,n[1]=0,n[2]=0,n[3]=0;let i=e.getSize(),s=t[1],o=0;for(let t=0;t<6&&s<i;t++){let i=ev.decodeDigit(e,n,s,ev.L_AND_G_PATTERNS);for(let e of(r+=String.fromCharCode(48+i%10),n))s+=e;i>=10&&(o|=1<<5-t)}r=ek.determineFirstDigit(r,o),s=ev.findGuardPattern(e,s,!0,ev.MIDDLE_PATTERN,new Int32Array(ev.MIDDLE_PATTERN.length).fill(0))[1];for(let t=0;t<6&&s<i;t++){let t=ev.decodeDigit(e,n,s,ev.L_PATTERNS);for(let e of(r+=String.fromCharCode(48+t),n))s+=e}return{rowOffset:s,resultString:r}}getBarcodeFormat(){return en.EAN_13}static determineFirstDigit(e,t){for(let r=0;r<10;r++)if(t===this.FIRST_DIGIT_ENCODINGS[r])return e=String.fromCharCode(48+r)+e;throw new Z}}ek.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26];class ex extends ev{constructor(){super(),this.decodeMiddleCounters=Int32Array.from([0,0,0,0])}decodeMiddle(e,t,r){let n=this.decodeMiddleCounters;n[0]=0,n[1]=0,n[2]=0,n[3]=0;let i=e.getSize(),s=t[1];for(let t=0;t<4&&s<i;t++){let t=ev.decodeDigit(e,n,s,ev.L_PATTERNS);for(let e of(r+=String.fromCharCode(48+t),n))s+=e}s=ev.findGuardPattern(e,s,!0,ev.MIDDLE_PATTERN,new Int32Array(ev.MIDDLE_PATTERN.length).fill(0))[1];for(let t=0;t<4&&s<i;t++){let t=ev.decodeDigit(e,n,s,ev.L_PATTERNS);for(let e of(r+=String.fromCharCode(48+t),n))s+=e}return{rowOffset:s,resultString:r}}getBarcodeFormat(){return en.EAN_8}}class eV extends ev{constructor(){super(...arguments),this.ean13Reader=new ek}getBarcodeFormat(){return en.UPC_A}decode(e,t){return this.maybeReturnResult(this.ean13Reader.decode(e))}decodeRow(e,t,r){return this.maybeReturnResult(this.ean13Reader.decodeRow(e,t,r))}decodeMiddle(e,t,r){return this.ean13Reader.decodeMiddle(e,t,r)}maybeReturnResult(e){let t=e.getText();if("0"===t.charAt(0)){let r=new er(t.substring(1),null,null,e.getResultPoints(),en.UPC_A);return null!=e.getResultMetadata()&&r.putAllMetadata(e.getResultMetadata()),r}throw new Z}reset(){this.ean13Reader.reset()}}class eU extends ev{constructor(){super(),this.decodeMiddleCounters=new Int32Array(4)}decodeMiddle(e,t,r){let n=this.decodeMiddleCounters.map(e=>e);n[0]=0,n[1]=0,n[2]=0,n[3]=0;let i=e.getSize(),s=t[1],o=0;for(let t=0;t<6&&s<i;t++){let i=eU.decodeDigit(e,n,s,eU.L_AND_G_PATTERNS);for(let e of(r+=String.fromCharCode(48+i%10),n))s+=e;i>=10&&(o|=1<<5-t)}return{rowOffset:s,resultString:eU.determineNumSysAndCheckDigit(r,o)}}decodeEnd(e,t){return eU.findGuardPatternWithoutCounters(e,t,!0,eU.MIDDLE_END_PATTERN)}checkChecksum(e){return ev.checkChecksum(eU.convertUPCEtoUPCA(e))}static determineNumSysAndCheckDigit(e,t){for(let r=0;r<=1;r++)for(let n=0;n<10;n++)if(t===this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[r][n])return String.fromCharCode(48+r)+e+String.fromCharCode(48+n);throw Z.getNotFoundInstance()}getBarcodeFormat(){return en.UPC_E}static convertUPCEtoUPCA(e){let t=e.slice(1,7).split("").map(e=>e.charCodeAt(0)),r=new z;r.append(e.charAt(0));let n=t[5];switch(n){case 0:case 1:case 2:r.appendChars(t,0,2),r.append(n),r.append("0000"),r.appendChars(t,2,3);break;case 3:r.appendChars(t,0,3),r.append("00000"),r.appendChars(t,3,2);break;case 4:r.appendChars(t,0,4),r.append("00000"),r.append(t[4]);break;default:r.appendChars(t,0,5),r.append("0000"),r.append(n)}return e.length>=8&&r.append(e.charAt(7)),r.toString()}}eU.MIDDLE_END_PATTERN=Int32Array.from([1,1,1,1,1,1]),eU.NUMSYS_AND_CHECK_DIGIT_PATTERNS=[Int32Array.from([56,52,50,49,44,38,35,42,41,37]),Int32Array.from([7,11,13,14,19,25,28,21,22,26])];class eH extends ey{constructor(e){super();let t=null==e?null:e.get(V.POSSIBLE_FORMATS),r=[];null==t?(r.push(new ek),r.push(new eV),r.push(new ex),r.push(new eU)):(t.indexOf(en.EAN_13)>-1&&r.push(new ek),t.indexOf(en.UPC_A)>-1&&r.push(new eV),t.indexOf(en.EAN_8)>-1&&r.push(new ex),t.indexOf(en.UPC_E)>-1&&r.push(new eU)),this.readers=r}decodeRow(e,t,r){for(let n of this.readers)try{let i=n.decodeRow(e,t,r),s=i.getBarcodeFormat()===en.EAN_13&&"0"===i.getText().charAt(0),o=null==r?null:r.get(V.POSSIBLE_FORMATS),a=null==o||o.includes(en.UPC_A);if(s&&a){let e=i.getRawBytes(),t=new er(i.getText().substring(1),e,e?e.length:null,i.getResultPoints(),en.UPC_A);return t.putAllMetadata(i.getResultMetadata()),t}return i}catch(e){}throw new Z}reset(){for(let e of this.readers)e.reset()}}class eG extends ey{constructor(){super(),this.decodeFinderCounters=new Int32Array(4),this.dataCharacterCounters=new Int32Array(8),this.oddRoundingErrors=[,,,,],this.evenRoundingErrors=[,,,,],this.oddCounts=Array(this.dataCharacterCounters.length/2),this.evenCounts=Array(this.dataCharacterCounters.length/2)}getDecodeFinderCounters(){return this.decodeFinderCounters}getDataCharacterCounters(){return this.dataCharacterCounters}getOddRoundingErrors(){return this.oddRoundingErrors}getEvenRoundingErrors(){return this.evenRoundingErrors}getOddCounts(){return this.oddCounts}getEvenCounts(){return this.evenCounts}parseFinderValue(e,t){for(let r=0;r<t.length;r++)if(ey.patternMatchVariance(e,t[r],eG.MAX_INDIVIDUAL_VARIANCE)<eG.MAX_AVG_VARIANCE)return r;throw new Z}static count(e){return ef.sum(new Int32Array(e))}static increment(e,t){let r=0,n=t[0];for(let i=1;i<e.length;i++)t[i]>n&&(n=t[i],r=i);e[r]++}static decrement(e,t){let r=0,n=t[0];for(let i=1;i<e.length;i++)t[i]<n&&(n=t[i],r=i);e[r]--}static isFinderPattern(e){let t=e[0]+e[1],r=t+e[2]+e[3],n=t/r;if(n>=eG.MIN_FINDER_PATTERN_RATIO&&n<=eG.MAX_FINDER_PATTERN_RATIO){let t=Number.MAX_SAFE_INTEGER,r=Number.MIN_SAFE_INTEGER;for(let n of e)n>r&&(r=n),n<t&&(t=n);return r<10*t}return!1}}eG.MAX_AVG_VARIANCE=.2,eG.MAX_INDIVIDUAL_VARIANCE=.45,eG.MIN_FINDER_PATTERN_RATIO=9.5/12,eG.MAX_FINDER_PATTERN_RATIO=12.5/14;class eX{constructor(e,t){this.value=e,this.checksumPortion=t}getValue(){return this.value}getChecksumPortion(){return this.checksumPortion}toString(){return this.value+"("+this.checksumPortion+")"}equals(e){return e instanceof eX&&this.value===e.value&&this.checksumPortion===e.checksumPortion}hashCode(){return this.value^this.checksumPortion}}class eW{constructor(e,t,r,n,i){this.value=e,this.startEnd=t,this.value=e,this.startEnd=t,this.resultPoints=[],this.resultPoints.push(new eA(r,i)),this.resultPoints.push(new eA(n,i))}getValue(){return this.value}getStartEnd(){return this.startEnd}getResultPoints(){return this.resultPoints}equals(e){return e instanceof eW&&this.value===e.value}hashCode(){return this.value}}class ez{constructor(){}static getRSSvalue(e,t,r){let n=0;for(let t of e)n+=t;let i=0,s=0,o=e.length;for(let a=0;a<o-1;a++){let l;for(l=1,s|=1<<a;l<e[a];l++,s&=~(1<<a)){let e=ez.combins(n-l-1,o-a-2);if(r&&0===s&&n-l-(o-a-1)>=o-a-1&&(e-=ez.combins(n-l-(o-a),o-a-2)),o-a-1>1){let r=0;for(let e=n-l-(o-a-2);e>t;e--)r+=ez.combins(n-l-e-1,o-a-3);e-=r*(o-1-a)}else n-l>t&&e--;i+=e}n-=l}return i}static combins(e,t){let r,n;e-t>t?(n=t,r=e-t):(n=e-t,r=t);let i=1,s=1;for(let t=e;t>r;t--)i*=t,s<=n&&(i/=s,s++);for(;s<=n;)i/=s,s++;return i}}class eY{static buildBitArray(e){let t=2*e.length-1;null==e[e.length-1].getRightChar()&&(t-=1);let r=new x(12*t),n=0,i=e[0].getRightChar().getValue();for(let e=11;e>=0;--e)(i&1<<e)!=0&&r.set(n),n++;for(let t=1;t<e.length;++t){let i=e[t],s=i.getLeftChar().getValue();for(let e=11;e>=0;--e)(s&1<<e)!=0&&r.set(n),n++;if(null!=i.getRightChar()){let e=i.getRightChar().getValue();for(let t=11;t>=0;--t)(e&1<<t)!=0&&r.set(n),n++}}return r}}class eZ{constructor(e,t){t?this.decodedInformation=null:(this.finished=e,this.decodedInformation=t)}getDecodedInformation(){return this.decodedInformation}isFinished(){return this.finished}}class eK{constructor(e){this.newPosition=e}getNewPosition(){return this.newPosition}}class eq extends eK{constructor(e,t){super(e),this.value=t}getValue(){return this.value}isFNC1(){return this.value===eq.FNC1}}eq.FNC1="$";class eQ extends eK{constructor(e,t,r){super(e),r?(this.remaining=!0,this.remainingValue=this.remainingValue):(this.remaining=!1,this.remainingValue=0),this.newString=t}getNewString(){return this.newString}isRemaining(){return this.remaining}getRemainingValue(){return this.remainingValue}}class ej extends eK{constructor(e,t,r){if(super(e),t<0||t>10||r<0||r>10)throw new U;this.firstDigit=t,this.secondDigit=r}getFirstDigit(){return this.firstDigit}getSecondDigit(){return this.secondDigit}getValue(){return 10*this.firstDigit+this.secondDigit}isFirstDigitFNC1(){return this.firstDigit===ej.FNC1}isSecondDigitFNC1(){return this.secondDigit===ej.FNC1}isAnyFNC1(){return this.firstDigit===ej.FNC1||this.secondDigit===ej.FNC1}}ej.FNC1=10;class eJ{constructor(){}static parseFieldsInGeneralPurpose(e){if(!e)return null;if(e.length<2)throw new Z;let t=e.substring(0,2);for(let r of eJ.TWO_DIGIT_DATA_LENGTH)if(r[0]===t){if(r[1]===eJ.VARIABLE_LENGTH)return eJ.processVariableAI(2,r[2],e);return eJ.processFixedAI(2,r[1],e)}if(e.length<3)throw new Z;let r=e.substring(0,3);for(let t of eJ.THREE_DIGIT_DATA_LENGTH)if(t[0]===r){if(t[1]===eJ.VARIABLE_LENGTH)return eJ.processVariableAI(3,t[2],e);return eJ.processFixedAI(3,t[1],e)}for(let t of eJ.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH)if(t[0]===r){if(t[1]===eJ.VARIABLE_LENGTH)return eJ.processVariableAI(4,t[2],e);return eJ.processFixedAI(4,t[1],e)}if(e.length<4)throw new Z;let n=e.substring(0,4);for(let t of eJ.FOUR_DIGIT_DATA_LENGTH)if(t[0]===n){if(t[1]===eJ.VARIABLE_LENGTH)return eJ.processVariableAI(4,t[2],e);return eJ.processFixedAI(4,t[1],e)}throw new Z}static processFixedAI(e,t,r){if(r.length<e)throw new Z;let n=r.substring(0,e);if(r.length<e+t)throw new Z;let i=r.substring(e,e+t),s=r.substring(e+t),o="("+n+")"+i,a=eJ.parseFieldsInGeneralPurpose(s);return null==a?o:o+a}static processVariableAI(e,t,r){let n,i=r.substring(0,e);n=r.length<e+t?r.length:e+t;let s=r.substring(e,n),o=r.substring(n),a="("+i+")"+s,l=eJ.parseFieldsInGeneralPurpose(o);return null==l?a:a+l}}eJ.VARIABLE_LENGTH=[],eJ.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",eJ.VARIABLE_LENGTH,20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",eJ.VARIABLE_LENGTH,20],["22",eJ.VARIABLE_LENGTH,29],["30",eJ.VARIABLE_LENGTH,8],["37",eJ.VARIABLE_LENGTH,8],["90",eJ.VARIABLE_LENGTH,30],["91",eJ.VARIABLE_LENGTH,30],["92",eJ.VARIABLE_LENGTH,30],["93",eJ.VARIABLE_LENGTH,30],["94",eJ.VARIABLE_LENGTH,30],["95",eJ.VARIABLE_LENGTH,30],["96",eJ.VARIABLE_LENGTH,30],["97",eJ.VARIABLE_LENGTH,3],["98",eJ.VARIABLE_LENGTH,30],["99",eJ.VARIABLE_LENGTH,30]],eJ.THREE_DIGIT_DATA_LENGTH=[["240",eJ.VARIABLE_LENGTH,30],["241",eJ.VARIABLE_LENGTH,30],["242",eJ.VARIABLE_LENGTH,6],["250",eJ.VARIABLE_LENGTH,30],["251",eJ.VARIABLE_LENGTH,30],["253",eJ.VARIABLE_LENGTH,17],["254",eJ.VARIABLE_LENGTH,20],["400",eJ.VARIABLE_LENGTH,30],["401",eJ.VARIABLE_LENGTH,30],["402",17],["403",eJ.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",eJ.VARIABLE_LENGTH,20],["421",eJ.VARIABLE_LENGTH,15],["422",3],["423",eJ.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],eJ.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",eJ.VARIABLE_LENGTH,15],["391",eJ.VARIABLE_LENGTH,18],["392",eJ.VARIABLE_LENGTH,15],["393",eJ.VARIABLE_LENGTH,18],["703",eJ.VARIABLE_LENGTH,30]],eJ.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",eJ.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",eJ.VARIABLE_LENGTH,20],["8003",eJ.VARIABLE_LENGTH,30],["8004",eJ.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",eJ.VARIABLE_LENGTH,30],["8008",eJ.VARIABLE_LENGTH,12],["8018",18],["8020",eJ.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",eJ.VARIABLE_LENGTH,70],["8200",eJ.VARIABLE_LENGTH,70]];class e${constructor(e){this.buffer=new z,this.information=e}decodeAllCodes(e,t){let r=t,n=null;for(;;){let t=this.decodeGeneralPurposeField(r,n),i=eJ.parseFieldsInGeneralPurpose(t.getNewString());if(null!=i&&e.append(i),n=t.isRemaining()?""+t.getRemainingValue():null,r===t.getNewPosition())break;r=t.getNewPosition()}return e.toString()}isStillNumeric(e){if(e+7>this.information.getSize())return e+4<=this.information.getSize();for(let t=e;t<e+3;++t)if(this.information.get(t))return!0;return this.information.get(e+3)}decodeNumeric(e){if(e+7>this.information.getSize()){let t=this.extractNumericValueFromBitArray(e,4);return 0===t?new ej(this.information.getSize(),ej.FNC1,ej.FNC1):new ej(this.information.getSize(),t-1,ej.FNC1)}let t=this.extractNumericValueFromBitArray(e,7);return new ej(e+7,(t-8)/11,(t-8)%11)}extractNumericValueFromBitArray(e,t){return e$.extractNumericValueFromBitArray(this.information,e,t)}static extractNumericValueFromBitArray(e,t,r){let n=0;for(let i=0;i<r;++i)e.get(t+i)&&(n|=1<<r-i-1);return n}decodeGeneralPurposeField(e,t){this.buffer.setLengthToZero(),null!=t&&this.buffer.append(t),this.current.setPosition(e);let r=this.parseBlocks();return null!=r&&r.isRemaining()?new eQ(this.current.getPosition(),this.buffer.toString(),r.getRemainingValue()):new eQ(this.current.getPosition(),this.buffer.toString())}parseBlocks(){let e,t;do{let r=this.current.getPosition();if(e=this.current.isAlpha()?(t=this.parseAlphaBlock()).isFinished():this.current.isIsoIec646()?(t=this.parseIsoIec646Block()).isFinished():(t=this.parseNumericBlock()).isFinished(),r===this.current.getPosition()&&!e)break}while(!e);return t.getDecodedInformation()}parseNumericBlock(){for(;this.isStillNumeric(this.current.getPosition());){let e=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFirstDigitFNC1()){let t;return new eZ(!0,e.isSecondDigitFNC1()?new eQ(this.current.getPosition(),this.buffer.toString()):new eQ(this.current.getPosition(),this.buffer.toString(),e.getSecondDigit()))}if(this.buffer.append(e.getFirstDigit()),e.isSecondDigitFNC1())return new eZ(!0,new eQ(this.current.getPosition(),this.buffer.toString()));this.buffer.append(e.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new eZ(!1)}parseIsoIec646Block(){for(;this.isStillIsoIec646(this.current.getPosition());){let e=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1())return new eZ(!0,new eQ(this.current.getPosition(),this.buffer.toString()));this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new eZ(!1)}parseAlphaBlock(){for(;this.isStillAlpha(this.current.getPosition());){let e=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1())return new eZ(!0,new eQ(this.current.getPosition(),this.buffer.toString()));this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new eZ(!1)}isStillIsoIec646(e){if(e+5>this.information.getSize())return!1;let t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+7>this.information.getSize())return!1;let r=this.extractNumericValueFromBitArray(e,7);if(r>=64&&r<116)return!0;if(e+8>this.information.getSize())return!1;let n=this.extractNumericValueFromBitArray(e,8);return n>=232&&n<253}decodeIsoIec646(e){let t,r=this.extractNumericValueFromBitArray(e,5);if(15===r)return new eq(e+5,eq.FNC1);if(r>=5&&r<15)return new eq(e+5,"0"+(r-5));let n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<90)return new eq(e+7,""+(n+1));if(n>=90&&n<116)return new eq(e+7,""+(n+7));switch(this.extractNumericValueFromBitArray(e,8)){case 232:t="!";break;case 233:t='"';break;case 234:t="%";break;case 235:t="&";break;case 236:t="'";break;case 237:t="(";break;case 238:t=")";break;case 239:t="*";break;case 240:t="+";break;case 241:t=",";break;case 242:t="-";break;case 243:t=".";break;case 244:t="/";break;case 245:t=":";break;case 246:t=";";break;case 247:t="<";break;case 248:t="=";break;case 249:t=">";break;case 250:t="?";break;case 251:t="_";break;case 252:t=" ";break;default:throw new U}return new eq(e+8,t)}isStillAlpha(e){if(e+5>this.information.getSize())return!1;let t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+6>this.information.getSize())return!1;let r=this.extractNumericValueFromBitArray(e,6);return r>=16&&r<63}decodeAlphanumeric(e){let t,r=this.extractNumericValueFromBitArray(e,5);if(15===r)return new eq(e+5,eq.FNC1);if(r>=5&&r<15)return new eq(e+5,"0"+(r-5));let n=this.extractNumericValueFromBitArray(e,6);if(n>=32&&n<58)return new eq(e+6,""+(n+33));switch(n){case 58:t="*";break;case 59:t=",";break;case 60:t="-";break;case 61:t=".";break;case 62:t="/";break;default:throw new ed("Decoding invalid alphanumeric value: "+n)}return new eq(e+6,t)}isAlphaTo646ToAlphaLatch(e){if(e+1>this.information.getSize())return!1;for(let t=0;t<5&&t+e<this.information.getSize();++t)if(2===t){if(!this.information.get(e+2))return!1}else if(this.information.get(e+t))return!1;return!0}isAlphaOr646ToNumericLatch(e){if(e+3>this.information.getSize())return!1;for(let t=e;t<e+3;++t)if(this.information.get(t))return!1;return!0}isNumericToAlphaNumericLatch(e){if(e+1>this.information.getSize())return!1;for(let t=0;t<4&&t+e<this.information.getSize();++t)if(this.information.get(e+t))return!1;return!0}}class e1{constructor(e){this.information=e,this.generalDecoder=new e$(e)}getInformation(){return this.information}getGeneralDecoder(){return this.generalDecoder}}class e2 extends e1{constructor(e){super(e)}encodeCompressedGtin(e,t){e.append("(01)");let r=e.length();e.append("9"),this.encodeCompressedGtinWithoutAI(e,t,r)}encodeCompressedGtinWithoutAI(e,t,r){for(let r=0;r<4;++r){let n=this.getGeneralDecoder().extractNumericValueFromBitArray(t+10*r,10);n/100==0&&e.append("0"),n/10==0&&e.append("0"),e.append(n)}e2.appendCheckDigit(e,r)}static appendCheckDigit(e,t){let r=0;for(let n=0;n<13;n++){let i=e.charAt(n+t).charCodeAt(0)-48;r+=(1&n)==0?3*i:i}10==(r=10-r%10)&&(r=0),e.append(r)}}e2.GTIN_SIZE=40;class e0 extends e2{constructor(e){super(e)}parseInformation(){let e=new z;e.append("(01)");let t=e.length(),r=this.getGeneralDecoder().extractNumericValueFromBitArray(e0.HEADER_SIZE,4);return e.append(r),this.encodeCompressedGtinWithoutAI(e,e0.HEADER_SIZE+4,t),this.getGeneralDecoder().decodeAllCodes(e,e0.HEADER_SIZE+44)}}e0.HEADER_SIZE=4;class e3 extends e1{constructor(e){super(e)}parseInformation(){let e=new z;return this.getGeneralDecoder().decodeAllCodes(e,e3.HEADER_SIZE)}}e3.HEADER_SIZE=5;class e8 extends e2{constructor(e){super(e)}encodeCompressedWeight(e,t,r){let n=this.getGeneralDecoder().extractNumericValueFromBitArray(t,r);this.addWeightCode(e,n);let i=this.checkWeight(n),s=1e5;for(let t=0;t<5;++t)i/s==0&&e.append("0"),s/=10;e.append(i)}}class e6 extends e8{constructor(e){super(e)}parseInformation(){if(this.getInformation().getSize()!=e6.HEADER_SIZE+e8.GTIN_SIZE+e6.WEIGHT_SIZE)throw new Z;let e=new z;return this.encodeCompressedGtin(e,e6.HEADER_SIZE),this.encodeCompressedWeight(e,e6.HEADER_SIZE+e8.GTIN_SIZE,e6.WEIGHT_SIZE),e.toString()}}e6.HEADER_SIZE=5,e6.WEIGHT_SIZE=15;class e4 extends e6{constructor(e){super(e)}addWeightCode(e,t){e.append("(3103)")}checkWeight(e){return e}}class e7 extends e6{constructor(e){super(e)}addWeightCode(e,t){t<1e4?e.append("(3202)"):e.append("(3203)")}checkWeight(e){return e<1e4?e:e-1e4}}class e5 extends e2{constructor(e){super(e)}parseInformation(){if(this.getInformation().getSize()<e5.HEADER_SIZE+e2.GTIN_SIZE)throw new Z;let e=new z;this.encodeCompressedGtin(e,e5.HEADER_SIZE);let t=this.getGeneralDecoder().extractNumericValueFromBitArray(e5.HEADER_SIZE+e2.GTIN_SIZE,e5.LAST_DIGIT_SIZE);e.append("(392"),e.append(t),e.append(")");let r=this.getGeneralDecoder().decodeGeneralPurposeField(e5.HEADER_SIZE+e2.GTIN_SIZE+e5.LAST_DIGIT_SIZE,null);return e.append(r.getNewString()),e.toString()}}e5.HEADER_SIZE=8,e5.LAST_DIGIT_SIZE=2;class e9 extends e2{constructor(e){super(e)}parseInformation(){if(this.getInformation().getSize()<e9.HEADER_SIZE+e2.GTIN_SIZE)throw new Z;let e=new z;this.encodeCompressedGtin(e,e9.HEADER_SIZE);let t=this.getGeneralDecoder().extractNumericValueFromBitArray(e9.HEADER_SIZE+e2.GTIN_SIZE,e9.LAST_DIGIT_SIZE);e.append("(393"),e.append(t),e.append(")");let r=this.getGeneralDecoder().extractNumericValueFromBitArray(e9.HEADER_SIZE+e2.GTIN_SIZE+e9.LAST_DIGIT_SIZE,e9.FIRST_THREE_DIGITS_SIZE);r/100==0&&e.append("0"),r/10==0&&e.append("0"),e.append(r);let n=this.getGeneralDecoder().decodeGeneralPurposeField(e9.HEADER_SIZE+e2.GTIN_SIZE+e9.LAST_DIGIT_SIZE+e9.FIRST_THREE_DIGITS_SIZE,null);return e.append(n.getNewString()),e.toString()}}e9.HEADER_SIZE=8,e9.LAST_DIGIT_SIZE=2,e9.FIRST_THREE_DIGITS_SIZE=10;class te extends e8{constructor(e,t,r){super(e),this.dateCode=r,this.firstAIdigits=t}parseInformation(){if(this.getInformation().getSize()!=te.HEADER_SIZE+te.GTIN_SIZE+te.WEIGHT_SIZE+te.DATE_SIZE)throw new Z;let e=new z;return this.encodeCompressedGtin(e,te.HEADER_SIZE),this.encodeCompressedWeight(e,te.HEADER_SIZE+te.GTIN_SIZE,te.WEIGHT_SIZE),this.encodeCompressedDate(e,te.HEADER_SIZE+te.GTIN_SIZE+te.WEIGHT_SIZE),e.toString()}encodeCompressedDate(e,t){let r=this.getGeneralDecoder().extractNumericValueFromBitArray(t,te.DATE_SIZE);if(38400==r)return;e.append("("),e.append(this.dateCode),e.append(")");let n=r%32,i=(r/=32)%12+1,s=r/=12;s/10==0&&e.append("0"),e.append(s),i/10==0&&e.append("0"),e.append(i),n/10==0&&e.append("0"),e.append(n)}addWeightCode(e,t){e.append("("),e.append(this.firstAIdigits),e.append(t/1e5),e.append(")")}checkWeight(e){return e%1e5}}function tt(e){try{if(e.get(1))return new e0(e);if(!e.get(2))return new e3(e);switch(e$.extractNumericValueFromBitArray(e,1,4)){case 4:return new e4(e);case 5:return new e7(e)}switch(e$.extractNumericValueFromBitArray(e,1,5)){case 12:return new e5(e);case 13:return new e9(e)}switch(e$.extractNumericValueFromBitArray(e,1,7)){case 56:return new te(e,"310","11");case 57:return new te(e,"320","11");case 58:return new te(e,"310","13");case 59:return new te(e,"320","13");case 60:return new te(e,"310","15");case 61:return new te(e,"320","15");case 62:return new te(e,"310","17");case 63:return new te(e,"320","17")}}catch(t){throw console.log(t),new ed("unknown decoder: "+e)}}te.HEADER_SIZE=8,te.WEIGHT_SIZE=20,te.DATE_SIZE=16;class tr{constructor(e,t,r,n){this.leftchar=e,this.rightchar=t,this.finderpattern=r,this.maybeLast=n}mayBeLast(){return this.maybeLast}getLeftChar(){return this.leftchar}getRightChar(){return this.rightchar}getFinderPattern(){return this.finderpattern}mustBeLast(){return null==this.rightchar}toString(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(null==this.finderpattern?"null":this.finderpattern.getValue())+" ]"}static equals(e,t){return e instanceof tr&&tr.equalsOrNull(e.leftchar,t.leftchar)&&tr.equalsOrNull(e.rightchar,t.rightchar)&&tr.equalsOrNull(e.finderpattern,t.finderpattern)}static equalsOrNull(e,t){return null===e?null===t:tr.equals(e,t)}hashCode(){return this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue()}}class tn{constructor(e,t,r){this.pairs=e,this.rowNumber=t,this.wasReversed=r}getPairs(){return this.pairs}getRowNumber(){return this.rowNumber}isReversed(){return this.wasReversed}isEquivalent(e){return this.checkEqualitity(this,e)}toString(){return"{ "+this.pairs+" }"}equals(e,t){return e instanceof tn&&this.checkEqualitity(e,t)&&e.wasReversed===t.wasReversed}checkEqualitity(e,t){let r;if(e&&t)return e.forEach((e,n)=>{t.forEach(t=>{e.getLeftChar().getValue()===t.getLeftChar().getValue()&&e.getRightChar().getValue()===t.getRightChar().getValue()&&e.getFinderPatter().getValue()===t.getFinderPatter().getValue()&&(r=!0)})}),r}}class ti extends eG{constructor(e){super(...arguments),this.pairs=Array(ti.MAX_PAIRS),this.rows=[],this.startEnd=[2],this.verbose=!0===e}decodeRow(e,t,r){this.pairs.length=0,this.startFromEven=!1;try{return ti.constructResult(this.decodeRow2pairs(e,t))}catch(e){this.verbose&&console.log(e)}return this.pairs.length=0,this.startFromEven=!0,ti.constructResult(this.decodeRow2pairs(e,t))}reset(){this.pairs.length=0,this.rows.length=0}decodeRow2pairs(e,t){let r,n=!1;for(;!n;)try{this.pairs.push(this.retrieveNextPair(t,this.pairs,e))}catch(e){if(e instanceof Z){if(!this.pairs.length)throw new Z;n=!0}}if(this.checkChecksum())return this.pairs;if(r=!!this.rows.length,this.storeRow(e,!1),r){let e=this.checkRowsBoolean(!1);if(null!=e||null!=(e=this.checkRowsBoolean(!0)))return e}throw new Z}checkRowsBoolean(e){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,e&&(this.rows=this.rows.reverse());let t=null;try{t=this.checkRows([],0)}catch(e){this.verbose&&console.log(e)}return e&&(this.rows=this.rows.reverse()),t}checkRows(e,t){for(let r=t;r<this.rows.length;r++){let t=this.rows[r];for(let t of(this.pairs.length=0,e))this.pairs.push(t.getPairs());if(this.pairs.push(t.getPairs()),!ti.isValidSequence(this.pairs))continue;if(this.checkChecksum())return this.pairs;let n=Array(e);n.push(t);try{return this.checkRows(n,r+1)}catch(e){this.verbose&&console.log(e)}}throw new Z}static isValidSequence(e){for(let t of ti.FINDER_PATTERN_SEQUENCES){if(e.length>t.length)continue;let r=!0;for(let n=0;n<e.length;n++)if(e[n].getFinderPattern().getValue()!=t[n]){r=!1;break}if(r)return!0}return!1}storeRow(e,t){let r=0,n=!1,i=!1;for(;r<this.rows.length;){let t=this.rows[r];if(t.getRowNumber()>e){i=t.isEquivalent(this.pairs);break}n=t.isEquivalent(this.pairs),r++}i||n||ti.isPartialRow(this.pairs,this.rows)||(this.rows.push(r,new tn(this.pairs,e,t)),this.removePartialRows(this.pairs,this.rows))}removePartialRows(e,t){for(let r of t)if(r.getPairs().length!==e.length){for(let t of r.getPairs())for(let r of e)if(tr.equals(t,r))break}}static isPartialRow(e,t){for(let r of t){let t=!0;for(let n of e){let e=!1;for(let t of r.getPairs())if(n.equals(t)){e=!0;break}if(!e){t=!1;break}}if(t)return!0}return!1}getRows(){return this.rows}static constructResult(e){let t=tt(eY.buildBitArray(e)).parseInformation(),r=e[0].getFinderPattern().getResultPoints(),n=e[e.length-1].getFinderPattern().getResultPoints();return new er(t,null,null,[r[0],r[1],n[0],n[1]],en.RSS_EXPANDED,null)}checkChecksum(){let e=this.pairs.get(0),t=e.getLeftChar(),r=e.getRightChar();if(null==r)return!1;let n=r.getChecksumPortion(),i=2;for(let e=1;e<this.pairs.size();++e){let t=this.pairs.get(e);n+=t.getLeftChar().getChecksumPortion(),i++;let r=t.getRightChar();null!=r&&(n+=r.getChecksumPortion(),i++)}return 211*(i-4)+(n%=211)==t.getValue()}static getNextSecondBar(e,t){let r;return e.get(t)?(r=e.getNextUnset(t),r=e.getNextSet(r)):(r=e.getNextSet(t),r=e.getNextUnset(r)),r}retrieveNextPair(e,t,r){let n,i,s=t.length%2==0;this.startFromEven&&(s=!s);let o=!0,a=-1;do this.findNextPair(e,t,a),null==(n=this.parseFoundFinderPattern(e,r,s))?a=ti.getNextSecondBar(e,this.startEnd[0]):o=!1;while(o);let l=this.decodeDataCharacter(e,n,s,!0);if(!this.isEmptyPair(t)&&t[t.length-1].mustBeLast())throw new Z;try{i=this.decodeDataCharacter(e,n,s,!1)}catch(e){i=null,this.verbose&&console.log(e)}return new tr(l,i,n,!0)}isEmptyPair(e){return 0===e.length}findNextPair(e,t,r){let n,i=this.getDecodeFinderCounters();i[0]=0,i[1]=0,i[2]=0,i[3]=0;let s=e.getSize();n=r>=0?r:this.isEmptyPair(t)?0:t[t.length-1].getFinderPattern().getStartEnd()[1];let o=t.length%2!=0;this.startFromEven&&(o=!o);let a=!1;for(;n<s&&(a=!e.get(n));)n++;let l=0,h=n;for(let t=n;t<s;t++)if(e.get(t)!=a)i[l]++;else{if(3==l){if(o&&ti.reverseCounters(i),ti.isFinderPattern(i)){this.startEnd[0]=h,this.startEnd[1]=t;return}o&&ti.reverseCounters(i),h+=i[0]+i[1],i[0]=i[2],i[1]=i[3],i[2]=0,i[3]=0,l--}else l++;i[l]=1,a=!a}throw new Z}static reverseCounters(e){let t=e.length;for(let r=0;r<t/2;++r){let n=e[r];e[r]=e[t-r-1],e[t-r-1]=n}}parseFoundFinderPattern(e,t,r){let n,i,s,o;if(r){let t=this.startEnd[0]-1;for(;t>=0&&!e.get(t);)t--;t++,n=this.startEnd[0]-t,i=t,s=this.startEnd[1]}else i=this.startEnd[0],n=(s=e.getNextUnset(this.startEnd[1]+1))-this.startEnd[1];let a=this.getDecodeFinderCounters();P.arraycopy(a,0,a,1,a.length-1),a[0]=n;try{o=this.parseFinderValue(a,ti.FINDER_PATTERNS)}catch(e){return null}return new eW(o,[i,s],i,s,t)}decodeDataCharacter(e,t,r,n){let i=this.getDataCharacterCounters();for(let e=0;e<i.length;e++)i[e]=0;if(n)ti.recordPatternInReverse(e,t.getStartEnd()[0],i);else{ti.recordPattern(e,t.getStartEnd()[1],i);for(let e=0,t=i.length-1;e<t;e++,t--){let r=i[e];i[e]=i[t],i[t]=r}}let s=ef.sum(new Int32Array(i))/17,o=(t.getStartEnd()[1]-t.getStartEnd()[0])/15;if(Math.abs(s-o)/o>.3)throw new Z;let a=this.getOddCounts(),l=this.getEvenCounts(),h=this.getOddRoundingErrors(),u=this.getEvenRoundingErrors();for(let e=0;e<i.length;e++){let t=i[e]/s,r=t+.5;if(r<1){if(t<.3)throw new Z;r=1}else if(r>8){if(t>8.7)throw new Z;r=8}let n=e/2;(1&e)==0?(a[n]=r,h[n]=t-r):(l[n]=r,u[n]=t-r)}this.adjustOddEvenCounts(17);let d=4*t.getValue()+2*!r+ +!n-1,c=0,g=0;for(let e=a.length-1;e>=0;e--){if(ti.isNotA1left(t,r,n)){let t=ti.WEIGHTS[d][2*e];g+=a[e]*t}c+=a[e]}let f=0;for(let e=l.length-1;e>=0;e--)if(ti.isNotA1left(t,r,n)){let t=ti.WEIGHTS[d][2*e+1];f+=l[e]*t}let w=g+f;if((1&c)!=0||c>13||c<4)throw new Z;let A=(13-c)/2,C=ti.SYMBOL_WIDEST[A],E=ez.getRSSvalue(a,C,!0),m=ez.getRSSvalue(l,9-C,!1);return new eX(E*ti.EVEN_TOTAL_SUBSET[A]+m+ti.GSUM[A],w)}static isNotA1left(e,t,r){return!(0==e.getValue()&&t&&r)}adjustOddEvenCounts(e){let t=ef.sum(new Int32Array(this.getOddCounts())),r=ef.sum(new Int32Array(this.getEvenCounts())),n=!1,i=!1;t>13?i=!0:t<4&&(n=!0);let s=!1,o=!1;r>13?o=!0:r<4&&(s=!0);let a=t+r-e,l=(1&t)==1,h=(1&r)==0;if(1==a)if(l){if(h)throw new Z;i=!0}else{if(!h)throw new Z;o=!0}else if(-1==a)if(l){if(h)throw new Z;n=!0}else{if(!h)throw new Z;s=!0}else if(0==a){if(l){if(!h)throw new Z;t<r?(n=!0,o=!0):(i=!0,s=!0)}else if(h)throw new Z}else throw new Z;if(n){if(i)throw new Z;ti.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(i&&ti.decrement(this.getOddCounts(),this.getOddRoundingErrors()),s){if(o)throw new Z;ti.increment(this.getEvenCounts(),this.getOddRoundingErrors())}o&&ti.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())}}ti.SYMBOL_WIDEST=[7,5,4,3,1],ti.EVEN_TOTAL_SUBSET=[4,20,52,104,204],ti.GSUM=[0,348,1388,2948,3988],ti.FINDER_PATTERNS=[Int32Array.from([1,8,4,1]),Int32Array.from([3,6,4,1]),Int32Array.from([3,4,6,1]),Int32Array.from([3,2,8,1]),Int32Array.from([2,6,5,1]),Int32Array.from([2,2,9,1])],ti.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],ti.FINDER_PAT_A=0,ti.FINDER_PAT_B=1,ti.FINDER_PAT_C=2,ti.FINDER_PAT_D=3,ti.FINDER_PAT_E=4,ti.FINDER_PAT_F=5,ti.FINDER_PATTERN_SEQUENCES=[[ti.FINDER_PAT_A,ti.FINDER_PAT_A],[ti.FINDER_PAT_A,ti.FINDER_PAT_B,ti.FINDER_PAT_B],[ti.FINDER_PAT_A,ti.FINDER_PAT_C,ti.FINDER_PAT_B,ti.FINDER_PAT_D],[ti.FINDER_PAT_A,ti.FINDER_PAT_E,ti.FINDER_PAT_B,ti.FINDER_PAT_D,ti.FINDER_PAT_C],[ti.FINDER_PAT_A,ti.FINDER_PAT_E,ti.FINDER_PAT_B,ti.FINDER_PAT_D,ti.FINDER_PAT_D,ti.FINDER_PAT_F],[ti.FINDER_PAT_A,ti.FINDER_PAT_E,ti.FINDER_PAT_B,ti.FINDER_PAT_D,ti.FINDER_PAT_E,ti.FINDER_PAT_F,ti.FINDER_PAT_F],[ti.FINDER_PAT_A,ti.FINDER_PAT_A,ti.FINDER_PAT_B,ti.FINDER_PAT_B,ti.FINDER_PAT_C,ti.FINDER_PAT_C,ti.FINDER_PAT_D,ti.FINDER_PAT_D],[ti.FINDER_PAT_A,ti.FINDER_PAT_A,ti.FINDER_PAT_B,ti.FINDER_PAT_B,ti.FINDER_PAT_C,ti.FINDER_PAT_C,ti.FINDER_PAT_D,ti.FINDER_PAT_E,ti.FINDER_PAT_E],[ti.FINDER_PAT_A,ti.FINDER_PAT_A,ti.FINDER_PAT_B,ti.FINDER_PAT_B,ti.FINDER_PAT_C,ti.FINDER_PAT_C,ti.FINDER_PAT_D,ti.FINDER_PAT_E,ti.FINDER_PAT_F,ti.FINDER_PAT_F],[ti.FINDER_PAT_A,ti.FINDER_PAT_A,ti.FINDER_PAT_B,ti.FINDER_PAT_B,ti.FINDER_PAT_C,ti.FINDER_PAT_D,ti.FINDER_PAT_D,ti.FINDER_PAT_E,ti.FINDER_PAT_E,ti.FINDER_PAT_F,ti.FINDER_PAT_F]],ti.MAX_PAIRS=11;class ts extends eX{constructor(e,t,r){super(e,t),this.count=0,this.finderPattern=r}getFinderPattern(){return this.finderPattern}getCount(){return this.count}incrementCount(){this.count++}}class to extends eG{constructor(){super(...arguments),this.possibleLeftPairs=[],this.possibleRightPairs=[]}decodeRow(e,t,r){let n=this.decodePair(t,!1,e,r);to.addOrTally(this.possibleLeftPairs,n),t.reverse();let i=this.decodePair(t,!0,e,r);for(let e of(to.addOrTally(this.possibleRightPairs,i),t.reverse(),this.possibleLeftPairs))if(e.getCount()>1){for(let t of this.possibleRightPairs)if(t.getCount()>1&&to.checkChecksum(e,t))return to.constructResult(e,t)}throw new Z}static addOrTally(e,t){if(null==t)return;let r=!1;for(let n of e)if(n.getValue()===t.getValue()){n.incrementCount(),r=!0;break}r||e.push(t)}reset(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0}static constructResult(e,t){let r=new String(4537077*e.getValue()+t.getValue()).toString(),n=new z;for(let e=13-r.length;e>0;e--)n.append("0");n.append(r);let i=0;for(let e=0;e<13;e++){let t=n.charAt(e).charCodeAt(0)-48;i+=(1&e)==0?3*t:t}10==(i=10-i%10)&&(i=0),n.append(i.toString());let s=e.getFinderPattern().getResultPoints(),o=t.getFinderPattern().getResultPoints();return new er(n.toString(),null,0,[s[0],s[1],o[0],o[1]],en.RSS_14,new Date().getTime())}static checkChecksum(e,t){let r=(e.getChecksumPortion()+16*t.getChecksumPortion())%79,n=9*e.getFinderPattern().getValue()+t.getFinderPattern().getValue();return n>72&&n--,n>8&&n--,r===n}decodePair(e,t,r,n){try{let i=this.findFinderPattern(e,t),s=this.parseFoundFinderPattern(e,r,t,i),o=null==n?null:n.get(V.NEED_RESULT_POINT_CALLBACK);if(null!=o){let n=(i[0]+i[1])/2;t&&(n=e.getSize()-1-n),o.foundPossibleResultPoint(new eA(n,r))}let a=this.decodeDataCharacter(e,s,!0),l=this.decodeDataCharacter(e,s,!1);return new ts(1597*a.getValue()+l.getValue(),a.getChecksumPortion()+4*l.getChecksumPortion(),s)}catch(e){return null}}decodeDataCharacter(e,t,r){let n=this.getDataCharacterCounters();for(let e=0;e<n.length;e++)n[e]=0;if(r)ey.recordPatternInReverse(e,t.getStartEnd()[0],n);else{ey.recordPattern(e,t.getStartEnd()[1]+1,n);for(let e=0,t=n.length-1;e<t;e++,t--){let r=n[e];n[e]=n[t],n[t]=r}}let i=r?16:15,s=ef.sum(new Int32Array(n))/i,o=this.getOddCounts(),a=this.getEvenCounts(),l=this.getOddRoundingErrors(),h=this.getEvenRoundingErrors();for(let e=0;e<n.length;e++){let t=n[e]/s,r=Math.floor(t+.5);r<1?r=1:r>8&&(r=8);let i=Math.floor(e/2);(1&e)==0?(o[i]=r,l[i]=t-r):(a[i]=r,h[i]=t-r)}this.adjustOddEvenCounts(r,i);let u=0,d=0;for(let e=o.length-1;e>=0;e--)d*=9,d+=o[e],u+=o[e];let c=0,g=0;for(let e=a.length-1;e>=0;e--)c*=9,c+=a[e],g+=a[e];let f=d+3*c;if(r){if((1&u)!=0||u>12||u<4)throw new Z;let e=(12-u)/2,t=to.OUTSIDE_ODD_WIDEST[e],r=ez.getRSSvalue(o,t,!1),n=ez.getRSSvalue(a,9-t,!0);return new eX(r*to.OUTSIDE_EVEN_TOTAL_SUBSET[e]+n+to.OUTSIDE_GSUM[e],f)}{if((1&g)!=0||g>10||g<4)throw new Z;let e=(10-g)/2,t=to.INSIDE_ODD_WIDEST[e],r=ez.getRSSvalue(o,t,!0);return new eX(ez.getRSSvalue(a,9-t,!1)*to.INSIDE_ODD_TOTAL_SUBSET[e]+r+to.INSIDE_GSUM[e],f)}}findFinderPattern(e,t){let r=this.getDecodeFinderCounters();r[0]=0,r[1]=0,r[2]=0,r[3]=0;let n=e.getSize(),i=!1,s=0;for(;s<n&&t!==(i=!e.get(s));)s++;let o=0,a=s;for(let t=s;t<n;t++)if(e.get(t)!==i)r[o]++;else{if(3===o){if(eG.isFinderPattern(r))return[a,t];a+=r[0]+r[1],r[0]=r[2],r[1]=r[3],r[2]=0,r[3]=0,o--}else o++;r[o]=1,i=!i}throw new Z}parseFoundFinderPattern(e,t,r,n){let i=e.get(n[0]),s=n[0]-1;for(;s>=0&&i!==e.get(s);)s--;s++;let o=n[0]-s,a=this.getDecodeFinderCounters(),l=new Int32Array(a.length);P.arraycopy(a,0,l,1,a.length-1),l[0]=o;let h=this.parseFinderValue(l,to.FINDER_PATTERNS),u=s,d=n[1];return r&&(u=e.getSize()-1-u,d=e.getSize()-1-d),new eW(h,[s,n[1]],u,d,t)}adjustOddEvenCounts(e,t){let r=ef.sum(new Int32Array(this.getOddCounts())),n=ef.sum(new Int32Array(this.getEvenCounts())),i=!1,s=!1,o=!1,a=!1;e?(r>12?s=!0:r<4&&(i=!0),n>12?a=!0:n<4&&(o=!0)):(r>11?s=!0:r<5&&(i=!0),n>10?a=!0:n<4&&(o=!0));let l=r+n-t,h=(1&r)==+!!e,u=(1&n)==1;if(1===l)if(h){if(u)throw new Z;s=!0}else{if(!u)throw new Z;a=!0}else if(-1===l)if(h){if(u)throw new Z;i=!0}else{if(!u)throw new Z;o=!0}else if(0===l){if(h){if(!u)throw new Z;r<n?(i=!0,a=!0):(s=!0,o=!0)}else if(u)throw new Z}else throw new Z;if(i){if(s)throw new Z;eG.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(s&&eG.decrement(this.getOddCounts(),this.getOddRoundingErrors()),o){if(a)throw new Z;eG.increment(this.getEvenCounts(),this.getOddRoundingErrors())}a&&eG.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())}}to.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],to.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],to.OUTSIDE_GSUM=[0,161,961,2015,2715],to.INSIDE_GSUM=[0,336,1036,1516],to.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],to.INSIDE_ODD_WIDEST=[2,4,6,8],to.FINDER_PATTERNS=[Int32Array.from([3,8,2,1]),Int32Array.from([3,5,5,1]),Int32Array.from([3,3,7,1]),Int32Array.from([3,1,9,1]),Int32Array.from([2,7,4,1]),Int32Array.from([2,5,6,1]),Int32Array.from([2,3,8,1]),Int32Array.from([1,5,7,1]),Int32Array.from([1,3,9,1])];class ta extends ey{constructor(e,t){super(),this.readers=[],this.verbose=!0===t;let r=e?e.get(V.POSSIBLE_FORMATS):null,n=e&&void 0!==e.get(V.ASSUME_CODE_39_CHECK_DIGIT);r?((r.includes(en.EAN_13)||r.includes(en.UPC_A)||r.includes(en.EAN_8)||r.includes(en.UPC_E))&&this.readers.push(new eH(e)),r.includes(en.CODE_39)&&this.readers.push(new eM(n)),r.includes(en.CODE_128)&&this.readers.push(new eO),r.includes(en.ITF)&&this.readers.push(new eB),r.includes(en.RSS_14)&&this.readers.push(new to),r.includes(en.RSS_EXPANDED)&&this.readers.push(new ti(this.verbose))):(this.readers.push(new eH(e)),this.readers.push(new eM),this.readers.push(new eH(e)),this.readers.push(new eO),this.readers.push(new eB),this.readers.push(new to),this.readers.push(new ti(this.verbose)))}decodeRow(e,t,r){for(let n=0;n<this.readers.length;n++)try{return this.readers[n].decodeRow(e,t,r)}catch(e){}throw new Z}reset(){this.readers.forEach(e=>e.reset())}}class tl extends et{constructor(e=500,t){super(new ta(t),e,t)}}class th{constructor(e,t,r){this.ecCodewords=e,this.ecBlocks=[t],r&&this.ecBlocks.push(r)}getECCodewords(){return this.ecCodewords}getECBlocks(){return this.ecBlocks}}class tu{constructor(e,t){this.count=e,this.dataCodewords=t}getCount(){return this.count}getDataCodewords(){return this.dataCodewords}}class td{constructor(e,t,r,n,i,s){this.versionNumber=e,this.symbolSizeRows=t,this.symbolSizeColumns=r,this.dataRegionSizeRows=n,this.dataRegionSizeColumns=i,this.ecBlocks=s;let o=0,a=s.getECCodewords();for(let e of s.getECBlocks())o+=e.getCount()*(e.getDataCodewords()+a);this.totalCodewords=o}getVersionNumber(){return this.versionNumber}getSymbolSizeRows(){return this.symbolSizeRows}getSymbolSizeColumns(){return this.symbolSizeColumns}getDataRegionSizeRows(){return this.dataRegionSizeRows}getDataRegionSizeColumns(){return this.dataRegionSizeColumns}getTotalCodewords(){return this.totalCodewords}getECBlocks(){return this.ecBlocks}static getVersionForDimensions(e,t){if((1&e)!=0||(1&t)!=0)throw new U;for(let r of td.VERSIONS)if(r.symbolSizeRows===e&&r.symbolSizeColumns===t)return r;throw new U}toString(){return""+this.versionNumber}static buildVersions(){return[new td(1,10,10,8,8,new th(5,new tu(1,3))),new td(2,12,12,10,10,new th(7,new tu(1,5))),new td(3,14,14,12,12,new th(10,new tu(1,8))),new td(4,16,16,14,14,new th(12,new tu(1,12))),new td(5,18,18,16,16,new th(14,new tu(1,18))),new td(6,20,20,18,18,new th(18,new tu(1,22))),new td(7,22,22,20,20,new th(20,new tu(1,30))),new td(8,24,24,22,22,new th(24,new tu(1,36))),new td(9,26,26,24,24,new th(28,new tu(1,44))),new td(10,32,32,14,14,new th(36,new tu(1,62))),new td(11,36,36,16,16,new th(42,new tu(1,86))),new td(12,40,40,18,18,new th(48,new tu(1,114))),new td(13,44,44,20,20,new th(56,new tu(1,144))),new td(14,48,48,22,22,new th(68,new tu(1,174))),new td(15,52,52,24,24,new th(42,new tu(2,102))),new td(16,64,64,14,14,new th(56,new tu(2,140))),new td(17,72,72,16,16,new th(36,new tu(4,92))),new td(18,80,80,18,18,new th(48,new tu(4,114))),new td(19,88,88,20,20,new th(56,new tu(4,144))),new td(20,96,96,22,22,new th(68,new tu(4,174))),new td(21,104,104,24,24,new th(56,new tu(6,136))),new td(22,120,120,18,18,new th(68,new tu(6,175))),new td(23,132,132,20,20,new th(62,new tu(8,163))),new td(24,144,144,22,22,new th(62,new tu(8,156),new tu(2,155))),new td(25,8,18,6,16,new th(7,new tu(1,5))),new td(26,8,32,6,14,new th(11,new tu(1,10))),new td(27,12,26,10,24,new th(14,new tu(1,16))),new td(28,12,36,10,16,new th(18,new tu(1,22))),new td(29,16,36,14,16,new th(24,new tu(1,32))),new td(30,16,48,14,22,new th(28,new tu(1,49)))]}}td.VERSIONS=td.buildVersions();class tc{constructor(e){let t=e.getHeight();if(t<8||t>144||(1&t)!=0)throw new U;this.version=tc.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new Y(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}getVersion(){return this.version}static readVersion(e){let t=e.getHeight(),r=e.getWidth();return td.getVersionForDimensions(t,r)}readCodewords(){let e=new Int8Array(this.version.getTotalCodewords()),t=0,r=4,n=0,i=this.mappingBitMatrix.getHeight(),s=this.mappingBitMatrix.getWidth(),o=!1,a=!1,l=!1,h=!1;do if(r!==i||0!==n||o)if(r!==i-2||0!==n||(3&s)==0||a)if(r!==i+4||2!==n||(7&s)!=0||l)if(r!==i-2||0!==n||(7&s)!=4||h){do r<i&&n>=0&&!this.readMappingMatrix.get(n,r)&&(e[t++]=255&this.readUtah(r,n,i,s)),r-=2,n+=2;while(r>=0&&n<s);r+=1,n+=3;do r>=0&&n<s&&!this.readMappingMatrix.get(n,r)&&(e[t++]=255&this.readUtah(r,n,i,s)),r+=2,n-=2;while(r<i&&n>=0);r+=3,n+=1}else e[t++]=255&this.readCorner4(i,s),r-=2,n+=2,h=!0;else e[t++]=255&this.readCorner3(i,s),r-=2,n+=2,l=!0;else e[t++]=255&this.readCorner2(i,s),r-=2,n+=2,a=!0;else e[t++]=255&this.readCorner1(i,s),r-=2,n+=2,o=!0;while(r<i||n<s);if(t!==this.version.getTotalCodewords())throw new U;return e}readModule(e,t,r,n){return e<0&&(e+=r,t+=4-(r+4&7)),t<0&&(t+=n,e+=4-(n+4&7)),this.readMappingMatrix.set(t,e),this.mappingBitMatrix.get(t,e)}readUtah(e,t,r,n){let i=0;return this.readModule(e-2,t-2,r,n)&&(i|=1),i<<=1,this.readModule(e-2,t-1,r,n)&&(i|=1),i<<=1,this.readModule(e-1,t-2,r,n)&&(i|=1),i<<=1,this.readModule(e-1,t-1,r,n)&&(i|=1),i<<=1,this.readModule(e-1,t,r,n)&&(i|=1),i<<=1,this.readModule(e,t-2,r,n)&&(i|=1),i<<=1,this.readModule(e,t-1,r,n)&&(i|=1),i<<=1,this.readModule(e,t,r,n)&&(i|=1),i}readCorner1(e,t){let r=0;return this.readModule(e-1,0,e,t)&&(r|=1),r<<=1,this.readModule(e-1,1,e,t)&&(r|=1),r<<=1,this.readModule(e-1,2,e,t)&&(r|=1),r<<=1,this.readModule(0,t-2,e,t)&&(r|=1),r<<=1,this.readModule(0,t-1,e,t)&&(r|=1),r<<=1,this.readModule(1,t-1,e,t)&&(r|=1),r<<=1,this.readModule(2,t-1,e,t)&&(r|=1),r<<=1,this.readModule(3,t-1,e,t)&&(r|=1),r}readCorner2(e,t){let r=0;return this.readModule(e-3,0,e,t)&&(r|=1),r<<=1,this.readModule(e-2,0,e,t)&&(r|=1),r<<=1,this.readModule(e-1,0,e,t)&&(r|=1),r<<=1,this.readModule(0,t-4,e,t)&&(r|=1),r<<=1,this.readModule(0,t-3,e,t)&&(r|=1),r<<=1,this.readModule(0,t-2,e,t)&&(r|=1),r<<=1,this.readModule(0,t-1,e,t)&&(r|=1),r<<=1,this.readModule(1,t-1,e,t)&&(r|=1),r}readCorner3(e,t){let r=0;return this.readModule(e-1,0,e,t)&&(r|=1),r<<=1,this.readModule(e-1,t-1,e,t)&&(r|=1),r<<=1,this.readModule(0,t-3,e,t)&&(r|=1),r<<=1,this.readModule(0,t-2,e,t)&&(r|=1),r<<=1,this.readModule(0,t-1,e,t)&&(r|=1),r<<=1,this.readModule(1,t-3,e,t)&&(r|=1),r<<=1,this.readModule(1,t-2,e,t)&&(r|=1),r<<=1,this.readModule(1,t-1,e,t)&&(r|=1),r}readCorner4(e,t){let r=0;return this.readModule(e-3,0,e,t)&&(r|=1),r<<=1,this.readModule(e-2,0,e,t)&&(r|=1),r<<=1,this.readModule(e-1,0,e,t)&&(r|=1),r<<=1,this.readModule(0,t-2,e,t)&&(r|=1),r<<=1,this.readModule(0,t-1,e,t)&&(r|=1),r<<=1,this.readModule(1,t-1,e,t)&&(r|=1),r<<=1,this.readModule(2,t-1,e,t)&&(r|=1),r<<=1,this.readModule(3,t-1,e,t)&&(r|=1),r}extractDataRegion(e){let t=this.version.getSymbolSizeRows(),r=this.version.getSymbolSizeColumns();if(e.getHeight()!==t)throw new O("Dimension of bitMatrix must match the version size");let n=this.version.getDataRegionSizeRows(),i=this.version.getDataRegionSizeColumns(),s=t/n|0,o=r/i|0,a=new Y(o*i,s*n);for(let t=0;t<s;++t){let r=t*n;for(let s=0;s<o;++s){let o=s*i;for(let l=0;l<n;++l){let h=t*(n+2)+1+l,u=r+l;for(let t=0;t<i;++t){let r=s*(i+2)+1+t;if(e.get(r,h)){let e=o+t;a.set(e,u)}}}}}return a}}class tg{constructor(e,t){this.numDataCodewords=e,this.codewords=t}static getDataBlocks(e,t){let r=t.getECBlocks(),n=0,i=r.getECBlocks();for(let e of i)n+=e.getCount();let s=Array(n),o=0;for(let e of i)for(let t=0;t<e.getCount();t++){let t=e.getDataCodewords(),n=r.getECCodewords()+t;s[o++]=new tg(t,new Uint8Array(n))}let a=s[0].codewords.length-r.getECCodewords(),l=a-1,h=0;for(let t=0;t<l;t++)for(let r=0;r<o;r++)s[r].codewords[t]=e[h++];let u=24===t.getVersionNumber(),d=u?8:o;for(let t=0;t<d;t++)s[t].codewords[a-1]=e[h++];let c=s[0].codewords.length;for(let t=a;t<c;t++)for(let r=0;r<o;r++){let n=u?(r+8)%o:r,i=u&&n>7?t-1:t;s[n].codewords[i]=e[h++]}if(h!==e.length)throw new O;return s}getNumDataCodewords(){return this.numDataCodewords}getCodewords(){return this.codewords}}class tf{constructor(e){this.bytes=e,this.byteOffset=0,this.bitOffset=0}getBitOffset(){return this.bitOffset}getByteOffset(){return this.byteOffset}readBits(e){if(e<1||e>32||e>this.available())throw new O(""+e);let t=0,r=this.bitOffset,n=this.byteOffset,i=this.bytes;if(r>0){let s=8-r,o=e<s?e:s,a=s-o;t=(i[n]&255>>8-o<<a)>>a,e-=o,8===(r+=o)&&(r=0,n++)}if(e>0){for(;e>=8;)t=t<<8|255&i[n],n++,e-=8;if(e>0){let s=8-e;t=t<<e|(i[n]&255>>s<<s)>>s,r+=e}}return this.bitOffset=r,this.byteOffset=n,t}available(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset}}(l=m||(m={}))[l.PAD_ENCODE=0]="PAD_ENCODE",l[l.ASCII_ENCODE=1]="ASCII_ENCODE",l[l.C40_ENCODE=2]="C40_ENCODE",l[l.TEXT_ENCODE=3]="TEXT_ENCODE",l[l.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",l[l.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",l[l.BASE256_ENCODE=6]="BASE256_ENCODE";class tw{static decode(e){let t=new tf(e),r=new z,n=new z,i=[],s=m.ASCII_ENCODE;do if(s===m.ASCII_ENCODE)s=this.decodeAsciiSegment(t,r,n);else{switch(s){case m.C40_ENCODE:this.decodeC40Segment(t,r);break;case m.TEXT_ENCODE:this.decodeTextSegment(t,r);break;case m.ANSIX12_ENCODE:this.decodeAnsiX12Segment(t,r);break;case m.EDIFACT_ENCODE:this.decodeEdifactSegment(t,r);break;case m.BASE256_ENCODE:this.decodeBase256Segment(t,r,i);break;default:throw new U}s=m.ASCII_ENCODE}while(s!==m.PAD_ENCODE&&t.available()>0);return n.length()>0&&r.append(n.toString()),new es(e,r.toString(),0===i.length?null:i,null)}static decodeAsciiSegment(e,t,r){let n=!1;do{let i=e.readBits(8);if(0===i)throw new U;if(i<=128){n&&(i+=128),t.append(String.fromCharCode(i-1));break}if(129===i)return m.PAD_ENCODE;else if(i<=229){let e=i-130;e<10&&t.append("0"),t.append(""+e)}else switch(i){case 230:return m.C40_ENCODE;case 231:return m.BASE256_ENCODE;case 232:t.append("\x1d");break;case 233:case 234:case 241:break;case 235:n=!0;break;case 236:t.append("[)>\x1e05\x1d"),r.insert(0,"\x1e\x04");break;case 237:t.append("[)>\x1e06\x1d"),r.insert(0,"\x1e\x04");break;case 238:return m.ANSIX12_ENCODE;case 239:return m.TEXT_ENCODE;case 240:return m.EDIFACT_ENCODE;default:if(254!==i||0!==e.available())throw new U}}while(e.available()>0);return m.ASCII_ENCODE}static decodeC40Segment(e,t){let r=!1,n=[],i=0;do{if(8===e.available())return;let s=e.readBits(8);if(254===s)return;this.parseTwoBytes(s,e.readBits(8),n);for(let e=0;e<3;e++){let s=n[e];switch(i){case 0:if(s<3)i=s+1;else if(s<this.C40_BASIC_SET_CHARS.length){let e=this.C40_BASIC_SET_CHARS[s];r?(t.append(String.fromCharCode(e.charCodeAt(0)+128)),r=!1):t.append(e)}else throw new U;break;case 1:r?(t.append(String.fromCharCode(s+128)),r=!1):t.append(String.fromCharCode(s)),i=0;break;case 2:if(s<this.C40_SHIFT2_SET_CHARS.length){let e=this.C40_SHIFT2_SET_CHARS[s];r?(t.append(String.fromCharCode(e.charCodeAt(0)+128)),r=!1):t.append(e)}else switch(s){case 27:t.append("\x1d");break;case 30:r=!0;break;default:throw new U}i=0;break;case 3:r?(t.append(String.fromCharCode(s+224)),r=!1):t.append(String.fromCharCode(s+96)),i=0;break;default:throw new U}}}while(e.available()>0)}static decodeTextSegment(e,t){let r=!1,n=[],i=0;do{if(8===e.available())return;let s=e.readBits(8);if(254===s)return;this.parseTwoBytes(s,e.readBits(8),n);for(let e=0;e<3;e++){let s=n[e];switch(i){case 0:if(s<3)i=s+1;else if(s<this.TEXT_BASIC_SET_CHARS.length){let e=this.TEXT_BASIC_SET_CHARS[s];r?(t.append(String.fromCharCode(e.charCodeAt(0)+128)),r=!1):t.append(e)}else throw new U;break;case 1:r?(t.append(String.fromCharCode(s+128)),r=!1):t.append(String.fromCharCode(s)),i=0;break;case 2:if(s<this.TEXT_SHIFT2_SET_CHARS.length){let e=this.TEXT_SHIFT2_SET_CHARS[s];r?(t.append(String.fromCharCode(e.charCodeAt(0)+128)),r=!1):t.append(e)}else switch(s){case 27:t.append("\x1d");break;case 30:r=!0;break;default:throw new U}i=0;break;case 3:if(s<this.TEXT_SHIFT3_SET_CHARS.length){let e=this.TEXT_SHIFT3_SET_CHARS[s];r?(t.append(String.fromCharCode(e.charCodeAt(0)+128)),r=!1):t.append(e),i=0}else throw new U;break;default:throw new U}}}while(e.available()>0)}static decodeAnsiX12Segment(e,t){let r=[];do{if(8===e.available())return;let n=e.readBits(8);if(254===n)return;this.parseTwoBytes(n,e.readBits(8),r);for(let e=0;e<3;e++){let n=r[e];switch(n){case 0:t.append("\r");break;case 1:t.append("*");break;case 2:t.append(">");break;case 3:t.append(" ");break;default:if(n<14)t.append(String.fromCharCode(n+44));else if(n<40)t.append(String.fromCharCode(n+51));else throw new U}}}while(e.available()>0)}static parseTwoBytes(e,t,r){let n=(e<<8)+t-1,i=Math.floor(n/1600);r[0]=i,n-=1600*i,i=Math.floor(n/40),r[1]=i,r[2]=n-40*i}static decodeEdifactSegment(e,t){do{if(16>=e.available())return;for(let r=0;r<4;r++){let r=e.readBits(6);if(31===r){let t=8-e.getBitOffset();8!==t&&e.readBits(t);return}(32&r)==0&&(r|=64),t.append(String.fromCharCode(r))}}while(e.available()>0)}static decodeBase256Segment(e,t,r){let n,i=1+e.getByteOffset(),s=this.unrandomize255State(e.readBits(8),i++);if((n=0===s?e.available()/8|0:s<250?s:250*(s-249)+this.unrandomize255State(e.readBits(8),i++))<0)throw new U;let o=new Uint8Array(n);for(let t=0;t<n;t++){if(8>e.available())throw new U;o[t]=this.unrandomize255State(e.readBits(8),i++)}r.push(o);try{t.append(X.decode(o,W.ISO88591))}catch(e){throw new ed("Platform does not support required encoding: "+e.message)}}static unrandomize255State(e,t){let r=e-(149*t%255+1);return r>=0?r:r+256}}tw.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],tw.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],tw.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],tw.TEXT_SHIFT2_SET_CHARS=tw.C40_SHIFT2_SET_CHARS,tw.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",""];class tA{constructor(){this.rsDecoder=new ec(eh.DATA_MATRIX_FIELD_256)}decode(e){let t=new tc(e),r=t.getVersion(),n=t.readCodewords(),i=tg.getDataBlocks(n,r),s=0;for(let e of i)s+=e.getNumDataCodewords();let o=new Uint8Array(s),a=i.length;for(let e=0;e<a;e++){let t=i[e],r=t.getCodewords(),n=t.getNumDataCodewords();this.correctErrors(r,n);for(let t=0;t<n;t++)o[t*a+e]=r[t]}return tw.decode(o)}correctErrors(e,t){let r=new Int32Array(e);try{this.rsDecoder.decode(r,e.length-t)}catch(e){throw new B}for(let n=0;n<t;n++)e[n]=r[n]}}class tC{constructor(e){this.image=e,this.rectangleDetector=new em(this.image)}detect(){let e=this.rectangleDetector.detect(),t=this.detectSolid1(e);if((t=this.detectSolid2(t))[3]=this.correctTopRight(t),!t[3])throw new Z;let r=(t=this.shiftToModuleCenter(t))[0],n=t[1],i=t[2],s=t[3],o=this.transitionsBetween(r,s)+1,a=this.transitionsBetween(i,s)+1;return(1&o)==1&&(o+=1),(1&a)==1&&(a+=1),4*o<7*a&&4*a<7*o&&(o=a=Math.max(o,a)),new eC(tC.sampleGrid(this.image,r,n,i,s,o,a),[r,n,i,s])}static shiftPoint(e,t,r){let n=(t.getX()-e.getX())/(r+1),i=(t.getY()-e.getY())/(r+1);return new eA(e.getX()+n,e.getY()+i)}static moveAway(e,t,r){let n=e.getX(),i=e.getY();return n<t?n-=1:n+=1,i<r?i-=1:i+=1,new eA(n,i)}detectSolid1(e){let t=e[0],r=e[1],n=e[3],i=e[2],s=this.transitionsBetween(t,r),o=this.transitionsBetween(r,n),a=this.transitionsBetween(n,i),l=this.transitionsBetween(i,t),h=s,u=[i,t,r,n];return h>o&&(h=o,u[0]=t,u[1]=r,u[2]=n,u[3]=i),h>a&&(h=a,u[0]=r,u[1]=n,u[2]=i,u[3]=t),h>l&&(u[0]=n,u[1]=i,u[2]=t,u[3]=r),u}detectSolid2(e){let t=e[0],r=e[1],n=e[2],i=e[3],s=this.transitionsBetween(t,i),o=tC.shiftPoint(r,n,(s+1)*4),a=tC.shiftPoint(n,r,(s+1)*4);return this.transitionsBetween(o,t)<this.transitionsBetween(a,i)?(e[0]=t,e[1]=r,e[2]=n,e[3]=i):(e[0]=r,e[1]=n,e[2]=i,e[3]=t),e}correctTopRight(e){let t=e[0],r=e[1],n=e[2],i=e[3],s=this.transitionsBetween(t,i),o=this.transitionsBetween(r,i),a=tC.shiftPoint(t,r,(o+1)*4),l=tC.shiftPoint(n,r,(s+1)*4);s=this.transitionsBetween(a,i),o=this.transitionsBetween(l,i);let h=new eA(i.getX()+(n.getX()-r.getX())/(s+1),i.getY()+(n.getY()-r.getY())/(s+1)),u=new eA(i.getX()+(t.getX()-r.getX())/(o+1),i.getY()+(t.getY()-r.getY())/(o+1));return this.isValid(h)?this.isValid(u)?this.transitionsBetween(a,h)+this.transitionsBetween(l,h)>this.transitionsBetween(a,u)+this.transitionsBetween(l,u)?h:u:h:this.isValid(u)?u:null}shiftToModuleCenter(e){let t,r,n=e[0],i=e[1],s=e[2],o=e[3],a=this.transitionsBetween(n,o)+1,l=this.transitionsBetween(s,o)+1,h=tC.shiftPoint(n,i,4*l),u=tC.shiftPoint(s,i,4*a);a=this.transitionsBetween(h,o)+1,l=this.transitionsBetween(u,o)+1,(1&a)==1&&(a+=1),(1&l)==1&&(l+=1);let d=(n.getX()+i.getX()+s.getX()+o.getX())/4,c=(n.getY()+i.getY()+s.getY()+o.getY())/4;return n=tC.moveAway(n,d,c),i=tC.moveAway(i,d,c),s=tC.moveAway(s,d,c),o=tC.moveAway(o,d,c),h=tC.shiftPoint(n,i,4*l),h=tC.shiftPoint(h,o,4*a),t=tC.shiftPoint(i,n,4*l),t=tC.shiftPoint(t,s,4*a),u=tC.shiftPoint(s,o,4*l),u=tC.shiftPoint(u,i,4*a),r=tC.shiftPoint(o,s,4*l),[h,t,u,r=tC.shiftPoint(r,n,4*a)]}isValid(e){return e.getX()>=0&&e.getX()<this.image.getWidth()&&e.getY()>0&&e.getY()<this.image.getHeight()}static sampleGrid(e,t,r,n,i,s,o){return ep.getInstance().sampleGrid(e,s,o,.5,.5,s-.5,.5,s-.5,o-.5,.5,o-.5,t.getX(),t.getY(),i.getX(),i.getY(),n.getX(),n.getY(),r.getX(),r.getY())}transitionsBetween(e,t){let r=Math.trunc(e.getX()),n=Math.trunc(e.getY()),i=Math.trunc(t.getX()),s=Math.trunc(t.getY()),o=Math.abs(s-n)>Math.abs(i-r);if(o){let e=r;r=n,n=e,e=i,i=s,s=e}let a=Math.abs(i-r),l=Math.abs(s-n),h=-a/2,u=n<s?1:-1,d=r<i?1:-1,c=0,g=this.image.get(o?n:r,o?r:n);for(let e=r,t=n;e!==i;e+=d){let r=this.image.get(o?t:e,o?e:t);if(r!==g&&(c++,g=r),(h+=l)>0){if(t===s)break;t+=u,h-=a}}return c}}class tE{constructor(){this.decoder=new tA}decode(e,t=null){let r,n;if(null!=t&&t.has(V.PURE_BARCODE)){let t=tE.extractPureBits(e.getBlackMatrix());r=this.decoder.decode(t),n=tE.NO_POINTS}else{let t=new tC(e.getBlackMatrix()).detect();r=this.decoder.decode(t.getBits()),n=t.getPoints()}let i=r.getRawBytes(),s=new er(r.getText(),i,8*i.length,n,en.DATA_MATRIX,P.currentTimeMillis()),o=r.getByteSegments();null!=o&&s.putMetadata(ei.BYTE_SEGMENTS,o);let a=r.getECLevel();return null!=a&&s.putMetadata(ei.ERROR_CORRECTION_LEVEL,a),s}reset(){}static extractPureBits(e){let t=e.getTopLeftOnBit(),r=e.getBottomRightOnBit();if(null==t||null==r)throw new Z;let n=this.moduleSize(t,e),i=t[1],s=r[1],o=t[0],a=(r[0]-o+1)/n,l=(s-i+1)/n;if(a<=0||l<=0)throw new Z;let h=n/2;i+=h,o+=h;let u=new Y(a,l);for(let t=0;t<l;t++){let r=i+t*n;for(let i=0;i<a;i++)e.get(o+i*n,r)&&u.set(i,t)}return u}static moduleSize(e,t){let r=t.getWidth(),n=e[0],i=e[1];for(;n<r&&t.get(n,i);)n++;if(n===r)throw new Z;let s=n-e[0];if(0===s)throw new Z;return s}}tE.NO_POINTS=[];class tm extends et{constructor(e=500){super(new tE,e)}}(h=_||(_={}))[h.L=0]="L",h[h.M=1]="M",h[h.Q=2]="Q",h[h.H=3]="H";class t_{constructor(e,t,r){this.value=e,this.stringValue=t,this.bits=r,t_.FOR_BITS.set(r,this),t_.FOR_VALUE.set(e,this)}getValue(){return this.value}getBits(){return this.bits}static fromString(e){switch(e){case"L":return t_.L;case"M":return t_.M;case"Q":return t_.Q;case"H":return t_.H;default:throw new y(e+"not available")}}toString(){return this.stringValue}equals(e){return e instanceof t_&&this.value===e.value}static forBits(e){if(e<0||e>=t_.FOR_BITS.size)throw new O;return t_.FOR_BITS.get(e)}}t_.FOR_BITS=new Map,t_.FOR_VALUE=new Map,t_.L=new t_(_.L,"L",1),t_.M=new t_(_.M,"M",0),t_.Q=new t_(_.Q,"Q",3),t_.H=new t_(_.H,"H",2);class tI{constructor(e){this.errorCorrectionLevel=t_.forBits(e>>3&3),this.dataMask=7&e}static numBitsDiffering(e,t){return k.bitCount(e^t)}static decodeFormatInformation(e,t){let r=tI.doDecodeFormatInformation(e,t);return null!==r?r:tI.doDecodeFormatInformation(e^tI.FORMAT_INFO_MASK_QR,t^tI.FORMAT_INFO_MASK_QR)}static doDecodeFormatInformation(e,t){let r=Number.MAX_SAFE_INTEGER,n=0;for(let i of tI.FORMAT_INFO_DECODE_LOOKUP){let s=i[0];if(s===e||s===t)return new tI(i[1]);let o=tI.numBitsDiffering(e,s);o<r&&(n=i[1],r=o),e!==t&&(o=tI.numBitsDiffering(t,s))<r&&(n=i[1],r=o)}return r<=3?new tI(n):null}getErrorCorrectionLevel(){return this.errorCorrectionLevel}getDataMask(){return this.dataMask}hashCode(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask}equals(e){return e instanceof tI&&this.errorCorrectionLevel===e.errorCorrectionLevel&&this.dataMask===e.dataMask}}tI.FORMAT_INFO_MASK_QR=21522,tI.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])];class tS{constructor(e,...t){this.ecCodewordsPerBlock=e,this.ecBlocks=t}getECCodewordsPerBlock(){return this.ecCodewordsPerBlock}getNumBlocks(){let e=0;for(let t of this.ecBlocks)e+=t.getCount();return e}getTotalECCodewords(){return this.ecCodewordsPerBlock*this.getNumBlocks()}getECBlocks(){return this.ecBlocks}}class tp{constructor(e,t){this.count=e,this.dataCodewords=t}getCount(){return this.count}getDataCodewords(){return this.dataCodewords}}class tT{constructor(e,t,...r){this.versionNumber=e,this.alignmentPatternCenters=t,this.ecBlocks=r;let n=0,i=r[0].getECCodewordsPerBlock();for(let e of r[0].getECBlocks())n+=e.getCount()*(e.getDataCodewords()+i);this.totalCodewords=n}getVersionNumber(){return this.versionNumber}getAlignmentPatternCenters(){return this.alignmentPatternCenters}getTotalCodewords(){return this.totalCodewords}getDimensionForVersion(){return 17+4*this.versionNumber}getECBlocksForLevel(e){return this.ecBlocks[e.getValue()]}static getProvisionalVersionForDimension(e){if(e%4!=1)throw new U;try{return this.getVersionForNumber((e-17)/4)}catch(e){throw new U}}static getVersionForNumber(e){if(e<1||e>40)throw new O;return tT.VERSIONS[e-1]}static decodeVersionInformation(e){let t=Number.MAX_SAFE_INTEGER,r=0;for(let n=0;n<tT.VERSION_DECODE_INFO.length;n++){let i=tT.VERSION_DECODE_INFO[n];if(i===e)return tT.getVersionForNumber(n+7);let s=tI.numBitsDiffering(e,i);s<t&&(r=n+7,t=s)}return t<=3?tT.getVersionForNumber(r):null}buildFunctionPattern(){let e=this.getDimensionForVersion(),t=new Y(e);t.setRegion(0,0,9,9),t.setRegion(e-8,0,8,9),t.setRegion(0,e-8,9,8);let r=this.alignmentPatternCenters.length;for(let e=0;e<r;e++){let n=this.alignmentPatternCenters[e]-2;for(let i=0;i<r;i++)(0!==e||0!==i&&i!==r-1)&&(e!==r-1||0!==i)&&t.setRegion(this.alignmentPatternCenters[i]-2,n,5,5)}return t.setRegion(6,9,1,e-17),t.setRegion(9,6,e-17,1),this.versionNumber>6&&(t.setRegion(e-11,0,3,6),t.setRegion(0,e-11,6,3)),t}toString(){return""+this.versionNumber}}tT.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),tT.VERSIONS=[new tT(1,new Int32Array(0),new tS(7,new tp(1,19)),new tS(10,new tp(1,16)),new tS(13,new tp(1,13)),new tS(17,new tp(1,9))),new tT(2,Int32Array.from([6,18]),new tS(10,new tp(1,34)),new tS(16,new tp(1,28)),new tS(22,new tp(1,22)),new tS(28,new tp(1,16))),new tT(3,Int32Array.from([6,22]),new tS(15,new tp(1,55)),new tS(26,new tp(1,44)),new tS(18,new tp(2,17)),new tS(22,new tp(2,13))),new tT(4,Int32Array.from([6,26]),new tS(20,new tp(1,80)),new tS(18,new tp(2,32)),new tS(26,new tp(2,24)),new tS(16,new tp(4,9))),new tT(5,Int32Array.from([6,30]),new tS(26,new tp(1,108)),new tS(24,new tp(2,43)),new tS(18,new tp(2,15),new tp(2,16)),new tS(22,new tp(2,11),new tp(2,12))),new tT(6,Int32Array.from([6,34]),new tS(18,new tp(2,68)),new tS(16,new tp(4,27)),new tS(24,new tp(4,19)),new tS(28,new tp(4,15))),new tT(7,Int32Array.from([6,22,38]),new tS(20,new tp(2,78)),new tS(18,new tp(4,31)),new tS(18,new tp(2,14),new tp(4,15)),new tS(26,new tp(4,13),new tp(1,14))),new tT(8,Int32Array.from([6,24,42]),new tS(24,new tp(2,97)),new tS(22,new tp(2,38),new tp(2,39)),new tS(22,new tp(4,18),new tp(2,19)),new tS(26,new tp(4,14),new tp(2,15))),new tT(9,Int32Array.from([6,26,46]),new tS(30,new tp(2,116)),new tS(22,new tp(3,36),new tp(2,37)),new tS(20,new tp(4,16),new tp(4,17)),new tS(24,new tp(4,12),new tp(4,13))),new tT(10,Int32Array.from([6,28,50]),new tS(18,new tp(2,68),new tp(2,69)),new tS(26,new tp(4,43),new tp(1,44)),new tS(24,new tp(6,19),new tp(2,20)),new tS(28,new tp(6,15),new tp(2,16))),new tT(11,Int32Array.from([6,30,54]),new tS(20,new tp(4,81)),new tS(30,new tp(1,50),new tp(4,51)),new tS(28,new tp(4,22),new tp(4,23)),new tS(24,new tp(3,12),new tp(8,13))),new tT(12,Int32Array.from([6,32,58]),new tS(24,new tp(2,92),new tp(2,93)),new tS(22,new tp(6,36),new tp(2,37)),new tS(26,new tp(4,20),new tp(6,21)),new tS(28,new tp(7,14),new tp(4,15))),new tT(13,Int32Array.from([6,34,62]),new tS(26,new tp(4,107)),new tS(22,new tp(8,37),new tp(1,38)),new tS(24,new tp(8,20),new tp(4,21)),new tS(22,new tp(12,11),new tp(4,12))),new tT(14,Int32Array.from([6,26,46,66]),new tS(30,new tp(3,115),new tp(1,116)),new tS(24,new tp(4,40),new tp(5,41)),new tS(20,new tp(11,16),new tp(5,17)),new tS(24,new tp(11,12),new tp(5,13))),new tT(15,Int32Array.from([6,26,48,70]),new tS(22,new tp(5,87),new tp(1,88)),new tS(24,new tp(5,41),new tp(5,42)),new tS(30,new tp(5,24),new tp(7,25)),new tS(24,new tp(11,12),new tp(7,13))),new tT(16,Int32Array.from([6,26,50,74]),new tS(24,new tp(5,98),new tp(1,99)),new tS(28,new tp(7,45),new tp(3,46)),new tS(24,new tp(15,19),new tp(2,20)),new tS(30,new tp(3,15),new tp(13,16))),new tT(17,Int32Array.from([6,30,54,78]),new tS(28,new tp(1,107),new tp(5,108)),new tS(28,new tp(10,46),new tp(1,47)),new tS(28,new tp(1,22),new tp(15,23)),new tS(28,new tp(2,14),new tp(17,15))),new tT(18,Int32Array.from([6,30,56,82]),new tS(30,new tp(5,120),new tp(1,121)),new tS(26,new tp(9,43),new tp(4,44)),new tS(28,new tp(17,22),new tp(1,23)),new tS(28,new tp(2,14),new tp(19,15))),new tT(19,Int32Array.from([6,30,58,86]),new tS(28,new tp(3,113),new tp(4,114)),new tS(26,new tp(3,44),new tp(11,45)),new tS(26,new tp(17,21),new tp(4,22)),new tS(26,new tp(9,13),new tp(16,14))),new tT(20,Int32Array.from([6,34,62,90]),new tS(28,new tp(3,107),new tp(5,108)),new tS(26,new tp(3,41),new tp(13,42)),new tS(30,new tp(15,24),new tp(5,25)),new tS(28,new tp(15,15),new tp(10,16))),new tT(21,Int32Array.from([6,28,50,72,94]),new tS(28,new tp(4,116),new tp(4,117)),new tS(26,new tp(17,42)),new tS(28,new tp(17,22),new tp(6,23)),new tS(30,new tp(19,16),new tp(6,17))),new tT(22,Int32Array.from([6,26,50,74,98]),new tS(28,new tp(2,111),new tp(7,112)),new tS(28,new tp(17,46)),new tS(30,new tp(7,24),new tp(16,25)),new tS(24,new tp(34,13))),new tT(23,Int32Array.from([6,30,54,78,102]),new tS(30,new tp(4,121),new tp(5,122)),new tS(28,new tp(4,47),new tp(14,48)),new tS(30,new tp(11,24),new tp(14,25)),new tS(30,new tp(16,15),new tp(14,16))),new tT(24,Int32Array.from([6,28,54,80,106]),new tS(30,new tp(6,117),new tp(4,118)),new tS(28,new tp(6,45),new tp(14,46)),new tS(30,new tp(11,24),new tp(16,25)),new tS(30,new tp(30,16),new tp(2,17))),new tT(25,Int32Array.from([6,32,58,84,110]),new tS(26,new tp(8,106),new tp(4,107)),new tS(28,new tp(8,47),new tp(13,48)),new tS(30,new tp(7,24),new tp(22,25)),new tS(30,new tp(22,15),new tp(13,16))),new tT(26,Int32Array.from([6,30,58,86,114]),new tS(28,new tp(10,114),new tp(2,115)),new tS(28,new tp(19,46),new tp(4,47)),new tS(28,new tp(28,22),new tp(6,23)),new tS(30,new tp(33,16),new tp(4,17))),new tT(27,Int32Array.from([6,34,62,90,118]),new tS(30,new tp(8,122),new tp(4,123)),new tS(28,new tp(22,45),new tp(3,46)),new tS(30,new tp(8,23),new tp(26,24)),new tS(30,new tp(12,15),new tp(28,16))),new tT(28,Int32Array.from([6,26,50,74,98,122]),new tS(30,new tp(3,117),new tp(10,118)),new tS(28,new tp(3,45),new tp(23,46)),new tS(30,new tp(4,24),new tp(31,25)),new tS(30,new tp(11,15),new tp(31,16))),new tT(29,Int32Array.from([6,30,54,78,102,126]),new tS(30,new tp(7,116),new tp(7,117)),new tS(28,new tp(21,45),new tp(7,46)),new tS(30,new tp(1,23),new tp(37,24)),new tS(30,new tp(19,15),new tp(26,16))),new tT(30,Int32Array.from([6,26,52,78,104,130]),new tS(30,new tp(5,115),new tp(10,116)),new tS(28,new tp(19,47),new tp(10,48)),new tS(30,new tp(15,24),new tp(25,25)),new tS(30,new tp(23,15),new tp(25,16))),new tT(31,Int32Array.from([6,30,56,82,108,134]),new tS(30,new tp(13,115),new tp(3,116)),new tS(28,new tp(2,46),new tp(29,47)),new tS(30,new tp(42,24),new tp(1,25)),new tS(30,new tp(23,15),new tp(28,16))),new tT(32,Int32Array.from([6,34,60,86,112,138]),new tS(30,new tp(17,115)),new tS(28,new tp(10,46),new tp(23,47)),new tS(30,new tp(10,24),new tp(35,25)),new tS(30,new tp(19,15),new tp(35,16))),new tT(33,Int32Array.from([6,30,58,86,114,142]),new tS(30,new tp(17,115),new tp(1,116)),new tS(28,new tp(14,46),new tp(21,47)),new tS(30,new tp(29,24),new tp(19,25)),new tS(30,new tp(11,15),new tp(46,16))),new tT(34,Int32Array.from([6,34,62,90,118,146]),new tS(30,new tp(13,115),new tp(6,116)),new tS(28,new tp(14,46),new tp(23,47)),new tS(30,new tp(44,24),new tp(7,25)),new tS(30,new tp(59,16),new tp(1,17))),new tT(35,Int32Array.from([6,30,54,78,102,126,150]),new tS(30,new tp(12,121),new tp(7,122)),new tS(28,new tp(12,47),new tp(26,48)),new tS(30,new tp(39,24),new tp(14,25)),new tS(30,new tp(22,15),new tp(41,16))),new tT(36,Int32Array.from([6,24,50,76,102,128,154]),new tS(30,new tp(6,121),new tp(14,122)),new tS(28,new tp(6,47),new tp(34,48)),new tS(30,new tp(46,24),new tp(10,25)),new tS(30,new tp(2,15),new tp(64,16))),new tT(37,Int32Array.from([6,28,54,80,106,132,158]),new tS(30,new tp(17,122),new tp(4,123)),new tS(28,new tp(29,46),new tp(14,47)),new tS(30,new tp(49,24),new tp(10,25)),new tS(30,new tp(24,15),new tp(46,16))),new tT(38,Int32Array.from([6,32,58,84,110,136,162]),new tS(30,new tp(4,122),new tp(18,123)),new tS(28,new tp(13,46),new tp(32,47)),new tS(30,new tp(48,24),new tp(14,25)),new tS(30,new tp(42,15),new tp(32,16))),new tT(39,Int32Array.from([6,26,54,82,110,138,166]),new tS(30,new tp(20,117),new tp(4,118)),new tS(28,new tp(40,47),new tp(7,48)),new tS(30,new tp(43,24),new tp(22,25)),new tS(30,new tp(10,15),new tp(67,16))),new tT(40,Int32Array.from([6,30,58,86,114,142,170]),new tS(30,new tp(19,118),new tp(6,119)),new tS(28,new tp(18,47),new tp(31,48)),new tS(30,new tp(34,24),new tp(34,25)),new tS(30,new tp(20,15),new tp(61,16)))],(u=I||(I={}))[u.DATA_MASK_000=0]="DATA_MASK_000",u[u.DATA_MASK_001=1]="DATA_MASK_001",u[u.DATA_MASK_010=2]="DATA_MASK_010",u[u.DATA_MASK_011=3]="DATA_MASK_011",u[u.DATA_MASK_100=4]="DATA_MASK_100",u[u.DATA_MASK_101=5]="DATA_MASK_101",u[u.DATA_MASK_110=6]="DATA_MASK_110",u[u.DATA_MASK_111=7]="DATA_MASK_111";class tR{constructor(e,t){this.value=e,this.isMasked=t}unmaskBitMatrix(e,t){for(let r=0;r<t;r++)for(let n=0;n<t;n++)this.isMasked(r,n)&&e.flip(n,r)}}tR.values=new Map([[I.DATA_MASK_000,new tR(I.DATA_MASK_000,(e,t)=>(e+t&1)==0)],[I.DATA_MASK_001,new tR(I.DATA_MASK_001,(e,t)=>(1&e)==0)],[I.DATA_MASK_010,new tR(I.DATA_MASK_010,(e,t)=>t%3==0)],[I.DATA_MASK_011,new tR(I.DATA_MASK_011,(e,t)=>(e+t)%3==0)],[I.DATA_MASK_100,new tR(I.DATA_MASK_100,(e,t)=>(Math.floor(e/2)+Math.floor(t/3)&1)==0)],[I.DATA_MASK_101,new tR(I.DATA_MASK_101,(e,t)=>e*t%6==0)],[I.DATA_MASK_110,new tR(I.DATA_MASK_110,(e,t)=>e*t%6<3)],[I.DATA_MASK_111,new tR(I.DATA_MASK_111,(e,t)=>(e+t+e*t%3&1)==0)]]);class tN{constructor(e){let t=e.getHeight();if(t<21||(3&t)!=1)throw new U;this.bitMatrix=e}readFormatInformation(){if(null!==this.parsedFormatInfo&&void 0!==this.parsedFormatInfo)return this.parsedFormatInfo;let e=0;for(let t=0;t<6;t++)e=this.copyBit(t,8,e);e=this.copyBit(7,8,e),e=this.copyBit(8,8,e),e=this.copyBit(8,7,e);for(let t=5;t>=0;t--)e=this.copyBit(8,t,e);let t=this.bitMatrix.getHeight(),r=0,n=t-7;for(let e=t-1;e>=n;e--)r=this.copyBit(8,e,r);for(let e=t-8;e<t;e++)r=this.copyBit(e,8,r);if(this.parsedFormatInfo=tI.decodeFormatInformation(e,r),null!==this.parsedFormatInfo)return this.parsedFormatInfo;throw new U}readVersion(){if(null!==this.parsedVersion&&void 0!==this.parsedVersion)return this.parsedVersion;let e=this.bitMatrix.getHeight(),t=Math.floor((e-17)/4);if(t<=6)return tT.getVersionForNumber(t);let r=0,n=e-11;for(let t=5;t>=0;t--)for(let i=e-9;i>=n;i--)r=this.copyBit(i,t,r);let i=tT.decodeVersionInformation(r);if(null!==i&&i.getDimensionForVersion()===e)return this.parsedVersion=i,i;r=0;for(let t=5;t>=0;t--)for(let i=e-9;i>=n;i--)r=this.copyBit(t,i,r);if(null!==(i=tT.decodeVersionInformation(r))&&i.getDimensionForVersion()===e)return this.parsedVersion=i,i;throw new U}copyBit(e,t,r){return(this.isMirror?this.bitMatrix.get(t,e):this.bitMatrix.get(e,t))?r<<1|1:r<<1}readCodewords(){let e=this.readFormatInformation(),t=this.readVersion(),r=tR.values.get(e.getDataMask()),n=this.bitMatrix.getHeight();r.unmaskBitMatrix(this.bitMatrix,n);let i=t.buildFunctionPattern(),s=!0,o=new Uint8Array(t.getTotalCodewords()),a=0,l=0,h=0;for(let e=n-1;e>0;e-=2){6===e&&e--;for(let t=0;t<n;t++){let r=s?n-1-t:t;for(let t=0;t<2;t++)i.get(e-t,r)||(h++,l<<=1,this.bitMatrix.get(e-t,r)&&(l|=1),8===h&&(o[a++]=l,h=0,l=0))}s=!s}if(a!==t.getTotalCodewords())throw new U;return o}remask(){if(null===this.parsedFormatInfo)return;let e=tR.values[this.parsedFormatInfo.getDataMask()],t=this.bitMatrix.getHeight();e.unmaskBitMatrix(this.bitMatrix,t)}setMirror(e){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=e}mirror(){let e=this.bitMatrix;for(let t=0,r=e.getWidth();t<r;t++)for(let r=t+1,n=e.getHeight();r<n;r++)e.get(t,r)!==e.get(r,t)&&(e.flip(r,t),e.flip(t,r))}}class tD{constructor(e,t){this.numDataCodewords=e,this.codewords=t}static getDataBlocks(e,t,r){if(e.length!==t.getTotalCodewords())throw new O;let n=t.getECBlocksForLevel(r),i=0,s=n.getECBlocks();for(let e of s)i+=e.getCount();let o=Array(i),a=0;for(let e of s)for(let t=0;t<e.getCount();t++){let t=e.getDataCodewords(),r=n.getECCodewordsPerBlock()+t;o[a++]=new tD(t,new Uint8Array(r))}let l=o[0].codewords.length,h=o.length-1;for(;h>=0&&o[h].codewords.length!==l;)h--;h++;let u=l-n.getECCodewordsPerBlock(),d=0;for(let t=0;t<u;t++)for(let r=0;r<a;r++)o[r].codewords[t]=e[d++];for(let t=h;t<a;t++)o[t].codewords[u]=e[d++];let c=o[0].codewords.length;for(let t=u;t<c;t++)for(let r=0;r<a;r++){let n=r<h?t:t+1;o[r].codewords[n]=e[d++]}return o}getNumDataCodewords(){return this.numDataCodewords}getCodewords(){return this.codewords}}(d=S||(S={}))[d.TERMINATOR=0]="TERMINATOR",d[d.NUMERIC=1]="NUMERIC",d[d.ALPHANUMERIC=2]="ALPHANUMERIC",d[d.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",d[d.BYTE=4]="BYTE",d[d.ECI=5]="ECI",d[d.KANJI=6]="KANJI",d[d.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",d[d.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",d[d.HANZI=9]="HANZI";class ty{constructor(e,t,r,n){this.value=e,this.stringValue=t,this.characterCountBitsForVersions=r,this.bits=n,ty.FOR_BITS.set(n,this),ty.FOR_VALUE.set(e,this)}static forBits(e){let t=ty.FOR_BITS.get(e);if(void 0===t)throw new O;return t}getCharacterCountBits(e){let t,r=e.getVersionNumber();return t=r<=9?0:r<=26?1:2,this.characterCountBitsForVersions[t]}getValue(){return this.value}getBits(){return this.bits}equals(e){return e instanceof ty&&this.value===e.value}toString(){return this.stringValue}}ty.FOR_BITS=new Map,ty.FOR_VALUE=new Map,ty.TERMINATOR=new ty(S.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),ty.NUMERIC=new ty(S.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),ty.ALPHANUMERIC=new ty(S.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),ty.STRUCTURED_APPEND=new ty(S.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),ty.BYTE=new ty(S.BYTE,"BYTE",Int32Array.from([8,16,16]),4),ty.ECI=new ty(S.ECI,"ECI",Int32Array.from([0,0,0]),7),ty.KANJI=new ty(S.KANJI,"KANJI",Int32Array.from([8,10,12]),8),ty.FNC1_FIRST_POSITION=new ty(S.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),ty.FNC1_SECOND_POSITION=new ty(S.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),ty.HANZI=new ty(S.HANZI,"HANZI",Int32Array.from([8,10,12]),13);class tO{static decode(e,t,r,n){let i=new tf(e),s=new z,o=[],a=-1,l=-1;try{let e,r=null,h=!1;do{if(4>i.available())e=ty.TERMINATOR;else{let t=i.readBits(4);e=ty.forBits(t)}switch(e){case ty.TERMINATOR:break;case ty.FNC1_FIRST_POSITION:case ty.FNC1_SECOND_POSITION:h=!0;break;case ty.STRUCTURED_APPEND:if(16>i.available())throw new U;a=i.readBits(8),l=i.readBits(8);break;case ty.ECI:let u=tO.parseECIValue(i);if(r=H.getCharacterSetECIByValue(u),null===r)throw new U;break;case ty.HANZI:let d=i.readBits(4),c=i.readBits(e.getCharacterCountBits(t));d===tO.GB2312_SUBSET&&tO.decodeHanziSegment(i,s,c);break;default:let g=i.readBits(e.getCharacterCountBits(t));switch(e){case ty.NUMERIC:tO.decodeNumericSegment(i,s,g);break;case ty.ALPHANUMERIC:tO.decodeAlphanumericSegment(i,s,g,h);break;case ty.BYTE:tO.decodeByteSegment(i,s,g,r,o,n);break;case ty.KANJI:tO.decodeKanjiSegment(i,s,g);break;default:throw new U}}}while(e!==ty.TERMINATOR)}catch(e){throw new U}return new es(e,s.toString(),0===o.length?null:o,null===r?null:r.toString(),a,l)}static decodeHanziSegment(e,t,r){if(13*r>e.available())throw new U;let n=new Uint8Array(2*r),i=0;for(;r>0;){let t=e.readBits(13),s=t/96<<8|t%96;s<959?s+=41377:s+=42657,n[i]=s>>8&255,n[i+1]=255&s,i+=2,r--}try{t.append(X.decode(n,W.GB2312))}catch(e){throw new U(e)}}static decodeKanjiSegment(e,t,r){if(13*r>e.available())throw new U;let n=new Uint8Array(2*r),i=0;for(;r>0;){let t=e.readBits(13),s=t/192<<8|t%192;s<7936?s+=33088:s+=49472,n[i]=s>>8,n[i+1]=s,i+=2,r--}try{t.append(X.decode(n,W.SHIFT_JIS))}catch(e){throw new U(e)}}static decodeByteSegment(e,t,r,n,i,s){let o;if(8*r>e.available())throw new U;let a=new Uint8Array(r);for(let t=0;t<r;t++)a[t]=e.readBits(8);o=null===n?W.guessEncoding(a,s):n.getName();try{t.append(X.decode(a,o))}catch(e){throw new U(e)}i.push(a)}static toAlphaNumericChar(e){if(e>=tO.ALPHANUMERIC_CHARS.length)throw new U;return tO.ALPHANUMERIC_CHARS[e]}static decodeAlphanumericSegment(e,t,r,n){let i=t.length();for(;r>1;){if(11>e.available())throw new U;let n=e.readBits(11);t.append(tO.toAlphaNumericChar(Math.floor(n/45))),t.append(tO.toAlphaNumericChar(n%45)),r-=2}if(1===r){if(6>e.available())throw new U;t.append(tO.toAlphaNumericChar(e.readBits(6)))}if(n)for(let e=i;e<t.length();e++)"%"===t.charAt(e)&&(e<t.length()-1&&"%"===t.charAt(e+1)?t.deleteCharAt(e+1):t.setCharAt(e,"\x1d"))}static decodeNumericSegment(e,t,r){for(;r>=3;){if(10>e.available())throw new U;let n=e.readBits(10);if(n>=1e3)throw new U;t.append(tO.toAlphaNumericChar(Math.floor(n/100))),t.append(tO.toAlphaNumericChar(Math.floor(n/10)%10)),t.append(tO.toAlphaNumericChar(n%10)),r-=3}if(2===r){if(7>e.available())throw new U;let r=e.readBits(7);if(r>=100)throw new U;t.append(tO.toAlphaNumericChar(Math.floor(r/10))),t.append(tO.toAlphaNumericChar(r%10))}else if(1===r){if(4>e.available())throw new U;let r=e.readBits(4);if(r>=10)throw new U;t.append(tO.toAlphaNumericChar(r))}}static parseECIValue(e){let t=e.readBits(8);if((128&t)==0)return 127&t;if((192&t)==128)return(63&t)<<8|e.readBits(8);if((224&t)==192)return(31&t)<<16|e.readBits(16);throw new U}}tO.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",tO.GB2312_SUBSET=1;class tM{constructor(e){this.mirrored=e}isMirrored(){return this.mirrored}applyMirroredCorrection(e){if(!this.mirrored||null===e||e.length<3)return;let t=e[0];e[0]=e[2],e[2]=t}}class tB{constructor(){this.rsDecoder=new ec(eh.QR_CODE_FIELD_256)}decodeBooleanArray(e,t){return this.decodeBitMatrix(Y.parseFromBooleanArray(e),t)}decodeBitMatrix(e,t){let r=new tN(e),n=null;try{return this.decodeBitMatrixParser(r,t)}catch(e){n=e}try{r.remask(),r.setMirror(!0),r.readVersion(),r.readFormatInformation(),r.mirror();let e=this.decodeBitMatrixParser(r,t);return e.setOther(new tM(!0)),e}catch(e){if(null!==n)throw n;throw e}}decodeBitMatrixParser(e,t){let r=e.readVersion(),n=e.readFormatInformation().getErrorCorrectionLevel(),i=e.readCodewords(),s=tD.getDataBlocks(i,r,n),o=0;for(let e of s)o+=e.getNumDataCodewords();let a=new Uint8Array(o),l=0;for(let e of s){let t=e.getCodewords(),r=e.getNumDataCodewords();this.correctErrors(t,r);for(let e=0;e<r;e++)a[l++]=t[e]}return tO.decode(a,r,n,t)}correctErrors(e,t){let r=new Int32Array(e);try{this.rsDecoder.decode(r,e.length-t)}catch(e){throw new B}for(let n=0;n<t;n++)e[n]=r[n]}}class tb extends eA{constructor(e,t,r){super(e,t),this.estimatedModuleSize=r}aboutEquals(e,t,r){if(Math.abs(t-this.getY())<=e&&Math.abs(r-this.getX())<=e){let t=Math.abs(e-this.estimatedModuleSize);return t<=1||t<=this.estimatedModuleSize}return!1}combineEstimate(e,t,r){let n=(this.getX()+t)/2;return new tb(n,(this.getY()+e)/2,(this.estimatedModuleSize+r)/2)}}class tP{constructor(e,t,r,n,i,s,o){this.image=e,this.startX=t,this.startY=r,this.width=n,this.height=i,this.moduleSize=s,this.resultPointCallback=o,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}find(){let e=this.startX,t=this.height,r=e+this.width,n=this.startY+t/2,i=new Int32Array(3),s=this.image;for(let o=0;o<t;o++){let t=n+((1&o)==0?Math.floor((o+1)/2):-Math.floor((o+1)/2));i[0]=0,i[1]=0,i[2]=0;let a=e;for(;a<r&&!s.get(a,t);)a++;let l=0;for(;a<r;){if(s.get(a,t))if(1===l)i[1]++;else if(2===l){if(this.foundPatternCross(i)){let e=this.handlePossibleCenter(i,t,a);if(null!==e)return e}i[0]=i[2],i[1]=1,i[2]=0,l=1}else i[++l]++;else 1===l&&l++,i[l]++;a++}if(this.foundPatternCross(i)){let e=this.handlePossibleCenter(i,t,r);if(null!==e)return e}}if(0!==this.possibleCenters.length)return this.possibleCenters[0];throw new Z}static centerFromEnd(e,t){return t-e[2]-e[1]/2}foundPatternCross(e){let t=this.moduleSize,r=t/2;for(let n=0;n<3;n++)if(Math.abs(t-e[n])>=r)return!1;return!0}crossCheckVertical(e,t,r,n){let i=this.image,s=i.getHeight(),o=this.crossCheckStateCount;o[0]=0,o[1]=0,o[2]=0;let a=e;for(;a>=0&&i.get(t,a)&&o[1]<=r;)o[1]++,a--;if(a<0||o[1]>r)return NaN;for(;a>=0&&!i.get(t,a)&&o[0]<=r;)o[0]++,a--;if(o[0]>r)return NaN;for(a=e+1;a<s&&i.get(t,a)&&o[1]<=r;)o[1]++,a++;if(a===s||o[1]>r)return NaN;for(;a<s&&!i.get(t,a)&&o[2]<=r;)o[2]++,a++;return o[2]>r||5*Math.abs(o[0]+o[1]+o[2]-n)>=2*n?NaN:this.foundPatternCross(o)?tP.centerFromEnd(o,a):NaN}handlePossibleCenter(e,t,r){let n=e[0]+e[1]+e[2],i=tP.centerFromEnd(e,r),s=this.crossCheckVertical(t,i,2*e[1],n);if(!isNaN(s)){let t=(e[0]+e[1]+e[2])/3;for(let e of this.possibleCenters)if(e.aboutEquals(t,s,i))return e.combineEstimate(s,i,t);let r=new tb(i,s,t);this.possibleCenters.push(r),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(r)}return null}}class tL extends eA{constructor(e,t,r,n){super(e,t),this.estimatedModuleSize=r,this.count=n,void 0===n&&(this.count=1)}getEstimatedModuleSize(){return this.estimatedModuleSize}getCount(){return this.count}aboutEquals(e,t,r){if(Math.abs(t-this.getY())<=e&&Math.abs(r-this.getX())<=e){let t=Math.abs(e-this.estimatedModuleSize);return t<=1||t<=this.estimatedModuleSize}return!1}combineEstimate(e,t,r){let n=this.count+1,i=(this.count*this.getX()+t)/n;return new tL(i,(this.count*this.getY()+e)/n,(this.count*this.estimatedModuleSize+r)/n,n)}}class tF{constructor(e){this.bottomLeft=e[0],this.topLeft=e[1],this.topRight=e[2]}getBottomLeft(){return this.bottomLeft}getTopLeft(){return this.topLeft}getTopRight(){return this.topRight}}class tv{constructor(e,t){this.image=e,this.resultPointCallback=t,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=t}getImage(){return this.image}getPossibleCenters(){return this.possibleCenters}find(e){let t=null!=e&&void 0!==e.get(V.TRY_HARDER),r=null!=e&&void 0!==e.get(V.PURE_BARCODE),n=this.image,i=n.getHeight(),s=n.getWidth(),o=Math.floor(3*i/(4*tv.MAX_MODULES));(o<tv.MIN_SKIP||t)&&(o=tv.MIN_SKIP);let a=!1,l=new Int32Array(5);for(let e=o-1;e<i&&!a;e+=o){l[0]=0,l[1]=0,l[2]=0,l[3]=0,l[4]=0;let t=0;for(let i=0;i<s;i++)if(n.get(i,e))(1&t)==1&&t++,l[t]++;else if((1&t)==0)if(4===t)if(tv.foundPatternCross(l)){if(!0===this.handlePossibleCenter(l,e,i,r))if(o=2,!0===this.hasSkipped)a=this.haveMultiplyConfirmedCenters();else{let t=this.findRowSkip();t>l[2]&&(e+=t-l[2]-o,i=s-1)}else{l[0]=l[2],l[1]=l[3],l[2]=l[4],l[3]=1,l[4]=0,t=3;continue}t=0,l[0]=0,l[1]=0,l[2]=0,l[3]=0,l[4]=0}else l[0]=l[2],l[1]=l[3],l[2]=l[4],l[3]=1,l[4]=0,t=3;else l[++t]++;else l[t]++;tv.foundPatternCross(l)&&!0===this.handlePossibleCenter(l,e,s,r)&&(o=l[0],this.hasSkipped&&(a=this.haveMultiplyConfirmedCenters()))}let h=this.selectBestPatterns();return eA.orderBestPatterns(h),new tF(h)}static centerFromEnd(e,t){return t-e[4]-e[3]-e[2]/2}static foundPatternCross(e){let t=0;for(let r=0;r<5;r++){let n=e[r];if(0===n)return!1;t+=n}if(t<7)return!1;let r=t/7,n=r/2;return Math.abs(r-e[0])<n&&Math.abs(r-e[1])<n&&Math.abs(3*r-e[2])<3*n&&Math.abs(r-e[3])<n&&Math.abs(r-e[4])<n}getCrossCheckStateCount(){let e=this.crossCheckStateCount;return e[0]=0,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e}crossCheckDiagonal(e,t,r,n){let i=this.getCrossCheckStateCount(),s=0,o=this.image;for(;e>=s&&t>=s&&o.get(t-s,e-s);)i[2]++,s++;if(e<s||t<s)return!1;for(;e>=s&&t>=s&&!o.get(t-s,e-s)&&i[1]<=r;)i[1]++,s++;if(e<s||t<s||i[1]>r)return!1;for(;e>=s&&t>=s&&o.get(t-s,e-s)&&i[0]<=r;)i[0]++,s++;if(i[0]>r)return!1;let a=o.getHeight(),l=o.getWidth();for(s=1;e+s<a&&t+s<l&&o.get(t+s,e+s);)i[2]++,s++;if(e+s>=a||t+s>=l)return!1;for(;e+s<a&&t+s<l&&!o.get(t+s,e+s)&&i[3]<r;)i[3]++,s++;if(e+s>=a||t+s>=l||i[3]>=r)return!1;for(;e+s<a&&t+s<l&&o.get(t+s,e+s)&&i[4]<r;)i[4]++,s++;return!(i[4]>=r)&&Math.abs(i[0]+i[1]+i[2]+i[3]+i[4]-n)<2*n&&tv.foundPatternCross(i)}crossCheckVertical(e,t,r,n){let i=this.image,s=i.getHeight(),o=this.getCrossCheckStateCount(),a=e;for(;a>=0&&i.get(t,a);)o[2]++,a--;if(a<0)return NaN;for(;a>=0&&!i.get(t,a)&&o[1]<=r;)o[1]++,a--;if(a<0||o[1]>r)return NaN;for(;a>=0&&i.get(t,a)&&o[0]<=r;)o[0]++,a--;if(o[0]>r)return NaN;for(a=e+1;a<s&&i.get(t,a);)o[2]++,a++;if(a===s)return NaN;for(;a<s&&!i.get(t,a)&&o[3]<r;)o[3]++,a++;if(a===s||o[3]>=r)return NaN;for(;a<s&&i.get(t,a)&&o[4]<r;)o[4]++,a++;return o[4]>=r||5*Math.abs(o[0]+o[1]+o[2]+o[3]+o[4]-n)>=2*n?NaN:tv.foundPatternCross(o)?tv.centerFromEnd(o,a):NaN}crossCheckHorizontal(e,t,r,n){let i=this.image,s=i.getWidth(),o=this.getCrossCheckStateCount(),a=e;for(;a>=0&&i.get(a,t);)o[2]++,a--;if(a<0)return NaN;for(;a>=0&&!i.get(a,t)&&o[1]<=r;)o[1]++,a--;if(a<0||o[1]>r)return NaN;for(;a>=0&&i.get(a,t)&&o[0]<=r;)o[0]++,a--;if(o[0]>r)return NaN;for(a=e+1;a<s&&i.get(a,t);)o[2]++,a++;if(a===s)return NaN;for(;a<s&&!i.get(a,t)&&o[3]<r;)o[3]++,a++;if(a===s||o[3]>=r)return NaN;for(;a<s&&i.get(a,t)&&o[4]<r;)o[4]++,a++;return o[4]>=r||5*Math.abs(o[0]+o[1]+o[2]+o[3]+o[4]-n)>=n?NaN:tv.foundPatternCross(o)?tv.centerFromEnd(o,a):NaN}handlePossibleCenter(e,t,r,n){let i=e[0]+e[1]+e[2]+e[3]+e[4],s=tv.centerFromEnd(e,r),o=this.crossCheckVertical(t,Math.floor(s),e[2],i);if(!isNaN(o)&&!isNaN(s=this.crossCheckHorizontal(Math.floor(s),Math.floor(o),e[2],i))&&(!n||this.crossCheckDiagonal(Math.floor(o),Math.floor(s),e[2],i))){let e=i/7,t=!1,r=this.possibleCenters;for(let n=0,i=r.length;n<i;n++){let i=r[n];if(i.aboutEquals(e,o,s)){r[n]=i.combineEstimate(o,s,e),t=!0;break}}if(!t){let t=new tL(s,o,e);r.push(t),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(t)}return!0}return!1}findRowSkip(){if(this.possibleCenters.length<=1)return 0;let e=null;for(let t of this.possibleCenters)if(t.getCount()>=tv.CENTER_QUORUM)if(null!=e)return this.hasSkipped=!0,Math.floor((Math.abs(e.getX()-t.getX())-Math.abs(e.getY()-t.getY()))/2);else e=t;return 0}haveMultiplyConfirmedCenters(){let e=0,t=0,r=this.possibleCenters.length;for(let r of this.possibleCenters)r.getCount()>=tv.CENTER_QUORUM&&(e++,t+=r.getEstimatedModuleSize());if(e<3)return!1;let n=t/r,i=0;for(let e of this.possibleCenters)i+=Math.abs(e.getEstimatedModuleSize()-n);return i<=.05*t}selectBestPatterns(){let e,t=this.possibleCenters.length;if(t<3)throw new Z;let r=this.possibleCenters;if(t>3){let n=0,i=0;for(let e of this.possibleCenters){let t=e.getEstimatedModuleSize();n+=t,i+=t*t}e=n/t;let s=Math.sqrt(i/t-e*e);r.sort((t,r)=>{let n=Math.abs(r.getEstimatedModuleSize()-e),i=Math.abs(t.getEstimatedModuleSize()-e);return n<i?-1:+(n>i)});let o=Math.max(.2*e,s);for(let t=0;t<r.length&&r.length>3;t++)Math.abs(r[t].getEstimatedModuleSize()-e)>o&&(r.splice(t,1),t--)}if(r.length>3){let t=0;for(let e of r)t+=e.getEstimatedModuleSize();e=t/r.length,r.sort((t,r)=>{if(r.getCount()!==t.getCount())return r.getCount()-t.getCount();{let n=Math.abs(r.getEstimatedModuleSize()-e),i=Math.abs(t.getEstimatedModuleSize()-e);return n<i?1:n>i?-1:0}}),r.splice(3)}return[r[0],r[1],r[2]]}}tv.CENTER_QUORUM=2,tv.MIN_SKIP=3,tv.MAX_MODULES=57;class tk{constructor(e){this.image=e}getImage(){return this.image}getResultPointCallback(){return this.resultPointCallback}detect(e){this.resultPointCallback=null==e?null:e.get(V.NEED_RESULT_POINT_CALLBACK);let t=new tv(this.image,this.resultPointCallback).find(e);return this.processFinderPatternInfo(t)}processFinderPatternInfo(e){let t,r=e.getTopLeft(),n=e.getTopRight(),i=e.getBottomLeft(),s=this.calculateModuleSize(r,n,i);if(s<1)throw new Z("No pattern found in proccess finder.");let o=tk.computeDimension(r,n,i,s),a=tT.getProvisionalVersionForDimension(o),l=a.getDimensionForVersion()-7,h=null;if(a.getAlignmentPatternCenters().length>0){let e=n.getX()-r.getX()+i.getX(),t=n.getY()-r.getY()+i.getY(),o=1-3/l,a=Math.floor(r.getX()+o*(e-r.getX())),u=Math.floor(r.getY()+o*(t-r.getY()));for(let e=4;e<=16;e<<=1)try{h=this.findAlignmentInRegion(s,a,u,e);break}catch(e){if(!(e instanceof Z))throw e}}let u=tk.createTransform(r,n,i,h,o);return new eC(tk.sampleGrid(this.image,u,o),null===h?[i,r,n]:[i,r,n,h])}static createTransform(e,t,r,n,i){let s,o,a,l,h=i-3.5;return null!==n?(s=n.getX(),o=n.getY(),l=a=h-3):(s=t.getX()-e.getX()+r.getX(),o=t.getY()-e.getY()+r.getY(),a=h,l=h),eI.quadrilateralToQuadrilateral(3.5,3.5,h,3.5,a,l,3.5,h,e.getX(),e.getY(),t.getX(),t.getY(),s,o,r.getX(),r.getY())}static sampleGrid(e,t,r){return ep.getInstance().sampleGridWithTransform(e,r,r,t)}static computeDimension(e,t,r,n){let i=Math.floor((ef.round(eA.distance(e,t)/n)+ef.round(eA.distance(e,r)/n))/2)+7;switch(3&i){case 0:i++;break;case 2:i--;break;case 3:throw new Z("Dimensions could be not found.")}return i}calculateModuleSize(e,t,r){return(this.calculateModuleSizeOneWay(e,t)+this.calculateModuleSizeOneWay(e,r))/2}calculateModuleSizeOneWay(e,t){let r=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY())),n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY()));return isNaN(r)?n/7:isNaN(n)?r/7:(r+n)/14}sizeOfBlackWhiteBlackRunBothWays(e,t,r,n){let i=this.sizeOfBlackWhiteBlackRun(e,t,r,n),s=1,o=e-(r-e);o<0?(s=e/(e-o),o=0):o>=this.image.getWidth()&&(s=(this.image.getWidth()-1-e)/(o-e),o=this.image.getWidth()-1);let a=Math.floor(t-(n-t)*s);return s=1,a<0?(s=t/(t-a),a=0):a>=this.image.getHeight()&&(s=(this.image.getHeight()-1-t)/(a-t),a=this.image.getHeight()-1),o=Math.floor(e+(o-e)*s),(i+=this.sizeOfBlackWhiteBlackRun(e,t,o,a))-1}sizeOfBlackWhiteBlackRun(e,t,r,n){let i=Math.abs(n-t)>Math.abs(r-e);if(i){let i=e;e=t,t=i,i=r,r=n,n=i}let s=Math.abs(r-e),o=Math.abs(n-t),a=-s/2,l=e<r?1:-1,h=t<n?1:-1,u=0,d=r+l;for(let r=e,c=t;r!==d;r+=l){let l=i?c:r,d=i?r:c;if(1===u===this.image.get(l,d)){if(2===u)return ef.distance(r,c,e,t);u++}if((a+=o)>0){if(c===n)break;c+=h,a-=s}}return 2===u?ef.distance(r+l,n,e,t):NaN}findAlignmentInRegion(e,t,r,n){let i=Math.floor(n*e),s=Math.max(0,t-i),o=Math.min(this.image.getWidth()-1,t+i);if(o-s<3*e)throw new Z("Alignment top exceeds estimated module size.");let a=Math.max(0,r-i),l=Math.min(this.image.getHeight()-1,r+i);if(l-a<3*e)throw new Z("Alignment bottom exceeds estimated module size.");return new tP(this.image,s,a,o-s,l-a,e,this.resultPointCallback).find()}}class tx{constructor(){this.decoder=new tB}getDecoder(){return this.decoder}decode(e,t){let r,n;if(null!=t&&void 0!==t.get(V.PURE_BARCODE)){let i=tx.extractPureBits(e.getBlackMatrix());r=this.decoder.decodeBitMatrix(i,t),n=tx.NO_POINTS}else{let i=new tk(e.getBlackMatrix()).detect(t);r=this.decoder.decodeBitMatrix(i.getBits(),t),n=i.getPoints()}r.getOther()instanceof tM&&r.getOther().applyMirroredCorrection(n);let i=new er(r.getText(),r.getRawBytes(),void 0,n,en.QR_CODE,void 0),s=r.getByteSegments();null!==s&&i.putMetadata(ei.BYTE_SEGMENTS,s);let o=r.getECLevel();return null!==o&&i.putMetadata(ei.ERROR_CORRECTION_LEVEL,o),r.hasStructuredAppend()&&(i.putMetadata(ei.STRUCTURED_APPEND_SEQUENCE,r.getStructuredAppendSequenceNumber()),i.putMetadata(ei.STRUCTURED_APPEND_PARITY,r.getStructuredAppendParity())),i}reset(){}static extractPureBits(e){let t=e.getTopLeftOnBit(),r=e.getBottomRightOnBit();if(null===t||null===r)throw new Z;let n=this.moduleSize(t,e),i=t[1],s=r[1],o=t[0],a=r[0];if(o>=a||i>=s||s-i!=a-o&&(a=o+(s-i))>=e.getWidth())throw new Z;let l=Math.round((a-o+1)/n),h=Math.round((s-i+1)/n);if(l<=0||h<=0||h!==l)throw new Z;let u=Math.floor(n/2);i+=u;let d=(o+=u)+Math.floor((l-1)*n)-a;if(d>0){if(d>u)throw new Z;o-=d}let c=i+Math.floor((h-1)*n)-s;if(c>0){if(c>u)throw new Z;i-=c}let g=new Y(l,h);for(let t=0;t<h;t++){let r=i+Math.floor(t*n);for(let i=0;i<l;i++)e.get(o+Math.floor(i*n),r)&&g.set(i,t)}return g}static moduleSize(e,t){let r=t.getHeight(),n=t.getWidth(),i=e[0],s=e[1],o=!0,a=0;for(;i<n&&s<r;){if(o!==t.get(i,s)){if(5==++a)break;o=!o}i++,s++}if(i===n||s===r)throw new Z;return(i-e[0])/7}}tx.NO_POINTS=[];class tV{PDF417Common(){}static getBitCountSum(e){return ef.sum(e)}static toIntArray(e){if(null==e||!e.length)return tV.EMPTY_INT_ARRAY;let t=new Int32Array(e.length),r=0;for(let n of e)t[r++]=n;return t}static getCodeword(e){let t=v.binarySearch(tV.SYMBOL_TABLE,262143&e);return t<0?-1:(tV.CODEWORD_TABLE[t]-1)%tV.NUMBER_OF_CODEWORDS}}tV.NUMBER_OF_CODEWORDS=929,tV.MAX_CODEWORDS_IN_BARCODE=tV.NUMBER_OF_CODEWORDS-1,tV.MIN_ROWS_IN_BARCODE=3,tV.MAX_ROWS_IN_BARCODE=90,tV.MODULES_IN_CODEWORD=17,tV.MODULES_IN_STOP_PATTERN=18,tV.BARS_IN_MODULE=8,tV.EMPTY_INT_ARRAY=new Int32Array([]),tV.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),tV.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]);class tU{constructor(e,t){this.bits=e,this.points=t}getBits(){return this.bits}getPoints(){return this.points}}class tH{static detectMultiple(e,t,r){let n=e.getBlackMatrix(),i=tH.detect(r,n);return i.length||((n=n.clone()).rotate180(),i=tH.detect(r,n)),new tU(n,i)}static detect(e,t){let r=[],n=0,i=0,s=!1;for(;n<t.getHeight();){let o=tH.findVertices(t,n,i);if(null==o[0]&&null==o[3]){if(!s)break;for(let e of(s=!1,i=0,r))null!=e[1]&&(n=Math.trunc(Math.max(n,e[1].getY()))),null!=e[3]&&(n=Math.max(n,Math.trunc(e[3].getY())));n+=tH.ROW_STEP;continue}if(s=!0,r.push(o),!e)break;null!=o[2]?(i=Math.trunc(o[2].getX()),n=Math.trunc(o[2].getY())):(i=Math.trunc(o[4].getX()),n=Math.trunc(o[4].getY()))}return r}static findVertices(e,t,r){let n=e.getHeight(),i=e.getWidth(),s=Array(8);return tH.copyToResult(s,tH.findRowsWithPattern(e,n,i,t,r,tH.START_PATTERN),tH.INDEXES_START_PATTERN),null!=s[4]&&(r=Math.trunc(s[4].getX()),t=Math.trunc(s[4].getY())),tH.copyToResult(s,tH.findRowsWithPattern(e,n,i,t,r,tH.STOP_PATTERN),tH.INDEXES_STOP_PATTERN),s}static copyToResult(e,t,r){for(let n=0;n<r.length;n++)e[r[n]]=t[n]}static findRowsWithPattern(e,t,r,n,i,s){let o=[,,,,],a=!1,l=new Int32Array(s.length);for(;n<t;n+=tH.ROW_STEP){let t=tH.findGuardPattern(e,i,n,r,!1,s,l);if(null!=t){for(;n>0;){let o=tH.findGuardPattern(e,i,--n,r,!1,s,l);if(null!=o)t=o;else{n++;break}}o[0]=new eA(t[0],n),o[1]=new eA(t[1],n),a=!0;break}}let h=n+1;if(a){let n=0,i=Int32Array.from([Math.trunc(o[0].getX()),Math.trunc(o[1].getX())]);for(;h<t;h++){let t=tH.findGuardPattern(e,i[0],h,r,!1,s,l);if(null!=t&&Math.abs(i[0]-t[0])<tH.MAX_PATTERN_DRIFT&&Math.abs(i[1]-t[1])<tH.MAX_PATTERN_DRIFT)i=t,n=0;else if(n>tH.SKIPPED_ROW_COUNT_MAX)break;else n++}h-=n+1,o[2]=new eA(i[0],h),o[3]=new eA(i[1],h)}return h-n<tH.BARCODE_MIN_HEIGHT&&v.fill(o,null),o}static findGuardPattern(e,t,r,n,i,s,o){v.fillWithin(o,0,o.length,0);let a=t,l=0;for(;e.get(a,r)&&a>0&&l++<tH.MAX_PIXEL_DRIFT;)a--;let h=a,u=0,d=s.length;for(let t=i;h<n;h++)if(e.get(h,r)!==t)o[u]++;else{if(u===d-1){if(tH.patternMatchVariance(o,s,tH.MAX_INDIVIDUAL_VARIANCE)<tH.MAX_AVG_VARIANCE)return new Int32Array([a,h]);a+=o[0]+o[1],P.arraycopy(o,2,o,0,u-1),o[u-1]=0,o[u]=0,u--}else u++;o[u]=1,t=!t}return u===d-1&&tH.patternMatchVariance(o,s,tH.MAX_INDIVIDUAL_VARIANCE)<tH.MAX_AVG_VARIANCE?new Int32Array([a,h-1]):null}static patternMatchVariance(e,t,r){let n=e.length,i=0,s=0;for(let r=0;r<n;r++)i+=e[r],s+=t[r];if(i<s)return 1/0;let o=i/s;r*=o;let a=0;for(let i=0;i<n;i++){let n=e[i],s=t[i]*o,l=n>s?n-s:s-n;if(l>r)return 1/0;a+=l}return a/i}}tH.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),tH.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),tH.MAX_AVG_VARIANCE=.42,tH.MAX_INDIVIDUAL_VARIANCE=.8,tH.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),tH.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),tH.MAX_PIXEL_DRIFT=3,tH.MAX_PATTERN_DRIFT=5,tH.SKIPPED_ROW_COUNT_MAX=25,tH.ROW_STEP=5,tH.BARCODE_MIN_HEIGHT=10;class tG{constructor(e,t){if(0===t.length)throw new O;this.field=e;let r=t.length;if(r>1&&0===t[0]){let e=1;for(;e<r&&0===t[e];)e++;e===r?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(r-e),P.arraycopy(t,e,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}getCoefficients(){return this.coefficients}getDegree(){return this.coefficients.length-1}isZero(){return 0===this.coefficients[0]}getCoefficient(e){return this.coefficients[this.coefficients.length-1-e]}evaluateAt(e){if(0===e)return this.getCoefficient(0);if(1===e){let e=0;for(let t of this.coefficients)e=this.field.add(e,t);return e}let t=this.coefficients[0],r=this.coefficients.length;for(let n=1;n<r;n++)t=this.field.add(this.field.multiply(e,t),this.coefficients[n]);return t}add(e){if(!this.field.equals(e.field))throw new O("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;let t=this.coefficients,r=e.coefficients;if(t.length>r.length){let e=t;t=r,r=e}let n=new Int32Array(r.length),i=r.length-t.length;P.arraycopy(r,0,n,0,i);for(let e=i;e<r.length;e++)n[e]=this.field.add(t[e-i],r[e]);return new tG(this.field,n)}subtract(e){if(!this.field.equals(e.field))throw new O("ModulusPolys do not have same ModulusGF field");return e.isZero()?this:this.add(e.negative())}multiply(e){return e instanceof tG?this.multiplyOther(e):this.multiplyScalar(e)}multiplyOther(e){if(!this.field.equals(e.field))throw new O("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new tG(this.field,new Int32Array([0]));let t=this.coefficients,r=t.length,n=e.coefficients,i=n.length,s=new Int32Array(r+i-1);for(let e=0;e<r;e++){let r=t[e];for(let t=0;t<i;t++)s[e+t]=this.field.add(s[e+t],this.field.multiply(r,n[t]))}return new tG(this.field,s)}negative(){let e=this.coefficients.length,t=new Int32Array(e);for(let r=0;r<e;r++)t[r]=this.field.subtract(0,this.coefficients[r]);return new tG(this.field,t)}multiplyScalar(e){if(0===e)return new tG(this.field,new Int32Array([0]));if(1===e)return this;let t=this.coefficients.length,r=new Int32Array(t);for(let n=0;n<t;n++)r[n]=this.field.multiply(this.coefficients[n],e);return new tG(this.field,r)}multiplyByMonomial(e,t){if(e<0)throw new O;if(0===t)return new tG(this.field,new Int32Array([0]));let r=this.coefficients.length,n=new Int32Array(r+e);for(let e=0;e<r;e++)n[e]=this.field.multiply(this.coefficients[e],t);return new tG(this.field,n)}toString(){let e=new z;for(let t=this.getDegree();t>=0;t--){let r=this.getCoefficient(t);0!==r&&(r<0?(e.append(" - "),r=-r):e.length()>0&&e.append(" + "),(0===t||1!==r)&&e.append(r),0!==t&&(1===t?e.append("x"):(e.append("x^"),e.append(t))))}return e.toString()}}class tX{add(e,t){return(e+t)%this.modulus}subtract(e,t){return(this.modulus+e-t)%this.modulus}exp(e){return this.expTable[e]}log(e){if(0===e)throw new O;return this.logTable[e]}inverse(e){if(0===e)throw new el;return this.expTable[this.modulus-this.logTable[e]-1]}multiply(e,t){return 0===e||0===t?0:this.expTable[(this.logTable[e]+this.logTable[t])%(this.modulus-1)]}getSize(){return this.modulus}equals(e){return e===this}}class tW extends tX{constructor(e,t){super(),this.modulus=e,this.expTable=new Int32Array(e),this.logTable=new Int32Array(e);let r=1;for(let n=0;n<e;n++)this.expTable[n]=r,r=r*t%e;for(let t=0;t<e-1;t++)this.logTable[this.expTable[t]]=t;this.zero=new tG(this,new Int32Array([0])),this.one=new tG(this,new Int32Array([1]))}getZero(){return this.zero}getOne(){return this.one}buildMonomial(e,t){if(e<0)throw new O;if(0===t)return this.zero;let r=new Int32Array(e+1);return r[0]=t,new tG(this,r)}}tW.PDF417_GF=new tW(tV.NUMBER_OF_CODEWORDS,3);class tz{constructor(){this.field=tW.PDF417_GF}decode(e,t,r){let n=new tG(this.field,e),i=new Int32Array(t),s=!1;for(let e=t;e>0;e--){let r=n.evaluateAt(this.field.exp(e));i[t-e]=r,0!==r&&(s=!0)}if(!s)return 0;let o=this.field.getOne();if(null!=r)for(let t of r){let r=this.field.exp(e.length-1-t),n=new tG(this.field,new Int32Array([this.field.subtract(0,r),1]));o=o.multiply(n)}let a=new tG(this.field,i),l=this.runEuclideanAlgorithm(this.field.buildMonomial(t,1),a,t),h=l[0],u=l[1],d=this.findErrorLocations(h),c=this.findErrorMagnitudes(u,h,d);for(let t=0;t<d.length;t++){let r=e.length-1-this.field.log(d[t]);if(r<0)throw B.getChecksumInstance();e[r]=this.field.subtract(e[r],c[t])}return d.length}runEuclideanAlgorithm(e,t,r){if(e.getDegree()<t.getDegree()){let r=e;e=t,t=r}let n=e,i=t,s=this.field.getZero(),o=this.field.getOne();for(;i.getDegree()>=Math.round(r/2);){let e=n,t=s;if(n=i,s=o,n.isZero())throw B.getChecksumInstance();i=e;let r=this.field.getZero(),a=n.getCoefficient(n.getDegree()),l=this.field.inverse(a);for(;i.getDegree()>=n.getDegree()&&!i.isZero();){let e=i.getDegree()-n.getDegree(),t=this.field.multiply(i.getCoefficient(i.getDegree()),l);r=r.add(this.field.buildMonomial(e,t)),i=i.subtract(n.multiplyByMonomial(e,t))}o=r.multiply(s).subtract(t).negative()}let a=o.getCoefficient(0);if(0===a)throw B.getChecksumInstance();let l=this.field.inverse(a);return[o.multiply(l),i.multiply(l)]}findErrorLocations(e){let t=e.getDegree(),r=new Int32Array(t),n=0;for(let i=1;i<this.field.getSize()&&n<t;i++)0===e.evaluateAt(i)&&(r[n]=this.field.inverse(i),n++);if(n!==t)throw B.getChecksumInstance();return r}findErrorMagnitudes(e,t,r){let n=t.getDegree(),i=new Int32Array(n);for(let e=1;e<=n;e++)i[n-e]=this.field.multiply(e,t.getCoefficient(e));let s=new tG(this.field,i),o=r.length,a=new Int32Array(o);for(let t=0;t<o;t++){let n=this.field.inverse(r[t]),i=this.field.subtract(0,e.evaluateAt(n)),o=this.field.inverse(s.evaluateAt(n));a[t]=this.field.multiply(i,o)}return a}}class tY{constructor(e,t,r,n,i){e instanceof tY?this.constructor_2(e):this.constructor_1(e,t,r,n,i)}constructor_1(e,t,r,n,i){let s=null==t||null==r,o=null==n||null==i;if(s&&o)throw new Z;s?(t=new eA(0,n.getY()),r=new eA(0,i.getY())):o&&(n=new eA(e.getWidth()-1,t.getY()),i=new eA(e.getWidth()-1,r.getY())),this.image=e,this.topLeft=t,this.bottomLeft=r,this.topRight=n,this.bottomRight=i,this.minX=Math.trunc(Math.min(t.getX(),r.getX())),this.maxX=Math.trunc(Math.max(n.getX(),i.getX())),this.minY=Math.trunc(Math.min(t.getY(),n.getY())),this.maxY=Math.trunc(Math.max(r.getY(),i.getY()))}constructor_2(e){this.image=e.image,this.topLeft=e.getTopLeft(),this.bottomLeft=e.getBottomLeft(),this.topRight=e.getTopRight(),this.bottomRight=e.getBottomRight(),this.minX=e.getMinX(),this.maxX=e.getMaxX(),this.minY=e.getMinY(),this.maxY=e.getMaxY()}static merge(e,t){return null==e?t:null==t?e:new tY(e.image,e.topLeft,e.bottomLeft,t.topRight,t.bottomRight)}addMissingRows(e,t,r){let n=this.topLeft,i=this.bottomLeft,s=this.topRight,o=this.bottomRight;if(e>0){let t=r?this.topLeft:this.topRight,i=Math.trunc(t.getY()-e);i<0&&(i=0);let o=new eA(t.getX(),i);r?n=o:s=o}if(t>0){let e=r?this.bottomLeft:this.bottomRight,n=Math.trunc(e.getY()+t);n>=this.image.getHeight()&&(n=this.image.getHeight()-1);let s=new eA(e.getX(),n);r?i=s:o=s}return new tY(this.image,n,i,s,o)}getMinX(){return this.minX}getMaxX(){return this.maxX}getMinY(){return this.minY}getMaxY(){return this.maxY}getTopLeft(){return this.topLeft}getTopRight(){return this.topRight}getBottomLeft(){return this.bottomLeft}getBottomRight(){return this.bottomRight}}class tZ{constructor(e,t,r,n){this.columnCount=e,this.errorCorrectionLevel=n,this.rowCountUpperPart=t,this.rowCountLowerPart=r,this.rowCount=t+r}getColumnCount(){return this.columnCount}getErrorCorrectionLevel(){return this.errorCorrectionLevel}getRowCount(){return this.rowCount}getRowCountUpperPart(){return this.rowCountUpperPart}getRowCountLowerPart(){return this.rowCountLowerPart}}class tK{constructor(){this.buffer=""}static form(e,t){let r=-1;return e.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function(e,n,i,s,o,a){let l;if("%%"===e)return"%";if(void 0===t[++r])return;e=s?parseInt(s.substr(1)):void 0;let h=o?parseInt(o.substr(1)):void 0;switch(a){case"s":l=t[r];break;case"c":l=t[r][0];break;case"f":l=parseFloat(t[r]).toFixed(e);break;case"p":l=parseFloat(t[r]).toPrecision(e);break;case"e":l=parseFloat(t[r]).toExponential(e);break;case"x":l=parseInt(t[r]).toString(h||16);break;case"d":l=parseFloat(parseInt(t[r],h||10).toPrecision(e)).toFixed(0)}l="object"==typeof l?JSON.stringify(l):(+l).toString(h);let u=parseInt(i),d=i&&i[0]+""=="0"?"0":" ";for(;l.length<u;)l=void 0!==n?l+d:d+l;return l})}format(e,...t){this.buffer+=tK.form(e,t)}toString(){return this.buffer}}class tq{constructor(e){this.boundingBox=new tY(e),this.codewords=Array(e.getMaxY()-e.getMinY()+1)}getCodewordNearby(e){let t=this.getCodeword(e);if(null!=t)return t;for(let r=1;r<tq.MAX_NEARBY_DISTANCE;r++){let n=this.imageRowToCodewordIndex(e)-r;if(n>=0&&null!=(t=this.codewords[n])||(n=this.imageRowToCodewordIndex(e)+r)<this.codewords.length&&null!=(t=this.codewords[n]))return t}return null}imageRowToCodewordIndex(e){return e-this.boundingBox.getMinY()}setCodeword(e,t){this.codewords[this.imageRowToCodewordIndex(e)]=t}getCodeword(e){return this.codewords[this.imageRowToCodewordIndex(e)]}getBoundingBox(){return this.boundingBox}getCodewords(){return this.codewords}toString(){let e=new tK,t=0;for(let r of this.codewords){if(null==r){e.format("%3d:    |   %n",t++);continue}e.format("%3d: %3d|%3d%n",t++,r.getRowNumber(),r.getValue())}return e.toString()}}tq.MAX_NEARBY_DISTANCE=5;class tQ{constructor(){this.values=new Map}setValue(e){e=Math.trunc(e);let t=this.values.get(e);null==t&&(t=0),t++,this.values.set(e,t)}getValue(){let e=-1,t=[];for(let[r,n]of this.values.entries()){let i={getKey:()=>r,getValue:()=>n};i.getValue()>e?(e=i.getValue(),(t=[]).push(i.getKey())):i.getValue()===e&&t.push(i.getKey())}return tV.toIntArray(t)}getConfidence(e){return this.values.get(e)}}class tj extends tq{constructor(e,t){super(e),this._isLeft=t}setRowNumbers(){for(let e of this.getCodewords())null!=e&&e.setRowNumberAsRowIndicatorColumn()}adjustCompleteIndicatorColumnRowNumbers(e){let t=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(t,e);let r=this.getBoundingBox(),n=this._isLeft?r.getTopLeft():r.getTopRight(),i=this._isLeft?r.getBottomLeft():r.getBottomRight(),s=this.imageRowToCodewordIndex(Math.trunc(n.getY())),o=this.imageRowToCodewordIndex(Math.trunc(i.getY())),a=-1,l=1,h=0;for(let r=s;r<o;r++){if(null==t[r])continue;let n=t[r],i=n.getRowNumber()-a;if(0===i)h++;else if(1===i)l=Math.max(l,h),h=1,a=n.getRowNumber();else if(i<0||n.getRowNumber()>=e.getRowCount()||i>r)t[r]=null;else{let e,s=(e=l>2?(l-2)*i:i)>=r;for(let n=1;n<=e&&!s;n++)s=null!=t[r-n];s?t[r]=null:(a=n.getRowNumber(),h=1)}}}getRowHeights(){let e=this.getBarcodeMetadata();if(null==e)return null;this.adjustIncompleteIndicatorColumnRowNumbers(e);let t=new Int32Array(e.getRowCount());for(let e of this.getCodewords())if(null!=e){let r=e.getRowNumber();if(r>=t.length)continue;t[r]++}return t}adjustIncompleteIndicatorColumnRowNumbers(e){let t=this.getBoundingBox(),r=this._isLeft?t.getTopLeft():t.getTopRight(),n=this._isLeft?t.getBottomLeft():t.getBottomRight(),i=this.imageRowToCodewordIndex(Math.trunc(r.getY())),s=this.imageRowToCodewordIndex(Math.trunc(n.getY())),o=this.getCodewords(),a=-1;for(let t=i;t<s;t++){if(null==o[t])continue;let r=o[t];r.setRowNumberAsRowIndicatorColumn();let n=r.getRowNumber()-a;0===n||(1===n?a=r.getRowNumber():r.getRowNumber()>=e.getRowCount()?o[t]=null:a=r.getRowNumber())}}getBarcodeMetadata(){let e=this.getCodewords(),t=new tQ,r=new tQ,n=new tQ,i=new tQ;for(let s of e){if(null==s)continue;s.setRowNumberAsRowIndicatorColumn();let e=s.getValue()%30,o=s.getRowNumber();switch(!this._isLeft&&(o+=2),o%3){case 0:r.setValue(3*e+1);break;case 1:i.setValue(e/3),n.setValue(e%3);break;case 2:t.setValue(e+1)}}if(0===t.getValue().length||0===r.getValue().length||0===n.getValue().length||0===i.getValue().length||t.getValue()[0]<1||r.getValue()[0]+n.getValue()[0]<tV.MIN_ROWS_IN_BARCODE||r.getValue()[0]+n.getValue()[0]>tV.MAX_ROWS_IN_BARCODE)return null;let s=new tZ(t.getValue()[0],r.getValue()[0],n.getValue()[0],i.getValue()[0]);return this.removeIncorrectCodewords(e,s),s}removeIncorrectCodewords(e,t){for(let r=0;r<e.length;r++){let n=e[r];if(null==e[r])continue;let i=n.getValue()%30,s=n.getRowNumber();if(s>t.getRowCount()){e[r]=null;continue}switch(!this._isLeft&&(s+=2),s%3){case 0:3*i+1!==t.getRowCountUpperPart()&&(e[r]=null);break;case 1:(Math.trunc(i/3)!==t.getErrorCorrectionLevel()||i%3!==t.getRowCountLowerPart())&&(e[r]=null);break;case 2:i+1!==t.getColumnCount()&&(e[r]=null)}}}isLeft(){return this._isLeft}toString(){return"IsLeft: "+this._isLeft+"\n"+super.toString()}}class tJ{constructor(e,t){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=e,this.barcodeColumnCount=e.getColumnCount(),this.boundingBox=t,this.detectionResultColumns=Array(this.barcodeColumnCount+2)}getDetectionResultColumns(){let e;this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);let t=tV.MAX_CODEWORDS_IN_BARCODE;do e=t,t=this.adjustRowNumbersAndGetCount();while(t>0&&t<e);return this.detectionResultColumns}adjustIndicatorColumnRowNumbers(e){null!=e&&e.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)}adjustRowNumbersAndGetCount(){let e=this.adjustRowNumbersByRow();if(0===e)return 0;for(let e=1;e<this.barcodeColumnCount+1;e++){let t=this.detectionResultColumns[e].getCodewords();for(let r=0;r<t.length;r++)null!=t[r]&&(t[r].hasValidRowNumber()||this.adjustRowNumbers(e,r,t))}return e}adjustRowNumbersByRow(){return this.adjustRowNumbersFromBothRI(),this.adjustRowNumbersFromLRI()+this.adjustRowNumbersFromRRI()}adjustRowNumbersFromBothRI(){if(null==this.detectionResultColumns[0]||null==this.detectionResultColumns[this.barcodeColumnCount+1])return;let e=this.detectionResultColumns[0].getCodewords(),t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords();for(let r=0;r<e.length;r++)if(null!=e[r]&&null!=t[r]&&e[r].getRowNumber()===t[r].getRowNumber())for(let t=1;t<=this.barcodeColumnCount;t++){let n=this.detectionResultColumns[t].getCodewords()[r];null!=n&&(n.setRowNumber(e[r].getRowNumber()),n.hasValidRowNumber()||(this.detectionResultColumns[t].getCodewords()[r]=null))}}adjustRowNumbersFromRRI(){if(null==this.detectionResultColumns[this.barcodeColumnCount+1])return 0;let e=0,t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords();for(let r=0;r<t.length;r++){if(null==t[r])continue;let n=t[r].getRowNumber(),i=0;for(let t=this.barcodeColumnCount+1;t>0&&i<this.ADJUST_ROW_NUMBER_SKIP;t--){let s=this.detectionResultColumns[t].getCodewords()[r];null!=s&&(i=tJ.adjustRowNumberIfValid(n,i,s),!s.hasValidRowNumber()&&e++)}}return e}adjustRowNumbersFromLRI(){if(null==this.detectionResultColumns[0])return 0;let e=0,t=this.detectionResultColumns[0].getCodewords();for(let r=0;r<t.length;r++){if(null==t[r])continue;let n=t[r].getRowNumber(),i=0;for(let t=1;t<this.barcodeColumnCount+1&&i<this.ADJUST_ROW_NUMBER_SKIP;t++){let s=this.detectionResultColumns[t].getCodewords()[r];null!=s&&(i=tJ.adjustRowNumberIfValid(n,i,s),!s.hasValidRowNumber()&&e++)}}return e}static adjustRowNumberIfValid(e,t,r){return null==r||!r.hasValidRowNumber()&&(r.isValidRowNumber(e)?(r.setRowNumber(e),t=0):++t),t}adjustRowNumbers(e,t,r){if(!this.detectionResultColumns[e-1])return;let n=r[t],i=this.detectionResultColumns[e-1].getCodewords(),s=i;null!=this.detectionResultColumns[e+1]&&(s=this.detectionResultColumns[e+1].getCodewords());let o=Array(14);for(let e of(o[2]=i[t],o[3]=s[t],t>0&&(o[0]=r[t-1],o[4]=i[t-1],o[5]=s[t-1]),t>1&&(o[8]=r[t-2],o[10]=i[t-2],o[11]=s[t-2]),t<r.length-1&&(o[1]=r[t+1],o[6]=i[t+1],o[7]=s[t+1]),t<r.length-2&&(o[9]=r[t+2],o[12]=i[t+2],o[13]=s[t+2]),o))if(tJ.adjustRowNumber(n,e))return}static adjustRowNumber(e,t){return null!=t&&!!t.hasValidRowNumber()&&t.getBucket()===e.getBucket()&&(e.setRowNumber(t.getRowNumber()),!0)}getBarcodeColumnCount(){return this.barcodeColumnCount}getBarcodeRowCount(){return this.barcodeMetadata.getRowCount()}getBarcodeECLevel(){return this.barcodeMetadata.getErrorCorrectionLevel()}setBoundingBox(e){this.boundingBox=e}getBoundingBox(){return this.boundingBox}setDetectionResultColumn(e,t){this.detectionResultColumns[e]=t}getDetectionResultColumn(e){return this.detectionResultColumns[e]}toString(){let e=this.detectionResultColumns[0];null==e&&(e=this.detectionResultColumns[this.barcodeColumnCount+1]);let t=new tK;for(let r=0;r<e.getCodewords().length;r++){t.format("CW %3d:",r);for(let e=0;e<this.barcodeColumnCount+2;e++){if(null==this.detectionResultColumns[e]){t.format("    |   ");continue}let n=this.detectionResultColumns[e].getCodewords()[r];if(null==n){t.format("    |   ");continue}t.format(" %3d|%3d",n.getRowNumber(),n.getValue())}t.format("%n")}return t.toString()}}class t${constructor(e,t,r,n){this.rowNumber=t$.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(t),this.bucket=Math.trunc(r),this.value=Math.trunc(n)}hasValidRowNumber(){return this.isValidRowNumber(this.rowNumber)}isValidRowNumber(e){return e!==t$.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3}setRowNumberAsRowIndicatorColumn(){this.rowNumber=Math.trunc(3*Math.trunc(this.value/30)+Math.trunc(this.bucket/3))}getWidth(){return this.endX-this.startX}getStartX(){return this.startX}getEndX(){return this.endX}getBucket(){return this.bucket}getValue(){return this.value}getRowNumber(){return this.rowNumber}setRowNumber(e){this.rowNumber=e}toString(){return this.rowNumber+"|"+this.value}}t$.BARCODE_ROW_UNKNOWN=-1;class t1{static initialize(){for(let e=0;e<tV.SYMBOL_TABLE.length;e++){let t=tV.SYMBOL_TABLE[e],r=1&t;for(let n=0;n<tV.BARS_IN_MODULE;n++){let i=0;for(;(1&t)===r;)i+=1,t>>=1;r=1&t,t1.RATIOS_TABLE[e]||(t1.RATIOS_TABLE[e]=Array(tV.BARS_IN_MODULE)),t1.RATIOS_TABLE[e][tV.BARS_IN_MODULE-n-1]=Math.fround(i/tV.MODULES_IN_CODEWORD)}}this.bSymbolTableReady=!0}static getDecodedValue(e){let t=t1.getDecodedCodewordValue(t1.sampleBitCounts(e));return -1!==t?t:t1.getClosestDecodedValue(e)}static sampleBitCounts(e){let t=ef.sum(e),r=new Int32Array(tV.BARS_IN_MODULE),n=0,i=0;for(let s=0;s<tV.MODULES_IN_CODEWORD;s++){let o=t/(2*tV.MODULES_IN_CODEWORD)+s*t/tV.MODULES_IN_CODEWORD;i+e[n]<=o&&(i+=e[n],n++),r[n]++}return r}static getDecodedCodewordValue(e){let t=t1.getBitValue(e);return -1===tV.getCodeword(t)?-1:t}static getBitValue(e){let t=0;for(let r=0;r<e.length;r++)for(let n=0;n<e[r];n++)t=t<<1|r%2==0;return Math.trunc(t)}static getClosestDecodedValue(e){let t=ef.sum(e),r=Array(tV.BARS_IN_MODULE);if(t>1)for(let n=0;n<r.length;n++)r[n]=Math.fround(e[n]/t);let n=ew.MAX_VALUE,i=-1;this.bSymbolTableReady||t1.initialize();for(let e=0;e<t1.RATIOS_TABLE.length;e++){let t=0,s=t1.RATIOS_TABLE[e];for(let e=0;e<tV.BARS_IN_MODULE;e++){let i=Math.fround(s[e]-r[e]);if((t+=Math.fround(i*i))>=n)break}t<n&&(n=t,i=tV.SYMBOL_TABLE[e])}return i}}t1.bSymbolTableReady=!1,t1.RATIOS_TABLE=Array(tV.SYMBOL_TABLE.length).map(e=>Array(tV.BARS_IN_MODULE));class t2{constructor(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}getSegmentIndex(){return this.segmentIndex}setSegmentIndex(e){this.segmentIndex=e}getFileId(){return this.fileId}setFileId(e){this.fileId=e}getOptionalData(){return this.optionalData}setOptionalData(e){this.optionalData=e}isLastSegment(){return this.lastSegment}setLastSegment(e){this.lastSegment=e}getSegmentCount(){return this.segmentCount}setSegmentCount(e){this.segmentCount=e}getSender(){return this.sender||null}setSender(e){this.sender=e}getAddressee(){return this.addressee||null}setAddressee(e){this.addressee=e}getFileName(){return this.fileName}setFileName(e){this.fileName=e}getFileSize(){return this.fileSize}setFileSize(e){this.fileSize=e}getChecksum(){return this.checksum}setChecksum(e){this.checksum=e}getTimestamp(){return this.timestamp}setTimestamp(e){this.timestamp=e}}class t0{static parseLong(e,t){return parseInt(e,t)}}class t3 extends D{}t3.kind="NullPointerException";class t8{writeBytes(e){this.writeBytesOffset(e,0,e.length)}writeBytesOffset(e,t,r){if(null==e)throw new t3;if(t<0||t>e.length||r<0||t+r>e.length||t+r<0)throw new L;if(0!==r)for(let n=0;n<r;n++)this.write(e[t+n])}flush(){}close(){}}class t6 extends D{}class t4 extends t8{constructor(e=32){if(super(),this.count=0,e<0)throw new O("Negative initial size: "+e);this.buf=new Uint8Array(e)}ensureCapacity(e){e-this.buf.length>0&&this.grow(e)}grow(e){let t=this.buf.length<<1;if(t-e<0&&(t=e),t<0){if(e<0)throw new t6;t=k.MAX_VALUE}this.buf=v.copyOfUint8Array(this.buf,t)}write(e){this.ensureCapacity(this.count+1),this.buf[this.count]=e,this.count+=1}writeBytesOffset(e,t,r){if(t<0||t>e.length||r<0||t+r-e.length>0)throw new L;this.ensureCapacity(this.count+r),P.arraycopy(e,t,this.buf,this.count,r),this.count+=r}writeTo(e){e.writeBytesOffset(this.buf,0,this.count)}reset(){this.count=0}toByteArray(){return v.copyOfUint8Array(this.buf,this.count)}size(){return this.count}toString(e){return e?"string"==typeof e?this.toString_string(e):this.toString_number(e):this.toString_void()}toString_void(){return new String(this.buf).toString()}toString_string(e){return new String(this.buf).toString()}toString_number(e){return new String(this.buf).toString()}close(){}}function t7(){if("undefined"!=typeof window)return window.BigInt||null;if(void 0!==r.g)return r.g.BigInt||null;if("undefined"!=typeof self)return self.BigInt||null;throw Error("Can't search globals for BigInt!")}function t5(e){if(void 0===t&&(t=t7()),null===t)throw Error("BigInt is not supported!");return t(e)}(c=p||(p={}))[c.ALPHA=0]="ALPHA",c[c.LOWER=1]="LOWER",c[c.MIXED=2]="MIXED",c[c.PUNCT=3]="PUNCT",c[c.ALPHA_SHIFT=4]="ALPHA_SHIFT",c[c.PUNCT_SHIFT=5]="PUNCT_SHIFT";class t9{static decode(e,t){let r=new z(""),n=H.ISO8859_1;r.enableDecoding(n);let i=1,s=e[i++],o=new t2;for(;i<e[0];){switch(s){case t9.TEXT_COMPACTION_MODE_LATCH:i=t9.textCompaction(e,i,r);break;case t9.BYTE_COMPACTION_MODE_LATCH:case t9.BYTE_COMPACTION_MODE_LATCH_6:i=t9.byteCompaction(s,e,n,i,r);break;case t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:r.append(e[i++]);break;case t9.NUMERIC_COMPACTION_MODE_LATCH:i=t9.numericCompaction(e,i,r);break;case t9.ECI_CHARSET:H.getCharacterSetECIByValue(e[i++]);break;case t9.ECI_GENERAL_PURPOSE:i+=2;break;case t9.ECI_USER_DEFINED:i++;break;case t9.BEGIN_MACRO_PDF417_CONTROL_BLOCK:i=t9.decodeMacroBlock(e,i,o);break;case t9.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t9.MACRO_PDF417_TERMINATOR:throw new U;default:i--,i=t9.textCompaction(e,i,r)}if(i<e.length)s=e[i++];else throw U.getFormatInstance()}if(0===r.length())throw U.getFormatInstance();let a=new es(null,r.toString(),null,t);return a.setOther(o),a}static decodeMacroBlock(e,t,r){if(t+t9.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw U.getFormatInstance();let n=new Int32Array(t9.NUMBER_OF_SEQUENCE_CODEWORDS);for(let r=0;r<t9.NUMBER_OF_SEQUENCE_CODEWORDS;r++,t++)n[r]=e[t];r.setSegmentIndex(k.parseInt(t9.decodeBase900toBase10(n,t9.NUMBER_OF_SEQUENCE_CODEWORDS)));let i=new z;t=t9.textCompaction(e,t,i),r.setFileId(i.toString());let s=-1;for(e[t]===t9.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(s=t+1);t<e[0];)switch(e[t]){case t9.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(e[++t]){case t9.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:let n=new z;t=t9.textCompaction(e,t+1,n),r.setFileName(n.toString());break;case t9.MACRO_PDF417_OPTIONAL_FIELD_SENDER:let i=new z;t=t9.textCompaction(e,t+1,i),r.setSender(i.toString());break;case t9.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:let s=new z;t=t9.textCompaction(e,t+1,s),r.setAddressee(s.toString());break;case t9.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:let o=new z;t=t9.numericCompaction(e,t+1,o),r.setSegmentCount(k.parseInt(o.toString()));break;case t9.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:let a=new z;t=t9.numericCompaction(e,t+1,a),r.setTimestamp(t0.parseLong(a.toString()));break;case t9.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:let l=new z;t=t9.numericCompaction(e,t+1,l),r.setChecksum(k.parseInt(l.toString()));break;case t9.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:let h=new z;t=t9.numericCompaction(e,t+1,h),r.setFileSize(t0.parseLong(h.toString()));break;default:throw U.getFormatInstance()}break;case t9.MACRO_PDF417_TERMINATOR:t++,r.setLastSegment(!0);break;default:throw U.getFormatInstance()}if(-1!==s){let n=t-s;r.isLastSegment()&&n--,r.setOptionalData(v.copyOfRange(e,s,s+n))}return t}static textCompaction(e,t,r){let n=new Int32Array((e[0]-t)*2),i=new Int32Array((e[0]-t)*2),s=0,o=!1;for(;t<e[0]&&!o;){let r=e[t++];if(r<t9.TEXT_COMPACTION_MODE_LATCH)n[s]=r/30,n[s+1]=r%30,s+=2;else switch(r){case t9.TEXT_COMPACTION_MODE_LATCH:n[s++]=t9.TEXT_COMPACTION_MODE_LATCH;break;case t9.BYTE_COMPACTION_MODE_LATCH:case t9.BYTE_COMPACTION_MODE_LATCH_6:case t9.NUMERIC_COMPACTION_MODE_LATCH:case t9.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t9.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t9.MACRO_PDF417_TERMINATOR:t--,o=!0;break;case t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n[s]=t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,r=e[t++],i[s]=r,s++}}return t9.decodeTextCompaction(n,i,s,r),t}static decodeTextCompaction(e,t,r,n){let i=p.ALPHA,s=p.ALPHA,o=0;for(;o<r;){let r=e[o],a="";switch(i){case p.ALPHA:if(r<26)a=String.fromCharCode(65+r);else switch(r){case 26:a=" ";break;case t9.LL:i=p.LOWER;break;case t9.ML:i=p.MIXED;break;case t9.PS:s=i,i=p.PUNCT_SHIFT;break;case t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(t[o]);break;case t9.TEXT_COMPACTION_MODE_LATCH:i=p.ALPHA}break;case p.LOWER:if(r<26)a=String.fromCharCode(97+r);else switch(r){case 26:a=" ";break;case t9.AS:s=i,i=p.ALPHA_SHIFT;break;case t9.ML:i=p.MIXED;break;case t9.PS:s=i,i=p.PUNCT_SHIFT;break;case t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(t[o]);break;case t9.TEXT_COMPACTION_MODE_LATCH:i=p.ALPHA}break;case p.MIXED:if(r<t9.PL)a=t9.MIXED_CHARS[r];else switch(r){case t9.PL:i=p.PUNCT;break;case 26:a=" ";break;case t9.LL:i=p.LOWER;break;case t9.AL:i=p.ALPHA;break;case t9.PS:s=i,i=p.PUNCT_SHIFT;break;case t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(t[o]);break;case t9.TEXT_COMPACTION_MODE_LATCH:i=p.ALPHA}break;case p.PUNCT:if(r<t9.PAL)a=t9.PUNCT_CHARS[r];else switch(r){case t9.PAL:i=p.ALPHA;break;case t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(t[o]);break;case t9.TEXT_COMPACTION_MODE_LATCH:i=p.ALPHA}break;case p.ALPHA_SHIFT:if(i=s,r<26)a=String.fromCharCode(65+r);else switch(r){case 26:a=" ";break;case t9.TEXT_COMPACTION_MODE_LATCH:i=p.ALPHA}break;case p.PUNCT_SHIFT:if(i=s,r<t9.PAL)a=t9.PUNCT_CHARS[r];else switch(r){case t9.PAL:i=p.ALPHA;break;case t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(t[o]);break;case t9.TEXT_COMPACTION_MODE_LATCH:i=p.ALPHA}}""!==a&&n.append(a),o++}}static byteCompaction(e,t,r,n,i){let s=new t4,o=0,a=0,l=!1;switch(e){case t9.BYTE_COMPACTION_MODE_LATCH:let h=new Int32Array(6),u=t[n++];for(;n<t[0]&&!l;)switch(h[o++]=u,a=900*a+u,u=t[n++]){case t9.TEXT_COMPACTION_MODE_LATCH:case t9.BYTE_COMPACTION_MODE_LATCH:case t9.NUMERIC_COMPACTION_MODE_LATCH:case t9.BYTE_COMPACTION_MODE_LATCH_6:case t9.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t9.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t9.MACRO_PDF417_TERMINATOR:n--,l=!0;break;default:if(o%5==0&&o>0){for(let e=0;e<6;++e)s.write(Number(t5(a)>>t5(8*(5-e))));a=0,o=0}}n===t[0]&&u<t9.TEXT_COMPACTION_MODE_LATCH&&(h[o++]=u);for(let e=0;e<o;e++)s.write(h[e]);break;case t9.BYTE_COMPACTION_MODE_LATCH_6:for(;n<t[0]&&!l;){let e=t[n++];if(e<t9.TEXT_COMPACTION_MODE_LATCH)o++,a=900*a+e;else switch(e){case t9.TEXT_COMPACTION_MODE_LATCH:case t9.BYTE_COMPACTION_MODE_LATCH:case t9.NUMERIC_COMPACTION_MODE_LATCH:case t9.BYTE_COMPACTION_MODE_LATCH_6:case t9.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t9.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t9.MACRO_PDF417_TERMINATOR:n--,l=!0}if(o%5==0&&o>0){for(let e=0;e<6;++e)s.write(Number(t5(a)>>t5(8*(5-e))));a=0,o=0}}}return i.append(X.decode(s.toByteArray(),r)),n}static numericCompaction(e,t,r){let n=0,i=!1,s=new Int32Array(t9.MAX_NUMERIC_CODEWORDS);for(;t<e[0]&&!i;){let o=e[t++];if(t===e[0]&&(i=!0),o<t9.TEXT_COMPACTION_MODE_LATCH)s[n]=o,n++;else switch(o){case t9.TEXT_COMPACTION_MODE_LATCH:case t9.BYTE_COMPACTION_MODE_LATCH:case t9.BYTE_COMPACTION_MODE_LATCH_6:case t9.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t9.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t9.MACRO_PDF417_TERMINATOR:t--,i=!0}(n%t9.MAX_NUMERIC_CODEWORDS==0||o===t9.NUMERIC_COMPACTION_MODE_LATCH||i)&&n>0&&(r.append(t9.decodeBase900toBase10(s,n)),n=0)}return t}static decodeBase900toBase10(e,t){let r=t5(0);for(let n=0;n<t;n++)r+=t9.EXP900[t-n-1]*t5(e[n]);let n=r.toString();if("1"!==n.charAt(0))throw new U;return n.substring(1)}}t9.TEXT_COMPACTION_MODE_LATCH=900,t9.BYTE_COMPACTION_MODE_LATCH=901,t9.NUMERIC_COMPACTION_MODE_LATCH=902,t9.BYTE_COMPACTION_MODE_LATCH_6=924,t9.ECI_USER_DEFINED=925,t9.ECI_GENERAL_PURPOSE=926,t9.ECI_CHARSET=927,t9.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,t9.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,t9.MACRO_PDF417_TERMINATOR=922,t9.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,t9.MAX_NUMERIC_CODEWORDS=15,t9.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,t9.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,t9.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,t9.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,t9.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,t9.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,t9.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,t9.PL=25,t9.LL=27,t9.AS=27,t9.ML=28,t9.AL=28,t9.PS=29,t9.PAL=29,t9.PUNCT_CHARS=";<>@[\\]_`~!\r	,:\n-.$/\"|*()?{}'",t9.MIXED_CHARS="0123456789&\r	,:#-.$/+%*=^",t9.EXP900=t7()?function(){let e=[];e[0]=t5(1);let t=t5(900);e[1]=t;for(let r=2;r<16;r++)e[r]=e[r-1]*t;return e}():[],t9.NUMBER_OF_SEQUENCE_CODEWORDS=2;class re{constructor(){}static decode(e,t,r,n,i,s,o){let a,l=new tY(e,t,r,n,i),h=null,u=null;for(let r=!0;;r=!1){if(null!=t&&(h=re.getRowIndicatorColumn(e,l,t,!0,s,o)),null!=n&&(u=re.getRowIndicatorColumn(e,l,n,!1,s,o)),null==(a=re.merge(h,u)))throw Z.getNotFoundInstance();let i=a.getBoundingBox();if(r&&null!=i&&(i.getMinY()<l.getMinY()||i.getMaxY()>l.getMaxY()))l=i;else break}a.setBoundingBox(l);let d=a.getBarcodeColumnCount()+1;a.setDetectionResultColumn(0,h),a.setDetectionResultColumn(d,u);let c=null!=h;for(let t=1;t<=d;t++){let r,n=c?t:d-t;if(void 0!==a.getDetectionResultColumn(n))continue;r=0===n||n===d?new tj(l,0===n):new tq(l),a.setDetectionResultColumn(n,r);let i=-1,h=-1;for(let t=l.getMinY();t<=l.getMaxY();t++){if((i=re.getStartColumn(a,n,t,c))<0||i>l.getMaxX()){if(-1===h)continue;i=h}let u=re.detectCodeword(e,l.getMinX(),l.getMaxX(),c,i,t,s,o);null!=u&&(r.setCodeword(t,u),h=i,s=Math.min(s,u.getWidth()),o=Math.max(o,u.getWidth()))}}return re.createDecoderResult(a)}static merge(e,t){if(null==e&&null==t)return null;let r=re.getBarcodeMetadata(e,t);return null==r?null:new tJ(r,tY.merge(re.adjustBoundingBox(e),re.adjustBoundingBox(t)))}static adjustBoundingBox(e){if(null==e)return null;let t=e.getRowHeights();if(null==t)return null;let r=re.getMax(t),n=0;for(let e of t)if(n+=r-e,e>0)break;let i=e.getCodewords();for(let e=0;n>0&&null==i[e];e++)n--;let s=0;for(let e=t.length-1;e>=0&&(s+=r-t[e],!(t[e]>0));e--);for(let e=i.length-1;s>0&&null==i[e];e--)s--;return e.getBoundingBox().addMissingRows(n,s,e.isLeft())}static getMax(e){let t=-1;for(let r of e)t=Math.max(t,r);return t}static getBarcodeMetadata(e,t){let r,n;return null==e||null==(r=e.getBarcodeMetadata())?null==t?null:t.getBarcodeMetadata():null==t||null==(n=t.getBarcodeMetadata())?r:r.getColumnCount()!==n.getColumnCount()&&r.getErrorCorrectionLevel()!==n.getErrorCorrectionLevel()&&r.getRowCount()!==n.getRowCount()?null:r}static getRowIndicatorColumn(e,t,r,n,i,s){let o=new tj(t,n);for(let a=0;a<2;a++){let l=0===a?1:-1,h=Math.trunc(Math.trunc(r.getX()));for(let a=Math.trunc(Math.trunc(r.getY()));a<=t.getMaxY()&&a>=t.getMinY();a+=l){let t=re.detectCodeword(e,0,e.getWidth(),n,h,a,i,s);null!=t&&(o.setCodeword(a,t),h=n?t.getStartX():t.getEndX())}}return o}static adjustCodewordCount(e,t){let r=t[0][1],n=r.getValue(),i=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-re.getNumberOfECCodeWords(e.getBarcodeECLevel());if(0===n.length){if(i<1||i>tV.MAX_CODEWORDS_IN_BARCODE)throw Z.getNotFoundInstance();r.setValue(i)}else n[0]!==i&&r.setValue(i)}static createDecoderResult(e){let t=re.createBarcodeMatrix(e);re.adjustCodewordCount(e,t);let r=[],n=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),i=[],s=[];for(let o=0;o<e.getBarcodeRowCount();o++)for(let a=0;a<e.getBarcodeColumnCount();a++){let l=t[o][a+1].getValue(),h=o*e.getBarcodeColumnCount()+a;0===l.length?r.push(h):1===l.length?n[h]=l[0]:(s.push(h),i.push(l))}let o=Array(i.length);for(let e=0;e<o.length;e++)o[e]=i[e];return re.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),n,tV.toIntArray(r),tV.toIntArray(s),o)}static createDecoderResultFromAmbiguousValues(e,t,r,n,i){let s=new Int32Array(n.length),o=100;for(;o-- >0;){for(let e=0;e<s.length;e++)t[n[e]]=i[e][s[e]];try{return re.decodeCodewords(t,e,r)}catch(e){if(!(e instanceof B))throw e}if(0===s.length)break;for(let e=0;e<s.length;e++)if(s[e]<i[e].length-1){s[e]++;break}else if(s[e]=0,e===s.length-1)throw B.getChecksumInstance()}throw B.getChecksumInstance()}static createBarcodeMatrix(e){let t=Array.from({length:e.getBarcodeRowCount()},()=>Array(e.getBarcodeColumnCount()+2));for(let e=0;e<t.length;e++)for(let r=0;r<t[e].length;r++)t[e][r]=new tQ;let r=0;for(let n of e.getDetectionResultColumns()){if(null!=n){for(let e of n.getCodewords())if(null!=e){let n=e.getRowNumber();if(n>=0){if(n>=t.length)continue;t[n][r].setValue(e.getValue())}}}r++}return t}static isValidBarcodeColumn(e,t){return t>=0&&t<=e.getBarcodeColumnCount()+1}static getStartColumn(e,t,r,n){let i=n?1:-1,s=null;if(re.isValidBarcodeColumn(e,t-i)&&(s=e.getDetectionResultColumn(t-i).getCodeword(r)),null!=s)return n?s.getEndX():s.getStartX();if(null!=(s=e.getDetectionResultColumn(t).getCodewordNearby(r)))return n?s.getStartX():s.getEndX();if(re.isValidBarcodeColumn(e,t-i)&&(s=e.getDetectionResultColumn(t-i).getCodewordNearby(r)),null!=s)return n?s.getEndX():s.getStartX();let o=0;for(;re.isValidBarcodeColumn(e,t-i);){for(let r of(t-=i,e.getDetectionResultColumn(t).getCodewords()))if(null!=r)return(n?r.getEndX():r.getStartX())+i*o*(r.getEndX()-r.getStartX());o++}return n?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()}static detectCodeword(e,t,r,n,i,s,o,a){let l;i=re.adjustCodewordStartColumn(e,t,r,n,i,s);let h=re.getModuleBitCount(e,t,r,n,i,s);if(null==h)return null;let u=ef.sum(h);if(n)l=i+u;else{for(let e=0;e<h.length/2;e++){let t=h[e];h[e]=h[h.length-1-e],h[h.length-1-e]=t}i=(l=i)-u}if(!re.checkCodewordSkew(u,o,a))return null;let d=t1.getDecodedValue(h),c=tV.getCodeword(d);return -1===c?null:new t$(i,l,re.getCodewordBucketNumber(d),c)}static getModuleBitCount(e,t,r,n,i,s){let o=i,a=new Int32Array(8),l=0,h=n?1:-1,u=n;for(;(n?o<r:o>=t)&&l<a.length;)e.get(o,s)===u?(a[l]++,o+=h):(l++,u=!u);return l===a.length||o===(n?r:t)&&l===a.length-1?a:null}static getNumberOfECCodeWords(e){return 2<<e}static adjustCodewordStartColumn(e,t,r,n,i,s){let o=i,a=n?-1:1;for(let l=0;l<2;l++){for(;(n?o>=t:o<r)&&n===e.get(o,s);){if(Math.abs(i-o)>re.CODEWORD_SKEW_SIZE)return i;o+=a}a=-a,n=!n}return o}static checkCodewordSkew(e,t,r){return t-re.CODEWORD_SKEW_SIZE<=e&&e<=r+re.CODEWORD_SKEW_SIZE}static decodeCodewords(e,t,r){if(0===e.length)throw U.getFormatInstance();let n=1<<t+1,i=re.correctErrors(e,r,n);re.verifyCodewordCount(e,n);let s=t9.decode(e,""+t);return s.setErrorsCorrected(i),s.setErasures(r.length),s}static correctErrors(e,t,r){if(null!=t&&t.length>r/2+re.MAX_ERRORS||r<0||r>re.MAX_EC_CODEWORDS)throw B.getChecksumInstance();return re.errorCorrection.decode(e,r,t)}static verifyCodewordCount(e,t){if(e.length<4)throw U.getFormatInstance();let r=e[0];if(r>e.length)throw U.getFormatInstance();if(0===r)if(t<e.length)e[0]=e.length-t;else throw U.getFormatInstance()}static getBitCountForCodeword(e){let t=new Int32Array(8),r=0,n=t.length-1;for(;!((1&e)!==r&&(r=1&e,--n<0));)t[n]++,e>>=1;return t}static getCodewordBucketNumber(e){return e instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(e):this.getCodewordBucketNumber_number(e)}static getCodewordBucketNumber_number(e){return re.getCodewordBucketNumber(re.getBitCountForCodeword(e))}static getCodewordBucketNumber_Int32Array(e){return(e[0]-e[2]+e[4]-e[6]+9)%9}static toString(e){let t=new tK;for(let r=0;r<e.length;r++){t.format("Row %2d: ",r);for(let n=0;n<e[r].length;n++){let i=e[r][n];0===i.getValue().length?t.format("        ",null):t.format("%4d(%2d)",i.getValue()[0],i.getConfidence(i.getValue()[0]))}t.format("%n")}return t.toString()}}re.CODEWORD_SKEW_SIZE=2,re.MAX_ERRORS=3,re.MAX_EC_CODEWORDS=512,re.errorCorrection=new tz;class rt{decode(e,t=null){let r=rt.decode(e,t,!1);if(null==r||0===r.length||null==r[0])throw Z.getNotFoundInstance();return r[0]}decodeMultiple(e,t=null){try{return rt.decode(e,t,!0)}catch(e){if(e instanceof U||e instanceof B)throw Z.getNotFoundInstance();throw e}}static decode(e,t,r){let n=[],i=tH.detectMultiple(e,t,r);for(let e of i.getPoints()){let t=re.decode(i.getBits(),e[4],e[5],e[6],e[7],rt.getMinCodewordWidth(e),rt.getMaxCodewordWidth(e)),r=new er(t.getText(),t.getRawBytes(),void 0,e,en.PDF_417);r.putMetadata(ei.ERROR_CORRECTION_LEVEL,t.getECLevel());let s=t.getOther();null!=s&&r.putMetadata(ei.PDF417_EXTRA_METADATA,s),n.push(r)}return n.map(e=>e)}static getMaxWidth(e,t){return null==e||null==t?0:Math.trunc(Math.abs(e.getX()-t.getX()))}static getMinWidth(e,t){return null==e||null==t?k.MAX_VALUE:Math.trunc(Math.abs(e.getX()-t.getX()))}static getMaxCodewordWidth(e){return Math.floor(Math.max(Math.max(rt.getMaxWidth(e[0],e[4]),rt.getMaxWidth(e[6],e[2])*tV.MODULES_IN_CODEWORD/tV.MODULES_IN_STOP_PATTERN),Math.max(rt.getMaxWidth(e[1],e[5]),rt.getMaxWidth(e[7],e[3])*tV.MODULES_IN_CODEWORD/tV.MODULES_IN_STOP_PATTERN)))}static getMinCodewordWidth(e){return Math.floor(Math.min(Math.min(rt.getMinWidth(e[0],e[4]),rt.getMinWidth(e[6],e[2])*tV.MODULES_IN_CODEWORD/tV.MODULES_IN_STOP_PATTERN),Math.min(rt.getMinWidth(e[1],e[5]),rt.getMinWidth(e[7],e[3])*tV.MODULES_IN_CODEWORD/tV.MODULES_IN_STOP_PATTERN)))}reset(){}}class rr extends D{}rr.kind="ReaderException";class rn{constructor(e,t){this.verbose=!0===e,t&&this.setHints(t)}decode(e,t){return t&&this.setHints(t),this.decodeInternal(e)}decodeWithState(e){return(null===this.readers||void 0===this.readers)&&this.setHints(null),this.decodeInternal(e)}setHints(e){this.hints=e;let t=null!=e&&!0===e.get(V.TRY_HARDER),r=null==e?null:e.get(V.POSSIBLE_FORMATS),n=[];if(null!=r){let i=r.some(e=>e===en.UPC_A||e===en.UPC_E||e===en.EAN_13||e===en.EAN_8||e===en.CODABAR||e===en.CODE_39||e===en.CODE_93||e===en.CODE_128||e===en.ITF||e===en.RSS_14||e===en.RSS_EXPANDED);i&&!t&&n.push(new ta(e,this.verbose)),r.includes(en.QR_CODE)&&n.push(new tx),r.includes(en.DATA_MATRIX)&&n.push(new tE),r.includes(en.AZTEC)&&n.push(new eN),r.includes(en.PDF_417)&&n.push(new rt),i&&t&&n.push(new ta(e,this.verbose))}0===n.length&&(t||n.push(new ta(e,this.verbose)),n.push(new tx),n.push(new tE),n.push(new eN),n.push(new rt),t&&n.push(new ta(e,this.verbose))),this.readers=n}reset(){if(null!==this.readers)for(let e of this.readers)e.reset()}decodeInternal(e){if(null===this.readers)throw new rr("No readers where selected, nothing can be read.");for(let t of this.readers)try{return t.decode(e,this.hints)}catch(e){if(e instanceof rr)continue}throw new Z("No MultiFormat Readers were able to detect the code.")}}class ri extends et{constructor(e=null,t=500){let r=new rn;r.setHints(e),super(r,t)}decodeBitmap(e){return this.reader.decodeWithState(e)}}class rs extends et{constructor(e=500){super(new rt,e)}}class ro extends et{constructor(e=500){super(new tx,e)}}(g=T||(T={}))[g.ERROR_CORRECTION=0]="ERROR_CORRECTION",g[g.CHARACTER_SET=1]="CHARACTER_SET",g[g.DATA_MATRIX_SHAPE=2]="DATA_MATRIX_SHAPE",g[g.MIN_SIZE=3]="MIN_SIZE",g[g.MAX_SIZE=4]="MAX_SIZE",g[g.MARGIN=5]="MARGIN",g[g.PDF417_COMPACT=6]="PDF417_COMPACT",g[g.PDF417_COMPACTION=7]="PDF417_COMPACTION",g[g.PDF417_DIMENSIONS=8]="PDF417_DIMENSIONS",g[g.AZTEC_LAYERS=9]="AZTEC_LAYERS",g[g.QR_VERSION=10]="QR_VERSION";var ra=T;class rl{constructor(e){this.field=e,this.cachedGenerators=[],this.cachedGenerators.push(new ea(e,Int32Array.from([1])))}buildGenerator(e){let t=this.cachedGenerators;if(e>=t.length){let r=t[t.length-1],n=this.field;for(let i=t.length;i<=e;i++){let e=r.multiply(new ea(n,Int32Array.from([1,n.exp(i-1+n.getGeneratorBase())])));t.push(e),r=e}}return t[e]}encode(e,t){if(0===t)throw new O("No error correction bytes");let r=e.length-t;if(r<=0)throw new O("No data bytes provided");let n=this.buildGenerator(t),i=new Int32Array(r);P.arraycopy(e,0,i,0,r);let s=new ea(this.field,i),o=(s=s.multiplyByMonomial(t,1)).divide(n)[1].getCoefficients(),a=t-o.length;for(let t=0;t<a;t++)e[r+t]=0;P.arraycopy(o,0,e,r+a,o.length)}}class rh{constructor(){}static applyMaskPenaltyRule1(e){return rh.applyMaskPenaltyRule1Internal(e,!0)+rh.applyMaskPenaltyRule1Internal(e,!1)}static applyMaskPenaltyRule2(e){let t=0,r=e.getArray(),n=e.getWidth(),i=e.getHeight();for(let e=0;e<i-1;e++){let i=r[e];for(let s=0;s<n-1;s++){let n=i[s];n===i[s+1]&&n===r[e+1][s]&&n===r[e+1][s+1]&&t++}}return rh.N2*t}static applyMaskPenaltyRule3(e){let t=0,r=e.getArray(),n=e.getWidth(),i=e.getHeight();for(let e=0;e<i;e++)for(let s=0;s<n;s++){let o=r[e];s+6<n&&1===o[s]&&0===o[s+1]&&1===o[s+2]&&1===o[s+3]&&1===o[s+4]&&0===o[s+5]&&1===o[s+6]&&(rh.isWhiteHorizontal(o,s-4,s)||rh.isWhiteHorizontal(o,s+7,s+11))&&t++,e+6<i&&1===r[e][s]&&0===r[e+1][s]&&1===r[e+2][s]&&1===r[e+3][s]&&1===r[e+4][s]&&0===r[e+5][s]&&1===r[e+6][s]&&(rh.isWhiteVertical(r,s,e-4,e)||rh.isWhiteVertical(r,s,e+7,e+11))&&t++}return t*rh.N3}static isWhiteHorizontal(e,t,r){t=Math.max(t,0),r=Math.min(r,e.length);for(let n=t;n<r;n++)if(1===e[n])return!1;return!0}static isWhiteVertical(e,t,r,n){r=Math.max(r,0),n=Math.min(n,e.length);for(let i=r;i<n;i++)if(1===e[i][t])return!1;return!0}static applyMaskPenaltyRule4(e){let t=0,r=e.getArray(),n=e.getWidth(),i=e.getHeight();for(let e=0;e<i;e++){let i=r[e];for(let e=0;e<n;e++)1===i[e]&&t++}let s=e.getHeight()*e.getWidth();return Math.floor(10*Math.abs(2*t-s)/s)*rh.N4}static getDataMaskBit(e,t,r){let n,i;switch(e){case 0:n=r+t&1;break;case 1:n=1&r;break;case 2:n=t%3;break;case 3:n=(r+t)%3;break;case 4:n=Math.floor(r/2)+Math.floor(t/3)&1;break;case 5:n=(1&(i=r*t))+i%3;break;case 6:n=(1&(i=r*t))+i%3&1;break;case 7:n=(i=r*t)%3+(r+t&1)&1;break;default:throw new O("Invalid mask pattern: "+e)}return 0===n}static applyMaskPenaltyRule1Internal(e,t){let r=0,n=t?e.getHeight():e.getWidth(),i=t?e.getWidth():e.getHeight(),s=e.getArray();for(let e=0;e<n;e++){let n=0,o=-1;for(let a=0;a<i;a++){let i=t?s[e][a]:s[a][e];i===o?n++:(n>=5&&(r+=rh.N1+(n-5)),n=1,o=i)}n>=5&&(r+=rh.N1+(n-5))}return r}}rh.N1=3,rh.N2=3,rh.N3=40,rh.N4=10;class ru{constructor(e,t){this.width=e,this.height=t;let r=Array(t);for(let n=0;n!==t;n++)r[n]=new Uint8Array(e);this.bytes=r}getHeight(){return this.height}getWidth(){return this.width}get(e,t){return this.bytes[t][e]}getArray(){return this.bytes}setNumber(e,t,r){this.bytes[t][e]=r}setBoolean(e,t,r){this.bytes[t][e]=+!!r}clear(e){for(let t of this.bytes)v.fill(t,e)}equals(e){if(!(e instanceof ru)||this.width!==e.width||this.height!==e.height)return!1;for(let t=0,r=this.height;t<r;++t){let r=this.bytes[t],n=e.bytes[t];for(let e=0,t=this.width;e<t;++e)if(r[e]!==n[e])return!1}return!0}toString(){let e=new z;for(let t=0,r=this.height;t<r;++t){let r=this.bytes[t];for(let t=0,n=this.width;t<n;++t)switch(r[t]){case 0:e.append(" 0");break;case 1:e.append(" 1");break;default:e.append("  ")}e.append("\n")}return e.toString()}}class rd{constructor(){this.maskPattern=-1}getMode(){return this.mode}getECLevel(){return this.ecLevel}getVersion(){return this.version}getMaskPattern(){return this.maskPattern}getMatrix(){return this.matrix}toString(){let e=new z;return e.append("<<\n"),e.append(" mode: "),e.append(this.mode?this.mode.toString():"null"),e.append("\n ecLevel: "),e.append(this.ecLevel?this.ecLevel.toString():"null"),e.append("\n version: "),e.append(this.version?this.version.toString():"null"),e.append("\n maskPattern: "),e.append(this.maskPattern.toString()),this.matrix?(e.append("\n matrix:\n"),e.append(this.matrix.toString())):e.append("\n matrix: null\n"),e.append(">>\n"),e.toString()}setMode(e){this.mode=e}setECLevel(e){this.ecLevel=e}setVersion(e){this.version=e}setMaskPattern(e){this.maskPattern=e}setMatrix(e){this.matrix=e}static isValidMaskPattern(e){return e>=0&&e<rd.NUM_MASK_PATTERNS}}rd.NUM_MASK_PATTERNS=8;class rc extends D{}rc.kind="WriterException";class rg{constructor(){}static clearMatrix(e){e.clear(255)}static buildMatrix(e,t,r,n,i){rg.clearMatrix(i),rg.embedBasicPatterns(r,i),rg.embedTypeInfo(t,n,i),rg.maybeEmbedVersionInfo(r,i),rg.embedDataBits(e,n,i)}static embedBasicPatterns(e,t){rg.embedPositionDetectionPatternsAndSeparators(t),rg.embedDarkDotAtLeftBottomCorner(t),rg.maybeEmbedPositionAdjustmentPatterns(e,t),rg.embedTimingPatterns(t)}static embedTypeInfo(e,t,r){let n=new x;rg.makeTypeInfoBits(e,t,n);for(let e=0,t=n.getSize();e<t;++e){let t=n.get(n.getSize()-1-e),i=rg.TYPE_INFO_COORDINATES[e],s=i[0],o=i[1];if(r.setBoolean(s,o,t),e<8){let n=r.getWidth()-e-1;r.setBoolean(n,8,t)}else{let n=r.getHeight()-7+(e-8);r.setBoolean(8,n,t)}}}static maybeEmbedVersionInfo(e,t){if(7>e.getVersionNumber())return;let r=new x;rg.makeVersionInfoBits(e,r);let n=17;for(let e=0;e<6;++e)for(let i=0;i<3;++i){let s=r.get(n);n--,t.setBoolean(e,t.getHeight()-11+i,s),t.setBoolean(t.getHeight()-11+i,e,s)}}static embedDataBits(e,t,r){let n=0,i=-1,s=r.getWidth()-1,o=r.getHeight()-1;for(;s>0;){for(6===s&&(s-=1);o>=0&&o<r.getHeight();){for(let i=0;i<2;++i){let a,l=s-i;rg.isEmpty(r.get(l,o))&&(n<e.getSize()?(a=e.get(n),++n):a=!1,255!==t&&rh.getDataMaskBit(t,l,o)&&(a=!a),r.setBoolean(l,o,a))}o+=i}o+=i=-i,s-=2}if(n!==e.getSize())throw new rc("Not all bits consumed: "+n+"/"+e.getSize())}static findMSBSet(e){return 32-k.numberOfLeadingZeros(e)}static calculateBCHCode(e,t){if(0===t)throw new O("0 polynomial");let r=rg.findMSBSet(t);for(e<<=r-1;rg.findMSBSet(e)>=r;)e^=t<<rg.findMSBSet(e)-r;return e}static makeTypeInfoBits(e,t,r){if(!rd.isValidMaskPattern(t))throw new rc("Invalid mask pattern");let n=e.getBits()<<3|t;r.appendBits(n,5);let i=rg.calculateBCHCode(n,rg.TYPE_INFO_POLY);r.appendBits(i,10);let s=new x;if(s.appendBits(rg.TYPE_INFO_MASK_PATTERN,15),r.xor(s),15!==r.getSize())throw new rc("should not happen but we got: "+r.getSize())}static makeVersionInfoBits(e,t){t.appendBits(e.getVersionNumber(),6);let r=rg.calculateBCHCode(e.getVersionNumber(),rg.VERSION_INFO_POLY);if(t.appendBits(r,12),18!==t.getSize())throw new rc("should not happen but we got: "+t.getSize())}static isEmpty(e){return 255===e}static embedTimingPatterns(e){for(let t=8;t<e.getWidth()-8;++t){let r=(t+1)%2;rg.isEmpty(e.get(t,6))&&e.setNumber(t,6,r),rg.isEmpty(e.get(6,t))&&e.setNumber(6,t,r)}}static embedDarkDotAtLeftBottomCorner(e){if(0===e.get(8,e.getHeight()-8))throw new rc;e.setNumber(8,e.getHeight()-8,1)}static embedHorizontalSeparationPattern(e,t,r){for(let n=0;n<8;++n){if(!rg.isEmpty(r.get(e+n,t)))throw new rc;r.setNumber(e+n,t,0)}}static embedVerticalSeparationPattern(e,t,r){for(let n=0;n<7;++n){if(!rg.isEmpty(r.get(e,t+n)))throw new rc;r.setNumber(e,t+n,0)}}static embedPositionAdjustmentPattern(e,t,r){for(let n=0;n<5;++n){let i=rg.POSITION_ADJUSTMENT_PATTERN[n];for(let s=0;s<5;++s)r.setNumber(e+s,t+n,i[s])}}static embedPositionDetectionPattern(e,t,r){for(let n=0;n<7;++n){let i=rg.POSITION_DETECTION_PATTERN[n];for(let s=0;s<7;++s)r.setNumber(e+s,t+n,i[s])}}static embedPositionDetectionPatternsAndSeparators(e){let t=rg.POSITION_DETECTION_PATTERN[0].length;rg.embedPositionDetectionPattern(0,0,e),rg.embedPositionDetectionPattern(e.getWidth()-t,0,e),rg.embedPositionDetectionPattern(0,e.getWidth()-t,e),rg.embedHorizontalSeparationPattern(0,7,e),rg.embedHorizontalSeparationPattern(e.getWidth()-8,7,e),rg.embedHorizontalSeparationPattern(0,e.getWidth()-8,e),rg.embedVerticalSeparationPattern(7,0,e),rg.embedVerticalSeparationPattern(e.getHeight()-7-1,0,e),rg.embedVerticalSeparationPattern(7,e.getHeight()-7,e)}static maybeEmbedPositionAdjustmentPatterns(e,t){if(2>e.getVersionNumber())return;let r=e.getVersionNumber()-1,n=rg.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[r];for(let e=0,r=n.length;e!==r;e++){let i=n[e];if(i>=0)for(let e=0;e!==r;e++){let r=n[e];r>=0&&rg.isEmpty(t.get(r,i))&&rg.embedPositionAdjustmentPattern(r-2,i-2,t)}}}}rg.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),rg.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),rg.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),rg.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),rg.VERSION_INFO_POLY=7973,rg.TYPE_INFO_POLY=1335,rg.TYPE_INFO_MASK_PATTERN=21522;class rf{constructor(e,t){this.dataBytes=e,this.errorCorrectionBytes=t}getDataBytes(){return this.dataBytes}getErrorCorrectionBytes(){return this.errorCorrectionBytes}}class rw{constructor(){}static calculateMaskPenalty(e){return rh.applyMaskPenaltyRule1(e)+rh.applyMaskPenaltyRule2(e)+rh.applyMaskPenaltyRule3(e)+rh.applyMaskPenaltyRule4(e)}static encode(e,t,r=null){let n,i=rw.DEFAULT_BYTE_MODE_ENCODING,s=null!==r&&void 0!==r.get(ra.CHARACTER_SET);s&&(i=r.get(ra.CHARACTER_SET).toString());let o=this.chooseMode(e,i),a=new x;if(o===ty.BYTE&&(s||rw.DEFAULT_BYTE_MODE_ENCODING!==i)){let e=H.getCharacterSetECIByName(i);void 0!==e&&this.appendECI(e,a)}this.appendModeInfo(o,a);let l=new x;if(this.appendBytes(e,o,l,i),null!==r&&void 0!==r.get(ra.QR_VERSION)){let e=Number.parseInt(r.get(ra.QR_VERSION).toString(),10);n=tT.getVersionForNumber(e);let i=this.calculateBitsNeeded(o,a,l,n);if(!this.willFit(i,n,t))throw new rc("Data too big for requested version")}else n=this.recommendVersion(t,o,a,l);let h=new x;h.appendBitArray(a);let u=o===ty.BYTE?l.getSizeInBytes():e.length;this.appendLengthInfo(u,n,o,h),h.appendBitArray(l);let d=n.getECBlocksForLevel(t),c=n.getTotalCodewords()-d.getTotalECCodewords();this.terminateBits(c,h);let g=this.interleaveWithECBytes(h,n.getTotalCodewords(),c,d.getNumBlocks()),f=new rd;f.setECLevel(t),f.setMode(o),f.setVersion(n);let w=n.getDimensionForVersion(),A=new ru(w,w),C=this.chooseMaskPattern(g,t,n,A);return f.setMaskPattern(C),rg.buildMatrix(g,t,n,C,A),f.setMatrix(A),f}static recommendVersion(e,t,r,n){let i=this.calculateBitsNeeded(t,r,n,tT.getVersionForNumber(1)),s=this.chooseVersion(i,e),o=this.calculateBitsNeeded(t,r,n,s);return this.chooseVersion(o,e)}static calculateBitsNeeded(e,t,r,n){return t.getSize()+e.getCharacterCountBits(n)+r.getSize()}static getAlphanumericCode(e){return e<rw.ALPHANUMERIC_TABLE.length?rw.ALPHANUMERIC_TABLE[e]:-1}static chooseMode(e,t=null){if(H.SJIS.getName()===t&&this.isOnlyDoubleByteKanji(e))return ty.KANJI;let r=!1,n=!1;for(let t=0,i=e.length;t<i;++t){let i=e.charAt(t);if(rw.isDigit(i))r=!0;else{if(-1===this.getAlphanumericCode(i.charCodeAt(0)))return ty.BYTE;n=!0}}return n?ty.ALPHANUMERIC:r?ty.NUMERIC:ty.BYTE}static isOnlyDoubleByteKanji(e){let t;try{t=X.encode(e,H.SJIS)}catch(e){return!1}let r=t.length;if(r%2!=0)return!1;for(let e=0;e<r;e+=2){let r=255&t[e];if((r<129||r>159)&&(r<224||r>235))return!1}return!0}static chooseMaskPattern(e,t,r,n){let i=Number.MAX_SAFE_INTEGER,s=-1;for(let o=0;o<rd.NUM_MASK_PATTERNS;o++){rg.buildMatrix(e,t,r,o,n);let a=this.calculateMaskPenalty(n);a<i&&(i=a,s=o)}return s}static chooseVersion(e,t){for(let r=1;r<=40;r++){let n=tT.getVersionForNumber(r);if(rw.willFit(e,n,t))return n}throw new rc("Data too big")}static willFit(e,t,r){let n=t.getTotalCodewords();return n-t.getECBlocksForLevel(r).getTotalECCodewords()>=(e+7)/8}static terminateBits(e,t){let r=8*e;if(t.getSize()>r)throw new rc("data bits cannot fit in the QR Code"+t.getSize()+" > "+r);for(let e=0;e<4&&t.getSize()<r;++e)t.appendBit(!1);let n=7&t.getSize();if(n>0)for(let e=n;e<8;e++)t.appendBit(!1);let i=e-t.getSizeInBytes();for(let e=0;e<i;++e)t.appendBits((1&e)==0?236:17,8);if(t.getSize()!==r)throw new rc("Bits size does not equal capacity")}static getNumDataBytesAndNumECBytesForBlockID(e,t,r,n,i,s){if(n>=r)throw new rc("Block ID too large");let o=e%r,a=r-o,l=Math.floor(e/r),h=Math.floor(t/r),u=h+1,d=l-h,c=l+1-u;if(d!==c)throw new rc("EC bytes mismatch");if(r!==a+o)throw new rc("RS blocks mismatch");if(e!==(h+d)*a+(u+c)*o)throw new rc("Total bytes mismatch");n<a?(i[0]=h,s[0]=d):(i[0]=u,s[0]=c)}static interleaveWithECBytes(e,t,r,n){if(e.getSizeInBytes()!==r)throw new rc("Number of bits and data bytes does not match");let i=0,s=0,o=0,a=[];for(let l=0;l<n;++l){let h=new Int32Array(1),u=new Int32Array(1);rw.getNumDataBytesAndNumECBytesForBlockID(t,r,n,l,h,u);let d=h[0],c=new Uint8Array(d);e.toBytes(8*i,c,0,d);let g=rw.generateECBytes(c,u[0]);a.push(new rf(c,g)),s=Math.max(s,d),o=Math.max(o,g.length),i+=h[0]}if(r!==i)throw new rc("Data bytes does not match offset");let l=new x;for(let e=0;e<s;++e)for(let t of a){let r=t.getDataBytes();e<r.length&&l.appendBits(r[e],8)}for(let e=0;e<o;++e)for(let t of a){let r=t.getErrorCorrectionBytes();e<r.length&&l.appendBits(r[e],8)}if(t!==l.getSizeInBytes())throw new rc("Interleaving error: "+t+" and "+l.getSizeInBytes()+" differ.");return l}static generateECBytes(e,t){let r=e.length,n=new Int32Array(r+t);for(let t=0;t<r;t++)n[t]=255&e[t];new rl(eh.QR_CODE_FIELD_256).encode(n,t);let i=new Uint8Array(t);for(let e=0;e<t;e++)i[e]=n[r+e];return i}static appendModeInfo(e,t){t.appendBits(e.getBits(),4)}static appendLengthInfo(e,t,r,n){let i=r.getCharacterCountBits(t);if(e>=1<<i)throw new rc(e+" is bigger than "+((1<<i)-1));n.appendBits(e,i)}static appendBytes(e,t,r,n){switch(t){case ty.NUMERIC:rw.appendNumericBytes(e,r);break;case ty.ALPHANUMERIC:rw.appendAlphanumericBytes(e,r);break;case ty.BYTE:rw.append8BitBytes(e,r,n);break;case ty.KANJI:rw.appendKanjiBytes(e,r);break;default:throw new rc("Invalid mode: "+t)}}static getDigit(e){return e.charCodeAt(0)-48}static isDigit(e){let t=rw.getDigit(e);return t>=0&&t<=9}static appendNumericBytes(e,t){let r=e.length,n=0;for(;n<r;){let i=rw.getDigit(e.charAt(n));if(n+2<r){let r=rw.getDigit(e.charAt(n+1)),s=rw.getDigit(e.charAt(n+2));t.appendBits(100*i+10*r+s,10),n+=3}else if(n+1<r){let r=rw.getDigit(e.charAt(n+1));t.appendBits(10*i+r,7),n+=2}else t.appendBits(i,4),n++}}static appendAlphanumericBytes(e,t){let r=e.length,n=0;for(;n<r;){let i=rw.getAlphanumericCode(e.charCodeAt(n));if(-1===i)throw new rc;if(n+1<r){let r=rw.getAlphanumericCode(e.charCodeAt(n+1));if(-1===r)throw new rc;t.appendBits(45*i+r,11),n+=2}else t.appendBits(i,6),n++}}static append8BitBytes(e,t,r){let n;try{n=X.encode(e,r)}catch(e){throw new rc(e)}for(let e=0,r=n.length;e!==r;e++){let r=n[e];t.appendBits(r,8)}}static appendKanjiBytes(e,t){let r;try{r=X.encode(e,H.SJIS)}catch(e){throw new rc(e)}let n=r.length;for(let e=0;e<n;e+=2){let n=(255&r[e])<<8|255&r[e+1],i=-1;if(n>=33088&&n<=40956?i=n-33088:n>=57408&&n<=60351&&(i=n-49472),-1===i)throw new rc("Invalid byte sequence");let s=(i>>8)*192+(255&i);t.appendBits(s,13)}}static appendECI(e,t){t.appendBits(ty.ECI.getBits(),4),t.appendBits(e.getValue(),8)}}rw.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),rw.DEFAULT_BYTE_MODE_ENCODING=H.UTF8.getName();class rA{write(e,t,r,n=null){if(0===e.length)throw new O("Found empty contents");if(t<0||r<0)throw new O("Requested dimensions are too small: "+t+"x"+r);let i=t_.L,s=rA.QUIET_ZONE_SIZE;null!==n&&(void 0!==n.get(ra.ERROR_CORRECTION)&&(i=t_.fromString(n.get(ra.ERROR_CORRECTION).toString())),void 0!==n.get(ra.MARGIN)&&(s=Number.parseInt(n.get(ra.MARGIN).toString(),10)));let o=rw.encode(e,i,n);return this.renderResult(o,t,r,s)}writeToDom(e,t,r,n,i=null){"string"==typeof e&&(e=document.querySelector(e));let s=this.write(t,r,n,i);e&&e.appendChild(s)}renderResult(e,t,r,n){let i=e.getMatrix();if(null===i)throw new ed;let s=i.getWidth(),o=i.getHeight(),a=s+2*n,l=o+2*n,h=Math.max(t,a),u=Math.max(r,l),d=Math.min(Math.floor(h/a),Math.floor(u/l)),c=Math.floor((h-s*d)/2),g=Math.floor((u-o*d)/2),f=this.createSVGElement(h,u);for(let e=0,t=g;e<o;e++,t+=d)for(let r=0,n=c;r<s;r++,n+=d)if(1===i.get(r,e)){let e=this.createSvgRectElement(n,t,d,d);f.appendChild(e)}return f}createSVGElement(e,t){let r=document.createElementNS(rA.SVG_NS,"svg");return r.setAttributeNS(null,"height",e.toString()),r.setAttributeNS(null,"width",t.toString()),r}createSvgRectElement(e,t,r,n){let i=document.createElementNS(rA.SVG_NS,"rect");return i.setAttributeNS(null,"x",e.toString()),i.setAttributeNS(null,"y",t.toString()),i.setAttributeNS(null,"height",r.toString()),i.setAttributeNS(null,"width",n.toString()),i.setAttributeNS(null,"fill","#000000"),i}}rA.QUIET_ZONE_SIZE=4,rA.SVG_NS="http://www.w3.org/2000/svg";class rC{encode(e,t,r,n,i){if(0===e.length)throw new O("Found empty contents");if(t!==en.QR_CODE)throw new O("Can only encode QR_CODE, but got "+t);if(r<0||n<0)throw new O(`Requested dimensions are too small: ${r}x${n}`);let s=t_.L,o=rC.QUIET_ZONE_SIZE;null!==i&&(void 0!==i.get(ra.ERROR_CORRECTION)&&(s=t_.fromString(i.get(ra.ERROR_CORRECTION).toString())),void 0!==i.get(ra.MARGIN)&&(o=Number.parseInt(i.get(ra.MARGIN).toString(),10)));let a=rw.encode(e,s,i);return rC.renderResult(a,r,n,o)}static renderResult(e,t,r,n){let i=e.getMatrix();if(null===i)throw new ed;let s=i.getWidth(),o=i.getHeight(),a=s+2*n,l=o+2*n,h=Math.max(t,a),u=Math.max(r,l),d=Math.min(Math.floor(h/a),Math.floor(u/l)),c=Math.floor((h-s*d)/2),g=Math.floor((u-o*d)/2),f=new Y(h,u);for(let e=0,t=g;e<o;e++,t+=d)for(let r=0,n=c;r<s;r++,n+=d)1===i.get(r,e)&&f.setRegion(n,t,d,d);return f}}rC.QUIET_ZONE_SIZE=4;class rE{encode(e,t,r,n,i){let s;if(t===en.QR_CODE)s=new rC;else throw new O("No encoder available for format "+t);return s.encode(e,t,r,n,i)}}class rm extends Q{constructor(e,t,r,n,i,s,o,a){if(super(s,o),this.yuvData=e,this.dataWidth=t,this.dataHeight=r,this.left=n,this.top=i,n+s>t||i+o>r)throw new O("Crop rectangle does not fit within image data.");a&&this.reverseHorizontal(s,o)}getRow(e,t){if(e<0||e>=this.getHeight())throw new O("Requested row is outside the image: "+e);let r=this.getWidth();(null==t||t.length<r)&&(t=new Uint8ClampedArray(r));let n=(e+this.top)*this.dataWidth+this.left;return P.arraycopy(this.yuvData,n,t,0,r),t}getMatrix(){let e=this.getWidth(),t=this.getHeight();if(e===this.dataWidth&&t===this.dataHeight)return this.yuvData;let r=e*t,n=new Uint8ClampedArray(r),i=this.top*this.dataWidth+this.left;if(e===this.dataWidth)return P.arraycopy(this.yuvData,i,n,0,r),n;for(let r=0;r<t;r++){let t=r*e;P.arraycopy(this.yuvData,i,n,t,e),i+=this.dataWidth}return n}isCropSupported(){return!0}crop(e,t,r,n){return new rm(this.yuvData,this.dataWidth,this.dataHeight,this.left+e,this.top+t,r,n,!1)}renderThumbnail(){let e=this.getWidth()/rm.THUMBNAIL_SCALE_FACTOR,t=this.getHeight()/rm.THUMBNAIL_SCALE_FACTOR,r=new Int32Array(e*t),n=this.yuvData,i=this.top*this.dataWidth+this.left;for(let s=0;s<t;s++){let t=s*e;for(let s=0;s<e;s++){let e=255&n[i+s*rm.THUMBNAIL_SCALE_FACTOR];r[t+s]=0xff000000|65793*e}i+=this.dataWidth*rm.THUMBNAIL_SCALE_FACTOR}return r}getThumbnailWidth(){return this.getWidth()/rm.THUMBNAIL_SCALE_FACTOR}getThumbnailHeight(){return this.getHeight()/rm.THUMBNAIL_SCALE_FACTOR}reverseHorizontal(e,t){let r=this.yuvData;for(let n=0,i=this.top*this.dataWidth+this.left;n<t;n++,i+=this.dataWidth){let t=i+e/2;for(let n=i,s=i+e-1;n<t;n++,s--){let e=r[n];r[n]=r[s],r[s]=e}}}invert(){return new j(this)}}rm.THUMBNAIL_SCALE_FACTOR=2;class r_ extends Q{constructor(e,t,r,n,i,s,o){if(super(t,r),this.dataWidth=n,this.dataHeight=i,this.left=s,this.top=o,4===e.BYTES_PER_ELEMENT){let n=t*r,i=new Uint8ClampedArray(n);for(let t=0;t<n;t++){let r=e[t],n=r>>16&255,s=r>>7&510,o=255&r;i[t]=(n+s+o)/4&255}this.luminances=i}else this.luminances=e;if(void 0===n&&(this.dataWidth=t),void 0===i&&(this.dataHeight=r),void 0===s&&(this.left=0),void 0===o&&(this.top=0),this.left+t>this.dataWidth||this.top+r>this.dataHeight)throw new O("Crop rectangle does not fit within image data.")}getRow(e,t){if(e<0||e>=this.getHeight())throw new O("Requested row is outside the image: "+e);let r=this.getWidth();(null==t||t.length<r)&&(t=new Uint8ClampedArray(r));let n=(e+this.top)*this.dataWidth+this.left;return P.arraycopy(this.luminances,n,t,0,r),t}getMatrix(){let e=this.getWidth(),t=this.getHeight();if(e===this.dataWidth&&t===this.dataHeight)return this.luminances;let r=e*t,n=new Uint8ClampedArray(r),i=this.top*this.dataWidth+this.left;if(e===this.dataWidth)return P.arraycopy(this.luminances,i,n,0,r),n;for(let r=0;r<t;r++){let t=r*e;P.arraycopy(this.luminances,i,n,t,e),i+=this.dataWidth}return n}isCropSupported(){return!0}crop(e,t,r,n){return new r_(this.luminances,r,n,this.dataWidth,this.dataHeight,this.left+e,this.top+t)}invert(){return new j(this)}}class rI extends H{static forName(e){return this.getCharacterSetECIByName(e)}}class rS{}rS.ISO_8859_1=H.ISO8859_1;class rp{isCompact(){return this.compact}setCompact(e){this.compact=e}getSize(){return this.size}setSize(e){this.size=e}getLayers(){return this.layers}setLayers(e){this.layers=e}getCodeWords(){return this.codeWords}setCodeWords(e){this.codeWords=e}getMatrix(){return this.matrix}setMatrix(e){this.matrix=e}}class rT{static singletonList(e){return[e]}static min(e,t){return e.sort(t)[0]}}class rR{constructor(e){this.previous=e}getPrevious(){return this.previous}}class rN extends rR{constructor(e,t,r){super(e),this.value=t,this.bitCount=r}appendTo(e,t){e.appendBits(this.value,this.bitCount)}add(e,t){return new rN(this,e,t)}addBinaryShift(e,t){return console.warn("addBinaryShift on SimpleToken, this simply returns a copy of this token"),new rN(this,e,t)}toString(){let e=this.value&(1<<this.bitCount)-1;return e|=1<<this.bitCount,"<"+k.toBinaryString(e|1<<this.bitCount).substring(1)+">"}}class rD extends rN{constructor(e,t,r){super(e,0,0),this.binaryShiftStart=t,this.binaryShiftByteCount=r}appendTo(e,t){for(let r=0;r<this.binaryShiftByteCount;r++)(0===r||31===r&&this.binaryShiftByteCount<=62)&&(e.appendBits(31,5),this.binaryShiftByteCount>62?e.appendBits(this.binaryShiftByteCount-31,16):0===r?e.appendBits(Math.min(this.binaryShiftByteCount,31),5):e.appendBits(this.binaryShiftByteCount-31,5)),e.appendBits(t[this.binaryShiftStart+r],8)}addBinaryShift(e,t){return new rD(this,e,t)}toString(){return"<"+this.binaryShiftStart+"::"+(this.binaryShiftStart+this.binaryShiftByteCount-1)+">"}}function ry(e,t,r){return new rN(e,t,r)}let rO=["UPPER","LOWER","DIGIT","MIXED","PUNCT"],rM=new rN(null,0,0),rB=[Int32Array.from([0,327708,327710,327709,656318]),Int32Array.from([590318,0,327710,327709,656318]),Int32Array.from([262158,590300,0,590301,932798]),Int32Array.from([327709,327708,656318,0,327710]),Int32Array.from([327711,656380,656382,656381,0])],rb=function(e){for(let t of e)v.fill(t,-1);return e[0][4]=0,e[1][4]=0,e[1][0]=28,e[3][4]=0,e[2][4]=0,e[2][0]=15,e}(v.createInt32Array(6,6));class rP{constructor(e,t,r,n){this.token=e,this.mode=t,this.binaryShiftByteCount=r,this.bitCount=n}getMode(){return this.mode}getToken(){return this.token}getBinaryShiftByteCount(){return this.binaryShiftByteCount}getBitCount(){return this.bitCount}latchAndAppend(e,t){let r=this.bitCount,n=this.token;if(e!==this.mode){let t=rB[this.mode][e];n=ry(n,65535&t,t>>16),r+=t>>16}let i=2===e?4:5;return new rP(n=ry(n,t,i),e,0,r+i)}shiftAndAppend(e,t){let r=this.token,n=2===this.mode?4:5;return r=ry(r,rb[this.mode][e],n),new rP(r=ry(r,t,5),this.mode,0,this.bitCount+n+5)}addBinaryShiftChar(e){let t=this.token,r=this.mode,n=this.bitCount;if(4===this.mode||2===this.mode){let e=rB[r][0];t=ry(t,65535&e,e>>16),n+=e>>16,r=0}let i=0===this.binaryShiftByteCount||31===this.binaryShiftByteCount?18:62===this.binaryShiftByteCount?9:8,s=new rP(t,r,this.binaryShiftByteCount+1,n+i);return 2078===s.binaryShiftByteCount&&(s=s.endBinaryShift(e+1)),s}endBinaryShift(e){if(0===this.binaryShiftByteCount)return this;let t=this.token;return new rP(t=new rD(t,e-this.binaryShiftByteCount,this.binaryShiftByteCount),this.mode,0,this.bitCount)}isBetterThanOrEqualTo(e){let t=this.bitCount+(rB[this.mode][e.mode]>>16);return this.binaryShiftByteCount<e.binaryShiftByteCount?t+=rP.calculateBinaryShiftCost(e)-rP.calculateBinaryShiftCost(this):this.binaryShiftByteCount>e.binaryShiftByteCount&&e.binaryShiftByteCount>0&&(t+=10),t<=e.bitCount}toBitArray(e){let t=[];for(let r=this.endBinaryShift(e.length).token;null!==r;r=r.getPrevious())t.unshift(r);let r=new x;for(let n of t)n.appendTo(r,e);return r}toString(){return W.format("%s bits=%d bytes=%d",rO[this.mode],this.bitCount,this.binaryShiftByteCount)}static calculateBinaryShiftCost(e){return e.binaryShiftByteCount>62?21:e.binaryShiftByteCount>31?20:10*(e.binaryShiftByteCount>0)}}rP.INITIAL_STATE=new rP(rM,0,0,0);let rL=function(e){let t=W.getCharCode(" "),r=W.getCharCode("."),n=W.getCharCode(",");e[0][t]=1;let i=W.getCharCode("Z"),s=W.getCharCode("A");for(let t=s;t<=i;t++)e[0][t]=t-s+2;e[1][t]=1;let o=W.getCharCode("z"),a=W.getCharCode("a");for(let t=a;t<=o;t++)e[1][t]=t-a+2;e[2][t]=1;let l=W.getCharCode("9"),h=W.getCharCode("0");for(let t=h;t<=l;t++)e[2][t]=t-h+2;e[2][n]=12,e[2][r]=13;let u=["\0"," ","\x01","\x02","\x03","\x04","\x05","\x06","\x07","\b","	","\n","\v","\f","\r","\x1b","\x1c","\x1d","\x1e","\x1f","@","\\","^","_","`","|","~",""];for(let t=0;t<u.length;t++)e[3][W.getCharCode(u[t])]=t;let d=["\0","\r","\0","\0","\0","\0","!","'","#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}"];for(let t=0;t<d.length;t++)W.getCharCode(d[t])>0&&(e[4][W.getCharCode(d[t])]=t);return e}(v.createInt32Array(5,256));class rF{constructor(e){this.text=e}encode(){let e=W.getCharCode(" "),t=W.getCharCode("\n"),r=rT.singletonList(rP.INITIAL_STATE);for(let n=0;n<this.text.length;n++){let i,s=n+1<this.text.length?this.text[n+1]:0;switch(this.text[n]){case W.getCharCode("\r"):i=2*(s===t);break;case W.getCharCode("."):i=3*(s===e);break;case W.getCharCode(","):i=4*(s===e);break;case W.getCharCode(":"):i=5*(s===e);break;default:i=0}i>0?(r=rF.updateStateListForPair(r,n,i),n++):r=this.updateStateListForChar(r,n)}return rT.min(r,(e,t)=>e.getBitCount()-t.getBitCount()).toBitArray(this.text)}updateStateListForChar(e,t){let r=[];for(let n of e)this.updateStateForChar(n,t,r);return rF.simplifyStates(r)}updateStateForChar(e,t,r){let n=255&this.text[t],i=rL[e.getMode()][n]>0,s=null;for(let o=0;o<=4;o++){let a=rL[o][n];if(a>0){if(null==s&&(s=e.endBinaryShift(t)),!i||o===e.getMode()||2===o){let e=s.latchAndAppend(o,a);r.push(e)}if(!i&&rb[e.getMode()][o]>=0){let e=s.shiftAndAppend(o,a);r.push(e)}}}if(e.getBinaryShiftByteCount()>0||0===rL[e.getMode()][n]){let n=e.addBinaryShiftChar(t);r.push(n)}}static updateStateListForPair(e,t,r){let n=[];for(let i of e)this.updateStateForPair(i,t,r,n);return this.simplifyStates(n)}static updateStateForPair(e,t,r,n){let i=e.endBinaryShift(t);if(n.push(i.latchAndAppend(4,r)),4!==e.getMode()&&n.push(i.shiftAndAppend(4,r)),3===r||4===r){let e=i.latchAndAppend(2,16-r).latchAndAppend(2,1);n.push(e)}if(e.getBinaryShiftByteCount()>0){let r=e.addBinaryShiftChar(t).addBinaryShiftChar(t+1);n.push(r)}}static simplifyStates(e){let t=[];for(let r of e){let e=!0;for(let n of t){if(n.isBetterThanOrEqualTo(r)){e=!1;break}r.isBetterThanOrEqualTo(n)&&(t=t.filter(e=>e!==n))}e&&t.push(r)}return t}}class rv{constructor(){}static encodeBytes(e){return rv.encode(e,rv.DEFAULT_EC_PERCENT,rv.DEFAULT_AZTEC_LAYERS)}static encode(e,t,r){let n,i,s,o,a,l,h=new rF(e).encode(),u=k.truncDivision(h.getSize()*t,100)+11,d=h.getSize()+u;if(r!==rv.DEFAULT_AZTEC_LAYERS){if(n=r<0,(i=Math.abs(r))>(n?rv.MAX_NB_BITS_COMPACT:rv.MAX_NB_BITS))throw new O(W.format("Illegal value %s for layers",r));let e=(s=rv.totalBitsInLayer(i,n))-s%(o=rv.WORD_SIZE[i]);if((a=rv.stuffBits(h,o)).getSize()+u>e||n&&a.getSize()>64*o)throw new O("Data to large for user specified layer")}else{o=0,a=null;for(let e=0;;e++){if(e>rv.MAX_NB_BITS)throw new O("Data too large for an Aztec code");if(i=(n=e<=3)?e+1:e,d>(s=rv.totalBitsInLayer(i,n)))continue;(null==a||o!==rv.WORD_SIZE[i])&&(o=rv.WORD_SIZE[i],a=rv.stuffBits(h,o));let t=s-s%o;if(!(n&&a.getSize()>64*o)&&a.getSize()+u<=t)break}}let c=rv.generateCheckWords(a,s,o),g=a.getSize()/o,f=rv.generateModeMessage(n,i,g),w=(n?11:14)+4*i,A=new Int32Array(w);if(n){l=w;for(let e=0;e<A.length;e++)A[e]=e}else{l=w+1+2*k.truncDivision(k.truncDivision(w,2)-1,15);let e=k.truncDivision(w,2),t=k.truncDivision(l,2);for(let r=0;r<e;r++){let n=r+k.truncDivision(r,15);A[e-r-1]=t-n-1,A[e+r]=t+n+1}}let C=new Y(l);for(let e=0,t=0;e<i;e++){let r=(i-e)*4+(n?9:12);for(let n=0;n<r;n++){let i=2*n;for(let s=0;s<2;s++)c.get(t+i+s)&&C.set(A[2*e+s],A[2*e+n]),c.get(t+2*r+i+s)&&C.set(A[2*e+n],A[w-1-2*e-s]),c.get(t+4*r+i+s)&&C.set(A[w-1-2*e-s],A[w-1-2*e-n]),c.get(t+6*r+i+s)&&C.set(A[w-1-2*e-n],A[2*e+s])}t+=8*r}if(rv.drawModeMessage(C,n,l,f),n)rv.drawBullsEye(C,k.truncDivision(l,2),5);else{rv.drawBullsEye(C,k.truncDivision(l,2),7);for(let e=0,t=0;e<k.truncDivision(w,2)-1;e+=15,t+=16)for(let e=1&k.truncDivision(l,2);e<l;e+=2)C.set(k.truncDivision(l,2)-t,e),C.set(k.truncDivision(l,2)+t,e),C.set(e,k.truncDivision(l,2)-t),C.set(e,k.truncDivision(l,2)+t)}let E=new rp;return E.setCompact(n),E.setSize(l),E.setLayers(i),E.setCodeWords(g),E.setMatrix(C),E}static drawBullsEye(e,t,r){for(let n=0;n<r;n+=2)for(let r=t-n;r<=t+n;r++)e.set(r,t-n),e.set(r,t+n),e.set(t-n,r),e.set(t+n,r);e.set(t-r,t-r),e.set(t-r+1,t-r),e.set(t-r,t-r+1),e.set(t+r,t-r),e.set(t+r,t-r+1),e.set(t+r,t+r-1)}static generateModeMessage(e,t,r){let n=new x;return e?(n.appendBits(t-1,2),n.appendBits(r-1,6),n=rv.generateCheckWords(n,28,4)):(n.appendBits(t-1,5),n.appendBits(r-1,11),n=rv.generateCheckWords(n,40,4)),n}static drawModeMessage(e,t,r,n){let i=k.truncDivision(r,2);if(t)for(let t=0;t<7;t++){let r=i-3+t;n.get(t)&&e.set(r,i-5),n.get(t+7)&&e.set(i+5,r),n.get(20-t)&&e.set(r,i+5),n.get(27-t)&&e.set(i-5,r)}else for(let t=0;t<10;t++){let r=i-5+t+k.truncDivision(t,5);n.get(t)&&e.set(r,i-7),n.get(t+10)&&e.set(i+7,r),n.get(29-t)&&e.set(r,i+7),n.get(39-t)&&e.set(i-7,r)}}static generateCheckWords(e,t,r){let n=e.getSize()/r,i=new rl(rv.getGF(r)),s=k.truncDivision(t,r),o=rv.bitsToWords(e,r,s);i.encode(o,s-n);let a=t%r,l=new x;for(let e of(l.appendBits(0,a),Array.from(o)))l.appendBits(e,r);return l}static bitsToWords(e,t,r){let n,i,s=new Int32Array(r);for(n=0,i=e.getSize()/t;n<i;n++){let r=0;for(let i=0;i<t;i++)r|=e.get(n*t+i)?1<<t-i-1:0;s[n]=r}return s}static getGF(e){switch(e){case 4:return eh.AZTEC_PARAM;case 6:return eh.AZTEC_DATA_6;case 8:return eh.AZTEC_DATA_8;case 10:return eh.AZTEC_DATA_10;case 12:return eh.AZTEC_DATA_12;default:throw new O("Unsupported word size "+e)}}static stuffBits(e,t){let r=new x,n=e.getSize(),i=(1<<t)-2;for(let s=0;s<n;s+=t){let o=0;for(let r=0;r<t;r++)(s+r>=n||e.get(s+r))&&(o|=1<<t-1-r);(o&i)===i?(r.appendBits(o&i,t),s--):(o&i)==0?(r.appendBits(1|o,t),s--):r.appendBits(o,t)}return r}static totalBitsInLayer(e,t){return((t?88:112)+16*e)*e}}rv.DEFAULT_EC_PERCENT=33,rv.DEFAULT_AZTEC_LAYERS=0,rv.MAX_NB_BITS=32,rv.MAX_NB_BITS_COMPACT=4,rv.WORD_SIZE=Int32Array.from([4,6,6,8,8,8,8,8,8,10,10,10,10,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12]);class rk{encode(e,t,r,n){return this.encodeWithHints(e,t,r,n,null)}encodeWithHints(e,t,r,n,i){let s=rS.ISO_8859_1,o=rv.DEFAULT_EC_PERCENT,a=rv.DEFAULT_AZTEC_LAYERS;return null!=i&&(i.has(ra.CHARACTER_SET)&&(s=rI.forName(i.get(ra.CHARACTER_SET).toString())),i.has(ra.ERROR_CORRECTION)&&(o=k.parseInt(i.get(ra.ERROR_CORRECTION).toString())),i.has(ra.AZTEC_LAYERS)&&(a=k.parseInt(i.get(ra.AZTEC_LAYERS).toString()))),rk.encodeLayers(e,t,r,n,s,o,a)}static encodeLayers(e,t,r,n,i,s,o){if(t!==en.AZTEC)throw new O("Can only encode AZTEC, but got "+t);let a=rv.encode(W.getBytes(e,i),s,o);return rk.renderResult(a,r,n)}static renderResult(e,t,r){let n=e.getMatrix();if(null==n)throw new ed;let i=n.getWidth(),s=n.getHeight(),o=Math.max(t,i),a=Math.max(r,s),l=Math.min(o/i,a/s),h=(o-i*l)/2,u=(a-s*l)/2,d=new Y(o,a);for(let e=0,t=u;e<s;e++,t+=l)for(let r=0,s=h;r<i;r++,s+=l)n.get(r,e)&&d.setRegion(s,t,l,l);return d}}e.AbstractExpandedDecoder=e1,e.ArgumentException=y,e.ArithmeticException=el,e.AztecCode=rp,e.AztecCodeReader=eN,e.AztecCodeWriter=rk,e.AztecDecoder=eg,e.AztecDetector=eR,e.AztecDetectorResult=eE,e.AztecEncoder=rv,e.AztecHighLevelEncoder=rF,e.AztecPoint=eT,e.BarcodeFormat=en,e.Binarizer=b,e.BinaryBitmap=M,e.BitArray=x,e.BitMatrix=Y,e.BitSource=tf,e.BrowserAztecCodeReader=eD,e.BrowserBarcodeReader=tl,e.BrowserCodeReader=et,e.BrowserDatamatrixCodeReader=tm,e.BrowserMultiFormatReader=ri,e.BrowserPDF417Reader=rs,e.BrowserQRCodeReader=ro,e.BrowserQRCodeSvgWriter=rA,e.CharacterSetECI=H,e.ChecksumException=B,e.Code128Reader=eO,e.Code39Reader=eM,e.DataMatrixDecodedBitStreamParser=tw,e.DataMatrixReader=tE,e.DecodeHintType=V,e.DecoderResult=es,e.DefaultGridSampler=eS,e.DetectorResult=eC,e.EAN13Reader=ek,e.EncodeHintType=ra,e.Exception=D,e.FormatException=U,e.GenericGF=eh,e.GenericGFPoly=ea,e.GlobalHistogramBinarizer=K,e.GridSampler=e_,e.GridSamplerInstance=ep,e.HTMLCanvasElementLuminanceSource=J,e.HybridBinarizer=q,e.ITFReader=eB,e.IllegalArgumentException=O,e.IllegalStateException=ed,e.InvertedLuminanceSource=j,e.LuminanceSource=Q,e.MathUtils=ef,e.MultiFormatOneDReader=ta,e.MultiFormatReader=rn,e.MultiFormatWriter=rE,e.NotFoundException=Z,e.OneDReader=ey,e.PDF417DecodedBitStreamParser=t9,e.PDF417DecoderErrorCorrection=tz,e.PDF417Reader=rt,e.PDF417ResultMetadata=t2,e.PerspectiveTransform=eI,e.PlanarYUVLuminanceSource=rm,e.QRCodeByteMatrix=ru,e.QRCodeDataMask=tR,e.QRCodeDecodedBitStreamParser=tO,e.QRCodeDecoderErrorCorrectionLevel=t_,e.QRCodeDecoderFormatInformation=tI,e.QRCodeEncoder=rw,e.QRCodeEncoderQRCode=rd,e.QRCodeMaskUtil=rh,e.QRCodeMatrixUtil=rg,e.QRCodeMode=ty,e.QRCodeReader=tx,e.QRCodeVersion=tT,e.QRCodeWriter=rC,e.RGBLuminanceSource=r_,e.RSS14Reader=to,e.RSSExpandedReader=ti,e.ReaderException=rr,e.ReedSolomonDecoder=ec,e.ReedSolomonEncoder=rl,e.ReedSolomonException=eu,e.Result=er,e.ResultMetadataType=ei,e.ResultPoint=eA,e.StringUtils=W,e.UnsupportedOperationException=G,e.VideoInputDevice=$,e.WhiteRectangleDetector=em,e.WriterException=rc,e.ZXingArrays=v,e.ZXingCharset=rI,e.ZXingInteger=k,e.ZXingStandardCharsets=rS,e.ZXingStringBuilder=z,e.ZXingStringEncoding=X,e.ZXingSystem=P,e.createAbstractExpandedDecoder=tt,Object.defineProperty(e,"__esModule",{value:!0})})(t)}}]);