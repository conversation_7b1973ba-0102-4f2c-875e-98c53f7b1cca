(()=>{var e={};e.id=5944,e.ids=[5453,5944],e.modules={2331:(e,r,t)=>{"use strict";t.d(r,{HD:()=>n,Ih:()=>a,OB:()=>o,Xz:()=>c,ju:()=>i});var s=t(95453);async function o(e,r){try{let t=(0,s.bG)(),o=await fetch(`${s.ST}/accounts/${e}/webhooks`,{method:"POST",headers:t,body:JSON.stringify(r)}),n=await o.json();if(!o.ok)return console.error("[RAZORPAY] Error creating webhook:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY] Exception creating webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function n(e,r){try{let t=(0,s.bG)(),o="";if(r){let e=new URLSearchParams;void 0!==r.from&&e.append("from",r.from.toString()),void 0!==r.to&&e.append("to",r.to.toString()),void 0!==r.count&&e.append("count",r.count.toString()),void 0!==r.skip&&e.append("skip",r.skip.toString());let t=e.toString();t&&(o=`?${t}`)}let n=await fetch(`${s.ST}/accounts/${e}/webhooks${o}`,{method:"GET",headers:t}),a=await n.json();if(!n.ok)return console.error("[RAZORPAY] Error fetching webhooks:",a),{success:!1,error:a};return{success:!0,data:a.items}}catch(e){return console.error("[RAZORPAY] Exception fetching webhooks:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function a(e,r){try{let t=(0,s.bG)(),o=await fetch(`${s.ST}/accounts/${e}/webhooks/${r}`,{method:"GET",headers:t}),n=await o.json();if(!o.ok)return console.error("[RAZORPAY] Error fetching webhook:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY] Exception fetching webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function c(e,r,t){try{if(!t.url)return{success:!1,error:"URL is required for updating a webhook"};if(!t.events||!Array.isArray(t.events)||0===t.events.length)return{success:!1,error:"Events array is required for updating a webhook"};let o=(0,s.bG)(),n=await fetch(`${s.ST}/accounts/${e}/webhooks/${r}`,{method:"PATCH",headers:o,body:JSON.stringify(t)}),a=await n.json();if(!n.ok)return console.error("[RAZORPAY] Error updating webhook:",a),{success:!1,error:a};return{success:!0,data:a}}catch(e){return console.error("[RAZORPAY] Exception updating webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function i(e,r){try{let t=(0,s.bG)(),o=await fetch(`${s.ST}/accounts/${e}/webhooks/${r}`,{method:"DELETE",headers:t});if(!o.ok){let e;try{e=await o.json()}catch(r){e={error:o.statusText}}return console.error("[RAZORPAY] Error deleting webhook:",e),{success:!1,error:e}}return{success:!0}}catch(e){return console.error("[RAZORPAY] Exception deleting webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55127:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>u});var o=t(96559),n=t(48088),a=t(37719),c=t(32190),i=t(2331);async function u(e){try{let{account_id:r,url:t,alert_email:s,secret:o,events:n}=await e.json();if(!r)return c.NextResponse.json({success:!1,message:"Missing account_id parameter"},{status:400});if(!t)return c.NextResponse.json({success:!1,message:"Missing url parameter"},{status:400});if(!n||!Array.isArray(n)||0===n.length)return c.NextResponse.json({success:!1,message:"Missing or invalid events parameter"},{status:400});let a={url:t,events:n};s&&(a.alert_email=s),o&&(a.secret=o);let u=await (0,i.OB)(r,a);if(!u.success)return c.NextResponse.json({success:!1,error:u.error},{status:400});return c.NextResponse.json({success:!0,data:u.data},{status:201})}catch(e){return console.error("[RAZORPAY_WEBHOOK_CREATE] Error creating webhook:",e),c.NextResponse.json({success:!1,message:"Error creating webhook",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/webhooks/razorpay/create/route",pathname:"/api/webhooks/razorpay/create",filename:"route",bundlePath:"app/api/webhooks/razorpay/create/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\create\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:f}=p;function l(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},95453:(e,r,t)=>{"use strict";t.d(r,{ST:()=>n,bG:()=>c,t6:()=>i});var s=t(55511),o=t.n(s);let n="https://api.razorpay.com/v2",a=()=>{let e,r;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",r=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!r)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!r,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:r}},c=()=>{let{keyId:e,keySecret:r}=a(),t=Buffer.from(`${e}:${r}`).toString("base64");return{Authorization:`Basic ${t}`,"Content-Type":"application/json"}},i=(e,r,t)=>{try{let s=o().createHmac("sha256",t).update(e).digest("hex");return o().timingSafeEqual(Buffer.from(r),Buffer.from(s))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(55127));module.exports=s})();