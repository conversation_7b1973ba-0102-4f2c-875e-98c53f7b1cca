(()=>{var e={};e.id=3502,e.ids=[3502,5453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,r,t)=>{"use strict";t.r(r),t.d(r,{createClient:()=>n});var s=t(34386);async function n(){let e="https://rnjolcoecogzgglnblqn.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!r)throw Error("Supabase environment variables are not set.");let n=null,i=null;try{let{headers:e,cookies:r}=await t.e(4999).then(t.bind(t,44999));n=await e(),i=await r()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||n&&"true"===n.get("x-playwright-testing"))&&n?function(e){let r=e.get("x-test-auth-state"),t=e.get("x-test-user-type"),s="customer"===t||"business"===t,n=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===r?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===r?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,r,t,s,n){let i=()=>{var i,a,o,u,c;return i=e,a=r,o=t,u=s,c=n,"customer_profiles"===i?{data:o&&"customer"===a?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:o&&"business"===a?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===a?{id:"test-subscription-id",plan_id:c,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===a?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},a=e=>({select:r=>a(e),eq:(r,t)=>a(e),neq:(r,t)=>a(e),gt:(r,t)=>a(e),gte:(r,t)=>a(e),lt:(r,t)=>a(e),lte:(r,t)=>a(e),like:(r,t)=>a(e),ilike:(r,t)=>a(e),is:(r,t)=>a(e),in:(r,t)=>a(e),contains:(r,t)=>a(e),containedBy:(r,t)=>a(e),rangeGt:(r,t)=>a(e),rangeGte:(r,t)=>a(e),rangeLt:(r,t)=>a(e),rangeLte:(r,t)=>a(e),rangeAdjacent:(r,t)=>a(e),overlaps:(r,t)=>a(e),textSearch:(r,t)=>a(e),match:r=>a(e),not:(r,t,s)=>a(e),or:r=>a(e),filter:(r,t,s)=>a(e),order:(r,t)=>a(e),limit:(r,t)=>a(e),range:(r,t,s)=>a(e),abortSignal:r=>a(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let r=i();return e?e(r):r},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>a(),insert:e=>({select:r=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async r=>{let t={data:Array.isArray(e)?e:[e],error:null};return r?r(t):t}}),then:async r=>{let t={data:Array.isArray(e)?e:[e],error:null};return r?r(t):t}}),update:e=>a(e),upsert:e=>a(e),delete:()=>a(),rpc:(e,r)=>a()}})(e,t,s,n,i)}}(n):i?(0,s.createServerClient)(e,r,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:r,value:t,options:s}of e)await i.set(r,t,s)}catch{}}}}):(0,s.createServerClient)(e,r,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92539:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>b,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>d});var n=t(96559),i=t(48088),a=t(37719),o=t(32190),u=t(32032),c=t(31546);async function l(e,{params:r}){try{let{id:e}=await r,t=await (0,u.createClient)(),{data:{user:s},error:n}=await t.auth.getUser();if(n||!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let{data:i,error:a}=await t.from("payment_subscriptions").select("*").eq("razorpay_subscription_id",e).single();if(a){if("PGRST116"===a.code)return o.NextResponse.json({success:!1,error:"Subscription not found"},{status:404});return console.error("[RAZORPAY_ERROR] Error fetching subscription:",a),o.NextResponse.json({success:!1,error:"Error fetching subscription"},{status:500})}if(i.business_profile_id!==s.id)return o.NextResponse.json({success:!1,error:"Unauthorized to access this subscription"},{status:403});let l=await (0,c.bC)(e);if(!l.success)return o.NextResponse.json({success:!1,error:l.error},{status:400});return o.NextResponse.json({success:!0,data:l.data},{status:200})}catch(e){return console.error("[RAZORPAY_ERROR] Error fetching scheduled changes:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}async function d(e,{params:r}){try{let{id:e}=await r,t=await (0,u.createClient)(),{data:{user:s},error:n}=await t.auth.getUser();if(n||!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let{data:i,error:a}=await t.from("payment_subscriptions").select("*").eq("razorpay_subscription_id",e).single();if(a){if("PGRST116"===a.code)return o.NextResponse.json({success:!1,error:"Subscription not found"},{status:404});return console.error("[RAZORPAY_ERROR] Error fetching subscription:",a),o.NextResponse.json({success:!1,error:"Error fetching subscription"},{status:500})}if(i.business_profile_id!==s.id)return o.NextResponse.json({success:!1,error:"Unauthorized to access this subscription"},{status:403});let l=await (0,c.xH)(e);if(!l.success)return o.NextResponse.json({success:!1,error:l.error},{status:400});return o.NextResponse.json({success:!0,data:l.data},{status:200})}catch(e){return console.error("[RAZORPAY_ERROR] Error cancelling scheduled changes:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/scheduled-changes/route",pathname:"/api/subscriptions/[id]/scheduled-changes",filename:"route",bundlePath:"app/api/subscriptions/[id]/scheduled-changes/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\scheduled-changes\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:b}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},94735:e=>{"use strict";e.exports=require("events")},95453:(e,r,t)=>{"use strict";t.d(r,{ST:()=>i,bG:()=>o,t6:()=>u});var s=t(55511),n=t.n(s);let i="https://api.razorpay.com/v2",a=()=>{let e,r;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",r=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!r)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!r,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:r}},o=()=>{let{keyId:e,keySecret:r}=a(),t=Buffer.from(`${e}:${r}`).toString("base64");return{Authorization:`Basic ${t}`,"Content-Type":"application/json"}},u=(e,r,t)=>{try{let s=n().createHmac("sha256",t).update(e).digest("hex");return n().timingSafeEqual(Buffer.from(r),Buffer.from(s))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,9398,4386,580,1546],()=>t(92539));module.exports=s})();