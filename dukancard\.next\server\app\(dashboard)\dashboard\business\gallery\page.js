(()=>{var e={};e.id=6701,e.ids=[6701,8640],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3956:(e,r,a)=>{Promise.resolve().then(a.bind(a,64522))},8098:(e,r,a)=>{"use strict";a.r(r),a.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>t.B});var t=a(64275)},8126:(e,r,a)=>{"use strict";a.d(r,{Lt:()=>U,Rx:()=>F,Zr:()=>B,EO:()=>O,$v:()=>$,ck:()=>G,wd:()=>L,r7:()=>T});var t=a(60687),s=a(43210),i=a(11273),n=a(98599),l=a(10991),o=a(70569),d=Symbol("radix.slottable"),c="AlertDialog",[u,m]=(0,i.A)(c,[l.Hs]),g=(0,l.Hs)(),p=e=>{let{__scopeAlertDialog:r,...a}=e,s=g(r);return(0,t.jsx)(l.bL,{...s,...a,modal:!0})};p.displayName=c,s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=g(a);return(0,t.jsx)(l.l9,{...i,...s,ref:r})}).displayName="AlertDialogTrigger";var h=e=>{let{__scopeAlertDialog:r,...a}=e,s=g(r);return(0,t.jsx)(l.ZL,{...s,...a})};h.displayName="AlertDialogPortal";var b=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=g(a);return(0,t.jsx)(l.hJ,{...i,...s,ref:r})});b.displayName="AlertDialogOverlay";var f="AlertDialogContent",[x,y]=u(f),v=function(e){let r=({children:e})=>(0,t.jsx)(t.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=d,r}("AlertDialogContent"),w=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,children:i,...d}=e,c=g(a),u=s.useRef(null),m=(0,n.s)(r,u),p=s.useRef(null);return(0,t.jsx)(l.G$,{contentName:f,titleName:S,docsSlug:"alert-dialog",children:(0,t.jsx)(x,{scope:a,cancelRef:p,children:(0,t.jsxs)(l.UC,{role:"alertdialog",...c,...d,ref:m,onOpenAutoFocus:(0,o.m)(d.onOpenAutoFocus,e=>{e.preventDefault(),p.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,t.jsx)(v,{children:i}),(0,t.jsx)(E,{contentRef:u})]})})})});w.displayName=f;var S="AlertDialogTitle",j=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=g(a);return(0,t.jsx)(l.hE,{...i,...s,ref:r})});j.displayName=S;var N="AlertDialogDescription",I=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=g(a);return(0,t.jsx)(l.VY,{...i,...s,ref:r})});I.displayName=N;var C=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=g(a);return(0,t.jsx)(l.bm,{...i,...s,ref:r})});C.displayName="AlertDialogAction";var _="AlertDialogCancel",A=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,{cancelRef:i}=y(_,a),o=g(a),d=(0,n.s)(r,i);return(0,t.jsx)(l.bm,{...o,...s,ref:d})});A.displayName=_;var E=({contentRef:e})=>{let r=`\`${f}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${f}\` by passing a \`${N}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${f}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return s.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(r)},[r,e]),null},P=a(96241),D=a(24934);function U({...e}){return(0,t.jsx)(p,{"data-slot":"alert-dialog",...e})}function R({...e}){return(0,t.jsx)(h,{"data-slot":"alert-dialog-portal",...e})}function k({className:e,...r}){return(0,t.jsx)(b,{"data-slot":"alert-dialog-overlay",className:(0,P.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...r})}function O({className:e,...r}){return(0,t.jsxs)(R,{children:[(0,t.jsx)(k,{}),(0,t.jsx)(w,{"data-slot":"alert-dialog-content",className:(0,P.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r})]})}function L({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,P.cn)("flex flex-col gap-2 text-center sm:text-left",e),...r})}function G({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,P.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...r})}function T({className:e,...r}){return(0,t.jsx)(j,{"data-slot":"alert-dialog-title",className:(0,P.cn)("text-lg font-semibold",e),...r})}function $({className:e,...r}){return(0,t.jsx)(I,{"data-slot":"alert-dialog-description",className:(0,P.cn)("text-muted-foreground text-sm",e),...r})}function F({className:e,...r}){return(0,t.jsx)(C,{className:(0,P.cn)((0,D.r)(),e),...r})}function B({className:e,...r}){return(0,t.jsx)(A,{className:(0,P.cn)((0,D.r)({variant:"outline"}),e),...r})}},8819:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13943:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},17740:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20333:(e,r,a)=>{"use strict";a.d(r,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\gallery\\\\GalleryPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx","default")},26227:(e,r,a)=>{Promise.resolve().then(a.bind(a,20333))},27910:e=>{"use strict";e.exports=require("stream")},28640:(e,r,a)=>{"use strict";async function t(e,r={}){let{format:a="webp",targetSizeKB:s=100,maxDimension:i=800,quality:n=.8}=r;return new Promise((r,t)=>{let l=new Image;l.onload=()=>{try{let o=document.createElement("canvas"),d=o.getContext("2d");if(!d)return void t(Error("Could not get canvas context"));let{width:c,height:u}=l;(c>i||u>i)&&(c>u?(u=u*i/c,c=i):(c=c*i/u,u=i)),o.width=c,o.height=u,d.drawImage(l,0,0,c,u);let m=n,g=0,p=()=>{o.toBlob(a=>{if(!a)return void t(Error("Failed to create blob"));let i=a.size/1024;if(i<=s||g>=5||m<=.1){let t=e.size/a.size;r({blob:a,finalSizeKB:Math.round(100*i)/100,compressionRatio:Math.round(100*t)/100,dimensions:{width:c,height:u}})}else g++,m=Math.max(.1,m-.15),p()},`image/${a}`,m)};p()}catch(e){t(e)}},l.onerror=()=>t(Error("Failed to load image")),l.src=URL.createObjectURL(e)})}async function s(e,r={}){let a=e.size/1048576,i=100,n=800,l=.7;return a<=2?(l=.7,n=800,i=90):a<=5?(l=.55,n=700,i=80):a<=10?(l=.45,n=600,i=70):(l=.35,n=550,i=60),t(e,{...r,targetSizeKB:r.targetSizeKB||i,maxDimension:r.maxDimension||n,quality:r.quality||l})}async function i(e,r={}){return t(e,{targetSizeKB:50,maxDimension:400,quality:.7,...r})}a.d(r,{compressImageUltraAggressiveClient:()=>s,q:()=>i})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,r,a)=>{"use strict";a.d(r,{CG:()=>t,SC:()=>s,cZ:()=>i});let t={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},s={BUSINESS:"business",CUSTOMERS:"customers"},i={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35217:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=a(65239),s=a(48088),i=a(88170),n=a.n(i),l=a(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(r,o);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,55786)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/gallery/page",pathname:"/dashboard/business/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},41862:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},45488:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>o});var t=a(37413);a(61120);var s=a(32032),i=a(64522);let n=["member_name","title","business_name","business_category","contact_email","phone","address_line","pincode","city","state","locality"];var l=a(39916);async function o({children:e}){let r=await (0,s.createClient)(),a=null,o=null,d=null,c=null,{data:{user:u}}=await r.auth.getUser();if(u){let{data:e,error:t}=await r.from("business_profiles").select(`
        business_name,
        logo_url,
        member_name,
        title,
        business_category,
        contact_email,
        phone,
        address_line,
        pincode,
        city,
        state,
        locality
      `).eq("id",u.id).single(),{data:s}=await r.from("payment_subscriptions").select("plan_id").eq("business_profile_id",u.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(t)console.error("Error fetching business profile in layout:",t.message);else if(e){a=e.business_name,o=e.logo_url,d=e.member_name,c=s?.plan_id||"free";let r=function(e){if(!e)return{isComplete:!1,missingFields:[...n],missingFieldLabels:["Your name","Your title","Business name","Business category","Contact email","Primary phone","Address line","Pincode","City","State","Locality/area"]};let r=[],a=[],t={member_name:"Your name",title:"Your title",business_name:"Business name",business_category:"Business category",contact_email:"Contact email",phone:"Primary phone",address_line:"Address line",pincode:"Pincode",city:"City",state:"State",locality:"Locality/area"};return n.forEach(s=>{let i=e[s];i&&""!==String(i).trim()||(r.push(s),a.push(t[s]))}),{isComplete:0===r.length,missingFields:r,missingFieldLabels:a}}(e);if(!r.isComplete){let e=function(e){if(0===e.length)return"";if(1===e.length)return`Please complete your ${e[0].toLowerCase()} to access the dashboard.`;if(2===e.length)return`Please complete your ${e[0].toLowerCase()} and ${e[1].toLowerCase()} to access the dashboard.`;let r=e[e.length-1],a=e.slice(0,-1);return`Please complete your ${a.map(e=>e.toLowerCase()).join(", ")}, and ${r.toLowerCase()} to access the dashboard.`}(r.missingFieldLabels);(0,l.redirect)(`/onboarding?message=${encodeURIComponent(e)}`)}}}else console.warn("No user found in business dashboard layout.");return(0,t.jsx)(i.default,{businessName:a,logoUrl:o,memberName:d,userPlan:c,children:e})}},51718:(e,r,a)=>{"use strict";a.d(r,{Em:()=>c,Li:()=>d,XR:()=>u,fb:()=>m});var t=a(67218);a(79130);var s=a(32032),i=a(62351),n=a(98344),l=a(63500),o=a(30468);async function d(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};let d=a.id,c=e.get("image");if(!c)return{success:!1,error:"No image file provided."};if(!["image/png","image/jpeg","image/gif","image/webp"].includes(c.type))return{success:!1,error:"Invalid file type."};if(c.size>0xf00000)return{success:!1,error:"File size must be less than 15MB."};let{data:u,error:m}=await r.from(o.CG.PAYMENT_SUBSCRIPTIONS).select(o.cZ.PLAN_ID).eq(o.cZ.BUSINESS_PROFILE_ID,d).order(o.cZ.CREATED_AT,{ascending:!1}).limit(1).maybeSingle();m&&console.error("Error fetching subscription data:",m);let g=u?.plan_id||"free",{data:p,error:h}=await r.from(o.CG.BUSINESS_PROFILES).select(o.cZ.GALLERY).eq(o.cZ.ID,d).single();if(h)return{success:!1,error:"Failed to fetch business profile."};let b=p?.gallery||[],f=Array.isArray(b)?b.length:0;if(!(0,l.v)(g,f))return{success:!1,error:`You have reached the limit of ${(0,l.w)(g)} gallery images for your ${g} plan. Please upgrade your plan to add more images.`};try{let e=new Date().getTime(),a=(0,n.$W)(d,e),t=Buffer.from(await c.arrayBuffer()),{error:s}=await r.storage.from(o.SC.BUSINESS).upload(a,t,{contentType:c.type,upsert:!0});if(s)return console.error("Gallery Image Upload Error:",s),{success:!1,error:`Failed to upload gallery image: ${s.message}`};let{data:l}=r.storage.from(o.SC.BUSINESS).getPublicUrl(a);if(!l?.publicUrl)return{success:!1,error:"Could not retrieve public URL after upload."};let u={id:`gallery_${e}`,url:l.publicUrl,path:a,created_at:new Date().toISOString()},m=Array.isArray(b)?[...b,u]:[u],{error:g}=await r.from(o.CG.BUSINESS_PROFILES).update({gallery:m}).eq(o.cZ.ID,d);if(g)return console.error("Gallery Update Error:",g),await r.storage.from(o.SC.BUSINESS).remove([a]),{success:!1,error:`Failed to update gallery: ${g.message}`};return(0,i.revalidatePath)("/dashboard/business/gallery"),{success:!0,image:u}}catch(e){return console.error("Unexpected error during gallery image upload:",e),{success:!1,error:`An unexpected error occurred: ${e instanceof Error?e.message:String(e)}`}}}async function c(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};let n=a.id;try{let{error:a}=await r.from(o.CG.BUSINESS_PROFILES).update({gallery:e}).eq(o.cZ.ID,n);if(a)return console.error("Gallery Order Update Error:",a),{success:!1,error:`Failed to update gallery order: ${a.message}`};return(0,i.revalidatePath)("/dashboard/business/gallery"),{success:!0}}catch(e){return console.error("Error updating gallery order:",e),{success:!1,error:"An unexpected error occurred while updating gallery order."}}}async function u(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};try{let{data:t,error:s}=await r.from(o.CG.BUSINESS_PROFILES).select(o.cZ.GALLERY).eq(o.cZ.ID,a.id).single();if(s||!t)return{success:!1,error:"Failed to fetch business profile."};let n=t.gallery||[],l=Array.isArray(n)?n.find(r=>r.id===e):null;if(!l)return{success:!1,error:"Image not found."};let d=!1;try{let{data:e,error:a}=await r.storage.from(o.SC.BUSINESS).remove([l.path]);a?console.error("Storage deletion error:",{error:a,path:l.path,bucket:o.SC.BUSINESS}):d=!0}catch(e){console.error("Exception during storage deletion:",e)}let c=Array.isArray(n)?n.filter(r=>r.id!==e):[],{error:u}=await r.from(o.CG.BUSINESS_PROFILES).update({gallery:c}).eq(o.cZ.ID,a.id);if(u)return{success:!1,error:`Failed to update gallery: ${u.message}`};if((0,i.revalidatePath)("/dashboard/business/gallery"),d)return{success:!0};return{success:!0,warning:"Image removed from gallery, but storage cleanup may have failed"}}catch(e){return console.error("Unexpected error during gallery image deletion:",e),{success:!1,error:`An unexpected error occurred: ${e instanceof Error?e.message:String(e)}`}}}async function m(){let e=await (0,s.createClient)(),{data:{user:r},error:a}=await e.auth.getUser();if(a||!r)return{images:[],error:"User not authenticated."};try{let{data:a,error:t}=await e.from(o.CG.BUSINESS_PROFILES).select(o.cZ.GALLERY).eq(o.cZ.ID,r.id).single();if(t)return{images:[],error:`Failed to fetch business profile: ${t.message}`};let s=a?.gallery||[];return{images:Array.isArray(s)?s:[]}}catch(e){return console.error("Unexpected error fetching gallery images:",e),{images:[],error:`An unexpected error occurred: ${e instanceof Error?e.message:String(e)}`}}}(0,a(17478).D)([d,c,u,m]),(0,t.A)(d,"40b4abcdf4b56fee20ded485f2c566ea38014b1780",null),(0,t.A)(c,"404a320a1c65860fcbb5305f902486a31a65356ee8",null),(0,t.A)(u,"40bed46f79802386d699491b1d6d0c5e77a52d30e9",null),(0,t.A)(m,"00b56c732869546462632cddbbc5b5e72308588529",null)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55786:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>c,metadata:()=>d});var t=a(37413),s=a(39916),i=a(32032),n=a(20333),l=a(51718),o=a(30468);let d={title:"Gallery - Dukancard Business",description:"Manage your business photo gallery",robots:"noindex, nofollow"};async function c(){let e=await (0,i.createClient)(),{data:{user:r}}=await e.auth.getUser();if(!r)return(0,s.redirect)("/login?message=Authentication required");let{data:a,error:d}=await e.from(o.CG.BUSINESS_PROFILES).select(`${o.cZ.BUSINESS_NAME}, ${o.cZ.LOGO_URL}`).eq(o.cZ.ID,r.id).single();if(d)return console.error("Error fetching business profile:",d),(0,s.redirect)("/dashboard/business?message=Failed to load profile");let{data:c,error:u}=await e.from(o.CG.PAYMENT_SUBSCRIPTIONS).select(o.cZ.PLAN_ID).eq(o.cZ.BUSINESS_PROFILE_ID,r.id).order(o.cZ.CREATED_AT,{ascending:!1}).limit(1).maybeSingle();u&&console.error("Error fetching subscription data:",u);let m=c?.plan_id||"free",{images:g,error:p}=await (0,l.fb)();return p&&console.error("Error fetching gallery images:",p),(0,t.jsx)("div",{className:"w-full max-w-screen-xl mx-auto",children:(0,t.jsx)(n.default,{initialImages:g||[],userPlan:m,businessName:a?.business_name||"Your Business"})})}},56241:(e,r,a)=>{"use strict";a.d(r,{default:()=>V});var t=a(60687),s=a(77882),i=a(52581),n=a(6475);let l=(0,n.createServerReference)("40b4abcdf4b56fee20ded485f2c566ea38014b1780",n.callServer,void 0,n.findSourceMapURL,"uploadGalleryImage"),o=(0,n.createServerReference)("40bed46f79802386d699491b1d6d0c5e77a52d30e9",n.callServer,void 0,n.findSourceMapURL,"deleteGalleryImage"),d=e=>e.size>0xf00000?(i.oR.error("File too large",{description:"Please select an image under 15MB"}),!1):!!["image/jpeg","image/png","image/webp","image/gif"].includes(e.type)||(i.oR.error("Invalid file type",{description:"Please select a JPG, PNG, WebP, or GIF image"}),!1),c=e=>URL.createObjectURL(e),u=e=>{switch(e){case"free":default:return 1;case"basic":return 3;case"growth":return 10;case"pro":return 50;case"enterprise":return 100}};var m=a(43210);let g=e=>{let[r,a]=(0,m.useState)(e),[t,s]=(0,m.useState)(0),[i,n]=(0,m.useState)(!1),[l,o]=(0,m.useState)({isUploading:!1,selectedFile:null,previewUrl:null,uploadDialogOpen:!1}),[d,c]=(0,m.useState)({isDeleting:!1,selectedImage:null,deleteDialogOpen:!1}),[u,g]=(0,m.useState)({lightboxImage:null});return(0,m.useEffect)(()=>{n(!0)},[]),(0,m.useEffect)(()=>{l.uploadDialogOpen||o(e=>({...e,selectedFile:null,previewUrl:null}))},[l.uploadDialogOpen]),{images:r,refreshKey:t,isClient:i,uploadState:l,deleteState:d,lightboxState:u,updateImages:e=>{a(e),s(e=>e+1)},updateUploadState:e=>{o(r=>({...r,...e}))},updateDeleteState:e=>{c(r=>({...r,...e}))},updateLightboxState:e=>{g(r=>({...r,...e}))}}},p=({canAddMore:e,userPlan:r,galleryLimit:a,updateUploadState:t})=>{let[s,n]=(0,m.useState)(!1);return{isDragging:s,handleDragEnter:e=>{e.preventDefault(),e.stopPropagation(),n(!0)},handleDragLeave:e=>{e.preventDefault(),e.stopPropagation(),e.currentTarget.contains(e.relatedTarget)||n(!1)},handleDragOver:e=>{e.preventDefault(),e.stopPropagation(),s||n(!0)},handleDrop:s=>{if(s.preventDefault(),s.stopPropagation(),n(!1),!e)return void i.oR.error("Gallery limit reached",{description:`You've reached your ${r} plan limit of ${a} photos. Please upgrade to add more.`});let l=s.dataTransfer.files;if(l.length>0){let e=l[0];d(e)&&t({selectedFile:e,previewUrl:c(e),uploadDialogOpen:!0})}},handleFileSelect:e=>{d(e)&&t({selectedFile:e,previewUrl:c(e)})}}};var h=a(51358);let b=(0,n.createServerReference)("404a320a1c65860fcbb5305f902486a31a65356ee8",n.callServer,void 0,n.findSourceMapURL,"updateGalleryOrder"),f=({images:e,updateImages:r})=>{let[a,t]=(0,m.useState)({orderedImages:e,hasUnsavedChanges:!1,isSavingOrder:!1,isReordering:!1}),[s,n]=(0,m.useState)(null);(0,m.useEffect)(()=>{t(r=>({...r,orderedImages:e,hasUnsavedChanges:!1}))},[e]);let l=async()=>{t(e=>({...e,isSavingOrder:!0}));try{let{success:e,error:s}=await b(a.orderedImages);e?(r(a.orderedImages),t(e=>({...e,hasUnsavedChanges:!1})),i.oR.success("Gallery order updated",{description:"Your gallery images have been reordered successfully"})):i.oR.error("Failed to save order",{description:s||"Failed to update gallery order"})}catch(e){console.error("Error saving gallery order:",e),i.oR.error("Failed to save order",{description:"An unexpected error occurred"})}finally{t(e=>({...e,isSavingOrder:!1}))}};return{reorderState:a,activeId:s,handleDragStart:e=>{n(e.active.id),t(e=>({...e,isReordering:!0}))},handleDragEnd:e=>{let{active:r,over:s}=e;if(n(null),t(e=>({...e,isReordering:!1})),r.id!==s?.id&&s){let e=a.orderedImages.findIndex(e=>e.id===r.id),i=a.orderedImages.findIndex(e=>e.id===s.id);if(-1!==e&&-1!==i){let r=(0,h.be)(a.orderedImages,e,i);t(e=>({...e,orderedImages:r,hasUnsavedChanges:!0}))}}},handleSaveOrder:l,handleResetOrder:()=>{t(r=>({...r,orderedImages:e,hasUnsavedChanges:!1}))}}};var x=a(28640),y=a(51361),v=a(24934);function w({canAddMore:e,isUploading:r,onUploadClick:a}){return(0,t.jsxs)(s.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,t.jsx)(y.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,t.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Media Management"})]}),(0,t.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Gallery Management"}),(0,t.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Showcase your business with beautiful photos and manage your visual content with drag-and-drop organization."})]}),(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsxs)(v.$,{onClick:a,disabled:!e||r,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),r?"Uploading...":"Add Photo"]})})]})}var S=a(96882),j=a(57601),N=a(30474),I=a(96241),C=a(62478),_=a(81381),A=a(17740),E=a(88233);function P({image:e,onViewImage:r,onDeleteImage:a}){let{attributes:s,listeners:i,setNodeRef:n,transform:l,transition:o,isDragging:d}=(0,h.gl)({id:e.id,transition:{duration:150,easing:"cubic-bezier(0.25, 1, 0.5, 1)"}}),c={transform:C.Ks.Transform.toString(l),transition:o,opacity:d?.3:1,zIndex:d?1e3:"auto"};return(0,t.jsx)("div",{ref:n,style:c,className:"relative group",children:(0,t.jsxs)("div",{className:"aspect-square relative overflow-hidden rounded-xl transition-all duration-300",children:[(0,t.jsx)(N.default,{src:e.url,alt:"Gallery image",fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,t.jsx)("div",{...s,...i,className:"absolute top-2 left-2 p-2 bg-black/70 rounded-md cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-30 hover:bg-black/80",style:{touchAction:"none"},children:(0,t.jsx)(_.A,{className:"h-4 w-4 text-white"})}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 pointer-events-none",children:(0,t.jsxs)("div",{className:"absolute bottom-3 right-3 flex space-x-2 pointer-events-auto",children:[(0,t.jsx)(v.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/20 backdrop-blur-sm text-white shadow-md hover:bg-white/30",onClick:a=>{a.stopPropagation(),r(e.url)},children:(0,t.jsx)(A.A,{className:"h-4 w-4"})}),(0,t.jsx)(v.$,{variant:"destructive",size:"icon",className:"h-8 w-8 bg-red-500/80 hover:bg-red-600/90 text-white shadow-md",onClick:r=>{r.stopPropagation(),a(e)},children:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]})})]})})}function D({image:e,onViewImage:r,onDeleteImage:a}){return(0,t.jsx)("div",{className:"relative group",children:(0,t.jsxs)("div",{className:"aspect-square relative overflow-hidden rounded-xl transition-all duration-300",children:[(0,t.jsx)(N.default,{src:e.url,alt:"Gallery image",fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 pointer-events-none",children:(0,t.jsxs)("div",{className:"absolute bottom-3 right-3 flex space-x-2 pointer-events-auto",children:[(0,t.jsx)(v.$,{variant:"secondary",size:"icon",className:"h-8 w-8 bg-white/20 backdrop-blur-sm text-white shadow-md hover:bg-white/30",onClick:a=>{a.stopPropagation(),r(e.url)},children:(0,t.jsx)(A.A,{className:"h-4 w-4"})}),(0,t.jsx)(v.$,{variant:"destructive",size:"icon",className:"h-8 w-8 bg-red-500/80 hover:bg-red-600/90 text-white shadow-md",onClick:r=>{r.stopPropagation(),a(e)},children:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]})})]})})}var U=a(13943),R=a(41862),k=a(8819);function O({isReordering:e,hasUnsavedChanges:r,isSavingOrder:a,onSaveOrder:i,onResetOrder:n}){return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:(0,I.cn)("flex items-center gap-2 text-sm rounded-lg p-3 transition-all duration-200",e?"bg-amber-500/20 text-amber-700 dark:text-amber-300 border border-amber-500/30":"bg-muted/30 text-muted-foreground"),children:[(0,t.jsx)(_.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e?"Reordering images... Drop to place in new position.":'Drag the grip handle (⋮⋮) on images to reorder them. Changes will be saved when you click "Save Order".'})]}),r&&(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"flex items-center justify-center gap-3 p-3 bg-amber-500/10 border border-amber-500/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-amber-700 dark:text-amber-300 font-medium",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"You have unsaved changes"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(v.$,{variant:"outline",size:"sm",onClick:n,disabled:a,className:"text-xs border-amber-500/30 hover:bg-amber-500/10",children:[(0,t.jsx)(U.A,{className:"mr-1 h-3 w-3"}),"Reset"]}),(0,t.jsx)(v.$,{onClick:i,disabled:a,className:"bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-xs",size:"sm",children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(R.A,{className:"mr-1 h-3 w-3 animate-spin"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"mr-1 h-3 w-3"}),"Save Order"]})})]})]})]})}var L=a(9005),G=a(16023);function T({canAddMore:e,isDragging:r,userPlan:a,galleryLimit:i,onUploadClick:n}){return(0,t.jsxs)(s.P.div,{className:(0,I.cn)("text-center py-20 border-2 border-dashed rounded-2xl bg-neutral-50/50 dark:bg-neutral-900/50 transition-all duration-300",e&&"hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50 cursor-pointer border-neutral-300 dark:border-neutral-700",!e&&"border-neutral-200 dark:border-neutral-800",r&&e&&"border-primary bg-primary/10 dark:bg-primary/5"),initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.3},onClick:e?n:void 0,children:[(0,t.jsx)("div",{className:(0,I.cn)("w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 flex items-center justify-center mb-6 transition-all duration-300",r&&e&&"bg-gradient-to-br from-primary/20 to-primary/10 scale-110 border-primary/40"),children:(0,t.jsx)(L.A,{className:(0,I.cn)("h-8 w-8 text-primary transition-all duration-300",r&&e&&"scale-110")})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:e?"Start Your Gallery":"Gallery Limit Reached"}),(0,t.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 max-w-md mx-auto leading-relaxed",children:e?(0,t.jsxs)(t.Fragment,{children:["Showcase your business with beautiful photos. Upload images to create an engaging visual experience for your customers.",(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"text-primary font-medium mt-2 block",children:r?"Drop your images here":"Click to upload or drag and drop"})]}):(0,t.jsxs)(t.Fragment,{children:["You've reached your plan's gallery limit of ",i," photos.",(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"text-primary font-medium mt-2 block",children:"Upgrade your plan to add more photos."})]})}),e&&(0,t.jsxs)(v.$,{onClick:e=>{e.stopPropagation(),n()},disabled:!e,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:[(0,t.jsx)(G.A,{className:"mr-2 h-4 w-4"}),"Upload Your First Photo"]})]})]})}function $({images:e,imagesCount:r,galleryLimit:a,canAddMore:i,isDragging:n,userPlan:l,isClient:o,reorderState:d,activeId:c,onDragStart:u,onDragEnd:m,onSaveOrder:g,onResetOrder:p,onViewImage:b,onDeleteImage:f,onUploadClick:x}){let y=(0,j.FR)((0,j.MS)(j.AN,{activationConstraint:{distance:8}}),(0,j.MS)(j.uN,{coordinateGetter:h.JR}));return(0,t.jsxs)(s.P.div,{className:"space-y-8",initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Gallery Collection"}),(0,t.jsx)("div",{className:"h-px flex-1 bg-gradient-to-r from-neutral-200 to-transparent dark:from-neutral-700"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-500 dark:text-neutral-400",children:[(0,t.jsx)("span",{className:"font-medium text-neutral-900 dark:text-neutral-100",children:r}),(0,t.jsx)("span",{children:"/"}),(0,t.jsx)("span",{children:a===1/0?"∞":a}),(0,t.jsx)(S.A,{className:"h-4 w-4 opacity-60"})]})]}),(0,t.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed",children:0===r?"Start building your gallery by uploading your first image.":`Manage your ${r} image${1===r?"":"s"} with drag-and-drop reordering.`})]}),(0,t.jsx)("div",{className:"relative",children:0===d.orderedImages.length?(0,t.jsx)(T,{canAddMore:i,isDragging:n,userPlan:l,galleryLimit:a,onUploadClick:x}):(0,t.jsxs)("div",{className:"space-y-4",children:[d.orderedImages.length>1&&o&&(0,t.jsx)(O,{isReordering:d.isReordering,hasUnsavedChanges:d.hasUnsavedChanges,isSavingOrder:d.isSavingOrder,onSaveOrder:g,onResetOrder:p}),o?(0,t.jsxs)(j.Mp,{sensors:y,collisionDetection:j.fp,onDragStart:u,onDragEnd:m,children:[(0,t.jsx)(h.gB,{items:d.orderedImages.map(e=>e.id),strategy:h.kL,children:(0,t.jsx)("div",{className:(0,I.cn)("grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 transition-all duration-200",d.isReordering&&"bg-primary/5 rounded-xl p-4"),children:d.orderedImages.map(e=>(0,t.jsx)(P,{image:e,onViewImage:b,onDeleteImage:f},e.id))})}),(0,t.jsx)(j.Hd,{children:c?(0,t.jsx)("div",{className:"aspect-square relative overflow-hidden rounded-lg border shadow-lg opacity-90 transform rotate-3 scale-105",children:(()=>{let e=d.orderedImages.find(e=>e.id===c);return e?(0,t.jsx)(N.default,{src:e.url,alt:"Dragging image",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}):null})()}):null})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6",children:d.orderedImages.map(e=>(0,t.jsx)(D,{image:e,onViewImage:b,onDeleteImage:f},e.id))})]})})]})}var F=a(37826);function B({uploadState:e,isDragging:r,imagesCount:a,galleryLimit:s,canAddMore:i,onOpenChange:n,onFileChange:l,onDragEnter:o,onDragOver:d,onDragLeave:c,onDrop:u,onUpload:m,onClearPreview:g}){return(0,t.jsx)(F.lG,{open:e.uploadDialogOpen,onOpenChange:n,children:(0,t.jsxs)(F.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(F.c7,{children:[(0,t.jsx)(F.L3,{children:"Upload Gallery Image"}),(0,t.jsx)(F.rr,{children:"Add a new photo to showcase your business"})]}),(0,t.jsx)("div",{className:"space-y-4 py-4",children:e.previewUrl?(0,t.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-lg border shadow-md",children:[(0,t.jsx)(N.default,{src:e.previewUrl,alt:"Preview",fill:!0,className:"object-cover"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300",children:(0,t.jsx)(v.$,{variant:"outline",size:"sm",className:"absolute top-2 right-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/20",onClick:g,children:"Change"})})]}):(0,t.jsxs)("div",{className:(0,I.cn)("flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-12 transition-all duration-200",i?r?"border-primary bg-primary/10":"border-neutral-200 dark:border-neutral-700 bg-muted/30 hover:bg-muted/50":"border-neutral-200 dark:border-neutral-700 bg-neutral-100/50 dark:bg-neutral-800/50 opacity-60"),onDragEnter:o,onDragOver:d,onDragLeave:c,onDrop:u,children:[(0,t.jsx)("div",{className:(0,I.cn)("w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-200",i?r?"bg-primary/20 scale-110":"bg-primary/10":"bg-neutral-200/50 dark:bg-neutral-700/50"),children:(0,t.jsx)(G.A,{className:(0,I.cn)("h-8 w-8 transition-all duration-200",i?r?"text-primary opacity-100":"text-primary opacity-80":"text-neutral-400 dark:text-neutral-600")})}),(0,t.jsxs)("label",{htmlFor:"gallery-image",className:(0,I.cn)("text-center",i?"cursor-pointer":"cursor-not-allowed"),children:[(0,t.jsx)("span",{className:(0,I.cn)("text-sm font-medium transition-all duration-200",i?"text-primary":"text-neutral-500 dark:text-neutral-400"),children:i?r?"Drop image here":"Click to upload":"Gallery limit reached"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground block mt-1",children:i?!r&&"or drag and drop":"Remove images to upload new ones"}),i&&(0,t.jsx)("span",{className:"text-xs text-muted-foreground block mt-1",children:"JPG, PNG, WebP, or GIF (max. 15MB)"}),(0,t.jsx)("input",{id:"gallery-image",type:"file",accept:"image/jpeg,image/png,image/webp,image/gif",className:"hidden",onChange:l,disabled:!i})]})]})}),(0,t.jsxs)(F.Es,{children:[(0,t.jsx)(v.$,{variant:"outline",onClick:()=>n(!1),disabled:e.isUploading,className:"border-neutral-200 hover:bg-neutral-100 dark:border-neutral-800 dark:hover:bg-neutral-800",children:"Cancel"}),(0,t.jsx)(v.$,{onClick:m,disabled:!e.selectedFile||e.isUploading||!i,className:"bg-primary hover:bg-primary/90 text-primary-foreground font-medium",children:e.isUploading?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(R.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uploading..."]}):i?"Upload":"Gallery Limit Reached"})]})]})})}var M=a(8126);function z({deleteState:e,onOpenChange:r,onDelete:a}){return(0,t.jsx)(M.Lt,{open:e.deleteDialogOpen,onOpenChange:r,children:(0,t.jsxs)(M.EO,{className:"border-neutral-200 dark:border-neutral-800 shadow-lg",children:[(0,t.jsxs)(M.wd,{children:[(0,t.jsx)(M.r7,{children:"Delete Gallery Image"}),(0,t.jsx)(M.$v,{children:"Are you sure you want to delete this image? This action cannot be undone."})]}),(0,t.jsxs)(M.ck,{children:[(0,t.jsx)(M.Zr,{disabled:e.isDeleting,className:"border-neutral-200 hover:bg-neutral-100 dark:border-neutral-800 dark:hover:bg-neutral-800",children:"Cancel"}),(0,t.jsx)(M.Rx,{onClick:e=>{e.preventDefault(),a()},disabled:e.isDeleting,className:"bg-red-500 hover:bg-red-600 text-white",children:e.isDeleting?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(R.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete"})]})]})})}var q=a(88920),Y=a(11860);function Z({lightboxState:e,onClose:r}){return(0,t.jsx)(q.N,{children:e.lightboxImage&&(0,t.jsxs)(s.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4",onClick:r,children:[(0,t.jsx)(s.P.button,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.2},className:"absolute top-4 right-4 text-white bg-black/50 rounded-full p-2 hover:bg-black/70 transition-colors duration-200 z-50",onClick:e=>{e.stopPropagation(),r()},children:(0,t.jsx)(Y.A,{className:"h-6 w-6"})}),(0,t.jsx)(s.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.3},className:"relative max-w-4xl max-h-[80vh] w-full h-full",onClick:e=>e.stopPropagation(),children:(0,t.jsx)(N.default,{src:e.lightboxImage,alt:"Gallery image",fill:!0,className:"object-contain",sizes:"100vw"})})]})})}function V({initialImages:e,userPlan:r,businessName:a}){let n=u(r),{images:d,isClient:c,uploadState:m,deleteState:h,lightboxState:b,updateImages:y,updateUploadState:v,updateDeleteState:S,updateLightboxState:j}=g(e),N=d.length<n,{isDragging:I,handleDragEnter:C,handleDragLeave:_,handleDragOver:A,handleDrop:E,handleFileSelect:P}=p({canAddMore:N,userPlan:r,galleryLimit:n,updateUploadState:v}),{reorderState:D,activeId:U,handleDragStart:R,handleDragEnd:k,handleSaveOrder:O,handleResetOrder:L}=f({images:d,updateImages:y}),G=async()=>{if(m.selectedFile){v({isUploading:!0});try{let e=await (0,x.compressImageUltraAggressiveClient)(m.selectedFile,{maxDimension:1200,targetSizeKB:100}),r=new File([e.blob],m.selectedFile.name,{type:e.blob.type}),a=new FormData;a.append("image",r);let{success:t,image:s,error:n}=await l(a);if(t&&s){let e=[...d,s];y(e),i.oR.success("Image uploaded",{description:"Your gallery image has been uploaded successfully"}),v({uploadDialogOpen:!1})}else n?.includes("File size must be less than 15MB")?i.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}):n?.includes("Invalid file type")?i.oR.error("Invalid file type",{description:"Please select a JPG, PNG, WebP, or GIF image"}):n?.includes("reached the limit")?i.oR.error("Gallery limit reached",{description:n}):i.oR.error("Upload failed",{description:n||"Failed to upload image"})}catch(e){console.error("Error uploading image:",e),i.oR.error("Upload failed",{description:"An unexpected error occurred"})}finally{v({isUploading:!1})}}},T=async()=>{if(h.selectedImage){S({isDeleting:!0});try{let e=await o(h.selectedImage.id),{success:r,error:a}=e;if(r){let r=d.filter(e=>e.id!==h.selectedImage.id);y(r),e.warning?i.oR.success("Image deleted",{description:e.warning}):i.oR.success("Image deleted",{description:"The gallery image has been deleted successfully"}),S({deleteDialogOpen:!1,selectedImage:null})}else i.oR.error("Deletion failed",{description:a||"Failed to delete image"})}catch(e){console.error("Error deleting image:",e),i.oR.error("Deletion failed",{description:"An unexpected error occurred"})}finally{S({isDeleting:!1})}}},F=()=>{v({uploadDialogOpen:!0})};return(0,t.jsxs)(s.P.div,{className:"space-y-10",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[(0,t.jsx)(w,{canAddMore:N,isUploading:m.isUploading,onUploadClick:F}),(0,t.jsx)($,{images:d,imagesCount:d.length,galleryLimit:n,canAddMore:N,isDragging:I,userPlan:r,isClient:c,reorderState:D,activeId:U,onDragStart:R,onDragEnd:k,onSaveOrder:O,onResetOrder:L,onViewImage:e=>{j({lightboxImage:e})},onDeleteImage:e=>{S({selectedImage:e,deleteDialogOpen:!0})},onUploadClick:F}),(0,t.jsx)(B,{uploadState:m,isDragging:I,imagesCount:d.length,galleryLimit:n,canAddMore:N,onOpenChange:e=>{v({uploadDialogOpen:e})},onFileChange:e=>{let r=e.target.files?.[0];r&&P(r)},onDragEnter:C,onDragOver:A,onDragLeave:_,onDrop:e=>{E(e)},onUpload:G,onClearPreview:()=>{v({selectedFile:null,previewUrl:null})}}),(0,t.jsx)(z,{deleteState:h,onOpenChange:e=>{S({deleteDialogOpen:e})},onDelete:T}),(0,t.jsx)(Z,{lightboxState:b,onClose:()=>{j({lightboxImage:null})}})]})}},61192:(e,r,a)=>{"use strict";a.d(r,{default:()=>u});var t=a(60687);a(43210);var s=a(27625),i=a(41956),n=a(38606),l=a(24861),o=a(21121),d=a(96241),c=a(52529);function u({children:e,businessName:r,logoUrl:a,memberName:u,userPlan:m}){return(0,t.jsx)(c.Q,{children:(0,t.jsxs)(l.GB,{children:[(0,t.jsx)(o.s,{businessName:r,logoUrl:a,memberName:u,userPlan:m}),(0,t.jsxs)(l.sF,{children:[(0,t.jsxs)(s.default,{businessName:r,logoUrl:a,userName:u,children:[(0,t.jsx)(l.x2,{className:"ml-auto md:ml-0"})," ",(0,t.jsx)(i.ThemeToggle,{variant:"dashboard"})]}),(0,t.jsx)("main",{className:(0,d.cn)("flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto","pb-20 sm:pb-18 md:pb-6","bg-white dark:bg-black"),children:e}),(0,t.jsx)(n.default,{})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63179:(e,r,a)=>{Promise.resolve().then(a.bind(a,56241))},63500:(e,r,a)=>{"use strict";function t(e){if(!e)return 0;switch(e){case"free":return 1;case"basic":case"trial":return 3;case"growth":return 10;case"pro":return 50;case"enterprise":return 100;default:return 0}}function s(e,r){return r<t(e)}a.d(r,{v:()=>s,w:()=>t})},64522:(e,r,a)=>{"use strict";a.d(r,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\components\\\\BusinessDashboardClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},76230:(e,r,a)=>{"use strict";a.r(r),a.d(r,{"00b56c732869546462632cddbbc5b5e72308588529":()=>t.fb,"404a320a1c65860fcbb5305f902486a31a65356ee8":()=>t.Em,"40b4abcdf4b56fee20ded485f2c566ea38014b1780":()=>t.Li,"40bed46f79802386d699491b1d6d0c5e77a52d30e9":()=>t.XR});var t=a(51718)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79988:(e,r,a)=>{Promise.resolve().then(a.bind(a,61192))},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96882:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},98344:(e,r,a)=>{"use strict";function t(e){if(!e||"string"!=typeof e)throw Error(`Invalid userId: expected string, got ${typeof e}. Value: ${e}`);if(e.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${e}`);let r=e.substring(0,2).toLowerCase(),a=e.substring(2,4).toLowerCase();return`users/${r}/${a}/${e}`}function s(e,r){let a=t(e);return`${a}/profile/logo_${r}.webp`}function i(e,r,a,s){let i=t(e);return`${i}/products/${r}/base/image_${a}_${s}.webp`}function n(e,r,a,s,i){let n=t(e);return`${n}/products/${r}/${a}/image_${s}_${i}.webp`}function l(e,r){let a=t(e);return`${a}/gallery/gallery_${r}.webp`}function o(e,r,a){let s=t(e);return`${s}/branding/header_${a}_${r}.webp`}a.d(r,{$W:()=>l,Vl:()=>n,Wl:()=>s,YV:()=>t,jA:()=>i,rN:()=>o})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,1753,6380,2186,1606,3037,3739,9538,5265],()=>a(35217));module.exports=t})();