"use strict";exports.id=4209,exports.ids=[4209],exports.modules={34209:(r,e,o)=>{o.d(e,{getSubscription:()=>n}),o(55511);let s=()=>{let r,e;if(r=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",e=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!r||!e)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!r,keySecret:!!e,env:"production"}),Error("Razorpay credentials not configured");return{keyId:r,keySecret:e}},t=()=>{let{keyId:r,keySecret:e}=s(),o=Buffer.from(`${r}:${e}`).toString("base64");return{Authorization:`Basic ${o}`,"Content-Type":"application/json"}};async function n(r){try{let e=t(),o=await fetch(`${"https://api.razorpay.com/v2".replace("/v2","/v1")}/subscriptions/${r}`,{method:"GET",headers:e}),s=await o.json();if(!o.ok)return console.error("[RAZORPAY_ERROR] Error fetching subscription:",s),{success:!1,error:s};return{success:!0,data:s}}catch(r){return console.error("[RAZORPAY_ERROR] Exception fetching subscription:",r),{success:!1,error:{message:r instanceof Error?r.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}}};