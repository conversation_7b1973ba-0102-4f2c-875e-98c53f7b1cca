(()=>{var e={};e.id=6735,e.ids=[6735],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30255:(e,s,t)=>{"use strict";t.d(s,{DE:()=>a,VC:()=>n,clearRazorpayColumnsAfterCancellation:()=>o,lJ:()=>i});var r=t(32032);async function i(e){let s=await (0,r.createClient)();try{let{data:r,error:i}=await s.from("payment_subscriptions").select("business_profile_id, subscription_status, plan_id").eq("razorpay_subscription_id",e).single();if(i||!r)return{consistent:!1,details:`Subscription not found: ${i?.message||"Unknown error"}`};let{data:n,error:a}=await s.from("business_profiles").select("has_active_subscription").eq("id",r.business_profile_id).single();if(a||!n)return{consistent:!1,details:`Business profile not found: ${a?.message||"Unknown error"}`};let{SubscriptionStateManager:o}=await t.e(33).then(t.bind(t,10033)),c=o.shouldHaveActiveSubscription(r.subscription_status,r.plan_id||"free"),u=n.has_active_subscription===c;return{consistent:u,details:u?"Subscription and business profile are consistent":`Inconsistency detected: subscription_status=${r.subscription_status}, has_active_subscription=${n.has_active_subscription}`}}catch(e){return{consistent:!1,details:`Error checking consistency: ${e instanceof Error?e.message:String(e)}`}}}async function n(e,s,t){let i=await (0,r.createClient)(),n=new Date().toISOString();try{let{data:r,error:a}=await i.rpc("update_subscription_atomic",{p_subscription_id:e,p_new_status:"active",p_business_profile_id:s,p_has_active_subscription:!1,p_additional_data:{plan_id:"free",plan_cycle:"monthly",subscription_start_date:n,cancelled_at:n,razorpay_subscription_id:null,razorpay_customer_id:null,razorpay_plan_id:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,subscription_expiry_time:null,subscription_charge_time:null,cancellation_requested_at:null,cancellation_reason:null,subscription_paused_at:null,original_plan_id:null,original_plan_cycle:null},p_webhook_timestamp:void 0});if(a||!r?.success)return console.error(`[ATOMIC_DOWNGRADE_ERROR] Atomic RPC failed for subscription ${e}. Reason: ${t}. Error:`,a||r?.error),{success:!1,message:`Atomic downgrade failed: ${a?.message||r?.error}`};return{success:!0,message:`Subscription downgraded to free plan due to ${t}`}}catch(s){return console.error(`[ATOMIC_DOWNGRADE] Exception during downgrade for subscription ${e}:`,s),{success:!1,message:`Exception during downgrade: ${s instanceof Error?s.message:String(s)}`}}}async function a(e,s){let t=await (0,r.createClient)();try{let{data:r,error:i}=await t.from("payment_subscriptions").select("plan_id, plan_cycle").eq("razorpay_subscription_id",e).maybeSingle();if(i)return console.error(`[REVERT_TO_TRIAL] Error fetching current subscription ${e}:`,i),{success:!1,message:`Error fetching current subscription: ${i.message}`};if(!r)return console.error(`[REVERT_TO_TRIAL] Subscription ${e} not found in database`),{success:!1,message:"Subscription not found in database"};let{data:n,error:a}=await t.rpc("update_subscription_atomic",{p_subscription_id:e,p_new_status:"trial",p_business_profile_id:s,p_has_active_subscription:!1,p_additional_data:{plan_id:r.plan_id,plan_cycle:"monthly",last_payment_id:null,last_payment_date:null,last_payment_method:null,subscription_start_date:null,subscription_expiry_time:null,subscription_charge_time:null,cancelled_at:null,cancellation_requested_at:null,cancellation_reason:null,subscription_paused_at:null,original_plan_id:null,original_plan_cycle:null},p_webhook_timestamp:void 0});if(a||!n?.success)return console.error(`[REVERT_TO_TRIAL] Atomic RPC failed for subscription ${e}:`,a||n?.error),{success:!1,message:`Atomic revert to trial failed: ${a?.message||n?.error}`};return{success:!0,message:`Subscription reverted to trial status with plan ${r.plan_id} and monthly cycle enforced - Razorpay IDs preserved until webhook confirmation`}}catch(s){return console.error(`[REVERT_TO_TRIAL] Exception during revert to trial for subscription ${e}:`,s),{success:!1,message:`Exception during revert to trial: ${s instanceof Error?s.message:String(s)}`}}}async function o(e,s){let t=await (0,r.createClient)(),i=new Date().toISOString();try{let{error:r}=await t.from("payment_subscriptions").update({razorpay_subscription_id:null,razorpay_customer_id:null,subscription_start_date:null,subscription_expiry_time:null,subscription_charge_time:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,cancellation_requested_at:null,cancellation_reason:null,subscription_paused_at:null,cancelled_at:null,last_webhook_timestamp:null,original_plan_id:null,original_plan_cycle:null,updated_at:i}).eq("business_profile_id",s).eq("razorpay_subscription_id",e);if(r)return console.error(`[CLEAR_RAZORPAY_COLUMNS] Error clearing Razorpay columns for subscription ${e}:`,r),{success:!1,message:`Error clearing Razorpay columns: ${r.message}`};return{success:!0,message:"Razorpay IDs and other columns cleared after webhook confirmation"}}catch(s){return console.error(`[CLEAR_RAZORPAY_COLUMNS] Exception during column clearing for subscription ${e}:`,s),{success:!1,message:`Exception during column clearing: ${s instanceof Error?s.message:String(s)}`}}}},32032:(e,s,t)=>{"use strict";t.r(s),t.d(s,{createClient:()=>i});var r=t(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!s)throw Error("Supabase environment variables are not set.");let i=null,n=null;try{let{headers:e,cookies:s}=await t.e(4999).then(t.bind(t,44999));i=await e(),n=await s()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let s=e.get("x-test-auth-state"),t=e.get("x-test-user-type"),r="customer"===t||"business"===t,i=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===s?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===s?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,s,t,r,i){let n=()=>{var n,a,o,c,u;return n=e,a=s,o=t,c=r,u=i,"customer_profiles"===n?{data:o&&"customer"===a?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:o&&"business"===a?{id:"test-user-id",business_slug:c||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===a?{id:"test-subscription-id",plan_id:u,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===a?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},a=e=>({select:s=>a(e),eq:(s,t)=>a(e),neq:(s,t)=>a(e),gt:(s,t)=>a(e),gte:(s,t)=>a(e),lt:(s,t)=>a(e),lte:(s,t)=>a(e),like:(s,t)=>a(e),ilike:(s,t)=>a(e),is:(s,t)=>a(e),in:(s,t)=>a(e),contains:(s,t)=>a(e),containedBy:(s,t)=>a(e),rangeGt:(s,t)=>a(e),rangeGte:(s,t)=>a(e),rangeLt:(s,t)=>a(e),rangeLte:(s,t)=>a(e),rangeAdjacent:(s,t)=>a(e),overlaps:(s,t)=>a(e),textSearch:(s,t)=>a(e),match:s=>a(e),not:(s,t,r)=>a(e),or:s=>a(e),filter:(s,t,r)=>a(e),order:(s,t)=>a(e),limit:(s,t)=>a(e),range:(s,t,r)=>a(e),abortSignal:s=>a(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let s=n();return e?e(s):s},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>a(),insert:e=>({select:s=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async s=>{let t={data:Array.isArray(e)?e:[e],error:null};return s?s(t):t}}),then:async s=>{let t={data:Array.isArray(e)?e:[e],error:null};return s?s(t):t}}),update:e=>a(e),upsert:e=>a(e),delete:()=>a(),rpc:(e,s)=>a()}})(e,t,r,i,n)}}(i):n?(0,r.createServerClient)(e,s,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:s,value:t,options:r}of e)await n.set(s,t,r)}catch{}}}}):(0,r.createServerClient)(e,s,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function s(e){var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}s.keys=()=>[],s.resolve=s,s.id=51906,e.exports=s},54573:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>m,routeModule:()=>_,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>b});var r={};t.r(r),t.d(r,{GET:()=>p,POST:()=>l});var i=t(96559),n=t(48088),a=t(37719),o=t(32190),c=t(32032),u=t(30255);async function l(e){try{let s,t=e.headers.get("x-api-key")||"",r=process.env.ADMIN_API_KEY||process.env.WEBHOOK_RETRY_API_KEY;if(!r||t!==r)return console.error("[FIX_INCONSISTENCY] Invalid or missing API key"),o.NextResponse.json({success:!1,message:"Invalid or missing API key"},{status:401});let{business_profile_id:i,dry_run:n=!1}=await e.json();if(!i)return o.NextResponse.json({success:!1,message:"Missing business_profile_id parameter"},{status:400});console.log(`[FIX_INCONSISTENCY] ${n?"Dry run":"Fixing"} inconsistency for business ${i}`);let a=await (0,c.createClient)(),{data:l,error:p}=await a.from("payment_subscriptions").select("subscription_status, plan_id, razorpay_subscription_id").eq("business_profile_id",i).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(p)return console.error("[FIX_INCONSISTENCY] Error getting subscription:",p),o.NextResponse.json({success:!1,message:`Error getting subscription: ${p.message}`},{status:500});if(!l)return o.NextResponse.json({success:!1,message:"No subscription found for this business profile"},{status:404});let{data:_,error:d}=await a.from("business_profiles").select("has_active_subscription").eq("id",i).single();if(d)return console.error("[FIX_INCONSISTENCY] Error getting business profile:",d),o.NextResponse.json({success:!1,message:`Error getting business profile: ${d.message}`},{status:500});let b="active"===l.subscription_status||"authenticated"===l.subscription_status||"trial"===l.subscription_status;if(_.has_active_subscription===b)return o.NextResponse.json({success:!0,message:"No inconsistency detected - subscription and business profile are already consistent",current_state:{subscription_status:l.subscription_status,has_active_subscription:_.has_active_subscription,plan_id:l.plan_id},verification:{consistent:!0,details:"Subscription and business profile are consistent"}});let g={subscription_status:l.subscription_status,has_active_subscription:_.has_active_subscription,plan_id:l.plan_id};if(n)return o.NextResponse.json({success:!0,message:`Dry run: Would fix inconsistency for business ${i}`,dry_run:!0,before:g,after:{subscription_status:l.subscription_status,has_active_subscription:b,plan_id:l.plan_id},changes_needed:{has_active_subscription:`${_.has_active_subscription} → ${b}`}});let{data:m,error:y}=await a.rpc("fix_subscription_inconsistency",{target_business_profile_id:i});if(y)return console.error("[FIX_INCONSISTENCY] Error fixing inconsistency:",y),o.NextResponse.json({success:!1,message:`Error fixing inconsistency: ${y.message}`},{status:500});s=l.razorpay_subscription_id?await (0,u.lJ)(l.razorpay_subscription_id):{consistent:!0,details:"No Razorpay subscription ID to verify"};let f={subscription_status:l.subscription_status,has_active_subscription:b,plan_id:l.plan_id};return await a.from("system_alerts").insert({alert_type:"SUBSCRIPTION_INCONSISTENCY",severity:"MEDIUM",message:`Fixed subscription inconsistency for business ${i}`,entity_id:i,subscription_id:l.razorpay_subscription_id,metadata:{action:"manual_fix",before:g,after:f,fix_result:m,verification:s},resolved:!0,resolved_at:new Date().toISOString()}),console.log(`[FIX_INCONSISTENCY] Successfully fixed inconsistency for business ${i}`),o.NextResponse.json({success:!0,message:m||`Fixed inconsistency for business ${i}`,before:g,after:f,verification:s,changes_made:{has_active_subscription:`${_.has_active_subscription} → ${b}`}})}catch(s){console.error("[FIX_INCONSISTENCY] Error during fix operation:",s);let e=s instanceof Error?s.message:String(s);return o.NextResponse.json({success:!1,message:"Fix operation failed",error:e},{status:500})}}async function p(e){try{let s=e.headers.get("x-api-key")||"",t=process.env.ADMIN_API_KEY||process.env.WEBHOOK_RETRY_API_KEY;if(!t||s!==t)return o.NextResponse.json({success:!1,message:"Invalid or missing API key"},{status:401});let r=await (0,c.createClient)(),{data:i,error:n}=await r.rpc("find_subscription_inconsistencies");if(n)return console.error("[FIX_INCONSISTENCY] Error getting inconsistencies:",n),o.NextResponse.json({success:!1,message:`Error getting inconsistencies: ${n.message}`},{status:500});return o.NextResponse.json({success:!0,count:i?.length||0,inconsistencies:i||[]})}catch(e){return console.error("[FIX_INCONSISTENCY] Error getting inconsistencies:",e),o.NextResponse.json({success:!1,message:"Failed to get inconsistencies",error:e instanceof Error?e.message:String(e)},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/fix-subscription-inconsistency/route",pathname:"/api/admin/fix-subscription-inconsistency",filename:"route",bundlePath:"app/api/admin/fix-subscription-inconsistency/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\admin\\fix-subscription-inconsistency\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:b,serverHooks:g}=_;function m(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:b})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,9398,4386,580],()=>t(54573));module.exports=r})();