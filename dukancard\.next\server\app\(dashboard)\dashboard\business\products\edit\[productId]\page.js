(()=>{var e={};e.id=8385,e.ids=[8385],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5113:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=t(65239),i=t(48088),s=t(88170),n=t.n(s),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let l={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["products",{children:["edit",{children:["[productId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,50430)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/products/edit/[productId]/page",pathname:"/dashboard/business/products/edit/[productId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14039:(e,r,t)=>{Promise.resolve().then(t.bind(t,21895))},18767:(e,r,t)=>{Promise.resolve().then(t.bind(t,22709))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21895:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\products\\\\edit\\\\[productId]\\\\EditProductClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx","default")},22709:(e,r,t)=>{"use strict";t.d(r,{default:()=>m});var a=t(60687),i=t(43210),s=t(16189),n=t(77882),d=t(52581),o=t(19080),l=t(28559),c=t(24934),u=t(41974),p=t(42933);function m({product:e,variants:r,planLimit:t,currentAvailableCount:m}){let b=(0,s.useRouter)(),[v,h]=(0,i.useTransition)(),f=r?.map(e=>e.id)||[],g={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.4}}},x=async(r,t,a,i)=>{h(async()=>{let s=new FormData;Object.entries(r).forEach(([e,r])=>{null!=r&&("boolean"==typeof r?s.append(e,r.toString()):s.append(e,String(r)))}),void 0!==a&&s.append("featuredImageIndex",String(a)),i&&i.length>0&&s.append("removedImageIndices",JSON.stringify(i)),t&&t.length>0&&t.forEach((e,r)=>{e&&s.append(`productImage_${r}`,e)});try{let t=await (0,p.G3)(e.id,s);if(t.success&&t.data){if(r.variants){d.oR.success("Product updated! Processing variants...");let t=0,a=0,i=r.variants.map(e=>e.id);for(let e of f.filter(e=>!i.includes(e)&&!e.startsWith("temp-")))try{let r=await (0,p.eF)(e);r.success?t++:(a++,console.error("Failed to delete variant:",e,r.error))}catch(r){a++,console.error("Error deleting variant:",e,r)}for(let i of r.variants)try{let r=new FormData;if(r.append("product_id",e.id),r.append("variant_name",i.variant_name),r.append("variant_values",JSON.stringify(i.variant_values)),void 0!==i.base_price&&null!==i.base_price&&i.base_price>0&&r.append("base_price",i.base_price.toString()),void 0!==i.discounted_price&&null!==i.discounted_price&&i.discounted_price>0&&r.append("discounted_price",i.discounted_price.toString()),r.append("is_available",i.is_available?"true":"false"),r.append("featured_image_index",(i.featured_image_index??0).toString()),i._removedImageIndices&&i._removedImageIndices.length>0&&r.append("remove_images",JSON.stringify(i._removedImageIndices)),i._imageFiles&&i._imageFiles.length>0&&i._imageFiles.forEach((e,t)=>{e&&r.append(`images[${t}]`,e)}),i.id.startsWith("temp-")){let e=await (0,p.yQ)(r);e.success?t++:(a++,console.error("Failed to create variant:",i.variant_name,e.error))}else{let e=await (0,p.Uz)(i.id,r);e.success?t++:(a++,console.error("Failed to update variant:",i.variant_name,e.error))}}catch(e){a++,console.error("Error processing variant:",i.variant_name,e)}t>0&&0===a?d.oR.success("Product and all variants updated successfully!"):t>0?d.oR.warning(`Product updated! ${t} variant${t>1?"s":""} processed successfully, ${a} failed.`):a>0&&d.oR.warning(`Product updated, but all ${a} variant${a>1?"s":""} failed to process.`)}else d.oR.success("Product updated successfully!");b.push("/dashboard/business/products")}else{let e=t.error||"Failed to update product";e.includes("Image exceeds 15MB limit")?d.oR.error("Image too large",{description:"Please select images smaller than 15MB each"}):e.includes("Invalid file type")?d.oR.error("Invalid file type",{description:"Please select JPG, PNG, WebP, or GIF images"}):e.includes("Body exceeded")?d.oR.error("Upload size limit exceeded",{description:"Please try uploading fewer images or smaller file sizes"}):d.oR.error("Failed to update product",{description:e})}}catch(e){console.error("Error updating product:",e),d.oR.error("An unexpected error occurred")}})};return(0,a.jsxs)(n.P.div,{className:"space-y-8",variants:g,initial:"hidden",animate:"visible",children:[(0,a.jsxs)(n.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",variants:{hidden:{opacity:0,y:-20},visible:{opacity:1,y:0,transition:{delay:.1,duration:.4,type:"spring",stiffness:200,damping:20}}},children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,a.jsx)(o.A,{className:"w-5 h-5 text-primary"})}),(0,a.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Product Management"})]}),(0,a.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Edit Product"}),(0,a.jsxs)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:['Update details for "',e.name,'" with advanced features and inventory management.']})]}),(0,a.jsx)("div",{className:"flex items-center gap-3",children:(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>b.push("/dashboard/business/products"),className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"Back to Products"]})})]}),(0,a.jsx)(n.P.div,{variants:g,children:(0,a.jsx)(u.A,{initialData:e,initialVariants:r,onSubmit:x,isSubmitting:v,isEditing:!0,planLimit:t,currentAvailableCount:m})})]})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},50430:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>o});var a=t(37413),i=t(32032),s=t(39916),n=t(21895),d=t(20670);let o={title:"Edit Product",robots:"noindex, nofollow"};async function l({params:e}){let{productId:r}=await e,t=await (0,i.createClient)(),{data:{user:o}}=await t.auth.getUser();if(!o)return(0,s.redirect)("/login?message=Authentication required");let{data:l,error:c}=await t.from("products_services").select(`
      id,
      business_id,
      product_type,
      name,
      description,
      base_price,
      discounted_price,
      is_available,
      image_url,
      images,
      featured_image_index,
      created_at,
      updated_at,
      slug
    `).eq("id",r).eq("business_id",o.id).single();(c||!l)&&(console.error("Error fetching product:",c?.message),(0,s.notFound)());let{data:u,error:p}=await t.from("payment_subscriptions").select("plan_id").eq("business_profile_id",o.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();p&&console.error("Error fetching subscription data:",p);let m=u?.plan_id||"free",b=(0,d.RL)(m),{count:v,error:h}=await t.from("products_services").select("id",{count:"exact",head:!0}).eq("business_id",o.id).eq("is_available",!0).not("id","eq",r);h&&console.error("Error counting available products:",h);let f=(v||0)+ +!!l.is_available,{data:g,error:x}=await t.from("product_variants").select(`
      id,
      product_id,
      variant_name,
      variant_values,
      base_price,
      discounted_price,
      is_available,
      images,
      featured_image_index,
      created_at,
      updated_at
    `).eq("product_id",r).order("created_at",{ascending:!0});x&&console.error("Error fetching product variants:",x);let _=(g||[]).map(e=>({...e,variant_values:"string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values})),y={...l,base_price:l.base_price||0,product_type:l.product_type||"physical",description:l.description||void 0,slug:l.slug||void 0};return(0,a.jsx)(n.default,{product:y,variants:_,planLimit:b,currentAvailableCount:f})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,1753,6380,5880,8567,4851,3442,2836,3064,3037,3739,9538,5265,9209,4812,9648,4685,8382],()=>t(5113));module.exports=a})();