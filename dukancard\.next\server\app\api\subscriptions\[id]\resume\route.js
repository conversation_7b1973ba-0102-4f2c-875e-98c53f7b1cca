(()=>{var e={};e.id=3432,e.ids=[3432,5453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{createClient:()=>i});var r=s(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let i=null,n=null;try{let{headers:e,cookies:t}=await s.e(4999).then(s.bind(s,44999));i=await e(),n=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,i=e.get("x-test-business-slug"),n=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,i){let n=()=>{var n,a,o,u,c;return n=e,a=t,o=s,u=r,c=i,"customer_profiles"===n?{data:o&&"customer"===a?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===n?{data:o&&"business"===a?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===n?{data:"business"===a?{id:"test-subscription-id",plan_id:c,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===n?{data:"business"===a?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},a=e=>({select:t=>a(e),eq:(t,s)=>a(e),neq:(t,s)=>a(e),gt:(t,s)=>a(e),gte:(t,s)=>a(e),lt:(t,s)=>a(e),lte:(t,s)=>a(e),like:(t,s)=>a(e),ilike:(t,s)=>a(e),is:(t,s)=>a(e),in:(t,s)=>a(e),contains:(t,s)=>a(e),containedBy:(t,s)=>a(e),rangeGt:(t,s)=>a(e),rangeGte:(t,s)=>a(e),rangeLt:(t,s)=>a(e),rangeLte:(t,s)=>a(e),rangeAdjacent:(t,s)=>a(e),overlaps:(t,s)=>a(e),textSearch:(t,s)=>a(e),match:t=>a(e),not:(t,s,r)=>a(e),or:t=>a(e),filter:(t,s,r)=>a(e),order:(t,s)=>a(e),limit:(t,s)=>a(e),range:(t,s,r)=>a(e),abortSignal:t=>a(e),single:async()=>n(),maybeSingle:async()=>n(),then:async e=>{let t=n();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>a(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>a(e),upsert:e=>a(e),delete:()=>a(),rpc:(e,t)=>a()}})(e,s,r,i,n)}}(i):n?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await n.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await n.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},42379:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>_,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>b});var r={};s.r(r),s.d(r,{POST:()=>l});var i=s(96559),n=s(48088),a=s(37719),o=s(32190),u=s(32032),c=s(31546);async function l(e,{params:t}){try{let{id:e}=await t,s=await (0,u.createClient)(),{data:{user:r},error:i}=await s.auth.getUser();if(i||!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let{data:n,error:a}=await s.from("payment_subscriptions").select("*").eq("razorpay_subscription_id",e).single();if(a){if("PGRST116"===a.code)return o.NextResponse.json({success:!1,error:"Subscription not found"},{status:404});return console.error("[RAZORPAY_ERROR] Error fetching subscription:",a),o.NextResponse.json({success:!1,error:"Error fetching subscription"},{status:500})}if(n.business_profile_id!==r.id)return o.NextResponse.json({success:!1,error:"Unauthorized to resume this subscription"},{status:403});if("halted"!==n.subscription_status)return o.NextResponse.json({success:!1,error:"Subscription is not paused"},{status:400});let l=await (0,c.fK)(e);if(!l.success)return o.NextResponse.json({success:!1,error:l.error},{status:400});let p=new Date().toISOString(),{error:d}=await s.from("payment_subscriptions").update({subscription_status:"active",subscription_paused_at:null,updated_at:p}).eq("razorpay_subscription_id",e);d&&console.error("[RAZORPAY_ERROR] Error updating subscription record:",d);let{data:b,error:_}=await s.rpc("update_subscription_atomic",{p_subscription_id:e,p_new_status:"active",p_business_profile_id:n.business_profile_id,p_has_active_subscription:!0,p_additional_data:{subscription_paused_at:null},p_webhook_timestamp:void 0});return _||!b?.success?console.error("[RAZORPAY_ERROR] Error updating subscription atomically:",_||b?.error):(console.log(`[RAZORPAY_DEBUG] Successfully updated subscription ${e} and business profile ${n.business_profile_id} atomically`),console.log("[RAZORPAY_DEBUG] Note: Business profile status remains offline until user explicitly sets it back to online")),o.NextResponse.json({success:!0,data:{...l.data,db_subscription:{id:n.id,subscription_status:"active",subscription_paused_at:null}}},{status:200})}catch(e){return console.error("[RAZORPAY_ERROR] Error resuming subscription:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/resume/route",pathname:"/api/subscriptions/[id]/resume",filename:"route",bundlePath:"app/api/subscriptions/[id]/resume/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\resume\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:b,serverHooks:_}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:b})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95453:(e,t,s)=>{"use strict";s.d(t,{ST:()=>n,bG:()=>o,t6:()=>u});var r=s(55511),i=s.n(r);let n="https://api.razorpay.com/v2",a=()=>{let e,t;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",t=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!t)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!t,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:t}},o=()=>{let{keyId:e,keySecret:t}=a(),s=Buffer.from(`${e}:${t}`).toString("base64");return{Authorization:`Basic ${s}`,"Content-Type":"application/json"}},u=(e,t,s)=>{try{let r=i().createHmac("sha256",s).update(e).digest("hex");return i().timingSafeEqual(Buffer.from(t),Buffer.from(r))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,9398,4386,580,1546],()=>s(42379));module.exports=r})();