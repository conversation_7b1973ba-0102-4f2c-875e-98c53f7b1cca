(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1489],{13717:(e,t,o)=>{"use strict";o.d(t,{A:()=>i});let i=(0,o(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},35941:(e,t,o)=>{Promise.resolve().then(o.bind(o,71604))},37108:(e,t,o)=>{"use strict";o.d(t,{A:()=>i});let i=(0,o(19946).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},47924:(e,t,o)=>{"use strict";o.d(t,{A:()=>i});let i=(0,o(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},49103:(e,t,o)=>{"use strict";o.d(t,{A:()=>i});let i=(0,o(19946).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},71604:(e,t,o)=>{"use strict";o.d(t,{default:()=>h});var i=o(95155);o(12115);var a=o(49103),r=o(13717),s=o(47924),n=o(81586),d=o(72713),c=o(37108),u=o(71199);let p=[{id:1,question:"How many products can I add to my digital card?",answer:"The number of products you can add depends on your subscription plan. The Free plan allows up to 5 products, the Basic plan allows up to 15 products, and the Growth plan allows up to 50 products. Pro and Enterprise plans offer unlimited products."},{id:2,question:"What information should I include for each product?",answer:"For best results, include a clear product name, detailed description, accurate pricing, high-quality images, and specify if the product is in stock. The more information you provide, the easier it is for customers to make decisions."},{id:3,question:"How do I add a new product to my storefront?",answer:"Go to your dashboard, click on 'Products & Services' in the sidebar menu, then click the 'Add New Item' button. Fill in the product details in the form that appears, upload an image, and click 'Save' to publish the product to your storefront."},{id:4,question:"What image size and format should I use for product photos?",answer:"For optimal display, use square images with dimensions of at least 800x800 pixels. Supported formats include JPG, PNG, and WebP. The system will automatically resize and optimize your images for different devices."},{id:5,question:"How do I edit or delete a product?",answer:"In your dashboard, go to the 'Products & Services' section. Hover over the product you want to modify and click the edit (pencil) icon to update details or the delete (trash) icon to remove the product. On mobile, tap on the product to reveal these options."},{id:6,question:"Can I organize my products into categories?",answer:"Currently, products are displayed in the order you set. You can use the drag-and-drop feature to arrange products in your preferred order. Category functionality will be added in future updates."},{id:7,question:"How do I set the price for my products?",answer:"When adding or editing a product, you can set the base price and discount price. If a discount price is provided, it will be displayed as the current price with the base price shown with a strikethrough, and the percentage discount will be calculated automatically."},{id:8,question:"Can customers purchase products directly through my digital card?",answer:"Currently, Dukancard focuses on showcasing your products and facilitating contact. Customers can reach out via the WhatsApp or Call buttons to inquire about or purchase products. Direct e-commerce functionality may be added in future updates."},{id:9,question:"How do I mark a product as out of stock?",answer:"When adding or editing a product, you can toggle the 'In Stock' switch to indicate availability. Out-of-stock products will be visually marked on your public card to inform customers."},{id:10,question:"Can I add services instead of physical products?",answer:"Yes, the product management system can be used for both physical products and services. Simply add your service as you would a product, with appropriate descriptions and pricing information."}],l=[{id:"adding-products",title:"Adding Products to Your Storefront",icon:(0,i.jsx)(a.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Accessing the Product Management Section",steps:["Log in to your Dukancard account and navigate to your dashboard.","Click on 'Products & Services' in the sidebar menu to access the product management interface.","You'll see a list of your existing products (if any) and an 'Add New Item' button at the top.","The dashboard shows how many product slots you've used out of your plan's allocation.","You can also see basic statistics about your products, such as view counts."],image:"/support/product-dashboard.jpg",imageAlt:"Product management dashboard",tip:"Regularly review your product list to ensure all information is up-to-date. Outdated information can lead to customer confusion and lost sales."},{title:"Creating a New Product",steps:["Click the 'Add New Item' button to open the product creation form.","Enter a clear, descriptive product name that customers will easily understand.","Write a detailed description highlighting key features, benefits, and specifications.","Set the base price of your product (required) and discount price (if applicable).","Toggle the 'In Stock' switch to indicate product availability."],image:"/support/add-product-form.jpg",imageAlt:"Add product form",tip:"Be specific and detailed in your product descriptions. Include dimensions, materials, usage instructions, or any other information that helps customers make informed decisions."},{title:"Uploading Product Images",steps:["In the product form, click on the image upload area to select a photo from your device.","Choose a high-quality image that clearly shows your product from the best angle.","For best results, use square images with dimensions of at least 800x800 pixels.","Supported formats include JPG, PNG, and WebP.","The system will automatically optimize your image for different devices and screen sizes."],image:"/support/product-image-upload.jpg",imageAlt:"Product image upload",tip:"High-quality images significantly increase customer interest. Use good lighting, a clean background, and show the product from multiple angles if possible."}]},{id:"managing-products",title:"Managing Your Product Inventory",icon:(0,i.jsx)(r.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Editing Product Information",steps:["In the Products & Services section of your dashboard, locate the product you want to modify.","Hover over the product card and click the edit (pencil) icon. On mobile, tap the product to reveal options.","Update any information in the form that appears, such as name, description, or pricing.","You can also replace the product image by clicking on the image upload area.","Click 'Save Changes' to update the product information on your public card."],image:"/support/edit-product.jpg",imageAlt:"Editing product information",tip:"Regularly update your product information to reflect any changes in pricing, features, or availability. This helps maintain customer trust."},{title:"Setting Product Pricing",steps:["When adding or editing a product, you'll see fields for 'Base Price' and 'Discount Price'.","Enter the regular price in the 'Base Price' field (required).","If you're offering a discount, enter the reduced price in the 'Discount Price' field.","When a discount price is provided, it will be displayed as the current price.","The base price will be shown with a strikethrough, and the percentage discount will be calculated automatically."],image:"/support/product-pricing.jpg",imageAlt:"Setting product pricing",tip:"Strategic pricing with visible discounts can drive customer interest. Consider offering special discounts for seasonal promotions or to clear inventory."},{title:"Removing Products",steps:["To remove a product, find it in your product list in the Products & Services section.","Hover over the product card and click the delete (trash) icon. On mobile, tap the product to reveal options.","A confirmation dialog will appear asking you to confirm the deletion.","Click 'Delete' to permanently remove the product from your storefront.","Note that deleted products cannot be recovered, so be certain before confirming."],image:"/support/delete-product.jpg",imageAlt:"Removing a product",tip:"Instead of deleting seasonal or temporarily unavailable products, consider marking them as out of stock. This way, you can easily make them available again without recreating the listing."}]},{id:"optimizing-products",title:"Optimizing Your Product Listings",icon:(0,i.jsx)(s.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Writing Effective Product Descriptions",steps:["Keep your product names clear, concise, and descriptive (recommended: 3-5 words).","In the description, highlight the key benefits and features that solve customer problems.","Include important specifications like size, color, material, or dimensions.","Use simple, conversational language that's easy for customers to understand.","Avoid technical jargon unless your target audience is familiar with it."],image:"/support/product-description.jpg",imageAlt:"Writing product descriptions",tip:"Focus on how the product benefits the customer rather than just listing features. For example, instead of just saying 'Waterproof material,' say 'Stays dry even in heavy rain, keeping your belongings protected.'"},{title:"Organizing Your Product Display",steps:["Products are displayed on your public card in the order they appear in your Products & Services section.","Use the drag-and-drop feature to arrange products in your preferred order.","Place your best-selling or featured products at the top for maximum visibility.","Group similar products together for a more organized appearance.","Consider seasonal arrangements, bringing timely or seasonal products to the top during relevant periods."],image:"/support/product-organization.jpg",imageAlt:"Organizing product display",tip:"Regularly refresh your product order to keep your storefront looking dynamic. Feature different products at the top to give all your offerings a chance at visibility."},{title:"Using Product Availability Status",steps:["When adding or editing a product, use the 'In Stock' toggle to indicate availability.","Products marked as out of stock will be visually indicated on your public card.","Update the stock status promptly when inventory changes to avoid customer disappointment.","For made-to-order items, you can mention this in the description while keeping the item in stock.","Consider adding expected restock dates in the description for out-of-stock items."],image:"/support/product-availability.jpg",imageAlt:"Product availability status",tip:"Transparency about product availability builds trust with customers. It's better to mark items as out of stock than to have customers inquire about unavailable products."}]}];function h(){let e=[{title:"Business Card Setup",description:"Learn how to create and customize your digital business card",icon:(0,i.jsx)(n.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/business-card-setup"},{title:"Analytics & Insights",description:"Understand how visitors interact with your digital card",icon:(0,i.jsx)(d.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/analytics"}];return(0,i.jsx)(u.A,{title:"Product Management",description:"Learn how to add, edit, and manage products in your digital storefront to showcase your offerings effectively.",icon:(0,i.jsx)(c.A,{className:"w-12 h-12 text-[var(--brand-gold)]"}),quickHelp:[{title:"Product Management Tips:",items:[{text:"Access products in Products & Services section"},{text:"Use square images (800x800px) for best results"},{text:"Keep product names clear and concise (3-5 words)"}]},{title:"Product Optimization:",items:[{text:"Set both base price and discount price for promotions"},{text:"Use drag-and-drop to arrange product order"},{text:"Update stock status promptly to avoid confusion"}]}],guideSections:l,faqs:p,relatedResources:e,navigationButtons:[{label:"Adding Products",href:"#adding-products"},{label:"Managing Products",href:"#managing-products"},{label:"Optimizing Listings",href:"#optimizing-products"},{label:"FAQs",href:"#faqs"}]})}},72713:(e,t,o)=>{"use strict";o.d(t,{A:()=>i});let i=(0,o(19946).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,1893,3580,8441,1684,7358],()=>t(35941)),_N_E=e.O()}]);