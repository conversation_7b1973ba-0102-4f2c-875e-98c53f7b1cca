(()=>{var e={};e.id=2441,e.ids=[2441,5357],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{createClient:()=>n});var r=s(34386);async function n(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let n=null,i=null;try{let{headers:e,cookies:t}=await s.e(4999).then(s.bind(s,44999));n=await e(),i=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||n&&"true"===n.get("x-playwright-testing"))&&n?function(e){let t=e.get("x-test-auth-state"),s=e.get("x-test-user-type"),r="customer"===s||"business"===s,n=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,s,r,n){let i=()=>{var i,a,o,u,l;return i=e,a=t,o=s,u=r,l=n,"customer_profiles"===i?{data:o&&"customer"===a?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:o&&"business"===a?{id:"test-user-id",business_slug:u||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===a?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===a?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},a=e=>({select:t=>a(e),eq:(t,s)=>a(e),neq:(t,s)=>a(e),gt:(t,s)=>a(e),gte:(t,s)=>a(e),lt:(t,s)=>a(e),lte:(t,s)=>a(e),like:(t,s)=>a(e),ilike:(t,s)=>a(e),is:(t,s)=>a(e),in:(t,s)=>a(e),contains:(t,s)=>a(e),containedBy:(t,s)=>a(e),rangeGt:(t,s)=>a(e),rangeGte:(t,s)=>a(e),rangeLt:(t,s)=>a(e),rangeLte:(t,s)=>a(e),rangeAdjacent:(t,s)=>a(e),overlaps:(t,s)=>a(e),textSearch:(t,s)=>a(e),match:t=>a(e),not:(t,s,r)=>a(e),or:t=>a(e),filter:(t,s,r)=>a(e),order:(t,s)=>a(e),limit:(t,s)=>a(e),range:(t,s,r)=>a(e),abortSignal:t=>a(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let t=i();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>a(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),then:async t=>{let s={data:Array.isArray(e)?e:[e],error:null};return t?t(s):s}}),update:e=>a(e),upsert:e=>a(e),delete:()=>a(),rpc:(e,t)=>a()}})(e,s,r,n,i)}}(n):i?(0,r.createServerClient)(e,t,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:t,value:s,options:r}of e)await i.set(t,s,r)}catch{}}}}):(0,r.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},41177:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var n=s(96559),i=s(48088),a=s(37719),o=s(62346),u=e([o]);o=(u.then?(await u)():u)[0];let c=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/webhooks/razorpay/route",pathname:"/api/webhooks/razorpay",filename:"route",bundlePath:"app/api/webhooks/razorpay/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:y}=c;function l(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}r()}catch(e){r(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55357:(e,t,s)=>{"use strict";function r(e,t=5){let s=Math.floor(Date.now()/1e3),n=60*t;return e>s+60?{valid:!1,error:`Webhook timestamp is in the future: ${e} > ${s}`}:s-e>n?{valid:!1,error:`Webhook timestamp is too old: ${s-e} seconds > ${n} seconds`}:{valid:!0}}function n(e,t){let s=[],n=[],i=function(e){let t=[],s=[];if(!e||"object"!=typeof e)return t.push("Payload must be an object"),{valid:!1,errors:t,warnings:s};if(e.event&&"string"==typeof e.event||t.push("Missing or invalid event type"),e.account_id&&"string"==typeof e.account_id||t.push("Missing or invalid account_id"),e.entity&&"string"==typeof e.entity||t.push("Missing or invalid entity type"),e.payload&&"object"==typeof e.payload||t.push("Missing or invalid payload object"),e.created_at)if("number"!=typeof e.created_at)t.push("created_at must be a number (Unix timestamp)");else{let t=r(e.created_at);t.valid||s.push(`Timestamp validation: ${t.error}`)}if("string"==typeof e.event&&e.event.startsWith("subscription.")){let r=e.payload;if(r.subscription){let e=r.subscription;if(e.entity&&"object"==typeof e.entity){let r=e.entity;r.id&&"string"==typeof r.id||t.push("Subscription entity must contain valid id"),r.status&&"string"==typeof r.status||s.push("Subscription entity should contain status")}else t.push("Subscription data must contain entity object")}else t.push("Subscription events must contain subscription data")}if("string"==typeof e.event&&e.event.startsWith("payment.")){let r=e.payload;if(r.payment){let e=r.payment;if(e.entity&&"object"==typeof e.entity){let r=e.entity;r.id&&"string"==typeof r.id||t.push("Payment entity must contain valid id"),r.amount&&"number"==typeof r.amount||s.push("Payment entity should contain amount")}else t.push("Payment data must contain entity object")}else t.push("Payment events must contain payment data")}return{valid:0===t.length,errors:t,warnings:s}}(e);if(s.push(...i.errors),n.push(...i.warnings),i.valid&&e){if("string"==typeof e.account_id){var a;let r=(a=e.account_id,t&&a!==t?{valid:!1,error:`Account ID mismatch: received ${a}, expected ${t}`}:{valid:!0});!r.valid&&r.error&&s.push(r.error)}if("number"==typeof e.created_at){let t=r(e.created_at);!t.valid&&t.error&&n.push(`Timestamp validation: ${t.error}`)}let i=function(e){let t=[],s=[],r=e.event,n=e.payload;if(!r)return t.push("Missing event type in webhook payload"),{errors:t,warnings:s};if(!n)return t.push("Missing payload in webhook data"),{errors:t,warnings:s};if(r.startsWith("subscription.")){let e=function(e,t){let s=[],r=[],n=t.subscription;if(!n||!n.entity)return s.push("Missing subscription entity in payload"),{errors:s,warnings:r};let i=n.entity;i.id||s.push("Missing subscription ID in entity"),i.status||s.push("Missing subscription status in entity"),i.plan_id||r.push("Missing plan_id in subscription entity");let a=i.status;switch(a&&!["created","authenticated","active","pending","halted","cancelled","completed","expired"].includes(a)&&r.push(`Unknown subscription status: ${a}`),e){case"subscription.activated":"active"!==a&&r.push(`Subscription activated event but status is ${a}, expected 'active'`);break;case"subscription.cancelled":"cancelled"!==a&&r.push(`Subscription cancelled event but status is ${a}, expected 'cancelled'`);break;case"subscription.charged":t.payment||s.push("Missing payment data in subscription.charged event");break;case"subscription.completed":"completed"!==a&&r.push(`Subscription completed event but status is ${a}, expected 'completed'`)}return{errors:s,warnings:r}}(r,n);t.push(...e.errors),s.push(...e.warnings)}if(r.startsWith("payment.")){let e=function(e,t){let s=[],r=[],n=t.payment;if(!n||!n.entity)return s.push("Missing payment entity in payload"),{errors:s,warnings:r};let i=n.entity;i.id||s.push("Missing payment ID in entity"),i.status||s.push("Missing payment status in entity"),i.amount||r.push("Missing payment amount in entity");let a=i.status;switch(a&&!["created","authorized","captured","refunded","failed"].includes(a)&&r.push(`Unknown payment status: ${a}`),e){case"payment.authorized":"authorized"!==a&&r.push(`Payment authorized event but status is ${a}, expected 'authorized'`);break;case"payment.captured":"captured"!==a&&r.push(`Payment captured event but status is ${a}, expected 'captured'`);break;case"payment.failed":"failed"!==a&&r.push(`Payment failed event but status is ${a}, expected 'failed'`)}return{errors:s,warnings:r}}(r,n);t.push(...e.errors),s.push(...e.warnings)}if(r.startsWith("invoice.")){let e=function(e,t){let s=[],r=[],n=t.invoice;if(!n||!n.entity)return s.push("Missing invoice entity in payload"),{errors:s,warnings:r};let i=n.entity;i.id||s.push("Missing invoice ID in entity"),i.status||s.push("Missing invoice status in entity");let a=i.status;return a&&!["draft","issued","partially_paid","paid","cancelled","expired"].includes(a)&&r.push(`Unknown invoice status: ${a}`),"invoice.paid"===e&&("paid"!==a&&r.push(`Invoice paid event but status is ${a}, expected 'paid'`),t.payment||s.push("Missing payment data in invoice.paid event")),{errors:s,warnings:r}}(r,n);t.push(...e.errors),s.push(...e.warnings)}return{errors:t,warnings:s}}(e);s.push(...i.errors),n.push(...i.warnings)}return{valid:0===s.length,errors:s,warnings:n}}function i(e){try{let t=e.event;if(t.startsWith("subscription."))return e.payload.subscription?.id||null;if(t.startsWith("payment."))return e.payload.payment?.entity?.id||null;if(t.startsWith("invoice."))return e.payload.invoice?.entity?.id||null;if(t.startsWith("refund."))return e.payload.refund?.entity?.id||null;return null}catch(e){return console.error("[WEBHOOK_VALIDATION] Error extracting entity ID:",e),null}}s.d(t,{Su:()=>n,extractEntityId:()=>i})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62346:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{POST:()=>l});var n=s(32190),i=s(33243),a=s(11337),o=s(55357),u=e([i]);async function l(e){try{let t,s;e.clone();try{if(t=await e.text(),console.log("[RAZORPAY_WEBHOOK] Received webhook with body length:",t.length),!t||""===t.trim())return console.error("[RAZORPAY_WEBHOOK] Empty request body received"),n.NextResponse.json({success:!1,message:"Empty request body received"},{status:400})}catch(e){return console.error("[RAZORPAY_WEBHOOK] Failed to read request body:",e),n.NextResponse.json({success:!1,message:"Failed to read request body"},{status:400})}let r=e.headers.get("x-razorpay-signature")||"";if(!r)return console.error("[RAZORPAY_WEBHOOK] Missing signature header"),n.NextResponse.json({success:!1,message:"Missing signature header"},{status:400});let u=e.headers.get("x-razorpay-event-id")||"";try{if(!(s=JSON.parse(t))||"object"!=typeof s||0===Object.keys(s).length)return console.error("[RAZORPAY_WEBHOOK] Empty or invalid payload object"),n.NextResponse.json({success:!1,message:"Empty or invalid payload object"},{status:400})}catch(i){console.error("[RAZORPAY_WEBHOOK] Failed to parse JSON payload:",i);let r=e.headers.get("content-type")||"";if(r.includes("application/json"))return n.NextResponse.json({success:!1,message:"Invalid JSON payload"},{status:400});s={rawContent:t,contentType:r,event:"unknown"}}let l=process.env.RAZORPAY_ACCOUNT_ID,c=(0,o.Su)(s,l);if(!c.valid)return console.error("[RAZORPAY_WEBHOOK] Webhook validation failed:",c.errors),n.NextResponse.json({success:!1,message:"Webhook validation failed",errors:c.errors},{status:400});c.warnings.length>0&&console.warn("[RAZORPAY_WEBHOOK] Webhook validation warnings:",c.warnings);let p=s.event;if(!p)return console.error("[RAZORPAY_WEBHOOK] Missing event type in payload"),n.NextResponse.json({success:!1,message:"Missing event type in payload"},{status:400});let d=Object.values(a.mz).includes(p);if(console.log(`[RAZORPAY_WEBHOOK] Processing ${p} event (ID: ${u})`),!d)return console.log(`[RAZORPAY_WEBHOOK] Unhandled event type: ${p}`),n.NextResponse.json({success:!0,message:"Unhandled event acknowledged"},{status:200});let y=await (0,i.handleRazorpayWebhook)(s,r,void 0,t,u);if(!y.success)return console.error(`[RAZORPAY_WEBHOOK] Error handling webhook: ${y.message}`),n.NextResponse.json({success:!1,message:y.message,error_id:y.error_id,note:"Error logged for retry. No need to resend this webhook."},{status:202});return n.NextResponse.json({success:!0,message:y.message},{status:200})}catch(t){console.error("[RAZORPAY_WEBHOOK] Error processing webhook:",t);let e=t instanceof Error?`${t.name}: ${t.message}`:String(t);return n.NextResponse.json({success:!1,message:`Error processing webhook: ${e}`,note:"Error logged for retry. No need to resend this webhook."},{status:202})}}i=(u.then?(await u)():u)[0],r()}catch(e){r(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,9398,4386,580,5193,8485,9423,5569,3243],()=>s(41177));module.exports=r})();