(()=>{var e={};e.id=8164,e.ids=[8164],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3956:(e,a,r)=>{Promise.resolve().then(r.bind(r,64522))},5336:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17581:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25334:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},27695:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>d,metadata:()=>l});var t=r(37413),s=r(32032),i=r(39916),n=r(70084);let l={title:"Settings - Dukancard Business",robots:"noindex, nofollow"};async function d(){var e;let a=await (0,s.createClient)(),{data:{user:r},error:l}=await a.auth.getUser();(l||!r)&&(0,i.redirect)("/login?message=Authentication required");let d=(e=r?.phone)?e.startsWith("91")&&12===e.length?e.substring(2):e.startsWith("+91")&&13===e.length?e.substring(3):e:null,o=r.app_metadata?.provider==="google",c=!!r.email,u=!!d,m="email";return o?m="google":u&&!c?m="phone":c&&(m="email"),(0,t.jsx)(n.default,{currentEmail:r?.email,currentPhone:d,registrationType:m})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37759:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(a,d);let o={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27695)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/settings/page",pathname:"/dashboard/business/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},39390:(e,a,r)=>{"use strict";r.d(a,{J:()=>n});var t=r(60687);r(43210);var s=r(78148),i=r(96241);function n({className:e,...a}){return(0,t.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},41550:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},41835:(e,a,r)=>{"use strict";r.r(a),r.d(a,{"00546ba3b44b1b5aeb96bb6a14ff1b7fc41225381a":()=>x,"006c62545b20ce4a9dee4ae76cb3cc282e7383fd0b":()=>b,"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>t.B,"00e4dae012fd1fa78d78881ff916619155bbfe5a13":()=>y,"00e8f070a14433b78596a187dc463547214ab707fa":()=>p,"406df61f9835ad85596559548510136ab5adcb44cc":()=>m,"40f6db33a1d20ceee7e9c2c3fab658082e75968126":()=>g,"603946f77e1dac69f9428ea02135bb55ba64d9fe1b":()=>v,"607838e0ab85cc057a08b13adf3219f03def6434c7":()=>f,"6083fff6d6a89d52f63e20263723ed072ce216ef41":()=>w,"60aa21197218e70daff6e3b37ae514a44cab915e66":()=>j,"60f9c4e1494842ac1b9682b8c0372a17177a0b9e5d":()=>h});var t=r(64275),s=r(91199);r(42087);var i=r(76881),n=r(7944),l=r(74208),d=r(68567);let o=d.z.object({email:d.z.string().email({message:"Please enter a valid email address."})}),c=d.z.object({email:d.z.string().email("Invalid email address.")}),u=d.z.object({email:d.z.string().email("Invalid email address."),otp:d.z.string().min(6,"OTP must be 6 digits.").max(6,"OTP must be 6 digits.")});async function m(e){let a=await (0,i.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r)return{success:!1,message:"Authentication required."};let s=o.safeParse(e);if(!s.success)return{success:!1,message:"Invalid email format."};let{email:n}=s.data,{error:l}=await a.auth.updateUser({email:n});return l?(console.error("Update Email Error:",l),{success:!1,message:l.message||"Failed to initiate email update."}):{success:!0,message:"Confirmation email sent to the new address. Please check your inbox."}}async function x(){let e=await (0,i.createClient)(),{data:{user:a},error:r}=await e.auth.getUser();if(r||!a||!a.email)return{success:!1,message:"Authentication required or no email found."};try{let{error:r}=await e.auth.signInWithOtp({email:a.email,options:{shouldCreateUser:!1}});if(r){let e="Failed to send verification code.";return(r.message?.includes("email_send_rate_limit")||r.message?.includes("over_email_send_rate_limit"))&&(e="Email rate limit exceeded. Please try again later."),{success:!1,message:e}}return{success:!0,message:"Verification code sent to your current email address.",email:a.email}}catch(e){return console.error("Error sending email change OTP:",e),{success:!1,message:"An unexpected error occurred while sending verification code."}}}async function h(e,a){let r=await (0,i.createClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return{success:!1,message:"Authentication required."};let n=u.safeParse({email:a.get("email"),otp:a.get("otp")});if(!n.success)return{success:!1,message:"Invalid data provided."};let{email:l,otp:d}=n.data;try{let{error:e}=await r.auth.verifyOtp({email:l,token:d,type:"email"});if(e){let a="Failed to verify code.";switch(e.code){case"invalid_otp":case"expired_otp":a="Invalid or expired verification code. Please try again.";break;case"too_many_requests":a="Too many verification attempts. Please wait before trying again.";break;default:a="Unable to verify code. Please try again."}return{success:!1,message:a}}return{success:!0,message:"OTP verified successfully."}}catch(e){return console.error("Error confirming email change OTP:",e),{success:!1,message:"An unexpected error occurred during OTP verification."}}}async function p(){let e=await (0,i.createClient)(),{data:{user:a},error:r}=await e.auth.getUser();return r||!a?{success:!1,hasEmail:!1,hasPhone:!1,message:"Authentication required."}:{success:!0,hasEmail:!!(a.email&&""!==a.email.trim()),hasPhone:!!(a.phone&&""!==a.phone.trim())}}async function b(){let e=await (0,i.createClient)(),{data:{user:a},error:r}=await e.auth.getUser();if(r||!a||!a.email)return{success:!1,message:"Authentication required or no email found."};try{let{error:r}=await e.auth.signInWithOtp({email:a.email,options:{shouldCreateUser:!1}});if(r){if(r.message?.includes("email_send_rate_limit")||r.message?.includes("over_email_send_rate_limit"))return{success:!1,message:"Email rate limit exceeded. Please try again later.",isConfigurationError:!0};return{success:!1,message:r.message||"Failed to send verification code."}}return{success:!0,message:"Verification code sent to your email address.",email:a.email}}catch(e){return{success:!1,message:"An unexpected error occurred while sending verification code."}}}async function f(e,a){let r=await (0,i.createClient)();try{let{error:t}=await r.auth.verifyOtp({email:e,token:a,type:"email"});if(t){let e="Failed to verify code.";switch(t.code){case"invalid_otp":case"expired_otp":e="Invalid or expired verification code. Please try again.";break;case"too_many_requests":e="Too many verification attempts. Please wait before trying again.";break;default:e="Unable to verify code. Please try again."}return{success:!1,message:e}}return{success:!0,message:"Verification successful."}}catch(e){return{success:!1,message:"An unexpected error occurred during verification."}}}async function g(e){let a=await (0,i.createClient)(),{data:{user:r},error:t}=await a.auth.getUser();if(t||!r||!r.phone)return{success:!1,message:"Authentication required or no phone found."};try{let{error:t}=await a.auth.signInWithPassword({phone:r.phone,password:e});if(t)return{success:!1,message:"Invalid password. Please try again."};return{success:!0,message:"Password verified successfully."}}catch(e){return{success:!1,message:"An unexpected error occurred during password verification."}}}async function y(){let e=await (0,i.createClient)(),{data:{user:a},error:t}=await e.auth.getUser();if(t||!a)return{success:!1,message:"Authentication required."};try{let{data:t,error:s}=await e.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",a.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(s&&console.error("Delete Account - Error fetching subscription:",s),t&&t.razorpay_subscription_id)if("authenticated"===t.subscription_status){console.log("[DELETE_ACCOUNT] Handling authenticated subscription cancellation");try{let{pauseSubscription:e}=await r.e(6763).then(r.bind(r,96763)),a=await e(t.razorpay_subscription_id,"now",!0);a.success?console.log("[DELETE_ACCOUNT] Successfully paused/cancelled authenticated subscription"):console.error("[DELETE_ACCOUNT] Error pausing authenticated subscription:",a.error)}catch(e){console.error("[DELETE_ACCOUNT] Error handling subscription cancellation:",e)}}else if("active"===t.subscription_status){console.log("[DELETE_ACCOUNT] Cancelling active subscription in Razorpay");try{let{cancelSubscription:e}=await r.e(6763).then(r.bind(r,96763)),a=await e(t.razorpay_subscription_id,!1);a.success?console.log("[DELETE_ACCOUNT] Successfully cancelled active subscription"):console.error("[DELETE_ACCOUNT] Error cancelling active subscription:",a.error)}catch(e){console.error("[DELETE_ACCOUNT] Error handling active subscription cancellation:",e)}}else if("halted"===t.subscription_status||"pending"===t.subscription_status||"cancellation_scheduled"===t.subscription_status){console.log(`[DELETE_ACCOUNT] Cancelling ${t.subscription_status} subscription in Razorpay`);try{let{cancelSubscription:e}=await r.e(6763).then(r.bind(r,96763)),a=await e(t.razorpay_subscription_id,!1);a.success?console.log(`[DELETE_ACCOUNT] Successfully cancelled ${t.subscription_status} subscription`):console.error(`[DELETE_ACCOUNT] Error cancelling ${t.subscription_status} subscription:`,a.error)}catch(e){console.error(`[DELETE_ACCOUNT] Error handling ${t.subscription_status} subscription cancellation:`,e)}}else console.log(`[DELETE_ACCOUNT] Subscription status is ${t.subscription_status} - no Razorpay cancellation needed`);try{let{createClient:e}=await Promise.resolve().then(r.bind(r,76881)),{getScalableUserPath:t}=await r.e(937).then(r.bind(r,50937)),s=await e(),i="business",n=t(a.id),l=async e=>{let{data:a,error:r}=await s.storage.from(i).list(e);if(r)return void console.error(`Error listing files in ${e}:`,r);if(!a||0===a.length)return;let t=[],n=[];for(let r of a){let a=e?`${e}/${r.name}`:r.name;null===r.metadata?n.push(a):t.push(a)}if(t.length>0){let{error:a}=await s.storage.from(i).remove(t);a&&"The resource was not found"!==a.message?console.error(`Error deleting files in ${e}:`,a):console.log(`Successfully deleted ${t.length} files in ${e}`)}for(let e of n)await l(e)};await l(n),console.log("Successfully cleaned up business storage data")}catch(e){console.error("Error cleaning up storage data:",e)}let{createClient:i}=await Promise.resolve().then(r.bind(r,76881));console.log("Deleting business profile...");let{error:l}=await e.from("business_profiles").delete().eq("id",a.id);if(l)return console.error("Error deleting business profile:",l),{success:!1,message:`Failed to delete business profile: ${l.message}`};console.log("Business profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed."),await e.auth.signOut();let{error:d}=await e.auth.admin.deleteUser(a.id,!1);if(d)return console.error("Error deleting user account:",d),{success:!1,message:`Failed to delete account: ${d.message}`};return(0,n.revalidatePath)("/","layout"),{success:!0,message:"Account deleted successfully."}}catch(e){return console.error("Delete Account - Unexpected error:",e),{success:!1,message:"An unexpected error occurred during account deletion."}}}async function v(e,a){let r=await (0,i.createClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return{message:"Not authenticated",success:!1};let n=c.safeParse({email:a.get("email")});if(!n.success)return{message:"Invalid data provided.",errors:n.error.flatten().fieldErrors,success:!1};let{email:d}=n.data;try{if(t.email)return{message:"otp_sent",success:!0};{let{error:e}=await r.auth.updateUser({email:d},{emailRedirectTo:`${(await (0,l.headers)()).get("origin")}/auth/callback?email_change_status=success&redirect_to=settings`});if(e){let a="Failed to link email address.";switch(e.code){case"email_exists":a="This email address is already registered with another account.";break;case"invalid_email":a="Please enter a valid email address.";break;case"email_change_confirm_limit":a="Too many email requests. Please wait before trying again.";break;case"over_email_send_rate_limit":a="Email rate limit exceeded. Please wait before trying again.";break;default:a="Unable to link email address. Please try again later."}return{message:a,success:!1}}return{message:"Email address linked successfully!",success:!0}}}catch(e){return{message:"An unexpected error occurred while linking email.",success:!1}}}async function j(e,a){let r=await (0,i.createClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return{message:"Not authenticated",success:!1};let n=c.safeParse({email:a.get("email")});if(!n.success)return{message:"Invalid data provided.",errors:n.error.flatten().fieldErrors,success:!1};let{email:d}=n.data;try{let{error:e}=await r.auth.updateUser({email:d},{emailRedirectTo:`${(await (0,l.headers)()).get("origin")}/auth/callback?email_change_status=success&redirect_to=settings`});if(e){let a="Failed to update email address.";switch(e.code){case"email_exists":a="This email address is already registered with another account.";break;case"invalid_email":a="Please enter a valid email address.";break;case"email_change_confirm_limit":a="Too many email change requests. Please wait before trying again.";break;case"over_email_send_rate_limit":a="Email rate limit exceeded. Please wait before requesting another verification email.";break;case"email_not_confirmed":a="Please confirm your current email address before changing it.";break;case"same_email":a="The new email address is the same as your current email.";break;default:a="Unable to update email address. Please try again later."}return{message:a,success:!1}}return{message:"Verification email sent to both old and new addresses. Please check your inbox to complete the change.",success:!0}}catch(e){return{message:"An unexpected error occurred while updating email.",success:!1}}}async function w(e,a){let r=await (0,i.createClient)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return{message:"Not authenticated",success:!1};let n=u.safeParse({email:a.get("email"),otp:a.get("otp")});if(!n.success)return{message:"Invalid data provided.",errors:n.error.flatten().fieldErrors,success:!1};let{email:l,otp:d}=n.data;try{let{error:e}=await r.auth.verifyOtp({email:l,token:d,type:"email"});if(e){let a="Failed to verify code.";switch(e.code){case"invalid_otp":case"expired_otp":a="Invalid or expired verification code. Please try again.";break;case"too_many_requests":a="Too many verification attempts. Please wait before trying again.";break;default:a="Unable to verify code. Please try again."}return{message:a,success:!1}}let{error:a}=await r.auth.updateUser({email:l});if(a)return{message:"Verification successful but failed to link email. Please contact support.",success:!1};return{message:"Email address linked successfully!",success:!0}}catch(e){return{message:"An unexpected error occurred while verifying code.",success:!1}}}(0,r(33331).D)([m,x,h,p,b,f,g,y,v,j,w]),(0,s.A)(m,"406df61f9835ad85596559548510136ab5adcb44cc",null),(0,s.A)(x,"00546ba3b44b1b5aeb96bb6a14ff1b7fc41225381a",null),(0,s.A)(h,"60f9c4e1494842ac1b9682b8c0372a17177a0b9e5d",null),(0,s.A)(p,"00e8f070a14433b78596a187dc463547214ab707fa",null),(0,s.A)(b,"006c62545b20ce4a9dee4ae76cb3cc282e7383fd0b",null),(0,s.A)(f,"607838e0ab85cc057a08b13adf3219f03def6434c7",null),(0,s.A)(g,"40f6db33a1d20ceee7e9c2c3fab658082e75968126",null),(0,s.A)(y,"00e4dae012fd1fa78d78881ff916619155bbfe5a13",null),(0,s.A)(v,"603946f77e1dac69f9428ea02135bb55ba64d9fe1b",null),(0,s.A)(j,"60aa21197218e70daff6e3b37ae514a44cab915e66",null),(0,s.A)(w,"6083fff6d6a89d52f63e20263723ed072ce216ef41",null)},43649:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44421:(e,a,r)=>{Promise.resolve().then(r.bind(r,70084))},45488:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>d});var t=r(37413);r(61120);var s=r(32032),i=r(64522);let n=["member_name","title","business_name","business_category","contact_email","phone","address_line","pincode","city","state","locality"];var l=r(39916);async function d({children:e}){let a=await (0,s.createClient)(),r=null,d=null,o=null,c=null,{data:{user:u}}=await a.auth.getUser();if(u){let{data:e,error:t}=await a.from("business_profiles").select(`
        business_name,
        logo_url,
        member_name,
        title,
        business_category,
        contact_email,
        phone,
        address_line,
        pincode,
        city,
        state,
        locality
      `).eq("id",u.id).single(),{data:s}=await a.from("payment_subscriptions").select("plan_id").eq("business_profile_id",u.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(t)console.error("Error fetching business profile in layout:",t.message);else if(e){r=e.business_name,d=e.logo_url,o=e.member_name,c=s?.plan_id||"free";let a=function(e){if(!e)return{isComplete:!1,missingFields:[...n],missingFieldLabels:["Your name","Your title","Business name","Business category","Contact email","Primary phone","Address line","Pincode","City","State","Locality/area"]};let a=[],r=[],t={member_name:"Your name",title:"Your title",business_name:"Business name",business_category:"Business category",contact_email:"Contact email",phone:"Primary phone",address_line:"Address line",pincode:"Pincode",city:"City",state:"State",locality:"Locality/area"};return n.forEach(s=>{let i=e[s];i&&""!==String(i).trim()||(a.push(s),r.push(t[s]))}),{isComplete:0===a.length,missingFields:a,missingFieldLabels:r}}(e);if(!a.isComplete){let e=function(e){if(0===e.length)return"";if(1===e.length)return`Please complete your ${e[0].toLowerCase()} to access the dashboard.`;if(2===e.length)return`Please complete your ${e[0].toLowerCase()} and ${e[1].toLowerCase()} to access the dashboard.`;let a=e[e.length-1],r=e.slice(0,-1);return`Please complete your ${r.map(e=>e.toLowerCase()).join(", ")}, and ${a.toLowerCase()} to access the dashboard.`}(a.missingFieldLabels);(0,l.redirect)(`/onboarding?message=${encodeURIComponent(e)}`)}}}else console.warn("No user found in business dashboard layout.");return(0,t.jsx)(i.default,{businessName:r,logoUrl:d,memberName:o,userPlan:c,children:e})}},46586:(e,a,r)=>{"use strict";r.d(a,{default:()=>H});var t=r(60687),s=r(43210),i=r(77882),n=r(84027),l=r(52581),d=r(68988),o=r(24934),c=r(41550),u=r(75034),m=r(96882),x=r(27605),h=r(63442),p=r(45880),b=r(37826),f=r(58164),g=r(99008),y=r(99891),v=r(41862),j=r(5336),w=r(6475);let N=(0,w.createServerReference)("00546ba3b44b1b5aeb96bb6a14ff1b7fc41225381a",w.callServer,void 0,w.findSourceMapURL,"sendEmailChangeOTP"),k=(0,w.createServerReference)("603946f77e1dac69f9428ea02135bb55ba64d9fe1b",w.callServer,void 0,w.findSourceMapURL,"linkBusinessEmail"),_=(0,w.createServerReference)("60f9c4e1494842ac1b9682b8c0372a17177a0b9e5d",w.callServer,void 0,w.findSourceMapURL,"confirmEmailChangeOTP"),A=(0,w.createServerReference)("60aa21197218e70daff6e3b37ae514a44cab915e66",w.callServer,void 0,w.findSourceMapURL,"updateBusinessEmailWithOTP"),C=p.z.object({email:p.z.string().email("Invalid email address.")}),E=p.z.object({otp:p.z.string().min(6,"OTP must be 6 digits.").max(6,"OTP must be 6 digits.")});function P({isOpen:e,onClose:a,currentEmail:r,onEmailUpdated:n}){let[u,m]=(0,s.useTransition)(),[p,w]=(0,s.useState)("email_input"),[P,S]=(0,s.useState)(""),[T,R]=(0,s.useState)(""),[U,F]=(0,s.useState)(""),L=(0,x.mN)({resolver:(0,h.u)(C),defaultValues:{email:""}}),q=(0,x.mN)({resolver:(0,h.u)(E),defaultValues:{otp:""}}),D=()=>{w("email_input"),S(""),R(""),F(""),L.reset(),q.reset(),a()};return(0,t.jsx)(b.lG,{open:e,onOpenChange:e=>!e&&D(),children:(0,t.jsxs)(b.Cf,{className:"sm:max-w-md",hideClose:!1,children:[(0,t.jsxs)(b.c7,{children:[(0,t.jsxs)(b.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"w-5 h-5 text-primary"}),"Update Email Address"]}),(0,t.jsx)(b.rr,{children:r?"Verify your current email first, then set a new email address.":"Link an email address to your account for better security."})]}),(0,t.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"space-y-6",children:["email_input"===p&&(0,t.jsx)("div",{className:"space-y-4",children:r?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:"Security Verification Required"})]}),(0,t.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["To change your email address, we need to verify your current email: ",(0,t.jsx)("strong",{children:r})]})]}),(0,t.jsx)(o.$,{onClick:()=>{m(async()=>{try{let e=await N();e.success?(S(e.email||r||""),w("otp_verification"),F(`We&apos;ve sent a 6-digit verification code to ${e.email||r}.`),l.oR.success("Verification code sent to your current email!")):(l.oR.error(e.message||"Failed to send verification code."),F(e.message||"Failed to send verification code."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})},disabled:u,className:"w-full",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending Code..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Send Verification Code"]})})]}):(0,t.jsx)(f.lV,{...L,children:(0,t.jsxs)("form",{onSubmit:L.handleSubmit(e=>{m(async()=>{try{let a=new FormData;a.append("email",e.email);let r=await k({message:null,success:!1},a);r.success?"otp_sent"===r.message?(S(e.email),w("otp_verification"),F(`We&apos;ve sent a 6-digit verification code to ${e.email}.`),l.oR.success("Verification code sent!")):(l.oR.success("Email linked successfully!"),D(),n?.()):(l.oR.error(r.message||"Failed to link email."),F(r.message||"Failed to link email."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,t.jsx)(f.zB,{control:L.control,name:"email",render:({field:e})=>(0,t.jsxs)(f.eI,{children:[(0,t.jsx)(f.lR,{children:"Email Address"}),(0,t.jsx)(f.MJ,{children:(0,t.jsx)(d.p,{type:"email",placeholder:"Enter your email address",...e,disabled:u})}),(0,t.jsx)(f.C5,{})]})}),(0,t.jsx)(o.$,{type:"submit",disabled:u,className:"w-full",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Linking Email..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Link Email Address"]})})]})})}),"otp_verification"===p&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full",children:(0,t.jsx)(c.A,{className:"w-6 h-6 text-green-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Check Your Email"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["We've sent a 6-digit code to ",(0,t.jsx)("strong",{children:P})]})]}),(0,t.jsx)(f.lV,{...q,children:(0,t.jsxs)("form",{onSubmit:q.handleSubmit(e=>{m(async()=>{try{let a=new FormData;a.append("email",P),a.append("otp",e.otp);let t=await _({email:P,otp:e.otp},a);t.success?r?(w("new_email_input"),F("OTP verified! Now enter your new email address."),l.oR.success("OTP verified! Enter your new email.")):(l.oR.success("Email verified and linked successfully!"),D(),n?.()):(l.oR.error(t.message||"Invalid OTP. Please try again."),F(t.message||"Invalid OTP. Please try again."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,t.jsx)(f.zB,{control:q.control,name:"otp",render:({field:e})=>(0,t.jsxs)(f.eI,{children:[(0,t.jsx)(f.lR,{children:"Verification Code"}),(0,t.jsx)(f.MJ,{children:(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(g.UV,{maxLength:6,...e,disabled:u,children:(0,t.jsxs)(g.NV,{children:[(0,t.jsx)(g.sF,{index:0}),(0,t.jsx)(g.sF,{index:1}),(0,t.jsx)(g.sF,{index:2}),(0,t.jsx)(g.sF,{index:3}),(0,t.jsx)(g.sF,{index:4}),(0,t.jsx)(g.sF,{index:5})]})})})}),(0,t.jsx)(f.C5,{})]})}),(0,t.jsx)(o.$,{type:"submit",disabled:u,className:"w-full",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Verifying..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"Verify Code"]})})]})})]}),"new_email_input"===p&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(j.A,{className:"w-4 h-4 text-green-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-green-900 dark:text-green-100",children:"Current Email Verified"})]}),(0,t.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:["Current email: ",(0,t.jsx)("strong",{children:r})]})]}),(0,t.jsx)(f.lV,{...L,children:(0,t.jsxs)("form",{onSubmit:L.handleSubmit(e=>{m(async()=>{try{let a=new FormData;a.append("email",e.email);let r=await A({message:null,success:!1},a);r.success?(l.oR.success("Email updated successfully!"),D(),n?.()):(l.oR.error(r.message||"Failed to update email."),F(r.message||"Failed to update email."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,t.jsx)(f.zB,{control:L.control,name:"email",render:({field:e})=>(0,t.jsxs)(f.eI,{children:[(0,t.jsx)(f.lR,{children:"New Email Address"}),(0,t.jsx)(f.MJ,{children:(0,t.jsx)(d.p,{type:"email",placeholder:"Enter your new email address",...e,disabled:u})}),(0,t.jsx)(f.C5,{})]})}),(0,t.jsx)(o.$,{type:"submit",disabled:u,className:"w-full",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Updating Email..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Update Email Address"]})})]})})]}),U&&(0,t.jsx)("div",{className:"p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,t.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:U})})]},p)]})})}function S({currentEmail:e,registrationType:a}){let[r,n]=(0,s.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20",children:(0,t.jsx)(c.A,{className:"w-5 h-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-50",children:"Email Address"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e?"Manage your account email address":"Link an email address for better security"})]})]})}),(0,t.jsx)("div",{className:"bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 p-6 space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 block",children:"Current Email Address"}),(0,t.jsx)(d.p,{type:"email",value:e||"",placeholder:e||"No email address linked",readOnly:!0,className:"bg-neutral-50 dark:bg-neutral-800 cursor-not-allowed"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1",children:e?"This is your current email address":"No email address is currently linked to your account"})]}),(0,t.jsxs)(o.$,{onClick:()=>n(!0),className:"w-full sm:w-auto",variant:"default",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),e?"Update Email Address":"Link Email Address"]})]})}),"google"===a&&(0,t.jsx)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1",children:"Google Account Integration"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"You signed up with Google. You can still link or update your email address for additional security options."})]})]})})]}),(0,t.jsx)(P,{isOpen:r,onClose:()=>n(!1),currentEmail:e,onEmailUpdated:()=>{window.location.reload()}})]})}var T=r(48340);function R({currentPhone:e}){return(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-green-500/10 to-green-500/5 border border-green-500/20",children:(0,t.jsx)(T.A,{className:"w-4 h-4 text-green-500"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-100",children:"Phone Number"})]}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed",children:e?"Your current phone number linked to this account.":"No phone number is currently linked to your account."})]}),(0,t.jsx)("div",{className:"space-y-6",children:e?(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Current Phone Number"}),(0,t.jsx)("div",{className:"mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md",children:(0,t.jsx)("span",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e})}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1",children:"Phone number changes are not currently supported. Contact support if you need to update your number."})]})}):(0,t.jsxs)("div",{className:"text-center p-6 rounded-lg bg-neutral-50 dark:bg-neutral-900/50 border border-neutral-200 dark:border-neutral-700",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 mx-auto mb-3 w-fit",children:(0,t.jsx)(T.A,{className:"w-6 h-6"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-2",children:"No Phone Number"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 max-w-sm mx-auto",children:"No phone number is currently linked to your account. Phone number linking is not available at this time."})]})})]})}var U=r(85778),F=r(25334),L=r(85814),q=r.n(L);function D(){return(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,t.jsx)(U.A,{className:"w-4 h-4 text-primary"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-100",children:"Edit Digital Card Profile"})]}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed",children:"Update your business details, contact information, social links, and appearance on your public digital card."})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(o.$,{asChild:!0,variant:"outline",size:"sm",className:"px-4 py-2 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(q(),{href:"/dashboard/business/card",className:"flex items-center",children:[(0,t.jsx)(U.A,{className:"w-4 h-4 mr-2"}),"Go to Card Editor",(0,t.jsx)(F.A,{className:"w-4 h-4 ml-2"})]})})})]})}var $=r(88920),O=r(16189),V=r(88233),z=r(43649),M=r(17581),I=r(35071);w.callServer,w.findSourceMapURL;let B=(0,w.createServerReference)("006c62545b20ce4a9dee4ae76cb3cc282e7383fd0b",w.callServer,void 0,w.findSourceMapURL,"sendDeleteAccountOTP"),W=(0,w.createServerReference)("607838e0ab85cc057a08b13adf3219f03def6434c7",w.callServer,void 0,w.findSourceMapURL,"verifyDeleteAccountOTP"),G=(0,w.createServerReference)("40f6db33a1d20ceee7e9c2c3fab658082e75968126",w.callServer,void 0,w.findSourceMapURL,"verifyDeleteAccountPassword"),Y=(0,w.createServerReference)("00e4dae012fd1fa78d78881ff916619155bbfe5a13",w.callServer,void 0,w.findSourceMapURL,"deleteAccount");function J(){let e=(0,O.useRouter)(),[a,r]=(0,s.useTransition)(),[n,u]=(0,s.useState)(""),[m,x]=(0,s.useState)(!1),[h,p]=(0,s.useState)(!1),[f,y]=(0,s.useState)("initial"),[j,w]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1),[_,A]=(0,s.useState)(""),[C,E]=(0,s.useState)(null),[P,S]=(0,s.useState)(""),[T,R]=(0,s.useState)(""),[U,F]=(0,s.useState)(!1),[L,q]=(0,s.useState)(!1),[D,J]=(0,s.useState)(!1),[H,K]=(0,s.useState)(!1),X=e=>{E(e),"email"===e?y("email-otp"):y("password")},Q=async()=>{q(!0);try{let e=await B();if(e.success)l.oR.success(e.message),e.email&&A(e.email);else if(l.oR.error(e.message),e.isConfigurationError)return}catch(e){l.oR.error("Failed to send verification code")}finally{q(!1)}},Z=async()=>{if(6!==P.length)return void l.oR.error("Please enter a valid 6-digit code");F(!0);try{let e=await W(_,P);e.success?(l.oR.success(e.message),J(!0),y("final-confirm")):l.oR.error(e.message)}catch(e){l.oR.error("Failed to verify code")}finally{F(!1)}},ee=async()=>{if(!T.trim())return void l.oR.error("Please enter your password");F(!0);try{let e=await G(T);e.success?(l.oR.success(e.message),J(!0),y("final-confirm")):l.oR.error(e.message)}catch(e){l.oR.error("Failed to verify password")}finally{F(!1)}},ea=()=>{u(""),p(!1),y("initial"),E(null),S(""),R(""),J(!1),A(""),K(!1)};return(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.3},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"pb-6 border-b border-red-200/60 dark:border-red-700/60",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-red-500/10 to-red-500/5 border border-red-500/20",children:(0,t.jsx)(V.A,{className:"w-4 h-4 text-red-500"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-red-600 dark:text-red-400",children:"Delete Account"})]}),(0,t.jsx)("p",{className:"text-sm text-red-600/80 dark:text-red-400/80 leading-relaxed",children:"Permanently delete your account and all associated data."})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30 mb-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(z.A,{className:"w-5 h-5 text-red-500 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-red-700 dark:text-red-400",children:"Warning: This action cannot be undone"}),(0,t.jsx)("p",{className:"text-xs text-red-600/80 dark:text-red-400/80 mt-1",children:"Deleting your account will permanently remove all your data, including your business card, products, and analytics. Any active subscriptions will be canceled."})]})]})}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)(o.$,{variant:"destructive",disabled:m,onClick:()=>p(!0),className:"px-4 py-2 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",type:"button",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]})}),(0,t.jsx)(b.lG,{open:h,onOpenChange:e=>{m||U||L||ea()},children:(0,t.jsxs)(b.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(b.c7,{className:"text-center pb-2",children:[(0,t.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20",children:(0,t.jsx)(i.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},children:(0,t.jsx)(V.A,{className:"h-8 w-8 text-red-500"})})}),(0,t.jsx)(b.L3,{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"choose-method"===f?"Verify Your Identity":"email-otp"===f?"Email Verification":"password"===f?"Password Verification":"Delete your account?"}),(0,t.jsx)(b.rr,{className:"text-gray-500 dark:text-gray-400 mt-2",children:"choose-method"===f?"Choose how you want to verify your identity before deleting your account.":"email-otp"===f?"We've sent a verification code to your email address.":"password"===f?"Please enter your current password to verify your identity.":"This action cannot be undone. All your data will be permanently removed."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[H&&(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 space-y-4",children:[(0,t.jsx)(v.A,{className:"h-8 w-8 animate-spin text-neutral-500"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Checking verification options..."})]}),!H&&"choose-method"===f&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mb-4",children:"For security, please verify your identity before proceeding:"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(o.$,{onClick:()=>X("email"),variant:"outline",className:"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 text-blue-500"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Email Verification"}),(0,t.jsx)("div",{className:"text-xs text-neutral-500",children:"Send OTP to your email"})]})]}),(0,t.jsxs)(o.$,{onClick:()=>X("password"),variant:"outline",className:"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",children:[(0,t.jsx)(M.A,{className:"h-5 w-5 text-green-500"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Password Verification"}),(0,t.jsx)("div",{className:"text-xs text-neutral-500",children:"Enter your current password"})]})]})]})]}),!H&&"email-otp"===f&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(o.$,{onClick:Q,disabled:L,variant:"outline",className:"mb-4",children:L?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin mr-2"}),"Sending..."]}):"Send Verification Code"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Enter 6-digit verification code:"}),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(g.UV,{maxLength:6,value:P,onChange:S,className:"gap-2",children:(0,t.jsxs)(g.NV,{children:[(0,t.jsx)(g.sF,{index:0,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:1,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:2,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:3,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:4,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:5,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"})]})})})]}),(0,t.jsx)(o.$,{onClick:Z,disabled:6!==P.length||U,className:"w-full",children:U?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin mr-2"}),"Verifying..."]}):"Verify Code"})]}),!H&&"password"===f&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Enter your current password:"}),(0,t.jsx)(d.p,{type:"password",value:T,onChange:e=>R(e.target.value),placeholder:"Current password",className:"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"})]}),(0,t.jsx)(o.$,{onClick:ee,disabled:!T.trim()||U,className:"w-full",children:U?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin mr-2"}),"Verifying..."]}):"Verify Password"})]}),!H&&"final-confirm"===f&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"bg-red-50 dark:bg-red-900/10 rounded-lg p-4 mb-6 border border-red-100 dark:border-red-800/20",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(z.A,{className:"h-5 w-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-red-700 dark:text-red-400",children:"Warning: All data will be permanently lost"}),(0,t.jsxs)("ul",{className:"text-xs text-red-600/80 dark:text-red-400/80 mt-2 space-y-1 list-disc pl-4",children:[(0,t.jsx)("li",{children:"Your business profile and digital card"}),(0,t.jsx)("li",{children:"All products and services"}),(0,t.jsx)("li",{children:"Analytics and customer data"}),(0,t.jsx)("li",{children:"Subscription information"})]})]})]})}),(0,t.jsxs)("p",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2",children:["Type ",(0,t.jsx)("span",{className:"font-bold text-red-500 dark:text-red-400",children:"DELETE"})," to confirm:"]}),(0,t.jsx)(d.p,{value:n,onChange:e=>u(e.target.value),placeholder:"DELETE",className:"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-red-500/30"})]})]}),(0,t.jsxs)(b.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 pt-4",children:[(0,t.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{ea()},disabled:m||U||L||H,className:"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200",children:[(0,t.jsx)(I.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),!H&&"final-confirm"===f&&(0,t.jsx)(i.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1",children:(0,t.jsx)(o.$,{type:"button",onClick:a=>(a.preventDefault(),"DELETE"!==n)?void l.oR.error('Please type "DELETE" to confirm'):(j||N)&&!D?void l.oR.error("Please complete verification first"):void(x(!0),r(async()=>{try{l.oR.success("Processing account deletion...");try{await Y(),ea(),e.push("/")}catch(a){a instanceof Error&&a.message.includes("Cannot read properties of undefined")?(ea(),e.push("/")):(console.error("Error in account deletion:",a),l.oR.error("Failed to delete account"),x(!1))}}catch(e){console.error("Unexpected error during account deletion:",e),l.oR.error("An unexpected error occurred"),x(!1)}})),disabled:"DELETE"!==n||m,className:`
                    w-full relative overflow-hidden
                    bg-gradient-to-r from-red-500 to-red-600
                    hover:from-red-600 hover:to-red-700
                    text-white font-medium
                    shadow-lg hover:shadow-xl
                    transition-all duration-300
                    before:absolute before:inset-0
                    before:bg-gradient-to-r before:from-red-400 before:to-red-500
                    before:opacity-0 hover:before:opacity-20
                    before:transition-opacity before:duration-300
                    ${"DELETE"!==n||m?"cursor-not-allowed opacity-80":""}
                  `,style:{boxShadow:"DELETE"!==n||m?"0 4px 20px rgba(239, 68, 68, 0.3)":"0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)"},children:(0,t.jsx)($.N,{mode:"wait",children:m?(0,t.jsxs)(i.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Deleting..."]},"deleting"):(0,t.jsxs)(i.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]},"delete")})})})]})]})})]})]})}function H({currentEmail:e,currentPhone:a,registrationType:r}){return(0,t.jsxs)(i.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-8",children:[(0,t.jsx)(i.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,t.jsx)(n.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,t.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Account Management"})]}),(0,t.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Account Settings"}),(0,t.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Manage your account security, contact information, and business preferences. Keep your profile up to date."})]})}),(0,t.jsxs)("div",{className:"space-y-12",children:[(0,t.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},children:(0,t.jsx)(S,{currentEmail:e,currentPhone:a,registrationType:r})}),(0,t.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3},children:(0,t.jsx)(R,{currentEmail:e,currentPhone:a,registrationType:r})}),(0,t.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.5},children:(0,t.jsx)(D,{})}),(0,t.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.6},children:(0,t.jsx)(J,{})})]})]})}},48340:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58164:(e,a,r)=>{"use strict";r.d(a,{C5:()=>g,MJ:()=>b,Rr:()=>f,eI:()=>h,lR:()=>p,lV:()=>o,zB:()=>u});var t=r(60687),s=r(43210),i=r(8730),n=r(27605),l=r(96241),d=r(39390);let o=n.Op,c=s.createContext({}),u=({...e})=>(0,t.jsx)(c.Provider,{value:{name:e.name},children:(0,t.jsx)(n.xI,{...e})}),m=()=>{let e=s.useContext(c),a=s.useContext(x),{getFieldState:r}=(0,n.xW)(),t=(0,n.lN)({name:e.name}),i=r(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=a;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},x=s.createContext({});function h({className:e,...a}){let r=s.useId();return(0,t.jsx)(x.Provider,{value:{id:r},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",e),...a})})}function p({className:e,...a}){let{error:r,formItemId:s}=m();return(0,t.jsx)(d.J,{"data-slot":"form-label","data-error":!!r,className:(0,l.cn)("data-[error=true]:text-destructive",e),htmlFor:s,...a})}function b({...e}){let{error:a,formItemId:r,formDescriptionId:s,formMessageId:n}=m();return(0,t.jsx)(i.DX,{"data-slot":"form-control",id:r,"aria-describedby":a?`${s} ${n}`:`${s}`,"aria-invalid":!!a,...e})}function f({className:e,...a}){let{formDescriptionId:r}=m();return(0,t.jsx)("p",{"data-slot":"form-description",id:r,className:(0,l.cn)("text-muted-foreground text-sm",e),...a})}function g({className:e,...a}){let{error:r,formMessageId:s}=m(),i=r?String(r?.message??""):a.children;return i?(0,t.jsx)("p",{"data-slot":"form-message",id:s,className:(0,l.cn)("text-destructive text-sm",e),...a,children:i}):null}},61192:(e,a,r)=>{"use strict";r.d(a,{default:()=>u});var t=r(60687);r(43210);var s=r(27625),i=r(41956),n=r(38606),l=r(24861),d=r(21121),o=r(96241),c=r(52529);function u({children:e,businessName:a,logoUrl:r,memberName:u,userPlan:m}){return(0,t.jsx)(c.Q,{children:(0,t.jsxs)(l.GB,{children:[(0,t.jsx)(d.s,{businessName:a,logoUrl:r,memberName:u,userPlan:m}),(0,t.jsxs)(l.sF,{children:[(0,t.jsxs)(s.default,{businessName:a,logoUrl:r,userName:u,children:[(0,t.jsx)(l.x2,{className:"ml-auto md:ml-0"})," ",(0,t.jsx)(i.ThemeToggle,{variant:"dashboard"})]}),(0,t.jsx)("main",{className:(0,o.cn)("flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto","pb-20 sm:pb-18 md:pb-6","bg-white dark:bg-black"),children:e}),(0,t.jsx)(n.default,{})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64522:(e,a,r)=>{"use strict";r.d(a,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\components\\\\BusinessDashboardClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx","default")},70084:(e,a,r)=>{"use strict";r.d(a,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\settings\\\\components\\\\SettingsPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},75034:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79988:(e,a,r)=>{Promise.resolve().then(r.bind(r,61192))},80869:(e,a,r)=>{Promise.resolve().then(r.bind(r,46586))},81630:e=>{"use strict";e.exports=require("http")},88233:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96882:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},99008:(e,a,r)=>{"use strict";r.d(a,{NV:()=>d,UV:()=>l,sF:()=>o});var t=r(60687),s=r(43210),i=r(78119),n=r(96241);function l({className:e,containerClassName:a,...r}){return(0,t.jsx)(i.wE,{"data-slot":"input-otp",containerClassName:(0,n.cn)("flex items-center gap-2 has-disabled:opacity-50",a),className:(0,n.cn)("disabled:cursor-not-allowed",e),...r})}function d({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"input-otp-group",className:(0,n.cn)("flex items-center",e),...a})}function o({index:e,className:a,...r}){let l=s.useContext(i.dK),{char:d,hasFakeCaret:o,isActive:c}=l?.slots[e]??{};return(0,t.jsxs)("div",{"data-slot":"input-otp-slot","data-active":c,className:(0,n.cn)("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]",a),...r,children:[d,o&&(0,t.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-caret-blink bg-foreground h-4 w-px duration-1000"})})]})}},99891:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var a=require("../../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,6380,5880,8567,3442,4208,634,3037,3739,9538,5265],()=>r(37759));module.exports=t})();