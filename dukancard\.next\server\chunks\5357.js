"use strict";exports.id=5357,exports.ids=[5357],exports.modules={55357:(t,e,i)=>{function n(t,e=5){let i=Math.floor(Date.now()/1e3),s=60*e;return t>i+60?{valid:!1,error:`Webhook timestamp is in the future: ${t} > ${i}`}:i-t>s?{valid:!1,error:`Webhook timestamp is too old: ${i-t} seconds > ${s} seconds`}:{valid:!0}}function s(t,e){let i=[],s=[],a=function(t){let e=[],i=[];if(!t||"object"!=typeof t)return e.push("Payload must be an object"),{valid:!1,errors:e,warnings:i};if(t.event&&"string"==typeof t.event||e.push("Missing or invalid event type"),t.account_id&&"string"==typeof t.account_id||e.push("Missing or invalid account_id"),t.entity&&"string"==typeof t.entity||e.push("Missing or invalid entity type"),t.payload&&"object"==typeof t.payload||e.push("Missing or invalid payload object"),t.created_at)if("number"!=typeof t.created_at)e.push("created_at must be a number (Unix timestamp)");else{let e=n(t.created_at);e.valid||i.push(`Timestamp validation: ${e.error}`)}if("string"==typeof t.event&&t.event.startsWith("subscription.")){let n=t.payload;if(n.subscription){let t=n.subscription;if(t.entity&&"object"==typeof t.entity){let n=t.entity;n.id&&"string"==typeof n.id||e.push("Subscription entity must contain valid id"),n.status&&"string"==typeof n.status||i.push("Subscription entity should contain status")}else e.push("Subscription data must contain entity object")}else e.push("Subscription events must contain subscription data")}if("string"==typeof t.event&&t.event.startsWith("payment.")){let n=t.payload;if(n.payment){let t=n.payment;if(t.entity&&"object"==typeof t.entity){let n=t.entity;n.id&&"string"==typeof n.id||e.push("Payment entity must contain valid id"),n.amount&&"number"==typeof n.amount||i.push("Payment entity should contain amount")}else e.push("Payment data must contain entity object")}else e.push("Payment events must contain payment data")}return{valid:0===e.length,errors:e,warnings:i}}(t);if(i.push(...a.errors),s.push(...a.warnings),a.valid&&t){if("string"==typeof t.account_id){var u;let n=(u=t.account_id,e&&u!==e?{valid:!1,error:`Account ID mismatch: received ${u}, expected ${e}`}:{valid:!0});!n.valid&&n.error&&i.push(n.error)}if("number"==typeof t.created_at){let e=n(t.created_at);!e.valid&&e.error&&s.push(`Timestamp validation: ${e.error}`)}let a=function(t){let e=[],i=[],n=t.event,s=t.payload;if(!n)return e.push("Missing event type in webhook payload"),{errors:e,warnings:i};if(!s)return e.push("Missing payload in webhook data"),{errors:e,warnings:i};if(n.startsWith("subscription.")){let t=function(t,e){let i=[],n=[],s=e.subscription;if(!s||!s.entity)return i.push("Missing subscription entity in payload"),{errors:i,warnings:n};let a=s.entity;a.id||i.push("Missing subscription ID in entity"),a.status||i.push("Missing subscription status in entity"),a.plan_id||n.push("Missing plan_id in subscription entity");let u=a.status;switch(u&&!["created","authenticated","active","pending","halted","cancelled","completed","expired"].includes(u)&&n.push(`Unknown subscription status: ${u}`),t){case"subscription.activated":"active"!==u&&n.push(`Subscription activated event but status is ${u}, expected 'active'`);break;case"subscription.cancelled":"cancelled"!==u&&n.push(`Subscription cancelled event but status is ${u}, expected 'cancelled'`);break;case"subscription.charged":e.payment||i.push("Missing payment data in subscription.charged event");break;case"subscription.completed":"completed"!==u&&n.push(`Subscription completed event but status is ${u}, expected 'completed'`)}return{errors:i,warnings:n}}(n,s);e.push(...t.errors),i.push(...t.warnings)}if(n.startsWith("payment.")){let t=function(t,e){let i=[],n=[],s=e.payment;if(!s||!s.entity)return i.push("Missing payment entity in payload"),{errors:i,warnings:n};let a=s.entity;a.id||i.push("Missing payment ID in entity"),a.status||i.push("Missing payment status in entity"),a.amount||n.push("Missing payment amount in entity");let u=a.status;switch(u&&!["created","authorized","captured","refunded","failed"].includes(u)&&n.push(`Unknown payment status: ${u}`),t){case"payment.authorized":"authorized"!==u&&n.push(`Payment authorized event but status is ${u}, expected 'authorized'`);break;case"payment.captured":"captured"!==u&&n.push(`Payment captured event but status is ${u}, expected 'captured'`);break;case"payment.failed":"failed"!==u&&n.push(`Payment failed event but status is ${u}, expected 'failed'`)}return{errors:i,warnings:n}}(n,s);e.push(...t.errors),i.push(...t.warnings)}if(n.startsWith("invoice.")){let t=function(t,e){let i=[],n=[],s=e.invoice;if(!s||!s.entity)return i.push("Missing invoice entity in payload"),{errors:i,warnings:n};let a=s.entity;a.id||i.push("Missing invoice ID in entity"),a.status||i.push("Missing invoice status in entity");let u=a.status;return u&&!["draft","issued","partially_paid","paid","cancelled","expired"].includes(u)&&n.push(`Unknown invoice status: ${u}`),"invoice.paid"===t&&("paid"!==u&&n.push(`Invoice paid event but status is ${u}, expected 'paid'`),e.payment||i.push("Missing payment data in invoice.paid event")),{errors:i,warnings:n}}(n,s);e.push(...t.errors),i.push(...t.warnings)}return{errors:e,warnings:i}}(t);i.push(...a.errors),s.push(...a.warnings)}return{valid:0===i.length,errors:i,warnings:s}}function a(t){try{let e=t.event;if(e.startsWith("subscription."))return t.payload.subscription?.id||null;if(e.startsWith("payment."))return t.payload.payment?.entity?.id||null;if(e.startsWith("invoice."))return t.payload.invoice?.entity?.id||null;if(e.startsWith("refund."))return t.payload.refund?.entity?.id||null;return null}catch(t){return console.error("[WEBHOOK_VALIDATION] Error extracting entity ID:",t),null}}i.d(e,{Su:()=>s,extractEntityId:()=>a})}};