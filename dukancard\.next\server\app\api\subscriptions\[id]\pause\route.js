(()=>{var e={};e.id=2817,e.ids=[2817,5453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5238:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>A,routeModule:()=>d,serverHooks:()=>R,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>S});var r={};t.r(r),t.d(r,{POST:()=>p});var i=t(96559),a=t(48088),n=t(37719),u=t(32190),o=t(32032),c=t(31546),l=t(30468);async function p(e,{params:s}){try{let{id:t}=await s,{pauseAt:r="now"}=await e.json(),i=await (0,o.createClient)(),{data:{user:a},error:n}=await i.auth.getUser();if(n||!a)return u.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let{data:p,error:d}=await i.from(l.CG.PAYMENT_SUBSCRIPTIONS).select("*").eq(l.cZ.RAZORPAY_SUBSCRIPTION_ID,t).single();if(d){if("PGRST116"===d.code)return u.NextResponse.json({success:!1,error:"Subscription not found"},{status:404});return console.error("[RAZORPAY_ERROR] Error fetching subscription:",d),u.NextResponse.json({success:!1,error:"Error fetching subscription"},{status:500})}if(p.business_profile_id!==a.id)return u.NextResponse.json({success:!1,error:"Unauthorized to pause this subscription"},{status:403});if("halted"===p.subscription_status)return u.NextResponse.json({success:!1,error:"Subscription is already paused"},{status:400});let _=await (0,c.pauseSubscription)(t,r);if(!_.success)return u.NextResponse.json({success:!1,error:_.error},{status:400});let S=new Date().toISOString(),{error:R}=await i.from(l.CG.PAYMENT_SUBSCRIPTIONS).update({subscription_status:"halted",subscription_paused_at:S,updated_at:S}).eq(l.cZ.RAZORPAY_SUBSCRIPTION_ID,t);R&&console.error("[RAZORPAY_ERROR] Error updating subscription record:",R);let{data:A,error:b}=await i.rpc("update_subscription_atomic",{p_subscription_id:t,p_new_status:"paused",p_business_profile_id:p.business_profile_id,p_has_active_subscription:!1,p_additional_data:{subscription_paused_at:S,status:"offline"},p_webhook_timestamp:void 0});return b||!A?.success?console.error("[RAZORPAY_ERROR] Error updating subscription atomically:",b||A?.error):console.log(`[RAZORPAY_DEBUG] Successfully updated subscription ${t} and business profile ${p.business_profile_id} atomically`),u.NextResponse.json({success:!0,data:{..._.data,db_subscription:{id:p.id,subscription_status:"halted",subscription_paused_at:S}}},{status:200})}catch(e){return console.error("[RAZORPAY_ERROR] Error pausing subscription:",e),u.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error occurred"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/pause/route",pathname:"/api/subscriptions/[id]/pause",filename:"route",bundlePath:"app/api/subscriptions/[id]/pause/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pause\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:_,workUnitAsyncStorage:S,serverHooks:R}=d;function A(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:S})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,s,t)=>{"use strict";t.d(s,{CG:()=>r,SC:()=>i,cZ:()=>a});let r={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},i={BUSINESS:"business",CUSTOMERS:"customers"},a={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},32032:(e,s,t)=>{"use strict";t.r(s),t.d(s,{createClient:()=>i});var r=t(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!s)throw Error("Supabase environment variables are not set.");let i=null,a=null;try{let{headers:e,cookies:s}=await t.e(4999).then(t.bind(t,44999));i=await e(),a=await s()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let s=e.get("x-test-auth-state"),t=e.get("x-test-user-type"),r="customer"===t||"business"===t,i=e.get("x-test-business-slug"),a=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===s?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===s?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,s,t,r,i){let a=()=>{var a,n,u,o,c;return a=e,n=s,u=t,o=r,c=i,"customer_profiles"===a?{data:u&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===a?{data:u&&"business"===n?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===a?{data:"business"===n?{id:"test-subscription-id",plan_id:c,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===a?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:s=>n(e),eq:(s,t)=>n(e),neq:(s,t)=>n(e),gt:(s,t)=>n(e),gte:(s,t)=>n(e),lt:(s,t)=>n(e),lte:(s,t)=>n(e),like:(s,t)=>n(e),ilike:(s,t)=>n(e),is:(s,t)=>n(e),in:(s,t)=>n(e),contains:(s,t)=>n(e),containedBy:(s,t)=>n(e),rangeGt:(s,t)=>n(e),rangeGte:(s,t)=>n(e),rangeLt:(s,t)=>n(e),rangeLte:(s,t)=>n(e),rangeAdjacent:(s,t)=>n(e),overlaps:(s,t)=>n(e),textSearch:(s,t)=>n(e),match:s=>n(e),not:(s,t,r)=>n(e),or:s=>n(e),filter:(s,t,r)=>n(e),order:(s,t)=>n(e),limit:(s,t)=>n(e),range:(s,t,r)=>n(e),abortSignal:s=>n(e),single:async()=>a(),maybeSingle:async()=>a(),then:async e=>{let s=a();return e?e(s):s},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:s=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async s=>{let t={data:Array.isArray(e)?e:[e],error:null};return s?s(t):t}}),then:async s=>{let t={data:Array.isArray(e)?e:[e],error:null};return s?s(t):t}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,s)=>n()}})(e,t,r,i,a)}}(i):a?(0,r.createServerClient)(e,s,{cookies:{getAll:async()=>await a.getAll(),async setAll(e){try{for(let{name:s,value:t,options:r}of e)await a.set(s,t,r)}catch{}}}}):(0,r.createServerClient)(e,s,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function s(e){var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}s.keys=()=>[],s.resolve=s,s.id=51906,e.exports=s},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95453:(e,s,t)=>{"use strict";t.d(s,{ST:()=>a,bG:()=>u,t6:()=>o});var r=t(55511),i=t.n(r);let a="https://api.razorpay.com/v2",n=()=>{let e,s;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",s=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!s)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!s,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:s}},u=()=>{let{keyId:e,keySecret:s}=n(),t=Buffer.from(`${e}:${s}`).toString("base64");return{Authorization:`Basic ${t}`,"Content-Type":"application/json"}},o=(e,s,t)=>{try{let r=i().createHmac("sha256",t).update(e).digest("hex");return i().timingSafeEqual(Buffer.from(s),Buffer.from(r))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,9398,4386,580,1546],()=>t(5238));module.exports=r})();