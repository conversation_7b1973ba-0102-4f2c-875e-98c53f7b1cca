"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4927],{28882:(e,t,a)=>{a.d(t,{Mo:()=>s,Uc:()=>i,b4:()=>n,kc:()=>o});var r=a(55594),l=a(48347);let s=r.Ik({logo_url:r.Yj().url({message:"Invalid URL format for logo/profile photo."}).optional().or(r.eu("")).nullable(),established_year:r.ai().int({message:"Established year must be a whole number."}).min(1800,{message:"Established year must be after 1800."}).max(new Date().getFullYear(),{message:"Established year cannot be in the future."}).optional().nullable(),address_line:r.Yj().min(1,{message:"Address line is required."}).max(100,{message:"Address line cannot exceed 100 characters."}),locality:r.Yj().min(1,{message:"Locality/area is required."}),city:r.Yj().min(1,{message:"City is required."}),state:r.Yj().min(1,{message:"State is required."}),pincode:r.Yj().min(6,{message:"Pincode must be 6 digits."}).max(6,{message:"Pincode must be 6 digits."}).regex(/^\d+$/,{message:"Pincode must contain only digits."}),phone:l.LE,instagram_url:r.Yj().url({message:"Invalid URL format for Instagram."}).optional().or(r.eu("")),facebook_url:r.Yj().url({message:"Invalid URL format for Facebook."}).optional().or(r.eu("")),whatsapp_number:l.LE.optional().or(r.eu("")),about_bio:r.Yj().max(100,{message:"Bio cannot exceed 100 characters."}).optional().or(r.eu("")),theme_color:r.Yj().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,{message:"Invalid hex color format (e.g., #RRGGBB or #RGB)."}).optional().or(r.eu("")),business_hours:r.bz().optional().nullable(),delivery_info:r.Yj().max(100,{message:"Delivery info cannot exceed 100 characters."}).optional().or(r.eu("")),business_category:r.Yj().min(1,{message:"Business category is required."}),status:r.k5(["online","offline"]).default("offline"),custom_branding:r.Ik({custom_header_text:r.Yj().max(50).optional().or(r.eu("")),custom_header_image_url:r.Yj().url().optional().or(r.eu("")),custom_header_image_light_url:r.Yj().url().optional().or(r.eu("")),custom_header_image_dark_url:r.Yj().url().optional().or(r.eu("")),hide_dukancard_branding:r.zM().optional(),pending_light_header_file:r.bz().optional(),pending_dark_header_file:r.bz().optional()}).optional().refine(e=>{if((null==e?void 0:e.hide_dukancard_branding)===!0){let t=(null==e?void 0:e.custom_header_text)&&""!==e.custom_header_text.trim(),a=(null==e?void 0:e.custom_header_image_url)&&""!==e.custom_header_image_url.trim(),r=(null==e?void 0:e.custom_header_image_light_url)&&""!==e.custom_header_image_light_url.trim(),l=(null==e?void 0:e.custom_header_image_dark_url)&&""!==e.custom_header_image_dark_url.trim();if(!t&&!a&&!r&&!l)return!1}return!0},{message:"Custom header text or image is required when hiding Dukancard branding",path:["custom_header_text"]}),custom_ads:r.Ik({enabled:r.zM().optional(),image_url:r.Yj().url().optional().or(r.eu("")),link_url:r.Yj().url().optional().or(r.eu("")),uploaded_at:r.Yj().optional().or(r.eu("")).nullable()}).optional(),business_slug:r.Yj().regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/,{message:"Slug must be lowercase letters, numbers, or hyphens, and cannot start/end with a hyphen."}).min(3,{message:"Slug must be at least 3 characters long."}).optional().or(r.eu("")),member_name:r.Yj().min(1,{message:"Member name is required."}).max(50,{message:"Name cannot exceed 50 characters."}),title:r.Yj().min(1,{message:"Title/Designation is required."}).max(50,{message:"Title cannot exceed 50 characters."}),business_name:r.Yj().min(1,{message:"Business name is required."}).max(100,{message:"Business name cannot exceed 100 characters."}),id:r.Yj().uuid().optional(),contact_email:r.Yj().email({message:"Please enter a valid email address"}).min(1,{message:"Contact email is required"}),has_active_subscription:r.zM().optional(),trial_end_date:r.Yj().optional().nullable(),created_at:r.KC([r.Yj(),r.p6()]).optional().transform(e=>e instanceof Date?e.toISOString():e),updated_at:r.KC([r.Yj(),r.p6()]).optional().transform(e=>e instanceof Date?e.toISOString():e),total_likes:r.ai().int().nonnegative().optional(),total_subscriptions:r.ai().int().nonnegative().optional(),average_rating:r.ai().nonnegative().optional(),total_visits:r.ai().int().nonnegative().optional()}),o={member_name:"",title:"",business_name:"",logo_url:null,established_year:null,address_line:"",locality:"",city:"",state:"",pincode:"",phone:"",instagram_url:"",facebook_url:"",whatsapp_number:"",about_bio:"",theme_color:"",business_hours:null,delivery_info:"",business_category:"",status:"offline",business_slug:"",contact_email:"",custom_branding:{custom_header_text:"",custom_header_image_url:"",custom_header_image_light_url:"",custom_header_image_dark_url:"",hide_dukancard_branding:!1,pending_light_header_file:null,pending_dark_header_file:null},custom_ads:{enabled:!1,image_url:"",link_url:"",uploaded_at:null}},n=["member_name","title","business_name","phone","address_line","pincode","city","state","locality","contact_email","business_category"],i=["member_name","title","business_name","phone","contact_email","business_category","address_line","pincode","city","state","locality"]},30185:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(95155);a(12115);let l=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})},37777:(e,t,a)=>{a.d(t,{Bc:()=>o,ZI:()=>d,k$:()=>i,m_:()=>n});var r=a(95155);a(12115);var l=a(78082),s=a(53999);function o(e){let{delayDuration:t=0,...a}=e;return(0,r.jsx)(l.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function n(e){let{...t}=e;return(0,r.jsx)(o,{children:(0,r.jsx)(l.bL,{"data-slot":"tooltip",...t})})}function i(e){let{...t}=e;return(0,r.jsx)(l.l9,{"data-slot":"tooltip-trigger",...t})}function d(e){let{className:t,sideOffset:a=0,children:o,...n}=e;return(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,s.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...n,children:[o,(0,r.jsx)(l.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},48061:(e,t,a)=>{a.d(t,{A:()=>Q});var r=a(95155),l=a(12115),s=a(28695),o=a(37777),n=a(28882),i=a(25627),d=a(53999),c=a(86151),m=a(97939),u=a(51976),h=a(12318),x=a(38564),g=a(19420),p=a(82147),f=a(86601),b=a(30185);let v=e=>{if(!e)return;let t=e.replace(/\D/g,"");if(t.length<10)return;let a=t.startsWith("91")?t:"91".concat(t);return"https://wa.me/".concat(a)},j=e=>{if(!e)return;let t=e.replace(/\D/g,"");if(!(t.length<10))return"tel:+91".concat(t)},w=e=>null==e?"N/A":"₹".concat(e.toLocaleString("en-IN")),y=e=>{if(!e||e.length<5)return e;let[t,a]=e.split(":"),r=parseInt(t,10);return isNaN(r)?e:"".concat(r%12||12,":").concat(a," ").concat(r>=12?"PM":"AM")},_=e=>{let t={monday:"Mon",tuesday:"Tue",wednesday:"Wed",thursday:"Thu",friday:"Fri",saturday:"Sat",sunday:"Sun"},a=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],r=[...e].sort((e,t)=>a.indexOf(e)-a.indexOf(t)),l=r.map(e=>t[e]||e);return 5===r.length&&["monday","tuesday","wednesday","thursday","friday"].every(e=>r.includes(e))?"Mon-Fri":2===r.length&&r.includes("saturday")&&r.includes("sunday")?"Sat-Sun":7===r.length?"All days":k(r,a)?"".concat(l[0],"-").concat(l[l.length-1]):l.join(", ")},k=(e,t)=>{if(e.length<=1)return!0;let a=e.map(e=>t.indexOf(e)).sort((e,t)=>e-t);for(let e=1;e<a.length;e++)if(a[e]!==a[e-1]+1)return!1;return!0};function N(e){return"pro"===e||"enterprise"===e}function S(e,t,a){return N(e)&&a?a:"var(--brand-gold)"}let C=[{id:"linen-paper",name:"Linen Paper",description:"Classic linen texture with subtle cross-hatching",path:"/textures/linen-paper.svg",category:"paper",darkModeOpacity:.3,lightModeOpacity:.2},{id:"cotton-paper",name:"Cotton Paper",description:"Soft, fibrous cotton paper texture",path:"/textures/cotton-paper.svg",category:"paper",darkModeOpacity:.3,lightModeOpacity:.2},{id:"recycled-paper",name:"Recycled Paper",description:"Eco-friendly recycled paper with small flecks",path:"/textures/recycled-paper.svg",category:"paper",darkModeOpacity:.3,lightModeOpacity:.2},{id:"laid-paper",name:"Laid Paper",description:"Traditional laid paper with horizontal lines",path:"/textures/laid-paper.svg",category:"paper",darkModeOpacity:.3,lightModeOpacity:.2},{id:"marble",name:"Marble",description:"Elegant marble texture with subtle veining",path:"/textures/marble.svg",category:"premium",darkModeOpacity:.3,lightModeOpacity:.2},{id:"brushed-metal",name:"Brushed Metal",description:"Sophisticated brushed metal finish",path:"/textures/brushed-metal.svg",category:"premium",darkModeOpacity:.3,lightModeOpacity:.2},{id:"subtle-dots",name:"Subtle Dots",description:"Modern pattern with subtle dot grid",path:"/textures/subtle-dots.svg",category:"modern",darkModeOpacity:.3,lightModeOpacity:.2},{id:"geometric",name:"Geometric",description:"Contemporary geometric pattern",path:"/textures/geometric.svg",category:"modern",darkModeOpacity:.3,lightModeOpacity:.2},{id:"texture-png",name:"Classic Texture",description:"Original texture from the application",path:"/texture.png",category:"paper",darkModeOpacity:.3,lightModeOpacity:.2},{id:"none",name:"No Texture",description:"Clean look without any texture",path:"",category:"modern",darkModeOpacity:0,lightModeOpacity:0}],M=e=>C.find(t=>t.id===e)||C[0];function P(e){let{finalThemeColor:t}=e,a=(0,l.useMemo)(()=>M("linen-paper"),[]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute inset-0 pointer-events-none z-5",style:{backgroundImage:'url("/decorative/card-texture.svg")',backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat",opacity:.6}}),a.path&&(0,r.jsx)("div",{className:"absolute inset-0 mix-blend-overlay pointer-events-none texture-background z-10 dark:opacity-[var(--dark-opacity)] opacity-[var(--light-opacity)]",style:{backgroundImage:a.path?"url(".concat(a.path,")"):"none","--dark-opacity":"".concat(.7*a.darkModeOpacity),"--light-opacity":"".concat(.7*a.lightModeOpacity),backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"repeat"}}),(0,r.jsx)("div",{className:"absolute inset-0 pointer-events-none z-10",style:{backgroundImage:'url("/decorative/subtle-pattern.svg")',backgroundRepeat:"repeat",backgroundSize:"20px 20px",opacity:.15}}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/20 mix-blend-overlay pointer-events-none z-15 opacity-20 dark:opacity-15"})]})}var T=a(66766);function z(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute top-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20",children:(0,r.jsx)("div",{style:{transform:"rotate(0deg)"},children:(0,r.jsx)(T.default,{src:"/decorative/card-border.svg",alt:"",width:48,height:48,className:"w-full h-full"})})}),(0,r.jsx)("div",{className:"absolute top-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20",children:(0,r.jsx)("div",{style:{transform:"rotate(90deg)"},children:(0,r.jsx)(T.default,{src:"/decorative/card-border.svg",alt:"",width:48,height:48,className:"w-full h-full"})})}),(0,r.jsx)("div",{className:"absolute bottom-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20",children:(0,r.jsx)("div",{style:{transform:"rotate(180deg)"},children:(0,r.jsx)(T.default,{src:"/decorative/card-border.svg",alt:"",width:48,height:48,className:"w-full h-full"})})}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20",children:(0,r.jsx)("div",{style:{transform:"rotate(270deg)"},children:(0,r.jsx)(T.default,{src:"/decorative/card-border.svg",alt:"",width:48,height:48,className:"w-full h-full"})})})]})}function A(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15"}),(0,r.jsx)("div",{className:"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15"}),(0,r.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-[var(--theme-color-5)] blur-3xl opacity-15 dark:opacity-10"}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl shadow-[inset_0_0_20px_rgba(0,0,0,0.1),inset_0_0_5px_var(--theme-color-20)] dark:shadow-[inset_0_0_20px_rgba(255,255,255,0.03),inset_0_0_5px_var(--theme-color-30)] pointer-events-none z-25"}),(0,r.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-[3px] pointer-events-none z-30",style:{background:"linear-gradient(to top, var(--theme-color), transparent)",opacity:.8}}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 w-[3px] pointer-events-none z-30",style:{background:"linear-gradient(to left, var(--theme-color), transparent)",opacity:.8}}),(0,r.jsx)("div",{className:"absolute inset-x-0 top-0 h-[3px] pointer-events-none z-30",style:{background:"linear-gradient(to bottom, var(--theme-color), transparent)",opacity:.8}}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 w-[3px] pointer-events-none z-30",style:{background:"linear-gradient(to right, var(--theme-color), transparent)",opacity:.8}}),(0,r.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-4 bg-gradient-to-t from-black/40 to-transparent pointer-events-none z-25"}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 w-4 bg-gradient-to-l from-black/40 to-transparent pointer-events-none z-25"}),(0,r.jsx)("div",{className:"absolute inset-x-0 top-0 h-[4px] bg-gradient-to-b from-white/60 to-transparent pointer-events-none z-25"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-white/60 to-transparent pointer-events-none z-25"}),(0,r.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-1 bg-gradient-to-t from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70"}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 w-1 bg-gradient-to-l from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70"}),(0,r.jsx)("div",{className:"absolute inset-0 shadow-[0_15px_60px_rgba(0,0,0,0.3),0_5px_20px_var(--theme-color-20)] dark:shadow-[0_15px_60px_rgba(0,0,0,0.5),0_5px_20px_var(--theme-color-30)] rounded-xl pointer-events-none z-25"})]})}var I=a(69074),F=a(51362);function E(e){let{userPlan:t,establishedYear:a,customBranding:l}=e,{resolvedTheme:s}=(0,F.D)(),o=function(e,t,a){var r,l,s,o,n;if(!N(e)||!t)return null;let i="dark"===a;return i&&(null==(r=t.custom_header_image_dark_url)?void 0:r.trim())?t.custom_header_image_dark_url:!i&&(null==(l=t.custom_header_image_light_url)?void 0:l.trim())||i&&(null==(s=t.custom_header_image_light_url)?void 0:s.trim())?t.custom_header_image_light_url:!i&&(null==(o=t.custom_header_image_dark_url)?void 0:o.trim())?t.custom_header_image_dark_url:(null==(n=t.custom_header_image_url)?void 0:n.trim())?t.custom_header_image_url:null}(t,l,s),n=N(t)&&(null==l?void 0:l.custom_header_text)?l.custom_header_text:null,i=N(t),d=i&&(null==l?void 0:l.hide_dukancard_branding)&&o,c=i&&(null==l?void 0:l.hide_dukancard_branding)&&n&&!d,m=!N(t)||!(null==l?void 0:l.hide_dukancard_branding);return(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsx)("div",{className:"flex items-center",children:d?(0,r.jsx)("div",{className:"max-w-[120px] max-h-[32px] overflow-hidden",children:(0,r.jsx)(T.default,{src:o,alt:"Custom header",width:120,height:32,className:"h-8 w-auto object-contain",style:{maxWidth:"120px",maxHeight:"32px"},onError:e=>{e.currentTarget.style.display="none"}})}):c?(0,r.jsx)("span",{className:"font-medium text-sm text-[var(--theme-color)]",children:n}):m&&(0,r.jsxs)("span",{className:"font-bold text-md text-[var(--theme-color)]",children:["Dukan",(0,r.jsx)("span",{className:"text-neutral-900 dark:text-white",children:"card"})]})}),a&&(0,r.jsxs)("div",{className:"flex items-center justify-center text-xs py-0.5 px-2 rounded-full bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)]",children:[(0,r.jsx)(I.A,{className:"w-3 h-3 mr-1 text-[var(--theme-color)]"}),(0,r.jsxs)("span",{className:"font-semibold",children:["Est. ",a]})]})]})}var O=a(71007),R=a(51154),B=a(48136),Y=a(81284);function D(e){let{logo_url:t,localPreviewUrl:a,logoUploadStatus:l,member_name:s,business_name:n,title:i,about_bio:d,finalThemeColor:c}=e;return(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 sm:w-24 sm:h-24 rounded-full border-3 border-[var(--theme-color)] overflow-hidden flex items-center justify-center shadow-lg mb-2 sm:mb-3 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 transform hover:scale-105 transition-transform duration-300",children:[(a||t&&"string"==typeof t&&""!==t.trim())&&(0,r.jsx)(T.default,{src:a||t||"",alt:"".concat(n," logo"),width:96,height:96,className:"object-cover w-full h-full",onError:e=>e.currentTarget.style.display="none"}),!a&&(!t||"string"==typeof t&&""===t.trim())&&"uploading"!==l&&(0,r.jsx)(O.A,{className:"w-12 h-12 opacity-50",color:c}),"uploading"===l&&(0,r.jsx)("div",{className:"absolute inset-0 bg-black/50 flex items-center justify-center",children:(0,r.jsx)(R.A,{className:"w-8 h-8 text-white animate-spin"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-tr from-[var(--theme-color-10)] via-transparent to-[var(--theme-color-10)] opacity-40"}),(0,r.jsx)("div",{className:"absolute -bottom-1 -right-1 w-full h-full rounded-full bg-black/5 blur-sm -z-10"})]}),(0,r.jsx)("h3",{className:"text-base sm:text-lg font-bold text-[--theme-color] mb-2 tracking-wide px-2 text-center",children:n?(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("span",{className:"line-clamp-1 relative cursor-default",children:(0,r.jsx)("span",{className:"relative",children:n})})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:n})})]})}):(0,r.jsx)(B.A,{className:"inline-block h-5 w-5 opacity-50",color:c})}),d&&(0,r.jsxs)("div",{className:"flex items-start text-xs text-neutral-600 dark:text-neutral-300 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 rounded-lg max-w-xs mx-auto mb-2 sm:mb-3",children:[(0,r.jsx)(Y.A,{className:"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]"}),(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("p",{className:"text-start line-clamp-2 cursor-default",children:d})}),(0,r.jsx)(o.ZI,{className:"max-w-xs",children:(0,r.jsx)("p",{children:d})})]})})]})]})}function q(){return(0,r.jsxs)("div",{className:"h-6 w-full mx-auto max-w-xs mb-3 relative flex items-center justify-center",children:[(0,r.jsx)("div",{className:"h-px w-full absolute",style:{background:"linear-gradient(to right, transparent, var(--theme-color-30), transparent)"}}),(0,r.jsxs)("div",{className:"relative z-10 flex items-center justify-center space-x-2",children:[(0,r.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]"}),(0,r.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-30)]"}),(0,r.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]"})]})]})}var L=a(4516),$=a(28883),G=a(14186),W=a(29799);function Z(e){let{fullAddress:t,displayAddressLine:a,locality:l,displayCityStatePin:s,phone:n,displayPhone:i,isAuthenticated:d,telUrl:c,displayEmail:m,mailtoUrl:u,business_hours:h,delivery_info:x}=e;return(0,r.jsxs)("div",{className:"flex flex-col gap-2 sm:gap-3 max-w-xs mx-auto overflow-hidden",children:[t&&(0,r.jsx)("div",{className:"text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full",children:(0,r.jsxs)("div",{className:"flex items-start mb-2.5",children:[(0,r.jsx)(L.A,{className:"w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]"}),(0,r.jsxs)("div",{className:"flex flex-col overflow-hidden",children:[a&&l?(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsxs)("span",{className:"font-medium text-xs line-clamp-1 cursor-default",children:[a,", ",l]})}),(0,r.jsx)(o.ZI,{children:(0,r.jsxs)("p",{children:[a,", ",l]})})]})}):(0,r.jsxs)(r.Fragment,{children:[a&&(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("span",{className:"font-medium text-xs line-clamp-1 cursor-default",children:a})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:a})})]})}),l&&(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("span",{className:"text-xs text-neutral-600 dark:text-neutral-300 line-clamp-1 cursor-default",children:l})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:l})})]})})]}),s&&(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("span",{className:"text-xs text-neutral-500 dark:text-neutral-400 line-clamp-1 cursor-default",children:s})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:s})})]})})]})]})}),(0,r.jsxs)("div",{className:"text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full",children:[n&&(0,r.jsxs)("div",{className:"flex items-center mb-2.5",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]"}),(0,r.jsx)("div",{className:"overflow-hidden",children:i&&(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("a",{href:d&&c?c:"#",className:d&&c?"hover:underline font-medium text-xs truncate block":"cursor-default font-medium text-xs truncate block",children:i})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:i})})]})})})]}),m&&(0,r.jsxs)("div",{className:"flex items-center mb-2.5",children:[(0,r.jsx)($.A,{className:"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]"}),(0,r.jsx)("div",{className:"overflow-hidden",children:(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("a",{href:d&&u?u:"#",className:d?"hover:underline font-medium text-xs truncate block":"cursor-default font-medium text-xs truncate block",children:m})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:m})})]})})})]}),(()=>{if(!h||"object"!=typeof h||0===Object.keys(h).length)return null;try{if(!Object.values(h).some(e=>e&&"object"==typeof e&&e.isOpen))return null;let e=Object.entries(h).filter(e=>{let[,t]=e;return t&&"object"==typeof t&&t.isOpen}).map(e=>{let[t,a]=e;return{day:t,hours:a.openTime&&a.closeTime?"".concat(y(a.openTime)," - ").concat(y(a.closeTime)):""}}).filter(e=>e.hours);if(0===e.length)return null;let t={};return e.forEach(e=>{let{day:a,hours:r}=e;t[r]||(t[r]=[]),t[r].push(a)}),(0,r.jsxs)("div",{className:"flex items-start mb-2.5",children:[(0,r.jsx)(G.A,{className:"w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]"}),(0,r.jsx)("div",{className:"text-xs font-medium",children:Object.entries(t).map((e,t)=>{let[a,l]=e,s=_(l);return(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("span",{className:"capitalize",children:[s,":"]}),(0,r.jsx)("span",{className:"ml-2",children:a})]},t)})})]})}catch(e){return console.error("Error parsing business hours:",e),null}})(),x&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(W.A,{className:"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]"}),(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("p",{className:"font-medium text-xs line-clamp-1 cursor-default",children:x})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:x})})]})})]})]})]})}function Q(e){var t,a,y;let{data:_,userPlan:k,logoUploadStatus:N="idle",localPreviewUrl:C=null,isAuthenticated:M=!1,totalLikes:T=0,totalSubscriptions:I=0,averageRating:F=0,isDemo:O=!1}=e,{logo_url:R=null!=(t=_.logo_url)?t:n.kc.logo_url,member_name:B=n.kc.member_name||"Your Name",business_name:Y=n.kc.business_name||"Your Business Name",about_bio:L=n.kc.about_bio,address_line:$=n.kc.address_line,locality:G=null!=(a=_.locality)?a:n.kc.locality,city:W=n.kc.city,state:Q=n.kc.state,established_year:U=null!=(y=_.established_year)?y:n.kc.established_year,pincode:H=n.kc.pincode,phone:V=n.kc.phone,instagram_url:X=n.kc.instagram_url,facebook_url:K=n.kc.facebook_url,whatsapp_number:J=n.kc.whatsapp_number,theme_color:ee=_.theme_color,business_hours:et=_.business_hours,delivery_info:ea=_.delivery_info,business_slug:er=n.kc.business_slug||"",products_services:el=[],contact_email:es=n.kc.contact_email,title:eo=_.title||""}=_,en=v(J),ei=j(V),ed=es?"mailto:".concat(es):void 0,ec=[$,G,W,Q,H].filter(Boolean).join(", "),em=M?V:(0,d.kY)(V),eu=M?es:(0,d.vA)(es),eh=(null==$?void 0:$.trim())||"",ex="".concat(W||"",", ").concat(Q||""," ").concat(H?"- ".concat(H):"").trim(),eg=er?"https://dukancard.in/".concat(er):null,ep=er?"dukancard.in/".concat(er):"Set Slug to activate",ef=(0,l.useMemo)(()=>S(k,_.custom_branding,ee),[ee,k,_.custom_branding]),eb=(0,l.useMemo)(()=>(function(e,t,a){let r=S(e,t,a);return{"--theme-color":r,"--theme-color-80":"".concat(r,"CC"),"--theme-color-50":"".concat(r,"80"),"--theme-color-30":"".concat(r,"4D"),"--theme-color-20":"".concat(r,"33"),"--theme-color-10":"".concat(r,"1A"),"--theme-color-5":"".concat(r,"0D"),"--theme-accent-end":"#E5C76E"}})(k,_.custom_branding,ee),[k,_.custom_branding,ee]);return(0,r.jsxs)(s.P.div,{"data-card-element":!0,className:(0,d.cn)("\n        relative w-full max-w-sm\n        rounded-xl overflow-hidden\n        transition-all duration-500\n\n        bg-gradient-to-br from-neutral-100 to-white dark:from-neutral-900 dark:to-neutral-950\n        shadow-xl\n        transform-gpu\n        border-0\n      "),style:eb,children:[(0,r.jsx)(P,{finalThemeColor:ef}),(0,r.jsx)(z,{}),(0,r.jsxs)("div",{className:"relative p-3 xs:p-4 sm:p-5 flex flex-col justify-between text-neutral-800 dark:text-white z-10",children:[(0,r.jsx)(E,{userPlan:k,establishedYear:U,customBranding:_.custom_branding}),(0,r.jsxs)("div",{className:"mt-1",children:[(0,r.jsx)(D,{logo_url:R,localPreviewUrl:C,logoUploadStatus:N,member_name:B,business_name:Y,title:eo,about_bio:L,finalThemeColor:ef}),(0,r.jsx)(q,{}),(0,r.jsx)(Z,{fullAddress:ec,displayAddressLine:eh,locality:G,displayCityStatePin:ex,phone:V,displayPhone:em,isAuthenticated:M,telUrl:ei,displayEmail:eu,mailtoUrl:ed,business_hours:et,delivery_info:ea}),el&&el.length>0&&(0,r.jsxs)("div",{className:"mt-3 max-w-xs mx-auto",children:[(0,r.jsxs)("div",{className:"flex items-center text-xs uppercase font-bold tracking-wider text-[--theme-color] mb-2 justify-center",children:[(0,r.jsx)(c.A,{className:"w-3 h-3 mr-1.5",color:ef}),"Products & Services"]}),(0,r.jsx)("div",{className:"space-y-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 p-2.5 rounded-lg",children:el.slice(0,3).map(e=>(0,r.jsx)("div",{className:"text-neutral-700 dark:text-neutral-200",children:(0,r.jsxs)("div",{className:"flex justify-between items-baseline gap-2",children:[(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("span",{className:"font-medium text-xs truncate cursor-default",children:e.name})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:e.name})})]})}),(0,r.jsx)("span",{className:"text-xs font-bold bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)] py-0.5 px-2 rounded-full flex-shrink-0",children:w(e.base_price)})]})},e.id))})]}),(0,r.jsx)("div",{className:"mt-3 max-w-xs mx-auto",children:O?(0,r.jsxs)("div",{className:"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1",children:"Scan for Dukancard Profile"}),(0,r.jsx)("p",{className:"text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default",children:"dukancard.in/demo-business"})]}),(0,r.jsx)("div",{className:"bg-white p-1.5 rounded-lg shadow-md flex-shrink-0",children:(0,r.jsx)(m.A,{className:"w-14 h-14 text-neutral-800",strokeWidth:1.5})})]}):eg?(0,r.jsxs)("div",{className:"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1",children:"Scan for Dukancard Profile"}),(0,r.jsx)(o.Bc,{children:(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:(0,r.jsx)("p",{className:"text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default",children:ep})}),(0,r.jsx)(o.ZI,{children:(0,r.jsx)("p",{children:ep})})]})})]}),(0,r.jsx)("div",{id:"business-card-qrcode",className:"bg-white p-1.5 rounded-lg shadow-md flex-shrink-0",children:(0,r.jsx)(i.Ay,{value:eg,size:60,level:"M",bgColor:"#FFFFFF",fgColor:"#000000"})})]}):(0,r.jsxs)("div",{className:"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1",children:"Scan for Dukancard Profile"}),(0,r.jsx)("p",{className:"text-xs font-mono text-neutral-500 dark:text-neutral-500 line-clamp-1",children:ep})]}),(0,r.jsx)("div",{className:"bg-white p-1.5 rounded-lg shadow-md opacity-50 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-14 h-14 text-[--theme-color]",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"})})})]})}),(0,r.jsxs)("div",{className:"flex justify-center items-center gap-2 sm:gap-4 text-xs text-neutral-500 dark:text-neutral-400 mt-3 mb-2 flex-wrap",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-red-500"}),(0,r.jsx)("span",{className:"font-medium",children:(0,d.gY)(T)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 text-blue-500"}),(0,r.jsx)("span",{className:"font-medium",children:(0,d.gY)(I)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 text-amber-500 fill-current"}),(0,r.jsx)("span",{className:"font-medium",children:F.toFixed(1)})]})]})]}),(0,r.jsx)("div",{className:"pt-3 pb-2",children:(0,r.jsx)(o.Bc,{children:(0,r.jsxs)("div",{className:"flex justify-center items-center space-x-2",children:[X&&(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:O?(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default",children:(0,r.jsx)(f.A,{className:"w-4 h-4 text-[--theme-color]"})}):(0,r.jsx)("a",{href:X,target:"_blank",rel:"noopener noreferrer",className:"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md",children:(0,r.jsx)(f.A,{className:"w-4 h-4 text-[--theme-color]"})})}),(0,r.jsx)(o.ZI,{className:"bg-neutral-800 text-xs text-white border-neutral-700",children:O?"Demo Instagram Button":"Instagram"})]}),K&&(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:O?(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default",children:(0,r.jsx)(b.A,{className:"w-4 h-4 text-[--theme-color]"})}):(0,r.jsx)("a",{href:K,target:"_blank",rel:"noopener noreferrer",className:"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md",children:(0,r.jsx)(b.A,{className:"w-4 h-4 text-[--theme-color]"})})}),(0,r.jsx)(o.ZI,{className:"bg-neutral-800 text-xs text-white border-neutral-700",children:O?"Demo Facebook Button":"Facebook"})]}),en&&(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:O?(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default",children:(0,r.jsx)(p.A,{className:"w-4 h-4 text-[--theme-color]"})}):(0,r.jsx)("a",{href:en,target:"_blank",rel:"noopener noreferrer",className:"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md",children:(0,r.jsx)(p.A,{className:"w-4 h-4 text-[--theme-color]"})})}),(0,r.jsx)(o.ZI,{className:"bg-neutral-800 text-xs text-white border-neutral-700",children:O?"Demo WhatsApp Button":"Chat on WhatsApp"})]}),em&&ei&&(0,r.jsxs)(o.m_,{children:[(0,r.jsx)(o.k$,{asChild:!0,children:O?(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default",children:(0,r.jsx)(g.A,{className:"w-4 h-4",color:ef})}):(0,r.jsx)("a",{href:M?ei:"#",className:"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all ".concat(M?"hover:scale-110 hover:shadow-md":"cursor-default opacity-70"),children:(0,r.jsx)(g.A,{className:"w-4 h-4",color:ef})})}),(0,r.jsx)(o.ZI,{className:"bg-neutral-800 text-xs text-white border-neutral-700",children:O?"Demo Call Button":"Call directly"})]})]})})}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-1.5 mt-2",style:{background:"linear-gradient(to right, var(--theme-color), var(--theme-accent-end), var(--theme-color))"}})]}),(0,r.jsx)(A,{})]})}},48347:(e,t,a)=>{a.d(t,{LE:()=>l,oX:()=>s});var r=a(55594);let l=r.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});r.z.object({email:r.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),r.z.object({email:r.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:r.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),r.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character");let s=r.z.object({mobile:l,password:r.z.string().trim().min(1,{message:"Password is required"})})},60787:(e,t,a)=>{a.d(t,{A:()=>k});var r=a(95155),l=a(12115),s=a(56671),o=a(91788),n=a(81586),i=a(23837),d=a(97939),c=a(97168),m=a(53999),u=a(28695),h=a(25627);async function x(e,t){let a=new XMLSerializer().serializeToString(e);return new Promise((e,r)=>{let l=new Image;l.onload=()=>{let a=document.createElement("canvas");a.width=1e3,a.height=1e3;let s=a.getContext("2d");if(!s)return void r(Error("Could not get canvas context"));s.fillStyle="#FFFFFF",s.fillRect(0,0,1e3,1e3),s.imageSmoothingEnabled=!0,s.imageSmoothingQuality="high";s.drawImage(l,50,50,900,900),s.strokeStyle="#EEEEEE",s.lineWidth=2,s.strokeRect(48,48,904,904);let o=a.toDataURL("image/png",1),n=document.createElement("a");n.download="".concat(t,"-qr-code.png"),n.href=o,n.click(),e()},l.onerror=()=>{r(Error("Could not load QR code SVG"))},l.src="data:image/svg+xml;base64,".concat(btoa(a))})}async function g(e,t){let{businessName:a,ownerName:r,address:l,slug:s,themeColor:o="#F59E0B"}=t,n=document.createElement("canvas");n.width=2480,n.height=3508;let i=n.getContext("2d");if(!i)throw Error("Could not get canvas context");i.fillStyle="#FFFFFF",i.fillRect(0,0,2480,3508);let d=i.createLinearGradient(0,0,0,3508);d.addColorStop(0,"#FFFFFF"),d.addColorStop(1,"#F8F8F8"),i.fillStyle=d,i.fillRect(0,0,2480,3508),function(e,t,a){e.save(),e.globalAlpha=.03;for(let t=0;t<2480;t+=20)for(let a=0;a<3508;a+=20)e.beginPath(),e.arc(t,a,1,0,2*Math.PI),e.fillStyle="#000000",e.fill();e.restore()}(i,2480,3508),function(e,t,a,r){let l=e.createLinearGradient(0,0,2480,3508);l.addColorStop(0,r),l.addColorStop(.5,b(r,20)),l.addColorStop(1,r),e.strokeStyle=l,e.lineWidth=3,e.strokeRect(40,40,t-80,a-80),e.shadowColor="rgba(0, 0, 0, 0.1)",e.shadowBlur=15,e.shadowOffsetX=0,e.shadowOffsetY=0,e.strokeStyle=l,e.lineWidth=8,e.strokeRect(80,80,t-160,a-160),e.shadowColor="transparent",e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0,p(e,80,80,120,r,"top-left"),p(e,t-80,80,120,r,"top-right"),p(e,80,a-80,120,r,"bottom-left"),p(e,t-80,a-80,120,r,"bottom-right"),function(e,t,a,r){e.save(),e.globalAlpha=.2;for(let a=120;a<t-120;a+=40)e.beginPath(),e.arc(a,80,2,0,2*Math.PI),e.fillStyle=r,e.fill();for(let l=120;l<t-120;l+=40)e.beginPath(),e.arc(l,a-80,2,0,2*Math.PI),e.fillStyle=r,e.fill();for(let t=120;t<a-120;t+=40)e.beginPath(),e.arc(80,t,2,0,2*Math.PI),e.fillStyle=r,e.fill();for(let l=120;l<a-120;l+=40)e.beginPath(),e.arc(t-80,l,2,0,2*Math.PI),e.fillStyle=r,e.fill();e.restore()}(e,t,a,r)}(i,2480,3508,o),function(e,t,a,r){e.save();let l=e.createRadialGradient(0,0,0,0,0,400);l.addColorStop(0,r),l.addColorStop(1,"rgba(255, 255, 255, 0)");let s=e.createRadialGradient(2480,3508,0,t,a,400);s.addColorStop(0,r),s.addColorStop(1,"rgba(255, 255, 255, 0)"),e.globalAlpha=.08,e.beginPath(),e.arc(0,0,400,0,2*Math.PI),e.fillStyle=l,e.fill(),e.beginPath(),e.arc(t,a,400,0,2*Math.PI),e.fillStyle=s,e.fill(),e.globalAlpha=.05;let o=Math.ceil(a/60),n=Math.ceil(t/60);for(let t=0;t<o;t++)for(let a=0;a<n;a++)if(Math.abs(t-o/2)+Math.abs(a-n/2)<o/3){let l=60*a,s=60*t;(t+a)%2==0&&(e.beginPath(),e.arc(l+30,s+30,2,0,2*Math.PI),e.fillStyle=r,e.fill())}e.restore()}(i,2480,3508,o),i.fillStyle="#333333",i.textAlign="center";let c=160;i.font="bold ".concat(c,"px 'Arial'");let m=i.measureText(a).width;for(;m>1860&&c>80;)c-=10,i.font="bold ".concat(c,"px 'Arial'"),m=i.measureText(a).width;if(m>1860){let e=v(a,30),t=420.96;e.forEach(e=>{i.fillText(e,1240,t,1984),t+=.8*c})}else i.fillText(a,1240,526.1999999999999,1984);let u=Math.min(i.measureText(a).width,1488);i.beginPath(),i.moveTo(1240-u/2,596.36),i.lineTo(1240+u/2,596.36),i.strokeStyle=o,i.lineWidth=6,i.stroke(),i.font="80px 'Arial'",i.fillStyle="#555555",i.fillText("Scan to view our digital card",1240,771.76,1984);let h=v(a,30),x=2480*(h.length>1?.3:.35),g=(2480-x)/2,j=h.length>1?1227.8:1052.3999999999999;!function(e,t,a,r,l){let s=r+200,o=r+200,n=t-100,i=a-100;e.fillStyle="#FFFFFF",f(e,n,i,s,o,20),e.fill(),e.shadowColor="rgba(0, 0, 0, 0.15)",e.shadowBlur=40,e.shadowOffsetX=0,e.shadowOffsetY=15,e.fillStyle="#FFFFFF",f(e,n,i,s,o,20),e.fill(),e.shadowColor="transparent",e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0;let d=e.createLinearGradient(n,i,n+s,i+o);d.addColorStop(0,l),d.addColorStop(.5,b(l,20)),d.addColorStop(1,l),e.strokeStyle=d,e.lineWidth=3,f(e,n,i,s,o,20),e.stroke(),e.beginPath(),e.moveTo(n,i+30),e.lineTo(n,i),e.lineTo(n+30,i),e.strokeStyle=l,e.lineWidth=5,e.stroke(),e.beginPath(),e.moveTo(n+s-30,i),e.lineTo(n+s,i),e.lineTo(n+s,i+30),e.stroke(),e.beginPath(),e.moveTo(n,i+o-30),e.lineTo(n,i+o),e.lineTo(n+30,i+o),e.stroke(),e.beginPath(),e.moveTo(n+s-30,i+o),e.lineTo(n+s,i+o),e.lineTo(n+s,i+o-30),e.stroke()}(i,g,j,x,o);let w=new XMLSerializer().serializeToString(e);return new Promise((e,t)=>{let a=new Image;a.onload=()=>{let t;i.drawImage(a,g,j,x,x);let d="dukancard.in/".concat(s),c=70;i.font="bold ".concat(c,"px 'Arial'");let m=i.measureText(d).width;for(;m>1736&&c>40;)c-=5,i.font="bold ".concat(c,"px 'Arial'"),m=i.measureText(d).width;i.fillStyle="#333333",i.fillText(d,1240,j+x+180,1984),function(e,t,a,r,l){e.save();let s=e.createLinearGradient(372,a,t/2+r/2,a);s.addColorStop(0,"rgba(255, 255, 255, 0)"),s.addColorStop(.1,l),s.addColorStop(.5,b(l,20)),s.addColorStop(.9,l),s.addColorStop(1,"rgba(255, 255, 255, 0)"),e.beginPath(),e.moveTo(t/2-r/2,a),e.lineTo(t/2+r/2,a),e.strokeStyle=s,e.lineWidth=3,e.stroke(),e.beginPath(),e.arc(t/2,a,15,0,2*Math.PI),e.fillStyle=l,e.fill(),e.beginPath(),e.arc(t/2,a,20,0,2*Math.PI),e.strokeStyle=l,e.lineWidth=2,e.stroke();let o=r/4/6;for(let s=1;s<=6;s++){let n=t/2-r/8-s*o;e.beginPath(),e.arc(n,a,3,0,2*Math.PI),e.fillStyle=l,e.fill()}for(let s=1;s<=6;s++){let n=t/2+r/8+s*o;e.beginPath(),e.arc(n,a,3,0,2*Math.PI),e.fillStyle=l,e.fill()}e.restore()}(i,2480,j+x+240,1736,o);let u=100;i.font="bold ".concat(u,"px 'Arial'"),i.fillStyle="#333333";let h=i.measureText(r).width;for(;h>1860&&u>60;)u-=5,i.font="bold ".concat(u,"px 'Arial'"),h=i.measureText(r).width;if(h>1860){let e=v(r,25),t=2631;e.forEach(e=>{i.fillText(e,1240,t,1984),t+=.7*u})}else i.fillText(r,1240,2631,1984);let p=70;i.font="".concat(p,"px 'Arial'"),i.fillStyle="#555555",t=h>1860?2631+v(r,25).length*u*.7+50:2806.4;let f=v(l,50);f.length>3&&(p=60,i.font="".concat(p,"px 'Arial'")),f.forEach(e=>{i.fillText(e,1240,t,1984),t+=p+20}),i.font="50px 'Arial'",i.fillStyle="#888888";let w=Math.min(3408,t+150);i.fillText("Powered by Dukancard",1240,w,1984);let y=n.toDataURL("image/jpeg",.95),_=document.createElement("a");_.download="".concat(s,"-qrcode.jpg"),_.href=y,_.click(),e()},a.onerror=()=>{t(Error("Could not load QR code SVG"))},a.src="data:image/svg+xml;base64,".concat(btoa(w))})}function p(e,t,a,r,l,s){e.save();let o=e.createLinearGradient(s.includes("left")?t:t-r,s.includes("top")?a:a-r,s.includes("left")?t+r:t,s.includes("top")?a+r:a);o.addColorStop(0,l),o.addColorStop(1,b(l,20)),e.strokeStyle=o,e.lineWidth=8,e.lineCap="round",e.beginPath(),"top-left"===s?(e.moveTo(t,a+r),e.lineTo(t,a),e.lineTo(t+r,a),e.moveTo(t+30,a+30),e.arc(t+30,a+30,8,0,2*Math.PI)):"top-right"===s?(e.moveTo(t-r,a),e.lineTo(t,a),e.lineTo(t,a+r),e.moveTo(t-30,a+30),e.arc(t-30,a+30,8,0,2*Math.PI)):"bottom-left"===s?(e.moveTo(t,a-r),e.lineTo(t,a),e.lineTo(t+r,a),e.moveTo(t+30,a-30),e.arc(t+30,a-30,8,0,2*Math.PI)):"bottom-right"===s&&(e.moveTo(t-r,a),e.lineTo(t,a),e.lineTo(t,a-r),e.moveTo(t-30,a-30),e.arc(t-30,a-30,8,0,2*Math.PI)),e.stroke(),e.fillStyle=l,"top-left"===s?(e.beginPath(),e.arc(t+30,a+30,8,0,2*Math.PI),e.fill()):"top-right"===s?(e.beginPath(),e.arc(t-30,a+30,8,0,2*Math.PI),e.fill()):"bottom-left"===s?(e.beginPath(),e.arc(t+30,a-30,8,0,2*Math.PI),e.fill()):"bottom-right"===s&&(e.beginPath(),e.arc(t-30,a-30,8,0,2*Math.PI),e.fill()),e.restore()}function f(e,t,a,r,l,s){e.beginPath(),e.moveTo(t+s,a),e.lineTo(t+r-s,a),e.quadraticCurveTo(t+r,a,t+r,a+s),e.lineTo(t+r,a+l-s),e.quadraticCurveTo(t+r,a+l,t+r-s,a+l),e.lineTo(t+s,a+l),e.quadraticCurveTo(t,a+l,t,a+l-s),e.lineTo(t,a+s),e.quadraticCurveTo(t,a,t+s,a),e.closePath()}function b(e,t){let a,r,l;return e.startsWith("#")?(a=parseInt(e.slice(1,3),16),r=parseInt(e.slice(3,5),16),l=parseInt(e.slice(5,7),16)):(a=245,r=158,l=11),a=Math.max(0,Math.min(255,a+t)),r=Math.max(0,Math.min(255,r+t)),l=Math.max(0,Math.min(255,l+t)),"#".concat(a.toString(16).padStart(2,"0")).concat(r.toString(16).padStart(2,"0")).concat(l.toString(16).padStart(2,"0"))}function v(e,t){let a=e.split(" "),r=[],l="";return a.forEach(e=>{(l+e).length<=t?l+=(l?" ":"")+e:(r.push(l),l=e)}),l&&r.push(l),r}var j=a(78014);async function w(e,t){let{businessSlug:a,quality:r=1,scale:l=3,preserveRoundedCorners:s=!0}=t;try{let t=e.getBoundingClientRect();if(console.log("Card element dimensions:",{width:t.width,height:t.height,offsetWidth:e.offsetWidth,offsetHeight:e.offsetHeight,className:e.className,tagName:e.tagName}),0===t.width||0===t.height)throw Error("Card element has invalid dimensions");let o=await (0,j.Tf)(e,{quality:r,scale:l,backgroundColor:"transparent",style:{transform:"scale(1)",transformOrigin:"top left",borderRadius:s?"inherit":"0",width:"".concat(t.width,"px"),height:"".concat(t.height,"px"),maxWidth:"".concat(t.width,"px"),maxHeight:"".concat(t.height,"px"),overflow:"hidden"},width:t.width,height:t.height}),n=document.createElement("a");n.download="".concat(a,"-digital-card.png"),n.href=o,n.click()}catch(e){throw console.error("Error downloading business card as PNG:",e),Error("Failed to download business card as PNG")}}async function y(e,t){return w(e,t)}var _=a(67133);function k(e){let{businessSlug:t,businessName:a,ownerName:p="",businessAddress:f="",themeColor:b="#F59E0B",className:v}=e,[j,w]=(0,l.useState)(null),k=(0,l.useRef)(null),[N,S]=(0,l.useState)(!1);(0,l.useEffect)(()=>{S(!0)},[]),(0,l.useEffect)(()=>{if(k.current){let e=k.current.querySelector("svg");e instanceof SVGSVGElement&&w(e)}},[]);let C=async()=>{if(!t)return void s.oR.error("Business slug not available.");if(!j)return void s.oR.error("QR code not available for download.");try{let e=f.trim()||"Address not available",r=p.trim()||"Owner";await g(j,{businessName:a,ownerName:r,address:e,slug:t,qrValue:"".concat("http://localhost:3000","/").concat(t),themeColor:b}),s.oR.success("A4 QR code downloaded!")}catch(e){console.error("Error generating QR code:",e),s.oR.error("Could not download QR code.")}},M=async()=>{if(!t)return void s.oR.error("Business slug not available.");if(!j)return void s.oR.error("QR code not available for download.");try{await x(j,t),s.oR.success("High-quality QR image downloaded!")}catch(e){console.error("Error downloading QR image:",e),s.oR.error("Could not download QR image.")}},P=async()=>{if(!t)return void s.oR.error("Business slug not available.");try{let e=function(e){let t=document,a=[];for(let e of["[data-card-element]",".business-card-preview",".business-card","#business-card",".card-preview"])t.querySelectorAll(e).forEach(e=>{let t=e.getBoundingClientRect();t.width>0&&t.height>0&&t.width<=500&&a.push(e)});if(0===a.length)return null;if(1===a.length)return a[0];let r=a.map(e=>{let t=0,a=e.querySelector('a[href^="tel:"]'),r=e.querySelector('a[href^="mailto:"]'),l=(null==a?void 0:a.textContent)||"",s=(null==r?void 0:r.textContent)||"";l&&!l.includes("*")&&(t+=10),s&&!s.includes("*")&&(t+=10);let o=e.getBoundingClientRect();return o.top>=0&&o.left>=0&&(t+=5),e.closest("nav, header, footer")||(t+=5),o.width>300&&(t+=3),{element:e,score:t}});return r.sort((e,t)=>t.score-e.score),r[0].element}();if(e&&e.getBoundingClientRect().width>500){let t=e.querySelector("[data-card-element]");t&&(e=t)}if(!e)return void s.oR.error("Business card not found for download.");await y(e,{businessName:a,businessSlug:t}),s.oR.success("Digital card downloaded as PNG!")}catch(e){console.error("Error downloading business card as PNG:",e),s.oR.error("Could not download business card.")}},T=t?"https://dukancard.in/".concat(t):"";return(0,r.jsxs)("div",{className:(0,m.cn)("w-full max-w-sm mx-auto space-y-5 mt-6",v),children:[(0,r.jsx)("div",{className:"hidden",children:(0,r.jsx)("div",{id:"public-card-qrcode",ref:k,children:(0,r.jsx)(h.Ay,{value:T,size:300,level:"M",bgColor:"#FFFFFF",fgColor:"#000000"})})}),(0,r.jsxs)("div",{className:"w-full relative group",children:[N&&(0,r.jsx)(u.P.div,{className:"absolute -inset-0.5 rounded-full blur-md",style:{background:"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))"},initial:{opacity:.7},animate:{opacity:[.7,.9,.7],boxShadow:["0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)","0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)","0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)"]},transition:{duration:2,repeat:1/0,repeatType:"loop",ease:"easeInOut"}}),(0,r.jsxs)(_.rI,{children:[(0,r.jsx)(_.ty,{asChild:!0,children:(0,r.jsxs)(c.$,{className:(0,m.cn)("w-full py-6 relative overflow-hidden group","bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90","text-black dark:text-neutral-900 font-medium text-base","border-none rounded-full shadow-lg hover:shadow-xl","transition-all duration-300 ease-out"),children:[(0,r.jsx)("span",{className:"absolute inset-0 w-full h-full overflow-hidden",children:(0,r.jsx)("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none"})}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3 relative z-10",children:[(0,r.jsx)("div",{className:"bg-white/20 p-2 rounded-lg",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-black dark:text-neutral-900"})}),(0,r.jsx)("span",{className:"text-black dark:text-neutral-900 font-semibold",children:"Download Options"})]})]})}),(0,r.jsxs)(_.SQ,{align:"center",className:"w-56 bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800",children:[(0,r.jsxs)(_._2,{onClick:P,className:"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800",children:[(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Download Digital Card (PNG)"})]}),(0,r.jsx)(_.mB,{}),(0,r.jsxs)(_._2,{onClick:C,className:"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800",children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Download A4 Size QR"})]}),(0,r.jsxs)(_._2,{onClick:M,className:"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Download High-Quality QR Image"})]})]})]})]})]})}},82147:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(95155);a(12115);let l=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,r.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"})})},86601:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(95155);a(12115);let l=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,r.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"})})}}]);