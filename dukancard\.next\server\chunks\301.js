"use strict";exports.id=301,exports.ids=[301],exports.modules={301:(e,s,o)=>{o.a(e,async(e,t)=>{try{o.d(s,{handleSubscriptionCompleted:()=>l});var r=o(32032),n=o(65193),i=o(28485),a=o(94230),c=e([i]);async function l(e,s,o){let t=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription completed: ${c}`);let l=(0,n.extractWebhookTimestamp)(e);t={subscriptionId:c,eventType:"subscription.completed",eventId:o||`completed_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let p=await i.webhookProcessor.processWebhookEvent(t);if(!p.shouldProcess)return{success:p.success,message:p.message};let d=await (0,r.createClient)(),{data:u,error:_}=await d.from("payment_subscriptions").select("subscription_status, plan_id, business_profile_id").eq("razorpay_subscription_id",c).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${c}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!u)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${c}, skipping completion processing`),{success:!0,message:"No subscription found to complete"};console.log(`[RAZORPAY_WEBHOOK] Completing subscription ${c}, downgrading to free plan`);let m=new Date().toISOString(),b=await (0,a.M)({subscription_id:c,business_profile_id:u.business_profile_id,subscription_status:"active",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",subscription_start_date:m,cancelled_at:m,razorpay_subscription_id:null,razorpay_customer_id:null,razorpay_plan_id:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,updated_at:m}});if(b.success)return await i.webhookProcessor.markEventAsSuccess(t.eventId,"Subscription completed and downgraded to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully completed subscription ${c} and downgraded to free plan`),{success:!0,message:"Subscription completed and downgraded to free plan"};return await i.webhookProcessor.markEventAsFailed(t.eventId,b.message),console.error(`[RAZORPAY_WEBHOOK] Failed to complete subscription ${c}:`,b.message),b}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription completed:",e),{success:!1,message:`Error handling subscription completed: ${e instanceof Error?e.message:String(e)}`}}}i=(c.then?(await c)():c)[0],t()}catch(e){t(e)}})}};