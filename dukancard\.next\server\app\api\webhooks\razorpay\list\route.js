(()=>{var e={};e.id=7872,e.ids=[5453,7872],e.modules={321:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>l});var o={};t.r(o),t.d(o,{GET:()=>u});var s=t(96559),n=t(48088),a=t(37719),c=t(32190),i=t(2331);async function u(e){try{let r=new URL(e.url),t=r.searchParams.get("account_id");if(!t)return c.NextResponse.json({success:!1,message:"Missing account_id parameter"},{status:400});let o={},s=r.searchParams.get("from");if(s){let e=parseInt(s,10);isNaN(e)||(o.from=e)}let n=r.searchParams.get("to");if(n){let e=parseInt(n,10);isNaN(e)||(o.to=e)}let a=r.searchParams.get("count");if(a){let e=parseInt(a,10);isNaN(e)||(o.count=e)}let u=r.searchParams.get("skip");if(u){let e=parseInt(u,10);isNaN(e)||(o.skip=e)}let p=await (0,i.HD)(t,Object.keys(o).length>0?o:void 0);if(!p.success)return c.NextResponse.json({success:!1,error:p.error},{status:400});return c.NextResponse.json({success:!0,data:p.data},{status:200})}catch(e){return console.error("[RAZORPAY_WEBHOOK_LIST] Error listing webhooks:",e),c.NextResponse.json({success:!1,message:"Error listing webhooks",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/webhooks/razorpay/list/route",pathname:"/api/webhooks/razorpay/list",filename:"route",bundlePath:"app/api/webhooks/razorpay/list/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\list\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:h,workUnitAsyncStorage:l,serverHooks:d}=p;function f(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:l})}},2331:(e,r,t)=>{"use strict";t.d(r,{HD:()=>n,Ih:()=>a,OB:()=>s,Xz:()=>c,ju:()=>i});var o=t(95453);async function s(e,r){try{let t=(0,o.bG)(),s=await fetch(`${o.ST}/accounts/${e}/webhooks`,{method:"POST",headers:t,body:JSON.stringify(r)}),n=await s.json();if(!s.ok)return console.error("[RAZORPAY] Error creating webhook:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY] Exception creating webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function n(e,r){try{let t=(0,o.bG)(),s="";if(r){let e=new URLSearchParams;void 0!==r.from&&e.append("from",r.from.toString()),void 0!==r.to&&e.append("to",r.to.toString()),void 0!==r.count&&e.append("count",r.count.toString()),void 0!==r.skip&&e.append("skip",r.skip.toString());let t=e.toString();t&&(s=`?${t}`)}let n=await fetch(`${o.ST}/accounts/${e}/webhooks${s}`,{method:"GET",headers:t}),a=await n.json();if(!n.ok)return console.error("[RAZORPAY] Error fetching webhooks:",a),{success:!1,error:a};return{success:!0,data:a.items}}catch(e){return console.error("[RAZORPAY] Exception fetching webhooks:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function a(e,r){try{let t=(0,o.bG)(),s=await fetch(`${o.ST}/accounts/${e}/webhooks/${r}`,{method:"GET",headers:t}),n=await s.json();if(!s.ok)return console.error("[RAZORPAY] Error fetching webhook:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY] Exception fetching webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function c(e,r,t){try{if(!t.url)return{success:!1,error:"URL is required for updating a webhook"};if(!t.events||!Array.isArray(t.events)||0===t.events.length)return{success:!1,error:"Events array is required for updating a webhook"};let s=(0,o.bG)(),n=await fetch(`${o.ST}/accounts/${e}/webhooks/${r}`,{method:"PATCH",headers:s,body:JSON.stringify(t)}),a=await n.json();if(!n.ok)return console.error("[RAZORPAY] Error updating webhook:",a),{success:!1,error:a};return{success:!0,data:a}}catch(e){return console.error("[RAZORPAY] Exception updating webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function i(e,r){try{let t=(0,o.bG)(),s=await fetch(`${o.ST}/accounts/${e}/webhooks/${r}`,{method:"DELETE",headers:t});if(!s.ok){let e;try{e=await s.json()}catch(r){e={error:s.statusText}}return console.error("[RAZORPAY] Error deleting webhook:",e),{success:!1,error:e}}return{success:!0}}catch(e){return console.error("[RAZORPAY] Exception deleting webhook:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},95453:(e,r,t)=>{"use strict";t.d(r,{ST:()=>n,bG:()=>c,t6:()=>i});var o=t(55511),s=t.n(o);let n="https://api.razorpay.com/v2",a=()=>{let e,r;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",r=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!r)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!r,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:r}},c=()=>{let{keyId:e,keySecret:r}=a(),t=Buffer.from(`${e}:${r}`).toString("base64");return{Authorization:`Basic ${t}`,"Content-Type":"application/json"}},i=(e,r,t)=>{try{let o=s().createHmac("sha256",t).update(e).digest("hex");return s().timingSafeEqual(Buffer.from(r),Buffer.from(o))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4447,580],()=>t(321));module.exports=o})();