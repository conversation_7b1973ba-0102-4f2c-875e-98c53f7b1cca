"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196],{90196:(e,t,i)=>{async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{format:i="webp",targetSizeKB:n=100,maxDimension:a=800,quality:r=.8}=t;return new Promise((t,o)=>{let l=new Image;l.onload=()=>{try{let s=document.createElement("canvas"),c=s.getContext("2d");if(!c)return void o(Error("Could not get canvas context"));let{width:u,height:d}=l;(u>a||d>a)&&(u>d?(d=d*a/u,u=a):(u=u*a/d,d=a)),s.width=u,s.height=d,c.drawImage(l,0,0,u,d);let m=r,g=0,h=()=>{s.toBlob(i=>{if(!i)return void o(Error("Failed to create blob"));let a=i.size/1024;if(a<=n||g>=5||m<=.1){let n=e.size/i.size;t({blob:i,finalSizeKB:Math.round(100*a)/100,compressionRatio:Math.round(100*n)/100,dimensions:{width:u,height:d}})}else g++,m=Math.max(.1,m-.15),h()},"image/".concat(i),m)};h()}catch(e){o(e)}},l.onerror=()=>o(Error("Failed to load image")),l.src=URL.createObjectURL(e)})}async function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.size/1048576,a=100,r=800,o=.7;return i<=2?(o=.7,r=800,a=90):i<=5?(o=.55,r=700,a=80):i<=10?(o=.45,r=600,a=70):(o=.35,r=550,a=60),n(e,{...t,targetSizeKB:t.targetSizeKB||a,maxDimension:t.maxDimension||r,quality:t.quality||o})}async function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n(e,{targetSizeKB:50,maxDimension:400,quality:.7,...t})}i.d(t,{compressImageUltraAggressiveClient:()=>a,q:()=>r})}}]);