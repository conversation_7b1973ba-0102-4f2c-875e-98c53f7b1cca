"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7542],{2462:(e,t,r)=>{r.d(t,{W:()=>n});var a=r(34477);let n=(0,a.createServerReference)("40b79faef168e2af792fe9df79d32b29f3f0fe259d",a.callServer,void 0,a.findSourceMapURL,"deleteReview")},27737:(e,t,r)=>{r.d(t,{E:()=>s});var a=r(95155),n=r(53999);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,n.cn)("bg-accent animate-pulse rounded-md",t),...r})}},40019:(e,t,r)=>{r.d(t,{A:()=>k});var a=r(95155),n=r(12115),s=r(6874),i=r.n(s),l=r(28695),o=r(97168),d=r(69663),u=r(33786),c=r(23227),m=r(71007),x=r(38564),v=r(54416),g=r(51154),b=r(5196),h=r(13717),f=r(62525),p=r(2462),w=r(56671),N=r(8830),j=r(53999),y=r(99474);function k(e){var t,r,s,k;let _,{review:A,onDeleteSuccess:z,isReviewsReceivedTab:C=!1}=e,[S,R]=(0,n.useState)(!1),[P,I]=(0,n.useState)(!1),[B,$]=(0,n.useState)(!1),[F,U]=(0,n.useState)(A.rating),[E,L]=(0,n.useState)(A.review_text||""),K=A.business_profiles;try{_=(0,N.m)(new Date(A.updated_at||A.created_at),{addSuffix:!0})}catch(e){_="recently"}let W=async()=>{if(!A.business_profile_id)return void w.oR.error("Cannot delete review: Missing business information");if(confirm("Are you sure you want to delete this review?"))try{R(!0);let e=await (0,p.W)(A.business_profile_id);e.success?(w.oR.success("Review for ".concat((null==K?void 0:K.business_name)||"business"," deleted.")),z&&z(A.id)):w.oR.error("Failed to delete review: ".concat(e.error||"Unknown error"))}catch(e){w.oR.error("An error occurred while deleting the review")}finally{R(!1)}},q=async()=>{$(!0);try{let e=await fetch("/api/customer/reviews/update",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({reviewId:A.id,rating:F,reviewText:E})}),t=await e.json();e.ok?(w.oR.success("Review updated successfully"),I(!1),A.rating=F,A.review_text=E):w.oR.error(t.error||"Failed to update review")}catch(e){console.error("Error updating review:",e),w.oR.error("An unexpected error occurred")}finally{$(!1)}};return(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},whileHover:P?{}:{y:-5,transition:{duration:.2}},className:(0,j.cn)("rounded-xl border border-neutral-200 dark:border-neutral-800","bg-white dark:bg-black","shadow-sm p-4 transition-all duration-300 hover:shadow-md","relative overflow-hidden group",P&&"ring-2 ring-amber-400 dark:ring-amber-600"),children:[(0,a.jsx)("div",{className:"absolute inset-0 pointer-events-none opacity-5 dark:opacity-10",style:{backgroundImage:'url("/decorative/card-texture.svg")',backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)("div",{className:"flex items-start mb-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:["business"===A.reviewer_type&&A.reviewer_slug?(0,a.jsx)(i(),{href:"/".concat(A.reviewer_slug),target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsxs)(d.eu,{className:"h-10 w-10 border border-neutral-200 dark:border-neutral-800",children:[(null==K?void 0:K.logo_url)?(0,a.jsx)(d.BK,{src:K.logo_url,alt:(null==K?void 0:K.business_name)||"Business"}):null,(0,a.jsx)(d.q5,{className:"bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300",children:(null==K||null==(r=K.business_name)||null==(t=r[0])?void 0:t.toUpperCase())||"B"})]})}):(0,a.jsxs)(d.eu,{className:"h-10 w-10 border border-neutral-200 dark:border-neutral-800",children:[(null==K?void 0:K.logo_url)?(0,a.jsx)(d.BK,{src:K.logo_url,alt:(null==K?void 0:K.business_name)||"Business"}):null,(0,a.jsx)(d.q5,{className:"bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300",children:(null==K||null==(k=K.business_name)||null==(s=k[0])?void 0:s.toUpperCase())||"B"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"flex items-center gap-1",children:"business"===A.reviewer_type&&A.reviewer_slug?(0,a.jsxs)(i(),{href:"/".concat(A.reviewer_slug),target:"_blank",rel:"noopener noreferrer",className:"hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1",children:[(0,a.jsx)("h3",{className:"font-medium text-neutral-800 dark:text-neutral-200",children:(null==K?void 0:K.business_name)||"Unknown Business"}),(0,a.jsx)(u.A,{className:"h-3.5 w-3.5 opacity-70"})]}):(0,a.jsx)("h3",{className:"font-medium text-neutral-800 dark:text-neutral-200",children:(null==K?void 0:K.business_name)||"Unknown User"})}),C&&(0,a.jsx)("div",{className:"flex items-center gap-1 mt-1",children:"business"===A.reviewer_type?(0,a.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium",children:[(0,a.jsx)(c.A,{className:"h-3 w-3"}),"Business"]}):(0,a.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),"Customer"]})}),!P&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[Array.from({length:5}).map((e,t)=>(0,a.jsx)(x.A,{className:(0,j.cn)("h-4 w-4",t<A.rating?"text-amber-500 fill-amber-500":"text-neutral-300 dark:text-neutral-700")},t)),(0,a.jsx)("span",{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:_})]})]})]})}),P?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:5}).map((e,t)=>(0,a.jsx)("button",{type:"button",onClick:()=>U(t+1),className:"focus:outline-none",children:(0,a.jsx)(x.A,{className:(0,j.cn)("h-6 w-6 transition-colors",t<F?"text-amber-500 fill-amber-500":"text-neutral-300 dark:text-neutral-700 hover:text-amber-400")})},t))}),(0,a.jsx)(y.T,{value:E,onChange:e=>L(e.target.value),placeholder:"What did you like or dislike? What stood out about your experience?",className:"w-full bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-lg focus:ring-amber-400 focus:border-amber-400 transition-all duration-200 resize-none",maxLength:500,rows:4}),(0,a.jsxs)("div",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1 text-right",children:[E.length,"/500"]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-2 mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50",children:[(0,a.jsxs)(o.$,{variant:"outline",size:"sm",className:"text-xs h-8 gap-1",onClick:()=>{I(!1),U(A.rating),L(A.review_text||"")},children:[(0,a.jsx)(v.A,{className:"h-3.5 w-3.5"}),"Cancel"]}),(0,a.jsxs)(o.$,{variant:"default",size:"sm",className:"text-xs h-8 gap-1 bg-amber-500 hover:bg-amber-600 text-white",onClick:q,disabled:B,children:[B?(0,a.jsx)(g.A,{className:"h-3.5 w-3.5 animate-spin"}):(0,a.jsx)(b.A,{className:"h-3.5 w-3.5"}),"Save"]})]})]}):(0,a.jsxs)(a.Fragment,{children:[A.review_text&&(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("p",{className:"text-neutral-700 dark:text-neutral-300 text-sm leading-relaxed",children:A.review_text})}),!C&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50",children:["business"===A.reviewer_type&&(null==K?void 0:K.business_slug)?(0,a.jsx)(o.$,{asChild:!0,variant:"outline",size:"sm",className:"text-xs h-8 gap-1",children:(0,a.jsxs)(i(),{href:"/".concat(K.business_slug),target:"_blank",rel:"noopener noreferrer",children:[(0,a.jsx)(u.A,{className:"h-3.5 w-3.5"}),"View Business"]})}):(0,a.jsx)("div",{}),z&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(o.$,{variant:"outline",size:"sm",className:"text-xs h-8 gap-1 text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300 dark:text-blue-400 dark:border-blue-900 dark:hover:bg-blue-950/30",onClick:()=>{I(!0)},children:[(0,a.jsx)(h.A,{className:"h-3.5 w-3.5"}),"Edit"]}),(0,a.jsxs)(o.$,{variant:"outline",size:"sm",className:"text-xs h-8 gap-1 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-900 dark:hover:bg-red-950/30",onClick:W,disabled:S,children:[S?(0,a.jsx)(g.A,{className:"h-3.5 w-3.5 animate-spin"}):(0,a.jsx)(f.A,{className:"h-3.5 w-3.5"}),"Delete"]})]})]})]})]})]})}},53999:(e,t,r)=>{r.d(t,{M0:()=>u,Yq:()=>c,cn:()=>s,gV:()=>i,gY:()=>d,kY:()=>l,vA:()=>o,vv:()=>m});var a=r(52596),n=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function i(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function l(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),r=e.substring(e.length-2),a="*".repeat(e.length-4);return"".concat(t).concat(a).concat(r)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),r=t[0],a=t[1];if(r.length<=2||a.length<=2||!a.includes("."))return"Email Hidden";let n=r.substring(0,2)+"*".repeat(r.length-2),s=a.split("."),i=s[0],l=s.slice(1).join("."),o=i.substring(0,2)+"*".repeat(i.length-2);return"".concat(n,"@").concat(o,".").concat(l)}function d(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),r=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let a=r.length-1;a>=0;a--)if(t>=r[a].value)return(e/r[a].value).toFixed(1).replace(/\.0$/,"")+r[a].symbol;return e.toString()}function u(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let r={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(r.hour="2-digit",r.minute="2-digit",r.hour12=!0),e.toLocaleString("en-IN",r)}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(r){return"".concat(t," ").concat(e.toFixed(2))}}},67133:(e,t,r)=>{r.d(t,{SQ:()=>o,_2:()=>d,lp:()=>u,mB:()=>c,rI:()=>i,ty:()=>l});var a=r(95155);r(12115);var n=r(76215),s=r(53999);function i(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function l(e){let{...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:r=4,...i}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:r,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...i})})}function d(e){let{className:t,inset:r,variant:i="default",...l}=e;return(0,a.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":r,"data-variant":i,className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function u(e){let{className:t,inset:r,...i}=e;return(0,a.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":r,className:(0,s.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...i})}function c(e){let{className:t,...r}=e;return(0,a.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",t),...r})}},69663:(e,t,r)=>{r.d(t,{BK:()=>l,eu:()=>i,q5:()=>o});var a=r(95155);r(12115);var n=r(54011),s=r(53999);function i(e){let{className:t,...r}=e;return(0,a.jsx)(n.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)(n._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...r})}},93432:(e,t,r)=>{r.d(t,{Eb:()=>v,Iu:()=>c,M_:()=>b,WA:()=>g,cU:()=>m,dK:()=>u,n$:()=>x});var a=r(95155),n=r(12115),s=r(42355),i=r(13052),l=r(5623),o=r(53999),d=r(97168);let u=e=>{let{className:t,...r}=e;return(0,a.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,o.cn)("mx-auto flex w-full justify-center",t),...r})},c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("ul",{ref:t,className:(0,o.cn)("flex flex-row items-center gap-1",r),...n})});c.displayName="PaginationContent";let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("li",{ref:t,className:(0,o.cn)("",r),...n})});m.displayName="PaginationItem";let x=e=>{let{className:t,isActive:r,size:n="icon",...s}=e;return(0,a.jsx)("a",{"aria-current":r?"page":void 0,className:(0,o.cn)((0,d.r)({variant:r?"outline":"ghost",size:n}),t,r&&"bg-muted hover:bg-muted pointer-events-none"),...s})};x.displayName="PaginationLink";let v=e=>{let{className:t,...r}=e;return(0,a.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",t),...r,children:[(0,a.jsx)(s.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]})};v.displayName="PaginationPrevious";let g=e=>{let{className:t,...r}=e;return(0,a.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",t),...r,children:[(0,a.jsx)("span",{children:"Next"}),(0,a.jsx)(i.A,{className:"h-4 w-4"})]})};g.displayName="PaginationNext";let b=e=>{let{className:t,...r}=e;return(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",t),...r,children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"More pages"})]})};b.displayName="PaginationEllipsis"},97168:(e,t,r)=>{r.d(t,{$:()=>o,r:()=>l});var a=r(95155);r(12115);var n=r(99708),s=r(74466),i=r(53999);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,asChild:o=!1,...d}=e,u=o?n.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:s,className:t})),...d})}},99474:(e,t,r)=>{r.d(t,{T:()=>s});var a=r(95155);r(12115);var n=r(53999);function s(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}}}]);