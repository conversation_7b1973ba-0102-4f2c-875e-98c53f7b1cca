(()=>{var e={};e.id=2868,e.ids=[2868],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14719:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19559:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>u});var t=r(37413);r(61120);var s=r(14890),i=r(60644),n=r(11637),d=r(95006),l=r(92506),o=r(46501),c=r(21886),m=r(23392);function u({children:e}){return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(m.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,t.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,t.jsx)(s.default,{}),(0,t.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,t.jsx)(i.default,{}),(0,t.jsx)(d.default,{}),(0,t.jsx)(n.default,{}),(0,t.jsx)(l.default,{}),(0,t.jsx)(o.default,{}),(0,t.jsx)(c.default,{excludePaths:["/dashboard"]})]})})})}},20166:(e,a,r)=>{Promise.resolve().then(r.bind(r,23174))},23174:(e,a,r)=>{"use strict";r.d(a,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\contact\\\\ModernContactClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx","default")},25334:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},26373:(e,a,r)=>{"use strict";r.d(a,{A:()=>l});var t=r(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,a,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===a).join(" ").trim();var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,t.forwardRef)(({color:e="currentColor",size:a=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:d="",children:l,iconNode:o,...c},m)=>(0,t.createElement)("svg",{ref:m,...n,width:a,height:a,stroke:e,strokeWidth:s?24*Number(r)/Number(a):r,className:i("lucide",d),...c},[...o.map(([e,a])=>(0,t.createElement)(e,a)),...Array.isArray(l)?l:[l]])),l=(e,a)=>{let r=(0,t.forwardRef)(({className:r,...n},l)=>(0,t.createElement)(d,{ref:l,iconNode:a,className:i(`lucide-${s(e)}`,r),...n}));return r.displayName=`${e}`,r}},27900:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27908:(e,a,r)=>{"use strict";r.d(a,{A:()=>i});var t=r(60687),s=r(77882);function i({variant:e="gold",className:a=""}){return(0,t.jsxs)("div",{className:`w-full h-12 md:h-16 relative overflow-hidden ${a}`,children:[(0,t.jsx)("div",{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-7xl px-4 mx-auto",children:(0,t.jsx)(s.P.div,{className:"w-full h-px bg-gradient-to-r from-transparent via-neutral-400 to-transparent dark:via-neutral-600",initial:{width:"0%",left:"50%"},whileInView:{width:"100%",left:"0%"},viewport:{once:!0},transition:{duration:1.2}})}),(0,t.jsx)(s.P.div,{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-neutral-400 dark:bg-neutral-600",initial:{scale:0,opacity:0},whileInView:{scale:1,opacity:.7},viewport:{once:!0},transition:{duration:.5,delay:.7}}),(0,t.jsx)(s.P.div,{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full border-2 border-neutral-300 dark:border-neutral-700",initial:{scale:0,opacity:0},whileInView:{scale:1,opacity:.5},viewport:{once:!0},transition:{duration:.5,delay:.8}})]})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32289:(e,a,r)=>{Promise.resolve().then(r.bind(r,93119))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40228:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41550:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},45063:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>o,generateMetadata:()=>l});var t=r(37413),s=r(61120),i=r(23174),n=r(54670),d=r(63420);async function l(){let e=`Contact ${n.C.name}`,a=`Reach out to ${n.C.name} for support, inquiries, or feedback. Find our contact details, office address, and connect with us easily.`,r="http://localhost:3000",t=`${r}/contact`,s=`${r}/opengraph-image.png`;return{title:e,description:a,keywords:[`contact ${n.C.name}`,`${n.C.name} support`,`${n.C.name} phone number`,`${n.C.name} email`,`${n.C.name} address`,"digital business card help"],alternates:{canonical:"/contact"},openGraph:{title:e,description:a,url:t,siteName:n.C.name,type:"website",locale:"en_IN",images:[{url:s,width:1200,height:630,alt:`Contact ${n.C.name}`}]},twitter:{card:"summary_large_image",title:e,description:a,images:[s]},other:{"application-ld+json":JSON.stringify({"@context":"https://schema.org","@type":"ContactPage",name:e,description:a,url:t,isPartOf:{"@type":"WebSite",name:n.C.name,url:r},mainEntity:{"@type":"Organization",name:n.C.name,url:r,logo:`${r}/logo.png`,contactPoint:{"@type":"ContactPoint",telephone:n.C.contact.phone,contactType:"Customer Service",email:n.C.contact.email},address:{"@type":"PostalAddress",streetAddress:n.C.contact.address.street,addressLocality:n.C.contact.address.city,addressRegion:n.C.contact.address.state,postalCode:n.C.contact.address.postalCode,addressCountry:"IN"}}})}}}function o(){return(0,t.jsx)(s.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(d.A,{className:"animate-spin"})}),children:(0,t.jsx)(i.default,{})})}},48340:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},48730:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},50051:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=r(65239),s=r(48088),i=r(88170),n=r.n(i),d=r(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(a,l);let o={children:["",{children:["(main)",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,45063)),"C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19559)),"C:\\web-app\\dukancard\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(main)/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},54670:(e,a,r)=>{"use strict";r.d(a,{C:()=>t});let t={name:"Dukancard",description:"Create and share digital business cards, showcase products, and connect with customers.",url:"https://dukancard.in",ogImage:"https://dukancard.in/opengraph-image.png",contact:{email:"<EMAIL>",phone:"+91 8458060663",address:{street:"Bisra Road",city:"Rourkela",state:"Odisha",postalCode:"769001",country:"India",full:"Bisra Road, Rourkela, Odisha - 769001"},hours:"Monday - Friday: 9:00 AM - 6:00 PM"},legal:{privacyPolicy:"/privacy",termsOfService:"/terms",refundPolicy:"/refund"},support:{email:"<EMAIL>",phone:"+91 8458060663",helpCenter:"/support"},advertising:{email:"<EMAIL>",phone:"+************",page:"/advertise"}}},55192:(e,a,r)=>{"use strict";r.d(a,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>c});var t=r(60687);r(43210);var s=r(96241);function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function n({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function d({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...a})}function l({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...a})}function o({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...a})}function c({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...a})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58887:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63420:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(26373).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},65668:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92200:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]])},93119:(e,a,r)=>{"use strict";r.d(a,{default:()=>O});var t=r(60687),s=r(43210),i=r(82374),n=r(53342),d=r(77882),l=r(32688),o=r(27908),c=r(41550),m=r(48340),u=r(70334),h=r(24934);function x(){let[e,a]=(0,s.useState)(!1),r={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},i={hidden:{opacity:0,scale:.8,y:10},visible:{opacity:1,scale:1,y:0,transition:{duration:.5,type:"spring",stiffness:100}}};return(0,t.jsxs)("section",{className:"relative py-16 md:py-24 px-4 overflow-hidden",children:[(0,t.jsxs)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute top-1/4 left-1/2 -translate-x-1/2 w-1/2 h-1/2 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/5 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute bottom-0 right-0 w-1/3 h-1/3 bg-blue-500/10 dark:bg-blue-500/5 rounded-full blur-3xl"})]}),(0,t.jsx)("div",{className:"container mx-auto max-w-7xl",children:(0,t.jsxs)(d.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},initial:"hidden",animate:"visible",className:"text-center max-w-4xl mx-auto",children:[(0,t.jsxs)(d.P.h1,{variants:r,className:"text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6",children:["Get in ",(0,t.jsxs)("span",{className:"text-[var(--brand-gold)] relative",children:["Touch",e&&(0,t.jsx)(d.P.div,{className:"absolute -bottom-1 left-0 h-1 bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full",initial:{width:0,left:"50%"},animate:{width:"100%",left:0},transition:{duration:1,delay:.5}})]})]}),(0,t.jsx)(d.P.p,{variants:r,className:"text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto",children:"Have questions about Dukancard? Our team is here to help you elevate your digital presence. Reach out to us through any of the channels below."}),(0,t.jsxs)(d.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.8}}},className:"flex flex-wrap justify-center gap-4 md:gap-6 mb-10",children:[(0,t.jsx)(d.P.div,{variants:i,children:(0,t.jsxs)(h.$,{onClick:()=>{window.location.href=`mailto:${l.C.contact.email}`},variant:"outline",size:"lg",className:"bg-white/80 dark:bg-black/50 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group",children:[(0,t.jsx)(c.A,{className:"mr-2 h-5 w-5 text-[var(--brand-gold)] group-hover:scale-110 transition-transform"}),"Email Us"]})}),(0,t.jsx)(d.P.div,{variants:i,children:(0,t.jsxs)(h.$,{onClick:()=>{window.location.href=`tel:${l.C.contact.phone.replace(/\s+/g,"")}`},variant:"outline",size:"lg",className:"bg-white/80 dark:bg-black/50 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group",children:[(0,t.jsx)(m.A,{className:"mr-2 h-5 w-5 text-[var(--brand-gold)] group-hover:scale-110 transition-transform"}),"Call Us"]})})]}),(0,t.jsx)(d.P.div,{variants:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.5,delay:.6}},hover:{scale:1.05,transition:{duration:.2}},tap:{scale:.98,transition:{duration:.1}}},whileHover:"hover",whileTap:"tap",children:(0,t.jsxs)(h.$,{onClick:()=>{window.location.href="/register"},size:"lg",className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-6 py-2 h-auto shadow-md hover:shadow-lg transition-all duration-300",children:["Get Started",(0,t.jsx)(u.A,{className:"ml-2 h-4 w-4"})]})})]})})]})}var p=r(55192),b=r(52581),v=r(14719);let g=(0,r(62688).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var f=r(97992),y=r(25334),j=r(48730),w=r(40228);function k({contactInfo:e,sectionFadeIn:a,itemFadeIn:r}){let[i,n]=(0,s.useState)(null),l=async(e,a)=>{try{await navigator.clipboard.writeText(e),n(a),b.oR.success(`${a} copied to clipboard!`),setTimeout(()=>n(null),2e3)}catch(e){b.oR.error("Failed to copy to clipboard"),console.error("Failed to copy: ",e)}},o={rest:{scale:1,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},hover:{scale:1.02,boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",transition:{duration:.3,ease:"easeOut"}}},u={rest:{scale:1},hover:{scale:1.15,rotate:[0,5,-5,0],transition:{duration:.5,ease:"easeOut"}}};return(0,t.jsxs)(d.P.section,{id:"contact-info-section",variants:a,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},className:"py-16 px-4 container mx-auto max-w-7xl",children:[(0,t.jsxs)(d.P.div,{variants:r(0),className:"text-center mb-12",children:[(0,t.jsxs)("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:["Our ",(0,t.jsx)("span",{className:"text-[var(--brand-gold)]",children:"Contact Details"})]}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Reach out to us through any of these channels. We're here to help you with any questions or concerns."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 items-stretch",children:[(0,t.jsx)(d.P.div,{variants:r(1),whileHover:"hover",initial:"rest",animate:"rest",children:(0,t.jsx)(d.P.div,{variants:o,children:(0,t.jsxs)(p.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]",children:[(0,t.jsxs)("div",{className:"flex items-start mb-4",children:[(0,t.jsx)(d.P.div,{variants:u,className:"bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full",children:(0,t.jsx)(c.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-card-foreground",children:"Email Us"}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Our team usually responds within 24 hours"})]})]}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsx)("p",{className:"text-primary dark:text-[var(--brand-gold)] font-medium mb-4",children:e.email}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(h.$,{onClick:()=>{window.location.href=`mailto:${e.email}`},variant:"outline",size:"sm",className:"flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Send Email"]}),(0,t.jsx)(h.$,{onClick:()=>l(e.email,"Email"),variant:"outline",size:"icon",className:"border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:"Email"===i?(0,t.jsx)(v.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(g,{className:"h-4 w-4"})})]})]})]})})}),(0,t.jsx)(d.P.div,{variants:r(2),whileHover:"hover",initial:"rest",animate:"rest",children:(0,t.jsx)(d.P.div,{variants:o,children:(0,t.jsxs)(p.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]",children:[(0,t.jsxs)("div",{className:"flex items-start mb-4",children:[(0,t.jsx)(d.P.div,{variants:u,className:"bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full",children:(0,t.jsx)(m.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-card-foreground",children:"Call Us"}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Available during business hours"})]})]}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsx)("p",{className:"text-primary dark:text-[var(--brand-gold)] font-medium mb-4",children:e.phone}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(h.$,{onClick:()=>{window.location.href=`tel:${e.phone.replace(/\s+/g,"")}`},variant:"outline",size:"sm",className:"flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Call Now"]}),(0,t.jsx)(h.$,{onClick:()=>l(e.phone.replace(/\s+/g,""),"Phone"),variant:"outline",size:"icon",className:"border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:"Phone"===i?(0,t.jsx)(v.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(g,{className:"h-4 w-4"})})]})]})]})})}),(0,t.jsx)(d.P.div,{variants:r(3),whileHover:"hover",initial:"rest",animate:"rest",children:(0,t.jsx)(d.P.div,{variants:o,children:(0,t.jsxs)(p.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]",children:[(0,t.jsxs)("div",{className:"flex items-start mb-4",children:[(0,t.jsx)(d.P.div,{variants:u,className:"bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full",children:(0,t.jsx)(f.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-card-foreground",children:"Visit Us"}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Our office location"})]})]}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsxs)("address",{className:"not-italic text-primary dark:text-[var(--brand-gold)] font-medium mb-4",children:[e.address.street,(0,t.jsx)("br",{}),e.address.city,", ",e.address.state," - ",e.address.postalCode,(0,t.jsx)("br",{}),e.address.country]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(h.$,{onClick:()=>{window.open("https://maps.app.goo.gl/m6FfJHyYLC53HZiR7","_blank")},variant:"outline",size:"sm",className:"flex-1 border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"View on Map"]}),(0,t.jsx)(h.$,{onClick:()=>l(e.address.full,"Address"),variant:"outline",size:"icon",className:"border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",children:"Address"===i?(0,t.jsx)(v.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(g,{className:"h-4 w-4"})})]})]})]})})}),(0,t.jsx)(d.P.div,{variants:r(4),whileHover:"hover",initial:"rest",animate:"rest",children:(0,t.jsx)(d.P.div,{variants:o,children:(0,t.jsxs)(p.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 h-full flex flex-col justify-between min-h-[280px]",children:[(0,t.jsxs)("div",{className:"flex items-start mb-4",children:[(0,t.jsx)(d.P.div,{variants:u,className:"bg-primary/10 dark:bg-[var(--brand-gold)]/10 p-3 rounded-full",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-primary dark:text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-card-foreground",children:"Business Hours"}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"When we're available"})]})]}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsxs)("ul",{className:"space-y-2 text-primary dark:text-[var(--brand-gold)] font-medium mb-4",children:[(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Monday - Friday:"}),(0,t.jsx)("span",{children:e.hours.split(": ")[1]})]}),(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Saturday:"}),(0,t.jsx)("span",{children:"Closed"})]}),(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Sunday:"}),(0,t.jsx)("span",{children:"Closed"})]})]}),(0,t.jsxs)(h.$,{variant:"outline",size:"sm",className:"w-full border-primary/20 dark:border-[var(--brand-gold)]/20 hover:bg-primary/5 dark:hover:bg-[var(--brand-gold)]/5",onClick:()=>{let e=`https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent("Meeting with Dukancard")}&dates=20240101T${"09:00".replace(":","")}/20240101T${"18:00".replace(":","")}`;window.open(e,"_blank")},children:[(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Schedule Meeting"]})]})]})})})]})]})}var N=r(18265),C=r(92200);function P({address:e,sectionFadeIn:a,itemFadeIn:r}){let i=(0,s.useRef)(null),n=(0,N.W)(i,{once:!0,amount:.3}),[l,o]=(0,s.useState)(!1),c=()=>{window.open("https://maps.app.goo.gl/m6FfJHyYLC53HZiR7","_blank")};return(0,t.jsxs)(d.P.section,{id:"contact-map-section",variants:a,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},className:"py-16 px-4 container mx-auto max-w-7xl",children:[(0,t.jsxs)(d.P.div,{variants:r(0),className:"text-center mb-12",children:[(0,t.jsxs)("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:["Find ",(0,t.jsx)("span",{className:"text-[var(--brand-gold)]",children:"Us"})]}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Visit our office to meet the team and discuss your business needs in person."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)(d.P.div,{variants:r(1),className:"lg:col-span-2",ref:i,children:(0,t.jsx)(p.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 md:p-8 h-full",children:(0,t.jsx)("div",{className:"w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden border border-border dark:border-[var(--brand-gold)]/20 relative",children:(0,t.jsx)("div",{className:"absolute inset-0 bg-muted dark:bg-gradient-to-br dark:from-neutral-800 dark:to-black flex items-center justify-center",children:l&&n&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-[url('/map-grid.svg')] opacity-10 dark:opacity-20"}),(0,t.jsxs)(d.P.div,{initial:"initial",animate:"animate",variants:{initial:{y:-20,opacity:0},animate:{y:[0,-15,0],opacity:1,transition:{y:{duration:2,repeat:1/0,repeatType:"loop",ease:"easeInOut"},opacity:{duration:.5}}}},className:"relative z-10",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.A,{className:"w-16 h-16 text-[var(--brand-gold)]"}),(0,t.jsx)(d.P.div,{className:"absolute -bottom-2 left-1/2 -translate-x-1/2 w-8 h-8 bg-[var(--brand-gold)]/30 rounded-full",initial:{scale:0,opacity:1},animate:{scale:3,opacity:0},transition:{repeat:1/0,duration:2,ease:"easeOut"}})]}),(0,t.jsx)("div",{className:"absolute top-full left-1/2 -translate-x-1/2 mt-2 bg-white dark:bg-black px-3 py-1 rounded-full shadow-md border border-neutral-200 dark:border-neutral-800",children:(0,t.jsxs)("p",{className:"text-sm font-medium whitespace-nowrap",children:[e.city,", ",e.state]})})]}),(0,t.jsx)(d.P.div,{className:"absolute bottom-4 right-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:1,duration:.5},children:(0,t.jsxs)(h.$,{onClick:c,variant:"outline",size:"sm",className:"bg-white/90 dark:bg-black/70 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:scale-110 transition-transform"}),"Open in Google Maps"]})})]})})})})}),(0,t.jsx)(d.P.div,{variants:r(2),className:"lg:col-span-1",children:(0,t.jsxs)(p.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-6 md:p-8 h-full",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-foreground mb-6 flex items-center",children:[(0,t.jsx)(C.A,{className:"mr-2 h-5 w-5 text-[var(--brand-gold)]"}),"Directions"]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"bg-[var(--brand-gold)]/10 p-2 rounded-full",children:(0,t.jsx)(f.A,{className:"h-5 w-5 text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-foreground",children:"Our Address"}),(0,t.jsxs)("address",{className:"not-italic text-muted-foreground mt-1",children:[e.street,(0,t.jsx)("br",{}),e.city,", ",e.state," - ",e.postalCode,(0,t.jsx)("br",{}),e.country]})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"bg-[var(--brand-gold)]/10 p-2 rounded-full",children:(0,t.jsx)(j.A,{className:"h-5 w-5 text-[var(--brand-gold)]"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-foreground",children:"Office Hours"}),(0,t.jsxs)("ul",{className:"text-muted-foreground mt-1 space-y-1",children:[(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Monday - Friday:"}),(0,t.jsx)("span",{children:"9:00 AM - 6:00 PM"})]}),(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Saturday - Sunday:"}),(0,t.jsx)("span",{children:"Closed"})]})]})]})]}),(0,t.jsx)(d.P.div,{variants:{rest:{scale:1},hover:{scale:1.05,transition:{duration:.2}},tap:{scale:.98,transition:{duration:.1}}},whileHover:"hover",whileTap:"tap",className:"mt-6",children:(0,t.jsxs)(h.$,{onClick:c,className:"w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium",children:[(0,t.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Get Directions"]})}),(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-neutral-200 dark:border-neutral-800",children:(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Parking is available in the building's underground parking lot. Please call us when you arrive, and we'll guide you to our office."})})]})]})})]})]})}var A=r(16189);function M({sectionFadeIn:e,itemFadeIn:a}){let r=(0,A.useRouter)(),[i,n]=(0,s.useState)(!1),o={rest:{scale:1},hover:{scale:1.05,transition:{duration:.2}},tap:{scale:.98,transition:{duration:.1}}};return(0,t.jsxs)(d.P.section,{id:"contact-cta-section",variants:e,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},className:"py-16 md:py-24 px-4 relative overflow-hidden",children:[(0,t.jsxs)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute top-1/2 left-1/4 -translate-y-1/2 w-1/2 h-1/2 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/5 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute bottom-0 right-0 w-1/3 h-1/3 bg-blue-500/10 dark:bg-blue-500/5 rounded-full blur-3xl"})]}),(0,t.jsx)("div",{className:"container mx-auto max-w-5xl",children:(0,t.jsxs)("div",{className:"bg-white/80 dark:bg-black/50 backdrop-blur-sm border border-neutral-200 dark:border-neutral-800 rounded-2xl p-8 md:p-12",children:[(0,t.jsxs)(d.P.div,{variants:a(0),className:"text-center mb-8 md:mb-12",children:[(0,t.jsxs)("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:["Ready to ",(0,t.jsx)("span",{className:"text-[var(--brand-gold)]",children:"Get Started"}),"?"]}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Create your digital business card today and start connecting with customers in a whole new way."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6",children:[(0,t.jsx)(d.P.div,{variants:a(1),className:"md:col-span-1",children:(0,t.jsx)(d.P.div,{variants:o,whileHover:"hover",whileTap:"tap",children:(0,t.jsxs)(h.$,{onClick:()=>{r.push("/login")},size:"lg",className:"w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium py-2 h-auto shadow-md hover:shadow-lg transition-all duration-300",children:["Get Started",(0,t.jsx)(u.A,{className:"ml-2 h-4 w-4"})]})})}),(0,t.jsx)(d.P.div,{variants:a(2),className:"md:col-span-1",children:(0,t.jsx)(d.P.div,{variants:o,whileHover:"hover",whileTap:"tap",children:(0,t.jsxs)(h.$,{onClick:()=>{r.push("/pricing")},variant:"outline",size:"lg",className:"w-full border-neutral-300 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group py-2 h-auto",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:translate-x-1 transition-transform"}),"View Pricing"]})})}),(0,t.jsx)(d.P.div,{variants:a(4),className:"md:col-span-1",children:(0,t.jsx)(d.P.div,{variants:o,whileHover:"hover",whileTap:"tap",children:(0,t.jsxs)(h.$,{onClick:()=>window.location.href=`tel:${l.C.contact.phone.replace(/\s+/g,"")}`,variant:"outline",size:"lg",className:"w-full border-neutral-300 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group py-2 h-auto",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4 text-[var(--brand-gold)] group-hover:rotate-12 transition-transform"}),"Call Us"]})})})]}),(0,t.jsx)(d.P.div,{variants:a(5),className:"mt-8 pt-6 border-t border-neutral-200 dark:border-neutral-800 text-center",children:(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Or reach out via email at"," ",(0,t.jsx)("a",{href:`mailto:${l.C.contact.email}`,className:"text-[var(--brand-gold)] hover:underline",children:l.C.contact.email})]})})]})})]})}var $=r(58887),z=r(65668),q=r(27900);function S(){let[e,a]=(0,s.useState)(!1),[r,i]=(0,s.useState)(!1),n=e?(()=>{let e=[],a=[c.A,m.A,f.A,$.A,z.A,q.A];for(let r=0;r<12;r++){let s=a[r%a.length];e.push({id:r,icon:(0,t.jsx)(s,{}),posX:100*Math.random(),posY:100*Math.random(),size:10*Math.random()+15,opacity:.07*Math.random()+.03,duration:20*Math.random()+15,delay:5*Math.random(),moveX:(Math.random()-.5)*30,moveY:(Math.random()-.5)*30,rotate:360*Math.random(),rotateDirection:(Math.random()-.5)*360})}return e})():[];return(0,t.jsxs)("div",{className:"absolute inset-0 w-full h-full overflow-hidden pointer-events-none",children:[(0,t.jsx)(d.P.div,{className:"absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold)]/5 blur-3xl dark:bg-[var(--brand-gold)]/10",initial:{opacity:.5},animate:{opacity:.7},transition:{duration:4,repeat:1/0,repeatType:"reverse"}}),(0,t.jsx)(d.P.div,{className:"absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10",initial:{opacity:.5},animate:{opacity:.7},transition:{duration:5,repeat:1/0,repeatType:"reverse",delay:1}}),(0,t.jsx)(d.P.div,{className:"absolute bottom-0 left-1/3 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10",initial:{opacity:.5},animate:{opacity:.7},transition:{duration:6,repeat:1/0,repeatType:"reverse",delay:2}}),e&&n.map(e=>(0,t.jsx)(d.P.div,{className:"absolute text-[var(--brand-gold)]",style:{left:`${e.posX}%`,top:`${e.posY}%`,opacity:e.opacity,fontSize:`${r?.7*e.size:e.size}px`},initial:{rotate:e.rotate,x:0,y:0},animate:{rotate:e.rotate+e.rotateDirection,x:e.moveX,y:e.moveY},transition:{rotate:{duration:e.duration,repeat:1/0,ease:"linear"},x:{duration:e.duration/2,repeat:1/0,repeatType:"reverse",ease:"easeInOut",delay:e.delay},y:{duration:e.duration/3,repeat:1/0,repeatType:"reverse",ease:"easeInOut",delay:e.delay+2}},children:e.icon},e.id)),(0,t.jsx)("div",{className:"absolute inset-0 bg-[url('/grid-pattern.svg')] bg-repeat opacity-[0.015] dark:opacity-[0.03]"})]})}function O(){let[e,a]=(0,s.useState)(!1),r=(0,s.useRef)(null),{scrollYProgress:c}=(0,i.L)({target:r,offset:["start start","end start"]}),m=(0,n.G)(c,[0,.2,.8,1],[1,.8,.4,.2]),u={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,staggerChildren:.2}}},h=(e=0)=>({hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5,delay:.1*e}}});return(0,t.jsxs)("div",{ref:r,className:"min-h-screen bg-white dark:bg-black text-black dark:text-white overflow-hidden",children:[(0,t.jsx)(d.P.div,{className:"fixed inset-0 z-0 pointer-events-none",style:{opacity:m},children:(0,t.jsx)(S,{})}),(0,t.jsxs)(d.P.div,{initial:{opacity:0},animate:{opacity:+!!e},transition:{duration:.5},className:"w-full pt-8 sm:pt-10 md:pt-12 mt-2 sm:mt-3 md:mt-4",children:[(0,t.jsx)(x,{}),(0,t.jsx)(o.A,{variant:"gold"}),(0,t.jsx)(k,{contactInfo:l.C.contact,sectionFadeIn:u,itemFadeIn:h}),(0,t.jsx)(o.A,{variant:"gold"}),(0,t.jsx)(P,{address:l.C.contact.address,sectionFadeIn:u,itemFadeIn:h}),(0,t.jsx)(o.A,{variant:"blue"}),(0,t.jsx)(M,{sectionFadeIn:u,itemFadeIn:h})]})]})}},94735:e=>{"use strict";e.exports=require("events")},97992:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var a=require("../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[4447,6724,2997,1107,7065,3098,3037,6177],()=>r(50051));module.exports=t})();