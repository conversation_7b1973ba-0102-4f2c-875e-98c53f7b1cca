"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[356],{21433:(e,t,s)=>{s.d(t,{_:()=>c});var a=s(75168),r=s(47186);let l={enterprise:5,pro:4,growth:3,basic:2,free:1};async function i(e){let t=(0,a.U)();try{let{data:s,error:a}=await t.from(r.CG.CUSTOMER_PROFILES).select("*").eq(r.cZ.ID,e).single();if(a)return console.error("Error fetching user profile:",a.message),{data:null,error:a.message};return{data:s,error:null}}catch(e){return console.error("Unexpected error fetching user profile:",e),{data:null,error:"An unexpected error occurred."}}}async function n(e,t){let s=(0,a.U)(),{filter:n="smart",page:c=1,limit:o=10,city_slug:d,state_slug:u,locality_slug:m,pincode:x}=e;try{var h,p,f,g,v,b,j,N;let{data:{user:e}}=await s.auth.getUser(),a=s.from("unified_posts").select("*",{count:"exact"});switch(n){case"smart":if(e){let{data:t}=await s.from("subscriptions").select("business_profile_id").eq(r.cZ.USER_ID,e.id),l=(null==t?void 0:t.map(e=>e.business_profile_id))||[],[n,c]=await Promise.all([i(e.id),s.from(r.CG.BUSINESS_PROFILES).select("".concat(r.cZ.CITY_SLUG,", ").concat(r.cZ.STATE_SLUG,", ").concat(r.cZ.LOCALITY_SLUG,", ").concat(r.cZ.PINCODE)).eq(r.cZ.ID,e.id).single()]),o=n.data||c.data,d=[];l.length>0&&d.push("and(post_source.eq.business,author_id.in.(".concat(l.join(","),"))")),d.push("and(post_source.eq.customer,author_id.eq.".concat(e.id,")")),d.push("and(post_source.eq.business,author_id.eq.".concat(e.id,")")),(null==o?void 0:o.locality_slug)&&d.push("".concat(r.cZ.LOCALITY_SLUG,".eq.").concat(o.locality_slug)),(null==o?void 0:o.pincode)&&d.push("".concat(r.cZ.PINCODE,".eq.").concat(o.pincode)),(null==o?void 0:o.city_slug)&&d.push("".concat(r.cZ.CITY_SLUG,".eq.").concat(o.city_slug)),d.length>0&&(a=a.or(d.join(",")))}break;case"subscribed":if(e){let{data:t}=await s.from("subscriptions").select("business_profile_id").eq("user_id",e.id),r=(null==t?void 0:t.map(e=>e.business_profile_id))||[];if(!(r.length>0))return{success:!0,message:"No subscribed businesses found",data:{items:[],totalCount:0,hasMore:!1,hasJustCreatedPost:!1}};a=a.eq("post_source","business").in("author_id",r)}break;case"locality":if(m)a=a.eq("locality_slug",m);else if(e){let[t,l]=await Promise.all([s.from(r.CG.CUSTOMER_PROFILES).select(r.cZ.LOCALITY_SLUG).eq(r.cZ.ID,e.id).single(),s.from(r.CG.BUSINESS_PROFILES).select(r.cZ.LOCALITY_SLUG).eq(r.cZ.ID,e.id).single()]),i=(null==(h=t.data)?void 0:h.locality_slug)||(null==(p=l.data)?void 0:p.locality_slug);i&&(a=a.eq("locality_slug",i))}break;case"pincode":if(x)a=a.eq("pincode",x);else if(e){let[t,l]=await Promise.all([s.from(r.CG.CUSTOMER_PROFILES).select(r.cZ.PINCODE).eq(r.cZ.ID,e.id).single(),s.from(r.CG.BUSINESS_PROFILES).select(r.cZ.PINCODE).eq(r.cZ.ID,e.id).single()]),i=(null==(f=t.data)?void 0:f.pincode)||(null==(g=l.data)?void 0:g.pincode);i&&(a=a.eq("pincode",i))}break;case"city":if(d)a=a.eq("city_slug",d);else if(e){let[t,l]=await Promise.all([s.from(r.CG.CUSTOMER_PROFILES).select(r.cZ.CITY_SLUG).eq(r.cZ.ID,e.id).single(),s.from(r.CG.BUSINESS_PROFILES).select(r.cZ.CITY_SLUG).eq(r.cZ.ID,e.id).single()]),i=(null==(v=t.data)?void 0:v.city_slug)||(null==(b=l.data)?void 0:b.city_slug);i&&(a=a.eq("city_slug",i))}break;case"state":if(u)a=a.eq("state_slug",u);else if(e){let[t,l]=await Promise.all([s.from(r.CG.CUSTOMER_PROFILES).select(r.cZ.STATE_SLUG).eq(r.cZ.ID,e.id).single(),s.from(r.CG.BUSINESS_PROFILES).select(r.cZ.STATE_SLUG).eq(r.cZ.ID,e.id).single()]),i=(null==(j=t.data)?void 0:j.state_slug)||(null==(N=l.data)?void 0:N.state_slug);i&&(a=a.eq("state_slug",i))}}let _=(c-1)*o,{data:w,error:y,count:S}=await a.order("created_at",{ascending:!1}).range(_,_+o-1);if(y)return console.error("Error fetching unified feed posts:",y),{success:!1,message:"Failed to fetch posts",error:y.message};let E=w?function(e){var t,s,a,r;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{enableDiversity:n=!0,maintainChronologicalFlow:c=!0}=i;if(0===e.length)return[];let o=e.filter(e=>"customer"===e.post_source),d=e.filter(e=>"business"===e.post_source),u=(s=(t=o,0===t.length?[]:t.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())),a=function(e){if(0===e.length)return[];let t=new Map;e.forEach(e=>{t.has(e.author_id)||t.set(e.author_id,[]),t.get(e.author_id).push(e)}),t.forEach((e,s)=>{t.set(s,e.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()))});let s=[],a=[];return t.forEach((e,t)=>{e.length>0&&(s.push(e[0]),e.length>1&&a.push(...e.slice(1)))}),[...s.sort((e,t)=>{let s=e.business_plan||"free",a=t.business_plan||"free",r=l[s]||1,i=l[a]||1;return r!==i?i-r:new Date(t.created_at).getTime()-new Date(e.created_at).getTime()}),...a.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())]}(d),r=c,0===s.length?a:0===a.length?s:r?[...s,...a].sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()):[...a,...s]);return n?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{maxConsecutiveFromSameAuthor:s=1}=t;if(e.length<=1)return e;let a=[],r=[...e],l=null,i=0;for(;r.length>0;){let e=-1;for(let t=0;t<r.length;t++)if(r[t].author_id!==l){e=t;break}if(-1===e&&i<s&&(e=0),-1===e){for(let t=0;t<r.length;t++)if(r[t].author_id!==l){e=t;break}-1===e&&(e=0)}let t=r.splice(e,1)[0];a.push(t),t.author_id===l?i++:(i=1,l=t.author_id)}return a}(u):u}(w,{enableDiversity:!0,maintainChronologicalFlow:!0}):[],k=S||0,A=E.length===o&&_+o<k;if(!t||!t.justCreatedPostId)return{success:!0,message:"Posts fetched successfully",data:{items:E,totalCount:k,hasMore:A,hasJustCreatedPost:!1}};let C=function(e,t){if(!t.justCreatedPostId)return{posts:e,hasJustCreatedPost:!1};let s=e.find(e=>e.id===t.justCreatedPostId);return s?{posts:[s,...e.filter(e=>e.id!==t.justCreatedPostId)],hasJustCreatedPost:!0,justCreatedPost:s}:{posts:e,hasJustCreatedPost:!1}}(E,t);return{success:!0,message:"Posts fetched successfully",data:{items:C.posts,totalCount:k,hasMore:A,hasJustCreatedPost:C.hasJustCreatedPost,justCreatedPost:C.justCreatedPost,creationState:t}}}catch(e){return console.error("Unexpected error in getUnifiedFeedPosts:",e),{success:!1,message:"An unexpected error occurred",error:e instanceof Error?e.message:"Unknown error"}}}async function c(e,t){return await n(e,t)}},22607:(e,t,s)=>{s.d(t,{pD:()=>r,deletePost:()=>i,gg:()=>l});var a=s(34477);let r=(0,a.createServerReference)("4078b27f89338bdea95f74b22fd4d22c4710876eab",a.callServer,void 0,a.findSourceMapURL,"createPost"),l=(0,a.createServerReference)("60fef8195d22c743fbc958b9c6e7229ba7a09ebfb2",a.callServer,void 0,a.findSourceMapURL,"updatePost"),i=(0,a.createServerReference)("4009d2933c186901a1403c78560cd329314f35e005",a.callServer,void 0,a.findSourceMapURL,"deletePost");s(21433)},27737:(e,t,s)=>{s.d(t,{E:()=>l});var a=s(95155),r=s(53999);function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,r.cn)("bg-accent animate-pulse rounded-md",t),...s})}},30400:(e,t,s)=>{s.d(t,{A:()=>et});var a=s(95155),r=s(6874),l=s.n(r),i=s(66766),n=s(8830),c=s(28695),o=s(69663),d=s(51976),u=s(71366),m=s(66516),x=s(19420),h=s(82147);function p(e){let{business:t,hasWhatsApp:s,hasPhone:r,_postId:l,onShare:i}=e;return s||r?(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("button",{className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"Like",disabled:!0,children:(0,a.jsx)(d.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"Comment",disabled:!0,children:(0,a.jsx)(u.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"Share",onClick:()=>{i&&i()},children:(0,a.jsx)(m.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[s&&(0,a.jsx)("button",{onClick:()=>{if(s){var e;let s=null==(e=t.whatsapp_number)?void 0:e.replace(/\D/g,""),a="Hi ".concat(t.business_name,", I saw your post and would like to know more.");window.open("https://wa.me/".concat(s,"?text=").concat(encodeURIComponent(a)),"_blank")}},className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"WhatsApp",children:(0,a.jsx)(h.A,{className:"h-5 w-5"})}),r&&(0,a.jsx)("button",{onClick:()=>{r&&window.open("tel:".concat(t.phone),"_self")},className:"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors",title:"Call Now",children:(0,a.jsx)(x.A,{className:"h-5 w-5"})})]})]}):null}var f=s(61141),g=s(34477);let v=(0,g.createServerReference)("404b1d9bf7310a5b14e59bf18e153b291565613a40",g.callServer,void 0,g.findSourceMapURL,"fetchProductsByIds");var b=s(51154),j=s(71007),N=s(44020),_=s(44940),w=s(62525),y=s(4516),S=s(37108),E=s(12115),k=s(53999),A=s(75168);async function C(e,t,s,a){if(!a)return null;let r=(0,A.U)();try{let l=r.from("pincodes").select("OfficeName, DivisionName, StateName, Pincode").eq("Pincode",a);t&&(l=l.eq("city_slug",t)),s&&(l=l.eq("state_slug",s)),e&&(l=l.eq("locality_slug",e));let{data:i,error:n}=await l.limit(1);if(n)return console.error("Error fetching address data:",n),null;if(!i||0===i.length){let{data:e,error:t}=await r.from("pincodes").select("OfficeName, DivisionName, StateName, Pincode").eq("Pincode",a).limit(1);if(t||!e||0===e.length)return null;let s=e[0];return{locality:s.OfficeName||"",city:s.DivisionName||"",state:s.StateName||"",pincode:s.Pincode||""}}let c=i[0];return{locality:c.OfficeName||"",city:c.DivisionName||"",state:c.StateName||"",pincode:c.Pincode||""}}catch(e){return console.error("Error in fetchPostAddress:",e),null}}var P=s(97168),I=s(99474),L=s(54416),R=s(5196),T=s(56671),U=s(22607),O=s(48021),D=s(47924),G=s(10081),B=s(75143),q=s(50402),M=s(78266),Z=s(44895),F=s(60823),z=s(65617),Y=s(60924);function W(e){let{product:t,onRemove:s,formatPrice:r}=e,{attributes:l,listeners:n,setNodeRef:c,transform:o,transition:d,isDragging:u}=(0,q.gl)({id:t.id}),m={transform:M.Ks.Transform.toString(o),transition:d,opacity:u?.5:1};return(0,a.jsxs)("div",{ref:c,style:m,className:"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/50 rounded-lg border min-h-[60px] sm:min-h-[68px]",children:[(0,a.jsx)("div",{...l,...n,className:"cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground shrink-0",children:(0,a.jsx)(O.A,{className:"h-4 w-4"})}),(0,a.jsx)("div",{className:"relative h-8 w-8 sm:h-10 sm:w-10 shrink-0 rounded-md overflow-hidden bg-background",children:t.image_url?(0,a.jsx)(i.default,{src:t.image_url,alt:t.name,fill:!0,className:"object-cover",sizes:"(max-width: 640px) 32px, 40px"}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,a.jsx)(S.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0 pr-1 sm:pr-2",children:[(0,a.jsx)("div",{className:"font-medium text-xs sm:text-sm leading-tight mb-1",children:(0,a.jsx)("span",{className:"line-clamp-1 break-words",children:t.name})}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:t.discounted_price?(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-wrap",children:[(0,a.jsx)("span",{className:"text-primary font-medium",children:r(t.discounted_price)}),(0,a.jsx)("span",{className:"line-through text-xs",children:r(t.base_price)})]}):(0,a.jsx)("span",{className:"font-medium",children:r(t.base_price)})})]}),(0,a.jsx)(P.$,{variant:"ghost",size:"icon",className:"h-6 w-6 sm:h-8 sm:w-8 shrink-0 hover:bg-destructive/10 hover:text-destructive",onClick:()=>s(t.id),children:(0,a.jsx)(L.A,{className:"h-3 w-3 sm:h-4 sm:w-4"})})]})}function V(e){let{selectedProductIds:t,onProductsChange:s}=e,[r,l]=(0,E.useState)(!1),[n,c]=(0,E.useState)(""),[o,d]=(0,E.useState)(!1),[u,m]=(0,E.useState)([]),[x,h]=(0,E.useState)([]),[p,f]=(0,E.useState)(!1),g=(0,E.useRef)(null),v=(0,B.FR)((0,B.MS)(B.AN),(0,B.MS)(B.uN,{coordinateGetter:q.JR})),j=(0,E.useCallback)(async()=>{if(0===t.length)return void h([]);d(!0);try{let e=await (0,z.E)(t);if(e.success&&e.data){let s=t.map(t=>{var s;return null==(s=e.data)?void 0:s.find(e=>e.id===t)}).filter(Boolean);h(s)}else console.error("Error loading selected products:",e.error),h([])}catch(e){console.error("Error loading selected products:",e),h([])}finally{d(!1)}},[t]);(0,E.useEffect)(()=>{t.length>0&&j()},[t,j]);let N=async e=>{d(!0),f(!1);try{let t=await (0,Y.H)(e);t.success&&t.data?m(t.data):(console.error("Error searching products:",t.error),m([]))}catch(e){console.error("Error searching products:",e),m([])}finally{d(!1),f(!0)}};(0,E.useEffect)(()=>()=>{g.current&&clearTimeout(g.current)},[]);let _=e=>{let a;if(t.includes(e.id))a=t.filter(t=>t!==e.id),h(t=>t.filter(t=>t.id!==e.id));else{if(t.length>=5)return;a=[...t,e.id],h(t=>[...t,e])}s(a)},w=e=>{let a=t.filter(t=>t!==e);h(t=>t.filter(t=>t.id!==e)),s(a)},y=e=>null===e?"N/A":"₹".concat(e.toLocaleString("en-IN"));return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(F.AM,{open:r,onOpenChange:e=>{l(e),e||(c(""),m([]),f(!1))},children:[(0,a.jsx)(F.Wv,{asChild:!0,children:(0,a.jsxs)(P.$,{variant:"outline",role:"combobox","aria-expanded":r,className:"w-full justify-between h-auto min-h-[40px] px-3 py-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-left text-muted-foreground",children:"Search and add products..."})]}),(0,a.jsx)(G.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,a.jsx)(F.hl,{className:"p-0",align:"start",sideOffset:4,style:{width:"var(--radix-popover-trigger-width)"},children:(0,a.jsxs)(Z.uB,{children:[(0,a.jsx)(Z.G7,{placeholder:"Search your products...",value:n,onValueChange:e=>{if(c(e),g.current&&clearTimeout(g.current),e.length<2){m([]),f(!1);return}g.current=setTimeout(()=>{N(e)},300)},className:"h-9 border-0 focus:ring-0 focus:ring-offset-0"}),(0,a.jsxs)(Z.oI,{className:"max-h-[300px]",children:[o&&(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-6 w-6 animate-spin text-primary"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Searching products..."})]})}),!o&&0===u.length&&(0,a.jsx)(Z.xL,{children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,a.jsx)(S.A,{className:"h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:n.length<2?"Type at least 2 characters to search":p?"No products found":""}),n.length>=2&&p&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground mt-1",children:"Try a different search term"})]})}),!o&&u.length>0&&(0,a.jsx)(Z.L$,{children:u.map(e=>(0,a.jsxs)(Z.h_,{value:e.slug||e.id,onSelect:()=>{(t.length<5||t.includes(e.id))&&(_(e),l(!1),c(""),m([]),f(!1))},disabled:t.length>=5&&!t.includes(e.id),className:(0,k.cn)("flex items-center gap-3 p-3 cursor-pointer",t.length>=5&&!t.includes(e.id)?"opacity-50 cursor-not-allowed":""),children:[(0,a.jsx)("div",{className:"relative h-10 w-10 shrink-0 rounded-md overflow-hidden bg-muted",children:e.image_url?(0,a.jsx)(i.default,{src:e.image_url,alt:e.name,fill:!0,className:"object-cover",sizes:"40px"}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,a.jsx)(S.A,{className:"h-5 w-5 text-muted-foreground"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0 pr-2",children:[(0,a.jsx)("div",{className:"font-medium text-sm truncate mb-1",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.discounted_price?(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-wrap",children:[(0,a.jsx)("span",{className:"text-primary font-medium",children:y(e.discounted_price)}),(0,a.jsx)("span",{className:"line-through text-xs",children:y(e.base_price)})]}):(0,a.jsx)("span",{className:"font-medium",children:y(e.base_price)})})]}),(0,a.jsx)(R.A,{className:(0,k.cn)("ml-auto h-4 w-4",t.includes(e.id)?"opacity-100 text-primary":"opacity-0")})]},e.id))})]})]})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[o&&t.length>0&&0===x.length&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground",children:[(0,a.jsx)(S.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Loading Selected Products..."})]}),(0,a.jsx)("div",{className:"flex justify-center py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-6 w-6 animate-spin text-[var(--brand-gold)]"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Fetching product details..."})]})})]}),x.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground",children:[(0,a.jsx)(S.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Selected Products"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground font-normal",children:"(Drag to reorder)"})]}),(0,a.jsx)(B.Mp,{sensors:v,collisionDetection:B.fp,onDragEnd:e=>{let{active:t,over:a}=e;if(t.id!==(null==a?void 0:a.id)){let e=x.findIndex(e=>e.id===t.id),r=x.findIndex(e=>e.id===(null==a?void 0:a.id));if(-1!==e&&-1!==r){let t=(0,q.be)(x,e,r);h(t),s(t.map(e=>e.id))}}},children:(0,a.jsx)(q.gB,{items:x.map(e=>e.id),strategy:q._G,children:(0,a.jsx)("div",{className:"grid gap-2",children:x.map(e=>(0,a.jsx)(W,{product:e,onRemove:w,formatPrice:y},e.id))})})})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:0===t.length?"No products selected":"".concat(t.length," product").concat(1!==t.length?"s":""," selected")}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsxs)("span",{className:(0,k.cn)("font-medium",t.length>=5?"text-destructive":"text-muted-foreground"),children:[t.length,"/5"]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"max"})]})]}),t.length>=5&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded-md",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 shrink-0"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Maximum limit of 5 products reached"})]})]})]})}var H=s(60760);function J(e){let{postId:t,initialContent:s,initialProductIds:r,initialImageUrl:l,onSave:i,onCancel:n,className:o=""}=e,[d,u]=(0,E.useState)(s),[m,x]=(0,E.useState)(r),[h,p]=(0,E.useState)(!1),[f,g]=(0,E.useState)(s.length),v=(0,E.useRef)(null),j=f>2e3,N=d.trim()!==s.trim(),_=JSON.stringify(m.sort())!==JSON.stringify(r.sort()),w=N||_;(0,E.useEffect)(()=>{v.current&&(v.current.focus(),v.current.setSelectionRange(d.length,d.length))},[d.length]);let y=e=>{u(e),g(e.length)},k=async()=>{if(!w){null==n||n();return}if(j)return void T.oR.error("Content too long",{description:"Please reduce content to ".concat(2e3," characters or less.")});if(0===d.trim().length&&0===m.length)return void T.oR.error("Content or products required",{description:"Post must have either content or linked products."});p(!0);try{let e=await (0,U.gg)(t,{content:d.trim(),product_ids:m,image_url:l,mentioned_business_ids:[]});e.success?(T.oR.success("Post updated successfully"),null==i||i(d.trim(),m)):T.oR.error("Failed to update post",{description:e.error||"Please try again."})}catch(e){console.error("Error updating post:",e),T.oR.error("An unexpected error occurred")}finally{p(!1)}},A=()=>{u(s),g(s.length),x(r),null==n||n()};return(0,a.jsxs)("div",{className:"space-y-4 ".concat(o),children:[(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.T,{ref:v,value:d,onChange:e=>y(e.target.value),onKeyDown:e=>{"Escape"===e.key?A():"Enter"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),k())},placeholder:"What's on your mind?",className:"min-h-[100px] resize-none ".concat(j?"border-destructive focus:border-destructive":""),disabled:h}),(0,a.jsxs)("div",{className:"absolute bottom-2 right-2 text-xs ".concat(j?"text-destructive":"text-muted-foreground"),children:[f,"/",2e3]})]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-muted-foreground",children:["Linked Products (",m.length,")"]})]}),(0,a.jsx)(V,{selectedProductIds:m,onProductsChange:x})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-2",children:[(0,a.jsxs)(P.$,{variant:"ghost",size:"sm",onClick:A,disabled:h,className:"text-muted-foreground hover:text-foreground",children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-1"}),"Cancel"]}),(0,a.jsx)(c.P.div,{whileHover:{scale:w&&!h?1.02:1},whileTap:{scale:w&&!h?.98:1},children:(0,a.jsx)(P.$,{size:"sm",onClick:k,disabled:!w||j||h,className:"relative overflow-hidden",style:{background:w&&!h?"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)":void 0,boxShadow:w&&!h?"0 4px 20px rgba(59, 130, 246, 0.3)":"0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)"},children:(0,a.jsx)(H.N,{mode:"wait",children:h?(0,a.jsxs)(c.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-1 animate-spin"}),"Saving..."]},"saving"):(0,a.jsxs)(c.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 mr-1"}),"Save"]},"save")})})})]})]})}var K=s(99840);function $(e){let{isOpen:t,onOpenChange:s,postId:r,postContent:l,onDeleteSuccess:i}=e,[n,o]=(0,E.useState)(!1),d=async()=>{o(!0);try{let e=await (0,U.deletePost)(r);e.success?(T.oR.success("Post deleted successfully",{description:"Your post and associated media have been removed."}),null==i||i(),s(!1)):T.oR.error("Failed to delete post",{description:e.error||"Please try again."})}catch(e){console.error("Error deleting post:",e),T.oR.error("An unexpected error occurred",{description:"Please try again later."})}finally{o(!1)}};return(0,a.jsx)(K.lG,{open:t,onOpenChange:s,children:(0,a.jsxs)(K.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(K.c7,{className:"text-center pb-2",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20",children:(0,a.jsx)(c.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},children:(0,a.jsx)(w.A,{className:"h-8 w-8 text-red-500"})})}),(0,a.jsx)(K.L3,{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Delete this post?"}),(0,a.jsx)(K.rr,{className:"text-gray-500 dark:text-gray-400 mt-2",children:"This action cannot be undone."})]}),(0,a.jsxs)(K.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 pt-4",children:[(0,a.jsx)(P.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:n,className:"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200",children:"Cancel"}),(0,a.jsx)(c.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1",children:(0,a.jsx)(P.$,{type:"button",onClick:d,disabled:n,className:"\n                w-full relative overflow-hidden\n                bg-gradient-to-r from-red-500 to-red-600\n                hover:from-red-600 hover:to-red-700\n                text-white font-medium\n                shadow-lg hover:shadow-xl\n                transition-all duration-300\n                before:absolute before:inset-0\n                before:bg-gradient-to-r before:from-red-400 before:to-red-500\n                before:opacity-0 hover:before:opacity-20\n                before:transition-opacity before:duration-300\n                ".concat(n?"cursor-not-allowed opacity-80":"","\n              "),style:{boxShadow:n?"0 4px 20px rgba(239, 68, 68, 0.3)":"0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)"},children:(0,a.jsx)(H.N,{mode:"wait",children:n?(0,a.jsxs)(c.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Deleting..."]},"deleting"):(0,a.jsxs)(c.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Delete Post"]},"delete")})})})]})]})})}var Q=s(67133);function X(e){return"/post/".concat(e)}function ee(e){let{post:t,index:s=0,onPostUpdate:r,onPostDelete:d,onProductsUpdate:u,showActualAspectRatio:x=!1,disablePostClick:h=!1,enableImageFullscreen:g=!1}=e,[I,L]=(0,E.useState)(!0),[R,U]=(0,E.useState)(!1),[O,D]=(0,E.useState)(null),[G,B]=(0,E.useState)(!0),[q,M]=(0,E.useState)(!1),[Z,F]=(0,E.useState)(!1),[z,Y]=(0,E.useState)(t.content),[W,V]=(0,E.useState)(t.product_ids||[]),[H,K]=(0,E.useState)([]),[ee,et]=(0,E.useState)(!1),[es,ea]=(0,E.useState)(!1),er=(0,E.useRef)(null),[el,ei]=(0,E.useState)(!1),[en,ec]=(0,E.useState)(0),[eo,ed]=(0,E.useState)(0),[eu,em]=(0,E.useState)(0),{isOwner:ex,isLoading:eh}=function(e){let{postBusinessId:t}=e,[s,a]=(0,E.useState)(!1),[r,l]=(0,E.useState)(!0),[i,n]=(0,E.useState)(null);return(0,E.useEffect)(()=>{(async()=>{try{let e=(0,A.U)(),{data:{user:s},error:r}=await e.auth.getUser();if(r||!s){a(!1),n(null),l(!1);return}n(s.id),t&&s.id===t?a(!0):a(!1)}catch(e){console.error("Error checking post ownership:",e),a(!1),n(null)}finally{l(!1)}})()},[t]),{isOwner:s,isLoading:r,currentUserId:i}}({postBusinessId:t.business_id}),ep=t.business_profiles,ef=async()=>{try{var e;let s=(e=t.id,"".concat("http://localhost:3000","/post/").concat(e));if(navigator.share)return void await navigator.share({title:"Check out this post on Dukancard",url:s});await navigator.clipboard.writeText(s),T.oR.success("Post link copied to clipboard!")}catch(e){console.error("Error sharing post:",e),T.oR.error("Failed to share post")}};(0,E.useEffect)(()=>{if(0===W.length){K([]),et(!1);return}(async()=>{et(!0);try{let e=await v(W);if(!e.success){console.error("Error fetching products:",e.error),K([]);return}let t=W.map(t=>{var s;return null==(s=e.data)?void 0:s.find(e=>e.id===t)}).filter(Boolean);K(t)}catch(e){console.error("Error fetching products:",e),K([])}finally{et(!1)}})()},[W]),(0,E.useEffect)(()=>{let e=er.current;if(!e)return;let t=t=>{e.scrollWidth<=e.clientWidth||(ei(!0),ec(t.pageX-e.offsetLeft),ed(e.scrollLeft),em(0),e.style.cursor="grabbing",t.preventDefault())},s=t=>{if(!el||e.scrollWidth<=e.clientWidth)return;t.preventDefault();let s=t.pageX-e.offsetLeft,a=(s-en)*2;em(Math.abs(s-en)),e.scrollLeft=eo-a},a=()=>{ei(!1),e.style.cursor=e.scrollWidth>e.clientWidth?"grab":"default"},r=()=>{ei(!1),e.style.cursor=e.scrollWidth>e.clientWidth?"grab":"default"};return e.style.cursor=e.scrollWidth>e.clientWidth?"grab":"default",e.addEventListener("mousedown",t),document.addEventListener("mousemove",s),document.addEventListener("mouseup",a),e.addEventListener("mouseleave",r),()=>{e.removeEventListener("mousedown",t),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",a),e.removeEventListener("mouseleave",r)}},[H,el,en,eo,eu]);let eg=(e,t)=>{if(eu>5)return void e.preventDefault();window.open(t,"_blank","noopener,noreferrer")};if((0,E.useEffect)(()=>{ep&&(async()=>{B(!0);try{let e=await C(t.locality_slug,t.city_slug,t.state_slug,t.pincode);D(e)}catch(e){console.error("Error fetching address:",e),D(null)}finally{B(!1)}})()},[ep,t.locality_slug,t.city_slug,t.state_slug,t.pincode]),!ep)return null;let ev=(0,n.m)(new Date(t.created_at),{addSuffix:!0});return(0,a.jsxs)(c.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4,delay:.1*s,ease:"easeOut"}}},initial:"hidden",animate:"visible",className:(0,k.cn)("bg-white dark:bg-black","overflow-hidden mb-4 md:mb-6","md:rounded-xl md:border md:border-neutral-200 md:dark:border-neutral-800 md:shadow-sm md:hover:shadow-md md:transition-all md:duration-300"),children:[(0,a.jsxs)("div",{className:"p-4 pb-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[ep.business_slug?(0,a.jsxs)(l(),{href:"/".concat(ep.business_slug),target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-3 flex-1 min-w-0 group",children:[(0,a.jsxs)(o.eu,{className:"h-12 w-12 border-2 border-[var(--brand-gold)]/30 transition-transform group-hover:scale-105",children:[(0,a.jsx)(o.BK,{src:ep.logo_url||"",alt:ep.business_name||"Business",className:"object-cover"}),(0,a.jsx)(o.q5,{className:"bg-muted text-foreground border border-[var(--brand-gold)]/30",children:(0,a.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate group-hover:text-[var(--brand-gold)] transition-colors",children:ep.business_name}),ep.business_slug&&(0,a.jsxs)("div",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:["@",ep.business_slug]}),(0,a.jsx)("div",{className:"text-xs text-neutral-400 dark:text-neutral-500 mt-1",children:ev})]})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[(0,a.jsxs)(o.eu,{className:"h-12 w-12 border-2 border-[var(--brand-gold)]/30",children:[(0,a.jsx)(o.BK,{src:ep.logo_url||"",alt:ep.business_name||"Customer",className:"object-cover"}),(0,a.jsx)(o.q5,{className:"bg-muted text-foreground border border-[var(--brand-gold)]/30",children:(0,a.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate",children:ep.business_name}),(0,a.jsx)("div",{className:"text-xs text-neutral-400 dark:text-neutral-500 mt-1",children:ev})]})]}),(0,a.jsxs)(Q.rI,{children:[(0,a.jsx)(Q.ty,{asChild:!0,children:(0,a.jsxs)(P.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-full",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-neutral-500 dark:text-neutral-400"}),(0,a.jsx)("span",{className:"sr-only",children:"Open post menu"})]})}),(0,a.jsxs)(Q.SQ,{align:"end",className:"w-48",children:[(0,a.jsxs)(Q._2,{onClick:ef,className:"cursor-pointer",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Share post"]}),ex&&!eh&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Q.mB,{}),(0,a.jsxs)(Q._2,{onClick:()=>M(!0),className:"cursor-pointer",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Edit post"]}),(0,a.jsx)(Q.mB,{}),(0,a.jsxs)(Q._2,{onClick:()=>F(!0),className:"text-destructive focus:text-destructive cursor-pointer",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Delete post"]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-neutral-500 dark:text-neutral-400",children:[(0,a.jsx)(y.A,{className:"h-3.5 w-3.5 mr-1 flex-shrink-0"}),(0,a.jsx)("span",{children:(()=>{if(G)return"Loading address...";if(O){let e=[];return O.locality&&e.push(O.locality),O.city&&e.push(O.city),O.state&&e.push(O.state),O.pincode&&e.push(O.pincode),e.join(", ")}return function(e,t,s,a){let r=[];return e&&r.push(e.replace(/-/g," ")),t&&r.push(t.replace(/-/g," ")),s&&r.push(s.replace(/-/g," ")),a&&r.push(a),r.join(", ")}(t.locality_slug,t.city_slug,t.state_slug,t.pincode)})()})]})]}),(0,a.jsx)("div",{className:"px-4 pb-3",children:q?(0,a.jsx)(J,{postId:t.id,initialContent:z,initialProductIds:W,initialImageUrl:t.image_url,onSave:(e,s)=>{M(!1),Y(e),V(s),null==r||r(t.id,e),null==u||u(t.id,s)},onCancel:()=>{M(!1)}}):h?(0,a.jsx)("p",{className:"text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line",children:z}):(0,a.jsx)(l(),{href:X(t.id),className:"block cursor-pointer",children:(0,a.jsx)("p",{className:"text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line",children:z})})}),t.image_url&&(h?(0,a.jsx)("div",{className:(0,k.cn)("relative w-full transition-opacity duration-200",g?"cursor-pointer hover:opacity-95":""),onClick:g?()=>ea(!0):void 0,children:x?(0,a.jsxs)("div",{className:"relative w-full bg-neutral-100 dark:bg-neutral-800",children:[(0,a.jsx)(i.default,{src:t.image_url,alt:"Post image",width:800,height:600,className:(0,k.cn)("w-full h-auto object-contain transition-all duration-300",I&&"blur-sm scale-105",R&&"hidden"),onLoad:()=>L(!1),onError:()=>{U(!0),L(!1)},sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:s<3}),I&&(0,a.jsx)("div",{className:"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse"})]}):(0,a.jsxs)("div",{className:"relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800",children:[(0,a.jsx)(i.default,{src:t.image_url,alt:"Post image",fill:!0,className:(0,k.cn)("object-cover transition-all duration-300",I&&"blur-sm scale-105",R&&"hidden"),onLoad:()=>L(!1),onError:()=>{U(!0),L(!1)},sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:s<3}),I&&(0,a.jsx)("div",{className:"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse"})]})}):(0,a.jsx)(l(),{href:X(t.id),className:"block",children:(0,a.jsx)("div",{className:"relative w-full cursor-pointer hover:opacity-95 transition-opacity duration-200",children:x?(0,a.jsxs)("div",{className:"relative w-full bg-neutral-100 dark:bg-neutral-800",children:[(0,a.jsx)(i.default,{src:t.image_url,alt:"Post image",width:800,height:600,className:(0,k.cn)("w-full h-auto object-contain transition-all duration-300",I&&"blur-sm scale-105",R&&"hidden"),onLoad:()=>L(!1),onError:()=>{U(!0),L(!1)},sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:s<3}),I&&(0,a.jsx)("div",{className:"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse"})]}):(0,a.jsxs)("div",{className:"relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800",children:[(0,a.jsx)(i.default,{src:t.image_url,alt:"Post image",fill:!0,className:(0,k.cn)("object-cover transition-all duration-300",I&&"blur-sm scale-105",R&&"hidden"),onLoad:()=>L(!1),onError:()=>{U(!0),L(!1)},sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:s<3}),I&&(0,a.jsx)("div",{className:"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse"})]})})})),W.length>0&&!q&&(0,a.jsx)("div",{className:"px-4 pt-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-1.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-lg",children:(0,a.jsx)(S.A,{className:"h-4 w-4 text-[var(--brand-gold)]"})}),(0,a.jsxs)("h4",{className:"text-sm font-semibold text-neutral-900 dark:text-neutral-100",children:["Featured Products (",H.length,")"]})]}),ee?(0,a.jsx)("div",{className:"flex justify-center py-8",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-6 w-6 animate-spin text-[var(--brand-gold)]"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Loading products..."})]})}):(0,a.jsx)(c.P.div,{ref:er,className:"flex gap-3 overflow-x-auto scrollbar-hide pb-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},style:{scrollbarWidth:"none",msOverflowStyle:"none"},children:H.map(e=>(0,a.jsx)(c.P.div,{className:"flex-shrink-0 w-40",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},children:(0,a.jsx)("div",{className:"block h-full cursor-pointer",onClick:t=>{ep.business_slug&&eg(t,"/".concat(ep.business_slug,"/product/").concat(e.slug||e.id))},children:(0,a.jsx)(f.A,{product:{...e,description:void 0,product_type:"physical",base_price:e.base_price||0,slug:e.slug||void 0,is_available:!0,images:e.image_url?[e.image_url]:void 0,featured_image_index:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},isLink:!1})})},e.id))})]})}),(0,a.jsx)("div",{className:"p-4 pt-3",children:(0,a.jsx)(p,{business:ep,hasWhatsApp:!!(ep.whatsapp_number&&""!==ep.whatsapp_number.trim()),hasPhone:!!(ep.phone&&""!==ep.phone.trim()),_postId:t.id,onShare:ef})}),(0,a.jsx)($,{isOpen:Z,onOpenChange:F,postId:t.id,postContent:z,onDeleteSuccess:()=>{F(!1),null==d||d(t.id)}}),g&&es&&t.image_url&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4",onClick:()=>ea(!1),children:(0,a.jsxs)("div",{className:"relative max-w-full max-h-full",children:[(0,a.jsx)("button",{onClick:()=>ea(!1),className:"absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsx)(i.default,{src:t.image_url,alt:"Post image fullscreen",width:1200,height:800,className:"max-w-full max-h-full object-contain",onClick:e=>e.stopPropagation()})]})})]})}function et(e){let{post:t,index:s=0,onPostUpdate:r,onPostDelete:l,onProductsUpdate:i,showActualAspectRatio:n=!1,disablePostClick:c=!1,enableImageFullscreen:o=!1}=e,d={id:t.id,business_id:t.author_id,content:t.content,image_url:t.image_url,created_at:t.created_at,updated_at:t.updated_at,city_slug:t.city_slug,state_slug:t.state_slug,locality_slug:t.locality_slug,pincode:t.pincode,product_ids:t.product_ids,mentioned_business_ids:t.mentioned_business_ids,business_profiles:{id:t.author_id,business_name:t.author_name||("customer"===t.post_source?"Customer":"Business"),logo_url:t.author_avatar,business_slug:t.business_slug,phone:t.phone,whatsapp_number:t.whatsapp_number,city:null,state:null}};return(0,a.jsx)(ee,{post:d,index:s,onPostUpdate:r,onPostDelete:l,onProductsUpdate:i,showActualAspectRatio:n,disablePostClick:c,enableImageFullscreen:o})}},44895:(e,t,s)=>{s.d(t,{G7:()=>c,L$:()=>u,h_:()=>m,oI:()=>o,uB:()=>n,xL:()=>d});var a=s(95155);s(12115);var r=s(77740),l=s(47924),i=s(53999);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.uB,{"data-slot":"command",className:(0,i.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,a.jsx)(l.A,{className:"size-4 shrink-0 opacity-50"}),(0,a.jsx)(r.uB.Input,{"data-slot":"command-input",className:(0,i.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",t),...s})]})}function o(e){let{className:t,...s}=e;return(0,a.jsx)(r.uB.List,{"data-slot":"command-list",className:(0,i.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",t),...s})}function d(e){let{...t}=e;return(0,a.jsx)(r.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...t})}function u(e){let{className:t,...s}=e;return(0,a.jsx)(r.uB.Group,{"data-slot":"command-group",className:(0,i.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",t),...s})}function m(e){let{className:t,...s}=e;return(0,a.jsx)(r.uB.Item,{"data-slot":"command-item",className:(0,i.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}s(99840)},47186:(e,t,s)=>{s.d(t,{CG:()=>a,SC:()=>r,cZ:()=>l});let a={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},r={BUSINESS:"business",CUSTOMERS:"customers"},l={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},58685:(e,t,s)=>{s.d(t,{default:()=>n});var a=s(95155),r=s(28695),l=s(27737),i=s(53999);function n(e){let{index:t=0,showImage:s=!0,showProducts:n=!1}=e;return(0,a.jsxs)(r.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4,delay:.1*t,ease:"easeOut"}}},initial:"hidden",animate:"visible",className:(0,i.cn)("bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800","shadow-sm overflow-hidden mb-4 md:mb-6"),children:[(0,a.jsx)("div",{className:"p-4 pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 flex-1",children:[(0,a.jsx)(l.E,{className:"h-12 w-12 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)(l.E,{className:"h-4 w-32"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.E,{className:"h-3 w-24"}),(0,a.jsx)(l.E,{className:"h-3 w-1 rounded-full"}),(0,a.jsx)(l.E,{className:"h-3 w-16"})]})]})]}),(0,a.jsx)(l.E,{className:"h-8 w-8 rounded-full"})]})}),(0,a.jsxs)("div",{className:"px-4 pb-3 space-y-2",children:[(0,a.jsx)(l.E,{className:"h-4 w-full"}),(0,a.jsx)(l.E,{className:"h-4 w-3/4"}),(0,a.jsx)(l.E,{className:"h-4 w-1/2"})]}),s&&(0,a.jsx)("div",{className:"relative w-full",children:(0,a.jsx)(l.E,{className:"w-full aspect-[4/3]"})}),n&&(0,a.jsxs)("div",{className:"px-4 pt-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.E,{className:"h-6 w-6 rounded-lg"}),(0,a.jsx)(l.E,{className:"h-4 w-32"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3",children:Array.from({length:2}).map((e,t)=>(0,a.jsxs)("div",{className:"bg-neutral-50 dark:bg-neutral-900 rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden",children:[(0,a.jsx)(l.E,{className:"w-full aspect-square"}),(0,a.jsxs)("div",{className:"p-3 space-y-2",children:[(0,a.jsx)(l.E,{className:"h-4 w-full"}),(0,a.jsx)(l.E,{className:"h-4 w-16"})]})]},t))})]}),(0,a.jsx)("div",{className:"p-4 pt-3",children:(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(l.E,{className:"h-10 flex-1"}),(0,a.jsx)(l.E,{className:"h-10 flex-1"})]})})]})}},60823:(e,t,s)=>{s.d(t,{AM:()=>i,Wv:()=>n,hl:()=>c});var a=s(95155);s(12115);var r=s(98176),l=s(53999);function i(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"popover",...t})}function n(e){let{...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"popover-trigger",...t})}function c(e){let{className:t,align:s="center",sideOffset:i=4,...n}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{"data-slot":"popover-content",align:s,sideOffset:i,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...n})})}},60924:(e,t,s)=>{s.d(t,{H:()=>r});var a=s(34477);let r=(0,a.createServerReference)("4034b523293d3ffd37df154f2b315894750110b2c1",a.callServer,void 0,a.findSourceMapURL,"searchBusinessProducts")},61141:(e,t,s)=>{s.d(t,{A:()=>p});var a=s(95155),r=s(12115),l=s(66766),i=s(6874),n=s.n(i),c=s(27737),o=s(28695),d=s(60760),u=s(53999);let m=e=>null==e?null:e.toLocaleString("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}),x={hidden:{opacity:0,y:10},show:{opacity:1,y:0}},h={initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1,transition:{duration:.5,type:"spring",stiffness:400,damping:10}},hover:{scale:1.05,rotate:-2,transition:{type:"spring",stiffness:500}}};function p(e){var t;let{product:s,isLink:i=!0}=e,[p,f]=(0,r.useState)(!1),g=m(s.base_price),v=m(s.discounted_price),b=g,j=null,N=0,_="number"==typeof s.discounted_price&&s.discounted_price>0,w="number"==typeof s.base_price&&s.base_price>0;_&&w&&s.discounted_price<s.base_price?(b=v,j=g,N=Math.round((s.base_price-s.discounted_price)/s.base_price*100)):(b=g,j=null,N=0),b||(b="Price unavailable");let y=N>0,[S,E]=(0,r.useState)(!1),k=!s.is_available,A=(0,a.jsx)(o.P.div,{variants:x,initial:"hidden",animate:"show",className:"w-full overflow-hidden",children:(0,a.jsx)("div",{className:"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg",children:(0,a.jsxs)("div",{className:"relative w-full overflow-hidden rounded-lg",children:[(0,a.jsxs)("div",{className:"relative w-full overflow-hidden rounded-t-xl",children:[(()=>{var e;let t=s.image_url;if(s.images&&Array.isArray(s.images)&&s.images.length>0){let e="number"==typeof s.featured_image_index?Math.min(s.featured_image_index,s.images.length-1):0;t=s.images[e]}return!t||p?(0,a.jsx)("div",{className:"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl",children:(0,a.jsx)("svg",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}):(0,a.jsxs)("div",{className:"overflow-hidden",children:[!S&&(0,a.jsx)(c.E,{className:"absolute inset-0 rounded-t-xl"}),(0,a.jsx)(o.P.div,{className:"w-full",children:(0,a.jsx)(l.default,{src:t,alt:null!=(e=s.name)?e:"Product image",width:500,height:750,className:"w-full aspect-square object-cover ".concat(k?"filter grayscale opacity-70 transition-all duration-500":""," ").concat(S?"opacity-100":"opacity-0"," max-w-full"),loading:"lazy",onError:()=>f(!0),onLoad:()=>E(!0),quality:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",placeholder:"blur",style:{objectFit:"cover"}})})]})})(),k&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40",children:(0,a.jsx)("div",{className:"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground",children:(0,a.jsx)("span",{className:"font-medium tracking-wide uppercase text-xs sm:text-sm",children:"Out of Stock"})})}),y&&(0,a.jsx)(d.N,{children:(0,a.jsx)(o.P.div,{variants:h,initial:"initial",animate:"animate",whileHover:"hover",className:(0,u.cn)("absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg","bg-destructive","text-destructive-foreground border border-destructive-foreground/20","transform-gpu"),children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsx)("span",{className:"text-[7px] sm:text-[9px] md:text-[10px] font-medium",children:"SAVE"}),(0,a.jsxs)("span",{className:"text-[9px] sm:text-xs md:text-sm leading-none",children:[N,"%"]})]})},"discount-badge-".concat(s.id))})]}),(0,a.jsxs)("div",{className:"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1",children:[(0,a.jsx)("p",{className:"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden",children:null!=(t=s.name)?t:"Unnamed Product"}),s.description&&(0,a.jsx)("p",{className:"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate",children:s.description}),(0,a.jsx)("div",{className:"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1",children:(0,a.jsxs)("div",{className:"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full",children:[b&&(0,a.jsx)("p",{className:"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full",children:b}),j&&(0,a.jsx)("p",{className:"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500",children:j})]})})]})]})})});return i&&"business_slug"in s&&s.business_slug?(0,a.jsx)(n(),{href:"/".concat(s.business_slug,"/product/").concat(s.slug||s.id),className:"block h-full",children:A}):A}},65617:(e,t,s)=>{s.d(t,{E:()=>r});var a=s(34477);let r=(0,a.createServerReference)("401bed28fefc4fa316c9657ce0fb9ce951b2f6cefe",a.callServer,void 0,a.findSourceMapURL,"getSelectedProducts")},69663:(e,t,s)=>{s.d(t,{BK:()=>n,eu:()=>i,q5:()=>c});var a=s(95155);s(12115);var r=s(54011),l=s(53999);function i(e){let{className:t,...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)(r._V,{"data-slot":"avatar-image",className:(0,l.cn)("aspect-square size-full",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...s})}},82147:(e,t,s)=>{s.d(t,{A:()=>r});var a=s(95155);s(12115);let r=e=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"})})},99474:(e,t,s)=>{s.d(t,{T:()=>l});var a=s(95155);s(12115);var r=s(53999);function l(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}}}]);