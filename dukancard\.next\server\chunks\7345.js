exports.id=7345,exports.ids=[7345],exports.modules={17740:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]])},24133:(e,r,t)=>{"use strict";t.d(r,{default:()=>c});var a=t(60687),s=t(43210),i=t(77882),n=t(46001),l=t(24934),o=t(16189);function c(){let e=(0,o.useRouter)(),[r,t]=(0,s.useState)(!1);return(0,a.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center p-4 bg-white dark:bg-black",children:(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-md w-full bg-white dark:bg-neutral-900 rounded-xl shadow-lg p-8 border border-neutral-200 dark:border-neutral-800 text-center",children:[(0,a.jsx)("div",{className:"mb-6 flex justify-center",children:(0,a.jsx)("div",{className:"p-4 bg-neutral-100 dark:bg-neutral-800 rounded-full",children:(0,a.jsx)(n.A,{className:"h-12 w-12 text-[var(--brand-gold)]"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold mb-3 text-neutral-900 dark:text-neutral-100",children:"This Business is Currently Offline"}),(0,a.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 mb-6",children:"This business card is currently set to private or offline mode. Meanwhile, you can discover other businesses in your locality."}),(0,a.jsxs)("div",{className:"relative group",children:[r&&(0,a.jsx)(i.P.div,{className:"absolute -inset-0.5 rounded-md blur-md",style:{background:"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))"},initial:{opacity:.7},animate:{opacity:[.7,.9,.7],boxShadow:["0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)","0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)","0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)"]},transition:{duration:2,repeat:1/0,repeatType:"reverse"}}),(0,a.jsx)(l.$,{onClick:()=>e.push("/discover"),className:"relative w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-8 py-6 h-12 rounded-md shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer",children:(0,a.jsxs)("span",{className:"relative z-10 flex items-center justify-center gap-2",children:["Discover Businesses",(0,a.jsx)(n.A,{className:"h-5 w-5"})]})})]})]})})}},26373:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var a=t(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:l="",children:o,iconNode:c,...d},u)=>(0,a.createElement)("svg",{ref:u,...n,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:i("lucide",l),...d},[...c.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(o)?o:[o]])),o=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...n},o)=>(0,a.createElement)(l,{ref:o,iconNode:r,className:i(`lucide-${s(e)}`,t),...n}));return t.displayName=`${e}`,t}},36024:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var a=t(37413),s=t(63420);function i(){return(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(s.A,{className:"h-8 w-8 animate-spin text-[var(--brand-gold)]"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading business card..."})]})})}},37472:(e,r,t)=>{"use strict";t.d(r,{Wx:()=>d});var a=t(43210),s=Object.defineProperty,i=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,n=new Map,l=new WeakMap,o=0,c=void 0;function d({threshold:e,delay:r,trackVisibility:t,rootMargin:s,root:i,triggerOnce:u,skip:f,initialInView:g,fallbackInView:b,onChange:m}={}){var h;let[p,x]=a.useState(null),y=a.useRef(m),[v,w]=a.useState({inView:!!g,entry:void 0});y.current=m,a.useEffect(()=>{let a;if(!f&&p)return a=function(e,r,t={},a=c){if(void 0===window.IntersectionObserver&&void 0!==a){let s=e.getBoundingClientRect();return r(a,{isIntersecting:a,target:e,intersectionRatio:"number"==typeof t.threshold?t.threshold:0,time:0,boundingClientRect:s,intersectionRect:s,rootBounds:s}),()=>{}}let{id:s,observer:i,elements:d}=function(e){let r=Object.keys(e).sort().filter(r=>void 0!==e[r]).map(r=>{var t;return`${r}_${"root"===r?!(t=e.root)?"0":(l.has(t)||(o+=1,l.set(t,o.toString())),l.get(t)):e[r]}`}).toString(),t=n.get(r);if(!t){let a,s=new Map,i=new IntersectionObserver(r=>{r.forEach(r=>{var t;let i=r.isIntersecting&&a.some(e=>r.intersectionRatio>=e);e.trackVisibility&&void 0===r.isVisible&&(r.isVisible=i),null==(t=s.get(r.target))||t.forEach(e=>{e(i,r)})})},e);a=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),t={id:r,observer:i,elements:s},n.set(r,t)}return t}(t),u=d.get(e)||[];return d.has(e)||d.set(e,u),u.push(r),i.observe(e),function(){u.splice(u.indexOf(r),1),0===u.length&&(d.delete(e),i.unobserve(e)),0===d.size&&(i.disconnect(),n.delete(s))}}(p,(e,r)=>{w({inView:e,entry:r}),y.current&&y.current(e,r),r.isIntersecting&&u&&a&&(a(),a=void 0)},{root:i,rootMargin:s,threshold:e,trackVisibility:t,delay:r},b),()=>{a&&a()}},[Array.isArray(e)?e.toString():e,p,i,s,u,f,t,b,r]);let _=null==(h=v.entry)?void 0:h.target,j=a.useRef(void 0);p||!_||u||f||j.current===_||(j.current=_,w({inView:!!g,entry:void 0}));let k=[x,v.inView,v.entry];return k.ref=k[0],k.inView=k[1],k.entry=k[2],k}a.Component},48236:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var a=t(37413);t(61120);var s=t(14890),i=t(60644),n=t(11637),l=t(95006),o=t(92506),c=t(46501),d=t(21886),u=t(23392);function f({children:e}){return(0,a.jsx)(u.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,a.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,a.jsx)(s.default,{}),(0,a.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,a.jsx)(i.default,{}),(0,a.jsx)(l.default,{}),(0,a.jsx)(n.default,{}),(0,a.jsx)(o.default,{}),(0,a.jsx)(c.default,{}),(0,a.jsx)(d.default,{excludePaths:["/dashboard"]})]})})}},62299:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\[cardSlug]\\\\components\\\\OfflineBusinessMessage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx","default")},63420:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(26373).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},63500:(e,r,t)=>{"use strict";function a(e){if(!e)return 0;switch(e){case"free":return 1;case"basic":case"trial":return 3;case"growth":return 10;case"pro":return 50;case"enterprise":return 100;default:return 0}}function s(e,r){return r<a(e)}t.d(r,{v:()=>s,w:()=>a})},78335:()=>{},82944:(e,r,t)=>{"use strict";t.d(r,{V4:()=>o,rh:()=>l,t$:()=>n,yR:()=>c});var a=t(67218);t(79130);var s=t(32032),i=t(63500);async function n(e){let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.from("business_profiles").select("gallery").eq("id",e).single();if(a)return console.error("Error fetching business profile:",a),{images:[],error:`Failed to fetch gallery images: ${a.message}`};let s=t?.gallery||[],i=Array.isArray(s)?s:[];return i.sort((e,r)=>new Date(r.created_at).getTime()-new Date(e.created_at).getTime()),{images:i}}catch(e){return console.error("Unexpected error fetching gallery images:",e),{images:[],error:`An unexpected error occurred: ${e instanceof Error?e.message:String(e)}`}}}async function l(e){let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.from("business_profiles").select("id").eq("business_slug",e).eq("status","online").single();if(a||!t)return console.error("Error fetching business profile:",a),{images:[],totalCount:0,error:"Business not found"};let{data:s,error:n}=await r.from("public_subscription_status").select("plan_id").eq("business_profile_id",t.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();n&&console.error("Error fetching subscription data:",n);let l=s?.plan_id||"free",o=(0,i.w)(l),{data:c,error:d}=await r.from("business_profiles").select("gallery").eq("id",t.id).single();if(d)return console.error("Error fetching business gallery:",d),{images:[],totalCount:0,error:`Failed to fetch gallery images: ${d.message}`};let u=c?.gallery||[],f=Array.isArray(u)?u:[];f.sort((e,r)=>new Date(r.created_at).getTime()-new Date(e.created_at).getTime());let g=f.slice(0,o),b=Math.min(g.length,4),m=g.slice(0,b),h=g.length;return{images:m,totalCount:h}}catch(e){return console.error("Unexpected error fetching gallery images for tab:",e),{images:[],totalCount:0,error:`An unexpected error occurred: ${e instanceof Error?e.message:String(e)}`}}}async function o(e){let r=await (0,s.createClient)();try{let{data:t,error:a}=await r.from("business_profiles").select("id").eq("business_slug",e).eq("status","online").single();if(a||!t)return console.error("Error fetching business profile:",a),{images:[],error:"Business not found"};let{data:s,error:n}=await r.from("public_subscription_status").select("plan_id").eq("business_profile_id",t.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();n&&console.error("Error fetching subscription data:",n);let l=s?.plan_id||"free",o=(0,i.w)(l),{data:c,error:d}=await r.from("business_profiles").select("gallery").eq("id",t.id).single();if(d)return console.error("Error fetching business gallery:",d),{images:[],error:`Failed to fetch gallery images: ${d.message}`};let u=c?.gallery||[],f=Array.isArray(u)?u:[];return f.sort((e,r)=>new Date(r.created_at).getTime()-new Date(e.created_at).getTime()),{images:f.slice(0,o)}}catch(e){return console.error("Unexpected error fetching gallery images by slug:",e),{images:[],error:`An unexpected error occurred: ${e instanceof Error?e.message:String(e)}`}}}async function c(e,r=1,t=20){let a=await (0,s.createClient)();try{let{data:s,error:n}=await a.from("business_profiles").select("id").eq("business_slug",e).eq("status","online").single();if(n||!s)return console.error("Error fetching business profile:",n),{images:[],totalCount:0,totalPages:0,currentPage:r,hasNextPage:!1,hasPrevPage:!1,error:"Business not found"};let{data:l,error:o}=await a.from("public_subscription_status").select("plan_id").eq("business_profile_id",s.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();o&&console.error("Error fetching subscription data:",o);let c=l?.plan_id||"free",d=(0,i.w)(c),{data:u,error:f}=await a.from("business_profiles").select("gallery").eq("id",s.id).single();if(f)return console.error("Error fetching business gallery:",f),{images:[],totalCount:0,totalPages:0,currentPage:r,hasNextPage:!1,hasPrevPage:!1,error:`Failed to fetch gallery images: ${f.message}`};let g=u?.gallery||[],b=Array.isArray(g)?g:[];b.sort((e,r)=>new Date(r.created_at).getTime()-new Date(e.created_at).getTime());let m=b.slice(0,d),h=m.length,p=Math.ceil(h/t),x=(r-1)*t;return{images:m.slice(x,x+t),totalCount:h,totalPages:p,currentPage:r,hasNextPage:r<p,hasPrevPage:r>1}}catch(e){return console.error("Unexpected error fetching paginated gallery images:",e),{images:[],totalCount:0,totalPages:0,currentPage:r,hasNextPage:!1,hasPrevPage:!1,error:`An unexpected error occurred: ${e instanceof Error?e.message:String(e)}`}}}(0,t(17478).D)([n,l,o,c]),(0,a.A)(n,"40f500a30146bf20b7cbf9e14a4166a4ae90273c1b",null),(0,a.A)(l,"409d02bd002441f404c34d58f50a5ad0b89c512a13",null),(0,a.A)(o,"409de55978441ebca7afede7b28b40414d09429d58",null),(0,a.A)(c,"70c9dfab15ca22682df83077abf481dbf2eb62e561",null)},96487:()=>{},99953:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"0056cc8508beee15907e2741a09bee834aa2799373":()=>n.E,"00a8d295fac0c253c5942cbbd729c108ff5d62d666":()=>l.h,"40274d19c8c06b8c99a2839744992d7bcecb2341c5":()=>a.$,"407031dcd9eee2e858cfbb811d905061dca994df13":()=>a.f,"4088dc18235d8e33be19d97fc9c00c650f82e08953":()=>l.a,"409d02bd002441f404c34d58f50a5ad0b89c512a13":()=>c.rh,"409de55978441ebca7afede7b28b40414d09429d58":()=>c.V4,"40f500a30146bf20b7cbf9e14a4166a4ae90273c1b":()=>c.t$,"70c9dfab15ca22682df83077abf481dbf2eb62e561":()=>c.yR,"70e4edcef42c86cf3a9ba8c209ba199083b0a30092":()=>s.Z,"788721cac1d66dc0019d95bd960521fed5169baa89":()=>o.C,"7c76d89983ca9f1290f11616f3bb631fd67faec14d":()=>s.C,"7f1ff705112688da08e7c134e2df7387375812d40e":()=>i.K});var a=t(27628),s=t(46317),i=t(711),n=t(72116),l=t(11597),o=t(66352),c=t(82944)}};