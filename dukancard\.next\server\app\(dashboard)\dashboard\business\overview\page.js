(()=>{var e={};e.id=5618,e.ids=[5618],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8098:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>a.B});var a=t(64275)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11392:(e,r,t)=>{"use strict";t.d(r,{Cg:()=>i,F6:()=>d,Nw:()=>l,my:()=>a,s0:()=>n,w4:()=>s});let a=6048e5,s=864e5,i=6e4,n=36e5,l=43200,d=1440},11997:e=>{"use strict";e.exports=require("punycode")},13091:(e,r,t)=>{"use strict";t.d(r,{b:()=>s});var a=t(47138);function s(e,r){return(0,a.a)(e)-(0,a.a)(r)}},14889:(e,r,t)=>{"use strict";t.d(r,{z:()=>s});var a=t(47138);function s(e,r){let t=(0,a.a)(e),s=(0,a.a)(r),i=t.getTime()-s.getTime();return i<0?-1:i>0?1:i}},17166:(e,r,t)=>{Promise.resolve().then(t.bind(t,40320))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35780:(e,r,t)=>{"use strict";function a(e,r){return e instanceof Date?new e.constructor(r):new Date(r)}t.d(r,{w:()=>a})},40320:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\components\\\\BusinessDashboardClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx","default")},44064:(e,r,t)=>{"use strict";t.d(r,{O:()=>i});var a=t(84323),s=t(13091);function i(e,r,t){let i=(0,s.b)(e,r)/1e3;return(0,a.u)(t?.roundingMethod)(i)}},47138:(e,r,t)=>{"use strict";function a(e){let r=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===r?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===r||"string"==typeof e||"[object String]"===r?e:NaN)}t.d(r,{a:()=>a})},54781:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var a=t(37413),s=t(66819);function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55844:(e,r,t)=>{"use strict";t.d(r,{m:()=>l});var a=t(11392),s=t(47138);function i(e){let r=(0,s.a)(e);return r.setHours(0,0,0,0),r}var n=t(79943);function l(e,r){let t=i(e),s=i(r);return Math.round((t-(0,n.G)(t)-(s-(0,n.G)(s)))/a.w4)}},56918:(e,r,t)=>{Promise.resolve().then(t.bind(t,85802))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i,gV:()=>n});var a=t(75986),s=t(8974);function i(...e){return(0,s.QP)((0,a.$)(e))}function n(e){if(!e)return null;let r=e.trim();return(r.startsWith("+91")?r=r.substring(3):12===r.length&&r.startsWith("91")&&(r=r.substring(2)),/^\d{10}$/.test(r))?r:null}},70334:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},73481:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>u});var a=t(37413),s=t(61120),i=t(32032),n=t(39916),l=t(20670),d=t(40320),o=t(54781);let c=(e,r)=>{if(e)return(0,l.Nh)("yearly"===r?"yearly":"monthly").find(r=>r.id===e)},u={title:"Business Dashboard Overview",robots:"noindex, nofollow"};async function m(){let e=await (0,i.createClient)(),{data:{user:r}}=await e.auth.getUser();if(!r)return(0,n.redirect)("/login?message=Authentication required");let{data:t,error:l}=await e.from("business_profiles").select("business_name, business_slug, trial_end_date, total_likes, total_subscriptions, average_rating, logo_url, title, status").eq("id",r.id).single(),{data:u,error:m}=await e.from("payment_subscriptions").select("subscription_status, plan_id, plan_cycle").eq("business_profile_id",r.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(m&&console.error("Error fetching subscription data:",m),l||!t)return console.error("Error fetching business profile or profile not found:",l?.message),(0,n.redirect)("/login?message=Profile fetch error");let x=function(e,r){if(!e)return"inactive";let t=new Date,a=e.trial_end_date?new Date(e.trial_end_date):null;if(r?.subscription_status){let e=r.subscription_status,t=r.plan_id||"free";if(("active"===e||"authenticated"===e)&&"free"!==t)return"active";if("trial"===e)return"trial"}return a&&a>t?"trial":"inactive"}(t,u),p=u?.plan_id||"free",h=c(p,u?.plan_cycle||null),b={business_name:t.business_name||"",business_slug:t.business_slug||"",plan_id:p,plan_cycle:u?.plan_cycle||null,has_active_subscription:(u?.subscription_status==="active"||u?.subscription_status==="authenticated")&&"free"!==p,trial_end_date:t.trial_end_date,total_likes:t.total_likes||0,total_subscriptions:t.total_subscriptions||0,average_rating:t.average_rating||0,logo_url:t.logo_url,title:t.title,status:t.status||"offline"};return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)(o.E,{className:"h-[600px] w-full"}),children:(0,a.jsx)(d.default,{initialProfile:b,userId:r.id,subscriptionStatus:x,planDetails:h,subscription:u})})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79943:(e,r,t)=>{"use strict";t.d(r,{G:()=>s});var a=t(47138);function s(e){let r=(0,a.a)(e),t=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()));return t.setUTCFullYear(r.getFullYear()),e-t}},81630:e=>{"use strict";e.exports=require("http")},84323:(e,r,t)=>{"use strict";function a(e){return r=>{let t=(e?Math[e]:Math.trunc)(r);return 0===t?0:t}}t.d(r,{u:()=>a})},85802:(e,r,t)=>{"use strict";t.d(r,{default:()=>q});var a=t(60687),s=t(43210),i=t(77882),n=t(85814),l=t.n(n),d=t(49625),o=t(85778),c=t(24934);t(38398);var u=t(96241),m=t(67760),x=t(41312),p=t(64398);function h({title:e,value:r,icon:t,description:s,color:n,isUpdated:l=!1}){return(0,a.jsxs)(i.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,a.jsxs)("div",{className:"relative p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-neutral-600 dark:text-neutral-400 tracking-wide",children:e}),(0,a.jsx)("div",{className:"flex items-baseline gap-2",children:(0,a.jsx)(i.P.h3,{className:"text-3xl font-bold text-neutral-900 dark:text-neutral-100 tracking-tight",variants:{initial:{scale:1},update:{scale:1.05,transition:{duration:.3}}},initial:"initial",animate:l?"update":"initial",children:"string"==typeof r?r:r.toLocaleString()})})]}),(0,a.jsx)("div",{className:`
            flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300
            ${{rose:{icon:"bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400 group-hover:bg-rose-100 dark:group-hover:bg-rose-900/30",accent:"text-rose-600 dark:text-rose-400"},blue:{icon:"bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30",accent:"text-blue-600 dark:text-blue-400"},amber:{icon:"bg-amber-50 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400 group-hover:bg-amber-100 dark:group-hover:bg-amber-900/30",accent:"text-amber-600 dark:text-amber-400"},red:{icon:"bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400 group-hover:bg-red-100 dark:group-hover:bg-red-900/30",accent:"text-red-600 dark:text-red-400"},yellow:{icon:"bg-yellow-50 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400 group-hover:bg-yellow-100 dark:group-hover:bg-yellow-900/30",accent:"text-yellow-600 dark:text-yellow-400"},primary:{icon:"bg-primary/10 text-primary group-hover:bg-primary/15",accent:"text-primary"},emerald:{icon:"bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400 group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/30",accent:"text-emerald-600 dark:text-emerald-400"}}[n].icon}
          `,children:(0,a.jsx)(t,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400 leading-relaxed",children:s}),l&&(0,a.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"flex items-center gap-2 text-xs font-medium text-primary",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary animate-pulse"}),"Updated"]})]})]})]})}function b({initialProfile:e,userId:r}){let[t,n]=(0,s.useState)(e),[l,d]=(0,s.useState)(!1),[o,c]=(0,s.useState)(!1),[b,f]=(0,s.useState)(!1);return(0,a.jsxs)(i.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-8",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Performance Overview"}),(0,a.jsx)("div",{className:"h-px flex-1 bg-gradient-to-r from-neutral-200 to-transparent dark:from-neutral-700"})]}),(0,a.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed",children:"Real-time insights into your business performance and customer engagement metrics."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(h,{title:"Total Likes",value:(0,u.gY)(t.total_likes),icon:m.A,description:"People who liked your business card",color:"red",isUpdated:l}),(0,a.jsx)(h,{title:"Subscribers",value:(0,u.gY)(t.total_subscriptions),icon:x.A,description:"People subscribed to your updates",color:"blue",isUpdated:o}),(0,a.jsx)(h,{title:"Average Rating",value:`${t.average_rating?.toFixed(1)||"0.0"}/5.0`,icon:p.A,description:"Customer satisfaction rating",color:"yellow",isUpdated:b})]})]})}var f=t(59821),g=t(38038);let v=(0,t(62688).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var j=t(70334),y=t(77493),w=t(8160);function N({subscriptionStatus:e,planDetails:r,trialEndDate:t,planCycle:s,subscription:i}){let n=()=>{if(!r)return(0,a.jsx)(g.A,{className:"w-3.5 h-3.5 mr-1"});switch(r.id){case"premium":case"enterprise":return(0,a.jsx)(v,{className:"w-3.5 h-3.5 mr-1"});default:return(0,a.jsx)(g.A,{className:"w-3.5 h-3.5 mr-1"})}},d=()=>{if(!r)return"";switch(r.id){case"premium":case"enterprise":return"border-[var(--brand-gold)]/50 text-[var(--brand-gold)]";case"pro":return"border-blue-500/50 text-blue-500 dark:border-blue-400/50 dark:text-blue-400";case"growth":default:return"border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400";case"basic":return"border-purple-500/50 text-purple-500 dark:border-purple-400/50 dark:text-purple-400";case"free":return"border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400"}};return(0,a.jsx)("div",{className:"h-full",children:(0,a.jsxs)("div",{className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6 h-full flex flex-col",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-3 mb-6",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,a.jsx)(g.A,{className:"w-5 h-5 text-primary"})}),(0,a.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Subscription"})]}),(0,a.jsx)("h3",{className:"text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Plan Status"})]}),"active"===e&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-emerald-500"}),(0,a.jsx)("span",{className:"text-xs font-medium text-emerald-600 dark:text-emerald-400",children:"Active"})]}),"trial"===e&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-amber-500"}),(0,a.jsx)("span",{className:"text-xs font-medium text-amber-600 dark:text-amber-400",children:"Trial"})]})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col justify-center",children:["active"===e&&r&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-center",children:[(0,a.jsx)("div",{children:(0,a.jsxs)(f.E,{variant:"outline",className:(0,u.cn)("border px-2 py-1 mb-2",d()),children:[n(),i?.subscription_status===w.dH.HALTED?"Paused":"Active"]})}),(0,a.jsx)("h3",{className:"text-base sm:text-lg font-medium mb-1",children:r.name}),"free"!==r.id&&(0,a.jsx)("p",{className:"text-xs sm:text-sm font-medium text-primary mb-1",children:"yearly"===s?"Yearly Plan":"Monthly Plan"}),i?.subscription_status===w.dH.HALTED?(0,a.jsx)("p",{className:"text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3",children:"Your subscription is currently paused. Your business card is offline until you resume your subscription."}):(0,a.jsx)("p",{className:"text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3",children:"Your subscription is active and all features are enabled"}),(0,a.jsx)("div",{className:"mt-2 w-full",children:(0,a.jsx)(c.$,{asChild:!0,variant:"outline",className:"w-full border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:(0,a.jsxs)(l(),{href:"/dashboard/business/plan",className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("span",{className:"whitespace-nowrap",children:"Manage Plan"}),(0,a.jsx)(j.A,{className:"w-4 h-4"})]})})})]}),"trial"===e&&t&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-center",children:[(0,a.jsx)("div",{children:(0,a.jsxs)(f.E,{variant:"outline",className:"border border-[var(--brand-gold)]/50 text-[var(--brand-gold)] px-2 py-1 mb-2",children:[(0,a.jsx)(v,{className:"w-3.5 h-3.5 mr-1"}),"Trial"]})}),(0,a.jsxs)("h3",{className:"text-base sm:text-lg font-medium mb-1",children:[r?r.name:"Premium"," Trial"]}),r&&"free"!==r.id&&(0,a.jsx)("p",{className:"text-xs sm:text-sm font-medium text-[var(--brand-gold)] mb-2",children:"yearly"===s?"Yearly Plan":"Monthly Plan"}),(0,a.jsx)("div",{className:"w-full overflow-x-auto px-1",children:(0,a.jsx)(y.A,{endDate:t,label:"",tooltipText:"Your trial will expire soon. Choose a plan to continue using all features."})}),(0,a.jsx)("div",{className:"mt-4 w-full",children:(0,a.jsx)(c.$,{asChild:!0,className:"w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:(0,a.jsxs)(l(),{href:"/dashboard/business/plan",className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("span",{className:"whitespace-nowrap",children:"Choose a Plan"}),(0,a.jsx)(j.A,{className:"w-4 h-4"})]})})})]}),"inactive"===e&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-center",children:[(0,a.jsx)("div",{children:(0,a.jsxs)(f.E,{variant:"outline",className:(0,u.cn)("border px-2 py-1 mb-2",i?.subscription_status===w.dH.HALTED&&r?d():"border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400"),children:[i?.subscription_status===w.dH.HALTED&&r?n():(0,a.jsx)(g.A,{className:"w-3.5 h-3.5 mr-1"}),i?.subscription_status===w.dH.HALTED?"Paused":"Free Plan"]})}),(0,a.jsx)("h3",{className:"text-base sm:text-lg font-medium mb-1",children:i?.subscription_status===w.dH.HALTED&&r?r.name:"Free Plan"}),i?.subscription_status===w.dH.HALTED&&r&&"free"!==r.id&&(0,a.jsx)("p",{className:"text-xs sm:text-sm font-medium text-primary mb-1",children:"yearly"===s?"Yearly Plan":"Monthly Plan"}),i?.subscription_status===w.dH.HALTED?(0,a.jsx)("p",{className:"text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3",children:"Your subscription is currently paused. Your business card is offline until you resume your subscription."}):(0,a.jsx)("p",{className:"text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3",children:"You are on the free plan with limited features. Upgrade to access more features."}),(0,a.jsx)("div",{className:"mt-2 w-full",children:(0,a.jsx)(c.$,{asChild:!0,className:"w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:(0,a.jsxs)(l(),{href:"/dashboard/business/plan",className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("span",{className:"whitespace-nowrap",children:i?.subscription_status===w.dH.HALTED?"Manage Plan":"Upgrade Plan"}),(0,a.jsx)(j.A,{className:"w-4 h-4"})]})})})]})]})]})]})})}var k=t(19080),_=t(53411),P=t(97051);function A({userPlan:e}){return(0,a.jsxs)("div",{className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6 h-full",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-3 mb-6",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,a.jsx)(o.A,{className:"w-5 h-5 text-primary"})}),(0,a.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Management"})]}),(0,a.jsx)("h3",{className:"text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Quick Actions"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-emerald-500"}),(0,a.jsx)("span",{className:"text-xs font-medium text-emerald-600 dark:text-emerald-400",children:"Ready"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-3 overflow-hidden",children:[(0,a.jsx)(c.$,{asChild:!0,variant:"outline",className:"w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium",children:(0,a.jsxs)(l(),{href:"/dashboard/business/card",className:"flex items-center gap-3",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 text-neutral-600 dark:text-neutral-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Edit Business Card"})]})}),(0,a.jsx)(c.$,{asChild:!0,variant:"outline",className:"w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium",children:(0,a.jsxs)(l(),{href:"/dashboard/business/products",className:"flex items-center gap-3",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 text-neutral-600 dark:text-neutral-400"}),(0,a.jsx)("span",{className:"text-sm",children:"Manage Products"})]})}),(0,a.jsx)(c.$,{asChild:!0,variant:"outline",className:"w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium",children:(0,a.jsxs)(l(),{href:"/dashboard/business/analytics",className:"flex items-center gap-3",children:[(0,a.jsx)(_.A,{className:"w-4 h-4 text-neutral-600 dark:text-neutral-400"}),(0,a.jsx)("span",{className:"text-sm",children:"View Analytics"})]})}),(0,a.jsx)(c.$,{asChild:!0,variant:"outline",className:"w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium",children:(0,a.jsxs)(l(),{href:"/dashboard/business/likes",className:"flex items-center gap-3",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 text-neutral-600 dark:text-neutral-400"}),(0,a.jsx)("span",{className:"text-sm",children:"My Likes"})]})}),(0,a.jsx)(c.$,{asChild:!0,variant:"outline",className:"w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium",children:(0,a.jsxs)(l(),{href:"/dashboard/business/subscriptions",className:"flex items-center gap-3",children:[(0,a.jsx)(P.A,{className:"w-4 h-4 text-neutral-600 dark:text-neutral-400"}),(0,a.jsx)("span",{className:"text-sm",children:"My Subscriptions"})]})})]})]})]})}var C=t(3018),M=t(93613);function D(){return(0,a.jsx)(i.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},className:"mb-6",children:(0,a.jsxs)(C.Fc,{variant:"destructive",className:"relative overflow-hidden rounded-2xl border border-red-200/60 dark:border-red-800/60 bg-red-50/50 dark:bg-red-950/50 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-red-50/80 to-red-50/40 dark:from-red-950/80 dark:to-red-950/40"}),(0,a.jsx)("div",{className:"relative z-10",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-red-100 to-red-50 dark:from-red-900/50 dark:to-red-950/50 border border-red-200 dark:border-red-800",children:(0,a.jsx)(M.A,{className:"h-5 w-5 text-red-600 dark:text-red-400"})}),(0,a.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(C.XL,{className:"text-lg font-bold text-red-800 dark:text-red-300 tracking-tight",children:"Your Business Card is Offline"}),(0,a.jsx)(C.TN,{className:"text-red-700 dark:text-red-400 leading-relaxed",children:"Your business card is currently set to offline status and won't appear in search results or discovery pages."})]}),(0,a.jsx)(c.$,{asChild:!0,className:"bg-red-600 hover:bg-red-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:(0,a.jsxs)(l(),{href:"/dashboard/business/card",className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:"Go Online Now"}),(0,a.jsx)(j.A,{className:"h-4 w-4"})]})})]})]})})]})})}function q({initialProfile:e,userId:r,subscriptionStatus:t,planDetails:n,subscription:u}){let[m,x]=(0,s.useState)(0),p={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4}}};return(0,a.jsxs)(i.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-10",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,a.jsx)(d.A,{className:"w-5 h-5 text-primary"})}),(0,a.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Business Dashboard"})]}),(0,a.jsxs)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:["Welcome, ",e.business_name||"Business Owner"]}),(0,a.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Monitor your business performance, track key metrics, and manage your digital presence with real-time insights."})]}),(0,a.jsx)("div",{className:"flex items-center gap-3",children:(0,a.jsx)(l(),{href:"/dashboard/business/card",children:(0,a.jsxs)(c.$,{className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Manage Card"]})})})]}),"offline"===e.status&&(0,a.jsx)(i.P.div,{variants:p,children:(0,a.jsx)(D,{})}),(0,a.jsx)(i.P.div,{variants:p,children:(0,a.jsx)(b,{initialProfile:e,userId:r,userPlan:e.plan_id})}),(0,a.jsx)(i.P.div,{variants:p,children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)(N,{subscriptionStatus:t,planDetails:n,trialEndDate:e.trial_end_date,planCycle:e.plan_cycle,subscription:u}),(0,a.jsx)(A,{userPlan:e.plan_id})]})}),(0,a.jsx)(i.P.div,{variants:p,children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Recent Activities"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Activities feature is coming soon."})]})})]})}},87919:(e,r,t)=>{"use strict";t.d(r,{W:()=>i});var a=t(14889),s=t(47138);function i(e,r){let t,i=(0,s.a)(e),n=(0,s.a)(r),l=(0,a.z)(i,n),d=Math.abs(function(e,r){let t=(0,s.a)(e),a=(0,s.a)(r);return 12*(t.getFullYear()-a.getFullYear())+(t.getMonth()-a.getMonth())}(i,n));if(d<1)t=0;else{1===i.getMonth()&&i.getDate()>27&&i.setDate(30),i.setMonth(i.getMonth()-l*d);let r=(0,a.z)(i,n)===-l;(function(e){let r=(0,s.a)(e);return+function(e){let r=(0,s.a)(e);return r.setHours(23,59,59,999),r}(r)==+function(e){let r=(0,s.a)(e),t=r.getMonth();return r.setFullYear(r.getFullYear(),t+1,0),r.setHours(23,59,59,999),r}(r)})((0,s.a)(e))&&1===d&&1===(0,a.z)(e,n)&&(r=!1),t=l*(d-Number(r))}return 0===t?0:t}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95543:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(r,d);let o={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["overview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73481)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/overview/page",pathname:"/dashboard/business/overview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,6380,4017,802,3037,3739,9538,5265,9209,2799],()=>t(95543));module.exports=a})();