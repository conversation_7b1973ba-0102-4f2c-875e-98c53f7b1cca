(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3833],{2295:(e,a,r)=>{"use strict";r.d(a,{default:()=>ef});var t=r(95155),s=r(28695),l=r(60760),n=r(12115),i=r(56671),d=r(45964),o=r.n(d),c=r(88145),u=r(40646),x=r(54861),m=r(23451);let h={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.05}}},p={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},b={initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1,transition:{duration:.4,ease:"easeOut"}},exit:{opacity:0,scale:.95,transition:{duration:.3,ease:"easeIn"}}},g=(0,n.createContext)(void 0);function f(e){let{children:a,initialData:r,initialCount:s,planLimit:l,initialError:d}=e,[h,p]=(0,n.useState)(r),[b,f]=(0,n.useState)(s),[v,j]=(0,n.useState)(1),[y,N]=(0,n.useState)(10),[w,k]=(0,n.useState)(!1),[P,A]=(0,n.useTransition)(),[E,_]=(0,n.useState)("table"),[S,C]=(0,n.useState)(""),[I,z]=(0,n.useState)(""),[F,L]=(0,n.useState)("created_desc"),[M,R]=(0,n.useState)(!0),[D,V]=(0,n.useState)(!0),[$,O]=(0,n.useState)(null),T=Math.ceil(b/y);(0,n.useEffect)(()=>{d&&i.oR.error("Failed to load initial products: ".concat(d))},[d]);let U=o()(e=>{z(e)},500);(0,n.useEffect)(()=>(U(S),()=>U.cancel()),[S,U]);let Z=(0,n.useCallback)(async e=>{k(!0);try{let a=await (0,m.Y6)(e,y,{searchTerm:I||void 0},F);a.data?(p(a.data),j(e),void 0!==a.count&&f(a.count)):a.error&&i.oR.error("Failed to load products: ".concat(a.error))}catch(e){i.oR.error("An unexpected error occurred while loading products."),console.error("Error fetching products:",e)}finally{k(!1),V(!1)}},[I,F,y]);(0,n.useEffect)(()=>{j(1),Z(1)},[I,F,Z]),(0,n.useEffect)(()=>{j(1),Z(1)},[y,Z]),(0,n.useEffect)(()=>{(async()=>{R(!0);try{L("created_desc")}catch(e){console.error("Error during initialization:",e)}finally{R(!1),V(!1)}})()},[]);let q=async()=>{$&&A(async()=>{try{let e=await (0,m.i4)($);e.success?(i.oR.success("Item deleted successfully."),p(e=>e.filter(e=>e.id!==$)),f(e=>e-1)):i.oR.error(e.error||"Failed to delete item.")}catch(e){i.oR.error("An unexpected error occurred during deletion."),console.error("Delete operation failed:",e)}finally{O(null)}})},H=async()=>{};return(0,t.jsx)(g.Provider,{value:{products:h,totalCount:b,currentPage:v,totalPages:T,planLimit:l,canAddMore:l===1/0||b<l,itemsPerPage:y,viewType:E,setViewType:_,isLoading:w,isPending:P,isInitialLoading:D,searchTerm:S,setSearchTerm:C,sortBy:F,setSortBy:L,deletingProductId:$,setDeletingProductId:O,goToPage:e=>{w||e<1||e>T||Z(e)},setItemsPerPage:e=>{N(e)},handleAddNew:()=>{},handleDeleteConfirm:q,handleSave:H,getFilterStatusText:()=>{let e=[];return I&&e.push('Search: "'.concat(I,'"')),e.length>0?" | ".concat(e.join(", ")):""},getProductStatusBadge:e=>e?(0,t.jsxs)(c.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",children:[(0,t.jsx)(u.A,{className:"w-3 h-3 mr-1"}),"Available"]}):(0,t.jsxs)(c.E,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",children:[(0,t.jsx)(x.A,{className:"w-3 h-3 mr-1"}),"Unavailable"]})},children:a})}function v(){let e=(0,n.useContext)(g);if(void 0===e)throw Error("useProducts must be used within a ProductsProvider");return e}var j=r(57615),y=r(49103),N=r(6874),w=r.n(N),k=r(97168);function P(){let{canAddMore:e,isPending:a,isLoading:r}=v();return(0,t.jsxs)(s.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",variants:p,children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,t.jsx)(j.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,t.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Inventory Management"})]}),(0,t.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Products & Services"}),(0,t.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Manage your complete product catalog with advanced filtering, bulk operations, and real-time inventory tracking."})]}),(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsx)(w(),{href:"/dashboard/business/products/add",children:(0,t.jsxs)(k.$,{disabled:!e||a||r,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Add New Product"]})})})]})}var A=r(47924),E=r(54416),_=r(21492),S=r(89852),C=r(82714),I=r(95784);function z(){let{searchTerm:e,setSearchTerm:a,sortBy:r,setSortBy:l,isLoading:n,isPending:i}=v();return(0,t.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 p-6 rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm",children:[(0,t.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,t.jsxs)(C.J,{htmlFor:"search-products",className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 text-primary"}),"Search Products"]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)(A.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500 transition-colors group-focus-within:text-primary"}),(0,t.jsx)(S.p,{id:"search-products",type:"text",placeholder:"Search by name, description, or SKU...",value:e,onChange:e=>a(e.target.value),className:"w-full pl-12 pr-10 h-14 rounded-xl border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 placeholder:text-neutral-400",disabled:n}),e&&(0,t.jsx)(k.$,{variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200",onClick:()=>a(""),children:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"w-full sm:w-80 space-y-3",children:[(0,t.jsxs)(C.J,{htmlFor:"sort-by",className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 text-primary"}),"Sort By"]}),(0,t.jsxs)(I.l6,{value:r,onValueChange:e=>l(e),disabled:n||i,children:[(0,t.jsx)(I.bq,{id:"sort-by",className:"w-full h-14 rounded-xl border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",children:(0,t.jsx)(I.yv,{placeholder:"Choose sorting..."})}),(0,t.jsxs)(I.gC,{className:"border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-xl bg-white dark:bg-neutral-900 p-2",children:[(0,t.jsx)(I.eb,{value:"created_desc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Newest First"}),(0,t.jsx)(I.eb,{value:"created_asc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Oldest First"}),(0,t.jsx)(I.eb,{value:"name_asc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Name (A-Z)"}),(0,t.jsx)(I.eb,{value:"name_desc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Name (Z-A)"}),(0,t.jsx)(I.eb,{value:"price_asc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Price (Low to High)"}),(0,t.jsx)(I.eb,{value:"price_desc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Price (High to Low)"}),(0,t.jsx)(I.eb,{value:"available_first",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Available First"}),(0,t.jsx)(I.eb,{value:"unavailable_first",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Unavailable First"})]})]})]})]})})}var F=r(37108),L=r(43453),M=r(33109);function R(){let{products:e,totalCount:a,planLimit:r}=v(),l=e.filter(e=>e.is_available).length,n=e.filter(e=>!e.is_available).length,i=e.length>0?Math.round(l/e.length*100):0,d=[{title:"Total Products",value:a,subtitle:r===1/0?"Unlimited plan":"".concat(a," of ").concat(r," used"),icon:F.A,color:"primary",progress:r===1/0?15:Math.min(100,Math.round(a/r*100))},{title:"Available",value:l,subtitle:"".concat(i,"% of inventory active"),icon:L.A,color:"emerald",progress:i},{title:"Unavailable",value:n,subtitle:0===n?"All products active":"".concat(100-i,"% inactive"),icon:x.A,color:"rose",progress:100-i}];return(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:d.map((e,a)=>(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*a},className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,t.jsxs)("div",{className:"relative p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-neutral-600 dark:text-neutral-400 tracking-wide",children:e.title}),(0,t.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-neutral-900 dark:text-neutral-100 tracking-tight",children:e.value.toLocaleString()}),"Total Products"===e.title&&(0,t.jsx)(M.A,{className:"w-4 h-4 text-primary opacity-60"})]})]}),(0,t.jsx)("div",{className:"\n                flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300\n                ".concat("primary"===e.color?"bg-primary/10 text-primary group-hover:bg-primary/15":"","\n                ").concat("emerald"===e.color?"bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400 group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/30":"","\n                ").concat("rose"===e.color?"bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400 group-hover:bg-rose-100 dark:group-hover:bg-rose-900/30":"","\n              "),children:(0,t.jsx)(e.icon,{className:"w-6 h-6"})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400 leading-relaxed",children:e.subtitle}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center text-xs text-neutral-500 dark:text-neutral-400",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[e.progress,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-neutral-200 dark:bg-neutral-800 rounded-full h-2 overflow-hidden",children:(0,t.jsx)(s.P.div,{initial:{width:0},animate:{width:"".concat(e.progress,"%")},transition:{duration:1,delay:.2*a+.5,ease:"easeOut"},className:"\n                      h-full rounded-full transition-all duration-300\n                      ".concat("primary"===e.color?"bg-gradient-to-r from-primary to-primary/80":"","\n                      ").concat("emerald"===e.color?"bg-gradient-to-r from-emerald-500 to-emerald-400":"","\n                      ").concat("rose"===e.color?"bg-gradient-to-r from-rose-500 to-rose-400":"","\n                    ")})})]})]})]})]},e.title))})}var D=r(66932),V=r(11004),$=r(31787),O=r(14541),T=r(60704),U=r(53999);function Z(e){let{className:a,...r}=e;return(0,t.jsx)(T.bL,{"data-slot":"tabs",className:(0,U.cn)("flex flex-col gap-2",a),...r})}function q(e){let{className:a,...r}=e;return(0,t.jsx)(T.B8,{"data-slot":"tabs-list",className:(0,U.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...r})}function H(e){let{className:a,...r}=e;return(0,t.jsx)(T.l9,{"data-slot":"tabs-trigger",className:(0,U.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...r})}function B(){let{viewType:e,setViewType:a,products:r,totalCount:l,itemsPerPage:n,setItemsPerPage:i,planLimit:d,getFilterStatusText:o,currentPage:u,isLoading:x}=v(),m=Math.min(u*n,l);return(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},className:"flex flex-col lg:flex-row lg:items-center justify-between gap-4 py-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3",children:[(0,t.jsx)("div",{className:"text-sm text-neutral-700 dark:text-neutral-300",children:0===r.length?(0,t.jsx)("span",{className:"font-medium",children:"No products found"}):(0,t.jsxs)("span",{children:["Showing ",(0,t.jsx)("span",{className:"font-semibold text-neutral-900 dark:text-neutral-100",children:(u-1)*n+1})," to"," ",(0,t.jsx)("span",{className:"font-semibold text-neutral-900 dark:text-neutral-100",children:m})," of"," ",(0,t.jsx)("span",{className:"font-semibold text-neutral-900 dark:text-neutral-100",children:l})," products",d!==1/0&&(0,t.jsxs)("span",{className:"text-neutral-500 dark:text-neutral-400 ml-1",children:["(limit: ",d,")"]})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[o()&&(0,t.jsxs)(c.E,{className:"bg-primary/10 text-primary border-primary/20 text-xs font-medium px-2 py-1 rounded-full",children:[(0,t.jsx)(D.A,{className:"w-3 h-3 mr-1"}),"Filtered"]}),d===1/0&&(0,t.jsxs)(c.E,{className:"bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 dark:from-purple-900/30 dark:to-pink-900/30 dark:text-purple-400 border-0 text-xs font-medium px-2 py-1 rounded-full",children:[(0,t.jsx)(V.A,{className:"w-3 h-3 mr-1"}),"Unlimited Plan"]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,t.jsx)(ep,{itemsPerPage:n,onItemsPerPageChange:i,isLoading:x}),(0,t.jsx)(Z,{value:e,onValueChange:e=>a(e),className:"w-auto",children:(0,t.jsxs)(q,{className:"grid w-40 grid-cols-2 bg-white dark:bg-neutral-900 border border-neutral-200/60 dark:border-neutral-800/60 rounded-xl p-1 shadow-sm",children:[(0,t.jsxs)(H,{value:"table",className:"text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200 px-3 py-2 flex items-center justify-center gap-2",children:[(0,t.jsx)($.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Table"})]}),(0,t.jsxs)(H,{value:"grid",className:"text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200 px-3 py-2 flex items-center justify-center gap-2",children:[(0,t.jsx)(O.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Grid"})]})]})})]})]})}var J=r(13052),Y=r(5623),Q=r(13717),G=r(62525),K=r(88524),W=r(67133),X=r(66766);function ee(e){let{view:a}=e,{searchTerm:r,handleAddNew:s,canAddMore:l}=v();return"table"===a?(0,t.jsxs)("div",{className:"h-32 flex flex-col items-center justify-center text-neutral-500 dark:text-neutral-400",children:[(0,t.jsx)(F.A,{className:"h-6 sm:h-8 w-6 sm:w-8 mb-2 opacity-40"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-center max-w-xs",children:r?"No products match your current filters":"You haven't added any products or services yet"}),!r&&(0,t.jsx)(k.$,{variant:"link",className:"mt-2 text-xs sm:text-sm text-primary hover:text-primary/80",onClick:s,disabled:!l,children:"Add your first item"})]}):(0,t.jsxs)("div",{className:"col-span-full h-40 flex flex-col items-center justify-center text-neutral-500 dark:text-neutral-400 border border-dashed border-neutral-200 dark:border-neutral-700 rounded-xl bg-neutral-50 dark:bg-neutral-800/50",children:[(0,t.jsx)(F.A,{className:"h-6 sm:h-8 w-6 sm:w-8 mb-2 opacity-40"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-center max-w-xs",children:r?"No products match your current filters":"You haven't added any products or services yet"}),!r&&(0,t.jsx)(k.$,{variant:"link",className:"mt-2 text-xs sm:text-sm text-primary hover:text-primary/80",onClick:s,disabled:!l,children:"Add your first item"})]})}var ea=r(75401),er=r(34477);let et=(0,er.createServerReference)("40e15bace5efcde3c132186e902abe40403e1fa4ff",er.callServer,void 0,er.findSourceMapURL,"getProductWithVariants");function es(){let{products:e,isLoading:a,isPending:r,deletingProductId:d,setDeletingProductId:o}=v(),[u,x]=(0,n.useState)(new Set),[m,h]=(0,n.useState)({}),[p,g]=(0,n.useState)(new Set),f=async e=>{let a=new Set(u);if(a.has(e))a.delete(e);else if(a.add(e),!m[e]){g(a=>new Set(a).add(e));try{let r=await et(e);r.success&&r.data?h(a=>{var t;return{...a,[e]:(null==(t=r.data)?void 0:t.variants)||[]}}):(i.oR.error("Failed to load product variants"),a.delete(e))}catch(r){console.error("Error loading variants:",r),i.oR.error("Failed to load product variants"),a.delete(e)}finally{g(a=>{let r=new Set(a);return r.delete(e),r})}}x(a)};return(0,t.jsx)(s.P.div,{variants:b,initial:"initial",animate:"animate",exit:"exit",className:"space-y-6",children:(0,t.jsx)("div",{className:"rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm",children:(0,t.jsxs)(K.XI,{children:[(0,t.jsx)(K.A0,{children:(0,t.jsxs)(K.Hj,{className:"border-b border-neutral-200/60 dark:border-neutral-800/60 bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 hover:from-neutral-100/80 hover:to-neutral-150/40 dark:hover:from-neutral-700/50 dark:hover:to-neutral-800/30 transition-all duration-200",children:[(0,t.jsx)(K.nd,{className:"w-12 text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4"}),(0,t.jsx)(K.nd,{className:"w-20 text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Image"}),(0,t.jsx)(K.nd,{className:"text-left text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4 px-6",children:"Product Details"}),(0,t.jsx)(K.nd,{className:"text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Category"}),(0,t.jsx)(K.nd,{className:"text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Variants"}),(0,t.jsx)(K.nd,{className:"text-right text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Pricing"}),(0,t.jsx)(K.nd,{className:"text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Status"}),(0,t.jsx)(K.nd,{className:"text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4 w-20",children:"Actions"})]})}),(0,t.jsxs)(K.BF,{children:[!a&&0===e.length&&(0,t.jsx)(K.Hj,{children:(0,t.jsx)(K.nA,{colSpan:8,className:"h-32 text-center",children:(0,t.jsx)(ee,{view:"table"})})}),e.map((e,a)=>{var i,x,h;let b=u.has(e.id),g=p.has(e.id),v=e.variant_count>0;return(0,t.jsxs)(n.Fragment,{children:[(0,t.jsxs)(s.P.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*a},className:"\n                      group border-b border-neutral-100/60 dark:border-neutral-800/60\n                      hover:bg-gradient-to-r hover:from-neutral-50/80 hover:to-neutral-100/40\n                      dark:hover:from-neutral-800/50 dark:hover:to-neutral-900/30\n                      ".concat(r&&d===e.id?"opacity-50 pointer-events-none":"","\n                      ").concat(b?"bg-gradient-to-r from-primary/5 to-primary/2 dark:from-primary/10 dark:to-primary/5":"","\n                      transition-all duration-300\n                    "),children:[(0,t.jsx)(K.nA,{className:"py-4 px-4",children:v?(0,t.jsx)(k.$,{variant:"ghost",size:"sm",onClick:()=>f(e.id),disabled:g,className:"h-8 w-8 p-0 rounded-lg hover:bg-primary/10 transition-colors duration-200",children:g?(0,t.jsx)(s.P.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:(0,t.jsx)(F.A,{className:"h-4 w-4 text-primary"})}):(0,t.jsx)(s.P.div,{animate:{rotate:90*!!b},transition:{duration:.3,ease:"easeInOut"},children:(0,t.jsx)(J.A,{className:"h-4 w-4 text-neutral-600 dark:text-neutral-400"})})}):(0,t.jsx)("div",{className:"h-8 w-8"})}),(0,t.jsx)(K.nA,{className:"py-4 px-4",children:(0,t.jsx)("div",{className:"flex justify-center",children:e.image_url?(0,t.jsx)("div",{className:"relative w-12 h-12 rounded-xl overflow-hidden border border-neutral-200/60 dark:border-neutral-700/60 shadow-sm group-hover:shadow-md transition-all duration-300",children:(0,t.jsx)(X.default,{src:e.image_url,alt:null!=(x=e.name)?x:"Product image",className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-110",width:48,height:48})}):(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-900 rounded-xl flex items-center justify-center text-neutral-400 dark:text-neutral-500 border border-neutral-200/60 dark:border-neutral-700/60",children:(0,t.jsx)(F.A,{className:"w-6 h-6"})})})}),(0,t.jsx)(K.nA,{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-semibold text-sm text-neutral-900 dark:text-neutral-100 max-w-xs truncate",children:e.name}),e.description&&(0,t.jsx)("div",{className:"text-xs text-neutral-500 dark:text-neutral-400 max-w-xs truncate",children:e.description})]})}),(0,t.jsx)(K.nA,{className:"py-4 px-4 text-center",children:(0,t.jsx)(c.E,{variant:"secondary",className:"capitalize text-xs font-medium bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 border-0 px-3 py-1 rounded-full",children:e.product_type})}),(0,t.jsx)(K.nA,{className:"py-4 px-4 text-center",children:e.variant_count>0?(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-semibold text-sm text-neutral-900 dark:text-neutral-100",children:e.variant_count}),(0,t.jsxs)("div",{className:"text-xs text-neutral-500 dark:text-neutral-400",children:[e.available_variant_count," active"]})]}):(0,t.jsx)("div",{className:"text-xs text-neutral-400 dark:text-neutral-500 font-medium",children:"No variants"})}),(0,t.jsx)(K.nA,{className:"py-4 px-4 text-right",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-semibold text-sm text-neutral-900 dark:text-neutral-100",children:null!=(h=null==(i=e.base_price)?void 0:i.toLocaleString("en-IN",{style:"currency",currency:"INR"}))?h:"—"}),e.discounted_price&&(0,t.jsx)("div",{className:"text-xs text-emerald-600 dark:text-emerald-400 font-medium",children:e.discounted_price.toLocaleString("en-IN",{style:"currency",currency:"INR"})})]})}),(0,t.jsx)(K.nA,{className:"py-4 px-4 text-center",children:(0,t.jsx)(c.E,{variant:e.is_available?"default":"secondary",className:"\n                          text-xs font-medium px-3 py-1 rounded-full border-0\n                          ".concat(e.is_available?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400":"bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400","\n                        "),children:e.is_available?"Available":"Unavailable"})}),(0,t.jsx)(K.nA,{className:"py-4 px-4 text-center",children:(0,t.jsxs)(W.rI,{children:[(0,t.jsx)(W.ty,{asChild:!0,children:(0,t.jsx)(k.$,{variant:"ghost",size:"sm",disabled:r,className:"h-8 w-8 p-0 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors duration-200",children:(0,t.jsx)(Y.A,{className:"h-4 w-4 text-neutral-600 dark:text-neutral-400"})})}),(0,t.jsxs)(W.SQ,{align:"end",className:"w-48 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm shadow-lg",children:[(0,t.jsx)(W._2,{asChild:!0,children:(0,t.jsxs)(w(),{href:"/dashboard/business/products/edit/".concat(e.id),className:"flex items-center gap-2 px-3 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),"Edit Product"]})}),(0,t.jsx)(W._2,{asChild:!0,children:(0,t.jsxs)("button",{onClick:()=>o(e.id),disabled:r||d===e.id,className:"flex items-center gap-2 px-3 py-2 text-sm text-rose-600 dark:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20 rounded-lg transition-colors duration-200 cursor-pointer w-full text-left disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),"Delete Product"]})})]})]})})]}),(0,t.jsx)(l.N,{children:b&&v&&m[e.id]&&(0,t.jsx)(s.P.tr,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,t.jsx)(K.nA,{colSpan:8,className:"p-0",children:(0,t.jsxs)("div",{className:"p-6 border-l-4 border-primary/20",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-neutral-900 dark:text-neutral-100 mb-2",children:"Product Variants"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400",children:"Manage different variations of this product"})]}),(0,t.jsx)(ea.A,{productId:e.id,variants:m[e.id]||[],className:"border-0 bg-transparent rounded-xl",onAddVariant:()=>{window.location.href="/dashboard/business/products/edit/".concat(e.id,"?tab=variants")},onEditVariant:a=>{window.location.href="/dashboard/business/products/edit/".concat(e.id,"?tab=variants&variant=").concat(a.id)},onDeleteVariant:e=>{console.log("Delete variant:",e)},onToggleVariantAvailability:(e,a)=>{console.log("Toggle variant availability:",e,a)}})]})})},"expanded-".concat(e.id))})]},e.id)})]})]})})},"table-view")}var el=r(37475);function en(){let{products:e,isLoading:a,isPending:r,deletingProductId:l,setDeletingProductId:n}=v();return a||0!==e.length?(0,t.jsx)(s.P.div,{variants:b,initial:"initial",animate:"animate",exit:"exit",className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map((e,a)=>{var i,d,o;return(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*a},className:"\n            group relative rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60\n            bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden\n            hover:border-neutral-300/60 dark:hover:border-neutral-700/60\n            hover:shadow-lg hover:shadow-neutral-200/20 dark:hover:shadow-neutral-900/20\n            transition-all duration-300\n            ".concat(r&&l===e.id?"opacity-50 pointer-events-none":"","\n          "),whileHover:{y:-4,transition:{duration:.2}},children:[(0,t.jsxs)("div",{className:"relative aspect-square overflow-hidden bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-800 dark:to-neutral-900",children:[e.image_url?(0,t.jsx)(X.default,{src:e.image_url,alt:null!=(d=e.name)?d:"Product image",className:"object-cover w-full h-full transition-transform duration-500 group-hover:scale-110",fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,t.jsx)(F.A,{className:"w-16 h-16 text-neutral-300 dark:text-neutral-600"})}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,t.jsx)("div",{className:"absolute top-3 right-3",children:(0,t.jsx)(c.E,{variant:e.is_available?"default":"secondary",className:"\n                  text-xs font-medium px-2 py-1 rounded-full border-0 shadow-sm\n                  ".concat(e.is_available?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400":"bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400","\n                "),children:e.is_available?"Available":"Unavailable"})}),e.variant_count>0&&(0,t.jsx)("div",{className:"absolute top-3 left-3",children:(0,t.jsxs)(c.E,{className:"bg-primary/90 text-primary-foreground text-xs font-medium px-2 py-1 rounded-full border-0 shadow-sm",children:[e.variant_count," variant",e.variant_count>1?"s":""]})}),(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300",children:(0,t.jsxs)(W.rI,{children:[(0,t.jsx)(W.ty,{asChild:!0,children:(0,t.jsx)(k.$,{variant:"secondary",size:"sm",disabled:r,className:"bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm border border-neutral-200/60 dark:border-neutral-700/60 shadow-lg hover:shadow-xl transition-all duration-200",children:(0,t.jsx)(Y.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(W.SQ,{align:"center",className:"w-48 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm shadow-xl",children:[(0,t.jsx)(W._2,{asChild:!0,children:(0,t.jsxs)(w(),{href:"/dashboard/business/products/edit/".concat(e.id),className:"flex items-center gap-2 px-3 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),"Edit Product"]})}),(0,t.jsx)(W._2,{asChild:!0,children:(0,t.jsxs)("button",{onClick:()=>n(e.id),disabled:r||l===e.id,className:"flex items-center gap-2 px-3 py-2 text-sm text-rose-600 dark:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20 rounded-lg transition-colors duration-200 cursor-pointer w-full text-left disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),"Delete Product"]})})]})]})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight",children:e.name}),(0,t.jsx)(c.E,{variant:"secondary",className:"capitalize text-xs font-medium bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 border-0 px-2 py-1 rounded-full",children:e.product_type})]}),e.description&&(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2 leading-relaxed",children:e.description}),e.variant_count>0&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs",children:[(0,t.jsxs)(c.E,{className:"bg-primary/10 text-primary border-0 px-2 py-1 rounded-full",children:[e.variant_count," variant",e.variant_count>1?"s":""]}),(0,t.jsx)("span",{className:"text-neutral-400",children:"•"}),(0,t.jsxs)("span",{className:"text-emerald-600 dark:text-emerald-400 font-medium",children:[e.available_variant_count," active"]})]}),(0,t.jsx)("div",{className:"space-y-1",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Price"}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"font-bold text-lg text-neutral-900 dark:text-neutral-100 flex items-center",children:[(0,t.jsx)(el.A,{className:"h-4 w-4 mr-1"}),null!=(o=null==(i=e.base_price)?void 0:i.toLocaleString("en-IN"))?o:"—"]}),e.discounted_price&&(0,t.jsxs)("div",{className:"text-sm text-emerald-600 dark:text-emerald-400 font-medium flex items-center justify-end",children:[(0,t.jsx)(el.A,{className:"h-3 w-3 mr-1"}),e.discounted_price.toLocaleString("en-IN")]})]})]})})]})]},e.id)})},"grid-view"):(0,t.jsx)(ee,{view:"grid"})}var ei=r(16559),ed=r(51154);function eo(){let{deletingProductId:e,setDeletingProductId:a,handleDeleteConfirm:r,isPending:s}=v();return(0,t.jsx)(ei.Lt,{open:!!e,onOpenChange:e=>!e&&a(null),children:(0,t.jsxs)(ei.EO,{className:"bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800 rounded-xl",children:[(0,t.jsxs)(ei.wd,{children:[(0,t.jsx)(ei.r7,{className:"text-neutral-800 dark:text-neutral-100",children:"Delete Item"}),(0,t.jsx)(ei.$v,{className:"text-neutral-500 dark:text-neutral-400",children:"Are you sure you want to delete this item? This action cannot be undone."})]}),(0,t.jsxs)(ei.ck,{children:[(0,t.jsx)(ei.Zr,{className:"border-neutral-200 dark:border-neutral-700 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800",disabled:s,children:"Cancel"}),(0,t.jsx)(ei.Rx,{className:"bg-red-600 hover:bg-red-700 text-white",onClick:e=>{e.preventDefault(),r()},disabled:s,children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ed.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete"})]})]})})}var ec=r(27737);function eu(e){let{view:a,count:r=10}=e;return"table"===a?(0,t.jsxs)("div",{className:"rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm",children:[(0,t.jsx)("div",{className:"border-b border-neutral-200/60 dark:border-neutral-800/60 bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-8 gap-4",children:[(0,t.jsx)(ec.E,{className:"h-4 w-8"}),(0,t.jsx)(ec.E,{className:"h-4 w-12"}),(0,t.jsx)(ec.E,{className:"h-4 w-24"}),(0,t.jsx)(ec.E,{className:"h-4 w-16"}),(0,t.jsx)(ec.E,{className:"h-4 w-16"}),(0,t.jsx)(ec.E,{className:"h-4 w-20"}),(0,t.jsx)(ec.E,{className:"h-4 w-16"}),(0,t.jsx)(ec.E,{className:"h-4 w-12"})]})}),(0,t.jsx)("div",{className:"divide-y divide-neutral-100/60 dark:divide-neutral-800/60",children:[...Array(r)].map((e,a)=>(0,t.jsx)(s.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.05*a},className:"p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-8 gap-4 items-center",children:[(0,t.jsx)(ec.E,{className:"h-8 w-8 rounded-lg"}),(0,t.jsx)(ec.E,{className:"h-12 w-12 rounded-xl"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ec.E,{className:"h-4 w-32"}),(0,t.jsx)(ec.E,{className:"h-3 w-24"})]}),(0,t.jsx)(ec.E,{className:"h-6 w-16 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(ec.E,{className:"h-4 w-8"}),(0,t.jsx)(ec.E,{className:"h-3 w-12"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(ec.E,{className:"h-4 w-16"}),(0,t.jsx)(ec.E,{className:"h-3 w-12"})]}),(0,t.jsx)(ec.E,{className:"h-6 w-20 rounded-full"}),(0,t.jsx)(ec.E,{className:"h-8 w-8 rounded-lg"})]})},a))})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[...Array(r)].map((e,a)=>(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*a},className:"rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm",children:[(0,t.jsx)(ec.E,{className:"w-full aspect-square"}),(0,t.jsxs)("div",{className:"p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ec.E,{className:"h-6 w-3/4"}),(0,t.jsx)(ec.E,{className:"h-5 w-20 rounded-full"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ec.E,{className:"h-4 w-full"}),(0,t.jsx)(ec.E,{className:"h-4 w-2/3"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ec.E,{className:"h-5 w-16 rounded-full"}),(0,t.jsx)(ec.E,{className:"h-3 w-2"}),(0,t.jsx)(ec.E,{className:"h-4 w-12"})]}),(0,t.jsx)("div",{className:"space-y-1",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(ec.E,{className:"h-4 w-10"}),(0,t.jsxs)("div",{className:"text-right space-y-1",children:[(0,t.jsx)(ec.E,{className:"h-6 w-20"}),(0,t.jsx)(ec.E,{className:"h-4 w-16"})]})]})})]})]},a))})}var ex=r(42355);function em(e){let{currentPage:a,totalPages:r,totalItems:l,itemsPerPage:n,onPageChange:i,onItemsPerPageChange:d,isLoading:o=!1,className:c}=e;if(0===l)return null;let u=(a-1)*n+1,x=Math.min(a*n,l),m=(()=>{let e=[];if(r<=7)for(let a=1;a<=r;a++)e.push(a);else{e.push(1);let t=Math.max(2,a-2),s=Math.min(r-1,a+2);a<=4&&(s=Math.min(r-1,5)),a>=r-3&&(t=Math.max(2,r-4)),t>2&&e.push("ellipsis");for(let a=t;a<=s;a++)e.push(a);s<r-1&&e.push("ellipsis"),r>1&&e.push(r)}return e})();return(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:(0,U.cn)("flex flex-col sm:flex-row items-center justify-between gap-4 py-6",c),children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-4 text-sm text-neutral-600 dark:text-neutral-400",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsxs)("span",{children:["Showing ",u," to ",x," of ",l," products"]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"whitespace-nowrap",children:"Items per page:"}),(0,t.jsxs)(I.l6,{value:n.toString(),onValueChange:e=>d(parseInt(e)),disabled:o,children:[(0,t.jsx)(I.bq,{className:"w-20 h-8 text-sm border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 focus:ring-2 focus:ring-primary/20 focus:border-primary",children:(0,t.jsx)(I.yv,{})}),(0,t.jsxs)(I.gC,{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg bg-white dark:bg-neutral-900",children:[(0,t.jsx)(I.eb,{value:"10",className:"text-sm focus:bg-primary/10 focus:text-primary",children:"10"}),(0,t.jsx)(I.eb,{value:"25",className:"text-sm focus:bg-primary/10 focus:text-primary",children:"25"}),(0,t.jsx)(I.eb,{value:"50",className:"text-sm focus:bg-primary/10 focus:text-primary",children:"50"}),(0,t.jsx)(I.eb,{value:"100",className:"text-sm focus:bg-primary/10 focus:text-primary",children:"100"})]})]})]})]}),r>1&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)(k.$,{variant:"outline",size:"sm",onClick:()=>i(a-1),disabled:1===a||o,className:"h-9 px-3 border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 disabled:opacity-50",children:[(0,t.jsx)(ex.A,{className:"h-4 w-4 mr-1"}),"Previous"]}),(0,t.jsx)("div",{className:"flex items-center gap-1 mx-2",children:m.map((e,r)=>{if("ellipsis"===e)return(0,t.jsx)("div",{className:"flex items-center justify-center w-9 h-9 text-neutral-400 dark:text-neutral-500",children:(0,t.jsx)(Y.A,{className:"h-4 w-4"})},"ellipsis-".concat(r));let s=a===e;return(0,t.jsx)(k.$,{variant:s?"default":"outline",size:"sm",onClick:()=>i(e),disabled:o,className:(0,U.cn)("h-9 w-9 p-0 text-sm",s?"bg-primary hover:bg-primary/90 text-primary-foreground border-primary shadow-sm":"border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300"),children:e},e)})}),(0,t.jsxs)(k.$,{variant:"outline",size:"sm",onClick:()=>i(a+1),disabled:a===r||o,className:"h-9 px-3 border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 disabled:opacity-50",children:["Next",(0,t.jsx)(J.A,{className:"h-4 w-4 ml-1"})]})]})]})}var eh=r(381);function ep(e){let{itemsPerPage:a,onItemsPerPageChange:r,isLoading:l=!1,className:n}=e;return(0,t.jsxs)(s.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:(0,U.cn)("flex items-center gap-3",n),children:[(0,t.jsxs)(C.J,{htmlFor:"items-per-page",className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2 whitespace-nowrap",children:[(0,t.jsx)(eh.A,{className:"h-4 w-4 text-primary"}),"Items per page"]}),(0,t.jsxs)(I.l6,{value:a.toString(),onValueChange:e=>r(parseInt(e)),disabled:l,children:[(0,t.jsx)(I.bq,{id:"items-per-page",className:"w-24 h-9 text-sm border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",children:(0,t.jsx)(I.yv,{})}),(0,t.jsxs)(I.gC,{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg bg-white dark:bg-neutral-900",children:[(0,t.jsx)(I.eb,{value:"10",className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer",children:"10"}),(0,t.jsx)(I.eb,{value:"25",className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer",children:"25"}),(0,t.jsx)(I.eb,{value:"50",className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer",children:"50"}),(0,t.jsx)(I.eb,{value:"100",className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer",children:"100"})]})]})]})}function eb(e){let{initialData:a,initialCount:r,planLimit:s,error:l}=e;return(0,t.jsx)(f,{initialData:a,initialCount:r,planLimit:s,initialError:l,children:(0,t.jsx)(eg,{})})}function eg(){let{viewType:e,isLoading:a,isInitialLoading:r,currentPage:n,totalPages:i,totalCount:d,itemsPerPage:o,goToPage:c,setItemsPerPage:u,products:x}=v();return(0,t.jsxs)(s.P.div,{className:"space-y-8",initial:"hidden",animate:"visible",variants:h,children:[(0,t.jsx)(s.P.div,{variants:h,children:(0,t.jsx)(P,{})}),(0,t.jsx)(s.P.div,{variants:h,children:(0,t.jsx)(R,{})}),(0,t.jsx)(s.P.div,{variants:h,children:(0,t.jsx)(z,{})}),(0,t.jsx)(s.P.div,{variants:h,children:(0,t.jsx)(B,{})}),(0,t.jsxs)(s.P.div,{variants:h,children:[r||a?(0,t.jsx)(eu,{view:e,count:o}):(0,t.jsx)(l.N,{mode:"wait",children:"table"===e?(0,t.jsx)(es,{},"table"):(0,t.jsx)(en,{},"grid")}),x.length>0&&(0,t.jsx)(em,{currentPage:n,totalPages:i,totalItems:d,itemsPerPage:o,onPageChange:c,onItemsPerPageChange:u,isLoading:a,className:"mt-8"})]}),(0,t.jsx)(eo,{})]})}function ef(e){let{initialData:a,initialCount:r,planLimit:l,error:n}=e;return(0,t.jsx)(s.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-6 relative",children:(0,t.jsx)(s.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4}}},className:"mb-6 relative z-10",children:(0,t.jsx)(eb,{initialData:a,initialCount:r,planLimit:l,error:n})})})}},16559:(e,a,r)=>{"use strict";r.d(a,{$v:()=>h,EO:()=>c,Lt:()=>i,Rx:()=>p,Zr:()=>b,ck:()=>x,r7:()=>m,wd:()=>u});var t=r(95155);r(12115);var s=r(62278),l=r(53999),n=r(97168);function i(e){let{...a}=e;return(0,t.jsx)(s.bL,{"data-slot":"alert-dialog",...a})}function d(e){let{...a}=e;return(0,t.jsx)(s.ZL,{"data-slot":"alert-dialog-portal",...a})}function o(e){let{className:a,...r}=e;return(0,t.jsx)(s.hJ,{"data-slot":"alert-dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...r})}function c(e){let{className:a,...r}=e;return(0,t.jsxs)(d,{children:[(0,t.jsx)(o,{}),(0,t.jsx)(s.UC,{"data-slot":"alert-dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...r})]})}function u(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...r})}function x(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...r})}function m(e){let{className:a,...r}=e;return(0,t.jsx)(s.hE,{"data-slot":"alert-dialog-title",className:(0,l.cn)("text-lg font-semibold",a),...r})}function h(e){let{className:a,...r}=e;return(0,t.jsx)(s.VY,{"data-slot":"alert-dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...r})}function p(e){let{className:a,...r}=e;return(0,t.jsx)(s.rc,{className:(0,l.cn)((0,n.r)(),a),...r})}function b(e){let{className:a,...r}=e;return(0,t.jsx)(s.ZD,{className:(0,l.cn)((0,n.r)({variant:"outline"}),a),...r})}},63651:(e,a,r)=>{Promise.resolve().then(r.bind(r,2295))}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,8695,6874,2290,6671,375,5152,7665,1884,6215,6766,6199,4577,346,9361,795,8441,1684,7358],()=>a(63651)),_N_E=e.O()}]);