"use strict";(()=>{var e={};e.id=2296,e.ids=[2296,5453],e.modules={2681:(e,r,t)=>{t.d(r,{d:()=>n});var s=t(67218);t(79130);var i=t(32032),a=t(12463);async function n(e,r){try{let t=await (0,i.createClient)(),{data:{user:s},error:n}=await t.auth.getUser();if(n||!s)return{success:!1,paymentRequired:!1,message:"Authentication required",error:"User not authenticated"};let l=await o(s.id);if(!l)return{success:!1,paymentRequired:!1,message:"Failed to get user context",error:"Could not retrieve user subscription context"};let p={planId:e,planCycle:r,context:l},b=a.b.validateRequest(p);if(!b.valid)return{success:!1,paymentRequired:!1,message:"Invalid subscription request",error:b.error};let f=a.b.determineSubscriptionFlow(p);switch(f.flowType){case"UPFRONT_PAYMENT":return await c(p,f);case"CREATE_AND_CANCEL":return await u(p,f);case"FRESH_SUBSCRIPTION":return await d(p,f);default:return{success:!1,paymentRequired:!1,message:"Unknown subscription flow",error:"Unsupported subscription flow type"}}}catch(e){return console.error("Subscription processing failed:",e),{success:!1,paymentRequired:!1,message:"Subscription processing failed",error:e instanceof Error?e.message:"Unknown error occurred"}}}async function o(e){try{let r=await (0,i.createClient)(),{data:t,error:s}=await r.from("business_profiles").select("id, trial_end_date, has_active_subscription, status").eq("id",e).single();if(s)return console.error("Profile error:",s),null;let{data:a,error:n}=await r.from("payment_subscriptions").select(`
        id,
        business_profile_id,
        razorpay_subscription_id,
        subscription_status,
        plan_id,
        plan_cycle,
        last_payment_method,
        subscription_start_date,
        subscription_expiry_time,
        cancelled_at
      `).eq("business_profile_id",e).single();return n&&"PGRST116"!==n.code&&console.error("Subscription error:",n),{userId:e,currentPlanId:a?.plan_id,currentPlanCycle:a?.plan_cycle,subscriptionStatus:a?.subscription_status,trialEndDate:t.trial_end_date,razorpaySubscriptionId:a?.razorpay_subscription_id,lastPaymentMethod:a?.last_payment_method,hasActiveSubscription:t.has_active_subscription}}catch(e){return console.error("Error getting subscription context:",e),null}}async function c(e,r){try{let{createSubscription:r}=await Promise.resolve().then(t.bind(t,33292)),s=await r(e.planId,e.planCycle);return{success:s.success,subscriptionId:"string"==typeof s.data?.subscription_id?s.data.subscription_id:void 0,paymentRequired:!0,message:s.success?"Subscription created successfully. Payment authorization required.":"Failed to create subscription",error:s.error,data:s.data}}catch(e){return console.error("Upfront payment flow error:",e),{success:!1,subscriptionId:void 0,paymentRequired:!1,message:"Failed to create subscription",error:e instanceof Error?e.message:"Unknown error"}}}async function u(e,r){if(!e.context.razorpaySubscriptionId)return console.error("Missing razorpaySubscriptionId in context for create and cancel flow"),{success:!1,subscriptionId:void 0,paymentRequired:!1,message:"Failed to process plan change: Current subscription ID not found.",error:"Missing razorpaySubscriptionId in context"};try{let{cancelAndCreateSubscription:r}=await Promise.resolve().then(t.bind(t,33292)),s=await r(e.context.razorpaySubscriptionId,e.planId,e.planCycle);return{success:s.success,subscriptionId:"string"==typeof s.data?.subscription_id?s.data.subscription_id:void 0,paymentRequired:!0,message:s.success?"New subscription created. Your current subscription will be cancelled after activation.":"Failed to create new subscription",error:s.error,data:s.data}}catch(e){return console.error("Create and cancel flow error:",e),{success:!1,subscriptionId:void 0,paymentRequired:!1,message:"Failed to create new subscription",error:e instanceof Error?e.message:"Unknown error"}}}async function d(e,r){try{let{createSubscription:s}=await Promise.resolve().then(t.bind(t,33292)),i=await s(e.planId,e.planCycle);return{success:i.success,subscriptionId:"string"==typeof i.data?.id?i.data.id:"string"==typeof i.data?.subscription_id?i.data.subscription_id:void 0,paymentRequired:r.requiresUpfrontPayment,message:i.success?"TRIAL_END"===r.paymentTiming?"Subscription created. Payment will be processed when trial ends.":"Subscription created successfully.":"Failed to create subscription",error:i.error,data:i.data}}catch(e){return console.error("Fresh subscription flow error:",e),{success:!1,subscriptionId:void 0,paymentRequired:!1,message:"Failed to create subscription",error:e instanceof Error?e.message:"Unknown error"}}}(0,t(17478).D)([n]),(0,s.A)(n,"60434c2bb58ea98948e04f9984c2dd28d0b1a0b75b",null)},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},12463:(e,r,t)=>{t.d(r,{b:()=>i});let s={FREE:"free",BASIC:"basic",GROWTH:"growth",PRO:"pro",ENTERPRISE:"enterprise"};class i{static determineSubscriptionFlow(e){let{context:r}=e,{trialEndDate:t,razorpaySubscriptionId:s}=r,i=this.isPostTrial(t),a=!!s;return i&&!a?{flowType:"UPFRONT_PAYMENT",requiresUpfrontPayment:!0,shouldCancelExisting:!1,shouldUpdateExisting:!1,paymentTiming:"IMMEDIATE",reason:"Post-trial user with no active subscription - requires upfront payment"}:a?{flowType:"CREATE_AND_CANCEL",requiresUpfrontPayment:!0,shouldCancelExisting:!0,shouldUpdateExisting:!1,paymentTiming:"IMMEDIATE",reason:"Active subscription - create new and cancel old (simplified flow for all payment methods)"}:{flowType:"FRESH_SUBSCRIPTION",requiresUpfrontPayment:!this.isOnTrial(t),shouldCancelExisting:!1,shouldUpdateExisting:!1,paymentTiming:this.isOnTrial(t)?"TRIAL_END":"IMMEDIATE",reason:"Fresh subscription - new user or trial user"}}static isPostTrial(e){return!!e&&new Date(e)<=new Date}static isOnTrial(e){return!!e&&new Date(e)>new Date}static validateRequest(e){let{planId:r,planCycle:t,context:i}=e;return Object.values(s).includes(r)?["monthly","yearly"].includes(t)?i.userId?{valid:!0}:{valid:!1,error:"User ID is required"}:{valid:!1,error:"Invalid plan cycle"}:{valid:!1,error:"Invalid plan ID"}}static getFlowDescription(e){switch(e.flowType){case"UPFRONT_PAYMENT":return"Payment will be processed immediately and subscription activated upon successful payment.";case"CREATE_AND_CANCEL":return"A new subscription will be created and your current subscription will be cancelled after activation.";case"FRESH_SUBSCRIPTION":return"TRIAL_END"===e.paymentTiming?"Subscription will be created and payment will be processed when your trial ends.":"New subscription will be created and activated immediately.";default:return"Subscription will be processed according to your current plan status."}}}},27910:e=>{e.exports=require("stream")},29038:(e,r,t)=>{t.r(r),t.d(r,{"00d4d8ffb0bd97f113de12ba4267247cf5a3b0b04c":()=>a.HW,"00efacc006752966ec1e7203fd5cdd059c8b2b6e30":()=>a.Ve,"401c3b86049e891affa0506ade0f31baeb2c3d455d":()=>a.GN,"404c603e0f39faf7ce2bc96725f060ce4e5faa5728":()=>a.bh,"4055b2843ad80548c3c3a2f5c81868718477c83ed4":()=>a.Oe,"40fb7d10f8dedc0c32f3187581b16bbca4c23379d6":()=>a.WX,"40fea71ecc67261c02da2d172b819e0eef02d3b41a":()=>a.$y,"602fc42cf7faa777c98df3e27fa695393e5d8fdce6":()=>i.createSubscription,"6034db8eca1b50b78925a7248b950085ef2be979fc":()=>a.EP,"60434c2bb58ea98948e04f9984c2dd28d0b1a0b75b":()=>s.d,"609589fd4be3c72f3931a1f9c536368933ba8b83da":()=>a.h,"609ab29520f078ddd91513228ac9d4ec796f522cb0":()=>a.m3,"60f426948b8391e4c3e4b292fc9538ce7045fbe366":()=>a.lC,"700d6b9fefc03908325de61c64af56a6cbddb19429":()=>i.cancelAndCreateSubscription});var s=t(2681),i=t(33292),a=t(27659)},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33292:(e,r,t)=>{t.d(r,{cancelAndCreateSubscription:()=>u,createSubscription:()=>c});var s=t(67218);t(79130);var i=t(31546),a=t(27659),n=t(79209);async function o(e,r,s,o){let c;try{c=(0,n.qD)(r,s)}catch(e){return(0,a.WX)(e instanceof Error?e.message:"Invalid plan selected")}let u=await Promise.resolve().then(t.bind(t,32032)).then(e=>e.createClient()),{data:{user:d},error:l}=await u.auth.getUser();if(l||!d)return(0,a.WX)("User not authenticated");let{data:p,error:b}=await u.from("business_profiles").select("business_name, contact_email, phone, trial_end_date").eq("id",e).single();if(b)return console.error("Error fetching profile:",b),(0,a.WX)("Error fetching user profile");let f=null,m=p.contact_email||d.email||"";if(m){let{findCustomerByEmail:r,createCustomer:s}=await t.e(8235).then(t.bind(t,8235)),i=await r(m);if(i.success&&i.data)f=i.data.id;else{let r=await s({name:p.business_name||d.user_metadata?.full_name||"Customer",email:m,contact:p.phone||"",notes:{user_id:e,business_name:p.business_name||""}});r.success&&r.data?f=r.data.id:console.error("Failed to create customer:",r.error)}}let _={plan_id:c,total_count:"monthly"===s?120:10,customer_notify:!0,notes:{business_profile_id:e,plan_type:r,plan_cycle:s},start_at:void 0,...f&&{customer_id:f}};o&&o>new Date&&(_.start_at=Math.floor(o.getTime()/1e3));let y=await (0,i.createSubscription)(_);return y.success?(0,a.$y)({...y.data,requires_authorization:!0,message:"Please complete payment authorization to activate your subscription."}):(console.error(`Error creating subscription: ${y.error}`),(0,a.WX)(String(y.error||"Unknown error")))}async function c(e,r){let{user:s,profile:i,error:n}=await (0,a.GN)("has_active_subscription, trial_end_date");if(n)return(0,a.WX)(n);if(i&&i.has_active_subscription){let e=await Promise.resolve().then(t.bind(t,32032)).then(e=>e.createClient()),{data:r,error:i}=await e.from("payment_subscriptions").select("subscription_status").eq("business_profile_id",s?.id||"").eq("subscription_status","halted").maybeSingle();return(i&&console.error("Error checking for halted subscription:",i),r)?(0,a.WX)("You have a paused subscription. Please resume your existing subscription before creating a new one."):(0,a.WX)("User already has an active subscription")}let c=i?.trial_end_date?new Date(i.trial_end_date):null,u=!!i&&(0,a.lC)(i);if(!s)return(0,a.WX)("User not found");let d=await o(s.id,e,r,u&&c?c:void 0);return((0,a.Ve)(),d.success&&d.data)?"requires_authorization"in d.data?d:(0,a.$y)({...d.data,requires_authorization:!0,message:"Please complete payment authorization to activate your subscription."}):!d.success&&d.error&&(console.error(`Error creating subscription: ${d.error}`),"string"==typeof d.error&&(d.error.includes("subscription_already_exists")||d.error.includes("SUBSCRIPTION_ALREADY_EXIST"))&&d.data)?(0,a.$y)({...d.data,requires_authorization:!0,message:"Please complete payment authorization for your existing subscription."}):d}async function u(e,r,s){let o,{user:c,error:u}=await (0,a.GN)();if(u)return(0,a.WX)(u);if(!c)return(0,a.WX)("User not found");let d=await Promise.resolve().then(t.bind(t,32032)).then(e=>e.createClient()),{data:l,error:p}=await d.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",c.id).eq("razorpay_subscription_id",e).maybeSingle();if(p)return console.error("Error fetching subscription:",p),(0,a.WX)("Error fetching subscription details");if(!l?.razorpay_subscription_id)return(0,a.WX)("Subscription does not belong to user");let{data:b,error:f}=await d.from("business_profiles").select("business_name, contact_email, phone, trial_end_date").eq("id",c.id).single();if(f)return console.error("Error fetching profile:",f),(0,a.WX)("Error fetching user profile");let m=b?.trial_end_date?new Date(b.trial_end_date):null,_=m&&m>new Date,y=null,h=b.contact_email||c.email||"";if(h){let{findCustomerByEmail:e,createCustomer:r}=await t.e(8235).then(t.bind(t,8235)),s=await e(h);if(s.success&&s.data)console.log("[CANCEL_AND_CREATE] Found existing customer:",y=s.data.id);else{let e=await r({name:b.business_name||"Customer",email:h,contact:b.phone||"",notes:{user_id:c.id,business_name:b.business_name||""}});e.success&&e.data?console.log("[CANCEL_AND_CREATE] Created new customer:",y=e.data.id):console.error("Failed to create customer:",e.error)}}try{o=(0,n.qD)(r,s)}catch(e){return(0,a.WX)(e instanceof Error?e.message:"Invalid plan selected")}let w={plan_id:o,total_count:"monthly"===s?120:10,customer_notify:!0,notes:{business_profile_id:c.id,old_subscription_id:l.razorpay_subscription_id,plan_type:r,plan_cycle:s,is_plan_switch:"true"},start_at:void 0,...y&&{customer_id:y}};_&&m&&(w.start_at=Math.floor(m.getTime()/1e3));let g=await (0,i.createSubscription)(w);return g.success&&g.data?.id?((0,a.Ve)(),(0,a.$y)({message:"New subscription created. Please complete the payment authorization. Your previous subscription will be cancelled automatically after successful activation.",subscription_id:g.data.id,short_url:g.data.short_url,requires_authorization:!0})):(console.error("Error creating new subscription:",g.error),(0,a.WX)("Could not create new subscription"))}(0,t(17478).D)([c,u]),(0,s.A)(c,"602fc42cf7faa777c98df3e27fa695393e5d8fdce6",null),(0,s.A)(u,"700d6b9fefc03908325de61c64af56a6cbddb19429",null)},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},58794:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>b,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>p});var s={};t.r(s),t.d(s,{POST:()=>u});var i=t(96559),a=t(48088),n=t(37719),o=t(32190),c=t(2681);async function u(e){try{let{planId:r,planCycle:t}=await e.json();if(!r||!t)return o.NextResponse.json({success:!1,error:"Missing required parameters: planId and planCycle"},{status:400});if(!["monthly","yearly"].includes(t))return o.NextResponse.json({success:!1,error:"Invalid plan cycle. Must be 'monthly' or 'yearly'"},{status:400});let s=await (0,c.d)(r,t);if(s.success)return o.NextResponse.json({success:!0,message:s.message,data:{subscription_id:s.subscriptionId,payment_required:s.paymentRequired,...s.data}});return o.NextResponse.json({success:!1,error:s.error||s.message},{status:400})}catch(e){return console.error("Error processing subscription:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Internal server error"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/subscriptions/centralized/route",pathname:"/api/subscriptions/centralized",filename:"route",bundlePath:"app/api/subscriptions/centralized/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\centralized\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:p,serverHooks:b}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:p})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},95453:(e,r,t)=>{t.d(r,{ST:()=>a,bG:()=>o,t6:()=>c});var s=t(55511),i=t.n(s);let a="https://api.razorpay.com/v2",n=()=>{let e,r;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",r=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!r)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!r,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:r}},o=()=>{let{keyId:e,keySecret:r}=n(),t=Buffer.from(`${e}:${r}`).toString("base64");return{Authorization:`Basic ${t}`,"Content-Type":"application/json"}},c=(e,r,t)=>{try{let s=i().createHmac("sha256",t).update(e).digest("hex");return i().timingSafeEqual(Buffer.from(r),Buffer.from(s))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,9398,4386,6724,580,2186,1546,9209,7365],()=>t(58794));module.exports=s})();