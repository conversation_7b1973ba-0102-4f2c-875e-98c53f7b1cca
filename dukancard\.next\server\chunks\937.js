"use strict";exports.id=937,exports.ids=[937],exports.modules={50937:(t,e,r)=>{function n(t){if(!t||"string"!=typeof t)throw Error(`Invalid userId: expected string, got ${typeof t}. Value: ${t}`);if(t.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${t}`);let e=t.substring(0,2).toLowerCase(),r=t.substring(2,4).toLowerCase();return`users/${e}/${r}/${t}`}function a(t,e,r,a){let $=n(t);return`${$}/products/${e}/base/image_${r}_${a}.webp`}function $(t,e,r,a,$){let o=n(t);return`${o}/products/${e}/${r}/image_${a}_${$}.webp`}function o(t,e,r,a,$){let o=n(t),s=$?new Date($):new Date,u=s.getFullYear(),i=String(s.getMonth()+1).padStart(2,"0");return`${o}/posts/${u}/${i}/${e}/image_${r}_${a}.webp`}function s(t,e,r){let a=n(t),$=new Date(r),o=$.getFullYear(),s=String($.getMonth()+1).padStart(2,"0");return`${a}/posts/${o}/${s}/${e}`}function u(t,e){let r=n(t);return`${r}/avatar/avatar_${e}.webp`}function i(t,e,r,a,$){let o=n(t),s=$?new Date($):new Date,u=s.getFullYear(),i=String(s.getMonth()+1).padStart(2,"0");return`${o}/posts/${u}/${i}/${e}/image_${r}_${a}.webp`}function l(t,e){let r=n(t);return`${r}/ads/custom_ad_${e}.webp`}r.d(e,{EK:()=>s,JU:()=>l,RE:()=>o,Vl:()=>$,getScalableUserPath:()=>n,jA:()=>a,jt:()=>i,tS:()=>u})}};