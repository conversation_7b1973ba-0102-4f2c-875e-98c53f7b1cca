"use strict";exports.id=8485,exports.ids=[8485],exports.modules={7885:(e,s,t)=>{t.d(s,{Z:()=>o});var i=t(65193);class r{validateWebhookSequence(e,s){let t=this.getValidStateTransitions(),i=this.isValidStateTransition(s.subscription_status,e.eventType,s.plan_id,t);return i.valid?{should:!0,reason:`STATE APPROVED: Valid transition from ${s.subscription_status} via ${e.eventType} - ${i.businessRule}`}:(console.warn(`[STATE_SEQUENCE] REJECTED: ${i.reason}`),{should:!1,reason:`STATE REJECTION: ${i.reason} - ${i.businessRule}`})}getValidStateTransitions(){return{trial:{allowedEvents:["subscription.authenticated","subscription.activated","subscription.cancelled","subscription.expired","subscription.updated"],description:"Trial users can authenticate, activate, cancel, or expire"},authenticated:{allowedEvents:["subscription.activated","subscription.cancelled","subscription.halted","subscription.charged","subscription.expired","subscription.updated"],description:"Authenticated subscriptions can activate, cancel, halt, charge, or expire"},active:{allowedEvents:["subscription.charged","subscription.cancelled","subscription.halted","subscription.expired","subscription.completed","subscription.updated"],description:"Active subscriptions can be charged, cancelled, halted, expired, or completed"},pending:{allowedEvents:["subscription.authenticated","subscription.activated","subscription.cancelled","subscription.expired","subscription.updated"],description:"Pending subscriptions can authenticate, activate, cancel, or expire"},halted:{allowedEvents:["subscription.cancelled","subscription.expired","subscription.activated","subscription.updated"],description:"Halted subscriptions can be cancelled, expired, or reactivated"},cancelled:{allowedEvents:[],description:"TERMINAL: Cancelled subscriptions cannot transition to any other state"},expired:{allowedEvents:[],description:"TERMINAL: Expired subscriptions cannot transition to any other state"},completed:{allowedEvents:[],description:"TERMINAL: Completed subscriptions cannot transition to any other state"}}}isValidStateTransition(e,s,t,i){if("trial"===e,"trial"===e&&"free"===t){let t=i[e];if(t&&t.allowedEvents.includes(s))return{valid:!0,reason:"Valid transition for fresh trial user",businessRule:t.description}}let r=i[e];return r?0===r.allowedEvents.length?{valid:!1,reason:`Terminal state reached: ${e}`,businessRule:r.description}:r.allowedEvents.includes(s)?{valid:!0,reason:`Valid transition from ${e} via ${s}`,businessRule:r.description}:{valid:!1,reason:`Invalid transition from ${e} via ${s}`,businessRule:`${r.description}. Allowed events: ${r.allowedEvents.join(", ")}`}:{valid:!1,reason:`Unknown current state: ${e}`,businessRule:"State not defined in transition matrix"}}}let n=new r;class a{async validateWebhookEventOrder(e,s){try{if(!s.last_webhook_timestamp)return{shouldProcess:!0,reason:"First webhook for subscription"};let t=new Date(s.last_webhook_timestamp).getTime()/1e3,i=e.webhookTimestamp,r=i-t;if("subscription.authenticated"===e.eventType&&"trial"===s.subscription_status&&"free"!==s.plan_id&&s.cancelled_at)return console.warn(`[WEBHOOK_ORDER] Rejecting authenticated webhook for cancelled subscription ${e.subscriptionId} - likely came after cancellation`),{shouldProcess:!1,reason:`Authenticated webhook rejected: subscription is in trial state (likely cancelled), plan: ${s.plan_id}`};if(r<-30)return{shouldProcess:!1,reason:`Webhook timestamp ${i} is ${Math.abs(r)} seconds older than last processed webhook ${t}`};return{shouldProcess:!0,reason:r>=0?`Webhook is newer than last processed (${r}s difference)`:`Webhook is within tolerance (${r}s difference, tolerance: 30s)`}}catch(e){return console.error("[WEBHOOK_ORDER] Error validating webhook order:",e),{shouldProcess:!0,reason:"Error during validation, allowing processing"}}}async shouldProcessEvent(e,s){let t=n.validateWebhookSequence(e,s);if(!t.should)return t;let r=i.SubscriptionStateManager.isTerminalStatus(s.subscription_status)||"free"===s.plan_id,a="halted"===s.subscription_status,o="subscription.activated"===e.eventType;if(r&&!["subscription.cancelled","subscription.expired","subscription.completed","subscription.halted"].includes(e.eventType)&&(!a||!o))return{should:!1,reason:`Subscription is in terminal state (${s.subscription_status}, plan: ${s.plan_id}), ignoring ${e.eventType} event`};return("subscription.cancelled"===e.eventType&&s.subscription_status===i.SUBSCRIPTION_STATUS.CANCELLED,s.subscription_status===i.SUBSCRIPTION_STATUS.TRIAL&&("subscription.authenticated"===e.eventType||e.eventType),"free"===s.plan_id&&("subscription.authenticated"===e.eventType||e.eventType),"subscription.activated"===e.eventType&&s.subscription_status===i.SUBSCRIPTION_STATUS.ACTIVE)?{should:!1,reason:"Subscription is already active"}:{should:!0,reason:"Event should be processed"}}}let o=new a},28485:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{webhookProcessor:()=>l});var r=t(32032),n=t(90258),a=t(29743),o=t(7885),c=e([n,a]);[n,a]=c.then?(await c)():c;class u{constructor(){this.adminClient=null}async getAdminClient(){return this.adminClient||(this.adminClient=await (0,r.createClient)()),this.adminClient}async processWebhookEvent(e){try{if(await a.K.checkEventIdempotency(e.eventId))return{success:!0,message:"Event already processed",shouldProcess:!1};let s=await n.D.getCurrentSubscriptionState(e.subscriptionId);if(!s){if(console.warn(`[WEBHOOK_PROCESSOR] Subscription ${e.subscriptionId} not found in database. This could be a race condition or the subscription was created outside our system.`),!this.shouldCreateMissingSubscription(e.eventType))return{success:!0,message:`Subscription not found for event ${e.eventType} - this may be expected`,shouldProcess:!1};{let t=await n.D.createMissingSubscriptionRecord(e);if(!t)return console.error(`[WEBHOOK_PROCESSOR] Failed to create missing subscription record for ${e.subscriptionId}`),{success:!1,message:"Subscription not found and could not be created",shouldProcess:!1};s=t}}if(s){let t=await o.Z.validateWebhookEventOrder(e,s);if(!t.shouldProcess)return console.warn(`[WEBHOOK_PROCESSOR] Rejecting out-of-order webhook: ${t.reason}`),{success:!0,message:`Webhook rejected: ${t.reason}`,shouldProcess:!1};let i=await o.Z.shouldProcessEvent(e,s);if(!i.should)return{success:!0,message:i.reason,shouldProcess:!1}}return await a.K.markEventAsProcessing(e.eventId,e.eventType),{success:!0,message:"Event validated and ready for processing",shouldProcess:!0}}catch(s){return console.error(`[WEBHOOK_PROCESSOR] Error processing webhook event ${e.eventId}:`,s),{success:!1,message:`Error processing webhook: ${s instanceof Error?s.message:String(s)}`,shouldProcess:!1}}}shouldCreateMissingSubscription(e){return["subscription.authenticated","subscription.activated","subscription.charged","subscription.halted","subscription.cancelled"].includes(e)}async markEventAsSuccess(e,s="Successfully processed"){await a.K.markEventAsSuccess(e,s)}async markEventAsFailed(e,s){await a.K.markEventAsFailed(e,s)}async updateSubscriptionStatus(e,s,t={},i){return await n.D.updateSubscriptionStatus(e,s,t,i)}}let l=new u;i()}catch(e){i(e)}})},29743:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{K:()=>n});var r=t(32032);class e{constructor(e){this.supabase=e}static async build(){let s=await (0,r.createClient)();return new e(s)}async checkEventIdempotency(e){let{data:s,error:t}=await this.supabase.from("processed_webhook_events").select("event_id").eq("event_id",e).in("status",["processed","success"]).maybeSingle();return t?(console.error("[EventManager] Error checking event idempotency:",t),!1):!!s}async markEventAsProcessing(e,s){let{error:t}=await this.supabase.from("processed_webhook_events").upsert({event_id:e,event_type:s,status:"processing",processed_at:new Date().toISOString()});t&&console.error("[EventManager] Error marking event as processing:",t)}async markEventAsSuccess(e,s="Successfully processed"){let{error:t}=await this.supabase.from("processed_webhook_events").update({status:"processed",processed_at:new Date().toISOString(),error_message:null,notes:s}).eq("event_id",e);t&&console.error("[EventManager] Error marking event as success:",t)}async markEventAsFailed(e,s){let{error:t}=await this.supabase.from("processed_webhook_events").update({status:"failed",processed_at:new Date().toISOString(),error_message:s}).eq("event_id",e);t&&console.error("[EventManager] Error marking event as failed:",t)}}let n=await e.build();i()}catch(e){i(e)}},1)},90258:(e,s,t)=>{t.a(e,async(e,i)=>{try{t.d(s,{D:()=>a});var r=t(32032),n=t(65193);class e{constructor(e){this.supabase=e}static async build(){let s=await (0,r.createClient)();return new e(s)}async getCurrentSubscriptionState(e){let{data:s,error:t}=await this.supabase.from("payment_subscriptions").select("id, business_profile_id, subscription_status, plan_id, cancelled_at, razorpay_subscription_id, updated_at, last_webhook_timestamp").eq("razorpay_subscription_id",e).maybeSingle();return t?(console.error("[SubscriptionManager] Error fetching subscription state:",t),null):s}async createMissingSubscriptionRecord(e){try{let s=e.payload,t=s?.payload?.subscription?.entity||s?.subscription?.entity;if(!t)return console.error(`[SubscriptionManager] No subscription data in webhook payload for ${e.subscriptionId}`),null;let i=t.notes?.business_profile_id||t.notes?.user_id;if(!i)return console.error(`[SubscriptionManager] No business_profile_id in subscription notes for ${e.subscriptionId}`),null;let{data:r,error:n}=await this.supabase.from("business_profiles").select("id").eq("id",i).single();if(n||!r)return console.error(`[SubscriptionManager] Business profile ${i} not found for subscription ${e.subscriptionId}`),null;let a=t.notes?.plan_type||"basic",o=t.notes?.plan_cycle||"monthly",c="created";"subscription.authenticated"===e.eventType?c="authenticated":"subscription.activated"===e.eventType&&(c="active");let{data:u,error:l}=await this.supabase.from("payment_subscriptions").insert({business_profile_id:i,razorpay_subscription_id:e.subscriptionId,plan_id:a,plan_cycle:o,subscription_status:c,razorpay_customer_id:t.customer_id||null,subscription_start_date:t.current_start?new Date(1e3*t.current_start).toISOString():null,subscription_expiry_time:t.current_end?new Date(1e3*t.current_end).toISOString():null,subscription_charge_time:t.charge_at?new Date(1e3*t.charge_at).toISOString():null,last_webhook_timestamp:e.webhookTimestamp?new Date(1e3*e.webhookTimestamp).toISOString():null}).select().single();if(l)return console.error("[SubscriptionManager] Error creating missing subscription record:",l),null;return u}catch(e){return console.error("[SubscriptionManager] Error in createMissingSubscriptionRecord:",e),null}}async updateSubscriptionStatus(e,s,t={},i){try{let r=await this.getCurrentSubscriptionState(e);if(!r)return console.warn(`[SubscriptionManager] Subscription ${e} not found during status update`),{success:!1,message:"Subscription not found"};let a=n.SubscriptionStateManager.shouldHaveActiveSubscription(s,t.plan_id||r.plan_id||"free"),{data:o,error:c}=await this.supabase.rpc("update_subscription_atomic",{p_subscription_id:e,p_new_status:s,p_business_profile_id:r.business_profile_id,p_has_active_subscription:a,p_additional_data:t,p_webhook_timestamp:i?new Date(1e3*i).toISOString():null});if(c)return console.error("[SubscriptionManager] RPC error updating subscription:",c),{success:!1,message:c.message};if(!o?.success)return console.error("[SubscriptionManager] RPC function returned error:",o),{success:!1,message:o?.error||"Unknown RPC error"};return{success:!0,message:`Atomically updated subscription to ${s} with has_active_subscription=${a}`}}catch(e){return console.error("[SubscriptionManager] Exception updating subscription:",e),{success:!1,message:`Exception: ${e instanceof Error?e.message:String(e)}`}}}}let a=await e.build();i()}catch(e){i(e)}},1)}};