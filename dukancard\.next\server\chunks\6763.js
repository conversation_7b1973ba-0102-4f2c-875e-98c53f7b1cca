"use strict";exports.id=6763,exports.ids=[6763],exports.modules={91106:(e,r,s)=>{s.d(r,{ST:()=>o,bG:()=>n}),s(55511);let o="https://api.razorpay.com/v2",t=()=>{let e,r;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",r=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!r)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!r,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:r}},n=()=>{let{keyId:e,keySecret:r}=t(),s=Buffer.from(`${e}:${r}`).toString("base64");return{Authorization:`Basic ${s}`,"Content-Type":"application/json"}}},96763:(e,r,s)=>{s.d(r,{cancelSubscription:()=>c,createSubscription:()=>t,getSubscription:()=>n,pauseSubscription:()=>a,fK:()=>u,nV:()=>i});var o=s(91106);async function t(e){try{let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions`,{method:"POST",headers:{...r,"Content-Type":"application/json"},body:JSON.stringify(e)}),t=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error creating subscription:",t),{success:!1,error:t};return{success:!0,data:t}}catch(e){return console.error("[RAZORPAY_ERROR] Exception creating subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function n(e){try{let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}`,{method:"GET",headers:r}),t=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error fetching subscription:",t),{success:!1,error:t};return{success:!0,data:t}}catch(e){return console.error("[RAZORPAY_ERROR] Exception fetching subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function c(e,r=!1){try{console.log(`[RAZORPAY_DEBUG] Cancelling subscription with ID: ${e}`);let s=(0,o.bG)(),t=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}/cancel`,{method:"POST",headers:{...s,"Content-Type":"application/json"},body:JSON.stringify(r?{cancel_at_cycle_end:1}:{})}),n=await t.json();if(!t.ok)return console.error("[RAZORPAY_ERROR] Error cancelling subscription:",n),{success:!1,error:n};return console.log(`[RAZORPAY_DEBUG] Successfully cancelled subscription: ${n.id}`),{success:!0,data:n}}catch(e){return console.error("[RAZORPAY_ERROR] Exception cancelling subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function i(e,r){try{console.log(`[RAZORPAY_DEBUG] Updating subscription with ID: ${e}`);let s=(0,o.bG)(),t=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}`,{method:"PATCH",headers:{...s,"Content-Type":"application/json"},body:JSON.stringify(r)}),n=await t.json();if(!t.ok)return console.error("[RAZORPAY_ERROR] Error updating subscription:",n),{success:!1,error:n};return console.log(`[RAZORPAY_DEBUG] Successfully updated subscription: ${n.id}`),{success:!0,data:n}}catch(e){return console.error("[RAZORPAY_ERROR] Exception updating subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function a(e,r="now",s=!1){let t;for(let n=1;n<=3;n++)try{console.log(`[RAZORPAY_DEBUG] Pausing subscription with ID: ${e} (attempt ${n}/3)`);let c=(0,o.bG)(),i=s?{}:{pause_at:r};console.log("[RAZORPAY_DEBUG] Request body for pause:",i);let a=new AbortController,u=setTimeout(()=>a.abort(),3e4),R=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}/pause`,{method:"POST",headers:{...c,"Content-Type":"application/json"},body:JSON.stringify(i),signal:a.signal});clearTimeout(u);let p=await R.json();if(!R.ok){if(console.error(`[RAZORPAY_ERROR] Error pausing subscription (attempt ${n}):`,p),R.status>=400&&R.status<500||(t=p,3===n))return{success:!1,error:p};let e=1e3*Math.pow(2,n-1);console.log(`[RAZORPAY_DEBUG] Retrying in ${e}ms...`),await new Promise(r=>setTimeout(r,e));continue}return console.log(`[RAZORPAY_DEBUG] Successfully paused subscription: ${p.id}`),{success:!0,data:p}}catch(e){if(console.error(`[RAZORPAY_ERROR] Exception pausing subscription (attempt ${n}):`,e),t=e,e instanceof Error&&"AbortError"===e.name)return{success:!1,error:{message:"Request timeout - please try again",code:"TIMEOUT_ERROR",type:"EXCEPTION"}};if(n<3){let e=1e3*Math.pow(2,n-1);console.log(`[RAZORPAY_DEBUG] Network error, retrying in ${e}ms...`),await new Promise(r=>setTimeout(r,e));continue}}return{success:!1,error:{message:t instanceof Error?t.message:"Network error after multiple retries",code:"NETWORK_ERROR",type:"EXCEPTION"}}}async function u(e){try{console.log(`[RAZORPAY_DEBUG] Resuming subscription with ID: ${e}`);let r=(0,o.bG)(),s=await fetch(`${o.ST.replace("/v2","/v1")}/subscriptions/${e}/resume`,{method:"POST",headers:{...r,"Content-Type":"application/json"},body:JSON.stringify({resume_at:"now"})}),t=await s.json();if(!s.ok)return console.error("[RAZORPAY_ERROR] Error resuming subscription:",t),{success:!1,error:t};return console.log(`[RAZORPAY_DEBUG] Successfully resumed subscription: ${t.id}`),{success:!0,data:t}}catch(e){return console.error("[RAZORPAY_ERROR] Exception resuming subscription:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}}};