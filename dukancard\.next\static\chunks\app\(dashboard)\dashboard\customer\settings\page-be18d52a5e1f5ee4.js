(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[418],{30070:(e,r,t)=>{"use strict";t.d(r,{C5:()=>g,MJ:()=>b,Rr:()=>p,eI:()=>h,lR:()=>f,lV:()=>o,zB:()=>u});var a=t(95155),s=t(12115),n=t(99708),i=t(62177),l=t(53999),d=t(82714);let o=i.Op,c=s.createContext({}),u=e=>{let{...r}=e;return(0,a.jsx)(c.Provider,{value:{name:r.name},children:(0,a.jsx)(i.xI,{...r})})},m=()=>{let e=s.useContext(c),r=s.useContext(x),{getFieldState:t}=(0,i.xW)(),a=(0,i.lN)({name:e.name}),n=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},x=s.createContext({});function h(e){let{className:r,...t}=e,n=s.useId();return(0,a.jsx)(x.Provider,{value:{id:n},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",r),...t})})}function f(e){let{className:r,...t}=e,{error:s,formItemId:n}=m();return(0,a.jsx)(d.J,{"data-slot":"form-label","data-error":!!s,className:(0,l.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...t})}function b(e){let{...r}=e,{error:t,formItemId:s,formDescriptionId:i,formMessageId:l}=m();return(0,a.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":t?"".concat(i," ").concat(l):"".concat(i),"aria-invalid":!!t,...r})}function p(e){let{className:r,...t}=e,{formDescriptionId:s}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,l.cn)("text-muted-foreground text-sm",r),...t})}function g(e){var r;let{className:t,...s}=e,{error:n,formMessageId:i}=m(),d=n?String(null!=(r=null==n?void 0:n.message)?r:""):s.children;return d?(0,a.jsx)("p",{"data-slot":"form-message",id:i,className:(0,l.cn)("text-destructive text-sm",t),...s,children:d}):null}},53999:(e,r,t)=>{"use strict";t.d(r,{M0:()=>c,Yq:()=>u,cn:()=>n,gV:()=>i,gY:()=>o,kY:()=>l,vA:()=>d,vv:()=>m});var a=t(52596),s=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}function i(e){if(!e)return null;let r=e.trim();return(r.startsWith("+91")?r=r.substring(3):12===r.length&&r.startsWith("91")&&(r=r.substring(2)),/^\d{10}$/.test(r))?r:null}function l(e){if(!e||e.length<4)return"Invalid Phone";let r=e.substring(0,2),t=e.substring(e.length-2),a="*".repeat(e.length-4);return"".concat(r).concat(a).concat(t)}function d(e){if(!e||!e.includes("@"))return"Invalid Email";let r=e.split("@"),t=r[0],a=r[1];if(t.length<=2||a.length<=2||!a.includes("."))return"Email Hidden";let s=t.substring(0,2)+"*".repeat(t.length-2),n=a.split("."),i=n[0],l=n.slice(1).join("."),d=i.substring(0,2)+"*".repeat(i.length-2);return"".concat(s,"@").concat(d,".").concat(l)}function o(e){if(null==e||isNaN(e))return"0";let r=Math.abs(e),t=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(r<1e5)return r>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let a=t.length-1;a>=0;a--)if(r>=t[a].value)return(e/t[a].value).toFixed(1).replace(/\.0$/,"")+t[a].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let t={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return r&&(t.hour="2-digit",t.minute="2-digit",t.hour12=!0),e.toLocaleString("en-IN",t)}function m(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:r,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(t){return"".concat(r," ").concat(e.toFixed(2))}}},69916:(e,r,t)=>{"use strict";t.d(r,{NV:()=>d,UV:()=>l,sF:()=>o});var a=t(95155),s=t(12115),n=t(1184),i=t(53999);function l(e){let{className:r,containerClassName:t,...s}=e;return(0,a.jsx)(n.wE,{"data-slot":"input-otp",containerClassName:(0,i.cn)("flex items-center gap-2 has-disabled:opacity-50",t),className:(0,i.cn)("disabled:cursor-not-allowed",r),...s})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"input-otp-group",className:(0,i.cn)("flex items-center",r),...t})}function o(e){var r;let{index:t,className:l,...d}=e,o=s.useContext(n.dK),{char:c,hasFakeCaret:u,isActive:m}=null!=(r=null==o?void 0:o.slots[t])?r:{};return(0,a.jsxs)("div",{"data-slot":"input-otp-slot","data-active":m,className:(0,i.cn)("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]",l),...d,children:[c,u&&(0,a.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-caret-blink bg-foreground h-4 w-px duration-1000"})})]})}},78969:(e,r,t)=>{Promise.resolve().then(t.bind(t,87164))},82714:(e,r,t)=>{"use strict";t.d(r,{J:()=>i});var a=t(95155);t(12115);var s=t(40968),n=t(53999);function i(e){let{className:r,...t}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},87164:(e,r,t)=>{"use strict";t.d(r,{default:()=>J});var a=t(95155),s=t(12115),n=t(28695),i=t(381),l=t(56671),d=t(34477);let o=(0,d.createServerReference)("00d2052c821136e9e2e8d8101d8866f4f3a3206603",d.callServer,void 0,d.findSourceMapURL,"checkDeleteAccountVerificationOptions"),c=(0,d.createServerReference)("00587c2eafaa8506ef7d1a09b6ceb2abfc5ab559fa",d.callServer,void 0,d.findSourceMapURL,"sendDeleteAccountOTP"),u=(0,d.createServerReference)("60890d22f4c7992f648c81b47a901bc0863dd3c988",d.callServer,void 0,d.findSourceMapURL,"verifyDeleteAccountOTP"),m=(0,d.createServerReference)("40393e24ab017bf6535b49b096bdca35f76e7cba5c",d.callServer,void 0,d.findSourceMapURL,"verifyDeleteAccountPassword"),x=(0,d.createServerReference)("00890d9a7f09ef9a0793405d70dcac6ff5a3417c20",d.callServer,void 0,d.findSourceMapURL,"deleteCustomerAccount");var h=t(97168),f=t(99840),b=t(89852),p=t(69916),g=t(35695),v=t(60760),j=t(62525),y=t(1243),N=t(51154),w=t(28883),k=t(46767),E=t(54861);function S(){let e=(0,g.useRouter)(),[r,t]=(0,s.useTransition)(),[i,d]=(0,s.useState)(""),[S,A]=(0,s.useState)(!1),[R,C]=(0,s.useState)(!1),[P,F]=(0,s.useState)("initial"),[V,L]=(0,s.useState)(!1),[T,D]=(0,s.useState)(!1),[_,I]=(0,s.useState)(""),[z,M]=(0,s.useState)(null),[O,U]=(0,s.useState)(""),[$,Y]=(0,s.useState)(""),[W,J]=(0,s.useState)(!1),[B,G]=(0,s.useState)(!1),[K,H]=(0,s.useState)(!1),[q,X]=(0,s.useState)(!1);(0,s.useEffect)(()=>{R&&"initial"===P&&Z()},[R,P]);let Z=async()=>{X(!0);try{let e=await o();e.success?(L(e.hasEmail),D(e.hasPhone),e.hasEmail&&e.hasPhone?F("choose-method"):e.hasEmail?(M("email"),F("email-otp")):e.hasPhone?(M("password"),F("password")):F("final-confirm")):l.oR.error(e.message||"Failed to check verification options")}catch(e){l.oR.error("An error occurred while checking verification options")}finally{X(!1)}},Q=e=>{M(e),"email"===e?F("email-otp"):F("password")},ee=async()=>{G(!0);try{let e=await c();if(e.success)l.oR.success(e.message),e.email&&I(e.email);else if(l.oR.error(e.message),e.isConfigurationError)return}catch(e){l.oR.error("Failed to send verification code")}finally{G(!1)}},er=async()=>{if(6!==O.length)return void l.oR.error("Please enter a valid 6-digit code");J(!0);try{let e=await u(_,O);e.success?(l.oR.success(e.message),H(!0),F("final-confirm")):l.oR.error(e.message)}catch(e){l.oR.error("Failed to verify code")}finally{J(!1)}},et=async()=>{if(!$.trim())return void l.oR.error("Please enter your password");J(!0);try{let e=await m($);e.success?(l.oR.success(e.message),H(!0),F("final-confirm")):l.oR.error(e.message)}catch(e){l.oR.error("Failed to verify password")}finally{J(!1)}},ea=()=>{d(""),C(!1),F("initial"),M(null),U(""),Y(""),H(!1),I(""),X(!1)};return(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"pb-6 border-b border-red-200/60 dark:border-red-700/60",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-red-500/10 to-red-500/5 border border-red-500/20",children:(0,a.jsx)(j.A,{className:"w-4 h-4 text-red-500"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-red-600 dark:text-red-400",children:"Delete Account"})]}),(0,a.jsx)("p",{className:"text-sm text-red-600/80 dark:text-red-400/80 leading-relaxed",children:"Permanently delete your account and all associated data."})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30 mb-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 text-red-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-red-700 dark:text-red-400",children:"Warning: This action cannot be undone"}),(0,a.jsx)("p",{className:"text-xs text-red-600/80 dark:text-red-400/80 mt-1",children:"Deleting your account will permanently remove all your data, including your saved cards, likes, reviews, and subscriptions. You will not be able to recover this information."})]})]})}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(h.$,{variant:"destructive",disabled:S,onClick:()=>C(!0),className:"px-4 py-2 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",type:"button",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]})}),(0,a.jsx)(f.lG,{open:R,onOpenChange:e=>{S||W||B||ea()},children:(0,a.jsxs)(f.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(f.c7,{className:"text-center pb-2",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20",children:(0,a.jsx)(n.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},children:(0,a.jsx)(j.A,{className:"h-8 w-8 text-red-500"})})}),(0,a.jsx)(f.L3,{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"choose-method"===P?"Verify Your Identity":"email-otp"===P?"Email Verification":"password"===P?"Password Verification":"Delete your account?"}),(0,a.jsx)(f.rr,{className:"text-gray-500 dark:text-gray-400 mt-2",children:"choose-method"===P?"Choose how you want to verify your identity before deleting your account.":"email-otp"===P?"We've sent a verification code to your email address.":"password"===P?"Please enter your current password to verify your identity.":"This action cannot be undone. All your data will be permanently removed."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[q&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 space-y-4",children:[(0,a.jsx)(N.A,{className:"h-8 w-8 animate-spin text-neutral-500"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Checking verification options..."})]}),!q&&"choose-method"===P&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mb-4",children:"For security, please verify your identity before proceeding:"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(h.$,{onClick:()=>Q("email"),variant:"outline",className:"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-blue-500"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-medium",children:"Email Verification"}),(0,a.jsx)("div",{className:"text-xs text-neutral-500",children:"Send OTP to your email"})]})]}),(0,a.jsxs)(h.$,{onClick:()=>Q("password"),variant:"outline",className:"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 text-green-500"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-medium",children:"Password Verification"}),(0,a.jsx)("div",{className:"text-xs text-neutral-500",children:"Enter your current password"})]})]})]})]}),!q&&"email-otp"===P&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(h.$,{onClick:ee,disabled:B,variant:"outline",className:"mb-4",children:B?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Sending..."]}):"Send Verification Code"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Enter 6-digit verification code:"}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(p.UV,{maxLength:6,value:O,onChange:U,className:"gap-2",children:(0,a.jsxs)(p.NV,{children:[(0,a.jsx)(p.sF,{index:0,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,a.jsx)(p.sF,{index:1,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,a.jsx)(p.sF,{index:2,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,a.jsx)(p.sF,{index:3,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,a.jsx)(p.sF,{index:4,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,a.jsx)(p.sF,{index:5,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"})]})})})]}),(0,a.jsx)(h.$,{onClick:er,disabled:6!==O.length||W,className:"w-full",children:W?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Verifying..."]}):"Verify Code"})]}),!q&&"password"===P&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Enter your current password:"}),(0,a.jsx)(b.p,{type:"password",value:$,onChange:e=>Y(e.target.value),placeholder:"Current password",className:"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"})]}),(0,a.jsx)(h.$,{onClick:et,disabled:!$.trim()||W,className:"w-full",children:W?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Verifying..."]}):"Verify Password"})]}),!q&&"final-confirm"===P&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-950/20 rounded-lg p-4 border border-red-100 dark:border-red-800/30",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-red-700 dark:text-red-400",children:"This will permanently delete:"}),(0,a.jsxs)("ul",{className:"text-xs text-red-600/80 dark:text-red-400/80 mt-1 space-y-0.5",children:[(0,a.jsx)("li",{children:"• Your saved business cards"}),(0,a.jsx)("li",{children:"• Your likes and subscriptions"}),(0,a.jsx)("li",{children:"• Your reviews and ratings"}),(0,a.jsx)("li",{children:"• Your account information"})]})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2",children:["Type ",(0,a.jsx)("span",{className:"font-bold text-red-500 dark:text-red-400",children:"DELETE"})," to confirm:"]}),(0,a.jsx)(b.p,{value:i,onChange:e=>d(e.target.value),placeholder:"DELETE",className:"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-red-500/30",disabled:S})]})]})]}),(0,a.jsxs)(f.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 pt-4",children:[(0,a.jsxs)(h.$,{type:"button",variant:"outline",onClick:()=>{ea()},disabled:S||W||B||q,className:"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200",children:[(0,a.jsx)(E.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),!q&&"final-confirm"===P&&(0,a.jsx)(n.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1",children:(0,a.jsx)(h.$,{type:"button",onClick:r=>(r.preventDefault(),"DELETE"!==i)?void l.oR.error('Please type "DELETE" to confirm'):(V||T)&&!K?void l.oR.error("Please complete verification first"):void(A(!0),t(async()=>{try{l.oR.success("Processing account deletion...");try{await x(),ea(),e.push("/")}catch(r){r instanceof Error&&r.message.includes("Cannot read properties of undefined")?(ea(),e.push("/")):(console.error("Error in account deletion:",r),l.oR.error("Failed to delete account"),A(!1))}}catch(e){console.error("Unexpected error during account deletion:",e),l.oR.error("An unexpected error occurred"),A(!1)}})),disabled:"DELETE"!==i||S,className:"\n                    w-full relative overflow-hidden\n                    bg-gradient-to-r from-red-500 to-red-600\n                    hover:from-red-600 hover:to-red-700\n                    text-white font-medium\n                    shadow-lg hover:shadow-xl\n                    transition-all duration-300\n                    before:absolute before:inset-0\n                    before:bg-gradient-to-r before:from-red-400 before:to-red-500\n                    before:opacity-0 hover:before:opacity-20\n                    before:transition-opacity before:duration-300\n                    ".concat("DELETE"!==i||S?"cursor-not-allowed opacity-80":"","\n                  "),style:{boxShadow:"DELETE"!==i||S?"0 4px 20px rgba(239, 68, 68, 0.3)":"0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)"},children:(0,a.jsx)(v.N,{mode:"wait",children:S?(0,a.jsxs)(n.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Deleting..."]},"deleting"):(0,a.jsxs)(n.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]},"delete")})})})]})]})})]})]})}var A=t(44940),R=t(81284),C=t(62177),P=t(90221),F=t(55594),V=t(30070),L=t(75525),T=t(40646);let D=(0,d.createServerReference)("00ac5331100c6eb661a835872f4aca4f1c0c57af60",d.callServer,void 0,d.findSourceMapURL,"sendEmailChangeOTP"),_=(0,d.createServerReference)("608844a615b87b85d4667dfe96d0b36222157db949",d.callServer,void 0,d.findSourceMapURL,"updateCustomerEmail"),I=(0,d.createServerReference)("60fe30a842b0e4f65cd95fe2d407dda8da860d82ff",d.callServer,void 0,d.findSourceMapURL,"confirmEmailChangeOTP"),z=(0,d.createServerReference)("6021663cdb43cf5374d99502948b3be7537d9a7970",d.callServer,void 0,d.findSourceMapURL,"updateCustomerEmailWithOTP"),M=F.z.object({email:F.z.string().email("Invalid email address.")}),O=F.z.object({otp:F.z.string().min(6,"OTP must be 6 digits.").max(6,"OTP must be 6 digits.")});function U(e){let{isOpen:r,onClose:t,currentEmail:i,onEmailUpdated:d}=e,[o,c]=(0,s.useTransition)(),[u,m]=(0,s.useState)("email_input"),[x,g]=(0,s.useState)(""),[v,j]=(0,s.useState)(""),[y,k]=(0,s.useState)(""),E=(0,C.mN)({resolver:(0,P.u)(M),defaultValues:{email:""}}),S=(0,C.mN)({resolver:(0,P.u)(O),defaultValues:{otp:""}}),A=()=>{m("email_input"),g(""),j(""),k(""),E.reset(),S.reset(),t()};return(0,a.jsx)(f.lG,{open:r,onOpenChange:e=>!e&&A(),children:(0,a.jsxs)(f.Cf,{className:"sm:max-w-md",hideClose:!1,children:[(0,a.jsxs)(f.c7,{children:[(0,a.jsxs)(f.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"w-5 h-5 text-primary"}),"Update Email Address"]}),(0,a.jsx)(f.rr,{children:i?"Verify your current email first, then set a new email address.":"Link an email address to your account for better security."})]}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"space-y-6",children:["email_input"===u&&(0,a.jsx)("div",{className:"space-y-4",children:i?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:"Security Verification Required"})]}),(0,a.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["To change your email address, we need to verify your current email: ",(0,a.jsx)("strong",{children:i})]})]}),(0,a.jsx)(h.$,{onClick:()=>{c(async()=>{try{let e=await D();e.success?(g(e.email||i||""),m("otp_verification"),k("We&apos;ve sent a 6-digit verification code to ".concat(e.email||i,".")),l.oR.success("Verification code sent to your current email!")):(l.oR.error(e.message||"Failed to send verification code."),k(e.message||"Failed to send verification code."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})},disabled:o,className:"w-full",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending Code..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Send Verification Code"]})})]}):(0,a.jsx)(V.lV,{...E,children:(0,a.jsxs)("form",{onSubmit:E.handleSubmit(e=>{c(async()=>{try{let r=new FormData;r.append("newEmail",e.email);let t=await _({message:null,success:!1},r);t.success?"otp_sent"===t.message?(g(e.email),m("otp_verification"),k("We&apos;ve sent a 6-digit verification code to ".concat(e.email,".")),l.oR.success("Verification code sent!")):(l.oR.success("Email linked successfully!"),A(),null==d||d()):(l.oR.error(t.message||"Failed to link email."),k(t.message||"Failed to link email."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,a.jsx)(V.zB,{control:E.control,name:"email",render:e=>{let{field:r}=e;return(0,a.jsxs)(V.eI,{children:[(0,a.jsx)(V.lR,{children:"Email Address"}),(0,a.jsx)(V.MJ,{children:(0,a.jsx)(b.p,{type:"email",placeholder:"Enter your email address",...r,disabled:o})}),(0,a.jsx)(V.C5,{})]})}}),(0,a.jsx)(h.$,{type:"submit",disabled:o,className:"w-full",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Linking Email..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Link Email Address"]})})]})})}),"otp_verification"===u&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full",children:(0,a.jsx)(w.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Check Your Email"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["We've sent a 6-digit code to ",(0,a.jsx)("strong",{children:x})]})]}),(0,a.jsx)(V.lV,{...S,children:(0,a.jsxs)("form",{onSubmit:S.handleSubmit(e=>{c(async()=>{try{let r=new FormData;r.append("email",x),r.append("otp",e.otp);let t=await I({email:x,otp:e.otp},r);t.success?i?(m("new_email_input"),k("OTP verified! Now enter your new email address."),l.oR.success("OTP verified! Enter your new email.")):(l.oR.success("Email verified and linked successfully!"),A(),null==d||d()):(l.oR.error(t.message||"Invalid OTP. Please try again."),k(t.message||"Invalid OTP. Please try again."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,a.jsx)(V.zB,{control:S.control,name:"otp",render:e=>{let{field:r}=e;return(0,a.jsxs)(V.eI,{children:[(0,a.jsx)(V.lR,{children:"Verification Code"}),(0,a.jsx)(V.MJ,{children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(p.UV,{maxLength:6,...r,disabled:o,children:(0,a.jsxs)(p.NV,{children:[(0,a.jsx)(p.sF,{index:0}),(0,a.jsx)(p.sF,{index:1}),(0,a.jsx)(p.sF,{index:2}),(0,a.jsx)(p.sF,{index:3}),(0,a.jsx)(p.sF,{index:4}),(0,a.jsx)(p.sF,{index:5})]})})})}),(0,a.jsx)(V.C5,{})]})}}),(0,a.jsx)(h.$,{type:"submit",disabled:o,className:"w-full",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Verifying..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.A,{className:"w-4 h-4 mr-2"}),"Verify Code"]})})]})})]}),"new_email_input"===u&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(T.A,{className:"w-4 h-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-900 dark:text-green-100",children:"Current Email Verified"})]}),(0,a.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:["Current email: ",(0,a.jsx)("strong",{children:i})]})]}),(0,a.jsx)(V.lV,{...E,children:(0,a.jsxs)("form",{onSubmit:E.handleSubmit(e=>{c(async()=>{try{let r=new FormData;r.append("email",e.email);let t=await z({message:null,success:!1},r);t.success?(l.oR.success("Email updated successfully!"),A(),null==d||d()):(l.oR.error(t.message||"Failed to update email."),k(t.message||"Failed to update email."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,a.jsx)(V.zB,{control:E.control,name:"email",render:e=>{let{field:r}=e;return(0,a.jsxs)(V.eI,{children:[(0,a.jsx)(V.lR,{children:"New Email Address"}),(0,a.jsx)(V.MJ,{children:(0,a.jsx)(b.p,{type:"email",placeholder:"Enter your new email address",...r,disabled:o})}),(0,a.jsx)(V.C5,{})]})}}),(0,a.jsx)(h.$,{type:"submit",disabled:o,className:"w-full",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Updating Email..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Update Email Address"]})})]})})]}),y&&(0,a.jsx)("div",{className:"p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,a.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:y})})]},u)]})})}function $(e){let{currentEmail:r,registrationType:t}=e,[i,l]=(0,s.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20",children:(0,a.jsx)(w.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-50",children:"Email Address"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:r?"Manage your account email address":"Link an email address for better security"})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 p-6 space-y-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 block",children:"Current Email Address"}),(0,a.jsx)(b.p,{type:"email",value:r||"",placeholder:r||"No email address linked",readOnly:!0,className:"bg-neutral-50 dark:bg-neutral-800 cursor-not-allowed"}),(0,a.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1",children:r?"This is your current email address":"No email address is currently linked to your account"})]}),(0,a.jsxs)(h.$,{onClick:()=>l(!0),className:"w-full sm:w-auto",variant:"default",children:[(0,a.jsx)(A.A,{className:"w-4 h-4 mr-2"}),r?"Update Email Address":"Link Email Address"]})]})}),"google"===t&&(0,a.jsx)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(R.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1",children:"Google Account Integration"}),(0,a.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"You signed up with Google. You can still link or update your email address for additional security options."})]})]})})]}),(0,a.jsx)(U,{isOpen:i,onClose:()=>l(!1),currentEmail:r,onEmailUpdated:()=>{window.location.reload()}})]})}var Y=t(19420);function W(e){let{currentPhone:r}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-green-500/10 to-green-500/5 border border-green-500/20",children:(0,a.jsx)(Y.A,{className:"w-4 h-4 text-green-500"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-100",children:"Phone Number"})]}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed",children:r?"Your current phone number linked to this account.":"No phone number is currently linked to your account."})]}),(0,a.jsx)("div",{className:"space-y-6",children:r?(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Current Phone Number"}),(0,a.jsx)("div",{className:"mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md",children:(0,a.jsx)("span",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:r})}),(0,a.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1",children:"Phone number changes are not currently supported. Contact support if you need to update your number."})]})}):(0,a.jsxs)("div",{className:"text-center p-6 rounded-lg bg-neutral-50 dark:bg-neutral-900/50 border border-neutral-200 dark:border-neutral-700",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 mx-auto mb-3 w-fit",children:(0,a.jsx)(Y.A,{className:"w-6 h-6"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-2",children:"No Phone Number"}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 max-w-sm mx-auto",children:"No phone number is currently linked to your account. Phone number linking is not available at this time."})]})})]})}function J(e){let{currentEmail:r,currentPhone:t,registrationType:d}=e;return(0,s.useEffect)(()=>{if("true"===new URLSearchParams(window.location.search).get("email_change_success")){l.oR.success("Email address updated successfully!",{description:"Your email address has been changed and verified.",duration:5e3});let e=window.location.pathname;window.history.replaceState({},"",e)}},[]),(0,a.jsxs)(n.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-8",children:[(0,a.jsx)(n.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,a.jsx)(i.A,{className:"w-5 h-5 text-primary"})}),(0,a.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Account Management"})]}),(0,a.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Account Settings"}),(0,a.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Manage your account security, contact information, and preferences. Keep your profile up to date."})]})}),(0,a.jsxs)("div",{className:"space-y-12",children:[(0,a.jsx)(n.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},children:(0,a.jsx)($,{currentEmail:r,currentPhone:t,registrationType:d})}),(0,a.jsx)(n.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3},children:(0,a.jsx)(W,{currentEmail:r,currentPhone:t,registrationType:d})}),(0,a.jsx)(n.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.5},children:(0,a.jsx)(S,{})})]})]})}},89852:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(95155);t(12115);var s=t(53999);function n(e){let{className:r,type:t,...n}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>l});var a=t(95155);t(12115);var s=t(99708),n=t(74466),i=t(53999);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:n,asChild:d=!1,...o}=e,c=d?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:n,className:r})),...o})}},99840:(e,r,t)=>{"use strict";t.d(r,{Cf:()=>m,Es:()=>h,HM:()=>c,L3:()=>f,c7:()=>x,lG:()=>l,rr:()=>b,zM:()=>d});var a=t(95155);t(12115);var s=t(45821),n=t(54416),i=t(53999);function l(e){let{...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"dialog",...r})}function d(e){let{...r}=e;return(0,a.jsx)(s.l9,{"data-slot":"dialog-trigger",...r})}function o(e){let{...r}=e;return(0,a.jsx)(s.ZL,{"data-slot":"dialog-portal",...r})}function c(e){let{...r}=e;return(0,a.jsx)(s.bm,{"data-slot":"dialog-close",...r})}function u(e){let{className:r,...t}=e;return(0,a.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",r),...t})}function m(e){let{className:r,children:t,hideClose:l=!1,...d}=e;return(0,a.jsxs)(o,{"data-slot":"dialog-portal",children:[(0,a.jsx)(u,{}),(0,a.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",r),...d,children:[t,!l&&(0,a.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",r),...t})}function h(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",r),...t})}function f(e){let{className:r,...t}=e;return(0,a.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",r),...t})}function b(e){let{className:r,...t}=e;return(0,a.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",r),...t})}}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,8695,2290,6671,375,1884,6199,221,9690,8441,1684,7358],()=>r(78969)),_N_E=e.O()}]);