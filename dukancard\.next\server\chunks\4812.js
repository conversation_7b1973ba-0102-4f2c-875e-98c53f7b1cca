"use strict";exports.id=4812,exports.ids=[937,4812,8640],exports.modules={28640:(e,t,a)=>{async function r(e,t={}){let{format:a="webp",targetSizeKB:n=100,maxDimension:s=800,quality:i=.8}=t;return new Promise((t,r)=>{let o=new Image;o.onload=()=>{try{let l=document.createElement("canvas"),d=l.getContext("2d");if(!d)return void r(Error("Could not get canvas context"));let{width:c,height:u}=o;(c>s||u>s)&&(c>u?(u=u*s/c,c=s):(c=c*s/u,u=s)),l.width=c,l.height=u,d.drawImage(o,0,0,c,u);let m=i,f=0,g=()=>{l.toBlob(a=>{if(!a)return void r(Error("Failed to create blob"));let s=a.size/1024;if(s<=n||f>=5||m<=.1){let r=e.size/a.size;t({blob:a,finalSizeKB:Math.round(100*s)/100,compressionRatio:Math.round(100*r)/100,dimensions:{width:c,height:u}})}else f++,m=Math.max(.1,m-.15),g()},`image/${a}`,m)};g()}catch(e){r(e)}},o.onerror=()=>r(Error("Failed to load image")),o.src=URL.createObjectURL(e)})}async function n(e,t={}){let a=e.size/1048576,s=100,i=800,o=.7;return a<=2?(o=.7,i=800,s=90):a<=5?(o=.55,i=700,s=80):a<=10?(o=.45,i=600,s=70):(o=.35,i=550,s=60),r(e,{...t,targetSizeKB:t.targetSizeKB||s,maxDimension:t.maxDimension||i,quality:t.quality||o})}async function s(e,t={}){return r(e,{targetSizeKB:50,maxDimension:400,quality:.7,...t})}a.d(t,{compressImageUltraAggressiveClient:()=>n,q:()=>s})},39390:(e,t,a)=>{a.d(t,{J:()=>i});var r=a(60687);a(43210);var n=a(78148),s=a(96241);function i({className:e,...t}){return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},50937:(e,t,a)=>{function r(e){if(!e||"string"!=typeof e)throw Error(`Invalid userId: expected string, got ${typeof e}. Value: ${e}`);if(e.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${e}`);let t=e.substring(0,2).toLowerCase(),a=e.substring(2,4).toLowerCase();return`users/${t}/${a}/${e}`}function n(e,t,a,n){let s=r(e);return`${s}/products/${t}/base/image_${a}_${n}.webp`}function s(e,t,a,n,s){let i=r(e);return`${i}/products/${t}/${a}/image_${n}_${s}.webp`}function i(e,t,a,n,s){let i=r(e),o=s?new Date(s):new Date,l=o.getFullYear(),d=String(o.getMonth()+1).padStart(2,"0");return`${i}/posts/${l}/${d}/${t}/image_${a}_${n}.webp`}function o(e,t,a){let n=r(e),s=new Date(a),i=s.getFullYear(),o=String(s.getMonth()+1).padStart(2,"0");return`${n}/posts/${i}/${o}/${t}`}function l(e,t){let a=r(e);return`${a}/avatar/avatar_${t}.webp`}function d(e,t,a,n,s){let i=r(e),o=s?new Date(s):new Date,l=o.getFullYear(),d=String(o.getMonth()+1).padStart(2,"0");return`${i}/posts/${l}/${d}/${t}/image_${a}_${n}.webp`}function c(e,t){let a=r(e);return`${a}/ads/custom_ad_${t}.webp`}a.d(t,{EK:()=>o,JU:()=>c,RE:()=>i,Vl:()=>s,getScalableUserPath:()=>r,jA:()=>n,jt:()=>d,tS:()=>l})},58164:(e,t,a)=>{a.d(t,{C5:()=>v,MJ:()=>x,Rr:()=>h,eI:()=>g,lR:()=>p,lV:()=>d,zB:()=>u});var r=a(60687),n=a(43210),s=a(8730),i=a(27605),o=a(96241),l=a(39390);let d=i.Op,c=n.createContext({}),u=({...e})=>(0,r.jsx)(c.Provider,{value:{name:e.name},children:(0,r.jsx)(i.xI,{...e})}),m=()=>{let e=n.useContext(c),t=n.useContext(f),{getFieldState:a}=(0,i.xW)(),r=(0,i.lN)({name:e.name}),s=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...s}},f=n.createContext({});function g({className:e,...t}){let a=n.useId();return(0,r.jsx)(f.Provider,{value:{id:a},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...t})})}function p({className:e,...t}){let{error:a,formItemId:n}=m();return(0,r.jsx)(l.J,{"data-slot":"form-label","data-error":!!a,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...t})}function x({...e}){let{error:t,formItemId:a,formDescriptionId:n,formMessageId:i}=m();return(0,r.jsx)(s.DX,{"data-slot":"form-control",id:a,"aria-describedby":t?`${n} ${i}`:`${n}`,"aria-invalid":!!t,...e})}function h({className:e,...t}){let{formDescriptionId:a}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:a,className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function v({className:e,...t}){let{error:a,formMessageId:n}=m(),s=a?String(a?.message??""):t.children;return s?(0,r.jsx)("p",{"data-slot":"form-message",id:n,className:(0,o.cn)("text-destructive text-sm",e),...t,children:s}):null}},58709:(e,t,a)=>{a.d(t,{A:()=>o});var r=a(60687),n=a(43210),s=a(24851),i=a(96241);function o({className:e,defaultValue:t,value:a,min:o=0,max:l=100,...d}){let c=n.useMemo(()=>Array.isArray(a)?a:Array.isArray(t)?t:[o,l],[a,t,o,l]);return(0,r.jsxs)(s.bL,{"data-slot":"slider",defaultValue:t,value:a,min:o,max:l,className:(0,i.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",e),...d,children:[(0,r.jsx)(s.CC,{"data-slot":"slider-track",className:(0,i.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,r.jsx)(s.Q6,{"data-slot":"slider-range",className:(0,i.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:c.length},(e,t)=>(0,r.jsx)(s.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},t))]})}},63974:(e,t,a)=>{a.d(t,{TR:()=>g,bq:()=>m,eb:()=>p,gC:()=>f,l6:()=>d,s3:()=>c,yv:()=>u});var r=a(60687);a(43210);var n=a(28695),s=a(78272),i=a(13964),o=a(3589),l=a(96241);function d({...e}){return(0,r.jsx)(n.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(n.YJ,{"data-slot":"select-group",...e})}function u({...e}){return(0,r.jsx)(n.WT,{"data-slot":"select-value",...e})}function m({className:e,size:t="default",children:a,...i}){return(0,r.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[a,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(s.A,{className:"size-4 opacity-50"})})]})}function f({className:e,children:t,position:a="popper",...s}){return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:[(0,r.jsx)(x,{}),(0,r.jsx)(n.LM,{className:(0,l.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(h,{})]})})}function g({className:e,...t}){return(0,r.jsx)(n.JU,{"data-slot":"select-label",className:(0,l.cn)("text-muted-foreground px-2 py-1.5 text-xs",e),...t})}function p({className:e,children:t,...a}){return(0,r.jsxs)(n.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(n.p4,{children:t})]})}function x({className:e,...t}){return(0,r.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"size-4"})})}function h({className:e,...t}){return(0,r.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(s.A,{className:"size-4"})})}}};