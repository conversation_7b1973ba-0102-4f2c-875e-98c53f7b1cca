(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4417],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>s,t:()=>i});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function s(...e){return n.useCallback(i(...e),e)}},12741:(e,t,r)=>{Promise.resolve().then(r.bind(r,51619)),Promise.resolve().then(r.bind(r,31772))},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:t,...s,width:a,height:a,stroke:r,strokeWidth:o?24*Number(l)/Number(a):l,className:i("lucide",c),...f},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),o=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:o,...c}=r;return(0,n.createElement)(l,{ref:s,iconNode:t,className:i("lucide-".concat(a(e)),o),...c})});return r.displayName="".concat(e),r}},31772:(e,t,r)=>{"use strict";r.d(t,{default:()=>b});var n=r(95155),a=r(12115),i=r(66766),s=r(6874),l=r.n(s),o=r(28695),c=r(60760),d=r(35169),u=r(57918),f=r(51154),m=r(54416),g=r(97168),h=r(34477);let v=(0,h.createServerReference)("70c9dfab15ca22682df83077abf481dbf2eb62e561",h.callServer,void 0,h.findSourceMapURL,"getBusinessGalleryImagesPaginated");var p=r(3096);function b(e){let{businessProfile:t,galleryImages:r,totalCount:s,totalPages:h,currentPage:b,hasNextPage:x,hasPrevPage:y,userPlan:w,isAuthenticated:j,currentUserId:N}=e,[k,A]=(0,a.useState)(null),[S,C]=(0,a.useState)(0),[E,P]=(0,a.useState)(!1),[_,R]=(0,a.useState)(r),[z,O]=(0,a.useState)(b),[I,L]=(0,a.useState)(!1),[$,M]=(0,a.useState)(x),{ref:W,inView:B}=(0,p.Wx)({threshold:.1,triggerOnce:!0}),{ref:F,inView:D}=(0,p.Wx)({threshold:.1}),V=(0,a.useCallback)(async()=>{if(!I&&$){L(!0);try{let e=z+1,r=await v(t.business_slug,e,20);r.images&&r.images.length>0?(R(e=>[...e,...r.images]),O(e),M(r.hasNextPage)):M(!1)}catch(e){console.error("Error loading more images:",e),M(!1)}finally{L(!1)}}},[t.business_slug,z,I,$]);(0,a.useEffect)(()=>{P(!0)},[]),(0,a.useEffect)(()=>{D&&$&&!I&&V()},[D,$,I,V]),(0,a.useEffect)(()=>(k?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[k]);let H=(e,t)=>{A(e),C(t)},T=()=>{A(null)};(0,a.useEffect)(()=>{let e=e=>{k&&"Escape"===e.key&&T()};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[k]);let U={hidden:{opacity:0,scale:.8},visible:{opacity:1,scale:1}};return(0,n.jsxs)("div",{className:"w-full max-w-screen-xl mx-auto px-4 sm:px-6 py-8",children:[(0,n.jsx)("div",{className:"flex items-center mb-6",children:(0,n.jsx)(l(),{href:"/".concat(t.business_slug),passHref:!0,children:(0,n.jsxs)(g.$,{variant:"ghost",size:"sm",className:"gap-1",children:[(0,n.jsx)(d.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Back to Business Card"})]})})}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsxs)("h1",{className:"text-2xl sm:text-3xl font-bold mb-2",children:[t.business_name," Gallery"]}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Browse all photos"})]}),(0,n.jsx)(o.P.div,{ref:W,className:"grid grid-cols-2 sm:grid-cols-4 gap-4",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:B?"visible":"hidden",children:_.map((e,r)=>(0,n.jsxs)(o.P.div,{className:"aspect-square relative overflow-hidden rounded-xl cursor-pointer group",onClick:()=>H(e.url,r),variants:U,whileHover:{scale:1.03},transition:{duration:.3},children:[(0,n.jsx)(i.default,{src:e.url,alt:"".concat(t.business_name," gallery image ").concat(r+1),fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-105",sizes:"(max-width: 640px) 50vw, 25vw"}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,n.jsx)("div",{className:"absolute bottom-2 right-2 bg-black/50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,n.jsx)(u.A,{className:"w-4 h-4"})})})]},e.id))}),$&&(0,n.jsx)("div",{ref:F,className:"flex justify-center py-8",children:I?(0,n.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,n.jsx)(f.A,{className:"h-5 w-5 animate-spin"}),(0,n.jsx)("span",{children:"Loading more images..."})]}):(0,n.jsx)("div",{className:"text-muted-foreground text-sm",children:"Scroll down to load more images"})}),!$&&_.length>0&&(0,n.jsx)("div",{className:"flex justify-center py-8",children:(0,n.jsx)("div",{className:"text-muted-foreground text-sm",children:"You've reached the end of the gallery"})}),(0,n.jsx)(c.N,{children:k&&(0,n.jsxs)(o.P.div,{className:"fixed inset-0 z-50 bg-black/90 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:T,children:[(0,n.jsx)("button",{className:"absolute top-4 right-4 z-10 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors",onClick:e=>{e.stopPropagation(),T()},children:(0,n.jsx)(m.A,{className:"w-6 h-6"})}),(0,n.jsx)("div",{className:"relative w-full h-full max-w-4xl max-h-[80vh] mx-auto p-4 flex items-center justify-center",onClick:e=>e.stopPropagation(),children:(0,n.jsx)(i.default,{src:k,alt:"".concat(t.business_name," gallery image ").concat(S+1),fill:!0,className:"object-contain",sizes:"100vw",priority:!0})})]})})]})}},34477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return i},findSourceMapURL:function(){return a.findSourceMapURL}});let n=r(53806),a=r(31818),i=r(34979).createServerReference},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51619:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var n=r(95155),a=r(12115),i=r(28695),s=r(5937),l=r(97168),o=r(35695);function c(){let e=(0,o.useRouter)(),[t,r]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{r(!0)},[]),(0,n.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center p-4 bg-white dark:bg-black",children:(0,n.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-md w-full bg-white dark:bg-neutral-900 rounded-xl shadow-lg p-8 border border-neutral-200 dark:border-neutral-800 text-center",children:[(0,n.jsx)("div",{className:"mb-6 flex justify-center",children:(0,n.jsx)("div",{className:"p-4 bg-neutral-100 dark:bg-neutral-800 rounded-full",children:(0,n.jsx)(s.A,{className:"h-12 w-12 text-[var(--brand-gold)]"})})}),(0,n.jsx)("h2",{className:"text-2xl font-bold mb-3 text-neutral-900 dark:text-neutral-100",children:"This Business is Currently Offline"}),(0,n.jsx)("p",{className:"text-neutral-600 dark:text-neutral-400 mb-6",children:"This business card is currently set to private or offline mode. Meanwhile, you can discover other businesses in your locality."}),(0,n.jsxs)("div",{className:"relative group",children:[t&&(0,n.jsx)(i.P.div,{className:"absolute -inset-0.5 rounded-md blur-md",style:{background:"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))"},initial:{opacity:.7},animate:{opacity:[.7,.9,.7],boxShadow:["0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)","0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)","0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)"]},transition:{duration:2,repeat:1/0,repeatType:"reverse"}}),(0,n.jsx)(l.$,{onClick:()=>e.push("/discover"),className:"relative w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-8 py-6 h-12 rounded-md shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer",children:(0,n.jsxs)("span",{className:"relative z-10 flex items-center justify-center gap-2",children:["Discover Businesses",(0,n.jsx)(s.A,{className:"h-5 w-5"})]})})]})]})})}},53999:(e,t,r)=>{"use strict";r.d(t,{M0:()=>d,Yq:()=>u,cn:()=>i,gV:()=>s,gY:()=>c,kY:()=>l,vA:()=>o,vv:()=>f});var n=r(52596),a=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function s(e){if(!e)return null;let t=e.trim();return(t.startsWith("+91")?t=t.substring(3):12===t.length&&t.startsWith("91")&&(t=t.substring(2)),/^\d{10}$/.test(t))?t:null}function l(e){if(!e||e.length<4)return"Invalid Phone";let t=e.substring(0,2),r=e.substring(e.length-2),n="*".repeat(e.length-4);return"".concat(t).concat(n).concat(r)}function o(e){if(!e||!e.includes("@"))return"Invalid Email";let t=e.split("@"),r=t[0],n=t[1];if(r.length<=2||n.length<=2||!n.includes("."))return"Email Hidden";let a=r.substring(0,2)+"*".repeat(r.length-2),i=n.split("."),s=i[0],l=i.slice(1).join("."),o=s.substring(0,2)+"*".repeat(s.length-2);return"".concat(a,"@").concat(o,".").concat(l)}function c(e){if(null==e||isNaN(e))return"0";let t=Math.abs(e),r=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(t<1e5)return t>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let n=r.length-1;n>=0;n--)if(t>=r[n].value)return(e/r[n].value).toFixed(1).replace(/\.0$/,"")+r[n].symbol;return e.toString()}function d(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let r={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return t&&(r.hour="2-digit",r.minute="2-digit",r.hour12=!0),e.toLocaleString("en-IN",r)}function f(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(r){return"".concat(t," ").concat(e.toFixed(2))}}},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var n=r(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,s=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:l}=t,o=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let i=a(t)||a(n);return s[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>l});var n=r(95155);r(12115);var a=r(99708),i=r(74466),s=r(53999);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:i,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,s.cn)(l({variant:r,size:i,className:t})),...c})}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>s});var n=r(12115),a=r(6101),i=r(95155),s=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var s;let e,l,o=(s=r,(l=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(l=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),c=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,a.t)(t,o):o),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...s}=e,l=n.Children.toArray(a),c=l.find(o);if(c){let e=c.props.children,a=l.map(t=>t!==c?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...s,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...s,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}("Slot"),l=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,6766,5016,8441,1684,7358],()=>t(12741)),_N_E=e.O()}]);