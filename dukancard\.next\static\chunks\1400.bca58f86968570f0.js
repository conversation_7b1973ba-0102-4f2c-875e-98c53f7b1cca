"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1400],{81400:(r,e,o)=>{o.d(e,{getSubscription:()=>a}),o(8777);var t=o(49509),c=o(49641).Buffer;let n=()=>{let r,e;if(r=t.env.RAZORPAY_LIVE_KEY_ID||"***********************",e=t.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!r||!e)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!r,keySecret:!!e,env:"production"}),Error("Razorpay credentials not configured");return{keyId:r,keySecret:e}},s=()=>{let{keyId:r,keySecret:e}=n(),o=c.from("".concat(r,":").concat(e)).toString("base64");return{Authorization:"Basic ".concat(o),"Content-Type":"application/json"}};async function a(r){try{let e=s(),o=await fetch("".concat("https://api.razorpay.com/v2".replace("/v2","/v1"),"/subscriptions/").concat(r),{method:"GET",headers:e}),t=await o.json();if(!o.ok)return console.error("[RAZORPAY_ERROR] Error fetching subscription:",t),{success:!1,error:t};return{success:!0,data:t}}catch(r){return console.error("[RAZORPAY_ERROR] Exception fetching subscription:",r),{success:!1,error:{message:r instanceof Error?r.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}}}]);