(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8743],{14858:(e,t,a)=>{Promise.resolve().then(a.bind(a,53687)),Promise.resolve().then(a.bind(a,81570))},81570:(e,t,a)=>{"use strict";a.d(t,{default:()=>q});var r=a(95155),s=a(35695),n=a(12115),c=a(94234),l=a(34477);let i=(0,l.createServerReference)("408f2158ec400c093504ae89fcc291c80da2d01c4f",l.callServer,void 0,l.findSourceMapURL,"searchLocalityCombined"),o=(0,l.createServerReference)("40748292c4304238cd62a2e4c621b487ca6777a364",l.callServer,void 0,l.findSourceMapURL,"fetchMoreBusinessCardsByLocalityCombined"),d=(0,l.createServerReference)("407bc5dc4231b686ee52efd08cbc50dc48858f4866",l.callServer,void 0,l.findSourceMapURL,"fetchMoreProductsByLocalityCombined");var u=a(77560);let m=(0,n.createContext)(void 0);function x(){let e=(0,n.useContext)(m);if(void 0===e)throw Error("useLocalityContext must be used within a LocalityProvider");return e}function h(e){let{children:t,locality:a,initialBusinesses:l}=e,x=(0,s.useSearchParams)(),h=x.get(c.b2)||"cards",p=x.get(c.OL)||"created_desc",y=x.get(c.v0)||"newest",g=x.get(c.ry)||"all",[b,f]=(0,n.useState)(h),[v,j]=(0,n.useState)(p),[N,w]=(0,n.useState)(y),[S,A]=(0,n.useState)(g),[P,k]=(0,n.useState)(!1),[_,C]=(0,n.useState)(!1),[M,L]=(0,n.useState)(null),[T,R]=(0,n.useState)(null),[B,E]=(0,n.useState)(l),[F,V]=(0,n.useState)([]),[U,Z]=(0,n.useState)(1),[H,I]=(0,n.useState)(!1),[D,O]=(0,n.useState)((null==l?void 0:l.length)||0),[q,z]=(0,n.useState)(!1),{handleBusinessSortChange:X,handleBusinessSearch:$,loadMoreBusinesses:K}=function(e,t,a,r,l,d,u,m,x,h,p,y,g){let b=(0,s.useSearchParams)(),[f,v]=(0,n.useTransition)();return{isPending:f,handleBusinessSortChange:t=>{h(t),v(async()=>{a(!0),p(null);try{let a=await i({localityName:e.name,pincode:e.pincode,viewType:"cards",sortBy:t,businessName:b.get(c.ho)||void 0});if(a.error)return void p(a.error);a.data&&(r(a.data),d(a.data.businesses||[]),l(a.data.isAuthenticated),u(a.data.hasMore),m(a.data.totalCount),x(1))}catch(e){console.error("Error in handleBusinessSortChange:",e),p("An unexpected error occurred. Please try again.")}finally{a(!1)}})},handleBusinessSearch:async t=>{a(!0),p(null);try{let a=await i({localityName:e.name,pincode:e.pincode,viewType:"cards",sortBy:g,businessName:t||void 0});if(a.error)return void p(a.error);a.data&&(r(a.data),d(a.data.businesses||[]),l(a.data.isAuthenticated),u(a.data.hasMore),m(a.data.totalCount),x(1))}catch(e){console.error("Error in handleBusinessSearch:",e),p("An unexpected error occurred. Please try again.")}finally{a(!1)}},loadMoreBusinesses:async()=>{if("cards"!==t)return;let a=y.length>0?Math.ceil(y.length/20)+1:1;try{let t=await o({localityName:e.name,pincode:e.pincode,page:a,sortBy:g,businessName:b.get(c.ho)||void 0});if(t.error)return void p(t.error);t.data&&(d(e=>{var a;return[...e,...(null==(a=t.data)?void 0:a.businesses)||[]]}),u(t.data.hasMore),x(a))}catch(e){console.error("Error in loadMoreBusinesses:",e),p("An unexpected error occurred. Please try again.")}}}}(a,b,k,R,z,E,I,O,Z,j,L,B,v),{handleProductSortChange:W,handleProductSearch:G,handleProductFilterChange:J,loadMoreProducts:Q}=function(e,t,a,r,l,o,m,x,h,p,y,g,b,f,v,j){let N=(0,s.useSearchParams)(),[w,S]=(0,n.useTransition)();return{isPending:w,handleProductSortChange:t=>{p(t),S(async()=>{a(!0),g(null);try{let a=await i({localityName:e.name,pincode:e.pincode,viewType:"products",sortBy:(0,u.tV)(t),productType:"all"===j?null:j,productName:N.get(c.u0)||void 0});if(a.error)return void g(a.error);a.data&&(r(a.data),o(a.data.products||[]),l(a.data.isAuthenticated),m(a.data.hasMore),x(a.data.totalCount),h(1))}catch(e){console.error("Error in handleProductSortChange:",e),g("An unexpected error occurred. Please try again.")}finally{a(!1)}})},handleProductSearch:async t=>{a(!0),g(null);try{let a=await i({localityName:e.name,pincode:e.pincode,viewType:"products",sortBy:(0,u.tV)(v),productType:"all"===j?null:j,productName:t||void 0});if(a.error)return void g(a.error);a.data&&(r(a.data),o(a.data.products||[]),l(a.data.isAuthenticated),m(a.data.hasMore),x(a.data.totalCount),h(1))}catch(e){console.error("Error in handleProductSearch:",e),g("An unexpected error occurred. Please try again.")}finally{a(!1)}},handleProductFilterChange:t=>{y(t),S(async()=>{a(!0),g(null);try{let a=await i({localityName:e.name,pincode:e.pincode,viewType:"products",sortBy:(0,u.tV)(v),productType:"all"===t?null:t,productName:N.get(c.u0)||void 0});if(a.error)return void g(a.error);a.data&&(r(a.data),o(a.data.products||[]),l(a.data.isAuthenticated),m(a.data.hasMore),x(a.data.totalCount),h(1))}catch(e){console.error("Error in handleProductFilterChange:",e),g("An unexpected error occurred. Please try again.")}finally{a(!1)}})},loadMoreProducts:async()=>{if("products"!==t)return;let a=b.length>0?Math.ceil(b.length/20)+1:1;try{let t=await d({localityName:e.name,pincode:e.pincode,page:a,sortBy:(0,u.tV)(v),productType:"all"===j?null:j,productName:N.get(c.u0)||void 0});if(t.error)return void g(t.error);t.data&&(o(e=>{var a;return[...e,...(null==(a=t.data)?void 0:a.products)||[]]}),m(t.data.hasMore),h(a))}catch(e){console.error("Error in loadMoreProducts:",e),g("An unexpected error occurred. Please try again.")}}}}(a,b,k,R,z,V,I,O,Z,w,A,L,F,0,N,S),{isPending:Y,handleViewChange:ee,performSearch:et}=function(e,t,a,r,s,c,l,o,d,m,x,h,p,y,g){let[b,f]=(0,n.useTransition)(),v=async()=>{r(!0),h(null);try{let a=await i({localityName:e.name,pincode:e.pincode,viewType:t,sortBy:"cards"===t?p:(0,u.tV)(y),productType:"products"===t&&"all"!==g?g:null});if(a.error)return void h(a.error);a.data&&(s(a.data),"cards"===t?l(a.data.businesses||[]):o(a.data.products||[]),c(a.data.isAuthenticated),d(a.data.hasMore),m(a.data.totalCount),x(1))}catch(e){console.error("Error in performSearch:",e),h("An unexpected error occurred. Please try again.")}finally{r(!1)}};return{isPending:b,handleViewChange:e=>{e!==t&&(a(e),f(async()=>{await v()}))},performSearch:v,loadMore:async()=>{}}}(a,b,f,k,R,z,E,V,I,O,Z,L,v,N,S),ea=async()=>{if(!_){C(!0);try{"cards"===b?await K():await Q()}catch(e){console.error("Error in loadMore:",e),L("An unexpected error occurred. Please try again.")}finally{C(!1)}}};return(0,n.useEffect)(()=>{"products"!==b||0!==F.length||P||et()},[]),(0,r.jsx)(m.Provider,{value:{locality:a,viewType:b,sortBy:v,isSearching:P,isPending:Y,isLoadingMore:_,searchError:M,productFilterBy:S,productSortBy:N,searchResult:T,businesses:B,products:F,currentPage:U,hasMore:H,totalCount:D,isAuthenticated:q,performSearch:et,handleViewChange:ee,handleBusinessSortChange:X,handleBusinessSearch:$,handleProductSearch:G,handleProductSortChange:W,handleProductFilterChange:J,loadMore:ea},children:t})}var p=a(47924),y=a(54416),g=a(89852),b=a(97168);function f(e){let{}=e,t=(0,s.useRouter)(),a=(0,s.useSearchParams)(),{viewType:l,handleBusinessSearch:i,handleProductSearch:o}=x(),d="cards"===l?a.get(c.ho)||"":a.get(c.u0)||"",[u,m]=(0,n.useState)(d),[h,f]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{m("cards"===l?a.get(c.ho)||"":a.get(c.u0)||"")},[l,a]),(0,r.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center",children:[(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),f(!0);let r=new URLSearchParams(a.toString());"cards"===l?(u?r.set(c.ho,u):r.delete(c.ho),r.delete(c.u0)):(u?r.set(c.u0,u):r.delete(c.u0),r.delete(c.ho)),t.push("/locality/".concat(a.get("localSlug"),"?").concat(r.toString())),"cards"===l?i(u):o(u),f(!1)},className:"w-full md:flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,r.jsx)(g.p,{type:"text",placeholder:"cards"===l?"Search businesses...":"Search products...",value:u,onChange:e=>m(e.target.value),className:"pl-10 pr-10 h-10 w-full"}),u&&(0,r.jsx)("button",{type:"button",onClick:()=>{m("");let e=new URLSearchParams(a.toString());"cards"===l?e.delete(c.ho):e.delete(c.u0),t.push("/locality/".concat(a.get("localSlug"),"?").concat(e.toString())),"cards"===l?i(""):o("")},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground",children:(0,r.jsx)(y.A,{className:"h-4 w-4"})})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 w-full md:w-auto",children:[(0,r.jsx)(b.$,{type:"button",variant:"cards"===l?"default":"outline",size:"sm",onClick:()=>{let e=new URLSearchParams(a.toString());e.set(c.b2,"cards"),t.push("/locality/".concat(a.get("localSlug"),"?").concat(e.toString()))},className:"flex-1 md:flex-none",children:"Businesses"}),(0,r.jsx)(b.$,{type:"button",variant:"products"===l?"default":"outline",size:"sm",onClick:()=>{let e=new URLSearchParams(a.toString());e.set(c.b2,"products"),t.push("/locality/".concat(a.get("localSlug"),"?").concat(e.toString()))},className:"flex-1 md:flex-none",children:"Products"})]})]})})}var v=a(85339),j=a(49026);function N(){let{searchError:e}=x();return e?(0,r.jsxs)(j.Fc,{variant:"destructive",className:"mb-4",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)(j.XL,{children:"Error"}),(0,r.jsx)(j.TN,{children:e})]}):null}var w=a(51154),S=a(90497);function A(e){let{businesses:t,lastItemRef:a}=e;return(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:t.map((e,s)=>s===t.length-1?(0,r.jsx)("div",{ref:a,children:(0,r.jsx)(S.A,{businesses:[e],isAuthenticated:!1})},e.id):(0,r.jsx)("div",{children:(0,r.jsx)(S.A,{businesses:[e],isAuthenticated:!1})},e.id))})}var P=a(61141),k=a(6874),_=a.n(k),C=a(28695);function M(e){let{products:t,lastItemRef:a}=e;return(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4",children:t.map((e,s)=>{let n="product-".concat(e.id);return(0,r.jsx)(C.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*s},className:"group",ref:s===t.length-1?a:void 0,children:e.business_slug?(0,r.jsx)(_(),{href:"/".concat(e.business_slug,"/product/").concat(e.slug||e.id),className:"block h-full",children:(0,r.jsx)("div",{className:"h-full",children:(0,r.jsx)(P.A,{product:e,isLink:!1})})}):(0,r.jsxs)("div",{className:"relative h-full",children:[(0,r.jsx)(P.A,{product:e,isLink:!1}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-red-500/80 text-white text-xs py-1 px-2 text-center rounded-b-md",children:"Unable to link to business"})]})},n)})})}var L=a(85213),T=a(5196),R=a(95784),B=a(58880);function E(){let{sortBy:e,handleBusinessSortChange:t,isSearching:a}=(0,B.u)();return(0,r.jsx)(C.P.div,{className:"flex justify-end",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,r.jsxs)("div",{className:"inline-flex items-center bg-white dark:bg-neutral-900 px-3 py-2 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800",children:[(0,r.jsx)(L.A,{className:"h-4 w-4 mr-2 text-[var(--brand-gold)]"}),(0,r.jsx)("span",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mr-2",children:"Sort by:"}),(0,r.jsxs)(R.l6,{value:e,onValueChange:e=>t(e),disabled:a,children:[(0,r.jsx)(R.bq,{className:"min-w-[180px] border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 focus:ring-offset-0",children:(0,r.jsx)(R.yv,{placeholder:"Sort by",children:{created_desc:"Newest First",name_asc:"Name (A-Z)",name_desc:"Name (Z-A)",likes_desc:"Most Liked",subscriptions_desc:"Most Subscribed",rating_desc:"Highest Rated"}[e]||"Sort by"})}),(0,r.jsxs)(R.gC,{children:[(0,r.jsxs)(R.s3,{children:[(0,r.jsx)(R.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5",children:"Date"}),(0,r.jsxs)(R.eb,{value:"created_desc",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("created_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Newest First"]})]}),(0,r.jsxs)(R.s3,{children:[(0,r.jsx)(R.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Name"}),(0,r.jsxs)(R.eb,{value:"name_asc",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("name_asc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Name (A-Z)"]}),(0,r.jsxs)(R.eb,{value:"name_desc",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("name_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Name (Z-A)"]})]}),(0,r.jsxs)(R.s3,{children:[(0,r.jsx)(R.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Popularity"}),(0,r.jsxs)(R.eb,{value:"likes_desc",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("likes_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Most Liked"]}),(0,r.jsxs)(R.eb,{value:"subscriptions_desc",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("subscriptions_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Most Subscribed"]}),(0,r.jsxs)(R.eb,{value:"rating_desc",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("rating_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Highest Rated"]})]})]})]})]})})}function F(){let{productSortBy:e,handleProductSortChange:t,isSearching:a}=(0,B.u)();return(0,r.jsx)(C.P.div,{className:"flex justify-end",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,r.jsxs)("div",{className:"inline-flex items-center bg-white dark:bg-neutral-900 px-3 py-2 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800",children:[(0,r.jsx)(L.A,{className:"h-4 w-4 mr-2 text-[var(--brand-gold)]"}),(0,r.jsx)("span",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mr-2",children:"Sort by:"}),(0,r.jsxs)(R.l6,{value:e,onValueChange:e=>t(e),disabled:a,children:[(0,r.jsx)(R.bq,{className:"min-w-[180px] border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 focus:ring-offset-0",children:(0,r.jsx)(R.yv,{placeholder:"Sort by",children:{newest:"Newest First",price_low:"Price (Low to High)",price_high:"Price (High to Low)",name_asc:"Name (A-Z)",name_desc:"Name (Z-A)"}[e]||"Sort by"})}),(0,r.jsxs)(R.gC,{children:[(0,r.jsxs)(R.s3,{children:[(0,r.jsx)(R.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5",children:"Date"}),(0,r.jsxs)(R.eb,{value:"newest",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("newest"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Newest First"]})]}),(0,r.jsxs)(R.s3,{children:[(0,r.jsx)(R.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Price"}),(0,r.jsxs)(R.eb,{value:"price_low",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("price_low"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Price (Low to High)"]}),(0,r.jsxs)(R.eb,{value:"price_high",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("price_high"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Price (High to Low)"]})]}),(0,r.jsxs)(R.s3,{children:[(0,r.jsx)(R.TR,{className:"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1",children:"Name"}),(0,r.jsxs)(R.eb,{value:"name_asc",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("name_asc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Name (A-Z)"]}),(0,r.jsxs)(R.eb,{value:"name_desc",className:"relative pl-8",children:[(0,r.jsx)(T.A,{className:"absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 ".concat("name_desc"===e?"text-[var(--brand-gold)] opacity-100":"opacity-0")}),"Name (Z-A)"]})]})]})]})]})})}function V(e){let{viewType:t}=e,a=(0,s.useSearchParams)(),n="cards"===t?a.get(c.ho):a.get(c.u0);return(0,r.jsx)(C.P.div,{className:"text-center py-16 px-4 bg-transparent rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2",children:"cards"===t?"No Businesses Found":"No Products Found"}),(0,r.jsxs)("p",{className:"text-neutral-500 dark:text-neutral-400 mb-4",children:["We couldn't find any ","cards"===t?"businesses":"products",n?' with "'.concat(n,'" in the name'):"",". Try adjusting your search criteria or browse all ","cards"===t?"businesses":"products","."]}),n&&(0,r.jsx)("button",{className:"inline-flex items-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-lg text-sm font-medium text-neutral-700 dark:text-neutral-300 transition-colors",onClick:()=>{console.log("Clear search clicked")},children:"Clear Search"})]})})}function U(){let{viewType:e,businesses:t,products:a,isSearching:s,isLoadingMore:c,hasMore:l,totalCount:i,loadMore:o}=x(),d=(0,n.useRef)(null),u=(0,n.useCallback)(e=>{s||c||(d.current&&d.current.disconnect(),d.current=new IntersectionObserver(e=>{e[0].isIntersecting&&l&&o()}),e&&d.current.observe(e))},[s,c,l,o]),m="cards"===e&&t.length>0||"products"===e&&a.length>0,h=s&&0===i;return(0,r.jsxs)("div",{className:"container mx-auto px-4 pb-20",children:[(0,r.jsx)("div",{className:"mb-6",children:"cards"===e?(0,r.jsx)(E,{}):(0,r.jsx)(F,{})}),h?(0,r.jsxs)("div",{className:"flex justify-center items-center py-20",children:[(0,r.jsx)(w.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,r.jsx)("span",{className:"ml-2 text-lg",children:"Loading..."})]}):m?(0,r.jsxs)(r.Fragment,{children:["cards"===e?(0,r.jsx)(A,{businesses:t,lastItemRef:u}):(0,r.jsx)(M,{products:a,lastItemRef:u}),c&&(0,r.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,r.jsx)(w.A,{className:"h-6 w-6 animate-spin text-primary"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading more..."})]})]}):(0,r.jsx)(V,{viewType:e})]})}var Z=a(4516);function H(){let e=(0,s.useSearchParams)(),{searchResult:t,viewType:a,locality:n}=x(),l=e.get(c.ho),i=e.get(c.u0),o="".concat(n.name,", ").concat(n.divisionName),d="".concat(n.name,", ").concat(n.divisionName,", ").concat(n.district,", ").concat(n.stateName," - ").concat(n.pincode),u="All businesses and products in ".concat(o),m=Z.A,h="Showing businesses and products from ".concat(d),y="";if(l||i){m=p.A;let e="cards"===a?l:i;u='Search results for "'.concat(e,'" in ').concat(o),h="Showing ".concat("cards"===a?"businesses":"products",' matching "').concat(e,'" in ').concat(d),y=e||""}if(t){let e=t.totalCount,r="cards"===a?1===e?"business":"businesses":1===e?"product":"products";h="Found ".concat(e," ").concat(r," in ").concat(d),y&&(h="Found ".concat(e," ").concat(r,' matching "').concat(y,'" in ').concat(d))}return(0,r.jsxs)("div",{className:"container mx-auto px-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(m,{className:"mr-2 h-5 w-5 text-primary"}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:u})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground ml-7",children:h})]})}var I=a(57340),D=a(13052);function O(){let{locality:e}=x();return(0,r.jsxs)("nav",{className:"container mx-auto px-4 py-4 flex items-center text-sm text-muted-foreground",children:[(0,r.jsxs)(_(),{href:"/?view=home",className:"flex items-center hover:text-primary transition-colors",children:[(0,r.jsx)(I.A,{className:"h-4 w-4 mr-1"}),(0,r.jsx)("span",{children:"Home"})]}),(0,r.jsx)(D.A,{className:"h-4 w-4 mx-1"}),(0,r.jsx)(_(),{href:"/discover",className:"hover:text-primary transition-colors",children:"Discover"}),(0,r.jsx)(D.A,{className:"h-4 w-4 mx-1"}),(0,r.jsx)("span",{className:"text-foreground font-medium",children:e.name})]})}function q(e){let{locality:t,initialBusinesses:a}=e,n=(0,s.useSearchParams)(),l=n.get(c.lX),i=n.get(c.KC),o=n.get(c.Ie);return(0,r.jsx)(h,{locality:t,initialBusinesses:a,children:(0,r.jsxs)("div",{className:"relative min-h-screen overflow-hidden bg-white dark:bg-black",children:[(0,r.jsx)(O,{}),(0,r.jsx)(f,{initialValues:{pincode:l,city:i,locality:o}}),(0,r.jsx)("div",{className:"container mx-auto px-4 my-2",children:(0,r.jsx)(N,{})}),(0,r.jsx)(H,{}),(0,r.jsx)(U,{})]})})}},89852:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(53999);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8695,6874,2290,375,5152,7665,6766,4577,3647,7823,3098,8441,1684,7358],()=>t(14858)),_N_E=e.O()}]);