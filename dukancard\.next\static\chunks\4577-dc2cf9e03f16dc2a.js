"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4577],{5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},43433:(e,t,r)=>{r.d(t,{UC:()=>eA,YJ:()=>eB,In:()=>eM,q7:()=>eV,VF:()=>eW,p4:()=>eO,JU:()=>eH,ZL:()=>eL,bL:()=>eI,wn:()=>eG,PP:()=>eF,l9:()=>eP,WT:()=>eD,LM:()=>e_});var n=r(12115),l=r(47650),o=r(89367),a=r(85185),i=r(57683),s=r(6101),u=r(46081),d=r(94315),c=r(19178),p=r(92293),f=r(25519),v=r(61285),h=r(35152),m=r(34378),g=r(63540),w=r(95155),x=Symbol("radix.slottable");function y(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===x}var b=r(39033),S=r(5845),C=r(52712),j=r(45503),k=r(2564),R=r(38168),N=r(31114),T=[" ","Enter","ArrowUp","ArrowDown"],E=[" ","Enter"],I="Select",[P,D,M]=(0,i.N)(I),[L,A]=(0,u.A)(I,[M,h.Bk]),_=(0,h.Bk)(),[B,H]=L(I),[V,O]=L(I),W=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:m,required:g,form:x}=e,y=_(t),[b,C]=n.useState(null),[j,k]=n.useState(null),[R,N]=n.useState(!1),T=(0,d.jH)(c),[E,D]=(0,S.i)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:I}),[M,L]=(0,S.i)({prop:i,defaultProp:s,onChange:u,caller:I}),A=n.useRef(null),H=!b||x||!!b.closest("form"),[O,W]=n.useState(new Set),F=Array.from(O).map(e=>e.props.value).join(";");return(0,w.jsx)(h.bL,{...y,children:(0,w.jsxs)(B,{required:g,scope:t,trigger:b,onTriggerChange:C,valueNode:j,onValueNodeChange:k,valueNodeHasChildren:R,onValueNodeHasChildrenChange:N,contentId:(0,v.B)(),value:M,onValueChange:L,open:E,onOpenChange:D,dir:T,triggerPointerDownPosRef:A,disabled:m,children:[(0,w.jsx)(P.Provider,{scope:t,children:(0,w.jsx)(V,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{W(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),H?(0,w.jsxs)(eR,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:M,onChange:e=>L(e.target.value),disabled:m,form:x,children:[void 0===M?(0,w.jsx)("option",{value:""}):null,Array.from(O)]},F):null]})})};W.displayName=I;var F="SelectTrigger",G=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=_(r),u=H(F,r),d=u.disabled||l,c=(0,s.s)(t,u.onTriggerChange),p=D(r),f=n.useRef("touch"),[v,m,x]=eT(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eE(t,e,r);void 0!==n&&u.onValueChange(n.value)}),y=e=>{d||(u.onOpenChange(!0),x()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,w.jsx)(h.Mz,{asChild:!0,...i,children:(0,w.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eN(u.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(y(),e.preventDefault())})})})});G.displayName=F;var K="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,u=H(K,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,C.N)(()=>{d(c)},[d,c]),(0,w.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eN(u.value)?(0,w.jsx)(w.Fragment,{children:a}):o})});U.displayName=K;var q=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});q.displayName="SelectIcon";var z=e=>(0,w.jsx)(m.Z,{asChild:!0,...e});z.displayName="SelectPortal";var Z="SelectContent",Y=n.forwardRef((e,t)=>{let r=H(Z,e.__scopeSelect),[o,a]=n.useState();return((0,C.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,w.jsx)(Q,{...e,ref:t}):o?l.createPortal((0,w.jsx)(J,{scope:e.__scopeSelect,children:(0,w.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,w.jsx)("div",{children:e.children})})}),o):null});Y.displayName=Z;var[J,X]=L(Z),$=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var o;let e,a,i=(o=r,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,s.t)(t,i):i),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,a=n.Children.toArray(l),i=a.find(y);if(i){let e=i.props.children,l=a.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,w.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,w.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:x,collisionPadding:y,sticky:b,hideWhenDetached:S,avoidCollisions:C,...j}=e,k=H(Z,r),[T,E]=n.useState(null),[I,P]=n.useState(null),M=(0,s.s)(t,e=>E(e)),[L,A]=n.useState(null),[_,B]=n.useState(null),V=D(r),[O,W]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(T)return(0,R.Eq)(T)},[T]),(0,p.Oh)();let G=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===n&&I&&(I.scrollTop=I.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[V,I]),K=n.useCallback(()=>G([L,T]),[G,L,T]);n.useEffect(()=>{O&&K()},[O,K]);let{onOpenChange:U,triggerPointerDownPosRef:q}=k;n.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=q.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=q.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():T.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[T,U,q]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[z,Y]=eT(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eE(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),X=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==k.value&&k.value===t||n)&&(A(e),n&&(F.current=!0))},[k.value]),Q=n.useCallback(()=>null==T?void 0:T.focus(),[T]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==k.value&&k.value===t||n)&&B(e)},[k.value]),en="popper"===l?et:ee,el=en===et?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:x,collisionPadding:y,sticky:b,hideWhenDetached:S,avoidCollisions:C}:{};return(0,w.jsx)(J,{scope:r,content:T,viewport:I,onViewportChange:P,itemRefCallback:X,selectedItem:L,onItemLeave:Q,itemTextRefCallback:er,focusSelectedItem:K,selectedItemText:_,position:l,isPositioned:O,searchRef:z,children:(0,w.jsx)(N.A,{as:$,allowPinchZoom:!0,children:(0,w.jsx)(f.n,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=k.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,w.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,w.jsx)(en,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>W(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,a.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>G(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=H(Z,r),u=X(Z,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=D(r),m=n.useRef(!1),x=n.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:S,focusSelectedItem:j}=u,k=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&y&&b&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(a,[10,Math.max(10,c-u)]);d.style.minWidth=s+"px",d.style.left=p+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(a,[10,Math.max(10,c-u)]);d.style.minWidth=s+"px",d.style.right=p+"px"}let a=h(),s=window.innerHeight-20,u=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=f+v+u+parseInt(c.paddingBottom,10)+g,x=Math.min(5*b.offsetHeight,w),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,N=b.offsetHeight/2,T=f+v+(b.offsetTop+N);if(T<=R){let e=a.length>0&&b===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-R,N+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+g);d.style.height=T+t+"px"}else{let e=a.length>0&&b===a[0].ref.current;d.style.top="0px";let t=Math.max(R,f+y.offsetTop+(e?j:0)+N);d.style.height=t+(w-T)+"px",y.scrollTop=T-R+y.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=x+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,p,y,b,S,i.dir,l]);(0,C.N)(()=>k(),[k]);let[R,N]=n.useState();(0,C.N)(()=>{p&&N(window.getComputedStyle(p).zIndex)},[p]);let T=n.useCallback(e=>{e&&!0===x.current&&(k(),null==j||j(),x.current=!1)},[k,j]);return(0,w.jsx)(er,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,w.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,w.jsx)(g.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});ee.displayName="SelectItemAlignedPosition";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=_(r);return(0,w.jsx)(h.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});et.displayName="SelectPopperPosition";var[er,en]=L(Z,{}),el="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=X(el,r),u=en(el,r),d=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,w.jsx)(P.Slot,{scope:r,children:(0,w.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=el;var ea="SelectGroup",[ei,es]=L(ea),eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,w.jsx)(ei,{scope:r,id:l,children:(0,w.jsx)(g.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});eu.displayName=ea;var ed="SelectLabel",ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=es(ed,r);return(0,w.jsx)(g.sG.div,{id:l.id,...n,ref:t})});ec.displayName=ed;var ep="SelectItem",[ef,ev]=L(ep),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...u}=e,d=H(ep,r),c=X(ep,r),p=d.value===l,[f,h]=n.useState(null!=i?i:""),[m,x]=n.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),b=(0,v.B)(),S=n.useRef("touch"),C=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,w.jsx)(ef,{scope:r,value:l,disabled:o,textId:b,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,w.jsx)(P.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,w.jsx)(g.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:y,onFocus:(0,a.m)(u.onFocus,()=>x(!0)),onBlur:(0,a.m)(u.onBlur,()=>x(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==S.current&&C()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===S.current&&C()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(S.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(E.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ep;var em="SelectItemText",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,u=H(em,r),d=X(em,r),c=ev(em,r),p=O(em,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,x=n.useMemo(()=>(0,w.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=p;return(0,C.N)(()=>(y(x),()=>b(x)),[y,b,x]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(g.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});eg.displayName=em;var ew="SelectItemIndicator",ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ev(ew,r).isSelected?(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ex.displayName=ew;var ey="SelectScrollUpButton",eb=n.forwardRef((e,t)=>{let r=X(ey,e.__scopeSelect),l=en(ey,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,C.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(ej,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eb.displayName=ey;var eS="SelectScrollDownButton",eC=n.forwardRef((e,t)=>{let r=X(eS,e.__scopeSelect),l=en(eS,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,C.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(ej,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eC.displayName=eS;var ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=X("SelectScrollButton",r),s=n.useRef(null),u=D(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,C.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{d()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var ek="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=_(r),o=H(ek,r),a=X(ek,r);return o.open&&"popper"===a.position?(0,w.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=ek;var eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,s.s)(t,a),u=(0,j.Z)(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[u,l]),(0,w.jsx)(g.sG.select,{...o,style:{...k.Qg,...o.style},ref:i,defaultValue:l})});function eN(e){return""===e||void 0===e}function eT(e){let t=(0,b.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eE(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eR.displayName="SelectBubbleInput";var eI=W,eP=G,eD=U,eM=q,eL=z,eA=Y,e_=eo,eB=eu,eH=ec,eV=eh,eO=eg,eW=ex,eF=eb,eG=eC},45503:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(12115);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},89367:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}}}]);