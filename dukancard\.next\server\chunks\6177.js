exports.id=6177,exports.ids=[6177],exports.modules={1247:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(60687),s=r(43210),n=r(88920),o=r(77882),l=r(46001),i=r(80189);function d(){let[e,t]=(0,s.useState)(!1);return e?(0,a.jsx)(n.N,{children:(0,a.jsx)(o.P.div,{className:"fixed bottom-24 right-6 z-50",initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.5},transition:{duration:.3},children:(0,a.jsx)(i.Bc,{children:(0,a.jsxs)(i.m_,{children:[(0,a.jsx)(i.k$,{asChild:!0,children:(0,a.jsx)("div",{className:"relative group",children:(0,a.jsxs)(o.P.button,{className:"relative flex items-center justify-center p-3 rounded-full bg-[var(--brand-gold)]/80 text-[var(--brand-gold-foreground)] shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-[var(--brand-gold)] focus:ring-offset-2 dark:focus:ring-offset-black cursor-not-allowed opacity-90",whileHover:{scale:1.05},disabled:!0,children:[(0,a.jsx)("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white dark:border-black"}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-[var(--brand-gold)]/40 blur-md -z-10"}),(0,a.jsx)(o.P.div,{animate:{rotate:[0,15,-15,0],scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,repeatType:"loop",ease:"easeInOut"},children:(0,a.jsx)(l.A,{className:"h-6 w-6"})})]})})}),(0,a.jsx)(i.ZI,{side:"right",className:"bg-black/90 dark:bg-white/90 text-white dark:text-black border-none px-3 py-2 font-medium",children:(0,a.jsxs)("p",{className:"flex items-center gap-1.5",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Dukan AI - Coming Soon!"})]})})]})})})}):null}},11637:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\BottomNav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx","default")},14890:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\Header.tsx","default")},19386:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(60687);r(43210);var s=r(85814),n=r.n(s),o=r(77882),l=r(96241),i=r(32688);let d=({className:e})=>{let t=[{title:"Company",links:[{title:"About Us",href:"/about"},{title:"Contact Us",href:"/contact"}]},{title:"Product",links:[{title:"Features",href:"/features"},{title:"Pricing",href:"/pricing"}]},{title:"Resources",links:[{title:"Blog",href:"/blog"},{title:"Support",href:"/support"},{title:"Advertise",href:i.C.advertising.page}]},{title:"Legal",links:[{title:"Privacy Policy",href:i.C.legal.privacyPolicy},{title:"Terms of Service",href:i.C.legal.termsOfService},{title:"Refund Policy",href:i.C.legal.refundPolicy},{title:"Cookie Policy",href:"/cookies"}]}],r={hidden:{opacity:0,y:15},visible:e=>({opacity:1,y:0,transition:{duration:.4,delay:.05*e,ease:"easeOut"}})};return(0,a.jsxs)(o.P.footer,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.5,ease:"easeOut"}}},initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},className:(0,l.cn)("bg-gradient-to-b from-muted/50 to-background dark:from-background dark:to-black/30 border-t border-border pt-16 pb-20 md:pb-8 relative overflow-hidden",e),children:[(0,a.jsxs)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute left-1/4 bottom-1/2 w-1/3 h-1/3 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl opacity-60"}),(0,a.jsx)("div",{className:"absolute right-1/4 top-1/2 w-1/4 h-1/4 bg-blue-500/5 dark:bg-blue-500/10 rounded-full blur-3xl opacity-60"})]}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 md:px-6 lg:px-8",children:[(0,a.jsxs)(o.P.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},transition:{staggerChildren:.1},className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 md:gap-8 lg:gap-12 pb-12",children:[(0,a.jsx)(o.P.div,{variants:r,custom:0,className:"col-span-2 md:col-span-3 lg:col-span-2",children:(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-foreground flex items-center",children:[" ",(0,a.jsx)("span",{className:"text-[var(--brand-gold)]",children:"Dukan"}),"card"]}),(0,a.jsxs)("p",{className:"text-muted-foreground mt-4",children:[" ","Elevate your business with our premium digital card solution. Connect with customers, showcase your offerings, and grow your brand."]})]})}),t.map((e,t)=>(0,a.jsxs)(o.P.div,{variants:r,custom:t+1,className:"lg:col-span-1",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-foreground mb-4",children:[" ",e.title]}),(0,a.jsx)("ul",{className:"space-y-2",children:e.links.map(e=>(0,a.jsxs)("li",{children:[" ",(0,a.jsx)(n(),{href:e.href,className:"text-muted-foreground hover:text-[var(--brand-gold)] transition-colors duration-200 text-sm",children:e.title})]},e.href))})]},e.title))]}),(0,a.jsxs)("div",{className:"border-t border-border py-6",children:[" ",(0,a.jsx)("div",{className:"flex flex-col md:flex-row justify-center items-center",children:(0,a.jsxs)("p",{className:"text-muted-foreground text-sm mb-4 md:mb-0 text-center",children:[" ","\xa9 ",new Date().getFullYear()," ",i.C.name,". All rights reserved."]})})]})]})]})}},21886:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\components\\\\ui\\\\scroll-to-top-button.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx","default")},28732:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(60687),s=r(43210),n=r(88920),o=r(77882),l=r(3589),i=r(16189);function d({excludePaths:e=[]}){let[t,r]=(0,s.useState)(!1),d=(0,i.usePathname)();return e.some(e=>d.startsWith(e)),(0,a.jsx)(n.N,{children:t&&(0,a.jsxs)(o.P.button,{className:"fixed bottom-[70px] md:bottom-[60px] lg:bottom-8 right-6 z-50 p-3 rounded-full bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-[var(--brand-gold)] focus:ring-offset-2 dark:focus:ring-offset-black cursor-pointer",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},initial:{opacity:0,scale:.5,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.5,y:20},transition:{duration:.3},whileHover:{scale:1.1},whileTap:{scale:.9},children:[(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-[var(--brand-gold)]/50 blur-md -z-10"}),(0,a.jsx)(o.P.div,{animate:{y:[0,-3,0]},transition:{duration:1.5,repeat:1/0,repeatType:"loop",ease:"easeInOut"},children:(0,a.jsx)(l.A,{className:"h-6 w-6"})})]})})}},32688:(e,t,r)=>{"use strict";r.d(t,{C:()=>a});let a={name:"Dukancard",description:"Create and share digital business cards, showcase products, and connect with customers.",url:"https://dukancard.in",ogImage:"https://dukancard.in/opengraph-image.png",contact:{email:"<EMAIL>",phone:"+91 8458060663",address:{street:"Bisra Road",city:"Rourkela",state:"Odisha",postalCode:"769001",country:"India",full:"Bisra Road, Rourkela, Odisha - 769001"},hours:"Monday - Friday: 9:00 AM - 6:00 PM"},legal:{privacyPolicy:"/privacy",termsOfService:"/terms",refundPolicy:"/refund"},support:{email:"<EMAIL>",phone:"+91 8458060663",helpCenter:"/support"},advertising:{email:"<EMAIL>",phone:"+************",page:"/advertise"}}},33903:(e,t,r)=>{Promise.resolve().then(r.bind(r,91164)),Promise.resolve().then(r.bind(r,38606)),Promise.resolve().then(r.bind(r,1247)),Promise.resolve().then(r.bind(r,19386)),Promise.resolve().then(r.bind(r,35491)),Promise.resolve().then(r.bind(r,53100)),Promise.resolve().then(r.bind(r,28732)),Promise.resolve().then(r.bind(r,10218))},35491:(e,t,r)=>{"use strict";r.d(t,{default:()=>A});var a=r(60687),s=r(43210),n=r.n(s),o=r(85814),l=r.n(o),i=r(96241),d=r(11860),c=r(12941);let u=({children:e,className:t})=>{let r=(0,s.useRef)(null),[o,l]=(0,s.useState)(!1);return n().useEffect(()=>{let e=()=>{window.scrollY>100?l(!0):l(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,a.jsx)("div",{ref:r,className:(0,i.cn)("sticky inset-x-0 top-20 z-40 w-full",t),children:n().Children.map(e,e=>n().isValidElement(e)?n().cloneElement(e,{visible:o}):e)})},p=({children:e,className:t,visible:r})=>(0,a.jsx)("div",{style:{minWidth:"900px",backdropFilter:r?"blur(10px)":"none",boxShadow:r?"0 0 24px rgba(34, 42, 53, 0.06), 0 1px 1px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(34, 42, 53, 0.04), 0 0 4px rgba(34, 42, 53, 0.08), 0 16px 68px rgba(47, 48, 55, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1) inset":"none",width:r?"40%":"100%",transform:r?"translateY(20px)":"translateY(0)",transition:"all 0.3s ease-out"},className:(0,i.cn)("relative z-[60] mx-auto hidden w-full max-w-7xl flex-row items-center justify-between self-start rounded-full bg-transparent px-4 py-4 lg:flex dark:bg-transparent",r&&"bg-white/80 dark:bg-neutral-950/80",t),children:e}),x=({items:e,className:t,onItemClick:r})=>{let[n,o]=(0,s.useState)(null);return(0,a.jsx)("div",{onMouseLeave:()=>o(null),className:(0,i.cn)("absolute inset-0 hidden flex-1 flex-row items-center justify-center space-x-2 text-sm font-medium text-zinc-600 transition duration-200 hover:text-zinc-800 lg:flex lg:space-x-2",t),children:e.map((e,t)=>(0,a.jsxs)("a",{onMouseEnter:()=>o(t),onClick:r,className:"relative px-4 py-3 text-neutral-600 dark:text-neutral-300",href:e.link,children:[n===t&&(0,a.jsx)("div",{className:"absolute inset-0 h-full w-full rounded-full bg-gray-100 dark:bg-neutral-800 transition-all duration-200"}),(0,a.jsxs)("span",{className:"relative z-20",children:[e.badge&&(0,a.jsx)("span",{className:"absolute -top-5 left-1/2 transform -translate-x-1/2 text-[8px] font-medium text-[var(--brand-gold)] bg-[var(--brand-gold)]/10 px-1 py-0.5 rounded border border-[var(--brand-gold)]/20 whitespace-nowrap",children:e.badge}),e.name]})]},`link-${t}`))})},m=({children:e,className:t,visible:r})=>(0,a.jsx)("div",{style:{backdropFilter:r?"blur(10px)":"none",boxShadow:r?"0 0 24px rgba(34, 42, 53, 0.06), 0 1px 1px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(34, 42, 53, 0.04), 0 0 4px rgba(34, 42, 53, 0.08), 0 16px 68px rgba(47, 48, 55, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1) inset":"none",width:r?"90%":"100%",paddingRight:r?"12px":"0px",paddingLeft:r?"12px":"0px",borderRadius:r?"4px":"2rem",transform:r?"translateY(20px)":"translateY(0)",transition:"all 0.3s ease-out"},className:(0,i.cn)("relative z-50 mx-auto flex w-full max-w-[calc(100vw-2rem)] flex-col items-center justify-between bg-transparent px-0 py-4 lg:hidden",r&&"bg-white/80 dark:bg-neutral-950/80",t),children:e}),b=({children:e,className:t})=>(0,a.jsx)("div",{className:(0,i.cn)("flex w-full flex-row items-center justify-between",t),children:e}),h=({children:e,className:t,isOpen:r})=>(0,a.jsx)(a.Fragment,{children:r&&(0,a.jsx)("div",{className:(0,i.cn)("absolute inset-x-0 top-16 z-50 flex w-full flex-col items-start justify-start gap-4 rounded-lg bg-white px-4 py-8 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:bg-neutral-950 transition-opacity duration-200",t),children:e})}),f=({isOpen:e,onClick:t})=>e?(0,a.jsx)(d.A,{className:"cursor-pointer text-black dark:text-white",onClick:t}):(0,a.jsx)(c.A,{className:"cursor-pointer text-black dark:text-white",onClick:t}),g=({href:e,as:t="a",children:r,className:s,variant:n="primary",...o})=>(0,a.jsx)(t,{href:e||void 0,className:(0,i.cn)("px-4 py-2 rounded-md bg-white button bg-white text-black text-sm font-bold relative cursor-pointer hover:-translate-y-0.5 transition duration-200 inline-block text-center",{primary:"shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]",secondary:"bg-transparent shadow-none dark:text-white",dark:"bg-black text-white shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]",gradient:"bg-gradient-to-b from-blue-500 to-blue-700 text-white shadow-[0px_2px_0px_0px_rgba(255,255,255,0.3)_inset]"}[n],s),...o,children:r});var v=r(41956),w=r(38398),_=r(6943),j=r(99270),k=r(46001),y=r(41312),N=r(70334),C=r(4952),P=r(16189);let A=()=>{let[e,t]=(0,s.useState)(!1),[r,n]=(0,s.useState)(null),[o,i]=(0,s.useState)(null),[d,c]=(0,s.useState)(!0),A=(0,P.usePathname)(),{scrollDirection:z,isScrolled:S}=(0,C.Y)({threshold:50}),I=S&&"down"===z&&(A.startsWith("/dashboard/")||"/discover"===A||A.startsWith("/post/"));(0,s.useEffect)(()=>{(async()=>{let e=(0,w.U)();c(!0);let{data:{user:t}}=await e.auth.getUser();if(n(t),t){let[r,a]=await Promise.all([e.from("customer_profiles").select("id").eq("id",t.id).maybeSingle(),e.from("business_profiles").select("id").eq("id",t.id).maybeSingle()]);r.data?i("customer"):a.data?i("business"):i(null)}else i(null);c(!1)})()},[]);let E=()=>"business"===o?"/dashboard/business":"/dashboard/customer",R=[{name:"Categories",link:"/categories",icon:_.A},{name:"Discover",link:"/discover",icon:j.A},{name:"Free Listing",link:"/login",badge:"Business",icon:k.A},{name:r?"Feed":"Community",link:r?E():"/login",icon:y.A}];return(0,a.jsx)("div",{className:"relative w-full",children:(0,a.jsxs)(u,{className:`fixed top-0 left-0 right-0 z-50 bg-background/80 dark:bg-background/90 backdrop-blur-lg border-b border-border/80 dark:border-border transition-transform duration-300 ease-in-out ${I?"-translate-y-full":"translate-y-0"}`,children:[(0,a.jsxs)(p,{className:"max-w-7xl mx-auto px-4 md:px-6 lg:px-8",children:[(0,a.jsx)(l(),{href:r?"/?view=home":"/",className:"flex items-center shrink-0 z-[100]",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("span",{className:"font-bold text-xl text-[var(--brand-gold)]",children:["Dukan",(0,a.jsx)("span",{className:"text-foreground",children:"card"})]}),(0,a.jsx)("span",{className:"text-xs text-[var(--brand-gold)]/80 -mt-1",children:"Your Neighborhood, Digitally Connected"})]})}),(0,a.jsx)(x,{items:R,className:"flex flex-1 flex-row items-center justify-center space-x-2 text-sm font-medium text-muted-foreground hover:text-[var(--brand-gold)]"}),(0,a.jsxs)("div",{className:"flex items-center gap-4 z-[70]",children:[(0,a.jsx)(v.ThemeToggle,{}),d?(0,a.jsx)("div",{className:"h-10 w-24 bg-muted rounded-full animate-pulse"}):r?(0,a.jsx)(g,{variant:"primary",as:"button",onClick:()=>window.location.href=E(),className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-full px-6 font-medium shadow hover:shadow-md",children:"Dashboard"}):(0,a.jsx)(g,{variant:"primary",as:"button",onClick:()=>window.location.href="/login",className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-full px-6 font-medium shadow hover:shadow-md",children:"Sign In"})]})]}),(0,a.jsxs)(m,{children:[(0,a.jsxs)(b,{children:[(0,a.jsx)(l(),{href:r?"/?view=home":"/",className:"flex items-center shrink-0 z-[100]",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("span",{className:"font-bold text-xl text-[var(--brand-gold)]",children:["Dukan",(0,a.jsx)("span",{className:"text-foreground",children:"card"})]}),(0,a.jsx)("span",{className:"text-xs text-[var(--brand-gold)]/80 -mt-1",children:"Your Neighborhood, Digitally Connected"})]})}),(0,a.jsx)(f,{isOpen:e,onClick:()=>t(!e)})]}),(0,a.jsxs)(h,{isOpen:e,onClose:()=>t(!1),className:"bg-background/95 backdrop-blur-md border-t border-border",children:[(0,a.jsx)("div",{className:"w-full space-y-2",children:R.map((e,r)=>{let s=e.icon;return(0,a.jsxs)(l(),{href:e.link,onClick:()=>t(!1),className:"group flex items-center justify-between w-full p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 hover:bg-white/80 dark:hover:bg-neutral-800/80 border border-neutral-200/50 dark:border-neutral-700/50 transition-all duration-200 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] group-hover:bg-[var(--brand-gold)]/20 transition-colors",children:(0,a.jsx)(s,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium text-foreground group-hover:text-[var(--brand-gold)] transition-colors",children:e.name}),e.badge&&(0,a.jsx)("span",{className:"text-xs text-[var(--brand-gold)] font-medium",children:e.badge})]})]}),(0,a.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground group-hover:text-[var(--brand-gold)] group-hover:translate-x-1 transition-all"})]},`mobile-link-${r}`)})}),(0,a.jsxs)("div",{className:"flex w-full flex-col gap-4 px-0",children:[(0,a.jsx)(v.ThemeToggle,{}),d?(0,a.jsx)("div",{className:"h-10 w-full bg-muted rounded-xl animate-pulse mx-4"}):r?(0,a.jsx)(g,{variant:"primary",as:"button",className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-xl w-full mx-4 shadow-md hover:shadow-lg transition-all",onClick:()=>{t(!1),window.location.href=E()},children:"Dashboard"}):(0,a.jsx)(g,{variant:"primary",as:"button",className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-xl w-full mx-4 shadow-md hover:shadow-lg transition-all",onClick:()=>{t(!1),window.location.href="/login"},children:"Sign In"})]})]})]})]})})}},46501:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\FloatingAIButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx","default")},53100:(e,t,r)=>{"use strict";r.d(t,{default:()=>s}),r(43210);var a=r(15251);let s=()=>(!(0,a.a)(),null)},60644:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\Footer.tsx","default")},80189:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>o,ZI:()=>d,k$:()=>i,m_:()=>l});var a=r(60687);r(43210);var s=r(58730),n=r(96241);function o({delayDuration:e=0,...t}){return(0,a.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function l({...e}){return(0,a.jsx)(o,{children:(0,a.jsx)(s.bL,{"data-slot":"tooltip",...e})})}function i({...e}){return(0,a.jsx)(s.l9,{"data-slot":"tooltip-trigger",...e})}function d({className:e,sideOffset:t=0,children:r,...o}){return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...o,children:[r,(0,a.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},91164:(e,t,r)=>{"use strict";r.d(t,{default:()=>p});var a=r(60687),s=r(43210),n=r(85814),o=r.n(n),l=r(20798),i=r(77882),d=r(32688),c=r(16189);let u={writingMode:"vertical-rl",textOrientation:"mixed",transform:"rotate(180deg)",letterSpacing:"0.05em",fontSize:"0.8rem",fontWeight:600};function p(){let[e,t]=(0,s.useState)(!1),[r,n]=(0,s.useState)(!1),p=(0,c.usePathname)(),x=p?.includes("/dashboard"),m="/advertise"===p;return!r||x||m?null:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed right-0 top-1/2 -translate-y-1/2 z-40 hidden sm:block",children:(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)(i.P.div,{className:"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-l-lg blur-md",animate:{opacity:e?.8:.5},transition:{duration:.3}}),(0,a.jsx)(o(),{href:d.C.advertising.page,children:(0,a.jsx)(i.P.div,{className:"relative",onMouseEnter:()=>t(!0),onMouseLeave:()=>t(!1),whileHover:{scale:1.03},whileTap:{scale:.98},children:(0,a.jsxs)("div",{className:"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 px-2 py-6 rounded-l-lg font-medium text-xs relative overflow-hidden shadow-lg flex flex-col items-center justify-center w-10 h-32",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center gap-2",children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{style:u,children:"Advertise"})]}),(0,a.jsx)(i.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear",repeatDelay:1}})]})})})]})}),(0,a.jsx)("div",{className:"fixed left-4 bottom-20 z-40 sm:hidden",children:(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)(i.P.div,{className:"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full blur-md",animate:{opacity:.6},transition:{duration:.3}}),(0,a.jsx)(o(),{href:d.C.advertising.page,children:(0,a.jsx)(i.P.div,{className:"relative",whileTap:{scale:.95},children:(0,a.jsxs)("div",{className:"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 p-2 rounded-full font-medium text-xs relative overflow-hidden shadow-md flex items-center justify-center w-10 h-10",children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),(0,a.jsx)(i.P.div,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none",initial:{x:"-100%"},animate:{x:"100%"},transition:{duration:2,repeat:1/0,ease:"linear",repeatDelay:1}})]})})})]})})]})}},92506:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\AdvertiseButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx","default")},95006:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\MobileFooter.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx","default")},97111:(e,t,r)=>{Promise.resolve().then(r.bind(r,92506)),Promise.resolve().then(r.bind(r,11637)),Promise.resolve().then(r.bind(r,46501)),Promise.resolve().then(r.bind(r,60644)),Promise.resolve().then(r.bind(r,14890)),Promise.resolve().then(r.bind(r,95006)),Promise.resolve().then(r.bind(r,21886)),Promise.resolve().then(r.bind(r,23392))}};