(()=>{var e={};e.id=4396,e.ids=[4396],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8098:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>s.B});var s=t(64275)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24107:(e,r,t)=>{"use strict";function s(e){let{pincode:r,state:t,city:s,locality:i}=e;return!!(r&&""!==r.trim()&&t&&""!==t.trim()&&s&&""!==s.trim()&&i&&""!==i.trim())}function i(e){let r=[];return e.pincode&&""!==e.pincode.trim()||r.push("pincode"),e.state&&""!==e.state.trim()||r.push("state"),e.city&&""!==e.city.trim()||r.push("city"),e.locality&&""!==e.locality.trim()||r.push("locality"),r}function a(e){if(0===e.length)return"";let r=e.map(e=>{switch(e){case"pincode":return"Pincode";case"state":return"State";case"city":return"City";case"locality":return"Locality";default:return e}});if(1===r.length)return`Please update your ${r[0]} in your profile.`;{if(2===r.length)return`Please update your ${r.join(" and ")} in your profile.`;let e=r.pop();return`Please update your ${r.join(", ")}, and ${e} in your profile.`}}t.d(r,{Gs:()=>s,SJ:()=>i,zp:()=>a})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31214:(e,r,t)=>{Promise.resolve().then(t.bind(t,42523))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},42523:(e,r,t)=>{"use strict";t.d(r,{default:()=>b});var s=t(60687),i=t(85814),a=t.n(i),o=t(24934),n=t(58869),d=t(84027),l=t(77882),c=t(58887),u=t(67760),p=t(41312),m=t(64398);function f({title:e,value:r,icon:t,description:i,color:n,href:d}){let l=(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center space-y-4",children:[(0,s.jsx)("div",{className:"p-3 rounded-xl bg-muted",children:(0,s.jsx)(t,{className:`w-6 h-6 ${{blue:"text-blue-600 dark:text-blue-400",indigo:"text-indigo-600 dark:text-indigo-400",purple:"text-purple-600 dark:text-purple-400",rose:"text-rose-600 dark:text-rose-400",red:"text-red-600 dark:text-red-400",yellow:"text-yellow-600 dark:text-yellow-400",brand:"text-amber-600 dark:text-amber-400"}[n]}`})}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:r}),(0,s.jsx)("div",{className:"text-sm font-medium text-muted-foreground",children:e})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:i}),d&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)(o.$,{asChild:!0,variant:"outline",size:"sm",className:"w-full text-xs font-medium",children:(0,s.jsxs)(a(),{href:d,children:["View ",e]})})})]})});return(0,s.jsx)("div",{className:"rounded-xl p-6 bg-card border border-border",children:l})}function h({initialReviewCount:e,initialSubscriptionCount:r,initialLikesCount:t,userId:i}){return(0,s.jsxs)(l.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[(0,s.jsx)("div",{className:"grid grid-cols-1 gap-4",children:(0,s.jsx)(f,{title:"Activity Score",value:e+2*r+t,icon:c.A,description:"Your engagement level",color:"brand"})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,s.jsx)(f,{title:"Likes",value:t,icon:u.A,description:"Businesses you've liked",color:"red",href:"/dashboard/customer/likes"}),(0,s.jsx)(f,{title:"Followers",value:r,icon:p.A,description:"Businesses you're following",color:"blue",href:"/dashboard/customer/subscriptions"}),(0,s.jsx)(f,{title:"Rating",value:e,icon:m.A,description:"Reviews you've left for businesses",color:"yellow",href:"/dashboard/customer/reviews"})]})]})}function b({customerName:e,userId:r,initialReviewCount:t,initialSubscriptionCount:i,initialLikesCount:l}){return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6",children:[(0,s.jsx)("div",{className:"p-3 rounded-xl bg-muted hidden sm:block",children:(0,s.jsx)(n.A,{className:"w-6 h-6 text-foreground"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-foreground",children:["Welcome, ",e]}),(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:"Manage your subscriptions and interactions"})]}),(0,s.jsx)(o.$,{asChild:!0,variant:"outline",size:"sm",children:(0,s.jsxs)(a(),{href:"/dashboard/customer/profile",className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Edit Profile"]})})]}),(0,s.jsx)(h,{initialReviewCount:t,initialSubscriptionCount:i,initialLikesCount:l,userId:r})]})}},44992:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"40397ca1727b354672455d32692e626008a169d968":()=>s.WO,"40930e22958ec27cc9c1459e0e63b2994bcf186979":()=>s.Xd,"40ab7a7f21cb4460091c8fb4edccd341d3b9116d41":()=>s.G_,"40e6b7c827369932950dcd3c394b2c641f20ee0597":()=>s.Hh,"40fbda5e049b7decd80deacff63f96dfec9a104b5a":()=>s.PD,"608517e8bbf255550985eb946a3ce32616cdf4cb5a":()=>s.kJ});var s=t(54694)},54694:(e,r,t)=>{"use strict";t.d(r,{G_:()=>l,Hh:()=>d,PD:()=>n,WO:()=>c,Xd:()=>p,kJ:()=>u});var s=t(67218);t(79130);var i=t(32032),a=t(24107),o=t(39916);async function n(e){let r=await (0,i.createClient)();try{let{data:t,error:s}=await r.from("customer_profiles").select("pincode, state, city, locality, address").eq("id",e).single();if(s)return console.error("Error fetching customer profile for address validation:",s),{isValid:!1,message:"Unable to verify your address information. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your address information"};let i={pincode:t?.pincode,state:t?.state,city:t?.city,locality:t?.locality,address:t?.address};if(!(0,a.Gs)(i)){let e=(0,a.SJ)(i),r=(0,a.zp)(e),t=`/dashboard/customer/profile?message=${encodeURIComponent(r)}`;return{isValid:!1,missingFields:e,message:r,redirectUrl:t}}return{isValid:!0}}catch(e){return console.error("Unexpected error during address validation:",e),{isValid:!1,message:"An error occurred while validating your address. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your address information"}}}async function d(e){let r=await n(e);!r.isValid&&r.redirectUrl&&(0,o.redirect)(r.redirectUrl)}async function l(e){let r=await (0,i.createClient)();try{let{data:t,error:s}=await r.from("customer_profiles").select("name").eq("id",e).single();if(s)return console.error("Error fetching customer profile for name validation:",s),{isValid:!1,message:"Unable to verify your profile information. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your profile information"};if(!(t?.name&&""!==t.name.trim())){let e="Please complete your name in your profile to access the dashboard.",r=`/dashboard/customer/profile?message=${encodeURIComponent(e)}`;return{isValid:!1,message:e,redirectUrl:r}}return{isValid:!0}}catch(e){return console.error("Unexpected error during name validation:",e),{isValid:!1,message:"An error occurred while validating your profile. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your profile information"}}}async function c(e){let r=await l(e);!r.isValid&&r.redirectUrl&&(0,o.redirect)(r.redirectUrl)}async function u(e,r=!1){await c(e),r||await d(e)}async function p(e){let r=await (0,i.createClient)();try{let{data:t,error:s}=await r.from("customer_profiles").select("pincode, state, city, locality, address").eq("id",e).single();if(s)return console.error("Error fetching customer address data:",s),{error:"Failed to fetch address data"};return{data:{pincode:t?.pincode,state:t?.state,city:t?.city,locality:t?.locality,address:t?.address}}}catch(e){return console.error("Unexpected error fetching address data:",e),{error:"An unexpected error occurred"}}}(0,t(17478).D)([n,d,l,c,u,p]),(0,s.A)(n,"40fbda5e049b7decd80deacff63f96dfec9a104b5a",null),(0,s.A)(d,"40e6b7c827369932950dcd3c394b2c641f20ee0597",null),(0,s.A)(l,"40ab7a7f21cb4460091c8fb4edccd341d3b9116d41",null),(0,s.A)(c,"40397ca1727b354672455d32692e626008a169d968",null),(0,s.A)(u,"608517e8bbf255550985eb946a3ce32616cdf4cb5a",null),(0,s.A)(p,"40930e22958ec27cc9c1459e0e63b2994bcf186979",null)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58887:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},61478:(e,r,t)=>{Promise.resolve().then(t.bind(t,81156))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81156:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\customer\\\\components\\\\CustomerDashboardClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92263:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(65239),i=t(48088),a=t(88170),o=t.n(a),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l={children:["",{children:["(dashboard)",{children:["dashboard",{children:["customer",{children:["overview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97823)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,78050)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/customer/overview/page",pathname:"/dashboard/customer/overview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94735:e=>{"use strict";e.exports=require("events")},97823:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>d});var s=t(37413),i=t(32032),a=t(39916),o=t(81156),n=t(54694);let d={title:"Customer Dashboard Overview - Dukancard",robots:"noindex, nofollow"};async function l(){let e=await (0,i.createClient)(),{data:{user:r}}=await e.auth.getUser();if(!r)return(0,a.redirect)("/login?message=Authentication required");await (0,n.kJ)(r.id);let{data:t,error:d}=await e.from("customer_profiles").select("name, email").eq("id",r.id).single();d&&console.error("Error fetching customer profile:",d?.message);let l=t?.name||"Valued Customer",{count:c,error:u}=await e.from("ratings_reviews").select("*",{count:"exact",head:!0}).eq("user_id",r.id);u&&console.error("Error fetching review count:",u);let{count:p,error:m}=await e.from("subscriptions").select("*",{count:"exact",head:!0}).eq("user_id",r.id);m&&console.error("Error fetching subscription count:",m);let{count:f,error:h}=await e.from("likes").select("*",{count:"exact",head:!0}).eq("user_id",r.id);return h&&console.error("Error fetching likes count:",h),(0,s.jsx)(o.default,{customerName:l,userId:r.id,initialReviewCount:c||0,initialSubscriptionCount:p||0,initialLikesCount:f||0})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,8072,3037,3739,9538,5918],()=>t(92263));module.exports=s})();