(()=>{var e={};e.id=633,e.ids=[633],e.modules={841:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\support\\\\settings\\\\SettingsClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10625:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>o.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>p,tree:()=>u});var n=i(65239),a=i(48088),s=i(88170),o=i.n(s),r=i(30893),c={};for(let e in r)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>r[e]);i.d(t,c);let u={children:["",{children:["(main)",{children:["support",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,21942)),"C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,19559)),"C:\\web-app\\dukancard\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(i.bind(i,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(i.bind(i,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx"],l={require:i,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(main)/support/settings/page",pathname:"/support/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14448:(e,t,i)=>{Promise.resolve().then(i.bind(i,20475))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20475:(e,t,i)=>{"use strict";i.d(t,{default:()=>g});var n=i(60687);i(43210);var a=i(58869),s=i(97051),o=i(99891),r=i(85778),c=i(93613),u=i(84027),d=i(63539);let l=[{id:1,question:"How do I change my email address?",answer:"To change your email address, go to Account > Settings in your dashboard. You'll find the email update section at the top of the page. Enter your new email address and click 'Update Email'. You'll need to verify the new email address before the change takes effect."},{id:2,question:"Can I change my username or business slug?",answer:"Yes, you can change your business slug by going to Business Management > Manage Card in your dashboard. Find the 'Business Slug' field, enter your new preferred slug, and click 'Save'. Note that changing your slug will change your public URL, so update any shared links accordingly."},{id:3,question:"How do I update my password?",answer:"To update your password, go to Account > Settings in your dashboard. Scroll down to the 'Password' section. Enter your current password and your new password, then click 'Update Password'. Choose a strong, unique password for better security."},{id:4,question:"How do I manage notification preferences?",answer:"Currently, notification preferences are managed through your email service. You can mark emails from Dukancard as important or filter them based on your preferences. We're working on adding a dedicated notification settings section in a future update."},{id:5,question:"Can I delete my account?",answer:"Yes, you can delete your account by going to Account > Settings in your dashboard. Scroll to the bottom of the page to find the 'Delete Account' section. This action is permanent and will remove all your data from our system, including your digital card, products, and analytics. Consider downloading any important data before proceeding."},{id:6,question:"How do I change my language preferences?",answer:"Currently, Dukancard is available in English only. We're working on adding support for additional languages in future updates. When available, language preferences will be found in the Settings > Preferences section."},{id:7,question:"Can I customize the appearance of my dashboard?",answer:"You can toggle between light and dark mode by clicking on your profile picture in the top right corner of the dashboard and selecting your preferred theme. More customization options for the dashboard will be available in future updates."},{id:8,question:"How do I manage my privacy settings?",answer:"Go to Settings > Privacy to manage your privacy settings. You can control what information is visible on your public card and how your data is used. We recommend reviewing these settings periodically to ensure they align with your preferences."},{id:9,question:"Can I link my social media accounts to my Dukancard account?",answer:"Currently, you can add your social media links to your digital card for display purposes, but direct account linking for login or cross-posting is not available. You can add these links in the Business Management > Manage Card section of your dashboard."},{id:10,question:"How do I update my business information?",answer:"To update your business information, go to Business Management > Manage Card in your dashboard. Here you can modify your business name, address, contact information, and other details. All changes will be immediately reflected on your public digital card."}],p=[{id:"account-settings",title:"Account Settings",icon:(0,n.jsx)(a.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Managing Your Profile Information",steps:["Log in to your Dukancard account and navigate to Account > Settings.","Review your current profile information including your email address.","To update your email, enter the new email address in the 'Email Update' section.","Click 'Update Email' to apply the changes.","You'll need to verify the new email address before the change takes effect."],image:"/support/profile-settings.jpg",imageAlt:"Profile settings page",tip:"Keep your contact information up to date to ensure you receive important account notifications and can recover your account if needed."},{title:"Changing Your Password",steps:["Go to Account > Settings in your dashboard.","Scroll down to the 'Password' section.","Enter your current password for verification.","Create a new strong password and enter it in the 'New Password' field.","Click 'Update Password' to save your new password."],image:"/support/password-change.jpg",imageAlt:"Password change interface",tip:"Use a strong, unique password that includes a mix of uppercase and lowercase letters, numbers, and special characters. Consider using a password manager to keep track of your passwords securely."},{title:"Updating Your Business Slug",steps:["Navigate to Business Management > Manage Card in your dashboard.","Find the 'Business Slug' field in the business information section.","Enter your new preferred slug (this will change your public URL).","Ensure the slug is simple, memorable, and related to your business.","Click 'Save' to update your slug and public URL (dukancard.in/your-slug)."],image:"/support/slug-settings.jpg",imageAlt:"Business slug settings",tip:"After changing your slug, update any shared links or QR codes to reflect your new URL. Your old URL will no longer work, so inform your contacts about the change."}]},{id:"notification-preferences",title:"Notification Preferences",icon:(0,n.jsx)(s.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Managing Email Notifications",steps:["Go to Settings > Notifications in your dashboard.","Review the list of available notification types.","Toggle the switch next to each notification type to enable or disable it.","Email notifications can include card views, contact button clicks, and system updates.","Click 'Save Preferences' to apply your changes."],image:"/support/email-notifications.jpg",imageAlt:"Email notification settings",tip:"Enable notifications for important business activities like contact button clicks, but consider disabling less critical notifications to avoid email overload."},{title:"Setting Up Digest Preferences",steps:["In the Notifications section, find the 'Digest Emails' option.","Choose your preferred frequency for digest emails (daily, weekly, or monthly).","Select which metrics and updates you want to include in your digest.","Digests can include summaries of card views, engagement metrics, and system updates.","Click 'Save Preferences' to apply your digest settings."],image:"/support/digest-settings.jpg",imageAlt:"Digest email settings",tip:"Weekly digests offer a good balance, providing regular updates without overwhelming your inbox. They're great for tracking trends over time."},{title:"Managing System Notifications",steps:["Navigate to the 'System Notifications' section in Settings > Notifications.","Review the types of system notifications available.","Toggle notifications for account security, feature updates, and maintenance alerts.","Choose your preferred notification channels for urgent alerts.","Click 'Save Preferences' to apply your settings."],image:"/support/system-notifications.jpg",imageAlt:"System notification settings",tip:"Always keep security notifications enabled to stay informed about important account security events or potential issues."}]},{id:"privacy-security",title:"Privacy & Security",icon:(0,n.jsx)(o.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),content:[{title:"Managing Privacy Settings",steps:["Go to Settings > Privacy in your dashboard.","Review the current visibility settings for your profile information.","Choose which elements of your business information are visible on your public card.","Adjust settings for analytics tracking and data usage.","Click 'Save Privacy Settings' to apply your changes."],image:"/support/privacy-settings.jpg",imageAlt:"Privacy settings page",tip:"Balance privacy with business needs. While protecting sensitive information is important, ensuring customers can easily contact you is essential for business growth."},{title:"Enhancing Account Security",steps:["Navigate to Settings > Account > Security.","Review your current security settings and recent account activity.","Enable additional security features like login notifications.","Consider changing your password regularly (every 3-6 months).","Review the devices and locations that have accessed your account."],image:"/support/security-settings.jpg",imageAlt:"Security settings page",tip:"If you notice any suspicious activity in your account access history, immediately change your password and contact our support team."},{title:"Managing Data and Exports",steps:["Go to Settings > Privacy > Data Management.","Review what data is stored in your account.","Use the 'Export Data' option to download a copy of your account data.","Learn about data retention policies and how your information is protected.","Find options for deleting specific data or your entire account if needed."],image:"/support/data-management.jpg",imageAlt:"Data management settings",tip:"Periodically export your data for your records, especially before making significant changes to your account or business information."}]}];function g(){let e=[{title:"Account & Billing",description:"Manage your account, subscription, and billing information",icon:(0,n.jsx)(r.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/account-billing"},{title:"Technical Issues",description:"Find solutions to common technical problems and troubleshooting",icon:(0,n.jsx)(c.A,{className:"w-6 h-6 text-[var(--brand-gold)]"}),href:"/support/technical-issues"}];return(0,n.jsx)(d.A,{title:"Settings & Preferences",description:"Learn how to configure your account settings, manage notifications, and customize your Dukancard experience.",icon:(0,n.jsx)(u.A,{className:"w-12 h-12 text-[var(--brand-gold)]"}),quickHelp:[{title:"Account Settings Tips:",items:[{text:"Update your email in Account → Settings"},{text:"Change business slug in Business Management → Manage Card"},{text:"Use a strong, unique password for better security"}]},{title:"Security Tips:",items:[{text:"Change your password regularly (every 3 months)"},{text:"Don't share your login credentials with others"},{text:"Log out when using shared or public devices"}]}],guideSections:p,faqs:l,relatedResources:e,navigationButtons:[{label:"Account Settings",href:"#account-settings"},{label:"Notifications",href:"#notification-preferences"},{label:"Privacy & Security",href:"#privacy-security"},{label:"FAQs",href:"#faqs"}]})}},21942:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r,generateMetadata:()=>o});var n=i(37413),a=i(841),s=i(54670);async function o(){let e="Settings & Preferences",t="Learn how to configure your Dukancard account settings and preferences. Get help with managing your profile, notification preferences, privacy settings, and more.",i="http://localhost:3000",n=`${i}/support/settings`,a=`${i}/opengraph-image.png`;return{title:e,description:t,keywords:["Dukancard settings","account preferences","profile management","notification settings","privacy settings","account configuration"],alternates:{canonical:"/support/settings"},openGraph:{title:e,description:t,url:n,siteName:s.C.name,type:"website",locale:"en_IN",images:[{url:a,width:1200,height:630,alt:`${s.C.name} Settings & Preferences Guide`}]},twitter:{card:"summary_large_image",title:e,description:t,images:[a]},other:{"application-ld+json":JSON.stringify({"@context":"https://schema.org","@type":"WebPage",name:e,description:t,url:n,isPartOf:{"@type":"WebSite",name:s.C.name,url:i}})}}}function r(){return(0,n.jsx)(a.default,{})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56304:(e,t,i)=>{Promise.resolve().then(i.bind(i,841))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84027:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},97051:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(62688).A)("Bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},99891:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),n=t.X(0,[4447,6724,2997,1107,7065,9389,3037,6177,1374],()=>i(10625));module.exports=n})();