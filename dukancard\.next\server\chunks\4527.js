"use strict";exports.id=4527,exports.ids=[4527],exports.modules={44527:(s,i,e)=>{e.d(i,{SubscriptionStateManager:()=>t.ew}),e(63032);var t=e(11798);e(76881);async function r(s){try{let i=await createClient();console.log(`[ATOMIC_TRANSACTION] Using atomic RPC for subscription ${s.subscription_id}`);let{data:e,error:t}=await i.rpc("update_subscription_atomic",{p_subscription_id:s.subscription_id,p_new_status:s.subscription_status,p_business_profile_id:s.business_profile_id,p_has_active_subscription:s.has_active_subscription,p_additional_data:s.additional_data||{},p_webhook_timestamp:s.additional_data?.last_webhook_timestamp||void 0});if(t)return console.error(`[ATOMIC_TRANSACTION] RPC error for ${s.subscription_id}:`,t),{success:!1,message:`RPC error: ${t.message}`};if(!e?.success)return console.error(`[ATOMIC_TRANSACTION] RPC function returned error for ${s.subscription_id}:`,e),{success:!1,message:e?.error||"Unknown RPC error"};return console.log(`[ATOMIC_TRANSACTION] Successfully updated subscription ${s.subscription_id} atomically`),{success:!0,message:"Atomic transaction completed successfully via RPC"}}catch(s){return console.error("[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:",s),{success:!1,message:`Atomic transaction exception: ${s instanceof Error?s.message:String(s)}`}}}}};