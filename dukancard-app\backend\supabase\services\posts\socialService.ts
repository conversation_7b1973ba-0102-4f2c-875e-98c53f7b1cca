/**
 * Social Service for React Native
 * Handles subscriptions, likes, and reviews data fetching and management
 */

import { supabase } from "@/lib/supabase";
import { Tables } from "@dukancard-types/supabase";
import { TABLES, COLUMNS } from "@/src/config/supabase/constants";

export interface ActivityMetrics {
  likesCount: number;
  reviewCount: number;
  subscriptionCount: number;
  lastUpdated: string;
}

// Types for subscriptions
export interface SubscriptionWithProfile {
  id: string;
  business_profiles: Tables<'business_profiles'> | null;
}

export interface SubscriptionsResult {
  items: SubscriptionWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Types for likes
export interface LikeWithProfile {
  id: string;
  business_profiles: Tables<'business_profiles'> | null;
}

export interface LikesResult {
  items: LikeWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Types for reviews
export interface ReviewBusinessProfile {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

export interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: ReviewBusinessProfile | null;
}

export interface ReviewsResult {
  items: ReviewData[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Subscriptions Service
 */
export const subscriptionsService = {
  /**
   * Fetch user subscriptions with pagination and search
   */
  async fetchSubscriptions(
    userId: string,
    page: number = 1,
    limit: number = 20,
    searchTerm: string = ""
  ): Promise<SubscriptionsResult> {
    try {
      // Get total count first with separate query to avoid count issues with joins
      let countQuery = supabase
        .from(TABLES.SUBSCRIPTIONS)
        .select(
          `
          ${COLUMNS.ID},
          ${TABLES.BUSINESS_PROFILES}!inner (
            ${COLUMNS.ID},
            ${COLUMNS.BUSINESS_NAME}
          )
        `,
          { count: "exact", head: true }
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter to count query if provided
      if (searchTerm && searchTerm.trim()) {
        countQuery = countQuery.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      const { count: totalCount, error: countError } = await countQuery;

      if (countError) {
        throw new Error(`Failed to get subscriptions count: ${countError.message}`);
      }

      // If no subscriptions, return empty result
      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Build the main query for fetching data
      let query = supabase
        .from(TABLES.SUBSCRIPTIONS)
        .select(
          `
          ${COLUMNS.ID},
          ${COLUMNS.BUSINESS_PROFILE_ID},
          ${TABLES.BUSINESS_PROFILES}!inner (*)
        `
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter if provided
      if (searchTerm && searchTerm.trim()) {
        query = query.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      // Apply pagination
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data: subscriptionsWithProfiles, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch subscriptions: ${error.message}`);
      }

      // Transform the data
      const transformedSubscriptions: SubscriptionWithProfile[] = (
        subscriptionsWithProfiles || []
      ).map(
        (sub: {
          id: string;
          business_profiles: Tables<'business_profiles'> | Tables<'business_profiles'>[];
        }) => ({
          id: sub.id,
          business_profiles: Array.isArray(sub.business_profiles)
            ? sub.business_profiles[0]
            : sub.business_profiles,
        })
      );

      const hasMore = totalCount > offset + limit;

      return {
        items: transformedSubscriptions,
        totalCount,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error in fetchSubscriptions:", error);
      throw error;
    }
  },

  /**
   * Unsubscribe from a business
   */
  async unsubscribe(subscriptionId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("subscriptions")
        .delete()
        .eq("id", subscriptionId);

      if (error) {
        throw new Error(`Failed to unsubscribe: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in unsubscribe:", error);
      throw error;
    }
  },
};

/**
 * Likes Service
 */
export const likesService = {
  /**
   * Fetch user likes with pagination and search
   */
  async fetchLikes(
    userId: string,
    page: number = 1,
    limit: number = 20,
    searchTerm: string = ""
  ): Promise<LikesResult> {
    try {
      // Build query with proper joins and filtering
      let query = supabase
        .from(TABLES.LIKES)
        .select(
          `
          ${COLUMNS.ID},
          ${TABLES.BUSINESS_PROFILES}!inner (*)
        `
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter if provided
      if (searchTerm && searchTerm.trim()) {
        query = query.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      // Get total count for pagination
      let countQuery = supabase
        .from(TABLES.LIKES)
        .select(
          `
          ${COLUMNS.ID},
          ${TABLES.BUSINESS_PROFILES}!inner (
            ${COLUMNS.ID},
            ${COLUMNS.BUSINESS_NAME}
          )
        `,
          { count: "exact", head: true }
        )
        .eq(COLUMNS.USER_ID, userId);

      if (searchTerm && searchTerm.trim()) {
        countQuery = countQuery.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      const { count: totalCount, error: countError } = await countQuery;

      if (countError) {
        throw new Error(`Failed to get likes count: ${countError.message}`);
      }

      // If no likes, return empty result
      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Apply pagination to the main query
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data: likesWithProfiles, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch likes: ${error.message}`);
      }

      // Transform the data
      const transformedLikes: LikeWithProfile[] = (likesWithProfiles || []).map(
        (like: {
          id: string;
          business_profiles: Tables<'business_profiles'> | Tables<'business_profiles'>[];
        }) => ({
          id: like.id,
          business_profiles: Array.isArray(like.business_profiles)
            ? like.business_profiles[0]
            : like.business_profiles,
        })
      );

      const hasMore = totalCount > offset + limit;

      return {
        items: transformedLikes,
        totalCount,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error in fetchLikes:", error);
      throw error;
    }
  },

  /**
   * Unlike a business
   */
  async unlike(likeId: string): Promise<void> {
    try {
      const { error } = await supabase.from("likes").delete().eq("id", likeId);

      if (error) {
        throw new Error(`Failed to unlike: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in unlike:", error);
      throw error;
    }
  },
};

/**
 * Reviews Service
 */
export const reviewsService = {
  /**
   * Fetch user reviews with pagination and sorting
   */
  async fetchReviews(
    userId: string,
    page: number = 1,
    limit: number = 20,
    sortBy: "newest" | "oldest" | "rating_high" | "rating_low" = "newest",
    searchTerm: string = ""
  ): Promise<ReviewsResult> {
    try {
      // Get total count first with separate query to avoid count issues with joins
      let countQuery = supabase
        .from(TABLES.RATINGS_REVIEWS)
        .select(
          `
          ${COLUMNS.ID},
          ${TABLES.BUSINESS_PROFILES}!inner (
            ${COLUMNS.ID},
            ${COLUMNS.BUSINESS_NAME}
          )
        `,
          { count: "exact", head: true }
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter to count query if provided
      if (searchTerm && searchTerm.trim()) {
        countQuery = countQuery.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      const { count: totalCount, error: countError } = await countQuery;

      if (countError) {
        throw new Error(`Failed to get reviews count: ${countError.message}`);
      }

      // If no reviews, return empty result
      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Build the main query for fetching data
      let query = supabase
        .from(TABLES.RATINGS_REVIEWS)
        .select(
          `
          ${COLUMNS.ID},
          ${COLUMNS.RATING},
          ${COLUMNS.REVIEW_TEXT},
          ${COLUMNS.CREATED_AT},
          ${COLUMNS.UPDATED_AT},
          ${COLUMNS.BUSINESS_PROFILE_ID},
          ${COLUMNS.USER_ID},
          ${TABLES.BUSINESS_PROFILES}!inner (
            ${COLUMNS.ID},
            ${COLUMNS.BUSINESS_NAME},
            ${COLUMNS.BUSINESS_SLUG},
            ${COLUMNS.LOGO_URL}
          )
        `
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter if provided
      if (searchTerm && searchTerm.trim()) {
        query = query.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      // Apply sorting
      switch (sortBy) {
        case "oldest":
          query = query.order(COLUMNS.CREATED_AT, { ascending: true });
          break;
        case "rating_high":
          query = query.order(COLUMNS.RATING, { ascending: false });
          break;
        case "rating_low":
          query = query.order(COLUMNS.RATING, { ascending: true });
          break;
        case "newest":
        default:
          query = query.order(COLUMNS.CREATED_AT, { ascending: false });
          break;
      }

      // Apply pagination
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data: reviews, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch reviews: ${error.message}`);
      }

      // Transform the data (business profiles are already joined)
      const transformedReviews: ReviewData[] = (reviews || []).map((review: {
        id: string;
        rating: number;
        review_text: string | null;
        created_at: string;
        updated_at: string;
        business_profile_id: string;
        user_id: string;
        business_profiles: ReviewBusinessProfile | ReviewBusinessProfile[];
      }) => ({
        id: review.id,
        rating: review.rating,
        review_text: review.review_text,
        created_at: review.created_at,
        updated_at: review.updated_at,
        business_profile_id: review.business_profile_id,
        user_id: review.user_id,
        business_profiles: Array.isArray(review.business_profiles)
          ? review.business_profiles[0]
          : review.business_profiles,
      }));

      const hasMore = totalCount > offset + limit;

      return {
        items: transformedReviews,
        totalCount,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error in fetchReviews:", error);
      throw error;
    }
  },

  /**
   * Delete a review
   */
  async deleteReview(reviewId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("ratings_reviews")
        .delete()
        .eq("id", reviewId);

      if (error) {
        throw new Error(`Failed to delete review: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in deleteReview:", error);
      throw error;
    }
  },

  /**
   * Update a review
   */
  async updateReview(
    reviewId: string,
    rating: number,
    reviewText: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from("ratings_reviews")
        .update({
          rating,
          review_text: reviewText || null,
          updated_at: new Date().toISOString(),
        })
        .eq("id", reviewId);

      if (error) {
        throw new Error(`Failed to update review: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in updateReview:", error);
      throw error;
    }
  },
};

/**
 * Get customer activity metrics with caching support
 */
export async function getActivityMetrics(
  userId: string
): Promise<ActivityMetrics | null> {
  try {
    // Try to get cached metrics first if useCache is true

    // Fetch fresh metrics from database
    const [likesResult, reviewsResult, subscriptionsResult] = await Promise.all(
      [
        supabase
          .from("likes")
          .select("id", { count: "exact" })
          .eq("user_id", userId),
        supabase
          .from("ratings_reviews")
          .select("id", { count: "exact" })
          .eq("user_id", userId),
        supabase
          .from("subscriptions")
          .select("id", { count: "exact" })
          .eq("user_id", userId),
      ]
    );

    const metrics: ActivityMetrics = {
      likesCount: likesResult.count || 0,
      reviewCount: reviewsResult.count || 0,
      subscriptionCount: subscriptionsResult.count || 0,
      lastUpdated: new Date().toISOString(),
    };

    // Cache the fresh metrics

    return metrics;
  } catch (error) {
    console.error("Error fetching activity metrics:", error);

    return null;
  }
}
