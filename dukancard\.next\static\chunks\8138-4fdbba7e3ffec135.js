(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8138],{3096:(e,t,n)=>{"use strict";n.d(t,{Wx:()=>c});var r=n(12115),i=Object.defineProperty,o=(e,t,n)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,s=new Map,l=new WeakMap,a=0,u=void 0;function c(){var e;let{threshold:t,delay:n,trackVisibility:i,rootMargin:o,root:c,triggerOnce:d,skip:h,initialInView:f,fallbackInView:p,onChange:m}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[v,y]=r.useState(null),_=r.useRef(m),[g,S]=r.useState({inView:!!f,entry:void 0});_.current=m,r.useEffect(()=>{let e;if(!h&&v)return e=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:u;if(void 0===window.IntersectionObserver&&void 0!==r){let i=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof n.threshold?n.threshold:0,time:0,boundingClientRect:i,intersectionRect:i,rootBounds:i}),()=>{}}let{id:i,observer:o,elements:c}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var n;return"".concat(t,"_").concat("root"===t?(n=e.root)?(l.has(n)||(a+=1,l.set(n,a.toString())),l.get(n)):"0":e[t])}).toString(),n=s.get(t);if(!n){let r,i=new Map,o=new IntersectionObserver(t=>{t.forEach(t=>{var n;let o=t.isIntersecting&&r.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=o),null==(n=i.get(t.target))||n.forEach(e=>{e(o,t)})})},e);r=o.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:o,elements:i},s.set(t,n)}return n}(n),d=c.get(e)||[];return c.has(e)||c.set(e,d),d.push(t),o.observe(e),function(){d.splice(d.indexOf(t),1),0===d.length&&(c.delete(e),o.unobserve(e)),0===c.size&&(o.disconnect(),s.delete(i))}}(v,(t,n)=>{S({inView:t,entry:n}),_.current&&_.current(t,n),n.isIntersecting&&d&&e&&(e(),e=void 0)},{root:c,rootMargin:o,threshold:t,trackVisibility:i,delay:n},p),()=>{e&&e()}},[Array.isArray(t)?t.toString():t,v,c,o,d,h,i,p,n]);let w=null==(e=g.entry)?void 0:e.target,b=r.useRef(void 0);v||!w||d||h||b.current===w||(b.current=w,S({inView:!!f,entry:void 0}));let A=[y,g.inView,g.entry];return A.ref=A[0],A.inView=A[1],A.entry=A[2],A}r.Component},4516:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5845:(e,t,n)=>{"use strict";n.d(t,{i:()=>l});var r,i=n(12115),o=n(52712),s=(r||(r=n.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),l=i.useRef(t);return s(()=>{l.current=t},[t]),i.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,i.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}Symbol("RADIX:SYNC_STATE")},11518:(e,t,n)=>{"use strict";e.exports=n(82269).style},16785:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},19420:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},28883:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},28905:(e,t,n)=>{"use strict";n.d(t,{C:()=>s});var r=n(12115),i=n(6101),o=n(52712),s=e=>{let{present:t,children:n}=e,s=function(e){var t,n;let[i,s]=r.useState(),a=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[d,h]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(a.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=a.current,n=u.current;if(n!==e){let r=c.current,i=l(t);e?h("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?h("UNMOUNT"):n&&r!==i?h("ANIMATION_OUT"):h("UNMOUNT"),u.current=e}},[e,h]),(0,o.N)(()=>{if(i){var e;let t,n=null!=(e=i.ownerDocument.defaultView)?e:window,r=e=>{let r=l(a.current).includes(e.animationName);if(e.target===i&&r&&(h("ANIMATION_END"),!u.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(c.current=l(a.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}h("ANIMATION_END")},[i,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,s(e)},[])}}(t),a="function"==typeof n?n({present:s.isPresent}):r.Children.only(n),u=(0,i.s)(s.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||s.isPresent?r.cloneElement(a,{ref:u}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}s.displayName="Presence"},33109:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},34869:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},52292:(e,t,n)=>{"use strict";n.d(t,{UC:()=>$,Y9:()=>H,bL:()=>B,l9:()=>W,q7:()=>G});var r=n(12115),i=n(46081),o=n(57683),s=n(6101),l=n(85185),a=n(5845),u=n(63540),c=n(88106),d=n(61285),h=n(94315),f=n(95155),p="Accordion",m=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[v,y,_]=(0,o.N)(p),[g,S]=(0,i.A)(p,[_,c.z3]),w=(0,c.z3)(),b=r.forwardRef((e,t)=>{let{type:n,...r}=e;return(0,f.jsx)(v.Provider,{scope:e.__scopeAccordion,children:"multiple"===n?(0,f.jsx)(N,{...r,ref:t}):(0,f.jsx)(k,{...r,ref:t})})});b.displayName=p;var[A,R]=g(p),[x,C]=g(p,{collapsible:!1}),k=r.forwardRef((e,t)=>{let{value:n,defaultValue:i,onValueChange:o=()=>{},collapsible:s=!1,...l}=e,[u,c]=(0,a.i)({prop:n,defaultProp:null!=i?i:"",onChange:o,caller:p});return(0,f.jsx)(A,{scope:e.__scopeAccordion,value:r.useMemo(()=>u?[u]:[],[u]),onItemOpen:c,onItemClose:r.useCallback(()=>s&&c(""),[s,c]),children:(0,f.jsx)(x,{scope:e.__scopeAccordion,collapsible:s,children:(0,f.jsx)(z,{...l,ref:t})})})}),N=r.forwardRef((e,t)=>{let{value:n,defaultValue:i,onValueChange:o=()=>{},...s}=e,[l,u]=(0,a.i)({prop:n,defaultProp:null!=i?i:[],onChange:o,caller:p}),c=r.useCallback(e=>u(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[u]),d=r.useCallback(e=>u(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[u]);return(0,f.jsx)(A,{scope:e.__scopeAccordion,value:l,onItemOpen:c,onItemClose:d,children:(0,f.jsx)(x,{scope:e.__scopeAccordion,collapsible:!0,children:(0,f.jsx)(z,{...s,ref:t})})})}),[j,I]=g(p),z=r.forwardRef((e,t)=>{let{__scopeAccordion:n,disabled:i,dir:o,orientation:a="vertical",...c}=e,d=r.useRef(null),p=(0,s.s)(d,t),_=y(n),g="ltr"===(0,h.jH)(o),S=(0,l.m)(e.onKeyDown,e=>{var t;if(!m.includes(e.key))return;let n=e.target,r=_().filter(e=>{var t;return!(null==(t=e.ref.current)?void 0:t.disabled)}),i=r.findIndex(e=>e.ref.current===n),o=r.length;if(-1===i)return;e.preventDefault();let s=i,l=o-1,u=()=>{(s=i+1)>l&&(s=0)},c=()=>{(s=i-1)<0&&(s=l)};switch(e.key){case"Home":s=0;break;case"End":s=l;break;case"ArrowRight":"horizontal"===a&&(g?u():c());break;case"ArrowDown":"vertical"===a&&u();break;case"ArrowLeft":"horizontal"===a&&(g?c():u());break;case"ArrowUp":"vertical"===a&&c()}null==(t=r[s%o].ref.current)||t.focus()});return(0,f.jsx)(j,{scope:n,disabled:i,direction:o,orientation:a,children:(0,f.jsx)(v.Slot,{scope:n,children:(0,f.jsx)(u.sG.div,{...c,"data-orientation":a,ref:p,onKeyDown:i?void 0:S})})})}),O="AccordionItem",[F,T]=g(O),M=r.forwardRef((e,t)=>{let{__scopeAccordion:n,value:r,...i}=e,o=I(O,n),s=R(O,n),l=w(n),a=(0,d.B)(),u=r&&s.value.includes(r)||!1,h=o.disabled||e.disabled;return(0,f.jsx)(F,{scope:n,open:u,disabled:h,triggerId:a,children:(0,f.jsx)(c.bL,{"data-orientation":o.orientation,"data-state":V(u),...l,...i,ref:t,disabled:h,open:u,onOpenChange:e=>{e?s.onItemOpen(r):s.onItemClose(r)}})})});M.displayName=O;var E="AccordionHeader",D=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,i=I(p,n),o=T(E,n);return(0,f.jsx)(u.sG.h3,{"data-orientation":i.orientation,"data-state":V(o.open),"data-disabled":o.disabled?"":void 0,...r,ref:t})});D.displayName=E;var P="AccordionTrigger",U=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,i=I(p,n),o=T(P,n),s=C(P,n),l=w(n);return(0,f.jsx)(v.ItemSlot,{scope:n,children:(0,f.jsx)(c.l9,{"aria-disabled":o.open&&!s.collapsible||void 0,"data-orientation":i.orientation,id:o.triggerId,...l,...r,ref:t})})});U.displayName=P;var L="AccordionContent",q=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,i=I(p,n),o=T(L,n),s=w(n);return(0,f.jsx)(c.UC,{role:"region","aria-labelledby":o.triggerId,"data-orientation":i.orientation,...s,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function V(e){return e?"open":"closed"}q.displayName=L;var B=b,G=M,H=D,W=U,$=q},61285:(e,t,n)=>{"use strict";n.d(t,{B:()=>a});var r,i=n(12115),o=n(52712),s=(r||(r=n.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function a(e){let[t,n]=i.useState(s());return(0,o.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},63578:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("Map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]])},66474:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},68375:()=>{},71539:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},82269:(e,t,n)=>{"use strict";var r=n(49509);n(68375);var i=n(12115),o=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),s=void 0!==r&&r.env&&!0,l=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,i=t.optimizeForSpeed,o=void 0===i?s:i;u(l(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",u("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var a="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=a?a.getAttribute("content"):null}var t,n=e.prototype;return n.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},n.isOptimizeForSpeed=function(){return this._optimizeForSpeed},n.inject=function(){var e=this;if(u(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(s||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},n.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},n.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},n.insertRule=function(e,t){if(u(l(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(t){return s||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},n.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var n="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(r){s||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];u(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},n.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];u(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},n.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},n.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},n.makeStyleTag=function(e,t,n){t&&u(l(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return n?i.insertBefore(r,n):i.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},d={};function h(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return d[r]||(d[r]="jsx-"+c(e+"-"+n)),d[r]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var n=e+t;return d[n]||(d[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[n]}var p=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,i=t.optimizeForSpeed,o=void 0!==i&&i;this._sheet=r||new a({name:"styled-jsx",optimizeForSpeed:o}),this._sheet.inject(),r&&"boolean"==typeof o&&(this._sheet.setOptimizeForSpeed(o),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,i=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var o=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=o,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return o.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var i=h(r,n);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return f(i,e)}):[f(i,t)]}}return{styleId:h(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=i.createContext(null);m.displayName="StyleSheetContext";var v=o.default.useInsertionEffect||o.default.useLayoutEffect,y="undefined"!=typeof window?new p:void 0;function _(e){var t=y||i.useContext(m);return t&&("undefined"==typeof window?t.add(e):v(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}_.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=_},85185:(e,t,n)=>{"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{m:()=>r})},88106:(e,t,n)=>{"use strict";n.d(t,{Ke:()=>b,R6:()=>S,UC:()=>k,bL:()=>x,l9:()=>C,z3:()=>m});var r=n(12115),i=n(85185),o=n(46081),s=n(5845),l=n(52712),a=n(6101),u=n(63540),c=n(28905),d=n(61285),h=n(95155),f="Collapsible",[p,m]=(0,o.A)(f),[v,y]=p(f),_=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:i,defaultOpen:o,disabled:l,onOpenChange:a,...c}=e,[p,m]=(0,s.i)({prop:i,defaultProp:null!=o&&o,onChange:a,caller:f});return(0,h.jsx)(v,{scope:n,disabled:l,contentId:(0,d.B)(),open:p,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),children:(0,h.jsx)(u.sG.div,{"data-state":R(p),"data-disabled":l?"":void 0,...c,ref:t})})});_.displayName=f;var g="CollapsibleTrigger",S=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,o=y(g,n);return(0,h.jsx)(u.sG.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":R(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:t,onClick:(0,i.m)(e.onClick,o.onOpenToggle)})});S.displayName=g;var w="CollapsibleContent",b=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,i=y(w,e.__scopeCollapsible);return(0,h.jsx)(c.C,{present:n||i.open,children:e=>{let{present:n}=e;return(0,h.jsx)(A,{...r,ref:t,present:n})}})});b.displayName=w;var A=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:i,children:o,...s}=e,c=y(w,n),[d,f]=r.useState(i),p=r.useRef(null),m=(0,a.s)(t,p),v=r.useRef(0),_=v.current,g=r.useRef(0),S=g.current,b=c.open||d,A=r.useRef(b),x=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>A.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=p.current;if(e){x.current=x.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();v.current=t.height,g.current=t.width,A.current||(e.style.transitionDuration=x.current.transitionDuration,e.style.animationName=x.current.animationName),f(i)}},[c.open,i]),(0,h.jsx)(u.sG.div,{"data-state":R(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!b,...s,ref:m,style:{"--radix-collapsible-content-height":_?"".concat(_,"px"):void 0,"--radix-collapsible-content-width":S?"".concat(S,"px"):void 0,...e.style},children:b&&o})});function R(e){return e?"open":"closed"}var x=_,C=S,k=b},92138:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}}]);