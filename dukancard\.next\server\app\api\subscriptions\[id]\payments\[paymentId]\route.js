(()=>{var e={};e.id=6864,e.ids=[6864],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11107:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>l});var i=r(96559),a=r(48088),n=r(37719),u=r(32190),o=r(32032);async function l(e,{params:t}){try{let{id:e,paymentId:r}=await t,s=await (0,o.createClient)(),{data:{user:i},error:a}=await s.auth.getUser();if(a||!i)return u.NextResponse.json({error:"Authentication required"},{status:401});let{data:n,error:l}=await s.from("payment_subscriptions").select("id, business_profile_id, razorpay_subscription_id").eq("razorpay_subscription_id",e).single();if(l||!n)return console.error("Error fetching subscription:",l),u.NextResponse.json({error:"Could not fetch subscription details"},{status:500});let{data:c,error:p}=await s.from("business_profiles").select("id").eq("id",n.business_profile_id).single();if(p||!c||c.id!==i.id)return u.NextResponse.json({error:"Unauthorized to access this subscription"},{status:403});return u.NextResponse.json({success:!0,data:{payment_id:r,subscription_id:e,status:"Not implemented with Razorpay yet"}})}catch(e){return console.error("Error in payment details API:",e),u.NextResponse.json({error:"An unexpected error occurred"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/subscriptions/[id]/payments/[paymentId]/route",pathname:"/api/subscriptions/[id]/payments/[paymentId]",filename:"route",bundlePath:"app/api/subscriptions/[id]/payments/[paymentId]/route"},resolvedPagePath:"C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\[paymentId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:m}=c;function y(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createClient:()=>i});var s=r(34386);async function i(){let e="https://rnjolcoecogzgglnblqn.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!t)throw Error("Supabase environment variables are not set.");let i=null,a=null;try{let{headers:e,cookies:t}=await r.e(4999).then(r.bind(r,44999));i=await e(),a=await t()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||i&&"true"===i.get("x-playwright-testing"))&&i?function(e){let t=e.get("x-test-auth-state"),r=e.get("x-test-user-type"),s="customer"===r||"business"===r,i=e.get("x-test-business-slug"),a=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===t?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===t?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,t,r,s,i){let a=()=>{var a,n,u,o,l;return a=e,n=t,u=r,o=s,l=i,"customer_profiles"===a?{data:u&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===a?{data:u&&"business"===n?{id:"test-user-id",business_slug:o||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===a?{data:"business"===n?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===a?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:t=>n(e),eq:(t,r)=>n(e),neq:(t,r)=>n(e),gt:(t,r)=>n(e),gte:(t,r)=>n(e),lt:(t,r)=>n(e),lte:(t,r)=>n(e),like:(t,r)=>n(e),ilike:(t,r)=>n(e),is:(t,r)=>n(e),in:(t,r)=>n(e),contains:(t,r)=>n(e),containedBy:(t,r)=>n(e),rangeGt:(t,r)=>n(e),rangeGte:(t,r)=>n(e),rangeLt:(t,r)=>n(e),rangeLte:(t,r)=>n(e),rangeAdjacent:(t,r)=>n(e),overlaps:(t,r)=>n(e),textSearch:(t,r)=>n(e),match:t=>n(e),not:(t,r,s)=>n(e),or:t=>n(e),filter:(t,r,s)=>n(e),order:(t,r)=>n(e),limit:(t,r)=>n(e),range:(t,r,s)=>n(e),abortSignal:t=>n(e),single:async()=>a(),maybeSingle:async()=>a(),then:async e=>{let t=a();return e?e(t):t},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:t=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),then:async t=>{let r={data:Array.isArray(e)?e:[e],error:null};return t?t(r):r}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,t)=>n()}})(e,r,s,i,a)}}(i):a?(0,s.createServerClient)(e,t,{cookies:{getAll:async()=>await a.getAll(),async setAll(e){try{for(let{name:t,value:r,options:s}of e)await a.set(t,r,s)}catch{}}}}):(0,s.createServerClient)(e,t,{cookies:{getAll:()=>[],setAll(){}}})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,9398,4386,580],()=>r(11107));module.exports=s})();