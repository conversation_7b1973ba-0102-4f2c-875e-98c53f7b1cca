[{"C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts": "1", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx": "2", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx": "3", "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx": "4", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\actions.ts": "5", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\DailyVisitTrendChart.tsx": "6", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedAnalyticsPageClient.tsx": "7", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedChartCard.tsx": "8", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedEngagementMetricsSection.tsx": "9", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedMetricCard.tsx": "10", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedVisitMetricsSection.tsx": "11", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\HourlyVisitTrendChart.tsx": "12", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\MonthlyVisitTrendChart.tsx": "13", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\PremiumFeatureLock.tsx": "14", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\page.tsx": "15", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customAdUpload.ts": "16", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customHeaderUpload.ts": "17", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts": "18", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeSpecificHeaderUpload.ts": "19", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts": "20", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts": "21", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts": "22", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx": "23", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx": "24", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx": "25", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx": "26", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx": "27", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx": "28", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx": "29", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx": "30", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx": "31", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx": "32", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx": "33", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx": "34", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomAdUpload.tsx": "35", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomBrandingSection.tsx": "36", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts": "37", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx": "38", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx": "39", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx": "40", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ThemeSpecificHeaderUpload.tsx": "41", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx": "42", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx": "43", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx": "44", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx": "45", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx": "46", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx": "47", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts": "48", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx": "49", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx": "50", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts": "51", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts": "52", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts": "53", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx": "54", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx": "55", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx": "56", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts": "57", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts": "58", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts": "59", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts": "60", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx": "61", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts": "62", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts": "63", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts": "64", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts": "65", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts": "66", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts": "67", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts": "68", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts": "69", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx": "70", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedSubscriptionStatus.tsx": "71", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx": "72", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx": "73", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx": "74", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx": "75", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx": "76", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedSubscriptionStatus.tsx": "77", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx": "78", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts": "79", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx": "80", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx": "81", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx": "82", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx": "83", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx": "84", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx": "85", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx": "86", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx": "87", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx": "88", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx": "89", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx": "90", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts": "91", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts": "92", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts": "93", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx": "94", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts": "95", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts": "96", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts": "97", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts": "98", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx": "99", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts": "100", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx": "101", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx": "102", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx": "103", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx": "104", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx": "105", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx": "106", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx": "107", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx": "108", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\animations.ts": "109", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BillingToggle.tsx": "110", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BusinessPlanSkeleton.tsx": "111", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\ConfirmationDialog.tsx": "112", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogBackground.tsx": "113", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogManager.tsx": "114", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedActionButtons.tsx": "115", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedCurrentPlanSection.tsx": "116", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedGlowButton.tsx": "117", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedInvoiceHistoryCard.tsx": "118", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanPageWithManager.tsx": "119", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanSelectionSection.tsx": "120", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedSubscriptionDetailsCard.tsx": "121", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedTrialAlert.tsx": "122", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\FirstTimePaidPlanDialog.tsx": "123", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PaymentMethodLimitationsDialog.tsx": "124", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanBackground.tsx": "125", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanPageContainer.tsx": "126", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\RealtimePlanPageClient.tsx": "127", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SimplifiedPlanActionDialog.tsx": "128", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\DialogComponents.tsx": "129", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EligiblePaymentMethodsCard.tsx": "130", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedPaymentHistoryCard.tsx": "131", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedSubscriptionActionCard.tsx": "132", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\hooks.ts": "133", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\index.ts": "134", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernCancellationDialog.tsx": "135", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernRefundDialog.tsx": "136", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernSubscriptionStatusCard.tsx": "137", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernTabs.tsx": "138", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionActions.ts": "139", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionButton.tsx": "140", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionManager.tsx": "141", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionProcessingIndicator.tsx": "142", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionTabsToggle.tsx": "143", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\types.ts": "144", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusBadge.tsx": "145", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusIndicator.tsx": "146", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionTabContent.tsx": "147", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialManagement.tsx": "148", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialSubscriptionWarningDialog.tsx": "149", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\UpiPaymentMethodWarning.tsx": "150", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\WebhookWaitingIndicator.tsx": "151", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\context\\SubscriptionProcessingContext.tsx": "152", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionHandler.ts": "153", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionLogic.ts": "154", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useUrlParameterHandler.ts": "155", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\page.tsx": "156", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\PlanPageWrapper.tsx": "157", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts": "158", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts": "159", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts": "160", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts": "161", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts": "162", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts": "163", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts": "164", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts": "165", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts": "166", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts": "167", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts": "168", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts": "169", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts": "170", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts": "171", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts": "172", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx": "173", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx": "174", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx": "175", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts": "176", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts": "177", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx": "178", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts": "179", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx": "180", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx": "181", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx": "182", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx": "183", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx": "184", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx": "185", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx": "186", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx": "187", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx": "188", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx": "189", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx": "190", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx": "191", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx": "192", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx": "193", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx": "194", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx": "195", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx": "196", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx": "197", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx": "198", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx": "199", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx": "200", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx": "201", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx": "202", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx": "203", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts": "204", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\actions.ts": "205", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx": "206", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx": "207", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx": "208", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx": "209", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts": "210", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx": "211", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx": "212", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx": "213", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx": "214", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx": "215", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx": "216", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx": "217", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts": "218", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts": "219", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx": "220", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx": "221", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx": "222", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx": "223", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx": "224", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx": "225", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx": "226", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts": "227", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx": "228", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx": "229", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx": "230", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx": "231", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx": "232", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts": "233", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts": "234", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx": "235", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx": "236", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx": "237", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx": "238", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts": "239", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx": "240", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx": "241", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx": "242", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "243", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts": "244", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx": "245", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx": "246", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\actions.ts": "247", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx": "248", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx": "249", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx": "250", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx": "251", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx": "252", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts": "253", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx": "254", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx": "255", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx": "256", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx": "257", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx": "258", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx": "259", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx": "260", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts": "261", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx": "262", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts": "263", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx": "264", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx": "265", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx": "266", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx": "267", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx": "268", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx": "269", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx": "270", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx": "271", "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx": "272", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx": "273", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx": "274", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx": "275", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx": "276", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx": "277", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx": "278", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx": "279", "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx": "280", "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts": "281", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx": "282", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx": "283", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx": "284", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx": "285", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx": "286", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx": "287", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx": "288", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx": "289", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx": "290", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts": "291", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx": "292", "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx": "293", "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx": "294", "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts": "295", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx": "296", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx": "297", "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx": "298", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx": "299", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx": "300", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx": "301", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx": "302", "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx": "303", "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx": "304", "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx": "305", "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx": "306", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx": "307", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts": "308", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\BillingToggle.tsx": "309", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx": "310", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx": "311", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx": "312", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx": "313", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx": "314", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx": "315", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx": "316", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx": "317", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts": "318", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx": "319", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx": "320", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx": "321", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx": "322", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx": "323", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx": "324", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx": "325", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx": "326", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx": "327", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx": "328", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx": "329", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PricingSection.tsx": "330", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx": "331", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx": "332", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx": "333", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx": "334", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx": "335", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts": "336", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx": "337", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx": "338", "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx": "339", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx": "340", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx": "341", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx": "342", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx": "343", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx": "344", "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx": "345", "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\FeatureComparisonTable.tsx": "346", "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx": "347", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx": "348", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx": "349", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx": "350", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx": "351", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx": "352", "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx": "353", "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx": "354", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx": "355", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx": "356", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts": "357", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts": "358", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts": "359", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts": "360", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts": "361", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts": "362", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx": "363", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx": "364", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx": "365", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx": "366", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx": "367", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx": "368", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx": "369", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx": "370", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx": "371", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx": "372", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx": "373", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx": "374", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx": "375", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx": "376", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx": "377", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx": "378", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx": "379", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx": "380", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx": "381", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx": "382", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx": "383", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx": "384", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx": "385", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx": "386", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx": "387", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx": "388", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx": "389", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx": "390", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx": "391", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx": "392", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx": "393", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx": "394", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx": "395", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx": "396", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts": "397", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts": "398", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts": "399", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts": "400", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx": "401", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts": "402", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts": "403", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx": "404", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx": "405", "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx": "406", "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts": "407", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx": "408", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx": "409", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx": "410", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx": "411", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx": "412", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx": "413", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\PlanComparisonSection.tsx": "414", "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx": "415", "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx": "416", "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts": "417", "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx": "418", "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx": "419", "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts": "420", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx": "421", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx": "422", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx": "423", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx": "424", "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx": "425", "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx": "426", "C:\\web-app\\dukancard\\app\\(main)\\page.tsx": "427", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\AnimatedTitle.tsx": "428", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\PricingAnimatedBackground.tsx": "429", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCards.tsx": "430", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCTA.tsx": "431", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingFAQ.tsx": "432", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingHero.tsx": "433", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingToggle.tsx": "434", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\FeatureComparisonTable.tsx": "435", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\EnhancedPricingPageClient.tsx": "436", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\page.tsx": "437", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx": "438", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx": "439", "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx": "440", "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx": "441", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx": "442", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx": "443", "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\AnalyticsClient.tsx": "444", "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\page.tsx": "445", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx": "446", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx": "447", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx": "448", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx": "449", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx": "450", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx": "451", "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx": "452", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx": "453", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx": "454", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx": "455", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx": "456", "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx": "457", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx": "458", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx": "459", "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx": "460", "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx": "461", "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx": "462", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts": "463", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx": "464", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx": "465", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx": "466", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx": "467", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx": "468", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx": "469", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\PlanSelectionStep.tsx": "470", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts": "471", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts": "472", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts": "473", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts": "474", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts": "475", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts": "476", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx": "477", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx": "478", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx": "479", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts": "480", "C:\\web-app\\dukancard\\app\\api\\admin\\fix-subscription-inconsistency\\route.ts": "481", "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts": "482", "C:\\web-app\\dukancard\\app\\api\\health\\subscription\\route.ts": "483", "C:\\web-app\\dukancard\\app\\api\\razorpay\\key\\route.ts": "484", "C:\\web-app\\dukancard\\app\\api\\subscription\\[id]\\payments\\route.ts": "485", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\centralized\\route.ts": "486", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\list\\route.ts": "487", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\my\\route.ts": "488", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\cancel\\route.ts": "489", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\invoices\\route.ts": "490", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pause\\route.ts": "491", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\route.ts": "492", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\[paymentId]\\route.ts": "493", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pending-update\\route.ts": "494", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\databaseOperations.ts": "495", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\errorHandlers.ts": "496", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\index.ts": "497", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\paymentHelpers.ts": "498", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\razorpayApi.ts": "499", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\types.ts": "500", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\validators.ts": "501", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\resume\\route.ts": "502", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\route.ts": "503", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\scheduled-changes\\route.ts": "504", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch\\route.ts": "505", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch-with-new-payment\\route.ts": "506", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\update\\route.ts": "507", "C:\\web-app\\dukancard\\app\\api\\test\\subscription-flow\\route.ts": "508", "C:\\web-app\\dukancard\\app\\api\\test\\subscription-scenarios\\route.ts": "509", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\create\\route.ts": "510", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\delete\\route.ts": "511", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\get\\route.ts": "512", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\list\\route.ts": "513", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\retry\\route.ts": "514", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\route.ts": "515", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\update\\route.ts": "516", "C:\\web-app\\dukancard\\app\\auth\\actions.ts": "517", "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts": "518", "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx": "519", "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx": "520", "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx": "521", "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx": "522", "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx": "523", "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx": "524", "C:\\web-app\\dukancard\\app\\components\\Footer.tsx": "525", "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx": "526", "C:\\web-app\\dukancard\\app\\components\\Header.tsx": "527", "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx": "528", "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx": "529", "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx": "530", "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx": "531", "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx": "532", "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx": "533", "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx": "534", "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx": "535", "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx": "536", "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx": "537", "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx": "538", "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx": "539", "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx": "540", "C:\\web-app\\dukancard\\app\\components\\PricingCard.tsx": "541", "C:\\web-app\\dukancard\\app\\components\\PricingCardContext.tsx": "542", "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx": "543", "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx": "544", "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx": "545", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx": "546", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx": "547", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx": "548", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx": "549", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx": "550", "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx": "551", "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx": "552", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts": "553", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx": "554", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx": "555", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx": "556", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx": "557", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx": "558", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx": "559", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx": "560", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx": "561", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts": "562", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx": "563", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx": "564", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx": "565", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx": "566", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx": "567", "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx": "568", "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx": "569", "C:\\web-app\\dukancard\\app\\context\\PaymentMethodLimitationsContext.tsx": "570", "C:\\web-app\\dukancard\\app\\layout.tsx": "571", "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts": "572", "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts": "573", "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts": "574", "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts": "575", "C:\\web-app\\dukancard\\app\\locality\\actions.ts": "576", "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx": "577", "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx": "578", "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx": "579", "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx": "580", "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx": "581", "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts": "582", "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts": "583", "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts": "584", "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx": "585", "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts": "586", "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts": "587", "C:\\web-app\\dukancard\\app\\locality\\layout.tsx": "588", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx": "589", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx": "590", "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx": "591", "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx": "592", "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx": "593", "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx": "594", "C:\\web-app\\dukancard\\app\\products\\sitemap.ts": "595", "C:\\web-app\\dukancard\\app\\robots.ts": "596", "C:\\web-app\\dukancard\\app\\sitemap.ts": "597", "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts": "598", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts": "599", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx": "600", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx": "601", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx": "602", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx": "603", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx": "604", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx": "605", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx": "606", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx": "607", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx": "608", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx": "609", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx": "610", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx": "611", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\VisitTracker.tsx": "612", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx": "613", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx": "614", "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx": "615", "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx": "616", "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx": "617", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts": "618", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx": "619", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx": "620", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx": "621", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx": "622", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx": "623", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx": "624", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx": "625", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx": "626", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx": "627", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts": "628", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx": "629", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx": "630", "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx": "631", "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx": "632", "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx": "633", "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx": "634", "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx": "635", "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx": "636", "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx": "637", "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx": "638", "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx": "639", "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx": "640", "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx": "641", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx": "642", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx": "643", "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx": "644", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx": "645", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx": "646", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx": "647", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts": "648", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts": "649", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts": "650", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx": "651", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx": "652", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx": "653", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx": "654", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx": "655", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx": "656", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx": "657", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx": "658", "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx": "659", "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx": "660", "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx": "661", "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx": "662", "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx": "663", "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx": "664", "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx": "665", "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx": "666", "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx": "667", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx": "668", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx": "669", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx": "670", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx": "671", "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx": "672", "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx": "673", "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx": "674", "C:\\web-app\\dukancard\\components\\ui\\alert.tsx": "675", "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx": "676", "C:\\web-app\\dukancard\\components\\ui\\badge.tsx": "677", "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx": "678", "C:\\web-app\\dukancard\\components\\ui\\button.tsx": "679", "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx": "680", "C:\\web-app\\dukancard\\components\\ui\\card.tsx": "681", "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx": "682", "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx": "683", "C:\\web-app\\dukancard\\components\\ui\\chart.tsx": "684", "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx": "685", "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx": "686", "C:\\web-app\\dukancard\\components\\ui\\command.tsx": "687", "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx": "688", "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx": "689", "C:\\web-app\\dukancard\\components\\ui\\form.tsx": "690", "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx": "691", "C:\\web-app\\dukancard\\components\\ui\\input.tsx": "692", "C:\\web-app\\dukancard\\components\\ui\\label.tsx": "693", "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx": "694", "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx": "695", "C:\\web-app\\dukancard\\components\\ui\\popover.tsx": "696", "C:\\web-app\\dukancard\\components\\ui\\progress.tsx": "697", "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx": "698", "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx": "699", "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx": "700", "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx": "701", "C:\\web-app\\dukancard\\components\\ui\\select.tsx": "702", "C:\\web-app\\dukancard\\components\\ui\\separator.tsx": "703", "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx": "704", "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx": "705", "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx": "706", "C:\\web-app\\dukancard\\components\\ui\\slider.tsx": "707", "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx": "708", "C:\\web-app\\dukancard\\components\\ui\\switch.tsx": "709", "C:\\web-app\\dukancard\\components\\ui\\table.tsx": "710", "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx": "711", "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx": "712", "C:\\web-app\\dukancard\\components\\ui\\toast.tsx": "713", "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx": "714", "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts": "715", "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx": "716", "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts": "717", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts": "718", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts": "719", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts": "720", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts": "721", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts": "722", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts": "723", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts": "724", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts": "725", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts": "726", "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts": "727", "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts": "728", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts": "729", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts": "730", "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts": "731", "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts": "732", "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts": "733", "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts": "734", "C:\\web-app\\dukancard\\lib\\actions\\location.ts": "735", "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts": "736", "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts": "737", "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts": "738", "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts": "739", "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts": "740", "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts": "741", "C:\\web-app\\dukancard\\lib\\actions\\posts.ts": "742", "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts": "743", "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts": "744", "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts": "745", "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts": "746", "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts": "747", "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts": "748", "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts": "749", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts": "750", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts": "751", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts": "752", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\activateTrial.ts": "753", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\centralized.ts": "754", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\confirm.ts": "755", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\create.ts": "756", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\index.ts": "757", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\cancel.ts": "758", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\change.ts": "759", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\index.ts": "760", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\manage.ts": "761", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\schedule.ts": "762", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\switch.ts": "763", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage.ts": "764", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\payment.ts": "765", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\status.ts": "766", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\types.ts": "767", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\utils.ts": "768", "C:\\web-app\\dukancard\\lib\\actions\\subscription.ts": "769", "C:\\web-app\\dukancard\\lib\\actions\\user\\getUserAndProfile.ts": "770", "C:\\web-app\\dukancard\\lib\\api\\response.ts": "771", "C:\\web-app\\dukancard\\lib\\cardDownloader.ts": "772", "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts": "773", "C:\\web-app\\dukancard\\lib\\config\\categories.ts": "774", "C:\\web-app\\dukancard\\lib\\config\\plans.ts": "775", "C:\\web-app\\dukancard\\lib\\config\\states.ts": "776", "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts": "777", "C:\\web-app\\dukancard\\lib\\csrf.ts": "778", "C:\\web-app\\dukancard\\lib\\errorHandling.ts": "779", "C:\\web-app\\dukancard\\lib\\PricingPlans.ts": "780", "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts": "781", "C:\\web-app\\dukancard\\lib\\rateLimiter.ts": "782", "C:\\web-app\\dukancard\\lib\\razorpay\\razorpayClient.ts": "783", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\create.ts": "784", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\get.ts": "785", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\index.ts": "786", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\types.ts": "787", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\update.ts": "788", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\invoice.ts": "789", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\getPayment.ts": "790", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\index.ts": "791", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\types.ts": "792", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment.ts": "793", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\plan.ts": "794", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\cancel.ts": "795", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\create.ts": "796", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\get.ts": "797", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\index.ts": "798", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\scheduled.ts": "799", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\types.ts": "800", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\update.ts": "801", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\webhook.ts": "802", "C:\\web-app\\dukancard\\lib\\razorpay\\types\\api.ts": "803", "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\auth.ts": "804", "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\loadRazorpaySDK.ts": "805", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\errorTracking.ts": "806", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\eventManager.ts": "807", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\subscriptionManager.ts": "808", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\types.ts": "809", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\eventOrderValidator.ts": "810", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\stateTransitionValidator.ts": "811", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\index.ts": "812", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\mainHandler.ts": "813", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\moreSubscriptionHandlers.ts": "814", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\paymentHandlers.ts": "815", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\refundHandlers.ts": "816", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-constants.ts": "817", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-db-updater.ts": "818", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-manager.ts": "819", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-validator.ts": "820", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionActivated.ts": "821", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionAuthenticated.ts": "822", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCancelled.ts": "823", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCharged.ts": "824", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCompleted.ts": "825", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionExpired.ts": "826", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionHalted.ts": "827", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionPending.ts": "828", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionUpdated.ts": "829", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\index.ts": "830", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionHandlers.ts": "831", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\transactionUtils.ts": "832", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\utils.ts": "833", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhook-utils.ts": "834", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhookProcessor.ts": "835", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handleWebhook.ts": "836", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\idempotency.ts": "837", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\monitoring.ts": "838", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\types.ts": "839", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\validation.ts": "840", "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts": "841", "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts": "842", "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts": "843", "C:\\web-app\\dukancard\\lib\\services\\socialService.ts": "844", "C:\\web-app\\dukancard\\lib\\services\\subscription.ts": "845", "C:\\web-app\\dukancard\\lib\\site-config.ts": "846", "C:\\web-app\\dukancard\\lib\\siteContent.ts": "847", "C:\\web-app\\dukancard\\lib\\subscription\\edge-validation.ts": "848", "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowManager.ts": "849", "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowTester.ts": "850", "C:\\web-app\\dukancard\\lib\\subscription\\types.ts": "851", "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts": "852", "C:\\web-app\\dukancard\\lib\\testing\\database.ts": "853", "C:\\web-app\\dukancard\\lib\\testing\\mswHandlers.ts": "854", "C:\\web-app\\dukancard\\lib\\testing\\SubscriptionScenarioTester.ts": "855", "C:\\web-app\\dukancard\\lib\\testing\\testDataFactories.ts": "856", "C:\\web-app\\dukancard\\lib\\testing\\testUtils.ts": "857", "C:\\web-app\\dukancard\\lib\\testing\\types.ts": "858", "C:\\web-app\\dukancard\\lib\\types\\activities.ts": "859", "C:\\web-app\\dukancard\\lib\\types\\api.ts": "860", "C:\\web-app\\dukancard\\lib\\types\\blog.ts": "861", "C:\\web-app\\dukancard\\lib\\types\\posts.ts": "862", "C:\\web-app\\dukancard\\lib\\types\\subscription.ts": "863", "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts": "864", "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts": "865", "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts": "866", "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts": "867", "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts": "868", "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts": "869", "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts": "870", "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts": "871", "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "872", "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts": "873", "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts": "874", "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts": "875", "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts": "876", "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts": "877", "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts": "878", "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts": "879", "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts": "880", "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts": "881", "C:\\web-app\\dukancard\\lib\\utils\\seo.ts": "882", "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts": "883", "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts": "884", "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts": "885", "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts": "886", "C:\\web-app\\dukancard\\lib\\utils.ts": "887"}, {"size": 3778, "mtime": 1753520783284, "results": "888", "hashOfConfig": "889"}, {"size": 11790, "mtime": 1753101910631, "results": "890", "hashOfConfig": "889"}, {"size": 4230, "mtime": 1753520831364, "results": "891", "hashOfConfig": "889"}, {"size": 2270, "mtime": 1753437208359, "results": "892", "hashOfConfig": "889"}, {"size": 20852, "mtime": 1753437482233, "results": "893", "hashOfConfig": "889"}, {"size": 8688, "mtime": 1752523372903, "results": "894", "hashOfConfig": "889"}, {"size": 15287, "mtime": 1752522988158, "results": "895", "hashOfConfig": "889"}, {"size": 2217, "mtime": 1752078894626, "results": "896", "hashOfConfig": "889"}, {"size": 3625, "mtime": 1752523160950, "results": "897", "hashOfConfig": "889"}, {"size": 5348, "mtime": 1752523090171, "results": "898", "hashOfConfig": "889"}, {"size": 4425, "mtime": 1752523255269, "results": "899", "hashOfConfig": "889"}, {"size": 7274, "mtime": 1752523470865, "results": "900", "hashOfConfig": "889"}, {"size": 9690, "mtime": 1752523680880, "results": "901", "hashOfConfig": "889"}, {"size": 1912, "mtime": 1752078894640, "results": "902", "hashOfConfig": "889"}, {"size": 2679, "mtime": 1753436763325, "results": "903", "hashOfConfig": "889"}, {"size": 9656, "mtime": 1753520866303, "results": "904", "hashOfConfig": "889"}, {"size": 6750, "mtime": 1753436763325, "results": "905", "hashOfConfig": "889"}, {"size": 7333, "mtime": 1753436763325, "results": "906", "hashOfConfig": "889"}, {"size": 8003, "mtime": 1753436763325, "results": "907", "hashOfConfig": "889"}, {"size": 273, "mtime": 1752078894640, "results": "908", "hashOfConfig": "889"}, {"size": 3199, "mtime": 1753436763325, "results": "909", "hashOfConfig": "889"}, {"size": 10302, "mtime": 1753436763325, "results": "910", "hashOfConfig": "889"}, {"size": 22168, "mtime": 1752078894640, "results": "911", "hashOfConfig": "889"}, {"size": 20755, "mtime": 1752078894653, "results": "912", "hashOfConfig": "889"}, {"size": 2220, "mtime": 1752078894653, "results": "913", "hashOfConfig": "889"}, {"size": 10914, "mtime": 1752078894653, "results": "914", "hashOfConfig": "889"}, {"size": 1630, "mtime": 1752078894653, "results": "915", "hashOfConfig": "889"}, {"size": 721, "mtime": 1752078894653, "results": "916", "hashOfConfig": "889"}, {"size": 8255, "mtime": 1752078894653, "results": "917", "hashOfConfig": "889"}, {"size": 21052, "mtime": 1752078894664, "results": "918", "hashOfConfig": "889"}, {"size": 4489, "mtime": 1752078894665, "results": "919", "hashOfConfig": "889"}, {"size": 13138, "mtime": 1752078894667, "results": "920", "hashOfConfig": "889"}, {"size": 2814, "mtime": 1752078894667, "results": "921", "hashOfConfig": "889"}, {"size": 14802, "mtime": 1752080815930, "results": "922", "hashOfConfig": "889"}, {"size": 15549, "mtime": 1752078894667, "results": "923", "hashOfConfig": "889"}, {"size": 13946, "mtime": 1752078894667, "results": "924", "hashOfConfig": "889"}, {"size": 1088, "mtime": 1752078894667, "results": "925", "hashOfConfig": "889"}, {"size": 2708, "mtime": 1752078894667, "results": "926", "hashOfConfig": "889"}, {"size": 9768, "mtime": 1752078894667, "results": "927", "hashOfConfig": "889"}, {"size": 23953, "mtime": 1752078894667, "results": "928", "hashOfConfig": "889"}, {"size": 5243, "mtime": 1752078894667, "results": "929", "hashOfConfig": "889"}, {"size": 3361, "mtime": 1752078894667, "results": "930", "hashOfConfig": "889"}, {"size": 3637, "mtime": 1752078894678, "results": "931", "hashOfConfig": "889"}, {"size": 6688, "mtime": 1752078894680, "results": "932", "hashOfConfig": "889"}, {"size": 2107, "mtime": 1752078894682, "results": "933", "hashOfConfig": "889"}, {"size": 3868, "mtime": 1752078894681, "results": "934", "hashOfConfig": "889"}, {"size": 4167, "mtime": 1752078894682, "results": "935", "hashOfConfig": "889"}, {"size": 3004, "mtime": 1752078894683, "results": "936", "hashOfConfig": "889"}, {"size": 6921, "mtime": 1752078894684, "results": "937", "hashOfConfig": "889"}, {"size": 8503, "mtime": 1752078894708, "results": "938", "hashOfConfig": "889"}, {"size": 9562, "mtime": 1752078894708, "results": "939", "hashOfConfig": "889"}, {"size": 4802, "mtime": 1752078894708, "results": "940", "hashOfConfig": "889"}, {"size": 1794, "mtime": 1752078894708, "results": "941", "hashOfConfig": "889"}, {"size": 5533, "mtime": 1752078894708, "results": "942", "hashOfConfig": "889"}, {"size": 2140, "mtime": 1752078894708, "results": "943", "hashOfConfig": "889"}, {"size": 5309, "mtime": 1752078894708, "results": "944", "hashOfConfig": "889"}, {"size": 3245, "mtime": 1752078894717, "results": "945", "hashOfConfig": "889"}, {"size": 8242, "mtime": 1753436763340, "results": "946", "hashOfConfig": "889"}, {"size": 2762, "mtime": 1753436763340, "results": "947", "hashOfConfig": "889"}, {"size": 7583, "mtime": 1753436763340, "results": "948", "hashOfConfig": "889"}, {"size": 2031, "mtime": 1753436763340, "results": "949", "hashOfConfig": "889"}, {"size": 1907, "mtime": 1753436763340, "results": "950", "hashOfConfig": "889"}, {"size": 9011, "mtime": 1752080815930, "results": "951", "hashOfConfig": "889"}, {"size": 2435, "mtime": 1752078894723, "results": "952", "hashOfConfig": "889"}, {"size": 1001, "mtime": 1752078894723, "results": "953", "hashOfConfig": "889"}, {"size": 559, "mtime": 1753031839333, "results": "954", "hashOfConfig": "889"}, {"size": 2573, "mtime": 1752078894723, "results": "955", "hashOfConfig": "889"}, {"size": 404, "mtime": 1752078894723, "results": "956", "hashOfConfig": "889"}, {"size": 1825, "mtime": 1752078894723, "results": "957", "hashOfConfig": "889"}, {"size": 4687, "mtime": 1752520901592, "results": "958", "hashOfConfig": "889"}, {"size": 12464, "mtime": 1752521239777, "results": "959", "hashOfConfig": "889"}, {"size": 6082, "mtime": 1753520888843, "results": "960", "hashOfConfig": "889"}, {"size": 2522, "mtime": 1752652071674, "results": "961", "hashOfConfig": "889"}, {"size": 2722, "mtime": 1752521491570, "results": "962", "hashOfConfig": "889"}, {"size": 6550, "mtime": 1753436763340, "results": "963", "hashOfConfig": "889"}, {"size": 5057, "mtime": 1752521333633, "results": "964", "hashOfConfig": "889"}, {"size": 6735, "mtime": 1752078894735, "results": "965", "hashOfConfig": "889"}, {"size": 8561, "mtime": 1752078894735, "results": "966", "hashOfConfig": "889"}, {"size": 10556, "mtime": 1753520917638, "results": "967", "hashOfConfig": "889"}, {"size": 1993, "mtime": 1752078894735, "results": "968", "hashOfConfig": "889"}, {"size": 3314, "mtime": 1752522195446, "results": "969", "hashOfConfig": "889"}, {"size": 6883, "mtime": 1753436763340, "results": "970", "hashOfConfig": "889"}, {"size": 2180, "mtime": 1752521782818, "results": "971", "hashOfConfig": "889"}, {"size": 1799, "mtime": 1752078894735, "results": "972", "hashOfConfig": "889"}, {"size": 2794, "mtime": 1752078894751, "results": "973", "hashOfConfig": "889"}, {"size": 3181, "mtime": 1753436763340, "results": "974", "hashOfConfig": "889"}, {"size": 2096, "mtime": 1753436763340, "results": "975", "hashOfConfig": "889"}, {"size": 2833, "mtime": 1752078894751, "results": "976", "hashOfConfig": "889"}, {"size": 6615, "mtime": 1752522674608, "results": "977", "hashOfConfig": "889"}, {"size": 8681, "mtime": 1753436763340, "results": "978", "hashOfConfig": "889"}, {"size": 2305, "mtime": 1752078894751, "results": "979", "hashOfConfig": "889"}, {"size": 2046, "mtime": 1753436763340, "results": "980", "hashOfConfig": "889"}, {"size": 3165, "mtime": 1753436763340, "results": "981", "hashOfConfig": "889"}, {"size": 2135, "mtime": 1753251129486, "results": "982", "hashOfConfig": "889"}, {"size": 767, "mtime": 1753436763340, "results": "983", "hashOfConfig": "889"}, {"size": 555, "mtime": 1753436763340, "results": "984", "hashOfConfig": "889"}, {"size": 1142, "mtime": 1752078894766, "results": "985", "hashOfConfig": "889"}, {"size": 1089, "mtime": 1752078894766, "results": "986", "hashOfConfig": "889"}, {"size": 3242, "mtime": 1753251129486, "results": "987", "hashOfConfig": "889"}, {"size": 1949, "mtime": 1753520944546, "results": "988", "hashOfConfig": "889"}, {"size": 10378, "mtime": 1753531001335, "results": "989", "hashOfConfig": "889"}, {"size": 4483, "mtime": 1752925036472, "results": "990", "hashOfConfig": "889"}, {"size": 3675, "mtime": 1752524849734, "results": "991", "hashOfConfig": "889"}, {"size": 4030, "mtime": 1752078894766, "results": "992", "hashOfConfig": "889"}, {"size": 1698, "mtime": 1752078894766, "results": "993", "hashOfConfig": "889"}, {"size": 3845, "mtime": 1753589702934, "results": "994", "hashOfConfig": "889"}, {"size": 5729, "mtime": 1753251129486, "results": "995", "hashOfConfig": "889"}, {"size": 2348, "mtime": 1753251129486, "results": "996", "hashOfConfig": "889"}, {"size": 1439, "mtime": 1752078894807, "results": "997", "hashOfConfig": "889"}, {"size": 2558, "mtime": 1752078894782, "results": "998", "hashOfConfig": "889"}, {"size": 4637, "mtime": 1752078894782, "results": "999", "hashOfConfig": "889"}, {"size": 2863, "mtime": 1752078894782, "results": "1000", "hashOfConfig": "889"}, {"size": 3966, "mtime": 1752078894782, "results": "1001", "hashOfConfig": "889"}, {"size": 3136, "mtime": 1752078894782, "results": "1002", "hashOfConfig": "889"}, {"size": 4782, "mtime": 1752078894782, "results": "1003", "hashOfConfig": "889"}, {"size": 8607, "mtime": 1752078894782, "results": "1004", "hashOfConfig": "889"}, {"size": 4656, "mtime": 1752078894782, "results": "1005", "hashOfConfig": "889"}, {"size": 23325, "mtime": 1753250912472, "results": "1006", "hashOfConfig": "889"}, {"size": 1229, "mtime": 1752078894782, "results": "1007", "hashOfConfig": "889"}, {"size": 6213, "mtime": 1752078894782, "results": "1008", "hashOfConfig": "889"}, {"size": 15610, "mtime": 1752078894782, "results": "1009", "hashOfConfig": "889"}, {"size": 3571, "mtime": 1752078894782, "results": "1010", "hashOfConfig": "889"}, {"size": 10880, "mtime": 1752078894782, "results": "1011", "hashOfConfig": "889"}, {"size": 2393, "mtime": 1752078894782, "results": "1012", "hashOfConfig": "889"}, {"size": 5434, "mtime": 1752078894797, "results": "1013", "hashOfConfig": "889"}, {"size": 9221, "mtime": 1752078894797, "results": "1014", "hashOfConfig": "889"}, {"size": 6955, "mtime": 1752078894797, "results": "1015", "hashOfConfig": "889"}, {"size": 21017, "mtime": 1752078894797, "results": "1016", "hashOfConfig": "889"}, {"size": 1762, "mtime": 1752078894807, "results": "1017", "hashOfConfig": "889"}, {"size": 8288, "mtime": 1752078894807, "results": "1018", "hashOfConfig": "889"}, {"size": 16465, "mtime": 1752078894807, "results": "1019", "hashOfConfig": "889"}, {"size": 12988, "mtime": 1752078894814, "results": "1020", "hashOfConfig": "889"}, {"size": 2432, "mtime": 1752078894814, "results": "1021", "hashOfConfig": "889"}, {"size": 770, "mtime": 1752078894814, "results": "1022", "hashOfConfig": "889"}, {"size": 12287, "mtime": 1752078894814, "results": "1023", "hashOfConfig": "889"}, {"size": 8702, "mtime": 1752078894814, "results": "1024", "hashOfConfig": "889"}, {"size": 27687, "mtime": 1752078894814, "results": "1025", "hashOfConfig": "889"}, {"size": 6021, "mtime": 1752078894814, "results": "1026", "hashOfConfig": "889"}, {"size": 5206, "mtime": 1752078894814, "results": "1027", "hashOfConfig": "889"}, {"size": 3546, "mtime": 1752078894814, "results": "1028", "hashOfConfig": "889"}, {"size": 14866, "mtime": 1752078894814, "results": "1029", "hashOfConfig": "889"}, {"size": 7461, "mtime": 1752078894814, "results": "1030", "hashOfConfig": "889"}, {"size": 2679, "mtime": 1752078894814, "results": "1031", "hashOfConfig": "889"}, {"size": 3078, "mtime": 1752078894823, "results": "1032", "hashOfConfig": "889"}, {"size": 8231, "mtime": 1752078894797, "results": "1033", "hashOfConfig": "889"}, {"size": 14159, "mtime": 1752078894797, "results": "1034", "hashOfConfig": "889"}, {"size": 4456, "mtime": 1752078894797, "results": "1035", "hashOfConfig": "889"}, {"size": 3114, "mtime": 1752078894797, "results": "1036", "hashOfConfig": "889"}, {"size": 4235, "mtime": 1752078894797, "results": "1037", "hashOfConfig": "889"}, {"size": 6620, "mtime": 1752078894797, "results": "1038", "hashOfConfig": "889"}, {"size": 776, "mtime": 1752078894807, "results": "1039", "hashOfConfig": "889"}, {"size": 11893, "mtime": 1752078894823, "results": "1040", "hashOfConfig": "889"}, {"size": 11640, "mtime": 1752078894823, "results": "1041", "hashOfConfig": "889"}, {"size": 6587, "mtime": 1752078894823, "results": "1042", "hashOfConfig": "889"}, {"size": 3524, "mtime": 1752078894830, "results": "1043", "hashOfConfig": "889"}, {"size": 12782, "mtime": 1753101910631, "results": "1044", "hashOfConfig": "889"}, {"size": 1654, "mtime": 1752078894782, "results": "1045", "hashOfConfig": "889"}, {"size": 7929, "mtime": 1753436763340, "results": "1046", "hashOfConfig": "889"}, {"size": 12970, "mtime": 1753436763340, "results": "1047", "hashOfConfig": "889"}, {"size": 8366, "mtime": 1753436763340, "results": "1048", "hashOfConfig": "889"}, {"size": 5098, "mtime": 1753436763340, "results": "1049", "hashOfConfig": "889"}, {"size": 15095, "mtime": 1753436763356, "results": "1050", "hashOfConfig": "889"}, {"size": 5428, "mtime": 1753436763356, "results": "1051", "hashOfConfig": "889"}, {"size": 10699, "mtime": 1753436763356, "results": "1052", "hashOfConfig": "889"}, {"size": 7346, "mtime": 1753521037509, "results": "1053", "hashOfConfig": "889"}, {"size": 9106, "mtime": 1753521094750, "results": "1054", "hashOfConfig": "889"}, {"size": 774, "mtime": 1752078894839, "results": "1055", "hashOfConfig": "889"}, {"size": 5527, "mtime": 1753104677045, "results": "1056", "hashOfConfig": "889"}, {"size": 585, "mtime": 1752170193569, "results": "1057", "hashOfConfig": "889"}, {"size": 8360, "mtime": 1753436763356, "results": "1058", "hashOfConfig": "889"}, {"size": 12397, "mtime": 1753436763363, "results": "1059", "hashOfConfig": "889"}, {"size": 99, "mtime": 1752078894830, "results": "1060", "hashOfConfig": "889"}, {"size": 10730, "mtime": 1753101910644, "results": "1061", "hashOfConfig": "889"}, {"size": 2570, "mtime": 1753251129502, "results": "1062", "hashOfConfig": "889"}, {"size": 16581, "mtime": 1753101910644, "results": "1063", "hashOfConfig": "889"}, {"size": 6920, "mtime": 1752078894861, "results": "1064", "hashOfConfig": "889"}, {"size": 8800, "mtime": 1752078894863, "results": "1065", "hashOfConfig": "889"}, {"size": 7997, "mtime": 1752078894845, "results": "1066", "hashOfConfig": "889"}, {"size": 743, "mtime": 1752083877657, "results": "1067", "hashOfConfig": "889"}, {"size": 2089, "mtime": 1752078894863, "results": "1068", "hashOfConfig": "889"}, {"size": 2029, "mtime": 1752170340365, "results": "1069", "hashOfConfig": "889"}, {"size": 5665, "mtime": 1752087097784, "results": "1070", "hashOfConfig": "889"}, {"size": 9160, "mtime": 1752086184726, "results": "1071", "hashOfConfig": "889"}, {"size": 2212, "mtime": 1752084237937, "results": "1072", "hashOfConfig": "889"}, {"size": 2762, "mtime": 1752165254320, "results": "1073", "hashOfConfig": "889"}, {"size": 4770, "mtime": 1752085989931, "results": "1074", "hashOfConfig": "889"}, {"size": 7051, "mtime": 1752165128789, "results": "1075", "hashOfConfig": "889"}, {"size": 5095, "mtime": 1752084296324, "results": "1076", "hashOfConfig": "889"}, {"size": 18791, "mtime": 1753106058738, "results": "1077", "hashOfConfig": "889"}, {"size": 4427, "mtime": 1752086297714, "results": "1078", "hashOfConfig": "889"}, {"size": 5264, "mtime": 1752078894845, "results": "1079", "hashOfConfig": "889"}, {"size": 16573, "mtime": 1752078894852, "results": "1080", "hashOfConfig": "889"}, {"size": 1339, "mtime": 1752078894852, "results": "1081", "hashOfConfig": "889"}, {"size": 30215, "mtime": 1753101910644, "results": "1082", "hashOfConfig": "889"}, {"size": 17692, "mtime": 1752078894855, "results": "1083", "hashOfConfig": "889"}, {"size": 31435, "mtime": 1753101910657, "results": "1084", "hashOfConfig": "889"}, {"size": 25674, "mtime": 1753101910657, "results": "1085", "hashOfConfig": "889"}, {"size": 10885, "mtime": 1752078894855, "results": "1086", "hashOfConfig": "889"}, {"size": 7850, "mtime": 1752086860223, "results": "1087", "hashOfConfig": "889"}, {"size": 12815, "mtime": 1753104769695, "results": "1088", "hashOfConfig": "889"}, {"size": 4213, "mtime": 1753106603423, "results": "1089", "hashOfConfig": "889"}, {"size": 1796, "mtime": 1753251129502, "results": "1090", "hashOfConfig": "889"}, {"size": 3085, "mtime": 1752086007532, "results": "1091", "hashOfConfig": "889"}, {"size": 2564, "mtime": 1752086713691, "results": "1092", "hashOfConfig": "889"}, {"size": 1081, "mtime": 1753513381804, "results": "1093", "hashOfConfig": "889"}, {"size": 10361, "mtime": 1753513706129, "results": "1094", "hashOfConfig": "889"}, {"size": 11044, "mtime": 1753515195388, "results": "1095", "hashOfConfig": "889"}, {"size": 5373, "mtime": 1752525844741, "results": "1096", "hashOfConfig": "889"}, {"size": 2006, "mtime": 1753026919824, "results": "1097", "hashOfConfig": "889"}, {"size": 26734, "mtime": 1753436763363, "results": "1098", "hashOfConfig": "889"}, {"size": 23396, "mtime": 1752691514782, "results": "1099", "hashOfConfig": "889"}, {"size": 1822, "mtime": 1752526641904, "results": "1100", "hashOfConfig": "889"}, {"size": 15608, "mtime": 1752763145906, "results": "1101", "hashOfConfig": "889"}, {"size": 4060, "mtime": 1752762369862, "results": "1102", "hashOfConfig": "889"}, {"size": 3182, "mtime": 1752526585020, "results": "1103", "hashOfConfig": "889"}, {"size": 4559, "mtime": 1752763551631, "results": "1104", "hashOfConfig": "889"}, {"size": 2104, "mtime": 1753251129502, "results": "1105", "hashOfConfig": "889"}, {"size": 547, "mtime": 1752761809080, "results": "1106", "hashOfConfig": "889"}, {"size": 3083, "mtime": 1753512968579, "results": "1107", "hashOfConfig": "889"}, {"size": 11679, "mtime": 1752525347906, "results": "1108", "hashOfConfig": "889"}, {"size": 4330, "mtime": 1752078894893, "results": "1109", "hashOfConfig": "889"}, {"size": 2300, "mtime": 1752078894894, "results": "1110", "hashOfConfig": "889"}, {"size": 1807, "mtime": 1752078894894, "results": "1111", "hashOfConfig": "889"}, {"size": 2559, "mtime": 1752078894894, "results": "1112", "hashOfConfig": "889"}, {"size": 2308, "mtime": 1752652107359, "results": "1113", "hashOfConfig": "889"}, {"size": 2136, "mtime": 1752078894894, "results": "1114", "hashOfConfig": "889"}, {"size": 591, "mtime": 1753445796006, "results": "1115", "hashOfConfig": "889"}, {"size": 6530, "mtime": 1752078894894, "results": "1116", "hashOfConfig": "889"}, {"size": 1372, "mtime": 1752925036472, "results": "1117", "hashOfConfig": "889"}, {"size": 3511, "mtime": 1752078894894, "results": "1118", "hashOfConfig": "889"}, {"size": 2499, "mtime": 1753251129502, "results": "1119", "hashOfConfig": "889"}, {"size": 2793, "mtime": 1753251129502, "results": "1120", "hashOfConfig": "889"}, {"size": 18035, "mtime": 1753436763363, "results": "1121", "hashOfConfig": "889"}, {"size": 6372, "mtime": 1752078894909, "results": "1122", "hashOfConfig": "889"}, {"size": 12390, "mtime": 1753515317601, "results": "1123", "hashOfConfig": "889"}, {"size": 1376, "mtime": 1752688533290, "results": "1124", "hashOfConfig": "889"}, {"size": 5647, "mtime": 1752688601357, "results": "1125", "hashOfConfig": "889"}, {"size": 7356, "mtime": 1752078894910, "results": "1126", "hashOfConfig": "889"}, {"size": 4583, "mtime": 1752078894910, "results": "1127", "hashOfConfig": "889"}, {"size": 6598, "mtime": 1752078894910, "results": "1128", "hashOfConfig": "889"}, {"size": 5978, "mtime": 1752078894910, "results": "1129", "hashOfConfig": "889"}, {"size": 13585, "mtime": 1752678735198, "results": "1130", "hashOfConfig": "889"}, {"size": 6909, "mtime": 1752078894910, "results": "1131", "hashOfConfig": "889"}, {"size": 7677, "mtime": 1752078894910, "results": "1132", "hashOfConfig": "889"}, {"size": 2322, "mtime": 1752078894910, "results": "1133", "hashOfConfig": "889"}, {"size": 7209, "mtime": 1752678129103, "results": "1134", "hashOfConfig": "889"}, {"size": 558, "mtime": 1753513910116, "results": "1135", "hashOfConfig": "889"}, {"size": 10540, "mtime": 1753514869215, "results": "1136", "hashOfConfig": "889"}, {"size": 733, "mtime": 1752078894910, "results": "1137", "hashOfConfig": "889"}, {"size": 1203, "mtime": 1752078894910, "results": "1138", "hashOfConfig": "889"}, {"size": 1827, "mtime": 1752078894910, "results": "1139", "hashOfConfig": "889"}, {"size": 2950, "mtime": 1753514279872, "results": "1140", "hashOfConfig": "889"}, {"size": 17615, "mtime": 1753436763363, "results": "1141", "hashOfConfig": "889"}, {"size": 15615, "mtime": 1752763206921, "results": "1142", "hashOfConfig": "889"}, {"size": 4083, "mtime": 1752762551074, "results": "1143", "hashOfConfig": "889"}, {"size": 2929, "mtime": 1752527064479, "results": "1144", "hashOfConfig": "889"}, {"size": 4305, "mtime": 1752763625974, "results": "1145", "hashOfConfig": "889"}, {"size": 5476, "mtime": 1752763083916, "results": "1146", "hashOfConfig": "889"}, {"size": 23276, "mtime": 1752527499610, "results": "1147", "hashOfConfig": "889"}, {"size": 1602, "mtime": 1753251129502, "results": "1148", "hashOfConfig": "889"}, {"size": 1380, "mtime": 1752762960440, "results": "1149", "hashOfConfig": "889"}, {"size": 3464, "mtime": 1752696729504, "results": "1150", "hashOfConfig": "889"}, {"size": 684, "mtime": 1753445926982, "results": "1151", "hashOfConfig": "889"}, {"size": 2007, "mtime": 1752078894925, "results": "1152", "hashOfConfig": "889"}, {"size": 3579, "mtime": 1752078894925, "results": "1153", "hashOfConfig": "889"}, {"size": 1181, "mtime": 1752078894925, "results": "1154", "hashOfConfig": "889"}, {"size": 2665, "mtime": 1752078894925, "results": "1155", "hashOfConfig": "889"}, {"size": 6679, "mtime": 1752925036472, "results": "1156", "hashOfConfig": "889"}, {"size": 3744, "mtime": 1752078894925, "results": "1157", "hashOfConfig": "889"}, {"size": 6038, "mtime": 1752078894925, "results": "1158", "hashOfConfig": "889"}, {"size": 1270, "mtime": 1752925036472, "results": "1159", "hashOfConfig": "889"}, {"size": 2007, "mtime": 1752078894925, "results": "1160", "hashOfConfig": "889"}, {"size": 5704, "mtime": 1752078894941, "results": "1161", "hashOfConfig": "889"}, {"size": 4915, "mtime": 1752078894941, "results": "1162", "hashOfConfig": "889"}, {"size": 4615, "mtime": 1752078894941, "results": "1163", "hashOfConfig": "889"}, {"size": 5211, "mtime": 1752078894941, "results": "1164", "hashOfConfig": "889"}, {"size": 5725, "mtime": 1752078894941, "results": "1165", "hashOfConfig": "889"}, {"size": 4799, "mtime": 1752078894941, "results": "1166", "hashOfConfig": "889"}, {"size": 7774, "mtime": 1752078894941, "results": "1167", "hashOfConfig": "889"}, {"size": 2147, "mtime": 1752078894941, "results": "1168", "hashOfConfig": "889"}, {"size": 5677, "mtime": 1753101910657, "results": "1169", "hashOfConfig": "889"}, {"size": 737, "mtime": 1752078894941, "results": "1170", "hashOfConfig": "889"}, {"size": 5712, "mtime": 1752078894941, "results": "1171", "hashOfConfig": "889"}, {"size": 8862, "mtime": 1752078894941, "results": "1172", "hashOfConfig": "889"}, {"size": 3996, "mtime": 1752078894941, "results": "1173", "hashOfConfig": "889"}, {"size": 6753, "mtime": 1752078894941, "results": "1174", "hashOfConfig": "889"}, {"size": 6562, "mtime": 1752078894941, "results": "1175", "hashOfConfig": "889"}, {"size": 1779, "mtime": 1752078894941, "results": "1176", "hashOfConfig": "889"}, {"size": 9940, "mtime": 1753436763363, "results": "1177", "hashOfConfig": "889"}, {"size": 161, "mtime": 1752078894941, "results": "1178", "hashOfConfig": "889"}, {"size": 189, "mtime": 1752078894956, "results": "1179", "hashOfConfig": "889"}, {"size": 565, "mtime": 1752078894956, "results": "1180", "hashOfConfig": "889"}, {"size": 8610, "mtime": 1752078894956, "results": "1181", "hashOfConfig": "889"}, {"size": 1870, "mtime": 1752078894956, "results": "1182", "hashOfConfig": "889"}, {"size": 2516, "mtime": 1752078894956, "results": "1183", "hashOfConfig": "889"}, {"size": 14853, "mtime": 1752080815937, "results": "1184", "hashOfConfig": "889"}, {"size": 4727, "mtime": 1753105010421, "results": "1185", "hashOfConfig": "889"}, {"size": 2890, "mtime": 1752078894956, "results": "1186", "hashOfConfig": "889"}, {"size": 1924, "mtime": 1752078894956, "results": "1187", "hashOfConfig": "889"}, {"size": 2239, "mtime": 1752078894956, "results": "1188", "hashOfConfig": "889"}, {"size": 1646, "mtime": 1752078894956, "results": "1189", "hashOfConfig": "889"}, {"size": 5334, "mtime": 1752078894972, "results": "1190", "hashOfConfig": "889"}, {"size": 1048, "mtime": 1752078894956, "results": "1191", "hashOfConfig": "889"}, {"size": 3451, "mtime": 1752078894956, "results": "1192", "hashOfConfig": "889"}, {"size": 5179, "mtime": 1752078894956, "results": "1193", "hashOfConfig": "889"}, {"size": 4986, "mtime": 1752078894956, "results": "1194", "hashOfConfig": "889"}, {"size": 4326, "mtime": 1752078894972, "results": "1195", "hashOfConfig": "889"}, {"size": 411, "mtime": 1752078894988, "results": "1196", "hashOfConfig": "889"}, {"size": 2337, "mtime": 1752078894974, "results": "1197", "hashOfConfig": "889"}, {"size": 4822, "mtime": 1752078894975, "results": "1198", "hashOfConfig": "889"}, {"size": 4706, "mtime": 1752078894975, "results": "1199", "hashOfConfig": "889"}, {"size": 4957, "mtime": 1752078894976, "results": "1200", "hashOfConfig": "889"}, {"size": 3841, "mtime": 1752078894977, "results": "1201", "hashOfConfig": "889"}, {"size": 18438, "mtime": 1752078894978, "results": "1202", "hashOfConfig": "889"}, {"size": 1266, "mtime": 1752078894978, "results": "1203", "hashOfConfig": "889"}, {"size": 8177, "mtime": 1752078894979, "results": "1204", "hashOfConfig": "889"}, {"size": 1581, "mtime": 1752078894980, "results": "1205", "hashOfConfig": "889"}, {"size": 1343, "mtime": 1752080815937, "results": "1206", "hashOfConfig": "889"}, {"size": 1799, "mtime": 1752078894980, "results": "1207", "hashOfConfig": "889"}, {"size": 6966, "mtime": 1752078894981, "results": "1208", "hashOfConfig": "889"}, {"size": 295, "mtime": 1752078894981, "results": "1209", "hashOfConfig": "889"}, {"size": 3916, "mtime": 1752078894982, "results": "1210", "hashOfConfig": "889"}, {"size": 5877, "mtime": 1752078894983, "results": "1211", "hashOfConfig": "889"}, {"size": 18346, "mtime": 1752078894983, "results": "1212", "hashOfConfig": "889"}, {"size": 897, "mtime": 1752078894983, "results": "1213", "hashOfConfig": "889"}, {"size": 3936, "mtime": 1752078894983, "results": "1214", "hashOfConfig": "889"}, {"size": 23617, "mtime": 1752078894983, "results": "1215", "hashOfConfig": "889"}, {"size": 3305, "mtime": 1752078894983, "results": "1216", "hashOfConfig": "889"}, {"size": 3489, "mtime": 1752078894983, "results": "1217", "hashOfConfig": "889"}, {"size": 5456, "mtime": 1752078894988, "results": "1218", "hashOfConfig": "889"}, {"size": 1739, "mtime": 1752078894988, "results": "1219", "hashOfConfig": "889"}, {"size": 6529, "mtime": 1752078894988, "results": "1220", "hashOfConfig": "889"}, {"size": 3222, "mtime": 1752078894988, "results": "1221", "hashOfConfig": "889"}, {"size": 5762, "mtime": 1752078894988, "results": "1222", "hashOfConfig": "889"}, {"size": 3984, "mtime": 1752078894988, "results": "1223", "hashOfConfig": "889"}, {"size": 134, "mtime": 1752078894988, "results": "1224", "hashOfConfig": "889"}, {"size": 848, "mtime": 1752078894988, "results": "1225", "hashOfConfig": "889"}, {"size": 2993, "mtime": 1752078894988, "results": "1226", "hashOfConfig": "889"}, {"size": 5827, "mtime": 1752078894956, "results": "1227", "hashOfConfig": "889"}, {"size": 4054, "mtime": 1752078894988, "results": "1228", "hashOfConfig": "889"}, {"size": 5287, "mtime": 1752078894988, "results": "1229", "hashOfConfig": "889"}, {"size": 3121, "mtime": 1752078894988, "results": "1230", "hashOfConfig": "889"}, {"size": 3264, "mtime": 1752078894988, "results": "1231", "hashOfConfig": "889"}, {"size": 2619, "mtime": 1752078894988, "results": "1232", "hashOfConfig": "889"}, {"size": 3681, "mtime": 1752078894956, "results": "1233", "hashOfConfig": "889"}, {"size": 13610, "mtime": 1752078894988, "results": "1234", "hashOfConfig": "889"}, {"size": 5286, "mtime": 1752080815937, "results": "1235", "hashOfConfig": "889"}, {"size": 4433, "mtime": 1752078895003, "results": "1236", "hashOfConfig": "889"}, {"size": 5822, "mtime": 1752078894988, "results": "1237", "hashOfConfig": "889"}, {"size": 5776, "mtime": 1752078894988, "results": "1238", "hashOfConfig": "889"}, {"size": 13788, "mtime": 1752078894988, "results": "1239", "hashOfConfig": "889"}, {"size": 9337, "mtime": 1752078895003, "results": "1240", "hashOfConfig": "889"}, {"size": 3219, "mtime": 1752078894988, "results": "1241", "hashOfConfig": "889"}, {"size": 3306, "mtime": 1752078895003, "results": "1242", "hashOfConfig": "889"}, {"size": 14293, "mtime": 1752078895003, "results": "1243", "hashOfConfig": "889"}, {"size": 827, "mtime": 1752078895003, "results": "1244", "hashOfConfig": "889"}, {"size": 5416, "mtime": 1752080815952, "results": "1245", "hashOfConfig": "889"}, {"size": 6730, "mtime": 1752080815952, "results": "1246", "hashOfConfig": "889"}, {"size": 20267, "mtime": 1753436763363, "results": "1247", "hashOfConfig": "889"}, {"size": 13586, "mtime": 1753106163893, "results": "1248", "hashOfConfig": "889"}, {"size": 2735, "mtime": 1752078895003, "results": "1249", "hashOfConfig": "889"}, {"size": 925, "mtime": 1752078895003, "results": "1250", "hashOfConfig": "889"}, {"size": 1213, "mtime": 1752078895003, "results": "1251", "hashOfConfig": "889"}, {"size": 8134, "mtime": 1752078895003, "results": "1252", "hashOfConfig": "889"}, {"size": 957, "mtime": 1752078895003, "results": "1253", "hashOfConfig": "889"}, {"size": 2264, "mtime": 1752078895003, "results": "1254", "hashOfConfig": "889"}, {"size": 1677, "mtime": 1752078895003, "results": "1255", "hashOfConfig": "889"}, {"size": 1034, "mtime": 1752078895003, "results": "1256", "hashOfConfig": "889"}, {"size": 5544, "mtime": 1752078895003, "results": "1257", "hashOfConfig": "889"}, {"size": 2483, "mtime": 1752078895019, "results": "1258", "hashOfConfig": "889"}, {"size": 1092, "mtime": 1752078895019, "results": "1259", "hashOfConfig": "889"}, {"size": 4532, "mtime": 1752078895019, "results": "1260", "hashOfConfig": "889"}, {"size": 6920, "mtime": 1752080815952, "results": "1261", "hashOfConfig": "889"}, {"size": 3574, "mtime": 1752080815952, "results": "1262", "hashOfConfig": "889"}, {"size": 794, "mtime": 1752078895019, "results": "1263", "hashOfConfig": "889"}, {"size": 1902, "mtime": 1752078895019, "results": "1264", "hashOfConfig": "889"}, {"size": 1420, "mtime": 1752078895023, "results": "1265", "hashOfConfig": "889"}, {"size": 24194, "mtime": 1752080815952, "results": "1266", "hashOfConfig": "889"}, {"size": 555, "mtime": 1752078895023, "results": "1267", "hashOfConfig": "889"}, {"size": 4100, "mtime": 1752078895023, "results": "1268", "hashOfConfig": "889"}, {"size": 15578, "mtime": 1752078895023, "results": "1269", "hashOfConfig": "889"}, {"size": 3228, "mtime": 1752078895023, "results": "1270", "hashOfConfig": "889"}, {"size": 3514, "mtime": 1752078895023, "results": "1271", "hashOfConfig": "889"}, {"size": 23175, "mtime": 1752078895023, "results": "1272", "hashOfConfig": "889"}, {"size": 2060, "mtime": 1752078895023, "results": "1273", "hashOfConfig": "889"}, {"size": 16492, "mtime": 1752078895023, "results": "1274", "hashOfConfig": "889"}, {"size": 1149, "mtime": 1752078895023, "results": "1275", "hashOfConfig": "889"}, {"size": 3631, "mtime": 1752078895023, "results": "1276", "hashOfConfig": "889"}, {"size": 1859, "mtime": 1752078895023, "results": "1277", "hashOfConfig": "889"}, {"size": 4207, "mtime": 1752078895023, "results": "1278", "hashOfConfig": "889"}, {"size": 5060, "mtime": 1752078895023, "results": "1279", "hashOfConfig": "889"}, {"size": 3993, "mtime": 1752078895023, "results": "1280", "hashOfConfig": "889"}, {"size": 3872, "mtime": 1752078895023, "results": "1281", "hashOfConfig": "889"}, {"size": 1420, "mtime": 1752078895023, "results": "1282", "hashOfConfig": "889"}, {"size": 4730, "mtime": 1752078895035, "results": "1283", "hashOfConfig": "889"}, {"size": 5956, "mtime": 1752078895035, "results": "1284", "hashOfConfig": "889"}, {"size": 244, "mtime": 1752078895035, "results": "1285", "hashOfConfig": "889"}, {"size": 671, "mtime": 1752080815952, "results": "1286", "hashOfConfig": "889"}, {"size": 9220, "mtime": 1752078895035, "results": "1287", "hashOfConfig": "889"}, {"size": 9703, "mtime": 1752080815952, "results": "1288", "hashOfConfig": "889"}, {"size": 10536, "mtime": 1752080815952, "results": "1289", "hashOfConfig": "889"}, {"size": 12559, "mtime": 1752078895035, "results": "1290", "hashOfConfig": "889"}, {"size": 2402, "mtime": 1752080815952, "results": "1291", "hashOfConfig": "889"}, {"size": 1517, "mtime": 1752078895003, "results": "1292", "hashOfConfig": "889"}, {"size": 1951, "mtime": 1752078895003, "results": "1293", "hashOfConfig": "889"}, {"size": 4017, "mtime": 1752078895035, "results": "1294", "hashOfConfig": "889"}, {"size": 3456, "mtime": 1752078895035, "results": "1295", "hashOfConfig": "889"}, {"size": 4833, "mtime": 1752078895035, "results": "1296", "hashOfConfig": "889"}, {"size": 3938, "mtime": 1752078895035, "results": "1297", "hashOfConfig": "889"}, {"size": 5522, "mtime": 1752078895035, "results": "1298", "hashOfConfig": "889"}, {"size": 5183, "mtime": 1752078895035, "results": "1299", "hashOfConfig": "889"}, {"size": 7170, "mtime": 1752078895035, "results": "1300", "hashOfConfig": "889"}, {"size": 8695, "mtime": 1752078895051, "results": "1301", "hashOfConfig": "889"}, {"size": 1127, "mtime": 1752078895051, "results": "1302", "hashOfConfig": "889"}, {"size": 1607, "mtime": 1752078895035, "results": "1303", "hashOfConfig": "889"}, {"size": 2053, "mtime": 1752078895051, "results": "1304", "hashOfConfig": "889"}, {"size": 1135, "mtime": 1752078895051, "results": "1305", "hashOfConfig": "889"}, {"size": 9275, "mtime": 1752080815937, "results": "1306", "hashOfConfig": "889"}, {"size": 1655, "mtime": 1752078895051, "results": "1307", "hashOfConfig": "889"}, {"size": 4612, "mtime": 1753436763374, "results": "1308", "hashOfConfig": "889"}, {"size": 1388, "mtime": 1752078895051, "results": "1309", "hashOfConfig": "889"}, {"size": 8153, "mtime": 1752498088570, "results": "1310", "hashOfConfig": "889"}, {"size": 4771, "mtime": 1752485239443, "results": "1311", "hashOfConfig": "889"}, {"size": 5040, "mtime": 1753436763374, "results": "1312", "hashOfConfig": "889"}, {"size": 10831, "mtime": 1753436763374, "results": "1313", "hashOfConfig": "889"}, {"size": 1389, "mtime": 1752596854741, "results": "1314", "hashOfConfig": "889"}, {"size": 3766, "mtime": 1752078895051, "results": "1315", "hashOfConfig": "889"}, {"size": 3999, "mtime": 1752078895051, "results": "1316", "hashOfConfig": "889"}, {"size": 8192, "mtime": 1752078895066, "results": "1317", "hashOfConfig": "889"}, {"size": 4650, "mtime": 1752078895051, "results": "1318", "hashOfConfig": "889"}, {"size": 6600, "mtime": 1752078895051, "results": "1319", "hashOfConfig": "889"}, {"size": 5535, "mtime": 1752078895051, "results": "1320", "hashOfConfig": "889"}, {"size": 6402, "mtime": 1752078895051, "results": "1321", "hashOfConfig": "889"}, {"size": 6618, "mtime": 1752078895051, "results": "1322", "hashOfConfig": "889"}, {"size": 9406, "mtime": 1752078895051, "results": "1323", "hashOfConfig": "889"}, {"size": 4798, "mtime": 1752078895051, "results": "1324", "hashOfConfig": "889"}, {"size": 2182, "mtime": 1752078895066, "results": "1325", "hashOfConfig": "889"}, {"size": 14232, "mtime": 1752078895066, "results": "1326", "hashOfConfig": "889"}, {"size": 1626, "mtime": 1752078895066, "results": "1327", "hashOfConfig": "889"}, {"size": 14197, "mtime": 1752078895066, "results": "1328", "hashOfConfig": "889"}, {"size": 820, "mtime": 1752078895066, "results": "1329", "hashOfConfig": "889"}, {"size": 15316, "mtime": 1752078895066, "results": "1330", "hashOfConfig": "889"}, {"size": 1887, "mtime": 1752078895066, "results": "1331", "hashOfConfig": "889"}, {"size": 13686, "mtime": 1752078895066, "results": "1332", "hashOfConfig": "889"}, {"size": 1905, "mtime": 1752078895066, "results": "1333", "hashOfConfig": "889"}, {"size": 12380, "mtime": 1752078895066, "results": "1334", "hashOfConfig": "889"}, {"size": 1946, "mtime": 1752078895066, "results": "1335", "hashOfConfig": "889"}, {"size": 3999, "mtime": 1752078895066, "results": "1336", "hashOfConfig": "889"}, {"size": 6385, "mtime": 1752078895066, "results": "1337", "hashOfConfig": "889"}, {"size": 9551, "mtime": 1752078895066, "results": "1338", "hashOfConfig": "889"}, {"size": 13651, "mtime": 1752078895066, "results": "1339", "hashOfConfig": "889"}, {"size": 1826, "mtime": 1752078895066, "results": "1340", "hashOfConfig": "889"}, {"size": 1920, "mtime": 1752078895082, "results": "1341", "hashOfConfig": "889"}, {"size": 13936, "mtime": 1752078895082, "results": "1342", "hashOfConfig": "889"}, {"size": 1862, "mtime": 1752078895082, "results": "1343", "hashOfConfig": "889"}, {"size": 13015, "mtime": 1752078895082, "results": "1344", "hashOfConfig": "889"}, {"size": 13703, "mtime": 1752991606206, "results": "1345", "hashOfConfig": "889"}, {"size": 1899, "mtime": 1752078895086, "results": "1346", "hashOfConfig": "889"}, {"size": 11444, "mtime": 1752078895086, "results": "1347", "hashOfConfig": "889"}, {"size": 15279, "mtime": 1752078895086, "results": "1348", "hashOfConfig": "889"}, {"size": 852, "mtime": 1752078895086, "results": "1349", "hashOfConfig": "889"}, {"size": 1676, "mtime": 1753436763374, "results": "1350", "hashOfConfig": "889"}, {"size": 15760, "mtime": 1753436763374, "results": "1351", "hashOfConfig": "889"}, {"size": 759, "mtime": 1752588678090, "results": "1352", "hashOfConfig": "889"}, {"size": 4845, "mtime": 1752078895086, "results": "1353", "hashOfConfig": "889"}, {"size": 2787, "mtime": 1752588678105, "results": "1354", "hashOfConfig": "889"}, {"size": 9997, "mtime": 1752588678105, "results": "1355", "hashOfConfig": "889"}, {"size": 3634, "mtime": 1752078895086, "results": "1356", "hashOfConfig": "889"}, {"size": 10759, "mtime": 1752078895086, "results": "1357", "hashOfConfig": "889"}, {"size": 8359, "mtime": 1752588678105, "results": "1358", "hashOfConfig": "889"}, {"size": 995, "mtime": 1752078895086, "results": "1359", "hashOfConfig": "889"}, {"size": 3185, "mtime": 1752078895098, "results": "1360", "hashOfConfig": "889"}, {"size": 7318, "mtime": 1752078895098, "results": "1361", "hashOfConfig": "889"}, {"size": 4953, "mtime": 1752945992633, "results": "1362", "hashOfConfig": "889"}, {"size": 2913, "mtime": 1752078895099, "results": "1363", "hashOfConfig": "889"}, {"size": 1680, "mtime": 1752213788048, "results": "1364", "hashOfConfig": "889"}, {"size": 586, "mtime": 1752078895100, "results": "1365", "hashOfConfig": "889"}, {"size": 6995, "mtime": 1752078895086, "results": "1366", "hashOfConfig": "889"}, {"size": 1230, "mtime": 1752078895100, "results": "1367", "hashOfConfig": "889"}, {"size": 3561, "mtime": 1752078895100, "results": "1368", "hashOfConfig": "889"}, {"size": 9259, "mtime": 1753439540627, "results": "1369", "hashOfConfig": "889"}, {"size": 1417, "mtime": 1753436763404, "results": "1370", "hashOfConfig": "889"}, {"size": 7758, "mtime": 1753436763410, "results": "1371", "hashOfConfig": "889"}, {"size": 1485, "mtime": 1752078895145, "results": "1372", "hashOfConfig": "889"}, {"size": 5200, "mtime": 1753436763412, "results": "1373", "hashOfConfig": "889"}, {"size": 1954, "mtime": 1752078895161, "results": "1374", "hashOfConfig": "889"}, {"size": 4168, "mtime": 1752078895177, "results": "1375", "hashOfConfig": "889"}, {"size": 4294, "mtime": 1753436763420, "results": "1376", "hashOfConfig": "889"}, {"size": 8563, "mtime": 1753436763414, "results": "1377", "hashOfConfig": "889"}, {"size": 6840, "mtime": 1753436763415, "results": "1378", "hashOfConfig": "889"}, {"size": 6408, "mtime": 1753436763418, "results": "1379", "hashOfConfig": "889"}, {"size": 4277, "mtime": 1752078895145, "results": "1380", "hashOfConfig": "889"}, {"size": 3045, "mtime": 1752078895145, "results": "1381", "hashOfConfig": "889"}, {"size": 4119, "mtime": 1753033975155, "results": "1382", "hashOfConfig": "889"}, {"size": 5980, "mtime": 1752078895158, "results": "1383", "hashOfConfig": "889"}, {"size": 2041, "mtime": 1752078895158, "results": "1384", "hashOfConfig": "889"}, {"size": 228, "mtime": 1752078895158, "results": "1385", "hashOfConfig": "889"}, {"size": 7263, "mtime": 1752078895158, "results": "1386", "hashOfConfig": "889"}, {"size": 7147, "mtime": 1752078895161, "results": "1387", "hashOfConfig": "889"}, {"size": 846, "mtime": 1752078895161, "results": "1388", "hashOfConfig": "889"}, {"size": 3451, "mtime": 1752078895161, "results": "1389", "hashOfConfig": "889"}, {"size": 6345, "mtime": 1753101910671, "results": "1390", "hashOfConfig": "889"}, {"size": 4949, "mtime": 1753034133271, "results": "1391", "hashOfConfig": "889"}, {"size": 7441, "mtime": 1752078895161, "results": "1392", "hashOfConfig": "889"}, {"size": 4056, "mtime": 1753032433447, "results": "1393", "hashOfConfig": "889"}, {"size": 3193, "mtime": 1752078895161, "results": "1394", "hashOfConfig": "889"}, {"size": 9321, "mtime": 1753101910685, "results": "1395", "hashOfConfig": "889"}, {"size": 3720, "mtime": 1752078895178, "results": "1396", "hashOfConfig": "889"}, {"size": 3584, "mtime": 1752078895178, "results": "1397", "hashOfConfig": "889"}, {"size": 3246, "mtime": 1752078895178, "results": "1398", "hashOfConfig": "889"}, {"size": 1961, "mtime": 1752078895178, "results": "1399", "hashOfConfig": "889"}, {"size": 2418, "mtime": 1752078895178, "results": "1400", "hashOfConfig": "889"}, {"size": 3537, "mtime": 1752078895178, "results": "1401", "hashOfConfig": "889"}, {"size": 5109, "mtime": 1753101910685, "results": "1402", "hashOfConfig": "889"}, {"size": 9454, "mtime": 1752078895193, "results": "1403", "hashOfConfig": "889"}, {"size": 2906, "mtime": 1752078895195, "results": "1404", "hashOfConfig": "889"}, {"size": 1215, "mtime": 1753436763421, "results": "1405", "hashOfConfig": "889"}, {"size": 2508, "mtime": 1752078895195, "results": "1406", "hashOfConfig": "889"}, {"size": 852, "mtime": 1752078895240, "results": "1407", "hashOfConfig": "889"}, {"size": 4790, "mtime": 1752078895240, "results": "1408", "hashOfConfig": "889"}, {"size": 8785, "mtime": 1752696267110, "results": "1409", "hashOfConfig": "889"}, {"size": 696, "mtime": 1752078895240, "results": "1410", "hashOfConfig": "889"}, {"size": 3247, "mtime": 1752078895240, "results": "1411", "hashOfConfig": "889"}, {"size": 16852, "mtime": 1752078895240, "results": "1412", "hashOfConfig": "889"}, {"size": 6060, "mtime": 1752078895240, "results": "1413", "hashOfConfig": "889"}, {"size": 605, "mtime": 1752078895240, "results": "1414", "hashOfConfig": "889"}, {"size": 9700, "mtime": 1752686119805, "results": "1415", "hashOfConfig": "889"}, {"size": 706, "mtime": 1752078895240, "results": "1416", "hashOfConfig": "889"}, {"size": 1299, "mtime": 1752078895256, "results": "1417", "hashOfConfig": "889"}, {"size": 918, "mtime": 1752078895256, "results": "1418", "hashOfConfig": "889"}, {"size": 1155, "mtime": 1752078895256, "results": "1419", "hashOfConfig": "889"}, {"size": 1050, "mtime": 1752078895256, "results": "1420", "hashOfConfig": "889"}, {"size": 573, "mtime": 1752078895256, "results": "1421", "hashOfConfig": "889"}, {"size": 1580, "mtime": 1752078895256, "results": "1422", "hashOfConfig": "889"}, {"size": 766, "mtime": 1752078895256, "results": "1423", "hashOfConfig": "889"}, {"size": 3070, "mtime": 1752078895240, "results": "1424", "hashOfConfig": "889"}, {"size": 1187, "mtime": 1752078895240, "results": "1425", "hashOfConfig": "889"}, {"size": 560, "mtime": 1752078895240, "results": "1426", "hashOfConfig": "889"}, {"size": 10200, "mtime": 1753436763422, "results": "1427", "hashOfConfig": "889"}, {"size": 387, "mtime": 1752078895240, "results": "1428", "hashOfConfig": "889"}, {"size": 13488, "mtime": 1752078895240, "results": "1429", "hashOfConfig": "889"}, {"size": 1007, "mtime": 1752078895240, "results": "1430", "hashOfConfig": "889"}, {"size": 10905, "mtime": 1752078895240, "results": "1431", "hashOfConfig": "889"}, {"size": 1695, "mtime": 1752078895240, "results": "1432", "hashOfConfig": "889"}, {"size": 928, "mtime": 1752078895256, "results": "1433", "hashOfConfig": "889"}, {"size": 11881, "mtime": 1752078895256, "results": "1434", "hashOfConfig": "889"}, {"size": 5051, "mtime": 1752078895256, "results": "1435", "hashOfConfig": "889"}, {"size": 12521, "mtime": 1752078895256, "results": "1436", "hashOfConfig": "889"}, {"size": 8293, "mtime": 1752078895256, "results": "1437", "hashOfConfig": "889"}, {"size": 8100, "mtime": 1752078895256, "results": "1438", "hashOfConfig": "889"}, {"size": 468, "mtime": 1752078895240, "results": "1439", "hashOfConfig": "889"}, {"size": 8404, "mtime": 1752078895256, "results": "1440", "hashOfConfig": "889"}, {"size": 420, "mtime": 1752078895271, "results": "1441", "hashOfConfig": "889"}, {"size": 8986, "mtime": 1753531001344, "results": "1442", "hashOfConfig": "889"}, {"size": 2536, "mtime": 1752078895256, "results": "1443", "hashOfConfig": "889"}, {"size": 3620, "mtime": 1753514198416, "results": "1444", "hashOfConfig": "889"}, {"size": 2764, "mtime": 1752078895256, "results": "1445", "hashOfConfig": "889"}, {"size": 2336, "mtime": 1752078895256, "results": "1446", "hashOfConfig": "889"}, {"size": 15343, "mtime": 1752078895271, "results": "1447", "hashOfConfig": "889"}, {"size": 2399, "mtime": 1752078895271, "results": "1448", "hashOfConfig": "889"}, {"size": 2458, "mtime": 1753515135770, "results": "1449", "hashOfConfig": "889"}, {"size": 532, "mtime": 1752078895292, "results": "1450", "hashOfConfig": "889"}, {"size": 7698, "mtime": 1752842665103, "results": "1451", "hashOfConfig": "889"}, {"size": 2870, "mtime": 1752525255000, "results": "1452", "hashOfConfig": "889"}, {"size": 3578, "mtime": 1752525237933, "results": "1453", "hashOfConfig": "889"}, {"size": 3654, "mtime": 1752078895271, "results": "1454", "hashOfConfig": "889"}, {"size": 2711, "mtime": 1752078895271, "results": "1455", "hashOfConfig": "889"}, {"size": 5073, "mtime": 1752078895240, "results": "1456", "hashOfConfig": "889"}, {"size": 468, "mtime": 1752078895293, "results": "1457", "hashOfConfig": "889"}, {"size": 2384, "mtime": 1752078895293, "results": "1458", "hashOfConfig": "889"}, {"size": 2875, "mtime": 1752078895293, "results": "1459", "hashOfConfig": "889"}, {"size": 6770, "mtime": 1753069585595, "results": "1460", "hashOfConfig": "889"}, {"size": 3476, "mtime": 1752078895293, "results": "1461", "hashOfConfig": "889"}, {"size": 2968, "mtime": 1752078895293, "results": "1462", "hashOfConfig": "889"}, {"size": 7056, "mtime": 1753436763423, "results": "1463", "hashOfConfig": "889"}, {"size": 543, "mtime": 1752078895293, "results": "1464", "hashOfConfig": "889"}, {"size": 881, "mtime": 1752078895303, "results": "1465", "hashOfConfig": "889"}, {"size": 563, "mtime": 1752078895303, "results": "1466", "hashOfConfig": "889"}, {"size": 5611, "mtime": 1752078895303, "results": "1467", "hashOfConfig": "889"}, {"size": 2417, "mtime": 1752078895303, "results": "1468", "hashOfConfig": "889"}, {"size": 2958, "mtime": 1752078895303, "results": "1469", "hashOfConfig": "889"}, {"size": 140, "mtime": 1752078895303, "results": "1470", "hashOfConfig": "889"}, {"size": 5043, "mtime": 1752078895303, "results": "1471", "hashOfConfig": "889"}, {"size": 3818, "mtime": 1752078895303, "results": "1472", "hashOfConfig": "889"}, {"size": 6246, "mtime": 1752078895303, "results": "1473", "hashOfConfig": "889"}, {"size": 6801, "mtime": 1752078895303, "results": "1474", "hashOfConfig": "889"}, {"size": 2232, "mtime": 1752078895303, "results": "1475", "hashOfConfig": "889"}, {"size": 1235, "mtime": 1752078895303, "results": "1476", "hashOfConfig": "889"}, {"size": 2119, "mtime": 1752078895293, "results": "1477", "hashOfConfig": "889"}, {"size": 3747, "mtime": 1752078895293, "results": "1478", "hashOfConfig": "889"}, {"size": 2394, "mtime": 1752078895303, "results": "1479", "hashOfConfig": "889"}, {"size": 431, "mtime": 1752078895303, "results": "1480", "hashOfConfig": "889"}, {"size": 1606, "mtime": 1752078895303, "results": "1481", "hashOfConfig": "889"}, {"size": 3178, "mtime": 1753436763424, "results": "1482", "hashOfConfig": "889"}, {"size": 2689, "mtime": 1752078895303, "results": "1483", "hashOfConfig": "889"}, {"size": 1735, "mtime": 1752080815962, "results": "1484", "hashOfConfig": "889"}, {"size": 1639, "mtime": 1752080815962, "results": "1485", "hashOfConfig": "889"}, {"size": 8235, "mtime": 1753436763388, "results": "1486", "hashOfConfig": "889"}, {"size": 2860, "mtime": 1752078895114, "results": "1487", "hashOfConfig": "889"}, {"size": 8058, "mtime": 1752078895100, "results": "1488", "hashOfConfig": "889"}, {"size": 19772, "mtime": 1752080815952, "results": "1489", "hashOfConfig": "889"}, {"size": 6916, "mtime": 1752078895100, "results": "1490", "hashOfConfig": "889"}, {"size": 5552, "mtime": 1752078895100, "results": "1491", "hashOfConfig": "889"}, {"size": 2806, "mtime": 1752078895100, "results": "1492", "hashOfConfig": "889"}, {"size": 8383, "mtime": 1753436763388, "results": "1493", "hashOfConfig": "889"}, {"size": 14273, "mtime": 1753436763388, "results": "1494", "hashOfConfig": "889"}, {"size": 6718, "mtime": 1752078895111, "results": "1495", "hashOfConfig": "889"}, {"size": 5950, "mtime": 1753436763388, "results": "1496", "hashOfConfig": "889"}, {"size": 3064, "mtime": 1752078895114, "results": "1497", "hashOfConfig": "889"}, {"size": 1050, "mtime": 1752078895114, "results": "1498", "hashOfConfig": "889"}, {"size": 22169, "mtime": 1752078895114, "results": "1499", "hashOfConfig": "889"}, {"size": 4436, "mtime": 1752078895114, "results": "1500", "hashOfConfig": "889"}, {"size": 9131, "mtime": 1753436763388, "results": "1501", "hashOfConfig": "889"}, {"size": 4551, "mtime": 1753101910657, "results": "1502", "hashOfConfig": "889"}, {"size": 1237, "mtime": 1752078895114, "results": "1503", "hashOfConfig": "889"}, {"size": 456, "mtime": 1752078895114, "results": "1504", "hashOfConfig": "889"}, {"size": 15175, "mtime": 1753436763401, "results": "1505", "hashOfConfig": "889"}, {"size": 15806, "mtime": 1753106539818, "results": "1506", "hashOfConfig": "889"}, {"size": 1555, "mtime": 1752078895123, "results": "1507", "hashOfConfig": "889"}, {"size": 12447, "mtime": 1752078895123, "results": "1508", "hashOfConfig": "889"}, {"size": 3075, "mtime": 1752078895123, "results": "1509", "hashOfConfig": "889"}, {"size": 2658, "mtime": 1752078895123, "results": "1510", "hashOfConfig": "889"}, {"size": 4697, "mtime": 1752078895123, "results": "1511", "hashOfConfig": "889"}, {"size": 22440, "mtime": 1753105300361, "results": "1512", "hashOfConfig": "889"}, {"size": 3730, "mtime": 1752078895123, "results": "1513", "hashOfConfig": "889"}, {"size": 12465, "mtime": 1753105319939, "results": "1514", "hashOfConfig": "889"}, {"size": 3044, "mtime": 1752078895129, "results": "1515", "hashOfConfig": "889"}, {"size": 5097, "mtime": 1752078895129, "results": "1516", "hashOfConfig": "889"}, {"size": 10225, "mtime": 1753101910657, "results": "1517", "hashOfConfig": "889"}, {"size": 2253, "mtime": 1752078895123, "results": "1518", "hashOfConfig": "889"}, {"size": 3263, "mtime": 1753436763388, "results": "1519", "hashOfConfig": "889"}, {"size": 5326, "mtime": 1752080815962, "results": "1520", "hashOfConfig": "889"}, {"size": 5995, "mtime": 1752078895316, "results": "1521", "hashOfConfig": "889"}, {"size": 3946, "mtime": 1752078895319, "results": "1522", "hashOfConfig": "889"}, {"size": 8264, "mtime": 1752080815962, "results": "1523", "hashOfConfig": "889"}, {"size": 3007, "mtime": 1752078895320, "results": "1524", "hashOfConfig": "889"}, {"size": 4189, "mtime": 1752078895321, "results": "1525", "hashOfConfig": "889"}, {"size": 9778, "mtime": 1752078895321, "results": "1526", "hashOfConfig": "889"}, {"size": 10169, "mtime": 1752080815962, "results": "1527", "hashOfConfig": "889"}, {"size": 10217, "mtime": 1752080815971, "results": "1528", "hashOfConfig": "889"}, {"size": 6291, "mtime": 1752078895323, "results": "1529", "hashOfConfig": "889"}, {"size": 7264, "mtime": 1752078895323, "results": "1530", "hashOfConfig": "889"}, {"size": 7194, "mtime": 1752078895323, "results": "1531", "hashOfConfig": "889"}, {"size": 3629, "mtime": 1752682891985, "results": "1532", "hashOfConfig": "889"}, {"size": 8662, "mtime": 1752078895323, "results": "1533", "hashOfConfig": "889"}, {"size": 4435, "mtime": 1753101910685, "results": "1534", "hashOfConfig": "889"}, {"size": 19439, "mtime": 1752078895335, "results": "1535", "hashOfConfig": "889"}, {"size": 7315, "mtime": 1752078895335, "results": "1536", "hashOfConfig": "889"}, {"size": 8073, "mtime": 1752078895335, "results": "1537", "hashOfConfig": "889"}, {"size": 1910, "mtime": 1752078895335, "results": "1538", "hashOfConfig": "889"}, {"size": 8788, "mtime": 1752080815971, "results": "1539", "hashOfConfig": "889"}, {"size": 459, "mtime": 1752683108071, "results": "1540", "hashOfConfig": "889"}, {"size": 919, "mtime": 1752078895323, "results": "1541", "hashOfConfig": "889"}, {"size": 25953, "mtime": 1753106301218, "results": "1542", "hashOfConfig": "889"}, {"size": 3391, "mtime": 1752696314876, "results": "1543", "hashOfConfig": "889"}, {"size": 3447, "mtime": 1752078895323, "results": "1544", "hashOfConfig": "889"}, {"size": 41540, "mtime": 1752925036472, "results": "1545", "hashOfConfig": "889"}, {"size": 18331, "mtime": 1752925036472, "results": "1546", "hashOfConfig": "889"}, {"size": 2272, "mtime": 1753436763425, "results": "1547", "hashOfConfig": "889"}, {"size": 935, "mtime": 1752078895335, "results": "1548", "hashOfConfig": "889"}, {"size": 5951, "mtime": 1753101910685, "results": "1549", "hashOfConfig": "889"}, {"size": 1935, "mtime": 1752078895335, "results": "1550", "hashOfConfig": "889"}, {"size": 625, "mtime": 1752078895335, "results": "1551", "hashOfConfig": "889"}, {"size": 7782, "mtime": 1753521123008, "results": "1552", "hashOfConfig": "889"}, {"size": 8655, "mtime": 1753436763427, "results": "1553", "hashOfConfig": "889"}, {"size": 10022, "mtime": 1753452275165, "results": "1554", "hashOfConfig": "889"}, {"size": 7671, "mtime": 1752078895335, "results": "1555", "hashOfConfig": "889"}, {"size": 5281, "mtime": 1752078895335, "results": "1556", "hashOfConfig": "889"}, {"size": 5670, "mtime": 1753436763427, "results": "1557", "hashOfConfig": "889"}, {"size": 4400, "mtime": 1752078895335, "results": "1558", "hashOfConfig": "889"}, {"size": 5504, "mtime": 1753436763427, "results": "1559", "hashOfConfig": "889"}, {"size": 638, "mtime": 1752078895335, "results": "1560", "hashOfConfig": "889"}, {"size": 2119, "mtime": 1752078895335, "results": "1561", "hashOfConfig": "889"}, {"size": 4021, "mtime": 1752078895335, "results": "1562", "hashOfConfig": "889"}, {"size": 1680, "mtime": 1752078895335, "results": "1563", "hashOfConfig": "889"}, {"size": 1150, "mtime": 1752078895335, "results": "1564", "hashOfConfig": "889"}, {"size": 1677, "mtime": 1752078895335, "results": "1565", "hashOfConfig": "889"}, {"size": 2466, "mtime": 1752078895335, "results": "1566", "hashOfConfig": "889"}, {"size": 2140, "mtime": 1752498259457, "results": "1567", "hashOfConfig": "889"}, {"size": 2995, "mtime": 1752078895335, "results": "1568", "hashOfConfig": "889"}, {"size": 2081, "mtime": 1752078895350, "results": "1569", "hashOfConfig": "889"}, {"size": 5798, "mtime": 1752078895350, "results": "1570", "hashOfConfig": "889"}, {"size": 2814, "mtime": 1752078895350, "results": "1571", "hashOfConfig": "889"}, {"size": 10137, "mtime": 1752078895350, "results": "1572", "hashOfConfig": "889"}, {"size": 1258, "mtime": 1752078895350, "results": "1573", "hashOfConfig": "889"}, {"size": 833, "mtime": 1752078895350, "results": "1574", "hashOfConfig": "889"}, {"size": 4833, "mtime": 1752078895350, "results": "1575", "hashOfConfig": "889"}, {"size": 4119, "mtime": 1752078895350, "results": "1576", "hashOfConfig": "889"}, {"size": 8541, "mtime": 1752078895350, "results": "1577", "hashOfConfig": "889"}, {"size": 3926, "mtime": 1752078895350, "results": "1578", "hashOfConfig": "889"}, {"size": 2331, "mtime": 1752078895350, "results": "1579", "hashOfConfig": "889"}, {"size": 988, "mtime": 1752078895350, "results": "1580", "hashOfConfig": "889"}, {"size": 635, "mtime": 1752078895350, "results": "1581", "hashOfConfig": "889"}, {"size": 6792, "mtime": 1752078895350, "results": "1582", "hashOfConfig": "889"}, {"size": 3090, "mtime": 1752078895350, "results": "1583", "hashOfConfig": "889"}, {"size": 1683, "mtime": 1752078895350, "results": "1584", "hashOfConfig": "889"}, {"size": 771, "mtime": 1752078895350, "results": "1585", "hashOfConfig": "889"}, {"size": 1511, "mtime": 1752078895350, "results": "1586", "hashOfConfig": "889"}, {"size": 8203, "mtime": 1752078895350, "results": "1587", "hashOfConfig": "889"}, {"size": 1703, "mtime": 1752078895350, "results": "1588", "hashOfConfig": "889"}, {"size": 2479, "mtime": 1752078895350, "results": "1589", "hashOfConfig": "889"}, {"size": 6438, "mtime": 1752078895350, "results": "1590", "hashOfConfig": "889"}, {"size": 732, "mtime": 1752078895350, "results": "1591", "hashOfConfig": "889"}, {"size": 4244, "mtime": 1752078895350, "results": "1592", "hashOfConfig": "889"}, {"size": 22737, "mtime": 1752078895366, "results": "1593", "hashOfConfig": "889"}, {"size": 289, "mtime": 1752078895366, "results": "1594", "hashOfConfig": "889"}, {"size": 2064, "mtime": 1752078895366, "results": "1595", "hashOfConfig": "889"}, {"size": 589, "mtime": 1752078895366, "results": "1596", "hashOfConfig": "889"}, {"size": 1208, "mtime": 1752078895366, "results": "1597", "hashOfConfig": "889"}, {"size": 2564, "mtime": 1752078895366, "results": "1598", "hashOfConfig": "889"}, {"size": 2035, "mtime": 1752078895366, "results": "1599", "hashOfConfig": "889"}, {"size": 777, "mtime": 1752078895366, "results": "1600", "hashOfConfig": "889"}, {"size": 3457, "mtime": 1752078895366, "results": "1601", "hashOfConfig": "889"}, {"size": 1952, "mtime": 1752078895366, "results": "1602", "hashOfConfig": "889"}, {"size": 145, "mtime": 1752078895366, "results": "1603", "hashOfConfig": "889"}, {"size": 831, "mtime": 1752078895366, "results": "1604", "hashOfConfig": "889"}, {"size": 3614, "mtime": 1753105671578, "results": "1605", "hashOfConfig": "889"}, {"size": 2385, "mtime": 1753247176832, "results": "1606", "hashOfConfig": "889"}, {"size": 6785, "mtime": 1753247176840, "results": "1607", "hashOfConfig": "889"}, {"size": 668, "mtime": 1752078895397, "results": "1608", "hashOfConfig": "889"}, {"size": 3684, "mtime": 1752080815978, "results": "1609", "hashOfConfig": "889"}, {"size": 3392, "mtime": 1753436763427, "results": "1610", "hashOfConfig": "889"}, {"size": 5515, "mtime": 1753070785826, "results": "1611", "hashOfConfig": "889"}, {"size": 1819, "mtime": 1753070680799, "results": "1612", "hashOfConfig": "889"}, {"size": 832, "mtime": 1753070800480, "results": "1613", "hashOfConfig": "889"}, {"size": 2364, "mtime": 1752078895397, "results": "1614", "hashOfConfig": "889"}, {"size": 10155, "mtime": 1753105726378, "results": "1615", "hashOfConfig": "889"}, {"size": 178, "mtime": 1752078895397, "results": "1616", "hashOfConfig": "889"}, {"size": 8054, "mtime": 1752925036485, "results": "1617", "hashOfConfig": "889"}, {"size": 198, "mtime": 1752078895397, "results": "1618", "hashOfConfig": "889"}, {"size": 6353, "mtime": 1752078895413, "results": "1619", "hashOfConfig": "889"}, {"size": 12499, "mtime": 1753436763427, "results": "1620", "hashOfConfig": "889"}, {"size": 17868, "mtime": 1752080815978, "results": "1621", "hashOfConfig": "889"}, {"size": 1712, "mtime": 1753101910685, "results": "1622", "hashOfConfig": "889"}, {"size": 6536, "mtime": 1753069810361, "results": "1623", "hashOfConfig": "889"}, {"size": 9989, "mtime": 1752925036485, "results": "1624", "hashOfConfig": "889"}, {"size": 3278, "mtime": 1753101910685, "results": "1625", "hashOfConfig": "889"}, {"size": 651, "mtime": 1752078895413, "results": "1626", "hashOfConfig": "889"}, {"size": 878, "mtime": 1752078895413, "results": "1627", "hashOfConfig": "889"}, {"size": 11007, "mtime": 1753437325044, "results": "1628", "hashOfConfig": "889"}, {"size": 1318, "mtime": 1752078895413, "results": "1629", "hashOfConfig": "889"}, {"size": 152, "mtime": 1752078895413, "results": "1630", "hashOfConfig": "889"}, {"size": 1407, "mtime": 1753070813299, "results": "1631", "hashOfConfig": "889"}, {"size": 3482, "mtime": 1753106562909, "results": "1632", "hashOfConfig": "889"}, {"size": 2311, "mtime": 1753436763427, "results": "1633", "hashOfConfig": "889"}, {"size": 4772, "mtime": 1753436763427, "results": "1634", "hashOfConfig": "889"}, {"size": 10339, "mtime": 1753436763427, "results": "1635", "hashOfConfig": "889"}, {"size": 2424, "mtime": 1753101910697, "results": "1636", "hashOfConfig": "889"}, {"size": 3387, "mtime": 1753101910697, "results": "1637", "hashOfConfig": "889"}, {"size": 3822, "mtime": 1753030756833, "results": "1638", "hashOfConfig": "889"}, {"size": 3630, "mtime": 1753030782254, "results": "1639", "hashOfConfig": "889"}, {"size": 6648, "mtime": 1753101910697, "results": "1640", "hashOfConfig": "889"}, {"size": 5830, "mtime": 1753436763427, "results": "1641", "hashOfConfig": "889"}, {"size": 9159, "mtime": 1752078895423, "results": "1642", "hashOfConfig": "889"}, {"size": 22790, "mtime": 1753436763427, "results": "1643", "hashOfConfig": "889"}, {"size": 13447, "mtime": 1753025305927, "results": "1644", "hashOfConfig": "889"}, {"size": 2009, "mtime": 1752078895429, "results": "1645", "hashOfConfig": "889"}, {"size": 7106, "mtime": 1753025332125, "results": "1646", "hashOfConfig": "889"}, {"size": 15438, "mtime": 1753101910697, "results": "1647", "hashOfConfig": "889"}, {"size": 700, "mtime": 1752078895429, "results": "1648", "hashOfConfig": "889"}, {"size": 7366, "mtime": 1753025372961, "results": "1649", "hashOfConfig": "889"}, {"size": 2827, "mtime": 1753032425142, "results": "1650", "hashOfConfig": "889"}, {"size": 20026, "mtime": 1753025850817, "results": "1651", "hashOfConfig": "889"}, {"size": 391, "mtime": 1752078895429, "results": "1652", "hashOfConfig": "889"}, {"size": 11167, "mtime": 1752078895429, "results": "1653", "hashOfConfig": "889"}, {"size": 14242, "mtime": 1753436763427, "results": "1654", "hashOfConfig": "889"}, {"size": 675, "mtime": 1752078895429, "results": "1655", "hashOfConfig": "889"}, {"size": 10213, "mtime": 1753070867175, "results": "1656", "hashOfConfig": "889"}, {"size": 3534, "mtime": 1752078895423, "results": "1657", "hashOfConfig": "889"}, {"size": 651, "mtime": 1752078895429, "results": "1658", "hashOfConfig": "889"}, {"size": 1264, "mtime": 1752078895429, "results": "1659", "hashOfConfig": "889"}, {"size": 6440, "mtime": 1752078895429, "results": "1660", "hashOfConfig": "889"}, {"size": 7233, "mtime": 1752078895429, "results": "1661", "hashOfConfig": "889"}, {"size": 22742, "mtime": 1752078895429, "results": "1662", "hashOfConfig": "889"}, {"size": 20955, "mtime": 1752078895429, "results": "1663", "hashOfConfig": "889"}, {"size": 2503, "mtime": 1752078895429, "results": "1664", "hashOfConfig": "889"}, {"size": 17737, "mtime": 1752078895444, "results": "1665", "hashOfConfig": "889"}, {"size": 1604, "mtime": 1752078895444, "results": "1666", "hashOfConfig": "889"}, {"size": 4395, "mtime": 1752078895444, "results": "1667", "hashOfConfig": "889"}, {"size": 3461, "mtime": 1752078895397, "results": "1668", "hashOfConfig": "889"}, {"size": 25034, "mtime": 1752078895444, "results": "1669", "hashOfConfig": "889"}, {"size": 2857, "mtime": 1752078895444, "results": "1670", "hashOfConfig": "889"}, {"size": 2531, "mtime": 1752078895444, "results": "1671", "hashOfConfig": "889"}, {"size": 1791, "mtime": 1752078895444, "results": "1672", "hashOfConfig": "889"}, {"size": 4594, "mtime": 1752078895444, "results": "1673", "hashOfConfig": "889"}, {"size": 204, "mtime": 1752078895444, "results": "1674", "hashOfConfig": "889"}, {"size": 1354, "mtime": 1752078895444, "results": "1675", "hashOfConfig": "889"}, {"size": 2050, "mtime": 1752078895444, "results": "1676", "hashOfConfig": "889"}, {"size": 6896, "mtime": 1752078895444, "results": "1677", "hashOfConfig": "889"}, {"size": 5462, "mtime": 1752078895444, "results": "1678", "hashOfConfig": "889"}, {"size": 413, "mtime": 1752078895444, "results": "1679", "hashOfConfig": "889"}, {"size": 6636, "mtime": 1752078895444, "results": "1680", "hashOfConfig": "889"}, {"size": 5603, "mtime": 1752078895444, "results": "1681", "hashOfConfig": "889"}, {"size": 4862, "mtime": 1752078895444, "results": "1682", "hashOfConfig": "889"}, {"size": 2137, "mtime": 1752078895444, "results": "1683", "hashOfConfig": "889"}, {"size": 1973, "mtime": 1752078895460, "results": "1684", "hashOfConfig": "889"}, {"size": 3119, "mtime": 1752078895460, "results": "1685", "hashOfConfig": "889"}, {"size": 660, "mtime": 1752078895460, "results": "1686", "hashOfConfig": "889"}, {"size": 5475, "mtime": 1752078895460, "results": "1687", "hashOfConfig": "889"}, {"size": 1623, "mtime": 1752078895460, "results": "1688", "hashOfConfig": "889"}, {"size": 8552, "mtime": 1752078895460, "results": "1689", "hashOfConfig": "889"}, {"size": 10690, "mtime": 1752078895460, "results": "1690", "hashOfConfig": "889"}, {"size": 6640, "mtime": 1752078895460, "results": "1691", "hashOfConfig": "889"}, {"size": 3241, "mtime": 1752078895460, "results": "1692", "hashOfConfig": "889"}, {"size": 7150, "mtime": 1752078895460, "results": "1693", "hashOfConfig": "889"}, {"size": 4983, "mtime": 1753101910697, "results": "1694", "hashOfConfig": "889"}, {"size": 2445, "mtime": 1753032850435, "results": "1695", "hashOfConfig": "889"}, {"size": 6220, "mtime": 1753032880536, "results": "1696", "hashOfConfig": "889"}, {"size": 489, "mtime": 1752078895476, "results": "1697", "hashOfConfig": "889"}, {"size": 4821, "mtime": 1752078895476, "results": "1698", "hashOfConfig": "889"}, {"size": 5260, "mtime": 1752078895476, "results": "1699", "hashOfConfig": "889"}, {"size": 399, "mtime": 1752078895476, "results": "1700", "hashOfConfig": "889"}, {"size": 10939, "mtime": 1753031432329, "results": "1701", "hashOfConfig": "889"}, {"size": 326, "mtime": 1752078895476, "results": "1702", "hashOfConfig": "889"}, {"size": 16659, "mtime": 1753101910697, "results": "1703", "hashOfConfig": "889"}, {"size": 5532, "mtime": 1752078895476, "results": "1704", "hashOfConfig": "889"}, {"size": 745, "mtime": 1752078895476, "results": "1705", "hashOfConfig": "889"}, {"size": 18910, "mtime": 1753101910697, "results": "1706", "hashOfConfig": "889"}, {"size": 4902, "mtime": 1752078895491, "results": "1707", "hashOfConfig": "889"}, {"size": 5364, "mtime": 1753101910697, "results": "1708", "hashOfConfig": "889"}, {"size": 13619, "mtime": 1753101910697, "results": "1709", "hashOfConfig": "889"}, {"size": 17685, "mtime": 1753101910697, "results": "1710", "hashOfConfig": "889"}, {"size": 6851, "mtime": 1753101910697, "results": "1711", "hashOfConfig": "889"}, {"size": 7605, "mtime": 1753101910697, "results": "1712", "hashOfConfig": "889"}, {"size": 5185, "mtime": 1753101910711, "results": "1713", "hashOfConfig": "889"}, {"size": 5336, "mtime": 1753101910711, "results": "1714", "hashOfConfig": "889"}, {"size": 5761, "mtime": 1753101910711, "results": "1715", "hashOfConfig": "889"}, {"size": 4665, "mtime": 1753101910711, "results": "1716", "hashOfConfig": "889"}, {"size": 2533, "mtime": 1752078895494, "results": "1717", "hashOfConfig": "889"}, {"size": 423, "mtime": 1752078895494, "results": "1718", "hashOfConfig": "889"}, {"size": 730, "mtime": 1752078895494, "results": "1719", "hashOfConfig": "889"}, {"size": 18833, "mtime": 1753101910711, "results": "1720", "hashOfConfig": "889"}, {"size": 215, "mtime": 1752078895494, "results": "1721", "hashOfConfig": "889"}, {"size": 4141, "mtime": 1752078895494, "results": "1722", "hashOfConfig": "889"}, {"size": 5808, "mtime": 1753101910711, "results": "1723", "hashOfConfig": "889"}, {"size": 442, "mtime": 1752078895460, "results": "1724", "hashOfConfig": "889"}, {"size": 6526, "mtime": 1753101910711, "results": "1725", "hashOfConfig": "889"}, {"size": 8817, "mtime": 1753101910711, "results": "1726", "hashOfConfig": "889"}, {"size": 3451, "mtime": 1752078895508, "results": "1727", "hashOfConfig": "889"}, {"size": 14131, "mtime": 1752078895508, "results": "1728", "hashOfConfig": "889"}, {"size": 1425, "mtime": 1752692226155, "results": "1729", "hashOfConfig": "889"}, {"size": 3060, "mtime": 1752080816015, "results": "1730", "hashOfConfig": "889"}, {"size": 5105, "mtime": 1752078895508, "results": "1731", "hashOfConfig": "889"}, {"size": 31132, "mtime": 1753514047607, "results": "1732", "hashOfConfig": "889"}, {"size": 10437, "mtime": 1753436763427, "results": "1733", "hashOfConfig": "889"}, {"size": 1344, "mtime": 1752078895508, "results": "1734", "hashOfConfig": "889"}, {"size": 2968, "mtime": 1752078895508, "results": "1735", "hashOfConfig": "889"}, {"size": 5366, "mtime": 1752078895508, "results": "1736", "hashOfConfig": "889"}, {"size": 4748, "mtime": 1752078895508, "results": "1737", "hashOfConfig": "889"}, {"size": 15108, "mtime": 1753101910738, "results": "1738", "hashOfConfig": "889"}, {"size": 2252, "mtime": 1752078895508, "results": "1739", "hashOfConfig": "889"}, {"size": 1801, "mtime": 1753447333708, "results": "1740", "hashOfConfig": "889"}, {"size": 5627, "mtime": 1753101910738, "results": "1741", "hashOfConfig": "889"}, {"size": 4228, "mtime": 1752313833841, "results": "1742", "hashOfConfig": "889"}, {"size": 7156, "mtime": 1752078895520, "results": "1743", "hashOfConfig": "889"}, {"size": 9274, "mtime": 1752313704411, "results": "1744", "hashOfConfig": "889"}, {"size": 7254, "mtime": 1752313705953, "results": "1745", "hashOfConfig": "889"}, {"size": 737, "mtime": 1753101910738, "results": "1746", "hashOfConfig": "889"}, {"size": 504, "mtime": 1753101910738, "results": "1747", "hashOfConfig": "889"}, {"size": 416, "mtime": 1752078895520, "results": "1748", "hashOfConfig": "889"}, {"size": 1666, "mtime": 1753101910738, "results": "1749", "hashOfConfig": "889"}, {"size": 5102, "mtime": 1753070702360, "results": "1750", "hashOfConfig": "889"}, {"size": 1838, "mtime": 1752078895524, "results": "1751", "hashOfConfig": "889"}, {"size": 3163, "mtime": 1752078895525, "results": "1752", "hashOfConfig": "889"}, {"size": 4249, "mtime": 1752078895525, "results": "1753", "hashOfConfig": "889"}, {"size": 6892, "mtime": 1752685339243, "results": "1754", "hashOfConfig": "889"}, {"size": 4659, "mtime": 1752078895525, "results": "1755", "hashOfConfig": "889"}, {"size": 5800, "mtime": 1752078895525, "results": "1756", "hashOfConfig": "889"}, {"size": 1337, "mtime": 1752078895525, "results": "1757", "hashOfConfig": "889"}, {"size": 3715, "mtime": 1753436763427, "results": "1758", "hashOfConfig": "889"}, {"size": 5943, "mtime": 1752078895525, "results": "1759", "hashOfConfig": "889"}, {"size": 9375, "mtime": 1753436763427, "results": "1760", "hashOfConfig": "889"}, {"size": 10403, "mtime": 1753436763427, "results": "1761", "hashOfConfig": "889"}, {"size": 4149, "mtime": 1753436763442, "results": "1762", "hashOfConfig": "889"}, {"size": 7483, "mtime": 1752078895525, "results": "1763", "hashOfConfig": "889"}, {"size": 5633, "mtime": 1753436763442, "results": "1764", "hashOfConfig": "889"}, {"size": 6872, "mtime": 1752078895540, "results": "1765", "hashOfConfig": "889"}, {"size": 5929, "mtime": 1752078895540, "results": "1766", "hashOfConfig": "889"}, {"size": 1824, "mtime": 1752078895540, "results": "1767", "hashOfConfig": "889"}, {"size": 2184, "mtime": 1752078895540, "results": "1768", "hashOfConfig": "889"}, {"size": 5432, "mtime": 1752685361427, "results": "1769", "hashOfConfig": "889"}, {"size": 7773, "mtime": 1752080816015, "results": "1770", "hashOfConfig": "889"}, {"size": 1869, "mtime": 1753436763442, "results": "1771", "hashOfConfig": "889"}, {"size": 6965, "mtime": 1752078895540, "results": "1772", "hashOfConfig": "889"}, {"size": 7497, "mtime": 1752427779267, "results": "1773", "hashOfConfig": "889"}, {"size": 10682, "mtime": 1753101910738, "results": "1774", "hashOfConfig": "889"}, {"size": 6604, "mtime": 1752078895525, "results": "1775", "hashOfConfig": "889"}, {"filePath": "1776", "messages": "1777", "suppressedMessages": "1778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3j0uch", {"filePath": "1779", "messages": "1780", "suppressedMessages": "1781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1782", "messages": "1783", "suppressedMessages": "1784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1785", "messages": "1786", "suppressedMessages": "1787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1788", "messages": "1789", "suppressedMessages": "1790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1791", "messages": "1792", "suppressedMessages": "1793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1794", "messages": "1795", "suppressedMessages": "1796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1797", "messages": "1798", "suppressedMessages": "1799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1800", "messages": "1801", "suppressedMessages": "1802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1803", "messages": "1804", "suppressedMessages": "1805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1806", "messages": "1807", "suppressedMessages": "1808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1809", "messages": "1810", "suppressedMessages": "1811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1812", "messages": "1813", "suppressedMessages": "1814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1815", "messages": "1816", "suppressedMessages": "1817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1818", "messages": "1819", "suppressedMessages": "1820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1821", "messages": "1822", "suppressedMessages": "1823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1824", "messages": "1825", "suppressedMessages": "1826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1827", "messages": "1828", "suppressedMessages": "1829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1830", "messages": "1831", "suppressedMessages": "1832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1833", "messages": "1834", "suppressedMessages": "1835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1836", "messages": "1837", "suppressedMessages": "1838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1839", "messages": "1840", "suppressedMessages": "1841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1842", "messages": "1843", "suppressedMessages": "1844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1845", "messages": "1846", "suppressedMessages": "1847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1848", "messages": "1849", "suppressedMessages": "1850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1851", "messages": "1852", "suppressedMessages": "1853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1854", "messages": "1855", "suppressedMessages": "1856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1857", "messages": "1858", "suppressedMessages": "1859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1860", "messages": "1861", "suppressedMessages": "1862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1863", "messages": "1864", "suppressedMessages": "1865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1866", "messages": "1867", "suppressedMessages": "1868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1869", "messages": "1870", "suppressedMessages": "1871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1872", "messages": "1873", "suppressedMessages": "1874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1875", "messages": "1876", "suppressedMessages": "1877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1878", "messages": "1879", "suppressedMessages": "1880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1881", "messages": "1882", "suppressedMessages": "1883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1884", "messages": "1885", "suppressedMessages": "1886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1887", "messages": "1888", "suppressedMessages": "1889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1890", "messages": "1891", "suppressedMessages": "1892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1893", "messages": "1894", "suppressedMessages": "1895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1896", "messages": "1897", "suppressedMessages": "1898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1899", "messages": "1900", "suppressedMessages": "1901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1902", "messages": "1903", "suppressedMessages": "1904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1905", "messages": "1906", "suppressedMessages": "1907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1908", "messages": "1909", "suppressedMessages": "1910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1911", "messages": "1912", "suppressedMessages": "1913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1914", "messages": "1915", "suppressedMessages": "1916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1917", "messages": "1918", "suppressedMessages": "1919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1920", "messages": "1921", "suppressedMessages": "1922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1923", "messages": "1924", "suppressedMessages": "1925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1926", "messages": "1927", "suppressedMessages": "1928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1929", "messages": "1930", "suppressedMessages": "1931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1932", "messages": "1933", "suppressedMessages": "1934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1935", "messages": "1936", "suppressedMessages": "1937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1938", "messages": "1939", "suppressedMessages": "1940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1941", "messages": "1942", "suppressedMessages": "1943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1944", "messages": "1945", "suppressedMessages": "1946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1947", "messages": "1948", "suppressedMessages": "1949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1950", "messages": "1951", "suppressedMessages": "1952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1953", "messages": "1954", "suppressedMessages": "1955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1956", "messages": "1957", "suppressedMessages": "1958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1959", "messages": "1960", "suppressedMessages": "1961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1962", "messages": "1963", "suppressedMessages": "1964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1965", "messages": "1966", "suppressedMessages": "1967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1968", "messages": "1969", "suppressedMessages": "1970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1971", "messages": "1972", "suppressedMessages": "1973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1974", "messages": "1975", "suppressedMessages": "1976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1977", "messages": "1978", "suppressedMessages": "1979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1980", "messages": "1981", "suppressedMessages": "1982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1983", "messages": "1984", "suppressedMessages": "1985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1986", "messages": "1987", "suppressedMessages": "1988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1989", "messages": "1990", "suppressedMessages": "1991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1992", "messages": "1993", "suppressedMessages": "1994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1995", "messages": "1996", "suppressedMessages": "1997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1998", "messages": "1999", "suppressedMessages": "2000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2001", "messages": "2002", "suppressedMessages": "2003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2004", "messages": "2005", "suppressedMessages": "2006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2007", "messages": "2008", "suppressedMessages": "2009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2010", "messages": "2011", "suppressedMessages": "2012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2013", "messages": "2014", "suppressedMessages": "2015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2016", "messages": "2017", "suppressedMessages": "2018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2019", "messages": "2020", "suppressedMessages": "2021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2022", "messages": "2023", "suppressedMessages": "2024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2025", "messages": "2026", "suppressedMessages": "2027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2028", "messages": "2029", "suppressedMessages": "2030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2031", "messages": "2032", "suppressedMessages": "2033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2034", "messages": "2035", "suppressedMessages": "2036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2037", "messages": "2038", "suppressedMessages": "2039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2040", "messages": "2041", "suppressedMessages": "2042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2043", "messages": "2044", "suppressedMessages": "2045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2046", "messages": "2047", "suppressedMessages": "2048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2049", "messages": "2050", "suppressedMessages": "2051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2052", "messages": "2053", "suppressedMessages": "2054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2055", "messages": "2056", "suppressedMessages": "2057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2058", "messages": "2059", "suppressedMessages": "2060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2061", "messages": "2062", "suppressedMessages": "2063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2064", "messages": "2065", "suppressedMessages": "2066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2067", "messages": "2068", "suppressedMessages": "2069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2070", "messages": "2071", "suppressedMessages": "2072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2073", "messages": "2074", "suppressedMessages": "2075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2076", "messages": "2077", "suppressedMessages": "2078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2079", "messages": "2080", "suppressedMessages": "2081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2082", "messages": "2083", "suppressedMessages": "2084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2085", "messages": "2086", "suppressedMessages": "2087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2088", "messages": "2089", "suppressedMessages": "2090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2091", "messages": "2092", "suppressedMessages": "2093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2094", "messages": "2095", "suppressedMessages": "2096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2097", "messages": "2098", "suppressedMessages": "2099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2100", "messages": "2101", "suppressedMessages": "2102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2103", "messages": "2104", "suppressedMessages": "2105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2106", "messages": "2107", "suppressedMessages": "2108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2109", "messages": "2110", "suppressedMessages": "2111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2112", "messages": "2113", "suppressedMessages": "2114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2115", "messages": "2116", "suppressedMessages": "2117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2118", "messages": "2119", "suppressedMessages": "2120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2121", "messages": "2122", "suppressedMessages": "2123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2124", "messages": "2125", "suppressedMessages": "2126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2127", "messages": "2128", "suppressedMessages": "2129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2130", "messages": "2131", "suppressedMessages": "2132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2133", "messages": "2134", "suppressedMessages": "2135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2136", "messages": "2137", "suppressedMessages": "2138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2139", "messages": "2140", "suppressedMessages": "2141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2142", "messages": "2143", "suppressedMessages": "2144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2145", "messages": "2146", "suppressedMessages": "2147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2148", "messages": "2149", "suppressedMessages": "2150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2151", "messages": "2152", "suppressedMessages": "2153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2154", "messages": "2155", "suppressedMessages": "2156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2157", "messages": "2158", "suppressedMessages": "2159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2160", "messages": "2161", "suppressedMessages": "2162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2163", "messages": "2164", "suppressedMessages": "2165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2166", "messages": "2167", "suppressedMessages": "2168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2169", "messages": "2170", "suppressedMessages": "2171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2172", "messages": "2173", "suppressedMessages": "2174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2175", "messages": "2176", "suppressedMessages": "2177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2178", "messages": "2179", "suppressedMessages": "2180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2181", "messages": "2182", "suppressedMessages": "2183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2184", "messages": "2185", "suppressedMessages": "2186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2187", "messages": "2188", "suppressedMessages": "2189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2190", "messages": "2191", "suppressedMessages": "2192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2193", "messages": "2194", "suppressedMessages": "2195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2196", "messages": "2197", "suppressedMessages": "2198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2199", "messages": "2200", "suppressedMessages": "2201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2202", "messages": "2203", "suppressedMessages": "2204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2205", "messages": "2206", "suppressedMessages": "2207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2208", "messages": "2209", "suppressedMessages": "2210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2211", "messages": "2212", "suppressedMessages": "2213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2214", "messages": "2215", "suppressedMessages": "2216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2217", "messages": "2218", "suppressedMessages": "2219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2220", "messages": "2221", "suppressedMessages": "2222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2223", "messages": "2224", "suppressedMessages": "2225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2226", "messages": "2227", "suppressedMessages": "2228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2229", "messages": "2230", "suppressedMessages": "2231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2232", "messages": "2233", "suppressedMessages": "2234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2235", "messages": "2236", "suppressedMessages": "2237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2238", "messages": "2239", "suppressedMessages": "2240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2241", "messages": "2242", "suppressedMessages": "2243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2244", "messages": "2245", "suppressedMessages": "2246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2247", "messages": "2248", "suppressedMessages": "2249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2250", "messages": "2251", "suppressedMessages": "2252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2253", "messages": "2254", "suppressedMessages": "2255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2256", "messages": "2257", "suppressedMessages": "2258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2259", "messages": "2260", "suppressedMessages": "2261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2262", "messages": "2263", "suppressedMessages": "2264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2265", "messages": "2266", "suppressedMessages": "2267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2268", "messages": "2269", "suppressedMessages": "2270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2271", "messages": "2272", "suppressedMessages": "2273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2274", "messages": "2275", "suppressedMessages": "2276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2277", "messages": "2278", "suppressedMessages": "2279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2280", "messages": "2281", "suppressedMessages": "2282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2283", "messages": "2284", "suppressedMessages": "2285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2286", "messages": "2287", "suppressedMessages": "2288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2289", "messages": "2290", "suppressedMessages": "2291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2292", "messages": "2293", "suppressedMessages": "2294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2295", "messages": "2296", "suppressedMessages": "2297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2298", "messages": "2299", "suppressedMessages": "2300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2301", "messages": "2302", "suppressedMessages": "2303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2304", "messages": "2305", "suppressedMessages": "2306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2307", "messages": "2308", "suppressedMessages": "2309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2310", "messages": "2311", "suppressedMessages": "2312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2313", "messages": "2314", "suppressedMessages": "2315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2316", "messages": "2317", "suppressedMessages": "2318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2319", "messages": "2320", "suppressedMessages": "2321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2322", "messages": "2323", "suppressedMessages": "2324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2325", "messages": "2326", "suppressedMessages": "2327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2328", "messages": "2329", "suppressedMessages": "2330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2331", "messages": "2332", "suppressedMessages": "2333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2334", "messages": "2335", "suppressedMessages": "2336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2337", "messages": "2338", "suppressedMessages": "2339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2340", "messages": "2341", "suppressedMessages": "2342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2343", "messages": "2344", "suppressedMessages": "2345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2346", "messages": "2347", "suppressedMessages": "2348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2349", "messages": "2350", "suppressedMessages": "2351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2352", "messages": "2353", "suppressedMessages": "2354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2355", "messages": "2356", "suppressedMessages": "2357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2358", "messages": "2359", "suppressedMessages": "2360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2361", "messages": "2362", "suppressedMessages": "2363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2364", "messages": "2365", "suppressedMessages": "2366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2367", "messages": "2368", "suppressedMessages": "2369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2370", "messages": "2371", "suppressedMessages": "2372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2373", "messages": "2374", "suppressedMessages": "2375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2376", "messages": "2377", "suppressedMessages": "2378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2379", "messages": "2380", "suppressedMessages": "2381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2382", "messages": "2383", "suppressedMessages": "2384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2385", "messages": "2386", "suppressedMessages": "2387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2388", "messages": "2389", "suppressedMessages": "2390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2391", "messages": "2392", "suppressedMessages": "2393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2394", "messages": "2395", "suppressedMessages": "2396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2397", "messages": "2398", "suppressedMessages": "2399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2400", "messages": "2401", "suppressedMessages": "2402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2403", "messages": "2404", "suppressedMessages": "2405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2406", "messages": "2407", "suppressedMessages": "2408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2409", "messages": "2410", "suppressedMessages": "2411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2412", "messages": "2413", "suppressedMessages": "2414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2415", "messages": "2416", "suppressedMessages": "2417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2418", "messages": "2419", "suppressedMessages": "2420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2421", "messages": "2422", "suppressedMessages": "2423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2424", "messages": "2425", "suppressedMessages": "2426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2427", "messages": "2428", "suppressedMessages": "2429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2430", "messages": "2431", "suppressedMessages": "2432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2433", "messages": "2434", "suppressedMessages": "2435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2436", "messages": "2437", "suppressedMessages": "2438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2439", "messages": "2440", "suppressedMessages": "2441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2442", "messages": "2443", "suppressedMessages": "2444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2445", "messages": "2446", "suppressedMessages": "2447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2448", "messages": "2449", "suppressedMessages": "2450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2451", "messages": "2452", "suppressedMessages": "2453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2454", "messages": "2455", "suppressedMessages": "2456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2457", "messages": "2458", "suppressedMessages": "2459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2460", "messages": "2461", "suppressedMessages": "2462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2463", "messages": "2464", "suppressedMessages": "2465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2466", "messages": "2467", "suppressedMessages": "2468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2469", "messages": "2470", "suppressedMessages": "2471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2472", "messages": "2473", "suppressedMessages": "2474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2475", "messages": "2476", "suppressedMessages": "2477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2478", "messages": "2479", "suppressedMessages": "2480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2481", "messages": "2482", "suppressedMessages": "2483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2484", "messages": "2485", "suppressedMessages": "2486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2487", "messages": "2488", "suppressedMessages": "2489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2490", "messages": "2491", "suppressedMessages": "2492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2493", "messages": "2494", "suppressedMessages": "2495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2496", "messages": "2497", "suppressedMessages": "2498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2499", "messages": "2500", "suppressedMessages": "2501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2502", "messages": "2503", "suppressedMessages": "2504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2505", "messages": "2506", "suppressedMessages": "2507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2508", "messages": "2509", "suppressedMessages": "2510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2511", "messages": "2512", "suppressedMessages": "2513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2514", "messages": "2515", "suppressedMessages": "2516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2517", "messages": "2518", "suppressedMessages": "2519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2520", "messages": "2521", "suppressedMessages": "2522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2523", "messages": "2524", "suppressedMessages": "2525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2526", "messages": "2527", "suppressedMessages": "2528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2529", "messages": "2530", "suppressedMessages": "2531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2532", "messages": "2533", "suppressedMessages": "2534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2535", "messages": "2536", "suppressedMessages": "2537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2538", "messages": "2539", "suppressedMessages": "2540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2541", "messages": "2542", "suppressedMessages": "2543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2544", "messages": "2545", "suppressedMessages": "2546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2547", "messages": "2548", "suppressedMessages": "2549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2550", "messages": "2551", "suppressedMessages": "2552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2553", "messages": "2554", "suppressedMessages": "2555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2556", "messages": "2557", "suppressedMessages": "2558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2559", "messages": "2560", "suppressedMessages": "2561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2562", "messages": "2563", "suppressedMessages": "2564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2565", "messages": "2566", "suppressedMessages": "2567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2568", "messages": "2569", "suppressedMessages": "2570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2571", "messages": "2572", "suppressedMessages": "2573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2574", "messages": "2575", "suppressedMessages": "2576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2577", "messages": "2578", "suppressedMessages": "2579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2580", "messages": "2581", "suppressedMessages": "2582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2583", "messages": "2584", "suppressedMessages": "2585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2586", "messages": "2587", "suppressedMessages": "2588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2589", "messages": "2590", "suppressedMessages": "2591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2592", "messages": "2593", "suppressedMessages": "2594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2595", "messages": "2596", "suppressedMessages": "2597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2598", "messages": "2599", "suppressedMessages": "2600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2601", "messages": "2602", "suppressedMessages": "2603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2604", "messages": "2605", "suppressedMessages": "2606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2607", "messages": "2608", "suppressedMessages": "2609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2610", "messages": "2611", "suppressedMessages": "2612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2613", "messages": "2614", "suppressedMessages": "2615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2616", "messages": "2617", "suppressedMessages": "2618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2619", "messages": "2620", "suppressedMessages": "2621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2622", "messages": "2623", "suppressedMessages": "2624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2625", "messages": "2626", "suppressedMessages": "2627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2628", "messages": "2629", "suppressedMessages": "2630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2631", "messages": "2632", "suppressedMessages": "2633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2634", "messages": "2635", "suppressedMessages": "2636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2637", "messages": "2638", "suppressedMessages": "2639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2640", "messages": "2641", "suppressedMessages": "2642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2643", "messages": "2644", "suppressedMessages": "2645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2646", "messages": "2647", "suppressedMessages": "2648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2649", "messages": "2650", "suppressedMessages": "2651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2652", "messages": "2653", "suppressedMessages": "2654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2655", "messages": "2656", "suppressedMessages": "2657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2658", "messages": "2659", "suppressedMessages": "2660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2661", "messages": "2662", "suppressedMessages": "2663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2664", "messages": "2665", "suppressedMessages": "2666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2667", "messages": "2668", "suppressedMessages": "2669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2670", "messages": "2671", "suppressedMessages": "2672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2673", "messages": "2674", "suppressedMessages": "2675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2676", "messages": "2677", "suppressedMessages": "2678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2679", "messages": "2680", "suppressedMessages": "2681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2682", "messages": "2683", "suppressedMessages": "2684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2685", "messages": "2686", "suppressedMessages": "2687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2688", "messages": "2689", "suppressedMessages": "2690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2691", "messages": "2692", "suppressedMessages": "2693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2694", "messages": "2695", "suppressedMessages": "2696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2697", "messages": "2698", "suppressedMessages": "2699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2700", "messages": "2701", "suppressedMessages": "2702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2703", "messages": "2704", "suppressedMessages": "2705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2706", "messages": "2707", "suppressedMessages": "2708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2709", "messages": "2710", "suppressedMessages": "2711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2712", "messages": "2713", "suppressedMessages": "2714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2715", "messages": "2716", "suppressedMessages": "2717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2718", "messages": "2719", "suppressedMessages": "2720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2721", "messages": "2722", "suppressedMessages": "2723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2724", "messages": "2725", "suppressedMessages": "2726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2727", "messages": "2728", "suppressedMessages": "2729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2730", "messages": "2731", "suppressedMessages": "2732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2733", "messages": "2734", "suppressedMessages": "2735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2736", "messages": "2737", "suppressedMessages": "2738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2739", "messages": "2740", "suppressedMessages": "2741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2742", "messages": "2743", "suppressedMessages": "2744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2745", "messages": "2746", "suppressedMessages": "2747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2748", "messages": "2749", "suppressedMessages": "2750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2751", "messages": "2752", "suppressedMessages": "2753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2754", "messages": "2755", "suppressedMessages": "2756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2757", "messages": "2758", "suppressedMessages": "2759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2760", "messages": "2761", "suppressedMessages": "2762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2763", "messages": "2764", "suppressedMessages": "2765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2766", "messages": "2767", "suppressedMessages": "2768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2769", "messages": "2770", "suppressedMessages": "2771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2772", "messages": "2773", "suppressedMessages": "2774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2775", "messages": "2776", "suppressedMessages": "2777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2778", "messages": "2779", "suppressedMessages": "2780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2781", "messages": "2782", "suppressedMessages": "2783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2784", "messages": "2785", "suppressedMessages": "2786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2787", "messages": "2788", "suppressedMessages": "2789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2790", "messages": "2791", "suppressedMessages": "2792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2793", "messages": "2794", "suppressedMessages": "2795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2796", "messages": "2797", "suppressedMessages": "2798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2799", "messages": "2800", "suppressedMessages": "2801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2802", "messages": "2803", "suppressedMessages": "2804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2805", "messages": "2806", "suppressedMessages": "2807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2808", "messages": "2809", "suppressedMessages": "2810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2811", "messages": "2812", "suppressedMessages": "2813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2814", "messages": "2815", "suppressedMessages": "2816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2817", "messages": "2818", "suppressedMessages": "2819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2820", "messages": "2821", "suppressedMessages": "2822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2823", "messages": "2824", "suppressedMessages": "2825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2826", "messages": "2827", "suppressedMessages": "2828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2829", "messages": "2830", "suppressedMessages": "2831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2832", "messages": "2833", "suppressedMessages": "2834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2835", "messages": "2836", "suppressedMessages": "2837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2838", "messages": "2839", "suppressedMessages": "2840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2841", "messages": "2842", "suppressedMessages": "2843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2844", "messages": "2845", "suppressedMessages": "2846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2847", "messages": "2848", "suppressedMessages": "2849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2850", "messages": "2851", "suppressedMessages": "2852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2853", "messages": "2854", "suppressedMessages": "2855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2856", "messages": "2857", "suppressedMessages": "2858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2859", "messages": "2860", "suppressedMessages": "2861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2862", "messages": "2863", "suppressedMessages": "2864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2865", "messages": "2866", "suppressedMessages": "2867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2868", "messages": "2869", "suppressedMessages": "2870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2871", "messages": "2872", "suppressedMessages": "2873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2874", "messages": "2875", "suppressedMessages": "2876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2877", "messages": "2878", "suppressedMessages": "2879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2880", "messages": "2881", "suppressedMessages": "2882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2883", "messages": "2884", "suppressedMessages": "2885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2886", "messages": "2887", "suppressedMessages": "2888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2889", "messages": "2890", "suppressedMessages": "2891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2892", "messages": "2893", "suppressedMessages": "2894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2895", "messages": "2896", "suppressedMessages": "2897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2898", "messages": "2899", "suppressedMessages": "2900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2901", "messages": "2902", "suppressedMessages": "2903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2904", "messages": "2905", "suppressedMessages": "2906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2907", "messages": "2908", "suppressedMessages": "2909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2910", "messages": "2911", "suppressedMessages": "2912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2913", "messages": "2914", "suppressedMessages": "2915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2916", "messages": "2917", "suppressedMessages": "2918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2919", "messages": "2920", "suppressedMessages": "2921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2922", "messages": "2923", "suppressedMessages": "2924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2925", "messages": "2926", "suppressedMessages": "2927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2928", "messages": "2929", "suppressedMessages": "2930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2931", "messages": "2932", "suppressedMessages": "2933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2934", "messages": "2935", "suppressedMessages": "2936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2937", "messages": "2938", "suppressedMessages": "2939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2940", "messages": "2941", "suppressedMessages": "2942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2943", "messages": "2944", "suppressedMessages": "2945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2946", "messages": "2947", "suppressedMessages": "2948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2949", "messages": "2950", "suppressedMessages": "2951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2952", "messages": "2953", "suppressedMessages": "2954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2955", "messages": "2956", "suppressedMessages": "2957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2958", "messages": "2959", "suppressedMessages": "2960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2961", "messages": "2962", "suppressedMessages": "2963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2964", "messages": "2965", "suppressedMessages": "2966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2967", "messages": "2968", "suppressedMessages": "2969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2970", "messages": "2971", "suppressedMessages": "2972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2973", "messages": "2974", "suppressedMessages": "2975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2976", "messages": "2977", "suppressedMessages": "2978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2979", "messages": "2980", "suppressedMessages": "2981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2982", "messages": "2983", "suppressedMessages": "2984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2985", "messages": "2986", "suppressedMessages": "2987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2988", "messages": "2989", "suppressedMessages": "2990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2991", "messages": "2992", "suppressedMessages": "2993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2994", "messages": "2995", "suppressedMessages": "2996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2997", "messages": "2998", "suppressedMessages": "2999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3000", "messages": "3001", "suppressedMessages": "3002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3003", "messages": "3004", "suppressedMessages": "3005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3006", "messages": "3007", "suppressedMessages": "3008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3009", "messages": "3010", "suppressedMessages": "3011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3012", "messages": "3013", "suppressedMessages": "3014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3015", "messages": "3016", "suppressedMessages": "3017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3018", "messages": "3019", "suppressedMessages": "3020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3021", "messages": "3022", "suppressedMessages": "3023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3024", "messages": "3025", "suppressedMessages": "3026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3027", "messages": "3028", "suppressedMessages": "3029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3030", "messages": "3031", "suppressedMessages": "3032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3033", "messages": "3034", "suppressedMessages": "3035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3036", "messages": "3037", "suppressedMessages": "3038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3039", "messages": "3040", "suppressedMessages": "3041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3042", "messages": "3043", "suppressedMessages": "3044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3045", "messages": "3046", "suppressedMessages": "3047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3048", "messages": "3049", "suppressedMessages": "3050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3051", "messages": "3052", "suppressedMessages": "3053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3054", "messages": "3055", "suppressedMessages": "3056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3057", "messages": "3058", "suppressedMessages": "3059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3060", "messages": "3061", "suppressedMessages": "3062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3063", "messages": "3064", "suppressedMessages": "3065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3066", "messages": "3067", "suppressedMessages": "3068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3069", "messages": "3070", "suppressedMessages": "3071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3072", "messages": "3073", "suppressedMessages": "3074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3075", "messages": "3076", "suppressedMessages": "3077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3078", "messages": "3079", "suppressedMessages": "3080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3081", "messages": "3082", "suppressedMessages": "3083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3084", "messages": "3085", "suppressedMessages": "3086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3087", "messages": "3088", "suppressedMessages": "3089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3090", "messages": "3091", "suppressedMessages": "3092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3093", "messages": "3094", "suppressedMessages": "3095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3096", "messages": "3097", "suppressedMessages": "3098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3099", "messages": "3100", "suppressedMessages": "3101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3102", "messages": "3103", "suppressedMessages": "3104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3105", "messages": "3106", "suppressedMessages": "3107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3108", "messages": "3109", "suppressedMessages": "3110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3111", "messages": "3112", "suppressedMessages": "3113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3114", "messages": "3115", "suppressedMessages": "3116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3117", "messages": "3118", "suppressedMessages": "3119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3120", "messages": "3121", "suppressedMessages": "3122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3123", "messages": "3124", "suppressedMessages": "3125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3126", "messages": "3127", "suppressedMessages": "3128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3129", "messages": "3130", "suppressedMessages": "3131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3132", "messages": "3133", "suppressedMessages": "3134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3135", "messages": "3136", "suppressedMessages": "3137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3138", "messages": "3139", "suppressedMessages": "3140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3141", "messages": "3142", "suppressedMessages": "3143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3144", "messages": "3145", "suppressedMessages": "3146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3147", "messages": "3148", "suppressedMessages": "3149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3150", "messages": "3151", "suppressedMessages": "3152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3153", "messages": "3154", "suppressedMessages": "3155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3156", "messages": "3157", "suppressedMessages": "3158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3159", "messages": "3160", "suppressedMessages": "3161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3162", "messages": "3163", "suppressedMessages": "3164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3165", "messages": "3166", "suppressedMessages": "3167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3168", "messages": "3169", "suppressedMessages": "3170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3171", "messages": "3172", "suppressedMessages": "3173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3174", "messages": "3175", "suppressedMessages": "3176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3177", "messages": "3178", "suppressedMessages": "3179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3180", "messages": "3181", "suppressedMessages": "3182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3183", "messages": "3184", "suppressedMessages": "3185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3186", "messages": "3187", "suppressedMessages": "3188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3189", "messages": "3190", "suppressedMessages": "3191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3192", "messages": "3193", "suppressedMessages": "3194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3195", "messages": "3196", "suppressedMessages": "3197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3198", "messages": "3199", "suppressedMessages": "3200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3201", "messages": "3202", "suppressedMessages": "3203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3204", "messages": "3205", "suppressedMessages": "3206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3207", "messages": "3208", "suppressedMessages": "3209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3210", "messages": "3211", "suppressedMessages": "3212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3213", "messages": "3214", "suppressedMessages": "3215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3216", "messages": "3217", "suppressedMessages": "3218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3219", "messages": "3220", "suppressedMessages": "3221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3222", "messages": "3223", "suppressedMessages": "3224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3225", "messages": "3226", "suppressedMessages": "3227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3228", "messages": "3229", "suppressedMessages": "3230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3231", "messages": "3232", "suppressedMessages": "3233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3234", "messages": "3235", "suppressedMessages": "3236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3237", "messages": "3238", "suppressedMessages": "3239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3240", "messages": "3241", "suppressedMessages": "3242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3243", "messages": "3244", "suppressedMessages": "3245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3246", "messages": "3247", "suppressedMessages": "3248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3249", "messages": "3250", "suppressedMessages": "3251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3252", "messages": "3253", "suppressedMessages": "3254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3255", "messages": "3256", "suppressedMessages": "3257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3258", "messages": "3259", "suppressedMessages": "3260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3261", "messages": "3262", "suppressedMessages": "3263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3264", "messages": "3265", "suppressedMessages": "3266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3267", "messages": "3268", "suppressedMessages": "3269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3270", "messages": "3271", "suppressedMessages": "3272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3273", "messages": "3274", "suppressedMessages": "3275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3276", "messages": "3277", "suppressedMessages": "3278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3279", "messages": "3280", "suppressedMessages": "3281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3282", "messages": "3283", "suppressedMessages": "3284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3285", "messages": "3286", "suppressedMessages": "3287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3288", "messages": "3289", "suppressedMessages": "3290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3291", "messages": "3292", "suppressedMessages": "3293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3294", "messages": "3295", "suppressedMessages": "3296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3297", "messages": "3298", "suppressedMessages": "3299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3300", "messages": "3301", "suppressedMessages": "3302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3303", "messages": "3304", "suppressedMessages": "3305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3306", "messages": "3307", "suppressedMessages": "3308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3309", "messages": "3310", "suppressedMessages": "3311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3312", "messages": "3313", "suppressedMessages": "3314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3315", "messages": "3316", "suppressedMessages": "3317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3318", "messages": "3319", "suppressedMessages": "3320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3321", "messages": "3322", "suppressedMessages": "3323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3324", "messages": "3325", "suppressedMessages": "3326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3327", "messages": "3328", "suppressedMessages": "3329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3330", "messages": "3331", "suppressedMessages": "3332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3333", "messages": "3334", "suppressedMessages": "3335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3336", "messages": "3337", "suppressedMessages": "3338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3339", "messages": "3340", "suppressedMessages": "3341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3342", "messages": "3343", "suppressedMessages": "3344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3345", "messages": "3346", "suppressedMessages": "3347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3348", "messages": "3349", "suppressedMessages": "3350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3351", "messages": "3352", "suppressedMessages": "3353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3354", "messages": "3355", "suppressedMessages": "3356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3357", "messages": "3358", "suppressedMessages": "3359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3360", "messages": "3361", "suppressedMessages": "3362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3363", "messages": "3364", "suppressedMessages": "3365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3366", "messages": "3367", "suppressedMessages": "3368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3369", "messages": "3370", "suppressedMessages": "3371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3372", "messages": "3373", "suppressedMessages": "3374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3375", "messages": "3376", "suppressedMessages": "3377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3378", "messages": "3379", "suppressedMessages": "3380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3381", "messages": "3382", "suppressedMessages": "3383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3384", "messages": "3385", "suppressedMessages": "3386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3387", "messages": "3388", "suppressedMessages": "3389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3390", "messages": "3391", "suppressedMessages": "3392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3393", "messages": "3394", "suppressedMessages": "3395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3396", "messages": "3397", "suppressedMessages": "3398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3399", "messages": "3400", "suppressedMessages": "3401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3402", "messages": "3403", "suppressedMessages": "3404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3405", "messages": "3406", "suppressedMessages": "3407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3408", "messages": "3409", "suppressedMessages": "3410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3411", "messages": "3412", "suppressedMessages": "3413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3414", "messages": "3415", "suppressedMessages": "3416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3417", "messages": "3418", "suppressedMessages": "3419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3420", "messages": "3421", "suppressedMessages": "3422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3423", "messages": "3424", "suppressedMessages": "3425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3426", "messages": "3427", "suppressedMessages": "3428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3429", "messages": "3430", "suppressedMessages": "3431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3432", "messages": "3433", "suppressedMessages": "3434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3435", "messages": "3436", "suppressedMessages": "3437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3438", "messages": "3439", "suppressedMessages": "3440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3441", "messages": "3442", "suppressedMessages": "3443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3444", "messages": "3445", "suppressedMessages": "3446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3447", "messages": "3448", "suppressedMessages": "3449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3450", "messages": "3451", "suppressedMessages": "3452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3453", "messages": "3454", "suppressedMessages": "3455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3456", "messages": "3457", "suppressedMessages": "3458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3459", "messages": "3460", "suppressedMessages": "3461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3462", "messages": "3463", "suppressedMessages": "3464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3465", "messages": "3466", "suppressedMessages": "3467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3468", "messages": "3469", "suppressedMessages": "3470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3471", "messages": "3472", "suppressedMessages": "3473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3474", "messages": "3475", "suppressedMessages": "3476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3477", "messages": "3478", "suppressedMessages": "3479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3480", "messages": "3481", "suppressedMessages": "3482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3483", "messages": "3484", "suppressedMessages": "3485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3486", "messages": "3487", "suppressedMessages": "3488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3489", "messages": "3490", "suppressedMessages": "3491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3492", "messages": "3493", "suppressedMessages": "3494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3495", "messages": "3496", "suppressedMessages": "3497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3498", "messages": "3499", "suppressedMessages": "3500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3501", "messages": "3502", "suppressedMessages": "3503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3504", "messages": "3505", "suppressedMessages": "3506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3507", "messages": "3508", "suppressedMessages": "3509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3510", "messages": "3511", "suppressedMessages": "3512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3513", "messages": "3514", "suppressedMessages": "3515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3516", "messages": "3517", "suppressedMessages": "3518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3519", "messages": "3520", "suppressedMessages": "3521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3522", "messages": "3523", "suppressedMessages": "3524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3525", "messages": "3526", "suppressedMessages": "3527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3528", "messages": "3529", "suppressedMessages": "3530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3531", "messages": "3532", "suppressedMessages": "3533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3534", "messages": "3535", "suppressedMessages": "3536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3537", "messages": "3538", "suppressedMessages": "3539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3540", "messages": "3541", "suppressedMessages": "3542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3543", "messages": "3544", "suppressedMessages": "3545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3546", "messages": "3547", "suppressedMessages": "3548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3549", "messages": "3550", "suppressedMessages": "3551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3552", "messages": "3553", "suppressedMessages": "3554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3555", "messages": "3556", "suppressedMessages": "3557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3558", "messages": "3559", "suppressedMessages": "3560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3561", "messages": "3562", "suppressedMessages": "3563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3564", "messages": "3565", "suppressedMessages": "3566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3567", "messages": "3568", "suppressedMessages": "3569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3570", "messages": "3571", "suppressedMessages": "3572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3573", "messages": "3574", "suppressedMessages": "3575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3576", "messages": "3577", "suppressedMessages": "3578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3579", "messages": "3580", "suppressedMessages": "3581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3582", "messages": "3583", "suppressedMessages": "3584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3585", "messages": "3586", "suppressedMessages": "3587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3588", "messages": "3589", "suppressedMessages": "3590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3591", "messages": "3592", "suppressedMessages": "3593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3594", "messages": "3595", "suppressedMessages": "3596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3597", "messages": "3598", "suppressedMessages": "3599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3600", "messages": "3601", "suppressedMessages": "3602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3603", "messages": "3604", "suppressedMessages": "3605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3606", "messages": "3607", "suppressedMessages": "3608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3609", "messages": "3610", "suppressedMessages": "3611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3612", "messages": "3613", "suppressedMessages": "3614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3615", "messages": "3616", "suppressedMessages": "3617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3618", "messages": "3619", "suppressedMessages": "3620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3621", "messages": "3622", "suppressedMessages": "3623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3624", "messages": "3625", "suppressedMessages": "3626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3627", "messages": "3628", "suppressedMessages": "3629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3630", "messages": "3631", "suppressedMessages": "3632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3633", "messages": "3634", "suppressedMessages": "3635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3636", "messages": "3637", "suppressedMessages": "3638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3639", "messages": "3640", "suppressedMessages": "3641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3642", "messages": "3643", "suppressedMessages": "3644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3645", "messages": "3646", "suppressedMessages": "3647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3648", "messages": "3649", "suppressedMessages": "3650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3651", "messages": "3652", "suppressedMessages": "3653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3654", "messages": "3655", "suppressedMessages": "3656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3657", "messages": "3658", "suppressedMessages": "3659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3660", "messages": "3661", "suppressedMessages": "3662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3663", "messages": "3664", "suppressedMessages": "3665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3666", "messages": "3667", "suppressedMessages": "3668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3669", "messages": "3670", "suppressedMessages": "3671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3672", "messages": "3673", "suppressedMessages": "3674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3675", "messages": "3676", "suppressedMessages": "3677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3678", "messages": "3679", "suppressedMessages": "3680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3681", "messages": "3682", "suppressedMessages": "3683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3684", "messages": "3685", "suppressedMessages": "3686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3687", "messages": "3688", "suppressedMessages": "3689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3690", "messages": "3691", "suppressedMessages": "3692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3693", "messages": "3694", "suppressedMessages": "3695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3696", "messages": "3697", "suppressedMessages": "3698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3699", "messages": "3700", "suppressedMessages": "3701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3702", "messages": "3703", "suppressedMessages": "3704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3705", "messages": "3706", "suppressedMessages": "3707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3708", "messages": "3709", "suppressedMessages": "3710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3711", "messages": "3712", "suppressedMessages": "3713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3714", "messages": "3715", "suppressedMessages": "3716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3717", "messages": "3718", "suppressedMessages": "3719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3720", "messages": "3721", "suppressedMessages": "3722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3723", "messages": "3724", "suppressedMessages": "3725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3726", "messages": "3727", "suppressedMessages": "3728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3729", "messages": "3730", "suppressedMessages": "3731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3732", "messages": "3733", "suppressedMessages": "3734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3735", "messages": "3736", "suppressedMessages": "3737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3738", "messages": "3739", "suppressedMessages": "3740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3741", "messages": "3742", "suppressedMessages": "3743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3744", "messages": "3745", "suppressedMessages": "3746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3747", "messages": "3748", "suppressedMessages": "3749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3750", "messages": "3751", "suppressedMessages": "3752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3753", "messages": "3754", "suppressedMessages": "3755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3756", "messages": "3757", "suppressedMessages": "3758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3759", "messages": "3760", "suppressedMessages": "3761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3762", "messages": "3763", "suppressedMessages": "3764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3765", "messages": "3766", "suppressedMessages": "3767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3768", "messages": "3769", "suppressedMessages": "3770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3771", "messages": "3772", "suppressedMessages": "3773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3774", "messages": "3775", "suppressedMessages": "3776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3777", "messages": "3778", "suppressedMessages": "3779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3780", "messages": "3781", "suppressedMessages": "3782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3783", "messages": "3784", "suppressedMessages": "3785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3786", "messages": "3787", "suppressedMessages": "3788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3789", "messages": "3790", "suppressedMessages": "3791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3792", "messages": "3793", "suppressedMessages": "3794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3795", "messages": "3796", "suppressedMessages": "3797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3798", "messages": "3799", "suppressedMessages": "3800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3801", "messages": "3802", "suppressedMessages": "3803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3804", "messages": "3805", "suppressedMessages": "3806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3807", "messages": "3808", "suppressedMessages": "3809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3810", "messages": "3811", "suppressedMessages": "3812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3813", "messages": "3814", "suppressedMessages": "3815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3816", "messages": "3817", "suppressedMessages": "3818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3819", "messages": "3820", "suppressedMessages": "3821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3822", "messages": "3823", "suppressedMessages": "3824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3825", "messages": "3826", "suppressedMessages": "3827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3828", "messages": "3829", "suppressedMessages": "3830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3831", "messages": "3832", "suppressedMessages": "3833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3834", "messages": "3835", "suppressedMessages": "3836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3837", "messages": "3838", "suppressedMessages": "3839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3840", "messages": "3841", "suppressedMessages": "3842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3843", "messages": "3844", "suppressedMessages": "3845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3846", "messages": "3847", "suppressedMessages": "3848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3849", "messages": "3850", "suppressedMessages": "3851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3852", "messages": "3853", "suppressedMessages": "3854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3855", "messages": "3856", "suppressedMessages": "3857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3858", "messages": "3859", "suppressedMessages": "3860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3861", "messages": "3862", "suppressedMessages": "3863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3864", "messages": "3865", "suppressedMessages": "3866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3867", "messages": "3868", "suppressedMessages": "3869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3870", "messages": "3871", "suppressedMessages": "3872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3873", "messages": "3874", "suppressedMessages": "3875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3876", "messages": "3877", "suppressedMessages": "3878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3879", "messages": "3880", "suppressedMessages": "3881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3882", "messages": "3883", "suppressedMessages": "3884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3885", "messages": "3886", "suppressedMessages": "3887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3888", "messages": "3889", "suppressedMessages": "3890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3891", "messages": "3892", "suppressedMessages": "3893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3894", "messages": "3895", "suppressedMessages": "3896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3897", "messages": "3898", "suppressedMessages": "3899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3900", "messages": "3901", "suppressedMessages": "3902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3903", "messages": "3904", "suppressedMessages": "3905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3906", "messages": "3907", "suppressedMessages": "3908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3909", "messages": "3910", "suppressedMessages": "3911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3912", "messages": "3913", "suppressedMessages": "3914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3915", "messages": "3916", "suppressedMessages": "3917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3918", "messages": "3919", "suppressedMessages": "3920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3921", "messages": "3922", "suppressedMessages": "3923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3924", "messages": "3925", "suppressedMessages": "3926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3927", "messages": "3928", "suppressedMessages": "3929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3930", "messages": "3931", "suppressedMessages": "3932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3933", "messages": "3934", "suppressedMessages": "3935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3936", "messages": "3937", "suppressedMessages": "3938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3939", "messages": "3940", "suppressedMessages": "3941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3942", "messages": "3943", "suppressedMessages": "3944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3945", "messages": "3946", "suppressedMessages": "3947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3948", "messages": "3949", "suppressedMessages": "3950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3951", "messages": "3952", "suppressedMessages": "3953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3954", "messages": "3955", "suppressedMessages": "3956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3957", "messages": "3958", "suppressedMessages": "3959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3960", "messages": "3961", "suppressedMessages": "3962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3963", "messages": "3964", "suppressedMessages": "3965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3966", "messages": "3967", "suppressedMessages": "3968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3969", "messages": "3970", "suppressedMessages": "3971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3972", "messages": "3973", "suppressedMessages": "3974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3975", "messages": "3976", "suppressedMessages": "3977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3978", "messages": "3979", "suppressedMessages": "3980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3981", "messages": "3982", "suppressedMessages": "3983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3984", "messages": "3985", "suppressedMessages": "3986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3987", "messages": "3988", "suppressedMessages": "3989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3990", "messages": "3991", "suppressedMessages": "3992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3993", "messages": "3994", "suppressedMessages": "3995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3996", "messages": "3997", "suppressedMessages": "3998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3999", "messages": "4000", "suppressedMessages": "4001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4002", "messages": "4003", "suppressedMessages": "4004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4005", "messages": "4006", "suppressedMessages": "4007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4008", "messages": "4009", "suppressedMessages": "4010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4011", "messages": "4012", "suppressedMessages": "4013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4014", "messages": "4015", "suppressedMessages": "4016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4017", "messages": "4018", "suppressedMessages": "4019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4020", "messages": "4021", "suppressedMessages": "4022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4023", "messages": "4024", "suppressedMessages": "4025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4026", "messages": "4027", "suppressedMessages": "4028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4029", "messages": "4030", "suppressedMessages": "4031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4032", "messages": "4033", "suppressedMessages": "4034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4035", "messages": "4036", "suppressedMessages": "4037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4038", "messages": "4039", "suppressedMessages": "4040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4041", "messages": "4042", "suppressedMessages": "4043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4044", "messages": "4045", "suppressedMessages": "4046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4047", "messages": "4048", "suppressedMessages": "4049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4050", "messages": "4051", "suppressedMessages": "4052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4053", "messages": "4054", "suppressedMessages": "4055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4056", "messages": "4057", "suppressedMessages": "4058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4059", "messages": "4060", "suppressedMessages": "4061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4062", "messages": "4063", "suppressedMessages": "4064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4065", "messages": "4066", "suppressedMessages": "4067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4068", "messages": "4069", "suppressedMessages": "4070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4071", "messages": "4072", "suppressedMessages": "4073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4074", "messages": "4075", "suppressedMessages": "4076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4077", "messages": "4078", "suppressedMessages": "4079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4080", "messages": "4081", "suppressedMessages": "4082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4083", "messages": "4084", "suppressedMessages": "4085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4086", "messages": "4087", "suppressedMessages": "4088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4089", "messages": "4090", "suppressedMessages": "4091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4092", "messages": "4093", "suppressedMessages": "4094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4095", "messages": "4096", "suppressedMessages": "4097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4098", "messages": "4099", "suppressedMessages": "4100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4101", "messages": "4102", "suppressedMessages": "4103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4104", "messages": "4105", "suppressedMessages": "4106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4107", "messages": "4108", "suppressedMessages": "4109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4110", "messages": "4111", "suppressedMessages": "4112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4113", "messages": "4114", "suppressedMessages": "4115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4116", "messages": "4117", "suppressedMessages": "4118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4119", "messages": "4120", "suppressedMessages": "4121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4122", "messages": "4123", "suppressedMessages": "4124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4125", "messages": "4126", "suppressedMessages": "4127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4128", "messages": "4129", "suppressedMessages": "4130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4131", "messages": "4132", "suppressedMessages": "4133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4134", "messages": "4135", "suppressedMessages": "4136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4137", "messages": "4138", "suppressedMessages": "4139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4140", "messages": "4141", "suppressedMessages": "4142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4143", "messages": "4144", "suppressedMessages": "4145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4146", "messages": "4147", "suppressedMessages": "4148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4149", "messages": "4150", "suppressedMessages": "4151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4152", "messages": "4153", "suppressedMessages": "4154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4155", "messages": "4156", "suppressedMessages": "4157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4158", "messages": "4159", "suppressedMessages": "4160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4161", "messages": "4162", "suppressedMessages": "4163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4164", "messages": "4165", "suppressedMessages": "4166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4167", "messages": "4168", "suppressedMessages": "4169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4170", "messages": "4171", "suppressedMessages": "4172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4173", "messages": "4174", "suppressedMessages": "4175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4176", "messages": "4177", "suppressedMessages": "4178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4179", "messages": "4180", "suppressedMessages": "4181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4182", "messages": "4183", "suppressedMessages": "4184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4185", "messages": "4186", "suppressedMessages": "4187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4188", "messages": "4189", "suppressedMessages": "4190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4191", "messages": "4192", "suppressedMessages": "4193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4194", "messages": "4195", "suppressedMessages": "4196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4197", "messages": "4198", "suppressedMessages": "4199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4200", "messages": "4201", "suppressedMessages": "4202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4203", "messages": "4204", "suppressedMessages": "4205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4206", "messages": "4207", "suppressedMessages": "4208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4209", "messages": "4210", "suppressedMessages": "4211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4212", "messages": "4213", "suppressedMessages": "4214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4215", "messages": "4216", "suppressedMessages": "4217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4218", "messages": "4219", "suppressedMessages": "4220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4221", "messages": "4222", "suppressedMessages": "4223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4224", "messages": "4225", "suppressedMessages": "4226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4227", "messages": "4228", "suppressedMessages": "4229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4230", "messages": "4231", "suppressedMessages": "4232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4233", "messages": "4234", "suppressedMessages": "4235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4236", "messages": "4237", "suppressedMessages": "4238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4239", "messages": "4240", "suppressedMessages": "4241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4242", "messages": "4243", "suppressedMessages": "4244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4245", "messages": "4246", "suppressedMessages": "4247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4248", "messages": "4249", "suppressedMessages": "4250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4251", "messages": "4252", "suppressedMessages": "4253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4254", "messages": "4255", "suppressedMessages": "4256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4257", "messages": "4258", "suppressedMessages": "4259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4260", "messages": "4261", "suppressedMessages": "4262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4263", "messages": "4264", "suppressedMessages": "4265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4266", "messages": "4267", "suppressedMessages": "4268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4269", "messages": "4270", "suppressedMessages": "4271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4272", "messages": "4273", "suppressedMessages": "4274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4275", "messages": "4276", "suppressedMessages": "4277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4278", "messages": "4279", "suppressedMessages": "4280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4281", "messages": "4282", "suppressedMessages": "4283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4284", "messages": "4285", "suppressedMessages": "4286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4287", "messages": "4288", "suppressedMessages": "4289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4290", "messages": "4291", "suppressedMessages": "4292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4293", "messages": "4294", "suppressedMessages": "4295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4296", "messages": "4297", "suppressedMessages": "4298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4299", "messages": "4300", "suppressedMessages": "4301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4302", "messages": "4303", "suppressedMessages": "4304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4305", "messages": "4306", "suppressedMessages": "4307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4308", "messages": "4309", "suppressedMessages": "4310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4311", "messages": "4312", "suppressedMessages": "4313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4314", "messages": "4315", "suppressedMessages": "4316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4317", "messages": "4318", "suppressedMessages": "4319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4320", "messages": "4321", "suppressedMessages": "4322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4323", "messages": "4324", "suppressedMessages": "4325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4326", "messages": "4327", "suppressedMessages": "4328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4329", "messages": "4330", "suppressedMessages": "4331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4332", "messages": "4333", "suppressedMessages": "4334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4335", "messages": "4336", "suppressedMessages": "4337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4338", "messages": "4339", "suppressedMessages": "4340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4341", "messages": "4342", "suppressedMessages": "4343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4344", "messages": "4345", "suppressedMessages": "4346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4347", "messages": "4348", "suppressedMessages": "4349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4350", "messages": "4351", "suppressedMessages": "4352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4353", "messages": "4354", "suppressedMessages": "4355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4356", "messages": "4357", "suppressedMessages": "4358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4359", "messages": "4360", "suppressedMessages": "4361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4362", "messages": "4363", "suppressedMessages": "4364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4365", "messages": "4366", "suppressedMessages": "4367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4368", "messages": "4369", "suppressedMessages": "4370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4371", "messages": "4372", "suppressedMessages": "4373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4374", "messages": "4375", "suppressedMessages": "4376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4377", "messages": "4378", "suppressedMessages": "4379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4380", "messages": "4381", "suppressedMessages": "4382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4383", "messages": "4384", "suppressedMessages": "4385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4386", "messages": "4387", "suppressedMessages": "4388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4389", "messages": "4390", "suppressedMessages": "4391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4392", "messages": "4393", "suppressedMessages": "4394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4395", "messages": "4396", "suppressedMessages": "4397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4398", "messages": "4399", "suppressedMessages": "4400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4401", "messages": "4402", "suppressedMessages": "4403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4404", "messages": "4405", "suppressedMessages": "4406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4407", "messages": "4408", "suppressedMessages": "4409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4410", "messages": "4411", "suppressedMessages": "4412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4413", "messages": "4414", "suppressedMessages": "4415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4416", "messages": "4417", "suppressedMessages": "4418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4419", "messages": "4420", "suppressedMessages": "4421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4422", "messages": "4423", "suppressedMessages": "4424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4425", "messages": "4426", "suppressedMessages": "4427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4428", "messages": "4429", "suppressedMessages": "4430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4431", "messages": "4432", "suppressedMessages": "4433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4434", "messages": "4435", "suppressedMessages": "4436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\DailyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedAnalyticsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedChartCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedEngagementMetricsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedVisitMetricsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\HourlyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\MonthlyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\PremiumFeatureLock.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customAdUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customHeaderUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeSpecificHeaderUpload.ts", ["4437"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts", ["4438"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomAdUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomBrandingSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ThemeSpecificHeaderUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedSubscriptionStatus.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedSubscriptionStatus.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts", ["4439", "4440"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BillingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BusinessPlanSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\ConfirmationDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedActionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedCurrentPlanSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedGlowButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedInvoiceHistoryCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanPageWithManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanSelectionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedSubscriptionDetailsCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedTrialAlert.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\FirstTimePaidPlanDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PaymentMethodLimitationsDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanPageContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\RealtimePlanPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SimplifiedPlanActionDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\DialogComponents.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EligiblePaymentMethodsCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedPaymentHistoryCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedSubscriptionActionCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\hooks.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernCancellationDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernRefundDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernSubscriptionStatusCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernTabs.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionProcessingIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionTabsToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusBadge.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionTabContent.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialManagement.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialSubscriptionWarningDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\UpiPaymentMethodWarning.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\WebhookWaitingIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\context\\SubscriptionProcessingContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionHandler.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionLogic.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useUrlParameterHandler.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\PlanPageWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts", ["4441", "4442", "4443", "4444", "4445", "4446"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts", ["4447", "4448"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts", ["4449", "4450", "4451"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts", ["4452"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx", ["4453", "4454", "4455", "4456"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx", ["4457"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx", ["4458", "4459", "4460"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx", ["4461", "4462"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx", ["4463", "4464"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx", ["4465"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx", ["4466"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\actions.ts", ["4467", "4468"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts", ["4469", "4470"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts", ["4471", "4472", "4473", "4474", "4475", "4476"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts", ["4477", "4478", "4479", "4480"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts", ["4481", "4482", "4483", "4484"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts", ["4485", "4486"], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx", ["4487"], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\BillingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PricingSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\FeatureComparisonTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx", [], ["4488"], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\PlanComparisonSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\AnimatedTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\PricingAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCards.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCTA.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingFAQ.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingHero.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\FeatureComparisonTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\EnhancedPricingPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\AnalyticsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts", ["4489", "4490", "4491", "4492"], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\PlanSelectionStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\admin\\fix-subscription-inconsistency\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts", ["4493", "4494"], [], "C:\\web-app\\dukancard\\app\\api\\health\\subscription\\route.ts", ["4495", "4496"], [], "C:\\web-app\\dukancard\\app\\api\\razorpay\\key\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscription\\[id]\\payments\\route.ts", ["4497", "4498"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\centralized\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\list\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\my\\route.ts", ["4499", "4500"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\cancel\\route.ts", ["4501", "4502"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\invoices\\route.ts", ["4503", "4504"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pause\\route.ts", ["4505", "4506", "4507", "4508"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\[paymentId]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pending-update\\route.ts", ["4509", "4510", "4511", "4512", "4513", "4514"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\databaseOperations.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\errorHandlers.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\paymentHelpers.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\razorpayApi.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\validators.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\resume\\route.ts", ["4515", "4516"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\route.ts", ["4517", "4518"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\scheduled-changes\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch-with-new-payment\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\update\\route.ts", ["4519", "4520", "4521"], [], "C:\\web-app\\dukancard\\app\\api\\test\\subscription-flow\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\test\\subscription-scenarios\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\create\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\delete\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\get\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\list\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\retry\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\update\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\auth\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Footer.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Header.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx", [], ["4522"], "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PricingCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PricingCardContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx", [], ["4523"], "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx", [], [], "C:\\web-app\\dukancard\\app\\context\\PaymentMethodLimitationsContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts", ["4524", "4525", "4526"], ["4527"], "C:\\web-app\\dukancard\\app\\locality\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx", [], ["4528"], "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\products\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\robots.ts", [], [], "C:\\web-app\\dukancard\\app\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts", ["4529", "4530"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\VisitTracker.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx", ["4531"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx", ["4532", "4533"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts", ["4534"], ["4535"], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx", ["4536", "4537", "4538", "4539"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx", ["4540"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx", [], [], "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx", ["4541"], [], "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx", ["4542", "4543"], [], "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx", ["4544", "4545", "4546", "4547"], [], "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\badge.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\card.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\chart.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\command.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\form.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\label.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\popover.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\progress.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\select.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\separator.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\slider.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\switch.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\table.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\toast.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts", [], [], "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx", [], [], "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts", ["4548", "4549", "4550"], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts", [], ["4551", "4552"], "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts", ["4553"], [], "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts", ["4554", "4555", "4556", "4557", "4558", "4559", "4560", "4561"], [], "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts", ["4562", "4563", "4564", "4565", "4566"], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts", ["4567", "4568", "4569"], [], "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts", ["4570", "4571", "4572", "4573"], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\activateTrial.ts", ["4574", "4575", "4576", "4577", "4578", "4579", "4580", "4581"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\centralized.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\confirm.ts", ["4582", "4583", "4584", "4585", "4586", "4587", "4588", "4589", "4590", "4591"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\cancel.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\change.ts", ["4592", "4593", "4594"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\manage.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\schedule.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\switch.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\payment.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\status.ts", ["4595", "4596", "4597", "4598", "4599", "4600", "4601"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\user\\getUserAndProfile.ts", [], [], "C:\\web-app\\dukancard\\lib\\api\\response.ts", [], [], "C:\\web-app\\dukancard\\lib\\cardDownloader.ts", [], [], "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\categories.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\plans.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\states.ts", [], [], "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts", [], [], "C:\\web-app\\dukancard\\lib\\csrf.ts", [], [], "C:\\web-app\\dukancard\\lib\\errorHandling.ts", [], ["4602", "4603"], "C:\\web-app\\dukancard\\lib\\PricingPlans.ts", [], [], "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts", [], [], "C:\\web-app\\dukancard\\lib\\rateLimiter.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\razorpayClient.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\get.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\update.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\invoice.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\getPayment.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\plan.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\cancel.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\get.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\scheduled.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\update.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\webhook.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\types\\api.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\auth.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\loadRazorpaySDK.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\errorTracking.ts", ["4604", "4605", "4606"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\eventManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\subscriptionManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\eventOrderValidator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\stateTransitionValidator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\mainHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\moreSubscriptionHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\paymentHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\refundHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-constants.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-db-updater.ts", ["4607", "4608", "4609"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-manager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-validator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionActivated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionAuthenticated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCancelled.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCharged.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCompleted.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionExpired.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionHalted.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionPending.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionUpdated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\transactionUtils.ts", ["4610", "4611", "4612", "4613", "4614", "4615", "4616", "4617", "4618", "4619"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhook-utils.ts", [], ["4620"], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhookProcessor.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handleWebhook.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\idempotency.ts", ["4621"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\monitoring.ts", ["4622", "4623", "4624"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\validation.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts", [], ["4625"], "C:\\web-app\\dukancard\\lib\\services\\socialService.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\subscription.ts", ["4626"], [], "C:\\web-app\\dukancard\\lib\\site-config.ts", [], [], "C:\\web-app\\dukancard\\lib\\siteContent.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\edge-validation.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowTester.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\database.ts", ["4627"], [], "C:\\web-app\\dukancard\\lib\\testing\\mswHandlers.ts", [], ["4628", "4629"], "C:\\web-app\\dukancard\\lib\\testing\\SubscriptionScenarioTester.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\testDataFactories.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\testUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\activities.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\api.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\blog.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\subscription.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\seo.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts", ["4630", "4631"], [], "C:\\web-app\\dukancard\\lib\\utils.ts", [], [], {"ruleId": "4632", "severity": 1, "message": "4633", "line": 126, "column": 14, "nodeType": "4634", "messageId": "4635", "endLine": 126, "endColumn": 17, "suggestions": "4636"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 95, "column": 50, "nodeType": "4634", "messageId": "4635", "endLine": 95, "endColumn": 53, "suggestions": "4637"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 144, "column": 44, "nodeType": "4634", "messageId": "4635", "endLine": 144, "endColumn": 47, "suggestions": "4638"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 285, "column": 44, "nodeType": "4634", "messageId": "4635", "endLine": 285, "endColumn": 47, "suggestions": "4639"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 137, "column": 41, "nodeType": "4634", "messageId": "4635", "endLine": 137, "endColumn": 44, "suggestions": "4640"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 141, "column": 21, "nodeType": "4634", "messageId": "4635", "endLine": 141, "endColumn": 24, "suggestions": "4641"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 262, "column": 65, "nodeType": "4634", "messageId": "4635", "endLine": 262, "endColumn": 68, "suggestions": "4642"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 266, "column": 21, "nodeType": "4634", "messageId": "4635", "endLine": 266, "endColumn": 24, "suggestions": "4643"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 267, "column": 41, "nodeType": "4634", "messageId": "4635", "endLine": 267, "endColumn": 44, "suggestions": "4644"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 267, "column": 95, "nodeType": "4634", "messageId": "4635", "endLine": 267, "endColumn": 98, "suggestions": "4645"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 66, "column": 35, "nodeType": "4634", "messageId": "4635", "endLine": 66, "endColumn": 38, "suggestions": "4646"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 67, "column": 35, "nodeType": "4634", "messageId": "4635", "endLine": 67, "endColumn": 38, "suggestions": "4647"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 69, "column": 54, "nodeType": "4634", "messageId": "4635", "endLine": 69, "endColumn": 57, "suggestions": "4648"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 110, "column": 52, "nodeType": "4634", "messageId": "4635", "endLine": 110, "endColumn": 55, "suggestions": "4649"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 152, "column": 60, "nodeType": "4634", "messageId": "4635", "endLine": 152, "endColumn": 63, "suggestions": "4650"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 114, "column": 20, "nodeType": "4634", "messageId": "4635", "endLine": 114, "endColumn": 23, "suggestions": "4651"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 142, "column": 33, "nodeType": "4634", "messageId": "4635", "endLine": 142, "endColumn": 36, "suggestions": "4652"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 142, "column": 65, "nodeType": "4634", "messageId": "4635", "endLine": 142, "endColumn": 68, "suggestions": "4653"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 143, "column": 31, "nodeType": "4634", "messageId": "4635", "endLine": 143, "endColumn": 34, "suggestions": "4654"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 143, "column": 63, "nodeType": "4634", "messageId": "4635", "endLine": 143, "endColumn": 66, "suggestions": "4655"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 424, "column": 63, "nodeType": "4634", "messageId": "4635", "endLine": 424, "endColumn": 66, "suggestions": "4656"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 291, "column": 70, "nodeType": "4634", "messageId": "4635", "endLine": 291, "endColumn": 73, "suggestions": "4657"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 295, "column": 31, "nodeType": "4634", "messageId": "4635", "endLine": 295, "endColumn": 34, "suggestions": "4658"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 308, "column": 27, "nodeType": "4634", "messageId": "4635", "endLine": 308, "endColumn": 30, "suggestions": "4659"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 70, "column": 37, "nodeType": "4634", "messageId": "4635", "endLine": 70, "endColumn": 40, "suggestions": "4660"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 106, "column": 55, "nodeType": "4634", "messageId": "4635", "endLine": 106, "endColumn": 58, "suggestions": "4661"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 292, "column": 65, "nodeType": "4634", "messageId": "4635", "endLine": 292, "endColumn": 68, "suggestions": "4662"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 429, "column": 79, "nodeType": "4634", "messageId": "4635", "endLine": 429, "endColumn": 82, "suggestions": "4663"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 191, "column": 54, "nodeType": "4634", "messageId": "4635", "endLine": 191, "endColumn": 57, "suggestions": "4664"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 115, "column": 73, "nodeType": "4634", "messageId": "4635", "endLine": 115, "endColumn": 76, "suggestions": "4665"}, {"ruleId": "4666", "severity": 1, "message": "4667", "line": 3, "column": 26, "nodeType": "4668", "messageId": "4669", "endLine": 3, "endColumn": 39, "suggestions": "4670"}, {"ruleId": "4671", "severity": 1, "message": "4667", "line": 3, "column": 26, "nodeType": null, "messageId": "4669", "endLine": 3, "endColumn": 39}, {"ruleId": "4666", "severity": 1, "message": "4672", "line": 512, "column": 13, "nodeType": "4668", "messageId": "4669", "endLine": 512, "endColumn": 25}, {"ruleId": "4671", "severity": 1, "message": "4672", "line": 512, "column": 13, "nodeType": null, "messageId": "4669", "endLine": 512, "endColumn": 25}, {"ruleId": "4666", "severity": 1, "message": "4673", "line": 5, "column": 3, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 22, "suggestions": "4674"}, {"ruleId": "4671", "severity": 1, "message": "4673", "line": 5, "column": 3, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 22}, {"ruleId": "4666", "severity": 1, "message": "4675", "line": 6, "column": 3, "nodeType": "4668", "messageId": "4669", "endLine": 6, "endColumn": 18, "suggestions": "4676"}, {"ruleId": "4671", "severity": 1, "message": "4675", "line": 6, "column": 3, "nodeType": null, "messageId": "4669", "endLine": 6, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4677", "line": 7, "column": 3, "nodeType": "4668", "messageId": "4669", "endLine": 7, "endColumn": 26, "suggestions": "4678"}, {"ruleId": "4671", "severity": 1, "message": "4677", "line": 7, "column": 3, "nodeType": null, "messageId": "4669", "endLine": 7, "endColumn": 26}, {"ruleId": "4666", "severity": 1, "message": "4679", "line": 4, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 4, "endColumn": 24, "suggestions": "4680"}, {"ruleId": "4671", "severity": 1, "message": "4679", "line": 4, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 4, "endColumn": 24}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4682"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4679", "line": 4, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 4, "endColumn": 24, "suggestions": "4683"}, {"ruleId": "4671", "severity": 1, "message": "4679", "line": 4, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 4, "endColumn": 24}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4684"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 89, "column": 59, "nodeType": "4634", "messageId": "4635", "endLine": 89, "endColumn": 62, "suggestions": "4685"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 90, "column": 49, "nodeType": "4634", "messageId": "4635", "endLine": 90, "endColumn": 52, "suggestions": "4686"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 79, "column": 37, "nodeType": "4634", "messageId": "4635", "endLine": 79, "endColumn": 40, "suggestions": "4687"}, {"ruleId": "4688", "severity": 1, "message": "4689", "line": 282, "column": 6, "nodeType": "4690", "endLine": 282, "endColumn": 8, "suggestions": "4691", "suppressions": "4692"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 397, "column": 19, "nodeType": "4634", "messageId": "4635", "endLine": 397, "endColumn": 22, "suggestions": "4693"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 398, "column": 67, "nodeType": "4634", "messageId": "4635", "endLine": 398, "endColumn": 70, "suggestions": "4694"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 399, "column": 20, "nodeType": "4634", "messageId": "4635", "endLine": 399, "endColumn": 23, "suggestions": "4695"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 405, "column": 32, "nodeType": "4634", "messageId": "4635", "endLine": 405, "endColumn": 35, "suggestions": "4696"}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 4, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 4, "endColumn": 18, "suggestions": "4697"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 4, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 4, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 4, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 4, "endColumn": 18, "suggestions": "4698"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 4, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 4, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4699"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4700"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4701"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4702"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4703"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 168, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 168, "endColumn": 45, "suggestions": "4704"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 169, "column": 113, "nodeType": "4634", "messageId": "4635", "endLine": 169, "endColumn": 116, "suggestions": "4705"}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4706"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4707", "line": 59, "column": 11, "nodeType": "4668", "messageId": "4669", "endLine": 59, "endColumn": 15, "suggestions": "4708"}, {"ruleId": "4671", "severity": 1, "message": "4707", "line": 59, "column": 11, "nodeType": null, "messageId": "4669", "endLine": 59, "endColumn": 15}, {"ruleId": "4666", "severity": 1, "message": "4709", "line": 60, "column": 11, "nodeType": "4668", "messageId": "4669", "endLine": 60, "endColumn": 16, "suggestions": "4710"}, {"ruleId": "4671", "severity": 1, "message": "4709", "line": 60, "column": 11, "nodeType": null, "messageId": "4669", "endLine": 60, "endColumn": 16}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 166, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 166, "endColumn": 45, "suggestions": "4711"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 167, "column": 113, "nodeType": "4634", "messageId": "4635", "endLine": 167, "endColumn": 116, "suggestions": "4712"}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4713"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 246, "column": 44, "nodeType": "4634", "messageId": "4635", "endLine": 246, "endColumn": 47, "suggestions": "4714"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 254, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 254, "endColumn": 45, "suggestions": "4715"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 255, "column": 113, "nodeType": "4634", "messageId": "4635", "endLine": 255, "endColumn": 116, "suggestions": "4716"}, {"ruleId": "4717", "severity": 1, "message": "4718", "line": 25, "column": 9, "nodeType": "4719", "endLine": 31, "endColumn": 11, "suppressions": "4720"}, {"ruleId": "4688", "severity": 1, "message": "4721", "line": 53, "column": 6, "nodeType": "4690", "endLine": 53, "endColumn": 46, "suggestions": "4722", "suppressions": "4723"}, {"ruleId": "4666", "severity": 1, "message": "4724", "line": 132, "column": 15, "nodeType": "4668", "messageId": "4669", "endLine": 132, "endColumn": 33, "suggestions": "4725"}, {"ruleId": "4671", "severity": 1, "message": "4724", "line": 132, "column": 15, "nodeType": null, "messageId": "4669", "endLine": 132, "endColumn": 33}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 149, "column": 54, "nodeType": "4634", "messageId": "4635", "endLine": 149, "endColumn": 57, "suggestions": "4726"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 158, "column": 57, "nodeType": "4634", "messageId": "4635", "endLine": 158, "endColumn": 60, "suggestions": "4727", "suppressions": "4728"}, {"ruleId": "4688", "severity": 1, "message": "4729", "line": 172, "column": 6, "nodeType": "4690", "endLine": 172, "endColumn": 8, "suggestions": "4730", "suppressions": "4731"}, {"ruleId": "4666", "severity": 1, "message": "4732", "line": 8, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 8, "endColumn": 16, "suggestions": "4733"}, {"ruleId": "4671", "severity": 1, "message": "4732", "line": 8, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 8, "endColumn": 16}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 92, "column": 35, "nodeType": "4634", "messageId": "4635", "endLine": 92, "endColumn": 38, "suggestions": "4734"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 289, "column": 57, "nodeType": "4634", "messageId": "4635", "endLine": 289, "endColumn": 60, "suggestions": "4735"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 290, "column": 47, "nodeType": "4634", "messageId": "4635", "endLine": 290, "endColumn": 50, "suggestions": "4736"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 244, "column": 17, "nodeType": "4634", "messageId": "4635", "endLine": 244, "endColumn": 20, "suggestions": "4737"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 441, "column": 53, "nodeType": "4634", "messageId": "4635", "endLine": 441, "endColumn": 56, "suggestions": "4738", "suppressions": "4739"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 67, "column": 79, "nodeType": "4634", "messageId": "4635", "endLine": 67, "endColumn": 82, "suggestions": "4740"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 117, "column": 81, "nodeType": "4634", "messageId": "4635", "endLine": 117, "endColumn": 84, "suggestions": "4741"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 156, "column": 88, "nodeType": "4634", "messageId": "4635", "endLine": 156, "endColumn": 91, "suggestions": "4742"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 169, "column": 69, "nodeType": "4634", "messageId": "4635", "endLine": 169, "endColumn": 72, "suggestions": "4743"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 205, "column": 55, "nodeType": "4634", "messageId": "4635", "endLine": 205, "endColumn": 58, "suggestions": "4744"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 45, "column": 21, "nodeType": "4634", "messageId": "4635", "endLine": 45, "endColumn": 24, "suggestions": "4745"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 78, "column": 21, "nodeType": "4634", "messageId": "4635", "endLine": 78, "endColumn": 24, "suggestions": "4746"}, {"ruleId": "4688", "severity": 1, "message": "4747", "line": 115, "column": 6, "nodeType": "4690", "endLine": 115, "endColumn": 8, "suggestions": "4748"}, {"ruleId": "4666", "severity": 1, "message": "4749", "line": 30, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 30, "endColumn": 25, "suggestions": "4750"}, {"ruleId": "4671", "severity": 1, "message": "4749", "line": 30, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 30, "endColumn": 25}, {"ruleId": "4666", "severity": 1, "message": "4751", "line": 31, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 31, "endColumn": 22, "suggestions": "4752"}, {"ruleId": "4671", "severity": 1, "message": "4751", "line": 31, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 31, "endColumn": 22}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 115, "column": 23, "nodeType": "4634", "messageId": "4635", "endLine": 115, "endColumn": 26, "suggestions": "4753"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 122, "column": 24, "nodeType": "4634", "messageId": "4635", "endLine": 122, "endColumn": 27, "suggestions": "4754"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 123, "column": 19, "nodeType": "4634", "messageId": "4635", "endLine": 123, "endColumn": 22, "suggestions": "4755"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 9, "column": 37, "nodeType": "4634", "messageId": "4635", "endLine": 9, "endColumn": 40, "suggestions": "4756", "suppressions": "4757"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 9, "column": 67, "nodeType": "4634", "messageId": "4635", "endLine": 9, "endColumn": 70, "suggestions": "4758", "suppressions": "4759"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 289, "column": 56, "nodeType": "4634", "messageId": "4635", "endLine": 289, "endColumn": 59, "suggestions": "4760"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 51, "column": 24, "nodeType": "4634", "messageId": "4635", "endLine": 51, "endColumn": 27, "suggestions": "4761"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 51, "column": 68, "nodeType": "4634", "messageId": "4635", "endLine": 51, "endColumn": 71, "suggestions": "4762"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 138, "column": 24, "nodeType": "4634", "messageId": "4635", "endLine": 138, "endColumn": 27, "suggestions": "4763"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 138, "column": 68, "nodeType": "4634", "messageId": "4635", "endLine": 138, "endColumn": 71, "suggestions": "4764"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 233, "column": 24, "nodeType": "4634", "messageId": "4635", "endLine": 233, "endColumn": 27, "suggestions": "4765"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 233, "column": 68, "nodeType": "4634", "messageId": "4635", "endLine": 233, "endColumn": 71, "suggestions": "4766"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 344, "column": 24, "nodeType": "4634", "messageId": "4635", "endLine": 344, "endColumn": 27, "suggestions": "4767"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 344, "column": 68, "nodeType": "4634", "messageId": "4635", "endLine": 344, "endColumn": 71, "suggestions": "4768"}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 3, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 3, "endColumn": 18, "suggestions": "4769"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 3, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 3, "endColumn": 18}, {"ruleId": "4666", "severity": 1, "message": "4732", "line": 3, "column": 20, "nodeType": "4668", "messageId": "4669", "endLine": 3, "endColumn": 26, "suggestions": "4770"}, {"ruleId": "4671", "severity": 1, "message": "4732", "line": 3, "column": 20, "nodeType": null, "messageId": "4669", "endLine": 3, "endColumn": 26}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 92, "column": 43, "nodeType": "4634", "messageId": "4635", "endLine": 92, "endColumn": 46, "suggestions": "4771"}, {"ruleId": "4666", "severity": 1, "message": "4772", "line": 38, "column": 9, "nodeType": "4668", "messageId": "4669", "endLine": 38, "endColumn": 17, "suggestions": "4773"}, {"ruleId": "4671", "severity": 1, "message": "4772", "line": 38, "column": 9, "nodeType": null, "messageId": "4669", "endLine": 38, "endColumn": 17}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 124, "column": 70, "nodeType": "4634", "messageId": "4635", "endLine": 124, "endColumn": 73, "suggestions": "4774"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 95, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 95, "endColumn": 45, "suggestions": "4775"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 130, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 130, "endColumn": 45, "suggestions": "4776"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 324, "column": 41, "nodeType": "4634", "messageId": "4635", "endLine": 324, "endColumn": 44, "suggestions": "4777"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 334, "column": 41, "nodeType": "4634", "messageId": "4635", "endLine": 334, "endColumn": 44, "suggestions": "4778"}, {"ruleId": "4666", "severity": 1, "message": "4679", "line": 2, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 2, "endColumn": 24, "suggestions": "4779"}, {"ruleId": "4671", "severity": 1, "message": "4679", "line": 2, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 2, "endColumn": 24}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 3, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 3, "endColumn": 18, "suggestions": "4780"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 3, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 3, "endColumn": 18}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 99, "column": 34, "nodeType": "4634", "messageId": "4635", "endLine": 99, "endColumn": 37, "suggestions": "4781"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 106, "column": 12, "nodeType": "4634", "messageId": "4635", "endLine": 106, "endColumn": 15, "suggestions": "4782"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 110, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 110, "endColumn": 45, "suggestions": "4783"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 111, "column": 91, "nodeType": "4634", "messageId": "4635", "endLine": 111, "endColumn": 94, "suggestions": "4784"}, {"ruleId": "4666", "severity": 1, "message": "4679", "line": 4, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 4, "endColumn": 24, "suggestions": "4785"}, {"ruleId": "4671", "severity": 1, "message": "4679", "line": 4, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 4, "endColumn": 24}, {"ruleId": "4666", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": "4668", "messageId": "4669", "endLine": 5, "endColumn": 18, "suggestions": "4786"}, {"ruleId": "4671", "severity": 1, "message": "4681", "line": 5, "column": 10, "nodeType": null, "messageId": "4669", "endLine": 5, "endColumn": 18}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 414, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 414, "endColumn": 45, "suggestions": "4787"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 418, "column": 44, "nodeType": "4634", "messageId": "4635", "endLine": 418, "endColumn": 47, "suggestions": "4788"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 419, "column": 121, "nodeType": "4634", "messageId": "4635", "endLine": 419, "endColumn": 124, "suggestions": "4789"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 449, "column": 32, "nodeType": "4634", "messageId": "4635", "endLine": 449, "endColumn": 35, "suggestions": "4790"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 453, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 453, "endColumn": 45, "suggestions": "4791"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 454, "column": 100, "nodeType": "4634", "messageId": "4635", "endLine": 454, "endColumn": 103, "suggestions": "4792"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 159, "column": 10, "nodeType": "4634", "messageId": "4635", "endLine": 159, "endColumn": 13, "suggestions": "4793"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 163, "column": 40, "nodeType": "4634", "messageId": "4635", "endLine": 163, "endColumn": 43, "suggestions": "4794"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 164, "column": 94, "nodeType": "4634", "messageId": "4635", "endLine": 164, "endColumn": 97, "suggestions": "4795"}, {"ruleId": "4666", "severity": 1, "message": "4772", "line": 28, "column": 9, "nodeType": "4668", "messageId": "4669", "endLine": 28, "endColumn": 17, "suggestions": "4796"}, {"ruleId": "4671", "severity": 1, "message": "4772", "line": 28, "column": 9, "nodeType": null, "messageId": "4669", "endLine": 28, "endColumn": 17}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 121, "column": 12, "nodeType": "4634", "messageId": "4635", "endLine": 121, "endColumn": 15, "suggestions": "4797"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 125, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 125, "endColumn": 45, "suggestions": "4798"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 126, "column": 113, "nodeType": "4634", "messageId": "4635", "endLine": 126, "endColumn": 116, "suggestions": "4799"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 240, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 240, "endColumn": 45, "suggestions": "4800"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 241, "column": 113, "nodeType": "4634", "messageId": "4635", "endLine": 241, "endColumn": 116, "suggestions": "4801"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 6, "column": 18, "nodeType": "4634", "messageId": "4635", "endLine": 6, "endColumn": 21, "suggestions": "4802", "suppressions": "4803"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 13, "column": 10, "nodeType": "4634", "messageId": "4635", "endLine": 13, "endColumn": 13, "suggestions": "4804", "suppressions": "4805"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 57, "column": 27, "nodeType": "4634", "messageId": "4635", "endLine": 57, "endColumn": 30, "suggestions": "4806"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 112, "column": 29, "nodeType": "4634", "messageId": "4635", "endLine": 112, "endColumn": 32, "suggestions": "4807"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 157, "column": 12, "nodeType": "4634", "messageId": "4635", "endLine": 157, "endColumn": 15, "suggestions": "4808"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 386, "column": 60, "nodeType": "4634", "messageId": "4635", "endLine": 386, "endColumn": 63, "suggestions": "4809"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 398, "column": 21, "nodeType": "4634", "messageId": "4635", "endLine": 398, "endColumn": 24, "suggestions": "4810"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 402, "column": 29, "nodeType": "4634", "messageId": "4635", "endLine": 402, "endColumn": 32, "suggestions": "4811"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 149, "column": 58, "nodeType": "4634", "messageId": "4635", "endLine": 149, "endColumn": 61, "suggestions": "4812"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 153, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 153, "endColumn": 45, "suggestions": "4813"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 154, "column": 130, "nodeType": "4634", "messageId": "4635", "endLine": 154, "endColumn": 133, "suggestions": "4814"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 157, "column": 89, "nodeType": "4634", "messageId": "4635", "endLine": 157, "endColumn": 92, "suggestions": "4815"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 279, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 279, "endColumn": 45, "suggestions": "4816"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 280, "column": 171, "nodeType": "4634", "messageId": "4635", "endLine": 280, "endColumn": 174, "suggestions": "4817"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 283, "column": 87, "nodeType": "4634", "messageId": "4635", "endLine": 283, "endColumn": 90, "suggestions": "4818"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 371, "column": 42, "nodeType": "4634", "messageId": "4635", "endLine": 371, "endColumn": 45, "suggestions": "4819"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 372, "column": 138, "nodeType": "4634", "messageId": "4635", "endLine": 372, "endColumn": 141, "suggestions": "4820"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 375, "column": 93, "nodeType": "4634", "messageId": "4635", "endLine": 375, "endColumn": 96, "suggestions": "4821"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 12, "column": 50, "nodeType": "4634", "messageId": "4635", "endLine": 12, "endColumn": 53, "suggestions": "4822", "suppressions": "4823"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 121, "column": 14, "nodeType": "4634", "messageId": "4635", "endLine": 121, "endColumn": 17, "suggestions": "4824"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 64, "column": 61, "nodeType": "4634", "messageId": "4635", "endLine": 64, "endColumn": 64, "suggestions": "4825"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 69, "column": 87, "nodeType": "4634", "messageId": "4635", "endLine": 69, "endColumn": 90, "suggestions": "4826"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 206, "column": 37, "nodeType": "4634", "messageId": "4635", "endLine": 206, "endColumn": 40, "suggestions": "4827"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 55, "column": 31, "nodeType": "4634", "messageId": "4635", "endLine": 55, "endColumn": 34, "suggestions": "4828", "suppressions": "4829"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 257, "column": 40, "nodeType": "4634", "messageId": "4635", "endLine": 257, "endColumn": 43, "suggestions": "4830"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 150, "column": 12, "nodeType": "4634", "messageId": "4635", "endLine": 150, "endColumn": 15, "suggestions": "4831"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 131, "column": 60, "nodeType": "4634", "messageId": "4635", "endLine": 131, "endColumn": 63, "suggestions": "4832", "suppressions": "4833"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 138, "column": 61, "nodeType": "4634", "messageId": "4635", "endLine": 138, "endColumn": 64, "suggestions": "4834", "suppressions": "4835"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 318, "column": 47, "nodeType": "4634", "messageId": "4635", "endLine": 318, "endColumn": 50, "suggestions": "4836"}, {"ruleId": "4632", "severity": 1, "message": "4633", "line": 334, "column": 50, "nodeType": "4634", "messageId": "4635", "endLine": 334, "endColumn": 53, "suggestions": "4837"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["4838", "4839"], ["4840", "4841"], ["4842", "4843"], ["4844", "4845"], ["4846", "4847"], ["4848", "4849"], ["4850", "4851"], ["4852", "4853"], ["4854", "4855"], ["4856", "4857"], ["4858", "4859"], ["4860", "4861"], ["4862", "4863"], ["4864", "4865"], ["4866", "4867"], ["4868", "4869"], ["4870", "4871"], ["4872", "4873"], ["4874", "4875"], ["4876", "4877"], ["4878", "4879"], ["4880", "4881"], ["4882", "4883"], ["4884", "4885"], ["4886", "4887"], ["4888", "4889"], ["4890", "4891"], ["4892", "4893"], ["4894", "4895"], ["4896", "4897"], "no-unused-vars", "'ReviewsResult' is defined but never used. Allowed unused vars must match /^_/u.", "Identifier", "unusedVar", ["4898"], "@typescript-eslint/no-unused-vars", "'createClient' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'FollowerWithProfile' is defined but never used. Allowed unused vars must match /^_/u.", ["4899"], "'FollowersResult' is defined but never used. Allowed unused vars must match /^_/u.", ["4900"], "'SubscriptionWithProfile' is defined but never used. Allowed unused vars must match /^_/u.", ["4901"], "'SupabaseClient' is defined but never used. Allowed unused vars must match /^_/u.", ["4902"], "'Database' is defined but never used. Allowed unused vars must match /^_/u.", ["4903"], ["4904"], ["4905"], ["4906", "4907"], ["4908", "4909"], ["4910", "4911"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'initialBusinessName', 'initialCategory', 'initialCity', 'initialLocality', 'initialPincode', 'performSearch', 'productFilterBy', and 'productSortBy'. Either include them or remove the dependency array.", "ArrayExpression", ["4912"], ["4913"], ["4914", "4915"], ["4916", "4917"], ["4918", "4919"], ["4920", "4921"], ["4922"], ["4923"], ["4924"], ["4925"], ["4926"], ["4927"], ["4928"], ["4929", "4930"], ["4931", "4932"], ["4933"], "'page' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4934"], "'count' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4935"], ["4936", "4937"], ["4938", "4939"], ["4940"], ["4941", "4942"], ["4943", "4944"], ["4945", "4946"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["4947"], "React Hook useEffect has a missing dependency: 'loadReviews'. Either include it or remove the dependency array.", ["4948"], ["4949"], "'ProductServiceData' is defined but never used. Allowed unused vars must match /^_/u.", ["4950"], ["4951", "4952"], ["4953", "4954"], ["4955"], "React Hook useEffect has missing dependencies: 'isSearching', 'performSearch', 'products.length', and 'viewType'. Either include them or remove the dependency array.", ["4956"], ["4957"], "'Tables' is defined but never used. Allowed unused vars must match /^_/u.", ["4958"], ["4959", "4960"], ["4961", "4962"], ["4963", "4964"], ["4965", "4966"], ["4967", "4968"], ["4969"], ["4970", "4971"], ["4972", "4973"], ["4974", "4975"], ["4976", "4977"], ["4978", "4979"], ["4980", "4981"], ["4982", "4983"], "React Hook useCallback has a missing dependency: 'handleScanSuccess'. Either include it or remove the dependency array.", ["4984"], "'realtimeService' is defined but never used. Allowed unused vars must match /^_/u.", ["4985"], "'createClient' is defined but never used. Allowed unused vars must match /^_/u.", ["4986"], ["4987", "4988"], ["4989", "4990"], ["4991", "4992"], ["4993", "4994"], ["4995"], ["4996", "4997"], ["4998"], ["4999", "5000"], ["5001", "5002"], ["5003", "5004"], ["5005", "5006"], ["5007", "5008"], ["5009", "5010"], ["5011", "5012"], ["5013", "5014"], ["5015", "5016"], ["5017"], ["5018"], ["5019", "5020"], "'supabase' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["5021"], ["5022", "5023"], ["5024", "5025"], ["5026", "5027"], ["5028", "5029"], ["5030", "5031"], ["5032"], ["5033"], ["5034", "5035"], ["5036", "5037"], ["5038", "5039"], ["5040", "5041"], ["5042"], ["5043"], ["5044", "5045"], ["5046", "5047"], ["5048", "5049"], ["5050", "5051"], ["5052", "5053"], ["5054", "5055"], ["5056", "5057"], ["5058", "5059"], ["5060", "5061"], ["5062"], ["5063", "5064"], ["5065", "5066"], ["5067", "5068"], ["5069", "5070"], ["5071", "5072"], ["5073", "5074"], ["5075"], ["5076", "5077"], ["5078"], ["5079", "5080"], ["5081", "5082"], ["5083", "5084"], ["5085", "5086"], ["5087", "5088"], ["5089", "5090"], ["5091", "5092"], ["5093", "5094"], ["5095", "5096"], ["5097", "5098"], ["5099", "5100"], ["5101", "5102"], ["5103", "5104"], ["5105", "5106"], ["5107", "5108"], ["5109", "5110"], ["5111", "5112"], ["5113"], ["5114", "5115"], ["5116", "5117"], ["5118", "5119"], ["5120", "5121"], ["5122", "5123"], ["5124"], ["5125", "5126"], ["5127", "5128"], ["5129", "5130"], ["5131"], ["5132", "5133"], ["5134"], ["5135", "5136"], ["5137", "5138"], {"messageId": "5139", "fix": "5140", "desc": "5141"}, {"messageId": "5142", "fix": "5143", "desc": "5144"}, {"messageId": "5139", "fix": "5145", "desc": "5141"}, {"messageId": "5142", "fix": "5146", "desc": "5144"}, {"messageId": "5139", "fix": "5147", "desc": "5141"}, {"messageId": "5142", "fix": "5148", "desc": "5144"}, {"messageId": "5139", "fix": "5149", "desc": "5141"}, {"messageId": "5142", "fix": "5150", "desc": "5144"}, {"messageId": "5139", "fix": "5151", "desc": "5141"}, {"messageId": "5142", "fix": "5152", "desc": "5144"}, {"messageId": "5139", "fix": "5153", "desc": "5141"}, {"messageId": "5142", "fix": "5154", "desc": "5144"}, {"messageId": "5139", "fix": "5155", "desc": "5141"}, {"messageId": "5142", "fix": "5156", "desc": "5144"}, {"messageId": "5139", "fix": "5157", "desc": "5141"}, {"messageId": "5142", "fix": "5158", "desc": "5144"}, {"messageId": "5139", "fix": "5159", "desc": "5141"}, {"messageId": "5142", "fix": "5160", "desc": "5144"}, {"messageId": "5139", "fix": "5161", "desc": "5141"}, {"messageId": "5142", "fix": "5162", "desc": "5144"}, {"messageId": "5139", "fix": "5163", "desc": "5141"}, {"messageId": "5142", "fix": "5164", "desc": "5144"}, {"messageId": "5139", "fix": "5165", "desc": "5141"}, {"messageId": "5142", "fix": "5166", "desc": "5144"}, {"messageId": "5139", "fix": "5167", "desc": "5141"}, {"messageId": "5142", "fix": "5168", "desc": "5144"}, {"messageId": "5139", "fix": "5169", "desc": "5141"}, {"messageId": "5142", "fix": "5170", "desc": "5144"}, {"messageId": "5139", "fix": "5171", "desc": "5141"}, {"messageId": "5142", "fix": "5172", "desc": "5144"}, {"messageId": "5139", "fix": "5173", "desc": "5141"}, {"messageId": "5142", "fix": "5174", "desc": "5144"}, {"messageId": "5139", "fix": "5175", "desc": "5141"}, {"messageId": "5142", "fix": "5176", "desc": "5144"}, {"messageId": "5139", "fix": "5177", "desc": "5141"}, {"messageId": "5142", "fix": "5178", "desc": "5144"}, {"messageId": "5139", "fix": "5179", "desc": "5141"}, {"messageId": "5142", "fix": "5180", "desc": "5144"}, {"messageId": "5139", "fix": "5181", "desc": "5141"}, {"messageId": "5142", "fix": "5182", "desc": "5144"}, {"messageId": "5139", "fix": "5183", "desc": "5141"}, {"messageId": "5142", "fix": "5184", "desc": "5144"}, {"messageId": "5139", "fix": "5185", "desc": "5141"}, {"messageId": "5142", "fix": "5186", "desc": "5144"}, {"messageId": "5139", "fix": "5187", "desc": "5141"}, {"messageId": "5142", "fix": "5188", "desc": "5144"}, {"messageId": "5139", "fix": "5189", "desc": "5141"}, {"messageId": "5142", "fix": "5190", "desc": "5144"}, {"messageId": "5139", "fix": "5191", "desc": "5141"}, {"messageId": "5142", "fix": "5192", "desc": "5144"}, {"messageId": "5139", "fix": "5193", "desc": "5141"}, {"messageId": "5142", "fix": "5194", "desc": "5144"}, {"messageId": "5139", "fix": "5195", "desc": "5141"}, {"messageId": "5142", "fix": "5196", "desc": "5144"}, {"messageId": "5139", "fix": "5197", "desc": "5141"}, {"messageId": "5142", "fix": "5198", "desc": "5144"}, {"messageId": "5139", "fix": "5199", "desc": "5141"}, {"messageId": "5142", "fix": "5200", "desc": "5144"}, {"messageId": "5139", "fix": "5201", "desc": "5141"}, {"messageId": "5142", "fix": "5202", "desc": "5144"}, {"messageId": "5203", "data": "5204", "fix": "5205", "desc": "5206"}, {"messageId": "5203", "data": "5207", "fix": "5208", "desc": "5209"}, {"messageId": "5203", "data": "5210", "fix": "5211", "desc": "5212"}, {"messageId": "5203", "data": "5213", "fix": "5214", "desc": "5215"}, {"messageId": "5203", "data": "5216", "fix": "5217", "desc": "5218"}, {"messageId": "5203", "data": "5219", "fix": "5220", "desc": "5221"}, {"messageId": "5203", "data": "5222", "fix": "5223", "desc": "5218"}, {"messageId": "5203", "data": "5224", "fix": "5225", "desc": "5221"}, {"messageId": "5139", "fix": "5226", "desc": "5141"}, {"messageId": "5142", "fix": "5227", "desc": "5144"}, {"messageId": "5139", "fix": "5228", "desc": "5141"}, {"messageId": "5142", "fix": "5229", "desc": "5144"}, {"messageId": "5139", "fix": "5230", "desc": "5141"}, {"messageId": "5142", "fix": "5231", "desc": "5144"}, {"desc": "5232", "fix": "5233"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5236", "desc": "5141"}, {"messageId": "5142", "fix": "5237", "desc": "5144"}, {"messageId": "5139", "fix": "5238", "desc": "5141"}, {"messageId": "5142", "fix": "5239", "desc": "5144"}, {"messageId": "5139", "fix": "5240", "desc": "5141"}, {"messageId": "5142", "fix": "5241", "desc": "5144"}, {"messageId": "5139", "fix": "5242", "desc": "5141"}, {"messageId": "5142", "fix": "5243", "desc": "5144"}, {"messageId": "5203", "data": "5244", "fix": "5245", "desc": "5221"}, {"messageId": "5203", "data": "5246", "fix": "5247", "desc": "5221"}, {"messageId": "5203", "data": "5248", "fix": "5249", "desc": "5221"}, {"messageId": "5203", "data": "5250", "fix": "5251", "desc": "5221"}, {"messageId": "5203", "data": "5252", "fix": "5253", "desc": "5221"}, {"messageId": "5203", "data": "5254", "fix": "5255", "desc": "5221"}, {"messageId": "5203", "data": "5256", "fix": "5257", "desc": "5221"}, {"messageId": "5139", "fix": "5258", "desc": "5141"}, {"messageId": "5142", "fix": "5259", "desc": "5144"}, {"messageId": "5139", "fix": "5260", "desc": "5141"}, {"messageId": "5142", "fix": "5261", "desc": "5144"}, {"messageId": "5203", "data": "5262", "fix": "5263", "desc": "5221"}, {"messageId": "5203", "data": "5264", "fix": "5265", "desc": "5266"}, {"messageId": "5203", "data": "5267", "fix": "5268", "desc": "5269"}, {"messageId": "5139", "fix": "5270", "desc": "5141"}, {"messageId": "5142", "fix": "5271", "desc": "5144"}, {"messageId": "5139", "fix": "5272", "desc": "5141"}, {"messageId": "5142", "fix": "5273", "desc": "5144"}, {"messageId": "5203", "data": "5274", "fix": "5275", "desc": "5221"}, {"messageId": "5139", "fix": "5276", "desc": "5141"}, {"messageId": "5142", "fix": "5277", "desc": "5144"}, {"messageId": "5139", "fix": "5278", "desc": "5141"}, {"messageId": "5142", "fix": "5279", "desc": "5144"}, {"messageId": "5139", "fix": "5280", "desc": "5141"}, {"messageId": "5142", "fix": "5281", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"desc": "5282", "fix": "5283"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5203", "data": "5284", "fix": "5285", "desc": "5286"}, {"messageId": "5139", "fix": "5287", "desc": "5141"}, {"messageId": "5142", "fix": "5288", "desc": "5144"}, {"messageId": "5139", "fix": "5289", "desc": "5141"}, {"messageId": "5142", "fix": "5290", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"desc": "5291", "fix": "5292"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5203", "data": "5293", "fix": "5294", "desc": "5295"}, {"messageId": "5139", "fix": "5296", "desc": "5141"}, {"messageId": "5142", "fix": "5297", "desc": "5144"}, {"messageId": "5139", "fix": "5298", "desc": "5141"}, {"messageId": "5142", "fix": "5299", "desc": "5144"}, {"messageId": "5139", "fix": "5300", "desc": "5141"}, {"messageId": "5142", "fix": "5301", "desc": "5144"}, {"messageId": "5139", "fix": "5302", "desc": "5141"}, {"messageId": "5142", "fix": "5303", "desc": "5144"}, {"messageId": "5139", "fix": "5304", "desc": "5141"}, {"messageId": "5142", "fix": "5305", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5306", "desc": "5141"}, {"messageId": "5142", "fix": "5307", "desc": "5144"}, {"messageId": "5139", "fix": "5308", "desc": "5141"}, {"messageId": "5142", "fix": "5309", "desc": "5144"}, {"messageId": "5139", "fix": "5310", "desc": "5141"}, {"messageId": "5142", "fix": "5311", "desc": "5144"}, {"messageId": "5139", "fix": "5312", "desc": "5141"}, {"messageId": "5142", "fix": "5313", "desc": "5144"}, {"messageId": "5139", "fix": "5314", "desc": "5141"}, {"messageId": "5142", "fix": "5315", "desc": "5144"}, {"messageId": "5139", "fix": "5316", "desc": "5141"}, {"messageId": "5142", "fix": "5317", "desc": "5144"}, {"messageId": "5139", "fix": "5318", "desc": "5141"}, {"messageId": "5142", "fix": "5319", "desc": "5144"}, {"desc": "5320", "fix": "5321"}, {"messageId": "5203", "data": "5322", "fix": "5323", "desc": "5324"}, {"messageId": "5203", "data": "5325", "fix": "5326", "desc": "5327"}, {"messageId": "5139", "fix": "5328", "desc": "5141"}, {"messageId": "5142", "fix": "5329", "desc": "5144"}, {"messageId": "5139", "fix": "5330", "desc": "5141"}, {"messageId": "5142", "fix": "5331", "desc": "5144"}, {"messageId": "5139", "fix": "5332", "desc": "5141"}, {"messageId": "5142", "fix": "5333", "desc": "5144"}, {"messageId": "5139", "fix": "5334", "desc": "5141"}, {"messageId": "5142", "fix": "5335", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5336", "desc": "5141"}, {"messageId": "5142", "fix": "5337", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5338", "desc": "5141"}, {"messageId": "5142", "fix": "5339", "desc": "5144"}, {"messageId": "5139", "fix": "5340", "desc": "5141"}, {"messageId": "5142", "fix": "5341", "desc": "5144"}, {"messageId": "5139", "fix": "5342", "desc": "5141"}, {"messageId": "5142", "fix": "5343", "desc": "5144"}, {"messageId": "5139", "fix": "5344", "desc": "5141"}, {"messageId": "5142", "fix": "5345", "desc": "5144"}, {"messageId": "5139", "fix": "5346", "desc": "5141"}, {"messageId": "5142", "fix": "5347", "desc": "5144"}, {"messageId": "5139", "fix": "5348", "desc": "5141"}, {"messageId": "5142", "fix": "5349", "desc": "5144"}, {"messageId": "5139", "fix": "5350", "desc": "5141"}, {"messageId": "5142", "fix": "5351", "desc": "5144"}, {"messageId": "5139", "fix": "5352", "desc": "5141"}, {"messageId": "5142", "fix": "5353", "desc": "5144"}, {"messageId": "5139", "fix": "5354", "desc": "5141"}, {"messageId": "5142", "fix": "5355", "desc": "5144"}, {"messageId": "5203", "data": "5356", "fix": "5357", "desc": "5221"}, {"messageId": "5203", "data": "5358", "fix": "5359", "desc": "5295"}, {"messageId": "5139", "fix": "5360", "desc": "5141"}, {"messageId": "5142", "fix": "5361", "desc": "5144"}, {"messageId": "5203", "data": "5362", "fix": "5363", "desc": "5364"}, {"messageId": "5139", "fix": "5365", "desc": "5141"}, {"messageId": "5142", "fix": "5366", "desc": "5144"}, {"messageId": "5139", "fix": "5367", "desc": "5141"}, {"messageId": "5142", "fix": "5368", "desc": "5144"}, {"messageId": "5139", "fix": "5369", "desc": "5141"}, {"messageId": "5142", "fix": "5370", "desc": "5144"}, {"messageId": "5139", "fix": "5371", "desc": "5141"}, {"messageId": "5142", "fix": "5372", "desc": "5144"}, {"messageId": "5139", "fix": "5373", "desc": "5141"}, {"messageId": "5142", "fix": "5374", "desc": "5144"}, {"messageId": "5203", "data": "5375", "fix": "5376", "desc": "5218"}, {"messageId": "5203", "data": "5377", "fix": "5378", "desc": "5221"}, {"messageId": "5139", "fix": "5379", "desc": "5141"}, {"messageId": "5142", "fix": "5380", "desc": "5144"}, {"messageId": "5139", "fix": "5381", "desc": "5141"}, {"messageId": "5142", "fix": "5382", "desc": "5144"}, {"messageId": "5139", "fix": "5383", "desc": "5141"}, {"messageId": "5142", "fix": "5384", "desc": "5144"}, {"messageId": "5139", "fix": "5385", "desc": "5141"}, {"messageId": "5142", "fix": "5386", "desc": "5144"}, {"messageId": "5203", "data": "5387", "fix": "5388", "desc": "5218"}, {"messageId": "5203", "data": "5389", "fix": "5390", "desc": "5221"}, {"messageId": "5139", "fix": "5391", "desc": "5141"}, {"messageId": "5142", "fix": "5392", "desc": "5144"}, {"messageId": "5139", "fix": "5393", "desc": "5141"}, {"messageId": "5142", "fix": "5394", "desc": "5144"}, {"messageId": "5139", "fix": "5395", "desc": "5141"}, {"messageId": "5142", "fix": "5396", "desc": "5144"}, {"messageId": "5139", "fix": "5397", "desc": "5141"}, {"messageId": "5142", "fix": "5398", "desc": "5144"}, {"messageId": "5139", "fix": "5399", "desc": "5141"}, {"messageId": "5142", "fix": "5400", "desc": "5144"}, {"messageId": "5139", "fix": "5401", "desc": "5141"}, {"messageId": "5142", "fix": "5402", "desc": "5144"}, {"messageId": "5139", "fix": "5403", "desc": "5141"}, {"messageId": "5142", "fix": "5404", "desc": "5144"}, {"messageId": "5139", "fix": "5405", "desc": "5141"}, {"messageId": "5142", "fix": "5406", "desc": "5144"}, {"messageId": "5139", "fix": "5407", "desc": "5141"}, {"messageId": "5142", "fix": "5408", "desc": "5144"}, {"messageId": "5203", "data": "5409", "fix": "5410", "desc": "5364"}, {"messageId": "5139", "fix": "5411", "desc": "5141"}, {"messageId": "5142", "fix": "5412", "desc": "5144"}, {"messageId": "5139", "fix": "5413", "desc": "5141"}, {"messageId": "5142", "fix": "5414", "desc": "5144"}, {"messageId": "5139", "fix": "5415", "desc": "5141"}, {"messageId": "5142", "fix": "5416", "desc": "5144"}, {"messageId": "5139", "fix": "5417", "desc": "5141"}, {"messageId": "5142", "fix": "5418", "desc": "5144"}, {"messageId": "5139", "fix": "5419", "desc": "5141"}, {"messageId": "5142", "fix": "5420", "desc": "5144"}, {"messageId": "5139", "fix": "5421", "desc": "5141"}, {"messageId": "5142", "fix": "5422", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5423", "desc": "5141"}, {"messageId": "5142", "fix": "5424", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5425", "desc": "5141"}, {"messageId": "5142", "fix": "5426", "desc": "5144"}, {"messageId": "5139", "fix": "5427", "desc": "5141"}, {"messageId": "5142", "fix": "5428", "desc": "5144"}, {"messageId": "5139", "fix": "5429", "desc": "5141"}, {"messageId": "5142", "fix": "5430", "desc": "5144"}, {"messageId": "5139", "fix": "5431", "desc": "5141"}, {"messageId": "5142", "fix": "5432", "desc": "5144"}, {"messageId": "5139", "fix": "5433", "desc": "5141"}, {"messageId": "5142", "fix": "5434", "desc": "5144"}, {"messageId": "5139", "fix": "5435", "desc": "5141"}, {"messageId": "5142", "fix": "5436", "desc": "5144"}, {"messageId": "5139", "fix": "5437", "desc": "5141"}, {"messageId": "5142", "fix": "5438", "desc": "5144"}, {"messageId": "5139", "fix": "5439", "desc": "5141"}, {"messageId": "5142", "fix": "5440", "desc": "5144"}, {"messageId": "5139", "fix": "5441", "desc": "5141"}, {"messageId": "5142", "fix": "5442", "desc": "5144"}, {"messageId": "5139", "fix": "5443", "desc": "5141"}, {"messageId": "5142", "fix": "5444", "desc": "5144"}, {"messageId": "5139", "fix": "5445", "desc": "5141"}, {"messageId": "5142", "fix": "5446", "desc": "5144"}, {"messageId": "5139", "fix": "5447", "desc": "5141"}, {"messageId": "5142", "fix": "5448", "desc": "5144"}, {"messageId": "5139", "fix": "5449", "desc": "5141"}, {"messageId": "5142", "fix": "5450", "desc": "5144"}, {"messageId": "5139", "fix": "5451", "desc": "5141"}, {"messageId": "5142", "fix": "5452", "desc": "5144"}, {"messageId": "5139", "fix": "5453", "desc": "5141"}, {"messageId": "5142", "fix": "5454", "desc": "5144"}, {"messageId": "5139", "fix": "5455", "desc": "5141"}, {"messageId": "5142", "fix": "5456", "desc": "5144"}, {"messageId": "5139", "fix": "5457", "desc": "5141"}, {"messageId": "5142", "fix": "5458", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5459", "desc": "5141"}, {"messageId": "5142", "fix": "5460", "desc": "5144"}, {"messageId": "5139", "fix": "5461", "desc": "5141"}, {"messageId": "5142", "fix": "5462", "desc": "5144"}, {"messageId": "5139", "fix": "5463", "desc": "5141"}, {"messageId": "5142", "fix": "5464", "desc": "5144"}, {"messageId": "5139", "fix": "5465", "desc": "5141"}, {"messageId": "5142", "fix": "5466", "desc": "5144"}, {"messageId": "5139", "fix": "5467", "desc": "5141"}, {"messageId": "5142", "fix": "5468", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5469", "desc": "5141"}, {"messageId": "5142", "fix": "5470", "desc": "5144"}, {"messageId": "5139", "fix": "5471", "desc": "5141"}, {"messageId": "5142", "fix": "5472", "desc": "5144"}, {"messageId": "5139", "fix": "5473", "desc": "5141"}, {"messageId": "5142", "fix": "5474", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5475", "desc": "5141"}, {"messageId": "5142", "fix": "5476", "desc": "5144"}, {"kind": "5234", "justification": "5235"}, {"messageId": "5139", "fix": "5477", "desc": "5141"}, {"messageId": "5142", "fix": "5478", "desc": "5144"}, {"messageId": "5139", "fix": "5479", "desc": "5141"}, {"messageId": "5142", "fix": "5480", "desc": "5144"}, "suggestUnknown", {"range": "5481", "text": "5482"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "5483", "text": "5484"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "5485", "text": "5482"}, {"range": "5486", "text": "5484"}, {"range": "5487", "text": "5482"}, {"range": "5488", "text": "5484"}, {"range": "5489", "text": "5482"}, {"range": "5490", "text": "5484"}, {"range": "5491", "text": "5482"}, {"range": "5492", "text": "5484"}, {"range": "5493", "text": "5482"}, {"range": "5494", "text": "5484"}, {"range": "5495", "text": "5482"}, {"range": "5496", "text": "5484"}, {"range": "5497", "text": "5482"}, {"range": "5498", "text": "5484"}, {"range": "5499", "text": "5482"}, {"range": "5500", "text": "5484"}, {"range": "5501", "text": "5482"}, {"range": "5502", "text": "5484"}, {"range": "5503", "text": "5482"}, {"range": "5504", "text": "5484"}, {"range": "5505", "text": "5482"}, {"range": "5506", "text": "5484"}, {"range": "5507", "text": "5482"}, {"range": "5508", "text": "5484"}, {"range": "5509", "text": "5482"}, {"range": "5510", "text": "5484"}, {"range": "5511", "text": "5482"}, {"range": "5512", "text": "5484"}, {"range": "5513", "text": "5482"}, {"range": "5514", "text": "5484"}, {"range": "5515", "text": "5482"}, {"range": "5516", "text": "5484"}, {"range": "5517", "text": "5482"}, {"range": "5518", "text": "5484"}, {"range": "5519", "text": "5482"}, {"range": "5520", "text": "5484"}, {"range": "5521", "text": "5482"}, {"range": "5522", "text": "5484"}, {"range": "5523", "text": "5482"}, {"range": "5524", "text": "5484"}, {"range": "5525", "text": "5482"}, {"range": "5526", "text": "5484"}, {"range": "5527", "text": "5482"}, {"range": "5528", "text": "5484"}, {"range": "5529", "text": "5482"}, {"range": "5530", "text": "5484"}, {"range": "5531", "text": "5482"}, {"range": "5532", "text": "5484"}, {"range": "5533", "text": "5482"}, {"range": "5534", "text": "5484"}, {"range": "5535", "text": "5482"}, {"range": "5536", "text": "5484"}, {"range": "5537", "text": "5482"}, {"range": "5538", "text": "5484"}, {"range": "5539", "text": "5482"}, {"range": "5540", "text": "5484"}, {"range": "5541", "text": "5482"}, {"range": "5542", "text": "5484"}, "removeVar", {"varName": "5543"}, {"range": "5544", "text": "5235"}, "Remove unused variable 'ReviewsResult'.", {"varName": "5545"}, {"range": "5546", "text": "5235"}, "Remove unused variable 'FollowerWithProfile'.", {"varName": "5547"}, {"range": "5548", "text": "5235"}, "Remove unused variable 'FollowersR<PERSON>ult'.", {"varName": "5549"}, {"range": "5550", "text": "5235"}, "Remove unused variable 'SubscriptionWithProfile'.", {"varName": "5551"}, {"range": "5552", "text": "5235"}, "Remove unused variable 'SupabaseClient'.", {"varName": "5553"}, {"range": "5554", "text": "5235"}, "Remove unused variable 'Database'.", {"varName": "5551"}, {"range": "5555", "text": "5235"}, {"varName": "5553"}, {"range": "5556", "text": "5235"}, {"range": "5557", "text": "5482"}, {"range": "5558", "text": "5484"}, {"range": "5559", "text": "5482"}, {"range": "5560", "text": "5484"}, {"range": "5561", "text": "5482"}, {"range": "5562", "text": "5484"}, "Update the dependencies array to be: [initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", {"range": "5563", "text": "5564"}, "directive", "", {"range": "5565", "text": "5482"}, {"range": "5566", "text": "5484"}, {"range": "5567", "text": "5482"}, {"range": "5568", "text": "5484"}, {"range": "5569", "text": "5482"}, {"range": "5570", "text": "5484"}, {"range": "5571", "text": "5482"}, {"range": "5572", "text": "5484"}, {"varName": "5553"}, {"range": "5573", "text": "5235"}, {"varName": "5553"}, {"range": "5574", "text": "5235"}, {"varName": "5553"}, {"range": "5575", "text": "5235"}, {"varName": "5553"}, {"range": "5576", "text": "5235"}, {"varName": "5553"}, {"range": "5577", "text": "5235"}, {"varName": "5553"}, {"range": "5578", "text": "5235"}, {"varName": "5553"}, {"range": "5579", "text": "5235"}, {"range": "5580", "text": "5482"}, {"range": "5581", "text": "5484"}, {"range": "5582", "text": "5482"}, {"range": "5583", "text": "5484"}, {"varName": "5553"}, {"range": "5584", "text": "5235"}, {"varName": "5585"}, {"range": "5586", "text": "5235"}, "Remove unused variable 'page'.", {"varName": "5587"}, {"range": "5588", "text": "5235"}, "Remove unused variable 'count'.", {"range": "5589", "text": "5482"}, {"range": "5590", "text": "5484"}, {"range": "5591", "text": "5482"}, {"range": "5592", "text": "5484"}, {"varName": "5553"}, {"range": "5593", "text": "5235"}, {"range": "5594", "text": "5482"}, {"range": "5595", "text": "5484"}, {"range": "5596", "text": "5482"}, {"range": "5597", "text": "5484"}, {"range": "5598", "text": "5482"}, {"range": "5599", "text": "5484"}, "Update the dependencies array to be: [currentPage, sortBy, businessProfileId, loadReviews]", {"range": "5600", "text": "5601"}, {"varName": "5602"}, {"range": "5603", "text": "5235"}, "Remove unused variable 'ProductServiceData'.", {"range": "5604", "text": "5482"}, {"range": "5605", "text": "5484"}, {"range": "5606", "text": "5482"}, {"range": "5607", "text": "5484"}, "Update the dependencies array to be: [isSearching, performSearch, products.length, viewType]", {"range": "5608", "text": "5609"}, {"varName": "5610"}, {"range": "5611", "text": "5235"}, "Remove unused variable 'Tables'.", {"range": "5612", "text": "5482"}, {"range": "5613", "text": "5484"}, {"range": "5614", "text": "5482"}, {"range": "5615", "text": "5484"}, {"range": "5616", "text": "5482"}, {"range": "5617", "text": "5484"}, {"range": "5618", "text": "5482"}, {"range": "5619", "text": "5484"}, {"range": "5620", "text": "5482"}, {"range": "5621", "text": "5484"}, {"range": "5622", "text": "5482"}, {"range": "5623", "text": "5484"}, {"range": "5624", "text": "5482"}, {"range": "5625", "text": "5484"}, {"range": "5626", "text": "5482"}, {"range": "5627", "text": "5484"}, {"range": "5628", "text": "5482"}, {"range": "5629", "text": "5484"}, {"range": "5630", "text": "5482"}, {"range": "5631", "text": "5484"}, {"range": "5632", "text": "5482"}, {"range": "5633", "text": "5484"}, {"range": "5634", "text": "5482"}, {"range": "5635", "text": "5484"}, "Update the dependencies array to be: [handleScanSuccess]", {"range": "5636", "text": "5637"}, {"varName": "5638"}, {"range": "5639", "text": "5235"}, "Remove unused variable 'realtimeService'.", {"varName": "5640"}, {"range": "5641", "text": "5235"}, "Remove unused variable 'createClient'.", {"range": "5642", "text": "5482"}, {"range": "5643", "text": "5484"}, {"range": "5644", "text": "5482"}, {"range": "5645", "text": "5484"}, {"range": "5646", "text": "5482"}, {"range": "5647", "text": "5484"}, {"range": "5648", "text": "5482"}, {"range": "5649", "text": "5484"}, {"range": "5650", "text": "5482"}, {"range": "5651", "text": "5484"}, {"range": "5652", "text": "5482"}, {"range": "5653", "text": "5484"}, {"range": "5654", "text": "5482"}, {"range": "5655", "text": "5484"}, {"range": "5656", "text": "5482"}, {"range": "5657", "text": "5484"}, {"range": "5658", "text": "5482"}, {"range": "5659", "text": "5484"}, {"range": "5660", "text": "5482"}, {"range": "5661", "text": "5484"}, {"range": "5662", "text": "5482"}, {"range": "5663", "text": "5484"}, {"range": "5664", "text": "5482"}, {"range": "5665", "text": "5484"}, {"range": "5666", "text": "5482"}, {"range": "5667", "text": "5484"}, {"range": "5668", "text": "5482"}, {"range": "5669", "text": "5484"}, {"varName": "5553"}, {"range": "5670", "text": "5235"}, {"varName": "5610"}, {"range": "5671", "text": "5235"}, {"range": "5672", "text": "5482"}, {"range": "5673", "text": "5484"}, {"varName": "5674"}, {"range": "5675", "text": "5235"}, "Remove unused variable 'supabase'.", {"range": "5676", "text": "5482"}, {"range": "5677", "text": "5484"}, {"range": "5678", "text": "5482"}, {"range": "5679", "text": "5484"}, {"range": "5680", "text": "5482"}, {"range": "5681", "text": "5484"}, {"range": "5682", "text": "5482"}, {"range": "5683", "text": "5484"}, {"range": "5684", "text": "5482"}, {"range": "5685", "text": "5484"}, {"varName": "5551"}, {"range": "5686", "text": "5235"}, {"varName": "5553"}, {"range": "5687", "text": "5235"}, {"range": "5688", "text": "5482"}, {"range": "5689", "text": "5484"}, {"range": "5690", "text": "5482"}, {"range": "5691", "text": "5484"}, {"range": "5692", "text": "5482"}, {"range": "5693", "text": "5484"}, {"range": "5694", "text": "5482"}, {"range": "5695", "text": "5484"}, {"varName": "5551"}, {"range": "5696", "text": "5235"}, {"varName": "5553"}, {"range": "5697", "text": "5235"}, {"range": "5698", "text": "5482"}, {"range": "5699", "text": "5484"}, {"range": "5700", "text": "5482"}, {"range": "5701", "text": "5484"}, {"range": "5702", "text": "5482"}, {"range": "5703", "text": "5484"}, {"range": "5704", "text": "5482"}, {"range": "5705", "text": "5484"}, {"range": "5706", "text": "5482"}, {"range": "5707", "text": "5484"}, {"range": "5708", "text": "5482"}, {"range": "5709", "text": "5484"}, {"range": "5710", "text": "5482"}, {"range": "5711", "text": "5484"}, {"range": "5712", "text": "5482"}, {"range": "5713", "text": "5484"}, {"range": "5714", "text": "5482"}, {"range": "5715", "text": "5484"}, {"varName": "5674"}, {"range": "5716", "text": "5235"}, {"range": "5717", "text": "5482"}, {"range": "5718", "text": "5484"}, {"range": "5719", "text": "5482"}, {"range": "5720", "text": "5484"}, {"range": "5721", "text": "5482"}, {"range": "5722", "text": "5484"}, {"range": "5723", "text": "5482"}, {"range": "5724", "text": "5484"}, {"range": "5725", "text": "5482"}, {"range": "5726", "text": "5484"}, {"range": "5727", "text": "5482"}, {"range": "5728", "text": "5484"}, {"range": "5729", "text": "5482"}, {"range": "5730", "text": "5484"}, {"range": "5731", "text": "5482"}, {"range": "5732", "text": "5484"}, {"range": "5733", "text": "5482"}, {"range": "5734", "text": "5484"}, {"range": "5735", "text": "5482"}, {"range": "5736", "text": "5484"}, {"range": "5737", "text": "5482"}, {"range": "5738", "text": "5484"}, {"range": "5739", "text": "5482"}, {"range": "5740", "text": "5484"}, {"range": "5741", "text": "5482"}, {"range": "5742", "text": "5484"}, {"range": "5743", "text": "5482"}, {"range": "5744", "text": "5484"}, {"range": "5745", "text": "5482"}, {"range": "5746", "text": "5484"}, {"range": "5747", "text": "5482"}, {"range": "5748", "text": "5484"}, {"range": "5749", "text": "5482"}, {"range": "5750", "text": "5484"}, {"range": "5751", "text": "5482"}, {"range": "5752", "text": "5484"}, {"range": "5753", "text": "5482"}, {"range": "5754", "text": "5484"}, {"range": "5755", "text": "5482"}, {"range": "5756", "text": "5484"}, {"range": "5757", "text": "5482"}, {"range": "5758", "text": "5484"}, {"range": "5759", "text": "5482"}, {"range": "5760", "text": "5484"}, {"range": "5761", "text": "5482"}, {"range": "5762", "text": "5484"}, {"range": "5763", "text": "5482"}, {"range": "5764", "text": "5484"}, {"range": "5765", "text": "5482"}, {"range": "5766", "text": "5484"}, {"range": "5767", "text": "5482"}, {"range": "5768", "text": "5484"}, {"range": "5769", "text": "5482"}, {"range": "5770", "text": "5484"}, {"range": "5771", "text": "5482"}, {"range": "5772", "text": "5484"}, {"range": "5773", "text": "5482"}, {"range": "5774", "text": "5484"}, {"range": "5775", "text": "5482"}, {"range": "5776", "text": "5484"}, {"range": "5777", "text": "5482"}, {"range": "5778", "text": "5484"}, {"range": "5779", "text": "5482"}, {"range": "5780", "text": "5484"}, {"range": "5781", "text": "5482"}, {"range": "5782", "text": "5484"}, {"range": "5783", "text": "5482"}, {"range": "5784", "text": "5484"}, {"range": "5785", "text": "5482"}, {"range": "5786", "text": "5484"}, [3990, 3993], "unknown", [3990, 3993], "never", [3157, 3160], [3157, 3160], [4508, 4511], [4508, 4511], [8534, 8537], [8534, 8537], [5356, 5359], [5356, 5359], [5519, 5522], [5519, 5522], [9806, 9809], [9806, 9809], [9989, 9992], [9989, 9992], [10035, 10038], [10035, 10038], [10089, 10092], [10089, 10092], [2514, 2517], [2514, 2517], [2602, 2605], [2602, 2605], [1926, 1929], [1926, 1929], [3315, 3318], [3315, 3318], [4948, 4951], [4948, 4951], [4201, 4204], [4201, 4204], [4527, 4530], [4527, 4530], [4559, 4562], [4559, 4562], [4621, 4624], [4621, 4624], [4653, 4656], [4653, 4656], [15428, 15431], [15428, 15431], [9228, 9231], [9228, 9231], [9438, 9441], [9438, 9441], [9809, 9812], [9809, 9812], [2386, 2389], [2386, 2389], [3553, 3556], [3553, 3556], [10077, 10080], [10077, 10080], [16494, 16497], [16494, 16497], [6043, 6046], [6043, 6046], [3496, 3499], [3496, 3499], "ReviewsResult", [38, 53], "FollowerWithProfile", [46, 69], "FollowersResult", [69, 88], "SubscriptionWithProfile", [88, 115], "SupabaseClient", [74, 129], "Database", [131, 175], [74, 129], [131, 175], [3752, 3755], [3752, 3755], [3819, 3822], [3819, 3822], [2107, 2110], [2107, 2110], [9509, 9511], "[initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", [14288, 14291], [14288, 14291], [14372, 14375], [14372, 14375], [14406, 14409], [14406, 14409], [14660, 14663], [14660, 14663], [163, 207], [185, 229], [255, 299], [236, 280], [252, 296], [255, 299], [251, 295], [5307, 5310], [5307, 5310], [5437, 5440], [5437, 5440], [249, 293], "page", [1753, 1812], "count", [1817, 1879], [5109, 5112], [5109, 5112], [5239, 5242], [5239, 5242], [245, 289], [7881, 7884], [7881, 7884], [8246, 8249], [8246, 8249], [8376, 8379], [8376, 8379], [1800, 1840], "[currentPage, sortBy, businessProfileId, loadReviews]", "ProductServiceData", [3570, 3588], [4123, 4126], [4123, 4126], [4605, 4608], [4605, 4608], [5544, 5546], "[isSearching, performSearch, products.length, viewType]", "Tables", [303, 345], [3267, 3270], [3267, 3270], [9983, 9986], [9983, 9986], [10035, 10038], [10035, 10038], [6590, 6593], [6590, 6593], [12572, 12575], [12572, 12575], [2152, 2155], [2152, 2155], [3970, 3973], [3970, 3973], [5410, 5413], [5410, 5413], [5983, 5986], [5983, 5986], [7360, 7363], [7360, 7363], [1660, 1663], [1660, 1663], [2569, 2572], [2569, 2572], [4157, 4159], "[handleScanSuccess]", "realtimeService", [747, 812], "createClient", [814, 869], [3732, 3735], [3732, 3735], [4043, 4046], [4043, 4046], [4067, 4070], [4067, 4070], [394, 397], [394, 397], [424, 427], [424, 427], [9442, 9445], [9442, 9445], [1549, 1552], [1549, 1552], [1593, 1596], [1593, 1596], [4462, 4465], [4462, 4465], [4506, 4509], [4506, 4509], [7783, 7786], [7783, 7786], [7827, 7830], [7827, 7830], [11366, 11369], [11366, 11369], [11410, 11413], [11410, 11413], [125, 134], [133, 141], [2752, 2755], [2752, 2755], "supabase", [961, 999], [4025, 4028], [4025, 4028], [2674, 2677], [2674, 2677], [3825, 3828], [3825, 3828], [9549, 9552], [9549, 9552], [9820, 9823], [9820, 9823], [57, 112], [114, 158], [4368, 4371], [4368, 4371], [4796, 4799], [4796, 4799], [4892, 4895], [4892, 4895], [5000, 5003], [5000, 5003], [74, 129], [131, 175], [19205, 19208], [19205, 19208], [19307, 19310], [19307, 19310], [19445, 19448], [19445, 19448], [20910, 20913], [20910, 20913], [21006, 21009], [21006, 21009], [21123, 21126], [21123, 21126], [6218, 6221], [6218, 6221], [6308, 6311], [6308, 6311], [6419, 6422], [6419, 6422], [868, 934], [5047, 5050], [5047, 5050], [5143, 5146], [5143, 5146], [5273, 5276], [5273, 5276], [9953, 9956], [9953, 9956], [10083, 10086], [10083, 10086], [170, 173], [170, 173], [362, 365], [362, 365], [1899, 1902], [1899, 1902], [3493, 3496], [3493, 3496], [4841, 4844], [4841, 4844], [17809, 17812], [17809, 17812], [18166, 18169], [18166, 18169], [18366, 18369], [18366, 18369], [6359, 6362], [6359, 6362], [6506, 6509], [6506, 6509], [6653, 6656], [6653, 6656], [6797, 6800], [6797, 6800], [11147, 11150], [11147, 11150], [11335, 11338], [11335, 11338], [11477, 11480], [11477, 11480], [15260, 15263], [15260, 15263], [15415, 15418], [15415, 15418], [15563, 15566], [15563, 15566], [478, 481], [478, 481], [4829, 4832], [4829, 4832], [2134, 2137], [2134, 2137], [2340, 2343], [2340, 2343], [7100, 7103], [7100, 7103], [1622, 1625], [1622, 1625], [8671, 8674], [8671, 8674], [4671, 4674], [4671, 4674], [3617, 3620], [3617, 3620], [3858, 3861], [3858, 3861], [9079, 9082], [9079, 9082], [9584, 9587], [9584, 9587]]