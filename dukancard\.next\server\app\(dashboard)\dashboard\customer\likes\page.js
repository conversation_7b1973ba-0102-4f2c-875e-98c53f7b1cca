(()=>{var e={};e.id=1169,e.ids=[1169],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9266:(e,r,s)=>{"use strict";s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\customer\\\\likes\\\\components\\\\LikesPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24107:(e,r,s)=>{"use strict";function t(e){let{pincode:r,state:s,city:t,locality:a}=e;return!!(r&&""!==r.trim()&&s&&""!==s.trim()&&t&&""!==t.trim()&&a&&""!==a.trim())}function a(e){let r=[];return e.pincode&&""!==e.pincode.trim()||r.push("pincode"),e.state&&""!==e.state.trim()||r.push("state"),e.city&&""!==e.city.trim()||r.push("city"),e.locality&&""!==e.locality.trim()||r.push("locality"),r}function i(e){if(0===e.length)return"";let r=e.map(e=>{switch(e){case"pincode":return"Pincode";case"state":return"State";case"city":return"City";case"locality":return"Locality";default:return e}});if(1===r.length)return`Please update your ${r[0]} in your profile.`;{if(2===r.length)return`Please update your ${r.join(" and ")} in your profile.`;let e=r.pop();return`Please update your ${r.join(", ")}, and ${e} in your profile.`}}s.d(r,{Gs:()=>t,SJ:()=>a,zp:()=>i})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44992:(e,r,s)=>{"use strict";s.r(r),s.d(r,{"40397ca1727b354672455d32692e626008a169d968":()=>t.WO,"40930e22958ec27cc9c1459e0e63b2994bcf186979":()=>t.Xd,"40ab7a7f21cb4460091c8fb4edccd341d3b9116d41":()=>t.G_,"40e6b7c827369932950dcd3c394b2c641f20ee0597":()=>t.Hh,"40fbda5e049b7decd80deacff63f96dfec9a104b5a":()=>t.PD,"608517e8bbf255550985eb946a3ce32616cdf4cb5a":()=>t.kJ});var t=s(54694)},46939:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>b,metadata:()=>x});var t=s(37413),a=s(32032),i=s(39916),n=s(1759),o=s(54781),l=s(85838),d=s(26919),c=s(9266),u=s(61120),p=s(89142),m=s(54694),f=s(22637);async function h(e,r=1,s=12,t=""){try{return await f.Qc.fetchLikes(e,r,s,t)}catch(e){throw console.error("Error in fetchCustomerLikes:",e),e}}let x={title:"My Likes - Dukancard",robots:"noindex, nofollow"};async function b({searchParams:e}){let{search:r,page:s}=await e,f=await (0,a.createClient)(),x=s?parseInt(s):1,b=r||"",{data:{user:y},error:g}=await f.auth.getUser();(g||!y)&&(0,i.redirect)("/login?message=Please log in to view your likes."),await (0,m.kJ)(y.id);try{let e=await h(y.id,x,12,b);return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)(u.Suspense,{fallback:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex flex-col gap-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"p-3 rounded-xl bg-muted hidden sm:block",children:(0,t.jsx)(l.A,{className:"w-6 h-6 text-rose-600 dark:text-rose-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Liked Businesses"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:"Businesses you've liked"})]})]}),(0,t.jsx)("div",{className:"w-full sm:w-80",children:(0,t.jsx)(o.E,{className:"h-10 w-full rounded-md"})})]})}),(0,t.jsx)(p.bJ,{})]}),children:(0,t.jsx)(c.default,{initialLikes:e.items,totalCount:e.totalCount,currentPage:e.currentPage,searchTerm:b})})})}catch(e){return(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsx)(n.XL,{children:"Error"}),(0,t.jsx)(n.TN,{children:"Could not load likes data. Please try again later."})]})}}},53225:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["customer",{children:["likes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,46939)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,78050)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/customer/likes/page",pathname:"/dashboard/customer/likes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54694:(e,r,s)=>{"use strict";s.d(r,{G_:()=>d,Hh:()=>l,PD:()=>o,WO:()=>c,Xd:()=>p,kJ:()=>u});var t=s(67218);s(79130);var a=s(32032),i=s(24107),n=s(39916);async function o(e){let r=await (0,a.createClient)();try{let{data:s,error:t}=await r.from("customer_profiles").select("pincode, state, city, locality, address").eq("id",e).single();if(t)return console.error("Error fetching customer profile for address validation:",t),{isValid:!1,message:"Unable to verify your address information. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your address information"};let a={pincode:s?.pincode,state:s?.state,city:s?.city,locality:s?.locality,address:s?.address};if(!(0,i.Gs)(a)){let e=(0,i.SJ)(a),r=(0,i.zp)(e),s=`/dashboard/customer/profile?message=${encodeURIComponent(r)}`;return{isValid:!1,missingFields:e,message:r,redirectUrl:s}}return{isValid:!0}}catch(e){return console.error("Unexpected error during address validation:",e),{isValid:!1,message:"An error occurred while validating your address. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your address information"}}}async function l(e){let r=await o(e);!r.isValid&&r.redirectUrl&&(0,n.redirect)(r.redirectUrl)}async function d(e){let r=await (0,a.createClient)();try{let{data:s,error:t}=await r.from("customer_profiles").select("name").eq("id",e).single();if(t)return console.error("Error fetching customer profile for name validation:",t),{isValid:!1,message:"Unable to verify your profile information. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your profile information"};if(!(s?.name&&""!==s.name.trim())){let e="Please complete your name in your profile to access the dashboard.",r=`/dashboard/customer/profile?message=${encodeURIComponent(e)}`;return{isValid:!1,message:e,redirectUrl:r}}return{isValid:!0}}catch(e){return console.error("Unexpected error during name validation:",e),{isValid:!1,message:"An error occurred while validating your profile. Please update your profile.",redirectUrl:"/dashboard/customer/profile?message=Please update your profile information"}}}async function c(e){let r=await d(e);!r.isValid&&r.redirectUrl&&(0,n.redirect)(r.redirectUrl)}async function u(e,r=!1){await c(e),r||await l(e)}async function p(e){let r=await (0,a.createClient)();try{let{data:s,error:t}=await r.from("customer_profiles").select("pincode, state, city, locality, address").eq("id",e).single();if(t)return console.error("Error fetching customer address data:",t),{error:"Failed to fetch address data"};return{data:{pincode:s?.pincode,state:s?.state,city:s?.city,locality:s?.locality,address:s?.address}}}catch(e){return console.error("Unexpected error fetching address data:",e),{error:"An unexpected error occurred"}}}(0,s(17478).D)([o,l,d,c,u,p]),(0,t.A)(o,"40fbda5e049b7decd80deacff63f96dfec9a104b5a",null),(0,t.A)(l,"40e6b7c827369932950dcd3c394b2c641f20ee0597",null),(0,t.A)(d,"40ab7a7f21cb4460091c8fb4edccd341d3b9116d41",null),(0,t.A)(c,"40397ca1727b354672455d32692e626008a169d968",null),(0,t.A)(u,"608517e8bbf255550985eb946a3ce32616cdf4cb5a",null),(0,t.A)(p,"40930e22958ec27cc9c1459e0e63b2994bcf186979",null)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64620:(e,r,s)=>{"use strict";s.d(r,{default:()=>u});var t=s(60687),a=s(43210),i=s(67760),n=s(16189),o=s(980);function l({initialLikes:e}){let r=(0,a.useMemo)(()=>e.map(e=>({id:e.id,profile:e.business_profiles?{id:e.business_profiles.id,name:e.business_profiles.business_name,slug:e.business_profiles.business_slug,logo_url:e.business_profiles.logo_url,city:e.business_profiles.city,state:e.business_profiles.state,locality:e.business_profiles.locality,type:"business"}:null})).filter(e=>null!==e.profile),[e]);return(0,t.jsx)(o.o8,{initialLikes:r,showUnlike:!0,emptyMessage:"You haven't liked any businesses yet.",emptyDescription:"Like businesses to see them here and get updates.",showDiscoverButton:!0,showVisitButton:!0,showAddress:!0,showRedirectIcon:!1})}var d=s(74354),c=s(96241);function u({initialLikes:e,totalCount:r,currentPage:s,searchTerm:u}){let p=(0,n.useRouter)(),m=(0,n.useSearchParams)(),[f,h]=(0,a.useState)(!1),[x,b]=(0,a.useState)(u),y=Math.ceil(r/12),g=(0,a.useCallback)(e=>{h(!0),b(e);let r=new URLSearchParams(m);e?r.set("search",e):r.delete("search"),r.delete("page"),p.push(`/dashboard/customer/likes?${r.toString()}`)},[p,m]),v=(0,a.useCallback)(e=>{h(!0);let r=new URLSearchParams(m);e>1?r.set("page",e.toString()):r.delete("page"),p.push(`/dashboard/customer/likes?${r.toString()}`)},[p,m]);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"p-3 rounded-xl bg-muted hidden sm:block",children:(0,t.jsx)(i.A,{className:"w-6 h-6 text-rose-600 dark:text-rose-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Liked Businesses"}),(0,t.jsxs)("p",{className:"text-muted-foreground mt-1",children:[(0,c.gY)(r)," ",1===r?"business":"businesses"," you've liked"]})]})]}),(0,t.jsx)("div",{className:"w-full sm:w-80",children:(0,t.jsx)(o._n,{onSearch:g,initialSearchTerm:x,placeholder:"Search businesses by name..."})})]}),x&&!f&&(0,t.jsxs)("div",{className:"text-sm text-muted-foreground border-l-4 border-primary pl-4",children:["Found ",(0,c.gY)(r)," ",1===r?"business":"businesses",x?` matching "${x}"`:""]})]}),(0,t.jsx)("div",{className:"space-y-6",children:f?(0,t.jsx)(o.bJ,{}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l,{initialLikes:e}),y>1&&(0,t.jsx)("div",{className:"flex justify-center pt-6",children:(0,t.jsx)(d.dK,{children:(0,t.jsxs)(d.Iu,{children:[s>1&&(0,t.jsx)(d.cU,{children:(0,t.jsx)(d.Eb,{href:"#",onClick:e=>{e.preventDefault(),v(s-1)}})}),Array.from({length:Math.min(5,y)},(e,r)=>{let a;return a=y<=5||s<=3?r+1:s>=y-2?y-4+r:s-2+r,(0,t.jsx)(d.cU,{children:(0,t.jsx)(d.n$,{href:"#",onClick:e=>{e.preventDefault(),v(a)},isActive:s===a,children:a})},a)}),s<y&&(0,t.jsx)(d.cU,{children:(0,t.jsx)(d.WA,{href:"#",onClick:e=>{e.preventDefault(),v(s+1)}})})]})})})]})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},74354:(e,r,s)=>{"use strict";s.d(r,{Eb:()=>f,Iu:()=>u,M_:()=>x,WA:()=>h,cU:()=>p,dK:()=>c,n$:()=>m});var t=s(60687),a=s(43210),i=s(47033),n=s(14952),o=s(93661),l=s(96241),d=s(24934);let c=({className:e,...r})=>(0,t.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,l.cn)("mx-auto flex w-full justify-center",e),...r}),u=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("ul",{ref:s,className:(0,l.cn)("flex flex-row items-center gap-1",e),...r}));u.displayName="PaginationContent";let p=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("li",{ref:s,className:(0,l.cn)("",e),...r}));p.displayName="PaginationItem";let m=({className:e,isActive:r,size:s="icon",...a})=>(0,t.jsx)("a",{"aria-current":r?"page":void 0,className:(0,l.cn)((0,d.r)({variant:r?"outline":"ghost",size:s}),e,r&&"bg-muted hover:bg-muted pointer-events-none"),...a});m.displayName="PaginationLink";let f=({className:e,...r})=>(0,t.jsxs)(m,{"aria-label":"Go to previous page",size:"default",className:(0,l.cn)("gap-1 pl-2.5",e),...r,children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Previous"})]});f.displayName="PaginationPrevious";let h=({className:e,...r})=>(0,t.jsxs)(m,{"aria-label":"Go to next page",size:"default",className:(0,l.cn)("gap-1 pr-2.5",e),...r,children:[(0,t.jsx)("span",{children:"Next"}),(0,t.jsx)(n.A,{className:"h-4 w-4"})]});h.displayName="PaginationNext";let x=({className:e,...r})=>(0,t.jsxs)("span",{"aria-hidden":!0,className:(0,l.cn)("flex h-9 w-9 items-center justify-center",e),...r,children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"More pages"})]});x.displayName="PaginationEllipsis"},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88081:(e,r,s)=>{Promise.resolve().then(s.bind(s,64620)),Promise.resolve().then(s.bind(s,92913)),Promise.resolve().then(s.bind(s,0)),Promise.resolve().then(s.bind(s,78723)),Promise.resolve().then(s.bind(s,27531)),Promise.resolve().then(s.bind(s,3487))},91645:e=>{"use strict";e.exports=require("net")},93661:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")},97809:(e,r,s)=>{Promise.resolve().then(s.bind(s,9266)),Promise.resolve().then(s.bind(s,33755)),Promise.resolve().then(s.bind(s,91114)),Promise.resolve().then(s.bind(s,92401)),Promise.resolve().then(s.bind(s,81469)),Promise.resolve().then(s.bind(s,44533))}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,4017,8072,3037,3739,9538,5918,7817,6969,4876],()=>s(53225));module.exports=t})();