(()=>{var e={};e.id=549,e.ids=[549],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12082:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>o});var a=t(37413),s=t(32032),i=t(39916),n=t(13328),d=t(20670);let o={title:"Add New Product",robots:"noindex, nofollow"};async function l(){let e=await (0,s.createClient)(),{data:{user:r}}=await e.auth.getUser();if(!r)return(0,i.redirect)("/login?message=Authentication required");let{error:t}=await e.from("business_profiles").select("id").eq("id",r.id).single();if(t)return console.error("Error fetching business profile:",t.message),(0,i.redirect)("/dashboard/business?error=Failed to load business profile");let{data:o,error:l}=await e.from("payment_subscriptions").select("plan_id").eq("business_profile_id",r.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();l&&console.error("Error fetching subscription data:",l);let c=o?.plan_id||"free",u=(0,d.RL)(c),{count:p,error:m}=await e.from("products_services").select("id",{count:"exact",head:!0}).eq("business_id",r.id).eq("is_available",!0);m&&console.error("Error counting available products:",m.message);let{count:x,error:h}=await e.from("products_services").select("id",{count:"exact",head:!0}).eq("business_id",r.id);return h?(console.error("Error counting products:",h.message),(0,i.redirect)("/dashboard/business/products?error=Failed to count products")):(0,a.jsx)(n.default,{planLimit:u,currentCount:x||0,currentAvailableCount:p||0})}},13328:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\products\\\\add\\\\AddProductClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40741:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let l={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["products",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,12082)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/products/add/page",pathname:"/dashboard/business/products/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55586:(e,r,t)=>{"use strict";t.d(r,{default:()=>x});var a=t(60687),s=t(43210),i=t(16189),n=t(77882),d=t(52581),o=t(71057),l=t(28559),c=t(24934),u=t(41974),p=t(42933),m=t(95588);function x({planLimit:e,currentCount:r,currentAvailableCount:t}){let x=(0,i.useRouter)(),[h,b]=(0,s.useTransition)(),f={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.4}}},v=async(e,r,t,a)=>{b(async()=>{let a=new FormData;Object.entries(e).forEach(([e,r])=>{null!=r&&("boolean"==typeof r?a.append(e,r.toString()):a.append(e,String(r)))}),void 0!==t&&a.append("featuredImageIndex",String(t)),r&&r.length>0&&r.forEach((e,r)=>{e&&a.append(`productImage_${r}`,e)});try{let r=await (0,p.ki)(a);if(r.success&&r.data){if(e.variants&&e.variants.length>0){d.oR.success("Product created! Adding variants...");let t=0,a=0;for(let s of e.variants)try{let e=new FormData;e.append("product_id",r.data.id),e.append("variant_name",s.variant_name),e.append("variant_values",JSON.stringify(s.variant_values)),void 0!==s.base_price&&null!==s.base_price&&s.base_price>0&&e.append("base_price",s.base_price.toString()),void 0!==s.discounted_price&&null!==s.discounted_price&&s.discounted_price>0&&e.append("discounted_price",s.discounted_price.toString()),e.append("is_available",s.is_available?"true":"false"),e.append("featured_image_index",(s.featured_image_index??0).toString()),s._imageFiles&&s._imageFiles.length>0&&s._imageFiles.forEach((r,t)=>{r&&e.append(`images[${t}]`,r)});let i=await (0,m.y)(e);i.success?t++:(a++,console.error("Failed to create variant:",s.variant_name,i.error))}catch(e){a++,console.error("Error creating variant:",s.variant_name,e)}t>0&&d.oR.success(`Product and ${t} variant${t>1?"s":""} created successfully!`),a>0&&d.oR.warning(`Product created, but ${a} variant${a>1?"s":""} failed to create. You can add them later by editing the product.`)}else d.oR.success("Product added successfully!");x.push("/dashboard/business/products")}else{let e=r.error||"Failed to add product";e.includes("Image exceeds 15MB limit")?d.oR.error("Image too large",{description:"Please select images smaller than 15MB each"}):e.includes("Invalid file type")?d.oR.error("Invalid file type",{description:"Please select JPG, PNG, WebP, or GIF images"}):e.includes("Body exceeded")?d.oR.error("Upload size limit exceeded",{description:"Please try uploading fewer images or smaller file sizes"}):d.oR.error("Failed to add product",{description:e})}}catch(e){console.error("Error adding product:",e),d.oR.error("An unexpected error occurred")}})};return(0,a.jsxs)(n.P.div,{className:"space-y-8",variants:f,initial:"hidden",animate:"visible",children:[(0,a.jsxs)(n.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",variants:{hidden:{opacity:0,y:-20},visible:{opacity:1,y:0,transition:{delay:.1,duration:.4,type:"spring",stiffness:200,damping:20}}},children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,a.jsx)(o.A,{className:"w-5 h-5 text-primary"})}),(0,a.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,a.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Product Management"})]}),(0,a.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Add New Product"}),(0,a.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Create a new product or service to showcase in your business profile with advanced features and inventory management."})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>x.push("/dashboard/business/products"),className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"Back to Products"]}),(0,a.jsx)("div",{className:"flex flex-col gap-2",children:(0,a.jsx)("div",{className:"flex items-center justify-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 rounded-lg",children:(0,a.jsxs)("div",{className:"text-sm font-medium",children:[(0,a.jsxs)("span",{className:"text-neutral-500 dark:text-neutral-400",children:["Available:"," "]}),(0,a.jsx)("span",{className:"text-neutral-800 dark:text-neutral-200",children:t}),(0,a.jsxs)("span",{className:"text-neutral-500 dark:text-neutral-400",children:[" ","of"," "]}),(0,a.jsx)("span",{className:`${t>=e?"text-red-500 dark:text-red-400 font-bold":"text-neutral-800 dark:text-neutral-200"}`,children:e===1/0?"Unlimited":e})]})})})]})]}),(0,a.jsx)(n.P.div,{variants:f,children:(0,a.jsx)(u.A,{onSubmit:v,isSubmitting:h,isEditing:!1,planLimit:e,currentAvailableCount:t})})]})}},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71057:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85699:(e,r,t)=>{Promise.resolve().then(t.bind(t,13328))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99771:(e,r,t)=>{Promise.resolve().then(t.bind(t,55586))}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,1753,6380,5880,8567,4851,3442,2836,3064,3037,3739,9538,5265,9209,4812,9648,4685,8382],()=>t(40741));module.exports=a})();