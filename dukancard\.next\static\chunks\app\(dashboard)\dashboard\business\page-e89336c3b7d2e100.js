(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6828],{23227:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},47960:(e,s,t)=>{Promise.resolve().then(t.bind(t,59190))},59190:(e,s,t)=>{"use strict";t.d(s,{default:()=>W});var a=t(95155),r=t(12115),i=t(3096),l=t(28695),n=t(60760),c=t(85339),d=t(51154),o=t(21433),m=t(49026),u=t(97168),h=t(30400),x=t(58685),p=t(42676),g=t(43694),f=t(48021),j=t(37108),y=t(54416),N=t(23227),b=t(47924),v=t(10081),w=t(5196),k=t(27213),_=t(4516),A=t(12486),S=t(69663),P=t(44895),F=t(60823),R=t(56671),C=t(75168),E=t(22607),M=t(35695),D=t(66766),L=t(36481),z=t(53999),B=t(65617),I=t(60924),U=t(75143),O=t(50402),$=t(78266);function T(e){let{product:s,onRemove:t,formatPrice:r}=e,{attributes:i,listeners:l,setNodeRef:n,transform:c,transition:d,isDragging:o}=(0,O.gl)({id:s.id}),m={transform:$.Ks.Transform.toString(c),transition:d,opacity:o?.5:1};return(0,a.jsxs)("div",{ref:n,style:m,className:"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/50 rounded-lg border min-h-[60px] sm:min-h-[68px]",children:[(0,a.jsx)("div",{...i,...l,className:"cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground shrink-0",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})}),(0,a.jsx)("div",{className:"relative h-8 w-8 sm:h-10 sm:w-10 shrink-0 rounded-md overflow-hidden bg-background",children:s.image_url?(0,a.jsx)(D.default,{src:s.image_url,alt:s.name,fill:!0,className:"object-cover",sizes:"(max-width: 640px) 32px, 40px"}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,a.jsx)(j.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0 pr-1 sm:pr-2",children:[(0,a.jsx)("div",{className:"font-medium text-xs sm:text-sm leading-tight mb-1",children:(0,a.jsx)("span",{className:"line-clamp-1 break-words",children:s.name})}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:s.discounted_price?(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-wrap",children:[(0,a.jsx)("span",{className:"text-primary font-medium",children:r(s.discounted_price)}),(0,a.jsx)("span",{className:"line-through text-xs",children:r(s.base_price)})]}):(0,a.jsx)("span",{className:"font-medium",children:r(s.base_price)})})]}),(0,a.jsx)(u.$,{variant:"ghost",size:"icon",className:"h-6 w-6 sm:h-8 sm:w-8 shrink-0 hover:bg-destructive/10 hover:text-destructive",onClick:()=>t(s.id),children:(0,a.jsx)(y.A,{className:"h-3 w-3 sm:h-4 sm:w-4"})})]})}function q(e){let{businessName:s,onPostCreated:i}=e,[c,o]=(0,r.useState)(!1),[m,h]=(0,r.useState)(""),[x,p]=(0,r.useState)(null),[g,f]=(0,r.useState)(!1),[$,q]=(0,r.useState)(null),[W,H]=(0,r.useState)(s||""),[K,G]=(0,r.useState)(null),[V,J]=(0,r.useState)(null),[X,Z]=(0,r.useState)(null),[Q,Y]=(0,r.useState)([]),[ee,es]=(0,r.useState)(!1),[et,ea]=(0,r.useState)(""),[er,ei]=(0,r.useState)(!1),[el,en]=(0,r.useState)([]),[ec,ed]=(0,r.useState)([]),[eo,em]=(0,r.useState)(!1),eu=(0,r.useRef)(null),eh=(0,r.useRef)(null),ex=(0,M.useRouter)(),ep=(0,U.FR)((0,U.MS)(U.AN),(0,U.MS)(U.uN,{coordinateGetter:O.JR})),eg=m.length,ef=eg>2e3;(0,r.useEffect)(()=>{(async()=>{let e=(0,C.U)(),{data:{user:t}}=await e.auth.getUser();if(t){let{data:a}=await e.from("business_profiles").select("business_name, logo_url").eq("id",t.id).single();a&&(H(a.business_name||s||"Business"),q(a.logo_url))}})()},[s]),(0,r.useEffect)(()=>{eu.current&&(eu.current.style.height="auto",eu.current.style.height="".concat(eu.current.scrollHeight,"px"))},[m]),(0,r.useEffect)(()=>{c&&eu.current&&eu.current.focus()},[c]);let ej=()=>{G(null),V&&URL.revokeObjectURL(V),J(null),p(null),Z(null)},ey=e=>{let s=e.match(/(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^\s]*)?)/i);return s?s[1]:null},eN=e=>{h(e);let s=ey(e);s&&s!==X?(Z(s),K||p(s)):!s&&X&&(Z(null),K||p(null))},eb=(0,r.useCallback)(async()=>{if(0===Q.length)return void ed([]);ei(!0);try{let e=await (0,B.E)(Q);if(e.success&&e.data){let s=Q.map(s=>{var t;return null==(t=e.data)?void 0:t.find(e=>e.id===s)}).filter(Boolean);ed(s)}else console.error("Error loading selected products:",e.error),ed([])}catch(e){console.error("Error loading selected products:",e),ed([])}finally{ei(!1)}},[Q]),ev=async e=>{ei(!0),em(!1);try{let s=await (0,I.H)(e);s.success&&s.data?en(s.data):(console.error("Error searching products:",s.error),en([]))}catch(e){console.error("Error searching products:",e),en([])}finally{ei(!1),em(!0)}},ew=e=>{let s;if(Q.includes(e.id))s=Q.filter(s=>s!==e.id),ed(s=>s.filter(s=>s.id!==e.id));else{if(Q.length>=5)return;s=[...Q,e.id],ed(s=>[...s,e])}Y(s)},ek=e=>{let s=Q.filter(s=>s!==e);ed(s=>s.filter(s=>s.id!==e)),Y(s)},e_=e=>null===e?"N/A":"₹".concat(e.toLocaleString("en-IN")),eA=async()=>{let e=(0,C.U)(),{data:{user:s}}=await e.auth.getUser();if(!s)return R.oR.error("Please log in to continue"),!1;let{data:t,error:a}=await e.from("business_profiles").select("business_name, pincode, city, state, locality").eq("id",s.id).single();return a?(R.oR.error("Failed to check business profile"),!1):!!(null==t?void 0:t.business_name)&&""!==t.business_name.trim()||(R.oR.error("Please complete your business name in your profile"),ex.push("/dashboard/business/profile"),!1)},eS=async()=>{if(!m.trim()&&!V&&!X)return void R.oR.error("Please add some content or an image");if(ef)return void R.oR.error("Post content is too long");f(!0);try{let e;if(!await eA())return void f(!1);if(K&&!x){let{compressImageUltraAggressiveClient:s}=await t.e(196).then(t.bind(t,90196)),a=await s(K,{maxDimension:1200,targetSizeKB:100}),r=new File([a.blob],K.name,{type:a.blob.type}),i=await (0,E.pD)({content:m.trim(),image_url:null,product_ids:Q,mentioned_business_ids:[]});if(i.success&&i.data){let s=i.data;try{let{uploadBusinessPostImage:a}=await t.e(6346).then(t.bind(t,86346)),i=new FormData;i.append("imageFile",r);let l=await a(i,s.id,s.created_at);if(l.success&&l.url)e=await (0,E.gg)(s.id,{content:m.trim(),image_url:l.url,product_ids:Q,mentioned_business_ids:[]});else{console.error("Image upload failed:",l.error);try{let{deletePost:e}=await Promise.resolve().then(t.bind(t,22607));await e(s.id),console.log("Rolled back post creation due to image upload failure")}catch(e){console.error("Failed to rollback post creation:",e)}R.oR.error("Failed to upload image. Please try again."),f(!1);return}}catch(e){console.error("Image upload error:",e);try{let{deletePost:e}=await Promise.resolve().then(t.bind(t,22607));await e(s.id),console.log("Rolled back post creation due to image upload error")}catch(e){console.error("Failed to rollback post creation:",e)}R.oR.error("Failed to upload image. Please try again."),f(!1);return}}else e=i}else e=await (0,E.pD)({content:m.trim(),image_url:x,product_ids:Q,mentioned_business_ids:[]});e.success?(R.oR.success("Post created successfully!"),h(""),p(null),ej(),o(!1),Y([]),es(!1),null==i||i()):R.oR.error(e.error||"Failed to create post")}catch(e){console.error("Error creating post:",e),R.oR.error("Failed to create post")}finally{f(!1)}};return(0,r.useEffect)(()=>{Q.length>0&&eb()},[Q,eb]),(0,r.useEffect)(()=>()=>{eh.current&&clearTimeout(eh.current)},[]),(0,a.jsxs)(l.P.div,{layout:!0,className:"bg-white dark:bg-black overflow-hidden md:border md:border-gray-200 md:dark:border-gray-700 md:rounded-xl md:shadow-sm",children:[(0,a.jsx)(n.N,{mode:"wait",children:!c&&(0,a.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer",onClick:()=>{o(!0)},children:[(0,a.jsxs)(S.eu,{className:"w-10 h-10 ring-2 ring-[#D4AF37]/30",children:[(0,a.jsx)(S.BK,{src:$||void 0,alt:W}),(0,a.jsx)(S.q5,{className:"bg-[#D4AF37] text-white text-sm font-medium",children:(0,a.jsx)(N.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"flex-1 bg-gray-50 dark:bg-gray-800 rounded-full px-4 py-3 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:["What's happening at ",W,"?"]})]})},"collapsed")}),(0,a.jsx)(n.N,{mode:"wait",children:c&&(0,a.jsxs)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(S.eu,{className:"w-10 h-10 ring-2 ring-[#D4AF37]/30",children:[(0,a.jsx)(S.BK,{src:$||void 0,alt:W}),(0,a.jsx)(S.q5,{className:"bg-[#D4AF37] text-white text-sm font-medium",children:(0,a.jsx)(N.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100 text-sm",children:W}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Business post"})]})]}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>{m.trim()||x||(o(!1),Y([]),es(!1))},className:"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsx)(L.A,{})}),(0,a.jsx)("textarea",{ref:eu,value:m,onChange:e=>eN(e.target.value),placeholder:"What's happening at ".concat(W,"?"),className:"w-full resize-none border-none outline-none text-lg placeholder-gray-500 dark:placeholder-gray-400 bg-transparent text-gray-900 dark:text-gray-100 min-h-[80px]",style:{maxHeight:"300px"}}),eg>0&&(0,a.jsxs)("div",{className:"text-xs mt-2 text-right ".concat(ef?"text-red-500":"text-gray-400"),children:[eg,"/",2e3]})]}),(0,a.jsx)(n.N,{children:(V||X)&&(0,a.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"px-4 pb-4",children:(0,a.jsxs)("div",{className:"relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700",children:[(0,a.jsx)(D.default,{src:V||X||"",alt:"Post image preview",width:500,height:300,className:"w-full h-auto max-h-96 object-cover",onError:ej}),(0,a.jsx)("button",{onClick:ej,className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1.5 hover:bg-opacity-70 transition-all",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),X&&!V&&(0,a.jsx)("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded",children:"Auto-detected image"})]})})}),(0,a.jsx)(n.N,{children:ee&&(0,a.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"px-4 pb-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(F.AM,{open:et.length>=2,onOpenChange:e=>{e||(ea(""),en([]),em(!1))},children:[(0,a.jsx)(F.Wv,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"outline",role:"combobox",className:"w-full justify-between h-auto min-h-[40px] px-3 py-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-left text-muted-foreground",children:"Search and add products..."})]}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,a.jsx)(F.hl,{className:"p-0",align:"start",sideOffset:4,style:{width:"var(--radix-popover-trigger-width)"},children:(0,a.jsxs)(P.uB,{children:[(0,a.jsx)(P.G7,{placeholder:"Search your products...",value:et,onValueChange:e=>{if(ea(e),eh.current&&clearTimeout(eh.current),e.length<2){en([]),em(!1);return}eh.current=setTimeout(()=>{ev(e)},300)},className:"h-9 border-0 focus:ring-0 focus:ring-offset-0"}),(0,a.jsxs)(P.oI,{className:"max-h-[300px]",children:[er&&(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-6 w-6 animate-spin text-[#D4AF37]"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Searching products..."})]})}),!er&&0===el.length&&(0,a.jsx)(P.xL,{children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:et.length<2?"Type at least 2 characters to search":eo?"No products found":""}),et.length>=2&&eo&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground mt-1",children:"Try a different search term"})]})}),!er&&el.length>0&&(0,a.jsx)(P.L$,{children:el.map(e=>(0,a.jsxs)(P.h_,{value:e.slug||e.id,onSelect:()=>{(Q.length<5||Q.includes(e.id))&&(ew(e),ea(""),en([]),em(!1))},disabled:Q.length>=5&&!Q.includes(e.id),className:(0,z.cn)("flex items-center gap-3 p-3 cursor-pointer",Q.length>=5&&!Q.includes(e.id)?"opacity-50 cursor-not-allowed":""),children:[(0,a.jsx)("div",{className:"relative h-10 w-10 shrink-0 rounded-md overflow-hidden bg-muted",children:e.image_url?(0,a.jsx)(D.default,{src:e.image_url,alt:e.name,fill:!0,className:"object-cover",sizes:"40px"}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,a.jsx)(j.A,{className:"h-5 w-5 text-muted-foreground"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0 pr-2",children:[(0,a.jsx)("div",{className:"font-medium text-sm truncate mb-1",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.discounted_price?(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-wrap",children:[(0,a.jsx)("span",{className:"text-[#D4AF37] font-medium",children:e_(e.discounted_price)}),(0,a.jsx)("span",{className:"line-through text-xs",children:e_(e.base_price)})]}):(0,a.jsx)("span",{className:"font-medium",children:e_(e.base_price)})})]}),(0,a.jsx)(w.A,{className:(0,z.cn)("ml-auto h-4 w-4",Q.includes(e.id)?"opacity-100 text-[#D4AF37]":"opacity-0")})]},e.id))})]})]})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[er&&Q.length>0&&0===ec.length&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Loading Selected Products..."})]}),(0,a.jsx)("div",{className:"flex justify-center py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-6 w-6 animate-spin text-[#D4AF37]"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Fetching product details..."})]})})]}),ec.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Selected Products"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground font-normal",children:"(Drag to reorder)"})]}),(0,a.jsx)(U.Mp,{sensors:ep,collisionDetection:U.fp,onDragEnd:e=>{let{active:s,over:t}=e;if(s.id!==(null==t?void 0:t.id)){let e=ec.findIndex(e=>e.id===s.id),a=ec.findIndex(e=>e.id===(null==t?void 0:t.id));if(-1!==e&&-1!==a){let s=(0,O.be)(ec,e,a);ed(s),Y(s.map(e=>e.id))}}},children:(0,a.jsx)(O.gB,{items:ec.map(e=>e.id),strategy:O._G,children:(0,a.jsx)("div",{className:"grid gap-2",children:ec.map(e=>(0,a.jsx)(T,{product:e,onRemove:ek,formatPrice:e_},e.id))})})})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:0===Q.length?"No products selected":"".concat(Q.length," product").concat(1!==Q.length?"s":""," selected")}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsxs)("span",{className:(0,z.cn)("font-medium",Q.length>=5?"text-destructive":"text-muted-foreground"),children:[Q.length,"/5"]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"max"})]})]}),Q.length>=5&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded-md",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 shrink-0"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Maximum limit of 5 products reached"})]})]})]})})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];return t?t.type.startsWith("image/")?t.size>5242880?void R.oR.error("Image size must be less than 5MB"):void(G(t),J(URL.createObjectURL(t)),p(null)):void R.oR.error("Please select an image file"):void 0},className:"hidden",id:"business-image-upload"}),(0,a.jsx)("label",{htmlFor:"business-image-upload",children:(0,a.jsx)(u.$,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-[#D4AF37] dark:text-gray-400 dark:hover:text-[#D4AF37] h-9 px-3",asChild:!0,children:(0,a.jsxs)("span",{children:[(0,a.jsx)(k.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Photo"})]})})}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{es(!ee)},className:"text-gray-500 hover:text-purple-500 dark:text-gray-400 dark:hover:text-purple-400 h-9 px-3",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Products"})]}),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 h-9 px-3",disabled:!0,children:[(0,a.jsx)(_.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Location"})]})]}),(0,a.jsx)(u.$,{onClick:eS,disabled:g||!m.trim()&&!V&&!X||ef,className:"bg-[#D4AF37] hover:bg-[#B8941F] text-white px-6 py-2 rounded-full font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Posting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Post"]})})]})]},"expanded")})]})}function W(e){let{initialPosts:s,initialTotalCount:t,initialHasMore:f,initialFilter:j="smart",citySlug:y,stateSlug:N,localitySlug:b,pincode:v,businessName:w="Business Owner"}=e,[k,_]=(0,r.useState)(s),[A,S]=(0,r.useState)(t),[P,F]=(0,r.useState)(f),[R,C]=(0,r.useState)(1),[E,M]=(0,r.useState)(!1),[D,L]=(0,r.useState)(j),{ref:z,inView:B}=(0,i.Wx)(),I=(0,r.useCallback)(async()=>{if(P&&!E){M(!0);try{var e;let s=R+1,t=await (0,o._)({filter:D,page:s,city_slug:y,state_slug:N,locality_slug:b,pincode:v});t.success&&(null==(e=t.data)?void 0:e.items)&&(_(e=>{let s=new Set(e.map(e=>e.id)),a=t.data.items.filter(e=>!s.has(e.id));return[...e,...a]}),F(t.data.hasMore||!1),S(t.data.totalCount||0),C(s))}catch(e){console.error("Error loading more posts:",e)}finally{M(!1)}}},[P,E,R,D,y,N,b,v]);(0,r.useEffect)(()=>{B&&P&&!E&&I()},[B,P,E,I]);let U=async e=>{if(e!==D){M(!0),L(e),_([]);try{let s=await (0,o._)({filter:e,page:1,city_slug:y,state_slug:N,locality_slug:b,pincode:v});s.success&&s.data&&(_(s.data.items),F(s.data.hasMore),S(s.data.totalCount),C(1))}catch(e){console.error("Error changing filter:",e)}finally{M(!1)}}},O=async()=>{try{var e;let s=await (0,o._)({filter:D,page:1,city_slug:y,state_slug:N,locality_slug:b,pincode:v});s.success&&(null==(e=s.data)?void 0:e.items)&&(_(s.data.items),F(s.data.hasMore||!1),S(s.data.totalCount||0),C(1))}catch(e){console.error("Error refreshing feed:",e)}},$=(e,s)=>{_(t=>t.map(t=>t.id===e?{...t,content:s}:t))},T=e=>{_(s=>s.filter(s=>s.id!==e)),S(e=>Math.max(0,e-1))},W=(e,s)=>{_(t=>t.map(t=>t.id===e?{...t,product_ids:s}:t))};return(0,a.jsxs)(g.A,{children:[(0,a.jsx)(p.A,{activeFilter:D,onFilterChange:U,isLoading:E}),(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:(0,a.jsx)(q,{businessName:w,onPostCreated:O})}),(0,a.jsx)(n.N,{mode:"wait",children:0!==k.length||E?(0,a.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"space-y-0",children:[E&&0===k.length&&(0,a.jsx)(a.Fragment,{children:Array.from({length:10}).map((e,s)=>(0,a.jsx)(x.default,{index:s,showImage:Math.random()>.3,showProducts:Math.random()>.7},"skeleton-".concat(s)))}),k.map((e,s)=>(0,a.jsx)(h.A,{post:e,index:s,onPostUpdate:$,onPostDelete:T,onProductsUpdate:W},e.id)),P&&(0,a.jsx)("div",{ref:z,className:"flex justify-center items-center py-8",children:E&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-neutral-500",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 animate-spin"}),(0,a.jsx)("span",{className:"text-sm",children:"Loading more posts..."})]})}),P&&!E&&(0,a.jsx)("div",{className:"flex justify-center mt-8 mb-4",children:(0,a.jsx)(u.$,{variant:"outline",onClick:I,disabled:E,className:"bg-white dark:bg-black border-neutral-200 dark:border-neutral-700",children:"Load More Posts"})})]},"posts-list"):(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:(0,a.jsxs)(m.Fc,{className:"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(m.XL,{children:"No posts found"}),(0,a.jsx)(m.TN,{children:(()=>{switch(D){case"smart":return"No posts available in your smart feed. Try subscribing to businesses or check other filters.";case"subscribed":return"Subscribe to businesses to see their posts here.";case"locality":return"No posts from your locality yet.";case"pincode":return"No posts from your pincode yet.";case"city":return"No posts from your city yet.";case"state":return"No posts from your state yet.";default:return"No posts available at the moment."}})()})]})},"empty-state")})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,8695,6874,2290,6671,375,5152,7665,1884,67,6215,6766,106,4081,8830,62,8703,356,4203,8441,1684,7358],()=>s(47960)),_N_E=e.O()}]);