(()=>{var e={};e.id=3833,e.ids=[3833],e.modules={1303:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6033:(e,r,a)=>{"use strict";a.d(r,{G:()=>o});var t=a(67218);a(79130);var s=a(32032),i=a(62351),n=a(52425),l=a(36905);async function o(e,r){let a,t,o=await (0,s.createClient)(),{data:{user:d},error:c}=await o.auth.getUser();if(c||!d)return{success:!1,error:"User not authenticated."};let u={},m=[],p=0,g=[];for(let[e,a]of r.entries())if(e.startsWith("productImage_")){let r=parseInt(e.split("_")[1],10);if(a instanceof File&&a.size>0){for(;m.length<=r;)m.push(null);m[r]=a}}else if("featuredImageIndex"===e)p=parseInt(a,10)||0;else if("removedImageIndices"===e)try{let e=JSON.parse(a);Array.isArray(e)&&e.forEach(e=>{"number"==typeof e&&g.push(e)})}catch(e){console.error("Error parsing removedImageIndices:",e)}else u[e]=a;u.product_type&&"string"!=typeof u.product_type&&(u.product_type=String(u.product_type)),u.base_price&&(u.base_price=Number(u.base_price)),u.discounted_price&&(u.discounted_price=Number(u.discounted_price)),u.is_available="true"===u.is_available||"on"===u.is_available;let f=n.w8.safeParse(u);if(!f.success){console.error("Update Product Validation Error:",f.error.flatten().fieldErrors);let e=Object.entries(f.error.flatten().fieldErrors).map(([e,r])=>`${e}: ${Array.isArray(r)?r.join(", "):r}`).join("; ");return{success:!1,error:`Invalid data: ${e}`}}let x={...f.data};x.updated_at=new Date().toISOString(),delete x.id,delete x.business_id,delete x.created_at,delete x.image_url;let{data:h,error:b}=await o.from("products_services").select("image_url, images, featured_image_index, name").eq("id",e).eq("business_id",d.id).single();if(b)return{success:!1,error:`Failed to fetch current product data: ${b.message}`};if(f.data.name||h.name,m.length>0||g.length>0){let r=h?.images||[],a=await (0,l.$S)(d.id,e,m,r,g);if(a.error)return{success:!1,error:`Failed to update images: ${a.error}`};if(Array.isArray(t=a.urls)&&t.length>0){let e=Math.min(p,t.length-1);x.image_url=t[e],x.images=t,x.featured_image_index=e}else x.image_url=null,x.images=[],x.featured_image_index=0}try{let{data:r,error:a}=await o.from("products_services").update(x).eq("id",e).eq("business_id",d.id).select("id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug").single();if(a||!r){if(console.error(`Failed to update product ${e}:`,a),a?.message?.includes("Cannot make product available")&&a?.message?.includes("reached the limit"))return{success:!1,error:a.message};if(a?.code==="PGRST116")return{success:!1,error:"Product/Service not found or you don't have permission to edit it."};return{success:!1,error:`Failed to update product/service: ${a?.message}`}}return(0,i.revalidatePath)("/dashboard/business/products"),{success:!0,data:r}}catch(r){return console.error(`Unexpected error updating product ${e}:`,r),{success:!1,error:`Unexpected error updating product: ${r instanceof Error?r.message:String(r)}`}}}(0,a(17478).D)([o]),(0,t.A)(o,"60628b2893a8cf908260d34968479634cae65b8c58",null)},8126:(e,r,a)=>{"use strict";a.d(r,{Lt:()=>U,Rx:()=>L,Zr:()=>M,EO:()=>R,$v:()=>O,ck:()=>F,wd:()=>q,r7:()=>T});var t=a(60687),s=a(43210),i=a(11273),n=a(98599),l=a(10991),o=a(70569),d=Symbol("radix.slottable"),c="AlertDialog",[u,m]=(0,i.A)(c,[l.Hs]),p=(0,l.Hs)(),g=e=>{let{__scopeAlertDialog:r,...a}=e,s=p(r);return(0,t.jsx)(l.bL,{...s,...a,modal:!0})};g.displayName=c,s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=p(a);return(0,t.jsx)(l.l9,{...i,...s,ref:r})}).displayName="AlertDialogTrigger";var f=e=>{let{__scopeAlertDialog:r,...a}=e,s=p(r);return(0,t.jsx)(l.ZL,{...s,...a})};f.displayName="AlertDialogPortal";var x=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=p(a);return(0,t.jsx)(l.hJ,{...i,...s,ref:r})});x.displayName="AlertDialogOverlay";var h="AlertDialogContent",[b,v]=u(h),_=function(e){let r=({children:e})=>(0,t.jsx)(t.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=d,r}("AlertDialogContent"),y=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,children:i,...d}=e,c=p(a),u=s.useRef(null),m=(0,n.s)(r,u),g=s.useRef(null);return(0,t.jsx)(l.G$,{contentName:h,titleName:j,docsSlug:"alert-dialog",children:(0,t.jsx)(b,{scope:a,cancelRef:g,children:(0,t.jsxs)(l.UC,{role:"alertdialog",...c,...d,ref:m,onOpenAutoFocus:(0,o.m)(d.onOpenAutoFocus,e=>{e.preventDefault(),g.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,t.jsx)(_,{children:i}),(0,t.jsx)(E,{contentRef:u})]})})})});y.displayName=h;var j="AlertDialogTitle",w=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=p(a);return(0,t.jsx)(l.hE,{...i,...s,ref:r})});w.displayName=j;var N="AlertDialogDescription",k=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=p(a);return(0,t.jsx)(l.VY,{...i,...s,ref:r})});k.displayName=N;var A=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,i=p(a);return(0,t.jsx)(l.bm,{...i,...s,ref:r})});A.displayName="AlertDialogAction";var $="AlertDialogCancel",P=s.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...s}=e,{cancelRef:i}=v($,a),o=p(a),d=(0,n.s)(r,i);return(0,t.jsx)(l.bm,{...o,...s,ref:d})});P.displayName=$;var E=({contentRef:e})=>{let r=`\`${h}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${h}\` by passing a \`${N}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${h}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return s.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(r)},[r,e]),null},S=a(96241),I=a(24934);function U({...e}){return(0,t.jsx)(g,{"data-slot":"alert-dialog",...e})}function C({...e}){return(0,t.jsx)(f,{"data-slot":"alert-dialog-portal",...e})}function D({className:e,...r}){return(0,t.jsx)(x,{"data-slot":"alert-dialog-overlay",className:(0,S.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...r})}function R({className:e,...r}){return(0,t.jsxs)(C,{children:[(0,t.jsx)(D,{}),(0,t.jsx)(y,{"data-slot":"alert-dialog-content",className:(0,S.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r})]})}function q({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,S.cn)("flex flex-col gap-2 text-center sm:text-left",e),...r})}function F({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,S.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...r})}function T({className:e,...r}){return(0,t.jsx)(w,{"data-slot":"alert-dialog-title",className:(0,S.cn)("text-lg font-semibold",e),...r})}function O({className:e,...r}){return(0,t.jsx)(k,{"data-slot":"alert-dialog-description",className:(0,S.cn)("text-muted-foreground text-sm",e),...r})}function L({className:e,...r}){return(0,t.jsx)(A,{className:(0,S.cn)((0,I.r)(),e),...r})}function M({className:e,...r}){return(0,t.jsx)(P,{className:(0,S.cn)((0,I.r)({variant:"outline"}),e),...r})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13673:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>c,metadata:()=>d});var t=a(37413),s=a(32032),i=a(39916);a(52425);var n=a(67777);a(69511),a(6033),a(82399),a(36905),a(52109),a(66591),a(89461),a(33700);var l=a(20670),o=a(15068);let d={title:"Manage Products",robots:"noindex, nofollow"};async function c(){let e=await (0,s.createClient)(),{data:{user:r},error:a}=await e.auth.getUser();(a||!r)&&(0,i.redirect)("/login");let d=await (0,n.Y)(1,10),{data:c,error:u}=await e.from("payment_subscriptions").select("plan_id").eq("business_profile_id",r.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();u&&console.error("Error fetching subscription for plan limit:",u);let m=c?.plan_id||"free",p=(0,l.RL)(m);return(0,t.jsx)(o.default,{initialData:d.data??[],initialCount:d.count??0,planLimit:p,error:d.error})}},14719:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},15068:(e,r,a)=>{"use strict";a.d(r,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\products\\\\components\\\\ProductsPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30468:(e,r,a)=>{"use strict";a.d(r,{CG:()=>t,SC:()=>s,cZ:()=>i});let t={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},s={BUSINESS:"business",CUSTOMERS:"customers"},i={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},33700:(e,r,a)=>{"use strict";a.d(r,{K6:()=>i,gL:()=>l,nC:()=>n,pU:()=>o,qU:()=>d});var t=a(67218);a(79130);var s=a(32032);async function i(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};try{let{data:t,error:s}=await r.rpc("get_product_with_variants",{product_uuid:e});if(s)return console.error("Error calling get_product_with_variants:",s),{success:!1,error:"Failed to fetch product data."};if(!t||0===t.length)return{success:!1,error:"Product not found."};let i=t[0],{data:n,error:l}=await r.from("products_services").select("business_id").eq("id",e).eq("business_id",a.id).single();if(l||!n)return{success:!1,error:"Product not found or access denied."};let o={id:i.product_id,business_id:a.id,product_type:"physical",name:i.product_name,description:i.product_description,base_price:i.product_base_price,discounted_price:i.product_discounted_price,is_available:i.product_is_available,images:i.product_images||[],featured_image_index:i.product_featured_image_index||0,slug:void 0,created_at:i.product_created_at||new Date().toISOString(),updated_at:i.product_updated_at||new Date().toISOString(),variant_count:Number(i.variant_count)||0,variants:Array.isArray(i.variants)?i.variants:[]};return{success:!0,data:o}}catch(e){return console.error("Unexpected error in getProductWithVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function n(e){let r=await (0,s.createClient)();try{let{data:a,error:t}=await r.rpc("get_available_product_variants",{product_uuid:e});if(t)return console.error("Error calling get_available_product_variants:",t),{success:!1,error:"Failed to fetch variant data."};return{success:!0,data:a||[]}}catch(e){return console.error("Unexpected error in getAvailableProductVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function l(e,r={}){let a=await (0,s.createClient)(),{data:{user:t},error:i}=await a.auth.getUser();if(i||!t)return{success:!1,error:"User not authenticated."};try{let{data:s,error:i}=await a.from("products_services").select("business_id").eq("id",e).eq("business_id",t.id).single();if(i||!s)return{success:!1,error:"Product not found or access denied."};let n=a.from("product_variants").select("*",{count:"exact"}).eq("product_id",e);switch(!r.includeUnavailable&&(n=n.eq("is_available",!0)),r.sortBy){case"created_asc":n=n.order("created_at",{ascending:!0});break;case"created_desc":default:n=n.order("created_at",{ascending:!1});break;case"name_asc":n=n.order("variant_name",{ascending:!0});break;case"name_desc":n=n.order("variant_name",{ascending:!1});break;case"price_asc":n=n.order("base_price",{ascending:!0,nullsFirst:!1});break;case"price_desc":n=n.order("base_price",{ascending:!1,nullsFirst:!0})}r.limit&&(n=n.limit(r.limit)),r.offset&&(n=n.range(r.offset,r.offset+(r.limit||10)-1));let{data:l,error:o,count:d}=await n;if(o)return console.error("Error fetching variants:",o),{success:!1,error:"Failed to fetch variants."};let c=l?.map(e=>({...e,variant_values:"string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values}))||[];return{success:!0,data:c,count:d||0}}catch(e){return console.error("Unexpected error in getProductVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function o(){let e=await (0,s.createClient)(),{data:{user:r},error:a}=await e.auth.getUser();if(a||!r)return{success:!1,error:"User not authenticated."};try{let{data:a,error:t}=await e.rpc("get_business_variant_stats",{business_uuid:r.id});if(t)return console.error("Error calling get_business_variant_stats:",t),{success:!1,error:"Failed to fetch variant statistics."};if(!a||0===a.length)return{success:!0,data:{total_products:0,products_with_variants:0,total_variants:0,available_variants:0}};let s=a[0];return{success:!0,data:{total_products:Number(s.total_products)||0,products_with_variants:Number(s.products_with_variants)||0,total_variants:Number(s.total_variants)||0,available_variants:Number(s.available_variants)||0}}}catch(e){return console.error("Unexpected error in getBusinessVariantStats:",e),{success:!1,error:"An unexpected error occurred."}}}async function d(e,r,a){let t=await (0,s.createClient)(),{data:{user:i},error:n}=await t.auth.getUser();if(n||!i)return{success:!1,error:"User not authenticated."};try{let{data:s,error:n}=await t.from("products_services").select("business_id").eq("id",e).eq("business_id",i.id).single();if(n||!s)return{success:!1,error:"Product not found or access denied."};let{data:l,error:o}=await t.rpc("is_variant_combination_unique",{product_uuid:e,variant_vals:r,exclude_variant_id:a||void 0});if(o)return console.error("Error calling is_variant_combination_unique:",o),{success:!1,error:"Failed to validate variant uniqueness."};return{success:!0,isUnique:l}}catch(e){return console.error("Unexpected error in checkVariantCombinationUnique:",e),{success:!1,error:"An unexpected error occurred."}}}(0,a(17478).D)([i,n,l,o,d]),(0,t.A)(i,"40e15bace5efcde3c132186e902abe40403e1fa4ff",null),(0,t.A)(n,"403a75d9205d3ac636b80de9ab8dbe80951e5d71b9",null),(0,t.A)(l,"602335d5c6ca713548cda36b584c6afe685e032e60",null),(0,t.A)(o,"00029e81b58a5a97fdfca38c6b984b28c00ce6cc90",null),(0,t.A)(d,"70ffeec93e6ac09c9ae41698e2e6418acc4707a28d",null)},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36141:(e,r,a)=>{"use strict";a.d(r,{default:()=>eM});var t=a(60687),s=a(77882),i=a(88920),n=a(43210),l=a.n(n),o=a(52581),d=a(20540),c=a.n(d),u=a(59821),m=a(5336),p=a(35071),g=a(42933);let f={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.05}}},x={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}}},h={initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1,transition:{duration:.4,ease:"easeOut"}},exit:{opacity:0,scale:.95,transition:{duration:.3,ease:"easeIn"}}},b=(0,n.createContext)(void 0);function v({children:e,initialData:r,initialCount:a,planLimit:s,initialError:i}){let[l,d]=(0,n.useState)(r),[f,x]=(0,n.useState)(a),[h,v]=(0,n.useState)(1),[_,y]=(0,n.useState)(10),[j,w]=(0,n.useState)(!1),[N,k]=(0,n.useTransition)(),[A,$]=(0,n.useState)("table"),[P,E]=(0,n.useState)(""),[S,I]=(0,n.useState)(""),[U,C]=(0,n.useState)("created_desc"),[D,R]=(0,n.useState)(!0),[q,F]=(0,n.useState)(!0),[T,O]=(0,n.useState)(null),L=Math.ceil(f/_);c()(e=>{I(e)},500);let M=(0,n.useCallback)(async e=>{w(!0);try{let r=await (0,g.Y6)(e,_,{searchTerm:S||void 0},U);r.data?(d(r.data),v(e),void 0!==r.count&&x(r.count)):r.error&&o.oR.error(`Failed to load products: ${r.error}`)}catch(e){o.oR.error("An unexpected error occurred while loading products."),console.error("Error fetching products:",e)}finally{w(!1),F(!1)}},[S,U,_]),V=async()=>{T&&k(async()=>{try{let e=await (0,g.i4)(T);e.success?(o.oR.success("Item deleted successfully."),d(e=>e.filter(e=>e.id!==T)),x(e=>e-1)):o.oR.error(e.error||"Failed to delete item.")}catch(e){o.oR.error("An unexpected error occurred during deletion."),console.error("Delete operation failed:",e)}finally{O(null)}})},z=async()=>{};return(0,t.jsx)(b.Provider,{value:{products:l,totalCount:f,currentPage:h,totalPages:L,planLimit:s,canAddMore:s===1/0||f<s,itemsPerPage:_,viewType:A,setViewType:$,isLoading:j,isPending:N,isInitialLoading:q,searchTerm:P,setSearchTerm:E,sortBy:U,setSortBy:C,deletingProductId:T,setDeletingProductId:O,goToPage:e=>{j||e<1||e>L||M(e)},setItemsPerPage:e=>{y(e)},handleAddNew:()=>{},handleDeleteConfirm:V,handleSave:z,getFilterStatusText:()=>{let e=[];return S&&e.push(`Search: "${S}"`),e.length>0?` | ${e.join(", ")}`:""},getProductStatusBadge:e=>e?(0,t.jsxs)(u.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",children:[(0,t.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Available"]}):(0,t.jsxs)(u.E,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",children:[(0,t.jsx)(p.A,{className:"w-3 h-3 mr-1"}),"Unavailable"]})},children:e})}function _(){let e=(0,n.useContext)(b);if(void 0===e)throw Error("useProducts must be used within a ProductsProvider");return e}var y=a(62688);let j=(0,y.A)("Package2",[["path",{d:"M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z",key:"1ront0"}],["path",{d:"m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9",key:"19h2x1"}],["path",{d:"M12 3v6",key:"1holv5"}]]);var w=a(1303),N=a(85814),k=a.n(N),A=a(24934);function $(){let{canAddMore:e,isPending:r,isLoading:a}=_();return(0,t.jsxs)(s.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",variants:x,children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,t.jsx)(j,{className:"w-5 h-5 text-primary"})}),(0,t.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,t.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Inventory Management"})]}),(0,t.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Products & Services"}),(0,t.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Manage your complete product catalog with advanced filtering, bulk operations, and real-time inventory tracking."})]}),(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsx)(k(),{href:"/dashboard/business/products/add",children:(0,t.jsxs)(A.$,{disabled:!e||r||a,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium",size:"lg",children:[(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Add New Product"]})})})]})}var P=a(99270),E=a(11860),S=a(54220),I=a(68988),U=a(39390),C=a(63974);function D(){let{searchTerm:e,setSearchTerm:r,sortBy:a,setSortBy:i,isLoading:n,isPending:l}=_();return(0,t.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 p-6 rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm",children:[(0,t.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,t.jsxs)(U.J,{htmlFor:"search-products",className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 text-primary"}),"Search Products"]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)(P.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500 transition-colors group-focus-within:text-primary"}),(0,t.jsx)(I.p,{id:"search-products",type:"text",placeholder:"Search by name, description, or SKU...",value:e,onChange:e=>r(e.target.value),className:"w-full pl-12 pr-10 h-14 rounded-xl border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 placeholder:text-neutral-400",disabled:n}),e&&(0,t.jsx)(A.$,{variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200",onClick:()=>r(""),children:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"w-full sm:w-80 space-y-3",children:[(0,t.jsxs)(U.J,{htmlFor:"sort-by",className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 text-primary"}),"Sort By"]}),(0,t.jsxs)(C.l6,{value:a,onValueChange:e=>i(e),disabled:n||l,children:[(0,t.jsx)(C.bq,{id:"sort-by",className:"w-full h-14 rounded-xl border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",children:(0,t.jsx)(C.yv,{placeholder:"Choose sorting..."})}),(0,t.jsxs)(C.gC,{className:"border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-xl bg-white dark:bg-neutral-900 p-2",children:[(0,t.jsx)(C.eb,{value:"created_desc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Newest First"}),(0,t.jsx)(C.eb,{value:"created_asc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Oldest First"}),(0,t.jsx)(C.eb,{value:"name_asc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Name (A-Z)"}),(0,t.jsx)(C.eb,{value:"name_desc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Name (Z-A)"}),(0,t.jsx)(C.eb,{value:"price_asc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Price (Low to High)"}),(0,t.jsx)(C.eb,{value:"price_desc",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Price (High to Low)"}),(0,t.jsx)(C.eb,{value:"available_first",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Available First"}),(0,t.jsx)(C.eb,{value:"unavailable_first",className:"text-sm rounded-lg focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 py-3",children:"Unavailable First"})]})]})]})]})})}var R=a(19080),q=a(14719),F=a(25541);function T(){let{products:e,totalCount:r,planLimit:a}=_(),i=e.filter(e=>e.is_available).length,n=e.filter(e=>!e.is_available).length,l=e.length>0?Math.round(i/e.length*100):0,o=[{title:"Total Products",value:r,subtitle:a===1/0?"Unlimited plan":`${r} of ${a} used`,icon:R.A,color:"primary",progress:a===1/0?15:Math.min(100,Math.round(r/a*100))},{title:"Available",value:i,subtitle:`${l}% of inventory active`,icon:q.A,color:"emerald",progress:l},{title:"Unavailable",value:n,subtitle:0===n?"All products active":`${100-l}% inactive`,icon:p.A,color:"rose",progress:100-l}];return(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:o.map((e,r)=>(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*r},className:"group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40"}),(0,t.jsxs)("div",{className:"relative p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-neutral-600 dark:text-neutral-400 tracking-wide",children:e.title}),(0,t.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-neutral-900 dark:text-neutral-100 tracking-tight",children:e.value.toLocaleString()}),"Total Products"===e.title&&(0,t.jsx)(F.A,{className:"w-4 h-4 text-primary opacity-60"})]})]}),(0,t.jsx)("div",{className:`
                flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300
                ${"primary"===e.color?"bg-primary/10 text-primary group-hover:bg-primary/15":""}
                ${"emerald"===e.color?"bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400 group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/30":""}
                ${"rose"===e.color?"bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400 group-hover:bg-rose-100 dark:group-hover:bg-rose-900/30":""}
              `,children:(0,t.jsx)(e.icon,{className:"w-6 h-6"})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400 leading-relaxed",children:e.subtitle}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center text-xs text-neutral-500 dark:text-neutral-400",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[e.progress,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-neutral-200 dark:bg-neutral-800 rounded-full h-2 overflow-hidden",children:(0,t.jsx)(s.P.div,{initial:{width:0},animate:{width:`${e.progress}%`},transition:{duration:1,delay:.2*r+.5,ease:"easeOut"},className:`
                      h-full rounded-full transition-all duration-300
                      ${"primary"===e.color?"bg-gradient-to-r from-primary to-primary/80":""}
                      ${"emerald"===e.color?"bg-gradient-to-r from-emerald-500 to-emerald-400":""}
                      ${"rose"===e.color?"bg-gradient-to-r from-rose-500 to-rose-400":""}
                    `})})]})]})]})]},e.title))})}var O=a(80462);let L=(0,y.A)("Infinity",[["path",{d:"M6 16c5 0 7-8 12-8a4 4 0 0 1 0 8c-5 0-7-8-12-8a4 4 0 1 0 0 8",key:"18ogeb"}]]),M=(0,y.A)("Table2",[["path",{d:"M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18",key:"gugj83"}]]),V=(0,y.A)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);var z=a(70569),Y=a(11273),G=a(72942),B=a(46059),J=a(3416),H=a(43),W=a(65551),Z=a(19344),K="Tabs",[X,Q]=(0,Y.A)(K,[G.RG]),ee=(0,G.RG)(),[er,ea]=X(K),et=n.forwardRef((e,r)=>{let{__scopeTabs:a,value:s,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:o,activationMode:d="automatic",...c}=e,u=(0,H.jH)(o),[m,p]=(0,W.i)({prop:s,onChange:i,defaultProp:n??"",caller:K});return(0,t.jsx)(er,{scope:a,baseId:(0,Z.B)(),value:m,onValueChange:p,orientation:l,dir:u,activationMode:d,children:(0,t.jsx)(J.sG.div,{dir:u,"data-orientation":l,...c,ref:r})})});et.displayName=K;var es="TabsList",ei=n.forwardRef((e,r)=>{let{__scopeTabs:a,loop:s=!0,...i}=e,n=ea(es,a),l=ee(a);return(0,t.jsx)(G.bL,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:s,children:(0,t.jsx)(J.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:r})})});ei.displayName=es;var en="TabsTrigger",el=n.forwardRef((e,r)=>{let{__scopeTabs:a,value:s,disabled:i=!1,...n}=e,l=ea(en,a),o=ee(a),d=ed(l.baseId,s),c=ec(l.baseId,s),u=s===l.value;return(0,t.jsx)(G.q7,{asChild:!0,...o,focusable:!i,active:u,children:(0,t.jsx)(J.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":c,"data-state":u?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...n,ref:r,onMouseDown:(0,z.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(s)}),onKeyDown:(0,z.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(s)}),onFocus:(0,z.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;u||i||!e||l.onValueChange(s)})})})});el.displayName=en;var eo="TabsContent";function ed(e,r){return`${e}-trigger-${r}`}function ec(e,r){return`${e}-content-${r}`}n.forwardRef((e,r)=>{let{__scopeTabs:a,value:s,forceMount:i,children:l,...o}=e,d=ea(eo,a),c=ed(d.baseId,s),u=ec(d.baseId,s),m=s===d.value,p=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(B.C,{present:i||m,children:({present:a})=>(0,t.jsx)(J.sG.div,{"data-state":m?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!a,id:u,tabIndex:0,...o,ref:r,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})})}).displayName=eo;var eu=a(96241);function em({className:e,...r}){return(0,t.jsx)(et,{"data-slot":"tabs",className:(0,eu.cn)("flex flex-col gap-2",e),...r})}function ep({className:e,...r}){return(0,t.jsx)(ei,{"data-slot":"tabs-list",className:(0,eu.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...r})}function eg({className:e,...r}){return(0,t.jsx)(el,{"data-slot":"tabs-trigger",className:(0,eu.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function ef(){let{viewType:e,setViewType:r,products:a,totalCount:i,itemsPerPage:n,setItemsPerPage:l,planLimit:o,getFilterStatusText:d,currentPage:c,isLoading:m}=_(),p=Math.min(c*n,i);return(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},className:"flex flex-col lg:flex-row lg:items-center justify-between gap-4 py-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3",children:[(0,t.jsx)("div",{className:"text-sm text-neutral-700 dark:text-neutral-300",children:0===a.length?(0,t.jsx)("span",{className:"font-medium",children:"No products found"}):(0,t.jsxs)("span",{children:["Showing ",(0,t.jsx)("span",{className:"font-semibold text-neutral-900 dark:text-neutral-100",children:(c-1)*n+1})," to"," ",(0,t.jsx)("span",{className:"font-semibold text-neutral-900 dark:text-neutral-100",children:p})," of"," ",(0,t.jsx)("span",{className:"font-semibold text-neutral-900 dark:text-neutral-100",children:i})," products",o!==1/0&&(0,t.jsxs)("span",{className:"text-neutral-500 dark:text-neutral-400 ml-1",children:["(limit: ",o,")"]})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[d()&&(0,t.jsxs)(u.E,{className:"bg-primary/10 text-primary border-primary/20 text-xs font-medium px-2 py-1 rounded-full",children:[(0,t.jsx)(O.A,{className:"w-3 h-3 mr-1"}),"Filtered"]}),o===1/0&&(0,t.jsxs)(u.E,{className:"bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 dark:from-purple-900/30 dark:to-pink-900/30 dark:text-purple-400 border-0 text-xs font-medium px-2 py-1 rounded-full",children:[(0,t.jsx)(L,{className:"w-3 h-3 mr-1"}),"Unlimited Plan"]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,t.jsx)(eT,{itemsPerPage:n,onItemsPerPageChange:l,isLoading:m}),(0,t.jsx)(em,{value:e,onValueChange:e=>r(e),className:"w-auto",children:(0,t.jsxs)(ep,{className:"grid w-40 grid-cols-2 bg-white dark:bg-neutral-900 border border-neutral-200/60 dark:border-neutral-800/60 rounded-xl p-1 shadow-sm",children:[(0,t.jsxs)(eg,{value:"table",className:"text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200 px-3 py-2 flex items-center justify-center gap-2",children:[(0,t.jsx)(M,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Table"})]}),(0,t.jsxs)(eg,{value:"grid",className:"text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-all duration-200 px-3 py-2 flex items-center justify-center gap-2",children:[(0,t.jsx)(V,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Grid"})]})]})})]})]})}var ex=a(14952),eh=a(93661),eb=a(63143),ev=a(88233),e_=a(96752),ey=a(55629),ej=a(30474);function ew({view:e}){let{searchTerm:r,handleAddNew:a,canAddMore:s}=_();return"table"===e?(0,t.jsxs)("div",{className:"h-32 flex flex-col items-center justify-center text-neutral-500 dark:text-neutral-400",children:[(0,t.jsx)(R.A,{className:"h-6 sm:h-8 w-6 sm:w-8 mb-2 opacity-40"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-center max-w-xs",children:r?"No products match your current filters":"You haven't added any products or services yet"}),!r&&(0,t.jsx)(A.$,{variant:"link",className:"mt-2 text-xs sm:text-sm text-primary hover:text-primary/80",onClick:a,disabled:!s,children:"Add your first item"})]}):(0,t.jsxs)("div",{className:"col-span-full h-40 flex flex-col items-center justify-center text-neutral-500 dark:text-neutral-400 border border-dashed border-neutral-200 dark:border-neutral-700 rounded-xl bg-neutral-50 dark:bg-neutral-800/50",children:[(0,t.jsx)(R.A,{className:"h-6 sm:h-8 w-6 sm:w-8 mb-2 opacity-40"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-center max-w-xs",children:r?"No products match your current filters":"You haven't added any products or services yet"}),!r&&(0,t.jsx)(A.$,{variant:"link",className:"mt-2 text-xs sm:text-sm text-primary hover:text-primary/80",onClick:a,disabled:!s,children:"Add your first item"})]})}var eN=a(8839),ek=a(6475);let eA=(0,ek.createServerReference)("40e15bace5efcde3c132186e902abe40403e1fa4ff",ek.callServer,void 0,ek.findSourceMapURL,"getProductWithVariants");function e$(){let{products:e,isLoading:r,isPending:a,deletingProductId:d,setDeletingProductId:c}=_(),[m,p]=(0,n.useState)(new Set),[g,f]=(0,n.useState)({}),[x,b]=(0,n.useState)(new Set),v=async e=>{let r=new Set(m);if(r.has(e))r.delete(e);else if(r.add(e),!g[e]){b(r=>new Set(r).add(e));try{let a=await eA(e);a.success&&a.data?f(r=>({...r,[e]:a.data?.variants||[]})):(o.oR.error("Failed to load product variants"),r.delete(e))}catch(a){console.error("Error loading variants:",a),o.oR.error("Failed to load product variants"),r.delete(e)}finally{b(r=>{let a=new Set(r);return a.delete(e),a})}}p(r)};return(0,t.jsx)(s.P.div,{variants:h,initial:"initial",animate:"animate",exit:"exit",className:"space-y-6",children:(0,t.jsx)("div",{className:"rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm",children:(0,t.jsxs)(e_.XI,{children:[(0,t.jsx)(e_.A0,{children:(0,t.jsxs)(e_.Hj,{className:"border-b border-neutral-200/60 dark:border-neutral-800/60 bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 hover:from-neutral-100/80 hover:to-neutral-150/40 dark:hover:from-neutral-700/50 dark:hover:to-neutral-800/30 transition-all duration-200",children:[(0,t.jsx)(e_.nd,{className:"w-12 text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4"}),(0,t.jsx)(e_.nd,{className:"w-20 text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Image"}),(0,t.jsx)(e_.nd,{className:"text-left text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4 px-6",children:"Product Details"}),(0,t.jsx)(e_.nd,{className:"text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Category"}),(0,t.jsx)(e_.nd,{className:"text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Variants"}),(0,t.jsx)(e_.nd,{className:"text-right text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Pricing"}),(0,t.jsx)(e_.nd,{className:"text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4",children:"Status"}),(0,t.jsx)(e_.nd,{className:"text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4 w-20",children:"Actions"})]})}),(0,t.jsxs)(e_.BF,{children:[!r&&0===e.length&&(0,t.jsx)(e_.Hj,{children:(0,t.jsx)(e_.nA,{colSpan:8,className:"h-32 text-center",children:(0,t.jsx)(ew,{view:"table"})})}),e.map((e,r)=>{let n=m.has(e.id),o=x.has(e.id),p=e.variant_count>0;return(0,t.jsxs)(l().Fragment,{children:[(0,t.jsxs)(s.P.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*r},className:`
                      group border-b border-neutral-100/60 dark:border-neutral-800/60
                      hover:bg-gradient-to-r hover:from-neutral-50/80 hover:to-neutral-100/40
                      dark:hover:from-neutral-800/50 dark:hover:to-neutral-900/30
                      ${a&&d===e.id?"opacity-50 pointer-events-none":""}
                      ${n?"bg-gradient-to-r from-primary/5 to-primary/2 dark:from-primary/10 dark:to-primary/5":""}
                      transition-all duration-300
                    `,children:[(0,t.jsx)(e_.nA,{className:"py-4 px-4",children:p?(0,t.jsx)(A.$,{variant:"ghost",size:"sm",onClick:()=>v(e.id),disabled:o,className:"h-8 w-8 p-0 rounded-lg hover:bg-primary/10 transition-colors duration-200",children:o?(0,t.jsx)(s.P.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:(0,t.jsx)(R.A,{className:"h-4 w-4 text-primary"})}):(0,t.jsx)(s.P.div,{animate:{rotate:90*!!n},transition:{duration:.3,ease:"easeInOut"},children:(0,t.jsx)(ex.A,{className:"h-4 w-4 text-neutral-600 dark:text-neutral-400"})})}):(0,t.jsx)("div",{className:"h-8 w-8"})}),(0,t.jsx)(e_.nA,{className:"py-4 px-4",children:(0,t.jsx)("div",{className:"flex justify-center",children:e.image_url?(0,t.jsx)("div",{className:"relative w-12 h-12 rounded-xl overflow-hidden border border-neutral-200/60 dark:border-neutral-700/60 shadow-sm group-hover:shadow-md transition-all duration-300",children:(0,t.jsx)(ej.default,{src:e.image_url,alt:e.name??"Product image",className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-110",width:48,height:48})}):(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-900 rounded-xl flex items-center justify-center text-neutral-400 dark:text-neutral-500 border border-neutral-200/60 dark:border-neutral-700/60",children:(0,t.jsx)(R.A,{className:"w-6 h-6"})})})}),(0,t.jsx)(e_.nA,{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-semibold text-sm text-neutral-900 dark:text-neutral-100 max-w-xs truncate",children:e.name}),e.description&&(0,t.jsx)("div",{className:"text-xs text-neutral-500 dark:text-neutral-400 max-w-xs truncate",children:e.description})]})}),(0,t.jsx)(e_.nA,{className:"py-4 px-4 text-center",children:(0,t.jsx)(u.E,{variant:"secondary",className:"capitalize text-xs font-medium bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 border-0 px-3 py-1 rounded-full",children:e.product_type})}),(0,t.jsx)(e_.nA,{className:"py-4 px-4 text-center",children:e.variant_count>0?(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-semibold text-sm text-neutral-900 dark:text-neutral-100",children:e.variant_count}),(0,t.jsxs)("div",{className:"text-xs text-neutral-500 dark:text-neutral-400",children:[e.available_variant_count," active"]})]}):(0,t.jsx)("div",{className:"text-xs text-neutral-400 dark:text-neutral-500 font-medium",children:"No variants"})}),(0,t.jsx)(e_.nA,{className:"py-4 px-4 text-right",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-semibold text-sm text-neutral-900 dark:text-neutral-100",children:e.base_price?.toLocaleString("en-IN",{style:"currency",currency:"INR"})??"—"}),e.discounted_price&&(0,t.jsx)("div",{className:"text-xs text-emerald-600 dark:text-emerald-400 font-medium",children:e.discounted_price.toLocaleString("en-IN",{style:"currency",currency:"INR"})})]})}),(0,t.jsx)(e_.nA,{className:"py-4 px-4 text-center",children:(0,t.jsx)(u.E,{variant:e.is_available?"default":"secondary",className:`
                          text-xs font-medium px-3 py-1 rounded-full border-0
                          ${e.is_available?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400":"bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400"}
                        `,children:e.is_available?"Available":"Unavailable"})}),(0,t.jsx)(e_.nA,{className:"py-4 px-4 text-center",children:(0,t.jsxs)(ey.rI,{children:[(0,t.jsx)(ey.ty,{asChild:!0,children:(0,t.jsx)(A.$,{variant:"ghost",size:"sm",disabled:a,className:"h-8 w-8 p-0 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors duration-200",children:(0,t.jsx)(eh.A,{className:"h-4 w-4 text-neutral-600 dark:text-neutral-400"})})}),(0,t.jsxs)(ey.SQ,{align:"end",className:"w-48 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm shadow-lg",children:[(0,t.jsx)(ey._2,{asChild:!0,children:(0,t.jsxs)(k(),{href:`/dashboard/business/products/edit/${e.id}`,className:"flex items-center gap-2 px-3 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)(eb.A,{className:"h-4 w-4"}),"Edit Product"]})}),(0,t.jsx)(ey._2,{asChild:!0,children:(0,t.jsxs)("button",{onClick:()=>c(e.id),disabled:a||d===e.id,className:"flex items-center gap-2 px-3 py-2 text-sm text-rose-600 dark:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20 rounded-lg transition-colors duration-200 cursor-pointer w-full text-left disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,t.jsx)(ev.A,{className:"h-4 w-4"}),"Delete Product"]})})]})]})})]}),(0,t.jsx)(i.N,{children:n&&p&&g[e.id]&&(0,t.jsx)(s.P.tr,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},className:"bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 border-b border-neutral-200/60 dark:border-neutral-800/60",children:(0,t.jsx)(e_.nA,{colSpan:8,className:"p-0",children:(0,t.jsxs)("div",{className:"p-6 border-l-4 border-primary/20",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-neutral-900 dark:text-neutral-100 mb-2",children:"Product Variants"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400",children:"Manage different variations of this product"})]}),(0,t.jsx)(eN.A,{productId:e.id,variants:g[e.id]||[],className:"border-0 bg-transparent rounded-xl",onAddVariant:()=>{window.location.href=`/dashboard/business/products/edit/${e.id}?tab=variants`},onEditVariant:r=>{window.location.href=`/dashboard/business/products/edit/${e.id}?tab=variants&variant=${r.id}`},onDeleteVariant:e=>{console.log("Delete variant:",e)},onToggleVariantAvailability:(e,r)=>{console.log("Toggle variant availability:",e,r)}})]})})},`expanded-${e.id}`)})]},e.id)})]})]})})},"table-view")}let eP=(0,y.A)("IndianRupee",[["path",{d:"M6 3h12",key:"ggurg9"}],["path",{d:"M6 8h12",key:"6g4wlu"}],["path",{d:"m6 13 8.5 8",key:"u1kupk"}],["path",{d:"M6 13h3",key:"wdp6ag"}],["path",{d:"M9 13c6.667 0 6.667-10 0-10",key:"1nkvk2"}]]);function eE(){let{products:e,isLoading:r,isPending:a,deletingProductId:i,setDeletingProductId:n}=_();return r||0!==e.length?(0,t.jsx)(s.P.div,{variants:h,initial:"initial",animate:"animate",exit:"exit",className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map((e,r)=>(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*r},className:`
            group relative rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60
            bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden
            hover:border-neutral-300/60 dark:hover:border-neutral-700/60
            hover:shadow-lg hover:shadow-neutral-200/20 dark:hover:shadow-neutral-900/20
            transition-all duration-300
            ${a&&i===e.id?"opacity-50 pointer-events-none":""}
          `,whileHover:{y:-4,transition:{duration:.2}},children:[(0,t.jsxs)("div",{className:"relative aspect-square overflow-hidden bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-800 dark:to-neutral-900",children:[e.image_url?(0,t.jsx)(ej.default,{src:e.image_url,alt:e.name??"Product image",className:"object-cover w-full h-full transition-transform duration-500 group-hover:scale-110",fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,t.jsx)(R.A,{className:"w-16 h-16 text-neutral-300 dark:text-neutral-600"})}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,t.jsx)("div",{className:"absolute top-3 right-3",children:(0,t.jsx)(u.E,{variant:e.is_available?"default":"secondary",className:`
                  text-xs font-medium px-2 py-1 rounded-full border-0 shadow-sm
                  ${e.is_available?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400":"bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400"}
                `,children:e.is_available?"Available":"Unavailable"})}),e.variant_count>0&&(0,t.jsx)("div",{className:"absolute top-3 left-3",children:(0,t.jsxs)(u.E,{className:"bg-primary/90 text-primary-foreground text-xs font-medium px-2 py-1 rounded-full border-0 shadow-sm",children:[e.variant_count," variant",e.variant_count>1?"s":""]})}),(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300",children:(0,t.jsxs)(ey.rI,{children:[(0,t.jsx)(ey.ty,{asChild:!0,children:(0,t.jsx)(A.$,{variant:"secondary",size:"sm",disabled:a,className:"bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm border border-neutral-200/60 dark:border-neutral-700/60 shadow-lg hover:shadow-xl transition-all duration-200",children:(0,t.jsx)(eh.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(ey.SQ,{align:"center",className:"w-48 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm shadow-xl",children:[(0,t.jsx)(ey._2,{asChild:!0,children:(0,t.jsxs)(k(),{href:`/dashboard/business/products/edit/${e.id}`,className:"flex items-center gap-2 px-3 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)(eb.A,{className:"h-4 w-4"}),"Edit Product"]})}),(0,t.jsx)(ey._2,{asChild:!0,children:(0,t.jsxs)("button",{onClick:()=>n(e.id),disabled:a||i===e.id,className:"flex items-center gap-2 px-3 py-2 text-sm text-rose-600 dark:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20 rounded-lg transition-colors duration-200 cursor-pointer w-full text-left disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,t.jsx)(ev.A,{className:"h-4 w-4"}),"Delete Product"]})})]})]})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight",children:e.name}),(0,t.jsx)(u.E,{variant:"secondary",className:"capitalize text-xs font-medium bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 border-0 px-2 py-1 rounded-full",children:e.product_type})]}),e.description&&(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2 leading-relaxed",children:e.description}),e.variant_count>0&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs",children:[(0,t.jsxs)(u.E,{className:"bg-primary/10 text-primary border-0 px-2 py-1 rounded-full",children:[e.variant_count," variant",e.variant_count>1?"s":""]}),(0,t.jsx)("span",{className:"text-neutral-400",children:"•"}),(0,t.jsxs)("span",{className:"text-emerald-600 dark:text-emerald-400 font-medium",children:[e.available_variant_count," active"]})]}),(0,t.jsx)("div",{className:"space-y-1",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Price"}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"font-bold text-lg text-neutral-900 dark:text-neutral-100 flex items-center",children:[(0,t.jsx)(eP,{className:"h-4 w-4 mr-1"}),e.base_price?.toLocaleString("en-IN")??"—"]}),e.discounted_price&&(0,t.jsxs)("div",{className:"text-sm text-emerald-600 dark:text-emerald-400 font-medium flex items-center justify-end",children:[(0,t.jsx)(eP,{className:"h-3 w-3 mr-1"}),e.discounted_price.toLocaleString("en-IN")]})]})]})})]})]},e.id))},"grid-view"):(0,t.jsx)(ew,{view:"grid"})}var eS=a(8126),eI=a(41862);function eU(){let{deletingProductId:e,setDeletingProductId:r,handleDeleteConfirm:a,isPending:s}=_();return(0,t.jsx)(eS.Lt,{open:!!e,onOpenChange:e=>!e&&r(null),children:(0,t.jsxs)(eS.EO,{className:"bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800 rounded-xl",children:[(0,t.jsxs)(eS.wd,{children:[(0,t.jsx)(eS.r7,{className:"text-neutral-800 dark:text-neutral-100",children:"Delete Item"}),(0,t.jsx)(eS.$v,{className:"text-neutral-500 dark:text-neutral-400",children:"Are you sure you want to delete this item? This action cannot be undone."})]}),(0,t.jsxs)(eS.ck,{children:[(0,t.jsx)(eS.Zr,{className:"border-neutral-200 dark:border-neutral-700 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800",disabled:s,children:"Cancel"}),(0,t.jsx)(eS.Rx,{className:"bg-red-600 hover:bg-red-700 text-white",onClick:e=>{e.preventDefault(),a()},disabled:s,children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eI.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete"})]})]})})}var eC=a(71463);function eD({view:e,count:r=10}){return"table"===e?(0,t.jsxs)("div",{className:"rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm",children:[(0,t.jsx)("div",{className:"border-b border-neutral-200/60 dark:border-neutral-800/60 bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-8 gap-4",children:[(0,t.jsx)(eC.E,{className:"h-4 w-8"}),(0,t.jsx)(eC.E,{className:"h-4 w-12"}),(0,t.jsx)(eC.E,{className:"h-4 w-24"}),(0,t.jsx)(eC.E,{className:"h-4 w-16"}),(0,t.jsx)(eC.E,{className:"h-4 w-16"}),(0,t.jsx)(eC.E,{className:"h-4 w-20"}),(0,t.jsx)(eC.E,{className:"h-4 w-16"}),(0,t.jsx)(eC.E,{className:"h-4 w-12"})]})}),(0,t.jsx)("div",{className:"divide-y divide-neutral-100/60 dark:divide-neutral-800/60",children:[...Array(r)].map((e,r)=>(0,t.jsx)(s.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.05*r},className:"p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-8 gap-4 items-center",children:[(0,t.jsx)(eC.E,{className:"h-8 w-8 rounded-lg"}),(0,t.jsx)(eC.E,{className:"h-12 w-12 rounded-xl"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eC.E,{className:"h-4 w-32"}),(0,t.jsx)(eC.E,{className:"h-3 w-24"})]}),(0,t.jsx)(eC.E,{className:"h-6 w-16 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(eC.E,{className:"h-4 w-8"}),(0,t.jsx)(eC.E,{className:"h-3 w-12"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(eC.E,{className:"h-4 w-16"}),(0,t.jsx)(eC.E,{className:"h-3 w-12"})]}),(0,t.jsx)(eC.E,{className:"h-6 w-20 rounded-full"}),(0,t.jsx)(eC.E,{className:"h-8 w-8 rounded-lg"})]})},r))})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[...Array(r)].map((e,r)=>(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*r},className:"rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm",children:[(0,t.jsx)(eC.E,{className:"w-full aspect-square"}),(0,t.jsxs)("div",{className:"p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eC.E,{className:"h-6 w-3/4"}),(0,t.jsx)(eC.E,{className:"h-5 w-20 rounded-full"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eC.E,{className:"h-4 w-full"}),(0,t.jsx)(eC.E,{className:"h-4 w-2/3"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eC.E,{className:"h-5 w-16 rounded-full"}),(0,t.jsx)(eC.E,{className:"h-3 w-2"}),(0,t.jsx)(eC.E,{className:"h-4 w-12"})]}),(0,t.jsx)("div",{className:"space-y-1",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(eC.E,{className:"h-4 w-10"}),(0,t.jsxs)("div",{className:"text-right space-y-1",children:[(0,t.jsx)(eC.E,{className:"h-6 w-20"}),(0,t.jsx)(eC.E,{className:"h-4 w-16"})]})]})})]})]},r))})}var eR=a(47033);function eq({currentPage:e,totalPages:r,totalItems:a,itemsPerPage:i,onPageChange:n,onItemsPerPageChange:l,isLoading:o=!1,className:d}){if(0===a)return null;let c=(e-1)*i+1,u=Math.min(e*i,a),m=(()=>{let a=[];if(r<=7)for(let e=1;e<=r;e++)a.push(e);else{a.push(1);let t=Math.max(2,e-2),s=Math.min(r-1,e+2);e<=4&&(s=Math.min(r-1,5)),e>=r-3&&(t=Math.max(2,r-4)),t>2&&a.push("ellipsis");for(let e=t;e<=s;e++)a.push(e);s<r-1&&a.push("ellipsis"),r>1&&a.push(r)}return a})();return(0,t.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:(0,eu.cn)("flex flex-col sm:flex-row items-center justify-between gap-4 py-6",d),children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-4 text-sm text-neutral-600 dark:text-neutral-400",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsxs)("span",{children:["Showing ",c," to ",u," of ",a," products"]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"whitespace-nowrap",children:"Items per page:"}),(0,t.jsxs)(C.l6,{value:i.toString(),onValueChange:e=>l(parseInt(e)),disabled:o,children:[(0,t.jsx)(C.bq,{className:"w-20 h-8 text-sm border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 focus:ring-2 focus:ring-primary/20 focus:border-primary",children:(0,t.jsx)(C.yv,{})}),(0,t.jsxs)(C.gC,{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg bg-white dark:bg-neutral-900",children:[(0,t.jsx)(C.eb,{value:"10",className:"text-sm focus:bg-primary/10 focus:text-primary",children:"10"}),(0,t.jsx)(C.eb,{value:"25",className:"text-sm focus:bg-primary/10 focus:text-primary",children:"25"}),(0,t.jsx)(C.eb,{value:"50",className:"text-sm focus:bg-primary/10 focus:text-primary",children:"50"}),(0,t.jsx)(C.eb,{value:"100",className:"text-sm focus:bg-primary/10 focus:text-primary",children:"100"})]})]})]})]}),r>1&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)(A.$,{variant:"outline",size:"sm",onClick:()=>n(e-1),disabled:1===e||o,className:"h-9 px-3 border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 disabled:opacity-50",children:[(0,t.jsx)(eR.A,{className:"h-4 w-4 mr-1"}),"Previous"]}),(0,t.jsx)("div",{className:"flex items-center gap-1 mx-2",children:m.map((r,a)=>{if("ellipsis"===r)return(0,t.jsx)("div",{className:"flex items-center justify-center w-9 h-9 text-neutral-400 dark:text-neutral-500",children:(0,t.jsx)(eh.A,{className:"h-4 w-4"})},`ellipsis-${a}`);let s=e===r;return(0,t.jsx)(A.$,{variant:s?"default":"outline",size:"sm",onClick:()=>n(r),disabled:o,className:(0,eu.cn)("h-9 w-9 p-0 text-sm",s?"bg-primary hover:bg-primary/90 text-primary-foreground border-primary shadow-sm":"border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300"),children:r},r)})}),(0,t.jsxs)(A.$,{variant:"outline",size:"sm",onClick:()=>n(e+1),disabled:e===r||o,className:"h-9 px-3 border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 disabled:opacity-50",children:["Next",(0,t.jsx)(ex.A,{className:"h-4 w-4 ml-1"})]})]})]})}var eF=a(84027);function eT({itemsPerPage:e,onItemsPerPageChange:r,isLoading:a=!1,className:i}){return(0,t.jsxs)(s.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:(0,eu.cn)("flex items-center gap-3",i),children:[(0,t.jsxs)(U.J,{htmlFor:"items-per-page",className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2 whitespace-nowrap",children:[(0,t.jsx)(eF.A,{className:"h-4 w-4 text-primary"}),"Items per page"]}),(0,t.jsxs)(C.l6,{value:e.toString(),onValueChange:e=>r(parseInt(e)),disabled:a,children:[(0,t.jsx)(C.bq,{id:"items-per-page",className:"w-24 h-9 text-sm border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",children:(0,t.jsx)(C.yv,{})}),(0,t.jsxs)(C.gC,{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg bg-white dark:bg-neutral-900",children:[(0,t.jsx)(C.eb,{value:"10",className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer",children:"10"}),(0,t.jsx)(C.eb,{value:"25",className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer",children:"25"}),(0,t.jsx)(C.eb,{value:"50",className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer",children:"50"}),(0,t.jsx)(C.eb,{value:"100",className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20 cursor-pointer",children:"100"})]})]})]})}function eO({initialData:e,initialCount:r,planLimit:a,error:s}){return(0,t.jsx)(v,{initialData:e,initialCount:r,planLimit:a,initialError:s,children:(0,t.jsx)(eL,{})})}function eL(){let{viewType:e,isLoading:r,isInitialLoading:a,currentPage:n,totalPages:l,totalCount:o,itemsPerPage:d,goToPage:c,setItemsPerPage:u,products:m}=_();return(0,t.jsxs)(s.P.div,{className:"space-y-8",initial:"hidden",animate:"visible",variants:f,children:[(0,t.jsx)(s.P.div,{variants:f,children:(0,t.jsx)($,{})}),(0,t.jsx)(s.P.div,{variants:f,children:(0,t.jsx)(T,{})}),(0,t.jsx)(s.P.div,{variants:f,children:(0,t.jsx)(D,{})}),(0,t.jsx)(s.P.div,{variants:f,children:(0,t.jsx)(ef,{})}),(0,t.jsxs)(s.P.div,{variants:f,children:[a||r?(0,t.jsx)(eD,{view:e,count:d}):(0,t.jsx)(i.N,{mode:"wait",children:"table"===e?(0,t.jsx)(e$,{},"table"):(0,t.jsx)(eE,{},"grid")}),m.length>0&&(0,t.jsx)(eq,{currentPage:n,totalPages:l,totalItems:o,itemsPerPage:d,onPageChange:c,onItemsPerPageChange:u,isLoading:r,className:"mt-8"})]}),(0,t.jsx)(eU,{})]})}function eM({initialData:e,initialCount:r,planLimit:a,error:i}){return(0,t.jsx)(s.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-6 relative",children:(0,t.jsx)(s.P.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4}}},className:"mb-6 relative z-10",children:(0,t.jsx)(eO,{initialData:e,initialCount:r,planLimit:a,error:i})})})}},36905:(e,r,a)=>{"use strict";a.d(r,{$S:()=>l,BU:()=>n,Up:()=>o});var t=a(67218);a(79130);var s=a(32032),i=a(98344);async function n(e,r,a,t=[],l=[],o="base",d){if(!e||"string"!=typeof e)return console.error("Invalid userId provided to handleMultipleImageUpload:",e),{urls:[],error:`Invalid userId: expected string, got ${typeof e}`};if(!r||"string"!=typeof r)return console.error("Invalid productId provided to handleMultipleImageUpload:",r),{urls:[],error:`Invalid productId: expected string, got ${typeof r}`};if("variant"===o&&(!d||"string"!=typeof d))return console.error("Invalid variantId provided for variant path type:",d),{urls:[],error:`Invalid variantId: expected string when pathType is 'variant', got ${typeof d}`};let c=a.filter(e=>null!==e);if(console.log("=== Image Upload Handler Debug ==="),console.log(`Path type: ${o}, Variant ID: ${d||"N/A"}`),console.log(`Valid image files: ${c.length}`),console.log(`Existing images: ${t.length}`),console.log(`Removed indices: ${l.length}`),0===c.length&&0===l.length)return console.log("No image operations to perform, returning existing URLs"),{urls:t};if(c.length>5)return{urls:[],error:`Maximum of 5 images allowed per ${"variant"===o?"variant":"product"}.`};let u=c.reduce((e,r)=>e+r.size,0);if(u>0x4b00000){let e=(u/1048576).toFixed(1);return{urls:[],error:`Total upload size (${e}MB) exceeds the 75MB limit for ${"variant"===o?"variant":"product"} images.`}}let m="business",p=[...t],g=await (0,s.createClient)();for(let e of l)if(e>=0&&e<t.length){let r=t[e];if(r){try{console.log(`Removing image at index ${e}: ${r}`);let a=new URL(r),t=a.pathname.split("/"),s=t.findIndex(e=>"business"===e);if(-1!==s&&s<t.length-1){let r=t.slice(s+1).join("/").split("?")[0];console.log(`Attempting to delete from storage path: ${r}`);let{error:a}=await g.storage.from(m).remove([r]);a&&"The resource was not found"!==a.message?(console.error(`Error deleting image at index ${e}:`,a),console.error("Delete error details:",a)):console.log(`Successfully deleted image at path: ${r}`)}else console.warn(`Could not extract storage path from URL: ${r}`),console.warn(`URL pathname: ${a.pathname}`),console.warn("Path parts:",t)}catch(r){console.error(`Error processing image URL for deletion at index ${e}:`,r)}p[e]=""}}let f=[...p.filter(e=>""!==e)],x=f.length;for(let t=0;t<a.length;t++){let s=a[t];if(s)try{let a=new Date().getTime()+Math.floor(1e3*Math.random()),n=x+t,l="variant"===o&&d?(0,i.Vl)(e,r,d,n,a):(0,i.jA)(e,r,n,a);console.log(`Uploading image ${t} (index ${n}) to path: ${l}`);let c=Buffer.from(await s.arrayBuffer()),{error:u}=await g.storage.from(m).upload(l,c,{contentType:s.type,upsert:!0});if(u){console.error(`Failed to upload image ${t}:`,u);continue}let{data:p}=g.storage.from(m).getPublicUrl(l);if(!p?.publicUrl){console.error(`Could not retrieve public URL for image ${t}`);continue}console.log(`Successfully uploaded image ${t}, URL: ${p.publicUrl}`),f.push(p.publicUrl)}catch(e){console.error(`Error processing image ${t}:`,e)}}return console.log(`Image upload complete. Returning ${f.length} URLs:`,f),console.log("=== End Image Upload Handler Debug ==="),{urls:f}}async function l(e,r,a,t=[],s=[]){return n(e,r,a,t,s,"base")}async function o(e,r,a,t,s=[],i=[]){return n(e,r,t,s,i,"variant",a)}(0,a(17478).D)([n,l,o]),(0,t.A)(n,"7f5d6d667d4cc9c503574c5b1be4effe09dd0b5cce",null),(0,t.A)(l,"7c3952b17920109cf4045de4541c0edf3828caac5d",null),(0,t.A)(o,"7e7db967276a6dd3efc6189832b27978b012cea709",null)},38269:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=a(65239),s=a(48088),i=a(88170),n=a.n(i),l=a(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(r,o);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,13673)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/products/page",pathname:"/dashboard/business/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},39390:(e,r,a)=>{"use strict";a.d(r,{J:()=>n});var t=a(60687);a(43210);var s=a(78148),i=a(96241);function n({className:e,...r}){return(0,t.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},39573:(e,r,a)=>{Promise.resolve().then(a.bind(a,36141))},47033:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},49301:(e,r,a)=>{Promise.resolve().then(a.bind(a,15068))},52109:(e,r,a)=>{"use strict";a.d(r,{Jr:()=>u,wg:()=>c,yQ:()=>d});var t=a(67218);a(79130);var s=a(32032),i=a(62351),n=a(52425),l=a(36905),o=a(30468);async function d(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};try{let t,s=e.get("product_id"),d=e.get("variant_name"),c=e.get("variant_values"),u=e.get("base_price"),m=e.get("discounted_price"),p="true"===e.get("is_available"),g=parseInt(e.get("featured_image_index"))||0;try{t=JSON.parse(c||"{}")}catch(e){return{success:!1,error:"Invalid variant values format."}}let f={product_id:s,variant_name:d,variant_values:t,base_price:u&&""!==u.trim()&&"undefined"!==u?parseFloat(u):null,discounted_price:m&&""!==m.trim()&&"undefined"!==m?parseFloat(m):null,is_available:p,featured_image_index:g},x=n.sX.safeParse(f);if(!x.success){console.error("Add Variant Validation Error:",x.error.flatten().fieldErrors);let e=x.error.flatten().fieldErrors,r=Object.entries(e).map(([e,r])=>`${e}: ${Array.isArray(r)?r.join(", "):r}`).join("; ");return{success:!1,error:`Invalid data: ${r}`}}let{data:h,error:b}=await r.from("products_services").select("id, business_id").eq("id",s).eq("business_id",a.id).single();if(b||!h)return{success:!1,error:"Product not found or access denied."};let{data:v,error:_}=await r.from("product_variants").select("id, variant_values").eq("product_id",s);if(_)return console.error("Error checking existing variants:",_),{success:!1,error:"Failed to validate variant uniqueness."};if(v&&v.length>0&&v.find(e=>{let r="string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values,a=Object.keys(r).sort(),s=Object.keys(t).sort();return a.length===s.length&&a.every(e=>s.includes(e)&&r[e]===t[e])}))return{success:!1,error:"A variant with this combination already exists."};let y=[],j=[];for(let[r,a]of(console.log("=== AddVariant FormData Debug ==="),e.entries()))console.log(`FormData key: ${r}, value type: ${typeof a}, value:`,a),r.startsWith("images[")&&a instanceof File&&a.size>0&&(console.log(`Found image file: ${r}, size: ${a.size}, name: ${a.name}`),j.push(a));console.log(`Total image files extracted: ${j.length}`),console.log("=== End AddVariant FormData Debug ===");let w={product_id:s,variant_name:d,variant_values:t,base_price:x.data.base_price,discounted_price:x.data.discounted_price,is_available:p,images:[],featured_image_index:x.data.featured_image_index??0},{data:N,error:k}=await r.from("product_variants").insert(w).select().single();if(k)return console.error("Error inserting variant:",k),{success:!1,error:`Failed to create variant: ${k.message}`};if(j.length>0){console.log(`Uploading ${j.length} images for variant ${N.id} in product ${s}`);let e=await (0,l.Up)(a.id,s,N.id,j);if(e.error)return console.error("Image upload failed:",e.error),await r.from(o.CG.PRODUCT_VARIANTS).delete().eq(o.cZ.ID,N.id),{success:!1,error:e.error||"Failed to upload images."};y=e.urls||[],console.log(`Successfully uploaded ${y.length} images:`,y);let t={images:y,featured_image_index:Math.min(g,Math.max(0,y.length-1))};console.log(`Updating variant ${N.id} with:`,t);let{error:i}=await r.from("product_variants").update(t).eq("id",N.id);if(i)return console.error("Error updating variant with images:",i),{success:!1,error:"Failed to update variant with images."};console.log("Successfully updated variant with images")}let A={...N,images:y,featured_image_index:y.length>0?Math.min(g,Math.max(0,y.length-1)):0};return(0,i.revalidatePath)("/dashboard/business/products"),(0,i.revalidatePath)(`/dashboard/business/products/${s}`),{success:!0,data:{...A,variant_values:"string"==typeof A.variant_values?JSON.parse(A.variant_values):A.variant_values,created_at:new Date(A.created_at),updated_at:new Date(A.updated_at)}}}catch(e){return console.error("Unexpected error in addProductVariant:",e),{success:!1,error:"An unexpected error occurred."}}}async function c(e,r){let a=await (0,s.createClient)(),{data:{user:t},error:n}=await a.auth.getUser();if(n||!t)return{success:!1,error:"User not authenticated."};try{let{data:s,error:n}=await a.from("products_services").select("id, business_id").eq("id",e).eq("business_id",t.id).single();if(n||!s)return{success:!1,error:"Product not found or access denied."};let l=r.map(r=>({product_id:e,variant_name:r.variant_name,variant_values:JSON.stringify(r.variant_values),base_price:r.base_price||null,discounted_price:r.discounted_price||null,is_available:r.is_available??!0,images:[],featured_image_index:void 0!==r.featured_image_index?r.featured_image_index:null})),{data:o,error:d}=await a.from("product_variants").insert(l).select();if(d)return console.error("Error inserting variants:",d),{success:!1,error:"Failed to create variants."};return(0,i.revalidatePath)("/dashboard/business/products"),(0,i.revalidatePath)(`/dashboard/business/products/${e}`),{success:!0,data:o?.map(e=>({...e,variant_values:"string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values,created_at:new Date(e.created_at),updated_at:new Date(e.updated_at)}))||[]}}catch(e){return console.error("Unexpected error in addMultipleVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function u(e,r){try{if(!e||!r||0===Object.keys(r).length)return{success:!1,error:"Invalid input parameters."};if(Object.keys(r).length>5)return{success:!1,error:"Maximum of 5 variant types allowed per product."};let a=Object.keys(r),t=a.map(e=>r[e]).reduce((e,r)=>e.flatMap(e=>r.map(r=>[...e,r])),[[]]).map(e=>{let r={};return a.forEach((a,t)=>{r[a]=e[t]}),{variant_name:e.join(" "),variant_values:r}});return{success:!0,combinations:t}}catch(e){return console.error("Error generating variant combinations:",e),{success:!1,error:"Failed to generate combinations."}}}(0,a(17478).D)([d,c,u]),(0,t.A)(d,"40e0b77a8dc6165a7703d56966ab44b8bb7215bc26",null),(0,t.A)(c,"605db269f98f874bac1f27e3d6b4e08414266e3ac8",null),(0,t.A)(u,"601694daaf4792dcf464368d8457fcd1259c923cf2",null)},52425:(e,r,a)=>{"use strict";a.d(r,{_F:()=>n,e2:()=>m,sX:()=>u,w8:()=>l});var t=a(70762);let s={id:t.Yj().uuid().optional(),business_id:t.Yj().uuid().optional(),product_type:t.k5(["physical","service"]).default("physical"),name:t.Yj().min(1,{message:"Product/Service name is required."}).max(100,{message:"Name cannot exceed 100 characters."}),description:t.Yj().max(500,{message:"Description cannot exceed 500 characters."}).optional().or(t.eu("")),base_price:t.au.number({required_error:"Base price is required.",invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}),discounted_price:t.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:t.zM().default(!0),image_url:t.Yj().url({message:"Invalid image URL format."}).optional().nullable(),images:t.YO(t.Yj()).optional().nullable(),featured_image_index:t.ai().int().min(0).optional().nullable(),slug:t.Yj().optional(),created_at:t.Yj().optional(),updated_at:t.Yj().optional()},i=t.Ik(s),n=i.omit({id:!0,business_id:!0,created_at:!0,updated_at:!0,image_url:!0}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),l=i.partial().refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),o=t.g1(t.Yj(),t.Yj()).refine(e=>Object.keys(e).length>0,{message:"At least one variant type-value pair is required."}).refine(e=>Object.keys(e).length<=5,{message:"Maximum of 5 variant types allowed per product."}),d={id:t.Yj().uuid().optional(),product_id:t.Yj().uuid(),variant_name:t.Yj().min(1,{message:"Variant name is required."}).max(100,{message:"Variant name cannot exceed 100 characters."}),variant_values:o,base_price:t.au.number({invalid_type_error:"Base price must be a number."}).positive({message:"Base price must be positive."}).optional().nullable(),discounted_price:t.au.number({invalid_type_error:"Discounted price must be a number."}).positive({message:"Discounted price must be positive."}).optional().nullable(),is_available:t.zM().default(!0),images:t.YO(t.Yj().url()).optional().nullable().default([]),featured_image_index:t.ai().int().min(0).optional().nullable(),created_at:t.p6().optional(),updated_at:t.p6().optional()},c=t.Ik(d),u=c.omit({id:!0,created_at:!0,updated_at:!0}).refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]}),m=c.partial().refine(e=>!e.base_price||!e.discounted_price||e.discounted_price<e.base_price,{message:"Discounted price must be less than base price.",path:["discounted_price"]});t.Ik({product_id:t.Yj().uuid(),variant_types_values:t.g1(t.Yj(),t.YO(t.Yj())).refine(e=>Object.keys(e).length>0,{message:"At least one variant type with values is required."}).refine(e=>Object.keys(e).length<=5,{message:"Maximum of 5 variant types allowed per product."})})},54220:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61274:(e,r,a)=>{"use strict";a.r(r),a.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>t.B,"60c57f5cb6147b25d698420a59de21049ed634d243":()=>s.J,"70d838791e5769544191e6b0dbbeb36c46196105ea":()=>s.O});var t=a(64275),s=a(64591)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,r,a)=>{"use strict";a.d(r,{TR:()=>g,bq:()=>m,eb:()=>f,gC:()=>p,l6:()=>d,s3:()=>c,yv:()=>u});var t=a(60687);a(43210);var s=a(28695),i=a(78272),n=a(13964),l=a(3589),o=a(96241);function d({...e}){return(0,t.jsx)(s.bL,{"data-slot":"select",...e})}function c({...e}){return(0,t.jsx)(s.YJ,{"data-slot":"select-group",...e})}function u({...e}){return(0,t.jsx)(s.WT,{"data-slot":"select-value",...e})}function m({className:e,size:r="default",children:a,...n}){return(0,t.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[a,(0,t.jsx)(s.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:r,position:a="popper",...i}){return(0,t.jsx)(s.ZL,{children:(0,t.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...i,children:[(0,t.jsx)(x,{}),(0,t.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,t.jsx)(h,{})]})})}function g({className:e,...r}){return(0,t.jsx)(s.JU,{"data-slot":"select-label",className:(0,o.cn)("text-muted-foreground px-2 py-1.5 text-xs",e),...r})}function f({className:e,children:r,...a}){return(0,t.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(s.VF,{children:(0,t.jsx)(n.A,{className:"size-4"})})}),(0,t.jsx)(s.p4,{children:r})]})}function x({className:e,...r}){return(0,t.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,t.jsx)(l.A,{className:"size-4"})})}function h({className:e,...r}){return(0,t.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,t.jsx)(i.A,{className:"size-4"})})}},66591:(e,r,a)=>{"use strict";a.d(r,{U:()=>o,w:()=>d});var t=a(67218);a(79130);var s=a(32032),i=a(62351),n=a(52425),l=a(36905);async function o(e,r){let a=await (0,s.createClient)(),{data:{user:t},error:o}=await a.auth.getUser();if(o||!t)return{success:!1,error:"User not authenticated."};try{let s,{data:o,error:d}=await a.from("product_variants").select(`
        id,
        product_id,
        variant_name,
        variant_values,
        base_price,
        discounted_price,
        is_available,
        images,
        featured_image_index,
        products_services!inner(business_id)
      `).eq("id",e).single();if(d||!o)return{success:!1,error:"Variant not found."};if(o.products_services.business_id!==t.id)return{success:!1,error:"Access denied."};let c=r.get("variant_name"),u=r.get("variant_values"),m=r.get("base_price"),p=r.get("discounted_price"),g=r.get("is_available"),f=r.get("featured_image_index"),x=r.get("remove_images");if(u)try{s=JSON.parse(u)}catch(e){return{success:!1,error:"Invalid variant values format."}}let h={id:e,product_id:o.product_id};null!==c&&(h.variant_name=c),s&&(h.variant_values=s),null!==m&&(h.base_price=m&&""!==m.trim()&&"undefined"!==m?parseFloat(m):null),null!==p&&(h.discounted_price=p&&""!==p.trim()&&"undefined"!==p?parseFloat(p):null),null!==g&&(h.is_available="true"===g),null!==f&&(h.featured_image_index=parseInt(f)||0);let b=n.e2.safeParse(h);if(!b.success){console.error("Update Variant Validation Error:",b.error.flatten().fieldErrors);let e=b.error.flatten().fieldErrors,r=Object.entries(e).map(([e,r])=>`${e}: ${Array.isArray(r)?r.join(", "):r}`).join("; ");return{success:!1,error:`Invalid data: ${r}`}}if(s){let{data:r,error:t}=await a.from("product_variants").select("id, variant_values").eq("product_id",o.product_id).neq("id",e);if(t)return console.error("Error checking existing variants:",t),{success:!1,error:"Failed to validate variant uniqueness."};if(r&&r.length>0&&r.find(e=>{let r="string"==typeof e.variant_values?JSON.parse(e.variant_values):e.variant_values,a=Object.keys(r).sort(),t=Object.keys(s).sort();return a.length===t.length&&a.every(e=>t.includes(e)&&r[e]===s[e])}))return{success:!1,error:"A variant with this combination already exists."}}let v=[...o.images||[]],_=[];if(x)try{_=JSON.parse(x),console.log(`Removing images at indices: ${_.join(", ")}`)}catch(e){console.error("Error parsing remove_images:",e)}let y=[];for(let[e,a]of(console.log("=== UpdateVariant FormData Debug ==="),r.entries()))console.log(`FormData key: ${e}, value type: ${typeof a}, value:`,a),e.startsWith("new_images[")&&a instanceof File&&a.size>0&&(console.log(`Found new image file: ${e}, size: ${a.size}, name: ${a.name}`),y.push(a)),e.startsWith("images[")&&a instanceof File&&a.size>0&&(console.log(`Found image file (images[] pattern): ${e}, size: ${a.size}, name: ${a.name}`),y.push(a));console.log(`Total image files extracted: ${y.length}`),console.log(`Original images before processing: ${v.length}`,v),console.log("=== End UpdateVariant FormData Debug ===");let j=v;if(y.length>0||_.length>0){console.log(`Processing images for variant ${e}: ${y.length} uploads, ${_.length} deletions`);let r=await (0,l.Up)(t.id,o.product_id,e,y,v,_);if(r.error)return console.error("Image operation failed:",r.error),{success:!1,error:r.error||"Failed to process images."};j=r.urls||[],console.log(`Successfully processed images. Final images: ${j.length}`,j)}else console.log("No image operations to perform");let w={};void 0!==b.data.variant_name&&(w.variant_name=b.data.variant_name),void 0!==b.data.variant_values&&(w.variant_values=b.data.variant_values),void 0!==b.data.base_price&&(w.base_price=b.data.base_price),void 0!==b.data.discounted_price&&(w.discounted_price=b.data.discounted_price),void 0!==b.data.is_available&&(w.is_available=b.data.is_available),w.images=j,w.featured_image_index=Math.min(b.data.featured_image_index||0,Math.max(0,j.length-1)),console.log(`Updating variant ${e} with:`,{images:w.images,featured_image_index:w.featured_image_index,imageCount:j.length});let{data:N,error:k}=await a.from("product_variants").update(w).eq("id",e).select().single();if(k)return console.error("Error updating variant:",k),{success:!1,error:"Failed to update variant."};return console.log("Successfully updated variant:",N),(0,i.revalidatePath)("/dashboard/business/products"),(0,i.revalidatePath)(`/dashboard/business/products/${o.product_id}`),{success:!0,data:{...N,variant_values:"string"==typeof N.variant_values?JSON.parse(N.variant_values):N.variant_values,featured_image_index:N.featured_image_index??0,created_at:new Date(N.created_at),updated_at:new Date(N.updated_at)}}}catch(e){return console.error("Unexpected error in updateProductVariant:",e),{success:!1,error:"An unexpected error occurred."}}}async function d(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};try{let t=0,s=0;for(let i of e)try{let{data:e,error:n}=await r.from("product_variants").select(`
            id,
            products_services!inner(business_id)
          `).eq("id",i.id).single();if(n||!e||e.products_services.business_id!==a.id){s++;continue}let{error:l}=await r.from("product_variants").update(i.data).eq("id",i.id);l?s++:t++}catch(e){s++}return(0,i.revalidatePath)("/dashboard/business/products"),{success:!0,updated_count:t,failed_count:s}}catch(e){return console.error("Unexpected error in updateMultipleVariants:",e),{success:!1,error:"An unexpected error occurred."}}}(0,a(17478).D)([o,d]),(0,t.A)(o,"604381af5c96732d294fa7a1b7bd35fc6aa1a9781b",null),(0,t.A)(d,"4055b8f6815995dd3ae4b7db159041d6254736584f",null)},67777:(e,r,a)=>{"use strict";a.d(r,{Y:()=>i});var t=a(67218);a(79130);var s=a(32032);async function i(e=1,r=10,a={},t="created_desc"){let n=await (0,s.createClient)(),{data:{user:l},error:o}=await n.auth.getUser();if(o||!l)return{error:"User not authenticated."};let d=(e-1)*r,c=n.from("products_services").select(`
      id,
      business_id,
      product_type,
      name,
      description,
      base_price,
      discounted_price,
      is_available,
      image_url,
      images,
      featured_image_index,
      created_at,
      updated_at,
      slug,
      product_variants(id, is_available)
    `,{count:"exact"}).eq("business_id",l.id);switch(a.searchTerm&&(c=c.or(`name.ilike.%${a.searchTerm}%,description.ilike.%${a.searchTerm}%`)),void 0!==a.hasVariants&&(c=a.hasVariants?c.not("product_variants","is",null):c.is("product_variants",null)),a.productType&&(c=c.eq("product_type",a.productType)),t){case"created_asc":c=c.order("created_at",{ascending:!0});break;case"price_asc":c=c.order("discounted_price",{ascending:!0,nullsFirst:!1}).order("base_price",{ascending:!0,nullsFirst:!1});break;case"price_desc":c=c.order("discounted_price",{ascending:!1,nullsFirst:!1}).order("base_price",{ascending:!1,nullsFirst:!1});break;case"name_asc":c=c.order("name",{ascending:!0});break;case"name_desc":c=c.order("name",{ascending:!1});break;case"available_first":c=c.order("is_available",{ascending:!1}).order("created_at",{ascending:!1});break;case"unavailable_first":c=c.order("is_available",{ascending:!0}).order("created_at",{ascending:!1});break;default:c=c.order("created_at",{ascending:!1})}c=c.range(d,d+r-1);let{data:u,error:m,count:p}=await c;if(m)return console.error("Fetch Products Error:",m),{error:"Failed to fetch products/services."};let g=(u||[]).map(e=>{let r=e.product_variants||[],a=r.length,t=r.filter(e=>e.is_available).length;return{id:e.id,business_id:e.business_id,product_type:e.product_type,name:e.name,description:e.description,base_price:e.base_price,discounted_price:e.discounted_price,is_available:e.is_available,image_url:e.image_url,images:e.images||[],featured_image_index:e.featured_image_index,slug:e.slug,created_at:new Date(e.created_at).toISOString(),updated_at:new Date(e.updated_at).toISOString(),variant_count:a,has_variants:a>0,available_variant_count:t}});return"variant_count_asc"===t?g.sort((e,r)=>e.variant_count-r.variant_count):"variant_count_desc"===t&&g.sort((e,r)=>r.variant_count-e.variant_count),{data:g,count:p??0}}(0,a(17478).D)([i]),(0,t.A)(i,"787e564da2fc6345f07ba27b32c3991bc3e2ebbaad",null)},69511:(e,r,a)=>{"use strict";a.d(r,{k:()=>o});var t=a(67218);a(79130);var s=a(32032),i=a(62351),n=a(52425),l=a(36905);async function o(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};let o={},d=[],c=0;for(let[r,a]of e.entries())if(r.startsWith("productImage_")){let e=parseInt(r.split("_")[1],10);if(a instanceof File&&a.size>0){for(;d.length<=e;)d.push(null);d[e]=a}}else"featuredImageIndex"===r?c=parseInt(a,10)||0:o[r]=a;o.product_type&&"string"!=typeof o.product_type&&(o.product_type=String(o.product_type)),o.base_price&&(o.base_price=Number(o.base_price)),o.discounted_price&&(o.discounted_price=Number(o.discounted_price)),o.is_available="true"===o.is_available||"on"===o.is_available;let u=n._F.safeParse(o);if(!u.success){console.error("Add Product Validation Error:",u.error.flatten().fieldErrors);let e=Object.entries(u.error.flatten().fieldErrors).map(([e,r])=>`${e}: ${Array.isArray(r)?r.join(", "):r}`).join("; ");return{success:!1,error:`Invalid data: ${e}`}}let m={...u.data,business_id:a.id,image_url:null},{data:p,error:g}=await r.from("products_services").insert(m).select("id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug").single();if(g||!p)return g?.message?.includes("Cannot make product available")&&g?.message?.includes("reached the limit")?{success:!1,error:g.message}:{success:!1,error:`Failed to add product/service: ${g?.message}`};let f=p.id,x=p.image_url,h=p.images||[];if(d.length>0){console.log("Starting image upload with:",{userId:a.id,userIdType:typeof a.id,productId:f,productIdType:typeof f,imageCount:d.length});let e=await (0,l.$S)(a.id,f,d);if(e.error)return console.error(`Image upload failed: ${e.error}`),{success:!1,error:`Product created, but image upload failed: ${e.error}`};if((h=e.urls).length>0){let e=Math.min(c,h.length-1),a={image_url:x=h[e],images:Array.isArray(h)?h.filter(e=>null!==e):[],featured_image_index:e},{data:t,error:s}=await r.from("products_services").update(a).eq("id",f).select("id, image_url, images, featured_image_index");if(s)return console.error(`Failed to update product with image URLs: ${s.message}`),{success:!1,error:`Product created, images uploaded, but failed to save URLs: ${s.message}`}}}(0,i.revalidatePath)("/dashboard/business/products");let{data:b,error:v}=await r.from("products_services").select("id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug").eq("id",f).single();return v&&console.error(`Failed to fetch latest product data: ${v.message}`),{success:!0,data:{...b||p,image_url:b?.image_url||x,images:b?.images||(Array.isArray(h)?h.filter(e=>null!==e):[]),featured_image_index:b?.featured_image_index||c,created_at:b?.created_at||p.created_at,updated_at:b?.updated_at||p.updated_at,slug:b?.slug||p.slug}}}(0,a(17478).D)([o]),(0,t.A)(o,"4028d9eae4d852411a8378996608e072b40402706b",null)},70281:(e,r,a)=>{"use strict";a.r(r),a.d(r,{"00029e81b58a5a97fdfca38c6b984b28c00ce6cc90":()=>u.pU,"4003407ec6313e0e5df396bd13f3f069cdc5666b21":()=>l.i,"4028d9eae4d852411a8378996608e072b40402706b":()=>s.k,"403a75d9205d3ac636b80de9ab8dbe80951e5d71b9":()=>u.nC,"4055b8f6815995dd3ae4b7db159041d6254736584f":()=>d.w,"405fdaee2ff8ff13e45b4811c849ef0b8a06eea405":()=>c.eq,"406284e9ef6a2be8ebf3ce5826f7572619ff00844e":()=>c.EM,"40e0b77a8dc6165a7703d56966ab44b8bb7215bc26":()=>o.yQ,"40e15bace5efcde3c132186e902abe40403e1fa4ff":()=>u.K6,"40f582f4b6156ae356636c0f4aa3c00d2946c8f64b":()=>c.eF,"601694daaf4792dcf464368d8457fcd1259c923cf2":()=>o.Jr,"602335d5c6ca713548cda36b584c6afe685e032e60":()=>u.gL,"604381af5c96732d294fa7a1b7bd35fc6aa1a9781b":()=>d.U,"605db269f98f874bac1f27e3d6b4e08414266e3ac8":()=>o.wg,"60628b2893a8cf908260d34968479634cae65b8c58":()=>n.G,"70ffeec93e6ac09c9ae41698e2e6418acc4707a28d":()=>u.qU,"787e564da2fc6345f07ba27b32c3991bc3e2ebbaad":()=>t.Y,"7c3952b17920109cf4045de4541c0edf3828caac5d":()=>i.$S,"7e7db967276a6dd3efc6189832b27978b012cea709":()=>i.Up,"7f5d6d667d4cc9c503574c5b1be4effe09dd0b5cce":()=>i.BU});var t=a(67777),s=a(69511),i=a(36905),n=a(6033),l=a(82399),o=a(52109),d=a(66591),c=a(89461),u=a(33700)},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,r,a)=>{"use strict";a.d(r,{b:()=>l});var t=a(43210),s=a(3416),i=a(60687),n=t.forwardRef((e,r)=>(0,i.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var l=n},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("Funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},82399:(e,r,a)=>{"use strict";a.d(r,{i:()=>l});var t=a(67218);a(79130);var s=a(32032),i=a(62351),n=a(98344);async function l(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};if(!e)return{success:!1,error:"Item ID is required."};let{data:l}=await r.from("products_services").select("images, name").eq("id",e).eq("business_id",a.id).single();if(!l)return{success:!1,error:"Product not found."};let o="business";try{if(l.images&&Array.isArray(l.images)&&l.images.length>0){for(let e of l.images)if(e)try{let a=new URL(e).pathname.split("/"),t=a.findIndex(e=>"business"===e);if(-1!==t&&t<a.length-1){let e=a.slice(t+1).join("/").split("?")[0];await r.storage.from(o).remove([e]),console.log(`Attempted to delete image from URL: ${e}`)}}catch(r){console.error(`Error processing image URL for deletion: ${e}`,r)}}let t=(0,n.YV)(a.id),{data:s,error:i}=await r.storage.from(o).list(`${t}/products`);if(i)console.error("Error listing product files:",i);else if(s){let a=[];for(let i of s)if(i.name===e){let{data:e,error:s}=await r.storage.from(o).list(`${t}/products/${i.name}`);if(s)console.error(`Error listing files in product folder ${i.name}:`,s);else if(e)for(let r of e)a.push(`${t}/products/${i.name}/${r.name}`)}if(a.length>0)for(let e=0;e<a.length;e+=100){let t=a.slice(e,e+100),{error:s}=await r.storage.from(o).remove(t);s&&"The resource was not found"!==s.message?console.warn(`Could not delete some product files (batch ${e}): ${s.message}`):console.log(`Successfully deleted batch ${e} of product files`)}}}catch(e){console.error("Error handling product file deletion:",e)}let{error:d}=await r.from("products_services").delete().eq("id",e).eq("business_id",a.id);return d?(console.error("Delete Product Error:",d),{success:!1,error:`Failed to delete product/service: ${d.message}`}):((0,i.revalidatePath)("/dashboard/business/products"),{success:!0})}(0,a(17478).D)([l]),(0,t.A)(l,"4003407ec6313e0e5df396bd13f3f069cdc5666b21",null)},89461:(e,r,a)=>{"use strict";a.d(r,{EM:()=>d,eF:()=>l,eq:()=>o});var t=a(67218);a(79130);var s=a(32032),i=a(62351),n=a(98344);async function l(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};try{let{data:t,error:s}=await r.from("product_variants").select(`
        id,
        product_id,
        is_available,
        images,
        products_services!inner(business_id)
      `).eq("id",e).single();if(s||!t)return{success:!1,error:"Variant not found."};if(t.products_services.business_id!==a.id)return{success:!1,error:"Access denied."};let{data:l,error:o}=await r.from("product_variants").select("id, is_available").eq("product_id",t.product_id);if(o)return console.error("Error counting variants:",o),{success:!1,error:"Failed to validate variant deletion."};let d=l?.filter(r=>r.is_available&&r.id!==e).length||0;if(0===d&&l&&l.length>1)return{success:!1,error:"Cannot delete the last available variant. At least one variant must remain available."};if(t.images&&Array.isArray(t.images)&&t.images.length>0){let s="business";for(let a of(console.log(`Deleting ${t.images.length} images for variant ${e}`),t.images))if(a)try{let e=new URL(a).pathname.split("/"),t=e.findIndex(e=>"business"===e);if(-1!==t&&t<e.length-1){let a=e.slice(t+1).join("/").split("?")[0],{error:i}=await r.storage.from(s).remove([a]);i&&"The resource was not found"!==i.message?console.error(`Error deleting variant image: ${a}`,i):console.log(`Successfully deleted variant image: ${a}`)}}catch(e){console.error(`Error processing variant image URL for deletion: ${a}`,e)}try{let i=(0,n.YV)(a.id),l=`${i}/products/${t.product_id}/${e}`,{data:o,error:d}=await r.storage.from(s).list(l);if(!d&&o&&o.length>0){let e=o.map(e=>`${l}/${e.name}`),{error:a}=await r.storage.from(s).remove(e);a&&"The resource was not found"!==a.message?console.error(`Error deleting variant folder: ${l}`,a):console.log(`Successfully deleted variant folder: ${l}`)}}catch(r){console.error(`Error cleaning up variant folder for ${e}:`,r)}}let{error:c}=await r.from("product_variants").delete().eq("id",e);if(c){if(console.error("Error deleting variant:",c),c.message.includes("at least one variant must be available"))return{success:!1,error:"Cannot delete the last available variant."};return{success:!1,error:"Failed to delete variant."}}return(0,i.revalidatePath)("/dashboard/business/products"),(0,i.revalidatePath)(`/dashboard/business/products/${t.product_id}`),{success:!0}}catch(e){return console.error("Unexpected error in deleteProductVariant:",e),{success:!1,error:"An unexpected error occurred."}}}async function o(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};try{let t=0,s=0,l=[],{data:o,error:d}=await r.from("product_variants").select(`
        id,
        product_id,
        is_available,
        variant_name,
        images,
        products_services!inner(business_id)
      `).in("id",e);if(d)return{success:!1,error:"Failed to fetch variant information."};if(!o||0===o.length)return{success:!1,error:"No variants found."};if(o.filter(e=>e.products_services.business_id!==a.id).length>0)return{success:!1,error:"Access denied to some variants."};let c=o.reduce((e,r)=>(e[r.product_id]||(e[r.product_id]=[]),e[r.product_id].push(r),e),{});for(let[e,i]of Object.entries(c)){let{data:o,error:d}=await r.from("product_variants").select("id, is_available").eq("product_id",e);if(d){i.forEach(e=>{l.push(`Failed to validate deletion for variant ${e.variant_name}`),s++});continue}let c=o?.filter(e=>e.is_available).length||0,u=i.filter(e=>e.is_available).length,m=c-u;if(0===m&&o&&o.length>i.length){i.forEach(e=>{l.push(`Cannot delete variant ${e.variant_name}: would leave no available variants`),s++});continue}for(let e of i)try{if(e.images&&Array.isArray(e.images)&&e.images.length>0){let t="business";for(let a of e.images)if(a)try{let e=new URL(a).pathname.split("/"),s=e.findIndex(e=>"business"===e);if(-1!==s&&s<e.length-1){let a=e.slice(s+1).join("/").split("?")[0];await r.storage.from(t).remove([a])}}catch(r){console.error(`Error deleting image for variant ${e.variant_name}:`,r)}try{let s=(0,n.YV)(a.id),i=`${s}/products/${e.product_id}/${e.id}`,{data:l}=await r.storage.from(t).list(i);if(l&&l.length>0){let e=l.map(e=>`${i}/${e.name}`);await r.storage.from(t).remove(e)}}catch(r){console.error(`Error cleaning up variant folder for ${e.variant_name}:`,r)}}let{error:i}=await r.from("product_variants").delete().eq("id",e.id);i?(l.push(`Failed to delete variant ${e.variant_name}: ${i.message}`),s++):t++}catch(r){l.push(`Unexpected error deleting variant ${e.variant_name}`),s++}}return(0,i.revalidatePath)("/dashboard/business/products"),{success:t>0,deleted_count:t,failed_count:s,errors:l.length>0?l:void 0}}catch(e){return console.error("Unexpected error in deleteMultipleVariants:",e),{success:!1,error:"An unexpected error occurred."}}}async function d(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};try{let{data:t,error:s}=await r.from("products_services").select("id, business_id").eq("id",e).eq("business_id",a.id).single();if(s||!t)return{success:!1,error:"Product not found or access denied."};let{count:i,error:n}=await r.from("product_variants").select("*",{count:"exact",head:!0}).eq("product_id",e);n&&console.error("Error counting variants:",n);let{error:l}=await r.from("product_variants").delete().eq("product_id",e);if(l)return console.error("Error deleting product variants:",l),{success:!1,error:"Failed to delete product variants."};return{success:!0,deleted_count:i||0}}catch(e){return console.error("Unexpected error in deleteAllProductVariants:",e),{success:!1,error:"An unexpected error occurred."}}}(0,a(17478).D)([l,o,d]),(0,t.A)(l,"40f582f4b6156ae356636c0f4aa3c00d2946c8f64b",null),(0,t.A)(o,"405fdaee2ff8ff13e45b4811c849ef0b8a06eea405",null),(0,t.A)(d,"406284e9ef6a2be8ebf3ce5826f7572619ff00844e",null)},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98344:(e,r,a)=>{"use strict";function t(e){if(!e||"string"!=typeof e)throw Error(`Invalid userId: expected string, got ${typeof e}. Value: ${e}`);if(e.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${e}`);let r=e.substring(0,2).toLowerCase(),a=e.substring(2,4).toLowerCase();return`users/${r}/${a}/${e}`}function s(e,r){let a=t(e);return`${a}/profile/logo_${r}.webp`}function i(e,r,a,s){let i=t(e);return`${i}/products/${r}/base/image_${a}_${s}.webp`}function n(e,r,a,s,i){let n=t(e);return`${n}/products/${r}/${a}/image_${s}_${i}.webp`}function l(e,r){let a=t(e);return`${a}/gallery/gallery_${r}.webp`}function o(e,r,a){let s=t(e);return`${s}/branding/header_${a}_${r}.webp`}a.d(r,{$W:()=>l,Vl:()=>n,Wl:()=>s,YV:()=>t,jA:()=>i,rN:()=>o})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,7793,1753,6380,5880,8567,4851,2186,2978,3037,3739,9538,5265,9209,9648],()=>a(38269));module.exports=t})();