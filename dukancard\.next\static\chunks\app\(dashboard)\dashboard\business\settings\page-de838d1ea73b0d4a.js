(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8164],{22419:(e,r,a)=>{Promise.resolve().then(a.bind(a,41091))},30070:(e,r,a)=>{"use strict";a.d(r,{C5:()=>g,MJ:()=>b,Rr:()=>p,eI:()=>h,lR:()=>f,lV:()=>o,zB:()=>u});var t=a(95155),s=a(12115),i=a(99708),n=a(62177),l=a(53999),d=a(82714);let o=n.Op,c=s.createContext({}),u=e=>{let{...r}=e;return(0,t.jsx)(c.Provider,{value:{name:r.name},children:(0,t.jsx)(n.xI,{...r})})},m=()=>{let e=s.useContext(c),r=s.useContext(x),{getFieldState:a}=(0,n.xW)(),t=(0,n.lN)({name:e.name}),i=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...i}},x=s.createContext({});function h(e){let{className:r,...a}=e,i=s.useId();return(0,t.jsx)(x.Provider,{value:{id:i},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",r),...a})})}function f(e){let{className:r,...a}=e,{error:s,formItemId:i}=m();return(0,t.jsx)(d.J,{"data-slot":"form-label","data-error":!!s,className:(0,l.cn)("data-[error=true]:text-destructive",r),htmlFor:i,...a})}function b(e){let{...r}=e,{error:a,formItemId:s,formDescriptionId:n,formMessageId:l}=m();return(0,t.jsx)(i.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(n," ").concat(l):"".concat(n),"aria-invalid":!!a,...r})}function p(e){let{className:r,...a}=e,{formDescriptionId:s}=m();return(0,t.jsx)("p",{"data-slot":"form-description",id:s,className:(0,l.cn)("text-muted-foreground text-sm",r),...a})}function g(e){var r;let{className:a,...s}=e,{error:i,formMessageId:n}=m(),d=i?String(null!=(r=null==i?void 0:i.message)?r:""):s.children;return d?(0,t.jsx)("p",{"data-slot":"form-message",id:n,className:(0,l.cn)("text-destructive text-sm",a),...s,children:d}):null}},41091:(e,r,a)=>{"use strict";a.d(r,{default:()=>q});var t=a(95155),s=a(12115),i=a(28695),n=a(381),l=a(56671),d=a(89852),o=a(97168),c=a(28883),u=a(44940),m=a(81284),x=a(62177),h=a(90221),f=a(55594),b=a(99840),p=a(30070),g=a(69916),v=a(75525),j=a(51154),y=a(40646),N=a(34477);let w=(0,N.createServerReference)("00546ba3b44b1b5aeb96bb6a14ff1b7fc41225381a",N.callServer,void 0,N.findSourceMapURL,"sendEmailChangeOTP"),k=(0,N.createServerReference)("603946f77e1dac69f9428ea02135bb55ba64d9fe1b",N.callServer,void 0,N.findSourceMapURL,"linkBusinessEmail"),E=(0,N.createServerReference)("60f9c4e1494842ac1b9682b8c0372a17177a0b9e5d",N.callServer,void 0,N.findSourceMapURL,"confirmEmailChangeOTP"),A=(0,N.createServerReference)("60aa21197218e70daff6e3b37ae514a44cab915e66",N.callServer,void 0,N.findSourceMapURL,"updateBusinessEmailWithOTP"),S=f.z.object({email:f.z.string().email("Invalid email address.")}),R=f.z.object({otp:f.z.string().min(6,"OTP must be 6 digits.").max(6,"OTP must be 6 digits.")});function C(e){let{isOpen:r,onClose:a,currentEmail:n,onEmailUpdated:u}=e,[m,f]=(0,s.useTransition)(),[N,C]=(0,s.useState)("email_input"),[P,F]=(0,s.useState)(""),[V,L]=(0,s.useState)(""),[T,D]=(0,s.useState)(""),_=(0,x.mN)({resolver:(0,h.u)(S),defaultValues:{email:""}}),I=(0,x.mN)({resolver:(0,h.u)(R),defaultValues:{otp:""}}),z=()=>{C("email_input"),F(""),L(""),D(""),_.reset(),I.reset(),a()};return(0,t.jsx)(b.lG,{open:r,onOpenChange:e=>!e&&z(),children:(0,t.jsxs)(b.Cf,{className:"sm:max-w-md",hideClose:!1,children:[(0,t.jsxs)(b.c7,{children:[(0,t.jsxs)(b.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"w-5 h-5 text-primary"}),"Update Email Address"]}),(0,t.jsx)(b.rr,{children:n?"Verify your current email first, then set a new email address.":"Link an email address to your account for better security."})]}),(0,t.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"space-y-6",children:["email_input"===N&&(0,t.jsx)("div",{className:"space-y-4",children:n?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:"Security Verification Required"})]}),(0,t.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["To change your email address, we need to verify your current email: ",(0,t.jsx)("strong",{children:n})]})]}),(0,t.jsx)(o.$,{onClick:()=>{f(async()=>{try{let e=await w();e.success?(F(e.email||n||""),C("otp_verification"),D("We&apos;ve sent a 6-digit verification code to ".concat(e.email||n,".")),l.oR.success("Verification code sent to your current email!")):(l.oR.error(e.message||"Failed to send verification code."),D(e.message||"Failed to send verification code."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})},disabled:m,className:"w-full",children:m?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending Code..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Send Verification Code"]})})]}):(0,t.jsx)(p.lV,{..._,children:(0,t.jsxs)("form",{onSubmit:_.handleSubmit(e=>{f(async()=>{try{let r=new FormData;r.append("email",e.email);let a=await k({message:null,success:!1},r);a.success?"otp_sent"===a.message?(F(e.email),C("otp_verification"),D("We&apos;ve sent a 6-digit verification code to ".concat(e.email,".")),l.oR.success("Verification code sent!")):(l.oR.success("Email linked successfully!"),z(),null==u||u()):(l.oR.error(a.message||"Failed to link email."),D(a.message||"Failed to link email."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,t.jsx)(p.zB,{control:_.control,name:"email",render:e=>{let{field:r}=e;return(0,t.jsxs)(p.eI,{children:[(0,t.jsx)(p.lR,{children:"Email Address"}),(0,t.jsx)(p.MJ,{children:(0,t.jsx)(d.p,{type:"email",placeholder:"Enter your email address",...r,disabled:m})}),(0,t.jsx)(p.C5,{})]})}}),(0,t.jsx)(o.$,{type:"submit",disabled:m,className:"w-full",children:m?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Linking Email..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Link Email Address"]})})]})})}),"otp_verification"===N&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full",children:(0,t.jsx)(c.A,{className:"w-6 h-6 text-green-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Check Your Email"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["We've sent a 6-digit code to ",(0,t.jsx)("strong",{children:P})]})]}),(0,t.jsx)(p.lV,{...I,children:(0,t.jsxs)("form",{onSubmit:I.handleSubmit(e=>{f(async()=>{try{let r=new FormData;r.append("email",P),r.append("otp",e.otp);let a=await E({email:P,otp:e.otp},r);a.success?n?(C("new_email_input"),D("OTP verified! Now enter your new email address."),l.oR.success("OTP verified! Enter your new email.")):(l.oR.success("Email verified and linked successfully!"),z(),null==u||u()):(l.oR.error(a.message||"Invalid OTP. Please try again."),D(a.message||"Invalid OTP. Please try again."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,t.jsx)(p.zB,{control:I.control,name:"otp",render:e=>{let{field:r}=e;return(0,t.jsxs)(p.eI,{children:[(0,t.jsx)(p.lR,{children:"Verification Code"}),(0,t.jsx)(p.MJ,{children:(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(g.UV,{maxLength:6,...r,disabled:m,children:(0,t.jsxs)(g.NV,{children:[(0,t.jsx)(g.sF,{index:0}),(0,t.jsx)(g.sF,{index:1}),(0,t.jsx)(g.sF,{index:2}),(0,t.jsx)(g.sF,{index:3}),(0,t.jsx)(g.sF,{index:4}),(0,t.jsx)(g.sF,{index:5})]})})})}),(0,t.jsx)(p.C5,{})]})}}),(0,t.jsx)(o.$,{type:"submit",disabled:m,className:"w-full",children:m?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Verifying..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Verify Code"]})})]})})]}),"new_email_input"===N&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-green-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-green-900 dark:text-green-100",children:"Current Email Verified"})]}),(0,t.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:["Current email: ",(0,t.jsx)("strong",{children:n})]})]}),(0,t.jsx)(p.lV,{..._,children:(0,t.jsxs)("form",{onSubmit:_.handleSubmit(e=>{f(async()=>{try{let r=new FormData;r.append("email",e.email);let a=await A({message:null,success:!1},r);a.success?(l.oR.success("Email updated successfully!"),z(),null==u||u()):(l.oR.error(a.message||"Failed to update email."),D(a.message||"Failed to update email."))}catch(e){l.oR.error("An unexpected error occurred."),console.error(e)}})}),className:"space-y-4",children:[(0,t.jsx)(p.zB,{control:_.control,name:"email",render:e=>{let{field:r}=e;return(0,t.jsxs)(p.eI,{children:[(0,t.jsx)(p.lR,{children:"New Email Address"}),(0,t.jsx)(p.MJ,{children:(0,t.jsx)(d.p,{type:"email",placeholder:"Enter your new email address",...r,disabled:m})}),(0,t.jsx)(p.C5,{})]})}}),(0,t.jsx)(o.$,{type:"submit",disabled:m,className:"w-full",children:m?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Updating Email..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Update Email Address"]})})]})})]}),T&&(0,t.jsx)("div",{className:"p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,t.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:T})})]},N)]})})}function P(e){let{currentEmail:r,registrationType:a}=e,[n,l]=(0,s.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20",children:(0,t.jsx)(c.A,{className:"w-5 h-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-50",children:"Email Address"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:r?"Manage your account email address":"Link an email address for better security"})]})]})}),(0,t.jsx)("div",{className:"bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 p-6 space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 block",children:"Current Email Address"}),(0,t.jsx)(d.p,{type:"email",value:r||"",placeholder:r||"No email address linked",readOnly:!0,className:"bg-neutral-50 dark:bg-neutral-800 cursor-not-allowed"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1",children:r?"This is your current email address":"No email address is currently linked to your account"})]}),(0,t.jsxs)(o.$,{onClick:()=>l(!0),className:"w-full sm:w-auto",variant:"default",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),r?"Update Email Address":"Link Email Address"]})]})}),"google"===a&&(0,t.jsx)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1",children:"Google Account Integration"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"You signed up with Google. You can still link or update your email address for additional security options."})]})]})})]}),(0,t.jsx)(C,{isOpen:n,onClose:()=>l(!1),currentEmail:r,onEmailUpdated:()=>{window.location.reload()}})]})}var F=a(19420);function V(e){let{currentPhone:r}=e;return(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-green-500/10 to-green-500/5 border border-green-500/20",children:(0,t.jsx)(F.A,{className:"w-4 h-4 text-green-500"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-100",children:"Phone Number"})]}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed",children:r?"Your current phone number linked to this account.":"No phone number is currently linked to your account."})]}),(0,t.jsx)("div",{className:"space-y-6",children:r?(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Current Phone Number"}),(0,t.jsx)("div",{className:"mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md",children:(0,t.jsx)("span",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:r})}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-1",children:"Phone number changes are not currently supported. Contact support if you need to update your number."})]})}):(0,t.jsxs)("div",{className:"text-center p-6 rounded-lg bg-neutral-50 dark:bg-neutral-900/50 border border-neutral-200 dark:border-neutral-700",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 mx-auto mb-3 w-fit",children:(0,t.jsx)(F.A,{className:"w-6 h-6"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-2",children:"No Phone Number"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 max-w-sm mx-auto",children:"No phone number is currently linked to your account. Phone number linking is not available at this time."})]})})]})}var L=a(81586),T=a(33786),D=a(6874),_=a.n(D);function I(){return(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,t.jsx)(L.A,{className:"w-4 h-4 text-primary"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-neutral-900 dark:text-neutral-100",children:"Edit Digital Card Profile"})]}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed",children:"Update your business details, contact information, social links, and appearance on your public digital card."})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(o.$,{asChild:!0,variant:"outline",size:"sm",className:"px-4 py-2 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(_(),{href:"/dashboard/business/card",className:"flex items-center",children:[(0,t.jsx)(L.A,{className:"w-4 h-4 mr-2"}),"Go to Card Editor",(0,t.jsx)(T.A,{className:"w-4 h-4 ml-2"})]})})})]})}var z=a(60760),M=a(35695),U=a(62525),O=a(1243),$=a(46767),Y=a(54861);let W=(0,N.createServerReference)("00e8f070a14433b78596a187dc463547214ab707fa",N.callServer,void 0,N.findSourceMapURL,"checkDeleteAccountVerificationOptions"),B=(0,N.createServerReference)("006c62545b20ce4a9dee4ae76cb3cc282e7383fd0b",N.callServer,void 0,N.findSourceMapURL,"sendDeleteAccountOTP"),J=(0,N.createServerReference)("607838e0ab85cc057a08b13adf3219f03def6434c7",N.callServer,void 0,N.findSourceMapURL,"verifyDeleteAccountOTP"),G=(0,N.createServerReference)("40f6db33a1d20ceee7e9c2c3fab658082e75968126",N.callServer,void 0,N.findSourceMapURL,"verifyDeleteAccountPassword"),K=(0,N.createServerReference)("00e4dae012fd1fa78d78881ff916619155bbfe5a13",N.callServer,void 0,N.findSourceMapURL,"deleteAccount");function H(){let e=(0,M.useRouter)(),[r,a]=(0,s.useTransition)(),[n,u]=(0,s.useState)(""),[m,x]=(0,s.useState)(!1),[h,f]=(0,s.useState)(!1),[p,v]=(0,s.useState)("initial"),[y,N]=(0,s.useState)(!1),[w,k]=(0,s.useState)(!1),[E,A]=(0,s.useState)(""),[S,R]=(0,s.useState)(null),[C,P]=(0,s.useState)(""),[F,V]=(0,s.useState)(""),[L,T]=(0,s.useState)(!1),[D,_]=(0,s.useState)(!1),[I,H]=(0,s.useState)(!1),[q,X]=(0,s.useState)(!1);(0,s.useEffect)(()=>{h&&"initial"===p&&Z()},[h,p]);let Z=async()=>{X(!0);try{let e=await W();e.success?(N(e.hasEmail),k(e.hasPhone),e.hasEmail&&A(""),e.hasEmail&&e.hasPhone?v("choose-method"):e.hasEmail?(R("email"),v("email-otp")):e.hasPhone?(R("password"),v("password")):v("final-confirm")):l.oR.error(e.message||"Failed to check verification options")}catch(e){l.oR.error("An error occurred while checking verification options")}finally{X(!1)}},Q=e=>{R(e),"email"===e?v("email-otp"):v("password")},ee=async()=>{_(!0);try{let e=await B();if(e.success)l.oR.success(e.message),e.email&&A(e.email);else if(l.oR.error(e.message),e.isConfigurationError)return}catch(e){l.oR.error("Failed to send verification code")}finally{_(!1)}},er=async()=>{if(6!==C.length)return void l.oR.error("Please enter a valid 6-digit code");T(!0);try{let e=await J(E,C);e.success?(l.oR.success(e.message),H(!0),v("final-confirm")):l.oR.error(e.message)}catch(e){l.oR.error("Failed to verify code")}finally{T(!1)}},ea=async()=>{if(!F.trim())return void l.oR.error("Please enter your password");T(!0);try{let e=await G(F);e.success?(l.oR.success(e.message),H(!0),v("final-confirm")):l.oR.error(e.message)}catch(e){l.oR.error("Failed to verify password")}finally{T(!1)}},et=()=>{u(""),f(!1),v("initial"),R(null),P(""),V(""),H(!1),A(""),X(!1)};return(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.3},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"pb-6 border-b border-red-200/60 dark:border-red-700/60",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-red-500/10 to-red-500/5 border border-red-500/20",children:(0,t.jsx)(U.A,{className:"w-4 h-4 text-red-500"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-red-600 dark:text-red-400",children:"Delete Account"})]}),(0,t.jsx)("p",{className:"text-sm text-red-600/80 dark:text-red-400/80 leading-relaxed",children:"Permanently delete your account and all associated data."})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30 mb-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(O.A,{className:"w-5 h-5 text-red-500 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-red-700 dark:text-red-400",children:"Warning: This action cannot be undone"}),(0,t.jsx)("p",{className:"text-xs text-red-600/80 dark:text-red-400/80 mt-1",children:"Deleting your account will permanently remove all your data, including your business card, products, and analytics. Any active subscriptions will be canceled."})]})]})}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)(o.$,{variant:"destructive",disabled:m,onClick:()=>f(!0),className:"px-4 py-2 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",type:"button",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]})}),(0,t.jsx)(b.lG,{open:h,onOpenChange:e=>{m||L||D||et()},children:(0,t.jsxs)(b.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(b.c7,{className:"text-center pb-2",children:[(0,t.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20",children:(0,t.jsx)(i.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},children:(0,t.jsx)(U.A,{className:"h-8 w-8 text-red-500"})})}),(0,t.jsx)(b.L3,{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"choose-method"===p?"Verify Your Identity":"email-otp"===p?"Email Verification":"password"===p?"Password Verification":"Delete your account?"}),(0,t.jsx)(b.rr,{className:"text-gray-500 dark:text-gray-400 mt-2",children:"choose-method"===p?"Choose how you want to verify your identity before deleting your account.":"email-otp"===p?"We've sent a verification code to your email address.":"password"===p?"Please enter your current password to verify your identity.":"This action cannot be undone. All your data will be permanently removed."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[q&&(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 space-y-4",children:[(0,t.jsx)(j.A,{className:"h-8 w-8 animate-spin text-neutral-500"}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Checking verification options..."})]}),!q&&"choose-method"===p&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 mb-4",children:"For security, please verify your identity before proceeding:"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(o.$,{onClick:()=>Q("email"),variant:"outline",className:"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 text-blue-500"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Email Verification"}),(0,t.jsx)("div",{className:"text-xs text-neutral-500",children:"Send OTP to your email"})]})]}),(0,t.jsxs)(o.$,{onClick:()=>Q("password"),variant:"outline",className:"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",children:[(0,t.jsx)($.A,{className:"h-5 w-5 text-green-500"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Password Verification"}),(0,t.jsx)("div",{className:"text-xs text-neutral-500",children:"Enter your current password"})]})]})]})]}),!q&&"email-otp"===p&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(o.$,{onClick:ee,disabled:D,variant:"outline",className:"mb-4",children:D?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Sending..."]}):"Send Verification Code"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Enter 6-digit verification code:"}),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(g.UV,{maxLength:6,value:C,onChange:P,className:"gap-2",children:(0,t.jsxs)(g.NV,{children:[(0,t.jsx)(g.sF,{index:0,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:1,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:2,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:3,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:4,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"}),(0,t.jsx)(g.sF,{index:5,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"})]})})})]}),(0,t.jsx)(o.$,{onClick:er,disabled:6!==C.length||L,className:"w-full",children:L?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Verifying..."]}):"Verify Code"})]}),!q&&"password"===p&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:"Enter your current password:"}),(0,t.jsx)(d.p,{type:"password",value:F,onChange:e=>V(e.target.value),placeholder:"Current password",className:"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"})]}),(0,t.jsx)(o.$,{onClick:ea,disabled:!F.trim()||L,className:"w-full",children:L?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Verifying..."]}):"Verify Password"})]}),!q&&"final-confirm"===p&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"bg-red-50 dark:bg-red-900/10 rounded-lg p-4 mb-6 border border-red-100 dark:border-red-800/20",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(O.A,{className:"h-5 w-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-red-700 dark:text-red-400",children:"Warning: All data will be permanently lost"}),(0,t.jsxs)("ul",{className:"text-xs text-red-600/80 dark:text-red-400/80 mt-2 space-y-1 list-disc pl-4",children:[(0,t.jsx)("li",{children:"Your business profile and digital card"}),(0,t.jsx)("li",{children:"All products and services"}),(0,t.jsx)("li",{children:"Analytics and customer data"}),(0,t.jsx)("li",{children:"Subscription information"})]})]})]})}),(0,t.jsxs)("p",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2",children:["Type ",(0,t.jsx)("span",{className:"font-bold text-red-500 dark:text-red-400",children:"DELETE"})," to confirm:"]}),(0,t.jsx)(d.p,{value:n,onChange:e=>u(e.target.value),placeholder:"DELETE",className:"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-red-500/30"})]})]}),(0,t.jsxs)(b.Es,{className:"flex flex-col-reverse sm:flex-row gap-3 pt-4",children:[(0,t.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{et()},disabled:m||L||D||q,className:"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200",children:[(0,t.jsx)(Y.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),!q&&"final-confirm"===p&&(0,t.jsx)(i.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1",children:(0,t.jsx)(o.$,{type:"button",onClick:r=>(r.preventDefault(),"DELETE"!==n)?void l.oR.error('Please type "DELETE" to confirm'):(y||w)&&!I?void l.oR.error("Please complete verification first"):void(x(!0),a(async()=>{try{l.oR.success("Processing account deletion...");try{await K(),et(),e.push("/")}catch(r){r instanceof Error&&r.message.includes("Cannot read properties of undefined")?(et(),e.push("/")):(console.error("Error in account deletion:",r),l.oR.error("Failed to delete account"),x(!1))}}catch(e){console.error("Unexpected error during account deletion:",e),l.oR.error("An unexpected error occurred"),x(!1)}})),disabled:"DELETE"!==n||m,className:"\n                    w-full relative overflow-hidden\n                    bg-gradient-to-r from-red-500 to-red-600\n                    hover:from-red-600 hover:to-red-700\n                    text-white font-medium\n                    shadow-lg hover:shadow-xl\n                    transition-all duration-300\n                    before:absolute before:inset-0\n                    before:bg-gradient-to-r before:from-red-400 before:to-red-500\n                    before:opacity-0 hover:before:opacity-20\n                    before:transition-opacity before:duration-300\n                    ".concat("DELETE"!==n||m?"cursor-not-allowed opacity-80":"","\n                  "),style:{boxShadow:"DELETE"!==n||m?"0 4px 20px rgba(239, 68, 68, 0.3)":"0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)"},children:(0,t.jsx)(z.N,{mode:"wait",children:m?(0,t.jsxs)(i.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Deleting..."]},"deleting"):(0,t.jsxs)(i.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:10},className:"flex items-center justify-center",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]},"delete")})})})]})]})})]})]})}function q(e){let{currentEmail:r,currentPhone:a,registrationType:d}=e;return(0,s.useEffect)(()=>{if("true"===new URLSearchParams(window.location.search).get("email_change_success")){l.oR.success("Email address updated successfully!",{description:"Your email address has been changed and verified.",duration:5e3});let e=window.location.pathname;window.history.replaceState({},"",e)}},[]),(0,t.jsxs)(i.P.div,{initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-8",children:[(0,t.jsx)(i.P.div,{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20",children:(0,t.jsx)(n.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsx)("div",{className:"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700"}),(0,t.jsx)("div",{className:"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase",children:"Account Management"})]}),(0,t.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight",children:"Account Settings"}),(0,t.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed",children:"Manage your account security, contact information, and business preferences. Keep your profile up to date."})]})}),(0,t.jsxs)("div",{className:"space-y-12",children:[(0,t.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},children:(0,t.jsx)(P,{currentEmail:r,currentPhone:a,registrationType:d})}),(0,t.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3},children:(0,t.jsx)(V,{currentEmail:r,currentPhone:a,registrationType:d})}),(0,t.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.5},children:(0,t.jsx)(I,{})}),(0,t.jsx)(i.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.6},children:(0,t.jsx)(H,{})})]})]})}},53999:(e,r,a)=>{"use strict";a.d(r,{M0:()=>c,Yq:()=>u,cn:()=>i,gV:()=>n,gY:()=>o,kY:()=>l,vA:()=>d,vv:()=>m});var t=a(52596),s=a(39688);function i(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}function n(e){if(!e)return null;let r=e.trim();return(r.startsWith("+91")?r=r.substring(3):12===r.length&&r.startsWith("91")&&(r=r.substring(2)),/^\d{10}$/.test(r))?r:null}function l(e){if(!e||e.length<4)return"Invalid Phone";let r=e.substring(0,2),a=e.substring(e.length-2),t="*".repeat(e.length-4);return"".concat(r).concat(t).concat(a)}function d(e){if(!e||!e.includes("@"))return"Invalid Email";let r=e.split("@"),a=r[0],t=r[1];if(a.length<=2||t.length<=2||!t.includes("."))return"Email Hidden";let s=a.substring(0,2)+"*".repeat(a.length-2),i=t.split("."),n=i[0],l=i.slice(1).join("."),d=n.substring(0,2)+"*".repeat(n.length-2);return"".concat(s,"@").concat(d,".").concat(l)}function o(e){if(null==e||isNaN(e))return"0";let r=Math.abs(e),a=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(r<1e5)return r>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let t=a.length-1;t>=0;t--)if(r>=a[t].value)return(e/a[t].value).toFixed(1).replace(/\.0$/,"")+a[t].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let a={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return r&&(a.hour="2-digit",a.minute="2-digit",a.hour12=!0),e.toLocaleString("en-IN",a)}function m(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:r,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(a){return"".concat(r," ").concat(e.toFixed(2))}}},69916:(e,r,a)=>{"use strict";a.d(r,{NV:()=>d,UV:()=>l,sF:()=>o});var t=a(95155),s=a(12115),i=a(1184),n=a(53999);function l(e){let{className:r,containerClassName:a,...s}=e;return(0,t.jsx)(i.wE,{"data-slot":"input-otp",containerClassName:(0,n.cn)("flex items-center gap-2 has-disabled:opacity-50",a),className:(0,n.cn)("disabled:cursor-not-allowed",r),...s})}function d(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"input-otp-group",className:(0,n.cn)("flex items-center",r),...a})}function o(e){var r;let{index:a,className:l,...d}=e,o=s.useContext(i.dK),{char:c,hasFakeCaret:u,isActive:m}=null!=(r=null==o?void 0:o.slots[a])?r:{};return(0,t.jsxs)("div",{"data-slot":"input-otp-slot","data-active":m,className:(0,n.cn)("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]",l),...d,children:[c,u&&(0,t.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-caret-blink bg-foreground h-4 w-px duration-1000"})})]})}},82714:(e,r,a)=>{"use strict";a.d(r,{J:()=>n});var t=a(95155);a(12115);var s=a(40968),i=a(53999);function n(e){let{className:r,...a}=e;return(0,t.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...a})}},89852:(e,r,a)=>{"use strict";a.d(r,{p:()=>i});var t=a(95155);a(12115);var s=a(53999);function i(e){let{className:r,type:a,...i}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},97168:(e,r,a)=>{"use strict";a.d(r,{$:()=>d,r:()=>l});var t=a(95155);a(12115);var s=a(99708),i=a(74466),n=a(53999);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:a,size:i,asChild:d=!1,...o}=e,c=d?s.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:a,size:i,className:r})),...o})}},99840:(e,r,a)=>{"use strict";a.d(r,{Cf:()=>m,Es:()=>h,HM:()=>c,L3:()=>f,c7:()=>x,lG:()=>l,rr:()=>b,zM:()=>d});var t=a(95155);a(12115);var s=a(45821),i=a(54416),n=a(53999);function l(e){let{...r}=e;return(0,t.jsx)(s.bL,{"data-slot":"dialog",...r})}function d(e){let{...r}=e;return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...r})}function o(e){let{...r}=e;return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...r})}function c(e){let{...r}=e;return(0,t.jsx)(s.bm,{"data-slot":"dialog-close",...r})}function u(e){let{className:r,...a}=e;return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",r),...a})}function m(e){let{className:r,children:a,hideClose:l=!1,...d}=e;return(0,t.jsxs)(o,{"data-slot":"dialog-portal",children:[(0,t.jsx)(u,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",r),...d,children:[a,!l&&(0,t.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",r),...a})}function h(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",r),...a})}function f(e){let{className:r,...a}=e;return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",r),...a})}function b(e){let{className:r,...a}=e;return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",r),...a})}}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,8695,6874,2290,6671,375,1884,6199,221,7224,8441,1684,7358],()=>r(22419)),_N_E=e.O()}]);