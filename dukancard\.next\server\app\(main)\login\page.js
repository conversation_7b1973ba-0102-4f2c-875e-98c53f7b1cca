(()=>{var e={};e.id=4049,e.ids=[4049,9317],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3589:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},6943:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},6961:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["(main)",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56862)),"C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19559)),"C:\\web-app\\dukancard\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(main)/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11711:(e,r,t)=>{"use strict";t.d(r,{s:()=>c});var s=t(66244),a=t(54642),i=t(23007);function n(e,r){[...r].reverse().forEach(t=>{let s=e.getVariant(t);s&&(0,a.U)(e,s),e.variantChildren&&e.variantChildren.forEach(e=>{n(e,r)})})}function o(){let e=!1,r=new Set,t={subscribe:e=>(r.add(e),()=>void r.delete(e)),start(t,a){(0,s.V)(e,"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.");let n=[];return r.forEach(e=>{n.push((0,i._)(e,t,{transitionOverride:a}))}),Promise.all(n)},set:t=>((0,s.V)(e,"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook."),r.forEach(e=>{var r,s;r=e,Array.isArray(s=t)?n(r,s):"string"==typeof s?n(r,[s]):(0,a.U)(r,s)})),stop(){r.forEach(e=>{e.values.forEach(e=>e.stop())})},mount:()=>(e=!0,()=>{e=!1,t.stop()})};return t}var d=t(72789),l=t(15124);let c=function(){let e=(0,d.M)(o);return(0,l.E)(e.mount,[]),e}},11997:e=>{"use strict";e.exports=require("punycode")},12941:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19559:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(37413);t(61120);var a=t(14890),i=t(60644),n=t(11637),o=t(95006),d=t(92506),l=t(46501),c=t(21886),u=t(23392);function m({children:e}){return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(u.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,s.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,s.jsx)(a.default,{}),(0,s.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,s.jsx)(i.default,{}),(0,s.jsx)(o.default,{}),(0,s.jsx)(n.default,{}),(0,s.jsx)(d.default,{}),(0,s.jsx)(l.default,{}),(0,s.jsx)(c.default,{excludePaths:["/dashboard"]})]})})})}},20798:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},23676:(e,r,t)=>{"use strict";t.d(r,{LE:()=>a,oX:()=>i});var s=t(68567);let a=s.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});s.z.object({email:s.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),s.z.object({email:s.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:s.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),s.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character");let i=s.z.object({mobile:a,password:s.z.string().trim().min(1,{message:"Password is required"})})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34387:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>z});var s=t(60687),a=t(16189),i=t(43210),n=t(77882),o=t(55192),d=t(52581),l=t(6475);let c=(0,l.createServerReference)("40efc1a848a2990bc559f61f9b7a0d0ed0084879a9",l.callServer,void 0,l.findSourceMapURL,"sendOTP"),u=(0,l.createServerReference)("40e65692ff5facc6e825e8935d9f98c2d059cf07bf",l.callServer,void 0,l.findSourceMapURL,"verifyOTP"),m=(0,l.createServerReference)("402d5a5d450959a841489083e44bf78e39b9046f6a",l.callServer,void 0,l.findSourceMapURL,"loginWithMobilePassword");var p=t(63442),g=t(27605),b=t(45880),x=t(24934),f=t(58164),h=t(68988),v=t(99008),y=t(41862),w=t(70334);let j=b.z.object({email:b.z.string().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),P=b.z.object({otp:b.z.string().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})});function k({step:e,email:r,countdown:t,isPending:a,onEmailSubmit:i,onOTPSubmit:n,onResendOTP:o,onBackToEmail:d}){let l=(0,g.mN)({resolver:(0,p.u)(j),defaultValues:{email:""}}),c=(0,g.mN)({resolver:(0,p.u)(P),defaultValues:{otp:""}});return"email"===e?(0,s.jsx)(f.lV,{...l,children:(0,s.jsxs)("form",{onSubmit:l.handleSubmit(i),className:"space-y-4 sm:space-y-6",children:[(0,s.jsx)(f.zB,{control:l.control,name:"email",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-foreground text-sm sm:text-base",children:"Email Address"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{id:"email-login-field",placeholder:"<EMAIL>",type:"email",...e,className:"bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(x.$,{type:"submit",className:"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",disabled:a,children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Sending OTP..."]}):(0,s.jsxs)(s.Fragment,{children:["Continue ",(0,s.jsx)(w.A,{className:"w-5 h-5 ml-2"})]})}),(0,s.jsx)("div",{className:"text-center mt-4 text-xs sm:text-sm",children:(0,s.jsx)("span",{className:"text-muted-foreground",children:"New to Dukancard? No worries! We'll create your account automatically."})})]})}):(0,s.jsx)(f.lV,{...c,children:(0,s.jsxs)("form",{onSubmit:c.handleSubmit(e=>{n({email:r,otp:e.otp})}),className:"space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{className:"text-center mb-4",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"We've sent a 6-digit code to"}),(0,s.jsx)("p",{className:"text-sm font-medium text-foreground",children:r}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:"Code expires in 24 hours"})]}),(0,s.jsx)(f.zB,{control:c.control,name:"otp",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-foreground text-sm sm:text-base text-center block",children:"Enter Verification Code"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(v.UV,{id:"otp-login-field",maxLength:6,value:e.value,onChange:r=>e.onChange(r),className:"gap-2",children:(0,s.jsxs)(v.NV,{children:[(0,s.jsx)(v.sF,{index:0,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,s.jsx)(v.sF,{index:1,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,s.jsx)(v.sF,{index:2,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,s.jsx)(v.sF,{index:3,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,s.jsx)(v.sF,{index:4,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,s.jsx)(v.sF,{index:5,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"})]})})})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(x.$,{type:"submit",className:"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",disabled:a,children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Verifying..."]}):(0,s.jsxs)(s.Fragment,{children:["Verify & Sign In ",(0,s.jsx)(w.A,{className:"w-5 h-5 ml-2"})]})}),(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-3 mt-4 text-xs sm:text-sm",children:[(0,s.jsx)("button",{type:"button",onClick:o,disabled:t>0,className:`${t>0?"text-muted-foreground cursor-not-allowed":"text-primary dark:text-[var(--brand-gold)] hover:underline cursor-pointer"}`,children:t>0?`Resend OTP in ${t}s`:"Resend OTP"}),(0,s.jsx)("button",{type:"button",onClick:d,className:"text-muted-foreground hover:text-foreground cursor-pointer",children:"← Change email address"})]})]})})}var _=t(87033),N=t(85814),A=t.n(N);function C({isPending:e,onSubmit:r}){let t=(0,g.mN)({resolver:(0,p.u)(_.oX),defaultValues:{mobile:"",password:""}});return(0,s.jsx)(f.lV,{...t,children:(0,s.jsxs)("form",{onSubmit:t.handleSubmit(r),className:"space-y-4 sm:space-y-6",children:[(0,s.jsx)(f.zB,{control:t.control,name:"mobile",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-foreground text-sm sm:text-base",children:"Mobile Number"}),(0,s.jsx)(f.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground",children:"+91"}),(0,s.jsx)(h.p,{placeholder:"9876543210",type:"tel",...e,onChange:r=>{let t=r.target.value;(t=(t=t.replace(/^\+91/,"")).replace(/\D/g,"")).length>10&&(t=t.slice(0,10)),e.onChange(t)},onKeyDown:e=>{let r=/^[0-9]$/.test(e.key),t=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);r||t||e.preventDefault()},className:"pl-12 bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base",maxLength:10})]})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:t.control,name:"password",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-foreground text-sm sm:text-base",children:"Password"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"••••••••",type:"password",...e,className:"bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(x.$,{type:"submit",className:"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",disabled:e,children:e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Signing in..."]}):(0,s.jsxs)(s.Fragment,{children:["Sign In ",(0,s.jsx)(w.A,{className:"w-5 h-5 ml-2"})]})}),(0,s.jsx)("div",{className:"text-center mt-4 text-xs sm:text-sm",children:(0,s.jsxs)("span",{className:"text-muted-foreground",children:["Don't have an account?"," ",(0,s.jsx)(A(),{href:"/register",className:"text-primary dark:text-[var(--brand-gold)] hover:underline font-medium",children:"Register here"})]})})]})})}function T({authMethod:e,step:r,onMethodChange:t}){return("email-otp"!==e||"email"!==r)&&"mobile-password"!==e?null:(0,s.jsxs)("div",{className:"flex rounded-lg bg-muted p-1 mb-6",children:[(0,s.jsx)("button",{type:"button",onClick:()=>t("email-otp"),className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${"email-otp"===e?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:"Email OTP"}),(0,s.jsx)("button",{type:"button",onClick:()=>t("mobile-password"),className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${"mobile-password"===e?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:"Mobile + Password"})]})}var S=t(38398);function R({redirectSlug:e,message:r,disabled:t}){let[a,n]=(0,i.useState)(null);async function o(t){try{n(t);let s=(0,S.U)(),a=`${window.location.origin}/auth/callback?closeWindow=true`;e&&(a+=`&redirect=${encodeURIComponent(e)}`),r&&(a+=`&message=${encodeURIComponent(r)}`);let{data:i,error:o}=await s.auth.signInWithOAuth({provider:t,options:{redirectTo:a,skipBrowserRedirect:!0,queryParams:{access_type:"offline",prompt:"select_account"}}});if(o){d.oR.error("Login failed",{description:o.message}),n(null);return}i?.url?(window.open(i.url,"_blank"),d.oR.info("Google sign-in opened in a new tab",{description:"Please complete the sign-in process in the new tab.",duration:5e3}),setTimeout(()=>{n(null)},1e3)):(d.oR.error("Failed to start Google sign-in",{description:"Please try again or use email login."}),n(null))}catch(r){let e=r instanceof Error?r.message:"An unexpected error occurred. Please try again.";d.oR.error("Login failed",{description:e}),n(null)}}return(0,s.jsx)("div",{className:"flex justify-center mb-5 sm:mb-6",children:(0,s.jsx)(x.$,{variant:"outline",className:"cursor-pointer bg-background hover:bg-muted border-border dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-800 w-full py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-foreground",onClick:()=>o("google"),disabled:!!a||t,children:"google"===a?(0,s.jsx)(y.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"w-4 h-4 mr-2",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Login with Google"]})})})}var O=t(69317);function z(){let e=(0,a.useRouter)();(0,a.useSearchParams)();let[r,t]=(0,i.useTransition)(),[l,p]=(0,i.useState)(null),[g,b]=(0,i.useState)(null),[x,f]=(0,i.useState)("email-otp"),[h,v]=(0,i.useState)("email"),[y,w]=(0,i.useState)(""),[j,P]=(0,i.useState)(0);async function _(){try{let r=(0,S.U)(),{data:{user:t}}=await r.auth.getUser();if(!t){console.error("No user found after login"),e.push("/?view=home");return}let s=await (0,O.getPostLoginRedirectPath)(r,t.id);if(l){if(l.includes("://")||l.startsWith("//")){console.warn("Attempted redirect to an external or malformed URL. Redirecting to default path."),e.push(s);return}e.push(`/${l}${g?`?message=${encodeURIComponent(g)}`:""}`)}else e.push(s)}catch(r){console.error("Error determining redirect path:",r),e.push("/?view=home")}}return(0,s.jsx)("div",{className:"w-full max-w-[90%] sm:max-w-md md:max-w-lg",children:(0,s.jsx)(n.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.2},children:(0,s.jsxs)(o.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg dark:shadow-[var(--brand-gold)]/10",children:[(0,s.jsxs)("div",{className:"text-center mb-6 sm:mb-8",children:[(0,s.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-foreground mb-1 sm:mb-2",children:"email-otp"===x&&"otp"===h?"Enter Verification Code":"Welcome to Dukancard"}),(0,s.jsx)("p",{className:"text-sm sm:text-base text-muted-foreground",children:"email-otp"===x&&"otp"===h?"Check your email for the 6-digit code":"email-otp"===x?"Sign in or create your account with email":"Sign in with your mobile number and password"}),g&&(0,s.jsx)("div",{className:`mt-4 p-2 sm:p-3 rounded-lg ${g.toLowerCase().includes("error")||g.toLowerCase().includes("failed")?"bg-destructive/10 text-destructive":"bg-green-500/10 text-green-600 dark:text-green-400"}`,children:(0,s.jsx)("p",{className:"text-xs sm:text-sm",children:g})})]}),(0,s.jsx)(T,{authMethod:x,step:h,onMethodChange:function(e){e!==x&&(f(e),v("email"))}}),(0,s.jsx)(R,{redirectSlug:l,message:g,disabled:r}),(0,s.jsxs)("div",{className:"relative mb-5 sm:mb-6",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-border"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,s.jsx)("span",{className:"bg-card px-2 text-muted-foreground",children:"email-otp"===x&&"email"===h?"Or continue with email":"mobile-password"===x?"Or continue with mobile":"Or use Google instead"})})]}),"email-otp"===x?(0,s.jsx)(k,{step:h,email:y,countdown:j,isPending:r,onEmailSubmit:function(e){t(async()=>{try{let r=await c(e);if(!r.success){if("isConfigurationError"in r&&r.isConfigurationError)return void d.oR.error("Configuration Error",{description:r.error,duration:1e4});d.oR.error("Failed to send OTP",{description:r.error});return}d.oR.success("OTP sent!",{description:r.message}),w(e.email),v("otp"),P(60)}catch(e){d.oR.error("Failed to send OTP",{description:"An unexpected error occurred. Please try again."})}})},onOTPSubmit:function(e){t(async()=>{try{let r=await u({email:e.email,otp:e.otp});if(!r.success)return void d.oR.error("OTP verification failed",{description:r.error});d.oR.success("Sign in successful!",{description:"Redirecting to your dashboard..."}),await _()}catch(e){d.oR.error("OTP verification failed",{description:"An unexpected error occurred. Please try again."})}})},onResendOTP:function(){j>0||t(async()=>{try{let e=await c({email:y});if(!e.success){if("isConfigurationError"in e&&e.isConfigurationError)return void d.oR.error("Configuration Error",{description:e.error,duration:1e4});d.oR.error("Failed to resend OTP",{description:e.error});return}d.oR.success("OTP resent!",{description:e.message}),P(60)}catch(e){d.oR.error("Failed to resend OTP",{description:"An unexpected error occurred. Please try again."})}})},onBackToEmail:function(){v("email"),w("")}}):(0,s.jsx)(C,{isPending:r,onSubmit:function(e){t(async()=>{try{let r=await m(e);if(!r.success)return void d.oR.error("Login failed",{description:r.error});d.oR.success("Sign in successful!",{description:"Redirecting to your dashboard..."}),await _()}catch(e){d.oR.error("Login failed",{description:"An unexpected error occurred. Please try again."})}})}})]})})})}},34631:e=>{"use strict";e.exports=require("tls")},39164:(e,r,t)=>{Promise.resolve().then(t.bind(t,74982)),Promise.resolve().then(t.bind(t,72194))},39390:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var s=t(60687);t(43210);var a=t(78148),i=t(96241);function n({className:e,...r}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},41862:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55192:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>c});var s=t(60687);t(43210);var a=t(96241);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56399:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=56399,e.exports=r},56862:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,generateMetadata:()=>o});var s=t(37413),a=t(72194),i=t(61120),n=t(74982);async function o(){return{title:"Sign In",description:"Sign in to your Dukancard account or create a new account with just your email address.",robots:"noindex, follow"}}function d(){return(0,s.jsxs)("div",{className:"w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6 relative overflow-hidden",children:[(0,s.jsx)(n.default,{}),(0,s.jsx)(i.Suspense,{fallback:(0,s.jsxs)("div",{className:"flex flex-col justify-center items-center min-h-screen gap-2 relative z-10",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--brand-gold)]"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading sign in form..."})]}),children:(0,s.jsx)(a.LoginForm,{})})]})}},58164:(e,r,t)=>{"use strict";t.d(r,{C5:()=>h,MJ:()=>x,Rr:()=>f,eI:()=>g,lR:()=>b,lV:()=>l,zB:()=>u});var s=t(60687),a=t(43210),i=t(8730),n=t(27605),o=t(96241),d=t(39390);let l=n.Op,c=a.createContext({}),u=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(n.xI,{...e})}),m=()=>{let e=a.useContext(c),r=a.useContext(p),{getFieldState:t}=(0,n.xW)(),s=(0,n.lN)({name:e.name}),i=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=r;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...i}},p=a.createContext({});function g({className:e,...r}){let t=a.useId();return(0,s.jsx)(p.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...r})})}function b({className:e,...r}){let{error:t,formItemId:a}=m();return(0,s.jsx)(d.J,{"data-slot":"form-label","data-error":!!t,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...r})}function x({...e}){let{error:r,formItemId:t,formDescriptionId:a,formMessageId:n}=m();return(0,s.jsx)(i.DX,{"data-slot":"form-control",id:t,"aria-describedby":r?`${a} ${n}`:`${a}`,"aria-invalid":!!r,...e})}function f({className:e,...r}){let{formDescriptionId:t}=m();return(0,s.jsx)("p",{"data-slot":"form-description",id:t,className:(0,o.cn)("text-muted-foreground text-sm",e),...r})}function h({className:e,...r}){let{error:t,formMessageId:a}=m(),i=t?String(t?.message??""):r.children;return i?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,o.cn)("text-destructive text-sm",e),...r,children:i}):null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65422:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"402d5a5d450959a841489083e44bf78e39b9046f6a":()=>m,"40e65692ff5facc6e825e8935d9f98c2d059cf07bf":()=>u,"40efc1a848a2990bc559f61f9b7a0d0ed0084879a9":()=>c});var s=t(91199);t(42087);var a=t(76881),i=t(23676);let n={over_email_send_rate_limit:"Email rate limit exceeded. Please wait before requesting another OTP.",over_request_rate_limit:"Too many requests. Please wait a few minutes before trying again.",over_sms_send_rate_limit:"Too many SMS messages sent. Please wait before requesting another OTP.",otp_expired:"OTP has expired. Please request a new one.",otp_disabled:"OTP authentication is currently disabled.",bad_jwt:"Invalid authentication token. Please sign in again.",session_expired:"Your session has expired. Please sign in again.",session_not_found:"Session not found. Please sign in again.",refresh_token_not_found:"Authentication expired. Please sign in again.",refresh_token_already_used:"Authentication expired. Please sign in again.",user_not_found:"User account not found.",user_banned:"Your account has been temporarily suspended.",email_not_confirmed:"Please verify your email address before signing in.",phone_not_confirmed:"Please verify your phone number before signing in.",invalid_credentials:"Invalid email or password.",signup_disabled:"New account registration is currently disabled.",email_exists:"An account with this email already exists.",phone_exists:"An account with this phone number already exists.",weak_password:"Password does not meet security requirements.",email_address_invalid:"Please enter a valid email address.",email_address_not_authorized:"This email address is not authorized for registration.",provider_disabled:"This sign-in method is currently disabled.",oauth_provider_not_supported:"This sign-in provider is not supported.",provider_email_needs_verification:"Please verify your email address to complete sign-in.",validation_failed:"Please check your input and try again.",bad_json:"Invalid request format. Please try again.",mfa_challenge_expired:"MFA challenge expired. Please try again.",mfa_verification_failed:"Invalid MFA code. Please try again.",insufficient_aal:"Additional authentication required.",captcha_failed:"CAPTCHA verification failed. Please try again.",conflict:"A conflict occurred. Please try again.",request_timeout:"Request timed out. Please try again.",unexpected_failure:"An unexpected error occurred. Please try again.",same_password:"New password must be different from your current password.",flow_state_expired:"Authentication session expired. Please start over.",flow_state_not_found:"Authentication session not found. Please start over.",reauthentication_needed:"Please verify your identity to continue.",reauthentication_not_valid:"Identity verification failed. Please try again."},o="An error occurred. Please try again.";function d(e){if(!e)return o;if("code"in e&&e.code){let r=n[e.code];if(r)return r}if(e.message){let r=e.message.toLowerCase();if(r.includes("failed to fetch")||r.includes("network request failed")||r.includes("network error"))return"Network error. Please check your internet connection and try again.";if(r.includes("token has expired")||r.includes("expired"))return"Your session has expired. Please sign in again.";if(r.includes("invalid token")||r.includes("invalid otp"))return"Invalid code. Please check and try again.";if(r.includes("rate limit")||r.includes("too many"))return"Too many attempts. Please wait before trying again.";if(r.includes("email already exists")||r.includes("user already exists"))return"An account with this email already exists.";if(r.includes("invalid email"))return"Please enter a valid email address.";if(r.includes("weak password"))return"Password does not meet security requirements."}return o}function l(e){return!!e&&"code"in e&&!!e.code&&"over_email_send_rate_limit"===e.code}async function c(e){let{email:r}=e,t=r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?{isValid:!0}:{isValid:!1,message:"Please enter a valid email address"}:{isValid:!1,message:"Email is required"};if(!t.isValid)return{success:!1,error:t.message};try{let e=await (0,a.createClient)(),{error:t}=await e.auth.signInWithOtp({email:r,options:{shouldCreateUser:!0,data:{auth_type:"email"}}});if(t){if(l(t))return{success:!1,error:"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.",isConfigurationError:!0};return{success:!1,error:d(t)}}return{success:!0,message:"OTP sent to your email address. Please check your inbox."}}catch(e){if(l(e))return{success:!1,error:"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.",isConfigurationError:!0};return{success:!1,error:d(e)}}}async function u(e){let{email:r,otp:t}=e,s=t?6!==t.length?{isValid:!1,message:"OTP must be 6 digits"}:/^\d{6}$/.test(t)?{isValid:!0}:{isValid:!1,message:"OTP must contain only numbers"}:{isValid:!1,message:"OTP is required"};if(!s.isValid)return{success:!1,error:s.message};let i=await (0,a.createClient)();try{let{data:e,error:s}=await i.auth.verifyOtp({email:r,token:t,type:"email"});if(s)return{success:!1,error:d(s)};return{success:!0,data:e,message:"Successfully signed in!"}}catch(e){return{success:!1,error:d(e)}}}async function m(e){let r=i.oX.safeParse(e);if(!r.success)return{success:!1,error:"Invalid mobile number or password format"};let{mobile:t,password:s}=r.data,n=await (0,a.createClient)();try{let e=`+91${t}`,{data:r,error:a}=await n.auth.signInWithPassword({phone:e,password:s});if(a)return{success:!1,error:d(a)};return{success:!0,data:r,message:"Successfully signed in!"}}catch(e){return{success:!1,error:d(e)}}}(0,t(33331).D)([c,u,m]),(0,s.A)(c,"40efc1a848a2990bc559f61f9b7a0d0ed0084879a9",null),(0,s.A)(u,"40e65692ff5facc6e825e8935d9f98c2d059cf07bf",null),(0,s.A)(m,"402d5a5d450959a841489083e44bf78e39b9046f6a",null)},68988:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(96241);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},69317:(e,r,t)=>{"use strict";async function s(e,r){try{let[t,s]=await Promise.all([e.from("customer_profiles").select("id").eq("id",r),e.from("business_profiles").select("id, business_slug").eq("id",r)]);if(t.error||s.error){if(console.error("[redirectAfterLogin] Supabase query error:",t.error,s.error),t.error?.code==="PGRST116"||s.error?.code==="PGRST116"||t.error?.message?.toLowerCase().includes("no rows")||s.error?.message?.toLowerCase().includes("no rows"))return"/choose-role";return"/?view=home"}if(t.data&&Array.isArray(t.data)&&t.data.length>0)return"/dashboard/customer";if(s.data&&Array.isArray(s.data)&&s.data.length>0){if(s.data[0].business_slug)return"/dashboard/business";return"/onboarding"}return"/choose-role"}catch(e){return console.error("[redirectAfterLogin] Unexpected error:",e),"/?view=home"}}t.d(r,{getPostLoginRedirectPath:()=>s})},70334:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},72194:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx","LoginForm")},74075:e=>{"use strict";e.exports=require("zlib")},74982:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\components\\\\auth\\\\AuthPageBackground.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx","default")},76881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{createClient:()=>a});var s=t(63014);async function a(){let e="https://rnjolcoecogzgglnblqn.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";if(!e||!r)throw Error("Supabase environment variables are not set.");let a=null,i=null;try{let{headers:e,cookies:r}=await Promise.all([t.e(4208),t.e(4659)]).then(t.bind(t,74208));a=await e(),i=await r()}catch(e){console.warn("next/headers not available in this context, using fallback")}return("true"===process.env.PLAYWRIGHT_TESTING||a&&"true"===a.get("x-playwright-testing"))&&a?function(e){let r=e.get("x-test-auth-state"),t=e.get("x-test-user-type"),s="customer"===t||"business"===t,a=e.get("x-test-business-slug"),i=e.get("x-test-plan-id")||"free";return{auth:{getUser:async()=>"authenticated"===r?{data:{user:{id:"test-user-id",email:"<EMAIL>"}},error:null}:{data:{user:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},getSession:async()=>"authenticated"===r?{data:{session:{user:{id:"test-user-id",email:"<EMAIL>"}}},error:null}:{data:{session:null},error:{message:"Unauthorized",name:"AuthApiError",status:401}},signInWithOtp:async()=>({data:{user:null,session:null},error:null}),signOut:async()=>({error:null})},from:e=>(function(e,r,t,s,a){let i=()=>{var i,n,o,d,l;return i=e,n=r,o=t,d=s,l=a,"customer_profiles"===i?{data:o&&"customer"===n?{id:"test-user-id",name:"Test Customer",avatar_url:null,phone:"+1234567890",email:"<EMAIL>",address:"Test Address",city:"Test City",state:"Test State",pincode:"123456"}:null,error:null}:"business_profiles"===i?{data:o&&"business"===n?{id:"test-user-id",business_slug:d||null,trial_end_date:null,has_active_subscription:!0,business_name:"Test Business",city_slug:"test-city",state_slug:"test-state",locality_slug:"test-locality",pincode:"123456",business_description:"Test business description",business_category:"retail",phone:"+1234567890",email:"<EMAIL>",website:"https://testbusiness.com"}:null,error:null}:"payment_subscriptions"===i?{data:"business"===n?{id:"test-subscription-id",plan_id:l,business_profile_id:"test-user-id",status:"active",created_at:"2024-01-01T00:00:00Z"}:null,error:null}:"products"===i?{data:"business"===n?[{id:"test-product-1",name:"Test Product 1",price:100,business_profile_id:"test-user-id",available:!0},{id:"test-product-2",name:"Test Product 2",price:200,business_profile_id:"test-user-id",available:!1}]:[],error:null}:{data:null,error:null}},n=e=>({select:r=>n(e),eq:(r,t)=>n(e),neq:(r,t)=>n(e),gt:(r,t)=>n(e),gte:(r,t)=>n(e),lt:(r,t)=>n(e),lte:(r,t)=>n(e),like:(r,t)=>n(e),ilike:(r,t)=>n(e),is:(r,t)=>n(e),in:(r,t)=>n(e),contains:(r,t)=>n(e),containedBy:(r,t)=>n(e),rangeGt:(r,t)=>n(e),rangeGte:(r,t)=>n(e),rangeLt:(r,t)=>n(e),rangeLte:(r,t)=>n(e),rangeAdjacent:(r,t)=>n(e),overlaps:(r,t)=>n(e),textSearch:(r,t)=>n(e),match:r=>n(e),not:(r,t,s)=>n(e),or:r=>n(e),filter:(r,t,s)=>n(e),order:(r,t)=>n(e),limit:(r,t)=>n(e),range:(r,t,s)=>n(e),abortSignal:r=>n(e),single:async()=>i(),maybeSingle:async()=>i(),then:async e=>{let r=i();return e?e(r):r},data:e||[],error:null,count:e?e.length:0,status:200,statusText:"OK"});return{select:e=>n(),insert:e=>({select:r=>({single:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),maybeSingle:async()=>({data:Array.isArray(e)?e[0]:e,error:null}),then:async r=>{let t={data:Array.isArray(e)?e:[e],error:null};return r?r(t):t}}),then:async r=>{let t={data:Array.isArray(e)?e:[e],error:null};return r?r(t):t}}),update:e=>n(e),upsert:e=>n(e),delete:()=>n(),rpc:(e,r)=>n()}})(e,t,s,a,i)}}(a):i?(0,s.createServerClient)(e,r,{cookies:{getAll:async()=>await i.getAll(),async setAll(e){try{for(let{name:r,value:t,options:s}of e)await i.set(r,t,s)}catch{}}}}):(0,s.createServerClient)(e,r,{cookies:{getAll:()=>[],setAll(){}}})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80352:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var s=t(60687),a=t(43210),i=t(11711),n=t(77882);function o(){let[e,r]=(0,a.useState)(!1),[t,o]=(0,a.useState)(!1),d=(0,i.s)(),l=(0,i.s)(),c=(0,i.s)(),u=(0,i.s)(),m=(0,i.s)(),p=(0,i.s)();return(0,s.jsx)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.P.div,{animate:d,className:"absolute inset-0 opacity-40 dark:opacity-30",style:{background:`radial-gradient(circle at 50% 50%,
                var(--brand-gold) 0%,
                rgba(var(--brand-gold-rgb), 0.3) 25%,
                rgba(var(--brand-gold-rgb), 0.1) 50%,
                rgba(0, 0, 255, 0.1) 75%,
                rgba(0, 0, 255, 0.05) 100%)`,filter:t?"blur(60px)":"blur(80px)"}}),(0,s.jsx)(n.P.div,{animate:l,className:"absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold-rgb)]/5 blur-3xl dark:bg-[var(--brand-gold-rgb)]/10 opacity-70"}),(0,s.jsx)(n.P.div,{animate:c,className:"absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10 opacity-70"}),(0,s.jsx)(n.P.div,{animate:u,className:"absolute bottom-0 left-1/4 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10 opacity-60"}),(0,s.jsxs)("svg",{className:"absolute inset-0 w-full h-full opacity-10 dark:opacity-20 pointer-events-none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("defs",{children:(0,s.jsxs)("filter",{id:"glow",x:"-50%",y:"-50%",width:"200%",height:"200%",children:[(0,s.jsx)("feGaussianBlur",{stdDeviation:"2",result:"blur"}),(0,s.jsx)("feComposite",{in:"SourceGraphic",in2:"blur",operator:"over",result:"glow"})]})}),(0,s.jsx)(n.P.line,{animate:m,x1:"20%",y1:"20%",x2:"80%",y2:"80%",stroke:"var(--brand-gold)",strokeWidth:"0.5",strokeOpacity:"0.3",filter:"url(#glow)"}),(0,s.jsx)(n.P.circle,{animate:p,cx:"50%",cy:"50%",r:"2",fill:"var(--brand-gold)",filter:"url(#glow)"})]})]})})}},81630:e=>{"use strict";e.exports=require("http")},87033:(e,r,t)=>{"use strict";t.d(r,{LE:()=>a,oX:()=>i});var s=t(45880);let a=s.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});s.z.object({email:s.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),s.z.object({email:s.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:s.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),s.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character");let i=s.z.object({mobile:a,password:s.z.string().trim().min(1,{message:"Password is required"})})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97308:(e,r,t)=>{Promise.resolve().then(t.bind(t,80352)),Promise.resolve().then(t.bind(t,34387))},99008:(e,r,t)=>{"use strict";t.d(r,{NV:()=>d,UV:()=>o,sF:()=>l});var s=t(60687),a=t(43210),i=t(78119),n=t(96241);function o({className:e,containerClassName:r,...t}){return(0,s.jsx)(i.wE,{"data-slot":"input-otp",containerClassName:(0,n.cn)("flex items-center gap-2 has-disabled:opacity-50",r),className:(0,n.cn)("disabled:cursor-not-allowed",e),...t})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"input-otp-group",className:(0,n.cn)("flex items-center",e),...r})}function l({index:e,className:r,...t}){let o=a.useContext(i.dK),{char:d,hasFakeCaret:l,isActive:c}=o?.slots[e]??{};return(0,s.jsxs)("div",{"data-slot":"input-otp-slot","data-active":c,className:(0,n.cn)("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]",r),...t,children:[d,l&&(0,s.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-caret-blink bg-foreground h-4 w-px duration-1000"})})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,6724,2997,1107,7065,3206,5880,8567,3442,634,3037,6177],()=>t(6961));module.exports=s})();