"use strict";exports.id=5447,exports.ids=[5447],exports.modules={75447:(e,s,t)=>{t.a(e,async(e,o)=>{try{t.d(s,{handleSubscriptionUpdated:()=>c});var r=t(65193),a=t(28485),n=e([a]);async function c(e,s,t){let o=null;try{let s=e.payload.subscription;if(!s||!s.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let n=s.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription updated: ${n}`);let c=(0,r.extractWebhookTimestamp)(e);o={subscriptionId:n,eventType:"subscription.updated",eventId:t||`updated_${n}_${Date.now()}`,payload:e,webhookTimestamp:c};let i=await a.webhookProcessor.processWebhookEvent(o);if(!i.shouldProcess)return{success:i.success,message:i.message};return{success:!0,message:"Subscription update acknowledged"}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription updated:",e),{success:!1,message:`Error handling subscription updated: ${e instanceof Error?e.message:String(e)}`}}}a=(n.then?(await n)():n)[0],o()}catch(e){o(e)}})}};