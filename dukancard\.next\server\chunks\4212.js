"use strict";exports.id=4212,exports.ids=[4212],exports.modules={13091:(t,e,r)=>{r.d(e,{b:()=>a});var n=r(47138);function a(t,e){return(0,n.a)(t)-(0,n.a)(e)}},14889:(t,e,r)=>{r.d(e,{z:()=>a});var n=r(47138);function a(t,e){let r=(0,n.a)(t),a=(0,n.a)(e),s=r.getTime()-a.getTime();return s<0?-1:s>0?1:s}},44064:(t,e,r)=>{r.d(e,{O:()=>s});var n=r(84323),a=r(13091);function s(t,e,r){let s=(0,a.b)(t,e)/1e3;return(0,n.u)(r?.roundingMethod)(s)}},74212:(t,e,r)=>{r.d(e,{m:()=>d});var n=r(35780),a=r(14889),s=r(11392),u=r(87919),o=r(44064),i=r(47138),f=r(3211),l=r(9903),c=r(79943);function d(t,e){return function(t,e,r){let n,d,m,h=(0,l.q)(),M=r?.locale??h.locale??f.c,D=(0,a.z)(t,e);if(isNaN(D))throw RangeError("Invalid time value");let g=Object.assign({},r,{addSuffix:r?.addSuffix,comparison:D});D>0?(n=(0,i.a)(e),d=(0,i.a)(t)):(n=(0,i.a)(t),d=(0,i.a)(e));let x=(0,o.O)(d,n),X=Math.round((x-((0,c.G)(d)-(0,c.G)(n))/1e3)/60);if(X<2)if(r?.includeSeconds)if(x<5)return M.formatDistance("lessThanXSeconds",5,g);else if(x<10)return M.formatDistance("lessThanXSeconds",10,g);else if(x<20)return M.formatDistance("lessThanXSeconds",20,g);else if(x<40)return M.formatDistance("halfAMinute",0,g);else if(x<60)return M.formatDistance("lessThanXMinutes",1,g);else return M.formatDistance("xMinutes",1,g);else if(0===X)return M.formatDistance("lessThanXMinutes",1,g);else return M.formatDistance("xMinutes",X,g);if(X<45)return M.formatDistance("xMinutes",X,g);if(X<90)return M.formatDistance("aboutXHours",1,g);if(X<s.F6){let t=Math.round(X/60);return M.formatDistance("aboutXHours",t,g)}if(X<2520)return M.formatDistance("xDays",1,g);else if(X<s.Nw){let t=Math.round(X/s.F6);return M.formatDistance("xDays",t,g)}else if(X<2*s.Nw)return m=Math.round(X/s.Nw),M.formatDistance("aboutXMonths",m,g);if((m=(0,u.W)(d,n))<12){let t=Math.round(X/s.Nw);return M.formatDistance("xMonths",t,g)}{let t=m%12,e=Math.trunc(m/12);return t<3?M.formatDistance("aboutXYears",e,g):t<9?M.formatDistance("overXYears",e,g):M.formatDistance("almostXYears",e+1,g)}}(t,(0,n.w)(t,Date.now()),e)}},84323:(t,e,r)=>{r.d(e,{u:()=>n});function n(t){return e=>{let r=(t?Math[t]:Math.trunc)(e);return 0===r?0:r}}},87919:(t,e,r)=>{r.d(e,{W:()=>s});var n=r(14889),a=r(47138);function s(t,e){let r,s=(0,a.a)(t),u=(0,a.a)(e),o=(0,n.z)(s,u),i=Math.abs(function(t,e){let r=(0,a.a)(t),n=(0,a.a)(e);return 12*(r.getFullYear()-n.getFullYear())+(r.getMonth()-n.getMonth())}(s,u));if(i<1)r=0;else{1===s.getMonth()&&s.getDate()>27&&s.setDate(30),s.setMonth(s.getMonth()-o*i);let e=(0,n.z)(s,u)===-o;(function(t){let e=(0,a.a)(t);return+function(t){let e=(0,a.a)(t);return e.setHours(23,59,59,999),e}(e)==+function(t){let e=(0,a.a)(t),r=e.getMonth();return e.setFullYear(e.getFullYear(),r+1,0),e.setHours(23,59,59,999),e}(e)})((0,a.a)(t))&&1===i&&1===(0,n.z)(t,u)&&(e=!1),r=o*(i-Number(e))}return 0===r?0:r}}};