"use strict";exports.id=3895,exports.ids=[3895],exports.modules={5851:(e,t,r)=>{r.d(t,{default:()=>u});var s=r(60687),a=r(43210),n=r(85814),o=r.n(n),l=r(24934),i=r(97051),c=r(56748),d=r(37033);function u({initialSubscriptions:e,onUnsubscribeSuccess:t,showUnsubscribe:r=!0,variant:n="default",emptyMessage:u="No subscriptions found.",emptyDescription:p="Subscribe to profiles to see them here.",showDiscoverButton:m=!1}){let[b,h]=(0,a.useState)(e),x=e=>{h(t=>t.filter(t=>t.id!==e)),t&&t(e)};return 0===b.length?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-20 text-center",children:[(0,s.jsxs)("div",{className:"relative mb-8",children:[(0,s.jsx)("div",{className:"absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"}),(0,s.jsx)("div",{className:"relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg",children:(0,s.jsx)(i.A,{className:"w-10 h-10 text-primary"})})]}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3",children:u}),(0,s.jsx)("p",{className:"text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2",children:p}),m&&(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)(l.$,{asChild:!0,variant:"outline",className:"gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",children:(0,s.jsxs)(o(),{href:"/discover",target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),"Discover Businesses"]})})})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:b.map((e,t)=>{let a=e.profile;return a?(0,s.jsx)("div",{className:"transform transition-all duration-200 hover:scale-[1.02]",children:(0,s.jsx)(d.default,{subscriptionId:e.id,profile:a,onUnsubscribeSuccess:r?x:void 0,showUnsubscribe:r,variant:n})},e.id):null})})}},8018:(e,t,r)=>{r.d(t,{SubscriptionListSkeleton:()=>n,default:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\subscriptions\\\\SubscriptionCardSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx","default"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call SubscriptionListSkeleton() from the server but SubscriptionListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx","SubscriptionListSkeleton")},9735:(e,t,r)=>{r.d(t,{default:()=>d});var s=r(60687),a=r(43210),n=r(99270),o=r(11860),l=r(68988),i=r(24934),c=r(96241);function d({onSearch:e,initialSearchTerm:t="",className:r,placeholder:d="Search..."}){let[u,p]=(0,a.useState)(t);return(0,s.jsx)("form",{onSubmit:t=>{t.preventDefault(),"function"==typeof e&&e(u)},className:(0,c.cn)("relative w-full",r),children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-neutral-500 dark:text-neutral-400"}),(0,s.jsx)(l.p,{type:"text",placeholder:d,value:u,onChange:e=>{p(e.target.value)},onKeyDown:t=>{"Enter"===t.key&&(t.preventDefault(),"function"==typeof e&&e(u))},className:"pl-10 pr-10 h-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"}),u&&(0,s.jsxs)(i.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>{p(""),"function"==typeof e&&e("")},className:"absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Clear search"})]})]})})}},16051:(e,t,r)=>{r.d(t,{default:()=>i});var s=r(60687),a=r(24934),n=r(47033),o=r(14952),l=r(96241);function i({currentPage:e,totalPages:t,onPageChange:r,className:i}){if(t<=1)return null;let c=(()=>{let r=[];if(t<=5)for(let e=1;e<=t;e++)r.push(e);else{r.push(1);let s=Math.max(2,e-1),a=Math.min(t-1,e+1);e<=3&&(a=Math.min(t-1,4)),e>=t-2&&(s=Math.max(2,t-3)),s>2&&r.push(-1);for(let e=s;e<=a;e++)r.push(e);a<t-1&&r.push(-2),r.push(t)}return r})();return(0,s.jsxs)("div",{className:(0,l.cn)("flex items-center justify-center space-x-1",i),children:[(0,s.jsxs)(a.$,{variant:"outline",size:"icon",onClick:()=>r(e-1),disabled:1===e,className:"h-8 w-8 p-0 border-neutral-200 dark:border-neutral-800",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Previous page"})]}),c.map((t,n)=>t<0?(0,s.jsx)("span",{className:"px-2 text-neutral-500 dark:text-neutral-400",children:"..."},`ellipsis-${n}`):(0,s.jsx)(a.$,{variant:e===t?"default":"outline",size:"sm",onClick:()=>r(t),className:(0,l.cn)("h-8 w-8 p-0",e===t?"bg-blue-500 hover:bg-blue-600 text-white border-transparent":"border-neutral-200 dark:border-neutral-800"),children:t},t)),(0,s.jsxs)(a.$,{variant:"outline",size:"icon",onClick:()=>r(e+1),disabled:e===t,className:"h-8 w-8 p-0 border-neutral-200 dark:border-neutral-800",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Next page"})]})]})}},17384:(e,t,r)=>{r.d(t,{fo:()=>s.SubscriptionListSkeleton}),r(59299);var s=r(8018);r(96429),r(19285),r(85785)},19068:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},19285:(e,t,r)=>{r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\subscriptions\\\\SubscriptionPagination.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx","default")},37033:(e,t,r)=>{r.d(t,{default:()=>v});var s=r(60687),a=r(43210),n=r(85814),o=r.n(n),l=r(24934),i=r(70373),c=r(58869),d=r(25334),u=r(17313),p=r(41862),m=r(19068),b=r(90516),h=r(52581),x=r(77882),f=r(96241);function v({subscriptionId:e,profile:t,onUnsubscribeSuccess:r,showUnsubscribe:n=!0,variant:v="default"}){let[g,k]=(0,a.useState)(!1),[j,w]=(0,a.useState)(!1),N=async()=>{if(r){k(!0);try{if(!t.id)return void h.oR.error("Cannot unsubscribe: Missing profile ID");let s=await (0,b.W)(t.id);s.success?(h.oR.success(`Unsubscribed from ${t.name||"profile"}.`),r(e)):h.oR.error(`Failed to unsubscribe: ${s.error||"Unknown error"}`)}catch(e){h.oR.error("An unexpected error occurred")}finally{k(!1)}}},y=[t.locality,t.city,t.state].filter(Boolean).join(", "),C="business"===t.type?`/${t.slug}`:`/profile/${t.slug}`,S=t.logo_url||t.avatar_url;return(0,s.jsxs)(x.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:(0,f.cn)("rounded-lg border p-0 overflow-hidden transition-all duration-300","bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",j?"shadow-md transform -translate-y-1":"shadow-sm","compact"===v&&"max-w-sm"),onMouseEnter:()=>w(!0),onMouseLeave:()=>w(!1),children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:(0,f.cn)("w-full bg-gradient-to-r from-blue-500/20 to-[var(--brand-gold)]/20 dark:from-blue-900/30 dark:to-[var(--brand-gold)]/30","compact"===v?"h-16":"h-20"),children:(0,s.jsx)("div",{className:"absolute inset-0 opacity-10 dark:opacity-20 bg-repeat",style:{backgroundImage:'url("/decorative/card-texture.svg")',backgroundSize:"200px"}})}),(0,s.jsx)("div",{className:(0,f.cn)("absolute left-4","compact"===v?"-bottom-4":"-bottom-6"),children:(0,s.jsx)("div",{className:"p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800",children:(0,s.jsxs)(i.eu,{className:(0,f.cn)("border border-neutral-200 dark:border-neutral-700 shadow-sm","compact"===v?"h-12 w-12":"h-16 w-16"),children:[S?(0,s.jsx)(i.BK,{src:S,alt:t.name??"Profile"}):null,(0,s.jsx)(i.q5,{className:(0,f.cn)("bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 text-blue-600 dark:text-blue-300 font-semibold","compact"===v?"text-lg":"text-xl"),children:"customer"===t.type?(0,s.jsx)(c.A,{className:"compact"===v?"h-4 w-4":"h-6 w-6"}):t.name?.charAt(0).toUpperCase()??"P"})]})})})]}),(0,s.jsx)("div",{className:(0,f.cn)("px-4 pb-4","compact"===v?"pt-6":"pt-8"),children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsx)("h3",{className:(0,f.cn)("font-semibold text-neutral-800 dark:text-neutral-200 group flex items-center gap-1","compact"===v?"text-base":"text-lg"),children:t.slug?(0,s.jsxs)(o(),{href:C,className:"hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1",target:"_blank",children:[t.name,(0,s.jsx)(d.A,{className:"h-3.5 w-3.5 opacity-70"})]}):(0,s.jsx)("span",{children:t.name})}),y&&(0,s.jsxs)("p",{className:(0,f.cn)("text-neutral-500 dark:text-neutral-400 mt-1 flex items-center","compact"===v?"text-xs":"text-sm"),children:[(0,s.jsx)("span",{className:"inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2"}),y]}),"customer"===t.type&&(0,s.jsxs)("p",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1 flex items-center",children:[(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1"}),"Customer"]}),"business"===t.type&&(0,s.jsxs)("p",{className:"text-xs text-green-600 dark:text-green-400 mt-1 flex items-center",children:[(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Business"]})]}),n&&r&&(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:N,disabled:g,className:(0,f.cn)("mt-2 w-full border-neutral-200 dark:border-neutral-700 transition-all duration-200","hover:bg-red-50 hover:text-red-600 hover:border-red-200","dark:hover:bg-red-950/30 dark:hover:text-red-400 dark:hover:border-red-900/50"),children:[g?(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Unsubscribe"]})]})})]})}},48536:(e,t,r)=>{r.d(t,{SubscriptionListSkeleton:()=>i,default:()=>l});var s=r(60687),a=r(77882),n=r(71463),o=r(96241);function l({index:e=0,variant:t="default"}){return(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.05*e},className:(0,o.cn)("rounded-lg border p-0 overflow-hidden transition-all duration-300","bg-white dark:bg-black border-neutral-200 dark:border-neutral-800","shadow-sm","compact"===t&&"max-w-sm"),children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.E,{className:(0,o.cn)("w-full","compact"===t?"h-16":"h-20")}),(0,s.jsx)("div",{className:(0,o.cn)("absolute left-4","compact"===t?"-bottom-4":"-bottom-6"),children:(0,s.jsx)("div",{className:"p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800",children:(0,s.jsx)(n.E,{className:(0,o.cn)("rounded-full","compact"===t?"h-12 w-12":"h-16 w-16")})})})]}),(0,s.jsx)("div",{className:(0,o.cn)("px-4 pb-4","compact"===t?"pt-6":"pt-8"),children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsx)(n.E,{className:(0,o.cn)("mb-2","compact"===t?"h-5 w-32":"h-6 w-40")}),(0,s.jsx)(n.E,{className:(0,o.cn)("mt-1","compact"===t?"h-3 w-24":"h-4 w-32")})]}),(0,s.jsx)(n.E,{className:(0,o.cn)("w-full mt-2 rounded-md","compact"===t?"h-8":"h-9")})]})})]})}function i({variant:e="default",count:t=6}){return(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:t}).map((t,r)=>(0,s.jsx)(l,{index:r,variant:e},r))})}},59299:(e,t,r)=>{r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\subscriptions\\\\SubscriptionCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx","default")},85785:(e,t,r)=>{r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\subscriptions\\\\SubscriptionList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx","default")},90516:(e,t,r)=>{r.d(t,{W:()=>a});var s=r(6475);let a=(0,s.createServerReference)("406d3eedcd0777bc7a4d097a7c3736c79aa39001bd",s.callServer,void 0,s.findSourceMapURL,"unsubscribeFromBusiness")},96429:(e,t,r)=>{r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\components\\\\shared\\\\subscriptions\\\\SubscriptionSearch.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx","default")},96750:(e,t,r)=>{r.d(t,{A$:()=>o.default,Qd:()=>n.default,__:()=>a.default,fo:()=>s.SubscriptionListSkeleton}),r(37033);var s=r(48536),a=r(9735),n=r(16051),o=r(5851)}};