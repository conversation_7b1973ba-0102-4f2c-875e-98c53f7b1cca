(()=>{var e={};e.id=6923,e.ids=[6923],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3956:(e,r,a)=>{Promise.resolve().then(a.bind(a,64522))},5515:(e,r,a)=>{"use strict";function t(e){return{id:e.id,business_name:e.business_name??"",contact_email:e.contact_email??"",has_active_subscription:!!e.has_active_subscription??!1,trial_end_date:"string"==typeof e.trial_end_date?e.trial_end_date:null,created_at:"string"==typeof e.created_at?e.created_at:void 0,updated_at:"string"==typeof e.updated_at?e.updated_at:void 0,logo_url:"string"==typeof e.logo_url?e.logo_url:null,member_name:"string"==typeof e.member_name?e.member_name:"",title:"string"==typeof e.title?e.title:"",address_line:"string"==typeof e.address_line?e.address_line:"",city:"string"==typeof e.city?e.city:"",state:"string"==typeof e.state?e.state:"",pincode:"string"==typeof e.pincode?e.pincode:"",locality:"string"==typeof e.locality?e.locality:"",phone:"string"==typeof e.phone?e.phone:"",instagram_url:"string"==typeof e.instagram_url?e.instagram_url:"",facebook_url:"string"==typeof e.facebook_url?e.facebook_url:"",whatsapp_number:"string"==typeof e.whatsapp_number?e.whatsapp_number:"",about_bio:"string"==typeof e.about_bio?e.about_bio:"",status:"string"==typeof e.status&&("online"===e.status||"offline"===e.status)?e.status:"offline",business_slug:"string"==typeof e.business_slug?e.business_slug:"",total_likes:"number"==typeof e.total_likes?e.total_likes:0,total_subscriptions:"number"==typeof e.total_subscriptions?e.total_subscriptions:0,average_rating:"number"==typeof e.average_rating?e.average_rating:0,theme_color:"string"==typeof e.theme_color?e.theme_color:"",delivery_info:"string"==typeof e.delivery_info?e.delivery_info:"",business_hours:e.business_hours??null,business_category:"string"==typeof e.business_category?e.business_category:"",established_year:"number"==typeof e.established_year?e.established_year:null,custom_branding:{custom_header_text:"string"==typeof e.custom_branding?.custom_header_text?e.custom_branding.custom_header_text:"",custom_header_image_url:"string"==typeof e.custom_branding?.custom_header_image_url?e.custom_branding.custom_header_image_url:"",custom_header_image_light_url:"string"==typeof e.custom_branding?.custom_header_image_light_url?e.custom_branding.custom_header_image_light_url:"",custom_header_image_dark_url:"string"==typeof e.custom_branding?.custom_header_image_dark_url?e.custom_branding.custom_header_image_dark_url:"",hide_dukancard_branding:"boolean"==typeof e.custom_branding?.hide_dukancard_branding&&e.custom_branding.hide_dukancard_branding,pending_light_header_file:null,pending_dark_header_file:null},custom_ads:e.custom_ads&&"object"==typeof e.custom_ads?{enabled:"boolean"==typeof e.custom_ads.enabled&&e.custom_ads.enabled,image_url:"string"==typeof e.custom_ads.image_url?e.custom_ads.image_url:"",link_url:"string"==typeof e.custom_ads.link_url?e.custom_ads.link_url:"",uploaded_at:"string"==typeof e.custom_ads.uploaded_at?e.custom_ads.uploaded_at:""}:{enabled:!1,image_url:"",link_url:"",uploaded_at:""}}}function s(e){let r=e.products_services?.sort((e,r)=>new Date(r.created_at??0).getTime()-new Date(e.created_at??0).getTime()).slice(0,10)??[];return{id:e.id,business_name:e.business_name??"",contact_email:e.contact_email??"",has_active_subscription:!!e.has_active_subscription??!1,trial_end_date:"string"==typeof e.trial_end_date?e.trial_end_date:null,created_at:"string"==typeof e.created_at?e.created_at:void 0,updated_at:"string"==typeof e.updated_at?e.updated_at:void 0,logo_url:"string"==typeof e.logo_url?e.logo_url:null,member_name:"string"==typeof e.member_name?e.member_name:"",title:"string"==typeof e.title?e.title:"",address_line:"string"==typeof e.address_line?e.address_line:"",city:"string"==typeof e.city?e.city:"",state:"string"==typeof e.state?e.state:"",pincode:"string"==typeof e.pincode?e.pincode:"",locality:"string"==typeof e.locality?e.locality:"",phone:"string"==typeof e.phone?e.phone:"",business_category:"string"==typeof e.business_category?e.business_category:"",business_hours:e.business_hours??null,delivery_info:"string"==typeof e.delivery_info?e.delivery_info:"",established_year:"number"==typeof e.established_year?e.established_year:null,instagram_url:"string"==typeof e.instagram_url?e.instagram_url:"",facebook_url:"string"==typeof e.facebook_url?e.facebook_url:"",whatsapp_number:"string"==typeof e.whatsapp_number?e.whatsapp_number:"",about_bio:"string"==typeof e.about_bio?e.about_bio:"",status:"string"==typeof e.status&&("online"===e.status||"offline"===e.status)?e.status:"offline",business_slug:"string"==typeof e.business_slug?e.business_slug:"",products_services:r}}a.d(r,{S:()=>s,p:()=>t})},8555:(e,r,a)=>{"use strict";a.d(r,{t:()=>s});var t=a(6475);let s=(0,t.createServerReference)("40d32ea8edc6596cf0013772648e4fa734c9679198",t.callServer,void 0,t.findSourceMapURL,"getPincodeDetails")},8588:(e,r,a)=>{"use strict";a.d(r,{A:()=>m});var t=a(60687),s=a(43210),l=a(17971),n=a(13964),i=a(96241),o=a(24934),d=a(4331),c=a(33135),u=a(25499);function m({value:e,onChange:r,placeholder:a="Select a category...",className:m,disabled:x=!1}){let[p,h]=s.useState(!1);return(0,t.jsxs)(c.AM,{open:p,onOpenChange:h,children:[(0,t.jsx)(c.Wv,{asChild:!0,children:(0,t.jsxs)(o.$,{variant:"outline",role:"combobox","aria-expanded":p,className:(0,i.cn)("w-full justify-between h-12 text-sm",m),disabled:x,children:[e?u.qW.find(r=>r.name===e)?.name:a,(0,t.jsx)(l.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,t.jsx)(c.hl,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:(0,t.jsxs)(d.uB,{children:[(0,t.jsx)(d.G7,{placeholder:"Search category...",className:"h-9 border-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"}),(0,t.jsxs)(d.oI,{className:"max-h-[300px]",children:[(0,t.jsx)(d.xL,{children:"No category found."}),(0,t.jsx)(d.L$,{children:u.qW.map(a=>(0,t.jsxs)(d.h_,{value:a.name,onSelect:a=>{r(a===e?"":a),h(!1)},children:[(0,t.jsx)(a.icon,{className:"mr-2 h-4 w-4"}),a.name,(0,t.jsx)(n.A,{className:(0,i.cn)("ml-auto h-4 w-4",e===a.name?"opacity-100":"opacity-0")})]},a.slug))})]})]})})]})}},8819:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13943:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},15501:(e,r,a)=>{Promise.resolve().then(a.bind(a,78909))},15616:(e,r,a)=>{"use strict";a.d(r,{T:()=>l});var t=a(60687);a(43210);var s=a(96241);function l({className:e,...r}){return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...r})}},15969:(e,r,a)=>{"use strict";a.d(r,{w:()=>c});var t=a(67218);a(79130);var s=a(32032),l=a(62351),n=a(59968),i=a(34536),o=a(26369),d=a(43988);async function c(e){let r=await (0,s.createClient)(),a=(0,n.L)(e);if(!a.success)return console.error("Validation Error:",a.error.flatten().fieldErrors),{success:!1,error:"Invalid data provided. Please check the form fields."};let{data:{user:t},error:c}=await r.auth.getUser();if(c||!t)return console.error("Auth Error:",c),{success:!1,error:"User not authenticated."};let{data:u,error:m}=await r.from("business_profiles").select("phone").eq("id",t.id).single();if(m)return console.error("Profile fetch error:",m),{success:!1,error:"Failed to fetch existing profile."};if("online"===a.data.status){let e=await (0,i.A)(t.id);if(!e.canGoOnline)return{success:!1,error:e.error||"Cannot set card to online status."}}let x=a.data.business_slug;if("online"===a.data.status){let e=await (0,o.o)(a.data.business_name,x||"",t.id);if(!e.success)return{success:!1,error:e.error||"Failed to generate unique slug."};x=e.slug}else x=a.data.business_slug;let p={...a.data.custom_branding};if(a.data.custom_branding?.pending_light_header_file){let e=a.data.custom_branding.pending_light_header_file,r=await (0,d.nL)(e,"light");if(!r.success||!r.url)return console.error("Light theme header upload failed:",r.error),{success:!1,error:`Failed to upload light theme header: ${r.error}`};p.custom_header_image_light_url&&await (0,d.Jh)(p.custom_header_image_light_url),await (0,d.i$)(t.id,"light",r.url),p.custom_header_image_light_url=r.url}if(a.data.custom_branding?.pending_dark_header_file){let e=a.data.custom_branding.pending_dark_header_file,r=await (0,d.nL)(e,"dark");if(!r.success||!r.url)return console.error("Dark theme header upload failed:",r.error),{success:!1,error:`Failed to upload dark theme header: ${r.error}`};p.custom_header_image_dark_url&&await (0,d.Jh)(p.custom_header_image_dark_url),await (0,d.i$)(t.id,"dark",r.url),p.custom_header_image_dark_url=r.url}if(a.data.custom_branding?.custom_header_image_light_url===""&&!a.data.custom_branding?.pending_light_header_file){let{data:e}=await r.from("business_profiles").select("custom_branding").eq("id",t.id).single();e?.custom_branding&&"object"==typeof e.custom_branding&&"custom_header_image_light_url"in e.custom_branding&&e.custom_branding.custom_header_image_light_url&&await (0,d.Jh)(e.custom_branding.custom_header_image_light_url),p.custom_header_image_light_url=""}if(a.data.custom_branding?.custom_header_image_dark_url===""&&!a.data.custom_branding?.pending_dark_header_file){let{data:e}=await r.from("business_profiles").select("custom_branding").eq("id",t.id).single();e?.custom_branding&&"object"==typeof e.custom_branding&&"custom_header_image_dark_url"in e.custom_branding&&e.custom_branding.custom_header_image_dark_url&&await (0,d.Jh)(e.custom_branding.custom_header_image_dark_url),p.custom_header_image_dark_url=""}delete p.pending_light_header_file,delete p.pending_dark_header_file;let h=function(e){if(!e)return null;try{let r=null;if(r="string"==typeof e?JSON.parse(e):e,"object"!=typeof r||null===r)return console.warn("Invalid business_hours format, setting to null"),null;return r}catch(e){return console.error("Error processing business_hours data:",e),null}}(a.data.business_hours),g={business_name:a.data.business_name,member_name:a.data.member_name,title:a.data.title,logo_url:a.data.logo_url,established_year:a.data.established_year,address_line:a.data.address_line,city:a.data.city,state:a.data.state,pincode:a.data.pincode,phone:a.data.phone,delivery_info:a.data.delivery_info,instagram_url:a.data.instagram_url,facebook_url:a.data.facebook_url,whatsapp_number:a.data.whatsapp_number,about_bio:a.data.about_bio,locality:a.data.locality,theme_color:a.data.theme_color,business_hours:h,status:a.data.status,business_slug:x,contact_email:a.data.contact_email,business_category:a.data.business_category,custom_branding:p,custom_ads:a.data.custom_ads},{data:b,error:f}=await r.from("business_profiles").update(g).eq("id",t.id).select(`
      id, business_name, member_name, title, logo_url, address_line, city, state, pincode, locality,
      phone, instagram_url, facebook_url, whatsapp_number, about_bio, status, business_slug,
      theme_color, delivery_info, business_hours, contact_email, has_active_subscription,
      trial_end_date, created_at, updated_at, total_likes, total_subscriptions, average_rating,
      business_category, custom_branding, custom_ads, established_year
    `).single();if(f)return console.error("Supabase Update Error:",f),{success:!1,error:`Failed to update profile: ${f.message}`};if(!b)return{success:!1,error:"Failed to update profile. Profile not found after update."};if(a.data.phone&&a.data.phone!==u.phone){let{error:e}=await r.auth.updateUser({phone:`+91${a.data.phone}`});e&&console.warn("Failed to update auth phone field:",e.message)}return(0,l.revalidatePath)("/dashboard/business/card"),"online"===g.status&&g.business_slug&&(0,l.revalidatePath)(`/(main)/card/${g.business_slug}`,"page"),{success:!0,data:b}}(0,a(17478).D)([c]),(0,t.A)(c,"40aa31586df3ce75d0e0d55282c9fb7a8889f88f1d",null)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19526:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},21761:(e,r,a)=>{"use strict";a.d(r,{z:()=>M,C:()=>F});var t=a(60687),s=a(43210),l=a(70569),n=a(98599),i=a(11273),o=a(3416),d=a(72942),c=a(65551),u=a(43),m=a(18853),x=a(83721),p=a(46059),h="Radio",[g,b]=(0,i.A)(h),[f,y]=g(h),_=s.forwardRef((e,r)=>{let{__scopeRadio:a,name:i,checked:d=!1,required:c,disabled:u,value:m="on",onCheck:x,form:p,...h}=e,[g,b]=s.useState(null),y=(0,n.s)(r,e=>b(e)),_=s.useRef(!1),v=!g||p||!!g.closest("form");return(0,t.jsxs)(f,{scope:a,checked:d,disabled:u,children:[(0,t.jsx)(o.sG.button,{type:"button",role:"radio","aria-checked":d,"data-state":N(d),"data-disabled":u?"":void 0,disabled:u,value:m,...h,ref:y,onClick:(0,l.m)(e.onClick,e=>{d||x?.(),v&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})}),v&&(0,t.jsx)(w,{control:g,bubbles:!_.current,name:i,value:m,checked:d,required:c,disabled:u,form:p,style:{transform:"translateX(-100%)"}})]})});_.displayName=h;var v="RadioIndicator",j=s.forwardRef((e,r)=>{let{__scopeRadio:a,forceMount:s,...l}=e,n=y(v,a);return(0,t.jsx)(p.C,{present:s||n.checked,children:(0,t.jsx)(o.sG.span,{"data-state":N(n.checked),"data-disabled":n.disabled?"":void 0,...l,ref:r})})});j.displayName=v;var w=s.forwardRef(({__scopeRadio:e,control:r,checked:a,bubbles:l=!0,...i},d)=>{let c=s.useRef(null),u=(0,n.s)(c,d),p=(0,x.Z)(a),h=(0,m.X)(r);return s.useEffect(()=>{let e=c.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==a&&r){let t=new Event("click",{bubbles:l});r.call(e,a),e.dispatchEvent(t)}},[p,a,l]),(0,t.jsx)(o.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:a,...i,tabIndex:-1,ref:u,style:{...i.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}w.displayName="RadioBubbleInput";var k=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],S="RadioGroup",[C,A]=(0,i.A)(S,[d.RG,b]),R=(0,d.RG)(),P=b(),[U,E]=C(S),I=s.forwardRef((e,r)=>{let{__scopeRadioGroup:a,name:s,defaultValue:l,value:n,required:i=!1,disabled:m=!1,orientation:x,dir:p,loop:h=!0,onValueChange:g,...b}=e,f=R(a),y=(0,u.jH)(p),[_,v]=(0,c.i)({prop:n,defaultProp:l??"",onChange:g,caller:S});return(0,t.jsx)(U,{scope:a,name:s,required:i,disabled:m,value:_,onValueChange:v,children:(0,t.jsx)(d.bL,{asChild:!0,...f,orientation:x,dir:y,loop:h,children:(0,t.jsx)(o.sG.div,{role:"radiogroup","aria-required":i,"aria-orientation":x,"data-disabled":m?"":void 0,dir:y,...b,ref:r})})})});I.displayName=S;var L="RadioGroupItem",T=s.forwardRef((e,r)=>{let{__scopeRadioGroup:a,disabled:i,...o}=e,c=E(L,a),u=c.disabled||i,m=R(a),x=P(a),p=s.useRef(null),h=(0,n.s)(r,p),g=c.value===o.value,b=s.useRef(!1);return s.useEffect(()=>{let e=e=>{k.includes(e.key)&&(b.current=!0)},r=()=>b.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,t.jsx)(d.q7,{asChild:!0,...m,focusable:!u,active:g,children:(0,t.jsx)(_,{disabled:u,required:c.required,checked:g,...x,...o,name:c.name,ref:h,onCheck:()=>c.onValueChange(o.value),onKeyDown:(0,l.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,l.m)(o.onFocus,()=>{b.current&&p.current?.click()})})})});T.displayName=L;var D=s.forwardRef((e,r)=>{let{__scopeRadioGroup:a,...s}=e,l=P(a);return(0,t.jsx)(j,{...l,...s,ref:r})});D.displayName="RadioGroupIndicator";let O=(0,a(62688).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var B=a(96241);function M({className:e,...r}){return(0,t.jsx)(I,{"data-slot":"radio-group",className:(0,B.cn)("grid gap-3",e),...r})}function F({className:e,...r}){return(0,t.jsx)(T,{"data-slot":"radio-group-item",className:(0,B.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,t.jsx)(D,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,t.jsx)(O,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},25941:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>c,metadata:()=>d});var t=a(37413);a(15969);var s=a(44027);a(29469),a(26369),a(70651);var l=a(69721),n=a(78909),i=a(32032),o=a(30468);let d={title:"Edit Business Card",description:"Manage and customize your digital business card.",robots:"noindex, nofollow"};async function c(){let{data:e,error:r}=await (0,s.F)();r&&console.error("Error fetching business card data:",r);let a=e??l.kc,d=await (0,i.createClient)(),{data:c,error:u}=await d.from(o.CG.PAYMENT_SUBSCRIPTIONS).select(`${o.cZ.PLAN_ID}, ${o.cZ.SUBSCRIPTION_STATUS}`).eq(o.cZ.BUSINESS_PROFILE_ID,a.id||"").order(o.cZ.CREATED_AT,{ascending:!1}).limit(1).maybeSingle();u&&console.error("Error fetching subscription data:",u);let m=c?.plan_id||"free",x=c?.subscription_status||null;return(0,t.jsx)("div",{className:"w-full",children:(0,t.jsx)(n.default,{initialData:a,currentUserPlan:m,subscriptionStatus:x})})}},26369:(e,r,a)=>{"use strict";a.d(r,{r:()=>o,o:()=>i});var t=a(67218);a(79130);var s=a(42136),l=a(59968),n=a(95685);async function i(e,r,a){let t=r||e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")||"business",i=!1,o=t,d=0;for(;!i&&d<n.Sg;){let{available:e,error:r}=await (0,s.T)(o,a);if(r)return console.error("Slug Check Error:",r),{success:!1,error:"Error checking slug availability."};if(e){i=!0;let e=o;if(!(0,l.Z)(e).success)return{success:!1,error:"Invalid business slug format generated. Please set one manually."};return{success:!0,slug:e}}if(d++,o=`${t}-${(0,n.Ak)()}`,d===n.Sg)return{success:!1,error:`Could not generate a unique slug for '${t}'. Please try setting one manually.`}}return{success:!1,error:"Failed to generate unique slug."}}async function o(e){return(0,s.T)(e)}(0,a(17478).D)([i,o]),(0,t.A)(i,"7084e43886b10d84694cbd609121dd23c12f1f7e3d",null),(0,t.A)(o,"407090e0ad54740b8bc9265b7b903efb29a22d1ef2",null)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29469:(e,r,a)=>{"use strict";a.d(r,{Au:()=>c,UX:()=>d,z$:()=>u});var t=a(67218);a(79130);var s=a(32032),l=a(62351),n=a(98344),i=a(95685),o=a(30468);async function d(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};let{error:n}=await r.from("business_profiles").update({logo_url:e,updated_at:new Date().toISOString()}).eq("id",a.id);return n?(console.error("Logo URL Update Error:",n),{success:!1,error:`Failed to update logo URL: ${n.message}`}):((0,l.revalidatePath)("/dashboard/business/card"),{success:!0})}async function c(){let e=await (0,s.createClient)(),{data:{user:r},error:a}=await e.auth.getUser();if(a||!r)return{success:!1,error:"User not authenticated."};let{data:t,error:n}=await e.from(o.CG.BUSINESS_PROFILES).select(o.cZ.LOGO_URL).eq(o.cZ.ID,r.id).single();if(n)return console.error("Error fetching profile for logo deletion:",n),{success:!1,error:"Failed to fetch profile information."};if(t?.logo_url)try{let e=t.logo_url.split("/storage/v1/object/public/business/");if(2===e.length){let r=e[1].split("?")[0],a=(0,s.createClient)(),{error:t}=await (await a).storage.from(i.vn).remove([r]);t&&"The resource was not found"!==t.message?console.error("Error deleting logo from storage:",t):console.log("Successfully deleted logo from storage:",r)}else console.warn("Could not parse logo URL for storage deletion:",t.logo_url)}catch(e){console.error("Error processing logo URL for deletion:",e)}let{error:d}=await e.from(o.CG.BUSINESS_PROFILES).update({logo_url:null,updated_at:new Date().toISOString()}).eq(o.cZ.ID,r.id);return d?(console.error("Error updating profile after logo deletion:",d),{success:!1,error:`Failed to update profile after logo deletion: ${d.message}`}):((0,l.revalidatePath)("/dashboard/business/card"),{success:!0})}async function u(e){let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"User not authenticated."};let l=a.id,o=e.get("logoFile");if(!o)return{success:!1,error:"No logo file provided."};if(!i.Ft.includes(o.type))return{success:!1,error:"Invalid file type."};if(o.size>1024*i.dg*1024)return{success:!1,error:`File size must be less than ${i.dg}MB.`};let d=new Date().getTime()+Math.floor(1e3*Math.random()),c=(0,n.Wl)(l,d);try{let e=(0,s.createClient)(),r=(0,n.YV)(l),a=`${r}/profile/`,{data:t,error:o}=await (await e).storage.from(i.vn).list(a,{limit:10});if(!o&&t&&t.length>0){let r=t.filter(e=>e.name.startsWith("logo_")).map(e=>`${a}${e.name}`);if(r.length>0){let{error:a}=await (await e).storage.from(i.vn).remove(r);a&&console.warn("Error deleting existing logos:",a.message)}}}catch(e){console.warn("Exception during logo deletion check:",e)}try{let e=Buffer.from(await o.arrayBuffer()),r=(0,s.createClient)(),{error:a}=await (await r).storage.from(i.vn).upload(c,e,{contentType:o.type,upsert:!0});if(a)return console.error("Logo Upload Error:",a),{success:!1,error:`Failed to upload logo: ${a.message}`};let{data:t}=(await r).storage.from(i.vn).getPublicUrl(c);if(!t?.publicUrl)return console.error("Get Public URL Error: URL data is null or missing publicUrl property for path:",c),{success:!1,error:"Could not retrieve public URL after upload."};return{success:!0,url:t.publicUrl}}catch(e){return console.error("Image Processing/Upload Error:",e),{success:!1,error:"Failed to process or upload image."}}}(0,a(17478).D)([d,c,u]),(0,t.A)(d,"407aa7558d8d3c2a2485318248f6b61b88e81c9984",null),(0,t.A)(c,"00a881513113b70ae43ac568b9503ff7a79d1663cb",null),(0,t.A)(u,"402541af2e999f9ab59f3228ba7ab00103f86ac358",null)},30468:(e,r,a)=>{"use strict";a.d(r,{CG:()=>t,SC:()=>s,cZ:()=>l});let t={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},s={BUSINESS:"business",CUSTOMERS:"customers"},l={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},32873:(e,r,a)=>{"use strict";a.r(r),a.d(r,{"003ab6b0c4c403b4241ff6804e098af66eec8fa0d6":()=>o.F,"00a881513113b70ae43ac568b9503ff7a79d1663cb":()=>d.Au,"402541af2e999f9ab59f3228ba7ab00103f86ac358":()=>d.z$,"4037c65e88249729e7df8a5f3bd0cfbee59696db24":()=>c.m,"407090e0ad54740b8bc9265b7b903efb29a22d1ef2":()=>l.r,"407aa7558d8d3c2a2485318248f6b61b88e81c9984":()=>d.UX,"407d18926be29b2c062f5d4bd5ba2ac786ce6f0a99":()=>s.A,"409bad1807ecdb6957facb9a3183633afc29b44d58":()=>i.Jh,"40aa31586df3ce75d0e0d55282c9fb7a8889f88f1d":()=>t.w,"6005206753a7bcbc5e79daca6654087807e4747c2c":()=>i.nL,"6064535eef5a317beac2d2607926347c9c73265cb4":()=>s.P,"60d3238d9658733e678eb0e501056bcd3469a6f842":()=>n.T,"7084e43886b10d84694cbd609121dd23c12f1f7e3d":()=>l.o,"70887b6a8a80ff0da8f218e9e9a6da5428e881d4ce":()=>i.i$});var t=a(15969),s=a(34536),l=a(26369),n=a(42136),i=a(43988),o=a(44027),d=a(29469),c=a(70651)},33135:(e,r,a)=>{"use strict";a.d(r,{AM:()=>n,Wv:()=>i,hl:()=>o});var t=a(60687);a(43210);var s=a(52676),l=a(96241);function n({...e}){return(0,t.jsx)(s.bL,{"data-slot":"popover",...e})}function i({...e}){return(0,t.jsx)(s.l9,{"data-slot":"popover-trigger",...e})}function o({className:e,align:r="center",sideOffset:a=4,...n}){return(0,t.jsx)(s.ZL,{children:(0,t.jsx)(s.UC,{"data-slot":"popover-content",align:r,sideOffset:a,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...n})})}},33873:e=>{"use strict";e.exports=require("path")},34536:(e,r,a)=>{"use strict";a.d(r,{A:()=>l,P:()=>n});var t=a(67218);a(79130);var s=a(32032);async function l(e){let r=await (0,s.createClient)(),{data:a,error:t}=await r.from("payment_subscriptions").select("subscription_status").eq("business_profile_id",e).order("created_at",{ascending:!1}).limit(1).maybeSingle();return t?(console.error("Error fetching subscription data:",t),{canGoOnline:!0}):a?.subscription_status==="halted"?(console.log(`User ${e} attempted to set card online with halted subscription`),{canGoOnline:!1,error:"Cannot set card to online status while your subscription is paused. Please resume your subscription first."}):{canGoOnline:!0}}async function n(e,r){let{data:t,error:s}=await r.from("payment_subscriptions").select("subscription_status").eq("business_profile_id",e).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(s)return console.error("Error fetching subscription data:",s),{shouldForceOffline:!1};let{SUBSCRIPTION_STATUS:l}=await a.e(5193).then(a.bind(a,65193));return t?.subscription_status===l.HALTED?{shouldForceOffline:!0,reason:"subscription is paused"}:{shouldForceOffline:!1}}(0,a(17478).D)([l,n]),(0,t.A)(l,"407d18926be29b2c062f5d4bd5ba2ac786ce6f0a99",null),(0,t.A)(n,"6064535eef5a317beac2d2607926347c9c73265cb4",null)},34631:e=>{"use strict";e.exports=require("tls")},35157:(e,r,a)=>{"use strict";a.d(r,{CG:()=>t,SC:()=>s,cZ:()=>l});let t={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},s={BUSINESS:"business",CUSTOMERS:"customers"},l={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},40214:(e,r,a)=>{"use strict";a.d(r,{d:()=>w});var t=a(60687),s=a(43210),l=a(70569),n=a(98599),i=a(11273),o=a(65551),d=a(83721),c=a(18853),u=a(3416),m="Switch",[x,p]=(0,i.A)(m),[h,g]=x(m),b=s.forwardRef((e,r)=>{let{__scopeSwitch:a,name:i,checked:d,defaultChecked:c,required:x,disabled:p,value:g="on",onCheckedChange:b,form:f,...y}=e,[j,w]=s.useState(null),N=(0,n.s)(r,e=>w(e)),k=s.useRef(!1),S=!j||f||!!j.closest("form"),[C,A]=(0,o.i)({prop:d,defaultProp:c??!1,onChange:b,caller:m});return(0,t.jsxs)(h,{scope:a,checked:C,disabled:p,children:[(0,t.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":C,"aria-required":x,"data-state":v(C),"data-disabled":p?"":void 0,disabled:p,value:g,...y,ref:N,onClick:(0,l.m)(e.onClick,e=>{A(e=>!e),S&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),S&&(0,t.jsx)(_,{control:j,bubbles:!k.current,name:i,value:g,checked:C,required:x,disabled:p,form:f,style:{transform:"translateX(-100%)"}})]})});b.displayName=m;var f="SwitchThumb",y=s.forwardRef((e,r)=>{let{__scopeSwitch:a,...s}=e,l=g(f,a);return(0,t.jsx)(u.sG.span,{"data-state":v(l.checked),"data-disabled":l.disabled?"":void 0,...s,ref:r})});y.displayName=f;var _=s.forwardRef(({__scopeSwitch:e,control:r,checked:a,bubbles:l=!0,...i},o)=>{let u=s.useRef(null),m=(0,n.s)(u,o),x=(0,d.Z)(a),p=(0,c.X)(r);return s.useEffect(()=>{let e=u.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==a&&r){let t=new Event("click",{bubbles:l});r.call(e,a),e.dispatchEvent(t)}},[x,a,l]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...i,tabIndex:-1,ref:m,style:{...i.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(e){return e?"checked":"unchecked"}_.displayName="SwitchBubbleInput";var j=a(96241);function w({className:e,...r}){return(0,t.jsx)(b,{"data-slot":"switch",className:(0,j.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,t.jsx)(y,{"data-slot":"switch-thumb",className:(0,j.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},41862:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42136:(e,r,a)=>{"use strict";a.d(r,{T:()=>i});var t=a(67218);a(79130);var s=a(32032),l=a(62351),n=a(70762);async function i(e,r){if((0,l.unstable_noStore)(),!e||e.length<3)return{available:!1,error:"Slug must be at least 3 characters."};if(!n.Yj().regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/).safeParse(e).success)return{available:!1,error:"Invalid format (lowercase, numbers, hyphens only)."};let a=await (0,s.createClient)(),t=r;if(!t){let{data:{user:e}}=await a.auth.getUser();t=e?.id}try{let{data:r,error:s}=await a.from("business_profiles").select("id, business_slug").ilike("business_slug",e).neq("id",t??"").maybeSingle();if(s)return{available:!1,error:"Database error checking slug."};return{available:!r}}catch(e){return{available:!1,error:"An unexpected error occurred."}}}(0,a(17478).D)([i]),(0,t.A)(i,"60d3238d9658733e678eb0e501056bcd3469a6f842",null)},43988:(e,r,a)=>{"use strict";a.d(r,{Jh:()=>o,i$:()=>d,nL:()=>i});var t=a(67218);a(79130);var s=a(32032),l=a(98344),n=a(30468);async function i(e,r){try{let a=await (0,s.createClient)(),{data:{user:t},error:i}=await a.auth.getUser();if(i||!t)return{success:!1,error:"Authentication required"};if(!e.type.startsWith("image/"))return{success:!1,error:"Invalid file type. Please upload an image file."};if(e.size>5242880){let r=(e.size/1048576).toFixed(1);return{success:!1,error:`Image size (${r}MB) is too large. Please choose an image smaller than 5MB.`}}let o=Date.now()+Math.floor(1e3*Math.random()),d=(0,l.rN)(t.id,o,r),c=Buffer.from(await e.arrayBuffer()),{error:u}=await a.storage.from(n.SC.BUSINESS).upload(d,c,{contentType:e.type,upsert:!0});if(u)return console.error("Theme Header Upload Error:",u),{success:!1,error:`Failed to upload image: ${u.message}`};let{data:m}=a.storage.from(n.SC.BUSINESS).getPublicUrl(d);if(!m?.publicUrl)return{success:!1,error:"Could not retrieve public URL after upload."};return{success:!0,url:m.publicUrl}}catch(e){return console.error("Theme header upload error:",e),{success:!1,error:"An unexpected error occurred during upload."}}}async function o(e){try{let r=await (0,s.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"Authentication required"};if(!e||""===e.trim())return{success:!0};try{let a=new URL(e).pathname.split("/"),t=a.findIndex(e=>"business"===e);if(-1!==t&&t<a.length-1){let e=a.slice(t+1).join("/").split("?")[0],{error:s}=await r.storage.from(n.SC.BUSINESS).remove([e]);if(s&&"The resource was not found"!==s.message)return console.error("Error deleting theme header from storage:",s),{success:!1,error:`Failed to delete image: ${s.message}`}}}catch(e){return console.error("Error processing image URL for deletion:",e),{success:!1,error:"Invalid image URL format."}}return{success:!0}}catch(e){return console.error("Theme header deletion error:",e),{success:!1,error:"An unexpected error occurred during deletion."}}}async function d(e,r,a){try{let t,l=await (0,s.createClient)(),i=e.slice(0,2)+"/"+e.slice(2,4)+"/"+e,o=`${i}/branding/`,{data:d,error:c}=await l.storage.from(n.SC.BUSINESS).list(o,{limit:20});if(c||!d)return void console.error("Error listing branding files:",c);let u=d.filter(e=>e.name.startsWith(`header_${r}_`)&&e.name.endsWith(".webp"));if(a)try{let e=new URL(a).pathname.split("/");t=e[e.length-1].split("?")[0]}catch(e){console.error("Error extracting filename from keep URL:",e)}let m=u.filter(e=>!t||e.name!==t).map(e=>`${o}${e.name}`);if(m.length>0){let{error:e}=await l.storage.from(n.SC.BUSINESS).remove(m);e&&console.error("Error cleaning up old theme header files:",e)}}catch(e){console.error("Error in cleanup function:",e)}}(0,a(17478).D)([i,o,d]),(0,t.A)(i,"6005206753a7bcbc5e79daca6654087807e4747c2c",null),(0,t.A)(o,"409bad1807ecdb6957facb9a3183633afc29b44d58",null),(0,t.A)(d,"70887b6a8a80ff0da8f218e9e9a6da5428e881d4ce",null)},44027:(e,r,a)=>{"use strict";a.d(r,{F:()=>o});var t=a(67218);a(79130);var s=a(32032),l=a(69721),n=a(5515),i=a(34536);async function o(){let e=await (0,s.createClient)(),{data:{user:r},error:a}=await e.auth.getUser();if(a||!r)return{error:"User not authenticated."};let{data:t,error:o}=await e.from("business_profiles").select(`
      id, business_name, contact_email, has_active_subscription,
      trial_end_date, created_at, updated_at, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug,
      total_likes, total_subscriptions, average_rating, theme_color, delivery_info, business_hours,
      business_category, custom_branding, custom_ads, established_year
    `).eq("id",r.id).single();if(o)return"PGRST116"===o.code?{data:void 0}:(console.error("Supabase Fetch Error:",o),{error:`Failed to fetch profile: ${o.message}`});if("online"===t.status){let a=!1,s="",n=await (0,i.P)(r.id,e);n.shouldForceOffline&&(a=!0,s=n.reason||"subscription issue");let o=l.b4.filter(e=>{let r=t[e];return!r||""===String(r).trim()});if(o.length>0&&(a=!0,s=`missing required fields: ${o.join(", ")}`),a){console.log(`User ${r.id} card forced offline due to ${s}.`);let{error:a}=await e.from("business_profiles").update({status:"offline"}).eq("id",r.id);a?console.error("Error forcing card offline:",a.message):t.status="offline"}}return{data:(0,n.p)(t)}}(0,a(17478).D)([o]),(0,t.A)(o,"003ab6b0c4c403b4241ff6804e098af66eec8fa0d6",null)},45488:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>o});var t=a(37413);a(61120);var s=a(32032),l=a(64522);let n=["member_name","title","business_name","business_category","contact_email","phone","address_line","pincode","city","state","locality"];var i=a(39916);async function o({children:e}){let r=await (0,s.createClient)(),a=null,o=null,d=null,c=null,{data:{user:u}}=await r.auth.getUser();if(u){let{data:e,error:t}=await r.from("business_profiles").select(`
        business_name,
        logo_url,
        member_name,
        title,
        business_category,
        contact_email,
        phone,
        address_line,
        pincode,
        city,
        state,
        locality
      `).eq("id",u.id).single(),{data:s}=await r.from("payment_subscriptions").select("plan_id").eq("business_profile_id",u.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(t)console.error("Error fetching business profile in layout:",t.message);else if(e){a=e.business_name,o=e.logo_url,d=e.member_name,c=s?.plan_id||"free";let r=function(e){if(!e)return{isComplete:!1,missingFields:[...n],missingFieldLabels:["Your name","Your title","Business name","Business category","Contact email","Primary phone","Address line","Pincode","City","State","Locality/area"]};let r=[],a=[],t={member_name:"Your name",title:"Your title",business_name:"Business name",business_category:"Business category",contact_email:"Contact email",phone:"Primary phone",address_line:"Address line",pincode:"Pincode",city:"City",state:"State",locality:"Locality/area"};return n.forEach(s=>{let l=e[s];l&&""!==String(l).trim()||(r.push(s),a.push(t[s]))}),{isComplete:0===r.length,missingFields:r,missingFieldLabels:a}}(e);if(!r.isComplete){let e=function(e){if(0===e.length)return"";if(1===e.length)return`Please complete your ${e[0].toLowerCase()} to access the dashboard.`;if(2===e.length)return`Please complete your ${e[0].toLowerCase()} and ${e[1].toLowerCase()} to access the dashboard.`;let r=e[e.length-1],a=e.slice(0,-1);return`Please complete your ${a.map(e=>e.toLowerCase()).join(", ")}, and ${r.toLowerCase()} to access the dashboard.`}(r.missingFieldLabels);(0,i.redirect)(`/onboarding?message=${encodeURIComponent(e)}`)}}}else console.warn("No user found in business dashboard layout.");return(0,t.jsx)(l.default,{businessName:a,logoUrl:o,memberName:d,userPlan:c,children:e})}},47342:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},55192:(e,r,a)=>{"use strict";a.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>n,wL:()=>c});var t=a(60687);a(43210);var s=a(96241);function l({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function i({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...r})}function o({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58887:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},59968:(e,r,a)=>{"use strict";a.d(r,{L:()=>l,Z:()=>n});var t=a(70762),s=a(69721);function l(e){let r=s.Mo.refine(e=>s.Uc.every(r=>e[r]&&""!==String(e[r]).trim()),{message:`Required fields missing: ${s.Uc.join(", ")}.`,path:["member_name"]});return("online"===e.status?r.refine(e=>s.b4.every(r=>e[r]&&""!==String(e[r]).trim()),{message:`Cannot set status to online. Required fields missing: ${s.b4.join(", ")}.`,path:["status"]}):r).safeParse(e)}function n(e){return t.Yj().regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/).min(3).safeParse(e)}},61192:(e,r,a)=>{"use strict";a.d(r,{default:()=>u});var t=a(60687);a(43210);var s=a(27625),l=a(41956),n=a(38606),i=a(24861),o=a(21121),d=a(96241),c=a(52529);function u({children:e,businessName:r,logoUrl:a,memberName:u,userPlan:m}){return(0,t.jsx)(c.Q,{children:(0,t.jsxs)(i.GB,{children:[(0,t.jsx)(o.s,{businessName:r,logoUrl:a,memberName:u,userPlan:m}),(0,t.jsxs)(i.sF,{children:[(0,t.jsxs)(s.default,{businessName:r,logoUrl:a,userName:u,children:[(0,t.jsx)(i.x2,{className:"ml-auto md:ml-0"})," ",(0,t.jsx)(l.ThemeToggle,{variant:"dashboard"})]}),(0,t.jsx)("main",{className:(0,d.cn)("flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto","pb-20 sm:pb-18 md:pb-6","bg-white dark:bg-black"),children:e}),(0,t.jsx)(n.default,{})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63123:(e,r,a)=>{"use strict";a.r(r),a.d(r,{"00a3593a777ba7988175db3502399fe90e4a6ac663":()=>t.B,"00c08d23d995424fbe98469845e395857a3ae1a19c":()=>m,"40142db2f2e9e23e42f1d64a8d98f054ab16849fcf":()=>s.eb,"40611deb994966b3586d7553548dd1d4f6162e7cdf":()=>c,"406809393363051c82bcecb759b1153ca34eced5e4":()=>s.JM,"40aaafea41f50b748ef546f36a08d4542ed53be507":()=>d,"40d32ea8edc6596cf0013772648e4fa734c9679198":()=>s.tz,"40e1c846bd05e867a4fbd45ac07e582abf0e9b482d":()=>u});var t=a(64275),s=a(56528),l=a(91199);a(42087);var n=a(76881),i=a(50937),o=a(35157);async function d(e){try{let r=await (0,n.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"Authentication required"};let s=e.get("image");if(!s)return{success:!1,error:"No image file provided"};if(!s.type.startsWith("image/"))return{success:!1,error:"Invalid file type. Please upload an image."};if(s.size>0xf00000)return{success:!1,error:"File too large. Maximum size is 15MB."};let l=Date.now()+Math.floor(1e3*Math.random()),d=(0,i.JU)(a.id,l),c=Buffer.from(await s.arrayBuffer()),{error:u}=await r.storage.from(o.SC.BUSINESS).upload(d,c,{contentType:s.type,upsert:!0});if(u)return console.error("Custom Ad Upload Error:",u),{success:!1,error:`Failed to upload image: ${u.message}`};let{data:m}=r.storage.from(o.SC.BUSINESS).getPublicUrl(d);if(!m?.publicUrl)return{success:!1,error:"Could not retrieve public URL after upload."};let{error:x}=await r.from(o.CG.BUSINESS_PROFILES).update({custom_ads:{enabled:!0,image_url:m.publicUrl,link_url:"",uploaded_at:new Date().toISOString()}}).eq("id",a.id);return x&&console.error("Database update error:",x),{success:!0,url:m.publicUrl}}catch(e){return console.error("Custom ad upload error:",e),{success:!1,error:"An unexpected error occurred during upload."}}}async function c(e){try{let r=await (0,n.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"Authentication required"};if(e&&e.trim())try{new URL(e)}catch{return{success:!1,error:"Invalid URL format"}}let{data:s,error:l}=await r.from(o.CG.BUSINESS_PROFILES).select("custom_ads").eq("id",a.id).single();if(l)return{success:!1,error:"Failed to fetch current ad data"};let i={...s?.custom_ads&&"object"==typeof s.custom_ads?s.custom_ads:{},link_url:e.trim()},{error:d}=await r.from(o.CG.BUSINESS_PROFILES).update({custom_ads:i}).eq("id",a.id);if(d)return{success:!1,error:"Failed to update ad link"};return{success:!0}}catch(e){return console.error("Custom ad link update error:",e),{success:!1,error:"An unexpected error occurred"}}}async function u(e){try{let r=await (0,n.createClient)(),{data:{user:a},error:t}=await r.auth.getUser();if(t||!a)return{success:!1,error:"Authentication required"};let{data:s,error:l}=await r.from(o.CG.BUSINESS_PROFILES).select("custom_ads").eq("id",a.id).single();if(l)return{success:!1,error:"Failed to fetch current ad data"};let i={...s?.custom_ads&&"object"==typeof s.custom_ads?s.custom_ads:{},enabled:e},{error:d}=await r.from(o.CG.BUSINESS_PROFILES).update({custom_ads:i}).eq("id",a.id);if(d)return{success:!1,error:"Failed to toggle ad state"};return{success:!0}}catch(e){return console.error("Custom ad toggle error:",e),{success:!1,error:"An unexpected error occurred"}}}async function m(){try{let e=await (0,n.createClient)(),{data:{user:r},error:a}=await e.auth.getUser();if(a||!r)return{success:!1,error:"Authentication required"};let{data:t,error:s}=await e.from(o.CG.BUSINESS_PROFILES).select("custom_ads").eq("id",r.id).single();if(s)return{success:!1,error:"Failed to fetch current ad data"};t?.custom_ads;let l=t?.custom_ads&&"object"==typeof t.custom_ads&&"image_url"in t.custom_ads?t.custom_ads.image_url:null;if(l&&"string"==typeof l)try{let e=l.split("/storage/v1/object/public/business/");if(2===e.length){let r=e[1],a=await (0,n.createClient)(),{error:t}=await a.storage.from(o.SC.BUSINESS).remove([r]);t&&console.error("Storage deletion error:",t)}}catch(e){console.error("Error deleting custom ad from storage:",e)}let{error:i}=await e.from(o.CG.BUSINESS_PROFILES).update({custom_ads:{enabled:!1,image_url:"",link_url:"",uploaded_at:null}}).eq("id",r.id);if(i)return{success:!1,error:"Failed to delete custom ad"};return{success:!0}}catch(e){return console.error("Custom ad delete error:",e),{success:!1,error:"An unexpected error occurred"}}}(0,a(33331).D)([d,c,u,m]),(0,l.A)(d,"40aaafea41f50b748ef546f36a08d4542ed53be507",null),(0,l.A)(c,"40611deb994966b3586d7553548dd1d4f6162e7cdf",null),(0,l.A)(u,"40e1c846bd05e867a4fbd45ac07e582abf0e9b482d",null),(0,l.A)(m,"00c08d23d995424fbe98469845e395857a3ae1a19c",null)},64522:(e,r,a)=>{"use strict";a.d(r,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\components\\\\BusinessDashboardClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx","default")},65471:(e,r,a)=>{"use strict";a.d(r,{A:()=>m});var t=a(60687),s=a(43210),l=a(47473),n=a(24934),i=a(37826),o=a(41862),d=a(58709);let c=e=>new Promise((r,a)=>{let t=new Image;t.addEventListener("load",()=>r(t)),t.addEventListener("error",e=>a(e)),t.setAttribute("crossOrigin","anonymous"),t.src=e});async function u(e,r){let a=await c(e),t=document.createElement("canvas"),s=t.getContext("2d");if(!s)return null;let l=a.naturalWidth/a.width,n=a.naturalHeight/a.height,i=window.devicePixelRatio||1;return t.width=r.width*i*l,t.height=r.height*i*n,s.setTransform(i,0,0,i,0,0),s.imageSmoothingQuality="high",s.drawImage(a,r.x*l,r.y*n,r.width*l,r.height*n,0,0,r.width*l,r.height*n),new Promise(e=>{t.toBlob(e,"image/png")})}function m({imgSrc:e,onCropComplete:r,onClose:a,isOpen:c}){let[m,x]=(0,s.useState)({x:0,y:0}),[p,h]=(0,s.useState)(1),[g,b]=(0,s.useState)(null),[f,y]=(0,s.useState)(!1),_=(0,s.useCallback)((e,r)=>{b(r)},[]),v=async()=>{if(!e||!g){console.warn("Image source or crop area not available."),r(null);return}y(!0);try{let a=await u(e,g);r(a)}catch(e){console.error("Error cropping image:",e),r(null)}finally{y(!1)}};return(0,t.jsx)(i.lG,{open:c,onOpenChange:e=>!e&&a(),children:(0,t.jsxs)(i.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsx)(i.c7,{children:(0,t.jsx)(i.L3,{children:"Crop Your Logo"})}),(0,t.jsx)("div",{className:"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800",children:e?(0,t.jsx)(l.Ay,{image:e,crop:m,zoom:p,aspect:1,cropShape:"round",showGrid:!1,onCropChange:x,onZoomChange:h,onCropComplete:_}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsx)("p",{children:"Loading image..."})})}),(0,t.jsx)("div",{className:"px-4 pb-4",children:(0,t.jsx)(d.A,{min:1,max:3,step:.1,value:[p],onValueChange:e=>h(e[0]),className:"w-full","aria-label":"Zoom slider"})}),(0,t.jsxs)(i.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:a,disabled:f,children:"Cancel"}),(0,t.jsxs)(n.$,{onClick:v,disabled:f,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]",children:[f?(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Crop Image"]})]})]})})}},66232:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},69721:(e,r,a)=>{"use strict";a.d(r,{Mo:()=>l,kc:()=>n,b4:()=>i,Uc:()=>o});var t=a(70762);let s=t.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});t.z.object({email:t.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),t.z.object({email:t.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:t.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),t.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),t.z.object({mobile:s,password:t.z.string().trim().min(1,{message:"Password is required"})});let l=t.Ik({logo_url:t.Yj().url({message:"Invalid URL format for logo/profile photo."}).optional().or(t.eu("")).nullable(),established_year:t.ai().int({message:"Established year must be a whole number."}).min(1800,{message:"Established year must be after 1800."}).max(new Date().getFullYear(),{message:"Established year cannot be in the future."}).optional().nullable(),address_line:t.Yj().min(1,{message:"Address line is required."}).max(100,{message:"Address line cannot exceed 100 characters."}),locality:t.Yj().min(1,{message:"Locality/area is required."}),city:t.Yj().min(1,{message:"City is required."}),state:t.Yj().min(1,{message:"State is required."}),pincode:t.Yj().min(6,{message:"Pincode must be 6 digits."}).max(6,{message:"Pincode must be 6 digits."}).regex(/^\d+$/,{message:"Pincode must contain only digits."}),phone:s,instagram_url:t.Yj().url({message:"Invalid URL format for Instagram."}).optional().or(t.eu("")),facebook_url:t.Yj().url({message:"Invalid URL format for Facebook."}).optional().or(t.eu("")),whatsapp_number:s.optional().or(t.eu("")),about_bio:t.Yj().max(100,{message:"Bio cannot exceed 100 characters."}).optional().or(t.eu("")),theme_color:t.Yj().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,{message:"Invalid hex color format (e.g., #RRGGBB or #RGB)."}).optional().or(t.eu("")),business_hours:t.bz().optional().nullable(),delivery_info:t.Yj().max(100,{message:"Delivery info cannot exceed 100 characters."}).optional().or(t.eu("")),business_category:t.Yj().min(1,{message:"Business category is required."}),status:t.k5(["online","offline"]).default("offline"),custom_branding:t.Ik({custom_header_text:t.Yj().max(50).optional().or(t.eu("")),custom_header_image_url:t.Yj().url().optional().or(t.eu("")),custom_header_image_light_url:t.Yj().url().optional().or(t.eu("")),custom_header_image_dark_url:t.Yj().url().optional().or(t.eu("")),hide_dukancard_branding:t.zM().optional(),pending_light_header_file:t.bz().optional(),pending_dark_header_file:t.bz().optional()}).optional().refine(e=>{if(e?.hide_dukancard_branding===!0){let r=e?.custom_header_text&&""!==e.custom_header_text.trim(),a=e?.custom_header_image_url&&""!==e.custom_header_image_url.trim(),t=e?.custom_header_image_light_url&&""!==e.custom_header_image_light_url.trim(),s=e?.custom_header_image_dark_url&&""!==e.custom_header_image_dark_url.trim();if(!r&&!a&&!t&&!s)return!1}return!0},{message:"Custom header text or image is required when hiding Dukancard branding",path:["custom_header_text"]}),custom_ads:t.Ik({enabled:t.zM().optional(),image_url:t.Yj().url().optional().or(t.eu("")),link_url:t.Yj().url().optional().or(t.eu("")),uploaded_at:t.Yj().optional().or(t.eu("")).nullable()}).optional(),business_slug:t.Yj().regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/,{message:"Slug must be lowercase letters, numbers, or hyphens, and cannot start/end with a hyphen."}).min(3,{message:"Slug must be at least 3 characters long."}).optional().or(t.eu("")),member_name:t.Yj().min(1,{message:"Member name is required."}).max(50,{message:"Name cannot exceed 50 characters."}),title:t.Yj().min(1,{message:"Title/Designation is required."}).max(50,{message:"Title cannot exceed 50 characters."}),business_name:t.Yj().min(1,{message:"Business name is required."}).max(100,{message:"Business name cannot exceed 100 characters."}),id:t.Yj().uuid().optional(),contact_email:t.Yj().email({message:"Please enter a valid email address"}).min(1,{message:"Contact email is required"}),has_active_subscription:t.zM().optional(),trial_end_date:t.Yj().optional().nullable(),created_at:t.KC([t.Yj(),t.p6()]).optional().transform(e=>e instanceof Date?e.toISOString():e),updated_at:t.KC([t.Yj(),t.p6()]).optional().transform(e=>e instanceof Date?e.toISOString():e),total_likes:t.ai().int().nonnegative().optional(),total_subscriptions:t.ai().int().nonnegative().optional(),average_rating:t.ai().nonnegative().optional(),total_visits:t.ai().int().nonnegative().optional()}),n={member_name:"",title:"",business_name:"",logo_url:null,established_year:null,address_line:"",locality:"",city:"",state:"",pincode:"",phone:"",instagram_url:"",facebook_url:"",whatsapp_number:"",about_bio:"",theme_color:"",business_hours:null,delivery_info:"",business_category:"",status:"offline",business_slug:"",contact_email:"",custom_branding:{custom_header_text:"",custom_header_image_url:"",custom_header_image_light_url:"",custom_header_image_dark_url:"",hide_dukancard_branding:!1,pending_light_header_file:null,pending_dark_header_file:null},custom_ads:{enabled:!1,image_url:"",link_url:"",uploaded_at:null}},i=["member_name","title","business_name","phone","address_line","pincode","city","state","locality","contact_email","business_category"],o=["member_name","title","business_name","phone","contact_email","business_category","address_line","pincode","city","state","locality"]},70651:(e,r,a)=>{"use strict";a.d(r,{m:()=>i});var t=a(67218);a(79130);var s=a(32032),l=a(5515),n=a(30468);async function i(e){if(!e)return{error:"Business slug is required."};let r=await (0,s.createClient)(),{data:a,error:t}=await r.from(n.CG.BUSINESS_PROFILES).select(`
      id, business_name, contact_email, has_active_subscription,
      trial_end_date, created_at, updated_at, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug,
      business_category, business_hours, delivery_info, established_year,
      products_services (
        id, name, description, base_price, is_available, image_url, created_at, updated_at
      )
    `).eq(n.cZ.BUSINESS_SLUG,e).eq(n.cZ.STATUS,"online").maybeSingle();return t?(console.error("Public Fetch Error:",t),{error:`Failed to fetch public profile: ${t.message}`}):a?{data:(0,l.S)(a)}:{error:"Profile not found or is not online."}}(0,a(17478).D)([i]),(0,t.A)(i,"4037c65e88249729e7df8a5f3bd0cfbee59696db24",null)},74075:e=>{"use strict";e.exports=require("zlib")},77676:(e,r,a)=>{"use strict";a.d(r,{default:()=>eX});var t=a(60687),s=a(43210),l=a(27605),n=a(63442),i=a(52581),o=a(77882),d=a(47342),c=a(24934);function u(e){let r=e.getBoundingClientRect(),a=window.scrollY+r.top-150,t=window.scrollY,s=a-t;e.classList.add("error-highlight"),setTimeout(()=>{e.classList.remove("error-highlight")},3e3);let l=e=>1-Math.pow(1-e,3),n=performance.now();requestAnimationFrame(function e(r){let a=Math.min((r-n)/800,1),i=l(a);window.scrollTo({top:t+s*i,behavior:"auto"}),a<1&&requestAnimationFrame(e)})}var m=a(16742),x=a(6475);let p=(0,x.createServerReference)("40aa31586df3ce75d0e0d55282c9fb7a8889f88f1d",x.callServer,void 0,x.findSourceMapURL,"updateBusinessCard"),h=(0,x.createServerReference)("402541af2e999f9ab59f3228ba7ab00103f86ac358",x.callServer,void 0,x.findSourceMapURL,"uploadLogoAndGetUrl"),g=(0,x.createServerReference)("407aa7558d8d3c2a2485318248f6b61b88e81c9984",x.callServer,void 0,x.findSourceMapURL,"updateLogoUrl"),b=(0,x.createServerReference)("00a881513113b70ae43ac568b9503ff7a79d1663cb",x.callServer,void 0,x.findSourceMapURL,"deleteLogoUrl");var f=a(28640),y=a(8555),_=a(58869),v=a(57800),j=a(17313),w=a(37360),N=a(40228),k=a(41550),S=a(16023),C=a(88233),A=a(41862),R=a(96882),P=a(58887),U=a(68988),E=a(15616),I=a(58164),L=a(8588),T=a(30474);function D({form:e,onFileSelect:r,isLogoUploading:a=!1,onLogoDelete:s}){let l=e.watch("about_bio")?.length||0;return(0,t.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,t.jsx)(_.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Basic Information"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Let's start with the essential details for your business card"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,t.jsx)(I.zB,{control:e.control,name:"member_name",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(_.A,{className:"h-3.5 w-3.5 text-primary"}),"Your Name",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(U.p,{placeholder:"e.g., Rajesh Mahapatra",...e,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:50}),(0,t.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[e.value?.length||0,"/50"]})]})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your full name as it will appear on the card"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"title",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(v.A,{className:"h-3.5 w-3.5 text-primary"}),"Your Title/Designation",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(U.p,{placeholder:"e.g., Owner, Manager, Developer",...e,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:50}),(0,t.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[e.value?.length||0,"/50"]})]})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your position or role within the business"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})]}),(0,t.jsx)(I.zB,{control:e.control,name:"business_name",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(j.A,{className:"h-3.5 w-3.5 text-primary"}),"Business Name",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(U.p,{placeholder:"e.g., Mahapatra Kirana & General Store",...e,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:100}),(0,t.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[e.value?.length||0,"/100"]})]})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"The name of your business or organization"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"business_category",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(w.A,{className:"h-3.5 w-3.5 text-primary"}),"Business Category",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)(L.A,{value:e.value||"",onChange:e.onChange,placeholder:"Select a business category",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"The category that best describes your business"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"established_year",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(N.A,{className:"h-3.5 w-3.5 text-primary"}),"Established Year"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(U.p,{placeholder:"e.g., 2015",...e,type:"number",min:"1800",max:new Date().getFullYear(),value:e.value||"",onChange:r=>{let a=r.target.value;e.onChange(""===a?null:parseInt(a,10))},className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"The year your business was established (will be displayed on your card)"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"contact_email",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(k.A,{className:"h-3.5 w-3.5 text-primary"}),"Contact Email",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(U.p,{placeholder:"e.g., <EMAIL>",...e,type:"email",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"}),(0,t.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[e.value?.length||0,"/100"]})]})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Email address for customers to contact you (required)"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"logo_url",render:()=>{let l=e.watch("logo_url"),n=l&&""!==l.trim()?l:null,i=n?n.split("/").pop()?.split("?")[0]:null;return(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(S.A,{className:"h-3.5 w-3.5 text-primary"}),"Logo / Profile Photo"]}),i&&(0,t.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-gradient-to-r from-neutral-50 to-neutral-100 dark:from-neutral-800/50 dark:to-neutral-800 border border-neutral-200 dark:border-neutral-700",children:[n&&(0,t.jsx)("div",{className:"h-10 w-10 sm:h-12 sm:w-12 rounded-md border border-neutral-200 dark:border-neutral-700 overflow-hidden bg-white dark:bg-black flex items-center justify-center shadow-sm",children:(0,t.jsx)(T.default,{src:n,alt:"Current logo",className:"h-full w-full object-contain",width:48,height:48,priority:!0})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-xs font-medium text-neutral-800 dark:text-neutral-200 truncate",children:i}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400",children:"Current logo"})]}),s&&!a&&(0,t.jsx)("button",{type:"button",onClick:s,className:"p-1.5 rounded-full bg-red-50 dark:bg-red-900/20 text-red-500 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors cursor-pointer",title:"Delete logo","aria-label":"Delete logo",children:(0,t.jsx)(C.A,{className:"h-3.5 w-3.5"})}),a&&(0,t.jsx)("div",{className:"p-1.5 rounded-full bg-neutral-100 dark:bg-neutral-800",children:(0,t.jsx)(A.A,{className:"h-3.5 w-3.5 animate-spin text-neutral-500 dark:text-neutral-400"})})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("label",{className:`flex flex-col items-center justify-center w-full h-20 sm:h-24 border-2 border-dashed ${a?"border-primary/30 bg-primary/5":"border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/50 hover:bg-neutral-100 dark:hover:bg-neutral-800"} rounded-lg ${a?"cursor-wait":"cursor-pointer"} transition-all duration-300`,children:[(0,t.jsx)("div",{className:"flex flex-col items-center justify-center pt-3 pb-3 sm:pt-4 sm:pb-4",children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"p-1.5 sm:p-2 rounded-full bg-primary/10 mb-1 sm:mb-2",children:(0,t.jsx)(A.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-primary animate-spin"})}),(0,t.jsx)("p",{className:"text-xs text-primary font-medium",children:"Uploading logo..."}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 text-center mt-0.5 max-w-xs px-4",children:"Please wait while we process your image"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"p-1.5 sm:p-2 rounded-full bg-primary/10 mb-1 sm:mb-2 hover:bg-primary/20 transition-colors",children:(0,t.jsx)(S.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-primary"})}),(0,t.jsx)("p",{className:"text-xs text-neutral-700 dark:text-neutral-300 font-medium",children:n?"Replace logo":"Drop your logo here"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 text-center mt-0.5 max-w-xs px-4",children:"PNG, JPG, GIF or WEBP (Max. 15MB)"})]})}),(0,t.jsx)(U.p,{type:"file",accept:"image/png, image/jpeg, image/gif, image/webp",className:"hidden",onChange:e=>r(e.target.files?.[0]||null),value:void 0,disabled:a})]})})}),(0,t.jsxs)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1",children:[(0,t.jsx)(R.A,{className:"w-3 h-3"}),"Your logo will be displayed prominently on your business card"]}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}}),(0,t.jsx)(I.zB,{control:e.control,name:"about_bio",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(P.A,{className:"h-3.5 w-3.5 text-primary"}),"About / Bio"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(E.T,{placeholder:"Tell us about your business or yourself...",...e,className:"min-h-[80px] sm:min-h-[100px] rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 p-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none transition-all",maxLength:100}),(0,t.jsxs)("div",{className:"absolute right-2 bottom-2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[l,"/100"]})]})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"A short description that will be displayed on your card"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})]}),(0,t.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm",children:(0,t.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,t.jsx)("div",{className:"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm",children:(0,t.jsx)(R.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300",children:"Pro Tip"}),(0,t.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed",children:"Keep your business name and title concise for better readability on your digital business card. A clear, professional photo or logo helps with brand recognition and creates a memorable first impression."})]})]})})]})}var O=a(97992),B=a(48340),M=a(11437),F=a(63974);function $({form:e,isPincodeLoading:r,availableLocalities:a,onPincodeChange:s}){return(0,t.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,t.jsx)(O.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Contact & Location"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Add your contact information and business location details"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,t.jsx)(I.zB,{control:e.control,name:"phone",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(B.A,{className:"h-3.5 w-3.5 text-primary"}),"Primary Phone",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(U.p,{placeholder:"9876543210",type:"tel",pattern:"[0-9]*",inputMode:"numeric",...e,onChange:r=>{let a=r.target.value.replace(/^\+91/,"");(a=a.replace(/\D/g,"")).length<=10&&e.onChange(a)},onKeyDown:e=>{let r=/^[0-9]$/.test(e.key),a=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);r||a||e.preventDefault()},className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your primary contact number for customers"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"address_line",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(j.A,{className:"h-3.5 w-3.5 text-primary"}),"Address Line",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(U.p,{placeholder:"e.g., Shop No. 12, Main Road",...e,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:100}),(0,t.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[e.value?.length||0,"/100"]})]})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your street address or landmark"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})]}),(0,t.jsx)(I.zB,{control:e.control,name:"pincode",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(M.A,{className:"h-3.5 w-3.5 text-primary"}),"Pincode",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[(0,t.jsx)(I.MJ,{className:"flex-1",children:(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(U.p,{placeholder:"e.g., 751001",...e,value:e.value??"",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",maxLength:6,type:"number",onChange:r=>{e.onChange(r),6===r.target.value.length&&s(r.target.value)},onInput:e=>{let r=e.target;r.value=r.value.replace(/[^0-9]/g,"")}})})}),r&&(0,t.jsx)("div",{className:"p-1.5 rounded-md bg-neutral-100 dark:bg-neutral-800",children:(0,t.jsx)(A.A,{className:"h-4 w-4 sm:h-5 sm:w-5 animate-spin text-primary"})})]}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"6-digit pincode to auto-fill city and state"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,t.jsx)(I.zB,{control:e.control,name:"city",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(O.A,{className:"h-3.5 w-3.5 text-primary/50"}),"City",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(U.p,{placeholder:"Auto-filled from Pincode",...e,value:e.value??"",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-100/80 dark:bg-neutral-800/40 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 cursor-not-allowed",readOnly:!0})})}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"state",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(O.A,{className:"h-3.5 w-3.5 text-primary/50"}),"State",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(U.p,{placeholder:"Auto-filled from Pincode",...e,value:e.value??"",className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-100/80 dark:bg-neutral-800/40 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 cursor-not-allowed",readOnly:!0})})}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})]}),(0,t.jsx)(I.zB,{control:e.control,name:"locality",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(O.A,{className:"h-3.5 w-3.5 text-primary"}),"Locality / Area",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)(F.l6,{onValueChange:e.onChange,value:e.value??"",disabled:0===a.length,children:[(0,t.jsx)(I.MJ,{children:(0,t.jsx)(F.bq,{className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",disabled:0===a.length,children:(0,t.jsx)(F.yv,{placeholder:0===a.length?"Enter Pincode first":"Select your locality"})})}),(0,t.jsx)(F.gC,{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg",children:a.map(e=>(0,t.jsx)(F.eb,{value:e,className:"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20",children:e},e))})]}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Select the specific area within the pincode"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})]}),(0,t.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm",children:(0,t.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,t.jsx)("div",{className:"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm",children:(0,t.jsx)(R.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300",children:"Location Tip"}),(0,t.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed",children:'Adding accurate location details helps customers find you easily. Pincode auto-fills city and state for consistency. Add your Google Maps URL to show a "Get Directions" button on your public card.'})]})]})})]})}var z=a(98971),q=a(13943);function G({form:e,currentUserPlan:r}){return(0,t.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,t.jsx)(z.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Appearance"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Customize your card's visual appearance"})]})]}),(0,t.jsx)("div",{className:"flex flex-col gap-4 sm:gap-6",children:(0,t.jsx)(I.zB,{control:e.control,name:"theme_color",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(z.A,{className:"h-3.5 w-3.5 text-primary"}),"Theme Color"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[(0,t.jsxs)("div",{className:"relative flex-grow",children:[(0,t.jsx)(z.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,t.jsx)(U.p,{type:"text",...e,className:`pl-10 w-full font-mono uppercase rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 ${"pro"!==r&&"enterprise"!==r?"opacity-60 cursor-not-allowed":""}`,value:e.value||"#F5D76E",onChange:a=>{if("pro"===r||"enterprise"===r){let r=a.target.value;(""===r||/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})$/.test(r))&&e.onChange(r)}},disabled:"pro"!==r&&"enterprise"!==r})]}),(0,t.jsx)(U.p,{type:"color",...e,className:`h-8 sm:h-10 w-10 sm:w-12 p-1 rounded-md ${"pro"===r||"enterprise"===r?"cursor-pointer":"cursor-not-allowed opacity-60"} border border-neutral-200 dark:border-neutral-700 hover:border-primary dark:hover:border-primary transition-all duration-200`,value:e.value||"#F5D76E",disabled:"pro"!==r&&"enterprise"!==r}),(0,t.jsx)("div",{className:"h-8 sm:h-10 w-8 sm:w-10 rounded-md border border-neutral-200 dark:border-neutral-700 shadow-inner transition-transform hover:scale-105",style:{backgroundColor:e.value||"#F5D76E"}}),("pro"===r||"enterprise"===r)&&(0,t.jsx)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>e.onChange(""),className:"h-8 sm:h-10 px-2 sm:px-3 text-xs hover:bg-red-50 hover:border-red-200 hover:text-red-600 dark:hover:bg-red-950/20 dark:hover:border-red-800 dark:hover:text-red-400 transition-colors",title:"Reset to default color",children:(0,t.jsx)(q.A,{className:"h-3 w-3 sm:h-3.5 sm:w-3.5"})})]})}),(0,t.jsxs)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1",children:[(0,t.jsx)(R.A,{className:"w-3 h-3"}),"pro"===r||"enterprise"===r?"Select the primary accent color for your card":"Theme customization is only available for Pro and Enterprise plans"]}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})}),("basic"===r||"growth"===r||"trial"===r)&&(0,t.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-blue-100 dark:border-blue-900/30 shadow-sm",children:(0,t.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,t.jsx)("div",{className:"p-1.5 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 mt-0.5 shadow-sm",children:(0,t.jsx)(R.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm font-medium text-blue-800 dark:text-blue-300",children:"Pro Plan Unlocks Custom Theme Colors"}),(0,t.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-400 mt-0.5 sm:mt-1 leading-relaxed",children:"Upgrade to Pro for more customization options, including custom theme colors for your card."})]}),(0,t.jsx)("button",{className:"ml-auto px-3 sm:px-4 py-1.5 sm:py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-xs sm:text-sm font-medium transition-colors",children:"Upgrade"})]})})]})}var V=a(12597),Y=a(13861),J=a(62688);let H=(0,J.A)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);var Z=a(9005),W=a(40214),K=a(59821),X=a(78377),Q=a(10218),ee=a(21134),er=a(363),ea=a(11860),et=a(55192);function es({theme:e,imageUrl:r,isUploading:a,isDeleting:l,isDragging:n,onFileSelect:i,onDelete:o,onDrop:d,onDragOver:u,onDragLeave:m}){let x=(0,s.useRef)(null),p="light"===e?ee.A:er.A,h="light"===e?"Light":"Dark",g="light"===e?"text-yellow-600":"text-blue-600",b="light"===e?"bg-yellow-50 dark:bg-yellow-950/20":"bg-blue-50 dark:bg-blue-950/20",f="light"===e?"border-yellow-200 dark:border-yellow-800":"border-blue-200 dark:border-blue-800";return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p,{className:`h-4 w-4 ${g}`}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[h," Theme"]})]}),(0,t.jsx)(et.Zp,{className:`border-dashed border-2 transition-colors cursor-pointer ${n?"border-primary bg-primary/5":"border-muted-foreground/25 hover:border-primary/50"}`,onDrop:r=>d(r,e),onDragOver:r=>u(r,e),onDragLeave:r=>m(r,e),onClick:()=>!a&&!l&&x.current?.click(),children:(0,t.jsxs)(et.Wu,{className:"p-4",children:[a?(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-lg bg-muted flex items-center justify-center mx-auto mb-2",children:(0,t.jsx)(A.A,{className:"h-5 w-5 text-muted-foreground animate-spin"})}),(0,t.jsx)("p",{className:"text-xs font-medium mb-1",children:"Uploading..."}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Processing ",h.toLowerCase()," theme image"]})]}):r?(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:`w-full h-16 overflow-hidden rounded-lg border-2 ${f} ${b}`,children:(0,t.jsx)(T.default,{src:r,alt:`${h} theme header`,width:200,height:64,className:"w-full h-full object-contain"})}),(0,t.jsx)(c.$,{type:"button",variant:"destructive",size:"sm",onClick:r=>{r.stopPropagation(),o(e)},disabled:l,className:"absolute top-1 right-1 h-6 w-6 p-0",children:l?(0,t.jsx)(A.A,{className:"h-3 w-3 animate-spin"}):(0,t.jsx)(ea.A,{className:"h-3 w-3"})})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:`text-xs font-medium ${g}`,children:[h," theme image uploaded"]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Click to replace"})]})]}):(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-lg bg-muted flex items-center justify-center mx-auto mb-2",children:(0,t.jsx)(S.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsxs)("p",{className:"text-xs font-medium mb-1",children:["Upload ",h," Image"]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mb-1",children:"Drag & drop or click"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"PNG • Max 5MB"})]}),(0,t.jsx)("input",{ref:x,type:"file",accept:"image/*",onChange:r=>i(r.target.files?.[0]||null,e),className:"hidden",disabled:a||l})]})})]})}function el({form:e,currentUserPlan:r}){let a="pro"===r||"enterprise"===r,{resolvedTheme:l}=(0,Q.D)(),[n,o]=(0,s.useState)(!1),[d,u]=(0,s.useState)(!1),m=r=>{r.trim()?e.setValue("custom_branding.hide_dukancard_branding",!0):e.setValue("custom_branding.hide_dukancard_branding",!1)},x=r=>{r||(e.clearErrors("custom_branding.custom_header_text"),e.trigger("custom_branding"))},p=async(r,a)=>{if(r){if(!r.type.startsWith("image/"))return void i.oR.error("Please upload a valid image file");if(r.size>5242880){let e=(r.size/1048576).toFixed(1);i.oR.error(`Image size (${e}MB) is too large. Please choose an image smaller than 5MB for optimal performance.`);return}try{let t=await (0,f.q)(r,{maxDimension:400,targetSizeKB:150}),s=new File([t.blob],r.name,{type:t.blob.type}),l=URL.createObjectURL(s);"light"===a?(e.setValue("custom_branding.custom_header_image_light_url",l,{shouldDirty:!0}),e.setValue("custom_branding.pending_light_header_file",s,{shouldDirty:!0})):(e.setValue("custom_branding.custom_header_image_dark_url",l,{shouldDirty:!0}),e.setValue("custom_branding.pending_dark_header_file",s,{shouldDirty:!0})),e.setValue("custom_branding.hide_dukancard_branding",!0,{shouldDirty:!0}),i.oR.success(`${"light"===a?"Light":"Dark"} theme header image compressed and ready. Click "Save Changes" to upload.`)}catch(e){console.error("Image compression failed:",e),i.oR.error("Failed to process image. Please try a different image.")}}},h=r=>{"light"===r?(e.setValue("custom_branding.custom_header_image_light_url","",{shouldDirty:!0}),e.setValue("custom_branding.pending_light_header_file",null,{shouldDirty:!0})):(e.setValue("custom_branding.custom_header_image_dark_url","",{shouldDirty:!0}),e.setValue("custom_branding.pending_dark_header_file",null,{shouldDirty:!0})),i.oR.success(`${"light"===r?"Light":"Dark"} theme header image will be removed when you save changes`)},g=(e,r)=>{e.preventDefault(),("light"===r?o:u)(!1);let a=e.dataTransfer.files;a.length>0&&p(a[0],r)},b=(e,r)=>{e.preventDefault(),("light"===r?o:u)(!0)},y=(e,r)=>{e.preventDefault(),("light"===r?o:u)(!1)};return a?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(z.A,{className:"h-5 w-5 text-primary"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-200",children:"Custom Branding"}),(0,t.jsx)(K.E,{variant:"default",className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:r?.toUpperCase()})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(I.zB,{control:e.control,name:"custom_branding.hide_dukancard_branding",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsxs)(I.lR,{className:"text-base font-medium flex items-center gap-2",children:[e.value?(0,t.jsx)(V.A,{className:"h-4 w-4"}):(0,t.jsx)(Y.A,{className:"h-4 w-4"}),"Hide Dukancard Branding"]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Remove Dukancard branding from your business card"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)(W.d,{checked:e.value,onCheckedChange:r=>{e.onChange(r),x(r)}})})]})}),(0,t.jsx)(X.w,{}),(0,t.jsx)(I.zB,{control:e.control,name:"custom_branding.custom_header_text",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(H,{className:"h-4 w-4 text-primary"}),"Custom Header Text"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)(U.p,{...e,value:e.value||"",onChange:r=>{e.onChange(r.target.value),m(r.target.value)},placeholder:"e.g., Powered by YourBrand",maxLength:50})}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:'Custom text to display in the header instead of Dukancard branding (max 50 characters). Required when "Hide Dukancard Branding" is enabled.'}),(0,t.jsx)(I.C5,{})]})}),(0,t.jsx)(X.w,{}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 text-primary","aria-label":"Theme specific header"}),"Theme-Specific Header Images (Alternative to Text)"]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Upload custom images for light and dark themes. Images will automatically switch based on the user's theme preference. PNG format recommended for best quality."}),(0,t.jsx)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)("span",{className:`w-2 h-2 rounded-full ${"light"===l?"bg-yellow-500":"bg-gray-400"}`}),"Current: ","light"===l?"Light":"dark"===l?"Dark":"System"]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(es,{theme:"light",imageUrl:e.watch("custom_branding.custom_header_image_light_url"),isUploading:!1,isDeleting:!1,isDragging:n,onFileSelect:p,onDelete:h,onDrop:g,onDragOver:b,onDragLeave:y}),(0,t.jsx)(es,{theme:"dark",imageUrl:e.watch("custom_branding.custom_header_image_dark_url"),isUploading:!1,isDeleting:!1,isDragging:d,onFileSelect:p,onDelete:h,onDrop:g,onDragOver:b,onDragLeave:y})]})]}),(0,t.jsx)(X.w,{}),(0,t.jsx)("div",{className:"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,t.jsx)(z.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1",children:"Theme Color Customization"}),(0,t.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["Customize your business card's theme color in the ",(0,t.jsx)("strong",{children:"Appearance"})," section above. Pro and Enterprise users can choose any custom color, while other plans use the default gold theme."]})]})]})})]})]}):(0,t.jsxs)("div",{className:"space-y-4 p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-950/20",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(z.A,{className:"h-5 w-5 text-amber-600 dark:text-amber-400"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-amber-800 dark:text-amber-200",children:"Custom Branding"}),(0,t.jsx)(K.E,{variant:"secondary",className:"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200",children:"Pro Feature"})]}),(0,t.jsx)("p",{className:"text-sm text-amber-700 dark:text-amber-300",children:"Upgrade to Pro or Enterprise plan to access custom branding features including custom logos, watermarks, colors, and the ability to hide Dukancard branding."}),(0,t.jsx)(c.$,{variant:"outline",className:"border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20",children:"Upgrade to Pro"})]})}let en=(0,x.createServerReference)("40aaafea41f50b748ef546f36a08d4542ed53be507",x.callServer,void 0,x.findSourceMapURL,"uploadCustomAdImage"),ei=(0,x.createServerReference)("00c08d23d995424fbe98469845e395857a3ae1a19c",x.callServer,void 0,x.findSourceMapURL,"deleteCustomAd");var eo=a(47473),ed=a(37826);let ec=(0,J.A)("Crop",[["path",{d:"M6 2v14a2 2 0 0 0 2 2h14",key:"ron5a4"}],["path",{d:"M18 22V8a2 2 0 0 0-2-2H2",key:"7s9ehn"}]]);var eu=a(58709);let em=e=>new Promise((r,a)=>{let t=new Image;t.addEventListener("load",()=>r(t)),t.addEventListener("error",e=>a(e)),t.setAttribute("crossOrigin","anonymous"),t.src=e});async function ex(e,r){let a=await em(e),t=document.createElement("canvas"),s=t.getContext("2d");if(!s)return null;let l=a.naturalWidth/a.width,n=a.naturalHeight/a.height,i=window.devicePixelRatio||1;return t.width=r.width*i*l,t.height=r.height*i*n,s.setTransform(i,0,0,i,0,0),s.imageSmoothingQuality="high",s.drawImage(a,r.x*l,r.y*n,r.width*l,r.height*n,0,0,r.width*l,r.height*n),new Promise(e=>{t.toBlob(e,"image/png")})}function ep({imgSrc:e,onCropComplete:r,onClose:a,isOpen:l,isUploading:n=!1}){let[i,o]=(0,s.useState)({x:0,y:0}),[d,u]=(0,s.useState)(1),[m,x]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1),g=(0,s.useCallback)((e,r)=>{x(r)},[]),b=async()=>{if(!e||!m){console.warn("Image source or crop area not available."),r(null);return}h(!0);try{let a=await ex(e,m);r(a)}catch(e){console.error("Error cropping image:",e),r(null)}finally{h(!1)}};return(0,t.jsx)(ed.lG,{open:l,onOpenChange:e=>!e&&!n&&!p&&a(),children:(0,t.jsxs)(ed.Cf,{className:"sm:max-w-[700px]",children:[(0,t.jsx)(ed.c7,{children:(0,t.jsxs)(ed.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ec,{className:"h-5 w-5 text-primary"}),"Crop Your Advertisement"]})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"relative h-[45vh] md:h-[55vh] w-full bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden border-2 border-primary/20",children:e?(0,t.jsx)(eo.Ay,{image:e,crop:i,zoom:d,aspect:16/9,cropShape:"rect",showGrid:!0,onCropChange:o,onZoomChange:u,onCropComplete:g,style:{containerStyle:{borderRadius:"8px",overflow:"hidden"},cropAreaStyle:{border:"2px solid hsl(var(--primary))",borderRadius:"4px"}}}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(A.A,{className:"h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading image..."})]})})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-neutral-700 dark:text-neutral-300",children:["Zoom: ",Math.round(100*d),"%"]}),(0,t.jsx)(eu.A,{value:[d],onValueChange:e=>u(e[0]),min:1,max:3,step:.1,className:"w-full"})]}),(0,t.jsx)("div",{className:"bg-neutral-50 dark:bg-neutral-900 rounded-lg p-3 border",children:(0,t.jsx)("div",{className:"flex items-center justify-center text-sm",children:(0,t.jsx)("span",{className:"text-muted-foreground",children:"Cropping to 16:9 aspect ratio for advertisement display"})})})]}),(0,t.jsxs)(ed.Es,{className:"gap-2",children:[(0,t.jsx)(c.$,{variant:"outline",onClick:a,disabled:p||n,children:"Cancel"}),(0,t.jsx)(c.$,{onClick:b,disabled:p||n||!m,className:"min-w-[120px]",children:p||n?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"mr-2 h-4 w-4 animate-spin"}),p?"Processing...":"Uploading..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ec,{className:"mr-2 h-4 w-4"}),"Upload Ad"]})})]})]})})}function eh({form:e,currentUserPlan:r}){let[a,l]=(0,s.useState)(!1),[n,o]=(0,s.useState)(!1),[u,m]=(0,s.useState)(!1),[x,p]=(0,s.useState)(null),[h,g]=(0,s.useState)(null),b=(0,s.useRef)(null),y="pro"===r||"enterprise"===r,_=(0,s.useCallback)(e=>{if(!e)return;if(!e.type.startsWith("image/"))return void i.oR.error("Please select a valid image file");if(e.size>5242880)return void i.oR.error("Image file size must be less than 5MB. Please choose a smaller file.");g(e);let r=new FileReader;r.onloadend=()=>{p(r.result)},r.readAsDataURL(e)},[]),v=(0,s.useCallback)(e=>{if(e.preventDefault(),m(!1),!y||a)return;let r=e.dataTransfer.files?.[0];r&&_(r)},[y,a,_]),j=(0,s.useCallback)(e=>{e.preventDefault(),y&&!a&&m(!0)},[y,a]),w=(0,s.useCallback)(e=>{e.preventDefault(),m(!1)},[]),N=async r=>{if(!h||!r){p(null),g(null);return}l(!0);try{let a=new File([r],h.name,{type:"image/png",lastModified:Date.now()}),t=await (0,f.compressImageUltraAggressiveClient)(a,{maxDimension:1200,targetSizeKB:100}),s=new File([t.blob],h.name,{type:t.blob.type}),l=new FormData;l.append("image",s);let n=await en(l);n.success&&n.url?(e.setValue("custom_ads.image_url",n.url),e.setValue("custom_ads.enabled",!0),e.setValue("custom_ads.uploaded_at",new Date().toISOString()),i.oR.success("Custom ad uploaded successfully!")):i.oR.error(n.error||"Failed to upload custom ad")}catch(e){console.error("Upload error:",e),i.oR.error("Failed to upload custom ad")}finally{l(!1),p(null),g(null)}},k=r=>{e.setValue("custom_ads.enabled",r,{shouldDirty:!0})},C=async()=>{o(!0);try{let r=await ei();r.success?(e.setValue("custom_ads.enabled",!1),e.setValue("custom_ads.image_url",""),e.setValue("custom_ads.link_url",""),e.setValue("custom_ads.uploaded_at",""),i.oR.success("Custom ad deleted successfully")):i.oR.error(r.error||"Failed to delete custom ad")}catch(e){console.error("Delete error:",e),i.oR.error("Failed to delete custom ad")}finally{o(!1)}};if(!y)return(0,t.jsxs)("div",{className:"space-y-4 p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-950/20",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5 text-amber-600 dark:text-amber-400","aria-label":"Custom ads feature"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-amber-800 dark:text-amber-200",children:"Custom Ads"}),(0,t.jsx)(K.E,{variant:"secondary",className:"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200",children:"Pro Feature"})]}),(0,t.jsx)("p",{className:"text-sm text-amber-700 dark:text-amber-300",children:"Upgrade to Pro or Enterprise plan to upload custom advertisement images that will be displayed on your public business card page."}),(0,t.jsx)(c.$,{variant:"outline",className:"border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20",children:"Upgrade to Pro"})]});let R=e.watch("custom_ads.image_url"),P=e.watch("custom_ads.enabled");return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5 text-primary","aria-label":"Custom advertisement"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-neutral-800 dark:text-neutral-200",children:"Custom Advertisement"}),(0,t.jsx)(K.E,{variant:"default",className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:r?.toUpperCase()})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(I.zB,{control:e.control,name:"custom_ads.enabled",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsxs)(I.lR,{className:"text-base font-medium flex items-center gap-2",children:[e.value?(0,t.jsx)(Y.A,{className:"h-4 w-4"}):(0,t.jsx)(V.A,{className:"h-4 w-4"}),"Show Custom Ad"]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Display your custom advertisement on the public business card page"})]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)(W.d,{checked:e.value,onCheckedChange:r=>{e.onChange(r),k(r)},disabled:!R})})]})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(I.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 text-primary"}),"Advertisement Image"]}),(0,t.jsx)(et.Zp,{className:`border-dashed border-2 transition-colors cursor-pointer ${u?"border-primary bg-primary/5":"border-muted-foreground/25 hover:border-primary/50"}`,onDrop:v,onDragOver:j,onDragLeave:w,onClick:()=>!a&&!n&&b.current?.click(),children:(0,t.jsxs)(et.Wu,{className:"p-6",children:[a?(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-4 border-2 border-primary/20",children:(0,t.jsx)(A.A,{className:"h-8 w-8 text-primary animate-spin"})}),(0,t.jsx)("p",{className:"text-sm font-medium mb-2 text-primary",children:"Uploading Custom Ad..."}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Compressing and optimizing image"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Uploading to secure storage"})]}),(0,t.jsx)("div",{className:"mt-3 w-full bg-muted rounded-full h-1.5",children:(0,t.jsx)("div",{className:"bg-primary h-1.5 rounded-full animate-pulse",style:{width:"70%"}})})]}):R?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"w-full aspect-[16/9] overflow-hidden rounded-lg border-2 border-green-200 dark:border-green-800",children:(0,t.jsx)(T.default,{src:R,alt:"Custom advertisement",width:400,height:225,className:"w-full h-full object-cover"})}),(0,t.jsx)(c.$,{type:"button",variant:"destructive",size:"sm",onClick:e=>{e.stopPropagation(),C()},disabled:n,className:"absolute top-2 right-2",children:n?(0,t.jsx)(A.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(ea.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{className:"absolute bottom-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded flex items-center gap-1",children:[(0,t.jsx)(Y.A,{className:"h-3 w-3"}),P?"Live":"Draft"]})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-green-700 dark:text-green-300",children:"Custom ad uploaded successfully"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[P?"Visible on your public card":"Enable to show on public card"," • Click to replace"]})]})]}):(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-lg bg-muted flex items-center justify-center mx-auto mb-3",children:(0,t.jsx)(S.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,t.jsx)("p",{className:"text-sm font-medium mb-1",children:"Upload Custom Ad"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mb-2",children:"Drag & drop or click to select"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,t.jsx)("strong",{children:"Aspect Ratio:"})," 16:9 (recommended)"]})]}),(0,t.jsx)("input",{ref:b,type:"file",accept:"image/*",onChange:e=>_(e.target.files?.[0]||null),className:"hidden",disabled:a||n})]})})]}),(0,t.jsx)(I.zB,{control:e.control,name:"custom_ads.link_url",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-primary"}),"Advertisement Link (Optional)"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)(U.p,{...e,value:e.value||"",onChange:r=>{e.onChange(r.target.value)},placeholder:"https://example.com"})}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:'Make your ad clickable by adding a website URL. Changes will be saved when you click "Save Changes".'}),(0,t.jsx)(I.C5,{})]})})]}),(0,t.jsx)(ep,{isOpen:!!x,imgSrc:x,onCropComplete:N,onClose:()=>{a||(p(null),g(null))},isUploading:a})]})}var eg=a(48730),eb=a(88059),ef=a(93437),ey=a(39390);let e_=(0,J.A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]),ev=e=>{if(!e||e.length<5)return e;let[r,a]=e.split(":"),t=parseInt(r,10);return isNaN(t)?e:`${t%12||12}:${a} ${t>=12?"PM":"AM"}`},ej={isOpen:!1,openTime:"09:00",closeTime:"18:00"},ew={monday:{...ej},tuesday:{...ej},wednesday:{...ej},thursday:{...ej},friday:{...ej},saturday:{...ej},sunday:{...ej}},eN={weekdays:{label:"Mon-Fri",days:["monday","tuesday","wednesday","thursday","friday"]},weekend:{label:"Sat-Sun",days:["saturday","sunday"]},all:{label:"All Days",days:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]}};function ek({value:e,onChange:r}){let[a,l]=(0,s.useState)(()=>{try{if(!e)return{...ew};let r={...ew};return Object.keys(ew).forEach(a=>{if(e[a]&&"object"==typeof e[a]){let t=e[a];r[a]={isOpen:"boolean"==typeof t.isOpen&&t.isOpen,openTime:"string"==typeof t.openTime?t.openTime:"09:00",closeTime:"string"==typeof t.closeTime?t.closeTime:"18:00"}}}),r}catch(e){return console.error("Error parsing business hours:",e),{...ew}}}),[n,i]=(0,s.useState)(()=>({weekdays:eN.weekdays.days.some(e=>a[e].isOpen),weekend:eN.weekend.days.some(e=>a[e].isOpen),all:Object.values(a).some(e=>e.isOpen)})),[o,d]=(0,s.useState)(()=>{let e=e=>{let r=eN[e].days.find(e=>a[e].isOpen);return r?{openTime:a[r].openTime,closeTime:a[r].closeTime}:{openTime:"09:00",closeTime:"18:00"}};return{weekdays:e("weekdays"),weekend:e("weekend"),all:e("all")}}),u=(e,t,s,n)=>{i(r=>({...r,[e]:t})),void 0!==s&&void 0!==n&&d(r=>({...r,[e]:{openTime:s,closeTime:n}}));let c={...a};eN[e].days.forEach(r=>{c[r]={isOpen:t,openTime:s||o[e].openTime,closeTime:n||o[e].closeTime}}),l(c),r(c)};return(0,t.jsxs)("div",{className:"border border-neutral-200 dark:border-neutral-700 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 overflow-hidden",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-neutral-200 dark:border-neutral-700",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eg.A,{className:"h-4 w-4 text-neutral-500"}),(0,t.jsx)("h3",{className:"text-sm font-medium",children:"Business Hours"})]})}),(0,t.jsx)("div",{className:"p-4 space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden",children:[(0,t.jsx)("div",{className:"flex items-center justify-between p-3 bg-neutral-100 dark:bg-neutral-800",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ef.S,{id:"weekdays-open",checked:n.weekdays,onCheckedChange:e=>{u("weekdays",!0===e)}}),(0,t.jsx)(ey.J,{htmlFor:"weekdays-open",className:"font-medium cursor-pointer",children:eN.weekdays.label})]})}),n.weekdays&&(0,t.jsx)("div",{className:"p-3 bg-white dark:bg-neutral-900",children:(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,t.jsx)(ey.J,{className:"text-xs text-neutral-500 w-full sm:w-auto",children:"Hours:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1 flex-wrap",children:[(0,t.jsx)(U.p,{type:"time",value:o.weekdays.openTime,onChange:e=>{u("weekdays",!0,e.target.value,o.weekdays.closeTime)},className:"w-32 py-1 px-2 text-sm"}),(0,t.jsx)("span",{className:"text-neutral-500 text-xs",children:"to"}),(0,t.jsx)(U.p,{type:"time",value:o.weekdays.closeTime,onChange:e=>{u("weekdays",!0,o.weekdays.openTime,e.target.value)},className:"w-32 py-1 px-2 text-sm"}),(0,t.jsxs)("span",{className:"text-neutral-500 text-xs ml-2",children:[ev(o.weekdays.openTime)," -"," ",ev(o.weekdays.closeTime)]})]})]})})]}),(0,t.jsxs)("div",{className:"rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden",children:[(0,t.jsx)("div",{className:"flex items-center justify-between p-3 bg-neutral-100 dark:bg-neutral-800",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ef.S,{id:"weekend-open",checked:n.weekend,onCheckedChange:e=>{u("weekend",!0===e)}}),(0,t.jsx)(ey.J,{htmlFor:"weekend-open",className:"font-medium cursor-pointer",children:eN.weekend.label})]})}),n.weekend&&(0,t.jsx)("div",{className:"p-3 bg-white dark:bg-neutral-900",children:(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,t.jsx)(ey.J,{className:"text-xs text-neutral-500 w-full sm:w-auto",children:"Hours:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1 flex-wrap",children:[(0,t.jsx)(U.p,{type:"time",value:o.weekend.openTime,onChange:e=>{u("weekend",!0,e.target.value,o.weekend.closeTime)},className:"w-32 py-1 px-2 text-sm"}),(0,t.jsx)("span",{className:"text-neutral-500 text-xs",children:"to"}),(0,t.jsx)(U.p,{type:"time",value:o.weekend.closeTime,onChange:e=>{u("weekend",!0,o.weekend.openTime,e.target.value)},className:"w-32 py-1 px-2 text-sm"}),(0,t.jsxs)("span",{className:"text-neutral-500 text-xs ml-2",children:[ev(o.weekend.openTime)," -"," ",ev(o.weekend.closeTime)]})]})]})})]})]})})]}),(0,t.jsx)("div",{className:"flex justify-end p-4 border-t border-neutral-200 dark:border-neutral-700",children:(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{l({...ew}),i({weekdays:!1,weekend:!1,all:!1}),d({weekdays:{openTime:"09:00",closeTime:"18:00"},weekend:{openTime:"09:00",closeTime:"18:00"},all:{openTime:"09:00",closeTime:"18:00"}}),r({...ew})},className:"text-xs",children:[(0,t.jsx)(e_,{className:"h-3 w-3 mr-1"}),"Reset All"]})})]})}function eS({form:e}){return(0,t.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,t.jsx)(eg.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Business Details"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Add your business hours and delivery information"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,t.jsx)(I.zB,{control:e.control,name:"business_hours",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(eg.A,{className:"h-3.5 w-3.5 text-primary"}),"Business Hours"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsx)(ek,{value:e.value,onChange:e.onChange})}),(0,t.jsxs)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1",children:[(0,t.jsx)(R.A,{className:"w-3 h-3"}),"Set your business hours to let customers know when you're open"]}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"delivery_info",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(eb.A,{className:"h-3.5 w-3.5 text-primary"}),"Delivery Info"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(eb.A,{className:"absolute left-3 top-3 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,t.jsx)(E.T,{placeholder:"e.g., Free delivery within 5km, Delivery charges apply...",...e,className:"min-h-[80px] pl-10 rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm resize-none transition-all duration-200",maxLength:100}),(0,t.jsxs)("div",{className:"absolute right-2 bottom-2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500",children:[e.value?.length||0,"/100"]})]})}),(0,t.jsxs)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1",children:[(0,t.jsx)(R.A,{className:"w-3 h-3"}),"Delivery details shown on your card"]}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})]})]})}var eC=a(66232),eA=a(19526);function eR({form:e}){let r=e.watch("phone"),[a,l]=(0,s.useState)(!1);return(0,t.jsxs)("div",{className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg bg-primary/10 text-primary self-start",children:(0,t.jsx)(d.A,{className:"w-4 sm:w-5 h-4 sm:h-5"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",children:"Links"}),(0,t.jsx)("p",{className:"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",children:"Add your social media and communication links"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,t.jsx)(I.zB,{control:e.control,name:"whatsapp_number",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(P.A,{className:"h-3.5 w-3.5 text-primary"}),"WhatsApp Number"]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(P.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,t.jsx)(U.p,{type:"tel",pattern:"[0-9]*",inputMode:"numeric",placeholder:"9876543210",...e,onChange:r=>{let a=r.target.value.replace(/^\+91/,"");(a=a.replace(/\D/g,"")).length<=10&&e.onChange(a)},onKeyDown:e=>{let r=/^[0-9]$/.test(e.key),a=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);r||a||e.preventDefault()},className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200",disabled:a})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(ef.S,{id:"copy_whatsapp",checked:a,onCheckedChange:e=>l(!!e),disabled:!r}),(0,t.jsx)(ey.J,{htmlFor:"copy_whatsapp",className:"text-xs font-normal cursor-pointer text-neutral-700 dark:text-neutral-300",children:"Use primary phone"})]})]}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Used to generate wa.me link. Enter 10-digit mobile number."}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),(0,t.jsx)(I.zB,{control:e.control,name:"instagram_url",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(eC.A,{className:"h-3.5 w-3.5 text-primary"}),"Instagram Profile URL"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(eC.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,t.jsx)(U.p,{type:"url",placeholder:"https://instagram.com/yourprofile",...e,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})]})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your Instagram profile link"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})]}),(0,t.jsx)(I.zB,{control:e.control,name:"facebook_url",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-1 sm:space-y-2",children:[(0,t.jsxs)(I.lR,{className:"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5",children:[(0,t.jsx)(eA.A,{className:"h-3.5 w-3.5 text-primary"}),"Facebook Page URL"]}),(0,t.jsx)(I.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(eA.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400"}),(0,t.jsx)(U.p,{type:"url",placeholder:"https://facebook.com/yourpage",...e,className:"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"})]})}),(0,t.jsx)(I.Rr,{className:"text-xs text-neutral-500 dark:text-neutral-400 ml-1",children:"Your Facebook page link"}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})})]}),(0,t.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm",children:(0,t.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,t.jsx)("div",{className:"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm",children:(0,t.jsx)(d.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300",children:"Links Tip"}),(0,t.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed",children:"Including your WhatsApp number and social media links makes it easier for customers to connect with you instantly."})]})]})})]})}var eP=a(93613),eU=a(13964),eE=a(5336),eI=a(35071),eL=a(20540),eT=a.n(eL);let eD=(0,x.createServerReference)("407090e0ad54740b8bc9265b7b903efb29a22d1ef2",x.callServer,void 0,x.findSourceMapURL,"checkSlugAvailability");var eO=a(21761);function eB({form:e,canGoOnline:r,isSubscriptionHalted:a=!1,onSlugCheckingChange:l}){let[n,i]=(0,s.useState)(!1),[c,u]=(0,s.useState)(null),[x,p]=(0,s.useState)(""),h=(0,s.useCallback)(()=>{let r=e.getValues();return m.b4.filter(e=>!r[e]||""===String(r[e]).trim())},[e]),g=(0,s.useCallback)(async r=>{if(!r||r.length<3||!/^[a-z0-9-]+$/.test(r))return void u(null);if(e.getValues("business_slug")!==r||!0!==c){i(!0),l?.(!0);try{let{available:e}=await eD(r);u(e)}catch(e){console.error("Error checking slug availability:",e),u(!1)}finally{i(!1),l?.(!1)}}},[e,c,l]);(0,s.useMemo)(()=>eT()(g,500),[g]),(0,s.useRef)(!1);let b={checking:n,available:c};return(0,t.jsxs)(o.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4},className:"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-lg p-4 sm:p-5 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-xl",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-5 sm:mb-6 pb-4 border-b border-neutral-100 dark:border-neutral-800",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary self-start shadow-sm",children:(0,t.jsx)(M.A,{className:"w-5 sm:w-6 h-5 sm:h-6"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-base sm:text-xl font-semibold text-neutral-800 dark:text-neutral-100",children:"Card Status & URL"}),(0,t.jsx)("p",{className:"text-sm text-neutral-500 dark:text-neutral-400 mt-1",children:"Configure your card's visibility and unique URL"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:gap-6",children:[(0,t.jsx)(I.zB,{control:e.control,name:"status",render:({field:e})=>(0,t.jsxs)(I.eI,{className:"space-y-3 sm:space-y-4",children:[(0,t.jsxs)(I.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 text-primary"}),"Card Status"]}),(0,t.jsxs)(eO.z,{onValueChange:e.onChange,value:e.value,className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5",children:[(0,t.jsx)(ey.J,{htmlFor:"status-offline",className:"cursor-pointer",children:(0,t.jsxs)(o.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},transition:{type:"spring",stiffness:400,damping:17},className:`relative h-full rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-sm
                      ${"offline"===e.value?"border-[--theme-color] bg-[--theme-color]/5 shadow-[--theme-color]/10":"border-neutral-200 dark:border-neutral-700 hover:border-neutral-300 dark:hover:border-neutral-600"}
                    `,children:["offline"===e.value&&(0,t.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-[--theme-color]"}),(0,t.jsxs)("div",{className:"p-4 sm:p-5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:`p-2 rounded-full ${"offline"===e.value?"bg-[--theme-color]/20":"bg-neutral-100 dark:bg-neutral-800"}`,children:(0,t.jsx)(V.A,{className:`h-5 w-5 ${"offline"===e.value?"text-[--theme-color]":"text-neutral-500 dark:text-neutral-400"}`})}),(0,t.jsx)("h4",{className:"text-base font-semibold text-neutral-800 dark:text-neutral-100",children:"Offline (Private)"})]}),(0,t.jsx)(eO.C,{value:"offline",id:"status-offline",className:"translate-y-0"})]}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed",children:"Your card is not publicly visible. Use for expired plans or private cards."})]})]})}),(0,t.jsx)(ey.J,{htmlFor:"status-online",className:`${!r?"cursor-not-allowed":"cursor-pointer"}`,children:(0,t.jsxs)(o.P.div,{whileHover:r?{scale:1.02}:{},whileTap:r?{scale:.98}:{},transition:{type:"spring",stiffness:400,damping:17},className:`relative h-full rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-sm
                      ${"online"===e.value?"border-[--theme-color] bg-[--theme-color]/5 shadow-[--theme-color]/10":"border-neutral-200 dark:border-neutral-700"}
                      ${!r?"opacity-70":"hover:border-neutral-300 dark:hover:border-neutral-600"}
                    `,children:["online"===e.value&&(0,t.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-[--theme-color]"}),(0,t.jsxs)("div",{className:"p-4 sm:p-5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:`p-2 rounded-full ${"online"===e.value?"bg-[--theme-color]/20":"bg-neutral-100 dark:bg-neutral-800"}`,children:(0,t.jsx)(Y.A,{className:`h-5 w-5 ${"online"===e.value?"text-[--theme-color]":"text-neutral-500 dark:text-neutral-400"}`})}),(0,t.jsx)("h4",{className:"text-base font-semibold text-neutral-800 dark:text-neutral-100",children:"Online (Public)"})]}),(0,t.jsx)(eO.C,{value:"online",id:"status-online",disabled:!r,className:"translate-y-0"})]}),(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed",children:"Make your card accessible via a unique URL. Requires an active plan and unique slug."}),a&&(0,t.jsxs)("div",{className:"mt-3 p-3 rounded-lg bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800/50",children:[(0,t.jsxs)("p",{className:"text-xs text-red-700 dark:text-red-400 flex items-center font-medium mb-2",children:[(0,t.jsx)(eP.A,{className:"w-3.5 h-3.5 mr-1.5 flex-shrink-0"}),"Subscription Paused"]}),(0,t.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400",children:"Your subscription is currently paused. You cannot set your card to online status until you resume your subscription. Please visit the Plan page to resume your subscription."})]}),!r&&!a&&(0,t.jsxs)("div",{className:"mt-3 p-3 rounded-lg bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800/50",children:[(0,t.jsxs)("p",{className:"text-xs text-amber-700 dark:text-amber-400 flex items-center font-medium mb-2",children:[(0,t.jsx)(eP.A,{className:"w-3.5 h-3.5 mr-1.5 flex-shrink-0"}),"Required fields to go online:"]}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-x-3 gap-y-1.5",children:(()=>{let e=h(),r={member_name:(0,t.jsx)(_.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),title:(0,t.jsx)(_.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),business_name:(0,t.jsx)(j.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),phone:(0,t.jsx)(B.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),address_line:(0,t.jsx)(j.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),pincode:(0,t.jsx)(O.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),city:(0,t.jsx)(O.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),state:(0,t.jsx)(O.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"}),locality:(0,t.jsx)(O.A,{className:"w-3 h-3 mr-1.5 flex-shrink-0"})},a={member_name:"Your Name",title:"Your Title",business_name:"Business Name",phone:"Primary Phone",address_line:"Address Line",pincode:"Pincode",city:"City",state:"State",locality:"Locality"};return m.b4.map(s=>{let l=e.includes(s);return(0,t.jsxs)("p",{className:`text-xs flex items-center ${l?"text-red-500 dark:text-red-400 font-medium":"text-green-500 dark:text-green-400"}`,children:[r[s],a[s],!l&&(0,t.jsx)(eU.A,{className:"w-3 h-3 ml-1 flex-shrink-0"})]},s)})})()})]})]})]})})]}),(0,t.jsx)(I.C5,{className:"text-xs text-red-500"})]})}),"online"===e.watch("status")&&(0,t.jsx)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,t.jsx)(I.zB,{control:e.control,name:"business_slug",render:({field:r})=>(0,t.jsxs)(I.eI,{className:"space-y-3 sm:space-y-4 mt-2",children:[(0,t.jsxs)(I.lR,{className:"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-primary"}),"Unique Card URL"]}),(0,t.jsx)("div",{className:"p-4 sm:p-5 rounded-xl border border-neutral-200 dark:border-neutral-700 bg-neutral-50/50 dark:bg-neutral-800/30 shadow-sm",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-0 rounded-lg overflow-hidden border-2 border-neutral-200 dark:border-neutral-700 focus-within:border-primary/70 focus-within:ring-2 focus-within:ring-primary/20 transition-all duration-200 shadow-sm",children:[(0,t.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 px-3 sm:px-4 py-3 border-r border-neutral-200 dark:border-neutral-700 text-neutral-600 dark:text-neutral-300 text-sm font-medium flex items-center",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 mr-2 text-neutral-500 dark:text-neutral-400"}),"dukancard.in/"]}),(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(I.MJ,{children:(0,t.jsx)(U.p,{placeholder:"your-unique-name",...r,className:"w-full border-0 bg-white dark:bg-black py-3 px-4 text-base focus:ring-0 shadow-none",onChange:e=>{let a=e.target.value.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,"");r.onChange(a),p(a),u(null)}})}),(0,t.jsxs)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:[b.checking&&(0,t.jsx)("div",{className:"bg-neutral-100 dark:bg-neutral-800 p-1 rounded-full",children:(0,t.jsx)(A.A,{className:"w-5 h-5 text-primary animate-spin"})}),!b.checking&&!0===b.available&&(0,t.jsx)("div",{className:"bg-green-50 dark:bg-green-900/30 p-1 rounded-full",children:(0,t.jsx)(eE.A,{className:"w-5 h-5 text-green-500 dark:text-green-400"})}),!b.checking&&!1===b.available&&(0,t.jsx)("div",{className:"bg-red-50 dark:bg-red-900/30 p-1 rounded-full",children:(0,t.jsx)(eI.A,{className:"w-5 h-5 text-red-500 dark:text-red-400"})})]})]})]}),(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("div",{className:"px-3 py-2 rounded-lg bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-sm font-mono flex items-center",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2 text-neutral-500 dark:text-neutral-400"}),"https://dukancard.in/",r.value||"your-unique-name"]})}),(0,t.jsxs)("div",{className:"flex items-center h-6 px-1",children:[b.checking&&(0,t.jsxs)("p",{className:"text-sm text-neutral-500 flex items-center",children:[(0,t.jsx)(A.A,{className:"w-3.5 h-3.5 mr-2 animate-spin"}),"Checking availability..."]}),!b.checking&&!0===b.available&&!e.formState.errors.business_slug&&(0,t.jsxs)("p",{className:"text-sm text-green-600 dark:text-green-400 flex items-center",children:[(0,t.jsx)(eE.A,{className:"w-3.5 h-3.5 mr-2"}),"URL is available!"]}),!b.checking&&!1===b.available&&(0,t.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,t.jsx)(eP.A,{className:"w-3.5 h-3.5 mr-2"}),"URL is already taken."]}),!b.checking&&null===b.available&&(0,t.jsxs)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400 flex items-center",children:[(0,t.jsx)(R.A,{className:"w-3.5 h-3.5 mr-2"}),"Lowercase letters, numbers, hyphens only. Min 3 chars."]})]})]})}),(0,t.jsx)(I.C5,{className:"text-sm text-red-500"})]})})})]}),(0,t.jsx)("div",{className:"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm",children:(0,t.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,t.jsx)("div",{className:"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm",children:(0,t.jsx)(R.A,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300",children:"Status & URL Tip"}),(0,t.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed",children:"Choose a unique, memorable slug for your card's URL to make it easy to share. Set to 'Online' to make your card publicly accessible."}),(0,t.jsx)("p",{className:"text-xs text-violet-700 dark:text-violet-400 mt-1.5 leading-relaxed",children:"To set your card status to 'Online', you must fill in all required fields: your name, title, business name, primary phone, address line, pincode, city, state, and locality."})]})]})})]})}function eM({form:e,canGoOnline:r,currentUserPlan:a,onFileSelect:s,isPincodeLoading:l,availableLocalities:n,onPincodeChange:i,isLogoUploading:o,onLogoDelete:d,isSubscriptionHalted:c,onSlugCheckingChange:u}){return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)(D,{form:e,onFileSelect:s,isLogoUploading:o,onLogoDelete:d}),(0,t.jsx)($,{form:e,isPincodeLoading:l,availableLocalities:n,onPincodeChange:i}),(0,t.jsx)(G,{form:e,currentUserPlan:a}),(0,t.jsx)(el,{form:e,currentUserPlan:a}),(0,t.jsx)(eh,{form:e,currentUserPlan:a}),(0,t.jsx)(eS,{form:e}),(0,t.jsx)(eR,{form:e}),(0,t.jsx)(eB,{form:e,slugStatus:{checking:!1,available:null,message:null},canGoOnline:r,isSubscriptionHalted:c,onSlugCheckingChange:u})]})}var eF=a(29821),e$=a(58719),ez=a(96241);let eq={hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.5,delay:.1,ease:"easeOut"}}};function eG({cardData:e,logoUploadStatus:r,localPreviewUrl:a,userPlan:s,cardPreviewRef:l}){return(0,t.jsxs)(o.P.div,{variants:eq,initial:"hidden",animate:"visible",style:{width:"100%",display:"flex",flexDirection:"column",alignItems:"center"},ref:l,children:[(0,t.jsx)(eF.A,{data:e,logoUploadStatus:r,localPreviewUrl:a,userPlan:s,isAuthenticated:!0,totalLikes:e.total_likes??0,totalSubscriptions:e.total_subscriptions??0,averageRating:e.average_rating??0}),(0,t.jsx)(e$.A,{businessSlug:e.business_slug||"",businessName:e.business_name||"",ownerName:e.member_name||"",businessAddress:(0,ez.M0)(e),themeColor:e.theme_color||"#F59E0B",className:"mt-6"})]})}var eV=a(65471);function eY({isOpen:e,isDeleting:r,onClose:a,onConfirm:s}){return(0,t.jsx)(ed.lG,{open:e,onOpenChange:e=>!e&&!r&&a(),children:(0,t.jsxs)(ed.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(ed.c7,{children:[(0,t.jsxs)(ed.L3,{className:"flex items-center gap-2 text-red-600 dark:text-red-500",children:[(0,t.jsx)(C.A,{className:"h-5 w-5"}),"Delete Logo"]}),(0,t.jsx)(ed.rr,{children:"Are you sure you want to delete your logo? This action cannot be undone."})]}),(0,t.jsx)("div",{className:"py-4",children:(0,t.jsx)("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:"Your logo will be permanently removed from your business card and from our storage."})}),(0,t.jsxs)(ed.Es,{children:[(0,t.jsx)(c.$,{variant:"outline",onClick:a,disabled:r,className:"border-neutral-200 dark:border-neutral-700",children:"Cancel"}),(0,t.jsx)(c.$,{variant:"destructive",onClick:s,disabled:r,className:"bg-red-600 hover:bg-red-700 text-white",children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete Logo"})]})]})})}function eJ({form:e,isPending:r,isLogoUploading:a,isCheckingSlug:s=!1,isPincodeLoading:l=!1,onSave:n}){return(0,t.jsxs)("div",{className:"mt-8 flex flex-col space-y-4",children:[(0,t.jsx)("div",{className:"h-px w-full bg-neutral-200 dark:bg-neutral-800"}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"text-sm text-neutral-500 dark:text-neutral-400",children:e.formState.isDirty?(0,t.jsxs)("span",{className:"flex items-center text-amber-600 dark:text-amber-400",children:[(0,t.jsxs)("span",{className:"relative flex h-2 w-2 mr-2",children:[(0,t.jsx)("span",{className:"animate-ping absolute inline-flex h-full w-full rounded-full bg-amber-400 opacity-75"}),(0,t.jsx)("span",{className:"relative inline-flex rounded-full h-2 w-2 bg-amber-500"})]}),"You have unsaved changes"]}):(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"relative flex h-2 w-2 mr-2",children:(0,t.jsx)("span",{className:"relative inline-flex rounded-full h-2 w-2 bg-green-500"})}),"All changes saved"]})}),(0,t.jsxs)(c.$,{type:"button",disabled:r||a||s||l||Object.keys(e.formState.errors).length>0,onClick:n,className:"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] disabled:opacity-50 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md",children:[r||a||s||l?(0,t.jsx)(A.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,r?"Saving...":a?"Uploading...":s?"Checking URL...":l?"Loading Location...":"Save Changes"]})]})]})}var eH=a(88920),eZ=a(43649),eW=a(8819);function eK({form:e,isPending:r,isLogoUploading:a,isCheckingSlug:l=!1,isPincodeLoading:n=!1,onSave:i,onDiscard:d}){let[u,m]=(0,s.useState)(!1),[x,p]=(0,s.useState)(!1),h=u&&x&&e.formState.isDirty;return(0,t.jsx)(eH.N,{children:h&&(0,t.jsx)(o.P.div,{initial:{opacity:0,y:-100},animate:{opacity:1,y:0},exit:{opacity:0,y:-100},transition:{duration:.3,ease:"easeOut"},className:"fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[calc(100%-2rem)] max-w-md",children:(0,t.jsx)("div",{className:"bg-white dark:bg-neutral-900 border border-amber-200 dark:border-amber-800 rounded-xl shadow-xl backdrop-blur-sm p-4 ring-1 ring-black/5 dark:ring-white/10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(eZ.A,{className:"h-5 w-5 text-amber-500"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-neutral-900 dark:text-neutral-100",children:"You have unsaved changes"}),(0,t.jsx)("p",{className:"text-xs text-neutral-600 dark:text-neutral-400",children:"Don't forget to save your changes before leaving"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(c.$,{size:"sm",variant:"outline",onClick:d,disabled:r||a||l||n,className:"text-xs px-2 py-1 h-7",children:[(0,t.jsx)(ea.A,{className:"h-3 w-3 mr-1"}),"Discard"]}),(0,t.jsx)(c.$,{size:"sm",onClick:i,disabled:r||a||l||n||Object.keys(e.formState.errors).length>0,className:"text-xs px-3 py-1.5 h-8 bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] hover:from-[var(--brand-gold-light)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] font-medium shadow-sm hover:shadow-md transition-all duration-200 border-0",children:r||a||l||n?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"h-3 w-3 mr-1.5 animate-spin"}),r?"Saving...":a?"Uploading...":l?"Checking...":n?"Loading...":"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eW.A,{className:"h-3 w-3 mr-1.5"}),"Save"]})})]})]})})})})}function eX({initialData:e,currentUserPlan:r,subscriptionStatus:a}){let[x,_]=(0,s.useState)(!1),[v,j]=(0,s.useState)(()=>{let r={...m.kc,...e};return{...r,member_name:r.member_name||"",title:r.title||"",business_name:r.business_name||"",status:r.status||"offline"}}),[w,N]=(0,s.useState)(()=>{let r={...m.kc,...e};return{...r,member_name:r.member_name||"",title:r.title||"",business_name:r.business_name||"",status:r.status||"offline"}}),[k,S]=(0,s.useTransition)(),[C,A]=(0,s.useState)(!1),[R,P]=(0,s.useState)(!1),U=(0,s.useRef)(null),E=(0,s.useMemo)(()=>({...m.kc,...e,locality:e?.locality??"",custom_branding:{...m.kc.custom_branding,...e?.custom_branding||{}},custom_ads:{...m.kc.custom_ads,...e?.custom_ads||{}}}),[e]),L=(0,l.mN)({resolver:(0,n.u)(m.Mo),defaultValues:E,mode:"onChange",resetOptions:{keepDirtyValues:!1,keepErrors:!1}}),T=L.watch(),D="halted"===a,O=!D&&m.b4.every(e=>T[e]&&""!==String(T[e]).trim()),{isPincodeLoading:B,availableLocalities:M,handlePincodeChange:F}=function({form:e,initialPincode:r,initialLocality:a}){let[t,l]=(0,s.useState)(!1),[n,o]=(0,s.useState)([]);return{isPincodeLoading:t,availableLocalities:n,handlePincodeChange:(0,s.useCallback)(async r=>{if(6!==r.length)return;l(!0),o([]),e.setValue("locality",""),e.setValue("city",""),e.setValue("state","");let a=await (0,y.t)(r);l(!1),a.error?i.oR.error(a.error):a.city&&a.state&&a.localities&&(e.setValue("city",a.city,{shouldValidate:!0}),e.setValue("state",a.state,{shouldValidate:!0}),o(a.localities),1===a.localities.length&&e.setValue("locality",a.localities[0],{shouldValidate:!0,shouldDirty:!1}),i.oR.success("City and State auto-filled. Please select your locality."))},[e])}}({form:L,initialPincode:e?.pincode,initialLocality:e?.locality}),{logoUploadStatus:$,localPreviewUrl:z,isLogoUploading:q,imageToCrop:G,onFileSelect:V,handleCropComplete:Y,handleCropDialogClose:J,handleLogoDelete:H,logoErrorDisplay:Z,isDeleteDialogOpen:W,isDeleting:K,closeDeleteDialog:X,confirmLogoDelete:Q}=function({form:e,initialLogoUrl:r="",onUpdateCardData:a}){let[t,l]=(0,s.useState)("idle"),[n,o]=(0,s.useState)(null),[d,c]=(0,s.useState)(null),[u,m]=(0,s.useTransition)(),[x,p]=(0,s.useState)(null),[y,_]=(0,s.useState)(null),v=async t=>{l("uploading"),o(null),m(async()=>{try{let s=await (0,f.compressImageUltraAggressiveClient)(t,{maxDimension:500,targetSizeKB:60}),n=new File([s.blob],t.name,{type:s.blob.type}),u=new FormData;u.append("logoFile",n);let m=await h(u);if(m.success&&m.url){let r=m.url;e.setValue("logo_url",r,{shouldDirty:!0,shouldTouch:!0}),a({logo_url:r}),l("success"),c(null),d&&URL.revokeObjectURL(d),i.oR.success("Logo uploaded successfully!");try{let t=await g(r);t.success||i.oR.error(`Logo uploaded, but failed to save URL: ${t.error}`),t.success&&(a({logo_url:r}),e.reset({...e.getValues(),logo_url:r}))}catch(e){console.error("Error saving logo URL:",e),i.oR.error("Error saving logo URL after upload.")}}else{l("error");let a=m.error||"Failed to upload logo.";o(a),c(null),d&&URL.revokeObjectURL(d),a.includes("File size must be less than 15MB")?i.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}):a.includes("Invalid file type")?i.oR.error("Invalid file type",{description:"Please select a JPG, PNG, WebP, or GIF image"}):i.oR.error("Upload failed",{description:a}),e.setValue("logo_url",r||"",{shouldDirty:!1})}}catch(e){console.error("Image compression failed:",e),l("error"),o("Failed to process image. Please try a different image."),i.oR.error("Image processing failed",{description:"Please try a different image or reduce the file size."})}})},[j,w]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1),S=()=>{e.getValues("logo_url")&&w(!0)},C=async()=>{k(!0),l("uploading"),o(null),m(async()=>{try{let r=await b();if(r.success){e.setValue("logo_url",null,{shouldDirty:!0,shouldTouch:!0}),a({logo_url:null}),l("idle"),c(null),d&&URL.revokeObjectURL(d);let r=document.querySelector('input[type="file"]');r&&(r.value=""),i.oR.success("Logo deleted successfully!"),e.reset({...e.getValues(),logo_url:null})}else l("error"),o(r.error||"Failed to delete logo."),i.oR.error(r.error||"Failed to delete logo.")}catch(e){console.error("Error deleting logo:",e),l("error"),o("An unexpected error occurred while deleting the logo."),i.oR.error("An unexpected error occurred while deleting the logo.")}finally{k(!1),w(!1)}})};return{logoUploadStatus:t,logoUploadError:n,localPreviewUrl:d,isLogoUploading:u,imageToCrop:x,onFileSelect:r=>{if(d&&(URL.revokeObjectURL(d),c(null)),r){if(r.size>0xf00000){i.oR.error("Image too large",{description:"Please select an image smaller than 15MB"}),e.setValue("logo_url",null,{shouldDirty:!0}),a({logo_url:null}),l("idle"),o("File size must be less than 15MB."),c(null);return}_(r);let t=new FileReader;t.onloadend=()=>{p(t.result)},t.readAsDataURL(r)}else e.setValue("logo_url",null,{shouldDirty:!0}),a({logo_url:null}),l("idle"),o(null),c(null)},handleLogoUpload:v,handleCropComplete:e=>{if(p(null),e&&y){let r=new File([e],y.name,{type:"image/webp"});c(URL.createObjectURL(r)),v(r)}else{console.log("Cropping cancelled or failed."),_(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")}},handleCropDialogClose:()=>{p(null),_(null);let e=document.querySelector('input[type="file"]');e&&(e.value="")},handleLogoDelete:S,logoErrorDisplay:"error"===t&&n?n:null,isDeleteDialogOpen:j,isDeleting:N,openDeleteDialog:S,closeDeleteDialog:()=>{N||w(!1)},confirmLogoDelete:C}}({form:L,initialLogoUrl:e?.logo_url||"",onUpdateCardData:e=>j(r=>({...r,...e}))}),ee=(e=!0)=>{let r=L.getValues();return(e?m.b4:m.Uc).filter(e=>!r[e]||""===String(r[e]).trim())},er=e=>({member_name:"Your Name",title:"Your Title",business_name:"Business Name",phone:"Primary Phone",address_line:"Address Line",pincode:"Pincode",city:"City",state:"State",locality:"Locality",contact_email:"Contact Email",business_category:"Business Category"})[e]||e,ea=e=>{if(Object.keys(L.formState.errors).length>0){!function(e="business-card-form"){setTimeout(()=>{let r=document.getElementById(e);if(!r)return;let a=r.querySelectorAll('[role="alert"]');if(!a||0===a.length){let e=r.querySelectorAll('.error-field, [aria-invalid="true"]');return e&&e.length>0?void u(e[0]):void 0}u(a[0])},100)}("business-card-form"),i.oR.error((0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Cannot save business card"}),(0,t.jsx)("p",{className:"text-sm mb-1",children:"Please fix the validation errors"})]}));return}let r=ee(!1);if(r.length>0){let e=r.map(er);i.oR.error((0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Cannot save business card"}),(0,t.jsx)("p",{className:"text-sm mb-1",children:"Please fill in the following required fields:"}),(0,t.jsx)("ul",{className:"text-sm list-disc pl-4",children:e.map((e,r)=>(0,t.jsx)("li",{children:e},r))})]}));let a=r[0];L.setFocus(a);return}if("online"===e.status&&D)return void i.oR.error((0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Cannot set card to online status"}),(0,t.jsx)("p",{className:"text-sm mb-1",children:"Your subscription is currently paused. Please resume your subscription to set your card online."})]}));if("online"===e.status&&!O&&!D){let e=ee(!0),r=e.map(er);i.oR.error((0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Cannot set card to online status"}),(0,t.jsx)("p",{className:"text-sm mb-1",children:"Please fill in the following required fields:"}),(0,t.jsx)("ul",{className:"text-sm list-disc pl-4",children:r.map((e,r)=>(0,t.jsx)("li",{children:e},r))})]}));let a=e[0];L.setFocus(a);return}S(async()=>{let r=await p(e);r.success&&r.data?(i.oR.success("Business card updated successfully!"),j(r.data),N(r.data),A(!0),L.reset(r.data,{keepDirtyValues:!1,keepErrors:!1,keepDirty:!1,keepIsSubmitted:!1}),setTimeout(()=>A(!1),100)):i.oR.error(r.error||"Failed to update business card.")})},et=(0,s.useCallback)(e=>{P(e)},[]),es=async()=>{if(k||R||B)return;if(Object.keys(L.formState.errors).length>0)return void i.oR.error("Please fix the form errors before saving");let e=L.getValues(),r=m.Mo.safeParse(e);if(!r.success){console.error("Validation failed:",r.error),i.oR.error("Please fix the form errors before saving");return}ea(r.data)};return x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eK,{form:L,isPending:k,isLogoUploading:q,isCheckingSlug:R,isPincodeLoading:B,onSave:es,onDiscard:()=>{A(!0),L.reset(w,{keepDirtyValues:!1,keepErrors:!1,keepDirty:!1,keepIsSubmitted:!1}),j(w),setTimeout(()=>A(!1),100),i.oR.info("Changes discarded")}}),(0,t.jsx)(I.lV,{...L,children:(0,t.jsxs)("form",{onSubmit:e=>e.preventDefault(),className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-8 lg:hidden",children:[(0,t.jsx)(eG,{cardData:v,logoUploadStatus:$,localPreviewUrl:z,userPlan:"trial"===r?"basic":r??void 0,cardPreviewRef:U}),(0,t.jsxs)(o.P.div,{initial:"hidden",animate:"visible",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6",children:[(0,t.jsx)("div",{className:"p-3 rounded-xl bg-muted hidden sm:block",children:(0,t.jsx)(d.A,{className:"w-6 h-6 text-foreground"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Edit Business Card"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:"Customize your digital business card below. Changes reflect in real-time."})]}),(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e=L.getValues("business_slug");e?window.open(`/${e}`,"_blank"):i.oR.error("Please set a business slug first.")},disabled:!L.getValues("business_slug"),className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),"View Public Card"]})]}),(0,t.jsx)(eM,{form:L,canGoOnline:O,currentUserPlan:r,onFileSelect:V,isPincodeLoading:B,availableLocalities:M,onPincodeChange:F,isLogoUploading:q,onLogoDelete:H,isSubscriptionHalted:D,onSlugCheckingChange:et}),(0,t.jsxs)("div",{className:"flex flex-col gap-2 sm:gap-3 mt-6",children:[Z&&(0,t.jsx)("p",{className:"text-xs text-red-500 dark:text-red-400 text-right",children:Z}),(0,t.jsx)(eJ,{form:L,isPending:k,isLogoUploading:q,isCheckingSlug:R,isPincodeLoading:B,onSave:es})]})]})]}),(0,t.jsxs)("div",{className:"hidden lg:block space-y-8",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6",children:[(0,t.jsx)("div",{className:"p-3 rounded-xl bg-muted",children:(0,t.jsx)(d.A,{className:"w-6 h-6 text-foreground"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Edit Business Card"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:"Customize your digital business card below. Changes reflect in real-time."})]}),(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e=L.getValues("business_slug");e?window.open(`/${e}`,"_blank"):i.oR.error("Please set a business slug first.")},disabled:!L.getValues("business_slug"),className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),"View Public Card"]})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8 sm:gap-12 pb-12 relative",children:[(0,t.jsx)("div",{className:"flex-[1] w-full lg:w-1/2 lg:sticky lg:top-24 self-start",children:(0,t.jsx)(eG,{cardData:v,logoUploadStatus:$,localPreviewUrl:z,userPlan:"trial"===r?"basic":r??void 0,cardPreviewRef:U})}),(0,t.jsxs)(o.P.div,{initial:"hidden",animate:"visible",className:"flex-[2] space-y-6",style:{flex:2,width:"100%",position:"sticky",top:"6rem",alignSelf:"flex-start"},children:[(0,t.jsx)(eM,{form:L,canGoOnline:O,currentUserPlan:r,onFileSelect:V,isPincodeLoading:B,availableLocalities:M,onPincodeChange:F,isLogoUploading:q,onLogoDelete:H,isSubscriptionHalted:D,onSlugCheckingChange:et}),(0,t.jsxs)("div",{className:"flex flex-col gap-2 sm:gap-3 mt-6",children:[Z&&(0,t.jsx)("p",{className:"text-xs text-red-500 dark:text-red-400 text-right",children:Z}),(0,t.jsx)(eJ,{form:L,isPending:k,isLogoUploading:q,isCheckingSlug:R,isPincodeLoading:B,onSave:es})]})]})]})]})]})}),(0,t.jsx)(eV.A,{isOpen:!!G,imgSrc:G,onClose:J,onCropComplete:Y}),(0,t.jsx)(eY,{isOpen:W,isDeleting:K,onClose:X,onConfirm:Q})]}):(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--brand-gold)] mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading editor..."})]})})}},78909:(e,r,a)=>{"use strict";a.d(r,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(dashboard)\\\\dashboard\\\\business\\\\card\\\\CardEditorClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79988:(e,r,a)=>{Promise.resolve().then(a.bind(a,61192))},81630:e=>{"use strict";e.exports=require("http")},85146:(e,r,a)=>{Promise.resolve().then(a.bind(a,77676))},90061:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=a(65239),s=a(48088),l=a(88170),n=a.n(l),i=a(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);a.d(r,o);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["business",{children:["card",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,25941)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,45488)),"C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/business/card/page",pathname:"/dashboard/business/card",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},93437:(e,r,a)=>{"use strict";a.d(r,{S:()=>i});var t=a(60687);a(43210);var s=a(40211),l=a(13964),n=a(96241);function i({className:e,...r}){return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(l.A,{className:"size-3.5"})})})}},94735:e=>{"use strict";e.exports=require("events")},95685:(e,r,a)=>{"use strict";let t,s;a.d(r,{Ft:()=>x,dg:()=>m,Sg:()=>u,vn:()=>p,Ak:()=>c});var l=a(30468),n=a(55511);let i=e=>{!t||t.length<e?(t=Buffer.allocUnsafe(128*e),n.randomFillSync(t),s=0):s+e>t.length&&(n.randomFillSync(t),s=0),s+=e},o=e=>(i(e|=0),t.subarray(s-e,s)),d=(e,r,a)=>{let t=(2<<31-Math.clz32(e.length-1|1))-1,s=Math.ceil(1.6*t*r/e.length);return (l=r)=>{let n="";for(;;){let r=a(s),i=s;for(;i--;)if((n+=e[r[i]&t]||"").length===l)return n}}},c=((e,r=21)=>d(e,r,o))("1234567890abcdef",6),u=5,m=15,x=["image/png","image/jpeg","image/gif","image/webp"],p=l.SC.BUSINESS},98344:(e,r,a)=>{"use strict";function t(e){if(!e||"string"!=typeof e)throw Error(`Invalid userId: expected string, got ${typeof e}. Value: ${e}`);if(e.length<4)throw Error(`Invalid userId: must be at least 4 characters long. Got: ${e}`);let r=e.substring(0,2).toLowerCase(),a=e.substring(2,4).toLowerCase();return`users/${r}/${a}/${e}`}function s(e,r){let a=t(e);return`${a}/profile/logo_${r}.webp`}function l(e,r,a,s){let l=t(e);return`${l}/products/${r}/base/image_${a}_${s}.webp`}function n(e,r,a,s,l){let n=t(e);return`${n}/products/${r}/${a}/image_${s}_${l}.webp`}function i(e,r){let a=t(e);return`${a}/gallery/gallery_${r}.webp`}function o(e,r,a){let s=t(e);return`${s}/branding/header_${a}_${r}.webp`}a.d(r,{$W:()=>i,Vl:()=>n,Wl:()=>s,YV:()=>t,jA:()=>l,rN:()=>o})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[4447,9398,4386,6724,2997,1107,7065,3206,9190,5129,1753,6380,5880,4851,3442,2836,2186,3064,7342,1580,2978,3037,3739,9538,5265,4,4812,6531],()=>a(90061));module.exports=t})();