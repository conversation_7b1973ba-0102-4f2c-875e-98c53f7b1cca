(()=>{var e={};e.id=7944,e.ids=[7944],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3589:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},4675:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["(main)",{children:["auth",{children:["callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,10283)),"C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19559)),"C:\\web-app\\dukancard\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,u=["C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(main)/auth/callback/page",pathname:"/auth/callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6943:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10283:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,dynamicParams:()=>a,generateMetadata:()=>o});var s=r(37413),n=r(61120),i=r(38732);let a=!0;async function o(){return{title:"Authenticating",robots:"noindex, nofollow"}}function l(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(i.default,{})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(37413);r(61120);var n=r(14890),i=r(60644),a=r(11637),o=r(95006),l=r(92506),d=r(46501),u=r(21886),c=r(23392);function p({children:e}){return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(c.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,s.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,s.jsx)(n.default,{}),(0,s.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,s.jsx)(i.default,{}),(0,s.jsx)(o.default,{}),(0,s.jsx)(a.default,{}),(0,s.jsx)(l.default,{}),(0,s.jsx)(d.default,{}),(0,s.jsx)(u.default,{excludePaths:["/dashboard"]})]})})})}},20798:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38732:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\auth\\\\callback\\\\AuthCallbackWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx","default")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51151:(e,t,r)=>{Promise.resolve().then(r.bind(r,38732))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59399:(e,t,r)=>{Promise.resolve().then(r.bind(r,63831))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63831:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(60687),n=r(43210),i=r(16189),a=r(38398),o=r(41862),l=r(37826),d=r(24934);function u(){let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),u=(0,a.U)(),[c,p]=(0,n.useState)(!1),[h,f]=(0,n.useState)(""),[x,m]=(0,n.useState)(""),b="true"===t.get("closeWindow");return(0,s.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(o.A,{className:"mx-auto h-8 w-8 text-[var(--brand-gold)] animate-spin"}),(0,s.jsx)("p",{className:"mt-4 text-lg font-medium",children:"Authenticating..."}),b&&(0,s.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"This window will close automatically after sign-in is complete."})]}),(0,s.jsx)(l.lG,{open:c,onOpenChange:p,children:(0,s.jsxs)(l.Cf,{className:"sm:max-w-[425px]",onEscapeKeyDown:e=>e.preventDefault(),onPointerDownOutside:e=>e.preventDefault(),children:[(0,s.jsxs)(l.c7,{children:[(0,s.jsx)(l.L3,{children:h}),(0,s.jsx)(l.rr,{children:x})]}),(0,s.jsx)(l.Es,{children:(0,s.jsx)(l.HM,{asChild:!0,children:(0,s.jsx)(d.$,{type:"button",onClick:async()=>{p(!1);let{data:t,error:s}=await u.auth.getUser();if(s||!t?.user)e.push("/login");else{let s=t.user.id;{let{getPostLoginRedirectPath:t}=await r.e(9317).then(r.bind(r,69317)),n=await t(u,s);e.push(n)}}},children:"Continue to Settings"})})})]})})]})}function c(){return(0,s.jsx)(u,{})}},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88920:(e,t,r)=>{"use strict";r.d(t,{N:()=>b});var s=r(60687),n=r(43210),i=r(12157),a=r(72789),o=r(15124),l=r(21279),d=r(32582);class u extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=e instanceof HTMLElement&&e.offsetWidth||0,s=this.props.sizeRef.current;s.height=t.offsetHeight||0,s.width=t.offsetWidth||0,s.top=t.offsetTop,s.left=t.offsetLeft,s.right=r-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c({children:e,isPresent:t,anchorX:r}){let i=(0,n.useId)(),a=(0,n.useRef)(null),o=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,n.useContext)(d.Q);return(0,n.useInsertionEffect)(()=>{let{width:e,height:s,top:n,left:d,right:u}=o.current;if(t||!a.current||!e||!s)return;let c="left"===r?`left: ${d}`:`right: ${u}`;a.current.dataset.motionPopId=i;let p=document.createElement("style");return l&&(p.nonce=l),document.head.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${s}px !important;
            ${c}px !important;
            top: ${n}px !important;
          }
        `),()=>{document.head.removeChild(p)}},[t]),(0,s.jsx)(u,{isPresent:t,childRef:a,sizeRef:o,children:n.cloneElement(e,{ref:a})})}let p=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:o,presenceAffectsLayout:d,mode:u,anchorX:p})=>{let f=(0,a.M)(h),x=(0,n.useId)(),m=!0,b=(0,n.useMemo)(()=>(m=!1,{id:x,initial:t,isPresent:r,custom:o,onExitComplete:e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;i&&i()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[r,f,i]);return d&&m&&(b={...b}),(0,n.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[r]),n.useEffect(()=>{r||f.size||!i||i()},[r]),"popLayout"===u&&(e=(0,s.jsx)(c,{isPresent:r,anchorX:p,children:e})),(0,s.jsx)(l.t.Provider,{value:b,children:e})};function h(){return new Map}var f=r(86044);let x=e=>e.key||"";function m(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}let b=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:d=!0,mode:u="sync",propagate:c=!1,anchorX:h="left"})=>{let[b,v]=(0,f.xQ)(c),y=(0,n.useMemo)(()=>m(e),[e]),g=c&&!b?[]:y.map(x),k=(0,n.useRef)(!0),j=(0,n.useRef)(y),w=(0,a.M)(()=>new Map),[P,C]=(0,n.useState)(y),[A,M]=(0,n.useState)(y);(0,o.E)(()=>{k.current=!1,j.current=y;for(let e=0;e<A.length;e++){let t=x(A[e]);g.includes(t)?w.delete(t):!0!==w.get(t)&&w.set(t,!1)}},[A,g.length,g.join("-")]);let q=[];if(y!==P){let e=[...y];for(let t=0;t<A.length;t++){let r=A[t],s=x(r);g.includes(s)||(e.splice(t,0,r),q.push(r))}return"wait"===u&&q.length&&(e=q),M(m(e)),C(y),null}let{forceRender:E}=(0,n.useContext)(i.L);return(0,s.jsx)(s.Fragment,{children:A.map(e=>{let n=x(e),i=(!c||!!b)&&(y===A||g.includes(n));return(0,s.jsx)(p,{isPresent:i,initial:(!k.current||!!r)&&void 0,custom:t,presenceAffectsLayout:d,mode:u,onExitComplete:i?void 0:()=>{if(!w.has(n))return;w.set(n,!0);let e=!0;w.forEach(t=>{t||(e=!1)}),e&&(E?.(),M(j.current),c&&v?.(),l&&l())},anchorX:h,children:e},n)})})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,6724,2997,1107,7065,3037,6177],()=>r(4675));module.exports=s})();