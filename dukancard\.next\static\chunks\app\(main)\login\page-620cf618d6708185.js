(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3193,4049],{30070:(e,r,t)=>{"use strict";t.d(r,{C5:()=>h,MJ:()=>f,Rr:()=>p,eI:()=>b,lR:()=>x,lV:()=>l,zB:()=>u});var a=t(95155),s=t(12115),n=t(99708),o=t(62177),i=t(53999),d=t(82714);let l=o.Op,c=s.createContext({}),u=e=>{let{...r}=e;return(0,a.jsx)(c.Provider,{value:{name:r.name},children:(0,a.jsx)(o.xI,{...r})})},m=()=>{let e=s.useContext(c),r=s.useContext(g),{getFieldState:t}=(0,o.xW)(),a=(0,o.lN)({name:e.name}),n=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},g=s.createContext({});function b(e){let{className:r,...t}=e,n=s.useId();return(0,a.jsx)(g.Provider,{value:{id:n},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",r),...t})})}function x(e){let{className:r,...t}=e,{error:s,formItemId:n}=m();return(0,a.jsx)(d.J,{"data-slot":"form-label","data-error":!!s,className:(0,i.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...t})}function f(e){let{...r}=e,{error:t,formItemId:s,formDescriptionId:o,formMessageId:i}=m();return(0,a.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":t?"".concat(o," ").concat(i):"".concat(o),"aria-invalid":!!t,...r})}function p(e){let{className:r,...t}=e,{formDescriptionId:s}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,i.cn)("text-muted-foreground text-sm",r),...t})}function h(e){var r;let{className:t,...s}=e,{error:n,formMessageId:o}=m(),d=n?String(null!=(r=null==n?void 0:n.message)?r:""):s.children;return d?(0,a.jsx)("p",{"data-slot":"form-message",id:o,className:(0,i.cn)("text-destructive text-sm",t),...s,children:d}):null}},48347:(e,r,t)=>{"use strict";t.d(r,{LE:()=>s,oX:()=>n});var a=t(55594);let s=a.z.string().trim().min(10,{message:"Mobile number must be 10 digits"}).max(10,{message:"Mobile number must be 10 digits"}).regex(/^[6-9]\d{9}$/,{message:"Please enter a valid Indian mobile number"});a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),a.z.object({email:a.z.string().trim().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"}),otp:a.z.string().trim().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})}),a.z.string().min(6,"Password must be at least 6 characters long").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character");let n=a.z.object({mobile:s,password:a.z.string().trim().min(1,{message:"Password is required"})})},53716:(e,r,t)=>{Promise.resolve().then(t.bind(t,85224)),Promise.resolve().then(t.bind(t,56342))},53999:(e,r,t)=>{"use strict";t.d(r,{M0:()=>c,Yq:()=>u,cn:()=>n,gV:()=>o,gY:()=>l,kY:()=>i,vA:()=>d,vv:()=>m});var a=t(52596),s=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}function o(e){if(!e)return null;let r=e.trim();return(r.startsWith("+91")?r=r.substring(3):12===r.length&&r.startsWith("91")&&(r=r.substring(2)),/^\d{10}$/.test(r))?r:null}function i(e){if(!e||e.length<4)return"Invalid Phone";let r=e.substring(0,2),t=e.substring(e.length-2),a="*".repeat(e.length-4);return"".concat(r).concat(a).concat(t)}function d(e){if(!e||!e.includes("@"))return"Invalid Email";let r=e.split("@"),t=r[0],a=r[1];if(t.length<=2||a.length<=2||!a.includes("."))return"Email Hidden";let s=t.substring(0,2)+"*".repeat(t.length-2),n=a.split("."),o=n[0],i=n.slice(1).join("."),d=o.substring(0,2)+"*".repeat(o.length-2);return"".concat(s,"@").concat(d,".").concat(i)}function l(e){if(null==e||isNaN(e))return"0";let r=Math.abs(e),t=[{value:1e5,symbol:"L"},{value:1e7,symbol:"Cr"},{value:1e9,symbol:"Ar"},{value:1e11,symbol:"Khar"},{value:1e13,symbol:"Neel"},{value:1e15,symbol:"Padma"},{value:1e17,symbol:"Shankh"}];if(r<1e5)return r>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+"K":e.toString();for(let a=t.length-1;a>=0;a--)if(r>=t[a].value)return(e/t[a].value).toFixed(1).replace(/\.0$/,"")+t[a].symbol;return e.toString()}function c(e){return[e.address_line,e.locality,e.city,e.state,e.pincode].filter(Boolean).join(", ")||"Address not available"}function u(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!(e instanceof Date)||isNaN(e.getTime()))return"Invalid date";let t={year:"numeric",month:"long",day:"numeric",timeZone:"Asia/Kolkata"};return r&&(t.hour="2-digit",t.minute="2-digit",t.hour12=!0),e.toLocaleString("en-IN",t)}function m(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR";if(null==e||isNaN(e))return"Invalid amount";try{return new Intl.NumberFormat("en-IN",{style:"currency",currency:r,minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}catch(t){return"".concat(r," ").concat(e.toFixed(2))}}},56342:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>E});var a=t(95155),s=t(35695),n=t(12115),o=t(28695),i=t(88482),d=t(56671),l=t(34477);let c=(0,l.createServerReference)("40efc1a848a2990bc559f61f9b7a0d0ed0084879a9",l.callServer,void 0,l.findSourceMapURL,"sendOTP"),u=(0,l.createServerReference)("40e65692ff5facc6e825e8935d9f98c2d059cf07bf",l.callServer,void 0,l.findSourceMapURL,"verifyOTP"),m=(0,l.createServerReference)("402d5a5d450959a841489083e44bf78e39b9046f6a",l.callServer,void 0,l.findSourceMapURL,"loginWithMobilePassword");var g=t(90221),b=t(62177),x=t(55594),f=t(97168),p=t(30070),h=t(89852),v=t(69916),j=t(51154),w=t(92138);let y=x.z.object({email:x.z.string().min(1,{message:"Email is required"}).email({message:"Please enter a valid email address"})}),N=x.z.object({otp:x.z.string().min(6,{message:"OTP must be 6 digits"}).max(6,{message:"OTP must be 6 digits"}).regex(/^\d{6}$/,{message:"OTP must be 6 digits"})});function k(e){let{step:r,email:t,countdown:s,isPending:n,onEmailSubmit:o,onOTPSubmit:i,onResendOTP:d,onBackToEmail:l}=e,c=(0,b.mN)({resolver:(0,g.u)(y),defaultValues:{email:""}}),u=(0,b.mN)({resolver:(0,g.u)(N),defaultValues:{otp:""}});return"email"===r?(0,a.jsx)(p.lV,{...c,children:(0,a.jsxs)("form",{onSubmit:c.handleSubmit(o),className:"space-y-4 sm:space-y-6",children:[(0,a.jsx)(p.zB,{control:c.control,name:"email",render:e=>{let{field:r}=e;return(0,a.jsxs)(p.eI,{children:[(0,a.jsx)(p.lR,{className:"text-foreground text-sm sm:text-base",children:"Email Address"}),(0,a.jsx)(p.MJ,{children:(0,a.jsx)(h.p,{id:"email-login-field",placeholder:"<EMAIL>",type:"email",...r,className:"bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"})}),(0,a.jsx)(p.C5,{})]})}}),(0,a.jsx)(f.$,{type:"submit",className:"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",disabled:n,children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Sending OTP..."]}):(0,a.jsxs)(a.Fragment,{children:["Continue ",(0,a.jsx)(w.A,{className:"w-5 h-5 ml-2"})]})}),(0,a.jsx)("div",{className:"text-center mt-4 text-xs sm:text-sm",children:(0,a.jsx)("span",{className:"text-muted-foreground",children:"New to Dukancard? No worries! We'll create your account automatically."})})]})}):(0,a.jsx)(p.lV,{...u,children:(0,a.jsxs)("form",{onSubmit:u.handleSubmit(e=>{i({email:t,otp:e.otp})}),className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{className:"text-center mb-4",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"We've sent a 6-digit code to"}),(0,a.jsx)("p",{className:"text-sm font-medium text-foreground",children:t}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:"Code expires in 24 hours"})]}),(0,a.jsx)(p.zB,{control:u.control,name:"otp",render:e=>{let{field:r}=e;return(0,a.jsxs)(p.eI,{children:[(0,a.jsx)(p.lR,{className:"text-foreground text-sm sm:text-base text-center block",children:"Enter Verification Code"}),(0,a.jsx)(p.MJ,{children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(v.UV,{id:"otp-login-field",maxLength:6,value:r.value,onChange:e=>r.onChange(e),className:"gap-2",children:(0,a.jsxs)(v.NV,{children:[(0,a.jsx)(v.sF,{index:0,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,a.jsx)(v.sF,{index:1,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,a.jsx)(v.sF,{index:2,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,a.jsx)(v.sF,{index:3,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,a.jsx)(v.sF,{index:4,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"}),(0,a.jsx)(v.sF,{index:5,className:"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"})]})})})}),(0,a.jsx)(p.C5,{})]})}}),(0,a.jsx)(f.$,{type:"submit",className:"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",disabled:n,children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Verifying..."]}):(0,a.jsxs)(a.Fragment,{children:["Verify & Sign In ",(0,a.jsx)(w.A,{className:"w-5 h-5 ml-2"})]})}),(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-3 mt-4 text-xs sm:text-sm",children:[(0,a.jsx)("button",{type:"button",onClick:d,disabled:s>0,className:"".concat(s>0?"text-muted-foreground cursor-not-allowed":"text-primary dark:text-[var(--brand-gold)] hover:underline cursor-pointer"),children:s>0?"Resend OTP in ".concat(s,"s"):"Resend OTP"}),(0,a.jsx)("button",{type:"button",onClick:l,className:"text-muted-foreground hover:text-foreground cursor-pointer",children:"← Change email address"})]})]})})}var P=t(48347),C=t(6874),R=t.n(C);function S(e){let{isPending:r,onSubmit:t}=e,s=(0,b.mN)({resolver:(0,g.u)(P.oX),defaultValues:{mobile:"",password:""}});return(0,a.jsx)(p.lV,{...s,children:(0,a.jsxs)("form",{onSubmit:s.handleSubmit(t),className:"space-y-4 sm:space-y-6",children:[(0,a.jsx)(p.zB,{control:s.control,name:"mobile",render:e=>{let{field:r}=e;return(0,a.jsxs)(p.eI,{children:[(0,a.jsx)(p.lR,{className:"text-foreground text-sm sm:text-base",children:"Mobile Number"}),(0,a.jsx)(p.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground",children:"+91"}),(0,a.jsx)(h.p,{placeholder:"9876543210",type:"tel",...r,onChange:e=>{let t=e.target.value;(t=(t=t.replace(/^\+91/,"")).replace(/\D/g,"")).length>10&&(t=t.slice(0,10)),r.onChange(t)},onKeyDown:e=>{let r=/^[0-9]$/.test(e.key),t=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(e.key);r||t||e.preventDefault()},className:"pl-12 bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base",maxLength:10})]})}),(0,a.jsx)(p.C5,{})]})}}),(0,a.jsx)(p.zB,{control:s.control,name:"password",render:e=>{let{field:r}=e;return(0,a.jsxs)(p.eI,{children:[(0,a.jsx)(p.lR,{className:"text-foreground text-sm sm:text-base",children:"Password"}),(0,a.jsx)(p.MJ,{children:(0,a.jsx)(h.p,{placeholder:"••••••••",type:"password",...r,className:"bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"})}),(0,a.jsx)(p.C5,{})]})}}),(0,a.jsx)(f.$,{type:"submit",className:"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",disabled:r,children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Signing in..."]}):(0,a.jsxs)(a.Fragment,{children:["Sign In ",(0,a.jsx)(w.A,{className:"w-5 h-5 ml-2"})]})}),(0,a.jsx)("div",{className:"text-center mt-4 text-xs sm:text-sm",children:(0,a.jsxs)("span",{className:"text-muted-foreground",children:["Don't have an account?"," ",(0,a.jsx)(R(),{href:"/register",className:"text-primary dark:text-[var(--brand-gold)] hover:underline font-medium",children:"Register here"})]})})]})})}function I(e){let{authMethod:r,step:t,onMethodChange:s}=e;return("email-otp"!==r||"email"!==t)&&"mobile-password"!==r?null:(0,a.jsxs)("div",{className:"flex rounded-lg bg-muted p-1 mb-6",children:[(0,a.jsx)("button",{type:"button",onClick:()=>s("email-otp"),className:"flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ".concat("email-otp"===r?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"),children:"Email OTP"}),(0,a.jsx)("button",{type:"button",onClick:()=>s("mobile-password"),className:"flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ".concat("mobile-password"===r?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"),children:"Mobile + Password"})]})}var O=t(75168);function T(e){let{redirectSlug:r,message:t,disabled:s}=e,[o,i]=(0,n.useState)(null);async function l(e){try{i(e);let a=(0,O.U)(),s="".concat(window.location.origin,"/auth/callback?closeWindow=true");r&&(s+="&redirect=".concat(encodeURIComponent(r))),t&&(s+="&message=".concat(encodeURIComponent(t)));let{data:n,error:o}=await a.auth.signInWithOAuth({provider:e,options:{redirectTo:s,skipBrowserRedirect:!0,queryParams:{access_type:"offline",prompt:"select_account"}}});if(o){d.oR.error("Login failed",{description:o.message}),i(null);return}(null==n?void 0:n.url)?(window.open(n.url,"_blank"),d.oR.info("Google sign-in opened in a new tab",{description:"Please complete the sign-in process in the new tab.",duration:5e3}),setTimeout(()=>{i(null)},1e3)):(d.oR.error("Failed to start Google sign-in",{description:"Please try again or use email login."}),i(null))}catch(r){let e=r instanceof Error?r.message:"An unexpected error occurred. Please try again.";d.oR.error("Login failed",{description:e}),i(null)}}return(0,a.jsx)("div",{className:"flex justify-center mb-5 sm:mb-6",children:(0,a.jsx)(f.$,{variant:"outline",className:"cursor-pointer bg-background hover:bg-muted border-border dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-800 w-full py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-foreground",onClick:()=>l("google"),disabled:!!o||s,children:"google"===o?(0,a.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"w-4 h-4 mr-2",children:[(0,a.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,a.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,a.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,a.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Login with Google"]})})})}var A=t(93193);function E(){let e=(0,s.useRouter)(),r=(0,s.useSearchParams)(),[t,l]=(0,n.useTransition)(),[g,b]=(0,n.useState)(null),[x,f]=(0,n.useState)(null),[p,h]=(0,n.useState)("email-otp"),[v,j]=(0,n.useState)("email"),[w,y]=(0,n.useState)(""),[N,P]=(0,n.useState)(0);async function C(){try{let r=(0,O.U)(),{data:{user:t}}=await r.auth.getUser();if(!t){console.error("No user found after login"),e.push("/?view=home");return}let a=await (0,A.getPostLoginRedirectPath)(r,t.id);if(g){if(g.includes("://")||g.startsWith("//")){console.warn("Attempted redirect to an external or malformed URL. Redirecting to default path."),e.push(a);return}e.push("/".concat(g).concat(x?"?message=".concat(encodeURIComponent(x)):""))}else e.push(a)}catch(r){console.error("Error determining redirect path:",r),e.push("/?view=home")}}return(0,n.useEffect)(()=>{let e=r.get("redirect");e&&b(e);let t=r.get("message");t&&f(t)},[r]),(0,n.useEffect)(()=>{if(N>0){let e=setTimeout(()=>P(N-1),1e3);return()=>clearTimeout(e)}},[N]),(0,a.jsx)("div",{className:"w-full max-w-[90%] sm:max-w-md md:max-w-lg",children:(0,a.jsx)(o.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.2},children:(0,a.jsxs)(i.Zp,{className:"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg dark:shadow-[var(--brand-gold)]/10",children:[(0,a.jsxs)("div",{className:"text-center mb-6 sm:mb-8",children:[(0,a.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-foreground mb-1 sm:mb-2",children:"email-otp"===p&&"otp"===v?"Enter Verification Code":"Welcome to Dukancard"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-muted-foreground",children:"email-otp"===p&&"otp"===v?"Check your email for the 6-digit code":"email-otp"===p?"Sign in or create your account with email":"Sign in with your mobile number and password"}),x&&(0,a.jsx)("div",{className:"mt-4 p-2 sm:p-3 rounded-lg ".concat(x.toLowerCase().includes("error")||x.toLowerCase().includes("failed")?"bg-destructive/10 text-destructive":"bg-green-500/10 text-green-600 dark:text-green-400"),children:(0,a.jsx)("p",{className:"text-xs sm:text-sm",children:x})})]}),(0,a.jsx)(I,{authMethod:p,step:v,onMethodChange:function(e){e!==p&&(h(e),j("email"))}}),(0,a.jsx)(T,{redirectSlug:g,message:x,disabled:t}),(0,a.jsxs)("div",{className:"relative mb-5 sm:mb-6",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-border"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,a.jsx)("span",{className:"bg-card px-2 text-muted-foreground",children:"email-otp"===p&&"email"===v?"Or continue with email":"mobile-password"===p?"Or continue with mobile":"Or use Google instead"})})]}),"email-otp"===p?(0,a.jsx)(k,{step:v,email:w,countdown:N,isPending:t,onEmailSubmit:function(e){l(async()=>{try{let r=await c(e);if(!r.success){if("isConfigurationError"in r&&r.isConfigurationError)return void d.oR.error("Configuration Error",{description:r.error,duration:1e4});d.oR.error("Failed to send OTP",{description:r.error});return}d.oR.success("OTP sent!",{description:r.message}),y(e.email),j("otp"),P(60)}catch(e){d.oR.error("Failed to send OTP",{description:"An unexpected error occurred. Please try again."})}})},onOTPSubmit:function(e){l(async()=>{try{let r=await u({email:e.email,otp:e.otp});if(!r.success)return void d.oR.error("OTP verification failed",{description:r.error});d.oR.success("Sign in successful!",{description:"Redirecting to your dashboard..."}),await C()}catch(e){d.oR.error("OTP verification failed",{description:"An unexpected error occurred. Please try again."})}})},onResendOTP:function(){N>0||l(async()=>{try{let e=await c({email:w});if(!e.success){if("isConfigurationError"in e&&e.isConfigurationError)return void d.oR.error("Configuration Error",{description:e.error,duration:1e4});d.oR.error("Failed to resend OTP",{description:e.error});return}d.oR.success("OTP resent!",{description:e.message}),P(60)}catch(e){d.oR.error("Failed to resend OTP",{description:"An unexpected error occurred. Please try again."})}})},onBackToEmail:function(){j("email"),y("")}}):(0,a.jsx)(S,{isPending:t,onSubmit:function(e){l(async()=>{try{let r=await m(e);if(!r.success)return void d.oR.error("Login failed",{description:r.error});d.oR.success("Sign in successful!",{description:"Redirecting to your dashboard..."}),await C()}catch(e){d.oR.error("Login failed",{description:"An unexpected error occurred. Please try again."})}})}})]})})})}},69916:(e,r,t)=>{"use strict";t.d(r,{NV:()=>d,UV:()=>i,sF:()=>l});var a=t(95155),s=t(12115),n=t(1184),o=t(53999);function i(e){let{className:r,containerClassName:t,...s}=e;return(0,a.jsx)(n.wE,{"data-slot":"input-otp",containerClassName:(0,o.cn)("flex items-center gap-2 has-disabled:opacity-50",t),className:(0,o.cn)("disabled:cursor-not-allowed",r),...s})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"input-otp-group",className:(0,o.cn)("flex items-center",r),...t})}function l(e){var r;let{index:t,className:i,...d}=e,l=s.useContext(n.dK),{char:c,hasFakeCaret:u,isActive:m}=null!=(r=null==l?void 0:l.slots[t])?r:{};return(0,a.jsxs)("div",{"data-slot":"input-otp-slot","data-active":m,className:(0,o.cn)("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]",i),...d,children:[c,u&&(0,a.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-caret-blink bg-foreground h-4 w-px duration-1000"})})]})}},75168:(e,r,t)=>{"use strict";t.d(r,{U:()=>s});var a=t(20067);function s(){let e="https://rnjolcoecogzgglnblqn.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o";return e&&r?(0,a.createBrowserClient)(e,r):(console.error("Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined."),(0,a.createBrowserClient)("",""))}},82714:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var a=t(95155);t(12115);var s=t(40968),n=t(53999);function o(e){let{className:r,...t}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},85224:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var a=t(95155),s=t(12115),n=t(46896),o=t(28695);function i(){let[e,r]=(0,s.useState)(!1),[t,i]=(0,s.useState)(!1),d=(0,n.s)(),l=(0,n.s)(),c=(0,n.s)(),u=(0,n.s)(),m=(0,n.s)(),g=(0,n.s)();return(0,s.useEffect)(()=>{r(!0),i(window.innerWidth<768);let e=()=>{i(window.innerWidth<768)};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,s.useEffect)(()=>{e&&(d.start({scale:1.1,transition:{duration:8,repeat:1/0,repeatType:"reverse",ease:"easeInOut"}}),l.start({scale:1.2,x:20,transition:{duration:10,repeat:1/0,repeatType:"reverse",ease:"easeInOut"}}),c.start({scale:1.15,x:-15,transition:{duration:12,repeat:1/0,repeatType:"reverse",ease:"easeInOut"}}),u.start({scale:1.25,y:25,transition:{duration:14,repeat:1/0,repeatType:"reverse",ease:"easeInOut"}}),m.start({scale:1.1,transition:{duration:6,repeat:1/0,repeatType:"reverse",ease:"easeInOut"}}),g.start({scale:1.3,transition:{duration:4,repeat:1/0,repeatType:"reverse",ease:"easeInOut"}}))},[e,d,l,c,u,m,g]),(0,a.jsx)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.P.div,{animate:d,className:"absolute inset-0 opacity-40 dark:opacity-30",style:{background:"radial-gradient(circle at 50% 50%,\n                var(--brand-gold) 0%,\n                rgba(var(--brand-gold-rgb), 0.3) 25%,\n                rgba(var(--brand-gold-rgb), 0.1) 50%,\n                rgba(0, 0, 255, 0.1) 75%,\n                rgba(0, 0, 255, 0.05) 100%)",filter:t?"blur(60px)":"blur(80px)"}}),(0,a.jsx)(o.P.div,{animate:l,className:"absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold-rgb)]/5 blur-3xl dark:bg-[var(--brand-gold-rgb)]/10 opacity-70"}),(0,a.jsx)(o.P.div,{animate:c,className:"absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10 opacity-70"}),(0,a.jsx)(o.P.div,{animate:u,className:"absolute bottom-0 left-1/4 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10 opacity-60"}),(0,a.jsxs)("svg",{className:"absolute inset-0 w-full h-full opacity-10 dark:opacity-20 pointer-events-none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("filter",{id:"glow",x:"-50%",y:"-50%",width:"200%",height:"200%",children:[(0,a.jsx)("feGaussianBlur",{stdDeviation:"2",result:"blur"}),(0,a.jsx)("feComposite",{in:"SourceGraphic",in2:"blur",operator:"over",result:"glow"})]})}),(0,a.jsx)(o.P.line,{animate:m,x1:"20%",y1:"20%",x2:"80%",y2:"80%",stroke:"var(--brand-gold)",strokeWidth:"0.5",strokeOpacity:"0.3",filter:"url(#glow)"}),(0,a.jsx)(o.P.circle,{animate:g,cx:"50%",cy:"50%",r:"2",fill:"var(--brand-gold)",filter:"url(#glow)"})]})]})})}},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>c});var a=t(95155);t(12115);var s=t(53999);function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},89852:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(95155);t(12115);var s=t(53999);function n(e){let{className:r,type:t,...n}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},93193:(e,r,t)=>{"use strict";async function a(e,r){try{let[d,l]=await Promise.all([e.from("customer_profiles").select("id").eq("id",r),e.from("business_profiles").select("id, business_slug").eq("id",r)]);if(d.error||l.error){var t,a,s,n,o,i;if(console.error("[redirectAfterLogin] Supabase query error:",d.error,l.error),(null==(t=d.error)?void 0:t.code)==="PGRST116"||(null==(a=l.error)?void 0:a.code)==="PGRST116"||(null==(n=d.error)||null==(s=n.message)?void 0:s.toLowerCase().includes("no rows"))||(null==(i=l.error)||null==(o=i.message)?void 0:o.toLowerCase().includes("no rows")))return"/choose-role";return"/?view=home"}if(d.data&&Array.isArray(d.data)&&d.data.length>0)return"/dashboard/customer";if(l.data&&Array.isArray(l.data)&&l.data.length>0){if(l.data[0].business_slug)return"/dashboard/business";return"/onboarding"}return"/choose-role"}catch(e){return console.error("[redirectAfterLogin] Unexpected error:",e),"/?view=home"}}t.d(r,{getPostLoginRedirectPath:()=>a})},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>i});var a=t(95155);t(12115);var s=t(99708),n=t(74466),o=t(53999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:n,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:n,className:r})),...l})}}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,8695,6874,6671,67,6199,221,7793,8441,1684,7358],()=>r(53716)),_N_E=e.O()}]);