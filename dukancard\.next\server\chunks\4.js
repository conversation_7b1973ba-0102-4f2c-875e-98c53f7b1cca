"use strict";exports.id=4,exports.ids=[4],exports.modules={4331:(e,i,n)=>{n.d(i,{G7:()=>c,L$:()=>u,h_:()=>p,oI:()=>l,uB:()=>o,xL:()=>d});var s=n(60687);n(43210);var r=n(70965),a=n(99270),t=n(96241);function o({className:e,...i}){return(0,s.jsx)(r.uB,{"data-slot":"command",className:(0,t.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...i})}function c({className:e,...i}){return(0,s.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,s.jsx)(a.A,{className:"size-4 shrink-0 opacity-50"}),(0,s.jsx)(r.uB.Input,{"data-slot":"command-input",className:(0,t.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...i})]})}function l({className:e,...i}){return(0,s.jsx)(r.uB.List,{"data-slot":"command-list",className:(0,t.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",e),...i})}function d({...e}){return(0,s.jsx)(r.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e})}function u({className:e,...i}){return(0,s.jsx)(r.uB.Group,{"data-slot":"command-group",className:(0,t.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...i})}function p({className:e,...i}){return(0,s.jsx)(r.uB.Item,{"data-slot":"command-item",className:(0,t.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}n(37826)},25499:(e,i,n)=>{n.d(i,{QH:()=>ew,bW:()=>eC,qW:()=>ef});var s=n(94577),r=n(13166),a=n(88059),t=n(79206),o=n(46001),c=n(28561),l=n(27864),d=n(17581),u=n(52153),p=n(87511),g=n(82080),m=n(74810),v=n(71057),A=n(26944),h=n(55947),y=n(62100),f=n(25387),C=n(57800),w=n(35583),S=n(41312),P=n(24391),b=n(59919),x=n(60137),k=n(56085),D=n(56981),M=n(27928),N=n(54082),T=n(47206),E=n(42238),F=n(68097),G=n(21067),H=n(27351),L=n(550),W=n(34410),B=n(85303),O=n(98971),_=n(94478),I=n(80375),R=n(20798),V=n(38038),j=n(82679),q=n(22915),z=n(8251),J=n(45583),$=n(32192),U=n(60052),Q=n(79410),Y=n(44381),K=n(96904),Z=n(56113),X=n(5263),ee=n(19930),ei=n(90318),en=n(47528),es=n(38313),er=n(51361),ea=n(28440),et=n(3900),eo=n(74606),ec=n(90131),el=n(73259),ed=n(17426),eu=n(53311),ep=n(71444),eg=n(33011),em=n(7569),ev=n(85905),eA=n(48210),eh=n(71850),ey=n(17313);let ef=[{name:"Restaurants",icon:s.A,slug:"restaurants",description:"Restaurants and dining establishments",isPopular:!0},{name:"Cafes & Bakeries",icon:r.A,slug:"cafes-bakeries",description:"Coffee shops, bakeries, and dessert places",isPopular:!0},{name:"Food Delivery",icon:a.A,slug:"food-delivery",description:"Food delivery services"},{name:"Catering",icon:s.A,slug:"catering",description:"Catering services for events"},{name:"Sweet Shops",icon:t.A,slug:"sweet-shops",description:"Traditional sweet shops and confectioneries"},{name:"Street Food",icon:s.A,slug:"street-food",description:"Street food vendors and stalls"},{name:"Cloud Kitchen",icon:s.A,slug:"cloud-kitchen",description:"Delivery-only food businesses"},{name:"Tiffin Services",icon:s.A,slug:"tiffin-services",description:"Home-cooked meal delivery services"},{name:"Retail Stores",icon:o.A,slug:"retail",description:"General retail and shops",isPopular:!0},{name:"Grocery & Supermarkets",icon:c.A,slug:"grocery",description:"Grocery stores and supermarkets",isPopular:!0},{name:"Fashion & Clothing",icon:l.A,slug:"fashion",description:"Clothing and fashion retailers",isPopular:!0},{name:"Electronics",icon:d.A,slug:"electronics",description:"Electronics and gadget stores"},{name:"Home Decor",icon:u.A,slug:"home-decor",description:"Home decor and furnishing stores"},{name:"Jewelry",icon:p.A,slug:"jewelry",description:"Jewelry and accessory stores"},{name:"Bookstores",icon:g.A,slug:"bookstores",description:"Book shops and stationers"},{name:"Footwear",icon:m.A,slug:"footwear",description:"Shoe and footwear stores"},{name:"Gift Shops",icon:v.A,slug:"gift-shops",description:"Gift and souvenir shops"},{name:"Eyewear",icon:A.A,slug:"eyewear",description:"Optical shops and eyewear retailers"},{name:"Mobile Shops",icon:d.A,slug:"mobile-shops",description:"Mobile phone retailers and repair shops"},{name:"Legal Services",icon:h.A,slug:"legal",description:"Lawyers and legal consultants",isPopular:!0},{name:"Financial Services",icon:y.A,slug:"financial",description:"Financial advisors and services",isPopular:!0},{name:"Accounting",icon:f.A,slug:"accounting",description:"Accounting and tax services"},{name:"Consulting",icon:C.A,slug:"consulting",description:"Business and management consulting"},{name:"Insurance",icon:w.A,slug:"insurance",description:"Insurance services and agents"},{name:"HR Services",icon:S.A,slug:"hr-services",description:"Human resources and recruitment services"},{name:"Tax Consultants",icon:f.A,slug:"tax-consultants",description:"Tax filing and consultation services"},{name:"Notary Services",icon:P.A,slug:"notary",description:"Notary and document verification services"},{name:"Translation Services",icon:b.A,slug:"translation",description:"Language translation and interpretation services"},{name:"Medical Clinics",icon:x.A,slug:"medical",description:"Medical clinics and doctors",isPopular:!0},{name:"Dental Care",icon:k.A,slug:"dental",description:"Dental clinics and services"},{name:"Pharmacy",icon:D.A,slug:"pharmacy",description:"Pharmacies and medical supplies"},{name:"Mental Health",icon:M.A,slug:"mental-health",description:"Mental health services and counseling"},{name:"Alternative Medicine",icon:N.A,slug:"alternative-medicine",description:"Ayurveda, homeopathy, and alternative treatments"},{name:"Diagnostic Centers",icon:T.A,slug:"diagnostic",description:"Medical testing and diagnostic centers"},{name:"Physiotherapy",icon:M.A,slug:"physiotherapy",description:"Physiotherapy and rehabilitation services"},{name:"Veterinary",icon:M.A,slug:"veterinary",description:"Veterinary clinics and pet healthcare"},{name:"Elder Care",icon:M.A,slug:"elder-care",description:"Elder care and assisted living services"},{name:"Maternity Care",icon:E.A,slug:"maternity",description:"Maternity and childcare services"},{name:"Salon & Spa",icon:F.A,slug:"salon-spa",description:"Beauty salons and spa services",isPopular:!0},{name:"Fitness",icon:G.A,slug:"fitness",description:"Gyms and fitness centers",isPopular:!0},{name:"Yoga & Meditation",icon:S.A,slug:"yoga",description:"Yoga studios and meditation centers"},{name:"Cosmetics",icon:k.A,slug:"cosmetics",description:"Cosmetics and beauty products"},{name:"Barber Shops",icon:F.A,slug:"barber",description:"Men's grooming and barber shops"},{name:"Wellness Centers",icon:k.A,slug:"wellness",description:"Wellness and holistic health centers"},{name:"Massage Therapy",icon:k.A,slug:"massage",description:"Massage and bodywork services"},{name:"Skin Care",icon:k.A,slug:"skin-care",description:"Skin care clinics and dermatology"},{name:"Schools",icon:H.A,slug:"schools",description:"Schools and educational institutions",isPopular:!0},{name:"Coaching Centers",icon:g.A,slug:"coaching",description:"Coaching and tutoring centers"},{name:"Vocational Training",icon:L.A,slug:"vocational",description:"Vocational and skill training"},{name:"Online Education",icon:W.A,slug:"online-education",description:"Online courses and e-learning"},{name:"Language Schools",icon:g.A,slug:"language",description:"Language learning and training centers"},{name:"Music Classes",icon:B.A,slug:"music-classes",description:"Music schools and training"},{name:"Dance Classes",icon:B.A,slug:"dance-classes",description:"Dance schools and training"},{name:"Art Schools",icon:O.A,slug:"art-schools",description:"Art and craft education"},{name:"Driving Schools",icon:_.A,slug:"driving-education",description:"Driving training and education"},{name:"Playschools",icon:E.A,slug:"playschools",description:"Preschools and early education"},{name:"Tuition Centers",icon:g.A,slug:"tuition",description:"Private tutoring and academic support"},{name:"IT Services",icon:I.A,slug:"it-services",description:"IT services and support",isPopular:!0},{name:"Software Development",icon:W.A,slug:"software",description:"Software development companies"},{name:"Web Development",icon:I.A,slug:"web-development",description:"Web design and development services"},{name:"Digital Marketing",icon:R.A,slug:"digital-marketing",description:"Digital marketing agencies and services"},{name:"App Development",icon:d.A,slug:"app-development",description:"Mobile app development services"},{name:"IT Hardware",icon:W.A,slug:"it-hardware",description:"Computer hardware sales and services"},{name:"Cyber Security",icon:V.A,slug:"cyber-security",description:"Cybersecurity services and solutions"},{name:"Cloud Services",icon:I.A,slug:"cloud-services",description:"Cloud computing and hosting services"},{name:"Data Analytics",icon:I.A,slug:"data-analytics",description:"Data analysis and business intelligence"},{name:"Auto Repair",icon:j.A,slug:"auto-repair",description:"Car repair and service centers",isPopular:!0},{name:"Car Dealerships",icon:_.A,slug:"car-dealerships",description:"New and used car dealerships"},{name:"Auto Parts",icon:q.A,slug:"auto-parts",description:"Automotive parts and accessories"},{name:"Two-Wheeler Services",icon:z.A,slug:"two-wheeler",description:"Motorcycle and scooter services"},{name:"Car Wash",icon:_.A,slug:"car-wash",description:"Car washing and detailing services"},{name:"Tyre Shops",icon:_.A,slug:"tyre-shops",description:"Tyre sales and services"},{name:"Auto Electricians",icon:J.A,slug:"auto-electricians",description:"Automotive electrical repair services"},{name:"Vehicle Rental",icon:_.A,slug:"vehicle-rental",description:"Car and bike rental services"},{name:"Real Estate",icon:$.A,slug:"real-estate",description:"Property and real estate services",isPopular:!0},{name:"Construction",icon:U.A,slug:"construction",description:"Construction services and contractors"},{name:"Interior Design",icon:O.A,slug:"interior-design",description:"Interior design and decoration services"},{name:"Architecture",icon:Q.A,slug:"architecture",description:"Architectural services and firms"},{name:"Property Management",icon:Q.A,slug:"property-management",description:"Property management services"},{name:"Building Materials",icon:Y.A,slug:"building-materials",description:"Construction materials suppliers"},{name:"Plumbing Services",icon:j.A,slug:"plumbing",description:"Plumbing installation and repair"},{name:"Electrical Services",icon:J.A,slug:"electrical",description:"Electrical installation and repair"},{name:"Painting Services",icon:K.A,slug:"painting",description:"House painting and finishing services"},{name:"Carpentry",icon:U.A,slug:"carpentry",description:"Carpentry and woodworking services"},{name:"Landscaping",icon:Z.A,slug:"landscaping",description:"Garden and landscape design services"},{name:"Hotels",icon:X.A,slug:"hotels",description:"Hotels and accommodations",isPopular:!0},{name:"Travel Agencies",icon:ee.A,slug:"travel-agencies",description:"Travel agencies and tour operators"},{name:"Transportation",icon:ei.A,slug:"transportation",description:"Transportation services"},{name:"Tourism",icon:en.A,slug:"tourism",description:"Tourism services and attractions"},{name:"Homestays",icon:$.A,slug:"homestays",description:"Homestays and guest houses"},{name:"Tour Guides",icon:ee.A,slug:"tour-guides",description:"Local tour guides and services"},{name:"Adventure Tourism",icon:ee.A,slug:"adventure-tourism",description:"Adventure sports and tourism"},{name:"Resorts",icon:X.A,slug:"resorts",description:"Resorts and vacation properties"},{name:"Visa Services",icon:P.A,slug:"visa-services",description:"Visa application and processing services"},{name:"Entertainment",icon:B.A,slug:"entertainment",description:"Entertainment venues and services",isPopular:!0},{name:"Event Management",icon:k.A,slug:"event-management",description:"Event planning and management services"},{name:"Wedding Services",icon:es.A,slug:"wedding",description:"Wedding planning and related services"},{name:"Photography",icon:er.A,slug:"photography",description:"Photography and videography services"},{name:"Cinema Halls",icon:ea.A,slug:"cinema",description:"Movie theaters and cinemas"},{name:"Gaming Zones",icon:et.A,slug:"gaming",description:"Gaming arcades and entertainment centers"},{name:"Party Venues",icon:B.A,slug:"party-venues",description:"Party and event venues"},{name:"DJs & Musicians",icon:B.A,slug:"djs-musicians",description:"DJs and live music performers"},{name:"Amusement Parks",icon:k.A,slug:"amusement-parks",description:"Amusement and theme parks"},{name:"Freelance Services",icon:C.A,slug:"freelance",description:"Independent professionals and freelancers",isPopular:!0},{name:"Graphic Design",icon:eo.A,slug:"graphic-design",description:"Graphic design services"},{name:"Content Creation",icon:ec.A,slug:"content-creation",description:"Content writing and creation services"},{name:"Art & Crafts",icon:K.A,slug:"art-crafts",description:"Artists and craftspeople"},{name:"Music & Performance",icon:el.A,slug:"music-performance",description:"Musicians and performers"},{name:"Videography",icon:ea.A,slug:"videography",description:"Video production and editing services"},{name:"Voice Over Artists",icon:el.A,slug:"voice-over",description:"Voice over and narration services"},{name:"Translators",icon:b.A,slug:"translators",description:"Language translation services"},{name:"Tutors",icon:g.A,slug:"tutors",description:"Private tutors and educators"},{name:"Consultants",icon:C.A,slug:"consultants",description:"Independent consultants and advisors"},{name:"Astrologers",icon:k.A,slug:"astrologers",description:"Astrology and horoscope services"},{name:"Manufacturing",icon:ed.A,slug:"manufacturing",description:"Manufacturing businesses"},{name:"Wholesale",icon:eu.A,slug:"wholesale",description:"Wholesale suppliers and distributors"},{name:"Textiles",icon:l.A,slug:"textiles",description:"Textile manufacturing and supplies"},{name:"Printing",icon:ep.A,slug:"printing",description:"Printing services and press"},{name:"Packaging",icon:Y.A,slug:"packaging",description:"Packaging materials and services"},{name:"Metal Works",icon:U.A,slug:"metal-works",description:"Metal fabrication and works"},{name:"Plastic Products",icon:ed.A,slug:"plastic-products",description:"Plastic manufacturing and products"},{name:"Handicrafts",icon:K.A,slug:"handicrafts",description:"Handmade crafts and products"},{name:"Furniture Making",icon:u.A,slug:"furniture",description:"Furniture manufacturing and carpentry"},{name:"Agriculture",icon:eg.A,slug:"agriculture",description:"Farming and agricultural services"},{name:"Dairy",icon:em.A,slug:"dairy",description:"Dairy farms and products"},{name:"Organic Products",icon:N.A,slug:"organic",description:"Organic farming and products"},{name:"Poultry",icon:N.A,slug:"poultry",description:"Poultry farming and products"},{name:"Fisheries",icon:em.A,slug:"fisheries",description:"Fish farming and aquaculture"},{name:"Nurseries",icon:N.A,slug:"nurseries",description:"Plant nurseries and gardening supplies"},{name:"Farm Equipment",icon:eg.A,slug:"farm-equipment",description:"Agricultural equipment and supplies"},{name:"Seed Suppliers",icon:N.A,slug:"seed-suppliers",description:"Seeds and agricultural inputs"},{name:"Floriculture",icon:Z.A,slug:"floriculture",description:"Flower growing and selling"},{name:"Utilities",icon:J.A,slug:"utilities",description:"Utility services"},{name:"Cleaning Services",icon:k.A,slug:"cleaning",description:"Cleaning and maintenance services"},{name:"Waste Management",icon:ev.A,slug:"waste-management",description:"Waste collection and recycling services"},{name:"Courier & Logistics",icon:a.A,slug:"logistics",description:"Courier, delivery, and logistics services"},{name:"Home Services",icon:$.A,slug:"home-services",description:"Home repair and maintenance services"},{name:"Pest Control",icon:eA.A,slug:"pest-control",description:"Pest control and extermination services"},{name:"Security Services",icon:V.A,slug:"security",description:"Security guards and services"},{name:"Laundry Services",icon:k.A,slug:"laundry",description:"Laundry and dry cleaning services"},{name:"Water Supply",icon:em.A,slug:"water-supply",description:"Water delivery and supply services"},{name:"Rental Services",icon:Y.A,slug:"rental",description:"Equipment and item rental services"},{name:"Religious Services",icon:eh.A,slug:"religious",description:"Religious institutions and services"},{name:"NGOs & Charities",icon:es.A,slug:"ngo",description:"Non-profit organizations and charities"},{name:"Government Services",icon:ey.A,slug:"government",description:"Government offices and services"},{name:"Repair Services",icon:j.A,slug:"repair",description:"General repair and maintenance services"},{name:"Tailoring",icon:F.A,slug:"tailoring",description:"Tailoring and alteration services"},{name:"Printing & Copying",icon:ep.A,slug:"printing-copying",description:"Printing, copying, and document services"},{name:"Astrology",icon:k.A,slug:"astrology",description:"Astrology and spiritual services"},{name:"Funeral Services",icon:eh.A,slug:"funeral",description:"Funeral homes and memorial services"},{name:"Daycare",icon:E.A,slug:"daycare",description:"Childcare and daycare services"},{name:"Pet Services",icon:M.A,slug:"pet-services",description:"Pet grooming, boarding, and care"},{name:"Other Services",icon:C.A,slug:"other",description:"Other business services not listed elsewhere"}];function eC(e){return e&&e>0&&e<ef.length?ef.slice(0,e):ef}function ew(e){let i=ef.filter(e=>e.isPopular);return e&&e>0&&e<i.length?i.slice(0,e):i}ef.slice(0,8),ef.slice(8,19),ef.slice(19,28),ef.slice(28,38),ef.slice(38,46),ef.slice(46,57),ef.slice(57,66),ef.slice(66,75),ef.slice(75,86),ef.slice(86,95),ef.slice(95,104),ef.slice(104,115),ef.slice(115,124),ef.slice(124,133),ef.slice(133,143),ef.slice(143)},56528:(e,i,n)=>{n.d(i,{JM:()=>o,eb:()=>t,tz:()=>a});var s=n(91199);n(42087);var r=n(76881);async function a(e){if(!e||!/^\d{6}$/.test(e))return{error:"Invalid Pincode format."};let i=await (0,r.createClient)();try{let{data:n,error:s}=await i.from("pincodes").select("OfficeName, DivisionName, StateName").eq("Pincode",e).order("OfficeName");if(s)return console.error("Pincode Fetch Error:",s),{error:"Database error fetching pincode details."};if(!n||0===n.length)return{error:"Pincode not found."};let r=n[0].StateName,a=n[0].DivisionName,t=[...new Set(n.map(e=>e.OfficeName))];return{data:{city:a,state:r,localities:t},city:a,state:r,localities:t}}catch(e){return console.error("Pincode Lookup Exception:",e),{error:"An unexpected error occurred during pincode lookup."}}}async function t(e){if(!e||e.length<2)return{error:"City name must be at least 2 characters."};let i=await (0,r.createClient)();try{let{data:n,error:s}=await i.from("pincodes").select("Pincode, OfficeName, StateName, DivisionName").ilike("DivisionName",`%${e}%`).order("Pincode");if(s)return console.error("City Fetch Error:",s),{error:"Database error fetching city details."};if(!n||0===n.length)return{error:"City not found."};let r=n[0].StateName,a=[...new Set(n.map(e=>e.Pincode))],t=[...new Set(n.map(e=>e.OfficeName))];return{data:{pincodes:a,state:r,localities:t},pincodes:a,state:r,localities:t}}catch(e){return console.error("City Lookup Exception:",e),{error:"An unexpected error occurred during city lookup."}}}async function o(e){if(!e||e.length<2)return{error:"Query must be at least 2 characters."};let i=await (0,r.createClient)();try{let{data:n,error:s}=await i.rpc("get_distinct_cities",{search_query:`%${e}%`,result_limit:5});if(s){console.error("City Suggestions Error:",s);try{let{data:n,error:s}=await i.from("pincodes").select("DivisionName").ilike("DivisionName",`%${e}%`).order("DivisionName").limit(100);if(s)throw s;if(!n||0===n.length)return{data:{cities:[]},cities:[]};let r=[...new Set(n.map(e=>e.DivisionName.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase())))].slice(0,5);return{data:{cities:r},cities:r}}catch(e){return console.error("Fallback City Query Error:",e),{error:"Database error fetching city suggestions."}}}if(!n||0===n.length)return{data:{cities:[]},cities:[]};let r=n.map(e=>e.city.toLowerCase().replace(/\b\w/g,e=>e.toUpperCase()));return{data:{cities:r},cities:r}}catch(e){return console.error("City Suggestions Exception:",e),{error:"An unexpected error occurred while fetching city suggestions."}}}(0,n(33331).D)([a,t,o]),(0,s.A)(a,"40d32ea8edc6596cf0013772648e4fa734c9679198",null),(0,s.A)(t,"40142db2f2e9e23e42f1d64a8d98f054ab16849fcf",null),(0,s.A)(o,"406809393363051c82bcecb759b1153ca34eced5e4",null)}};