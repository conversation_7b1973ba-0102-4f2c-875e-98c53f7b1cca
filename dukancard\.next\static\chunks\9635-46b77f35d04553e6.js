"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9635],{4884:(e,r,t)=>{t.d(r,{bL:()=>w,zi:()=>k});var n=t(12115),o=t(85185),l=t(6101),a=t(46081),i=t(5845),u=t(45503),s=t(11275),c=t(63540),d=t(95155),p="Switch",[f,h]=(0,a.A)(p),[v,g]=f(p),y=n.forwardRef((e,r)=>{let{__scopeSwitch:t,name:a,checked:u,defaultChecked:s,required:f,disabled:h,value:g="on",onCheckedChange:y,form:m,...x}=e,[w,k]=n.useState(null),j=(0,l.s)(r,e=>k(e)),R=n.useRef(!1),P=!w||m||!!w.closest("form"),[A,O]=(0,i.i)({prop:u,defaultProp:null!=s&&s,onChange:y,caller:p});return(0,d.jsxs)(v,{scope:t,checked:A,disabled:h,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":f,"data-state":C(A),"data-disabled":h?"":void 0,disabled:h,value:g,...x,ref:j,onClick:(0,o.m)(e.onClick,e=>{O(e=>!e),P&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),P&&(0,d.jsx)(b,{control:w,bubbles:!R.current,name:a,value:g,checked:A,required:f,disabled:h,form:m,style:{transform:"translateX(-100%)"}})]})});y.displayName=p;var m="SwitchThumb",x=n.forwardRef((e,r)=>{let{__scopeSwitch:t,...n}=e,o=g(m,t);return(0,d.jsx)(c.sG.span,{"data-state":C(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:r})});x.displayName=m;var b=n.forwardRef((e,r)=>{let{__scopeSwitch:t,control:o,checked:a,bubbles:i=!0,...c}=e,p=n.useRef(null),f=(0,l.s)(p,r),h=(0,u.Z)(a),v=(0,s.X)(o);return n.useEffect(()=>{let e=p.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==a&&r){let t=new Event("click",{bubbles:i});r.call(e,a),e.dispatchEvent(t)}},[h,a,i]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...c,tabIndex:-1,ref:f,style:{...c.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function C(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var w=y,k=x},6654:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=t(12115);function o(e,r){let t=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=t.current;e&&(t.current=null,e());let r=o.current;r&&(o.current=null,r())}else e&&(t.current=l(e,n)),r&&(o.current=l(r,n))},[e,r])}function l(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},10081:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},27213:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},43332:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98176:(e,r,t)=>{t.d(r,{UC:()=>V,ZL:()=>U,bL:()=>z,l9:()=>G});var n=t(12115),o=t(85185),l=t(6101),a=t(46081),i=t(19178),u=t(92293),s=t(25519),c=t(61285),d=t(35152),p=t(34378),f=t(28905),h=t(63540),v=t(95155),g=Symbol("radix.slottable");function y(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===g}var m=t(5845),x=t(38168),b=t(31114),C="Popover",[w,k]=(0,a.A)(C,[d.Bk]),j=(0,d.Bk)(),[R,P]=w(C),A=e=>{let{__scopePopover:r,children:t,open:o,defaultOpen:l,onOpenChange:a,modal:i=!1}=e,u=j(r),s=n.useRef(null),[p,f]=n.useState(!1),[h,g]=(0,m.i)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:C});return(0,v.jsx)(d.bL,{...u,children:(0,v.jsx)(R,{scope:r,contentId:(0,c.B)(),triggerRef:s,open:h,onOpenChange:g,onOpenToggle:n.useCallback(()=>g(e=>!e),[g]),hasCustomAnchor:p,onCustomAnchorAdd:n.useCallback(()=>f(!0),[]),onCustomAnchorRemove:n.useCallback(()=>f(!1),[]),modal:i,children:t})})};A.displayName=C;var O="PopoverAnchor";n.forwardRef((e,r)=>{let{__scopePopover:t,...o}=e,l=P(O,t),a=j(t),{onCustomAnchorAdd:i,onCustomAnchorRemove:u}=l;return n.useEffect(()=>(i(),()=>u()),[i,u]),(0,v.jsx)(d.Mz,{...a,...o,ref:r})}).displayName=O;var _="PopoverTrigger",E=n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,a=P(_,t),i=j(t),u=(0,l.s)(r,a.triggerRef),s=(0,v.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":q(a.open),...n,ref:u,onClick:(0,o.m)(e.onClick,a.onOpenToggle)});return a.hasCustomAnchor?s:(0,v.jsx)(d.Mz,{asChild:!0,...i,children:s})});E.displayName=_;var N="PopoverPortal",[D,F]=w(N,{forceMount:void 0}),I=e=>{let{__scopePopover:r,forceMount:t,children:n,container:o}=e,l=P(N,r);return(0,v.jsx)(D,{scope:r,forceMount:t,children:(0,v.jsx)(f.C,{present:t||l.open,children:(0,v.jsx)(p.Z,{asChild:!0,container:o,children:n})})})};I.displayName=N;var M="PopoverContent",S=n.forwardRef((e,r)=>{let t=F(M,e.__scopePopover),{forceMount:n=t.forceMount,...o}=e,l=P(M,e.__scopePopover);return(0,v.jsx)(f.C,{present:n||l.open,children:l.modal?(0,v.jsx)(T,{...o,ref:r}):(0,v.jsx)(B,{...o,ref:r})})});S.displayName=M;var L=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){var a;let e,i,u=(a=t,(i=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(i=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),s=function(e,r){let t={...r};for(let n in r){let o=e[n],l=r[n];/^on[A-Z]/.test(n)?o&&l?t[n]=(...e)=>{l(...e),o(...e)}:o&&(t[n]=o):"style"===n?t[n]={...o,...l}:"className"===n&&(t[n]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==n.Fragment&&(s.ref=r?(0,l.t)(r,u):u),n.cloneElement(t,s)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:o,...l}=e,a=n.Children.toArray(o),i=a.find(y);if(i){let e=i.props.children,o=a.map(r=>r!==i?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,v.jsx)(r,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,v.jsx)(r,{...l,ref:t,children:o})});return t.displayName=`${e}.Slot`,t}("PopoverContent.RemoveScroll"),T=n.forwardRef((e,r)=>{let t=P(M,e.__scopePopover),a=n.useRef(null),i=(0,l.s)(r,a),u=n.useRef(!1);return n.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,v.jsx)(b.A,{as:L,allowPinchZoom:!0,children:(0,v.jsx)(W,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;e.preventDefault(),u.current||null==(r=t.triggerRef.current)||r.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey;u.current=2===r.button||t},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),B=n.forwardRef((e,r)=>{let t=P(M,e.__scopePopover),o=n.useRef(!1),l=n.useRef(!1);return(0,v.jsx)(W,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var n,a;null==(n=e.onCloseAutoFocus)||n.call(e,r),r.defaultPrevented||(o.current||null==(a=t.triggerRef.current)||a.focus(),r.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:r=>{var n,a;null==(n=e.onInteractOutside)||n.call(e,r),r.defaultPrevented||(o.current=!0,"pointerdown"===r.detail.originalEvent.type&&(l.current=!0));let i=r.target;(null==(a=t.triggerRef.current)?void 0:a.contains(i))&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&l.current&&r.preventDefault()}})}),W=n.forwardRef((e,r)=>{let{__scopePopover:t,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:l,disableOutsidePointerEvents:a,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:h,...g}=e,y=P(M,t),m=j(t);return(0,u.Oh)(),(0,v.jsx)(s.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:l,children:(0,v.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:h,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:f,onDismiss:()=>y.onOpenChange(!1),children:(0,v.jsx)(d.UC,{"data-state":q(y.open),role:"dialog",id:y.contentId,...m,...g,ref:r,style:{...g.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Z="PopoverClose";function q(e){return e?"open":"closed"}n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,l=P(Z,t);return(0,v.jsx)(h.sG.button,{type:"button",...n,ref:r,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})}).displayName=Z,n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,o=j(t);return(0,v.jsx)(d.i3,{...o,...n,ref:r})}).displayName="PopoverArrow";var z=A,G=E,U=I,V=S}}]);