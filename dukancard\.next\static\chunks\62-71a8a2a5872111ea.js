"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[62],{27213:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},34477:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{callServer:function(){return r.callServer},createServerReference:function(){return i},findSourceMapURL:function(){return l.findSourceMapURL}});let r=n(53806),l=n(31818),i=n(34979).createServerReference},48021:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},50402:(e,t,n)=>{n.d(t,{JR:()=>C,_G:()=>c,be:()=>a,gB:()=>h,gl:()=>w,kL:()=>u});var r=n(12115),l=n(75143),i=n(78266);function a(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function o(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=a(t,r,n),o=t[l],u=i[l];return u&&o?{x:u.left-o.left,y:u.top-o.top,scaleX:u.width/o.width,scaleY:u.height/o.height}:null},s={scaleX:1,scaleY:1},c=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:a}=e,o=null!=(t=i[n])?t:r;if(!o)return null;if(l===n){let e=i[a];return e?{x:0,y:n<a?e.top+e.height-(o.top+o.height):e.top-o.top,...s}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=a?{x:0,y:-o.height-u,...s}:l<n&&l>=a?{x:0,y:o.height+u,...s}:{x:0,y:0,...s}},d="Sortable",f=r.createContext({activeIndex:-1,containerId:d,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function h(e){let{children:t,id:n,items:a,strategy:o=u,disabled:s=!1}=e,{active:c,dragOverlay:h,droppableRects:g,over:p,measureDroppableContainers:v}=(0,l.fF)(),m=(0,i.YG)(d,n),y=null!==h.rect,b=(0,r.useMemo)(()=>a.map(e=>"object"==typeof e&&"id"in e?e.id:e),[a]),w=null!=c,x=c?b.indexOf(c.id):-1,E=p?b.indexOf(p.id):-1,C=(0,r.useRef)(b),D=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(b,C.current),S=-1!==E&&-1===x||D,M="boolean"==typeof s?{draggable:s,droppable:s}:s;(0,i.Es)(()=>{D&&w&&v(b)},[D,b,w,v]),(0,r.useEffect)(()=>{C.current=b},[b]);let R=(0,r.useMemo)(()=>({activeIndex:x,containerId:m,disabled:M,disableTransforms:S,items:b,overIndex:E,useDragOverlay:y,sortedRects:b.reduce((e,t,n)=>{let r=g.get(t);return r&&(e[n]=r),e},Array(b.length)),strategy:o}),[x,m,M.draggable,M.droppable,S,b,E,g,y,o]);return r.createElement(f.Provider,{value:R},t)}let g=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return a(n,r,l).indexOf(t)},p=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},v={duration:200,easing:"ease"},m="transform",y=i.Ks.Transition.toString({property:m,duration:0,easing:"linear"}),b={roleDescription:"sortable"};function w(e){var t,n,a,u;let{animateLayoutChanges:s=p,attributes:c,disabled:d,data:h,getNewIndex:w=g,id:x,strategy:E,resizeObserverConfig:C,transition:D=v}=e,{items:S,containerId:M,activeIndex:R,disabled:k,disableTransforms:L,sortedRects:T,overIndex:O,useDragOverlay:N,strategy:P}=(0,r.useContext)(f),A=(t=d,n=k,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(a=null==t?void 0:t.draggable)?a:n.draggable,droppable:null!=(u=null==t?void 0:t.droppable)?u:n.droppable}),I=S.indexOf(x),z=(0,r.useMemo)(()=>({sortable:{containerId:M,index:I,items:S},...h}),[M,h,I,S]),j=(0,r.useMemo)(()=>S.slice(S.indexOf(x)),[S,x]),{rect:Y,node:W,isOver:B,setNodeRef:K}=(0,l.zM)({id:x,data:z,disabled:A.droppable,resizeObserverConfig:{updateMeasurementsFor:j,...C}}),{active:F,activatorEvent:U,activeNodeRect:X,attributes:_,setNodeRef:G,listeners:V,isDragging:H,over:q,setActivatorNodeRef:J,transform:Q}=(0,l.PM)({id:x,data:z,attributes:{...b,...c},disabled:A.draggable}),Z=(0,i.jn)(K,G),$=!!F,ee=$&&!L&&o(R)&&o(O),et=!N&&H,en=et&&ee?Q:null,er=ee?null!=en?en:(null!=E?E:P)({rects:T,activeNodeRect:X,activeIndex:R,overIndex:O,index:I}):null,el=o(R)&&o(O)?w({id:x,items:S,activeIndex:R,overIndex:O}):I,ei=null==F?void 0:F.id,ea=(0,r.useRef)({activeId:ei,items:S,newIndex:el,containerId:M}),eo=S!==ea.current.items,eu=s({active:F,containerId:M,isDragging:H,isSorting:$,id:x,index:I,items:S,newIndex:ea.current.newIndex,previousItems:ea.current.items,previousContainerId:ea.current.containerId,transition:D,wasDragging:null!=ea.current.activeId}),es=function(e){let{disabled:t,index:n,node:a,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.Es)(()=>{if(!t&&n!==c.current&&a.current){let e=o.current;if(e){let t=(0,l.Sj)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,a,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:I,node:W,rect:Y});return(0,r.useEffect)(()=>{$&&ea.current.newIndex!==el&&(ea.current.newIndex=el),M!==ea.current.containerId&&(ea.current.containerId=M),S!==ea.current.items&&(ea.current.items=S)},[$,el,M,S]),(0,r.useEffect)(()=>{if(ei===ea.current.activeId)return;if(null!=ei&&null==ea.current.activeId){ea.current.activeId=ei;return}let e=setTimeout(()=>{ea.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:F,activeIndex:R,attributes:_,data:z,rect:Y,index:I,newIndex:el,items:S,isOver:B,isSorting:$,isDragging:H,listeners:V,node:W,overIndex:O,over:q,setNodeRef:Z,setActivatorNodeRef:J,setDroppableNodeRef:K,setDraggableNodeRef:G,transform:null!=es?es:er,transition:es||eo&&ea.current.newIndex===I?y:(!et||(0,i.kx)(U))&&D&&($||eu)?i.Ks.Transition.toString({...D,property:m}):void 0}}function x(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let E=[l.vL.Down,l.vL.Right,l.vL.Up,l.vL.Left],C=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:a,droppableContainers:o,over:u,scrollableAncestors:s}}=t;if(E.includes(e.code)){if(e.preventDefault(),!n||!r)return;let t=[];o.getEnabled().forEach(n=>{if(!n||null!=n&&n.disabled)return;let i=a.get(n.id);if(i)switch(e.code){case l.vL.Down:r.top<i.top&&t.push(n);break;case l.vL.Up:r.top>i.top&&t.push(n);break;case l.vL.Left:r.left>i.left&&t.push(n);break;case l.vL.Right:r.left<i.left&&t.push(n)}});let c=(0,l.y$)({active:n,collisionRect:r,droppableRects:a,droppableContainers:t,pointerCoordinates:null}),d=(0,l.Vy)(c,"id");if(d===(null==u?void 0:u.id)&&c.length>1&&(d=c[1].id),null!=d){let e=o.get(n.id),t=o.get(d),u=t?a.get(t.id):null,c=null==t?void 0:t.node.current;if(c&&u&&e&&t){let n=(0,l.sl)(c).some((e,t)=>s[t]!==e),a=D(e,t),o=function(e,t){return!!x(e)&&!!x(t)&&!!D(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),d=n||!a?{x:0,y:0}:{x:o?r.width-u.width:0,y:o?r.height-u.height:0},f={x:u.left,y:u.top};return d.x&&d.y?f:(0,i.Re)(f,d)}}}};function D(e,t){return!!x(e)&&!!x(t)&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}},51154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},60760:(e,t,n)=>{n.d(t,{N:()=>m});var r=n(95155),l=n(12115),i=n(90869),a=n(82885),o=n(97494),u=n(80845),s=n(51508);class c extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=e instanceof HTMLElement&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:n,anchorX:i}=e,a=(0,l.useId)(),o=(0,l.useRef)(null),u=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,l.useContext)(s.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:l,right:s}=u.current;if(n||!o.current||!e||!t)return;o.current.dataset.motionPopId=a;let c=document.createElement("style");return d&&(c.nonce=d),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(l):"right: ".concat(s),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.removeChild(c)}},[n]),(0,r.jsx)(c,{isPresent:n,childRef:o,sizeRef:u,children:l.cloneElement(t,{ref:o})})}let f=e=>{let{children:t,initial:n,isPresent:i,onExitComplete:o,custom:s,presenceAffectsLayout:c,mode:f,anchorX:g}=e,p=(0,a.M)(h),v=(0,l.useId)(),m=!0,y=(0,l.useMemo)(()=>(m=!1,{id:v,initial:n,isPresent:i,custom:s,onExitComplete:e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;o&&o()},register:e=>(p.set(e,!1),()=>p.delete(e))}),[i,p,o]);return c&&m&&(y={...y}),(0,l.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[i]),l.useEffect(()=>{i||p.size||!o||o()},[i]),"popLayout"===f&&(t=(0,r.jsx)(d,{isPresent:i,anchorX:g,children:t})),(0,r.jsx)(u.t.Provider,{value:y,children:t})};function h(){return new Map}var g=n(32082);let p=e=>e.key||"";function v(e){let t=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&t.push(e)}),t}let m=e=>{let{children:t,custom:n,initial:u=!0,onExitComplete:s,presenceAffectsLayout:c=!0,mode:d="sync",propagate:h=!1,anchorX:m="left"}=e,[y,b]=(0,g.xQ)(h),w=(0,l.useMemo)(()=>v(t),[t]),x=h&&!y?[]:w.map(p),E=(0,l.useRef)(!0),C=(0,l.useRef)(w),D=(0,a.M)(()=>new Map),[S,M]=(0,l.useState)(w),[R,k]=(0,l.useState)(w);(0,o.E)(()=>{E.current=!1,C.current=w;for(let e=0;e<R.length;e++){let t=p(R[e]);x.includes(t)?D.delete(t):!0!==D.get(t)&&D.set(t,!1)}},[R,x.length,x.join("-")]);let L=[];if(w!==S){let e=[...w];for(let t=0;t<R.length;t++){let n=R[t],r=p(n);x.includes(r)||(e.splice(t,0,n),L.push(n))}return"wait"===d&&L.length&&(e=L),k(v(e)),M(w),null}let{forceRender:T}=(0,l.useContext)(i.L);return(0,r.jsx)(r.Fragment,{children:R.map(e=>{let t=p(e),l=(!h||!!y)&&(w===R||x.includes(t));return(0,r.jsx)(f,{isPresent:l,initial:(!E.current||!!u)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,onExitComplete:l?void 0:()=>{if(!D.has(t))return;D.set(t,!0);let e=!0;D.forEach(t=>{t||(e=!1)}),e&&(null==T||T(),k(C.current),h&&(null==b||b()),s&&s())},anchorX:m,children:e},t)})})}},62525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},75143:(e,t,n)=>{let r;n.d(t,{Mp:()=>eF,Hd:()=>e5,vL:()=>o,uN:()=>eu,AN:()=>eh,fp:()=>A,y$:()=>I,Sj:()=>K,Vy:()=>N,sl:()=>U,fF:()=>eG,PM:()=>e_,zM:()=>eH,MS:()=>S,FR:()=>M});var l,i,a,o,u,s,c,d,f,h,g=n(12115),p=n(47650),v=n(78266);let m={display:"none"};function y(e){let{id:t,value:n}=e;return g.createElement("div",{id:t,style:m},n)}function b(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return g.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let w=(0,g.createContext)(null),x={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},E={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function C(e){let{announcements:t=E,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=x}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,g.useState)("");return{announce:(0,g.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,v.YG)("DndLiveRegion"),[u,s]=(0,g.useState)(!1);(0,g.useEffect)(()=>{s(!0)},[]);var c=(0,g.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let d=(0,g.useContext)(w);if((0,g.useEffect)(()=>{if(!d)throw Error("useDndMonitor must be used within a children of <DndContext>");return d(c)},[c,d]),!u)return null;let f=g.createElement(g.Fragment,null,g.createElement(y,{id:r,value:l.draggable}),g.createElement(b,{id:o,announcement:a}));return n?(0,p.createPortal)(f,n):f}function D(){}function S(e,t){return(0,g.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function M(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,g.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let R=Object.freeze({x:0,y:0});function k(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function L(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function T(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function O(e){let{left:t,top:n,height:r,width:l}=e;return[{x:t,y:n},{x:t+l,y:n},{x:t,y:n+r},{x:t+l,y:n+r}]}function N(e,t){if(!e||0===e.length)return null;let[n]=e;return t?n[t]:n}function P(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let A=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=P(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=k(P(r),l);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(L)},I=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=O(t),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=O(r),a=Number((l.reduce((e,t,r)=>e+k(n[r],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:a}})}}return i.sort(L)},z=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(T)};function j(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:R}let Y=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1);function W(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let B={ignoreTransform:!1};function K(e,t){void 0===t&&(t=B);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,v.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=W(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function F(e){return K(e,{ignoreTransform:!0})}function U(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,v.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,v.sb)(l)||(0,v.xZ)(l)||n.includes(l))return n;let a=(0,v.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,v.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,v.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function X(e){let[t]=U(e,1);return null!=t?t:null}function _(e){return v.Sw&&e?(0,v.l6)(e)?e:(0,v.Ll)(e)?(0,v.wz)(e)||e===(0,v.TW)(e).scrollingElement?window:(0,v.sb)(e)?e:null:null:null}function G(e){return(0,v.l6)(e)?e.scrollX:e.scrollLeft}function V(e){return(0,v.l6)(e)?e.scrollY:e.scrollTop}function H(e){return{x:G(e),y:V(e)}}function q(e){return!!v.Sw&&!!e&&e===document.scrollingElement}function J(e){let t={x:0,y:0},n=q(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let Q={x:.2,y:.2};function Z(e){return e.reduce((e,t)=>(0,v.WQ)(e,H(t)),R)}function $(e,t){if(void 0===t&&(t=K),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);X(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let ee=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+G(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+V(t),0)}]];class et{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=U(t),r=Z(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,ee))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class en{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function er(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function el(e){e.preventDefault()}function ei(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let ea={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},eo=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class eu{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new en((0,v.TW)(t)),this.windowListeners=new en((0,v.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&$(n),t(R)}handleKeyDown(e){if((0,v.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=ea,coordinateGetter:i=eo,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u))return void this.handleEnd(e);if(l.cancel.includes(u))return void this.handleCancel(e);let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:R;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,v.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:f,minScroll:h}=J(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(l===o.Right?g.right-g.width/2:g.right,Math.max(l===o.Right?g.left:g.left+g.width/2,d.x)),y:Math.min(l===o.Down?g.bottom-g.height/2:g.bottom,Math.max(l===o.Down?g.top:g.top+g.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,m=l===o.Down&&!c||l===o.Up&&!i;if(v&&p.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=f.x||l===o.Left&&e>=h.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-f.x:n.scrollLeft-h.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(m&&p.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=f.y||l===o.Up&&e>=h.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-f.y:n.scrollTop-h.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,v.WQ)((0,v.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function es(e){return!!(e&&"distance"in e)}function ec(e){return!!(e&&"delay"in e)}eu.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=ea,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class ed{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,v.zk)(e);return e instanceof t?e:(0,v.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,v.TW)(i),this.documentListeners=new en(this.document),this.listeners=new en(n),this.windowListeners=new en((0,v.zk)(i)),this.initialCoordinates=null!=(r=(0,v.e_)(l))?r:R,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,el),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,el),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(ec(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(es(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,ei,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,v.e_)(e))?t:R,u=(0,v.Re)(r,o);if(!n&&a){if(es(a)){if(null!=a.tolerance&&er(u,a.tolerance))return this.handleCancel();if(er(u,a.distance))return this.handleStart()}return ec(a)&&er(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ef={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class eh extends ed{constructor(e){let{event:t}=e;super(e,ef,(0,v.TW)(t.target))}}eh.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let eg={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(u||(u={}));class ep extends ed{constructor(e){super(e,eg,(0,v.TW)(e.event.target))}}ep.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==u.RightClick&&(null==r||r({event:n}),!0)}}];let ev={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class em extends ed{constructor(e){super(e,ev)}static setup(){return window.addEventListener(ev.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ev.move.name,e)};function e(){}}}em.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(s||(s={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let ey={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(f||(f={})).Optimized="optimized";let eb=new Map;function ew(e,t){return(0,v.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function ex(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function eE(e){return new et(K(e),e)}function eC(e,t,n){void 0===t&&(t=eE);let[r,l]=(0,g.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=ex({callback:i});return(0,v.Es)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let eD=[];function eS(e,t){void 0===t&&(t=[]);let n=(0,g.useRef)(null);return(0,g.useEffect)(()=>{n.current=null},t),(0,g.useEffect)(()=>{let t=e!==R;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,v.Re)(e,n.current):R}function eM(e){return(0,g.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eR=[];function ek(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,v.sb)(t)?t:e}let eL=[{sensor:eh,options:{}},{sensor:eu,options:{}}],eT={current:{}},eO={draggable:{measure:F},droppable:{measure:F,strategy:d.WhileDragging,frequency:f.Optimized},dragOverlay:{measure:K}};class eN extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eP={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eN,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:D},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eO,measureDroppableContainers:D,windowRect:null,measuringScheduled:!1},eA={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:D,draggableNodes:new Map,over:null,measureDroppableContainers:D},eI=(0,g.createContext)(eA),ez=(0,g.createContext)(eP);function ej(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eN}}}function eY(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eN(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new eN(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eN(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eW(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,g.useContext)(eI),i=(0,v.ZC)(r),a=(0,v.ZC)(null==n?void 0:n.id);return(0,g.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,v.kx)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,v.ag)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}function eB(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}let eK=(0,g.createContext)({...R,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(h||(h={}));let eF=(0,g.memo)(function(e){var t,n,r,a,o,u;let{id:f,accessibility:m,autoScroll:y=!0,children:b,sensors:x=eL,collisionDetection:E=z,measuring:D,modifiers:S,...M}=e,[k,L]=(0,g.useReducer)(eY,void 0,ej),[T,O]=function(){let[e]=(0,g.useState)(()=>new Set),t=(0,g.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,g.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[P,A]=(0,g.useState)(h.Uninitialized),I=P===h.Initialized,{draggable:{active:W,nodes:B,translate:F},droppable:{containers:G}}=k,V=null!=W?B.get(W):null,$=(0,g.useRef)({initial:null,translated:null}),ee=(0,g.useMemo)(()=>{var e;return null!=W?{id:W,data:null!=(e=null==V?void 0:V.data)?e:eT,rect:$}:null},[W,V]),en=(0,g.useRef)(null),[er,el]=(0,g.useState)(null),[ei,ea]=(0,g.useState)(null),eo=(0,v.YN)(M,Object.values(M)),eu=(0,v.YG)("DndDescribedBy",f),es=(0,g.useMemo)(()=>G.getEnabled(),[G]),ec=(0,g.useMemo)(()=>({draggable:{...eO.draggable,...null==D?void 0:D.draggable},droppable:{...eO.droppable,...null==D?void 0:D.droppable},dragOverlay:{...eO.dragOverlay,...null==D?void 0:D.dragOverlay}}),[null==D?void 0:D.draggable,null==D?void 0:D.droppable,null==D?void 0:D.dragOverlay]),{droppableRects:ed,measureDroppableContainers:ef,measuringScheduled:eh}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,g.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,g.useRef)(e),f=function(){switch(s){case d.Always:return!1;case d.BeforeDragging:return n;default:return!n}}(),h=(0,v.YN)(f),p=(0,g.useCallback)(function(e){void 0===e&&(e=[]),h.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[h]),m=(0,g.useRef)(null),y=(0,v.KG)(t=>{if(f&&!n)return eb;if(!t||t===eb||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new et(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,f,u]);return(0,g.useEffect)(()=>{c.current=e},[e]),(0,g.useEffect)(()=>{f||p()},[n,f]),(0,g.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,g.useEffect)(()=>{f||"number"!=typeof o||null!==m.current||(m.current=setTimeout(()=>{p(),m.current=null},o))},[o,f,p,...r]),{droppableRects:y,measureDroppableContainers:p,measuringScheduled:null!=i}}(es,{dragging:I,dependencies:[F.x,F.y],config:ec.droppable}),eg=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,v.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(B,W),ep=(0,g.useMemo)(()=>ei?(0,v.e_)(ei):null,[ei]),ev=function(){let e=(null==er?void 0:er.autoScrollEnabled)===!1,t="object"==typeof y?!1===y.enabled:!1===y,n=I&&!e&&!t;return"object"==typeof y?{...y,enabled:n}:{enabled:n}}(),em=ew(eg,ec.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,g.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,v.Es)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=j(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=X(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=W?B.get(W):null,config:ev.layoutShiftCompensation,initialRect:em,measure:ec.draggable.measure});let eE=eC(eg,ec.draggable.measure,em),eN=eC(eg?eg.parentElement:null),eP=(0,g.useRef)({activatorEvent:null,active:null,activeNode:eg,collisionRect:null,collisions:null,droppableRects:ed,draggableNodes:B,draggingNode:null,draggingNodeRect:null,droppableContainers:G,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eA=G.getNodeFor(null==(t=eP.current.over)?void 0:t.id),eF=function(e){let{measure:t}=e,[n,r]=(0,g.useState)(null),l=ex({callback:(0,g.useCallback)(e=>{for(let{target:n}of e)if((0,v.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,g.useCallback)(e=>{let n=ek(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,v.lk)(i);return(0,g.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:ec.dragOverlay.measure}),eU=null!=(n=eF.nodeRef.current)?n:eg,eX=I?null!=(r=eF.rect)?r:eE:null,e_=!!(eF.nodeRef.current&&eF.rect),eG=function(e){let t=ew(e);return j(e,t)}(e_?null:eE),eV=eM(eU?(0,v.zk)(eU):null),eH=function(e){let t=(0,g.useRef)(e),n=(0,v.KG)(n=>e?n&&n!==eD&&e&&t.current&&e.parentNode===t.current.parentNode?n:U(e):eD,[e]);return(0,g.useEffect)(()=>{t.current=e},[e]),n}(I?null!=eA?eA:eg:null),eq=function(e,t){void 0===t&&(t=K);let[n]=e,r=eM(n?(0,v.zk)(n):null),[l,i]=(0,g.useState)(eR);function a(){i(()=>e.length?e.map(e=>q(e)?r:new et(t(e),e)):eR)}let o=ex({callback:a});return(0,v.Es)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(eH),eJ=eB(S,{transform:{x:F.x-eG.x,y:F.y-eG.y,scaleX:1,scaleY:1},activatorEvent:ei,active:ee,activeNodeRect:eE,containerNodeRect:eN,draggingNodeRect:eX,over:eP.current.over,overlayNodeRect:eF.rect,scrollableAncestors:eH,scrollableAncestorRects:eq,windowRect:eV}),eQ=ep?(0,v.WQ)(ep,F):null,eZ=function(e){let[t,n]=(0,g.useState)(null),r=(0,g.useRef)(e),l=(0,g.useCallback)(e=>{let t=_(e.target);t&&n(e=>e?(e.set(t,H(t)),new Map(e)):null)},[]);return(0,g.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=_(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,H(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=_(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,g.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,v.WQ)(e,t),R):Z(e):R,[e,t])}(eH),e$=eS(eZ),e0=eS(eZ,[eE]),e1=(0,v.WQ)(eJ,e$),e2=eX?Y(eX,eJ):null,e5=ee&&e2?E({active:ee,collisionRect:e2,droppableRects:ed,droppableContainers:es,pointerCoordinates:eQ}):null,e6=N(e5,"id"),[e9,e4]=(0,g.useState)(null),e8=(o=e_?eJ:(0,v.WQ)(eJ,e0),u=null!=(a=null==e9?void 0:e9.rect)?a:null,{...o,scaleX:u&&eE?u.width/eE.width:1,scaleY:u&&eE?u.height/eE.height:1}),e3=(0,g.useRef)(null),e7=(0,g.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==en.current)return;let i=B.get(en.current);if(!i)return;let a=e.nativeEvent,o=new n({active:en.current,activeNode:i,event:a,options:r,context:eP,onAbort(e){if(!B.get(e))return;let{onDragAbort:t}=eo.current,n={id:e};null==t||t(n),T({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!B.get(e))return;let{onDragPending:l}=eo.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),T({type:"onDragPending",event:i})},onStart(e){let t=en.current;if(null==t)return;let n=B.get(t);if(!n)return;let{onDragStart:r}=eo.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:$}};(0,p.unstable_batchedUpdates)(()=>{null==r||r(i),A(h.Initializing),L({type:l.DragStart,initialCoordinates:e,active:t}),T({type:"onDragStart",event:i}),el(e3.current),ea(a)})},onMove(e){L({type:l.DragMove,coordinates:e})},onEnd:u(l.DragEnd),onCancel:u(l.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=eP.current,o=null;if(t&&i){let{cancelDrop:u}=eo.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof u&&await Promise.resolve(u(o))&&(e=l.DragCancel)}en.current=null,(0,p.unstable_batchedUpdates)(()=>{L({type:e}),A(h.Uninitialized),e4(null),el(null),ea(null),e3.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=eo.current[t];null==e||e(o),T({type:t,event:o})}})}}e3.current=o},[B]),te=(0,g.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=B.get(r);null!==en.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},en.current=r,e7(n,t))},[B,e7]),tt=(0,g.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:te(e.handler,t)}))]},[]),[x,te]);(0,g.useEffect)(()=>{if(!v.Sw)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),(0,v.Es)(()=>{eE&&P===h.Initializing&&A(h.Initialized)},[eE,P]),(0,g.useEffect)(()=>{let{onDragMove:e}=eo.current,{active:t,activatorEvent:n,collisions:r,over:l}=eP.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e1.x,y:e1.y},over:l};(0,p.unstable_batchedUpdates)(()=>{null==e||e(i),T({type:"onDragMove",event:i})})},[e1.x,e1.y]),(0,g.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eP.current;if(!e||null==en.current||!t||!l)return;let{onDragOver:i}=eo.current,a=r.get(e6),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,p.unstable_batchedUpdates)(()=>{e4(o),null==i||i(u),T({type:"onDragOver",event:u})})},[e6]),(0,v.Es)(()=>{eP.current={activatorEvent:ei,active:ee,activeNode:eg,collisionRect:e2,collisions:e5,droppableRects:ed,draggableNodes:B,draggingNode:eU,draggingNodeRect:eX,droppableContainers:G,over:e9,scrollableAncestors:eH,scrollAdjustedTranslate:e1},$.current={initial:eX,translated:e2}},[ee,eg,e5,e2,B,eU,eX,ed,G,e9,eH,e1]),function(e){let{acceleration:t,activator:n=s.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:u=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:f,scrollableAncestorRects:h,delta:p,threshold:m}=e,y=function(e){let{delta:t,disabled:n}=e,r=(0,v.ZC)(t);return(0,v.KG)(e=>{if(n||!r||!e)return ey;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:p,disabled:!a}),[b,w]=(0,v.$$)(),x=(0,g.useRef)({x:0,y:0}),E=(0,g.useRef)({x:0,y:0}),C=(0,g.useMemo)(()=>{switch(n){case s.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case s.DraggableRect:return l}},[n,l,d]),D=(0,g.useRef)(null),S=(0,g.useCallback)(()=>{let e=D.current;if(!e)return;let t=x.current.x*E.current.x,n=x.current.y*E.current.y;e.scrollBy(t,n)},[]),M=(0,g.useMemo)(()=>u===c.TreeOrder?[...f].reverse():f,[u,f]);(0,g.useEffect)(()=>{if(!a||!f.length||!C)return void w();for(let e of M){if((null==r?void 0:r(e))===!1)continue;let n=h[f.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===l&&(l=Q);let{isTop:c,isBottom:d,isLeft:f,isRight:h}=J(e),g={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!c&&a<=t.top+v.height?(g.y=i.Backward,p.y=r*Math.abs((t.top+v.height-a)/v.height)):!d&&s>=t.bottom-v.height&&(g.y=i.Forward,p.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!h&&u>=t.right-v.width?(g.x=i.Forward,p.x=r*Math.abs((t.right-v.width-u)/v.width)):!f&&o<=t.left+v.width&&(g.x=i.Backward,p.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:g,speed:p}}(e,n,C,t,m);for(let e of["x","y"])y[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){w(),D.current=e,b(S,o),x.current=a,E.current=l;return}}x.current={x:0,y:0},E.current={x:0,y:0},w()},[t,S,r,w,a,o,JSON.stringify(C),JSON.stringify(y),b,f,M,h,JSON.stringify(m)])}({...ev,delta:F,draggingRect:e2,pointerCoordinates:eQ,scrollableAncestors:eH,scrollableAncestorRects:eq});let tn=(0,g.useMemo)(()=>({active:ee,activeNode:eg,activeNodeRect:eE,activatorEvent:ei,collisions:e5,containerNodeRect:eN,dragOverlay:eF,draggableNodes:B,droppableContainers:G,droppableRects:ed,over:e9,measureDroppableContainers:ef,scrollableAncestors:eH,scrollableAncestorRects:eq,measuringConfiguration:ec,measuringScheduled:eh,windowRect:eV}),[ee,eg,eE,ei,e5,eN,eF,B,G,ed,e9,ef,eH,eq,ec,eh,eV]),tr=(0,g.useMemo)(()=>({activatorEvent:ei,activators:tt,active:ee,activeNodeRect:eE,ariaDescribedById:{draggable:eu},dispatch:L,draggableNodes:B,over:e9,measureDroppableContainers:ef}),[ei,tt,ee,eE,L,eu,B,e9,ef]);return g.createElement(w.Provider,{value:O},g.createElement(eI.Provider,{value:tr},g.createElement(ez.Provider,{value:tn},g.createElement(eK.Provider,{value:e8},b)),g.createElement(eW,{disabled:(null==m?void 0:m.restoreFocus)===!1})),g.createElement(C,{...m,hiddenTextDescribedById:eu}))}),eU=(0,g.createContext)(null),eX="button";function e_(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,v.YG)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:f}=(0,g.useContext)(eI),{role:h=eX,roleDescription:p="draggable",tabIndex:m=0}=null!=l?l:{},y=(null==u?void 0:u.id)===t,b=(0,g.useContext)(y?eK:eU),[w,x]=(0,v.lk)(),[E,C]=(0,v.lk)(),D=(0,g.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),S=(0,v.YN)(n);return(0,v.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:E,data:S}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,g.useMemo)(()=>({role:h,tabIndex:m,"aria-disabled":r,"aria-pressed":!!y&&h===eX||void 0,"aria-roledescription":p,"aria-describedby":c.draggable}),[r,h,m,y,p,c.draggable]),isDragging:y,listeners:r?void 0:D,node:w,over:f,setNodeRef:x,setActivatorNodeRef:C,transform:b}}function eG(){return(0,g.useContext)(ez)}let eV={timeout:25};function eH(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=(0,v.YG)("Droppable"),{active:o,dispatch:u,over:s,measureDroppableContainers:c}=(0,g.useContext)(eI),d=(0,g.useRef)({disabled:n}),f=(0,g.useRef)(!1),h=(0,g.useRef)(null),p=(0,g.useRef)(null),{disabled:m,updateMeasurementsFor:y,timeout:b}={...eV,...i},w=(0,v.YN)(null!=y?y:r),x=ex({callback:(0,g.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),p.current=null},b)},[b]),disabled:m||!o}),E=(0,g.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),f.current=!1),e&&x.observe(e))},[x]),[C,D]=(0,v.lk)(E),S=(0,v.YN)(t);return(0,g.useEffect)(()=>{x&&C.current&&(x.disconnect(),f.current=!1,x.observe(C.current))},[C,x]),(0,g.useEffect)(()=>(u({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:C,rect:h,data:S}}),()=>u({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,g.useEffect)(()=>{n!==d.current.disabled&&(u({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)},[r,a,n,u]),{active:o,rect:h,isOver:(null==s?void 0:s.id)===r,node:C,over:s,setNodeRef:D}}function eq(e){let{animation:t,children:n}=e,[r,l]=(0,g.useState)(null),[i,a]=(0,g.useState)(null),o=(0,v.ZC)(n);return n||r||!o||l(o),(0,v.Es)(()=>{if(!i)return;let e=null==r?void 0:r.key,n=null==r?void 0:r.props.id;if(null==e||null==n)return void l(null);Promise.resolve(t(n,i)).then(()=>{l(null)})},[t,r,i]),g.createElement(g.Fragment,null,n,r?(0,g.cloneElement)(r,{ref:a}):null)}let eJ={x:0,y:0,scaleX:1,scaleY:1};function eQ(e){let{children:t}=e;return g.createElement(eI.Provider,{value:eA},g.createElement(eK.Provider,{value:eJ},t))}let eZ={position:"fixed",touchAction:"none"},e$=e=>(0,v.kx)(e)?"transform 250ms ease":void 0,e0=(0,g.forwardRef)((e,t)=>{let{as:n,activatorEvent:r,adjustScale:l,children:i,className:a,rect:o,style:u,transform:s,transition:c=e$}=e;if(!o)return null;let d=l?s:{...s,scaleX:1,scaleY:1},f={...eZ,width:o.width,height:o.height,top:o.top,left:o.left,transform:v.Ks.Transform.toString(d),transformOrigin:l&&r?function(e,t){let n=(0,v.e_)(e);if(!n)return"0 0";let r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}(r,o):void 0,transition:"function"==typeof c?c(r):c,...u};return g.createElement(n,{className:a,style:f,ref:t},i)}),e1={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:n}}=e;return[{transform:v.Ks.Transform.toString(t)},{transform:v.Ks.Transform.toString(n)}]},sideEffects:(r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}})},e2=0,e5=g.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:l,transition:i,modifiers:a,wrapperElement:o="div",className:u,zIndex:s=999}=e,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:h,draggableNodes:p,droppableContainers:m,dragOverlay:y,over:b,measuringConfiguration:w,scrollableAncestors:x,scrollableAncestorRects:E,windowRect:C}=eG(),D=(0,g.useContext)(eK),S=function(e){return(0,g.useMemo)(()=>{if(null!=e)return++e2},[e])}(null==d?void 0:d.id),M=eB(a,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:h,draggingNodeRect:y.rect,over:b,overlayNodeRect:y.rect,scrollableAncestors:x,scrollableAncestorRects:E,transform:D,windowRect:C}),R=ew(f),k=function(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:l}=e;return(0,v._q)((e,i)=>{if(null===t)return;let a=n.get(e);if(!a)return;let o=a.node.current;if(!o)return;let u=ek(i);if(!u)return;let{transform:s}=(0,v.zk)(i).getComputedStyle(i),c=W(s);if(!c)return;let d="function"==typeof t?t:function(e){let{duration:t,easing:n,sideEffects:r,keyframes:l}={...e1,...e};return e=>{let{active:i,dragOverlay:a,transform:o,...u}=e;if(!t)return;let s={x:a.rect.left-i.rect.left,y:a.rect.top-i.rect.top},c={scaleX:1!==o.scaleX?i.rect.width*o.scaleX/a.rect.width:1,scaleY:1!==o.scaleY?i.rect.height*o.scaleY/a.rect.height:1},d={x:o.x-s.x,y:o.y-s.y,...c},f=l({...u,active:i,dragOverlay:a,transform:{initial:o,final:d}}),[h]=f,g=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(g))return;let p=null==r?void 0:r({active:i,dragOverlay:a,...u}),v=a.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise(e=>{v.onfinish=()=>{null==p||p(),e()}})}}(t);return $(o,l.draggable.measure),d({active:{id:e,data:a.data,node:o,rect:l.draggable.measure(o)},draggableNodes:n,dragOverlay:{node:i,rect:l.dragOverlay.measure(u)},droppableContainers:r,measuringConfiguration:l,transform:c})})}({config:r,draggableNodes:p,droppableContainers:m,measuringConfiguration:w}),L=R?y.setRef:void 0;return g.createElement(eQ,null,g.createElement(eq,{animation:k},d&&S?g.createElement(e0,{key:S,id:d.id,ref:L,as:o,activatorEvent:c,adjustScale:t,className:u,transition:i,rect:R,style:{zIndex:s,...l},transform:M},n):null))})},78266:(e,t,n)=>{n.d(t,{$$:()=>p,Es:()=>h,KG:()=>m,Ks:()=>R,Ll:()=>o,Re:()=>D,Sw:()=>i,TW:()=>f,WQ:()=>C,YG:()=>x,YN:()=>v,ZC:()=>b,_q:()=>g,ag:()=>L,e_:()=>M,jn:()=>l,kx:()=>S,l6:()=>a,lk:()=>y,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(12115);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function f(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let h=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return h(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return h(()=>{n.current!==e&&(n.current=e)},t),n}function m(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function y(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function b(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function E(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let C=E(1),D=E(-1);function S(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function M(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let R=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[R.Translate.toString(e),R.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),k="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function L(e){return e.matches(k)?e:e.querySelector(k)}}}]);