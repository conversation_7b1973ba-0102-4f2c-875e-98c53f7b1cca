(()=>{var e={};e.id=5883,e.ids=[5883],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\web-app\\\\dukancard\\\\app\\\\(main)\\\\support\\\\SupportPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx","default")},5278:(e,t,a)=>{Promise.resolve().then(a.bind(a,4780))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19080:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19559:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(37413);a(61120);var s=a(14890),i=a(60644),n=a(11637),o=a(95006),d=a(92506),l=a(46501),c=a(21886),u=a(23392);function p({children:e}){return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(u.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300",children:[(0,r.jsx)(s.default,{}),(0,r.jsx)("main",{className:"flex-1 pt-24 pb-20 md:pb-0",children:e}),(0,r.jsx)(i.default,{}),(0,r.jsx)(o.default,{}),(0,r.jsx)(n.default,{}),(0,r.jsx)(d.default,{}),(0,r.jsx)(l.default,{}),(0,r.jsx)(c.default,{excludePaths:["/dashboard"]})]})})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33521:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),o=a(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);a.d(t,d);let l={children:["",{children:["(main)",{children:["support",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,52212)),"C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,19559)),"C:\\web-app\\dukancard\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\web-app\\dukancard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,90253))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(main)/support/page",pathname:"/support",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33872:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38429:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(60687),s=a(96241),i=a(77882),n=a(43210),o=a(18265);function d({title:e,highlightWords:t=[],className:a,subtitle:d,align:l="center",size:c="large"}){let u=(0,n.useRef)(null),p=(0,o.W)(u,{once:!0,amount:.3}),m=e.split(" ");return(0,r.jsxs)("div",{className:(0,s.cn)({left:"text-left",center:"text-center",right:"text-right"}[l],a),children:[(0,r.jsx)("h2",{ref:u,className:(0,s.cn)({small:"text-2xl md:text-3xl lg:text-4xl",medium:"text-3xl md:text-4xl lg:text-5xl",large:"text-4xl md:text-5xl lg:text-6xl"}[c],"font-bold text-foreground tracking-tight leading-tight"),children:m.map((e,a)=>{let s=t.some(t=>e.toLowerCase().includes(t.toLowerCase())),n=.1+.05*a;return(0,r.jsx)("span",{className:"inline-block mr-[0.3em]",children:s?(0,r.jsxs)(i.P.span,{className:"inline-block relative text-[var(--brand-gold)]",initial:{opacity:0,y:20},animate:p?{opacity:1,y:0}:{},transition:{duration:.5,delay:n},children:[e,(0,r.jsx)(i.P.span,{className:"absolute bottom-0 left-0 h-1 bg-[var(--brand-gold)] rounded-full",initial:{width:0},animate:p?{width:"100%"}:{},transition:{duration:.5,delay:.5+.05*a}}),(0,r.jsx)(i.P.span,{className:"absolute inset-0 rounded-lg -z-10 bg-[var(--brand-gold)]/20",animate:{opacity:[.2,.3,.2]},transition:{duration:3,repeat:1/0,repeatType:"reverse"}})]}):(0,r.jsx)(i.P.span,{className:"inline-block",initial:{opacity:0,y:20},animate:p?{opacity:1,y:0}:{},transition:{duration:.5,delay:n},children:e})},a)})}),d&&(0,r.jsxs)(i.P.p,{className:"text-lg md:text-xl text-muted-foreground mt-4 max-w-3xl mx-auto",initial:{opacity:0,y:10},animate:p?{opacity:1,y:0}:{},transition:{duration:.5,delay:.05*m.length+.2},children:[d,(0,r.jsx)(i.P.span,{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 dark:via-white/5 to-transparent",initial:{x:"-100%"},animate:p?{x:"100%"}:{},transition:{duration:1.5,delay:.05*m.length+.5,ease:"easeInOut"}})]})]})}},41550:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},48340:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},52212:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o,generateMetadata:()=>n});var r=a(37413),s=a(4780),i=a(54670);async function n(){let e="Support Center",t="Get help with Dukancard. Find answers to frequently asked questions and contact our support team for assistance with your digital business card.",a="http://localhost:3000",r=`${a}/support`,s=`${a}/opengraph-image.png`;return{title:e,description:t,keywords:["Dukancard support","digital business card help","Dukancard FAQ","Dukancard customer service","business card troubleshooting","Dukancard contact"],alternates:{canonical:"/support"},openGraph:{title:e,description:t,url:r,siteName:i.C.name,type:"website",locale:"en_IN",images:[{url:s,width:1200,height:630,alt:`${i.C.name} Support Center`}]},twitter:{card:"summary_large_image",title:e,description:t,images:[s]},other:{"application-ld+json":JSON.stringify({"@context":"https://schema.org","@type":"WebPage",name:e,description:t,url:r,isPartOf:{"@type":"WebSite",name:i.C.name,url:a}})}}}function o(){return(0,r.jsx)(s.default,{})}},53411:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},54670:(e,t,a)=>{"use strict";a.d(t,{C:()=>r});let r={name:"Dukancard",description:"Create and share digital business cards, showcase products, and connect with customers.",url:"https://dukancard.in",ogImage:"https://dukancard.in/opengraph-image.png",contact:{email:"<EMAIL>",phone:"+91 8458060663",address:{street:"Bisra Road",city:"Rourkela",state:"Odisha",postalCode:"769001",country:"India",full:"Bisra Road, Rourkela, Odisha - 769001"},hours:"Monday - Friday: 9:00 AM - 6:00 PM"},legal:{privacyPolicy:"/privacy",termsOfService:"/terms",refundPolicy:"/refund"},support:{email:"<EMAIL>",phone:"+91 8458060663",helpCenter:"/support"},advertising:{email:"<EMAIL>",phone:"+************",page:"/advertise"}}},55192:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>c});var r=a(60687);a(43210);var s=a(96241);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64446:(e,t,a)=>{"use strict";a.d(t,{default:()=>B});var r=a(60687),s=a(43210),i=a(77882),n=a(41312),o=a(85778),d=a(19080),l=a(53411),c=a(93613),u=a(84027),p=a(99270),m=a(70334),x=a(41550),h=a(48340),g=a(33872),b=a(68988),y=a(24934),v=a(55192),f=a(32688),w=a(85814),j=a.n(w),N=a(18265),k=a(88920),P=a(65668),C=a(78272),A=a(38429),q=a(96241);function S({faqItems:e,faqCategories:t,searchQuery:a,activeCategory:n,onCategoryChange:o}){let d=(0,s.useRef)(null),l=(0,N.W)(d,{once:!0,amount:.2}),[c,u]=(0,s.useState)(null),p=e.filter(e=>{let t=e.question.toLowerCase().includes(a.toLowerCase())||e.answer.toLowerCase().includes(a.toLowerCase()),r=n===e.category;return t&&r}),m=e=>{u(c===e?null:e)},x={hidden:{opacity:0,y:20},visible:e=>({opacity:1,y:0,transition:{duration:.5,delay:.1*e,ease:"easeOut"}})};return(0,r.jsx)("section",{ref:d,className:"py-16 px-4 md:px-6 lg:px-8 relative overflow-hidden",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("div",{className:"text-center mb-12",children:(0,r.jsx)(A.A,{title:"Frequently Asked Questions",highlightWords:["Questions"],subtitle:"Find quick answers to common questions about Dukancard",size:"medium"})}),(0,r.jsx)(i.P.div,{className:"absolute top-20 left-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5",initial:{opacity:0,scale:.5,rotate:-10},animate:l?{opacity:1,scale:1,rotate:0}:{},transition:{duration:.7,delay:.2},children:(0,r.jsx)(P.A,{size:120})}),(0,r.jsx)(i.P.div,{className:"absolute top-1/3 right-10 text-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]/5",initial:{opacity:0,scale:.5,rotate:10},animate:l?{opacity:1,scale:1,rotate:0}:{},transition:{duration:.7,delay:.4},children:(0,r.jsx)(P.A,{size:80})}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:l?{opacity:1,y:0}:{},transition:{duration:.5,delay:.3},className:"relative z-10 mb-8",children:(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-2 p-2 bg-muted/30 rounded-2xl backdrop-blur-sm border border-border/50",children:t.map((e,t)=>(0,r.jsxs)(i.P.button,{onClick:()=>o(e.id),className:(0,q.cn)("px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 relative overflow-hidden",n===e.id?"text-[var(--brand-gold-foreground)] shadow-lg":"text-muted-foreground hover:text-foreground hover:bg-muted/50"),whileHover:{scale:1.02},whileTap:{scale:.98},initial:{opacity:0,y:10},animate:l?{opacity:1,y:0}:{},transition:{duration:.3,delay:.4+.05*t},children:[n===e.id&&(0,r.jsx)(i.P.div,{className:"absolute inset-0 bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] rounded-xl",layoutId:"activeTab",transition:{type:"spring",bounce:.2,duration:.6}}),(0,r.jsx)("span",{className:"relative z-10",children:e.label})]},e.id))})}),(0,r.jsx)(k.N,{mode:"wait",children:(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},className:"relative z-10",children:p.length>0?(0,r.jsx)("div",{className:"space-y-4",children:p.map((e,t)=>(0,r.jsxs)(i.P.div,{variants:x,custom:t,initial:"hidden",animate:l?"visible":"hidden",className:"border border-border rounded-lg overflow-hidden bg-card/50 backdrop-blur-sm",children:[(0,r.jsxs)("button",{onClick:()=>m(t),className:(0,q.cn)("flex items-center justify-between w-full p-5 text-left transition-colors",c===t?"bg-muted/50":"hover:bg-muted/30"),children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-foreground pr-4",children:e.question}),(0,r.jsx)(i.P.div,{animate:{rotate:180*(c===t)},transition:{duration:.3},className:(0,q.cn)("flex-shrink-0 ml-2 p-1 rounded-full",c===t?"bg-[var(--brand-gold)]/20 text-[var(--brand-gold)]":"text-muted-foreground"),children:(0,r.jsx)(C.A,{size:18})})]}),(0,r.jsx)(k.N,{children:c===t&&(0,r.jsx)(i.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},className:"overflow-hidden",children:(0,r.jsx)("div",{className:"p-5 pt-0 border-t border-border",children:(0,r.jsx)(i.P.p,{initial:{y:10,opacity:0},animate:{y:0,opacity:1},transition:{duration:.3,delay:.1},className:"text-muted-foreground leading-relaxed",children:e.answer})})})})]},e.id))}):(0,r.jsxs)(i.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},className:"text-center py-12 bg-card/30 rounded-2xl border border-border/50",children:[(0,r.jsx)(P.A,{className:"w-16 h-16 text-muted-foreground/50 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground text-lg mb-4",children:"No FAQs found matching your search criteria."}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground/70 mb-6",children:"Try adjusting your search terms or browse other categories."})]})},n)}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:l?{opacity:1,y:0}:{},transition:{duration:.5,delay:.8},className:"text-center mt-12 text-muted-foreground",children:(0,r.jsxs)("p",{children:["Still have questions? Contact our support team at"," ",(0,r.jsx)("span",{className:"text-[var(--brand-gold)] font-medium",children:"<EMAIL>"})]})})]})})}let z=[{id:"general",label:"General"},{id:"account",label:"Account"},{id:"business-card",label:"Business Card"},{id:"products",label:"Products"},{id:"billing",label:"Billing"}],I=[{id:1,question:"What is Dukancard?",answer:"Dukancard is a digital business card platform that helps small businesses, shops, and freelancers create an online presence. It allows you to showcase your products, services, and contact information in a professional digital format that can be shared via QR code or link.",category:"general"},{id:2,question:"How do I create a Dukancard account?",answer:"To create a Dukancard account, click on the 'Sign Up' button on our homepage. You'll need to provide your email address and create a password. After verifying your email, you can choose your role (business or customer) and start setting up your digital business card.",category:"account"},{id:3,question:"Can I customize my digital business card?",answer:"Yes, you can customize your digital business card with your business name, logo, contact information, address, bio, and social media links. You can also add products or services with descriptions, images, and pricing.",category:"business-card"},{id:4,question:"How many products can I add to my digital card?",answer:"The number of products you can add depends on your subscription plan. The Basic plan allows up to 10 active products, while higher-tier plans will offer more product slots when they become available.",category:"products"},{id:5,question:"How do I share my digital business card?",answer:"You can share your digital business card by sending your unique Dukancard URL (dukancard.in/your-business-name) or by sharing your QR code. The QR code can be displayed at your physical location or included in your marketing materials.",category:"business-card"},{id:6,question:"What payment methods do you accept?",answer:"We accept all major credit cards, debit cards, UPI, and PayPal. All payments are processed securely through our payment partners.",category:"billing"},{id:7,question:"Can I cancel my subscription anytime?",answer:"Yes, you can cancel your subscription at any time from your account settings. Your access will continue until the end of your current billing period.",category:"billing"},{id:8,question:"Is there a free trial available?",answer:"Yes, we offer the first month free for all plans for first-time users.",category:"billing"},{id:10,question:"Can I track how many people view my digital card?",answer:"Yes, our Basic Analytics feature allows you to track storefront views and contact button clicks (WhatsApp/Call). More advanced analytics features will be available in our upcoming premium plans.",category:"business-card"}],M=[{title:"Account & Billing",icon:(0,r.jsx)(n.A,{className:"w-8 h-8 text-[var(--brand-gold)]"}),description:"Manage your account, subscription, and billing information.",url:"/support/account-billing"},{title:"Business Card Setup",icon:(0,r.jsx)(o.A,{className:"w-8 h-8 text-[var(--brand-gold)]"}),description:"Learn how to create and customize your digital business card.",url:"/support/business-card-setup"},{title:"Product Management",icon:(0,r.jsx)(d.A,{className:"w-8 h-8 text-[var(--brand-gold)]"}),description:"Add, edit, and manage products in your digital storefront.",url:"/support/product-management"},{title:"Analytics & Insights",icon:(0,r.jsx)(l.A,{className:"w-8 h-8 text-[var(--brand-gold)]"}),description:"Understand your visitor data and engagement metrics.",url:"/support/analytics"},{title:"Technical Issues",icon:(0,r.jsx)(c.A,{className:"w-8 h-8 text-[var(--brand-gold)]"}),description:"Get help with technical problems and troubleshooting.",url:"/support/technical-issues"},{title:"Settings & Preferences",icon:(0,r.jsx)(u.A,{className:"w-8 h-8 text-[var(--brand-gold)]"}),description:"Configure your account settings and preferences.",url:"/support/settings"}];function B(){let[e,t]=(0,s.useState)(""),[a,n]=(0,s.useState)("general"),o={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5}}};return(0,r.jsxs)("div",{className:"bg-background min-h-screen",children:[(0,r.jsxs)("section",{className:"relative pt-24 md:pt-32 pb-16 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto",children:[(0,r.jsx)("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:(0,r.jsx)("div",{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-3/4 h-3/4 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/5 rounded-full blur-3xl"})}),(0,r.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,r.jsxs)(i.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6",children:["How Can We ",(0,r.jsx)("span",{className:"text-[var(--brand-gold)]",children:"Help You?"})]}),(0,r.jsx)(i.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"text-lg text-muted-foreground mb-8",children:"Find answers to common questions or get in touch with our support team."}),(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"relative max-w-xl mx-auto mb-12",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-5 w-5"}),(0,r.jsx)(b.p,{type:"text",placeholder:"Search for help...",className:"pl-10 h-12 bg-background border-border",value:e,onChange:e=>t(e.target.value)})]})]})]}),(0,r.jsxs)("section",{className:"py-12 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-foreground mb-4",children:"Browse Support Topics"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Select a topic to find detailed guides and solutions"})]}),(0,r.jsx)(i.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:M.map((e,t)=>(0,r.jsx)(i.P.div,{variants:o,children:(0,r.jsx)(j(),{href:e.url,className:"block h-full",children:(0,r.jsxs)(v.Zp,{className:"h-full bg-card border border-border hover:border-[var(--brand-gold)]/50 hover:shadow-md transition-all duration-300 cursor-pointer",children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)("div",{className:"bg-[var(--brand-gold)]/10 p-3 rounded-lg w-fit",children:e.icon}),(0,r.jsx)(v.ZB,{className:"text-xl mt-4",children:e.title}),(0,r.jsx)(v.BT,{className:"text-muted-foreground",children:e.description})]}),(0,r.jsx)(v.wL,{className:"pt-0",children:(0,r.jsxs)(y.$,{variant:"ghost",className:"p-0 hover:bg-transparent hover:text-[var(--brand-gold)] text-muted-foreground cursor-pointer",children:["Learn more ",(0,r.jsx)(m.A,{className:"ml-2 w-4 h-4"})]})})]})})},t))})]}),(0,r.jsx)(S,{faqItems:I,faqCategories:z,searchQuery:e,activeCategory:a,onCategoryChange:n}),(0,r.jsx)("section",{className:"py-16 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"bg-gradient-to-r from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/5 dark:from-[var(--brand-gold)]/20 dark:to-black/50 rounded-2xl p-8 md:p-12",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-foreground mb-4",children:"Still Need Help?"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Our support team is ready to assist you with any questions or issues"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)(v.Zp,{className:"bg-card border border-border hover:border-[var(--brand-gold)]/50 hover:shadow-md transition-all duration-300",children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)("div",{className:"bg-[var(--brand-gold)]/10 p-3 rounded-lg w-fit",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-[var(--brand-gold)]"})}),(0,r.jsx)(v.ZB,{className:"text-xl mt-4",children:"Email Support"}),(0,r.jsx)(v.BT,{className:"text-muted-foreground",children:"Send us an email and we'll respond within 24 hours"})]}),(0,r.jsx)(v.wL,{children:(0,r.jsx)(y.$,{asChild:!0,variant:"outline",className:"w-full border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 cursor-pointer",children:(0,r.jsx)(j(),{href:`mailto:${f.C.support.email}`,children:"Email Us"})})})]}),(0,r.jsxs)(v.Zp,{className:"bg-card border border-border hover:border-[var(--brand-gold)]/50 hover:shadow-md transition-all duration-300",children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)("div",{className:"bg-[var(--brand-gold)]/10 p-3 rounded-lg w-fit",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-[var(--brand-gold)]"})}),(0,r.jsx)(v.ZB,{className:"text-xl mt-4",children:"Phone Support"}),(0,r.jsx)(v.BT,{className:"text-muted-foreground",children:"Call us during business hours for immediate assistance"})]}),(0,r.jsx)(v.wL,{children:(0,r.jsx)(y.$,{asChild:!0,variant:"outline",className:"w-full border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 cursor-pointer",children:(0,r.jsx)(j(),{href:`tel:${f.C.support.phone}`,children:"Call Us"})})})]}),(0,r.jsxs)(v.Zp,{className:"bg-card border border-border hover:border-[var(--brand-gold)]/50 hover:shadow-md transition-all duration-300",children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)("div",{className:"bg-[var(--brand-gold)]/10 p-3 rounded-lg w-fit",children:(0,r.jsx)(g.A,{className:"w-6 h-6 text-[var(--brand-gold)]"})}),(0,r.jsx)(v.ZB,{className:"text-xl mt-4",children:"Help Center"}),(0,r.jsx)(v.BT,{className:"text-muted-foreground",children:"Browse our comprehensive knowledge base"})]}),(0,r.jsx)(v.wL,{})]})]})]})})]})}},68988:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var r=a(60687);a(43210);var s=a(96241);function i({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},73734:(e,t,a)=>{Promise.resolve().then(a.bind(a,64446))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84027:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,6724,2997,1107,7065,9389,3037,6177],()=>a(33521));module.exports=r})();