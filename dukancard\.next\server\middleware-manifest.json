{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LQe1NbuqwtgvamA3sJfyGyki6D/xlrCTDJmYSjuVnIM=", "__NEXT_PREVIEW_MODE_ID": "40a1da2e4e1cf4b5d50cba4fcf292eeb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b5aab994f4bc89b73df7d9c21d7425e0495450809048f0ea28cc55a2edd8511f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b0f8d1724c455cc450612e0c2e019c8334b84d433b654846609ecfdd57f74c8b"}}}, "sortedMiddleware": ["/"], "functions": {}}