"use strict";exports.id=2836,exports.ids=[2836],exports.modules={52676:(e,t,r)=>{r.d(t,{UC:()=>z,ZL:()=>W,bL:()=>q,l9:()=>U});var n=r(43210),l=r(70569),o=r(98599),a=r(11273),i=r(31355),u=r(1359),c=r(32547),s=r(19344),d=r(55509),f=r(25028),p=r(46059),v=r(3416),m=r(60687),h=Symbol("radix.slottable");function g(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===h}var b=r(65551),E=r(63376),y=r(11490),w="Popover",[x,C]=(0,a.A)(w,[d.Bk]),k=(0,d.Bk)(),[R,A]=x(w),S=e=>{let{__scopePopover:t,children:r,open:l,defaultOpen:o,onOpenChange:a,modal:i=!1}=e,u=k(t),c=n.useRef(null),[f,p]=n.useState(!1),[v,h]=(0,b.i)({prop:l,defaultProp:o??!1,onChange:a,caller:w});return(0,m.jsx)(d.bL,{...u,children:(0,m.jsx)(R,{scope:t,contentId:(0,s.B)(),triggerRef:c,open:v,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:f,onCustomAnchorAdd:n.useCallback(()=>p(!0),[]),onCustomAnchorRemove:n.useCallback(()=>p(!1),[]),modal:i,children:r})})};S.displayName=w;var I="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:r,...l}=e,o=A(I,r),a=k(r),{onCustomAnchorAdd:i,onCustomAnchorRemove:u}=o;return n.useEffect(()=>(i(),()=>u()),[i,u]),(0,m.jsx)(d.Mz,{...a,...l,ref:t})}).displayName=I;var P="PopoverTrigger",j=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=A(P,r),i=k(r),u=(0,o.s)(t,a.triggerRef),c=(0,m.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":K(a.open),...n,ref:u,onClick:(0,l.m)(e.onClick,a.onOpenToggle)});return a.hasCustomAnchor?c:(0,m.jsx)(d.Mz,{asChild:!0,...i,children:c})});j.displayName=P;var O="PopoverPortal",[D,M]=x(O,{forceMount:void 0}),F=e=>{let{__scopePopover:t,forceMount:r,children:n,container:l}=e,o=A(O,t);return(0,m.jsx)(D,{scope:t,forceMount:r,children:(0,m.jsx)(p.C,{present:r||o.open,children:(0,m.jsx)(f.Z,{asChild:!0,container:l,children:n})})})};F.displayName=O;var B="PopoverContent",N=n.forwardRef((e,t)=>{let r=M(B,e.__scopePopover),{forceMount:n=r.forceMount,...l}=e,o=A(B,e.__scopePopover);return(0,m.jsx)(p.C,{present:n||o.open,children:o.modal?(0,m.jsx)($,{...l,ref:t}):(0,m.jsx)(G,{...l,ref:t})})});N.displayName=B;var _=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var a;let e,i,u=(a=r,(i=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(i=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,o.t)(t,u):u),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,a=n.Children.toArray(l),i=a.find(g);if(i){let e=i.props.children,l=a.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,m.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,m.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}("PopoverContent.RemoveScroll"),$=n.forwardRef((e,t)=>{let r=A(B,e.__scopePopover),a=n.useRef(null),i=(0,o.s)(t,a),u=n.useRef(!1);return n.useEffect(()=>{let e=a.current;if(e)return(0,E.Eq)(e)},[]),(0,m.jsx)(y.A,{as:_,allowPinchZoom:!0,children:(0,m.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),u.current||r.triggerRef.current?.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;u.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),G=n.forwardRef((e,t)=>{let r=A(B,e.__scopePopover),l=n.useRef(!1),o=n.useRef(!1);return(0,m.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(l.current||r.triggerRef.current?.focus(),t.preventDefault()),l.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(l.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:o,disableOutsidePointerEvents:a,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:v,...h}=e,g=A(B,r),b=k(r);return(0,u.Oh)(),(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:o,children:(0,m.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:v,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:p,onDismiss:()=>g.onOpenChange(!1),children:(0,m.jsx)(d.UC,{"data-state":K(g.open),role:"dialog",id:g.contentId,...b,...h,ref:t,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),V="PopoverClose";function K(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=A(V,r);return(0,m.jsx)(v.sG.button,{type:"button",...n,ref:t,onClick:(0,l.m)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=V,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,l=k(r);return(0,m.jsx)(d.i3,{...l,...n,ref:t})}).displayName="PopoverArrow";var q=S,U=j,W=F,z=N},70965:(e,t,r)=>{r.d(t,{uB:()=>D});var n=/[\\\/_+.#"@\[\(\{&]/,l=/[\\\/_+.#"@\[\(\{&]/g,o=/[\s-]/,a=/[\s-]/g;function i(e){return e.toLowerCase().replace(a," ")}var u=r(10991),c=r(43210),s=r(3416),d=r(19344),f=r(98599),p='[cmdk-group=""]',v='[cmdk-group-items=""]',m='[cmdk-item=""]',h=`${m}:not([aria-disabled="true"])`,g="cmdk-item-select",b="data-value",E=(e,t,r)=>(function(e,t,r){return function e(t,r,i,u,c,s,d){if(s===r.length)return c===t.length?1:.99;var f=`${c},${s}`;if(void 0!==d[f])return d[f];for(var p,v,m,h,g=u.charAt(s),b=i.indexOf(g,c),E=0;b>=0;)(p=e(t,r,i,u,b+1,s+1,d))>E&&(b===c?p*=1:n.test(t.charAt(b-1))?(p*=.8,(m=t.slice(c,b-1).match(l))&&c>0&&(p*=Math.pow(.999,m.length))):o.test(t.charAt(b-1))?(p*=.9,(h=t.slice(c,b-1).match(a))&&c>0&&(p*=Math.pow(.999,h.length))):(p*=.17,c>0&&(p*=Math.pow(.999,b-c))),t.charAt(b)!==r.charAt(s)&&(p*=.9999)),(p<.1&&i.charAt(b-1)===u.charAt(s+1)||u.charAt(s+1)===u.charAt(s)&&i.charAt(b-1)!==u.charAt(s))&&.1*(v=e(t,r,i,u,b+1,s+2,d))>p&&(p=.1*v),p>E&&(E=p),b=i.indexOf(g,b+1);return d[f]=E,E}(e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,t,i(e),i(t),0,0,{})})(e,t,r),y=c.createContext(void 0),w=()=>c.useContext(y),x=c.createContext(void 0),C=()=>c.useContext(x),k=c.createContext(void 0),R=c.forwardRef((e,t)=>{let r=B(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=B(()=>new Set),l=B(()=>new Map),o=B(()=>new Map),a=B(()=>new Set),i=M(e),{label:u,children:f,value:w,onValueChange:C,filter:k,shouldFilter:R,loop:A,disablePointerSelection:S=!1,vimBindings:I=!0,...P}=e,j=(0,d.B)(),O=(0,d.B)(),D=(0,d.B)(),N=c.useRef(null),_=$();F(()=>{if(void 0!==w){let e=w.trim();r.current.value=e,V.emit()}},[w]),F(()=>{_(6,Z)},[]);let V=c.useMemo(()=>({subscribe:e=>(a.current.add(e),()=>a.current.delete(e)),snapshot:()=>r.current,setState:(e,t,n)=>{var l,o,a,u;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)z(),U(),_(1,W);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(D);e?e.focus():null==(l=document.getElementById(j))||l.focus()}if(_(7,()=>{var e;r.current.selectedItemId=null==(e=T())?void 0:e.id,V.emit()}),n||_(5,Z),(null==(o=i.current)?void 0:o.value)!==void 0){null==(u=(a=i.current).onValueChange)||u.call(a,null!=t?t:"");return}}V.emit()}},emit:()=>{a.current.forEach(e=>e())}}),[]),K=c.useMemo(()=>({value:(e,t,n)=>{var l;t!==(null==(l=o.current.get(e))?void 0:l.value)&&(o.current.set(e,{value:t,keywords:n}),r.current.filtered.items.set(e,q(t,n)),_(2,()=>{U(),V.emit()}))},item:(e,t)=>(n.current.add(e),t&&(l.current.has(t)?l.current.get(t).add(e):l.current.set(t,new Set([e]))),_(3,()=>{z(),U(),r.current.value||W(),V.emit()}),()=>{o.current.delete(e),n.current.delete(e),r.current.filtered.items.delete(e);let t=T();_(4,()=>{z(),(null==t?void 0:t.getAttribute("id"))===e&&W(),V.emit()})}),group:e=>(l.current.has(e)||l.current.set(e,new Set),()=>{o.current.delete(e),l.current.delete(e)}),filter:()=>i.current.shouldFilter,label:u||e["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:j,inputId:D,labelId:O,listInnerRef:N}),[]);function q(e,t){var n,l;let o=null!=(l=null==(n=i.current)?void 0:n.filter)?l:E;return e?o(e,r.current.search,t):0}function U(){if(!r.current.search||!1===i.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let n=l.current.get(r),o=0;n.forEach(t=>{o=Math.max(e.get(t),o)}),t.push([r,o])});let n=N.current;H().sort((t,r)=>{var n,l;let o=t.getAttribute("id"),a=r.getAttribute("id");return(null!=(n=e.get(a))?n:0)-(null!=(l=e.get(o))?l:0)}).forEach(e=>{let t=e.closest(v);t?t.appendChild(e.parentElement===t?e:e.closest(`${v} > *`)):n.appendChild(e.parentElement===n?e:e.closest(`${v} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=N.current)?void 0:t.querySelector(`${p}[${b}="${encodeURIComponent(e[0])}"]`);null==r||r.parentElement.appendChild(r)})}function W(){let e=H().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(b);V.setState("value",t||void 0)}function z(){var e,t,a,u;if(!r.current.search||!1===i.current.shouldFilter){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let c=0;for(let l of n.current){let n=q(null!=(t=null==(e=o.current.get(l))?void 0:e.value)?t:"",null!=(u=null==(a=o.current.get(l))?void 0:a.keywords)?u:[]);r.current.filtered.items.set(l,n),n>0&&c++}for(let[e,t]of l.current)for(let n of t)if(r.current.filtered.items.get(n)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=c}function Z(){var e,t,r;let n=T();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(p))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function T(){var e;return null==(e=N.current)?void 0:e.querySelector(`${m}[aria-selected="true"]`)}function H(){var e;return Array.from((null==(e=N.current)?void 0:e.querySelectorAll(h))||[])}function J(e){let t=H()[e];t&&V.setState("value",t.getAttribute(b))}function Q(e){var t;let r=T(),n=H(),l=n.findIndex(e=>e===r),o=n[l+e];null!=(t=i.current)&&t.loop&&(o=l+e<0?n[n.length-1]:l+e===n.length?n[0]:n[l+e]),o&&V.setState("value",o.getAttribute(b))}function X(e){let t=T(),r=null==t?void 0:t.closest(p),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,p):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,p))?void 0:r.querySelector(h);n?V.setState("value",n.getAttribute(b)):Q(e)}let Y=()=>J(H().length-1),ee=e=>{e.preventDefault(),e.metaKey?Y():e.altKey?X(1):Q(1)},et=e=>{e.preventDefault(),e.metaKey?J(0):e.altKey?X(-1):Q(-1)};return c.createElement(s.sG.div,{ref:t,tabIndex:-1,...P,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=P.onKeyDown)||t.call(P,e);let r=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||r))switch(e.key){case"n":case"j":I&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":I&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),J(0);break;case"End":e.preventDefault(),Y();break;case"Enter":{e.preventDefault();let t=T();if(t){let e=new Event(g);t.dispatchEvent(e)}}}}},c.createElement("label",{"cmdk-label":"",htmlFor:K.inputId,id:K.labelId,style:L},u),G(e,e=>c.createElement(x.Provider,{value:V},c.createElement(y.Provider,{value:K},e))))}),A=c.forwardRef((e,t)=>{var r,n;let l=(0,d.B)(),o=c.useRef(null),a=c.useContext(k),i=w(),u=M(e),p=null!=(n=null==(r=u.current)?void 0:r.forceMount)?n:null==a?void 0:a.forceMount;F(()=>{if(!p)return i.item(l,null==a?void 0:a.id)},[p]);let v=_(l,o,[e.value,e.children,o],e.keywords),m=C(),h=N(e=>e.value&&e.value===v.current),b=N(e=>!!p||!1===i.filter()||!e.search||e.filtered.items.get(l)>0);function E(){var e,t;y(),null==(t=(e=u.current).onSelect)||t.call(e,v.current)}function y(){m.setState("value",v.current,!0)}if(c.useEffect(()=>{let t=o.current;if(!(!t||e.disabled))return t.addEventListener(g,E),()=>t.removeEventListener(g,E)},[b,e.onSelect,e.disabled]),!b)return null;let{disabled:x,value:R,onSelect:A,forceMount:S,keywords:I,...P}=e;return c.createElement(s.sG.div,{ref:(0,f.t)(o,t),...P,id:l,"cmdk-item":"",role:"option","aria-disabled":!!x,"aria-selected":!!h,"data-disabled":!!x,"data-selected":!!h,onPointerMove:x||i.getDisablePointerSelection()?void 0:y,onClick:x?void 0:E},e.children)}),S=c.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:l,...o}=e,a=(0,d.B)(),i=c.useRef(null),u=c.useRef(null),p=(0,d.B)(),v=w(),m=N(e=>!!l||!1===v.filter()||!e.search||e.filtered.groups.has(a));F(()=>v.group(a),[]),_(a,i,[e.value,e.heading,u]);let h=c.useMemo(()=>({id:a,forceMount:l}),[l]);return c.createElement(s.sG.div,{ref:(0,f.t)(i,t),...o,"cmdk-group":"",role:"presentation",hidden:!m||void 0},r&&c.createElement("div",{ref:u,"cmdk-group-heading":"","aria-hidden":!0,id:p},r),G(e,e=>c.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?p:void 0},c.createElement(k.Provider,{value:h},e))))}),I=c.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,l=c.useRef(null),o=N(e=>!e.search);return r||o?c.createElement(s.sG.div,{ref:(0,f.t)(l,t),...n,"cmdk-separator":"",role:"separator"}):null}),P=c.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,l=null!=e.value,o=C(),a=N(e=>e.search),i=N(e=>e.selectedItemId),u=w();return c.useEffect(()=>{null!=e.value&&o.setState("search",e.value)},[e.value]),c.createElement(s.sG.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":u.listId,"aria-labelledby":u.labelId,"aria-activedescendant":i,id:u.inputId,type:"text",value:l?e.value:a,onChange:e=>{l||o.setState("search",e.target.value),null==r||r(e.target.value)}})}),j=c.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...l}=e,o=c.useRef(null),a=c.useRef(null),i=N(e=>e.selectedItemId),u=w();return c.useEffect(()=>{if(a.current&&o.current){let e=a.current,t=o.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),c.createElement(s.sG.div,{ref:(0,f.t)(o,t),...l,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":i,"aria-label":n,id:u.listId},G(e,e=>c.createElement("div",{ref:(0,f.t)(a,u.listInnerRef),"cmdk-list-sizer":""},e)))}),O=c.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:l,contentClassName:o,container:a,...i}=e;return c.createElement(u.bL,{open:r,onOpenChange:n},c.createElement(u.ZL,{container:a},c.createElement(u.hJ,{"cmdk-overlay":"",className:l}),c.createElement(u.UC,{"aria-label":e.label,"cmdk-dialog":"",className:o},c.createElement(R,{ref:t,...i}))))}),D=Object.assign(R,{List:j,Item:A,Input:P,Group:S,Separator:I,Dialog:O,Empty:c.forwardRef((e,t)=>N(e=>0===e.filtered.count)?c.createElement(s.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:c.forwardRef((e,t)=>{let{progress:r,children:n,label:l="Loading...",...o}=e;return c.createElement(s.sG.div,{ref:t,...o,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":l},G(e,e=>c.createElement("div",{"aria-hidden":!0},e)))})});function M(e){let t=c.useRef(e);return F(()=>{t.current=e}),t}var F=c.useEffect;function B(e){let t=c.useRef();return void 0===t.current&&(t.current=e()),t}function N(e){let t=C(),r=()=>e(t.snapshot());return c.useSyncExternalStore(t.subscribe,r,r)}function _(e,t,r,n=[]){let l=c.useRef(),o=w();return F(()=>{var a;let i=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():l.current}})(),u=n.map(e=>e.trim());o.value(e,i,u),null==(a=t.current)||a.setAttribute(b,i),l.current=i}),l}var $=()=>{let[e,t]=c.useState(),r=B(()=>new Map);return F(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}};function G({asChild:e,children:t},r){let n;return e&&c.isValidElement(t)?c.cloneElement("function"==typeof(n=t.type)?n(t.props):"render"in n?n.render(t.props):t,{ref:t.ref},r(t.props.children)):r(t)}var L={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}}};