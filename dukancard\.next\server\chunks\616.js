"use strict";exports.id=616,exports.ids=[616,5453,8235],exports.modules={8235:(e,r,o)=>{o.d(r,{createCustomer:()=>t,findCustomerByEmail:()=>n});var s=o(95453);async function t(e){try{let r=(0,s.bG)(),o=await fetch(`${s.ST.replace("/v2","/v1")}/customers`,{method:"POST",headers:{...r,"Content-Type":"application/json"},body:JSON.stringify(e)}),t=await o.json();if(!o.ok)return console.error("[RAZORPAY_ERROR] Error creating customer:",t),{success:!1,error:t};return{success:!0,data:t}}catch(e){return console.error("[RAZORPAY_ERROR] Exception creating customer:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function c(e={}){try{let r=(0,s.bG)(),o=Object.entries(e).map(([e,r])=>`${e}=${encodeURIComponent(r)}`).join("&"),t=`${s.ST.replace("/v2","/v1")}/customers${o?`?${o}`:""}`,c=await fetch(t,{method:"GET",headers:r}),n=await c.json();if(!c.ok)return console.error("[RAZORPAY_ERROR] Error fetching customers:",n),{success:!1,error:n};return{success:!0,data:n}}catch(e){return console.error("[RAZORPAY_ERROR] Exception fetching customers:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}async function n(e){try{let r=await c({count:100});if(!r.success)return{success:!1,error:r.error};if(!r.data)return{success:!1,error:{message:"Invalid response format from Razorpay",code:"INVALID_RESPONSE",type:"ERROR"}};let o=r.data.items.find(r=>r.email.toLowerCase()===e.toLowerCase());if(o)return{success:!0,data:o};return{success:!1,error:{message:`No customer found with email: ${e}`,code:"CUSTOMER_NOT_FOUND",type:"NOT_FOUND"}}}catch(e){return console.error("[RAZORPAY_ERROR] Exception finding customer by email:",e),{success:!1,error:{message:e instanceof Error?e.message:"Unknown error occurred",code:"UNKNOWN_ERROR",type:"EXCEPTION"}}}}},95453:(e,r,o)=>{o.d(r,{ST:()=>c,bG:()=>a,t6:()=>i});var s=o(55511),t=o.n(s);let c="https://api.razorpay.com/v2",n=()=>{let e,r;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",r=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!r)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!r,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:r}},a=()=>{let{keyId:e,keySecret:r}=n(),o=Buffer.from(`${e}:${r}`).toString("base64");return{Authorization:`Basic ${o}`,"Content-Type":"application/json"}},i=(e,r,o)=>{try{let s=t().createHmac("sha256",o).update(e).digest("hex");return t().timingSafeEqual(Buffer.from(r),Buffer.from(s))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}}};